﻿// ReSharper disable InconsistentNaming

using System.Collections.Generic;
using System.Linq;

using Apq;
using Apq.ChangeBubbling;

using X.PB;

namespace Thing
{
    public partial class ThingBase
    {
        #region Buffs

        /// <summary>
        /// 携带的Buff:击中后按概率加给受击者
        /// </summary>
        public BubblingList<BuffThing> CarriedBuffs_HitForInjurer { get; }

        /// <summary>
        /// 携带的Buff:击杀后按概率加给攻击者
        /// </summary>
        public BubblingList<BuffThing> CarriedBuffs_KilledForAttacker { get; }

        /// <summary>
        /// 承受的Buff
        /// </summary>
        public BubblingList<BuffThing> Buffs { get; }

        #endregion
        
        #region 携带的Buff

        /// <summary>
        /// 按概率抽取携带的Buff:击中后按概率加给受击者
        /// </summary>
        public IList<BuffThing> PickBuffs_HitForInjurer(BuffRecvType buffRecvType)
        {
            return PickBuffs(CarriedBuffs_HitForInjurer, buffRecvType);
        }

        /// <summary>
        /// 按概率抽取携带的Buff:击杀后按概率加给攻击者
        /// </summary>
        public IList<BuffThing> PickBuffs_KilledForAttacker()
        {
            return PickBuffs(CarriedBuffs_KilledForAttacker, BuffRecvType.Killed);
        }

        /// <summary>
        /// 按概率抽取携带的Buff
        /// </summary>
        public IList<BuffThing> PickBuffs(IList<BuffThing> lst, BuffRecvType buffRecvType)
        {
            var trigPriority = buffRecvType switch
            {
                BuffRecvType.Killed => PropType.KilledBuffTrigPriority,
                BuffRecvType.Explose => PropType.ExploseBuffTrigPriority,
                BuffRecvType.KilledExplose => PropType.KilledExploseBuffTrigPriority,
                _ => PropType.HitBuffTrigPriority,
            };
            
            List<BuffThing> rtn = new();
            lst.ToList().ForEach(b =>
            {
                var priority = b.GetTotalDouble(trigPriority).FirstOrDefault();

                // if (this is GunThing gun)
                // {
                //     Debug.Log($"枪[{gun.CsvRow_Gun.Value.Id}]携带的Buff[{b.BuffId}]的触发概率是{priority}");
                // }

                if (RandomNum.RandomDouble(0, 1) < priority)
                {
                    rtn.Add(b);
                }
            });

            return rtn;
        }
        
        #endregion

        #region 承受的Buff

        /// <summary>
        /// 是否可加入指定的Buff
        /// </summary>
        public virtual bool CanAddBuff(BuffThing buff)
        {
            return buff.GetTotalLong(PropType.BuffCategory).FirstOrDefault() switch
            {
                (int)BuffType.Freeze => !Buffs
                    .Any(b => b.GetTotalLong(PropType.BuffCategory).FirstOrDefault() is (int)BuffType.Freeze
                        or (int)BuffType.HitBack),
                (int)BuffType.HitBack => !Buffs
                    .Any(b => b.GetTotalLong(PropType.BuffCategory).FirstOrDefault() is (int)BuffType.Freeze
                        or (int)BuffType.HitBack),
                _ => true
            };
        }

        /// <summary>
        /// 添加Buff 
        /// </summary>
        public void AddBuff(BuffThing buff, bool doStart)
        {
            Buffs.Add(buff);
            
            // 添加Buff后立即启动
            if (doStart)
            {
                buff.StartCdExecutor().Forget();
            }
        }

        /// <summary>
        /// 接受来自攻击方携带的击中后Buff(按配置的概率触发,可触发多个)
        /// </summary>
        public virtual void ReceiveBuffByBulletHit(ThingBase thing, BuffRecvType buffRecvType)
        {
            var lst = thing.PickBuffs_HitForInjurer(buffRecvType)
                .Select(x =>
                {
                    var buff = (BuffThing)x.Clone();
                    buff.Parent = Buffs;
                    buff.BuffRecvType = buffRecvType;
                    // 设置Buff的承受者
                    buff.Bearer = this;
                    return buff;
                }).Where(CanAddBuff).ToList();

            // 添加Buff并立即启动
            lst.ForEach(b => AddBuff(b, true));
        }

        /// <summary>
        /// 接受攻击方携带的杀敌后Buff(按配置的概率触发,可触发多个)
        /// </summary>
        public virtual void ReceiveBuffByKillEnemy(ThingBase thing)
        {
            var lst = thing.PickBuffs_KilledForAttacker()
                .Select(x =>
                {
                    var buff = (BuffThing)x.Clone();
                    buff.Parent = Buffs;
                    buff.BuffRecvType = BuffRecvType.Killed;
                    // 设置Buff的承受者
                    buff.Bearer = this;
                    return buff;
                }).Where(CanAddBuff).ToList();
            
            // 添加Buff并立即启动
            lst.ForEach(b => AddBuff(b, true));
        }

        #endregion
    }
}