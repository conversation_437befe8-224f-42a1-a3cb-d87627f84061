// ReSharper disable InconsistentNaming

using System.Collections.Generic;

using Thing;

using UnityEngine;

namespace Apq.Unity3D.UnityHelpers
{
    /// <summary>
    /// 标准抛物线轨迹生成器 - V64.3修复版
    /// </summary>
    /// <remarks>
    /// 修复：X轴匀速运动，Y轴按标准抛物线公式变化
    /// 符合标准抛物线：Y = A*X² + B*X + C，X是时间变量，Y随X变化
    /// </remarks>
    public class ParabolicLocusGenerator
    {
        /// <summary>
        /// 子弹发射器
        /// </summary>
        public GameObject BulletEjector { get; set; }

        #region 抛物线参数

        /// <summary>
        /// 起始位置
        /// </summary>
        public Vector3 StartPosition { get; set; }

        /// <summary>
        /// 目标位置（终点）
        /// </summary>
        public Vector3? TargetPosition { get; set; }

        /// <summary>
        /// 抛物线顶点高度偏移（相对于起点和终点连线的中点）
        /// </summary>
        public float ApexHeight { get; set; } = 36f;

        /// <summary>
        /// X轴抛物线弧度系数（控制X轴偏移幅度）
        /// </summary>
        public float ParabolicWidth { get; set; } = 38f;

        /// <summary>
        /// 路径分段数量
        /// </summary>
        public int SegmentCount { get; set; } = 50;

        #endregion

        /// <summary>
        /// 计算抛物线路径
        /// </summary>
        /// <param name="startPos">起始位置</param>
        /// <returns>抛物线路径点列表</returns>
        public List<Vector3> CalcParabolicPath(Vector3 startPos)
        {
            StartPosition = startPos;
            
            //Debug.Log($"88888 抛物线路径计算 - StartPos:{startPos} TargetPos:{TargetPosition} ApexHeight:{ApexHeight} SegmentCount:{SegmentCount}");
            
            List<Vector3> pathPoints = new List<Vector3>();

            if (!TargetPosition.HasValue)
            {
                //Debug.LogError($"88888 抛物线路径计算失败 - TargetPosition为空");
                return pathPoints;
            }
            
            Vector3 start = StartPosition;
            Vector3 end = TargetPosition.Value;
            
            // Y轴总距离
            float totalYDistance = end.y - start.y;
            
            // 计算抛物线顶点高度：起点和终点最高者再加上ApexHeight
            float maxY = Mathf.Max(start.y, end.y) + ApexHeight;

            // 计算关键点的X值用于日志输出
            Vector3 startPoint = CalculateStandardParabolicPoint(start, end, maxY, 0f);    // t=0 起始点
            Vector3 apexPoint = CalculateStandardParabolicPoint(start, end, maxY, 0.5f);   // t=0.5 最高点
            Vector3 endPoint = CalculateStandardParabolicPoint(start, end, maxY, 1f);      // t=1 终点
            
                ///    Debug.Log($"99999 抛物线关键点X值 - 起始点X:{startPoint.x:F2} 最高点X:{apexPoint.x:F2} 终点X:{endPoint.x:F2} StartY:{start.y:F2} EndY:{end.y:F2} MaxY:{maxY:F2}");

            for (int i = 0; i <= SegmentCount; i++)
            {
                float t = (float)i / SegmentCount;
                Vector3 point = CalculateStandardParabolicPoint(start, end, maxY, t);
                pathPoints.Add(point);
            }

            //Debug.Log($"88888 抛物线路径计算完成 - 生成{pathPoints.Count}个路径点 MaxY:{maxY}");
            return pathPoints;
        }

        /// <summary>
        /// 计算标准抛物线上的点 - V64.3修复版
        /// </summary>
        /// <param name="start">起点</param>
        /// <param name="end">终点</param>
        /// <param name="maxHeight">抛物线最高点Y坐标</param>
        /// <param name="t">时间进度（0-1）</param>
        /// <returns>抛物线上的点</returns>
        private Vector3 CalculateStandardParabolicPoint(Vector3 start, Vector3 end, float maxHeight, float t)
        {
            // X轴匀速运动：从起点到终点按t线性插值（X是时间变量）
            float currentX = Mathf.Lerp(start.x, end.x, t);
            
            // Y轴按标准抛物线公式变化：Y = A*X² + B*X + C
            // 需要计算A、B、C系数，使抛物线通过起点、终点和最高点
            
            float x0 = start.x;    // 起点X
            float y0 = start.y;    // 起点Y
            float x2 = end.x;      // 终点X
            float y2 = end.y;      // 终点Y
            float x1 = (x0 + x2) * 0.5f;  // 中点X（最高点）
            float y1 = maxHeight;  // 最高点Y
            
            // 根据三点计算抛物线系数：Y = A*X² + B*X + C
            // 通过起点 (x0, y0)、中点 (x1, y1)、终点 (x2, y2)
            float denominator = (x0 - x1) * (x0 - x2) * (x1 - x2);
            
            if (Mathf.Abs(denominator) < 0.001f)
            {
                // 如果分母太小，使用线性插值
                float linearY = Mathf.Lerp(start.y, end.y, t);
                return new Vector3(currentX, linearY, start.z);
            }
            
            float A = (x2 * (y1 - y0) + x1 * (y0 - y2) + x0 * (y2 - y1)) / denominator;
            float B = (x2 * x2 * (y0 - y1) + x1 * x1 * (y2 - y0) + x0 * x0 * (y1 - y2)) / denominator;
            float C = (x1 * x2 * (x1 - x2) * y0 + x2 * x0 * (x2 - x0) * y1 + x0 * x1 * (x0 - x1) * y2) / denominator;
            
            // 使用抛物线公式计算Y坐标
            float currentY = A * currentX * currentX + B * currentX + C;
            
            // 确保Z坐标保持不变
            float currentZ = start.z;
            
            // 调试日志（减少频率）
            if (t == 0f || t == 0.5f || t == 1f)
            {
                Debug.Log($"111111111 V64.9抛物线关键点 - t:{t:F1} X:{currentX:F1} Y:{currentY:F1} 起点:({x0:F1},{y0:F1}) 终点:({x2:F1},{y2:F1})");
            }
            
            return new Vector3(currentX, currentY, currentZ);
        }

        /// <summary>
        /// 根据进度获取抛物线上的位置
        /// </summary>
        /// <param name="progress">进度（0-1）</param>
        /// <returns>当前位置</returns>
        public Vector3 GetPositionAtProgress(float progress)
        {
            if (!TargetPosition.HasValue)
            {
                //Debug.LogError($"88888 抛物线位置计算失败 - TargetPosition为空");
                return StartPosition;
            }

            progress = Mathf.Clamp01(progress);
            
            Vector3 start = StartPosition;
            Vector3 end = TargetPosition.Value;
            float maxHeight = Mathf.Max(start.y, end.y) + ApexHeight;

            Vector3 position = CalculateStandardParabolicPoint(start, end, maxHeight, progress);
            
            return position;
        }

        /// <summary>
        /// 根据进度获取抛物线的切线方向
        /// </summary>
        /// <param name="progress">进度（0-1）</param>
        /// <returns>切线方向</returns>
        public Vector3 GetTangentAtProgress(float progress)
        {
            if (!TargetPosition.HasValue)
            {
                //Debug.LogError($"88888 抛物线切线计算失败 - TargetPosition为空");
                return Vector3.up;
            }

            progress = Mathf.Clamp01(progress);
            
            // 计算当前点和前后微小偏移点，用于求导数
            float delta = 0.01f;
            float progressBefore = Mathf.Max(0f, progress - delta);
            float progressAfter = Mathf.Min(1f, progress + delta);
            
            Vector3 posBefore = GetPositionAtProgress(progressBefore);
            Vector3 posAfter = GetPositionAtProgress(progressAfter);
            
            Vector3 tangent = (posAfter - posBefore).normalized;
            
            return tangent;
        }
    }
} 