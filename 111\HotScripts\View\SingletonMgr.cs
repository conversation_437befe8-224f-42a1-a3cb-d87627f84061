﻿// ReSharper disable InconsistentNaming

using System.Collections.Generic;

using Apq.Unity3D.Extension;

using CsvTables;

using UnityEngine;

using TypeCache = Apq.Reflection.TypeCache;

namespace View
{
    /// <summary>
    /// 单例管理器
    /// </summary>
    [DisallowMultipleComponent]
	public class SingletonMgr : MonoBehaviour
	{
		/// <summary>
		/// 获取或设置管理器
		/// </summary>
		public static SingletonMgr Instance { get; set; }

        public void Awake()
        {
            DontDestroyOnLoad(gameObject);
            Instance = this;

#if UNITY_EDITOR
            UnityEditor.EditorApplication.playModeStateChanged += EditorApplication_playModeStateChanged;
#endif
        }

        public void OnDestroy()
		{
			// 清空单态组件
			MonoBehaviours.Clear();

#if UNITY_EDITOR
            UnityEditor.EditorApplication.playModeStateChanged -= EditorApplication_playModeStateChanged;
#endif
        }

#if UNITY_EDITOR
        private static void EditorApplication_playModeStateChanged(UnityEditor.PlayModeStateChange obj)
        {
            switch (obj)
            {
                // 进入编辑模式后
                case UnityEditor.PlayModeStateChange.EnteredEditMode:
                    Instance = null;
                    break;
                // 退出编辑模式前
                case UnityEditor.PlayModeStateChange.ExitingEditMode:
                    Instance = null;
                    break;
                // 进入播放模式后
                case UnityEditor.PlayModeStateChange.EnteredPlayMode:
                    break;
                // 退出播放模式前
                case UnityEditor.PlayModeStateChange.ExitingPlayMode:
                    break;
            }
        }
#endif

        #region MonoBehaviours

        /// <summary>
        /// 单态组件字典
        /// </summary>
        public Dictionary<string, object> MonoBehaviours { get; } = new();

		/// <summary>
		/// 获取或添加组件
		/// </summary>
		/// <typeparam name="T">组件类型</typeparam>
		public T GetOrAddBehaviour<T>() where T : MonoBehaviour
		{
			if (MonoBehaviours.TryGetValue(typeof(T).FullName!, out var obj)
			    && obj is T rtn)
			{
				return rtn;
			}

			// 创建组件,并加入字典
			rtn = gameObject.GetOrAddComponent<T>();
			MonoBehaviours[typeof(T).FullName!] = rtn;

			return rtn;
		}

		/// <summary>
		/// 移除组件
		/// </summary>
		/// <typeparam name="T">组件类型</typeparam>
		public T RemoveBehaviour<T>() where T : MonoBehaviour
		{
			if (!MonoBehaviours.TryGetValue(typeof(T).FullName!, out var obj)) return default;

			MonoBehaviours.Remove(typeof(T).FullName!);
			if (obj is T rtn) return rtn;
			return default;
		}

		#endregion

		/// <summary>
		/// 全局管理器
		/// </summary>
		public GlobalMgr GlobalMgr => GetOrAddBehaviour<GlobalMgr>();

		/// <summary>
		/// 类型缓存
		/// </summary>
		public TypeCache TypeCache => GetOrAddBehaviour<TypeCache>();

		/// <summary>
		/// CSV加载器的管理器
		/// </summary>
		public CsvLoaderMgr CsvLoaderMgr => GetOrAddBehaviour<CsvLoaderMgr>();

		/// <summary>
		/// 角色数据管理器
		/// </summary>
		public ActorDataMgr ActorDataMgr => GetOrAddBehaviour<ActorDataMgr>();

		/// <summary>
		/// 战场管理器(仅战斗场景)
		/// </summary>
		public GameManager BattleMgr { get; set; }
    
        /// <summary>
        /// 游戏启动时加载配置表
        /// </summary>
        public void PreloadCsv()
        {
            CsvLoaderMgr.GetOrAddLoader<ConstValueCsv>();
            CsvLoaderMgr.GetOrAddLoader<CatMainStageCsv>();
            CsvLoaderMgr.GetOrAddLoader<PlayerBasePropCsv>();
            CsvLoaderMgr.GetOrAddLoader<CatMainMapCsv>();
            CsvLoaderMgr.GetOrAddLoader<CatMainMapTerrainCsv>();
            CsvLoaderMgr.GetOrAddLoader<CatMainNpcCsv>();
            CsvLoaderMgr.GetOrAddLoader<BattleBrushEnemyCsv>();
            CsvLoaderMgr.GetOrAddLoader<AiEventCsv>();
            CsvLoaderMgr.GetOrAddLoader<PropTypeCsv>();
            CsvLoaderMgr.GetOrAddLoader<CommonPropCsv>();
            CsvLoaderMgr.GetOrAddLoader<BulletCsv>();
            CsvLoaderMgr.GetOrAddLoader<GunCsv>();
            CsvLoaderMgr.GetOrAddLoader<EquipmentCsv>();
            CsvLoaderMgr.GetOrAddLoader<MedicamentCsv>();
            CsvLoaderMgr.GetOrAddLoader<CreatureCsv>();
            CsvLoaderMgr.GetOrAddLoader<MissionInfoCsv>();
            CsvLoaderMgr.GetOrAddLoader<CatDropCsv>();
        }
	}
}
