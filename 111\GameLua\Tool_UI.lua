---
--- Generated by <PERSON><PERSON><PERSON>(https://github.com/<PERSON>Lua)
--- Created by Administrator.
--- DateTime: 2023/7/22 11:27
---

---@class UI
local this = {}

---@param btn Button @按钮组件
---@param callback fun():void @点击事件
function this.AddBtnClickListener(btn, callback)
    if IsNullGo(btn) then
        return
    end
    btn.onClick:AddListenerEx(callback)
end

--- 弹出提示窗口（暂时定好接口，后续思考怎么搭建，才能更好面对各种情况）
---@param content string @展示的内容
---@param parent GameObject @挂载的父物体
function this.AddMessageWnd(content, parent)
    if StringL.IsEmptyOrWhiteSpace(content) or IsNullGo(parent) then
        return
    end
end

--- 添加飞字
function this.AddFlyTip(content, parent)

end

return this