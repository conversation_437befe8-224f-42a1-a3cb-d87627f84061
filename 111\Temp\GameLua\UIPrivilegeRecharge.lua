local luaID = ('UIPrivilegeRecharge')

local UIPrivilegeRecharge = {}

-- 初始化
function UIPrivilegeRecharge:OnCreate(objList, parentLua)
	self.objList = objList
	self.parentLua = parentLua
	self.item = self.objList.PrivilegeItem
	self.item.gameObject:SetActive(false)
	self.objList.Txt_PrivilegeTitle.text = GetGameText(luaID, 2)
	self.itemsContent = self.objList.PrivilegeContainer:GetRectTransform()
	self.btnList = {}
	self.buyItemList = {}

	local index = 1
	for k, v in ipairs(Schemes.PrivilegeCard.items) do
		if index > 2 then
			break
		end
		local item = {}
		local obj = GameObject.Instantiate(self.item, self.itemsContent)
		local objTrans = obj:GetRectTransform()
		Helper.FillLuaComps(objTrans, item)
		item.gameObject = obj
		item.objTrans = objTrans
		item.config = v
		item.index = index
		item.textOutline = item.Txt_PrivilegeItemDesc:GetComponent('Outline')
		table.insert(self.buyItemList, item)
		table.insert(self.btnList, item.Btn_BuyPrivilege)
		item.Btn_BuyPrivilege.onClick:AddListenerEx(function () 
			self:OnClickBuy(item)
		end)		
		item.Btn_PrivilegeGet.onClick:AddListenerEx(function () 
			self:OnClickGet(item)
		end)
		local rechargeConfig = Schemes.RechargeCard:Get(item.config.Renew)
		if rechargeConfig then
			item.Txt_BuyPrivilege.text = rechargeConfig.FirstRMB/100
		end
		if index == 1 then
			AtlasManager:AsyncGetSprite('XS-huangxd', item.Btn_BuyPrivilege)
			AtlasManager:AsyncGetSprite('XS-SX-bth', item.Img_PrivilegeTop)
			AtlasManager:AsyncGetSprite('XS-30t', item.Img_PrivilegeIcon)
			item.textOutline.effectColor = Color(172/255, 101/255, 0, 1)
		else
			AtlasManager:AsyncGetSprite('XS-lanxd', item.Btn_BuyPrivilege)
			AtlasManager:AsyncGetSprite('XS-SX-btl', item.Img_PrivilegeTop)
			AtlasManager:AsyncGetSprite('XS-vip', item.Img_PrivilegeIcon)
			item.textOutline.effectColor = Color(4/255, 92/255, 163/255, 1)
		end 
		item.Txt_PrivilegeItemTitle.text = item.config.Name
		item.Txt_PrivilegeItemDesc.text = string.gsub(item.config.Desc, '\\n', '\n')
		item.gameObject:SetActive(true)
		index = index + 1
	end 
	return true 
end

function UIPrivilegeRecharge:UpdateView()	
	local nowtime = HelperL.GetServerTime()
	local rateValue = '15%'
	local value = 100
	for k,v in ipairs(self.buyItemList) do
		local endtime = HeroDataManager:GetLogicData(v.config.SaveLogicID)
        if endtime then
			if nowtime < endtime then
				v.Txt_BuyPrivilege.text = GetGameText(luaID, 3)
				v.Img_PrivilegeRMB.gameObject:SetActive(false)
				v.Txt_BuyPrivilege.gameObject:SetActive(false)
				v.Txt_PrivilegeItemDesc.gameObject:SetActive(false)
				v.Txt_PrivilegeDesc.gameObject:SetActive(true)
				v.Img_PrivilegeZS.gameObject:SetActive(true)
				v.Btn_PrivilegeGet.gameObject:SetActive(true)
				v.Txt_PrivilegeTime.gameObject:SetActive(true)
				if v.index == 2 then
					rateValue = '40%'
					value = 200
				end 
				v.Txt_PrivilegeDesc.text = string.format(GetGameText(luaID, 4), rateValue, value)			
				local getDailyGift =  HeroDataManager:GetLogicBit(LOGIC_DATA.DATA_VIP_GET_PERDAY_GIFT, 16+v.index)
        		if getDailyGift == 0 then
					v.Txt_PrivilegeGet.text = GetGameText(luaID, 5)
					HelperL.SetImageGray(v.Btn_PrivilegeGet:GetComponent('Image'), false)
				else
					v.Txt_PrivilegeGet.text = GetGameText(luaID, 6)
					HelperL.SetImageGray(v.Btn_PrivilegeGet:GetComponent('Image'), true)
				end
			else
				local rechargeConfig = Schemes.RechargeCard:Get(v.config.Renew)
				if rechargeConfig then
					v.Txt_BuyPrivilege.text = rechargeConfig.FirstRMB/100
				end
				v.Img_PrivilegeRMB.gameObject:SetActive(true)
				v.Txt_BuyPrivilege.gameObject:SetActive(true)
				v.Txt_PrivilegeItemDesc.gameObject:SetActive(true)
				v.Txt_PrivilegeDesc.gameObject:SetActive(false)
				v.Img_PrivilegeZS.gameObject:SetActive(false)
				v.Btn_PrivilegeGet.gameObject:SetActive(false)
				v.Txt_PrivilegeTime.gameObject:SetActive(false)
			end
		end
		local itemRedDot = self.parentLua:SetWndRedDot(v.gameObject.transform)
		if itemRedDot then
			itemRedDot:AddCheckParam(RedDotCheckType.PrivilegeCard, v.config.ID)
		end
	end
end

-- 点击领取奖励
function UIPrivilegeRecharge:OnClickBuy(item)
	if not item then return end	
	local nowtime = HelperL.GetServerTime()
	local endtime = HeroDataManager:GetLogicData(item.config.SaveLogicID)
	if nowtime < endtime then return end
	HelperL.Recharge(item.config.Renew)
end

function UIPrivilegeRecharge:OnClickGet(item)
	if not item then return end	
	local nowtime = HelperL.GetServerTime()
	local endtime = HeroDataManager:GetLogicData(item.config.SaveLogicID)
	if nowtime > endtime then return end
	local getDailyGift =  HeroDataManager:GetLogicBit(LOGIC_DATA.DATA_VIP_GET_PERDAY_GIFT, 16+item.index)
	if getDailyGift == 1 then return end
    LuaModule.RunLuaRequest(string.format('LuaRequestGetPrivilegeCardPrize?cardID=%d',item.config.ID), self.onClickBuylBack)
end

function UIPrivilegeRecharge.onClickBuylBack(resultCode, content)
	local self = UIPrivilegeRecharge
	if resultCode == RESULT_CODE.RESULT_COMMON_SUCCEED then
		self:UpdateView()        
    end
end

 
-- 窗口关闭
function UIPrivilegeRecharge:OnClose()
	
end

-- 每秒更新
function UIPrivilegeRecharge:OnSecondUpdate()
	local nowtime = HelperL.GetServerTime()
	for k,v in ipairs(self.buyItemList) do
		local endtime = HeroDataManager:GetLogicData(v.config.SaveLogicID)
        if endtime then
			if nowtime < endtime then
				v.Txt_PrivilegeTime.text = HelperL.GetTimeString(TimeStringType.FullAuto1, endtime-nowtime)
			end
		end
	end
end


-- 窗口销毁
function UIPrivilegeRecharge:OnDestroy()
	-- 释放内存
	for i, btn in ipairs(self.btnList) do
		btn.onClick:RemoveAllListeners()
		btn.onClick:Invoke()
	end
end
return UIPrivilegeRecharge