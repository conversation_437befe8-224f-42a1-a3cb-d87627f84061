﻿using System.Collections.Generic;
using System.Linq;

using Apq.Unity3D.UnityHelpers;

using UnityEngine;

namespace ViewModel
{
    /// <summary>
    /// 直线移动轨迹
    /// </summary>
    public class StraightLocus
    {
        /// <summary>
        /// 子弹的直线移动线段(轨迹)
        /// </summary>
        public List<LineSegment> Locus { get; } = new();
        
        /// <summary>
        /// 获取或设置当前移动进度[0,1]
        /// </summary>
        public float Progress {
            get => Progress_m;
            set
            {
                Progress_m = Mathf.Clamp01(value);
                CurrentPosition_m = CalcCurrentPosition();
            } 
        }

        protected float Progress_m;

        /// <summary>
        /// 获取或设置当前位置
        /// </summary>
        public Vector3 CurrentPosition
        {
            get => CurrentPosition_m;
            set
            {
                CurrentPosition_m = value;
                Progress_m = CalcProgress();
            }
        }

        protected Vector3 CurrentPosition_m;

        /// <summary>
        /// 根据进度计算当前位置
        /// </summary>
        protected Vector3 CalcCurrentPosition()
        {
            if (Locus.Count <= 0) return Vector3.zero;
            
            // 默认为起点
            var rtn = Locus.First().PosStart;
            // 总长度
            var totalLength = Locus.Sum(x => x.Length);
            // 目标长度
            var targetLength = totalLength * Progress_m;
            // 已走过的长度
            float length = 0;
            foreach (var line in Locus)
            {
                rtn = line.PosStart;
                if (line.Length + length >= targetLength)
                {
                    var need = targetLength - length;
                    rtn += need * line.DirNormal;
                    break;
                }

                length += line.Length;
            }

            return rtn;
        }

        /// <summary>
        /// 根据当前位置计算进度
        /// </summary>
        protected float CalcProgress()
        {
            // 总长度
            var totalLength = Locus.Sum(x => x.Length);
            // 已走过的长度
            float length = 0;
            // 当前位置是否在某个线段上
            var  isInLine = false;
            foreach (var line in Locus)
            {
                if (!line.ContainsPoint(CurrentPosition_m))
                {
                    length += line.Length;
                }
                else
                {
                    isInLine = true;
                    length += (CurrentPosition_m - line.PosStart).magnitude;
                }
            }

            return isInLine ? length / totalLength : 0;
        }
    }
}