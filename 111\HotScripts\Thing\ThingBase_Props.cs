﻿using System;
using System.Collections.Generic;
using System.Linq;

using Apq.ChangeBubbling;
using Apq.Unity3D.Extension;

using Props;

using View;

using X.PB;

namespace Thing
{
    public partial class ThingBase
    {
        /// <summary>
        /// 附着在该物件上的属性(作用的目标不一定是自己)
        /// </summary>
        public BubblingList<CommonProp> AttachedProps { get; }

        /// <summary>
        /// 应用于该物件的属性(基础属性+提升)
        /// </summary>
        public BubblingList<HoistedProp> HoistedProps { get; }

        /// <summary>
        /// 将多个物件的提升后属性再汇总为总属性(如：生物+枪)
        /// </summary>
        public BubblingDic<PropType, AggProp> TotalProps { get; }
        
        #region 读写 物件自己的属性

        /// <summary>
        /// 获取或添加物件的属性(不存在时添加空属性)
        /// </summary>
        /// <param name="propType">属性类型</param>
        public virtual CommonProp GetOrAddProp(PropType propType)
        {
            if (!TryGetValue(propType, out var prop))
            {
                prop = new CommonProp(propType, this)
                {
                    AttachedThing = this,
                    PropType = propType,
                    ValueType = CommonProp.GetValueType(propType),
                    ApplyType = ApplyType.Attached
                };
                this[propType] = prop;
            }
            
            return prop;
        }

        /// <summary>
        /// 获取物件的属性值(整型)
        /// </summary>
        /// <param name="propType"></param>
        /// <returns></returns>
        public virtual List<long> GetLong(PropType propType)
        {
            var lstProps = Values.Where(x => x.PropType == propType && x.ValueType == CsValueType.Long).ToList();
            return lstProps.SelectMany(x => x.GetLong()).ToList();
        }
        /// <summary>
        /// 获取物件的属性值(浮点)
        /// </summary>
        public virtual List<double> GetDouble(PropType propType)
        {
            var lstProps = Values.Where(x => x.PropType == propType && x.ValueType == CsValueType.Double).ToList();
            return lstProps.SelectMany(x => x.GetDouble()).ToList();
        }

        /// <summary>
        /// 获取物件的属性值(字符串)
        /// </summary>
        public virtual List<string> GetString(PropType propType)
        {
            var lstProps = Values.Where(x => x.PropType == propType && x.ValueType == CsValueType.String).ToList();
            return lstProps.SelectMany(x => x.GetString()).ToList();
        }

        /// <summary>
        /// 设置物件的属性值(整型)
        /// </summary>
        public ThingBase SetLong(PropType propType, params long[] values)
        {
            GetOrAddProp(propType).SetLong(values);
            return this;
        }

        /// <summary>
        /// 按索引位置设置物件的属性值(整型)
        /// </summary>
        public ThingBase SetLong(PropType propType, int index, long value)
        {
            var prop = GetOrAddProp(propType);

            prop.LongValues[index] = value;
            return this;
        }

        /// <summary>
        /// 设置物件的属性值(浮点)
        /// </summary>
        public ThingBase SetDouble(PropType propType, params double[] values)
        {
            GetOrAddProp(propType).SetDouble(values);
            return this;
        }

        /// <summary>
        /// 按索引位置设置物件的属性值(浮点)
        /// </summary>
        public ThingBase SetDouble(PropType propType, int index, double value)
        {
            var prop = GetOrAddProp(propType);

            prop.DoubleValues[index] = value;
            return this;
        }

        /// <summary>
        /// 设置物件的属性值(字符串)
        /// </summary>
        public ThingBase SetString(PropType propType, params string[] values)
        {
            GetOrAddProp(propType).SetString(values);
            return this;
        }

        /// <summary>
        /// 按索引位置设置物件的属性值(字符串)
        /// </summary>
        public ThingBase SetString(PropType propType, int index, string value)
        {
            var prop = GetOrAddProp(propType);

            prop.StrValues[index] = value;
            return this;
        }

        #endregion
        
        #region 附着的属性

        /// <summary>
        /// 从配置表中找出对自己生效的固有属性
        /// </summary>
        public virtual List<CommonProp> FindInherentPropRows()
        {
            return new List<CommonProp>();
        }

        /// <summary>
        /// 增量加载附着的属性
        /// </summary>
        public virtual void ReloadAttachedProps()
        {
            var lst = FindInherentPropRows();

            lst.ForEach(p =>
            {
                AddAttachedProp(p);
            });
        }

        /// <summary>
        /// 找出一个附着的属性可应用到哪些生物
        /// </summary>
        /// <param name="prop"></param>
        /// <param name="containsSelf">是否包含自己</param>
        /// <param name="excludeApplied">是否排除已应用的生物</param>
        public virtual List<CreatureThing> FindCreaturesCanApply(CommonProp prop,
            bool containsSelf = false, bool excludeApplied = true)
        {
            var rtn = new List<CreatureThing>();
            // 自己能用不
            if (containsSelf && this is CreatureThing creature && CanApply(prop)) rtn.Add(creature);

            // 每个怪物能用不
            rtn.AddRange(SingletonMgr.Instance.BattleMgr.Monsters.Where(m =>
                m.CanApply(prop) &&
                (!excludeApplied || m.HoistedProps.ContainsProp(prop))));
            return rtn;
        }

        /// <summary>
        /// 找出所有附着的属性可应到哪些生物
        /// </summary>
        /// <param name="containsSelf">是否包含自己</param>
        public virtual List<CreatureThing> FindCreaturesCanApply(bool containsSelf = false)
        {
            return AttachedProps.SelectMany(p =>
                FindCreaturesCanApply(p, containsSelf, false)).Distinct().ToList();
        }

        /// <summary>
        /// 找出一个附着的属性可应用到哪些携带的Buff
        /// </summary>
        /// <param name="prop"></param>
        /// <param name="excludeApplied">是否排除已应用的Buff</param>
        /// <returns>基类总是返回空列表</returns>
        public virtual List<BuffThing> FindCarriedBuffsCanApply(CommonProp prop, bool excludeApplied = true)
        {
            var rtn = new List<BuffThing>();

            // 携带的Buff能用不
            rtn.AddRange(CarriedBuffs_HitForInjurer.Where(b =>
                b.CanApply(prop) &&
                (!excludeApplied || !b.HoistedProps.ContainsProp(prop))));
            rtn.AddRange(CarriedBuffs_KilledForAttacker.Where(b =>
                b.CanApply(prop) &&
                (!excludeApplied || !b.HoistedProps.ContainsProp(prop))));

            return rtn;
        }

        /// <summary>
        /// 重新生成携带的Buff(并加载Buff的附着属性、计算提升)
        /// </summary>
        public virtual void ReGenCarriedBuffs()
        {
            CarriedBuffs_HitForInjurer.Clear();
            CarriedBuffs_KilledForAttacker.Clear();
            AttachedProps.Where(p => p.PropType == PropType.CarriedBuffsHitForInjurer)
                .SelectMany(p => p.LongValues)
                .Where(id => id > 0).Distinct()
                .ToList().ForEach(buffId =>
                {
                    var buff = new BuffThing(null, CarriedBuffs_HitForInjurer)
                    {
                        BuffId = (int)buffId,
                        Owner = this,
                    };
                    CarriedBuffs_HitForInjurer.Add(buff);
                    buff.ReloadAttachedProps();
                    buff.PickProps();
                    _ = buff.HoistedProps.Select(h => h.ReHoist()).ToList();
                });
            AttachedProps.Where(p => p.PropType == PropType.CarriedBuffsKilledForAttacker)
                .SelectMany(p => p.LongValues)
                .Where(id => id > 0).Distinct()
                .ToList().ForEach(buffId =>
                {
                    var buff = new BuffThing(null, CarriedBuffs_KilledForAttacker)
                    {
                        BuffId = (int)buffId,
                        Owner = this,
                    };
                    CarriedBuffs_KilledForAttacker.Add(buff);
                    buff.ReloadAttachedProps();
                    buff.PickProps();
                    _ = buff.HoistedProps.Select(h => h.ReHoist()).ToList();
                });
        }

        /// <summary>
        /// 将一个附着的属性分发到应用的物件上
        /// </summary>
        /// <returns>应用到了哪些物件上</returns>
        public virtual List<ThingBase> DispatchAttachedProp(CommonProp p)
        {
            var rtn = new List<ThingBase>();

            // 应用到携带的Buff
            CarriedBuffs_HitForInjurer.ToList().ForEach(b =>
            {
                if (b.CanApply(p))
                {
                    b.AddProp(p);
                    if (!rtn.Contains(b)) rtn.Add(b);
                }
            });
            CarriedBuffs_KilledForAttacker.ToList().ForEach(b =>
            {
                if (b.CanApply(p))
                {
                    b.AddProp(p);
                    if (!rtn.Contains(b)) rtn.Add(b);
                }
            });

            // 应用到自己
            if (CanApply(p))
            {
                AddProp(p);
                if (!rtn.Contains(this)) rtn.Add(this);
            }

            return rtn;
        }

        /// <summary>
        /// 将所有附着的属性分发到应用的目标物件上
        /// </summary>
        /// <returns>应用到了哪些物件上</returns>
        public virtual List<ThingBase> DispatchAttachedProp()
        {
            return AttachedProps.SelectMany(DispatchAttachedProp).Distinct().ToList();
        }

        #endregion

        #region HoistedProps

        /// <summary>
        /// 获取或添加提升后的属性(不存在时添加应用到自己的空属性)
        /// </summary>
        /// <param name="propType">属性类型</param>
        public virtual HoistedProp GetOrAddHoistedProp(PropType propType)
        {
            // 找出被提升的属性,不存在则新增空属性
            var ph = HoistedProps.FirstOrDefault(p => p.PropType == propType);
            if (ph == null)
            {
                ph = new HoistedProp(null, HoistedProps)
                {
                    PropType = propType,
                    ValueType = CommonProp.GetValueType(propType),

                    ApplyType = ApplyType.Attached,
                    AttachedThing = this,
                };
                HoistedProps.Add(ph);
            }
            return ph;
        }

        /// <summary>
        /// 添加一个应用到自己的属性(克隆)
        /// </summary>
        /// <returns>加入了哪个提升后的属性中</returns>
        public virtual HoistedProp AddProp(CommonProp prop)
        {
            if (prop.PropType == PropType.HoistProp)
            {
                // 提升类属性没有提升方法时无效
                if (prop.HoistMethod == PropHoistMethod.None) return null;

                return GetOrAddHoistedProp(prop.HoistPropType).AddHoistProp(prop);
            }
            return GetOrAddHoistedProp(prop.PropType).AddBaseProp(prop);
        }

        /// <summary>
        /// 移除一个应用到自己的属性
        /// </summary>
        public virtual void SubtractProp(CommonProp prop)
        {
            if (prop.PropType == PropType.HoistProp)
            {
                GetOrAddHoistedProp(prop.HoistPropType).RemoveHoistProp(prop);
            }
            else
            {
                GetOrAddHoistedProp(prop.PropType).RemoveBaseProp(prop);
            }
        }

        /// <summary>
        /// 将生物的附着属性中应用到自己的属性添加进来
        /// </summary>
        /// <returns>是否有属性满足条件</returns>
        public virtual bool AddProp(CreatureThing creature)
        {
            var lst = creature.AttachedProps.FindPropsCanApplyTo(this);
            lst.ForEach(p => AddProp(p));
            return lst.Count > 0;
        }

        /// <summary>
        /// 将枪的附着属性中应用到自己的属性添加进来
        /// </summary>
        /// <returns>是否有属性满足条件</returns>
        public virtual bool AddProp(GunThing gun)
        {
            var lst = gun.AttachedProps.FindPropsCanApplyTo(this);
            lst.ForEach(p => AddProp(p));
            return lst.Count > 0;
        }

        /// <summary>
        /// 将Buff的附着属性中应用到自己的属性添加进来
        /// </summary>
        /// <returns>是否有属性满足条件</returns>
        public virtual bool AddProp(BuffThing buff)
        {
            var lst = buff.AttachedProps.FindPropsCanApplyTo(this);
            lst.ForEach(p => AddProp(p));
            return lst.Count > 0;
        }

        /// <summary>
        /// 从提升后的属性中移除来自生物的属性
        /// </summary>
        /// <returns>是否有属性满足条件</returns>
        public virtual bool RemoveProp(CreatureThing creature)
        {
            var lst = creature.AttachedProps.FindPropsCanApplyTo(this, false);
            lst.ForEach(SubtractProp);
            return lst.Count > 0;
        }

        /// <summary>
        /// 从提升后的属性中移除来自将指定物件的属性
        /// </summary>
        /// <returns>是否有属性满足条件</returns>
        public virtual bool RemoveProp(Guid guid)
        {
            var lst = HoistedProps.FindPropsFromThing(guid);
            lst.ForEach(SubtractProp);
            return lst.Count > 0;
        }

        /// <summary>
        /// 从提升后的属性中移除来自Buff的属性
        /// </summary>
        /// <returns>是否有属性满足条件</returns>
        public virtual bool RemoveProp(BuffThing buff)
        {
            var lst = buff.AttachedProps.FindPropsCanApplyTo(this, false);
            lst.ForEach(SubtractProp);
            return lst.Count > 0;
        }

        /// <summary>
        /// 从各处提取应用到自己的属性加入
        /// </summary>
        /// <returns>是否有新属性加入</returns>
        public virtual bool PickProps()
        {
            // 从自己的附着属性中提取
            var lst = AttachedProps.FindPropsCanApplyTo(this);
            lst.ForEach(p => AddProp(p));

            return lst.Count > 0;
        }

        /// <summary>
        /// 计算提升系数
        /// </summary>
        /// <param name="hoistProp">提升类属性</param>
        public virtual double CalcHoistCoe(CommonProp hoistProp)
        {
            return 1;
        }

        #endregion

        #region 生成TotalProps与读取

        /// <summary>
        /// 重新计算物件的总属性和携带的Buff的总属性
        /// </summary>
        /// <remarks>总属性默认是HoistedProps的汇总</remarks>
        public virtual void ReCalcTotalProps()
        {
            // 重新计算前的最大血量
            var originMaxHp = TotalProp_MaxHp;

            TotalProps.Clear();
            HoistedProps.ToList().ForEach(p =>
            {
                GetOrAddTotalProp(p.PropType).AggOnlyOne(p);
            });

            // 重新计算后的最大血量
            var newMaxHp = TotalProp_MaxHp;

            // 当前血量随最大血量变大(不超过最大血量)
            ReCalcHpWhenMaxHpChange(originMaxHp, newMaxHp);

            // 携带的Buff计算总属性
            CarriedBuffs_HitForInjurer.ToList().ForEach(b => b.ReCalcTotalProps());
            CarriedBuffs_KilledForAttacker.ToList().ForEach(b => b.ReCalcTotalProps());
        }

        /// <summary>
        /// 当前血量随最大血量变大(不超过最大血量)
        /// </summary>
        protected void ReCalcHpWhenMaxHpChange(double originMaxHp, double newMaxHp)
        {
            // 当前血量随之增加(不随之减少)
            var inc = newMaxHp - originMaxHp;
            var newHp = Hp.Value;
            if (inc > 0)
            {
                newHp += inc;

                // 当前血量不能超过最大血量
                if (newHp > newMaxHp)
                {
                    newHp = newMaxHp;
                }
                Hp.Value = newHp;
            }
        }

        /// <summary>
        /// 获取或添加物件的总属性(不存在时添加空属性)
        /// </summary>
        /// <param name="propType">属性类型</param>
        public virtual AggProp GetOrAddTotalProp(PropType propType)
        {
            if (TotalProps.TryGetValue(propType, out AggProp prop)) return prop;

            var aggProp = new AggProp(propType, TotalProps)
            {
                AttachedThing = this,
                PropType = propType,
                ValueType = CommonProp.GetValueType(propType),
                ApplyType = ApplyType.Attached
            };
            TotalProps[propType] = aggProp;

            return aggProp;
        }

        /// <summary>
        /// 从总属性中获取属性值(整型)
        /// </summary>
        public virtual List<long> GetTotalLong(PropType propType)
        {
            var rtn = TotalProps[propType]?.GetLong().ToList() ?? new();
            return rtn;
        }

        /// <summary>
        /// 从总属性中获取属性值(浮点)
        /// </summary>
        public virtual List<double> GetTotalDouble(PropType propType)
        {
            var rtn = TotalProps[propType]?.GetDouble().ToList() ?? new();
            return rtn;
        }

        /// <summary>
        /// 从总属性中获取属性值(字符串)
        /// </summary>
        public virtual List<string> GetTotalString(PropType propType)
        {
            var rtn = TotalProps[propType]?.GetString().ToList() ?? new();
            return rtn;
        }

        #endregion
    }
}