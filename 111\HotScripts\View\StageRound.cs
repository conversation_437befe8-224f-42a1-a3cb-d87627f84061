﻿using Cysharp.Threading.Tasks;

using UniRx;

using UnityEngine;

using ViewModel;

namespace View
{
    /// <summary>
    /// 关卡的一回合
    /// </summary>
    public class StageRound : MonoBehaviour
    {
        /// <summary>
        /// 当前回合数
        /// </summary>
        public IntReactiveProperty RoundNo { get; } = new();

        /// <summary>
        /// 当前回合的配置
        /// </summary>
        public MissionWaveInfoItem RoundCfg { get; set; }

        /// <summary>
        /// 当前回合的地图
        /// </summary>
        public Map Map { get; protected set; }
        
        /// <summary>
        /// 当前回合的刷怪器
        /// </summary>
        public MonsterSpawner MonsterSpawner { get; set; }

        public void Start()
        {
            // 处理事件:回合数更改后
            RoundNo.Where(_ => SingletonMgr.Instance.BattleMgr.StageMgr.Dic_Stage != null).Subscribe(_ =>
            {
                RoundCfg = SingletonMgr.Instance.BattleMgr.StageMgr.Dic_Stage[RoundNo.Value];
                
                // 切换地图
                SwitchMap().Forget();
            }).AddTo(this);
        }

        /// <summary>
        /// 切换到当前回合的地图
        /// </summary>
        public async UniTask SwitchMap()
        {
            Map = await SingletonMgr.Instance.BattleMgr.MapMgr.SwitchMap(RoundCfg.MapId);
            if (Map)
            {
                // 设置地图用到当前回合
                Map.CsvRow_MissionInfo.Value = SingletonMgr.Instance.BattleMgr.StageMgr.CsvRow_Mission;
                Map.RoundNo.Value = RoundNo.Value;
                Map.NpcMgr.Reset(Map.CsvRow_CatMainMap.Value);
            }
        }

        /// <summary>
        /// 获取下一回合的配置
        /// </summary>
        public MissionWaveInfoItem GetNextRoundCfg()
        {
            SingletonMgr.Instance.BattleMgr.StageMgr.Dic_Stage.TryGetValue(RoundNo.Value + 1, out var rtn);
            return rtn;
        }

        /// <summary>
        /// 重建刷怪器并启动
        /// </summary>
        public void StartMonsterSpawner()
        {
            StopMonsterSpawner();
            MonsterSpawner = new();
            MonsterSpawner.StartBrush().Forget();
        }

        /// <summary>
        /// 停止刷怪
        /// </summary>
        public void StopMonsterSpawner()
        {
            MonsterSpawner?.StopBrush();
        }
    }
}