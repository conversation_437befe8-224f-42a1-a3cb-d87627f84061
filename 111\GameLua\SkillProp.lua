
SkillProp = 
{
    EffectType_Prop = {
		--生命
		{ EffectType = 201,  ActorProp = "CD",                 EquipSmeltProp = "CD" },
		--基础攻击
		{ EffectType = 202,  ActorProp = "HitCD",             EquipSmeltProp = "HitCD" },
		--生命恢复
		{ EffectType = 203,  ActorProp = "AttackDuration",            EquipSmeltProp = "AttackDuration" },
		--攻击
		{ EffectType = 204,  ActorProp = "MinAttackDuration",      EquipSmeltProp = "MinAttackDuration" },
		--伤害范围
		{ EffectType = 205,  ActorProp = "BulletScale",        EquipSmeltProp = "BulletScale" },
		--狂暴时长
		{ EffectType = 206,  ActorProp = "AttackRadius",     EquipSmeltProp = "AttackRadius" },
		--能量上限
		{ EffectType = 207,  ActorProp = "ShootTimes",       EquipSmeltProp = "ShootTimes" },
		--子弹伤害
		{ EffectType = 208,  ActorProp = "BulletSpeed",     EquipSmeltProp = "CriticalStrike" },
		--直接伤害
		{ EffectType = 209,  ActorProp = "BulletQty", EquipSmeltProp = "AntiCriticalStrike" },
		--伤害减免
		{ EffectType = 210,  ActorProp = "SurroundSpeed",              EquipSmeltProp = "Parry" },
		--狂暴伤害
		{ EffectType = 211, ActorProp = "MaxSwingTimes",          EquipSmeltProp = "AntiParry" },
		--暴击
		{ EffectType = 212, ActorProp = "ExploseRadius" },
		--子弹速度
		{ EffectType = 213, ActorProp = "ExplosePriority",                EquipSmeltProp = "Hit" },
		--XP值恢复
		{ EffectType = 214, ActorProp = "KillExplosePriority",              EquipSmeltProp = "Armor" },
		--能量上限
		{ EffectType = 215, ActorProp = "KillExploseTimes",          EquipSmeltProp = "AntiArmor" },
		--减伤
		{ EffectType = 216, ActorProp = "ExploseCoe",    EquipSmeltProp = "DamageReduction" },
		--加伤
		{ EffectType = 217, ActorProp = "MaxPenetrateTimes",          EquipSmeltProp = "DamageAdd" },
		--移动速度
		{ EffectType = 218, ActorProp = "MaxBounceTimes" },
		--基础生命
		{ EffectType = 219, ActorProp = "MinDamageInterval",      EquipSmeltProp = "AttackDefense" },
		--XP技能伤害
		{ EffectType = 220, ActorProp = "MaxSeparateTimes" },
		--暴击率
		{ EffectType = 221, ActorProp = "BulletSeparate",    EquipSmeltProp = "Dodge" },
		--攻击速度
		{ EffectType = 222, ActorProp = "BulletLife" },
		--总生命
		{ EffectType = 223, ActorProp = "BuffPriority" },
		--总攻击
		{ EffectType = 224, ActorProp = "DamageCoe" },
		--暴击伤害
		{ EffectType = 225, ActorProp = "DenyMoveTime",     EquipSmeltProp = "MoveSpeed" },
		--对BOSS伤害
		{ EffectType = 226, ActorProp = "DenyMoveTimePct" },
		--获得经验
		{ EffectType = 227, ActorProp = "HitBackTime" },
		--获得金币
		{ EffectType = 228, ActorProp = "HitBackTimePct" },
		--吸血
		{ EffectType = 229, ActorProp = "HitBackSpeed" },
		--反伤
		{ EffectType = 230, ActorProp = "HitBackSpeedPct" },
        { EffectType = 231, ActorProp = "SlowTime" },
        { EffectType = 232, ActorProp = "SlowTimePct" },
        { EffectType = 233, ActorProp = "SlowSpeed" },
        { EffectType = 234, ActorProp = "SlowSpeedPct" },
	},
}

function SkillProp.AddTableNewKeyValue(tb,key)
    for k, v in pairs(tb) do
        if(k == key) then
            return true;
        end
    end
    tb[key] = 0;
end

function SkillProp.NewPropOjb()
	local obj = {}
	for _, tp in ipairs(SkillProp.EffectType_Prop) do
		obj[tp.ActorProp] = {};
        --obj[tp.ActorProp][0] = 0;
	end
	return obj;
end

function SkillProp.EffectType_Prop_First(EffectType)
	for _, tp in ipairs(SkillProp.EffectType_Prop) do
		if tp.EffectType == EffectType then
			return tp
		end
	end
end

---comment prop作为加成，应用到result上。(最终万分比视为数值)
---@param result table
---@param prop table
---@param CalcPct boolean 是否计算单件万分比
---@param pctBase table 单件万分比属性的基础值
---@return table result
function SkillProp.PropSum(result, prop)
	--print("PropSum -- 参数 CalcPct:", CalcPct, 'pctBase:', json.encode(pctBase))
	for key, value in pairs(prop) do
		--if type(value) == "number" then
            
			for skillId, v in pairs(value) do
                --SkillProp.PropAddValue(result, key, v,"all")
                SkillProp.PropAddValue(result, key, v,skillId)
            end
		--end
	end
	return result;
end

---comment 属性值增加指定数值
---@param refProp table
---@param key string
---@param value number
function SkillProp.PropAddValue(refProp, key, value,skillId)
	if key == nil then
		return
	end
	if value == nil then
		return
	end
    if skillId == nil then
		return
	end
	if refProp[key] == nil then
		refProp[key] = {}
	end
    SkillProp.AddTableNewKeyValue(refProp[key],skillId)
    refProp[key][skillId] = refProp[key][skillId] + value
end

---comment 读取升星属性
--- 传递的参数: starCount 或 quality、starNum
---@param starCount integer 星量，有值则以此为准
---@param quality integer 品质
---@param starNum integer 星数
function SkillProp.ReadStarProp(smeltID, starCount, quality, starNum)
	local rtn = SkillProp.NewPropOjb();
	local p2 = SkillProp.NewPropOjb();
	local csvRow_Smelt = ActorProp.ReadCSVRow_EquipSmelt(smeltID, starCount, quality, starNum)
	if csvRow_Smelt then
		for i = 1, #csvRow_Smelt.EquipEffectID do
			local effectID = tonumber(csvRow_Smelt.EquipEffectID[i]) / 10000
			--print("ReadStarProp -- local effectID:" .. effectID)
			local effectItem = Schemes.EquipEffect:Get(effectID)
			if effectItem then
				local tp = SkillProp.EffectType_Prop_First(effectItem.EffectType);
				if tp then
                    for i, skillId in ipairs(effectItem.PolishParam2) do
                        SkillProp.PropAddValue(p2, tp.ActorProp, effectItem.EffectParam,skillId);
                    end
					
				end
			end
		end

		if(string.len(csvRow_Smelt.StarLvlProps) > 2) then
			if(starCount == nil) then
				starCount = ActorProp.ConvertToStarCount(quality, starNum);
			end

			for i, v in ipairs(HelperL.Split(csvRow_Smelt.StarLvlProps, ";")) do
                local cdt = HelperL.Split(v, "|");
                local cdt_StarCount = ActorProp.ConvertToStarCount(cdt[1], cdt[2]);
				if(starCount >=cdt_StarCount)then
					local effectID = tonumber(cdt[3])
					--print("ReadStarProp -- local effectID:" .. effectID)
					local effectItem = Schemes.EquipEffect:Get(effectID)
					if effectItem then
						local tp = SkillProp.EffectType_Prop_First(effectItem.EffectType);
						if tp then
							for k, skillId in ipairs(effectItem.PolishParam2) do
								SkillProp.PropAddValue(p2, tp.ActorProp, effectItem.EffectParam,skillId);
							end							
						end
					end
				end
			end
		end

		SkillProp.PropSum(rtn, p2);
	end
	
	return rtn;
end

---把技能属性数据表转化成按技能id读取的表结构
function  SkillProp.GetPlayerSkillPropByClient()
	local tb = SkillProp.GetPlayerAllSkillProp();
	local rtn = {}
	for i, v in ipairs(Schemes.CatSkill.items) do
		--rtn[v.ID] = {};	
		local tbProp =  {};
		tbProp["SkillID"] = v.ID;
		for key, value in pairs(SkillProp.EffectType_Prop) do
			if tb.Result[value.ActorProp][0] ~= nil then
				tbProp[value.ActorProp] = tb.Result[value.ActorProp][0];
				--rtn[v.ID][value.ActorProp] = tb.Result[value.ActorProp][0];
			elseif tb.Result[value.ActorProp][v.ID] ~= nil then
				tbProp[value.ActorProp] = tb.Result[value.ActorProp][v.ID];
				--rtn[v.ID][value.ActorProp] = tb.Result[value.ActorProp][v.ID];
			else
				tbProp[value.ActorProp] = 0;
				--rtn[v.ID][value.ActorProp] = 0;
			end
		end
		table.insert(rtn,tbProp);
	end
	--SkillProp.print_r(rtn)
	--print(json.encode(rtn));
	return rtn
end

--- 读取单件装备的属性
---@param equip integer
---@return CalcEquipProp
function SkillProp.ReadEquipProp(equip)
	---@type CalcEquipProp
	local rtn = {
		-- 计算结果
		EquipProp = SkillProp.NewPropOjb(),
		-- 万分比加成的基础属性(SelfProp+StarProp+StarExpProp)
		BaseProp = SkillProp.NewPropOjb(),
		-- 装备自带的属性
		SelfProp = SkillProp.NewPropOjb(),
		-- 当前星级的升星属性(不含经验提升)
		StarProp = SkillProp.NewPropOjb(),
		-- 由升星经验带来的属性提升
		StarExpProp = SkillProp.NewPropOjb(),
		-- -- 上一星级的属性值
		-- PreStarProp = ActorProp.NewPropOjb(),
		-- -- 下一星级的属性值
		NextStarProp = SkillProp.NewPropOjb(),
		-- 实体唯一ID
		uid = 0,
		-- 物品ID
		GoodsID = 0,
		-- 物品名称
		GoodsName = "未找到",
		-- 实体类型
		EntityClass = 0,
		-- 实体子类型
		SubType = 0,
		-- 升星ID
		SmeltID = 0,
		-- 品质
		SmeltQuality = 0,
		-- 星数
		SmeltStarNum = 0,
		-- 星量
		SmeltStarCount = 0,
		-- 升星经验
		SmeltExp = 0,
	};

	if equip then
		rtn.uid = equip.uid;
		rtn.GoodsID = equip:GetProperty(ENTITY_FIELD.ENTITY_FIELD_ID)
		rtn.SmeltQuality = equip:GetProperty(EQUIPMENT_FIELD.EQUIPMENT_FIELD_QUALITY)
		rtn.SmeltStarNum = equip:GetProperty(EQUIPMENT_FIELD.EQUIPMENT_FIELD_STARNUM)
		rtn.SmeltExp = equip:GetProperty(EQUIPMENT_FIELD.EQUIPMENT_FIELD_SMELTEXP);
		local expRestEveryStar = false;
		if rtn.GoodsID then
			local csvRow_Equipment = Schemes.Equipment:Get(rtn.GoodsID)
			-- 装备类先算上装备本身的属性
			if (csvRow_Equipment) then
				rtn.GoodsName = csvRow_Equipment.GoodsName;
				rtn.EntityClass = csvRow_Equipment.EntityClass;
				rtn.SubType = csvRow_Equipment.SubType;
				rtn.SmeltID = csvRow_Equipment.SmeltID;
				expRestEveryStar = rtn.SubType >= 1 and rtn.SubType <= 12;
				if expRestEveryStar then
					rtn.SmeltExp = equip:GetProperty(EQUIPMENT_FIELD.EQUIPMENT_FIELD_CUSTOM)
				end

				-- 计算SelfProp
				for i = EQUIPMENT_FIELD.EQUIPMENT_FIELD_EFFECTID1, EQUIPMENT_FIELD.EQUIPMENT_FIELD_ADDEFFECT_GROUP10_ID5, 1 do
					local p = ActorProp.GetAttributeData(equip:GetProperty(i), i, rtn.GoodsID)
					if (p) then
						local tp = SkillProp.EffectType_Prop_First(p.Type);
						if tp then
                            for i, skillId in ipairs(p.PolishParam2) do
                                --print(tp.ActorProp,p.Value,p.Item.ID)
                                SkillProp.PropAddValue(rtn.SelfProp, tp.ActorProp, p.Value,skillId);
                            end
							
							--print("ReadEquipProp -- tp:", json.encode(tp), "p:", json.encode(p));
						end
					end
				end
				--print("ReadEquipProp -- rtn.SelfProp:", json.encode(rtn.SelfProp))
			end
			-- 道具类
			local csvRow_Medicament = Schemes.Medicament:Get(rtn.GoodsID)
			if (csvRow_Medicament) then
				rtn.GoodsName = csvRow_Medicament.GoodsName;
				rtn.EntityClass = csvRow_Medicament.EntityClass;
				rtn.SubType = csvRow_Medicament.SubType;
				rtn.SmeltID = csvRow_Medicament.SmeltID;
				rtn.SmeltQuality = csvRow_Medicament.Quality;
				rtn.SmeltStarNum = csvRow_Medicament.StarNum;
			end

			-- 根据品质、星数找到升星表的一行
			if not rtn.SmeltQuality then
				rtn.SmeltQuality = 0;
			end
			if not rtn.SmeltStarNum then
				rtn.SmeltStarNum = 0;
			end

			local csvRow_Smelt = nil;
			local csvRow_NextSmelt = nil;

			if (rtn.EntityClass == 2 and rtn.SubType == 26) then
				local slevel = ActorProp.GetEquipSmeltLevel(equip);
				rtn.SmeltQuality = 0;
				rtn.SmeltStarNum = slevel.level - 1;
				csvRow_Smelt = ActorProp.ReadCSVRow_EquipSmelt(rtn.SmeltID, nil, rtn.SmeltQuality, rtn.SmeltStarNum);
				csvRow_NextSmelt = ActorProp.ReadCSVRow_EquipSmelt(rtn.SmeltID, nil, rtn.SmeltQuality, rtn.SmeltStarNum + 1);
				if csvRow_Smelt then
					--print("ReadEquipProp -- 开始计算升星属性:")
					rtn.StarProp = SkillProp.ReadStarProp(rtn.SmeltID, nil, rtn.SmeltQuality, rtn.SmeltStarNum);
				end
				if(csvRow_NextSmelt) then
					rtn.NextStarProp = SkillProp.ReadStarProp(rtn.SmeltID, nil, rtn.SmeltQuality, rtn.SmeltStarNum + 1);
				end
			else
				rtn.SmeltStarCount = ActorProp.ConvertToStarCount(rtn.SmeltQuality, rtn.SmeltStarNum);
				csvRow_Smelt = ActorProp.ReadCSVRow_EquipSmelt(rtn.SmeltID, rtn.SmeltStarCount);
				csvRow_NextSmelt = ActorProp.ReadCSVRow_EquipSmelt(rtn.SmeltID, rtn.SmeltStarCount + 1);
				if csvRow_Smelt then
					--print("ReadEquipProp -- 开始计算升星属性:")
					rtn.StarProp = SkillProp.ReadStarProp(rtn.SmeltID, rtn.SmeltStarCount);
				end
				if csvRow_NextSmelt then
					rtn.NextStarProp = SkillProp.ReadStarProp(rtn.SmeltID, rtn.SmeltStarCount + 1);
				end
			end

			-- 计算升星属性
			if csvRow_Smelt then
				-- 未升到最后一级时,将升星经验换算为属性提升(之前是按进度比例,现在是按配置的倍率)
				if rtn.SmeltExp and rtn.SmeltExp > 0 and csvRow_NextSmelt then
					--print("ReadEquipProp -- 开始计算升星经验属性:")
					for key, value in pairs(rtn.StarProp) do
						for skillid, v in pairs(value) do
							if rtn.NextStarProp[key][skillid] > v then
								if expRestEveryStar and csvRow_Smelt.MeltingNumber > 0 then
									rtn.StarExpProp[key][skillid] = csvRow_Smelt.ExpProRatio * rtn.SmeltExp /
										csvRow_Smelt.MeltingNumber;
								elseif rtn.SmeltExp > csvRow_Smelt.SaleMoney and csvRow_Smelt.LevelExpRate > 0 then
									rtn.StarExpProp[key][skillid] = csvRow_Smelt.ExpProRatio *
										(rtn.SmeltExp - csvRow_Smelt.SaleMoney) / csvRow_Smelt.LevelExpRate;
								end
							end
						end
					end
				end
			end

			SkillProp.PropSum(rtn.BaseProp, rtn.SelfProp);
			SkillProp.PropSum(rtn.BaseProp, rtn.StarProp);
			SkillProp.PropSum(rtn.BaseProp, rtn.StarExpProp);

			--ActorProp.PropSum(rtn.EquipProp, rtn.BaseProp, true, rtn.BaseProp);
		end
	end
	return rtn;
end

--计算细胞技能属性（服务器无法算到正确的属性，放到客户端自己算）
function SkillProp.GetPlayerAllSkillProp()
	local rtn = {
		-- 计算结果
		Result = SkillProp.NewPropOjb(),
		-- 攻击能力(火力值)
		FirePower = 0,
		-- 防御能力
		DefensePower = 0,
		-- 细胞属性(按等级取配置表)
		ActorBase = SkillProp.NewPropOjb(),
		-- 细胞列表
		Airs = {},
		-- 装备列表
		Equips = {},
		-- 卡牌列表
		Cards = {},
		-- 菌落列表
		Fates = {},
		-- 宠物列表
		Pets = {},
		-- 坐骑装备
		MountEquips = {},
		-- 升星目标达成的额外属性
		StarReach = SkillProp.NewPropOjb(),
	};

	-- 细胞属性
	local skepAir = SkepModule:GetSkepByID(SKEPID.SKEPID_GODWEAPON)
	if skepAir then
		for _, weaponItem in ipairs(Schemes.EquipWeapon.items) do
			-- 看看背包篮子里有没有
			local airEntity = skepAir:GetEntityByGoodsID(weaponItem.ID)
			if airEntity then
				table.insert(rtn.Airs, SkillProp.ReadEquipProp(airEntity))
			end
		end
	end

	-- 装备属性
	local equipSkep = SkepModule.GetEquipSkep()
	for i = 1, 11 do
		if equipSkep[i] then
			local equipUID = equipSkep[i]
			local equip = EntityModule:GetEntity(equipUID)
			table.insert(rtn.Equips, SkillProp.ReadEquipProp(equip))
		end
	end

	-- 卡牌属性
	local packetIDList = Schemes.Equipment:GetByPacketID()
	local EquipmentData = packetIDList[10]
	for _, v in ipairs(EquipmentData) do
		local cardScheme = Schemes.EquipCollect:Get(v.ID)
		local cardSkep = SkepModule:GetSkepByID(SKEPID.SKEPID_MONSTERCARD)
		if cardScheme then
			local entity = cardSkep:GetEntityByGoodsID(cardScheme.ID)
			if entity then
				table.insert(rtn.Cards, SkillProp.ReadEquipProp(entity))
			end
		end
	end

	-- 坐骑装备
	EquipmentData = packetIDList[18]
	for _, v in pairs(EquipmentData) do
		--if v.ID > 600050 and v.ID < 600054 then
		local cardSkep = SkepModule:GetSkepByID(SKEPID.SKEPID_MOUNTEQUIP)
		local entity
		if cardSkep then
			entity = cardSkep:GetEntityByGoodsID(v.ID)
			if entity then
				local index = 1
				if v.ID == 600051 then
					index = 1
				elseif v.ID == 600052 then
					index = 2
				elseif v.ID == 600053 then
					index = 3
				end
				local commonText = Schemes.CommonText:Get(99 + index)
				--print(v.ID.."||"..tostring(EntityModule.hero.buffLC:HasBuff(commonText.Param1)) .."||"..EntityModule.hero.buffLC:GetBuffLeftTime(commonText.Param1))
				if EntityModule.hero.buffLC:HasBuff(commonText.Param1) and EntityModule.hero.buffLC:GetBuffLeftTime(commonText.Param1) > 1 then
					--print("佩戴：" .. v.ID)
					table.insert(rtn.MountEquips, SkillProp.ReadEquipProp(entity))
				else
					--print("取下"..v.ID)
					-- local str_req = string.format("LuaRequestTakeMountEquip?equip=%s", entity.uid)
					-- LuaModule.RunLuaRequest(str_req, function(result, content)
					-- 	if result == RESULT_CODE.RESULT_COMMON_SUCCEED then
					-- 		-- HelperL.ShowMessage(TipType.FlowText, GetGameText(luaID, 12))
					-- 	else
					-- 		HelperL.ShowMessage(TipType.FlowText, ResultCode:GetResultCodeStr(result))
					-- 	end
					-- end)
				end
			end
		end
		--end
	end

	-- 菌落属性
	local skepFate = SkepModule.GetFateEquipmentSkep()
	for i = 0, skepFate.indexMaxsize do
		if skepFate[i] then
			local equipUID = skepFate[i]
			local equip = EntityModule:GetEntity(equipUID)
			table.insert(rtn.Fates, SkillProp.ReadEquipProp(equip))
		end
	end

	-- 宠物属性(原坐骑)
	local skepMount = SkepModule:GetSkepByID(SKEPID.SKEPID_MOUNT)
	if skepMount then
		for i = 0, skepMount.indexMaxsize do
			if skepMount[i] then
				local entity = EntityModule:GetEntity(skepMount[i])
				if entity then
					local equipProp = SkillProp.ReadEquipProp(entity);
					table.insert(rtn.Pets, equipProp)
				end
			end
		end
	end

	-- 升星目标达成的额外属性
	local StarReach_EffectIDs = {};
	for i, v in ipairs(Schemes.EquipForgeAddition.items) do
		if (v.Type == 0) then
			local equipCount = ActorProp.Stat_Star(v.Condition2 - 1);
			if (equipCount >= v.Condition1) then
				table.insert(StarReach_EffectIDs, v.EffectList);
			end
		end
	end
	for _, effectList in ipairs(StarReach_EffectIDs) do
		for _, v in ipairs(effectList) do
			local effectID = tonumber(v) / 10000
			--print("ReadStarProp -- local effectID:" .. effectID)
			local effectItem = Schemes.EquipEffect:Get(effectID)
			if effectItem then
				local tp = SkillProp.EffectType_Prop_First(effectItem.EffectType);
				if tp then
					SkillProp.PropAddValue(rtn.StarReach, tp.ActorProp, effectItem.EffectParam);
				end
			end
		end
	end

	-- 细胞等级属性+细胞属性+装备属性+卡牌属性+菌落属性+宠物属性(原宝石)
	--rtn.ActorBase = ActorProp.ReadActorLevelProp();
	--print("GetPlayerFieldsByClient -- 细胞等级属性:", json.encode(rtn.ActorBase))
	--ActorProp.PropSum(rtn.Result, rtn.ActorBase);

	--print("GetPlayerFieldsByClient -- 细胞:", #rtn.Airs)
	for _, equipProp in ipairs(rtn.Airs) do
		-- print("GetPlayerFieldsByClient -- Airs:GoodsID=" .. equipProp.GoodsID
		-- 	.. "升星ID=" .. equipProp.SmeltID
		-- 	.. "品质=" .. equipProp.SmeltQuality
		-- 	.. "星数=" .. equipProp.SmeltStarNum
		-- 	.. "名称=" .. equipProp.GoodsName
		-- 	, json.encode(equipProp))
		SkillProp.PropSum(rtn.Result, equipProp.BaseProp);
	end

	--print("GetPlayerFieldsByClient -- 装备:", #rtn.Equips)
	for _, equipProp in ipairs(rtn.Equips) do
		-- print("GetPlayerFieldsByClient -- 装备:GoodsID=" .. equipProp.GoodsID
		-- 	.. "升星ID=" .. equipProp.SmeltID
		-- 	.. "品质=" .. equipProp.SmeltQuality
		-- 	.. "星数=" .. equipProp.SmeltStarNum
		-- 	.. "名称=" .. equipProp.GoodsName
		-- 	, json.encode(equipProp))
		SkillProp.PropSum(rtn.Result, equipProp.BaseProp);
	end

	--print("GetPlayerFieldsByClient -- 卡牌:", #rtn.Cards)
	for _, equipProp in ipairs(rtn.Cards) do
		-- print("GetPlayerFieldsByClient -- 卡牌:GoodsID=" .. equipProp.GoodsID
		-- 	.. "升星ID=" .. equipProp.SmeltID
		-- 	.. "品质=" .. equipProp.SmeltQuality
		-- 	.. "星数=" .. equipProp.SmeltStarNum
		-- 	.. "名称=" .. equipProp.GoodsName
		-- 	, json.encode(equipProp))
		SkillProp.PropSum(rtn.Result, equipProp.BaseProp);
	end

	--print("GetPlayerFieldsByClient -- 坐骑装备:", #rtn.MountEquips)
	for _, equipProp in ipairs(rtn.MountEquips) do
		-- print("GetPlayerFieldsByClient -- 坐骑装备:GoodsID=" .. equipProp.GoodsID
		-- 	.. "升星ID=" .. equipProp.SmeltID
		-- 	.. "品质=" .. equipProp.SmeltQuality
		-- 	.. "星数=" .. equipProp.SmeltStarNum
		-- 	.. "名称=" .. equipProp.GoodsName
		-- 	, json.encode(equipProp))
		SkillProp.PropSum(rtn.Result, equipProp.BaseProp);
	end

	--print("GetPlayerFieldsByClient -- 菌落:", #rtn.Fates)
	for _, equipProp in ipairs(rtn.Fates) do
		-- print("GetPlayerFieldsByClient -- 菌落:GoodsID=" .. equipProp.GoodsID
		-- 	.. "升星ID=" .. equipProp.SmeltID
		-- 	.. "品质=" .. equipProp.SmeltQuality
		-- 	.. "星数=" .. equipProp.SmeltStarNum
		-- 	.. "名称=" .. equipProp.GoodsName
		-- 	, json.encode(equipProp))
		SkillProp.PropSum(rtn.Result, equipProp.BaseProp);
	end

	--print("GetPlayerFieldsByClient -- 宠物:", #rtn.Pets)
	for _, equipProp in ipairs(rtn.Pets) do
		-- print("GetPlayerFieldsByClient -- 宠物:GoodsID=" .. equipProp.GoodsID
		-- 	.. "升星ID=" .. equipProp.SmeltID
		-- 	.. "品质=" .. equipProp.SmeltQuality
		-- 	.. "星数=" .. equipProp.SmeltStarNum
		-- 	.. "名称=" .. equipProp.GoodsName
		-- 	, json.encode(equipProp))
		SkillProp.PropSum(rtn.Result, equipProp.BaseProp);
	end

	-- 升星目标达成的额外属性
	SkillProp.PropSum(rtn.Result, rtn.StarReach);

	return rtn;
end

local print = print
local tconcat = table.concat
local tinsert = table.insert
local srep = string.rep
local type = type
local pairs = pairs
local tostring = tostring
local next = next
 
function SkillProp.print_r(root)
	print("------------------打印表数据-----------------")
    local cache = {  [root] = "." }
    local function _dump(t,space,name)
        local temp = {}
        for k,v in pairs(t) do
            local key = tostring(k)
            if cache[v] then
                tinsert(temp,"+" .. key .. " {" .. cache[v].."}")
            elseif type(v) == "table" then
                local new_key = name .. "." .. key
                cache[v] = new_key
                tinsert(temp,"+" .. key .. _dump(v,space .. (next(t,k) and "|" or " " ).. srep(" ",#key),new_key))
            else
                tinsert(temp,"+" .. key .. " [" .. tostring(v).."]")
            end
        end
        return tconcat(temp,"\n"..space)
    end
    print(_dump(root, "",""))
end