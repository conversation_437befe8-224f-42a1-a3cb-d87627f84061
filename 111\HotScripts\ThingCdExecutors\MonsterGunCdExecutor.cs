﻿using System.Linq;

using RxEventsM2V;

using Thing;

using UniRx;

using View;

namespace ThingCdExecutors
{
    /// <summary>
    ///     怪物阵营的枪的执行器
    /// </summary>
    public class MonsterGunCdExecutor : GunCdExecutor
    {
        /// <summary>
        ///     枪属于哪个怪物
        /// </summary>
        public MonsterThing MonsterThing => GunThing.Owner as MonsterThing;

        /// <summary>
        ///     获取玩家角色(数据)
        /// </summary>
        protected ActorThing Actor => SingletonMgr.Instance.BattleMgr.Actor;

        /// <summary>
        ///     射击一次后(按cd时长的一次循环)
        /// </summary>
        protected override void OnAfterShoot()
        {
            // _ = MonsterThing.AiEventList.Where(x => x.CanSuicide).Select(x =>
            // {
            //     x.ShotTimes.Value++;
            //     
            //     // 怪物自杀(延时)
            //     MessageBroker.Default.Publish(new ThingDead { Thing = MonsterThing });
            //     return x;
            // }).ToList();
        }
    }
}