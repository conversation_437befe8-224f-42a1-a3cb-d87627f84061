---
--- Generated by <PERSON><PERSON><PERSON>(https://github.com/EmmyLua)
--- Created by Administrator.
--- DateTime: 2023/7/25 21:16
--- 界面相关的处理器（广播监听、点击事件等）
---

local ClassBase = require("Base_Class")

---@class UIHandlerBase:ClassBase
---@field _base ClassBase
local this = class(ClassBase)

--- 入口
---@protected
function this:baseInit(view)
    self.view = view
    self:AddListener()
end

--- 添加监听
function this:AddListener()

end

--- 订阅事件
---@param eventId number|string @事件Id
---@param callback function @事件触发回调
function this:Subscribe(eventId, callback)
    if self.eventList == nil then
        ---@type table<integer, table<integer, function>>
        self.eventList = {}
    end
    if self.eventList[eventId] == nil then
        self.eventList[eventId] = {}
    end
    table.insert(self.eventList[eventId], callback)
    EventManager:Subscribe(eventId, callback)
end

--- 移除监听
---@protected
function this:RemoveListener()
    for eventId, callbackList in pairs(self.eventList) do
        for i = 1, #callbackList do
            EventManager:UnSubscribe(eventId, callbackList[i])
        end
    end
    self.eventList = nil
end

--- 销毁
function this:baseDestroy()
    --self:RemoveListener()
end

return this