--[[
    created:	2016/10/18
    author :	刘树齐
    purpose:	物品Tip里的获取地点管理
--]]
local luaID = ('ItemGainWay')

local ItemGainWay={}
local parameterList = {9,10,17,18,19,20,21,22,23,24,28,32,1001}

ItemGainWay.New = function (gainwayID)
	local m = {}
    local gainPlaceIDList = {}
	function m:Init()
		self.gainwayID = gainwayID
		m.Index = 0
	end

	function m:AddToParent(parentObj,vector3,itemRoot)
		if self.parentObj then
			return
		end
		self.parentObj = parentObj
		local prefab = HotResManager.ReadUI("ui/Store/prefab/GainWayOption")
		self.gameObject = NGUITools.AddChild(parentObj, prefab)
		self.itemRoot = itemRoot
		self.gameObject.transform.localPosition = vector3 or Vector3.zero
		self.goTrans = self.gameObject.transform
		self.view = self.goTrans.parent.parent:GetComponent('UIPanel')
		UIEventListener.Get(self.gameObject).onClick = function (go)
			self:OnPress(go)			
		end
		self:SetData(self.gameObject)
	end

	function m:SetData(Obj)
		gainwayID = gainwayID or self.gainwayID
		gainPlaceIDList[Obj] =  self:GetPlaceData(gainwayID)	
	end

	function m:OnPress(Obj)
		if not gainPlaceIDList[Obj] then print('当前产出ID错误 return') return end
		ItemGainWay.EnterGainWay(tonumber(self.gainwayID))
		NGUITools.Destroy(self.itemRoot)
	end
	
	m:Init()
	function m:GetPlaceData( gainway_id  )
		if gainway_id == '0' then
			NGUITools.Destroy(self.gameObject)
			return
		end
        local  mediGain = Schemes.MediGain.Get(tonumber(gainway_id))
        if mediGain then
        	self.gameObject:GetComponent('UISprite').spriteName = mediGain.Icon
        	self.gameObject:GetComponent('UISprite'):MakePixelPerfect()
        else
        	error(string.format('没有找到MediGain表的产出ID为'..tostring(gainway_id)))
        end
        return mediGain
     end
	return m
end

function ItemGainWay.LoadActivity( parameter )
	UIManager.LoadUI('UICommonDailyActivities')
	local param = {Id = parameter, StageID = 1}
	local param1,param2 = HelperL.CheckActivity()
	if param1 == param.Id then
		param.StageID = param2
	end
	UIManager.SendWndMsg('UICommonDailyActivities',UIWndMsg.UICommonDailyActivities.intiMsg,param)
end

function ItemGainWay.EnterGainWay(wayID)
	local schemesMediaGain = Schemes.MediGain.Get(wayID)
	local levelLimite = schemesMediaGain.LimitLevel
	local roleLevel = EntityModule.hero.propertyLC:GetProperty(CREATURE_FIELD.CREATURE_FIELD_LEVEL)
	if roleLevel >= levelLimite then
		if schemesMediaGain.Type == 0 then
			UIManager.OpenPage(schemesMediaGain.Parameter)
		elseif schemesMediaGain.Type == 1 then
			if schemesMediaGain.Parameter then
				local schemesChapter = Schemes.Chapter.Get(schemesMediaGain.Parameter)
				if schemesChapter.EntryCondition == 1 then
					--单人副本
					UIManager.OpenTaskEctype(1)
				else
					--多人副本
					EntityModule.hero.ectypeLC.selectedChallengeEctypeID = schemesMediaGain.Parameter
					UIManager.OpenTaskEctype(2)
				end
				EventManager.Fire(EventID.EctypeEnterByChapID,schemesMediaGain.Parameter)
			else
				error('无效的副本ID'..schemesMediaGain.Parameter)
			end
			--主线任务
		elseif schemesMediaGain.Type == 2 then
			UIManager:OpenWnd(WndID.MainTask)
			--大乱斗
		elseif schemesMediaGain.Type == 3 then
			for _,v in pairs(parameterList) do
				if schemesMediaGain.Parameter == v then
					ItemGainWay.LoadActivity(schemesMediaGain.Parameter )
				end
			end
		elseif schemesMediaGain.Type == 5 then
			--巡逻
			MiscDataCenter.BeginRunToFollowNpc()
		else
			error('获取途径失败 未定义的跳转类型'..schemesMediaGain.Type)
		end
	else
		local _,_,level = HelperL.CalculatePlayerLoopNum(levelLimite)
		HelperL.AddAMessageTip(string.format(GetGameText(luaID, 1),level))
		return
	end
end

function ItemGainWay.MakeGainWayText(lab, wayID, callBack, color)
	if tolua.isnull(lab) then
		return
	end
	color = color or "[00ff00]"
	local mediGainItem = Schemes.MediGain.Get(wayID)
	if mediGainItem then
		lab.text = color.."[u]"..mediGainItem.Text.."[/u]"
		UIEventListener.Get(lab.gameObject).onClick = function()
			if callBack then
				callBack()
			end
			ItemGainWay.EnterGainWay(wayID)
		end
	end
end

return ItemGainWay

