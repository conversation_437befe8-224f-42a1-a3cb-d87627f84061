--[[
   ********************************************************************
   created:	2016/09/09
   author :	张呈鹏
   purpose:	实体属性组件
   *********************************************************************
--]]
PropertyLC = {}

local function SetProperty(self, k, v)
	self.props[k] = v
end

local function GetProperty(self, k)
	return self.props[k]
end

local function GetFinalVocation(self)
	local genre = self:GetProperty(PLAYER_FIELD.PLAYER_FIELD_GENRE)
	if not genre or genre < GENRE.GENRE_NULL or genre >= GENRE.GENRE_MAX then
		genre = 0
	end
	return genre * 100 + self:GetProperty(PLAYER_FIELD.PLAYER_FIELD_VOCATION)
end

function PropertyLC.New()
	local o = {}
	o.props = {}
	o.SetProperty = SetProperty
	o.GetProperty = GetProperty
	o.GetFinalVocation = GetFinalVocation
	return o
end
