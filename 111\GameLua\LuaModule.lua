-- lua模块
require "LuaMessage_pb"

LuaModule = {}
LuaModule.Serial = 0
LuaModule.call_back_map_ = {}

function LuaModule.Handle(action, data)
	local self = LuaModule
	if action == LuaMessage_pb.MSG_LUA_RUNREQUEST then
		local m = LuaMessage_pb.SC_Lua_RunRequest()
		m:ParseFromString(data)
		if self.call_back_map_[m.Serial] ~= nil then
			local callback = self.call_back_map_[m.Serial]
			callback(m.Result,m.Content)
			self.call_back_map_[m.Serial] = nil
		end
	end
end

function LuaModule.RunLuaRequest(request, callback, isImmediately)
	print("发送lua请求", request)
	
	local self = LuaModule
	local m = LuaMessage_pb.CS_Lua_RunRequest()
	self.Serial = self.Serial + 1

	m.Serial = self.Serial
	m.NeedReply = (callback ~= nil)
	m.LuaRequest = request
	local data = Premier.Instance:GetNetwork():BuildSendData(
		ENDPOINT.ENDPOINT_GAMECLIENT,
		ENDPOINT.ENDPOINT_GAMESERVER,
		MSG_MODULEID.MSG_MODULEID_LUA,
		LuaMessage_pb.MSG_LUA_RUNREQUEST,
		m:SerializeToString()
    );
	if(isImmediately) then
		Premier.Instance:GetNetwork():SendImmediately(data);
	else
		Premier.Instance:GetNetwork():Send(data);
	end
    self.call_back_map_[self.Serial] = callback
	return data;
end

Premier.Instance:RegisterLuaModule(MSG_MODULEID.MSG_MODULEID_LUA, 'LuaModule.Handle')
