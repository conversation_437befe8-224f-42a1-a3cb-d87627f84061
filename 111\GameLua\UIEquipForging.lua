--[[
********************************************************************
    created:    2023/07/16
    author :    李锦剑
    purpose:    装备突变界面
*********************************************************************
--]]

local luaID = ('UIEquipForging')

local attribute = { 4, 5, 7, 9, }
--自动锁住的属性品质
local autoLockQuality = 7
---@class UIEquipForging:UIWndBase
local m = {}

-- 订阅事件列表
function m.GetOpenEventList()
    return {
        [EventID.LogicDataChange] = m.UpdateView,
        [EventID.OnHeroPropChange] = m.UpdateView,
        [EventID.OnSkepGoodsChange] = m.UpdateEquipCollectEntity,
    }
end

--预制体自适应
function m.AdaptScale()
    HelperL.AdaptScale_Width(m.objList.Item_AttributeBox)
    HelperL.AdaptScale(m.objList.Img_Bg1, 6)
    HelperL.AdaptScale(m.objList.Img_Bg2, 6)
end

--创建时事件
function m.OnCreate()
    if not SkepModule.GetEquipSkep() then return false end
    m.isLockList = {}
    m.adverID = 112
    m.equipItem = {}
    m.attributeButttonList = {}
    ---@type AttributeBox[] 属性框item集合
    m.attributeBoxList = {}
    ---@type table 自动突变复选框
    m.autoCheckboxList = {}
    m.equipCollectEntity = {}

    ---@type boolean 是自动突变
    m.isAutoCleanout = false
    m.objList.Obj_Guide.gameObject:SetActive(false)
    m.recommendEquipIndex = -1

    m.CreateEquip()
    m.RegisterClickEvent()
    m.CreateAutoWindow()
    --是否开启装备推荐
    m.isOpenRecommendGuide = m.GetRecommendEquip() ~= -1

    m.Item_Fight = FightAttribute.New()
    m.Item_Fight.Init(m.objList.Item_Fight)
    return true
end

--打开时
function m.OnOpen()
    m.objList.Obj_AutoWindow.gameObject:SetActive(false)
    m.objList.Obj_AdWindow.gameObject:SetActive(false)
    m.UpdateView()
    m.UpdateEquipCollectEntity()
    -- m.Guide()

    if m.recommendGuideTimer then
        m.recommendGuideTimer:Stop()
        m.recommendGuideTimer = nil
    end
    m.recommendGuideTimer = Timer.New(m.RecommendGuide, 0.5, 1)
    m.recommendGuideTimer:Start()
end

--关闭时
function m.OnClose()
    m.CancelAutoRequestRefine()
end

-- -- 新手引导
-- function m.Guide()
--     local heroLevel = EntityModule.hero:GetProperty(CREATURE_FIELD.CREATURE_FIELD_LEVEL)
--     local curStep = GuideManager:GetStep(GuideManager.EventType.EquipSophisticationGuide)
--     if heroLevel == WanOpenLevel[WndID.Equip] and curStep == 1 and not GuideManager.isRunning then
--         GuideManager.isRunning = true
--         local delayGuide = Timer.New(function()
--             GuideManager.StartGuide(GuideManager.EventType.EquipSophisticationGuide, 2)
--         end, 0.5, 1)
--         delayGuide:Start()
--     end
-- end

--注册点击事件
function m.RegisterClickEvent()
    --突变
    m.objList.Btn_Cleanout.onClick:AddListenerEx(function()
        if m.isAutoCleanout then
            m.CancelAutoRequestRefine()
        else
            m.RequestRefine()
        end
    end)
    --自动突变
    m.objList.Btn_AutoCleanout.onClick:AddListenerEx(function()
        m.IsPropShow = false
        if m.isAutoCleanout then
            m.CancelAutoRequestRefine()
        else
            m.objList.Obj_AutoWindow.gameObject:SetActive(true)
            m.objList.Img_Model.gameObject:SetActive(false)
        end
    end)
    m.objList.Btn_AutoHintOk.onClick:AddListenerEx(function()
        m.objList.Obj_AutoWindow.gameObject:SetActive(false)
        m.objList.Img_Model.gameObject:SetActive(true)
        m.isAutoCleanout = not m.isAutoCleanout
        if m.isAutoCleanout then
            m.objList.Txt_AutoHintOk.text = CommonTextID.CANCEL
            m.objList.Txt_AutoCleanout.text = CommonTextID.CANCEL
            m.objList.Txt_Cleanout.text = CommonTextID.CANCEL
            HelperL.ShowMessage(TipType.FlowText, GetGameText(luaID, 11))
            m.RequestRefine()
        else
            m.CancelAutoRequestRefine()
        end
    end)

    m.objList.Btn_Close2.onClick:AddListenerEx(function()
        m.objList.Obj_AutoWindow.gameObject:SetActive(false)
        m.objList.Img_Model.gameObject:SetActive(true)
    end)

    m.objList.Btn_Close.onClick:AddListenerEx(function()
        m:CloseSelf()
    end)

    m.objList.Btn_AdVideo.onClick:AddListenerEx(function()
        m.objList.Obj_AdWindow.gameObject:SetActive(true)
        m.UpdateAdWindow()
    end)
    m.objList.Btn_Close3.onClick:AddListenerEx(function()
        m.objList.Obj_AdWindow.gameObject:SetActive(false)
    end)

    m.objList.Btn_Summon.onClick:AddListenerEx(function()
        m.objList.Obj_AdWindow.gameObject:SetActive(false)
        if HelperL.IsNoAD() then
        else
            UIManager:OpenWnd(WndID.ADFree)
        end
    end)

    m.objList.Btn_WatchAd.onClick:AddListenerEx(function()
        m.objList.Obj_AdWindow.gameObject:SetActive(false)
        AdvertisementManager.ShowRewardAd(m.adverID)
    end)
end

--创建装备框
function m.CreateEquip()
    local skepEuip = SkepModule.GetEquipSkep()
    local parent
    for i, v in ipairs(EquipOrder) do
        if i < 5 then
            parent = m.objList.LSlots.transform
        else
            parent = m.objList.RSlots.transform
        end
        local o = m:CreateSubItem(parent, m.objList.Item_Equip)
        o.index = i
        o.Btn_Select.onClick:AddListenerEx(function()
            m.OnSelectedEquip(o.index)
        end)
        o.UpdateEntity = function(entity)
            if entity then
                local schemeItem = Schemes:GetGoodsConfig(entity:GetProperty(ENTITY_FIELD.ENTITY_FIELD_ID))
                o.Txt_Name.text = ''
                o.Txt_StarNum.text = '+' .. PropertyCompute.CalculateLevel(entity)
                o.Txt_Level.text = schemeItem.UseLevel .. CommonTextID.LEVEL
                AtlasManager:AsyncGetSprite(HelperL.GetImageByQuality(schemeItem.QualityLevel), o.Img_Bg)
                AtlasManager:AsyncGetSprite(schemeItem.IconID, o.Img_Icon)
                o.Img_Icon.gameObject:SetActive(true)
            else
                AtlasManager:AsyncGetSprite(HelperL.GetImageByQuality(0), o.Img_Bg)
                o.Txt_Name.text = GetEquipTypeDescribe(v)
                o.Txt_StarNum.text = ''
                o.Txt_Level.text = ''
                o.Img_Icon.gameObject:SetActive(false)
            end
        end
        o.UpdateEntity(EntityModule:GetEntity(skepEuip[v]))
        m.equipItem[i] = o
    end

    local o = m:CreateSubItem(m.objList.Obj_Slots, m.objList.Item_Equip)
    o.Btn_Select.onClick:AddListenerEx(function()
        SoundManager:PlaySound(1005)
        if o.GoodTip then
            HelperL.OnShowTips(o.GoodTip)
        end
    end)
    o.Txt_Name.gameObject:SetActive(false)
    o.UpdateEntity = function(entity)
        if entity then
            o.GoodTip = {}
            o.GoodTip.itemID = entity:GetProperty(ENTITY_FIELD.ENTITY_FIELD_ID)
            o.GoodTip.justShow = true
            local schemeItem = Schemes:GetGoodsConfig(o.GoodTip.itemID)
            o.Txt_StarNum.text = '+' .. PropertyCompute.CalculateLevel(entity)
            o.Txt_Level.text = schemeItem.UseLevel .. CommonTextID.LEVEL
            AtlasManager:AsyncGetSprite(HelperL.GetImageByQuality(schemeItem.QualityLevel), o.Img_Bg)
            AtlasManager:AsyncGetSprite(schemeItem.IconID, o.Img_Icon)
            o.Img_Icon.gameObject:SetActive(true)
        else
            o.GoodTip = nil
            AtlasManager:AsyncGetSprite(HelperL.GetImageByQuality(0), o.Img_Bg)
            o.Txt_StarNum.text = ''
            o.Txt_Level.text = ''
            o.Img_Icon.gameObject:SetActive(false)
        end
    end
    m.selectEntity = o

    m.OnSelectedEquip(1)
end

--创建角色模型
function m.UpdateUIModel(equipID)
    if not m.ui2DModel then
        m.ui2DModel = HelperL.CreateRenderTextureAndModel(m.objList.Img_Model)
    end
    m.ui2DModel:CreateUIModel3(equipID)
end

--初始化自动突变属性复选框
function m.CreateAutoWindow()
    local item, str, color
    for i, quality in ipairs(attribute) do
        item = {}
        item.com = m:CreateSubItem(m.objList.GridAutoHint, m.objList.Item_AutoCheckbox)
        item.com.Img_Checkbox.gameObject:SetActive(false)
        str = string.format(GetGameText(luaID, 17), ActorProp.GetQualityDesc(quality))
        item.com.Txt_Desc.text = string.format(ActorProp.GetQualityColor(quality, true), str)
        color = ActorProp.GetQualityColor2(quality)
        item.com.Img_Desc.color = color
        item.com.Btn_Checkbox:GetComponent("Image").color = color
        item.com.Btn_Checkbox.onClick:AddListenerEx(function()
            m.OnAutoCheckbox(i)
        end)
        table.insert(m.autoCheckboxList, item)
    end
    m.OnAutoCheckbox(1)
end

--自动突变属性复选框
function m.OnAutoCheckbox(index)
    if m.autoSelectedIndex == index then return end
    if m.autoSelectedIndex then
        m.autoCheckboxList[m.autoSelectedIndex].com.Img_Checkbox.gameObject:SetActive(false)
    end
    --自动突变--选择品质索引
    m.autoSelectedIndex = index
    m.autoCheckboxList[index].com.Img_Checkbox.gameObject:SetActive(true)
end

--装备选择事件
function m.OnSelectedEquip(index)
    if m.selectedIndex == index then return end
    if m.selectedIndex then
        m.equipItem[m.selectedIndex].Img_Select.gameObject:SetActive(false)
    end
    m.equipItem[index].Img_Select.gameObject:SetActive(true)
    ---@type integer 选中装备部位索引
    m.selectedIndex = index
    ---@type integer 选中装备部位类型
    m.selectedEquipOrder = EquipOrder[index]
    m.isLockList = {}
    m.UpdateSelectedEquip(1)
    m.RecommendGuide()
end

--更新属性按钮
function m.UpdateAttributeButtton()
    local skepEuip = SkepModule.GetEquipSkep()
    local entity = EntityModule:GetEntity(skepEuip[m.selectedEquipOrder])
    if entity then
        --获取已解锁属性数量
        local indexLock = 1
        local item, isUnlocked
        for i = 1, 10, 1 do
            if not m.attributeButttonList[i] then
                m.attributeButttonList[i] = m.CreateAttributeButtton(i)
            end
            isUnlocked = m.IsUnlocked(m.selectedEquipOrder, i)
            if isUnlocked then
                indexLock = i + 1
            end
            item = m.attributeButttonList[i]
            item.com.gameObject:SetActive(i <= indexLock)
            item.com.Img_Lock.gameObject:SetActive(not isUnlocked)
            item.com.Txt_Suit.gameObject:SetActive(isUnlocked)
        end
        m.objList.GridSuit:SetActive(true)
        m.objList.Scr_Euip:SetActive(true)
    else
        m.objList.GridSuit:SetActive(false)
        m.objList.Scr_Euip:SetActive(false)
    end
end

--判断是否解锁
function m.IsUnlocked(equipOrder, index)
    local bool = false
    if index > 1 then
        --当前组
        local num1 = m.GetQualityNum(equipOrder, index, 2)
        --未突变过
        if num1 == 0 then
            --上一组
            local num2 = m.GetQualityNum(equipOrder, index - 1, 5)
            local num3 = m.GetQualityNum(equipOrder, index - 1, 7)
            --两条S级和一条A级以上的属性
            if num2 >= 3 and num3 >= 2 then
                bool = true
            end
        else
            bool = true
        end
    else
        bool = true
    end
    return bool
end

--更新所选装备
function m.UpdateSelectedEquip(index)
    local skepEuip = SkepModule.GetEquipSkep()
    local entity = EntityModule:GetEntity(skepEuip[m.selectedEquipOrder])
    m.selectEntity.UpdateEntity(entity)
    if not entity then
        m.objList.GridEuip.gameObject:SetActive(false)
        m.objList.Btn_Cleanout.gameObject:SetActive(false)
        m.objList.Btn_AutoCleanout.gameObject:SetActive(false)
        m.objList.Txt_CleanoutHint.gameObject:SetActive(false)
        return
    end

    m.objList.GridEuip.gameObject:SetActive(true)
    m.objList.Btn_Cleanout.gameObject:SetActive(true)
    m.objList.Btn_AutoCleanout.gameObject:SetActive(true)
    m.objList.Txt_CleanoutHint.gameObject:SetActive(true)
    m.UpdateAttributeButtton()
    m.OnSelectedAttributeButtton(index)
end

--更新卡牌属性
function m.UpdateEquipCollectEntity()
    local cardSkep = SkepModule:GetSkepByID(SKEPID.SKEPID_MONSTERCARD)
    local typeList = Schemes.EquipCollect:GetByType(2)
    for _, v in ipairs(typeList) do
        m.equipCollectEntity[v.Order] = cardSkep:GetEntityByGoodsID(v.ID)
    end
end

--创建属性按钮
function m.CreateAttributeButtton(index)
    local item = {}
    item.index = index
    item.com = m:CreateSubItem(m.objList.GridSuit, m.objList.Item_AttributeButtton)
    item.com.Txt_Suit.text = index
    item.com.Btn_Suit.onClick:AddListenerEx(function()
        m.OnSelectedAttributeButtton(item.index)
    end)
    item.SetSelected = function(bool)
        item.com.Img_Suit.gameObject:SetActive(bool)
    end
    return item
end

---属性按钮选择事件
function m.OnSelectedAttributeButtton(index)
    if m.selectAttributeButton == index and m.selectedIndex == m.selectedEquipType2 then return end
    m.isLockList = {}
    -- local skepEuip = SkepModule.GetEquipSkep()
    -- local entity = EntityModule:GetEntity(skepEuip[m.selectedEquipOrder])
    if not m.IsUnlocked(m.selectedEquipOrder, index) then
        HelperL.ShowMessage(TipType.FlowText, GetGameText(luaID, 38))
        return
    end
    m.selectedEquipType2 = m.selectedIndex
    if m.selectAttributeButton then
        m.attributeButttonList[m.selectAttributeButton].SetSelected(false)
    end
    ---@type integer 选中属性按钮
    m.selectAttributeButton = index
    m.attributeButttonList[index].SetSelected(true)
    m.CancelAutoRequestRefine()
    m.UpdateView()

    for _, v in ipairs(m.attributeBoxList) do
        if v.isLock ~= true and v.data then
            --锁住>=S级品质的属性
            if v.data.Quality >= attribute[m.autoSelectedIndex or 1] or v.data.Quality >= autoLockQuality then
                m.LockAttribute(v.index)
            end
        end
    end
end

--更新界面
function m.UpdateView()
    local skepEuip = SkepModule.GetEquipSkep()
    -------------------更新角色模型------------------
    -- m.UpdateUIModel(HelperL.GetWeaponEquipID())

    -------------------更新装备实体------------------
    for i, v in ipairs(EquipOrder) do
        m.equipItem[i].UpdateEntity(EntityModule:GetEntity(skepEuip[v]))
    end

    --更新属性按钮
    m.UpdateAttributeButtton()

    local equipType = m.selectedEquipOrder
    local entity = EntityModule:GetEntity(skepEuip[equipType])
    ------------------装备属性组列表-------------------
    m.equipAttributeList = ActorProp.GetEquipAttributeList(entity)
    if m.equipAttributeList then
        local list = m.equipAttributeList[m.selectAttributeButton]
        if list then
            for i = 1, 5, 1 do
                if not m.attributeBoxList[i] then
                    m.attributeBoxList[i] = m.CreateAttributeBox(i)
                end
                m.attributeBoxList[i].UpdateData(list[i])
            end
            local autoNum = HeroDataManager:GetLogicData(LOGIC_DATA.DATA_AUTO_POLISHCOUNT)
            local color = (autoNum > 0) and UI_COLOR.White or UI_COLOR.Red
            m.objList.Txt_AutoHint.text = string.format(
                GetGameText(luaID, 16),
                color,
                autoNum
            )
            m.objList.Txt_Hint.text = string.format(
                GetGameText(luaID, 56),
                color,
                autoNum
            )
            m.objList.Btn_AdVideo.gameObject:SetActive(autoNum == 0)
        end
    end

    ------------------更新装备属性-------------------
    local str, content, value
    local equipProp = ActorProp.ReadEquipProp(m.equipCollectEntity[equipType])
    for k, v in pairs(equipProp.StarProp) do
        value = v + equipProp.StarExpProp[k]
        if value > 0 then
            if ActorProp.IsPercentum(k) then
                value = HelperL.Round(value / 100, 2) .. '%'
            end
            str = string.format('%s+%s', GetAttributeTypeDesc(ActorProp.GetEffectType(k)), value)
            if content == nil then
                content = str
            else
                content = content .. '      ' .. str
            end
        end
    end
    str = ''
    if content then
        str = string.format(GetGameText(luaID, 55), content)
    end
    m.objList.Txt_CleanoutHint.text = str
    m.objList.Txt_Attribute.text = string.format(GetGameText(luaID, 54), GetEquipTypeDescribe(equipType))

    local level = HeroDataManager:GetLogicByte(LOGIC_DATA.DATA_SOCIETY_ECTYPETOWER_HIGHSTAGE, 3)
    if level <= 0 then level = 1 end
    m.objList.Txt_AdTime.text = '+' .. HelperL.Split(Schemes.LotteryLevel:Get(1300 + level).Desc, ';')[1]

    ------------------更新消耗-------------------
    m.UpdateExpend()
end

--更新消耗
function m.UpdateExpend()
    local expend = m.GetExpend()
    AtlasManager:AsyncGetGoodsSprite(expend.sprite1, m.objList.Img_Expend)
    AtlasManager:AsyncGetGoodsSprite(expend.sprite2, m.objList.Img_Expend2)

    local level = HeroDataManager:GetLogicByte(LOGIC_DATA.DATA_SOCIETY_ECTYPETOWER_HIGHSTAGE, 3)
    if level <= 0 then level = 1 end
    local str = ''
    local cfg2 = Schemes.LotteryLevel:Get(1300 + level)
    if cfg2 then
        local descList = HelperL.Split(cfg2.Desc, ';')
        local ratio = 1 - (tonumber(descList[2]) or 1)
        if ratio > 0 then
            str = string.format('  <color=#FFA2FF>(-%s%%)</color>', ratio * 100)
        end
    end

    local color = '#109A84'
    if expend.amount1 < expend.expend1 then
        color = '#FF1600'
    end
    m.objList.Txt_Expend.text = string.format('<color=#109A84><color=%s>%s/%s</color></color>',
        color, expend.amount1, expend.expend1) .. str

    color = '#109A84'
    if expend.amount2 < expend.expend2 then
        color = '#FF1600'
    end
    m.objList.Txt_Expend2.text = string.format('<color=#109A84><color=%s>%s/%s</color></color>',
        color, expend.amount2, expend.expend2) .. str
end

--获取突变消耗
function m.GetExpend()
    local o = {
        expend1 = 0,
        amount1 = 0,
        sprite1 = nil,

        expend2 = 0,
        amount2 = 0,
        sprite2 = nil,
    }
    if m.selectAttributeButton and m.equipAttributeList then
        local cfg = Schemes.EquipEffect:Get(m.equipAttributeList[m.selectAttributeButton][1].EffectID)
        if cfg then
            local equipPolish = Schemes.EquipPolish:Get(cfg.GgoodsPolish)
            if equipPolish then
                local lockData, lockNum = m.GetLockDataAndNum()
                o.expend1 = equipPolish.Money
                o.amount1 = SkepModule:GetGoodsCount(9)
                o.sprite1 = 9
                o.expend2 = equipPolish.GoodNumber1 * lockNum
                o.amount2 = SkepModule:GetGoodsCount(equipPolish.GoodID1)
                o.sprite2 = equipPolish.GoodID1
            end
        end
    end
    return o
end

---创建属性框
---@param index integer
---@return AttributeBox
function m.CreateAttributeBox(index)
    ---@class AttributeBox
    local item = {}
    item.index = index
    item.com = m:CreateSubItem(m.objList.GridEuip, m.objList.Item_AttributeBox)
    ---@type fun(data: AttributeData)
    item.UpdateData = function(data)
        ---@type AttributeData
        item.data = data
        if data then
            item.isLock = m.isLockList[item.index]
            AtlasManager:AsyncGetSprite(item.isLock and 'suo_wks' or 'suo_ks', item.com.Img_Lock)
            item.com.Txt_Desc.text = data.Desc
            AtlasManager:AsyncGetSprite(m.GetQualityIconName(data.Quality), item.com.Img_IconBg)
            item.com.gameObject:SetActive(true)
        else
            item.isLock = false
            item.com.gameObject:SetActive(false)
        end
    end
    item.com.Btn_Lock.onClick:AddListenerEx(function()
        m.LockAttribute(item.index)
    end)
    return item
end

--获取品质图片名称
function m.GetQualityIconName(quality)
    if quality >= 0 and quality <= 1 then
        return 'yz_E'
    elseif quality == 2 then
        return 'yz_D'
    elseif quality == 3 then
        return 'yz_C'
    elseif quality == 4 then
        return 'yz_B'
    elseif quality >= 5 and quality <= 6 then
        return 'yz_A'
    elseif quality >= 7 and quality <= 8 then
        return 'yz_S'
    elseif quality >= 9 and quality <= 10 then
        return 'yz_SS'
    end
end

--锁住属性
function m.LockAttribute(index)
    local item = m.attributeBoxList[index]
    item.isLock = not item.isLock
    m.isLockList[index] = item.isLock
    AtlasManager:AsyncGetSprite(item.isLock and 'suo_wks' or 'suo_ks', item.com.Img_Lock)
    m.UpdateExpend()
end

---获取锁定属性和数量
---@return integer 锁定属性
---@return integer 数量
function m.GetLockDataAndNum()
    local lockData = 0
    local lockNum = 0
    for i = 1, 5, 1 do
        if m.isLockList[i] == true then
            lockData = Helper.SetBit(lockData, i)
            lockNum = lockNum + 1
        end
    end
    return lockData, lockNum
end

---判断购买条件
---@param lockNum integer 锁定的属性数量
---@return boolean
function m.JudgingBuyCondition(lockNum)
    local skepEuip = SkepModule.GetEquipSkep()
    local entity = EntityModule:GetEntity(skepEuip[m.selectedEquipOrder])
    if not entity then
        HelperL.ShowMessage(TipType.FlowText, GetGameText(luaID, 3))
        return false
    end

    --判断属性是否全部锁住
    local num = 0
    local list = m.equipAttributeList[m.selectAttributeButton]
    for k, v in pairs(list) do
        if v ~= nil then
            num = num + 1
        end
    end
    if lockNum >= num then
        HelperL.ShowMessage(TipType.FlowText, GetGameText(luaID, 4))
        return false
    end

    --读取突变消耗
    local cfg = Schemes.EquipEffect:Get(list[1].EffectID)
    if not cfg then return false end
    local equipPolish = Schemes.EquipPolish:Get(cfg.GgoodsPolish)
    if not equipPolish then return false end

    --判断功勋
    if HelperL.IsLackGoods(9, equipPolish.Money) then
        return false
    end

    --判断物品
    if HelperL.IsLackGoods(equipPolish.GoodID1, equipPolish.GoodNumber1 * lockNum) then
        return false
    end

    --自动突变次数
    local autoNum = HeroDataManager:GetLogicData(LOGIC_DATA.DATA_AUTO_POLISHCOUNT)
    if m.isAutoCleanout and autoNum <= 0 then
        HelperL.ShowMessage(TipType.FlowText, GetGameText(luaID, 19))
        return false
    end
    return true
end

---突变请求
function m.RequestRefine()
    if m.JudgeQualityLock() then
        ---@type NotarizeWindowsDatq
        local data = {
            type = NotarizeWindowsType.Windows3,
            content = string.format(GetGameText(luaID, 61), ActorProp.GetQualityDesc(autoLockQuality)),
            okCallback = m.RequestPolishEquip,
            cancelCallback = m.CancelAutoRequestRefine,
        }
        HelperL.NotarizeUI(data)
        return
    end
    m.RequestPolishEquip()
end

--发送突变请求
function m.RequestPolishEquip()
    local skepEuip = SkepModule.GetEquipSkep()
    local lockData, lockNum = m.GetLockDataAndNum()
    if m.JudgingBuyCondition(lockNum) then
        local entity = EntityModule:GetEntity(skepEuip[m.selectedEquipOrder])
        local str = "LuaRequestPolishEquip?equip=%s&index=%s&polishtype=%s&bindflag=%d&lock=%d&auto=%s"
        LuaModule.RunLuaRequest(
            string.format(str, entity.uid, m.selectAttributeButton, 0, 0, lockData,
                m.isAutoCleanout and 1 or 0),
            m.CallBackFunc
        )
    else
        m.CancelAutoRequestRefine()
    end
end

--取消自动突变
function m.CancelAutoRequestRefine()
    m.isAutoCleanout = false
    m.objList.Txt_AutoHintOk.text = CommonTextID.OK
    m.objList.Txt_AutoCleanout.text = GetGameText(luaID, 8)
    m.objList.Txt_Cleanout.text = GetGameText(luaID, 18)
end

---请求回调
---@param result integer
function m.CallBackFunc(result)
    local skepEuip = SkepModule.GetEquipSkep()
    if result == RESULT_CODE_BASE.RESULT_COMMON_SUCCEED[1] then
        local entity = EntityModule:GetEntity(skepEuip[m.selectedEquipOrder])
        --获取突变属性
        LuaModule.RunLuaRequest(
            string.format("LuaRequestPolishResult?equip=%s", entity.uid),
            m.CallBackAutoRequestRefine
        )
    else
        m.CancelAutoRequestRefine()
        ResultCode.ShowResultCodeCallback(result)
    end
end

---自动突变请求回调
function m.CallBackAutoRequestRefine(result)
    if result == RESULT_CODE_BASE.RESULT_COMMON_SUCCEED[1] then
        --更新界面
        m.UpdateView()
        --推荐装备引导
        m.RecommendGuide()

        --自动突变处理
        if not m.isAutoCleanout then return end
        local bool2 = false
        --判断突变到了想要的品质
        for _, v in ipairs(m.attributeBoxList) do
            if v.isLock ~= true and v.data then
                if v.data.Quality >= attribute[m.autoSelectedIndex] then
                    --突变到了想要的品质，并锁住属性
                    m.LockAttribute(v.index)
                    bool2 = true
                elseif v.data.Quality >= autoLockQuality then
                    --橙色及以上品质自动锁住属性
                    m.LockAttribute(v.index)
                end
            end
        end
        --洗到了想要的属性
        if bool2 then
            m.CancelAutoRequestRefine()
            return
        end

        -----------------继续自动突变---------------------
        if not m.requestRefineTimer then
            m.requestRefineTimer = Timer.New(function()
                m.requestRefineTimer:Stop()
                m.RequestRefine()
            end, 0.15, 1)
        end
        m.requestRefineTimer:Start()
    else
        m.CancelAutoRequestRefine()
        ResultCode.ShowResultCodeCallback(result)
    end
end

--更新看广告界面
function m.UpdateAdWindow()
    m.objList.Txt_AdHint1.text = GetGameText(luaID, 14)
    -- local isNoAD               = HelperL.IsNoAD()
    -- m.objList.Btn_Summon.gameObject:SetActive(not isNoAD)
    local level                = HeroDataManager:GetLogicByte(LOGIC_DATA.DATA_SOCIETY_ECTYPETOWER_HIGHSTAGE, 3)
    local exp                  = HeroDataManager:GetLogicData(LOGIC_DATA.DATA_WARBAND_ACTVCOUNTVALUESAVE3)
    if level <= 0 then level = 1 end
    local cfg = Schemes.LotteryLevel:Get(1300 + level)
    local str = ''
    if cfg then
        local descList = HelperL.Split(cfg.Desc, ';')
        local adNum = tonumber(descList[1]) or 0
        local ratio = (1 - (tonumber(descList[2]) or 0)) * 100
        str = string.format(GetGameText(luaID, 33), level, adNum, ratio .. '%', exp, cfg.LevelEXP)
    end
    m.objList.Txt_AdHint2.text = "X" .. level
    m.objList.Txt_AdHint3.text = str

    local state = HelperL.GetAdverState(m.adverID)
    m.objList.Btn_Ad2.gameObject:SetActive(state ~= 1)
    m.objList.Btn_WatchAd.gameObject:SetActive(state == 1)
end

--------------------------------------------------------------------
-- 推荐装备引导
--------------------------------------------------------------------
function m.RecommendGuide()
    if not m.isOpenRecommendGuide then return end
    --选中装备必须有一条蓝色品质以上的属性
    local bool = m.JudgeQuality(m.selectedIndex, 3)
    --获取没洗练过的装备索引
    local index = m.GetRecommendEquip()
    if index == -1 or not bool then
        m.isOpenRecommendGuide = index ~= -1
        m.recommendEquipIndex = -1
        m.objList.Obj_Guide.gameObject:SetActive(false)
        return
    end
    local item = m.equipItem[index]
    if not item then return end
    --推荐装备索引
    m.recommendEquipIndex = index
    m.objList.Obj_Guide.transform.position = item.transform.position
    m.objList.Obj_Guide.gameObject:SetActive(true)
end

--------------------------------------------------------------------
-- 获取推荐装备
--------------------------------------------------------------------
function m.GetRecommendEquip()
    --筛选未突变过的装备
    for index, v in ipairs(EquipOrder) do
        --全是白色品质的属性
        if not m.JudgeQuality(index, 1) then
            return index
        end
    end
    return -1
end

--------------------------------------------------------------------
-- 判断装备品质是否大于 quality
--------------------------------------------------------------------
function m.JudgeQuality(index, quality)
    local skepEuip = SkepModule.GetEquipSkep()
    local entity = EntityModule:GetEntity(skepEuip[EquipOrder[index]])
    if entity then
        local list = ActorProp.GetEquipAttributeList(entity)
        if list and list[1] then
            --判断是否有 quality 品质以上的属性
            for _, v in pairs(list[1]) do
                if v and v.Quality > quality then
                    return true
                end
            end
        end
    end
    return false
end

--------------------------------------------------------------------
-- 判断是否有橙色品质及以上的属性未锁定
--------------------------------------------------------------------
function m.JudgeQualityLock()
    local list = m.equipAttributeList[m.selectAttributeButton]
    if list then
        for i, v in pairs(list) do
            if v and v.Quality >= autoLockQuality and not m.isLockList[i] then
                return true
            end
        end
    end
    return false
end

--------------------------------------------------------------------
-- 获取大于等于指定品质的属性数量
--------------------------------------------------------------------
function m.GetQualityNum(equipOrder, index, quality)
    local num = 0
    local skepEuip = SkepModule.GetEquipSkep()
    local entity = EntityModule:GetEntity(skepEuip[equipOrder])
    if entity then
        local list = ActorProp.GetEquipAttributeList(entity)
        if list and list[index] then
            --判断是否有 quality 品质以上的属性
            for _, v in pairs(list[index]) do
                if v and v.Quality >= quality then
                    num = num + 1
                end
            end
        end
    end
    return num
end

return m
