--[[
********************************************************************
    created:	2024/06/19
    author :	李锦剑
    purpose:    Lua功能模块(客服端实现服务器某些功能)
*********************************************************************
--]]

--返回码
local code = RESULT_CODE_BASE

---Lua功能模块
---@type table<string,fun(parseData:ParseData)>
local m = {}

--------------------------------------------------------------------
---请求卡牌添加经验
---例子 "LuaRequestNewCardAddExp?card=%s&loopTimes=%s"
---参数 {equipID:integer(装备ID), loopTimes:integer(循环次数，用来实现多次升级)}
---@param parseData ParseData
--------------------------------------------------------------------
function m.LuaRequestNewCardAddExp(parseData)
    local log = "LuaRequestNewCardAddExp 请求"
    local equipID = parseData:GetRequestNumberParam("equipID")
    local loopTimes = parseData:GetRequestNumberParam("loopTimes")

    local equipment = Schemes.Equipment:Get(equipID)
    if not equipment then
        error(log .. '读取 Equipment 表失败 equipID=' .. equipID)
        parseData:Callback(code.RESULT_COMMON_FAILURE[1], '')
        return
    end
    local quality = equipment.Quality
    local starNum = equipment.StarNum
    local equipSmelt = Schemes.EquipSmelt:Get(equipment.SmeltID, quality, starNum)
    if not equipSmelt then
        parseData:Callback(code.RESULT_COMMON_FAILURE[1], '')
        return
    end

    if loopTimes < 1 then
        loopTimes = 1
    end

    local exp = LogicValue.GetEquipDataLV(equipID, 1)
    local value = (exp + equipSmelt.LevelExpRate) * loopTimes
    if value > equipSmelt.LevelMaxExp then
        value = equipSmelt.LevelMaxExp
    end

    local resultCode = code.RESULT_COMMON_SUCCEED[1]
    --保存逻辑值
    local bool = LogicValue.SetEquipDataLV(equipID, 1, value)
    if not bool then
        resultCode = code.RESULT_COMMON_FAILURE[1]
    end
    --操作成功
    parseData:Callback(resultCode, '')
end

--------------------------------------------------------------------
---请求保存副本进度
---例子 "LuaRequestSaveEctypeProgress?stageID=%s&isSuccess=%s&roundNo=%s"
---参数 {stageID:integer(副本ID), isSuccess:boolean(是否通关), roundNo:integer(刷怪波数)}
---@param parseData ParseData
--------------------------------------------------------------------
function m.LuaRequestSaveEctypeProgress(parseData)
    local log = "LuaRequestSaveEctypeProgress 请求"
    local stageID = parseData:GetRequestNumberParam("stageID")
    local isSuccess = parseData:GetRequestBooleanParam("isSuccess")
    local roundNo = parseData:GetRequestNumberParam("roundNo")

    local catMainStage = Schemes.CatMainStage:Get(stageID)
    if not catMainStage then
        error(log .. ' 读取 CatMainStage 表失败 stageID=' .. stageID)
        parseData:Callback(code.RESULT_COMMON_FAILURE[1], '')
        return
    end


    --获取刷怪波数
    local roundNoValue = LogicValue.GetEctypeBoxLV(stageID, EctypeBox_Index.MAX_RANK_NO)
    if roundNo > roundNoValue then
        --保存刷怪波数
        LogicValue.SetEctypeBoxLV(stageID, EctypeBox_Index.MAX_RANK_NO, roundNo)
    end

    --日常副本
    if catMainStage.FrontType == 6 then
        local missionInfo = Schemes.MissionInfo:Get(catMainStage.Map)
        local temp, _roundNo, _battleBrushEnemyID, _goldNum
        local battleBrushEnemyID = 0
        local strList = HelperL.Split(missionInfo.WaveInfo, "|")
        for i, str in ipairs(strList) do
            temp = HelperL.Split(str, ";")
            _roundNo = tonumber(temp[1]) or 0
            -- _goldNum = tonumber(temp[2]) or 0
            _battleBrushEnemyID = tonumber(temp[3]) or 0
            if _roundNo == roundNo then
                battleBrushEnemyID = _battleBrushEnemyID
                break
            end
        end

        local battleBrushEnemy = Schemes.BattleBrushEnemy:Get(battleBrushEnemyID)
        --保存每日积分
        if battleBrushEnemy and battleBrushEnemy.TurnSpeed ~= 0 then
            local value = LogicValue.GetEctypeBoxLV(stageID, EctypeBox_Index.EVERYDAY_ACCUMULATE_SCORE)
            value = value + battleBrushEnemy.TurnSpeed
            LogicValue.SetEctypeBoxLV(stageID, EctypeBox_Index.EVERYDAY_ACCUMULATE_SCORE, value)
        end
    end

    --通关才保存
    if isSuccess then
        --获取副本进度
        local value = LogicValue.GetEctypeStageLV(catMainStage.FrontType, EctypeStage_Index.MAX_STAGE_ID)
        if stageID > value then
            --保存副本进度
            local bool = LogicValue.SetEctypeStageLV(catMainStage.FrontType, EctypeStage_Index.MAX_STAGE_ID, stageID)
            if not bool then
                parseData:Callback(code.RESULT_COMMON_FAILURE[1], '')
                return
            end
        end
    end

    --操作成功
    parseData:Callback(code.RESULT_COMMON_SUCCEED[1], '')
end

--------------------------------------------------------------------
---请求领取副本宝箱奖励
---例子 "LuaRequestGetEctypeTreasureBoxRewards?stageID=%s&index=%s"
---参数 {stageID:integer(副本ID), index:integer(第几个宝箱)}
---@param parseData ParseData
--------------------------------------------------------------------
function m.LuaRequestGetEctypeTreasureBoxRewards(parseData)
    local log = "LuaRequestGetEctypeTreasureBoxRewards 请求"
    local stageID = parseData:GetRequestNumberParam("stageID")
    local index = parseData:GetRequestNumberParam("index")

    local catMainStage = Schemes.CatMainStage:Get(stageID)
    if not catMainStage then
        error(log .. '读取 CatMainStage 表失败 stageID=' .. stageID)
        parseData:Callback(code.RESULT_COMMON_FAILURE[1], '')
        return
    end

    if catMainStage.FrontType < 1 or catMainStage.FrontType > 14 then
        error(log .. '副本类型超出范围1-14 FrontType=' .. catMainStage.FrontType)
        parseData:Callback(code.RESULT_COMMON_FAILURE[1], '')
        return
    end

    --获取宝箱奖励和波数条件
    local strList = HelperL.Split(catMainStage.GeneralDrop, '|')
    local dataList = {}
    local temp, prizeID, condition
    for i, v in ipairs(strList) do
        temp = HelperL.Split(v, ';')
        condition = tonumber(temp[1]) or 0
        prizeID = tonumber(temp[2]) or 0
        table.insert(dataList, { PrizeID = prizeID, Condition = condition })
    end

    if index < 1 or index > #dataList then
        error(log .. '索引参数超出范围 index=' .. index)
        parseData:Callback(code.RESULT_COMMON_FAILURE[1], '')
        return
    end

    local value = LogicValue.GetEctypeBoxLV(stageID, EctypeBox_Index.TREASURE_LOGICAL_VALUE)
    local lv = LogicValue.GetBit(value, index)
    --判断已经领取
    if lv ~= 0 then
        parseData:Callback(code.RESULT_RECHARGECARD_HAVEGOT[1], '')
        return
    end

    local data = dataList[index]
    --主线副本
    if catMainStage.FrontType == 1 then
        --获取已通关副本ID
        local maxStageID = LogicValue.GetEctypeStageLV(catMainStage.FrontType, EctypeStage_Index.MAX_STAGE_ID)
        --获取最大波数
        local rankNo = LogicValue.GetEctypeBoxLV(stageID, EctypeBox_Index.MAX_RANK_NO)
        --判断是否可领取
        if maxStageID < stageID and rankNo < data.Condition then
            parseData:Callback(code.RESULT_TRAINING_EXPLOI_NOTCANPRIZE[1], '')
            return
        end
    end

    --日常副本
    if catMainStage.FrontType == 6 then
        --获取最大波数
        local score = LogicValue.GetEctypeBoxLV(stageID, EctypeBox_Index.EVERYDAY_ACCUMULATE_SCORE)
        --判断是否可领取
        if score < data.Condition then
            parseData:Callback(code.RESULT_TRAINING_EXPLOI_NOTCANPRIZE[1], '')
            return
        end
    end

    --请求领取副本宝箱奖励
    HelperL.GetDirectPrize(data.PrizeID, '0', function(resultCode, content)
        if resultCode == RESULT_CODE_BASE.RESULT_COMMON_SUCCEED[1] then
            --设置宝箱已领取
            local value2 = LogicValue.SetBit(value, index)
            local bool = LogicValue.SetEctypeBoxLV(stageID, EctypeBox_Index.TREASURE_LOGICAL_VALUE, value2)
            if not bool then
                parseData:Callback(code.RESULT_COMMON_FAILURE[1], '')
                return
            end
            --操作成功
            parseData:Callback(code.RESULT_COMMON_SUCCEED[1], '')
        else
            parseData:Callback(resultCode, content)
        end
    end)
end

--------------------------------------------------------------------
---请求卡牌添加经验
---例子 "LuaRequestNewCardAddExp2?smeltID=%s&loopTimes=%s"
---参数 {smeltID:integer(装备ID), loopTimes:integer(循环次数，用来实现多次升级)}
---@param parseData ParseData
--------------------------------------------------------------------
function m.LuaRequestNewCardAddExp2(parseData)
    local log = "LuaRequestNewCardAddExp2 请求"
    local smeltID = parseData:GetRequestNumberParam("smeltID")
    local loopTimes = parseData:GetRequestNumberParam("loopTimes")

    local upgradeLevel = LogicValue.GetEquipDataLV2(smeltID, 0)
    local equipSmelt = Schemes.EquipSmeltStar:GetEquipSmeltStarCfg(smeltID, upgradeLevel)
    if not equipSmelt then
        error(log .. '读取 EquipSmeltStar 表失败 stageID=' .. smeltID .. " upgradeLevel=" .. upgradeLevel)
        parseData:Callback(code.RESULT_COMMON_FAILURE[1], '')
        return
    end

    local equipSmeltList = Schemes.EquipSmeltStar:GetBySmeltID(smeltID)
    if not equipSmeltList then
        error(log .. '读取 EquipSmeltStar 表失败 stageID=' .. smeltID)
        parseData:Callback(code.RESULT_COMMON_FAILURE[1], '')
        return
    end

    --最后一个配置等级为最大等级
    local maxLevel = equipSmeltList[#equipSmeltList].StarLvl
    --判断是否满级
    if upgradeLevel >= maxLevel then
        parseData:Callback(code.RESULT_EQUIP_UPSTAR_FULLLEVEL[1], '')
        return
    end

    if loopTimes < 1 then
        loopTimes = 1
    end
    
    local exp = LogicValue.GetEquipDataLV2(smeltID, 1)
    local randExp = math.random(1, 3)
    local StarExpInc = equipSmelt.StarExpInc

    local endValue = 0
    print("smeltID =============== ",smeltID)
    print("equipSmelt.StarExpInc =============== ",equipSmelt.StarExpInc)
    if smeltID == 2 then
        if randExp >= -2 and randExp < 0 then
            endValue = randExp*equipSmelt.StarExpInc
            if math.abs(endValue) < -1 then
                endValue = -1
            end
            if exp < math.abs(endValue) then
                endValue = -exp
            end
        elseif randExp == 0 then
            endValue = 0
        elseif randExp > 0 and randExp <= 2 then
            endValue = randExp*equipSmelt.StarExpInc
            if math.abs(endValue) < 2 then
                endValue = 2
            end
        else    
            endValue = randExp*equipSmelt.StarExpInc
        end
    else
        endValue = equipSmelt.StarExpInc
    end
    

    local exValue = exp + endValue
    --奖励
    local goodInfo = '0'
    --消耗
    local costInfo = '0'
    local num
    if equipSmelt.CostGoodsID1 > 0 and equipSmelt.CostGoodsID1Num > 0 then
        num = math.floor(equipSmelt.CostGoodsID1Num * loopTimes)
        costInfo = string.format("%s;%s", equipSmelt.CostGoodsID1, num)
    end
    if equipSmelt.CostGoodsID2 > 0 and equipSmelt.CostGoodsID2Num > 0 then
        num = math.floor(equipSmelt.CostGoodsID2Num * loopTimes)
        if costInfo == '0' then
            costInfo = string.format("%s;%s", equipSmelt.CostGoodsID2, num)
        else
            costInfo = string.format("%s|%s;%s", costInfo, equipSmelt.CostGoodsID2, num)
        end
    end

    --扣消耗
    HelperL.RequestDirectGiveGoodsy(goodInfo, costInfo, function(resultCode, content)
        --扣消耗--失败
        if resultCode ~= code.RESULT_COMMON_SUCCEED[1] then
            parseData:Callback(resultCode, '')
            return
        end

        --保存逻辑值
        local bool = LogicValue.SetEquipDataLV2(smeltID, 1, exValue)
        if not bool then
            parseData:Callback(code.RESULT_COMMON_FAILURE[1], '')
            return
        end

        local level = 0
        for i = upgradeLevel, #equipSmeltList, 1 do
            if equipSmeltList[i] then
                --判断所需经验值
                if exValue >= equipSmeltList[i].StarExp then
                    level = equipSmeltList[i].StarLvl
                else
                    break
                end
            end
        end
        if level > upgradeLevel then
            LogicValue.SetEquipDataLV2(smeltID, 0, level)
        end
        --操作成功
        parseData:Callback(resultCode, randExp..':'..endValue)
    end, false)
end

--------------------------------------------------------------------
---请求领取签到奖励
---例子 "LuaRequestGetPrizesevenSignin?SigninID=%s"
---参数 {SigninID:integer(PrizesevenSignin.csv表ID)}
---@param parseData ParseData
--------------------------------------------------------------------
function m.LuaRequestGetPrizesevenSignin(parseData)
    local log = "LuaRequestGetPrizesevenSignin 请求"
    local SigninID = parseData:GetRequestNumberParam("SigninID")
    local cfg = Schemes.PrizesevenSignin:Get(SigninID)
    if not cfg then
        error(log .. '读取 PrizesevenSignin 表失败 SigninID=' .. SigninID)
        parseData:Callback(code.RESULT_COMMON_FAILURE[1], '')
        return
    end

    --判断是否已领取
    -- local lv = LogicValue.GetIntByIndex(cfg.ActorDataCatalog, cfg.Sort)
    -- if lv ~= 0 then
    --     parseData:Callback(code.RESULT_RECHARGECARD_HAVEGOT[1], '')
    --     return
    -- end

    local time = HelperL.GetServerTime()
    local signInNumber = HeroDataManager:GetLogicData(LOGIC_DATA.DATA_WEEK_SIGNIN_BITS)

    local signInTime = HeroDataManager:GetLogicData(LOGIC_DATA.DATA_HORSE_HAVEGET)
    --签到次数
    --local signInNumber = LogicValue.GetSignInData(SignInData_Index.SignInNumber)
    --累计签到次数
    --local signInCumulativeNumber = LogicValue.GetSignInData(SignInData_Index.SignInCumulativeNumber)
    --签到时间
    --local signInTime = LogicValue.GetSignInData(SignInData_Index.SignInTime)

    if cfg.PrizeType == 1 then
        --判断今日是否已签到
        if HelperL.is_same_day(time, signInTime) then
            --parseData:Callback(code.RESULT_SIGNED_IN[1], '')
            return
        end
        -- if cfg.ReceivePremiss == 1 and signInNumber < cfg.ReceivePremissParam then
        --     parseData:Callback(code.RESULT_NOT_SATISFIED_RECEIVE_CONDITION[1], '')
        --     return
        -- end
    elseif cfg.PrizeType == 2 then
        if cfg.ReceivePremiss == 2 and signInCumulativeNumber < cfg.ReceivePremissParam then
            parseData:Callback(code.RESULT_NOT_SATISFIED_RECEIVE_CONDITION[1], '')
            return
        end
    end

    --请求领取副本宝箱奖励
    HelperL.GetDirectPrize(cfg.PrizeId, '0', function(resultCode, content)
        if resultCode == RESULT_CODE_BASE.RESULT_COMMON_SUCCEED[1] then
            --设置宝箱已领取
            -- local bool = LogicValue.SetIntByIndex(cfg.ActorDataCatalog, cfg.Sort, 1)
            -- if not bool then
            --     parseData:Callback(code.RESULT_COMMON_FAILURE[1], '')
            --     return
            -- end

            if cfg.PrizeType == 1 then
                --增加签到次数
                --LogicValue.SetSigninData(SignInData_Index.SignInNumber, signInNumber + 1)
                --增加累计签到次数
                --LogicValue.SetSigninData(SignInData_Index.SignInCumulativeNumber, signInCumulativeNumber + 1)
                --重置签到时间
                --LogicValue.SetSigninData(SignInData_Index.SignInTime, time)

                local str_req1 = string.format('LuaRequestSetLogicData?logicID=%d&logicValue=%d', LOGIC_DATA.DATA_WEEK_SIGNIN_BITS,signInNumber + 1)
                LuaModule.RunLuaRequest(str_req1, function(resultCode2, content2)
                    print("resultCode2 = ", resultCode2)
                    if resultCode2 == RESULT_CODE_BASE.RESULT_COMMON_SUCCEED[1] then
                    end
                end)

                local str_req2 = string.format('LuaRequestSetLogicData?logicID=%d&logicValue=%d', LOGIC_DATA.DATA_HORSE_HAVEGET,time)
                LuaModule.RunLuaRequest(str_req2, function(resultCode2, content2)
                    print("resultCode2 = ", resultCode2)
                    if resultCode2 == RESULT_CODE_BASE.RESULT_COMMON_SUCCEED[1] then
                    end
                end)

            elseif cfg.PrizeType == 2 then
                -- local maxSort = LogicValue.GetSignInData(SignInData_Index.SignInCumulativeMaxSort)
                -- --重置累计签到奖励，已领取最大序号
                -- if cfg.Sort > maxSort then
                --     LogicValue.SetSigninData(SignInData_Index.SignInCumulativeMaxSort, cfg.Sort)
                -- end
            end

            --操作成功
            parseData:Callback(code.RESULT_COMMON_SUCCEED[1], '')
        else
            parseData:Callback(resultCode, content)
        end
    end)
end

--------------------------------------------------------------------
---请求领取任务奖励
---例子 "LuaRequestGetTaskAward?taskID=%s"
---参数 {taskID:integer(任务ID)}
---@param parseData ParseData
--------------------------------------------------------------------
function m.LuaRequestGetTaskAward(parseData)
    local log = "LuaRequestGetTaskAward 请求"
    local taskID = parseData:GetRequestNumberParam("taskID")
    local taskCfg = Schemes.Task:Get(taskID)
    if not taskCfg then
        error(log .. '读取 Task 表失败 taskID=' .. taskID)
        parseData:Callback(code.RESULT_COMMON_FAILURE[1], '')
        return
    end

    local jd = GamePlayerData.ActorTask:GetTaskProgress(taskID)
    if jd < taskCfg.Parameter2 then
        parseData:Callback(code.RESULT_TASK_PRETASK[1], '')
        return
    end

    local prizeId = taskCfg.PrizeID
    local costInfo = "0"
    HelperL.GetDirectPrize(prizeId, costInfo, function(resultCode, content)
        --操作失败
        if resultCode ~= RESULT_CODE_BASE.RESULT_COMMON_SUCCEED[1] then
            parseData:Callback(resultCode, content)
            return
        end

        --操作成功
        LogicValue.SetTaskDataLV(taskID, TaskData_Index.FinishID, taskID)
    end,false)
end

return m
