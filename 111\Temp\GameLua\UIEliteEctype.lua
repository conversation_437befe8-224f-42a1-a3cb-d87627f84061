--[[
********************************************************************
    created:    2024/07/23
    author :    gao
    purpose:    雪域荒原
*********************************************************************
--]]

local luaID = ('UIEliteEctype')

---首领副本
---@class UIEliteEctype: UIWndBase
local m = {}

--副本类型
m.ectypeType = Elite_EctypeType

--------------------------------------------------------------------
-- 订阅事件列表
--------------------------------------------------------------------
function m.GetOpenEventList()
    return {
        [EventID.LogicDataChange] = m.UpdateView,
        [EventID.EntitySyncBuff] = m.UpdateView,
        [EventID.OnGoodsPropChange] = m.UpdateView,
        [EventID.OnHeroPropChange] = m.UpdateView,
        [EventID.ActorDataChanged] = m.UpdateView,
    }
end

--------------------------------------------------------------------
-- 创建时
--------------------------------------------------------------------
function m.OnCreate()
    -- m.objList.Txt_Headline.text = GetGameText(luaID, 1)
    --m.objList.Txt_Content.text = GetGameText(luaID, 6)
    m.slotItem_List = {}

    m.catMainStageList = Schemes.CatMainStage:GetByFrontType(m.ectypeType)
    ---@type Item_Ectype2[]
    m.Item_Ectype_List = {}
    for i = 1, 4 do
    --for i, v in ipairs(m.catMainStageList) do
        m.Item_Ectype_List[i] = m.Creation_Item_Ectype(m.objList.Grid_Ectype, i)
        m.Item_Ectype_List[i].UpdateView(i)
    end



    -- m.comResInfo = ComResInfo.New()
    -- m.comResInfo.BindView(m.objList.Img_Res)

    m.RegisterClickEvent()
    return true
end

--------------------------------------------------------------------
--打开时
--------------------------------------------------------------------
function m.OnOpen()
    m.Select_Item_Ectype(m.selectIndex or 1)
end

--------------------------------------------------------------------
--注册点击事件
--------------------------------------------------------------------
function m.RegisterClickEvent()
    m:AddClick(m.objList.Btn_Close, function()
        m:CloseSelf()
    end)
end

--------------------------------------------------------------------
--排序
---@param a CatMainStageCfg
---@param b CatMainStageCfg
---@return boolean
--------------------------------------------------------------------
function m.Sort(a, b)
    return a.ID < b.ID
end

--------------------------------------------------------------------
--更新界面
--------------------------------------------------------------------
function m.UpdateView()
    -- table.sort(m.catMainStageList, m.Sort)
    for i = 1, 4 do
        m.Item_Ectype_List[i].UpdateView(i)
    end
    local stageID = GamePlayerData.GameEctype:GetChallengeableID(m.ectypeType)
    m.selectIndex = stageID % 100
    if m.selectIndex < 1 then
        m.selectIndex = 1
    end
    local positionInGroup = (m.selectIndex - 1) % 4 + 1
    m.selectIndex = positionInGroup
    if m.selectIndex then
        local cfg = m.Item_Ectype_List[m.selectIndex].cfg
        local expendList = HelperL.Split(cfg.Need, ';')
        --消耗物品ID
        m.expID = tonumber(expendList[1]) or 0
        --消耗物品数量
        m.expNum = tonumber(expendList[2]) or 0
        --广告物品ID
        -- local adID = tonumber(expendList[3]) or 0
        --消耗物品ID2
        m.expID2 = tonumber(expendList[4]) or 0
        --消耗物品数量2
        m.expNum2 = tonumber(expendList[5]) or 0

        if m.expID > 0 then
            local count1 = SkepModule:GetGoodsCount(m.expID)
            m.objList.Img_ChallengeGray.gameObject:SetActive(count1 < m.expNum)
            m.objList.Txt_Num.text = string.format(GetGameText(luaID, 3), count1)
            m.objList.Txt_Num.gameObject:SetActive(true)
        else
            m.objList.Txt_Num.gameObject:SetActive(false)
        end

        local list = HelperL.Split(cfg.Desc2, "\\n")
        str = ""
        for i = 1, #list do
            if i < #list then
                str = str .. list[i] .. "\n"
            else
                str = str .. list[i]
            end
        end
        m.objList.Txt_Content.text = str
        if m.expID2 > 0 then
            local count2 = SkepModule:GetGoodsCount(m.expID2)
            local color2 = count2 < m.expNum2 and "#FFA500" or "#ffffff"
            m.objList.Txt_Expend.text = string.format("<color=%s>%s/%s</color>", color2, count2, m.expNum2)
            AtlasManager:AsyncGetGoodsSprite(m.expID2, m.objList.Img_Expend)
            m.objList.Txt_Expend.gameObject:SetActive(true)
        else
            m.objList.Txt_Expend.gameObject:SetActive(false)
        end

        local Icon = "zjm_xltb"
        if cfg.Icon ~= "0" and cfg.Icon ~= "" then
            Icon = cfg.Icon
        end
        AtlasManager:AsyncGetSprite(Icon, m.objList.Img_EctypeIcon)

        --if cfg.ID ~= m.lastStageID then
            m.lastStageID = cfg.ID
            local prizeGoods = Schemes.CatMainStage:GetSucceedRewards(cfg.ID)
            local num = math.max(#prizeGoods, #m.slotItem_List)
            for i = 1, num, 1 do
                if not m.slotItem_List[i] then
                    m.slotItem_List[i] = _GAddSlotItem(m.objList.Grid_Goods)
                end
                if prizeGoods[i] then
                    m.slotItem_List[i]:SetItemID(prizeGoods[i].ID)
                    m.slotItem_List[i]:SetCount(prizeGoods[i].Num)
                    m.slotItem_List[i]:SetActive(true)
                else
                    m.slotItem_List[i]:SetActive(false)
                end
            end
        --end
    end
    m.objList.Btn_Challenge.onClick:RemoveAllListeners()
    local mStage = GamePlayerData.GameEctype:GetProgress(m.ectypeType)
    local stageID = GamePlayerData.GameEctype:GetChallengeableID(m.ectypeType)
    m.objList.Txt_Level.text = string.format(GetGameText(luaID, 7), stageID - 150000)
    if mStage % 100 == #m.catMainStageList then
        m.objList.Txt_TG.gameObject:SetActive(true)
        m.objList.Btn_Challenge.gameObject:SetActive(false)
    else
        m.objList.Txt_TG.gameObject:SetActive(false)
        m.objList.Btn_Challenge.gameObject:SetActive(true)
        m.objList.Btn_Challenge.onClick:AddListenerEx(function()
            m.ChuZheng()
        end)
    end
end

--------------------------------------------------------------------
-- 选择副本栏
--------------------------------------------------------------------
function m.Select_Item_Ectype(index)
    local item
    if m.selectIndex then
        item = m.Item_Ectype_List[m.selectIndex]
        item.com.Img_Bg1.gameObject:SetActive(true)
        item.com.Img_Bg2.gameObject:SetActive(false)
    end
    m.selectIndex = index
    item = m.Item_Ectype_List[index]
    item.com.Img_Bg1.gameObject:SetActive(false)
    item.com.Img_Bg2.gameObject:SetActive(true)
    m.selectCatMainStageID = item.cfg.ID
    m.UpdateView()
end

--------------------------------------------------------------------
-- 创建副本栏
--------------------------------------------------------------------
function m.Creation_Item_Ectype(parent, index)
    ---@class Item_Ectype2
    local item = {}
    item.index = index
    item.com = m:CreateSubItem(parent, m.objList.Item_Ectype)
    -- m:AddClick(item.com.Btn_Click, function()
    --     local stageID = GamePlayerData.GameEctype:GetChallengeableID(m.ectypeType)
    --     if item.cfg.ID > stageID then
    --         HelperL.ShowMessage(TipType.FlowText, string.format(GetGameText(luaID, 5), (item.cfg.ID % 10000) - 1))
    --         return
    --     end
    --     m.Select_Item_Ectype(item.index)
    -- end)

    ---更新界面
    item.UpdateView = function(i)
        local maxStage = GamePlayerData.GameEctype:GetProgress(m.ectypeType)
        if maxStage ~= 0 then
            maxStage = (maxStage) % 10000
            if maxStage >= #m.catMainStageList then
                maxStage = #m.catMainStageList
            end
        else
            maxStage = 0
        end
        local pagesize = 4
        local gapNum = math.floor(maxStage/pagesize)
        local startPos = gapNum * pagesize + 1
        local endPos = gapNum * pagesize + pagesize
        
        if endPos > #m.catMainStageList then
            endPos = #m.catMainStageList
            startPos = endPos - pagesize - 1
        end
        item.stageId = startPos + index - 1
        local cfg = m.catMainStageList[item.stageId]
        item.cfg = cfg
        if cfg then
            item.com.Txt_Title1.text = cfg.Name
            item.com.Txt_Title2.text = cfg.Name
            item.com.Txt_Title3.text = cfg.Name

            item.com.Img_Lock.gameObject:SetActive(false)
            local stageID = GamePlayerData.GameEctype:GetChallengeableID(m.ectypeType)
            --print("stageID ==== ",stageID)
            if item.cfg.ID > stageID then
                item.com.Img_Lock.gameObject:SetActive(true)
                item.com.Img_Bg1.gameObject:SetActive(true)
                item.com.Img_Bg2.gameObject:SetActive(false)
            else
                if stageID == item.cfg.ID then
                    item.com.Img_Lock.gameObject:SetActive(false)
                    item.com.Img_Bg1.gameObject:SetActive(false)
                    item.com.Img_Bg2.gameObject:SetActive(true)
                    item.com.Txt_Title2.gameObject:SetActive(true)
                    item.com.Txt_Title3.gameObject:SetActive(false)
                    item.com.Txt_TG.gameObject:SetActive(false)
                else
                    item.com.Img_Lock.gameObject:SetActive(false)
                    item.com.Img_Bg1.gameObject:SetActive(false)
                    item.com.Img_Bg2.gameObject:SetActive(true)
                    item.com.Txt_Title2.gameObject:SetActive(false)
                    item.com.Txt_Title3.gameObject:SetActive(true)
                    item.com.Txt_TG.gameObject:SetActive(true)
                end
            end

            item.com.gameObject:SetActive(true)
        else
            item.com.gameObject:SetActive(false)
        end
    end

    return item
end

--------------------------------------------------------------------
-- 出征
--------------------------------------------------------------------
function m.ChuZheng()
    local stageID = GamePlayerData.GameEctype:GetChallengeableID(m.ectypeType)

    local cfg = Schemes.CatMainStage:Get(stageID)
    if not cfg then
        return
    end

    local expendList = HelperL.Split(cfg.Need, ';')
    --消耗物品ID
    local expID = tonumber(expendList[1]) or 0
    --消耗物品数量
    local expNum = tonumber(expendList[2]) or 0
    --广告物品ID
    -- local adID = tonumber(expendList[3])
    --消耗物品ID2
    local expID2 = tonumber(expendList[4]) or 0
    --消耗物品数量2
    local expNum2 = tonumber(expendList[5]) or 0

    --判断挑战次数
    if HelperL.IsLackGoods(expID, expNum, false, false) then
        HelperL.ShowMessage(TipType.FlowText, GetGameText(luaID, 4))
        return
    end
    --消耗判断
    if HelperL.IsLackGoods(expID2, expNum2, false) then
        return
    end

    --m:CloseSelf()
    -- -- 进入战斗
    BattleManager:EnterBattle(cfg.ID)
end

return m
