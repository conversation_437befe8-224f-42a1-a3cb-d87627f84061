-- 塔防单个技能物件
local luaID = ('UISingleGoods')

-- 初始化物件
local itemPrefab = nil
local function InitSingleGoodsItem(self, parent)
	if not itemPrefab then
		itemPrefab = HotResManager.ReadUI('ui/Main/SingleGoods')
		if not itemPrefab then
			warn('InitSingleGoodsItem 找不到物品图标预制体')
			return false
		end
	end

	if tolua.isnull(parent) then
		warn('InitSingleGoodsItem 未提供父节点')
		return false
	end

	local itemObj = GameObject.Instantiate(itemPrefab.gameObject, parent.transform)
	local itemTrans = itemObj.transform
	local objList = {}
	Helper.FillLuaComps(itemTrans, objList)
	self.gameObject = itemObj
	self.transform = itemTrans
	self.objList = objList

	self.rectTrans = itemObj:GetRectTransform()
	self.transContent = objList.Rct_Content
	self.objList.Txt_Num.gameObject:SetActive(false)
	self.isValid = true
	self.showName = true
	self.isNum = false
	self.enableClick = true
	self.justShow = true
	self.transform:GetComponent("Button").onClick:AddListenerEx(function()
		--物品点击,显示物品查看界面
		-- --特殊处理，如果是特惠礼包界面，就不弹窗，飘字就行
		-- if UIManager:IsWndOpen(WndID.DiscountGift) then
		-- 	HelperL.ShowMessage(TipType.FlowText, self.config.GoodsName)
		-- end
		if self.itemID and self.enableClick then
			HelperL.OnShowTips(self)
		end
	end)

	return true
end

-- 设置物件数据
local function SetItemData(self, goodsID, goodsNum)
	local config = Schemes:GetGoodsConfig(goodsID)
	self.config = config
	self.itemID = goodsID

	if config then
		local quality = config.Quality > 0 and config.Quality or config.QualityLevel
		AtlasManager:AsyncGetSprite(HelperL.GetImageByQuality(quality), self.objList.Img_BG)
		AtlasManager:AsyncGetSprite(config.IconID, self.objList.Img_Icon)

		if not goodsNum then
			goodsNum = 1
		end
		local colorStr = HelperL.GetQualityColor(quality)
		self.objList.Txt_Name.text = string.format("<color=#%s>%s</color>", colorStr, config.GoodsName)
		self.objList.Txt_Num.text = HelperL.GetChangeNum(goodsNum)
		HelperL.SetImageMaterial(self.objList.Img_vfx, quality)
	else
		self.objList.Img_BG.sprite = nil
		self.objList.Img_Icon.sprite = nil
		self.objList.Txt_Name.text = ''
		self.objList.Txt_Num.text = ''
	end
end

-- 设置显示名字
local function SetShowName(self, isShow)
	if self.showName == isShow then
		return
	end
	self.showName = isShow
	self.objList.Txt_Name.gameObject:SetActive(isShow)
end

-- 设置名字字体大小
local function SetNameFontSize(self, size)
	self.objList.Txt_Name.fontSize = size
end

-- 设置显示名字
local function SetShowNum(self, isShow)
	if self.isNum == isShow then
		return
	end
	self.isNum = isShow
	self.objList.Txt_Num.gameObject:SetActive(isShow)
end

-- 设置物件是否可用
local function SetItemValid(self, isValid)
	if self.isValid == isValid then
		return
	end
	HelperL.SetImageGray(self.objList.Img_BG, not isValid)
	HelperL.SetImageGray(self.objList.Img_Icon, not isValid)
	self.isValid = isValid
end

-- 获取物件是否可用
local function IsItemValid(self)
	return self.isValid
end

-- 设置是否显示
local function SetVisible(self, isVisible)
	if self.gameObject.activeSelf ~= isVisible then
		self.gameObject:SetActive(isVisible)
	end
end

local function SetEnableClick(self, enable)
	self.enableClick = enable
end

-- 设置大小
local function SetSize(self, width, height)
	local layout = self.gameObject:GetComponent('LayoutElement')
	if layout then
		layout.preferredWidth = width
		layout.preferredHeight = height
	end
	self.rectTrans.sizeDelta = Vector2(width, height)
	--local vfx_trans = self.objList.Img_vfx:GetComponent('RectTransform')
	--vfx_trans.sizeDelta = Vector2(width+40, height+40)
end
-- 背景显示/隐藏
local function SetActiveImg_BG(self, isShow)
	if self.ImgBG == isShow then
		return
	end
	self.ImgBG = isShow
	self.objList.Img_BG.gameObject:SetActive(isShow)
end

function CreateSingleGoods(parent)
	---@class CreateSingleGoods
	local item = {}
	item.Init = InitSingleGoodsItem
	item.SetItemData = SetItemData
	item.SetItemValid = SetItemValid
	item.IsItemValid = IsItemValid
	item.SetVisible = SetVisible
	item.SetShowName = SetShowName
	item.SetShowNum = SetShowNum
	item.SetNameFontSize = SetNameFontSize
	item.SetSize = SetSize
	item.SetEnableClick = SetEnableClick
	item.SetActiveImg_BG = SetActiveImg_BG
	if not item:Init(parent) then
		warn('CreateSingleGoods 初始化失败')
		return nil
	end

	return item
end
