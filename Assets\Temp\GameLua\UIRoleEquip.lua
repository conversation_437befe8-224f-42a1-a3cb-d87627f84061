--[[
********************************************************************
    created:	2024/05/17
    purpose:    界面
*********************************************************************
--]]

local luaID = 'UIRoleEquip'

---@class UIRoleEquip:UIWndBase
local m = {}

--角色框
---@type Item_Role[]
m.Item_Role_List = {}
--------------------------------------------------------------------
-- 订阅事件列表
--------------------------------------------------------------------
function m.GetOpenEventList()
    return {
        [EventID.StoreList] = m.UpdateView,
        [EventID.StoreBuyItem] = m.UpdateView,
        [EventID.LogicDataChange] = m.UpdateView,
        [EventID.OnGoodsPropChange] = m.UpdateView,
        [EventID.OnHeroPropChange] = m.UpdateView,
        [EventID.ActorDataChanged] = m.UpdateView,
    }
end

--------------------------------------------------------------------
-- 初始化
--------------------------------------------------------------------
function m.OnCreate()
    ---按钮
    ---@type Item_Tab[]
    m.Item_Tab_List = {}

    m.curIndex = 1
    m.lastTime = 0
    --体魄ID
    ---@type integer[]
    m.EquipWeaponIDList = {}
    for i, v in ipairs(Schemes.EquipWeapon.items) do
        if v.GroupID < 1000 then -- 
            table.insert(m.EquipWeaponIDList, v.ID)
        end
    end

    for i = 1, #m.EquipWeaponIDList, 1 do
        m.Item_Role_List[i] = m.Creation_Item_Role(i)
    end
    

    m.equipID = m.EquipWeaponIDList[m.curIndex]
    m.curEquipID = m.equipID
    m.equipConfig = Schemes.Equipment:Get(m.equipID)

    m.XLSmeltID = m.equipConfig.SmeltBase
    m.XLSmeltList = Schemes.EquipSmeltStar:GetBySmeltID(m.XLSmeltID) or {}
    
    m.RegisterClickEvent()
    
    return true
end

--------------------------------------------------------------------
-- 窗口开启
--------------------------------------------------------------------
function m:OnOpen()
    -- 保存Obj_Model的初始位置
    if not m.objModelOriginalPos then
        m.objModelOriginalPos = Vector2(m.objList.Obj_Model.transform.localPosition.x, m.objList.Obj_Model.transform.localPosition.y)
    end
    
    m.UpdateRole()
    m.SelectBtnState(1)
    m.UpdateUIModel()
    m.UpdateView()
end

--------------------------------------------------------------------
--更新角色模型
--------------------------------------------------------------------
function m.UpdateUIModel()
    local cfg = Schemes.Equipment:Get(m.equipID)
    --暂时屏蔽    
    local gun = Schemes.Gun:Get(cfg.ConsignmentStyle)
    local bullet_List = Schemes.Bullet:GetByBulletId(gun.BulletId)
    local trans = m.objList.Obj_Model.transform
	for c = trans.childCount - 1, 0, -1 do
		if trans:GetChild(c) then
			GameObject.Destroy(trans:GetChild(c).gameObject)
		end
	end
    local parameter
    ResMgr.LoadGameObjectAsync("model/uiSpine/"..bullet_List[1].Model, function (obj, parameter)
        local model = GameObject.Instantiate(obj, m.objList.Obj_Model.transform)
        
        -- 停止之前可能存在的动画
        if m.floatAnimation then
            m.floatAnimation:Kill()
            m.floatAnimation = nil
        end
        
        -- 创建上下浮动的动画
        local objModel = m.objList.Obj_Model
        if not tolua.isnull(objModel) then
            -- 确保先回到初始位置
            if m.objModelOriginalPos then
                objModel.transform.localPosition = Vector3(m.objModelOriginalPos.x, m.objModelOriginalPos.y, 0)
                
                -- 创建动画序列
                m.floatAnimation = DOTween.Sequence()
                
                -- 往上浮动到+30
                m.floatAnimation:Append(objModel.transform:DOLocalMoveY(m.objModelOriginalPos.y + 30, 1.5):SetEase(TweeningEase.InOutSine))
                
                -- 往下浮动到-30
                m.floatAnimation:Append(objModel.transform:DOLocalMoveY(m.objModelOriginalPos.y - 30, 1.5):SetEase(TweeningEase.InOutSine))
                
                -- 设置为无限循环
                m.floatAnimation:SetLoops(-1, TweeningLoopType.Yoyo)
            else
                -- 如果没有保存的初始位置，使用当前位置
                local originalPos = objModel.transform.localPosition
                
                -- 创建动画序列
                m.floatAnimation = DOTween.Sequence()
                
                -- 往上浮动到+30
                m.floatAnimation:Append(objModel.transform:DOLocalMoveY(originalPos.y + 30, 1.5):SetEase(TweeningEase.InOutSine))
                
                -- 往下浮动到-30
                m.floatAnimation:Append(objModel.transform:DOLocalMoveY(originalPos.y - 30, 1.5):SetEase(TweeningEase.InOutSine))
                
                -- 设置为无限循环
                m.floatAnimation:SetLoops(-1, TweeningLoopType.Yoyo)
            end
        end
    end, parameter)
end

--------------------------------------------------------------------
--更新角色模型
--------------------------------------------------------------------
function m.ChangeUIModel(equipID)
    local cfg = Schemes.Equipment:Get(equipID)
    local level = GamePlayerData.ActorEquipNew:GetEquipUpgradeLevel(cfg.SmeltID)
    
    local gun = Schemes.Gun:Get(cfg.ConsignmentStyle)
    local bullet_List = Schemes.Bullet:GetByBulletId(gun.BulletId)
    local trans = m.objList.Obj_Model.transform
	for c = trans.childCount - 1, 0, -1 do
		if trans:GetChild(c) then
			GameObject.Destroy(trans:GetChild(c).gameObject)
		end
	end
    local parameter
    ResMgr.LoadGameObjectAsync("model/uiSpine/"..bullet_List[1].Model, function (obj, parameter)
        local model = GameObject.Instantiate(obj, m.objList.Obj_Model.transform)
        
        -- 停止之前可能存在的动画
        if m.floatAnimation then
            m.floatAnimation:Kill()
            m.floatAnimation = nil
        end
        
        -- 创建上下浮动的动画
        local objModel = m.objList.Obj_Model
        if not tolua.isnull(objModel) then
            -- 确保先回到初始位置
            if m.objModelOriginalPos then
                objModel.transform.localPosition = Vector3(m.objModelOriginalPos.x, m.objModelOriginalPos.y, 0)
                
                -- 创建动画序列
                m.floatAnimation = DOTween.Sequence()
                
                -- 往上浮动到+30
                m.floatAnimation:Append(objModel.transform:DOLocalMoveY(m.objModelOriginalPos.y + 30, 1.5):SetEase(TweeningEase.InOutSine))
                
                -- 往下浮动到-30
                m.floatAnimation:Append(objModel.transform:DOLocalMoveY(m.objModelOriginalPos.y - 30, 1.5):SetEase(TweeningEase.InOutSine))
                
                -- 设置为无限循环
                m.floatAnimation:SetLoops(-1, TweeningLoopType.Yoyo)
            else
                -- 如果没有保存的初始位置，使用当前位置
                local originalPos = objModel.transform.localPosition
                
                -- 创建动画序列
                m.floatAnimation = DOTween.Sequence()
                
                -- 往上浮动到+30
                m.floatAnimation:Append(objModel.transform:DOLocalMoveY(originalPos.y + 30, 1.5):SetEase(TweeningEase.InOutSine))
                
                -- 往下浮动到-30
                m.floatAnimation:Append(objModel.transform:DOLocalMoveY(originalPos.y - 30, 1.5):SetEase(TweeningEase.InOutSine))
                
                -- 设置为无限循环
                m.floatAnimation:SetLoops(-1, TweeningLoopType.Yoyo)
            end
        end
    end, parameter)
end

--------------------------------------------------------------------
--更新角色
--------------------------------------------------------------------
function m.UpdateRole()
    if m.ItemRole ~= nil then
        m.ItemRole.Img_Select.gameObject:SetActive(false)    
    end

    local dataList = m.EquipWeaponIDList
    for i, v in ipairs(dataList) do
        m.Item_Role_List[i].UpdateData(v)
    end 
    
    m.UpdateTabButtonRed()
end

m.ItemRole = nil
m.curEquipID = nil
--------------------------------------------------------------------
--创建属性框
--------------------------------------------------------------------
function m.Creation_Item_Role(index)
    ---@class Item_Role
    local item = {}
    item.index = index
    item.com = m:CreateSubItem(m.objList.Grid_Tab, m.objList.Item_Tab)
    item.com.Img_Select.gameObject:SetActive(false)
    m:AddClick(item.com.Btn_Click, function()
        if m.ItemRole == item.com then return end
        if m.ItemRole ~= nil then
            m.ItemRole.Img_Select.gameObject:SetActive(false)
        end
        m.ItemRole = item.com
        m.ItemRole.Img_Select.gameObject:SetActive(true)
        m.ChangeUIModel(item.equipID)
        m.curEquipID = item.equipID
        m.equipID = item.equipID
        m.equipConfig = Schemes.Equipment:Get(m.equipID)
    --========================================================================
        m.XLSmeltID = m.equipConfig.SmeltBase    

        m.SelectBtnState(m.curIndex)
    end)

    m:AddClick(item.com.Btn_Battle, function()
        print("33333333 装备出战-开始处理装备ID:", item.equipID)

        -- 检查装备是否已经出战
        if GamePlayerData.ActorEquip:IsWear(item.equipID, 1) then
            print("33333333 装备出战-装备已经出战，无需重复操作")
            return
        end

        -- 1. 设置主装备出战
        GamePlayerData.ActorEquip:ReplaceEquipIndex(item.equipID, 1, 1)
        print("33333333 装备出战-设置主装备出战:", item.equipID)

        -- 2. 获取并设置关联装备出战
        print("33333333 关联-开始获取装备", item.equipID, "的关联装备")
        local associatedEquipIDs = GetAllAssociatedEquipIDs(item.equipID)
        print("33333333 关联-装备出战找到关联装备数量:", #associatedEquipIDs)

        if #associatedEquipIDs > 0 then
            print("33333333 关联-开始设置关联装备出战")
            for i, associatedEquipID in ipairs(associatedEquipIDs) do
                -- 检查关联装备是否已经出战
                local isAlreadyWorn = GamePlayerData.ActorEquip:IsWear(associatedEquipID, 1)
                print("33333333 关联-装备出战关联装备", associatedEquipID, "是否已出战:", isAlreadyWorn)

                if not isAlreadyWorn then
                    GamePlayerData.ActorEquip:ReplaceEquipIndex(associatedEquipID, 2, 1)
                    print("33333333 关联-装备出战设置关联装备出战成功:", associatedEquipID)
                else
                    print("33333333 关联-装备出战关联装备已经出战，跳过:", associatedEquipID)
                end
            end
        else
            print("33333333 关联-装备", item.equipID, "没有配置关联装备")
        end

        -- 3. 同步装备数据到战斗系统
        print("33333333 关联-开始同步装备数据到战斗系统")
        SyncEquipmentToBattleSystem()

        -- 4. 执行【#逻辑值 200 X】，其中X值是本次操作出战的装备ID
        local chatContent = "#逻辑值 200 " .. item.equipID
        local msg = ChatMessage_pb.CS_Char_SendChat()
        msg.Channel = 12
        msg.Content = chatContent
        msg.ChatType = 0
        Premier.Instance:GetNetwork():SendFromLua(
            ENDPOINT.ENDPOINT_GAMECLIENT,
            ENDPOINT.ENDPOINT_GAMESERVER,
            MSG_MODULEID.MSG_MODULEID_CHAT,
            ChatMessage_pb.MSG_CHAT_SENDCHAT,
            msg:SerializeToString()
        )
        print("33333333 装备出战-设置逻辑值200:", item.equipID, "命令:", chatContent)

        -- 4. 更新界面状态
        if m.ItemRole == item.com then return end
        if m.ItemRole ~= nil then
            m.ItemRole.Img_Select.gameObject:SetActive(false)
        end
        m.ItemRole = item.com
        m.ItemRole.Img_Select.gameObject:SetActive(true)
        m.ChangeUIModel(item.equipID)
        m.curEquipID = item.equipID
        m.equipID = item.equipID
        m.equipConfig = Schemes.Equipment:Get(m.equipID)
    --========================================================================
        m.XLSmeltID = m.equipConfig.SmeltBase

        m.SelectBtnState(m.curIndex)

        -- 5. 打印最终出战装备列表确认
        local finalEquipIDs = GamePlayerData.ActorEquip:GetWeaponsKnapsack(1) or {}
        print("33333333 装备出战-最终出战装备列表:", json.encode(finalEquipIDs))
    end)
    ---更新数据
    ---@param equipID integer
    item.UpdateData = function(equipID)
        item.equipID = equipID
        if equipID then
            local cfg = Schemes.Equipment:Get(equipID)
            --local level = GamePlayerData.ActorEquipNew:GetEquipUpgradeLevel(cfg.SmeltBase)
            --local equipSmeltStar = Schemes.EquipSmeltStar:GetEquipSmeltStarCfg(cfg.SmeltBase, level)
            AtlasManager:AsyncGetSprite(cfg.IconID, item.com.Img_icon1)
            AtlasManager:AsyncGetSprite(cfg.IconID, item.com.Img_icon2)
            item.com.Txt_Name1.text = cfg.GoodsName
            item.com.Txt_Name2.text = cfg.GoodsName

            if GamePlayerData.ActorEquip:IsWear(equipID, 1) then
                item.com.Txt_Battle.text = "已出战"
            else
                item.com.Txt_Battle.text = "出战"
            end 

            item.com.gameObject:SetActive(true)
            -- local weaponsKnapsack = GamePlayerData.ActorEquip:GetWeaponsKnapsack(1) or {}
            -- local curEquipID = weaponsKnapsack[1] or 0
            if m.curEquipID == equipID and m.ItemRole == nil then                
                m.ItemRole = item.com
                m.curEquipID = equipID
                m.ItemRole.Img_Select.gameObject:SetActive(true)
            end
        else
            item.com.gameObject:SetActive(false)
        end

        item.UpdateRed()
    end

    item.UpdateRed = function()
        if m:Check_QHEquipRed(item.equipID) or m:Check_XLEquipRed(item.equipID) then
            item.com.Img_RedDot.gameObject:SetActive(true)
        else
            item.com.Img_RedDot.gameObject:SetActive(false)
        end
    end
    return item
end

-- 同步装备数据到战斗系统
function SyncEquipmentToBattleSystem()
    print("33333333 关联-同步装备数据到战斗系统开始")

    -- 获取当前主装备ID
    local weaponsKnapsack = GamePlayerData.ActorEquip:GetWeaponsKnapsack(1) or {}
    local mainEquipID = weaponsKnapsack[1] or 0

    if mainEquipID == 0 then
        print("33333333 关联-同步装备数据失败，没有主装备")
        return
    end

    -- 获取所有关联装备
    local associatedEquipIDs = GetAllAssociatedEquipIDs(mainEquipID)
    print("33333333 关联-同步装备数据，主装备:", mainEquipID, "关联装备:", json.encode(associatedEquipIDs))

    -- 构建完整的装备列表（主装备 + 关联装备）
    local allEquipIDs = {mainEquipID}
    for i = 1, #associatedEquipIDs do
        table.insert(allEquipIDs, associatedEquipIDs[i])
    end

    -- 转换装备ID为GunItem格式
    local gunItems = {}
    for i = 1, #allEquipIDs do
        local equipID = allEquipIDs[i]
        local equipCfg = Schemes.Equipment:Get(equipID)
        if equipCfg and equipCfg.ConsignmentStyle > 0 then
            local gunItem = {
                GunId = equipCfg.ConsignmentStyle,
                GoodsId = equipID,
                GunLvl = 1,
                StarCount = 1,
                GunGuid = CS.System.Guid.NewGuid(),
                PosInBag = {}
            }
            table.insert(gunItems, gunItem)
            print("33333333 关联-同步装备数据，装备ID:", equipID, "对应GunID:", equipCfg.ConsignmentStyle)
        else
            print("33333333 关联-同步装备数据，装备ID:", equipID, "没有对应的Gun配置")
        end
    end

    -- 保存到战斗背包数据
    if #gunItems > 0 then
        local jsonGunItems = json.encode(gunItems)
        print("33333333 关联-同步装备数据，保存GunItems:", jsonGunItems)

        -- 保存到ActorDataCatalog.BattleBagGuns
        local lstBattleBagGuns = {"[]", jsonGunItems}  -- 第一个是格子数据，第二个是枪数据
        CS.SingletonMgr.Instance.ActorDataMgr:SetString(CS.ActorDataCatalog.BattleBagGuns, lstBattleBagGuns)
        print("33333333 关联-同步装备数据完成，已保存到BattleBagGuns")
    else
        print("33333333 关联-同步装备数据失败，没有有效的Gun数据")
    end
end

-- 获取装备的所有关联装备ID（支持EffectID2和EffectID3双字段多层关联）
function GetAllAssociatedEquipIDs(equipId)
    print("33333333 关联-开始查找装备关联，主装备ID:", equipId)
    local associatedEquipIDs = {}
    local currentEquipId = equipId
    local visitedEquipIDs = {} -- 防止循环引用

    while true do
        -- 循环引用检测
        if visitedEquipIDs[currentEquipId] then
            print("33333333 关联-检测到循环引用，停止查找关联装备，当前ID=" .. currentEquipId)
            break
        end
        visitedEquipIDs[currentEquipId] = true

        -- 获取装备配置
        local equipWeaponConfig = Schemes.EquipWeapon:Get(currentEquipId)
        if not equipWeaponConfig then
            print("33333333 关联-未找到EquipWeapon配置，装备ID=" .. currentEquipId)
            break
        end

        print("33333333 关联-装备ID=" .. currentEquipId .. " EffectID2=" .. (equipWeaponConfig.EffectID2 or "nil") .. " EffectID3=" .. (equipWeaponConfig.EffectID3 or "nil"))

        local nextEquipId = nil
        local hasAssociated = false

        -- 优先检查EffectID2
        if equipWeaponConfig.EffectID2 and equipWeaponConfig.EffectID2 ~= 0 then
            local associatedId = equipWeaponConfig.EffectID2
            table.insert(associatedEquipIDs, associatedId)
            print("33333333 关联-找到EffectID2关联装备 " .. associatedId .. " (关联于装备 " .. currentEquipId .. ")")
            if not nextEquipId then
                nextEquipId = associatedId
            end
            hasAssociated = true
        end

        -- 检查EffectID3
        if equipWeaponConfig.EffectID3 and equipWeaponConfig.EffectID3 ~= 0 then
            local associatedId = equipWeaponConfig.EffectID3
            table.insert(associatedEquipIDs, associatedId)
            print("33333333 关联-找到EffectID3关联装备 " .. associatedId .. " (关联于装备 " .. currentEquipId .. ")")
            if not nextEquipId then
                nextEquipId = associatedId
            end
            hasAssociated = true
        end

        if hasAssociated and nextEquipId then
            print("33333333 关联-继续查找下一层关联，下一个装备ID=" .. nextEquipId)
            currentEquipId = nextEquipId -- 继续查找下一层关联
        else
            print("33333333 关联-装备 " .. currentEquipId .. " 无关联装备或EffectID2/EffectID3为0，停止查找")
            break
        end
    end

    print("33333333 关联-查找完成，总共找到关联装备数量:", #associatedEquipIDs)
    if #associatedEquipIDs > 0 then
        print("33333333 关联-关联装备列表:", json.encode(associatedEquipIDs))
    end
    return associatedEquipIDs
end

function m:Wear(equipId)

    for i = 1, #m.EquipWeaponIDList, 1 do
        if GamePlayerData.ActorEquip:IsWear(m.EquipWeaponIDList[i], 1) then
            --卸下装备
            GamePlayerData.ActorEquip:UnloadEquip(m.EquipWeaponIDList[i], 1)
        end
    end
    m.smallTimer= Timer.New(function()
        GamePlayerData.ActorEquip:WearEquip(equipId, 1)
	end, 0.5, 1)
	m.smallTimer:Start()

end


--------------------------------------------------------------------
-- 注册点击事件
--------------------------------------------------------------------
function m.RegisterClickEvent()
    m:AddClick(m.objList.Btn1, function()
        m.SelectBtnState(1)
    end)
    m:AddClick(m.objList.Btn2, function()
        m.SelectBtnState(2)
    end)
    m:AddClick(m.objList.Btn_QH, function()
        m.EquipUpgrade(m.equipID)
    end)

    m:AddClick(m.objList.Btn_XL, function()
        m.UpgradeXL(m.XLSmeltID)
    end)
end

function m.SelectBtnState(index)
    m.curIndex = index
    m.objList.Img_Select1.gameObject:SetActive(index == 1)
    m.objList.Img_Select2.gameObject:SetActive(index == 2)
    m.objList.QHGroup.gameObject:SetActive(index == 1)
    m.objList.XLGroup.gameObject:SetActive(index == 2)
    if index == 1 then
        m.UpdateQHInfo()
    end

    if index == 2 then
        m.UpdateXLInfo()
    end

    m.UpdateTabButtonRed()
end

function m.UpdateTabButtonRed()
    if m:Check_QHEquipRed(m.equipID) then
        m.objList.Img_Red1.gameObject:SetActive(true)
    else
        m.objList.Img_Red1.gameObject:SetActive(false)
    end

    if m:Check_XLEquipRed(m.equipID) then
        m.objList.Img_Red2.gameObject:SetActive(true)
    else
        m.objList.Img_Red2.gameObject:SetActive(false)
    end
end

--------------------------------------------------------------------
-- 更新界面
--------------------------------------------------------------------
function m.UpdateView()
    local dataList = m.EquipWeaponIDList
    for i, v in ipairs(dataList) do
        m.Item_Role_List[i].UpdateData(v)
    end 

    print("==========UpdateView================")

    m.UpdateQHInfo()
    m.UpdateXLInfo()
    m.UpdateTabButtonRed()
end


--检测--Equip--红点
function m:Check_QHEquipRed(equipId)
    local equipment = Schemes.Equipment:Get(equipId)
    local entity = SkepModule:GetSkepByID(equipment.PacketID):GetEntityByGoodsID(equipment.ID)
    if not entity then
        warn('未找到装备实体 equipID=', equipID)
        return false
    end
    local quality = entity:GetProperty(EQUIPMENT_FIELD.EQUIPMENT_FIELD_QUALITY)
    local starNum = entity:GetProperty(EQUIPMENT_FIELD.EQUIPMENT_FIELD_STARNUM)
    local smelt = Schemes.EquipSmelt:Get(equipment.SmeltID, quality, starNum)
    if not smelt then
        warn('未找到装备升星 EquipSmelt 配置 SmeltID=', equipment.SmeltID, quality, starNum)
        return false
    end
    if smelt.LevelMaxExp == -1 then
        return false
    end
    if HelperL.IsLackGoods(smelt.GoodsID, smelt.GoodsNum, false, false) then 
        return false
    end

    if HelperL.IsLackGoods(4, smelt.CostMoney, false, false) then 
        return false
    end

    return true
end

function m:Check_XLEquipRed(equipId)  
    local cfg = Schemes.Equipment:Get(equipId)
    local level = GamePlayerData.ActorEquipNew:GetEquipUpgradeLevel(cfg.SmeltBase)
    local maxLevel = PropertyCompute.GetEquipSmeltStarMaxLevel(cfg.SmeltBase)
    if level >= maxLevel - 1 then
        return false
    end

    local count = HeroDataManager:GetLogicData(LOGIC_DATA.DATA_RECHARGEDRAW_COUNT)
    if count >= 8 then return false end

    local equipSmeltStar = Schemes.EquipSmeltStar:GetEquipSmeltStarCfg(cfg.SmeltBase, level + 1)
    if equipSmeltStar then
        if HelperL.IsLackGoods(equipSmeltStar.CostGoodsID1, equipSmeltStar.CostGoodsID1Num, false, false) then
            return false
        end
        
        if HelperL.IsLackGoods(equipSmeltStar.CostGoodsID2, equipSmeltStar.CostGoodsID2Num, false, false) then
            return false
        end
    end

    return true
end
--------------------------------------------------------------------
--显示强化信息
--------------------------------------------------------------------
function m.UpdateQHInfo()
    m.objList.Btn_QH.gameObject:SetActive(false)    
    local equipment = Schemes.Equipment:Get(m.equipID)
    local quality = equipment.Quality
    local starNum = equipment.StarNum
    local level = 0
    local entity = SkepModule:GetSkepByID(equipment.PacketID):GetEntityByGoodsID(equipment.ID)
    --装备已激活
    if entity then
        quality = entity:GetProperty(EQUIPMENT_FIELD.EQUIPMENT_FIELD_QUALITY)
        starNum = entity:GetProperty(EQUIPMENT_FIELD.EQUIPMENT_FIELD_STARNUM)
        level = quality * 10 + starNum + 1
    end
    m.curLevel = level
    print("level ===== "..level)
    print("equipment.SmeltID ===== "..equipment.SmeltID)

    local smelt = Schemes.EquipSmelt:Get(equipment.SmeltID, quality, starNum)
    print("smelt.LevelMaxExp ===== "..smelt.LevelMaxExp)
    if smelt.LevelMaxExp ~= -1 then
        m.objList.Obj_Cur:SetActive(true)
        --m.objList.Obj_Next:SetActive(true)
        local equipSmeltStar = Schemes.EquipSmeltStar:GetEquipSmeltStarCfg(equipment.SmeltID, level)
        local str = Schemes.CommonProp:Get(equipSmeltStar.PropId).Remark
        local list = HelperL.Split( str,"\\n")
        str = ""
        for i = 1, #list do
            if i < #list then
                str = str..list[i].."\n"
            else
                str =  str..list[i]
            end
        end
        m.objList.Txt_QHLevel.text = level
        m.objList.Txt_CurDesc.text = str
        local nextSmelt = Schemes.EquipSmelt:Get(equipment.SmeltID, smelt.NextQuality, smelt.NextStarNum)  
        equipSmeltStar = Schemes.EquipSmeltStar:GetEquipSmeltStarCfg(equipment.SmeltID, level+1)
        str = Schemes.CommonProp:Get(equipSmeltStar.PropId).Remark
        local list = HelperL.Split( str,"\\n")
        str = ""
        for i = 1, #list do
            if i < #list then
                str = str..list[i].."\n"
            else
                str =  str..list[i]
            end
        end      
        m.objList.Txt_NextDesc.text = str
        m.objList.Txt_QHExpend.gameObject:SetActive(true)
        m.objList.Txt_QHExpend1.gameObject:SetActive(true)
        m.objList.Btn_QH.gameObject:SetActive(true)
        AtlasManager:AsyncGetGoodsSprite(nextSmelt.GoodsID, m.objList.Img_QHExpend)
        AtlasManager:AsyncGetGoodsSprite(4, m.objList.Img_QHExpend1)
        local num1 = SkepModule:GetGoodsCount(nextSmelt.GoodsID)
        local num2 = SkepModule:GetGoodsCount(4)
        local bool1 = not HelperL.IsLackGoods(nextSmelt.GoodsID, nextSmelt.GoodsNum, false, false)
        local bool2 = not HelperL.IsLackGoods(4, nextSmelt.CostMoney, false, false)
        m.objList.Txt_QHExpend1.text = string.format("<color=%s>%s</color>/%s", bool2 and "#FFFFFF" or "#F1A92F",
            HelperL.GetChangeNum(num2), HelperL.GetChangeNum(nextSmelt.CostMoney))
        m.objList.Txt_QHExpend.text = string.format("<color=%s>%s</color>/%s", bool2 and "#FFFFFF" or "#F1A92F",
            HelperL.GetChangeNum(num1), HelperL.GetChangeNum(nextSmelt.GoodsNum))
        if bool1 and bool2 then
            --m.objList.Img_UpgradeRedDot.gameObject:SetActive(true)
            m.objList.Img_QHGray.gameObject:SetActive(false)
        else
            --m.objList.Img_Expend.gameObject:SetActive(true)
            --m.objList.Img_Gray.gameObject:SetActive(true)
            m.objList.Img_QHGray.gameObject:SetActive(true)
        end
       -- m.objList.Obj_Cur.gameObject.transform.localPosition = Vector3(-253,-362,0)
        m.objList.Txt_MaxDesc.gameObject:SetActive(false)
    else
        m.objList.Obj_Cur:SetActive(true)
        --m.objList.Obj_Cur.gameObject.transform.localPosition = Vector3(0,-362,0)
        m.objList.Obj_Next:SetActive(false)
        m.objList.Txt_MaxDesc.gameObject:SetActive(true)
        m.objList.Txt_QHExpend.gameObject:SetActive(false)
        m.objList.Txt_QHExpend1.gameObject:SetActive(false)

        local equipSmeltStar = Schemes.EquipSmeltStar:GetEquipSmeltStarCfg(equipment.SmeltID, level)
        local str = Schemes.CommonProp:Get(equipSmeltStar.PropId).Remark
        local list = HelperL.Split( str,"\\n")
        str = ""
        for i = 1, #list do
            if i < #list then
                str = str..list[i].."\n"
            else
                str =  str..list[i]
            end
        end
        m.objList.Txt_CurDesc.text = str
    end
end

function m.UpdateXLInfo()    
    local starLvl = GamePlayerData.ActorEquipNew:GetEquipUpgradeLevel(m.XLSmeltID)
    warn("天赋升级:未找到升星配置 starLvl=", starLvl)
    local equipSmeltStar = Schemes.EquipSmeltStar:GetEquipSmeltStarCfg(m.XLSmeltID, starLvl)
    if equipSmeltStar == nil then
        warn("天赋升级:未找到升星配置 smeltID=", m.XLSmeltID, starLvl)
        return
    end

    local bool1 = HelperL.IsLackGoods(
        equipSmeltStar.CostGoodsID1, equipSmeltStar.CostGoodsID1Num, false, false)
    -- local bool2 = HelperL.IsLackGoods(
    --     equipSmeltStar.CostGoodsID2, equipSmeltStar.CostGoodsID2Num, false, false)
    local str = Schemes.CommonProp:Get(equipSmeltStar.PropId).Remark
    local list = HelperL.Split( str,"\\n")
    str = ""
    for i = 1, #list do
        if i < #list then
            str = str..list[i].."\n"
        else
            str =  str..list[i]
        end
    end
    m.objList.Txt_XLPro.text = str
    
    local num = SkepModule:GetGoodsCount(equipSmeltStar.CostGoodsID1)
    local count = HeroDataManager:GetLogicData(LOGIC_DATA.DATA_RECHARGEDRAW_COUNT)  
    if count >= 8 or bool1 then
        m.objList.Img_XLGray.gameObject:SetActive(true)
    else
        m.objList.Img_XLGray.gameObject:SetActive(false)
    end
    --m.objList.Img_XLGray.gameObject:SetActive(bool1 or count >= 8)
    AtlasManager:AsyncGetGoodsSprite(equipSmeltStar.CostGoodsID1, m.objList.Img_XLExpend)
    --AtlasManager:AsyncGetGoodsSprite(equipSmeltStar.CostGoodsID2, m.objList.Img_XLExpend1)
    m.objList.Txt_XLExpend.text = string.format("<color=%s>%s/%s</color>", color,HelperL.GetChangeNum(num), HelperL.GetChangeNum(equipSmeltStar.CostGoodsID1Num))
    --m.objList.Txt_XLExpend1.text = string.format("<color=%s>%s/%s</color>", color,HelperL.GetChangeNum(num1), HelperL.GetChangeNum(equipSmeltStar.CostGoodsID2Num))
    m.objList.Txt_Num.text = "今日剩余次数："..(8 - count)

end
--------------------------------------------------------------------
---装备升星
---@param equipID integer 装备ID
--------------------------------------------------------------------
function m.EquipUpgrade(equipId)
    local curTime = os.clock()
    if curTime - m.lastTime < 1 then
        print('点击太快了，请1秒后再点击！')
        return
    end
    m.lastTime = curTime
    
    local equipment = Schemes.Equipment:Get(equipId)
    local entity = SkepModule:GetSkepByID(equipment.PacketID):GetEntityByGoodsID(equipment.ID)
    if not entity then
        warn('未找到装备实体 equipID=', equipID)
        return
    end
    local quality = entity:GetProperty(EQUIPMENT_FIELD.EQUIPMENT_FIELD_QUALITY)
    local starNum = entity:GetProperty(EQUIPMENT_FIELD.EQUIPMENT_FIELD_STARNUM)
    local smelt = Schemes.EquipSmelt:Get(equipment.SmeltID, quality, starNum)
    if not smelt then
        warn('未找到装备升星 EquipSmelt 配置 SmeltID=', equipment.SmeltID, quality, starNum)
        return
    end
    if smelt.LevelMaxExp == -1 then
        return
    end
    if HelperL.IsLackGoods(smelt.GoodsID, smelt.GoodsNum, false) then 
        return 
    end

    if HelperL.IsLackGoods(4, smelt.CostMoney, false) then 
        return 
    end
    local str_req = string.format(
        "LuaRequestEquipStarUp?equip=%s&type=%s&bindflag=%s&AutoBuy=%s&LoopTimes=%d&WishType=%s",
        entity.uid, 0, 0, 0, 1, 2)
    LuaModule.RunLuaRequest(str_req, m.RequestCallback1)
end


--------------------------------------------------------------------
--请求回调
--------------------------------------------------------------------
function m.RequestCallback1(resultCode, content)
    if resultCode == RESULT_CODE_BASE.RESULT_COMMON_SUCCEED[1] then
        
        HelperL.PlayVFX(VFX_Type.QH_FX)
        HelperL.ShowMessage(TipType.FlowText, '强化等级+1。')
    else
        ResultCode:DefaultShowResultCode(resultCode, content)
    end
end



--------------------------------------------------------------------
--请求回调
--------------------------------------------------------------------
function m.RequestXLCallback(resultCode, content)
    if resultCode == RESULT_CODE_BASE.RESULT_COMMON_SUCCEED[1] then
        --m.UpdateView()  
        HelperL.PlayVFX(VFX_Type.PYJS_FX)
        HelperL.ShowMessage(TipType.FlowText, '洗练成功。')
        local count = HeroDataManager:GetLogicData(LOGIC_DATA.DATA_RECHARGEDRAW_COUNT)
        local str_req2 = string.format('LuaRequestSetLogicData?logicID=%d&logicValue=%d', LOGIC_DATA.DATA_RECHARGEDRAW_COUNT,count+1)
        LuaModule.RunLuaRequest(str_req2, function(resultCode2, content2)
            print("resultCode2 = ", resultCode2)
            if resultCode2 == RESULT_CODE_BASE.RESULT_COMMON_SUCCEED[1] then
                m.UpdateXLInfo()    
            end
        end)
    else
        ResultCode:DefaultShowResultCode(resultCode, content)
    end
end

--------------------------------------------------------------------
--xl升级
---@param smeltID integer 升星ID
--------------------------------------------------------------------
function m.UpgradeXL(smeltID)
    local curTime = Premier.Instance:GetServerTime()
    if curTime - m.lastTime < 1 then
        print('天赋升级点太快了1秒后尝试！')
        return
    end
    m.lastTime = curTime

    local starLvl = GamePlayerData.ActorEquipNew:GetEquipUpgradeLevel(smeltID)
    local equipSmeltStar = Schemes.EquipSmeltStar:GetEquipSmeltStarCfg(smeltID, starLvl)
    if equipSmeltStar == nil then
        warn("天赋升级:未找到升星配置 smeltID=", smeltID, starLvl)
        return
    end

    if HelperL.IsLackGoods(equipSmeltStar.CostGoodsID1, equipSmeltStar.CostGoodsID1Num, false) then 
        return 
    end
    local count = HeroDataManager:GetLogicData(LOGIC_DATA.DATA_RECHARGEDRAW_COUNT)
    if count >= 8 then return end
    
    m.lastStarLvl = starLvl
    m.lastSmeltID = smeltID

    local form = {}
    form["smeltID"] = smeltID
    form["loopTimes"] = 1
    LuaModuleNew.SendRequest(LuaRequestID.NewCardAddExp2, form, m.RequestXLCallback)
end

--------------------------------------------------------------------
-- 窗口关闭时清理资源
--------------------------------------------------------------------
function m:OnClose()
    -- 停止模型浮动动画，防止内存泄漏
    if m.floatAnimation then
        m.floatAnimation:Kill()
        m.floatAnimation = nil
    end
end

return m
