--[[
********************************************************************
    created:	2024/05/17
    purpose:    界面
*********************************************************************
--]]

local luaID = 'UIRoleEquip'

---@class UIRoleEquip:UIWndBase
local m = {}

--角色框
---@type Item_Role[]
m.Item_Role_List = {}
--------------------------------------------------------------------
-- 订阅事件列表
--------------------------------------------------------------------
function m.GetOpenEventList()
    return {
        [EventID.StoreList] = m.UpdateView,
        [EventID.StoreBuyItem] = m.UpdateView,
        [EventID.LogicDataChange] = m.UpdateView,
        [EventID.OnGoodsPropChange] = m.UpdateView,
        [EventID.OnHeroPropChange] = m.UpdateView,
        [EventID.ActorDataChanged] = m.UpdateView,
    }
end

--------------------------------------------------------------------
-- 初始化
--------------------------------------------------------------------
function m.OnCreate()
    ---按钮
    ---@type Item_Tab[]
    m.Item_Tab_List = {}

    m.curIndex = 1
    m.lastTime = 0
    --体魄ID
    ---@type integer[]
    m.EquipWeaponIDList = {}
    for i, v in ipairs(Schemes.EquipWeapon.items) do
        if v.GroupID < 1000 then -- 
            table.insert(m.EquipWeaponIDList, v.ID)
        end
    end

    for i = 1, #m.EquipWeaponIDList, 1 do
        m.Item_Role_List[i] = m.Creation_Item_Role(i)
    end
    

    m.equipID = m.EquipWeaponIDList[m.curIndex]
    m.curEquipID = m.equipID
    m.equipConfig = Schemes.Equipment:Get(m.equipID)

    m.XLSmeltID = m.equipConfig.SmeltBase
    m.XLSmeltList = Schemes.EquipSmeltStar:GetBySmeltID(m.XLSmeltID) or {}
    
    m.RegisterClickEvent()
    
    return true
end

--------------------------------------------------------------------
-- 窗口开启
--------------------------------------------------------------------
function m:OnOpen()
    -- 保存Obj_Model的初始位置
    if not m.objModelOriginalPos then
        m.objModelOriginalPos = Vector2(m.objList.Obj_Model.transform.localPosition.x, m.objList.Obj_Model.transform.localPosition.y)
    end
    
    m.UpdateRole()
    m.SelectBtnState(1)
    m.UpdateUIModel()
    m.UpdateView()
end

--------------------------------------------------------------------
--更新角色模型
--------------------------------------------------------------------
function m.UpdateUIModel()
    local cfg = Schemes.Equipment:Get(m.equipID)
    --暂时屏蔽    
    local gun = Schemes.Gun:Get(cfg.ConsignmentStyle)
    local bullet_List = Schemes.Bullet:GetByBulletId(gun.BulletId)
    local trans = m.objList.Obj_Model.transform
	for c = trans.childCount - 1, 0, -1 do
		if trans:GetChild(c) then
			GameObject.Destroy(trans:GetChild(c).gameObject)
		end
	end
    local parameter
    ResMgr.LoadGameObjectAsync("model/uiSpine/"..bullet_List[1].Model, function (obj, parameter)
        local model = GameObject.Instantiate(obj, m.objList.Obj_Model.transform)
        
        -- 停止之前可能存在的动画
        if m.floatAnimation then
            m.floatAnimation:Kill()
            m.floatAnimation = nil
        end
        
        -- 创建上下浮动的动画
        local objModel = m.objList.Obj_Model
        if not tolua.isnull(objModel) then
            -- 确保先回到初始位置
            if m.objModelOriginalPos then
                objModel.transform.localPosition = Vector3(m.objModelOriginalPos.x, m.objModelOriginalPos.y, 0)
                
                -- 创建动画序列
                m.floatAnimation = DOTween.Sequence()
                
                -- 往上浮动到+30
                m.floatAnimation:Append(objModel.transform:DOLocalMoveY(m.objModelOriginalPos.y + 30, 1.5):SetEase(TweeningEase.InOutSine))
                
                -- 往下浮动到-30
                m.floatAnimation:Append(objModel.transform:DOLocalMoveY(m.objModelOriginalPos.y - 30, 1.5):SetEase(TweeningEase.InOutSine))
                
                -- 设置为无限循环
                m.floatAnimation:SetLoops(-1, TweeningLoopType.Yoyo)
            else
                -- 如果没有保存的初始位置，使用当前位置
                local originalPos = objModel.transform.localPosition
                
                -- 创建动画序列
                m.floatAnimation = DOTween.Sequence()
                
                -- 往上浮动到+30
                m.floatAnimation:Append(objModel.transform:DOLocalMoveY(originalPos.y + 30, 1.5):SetEase(TweeningEase.InOutSine))
                
                -- 往下浮动到-30
                m.floatAnimation:Append(objModel.transform:DOLocalMoveY(originalPos.y - 30, 1.5):SetEase(TweeningEase.InOutSine))
                
                -- 设置为无限循环
                m.floatAnimation:SetLoops(-1, TweeningLoopType.Yoyo)
            end
        end
    end, parameter)
end

--------------------------------------------------------------------
--更新角色模型
--------------------------------------------------------------------
function m.ChangeUIModel(equipID)
    local cfg = Schemes.Equipment:Get(equipID)
    local level = GamePlayerData.ActorEquipNew:GetEquipUpgradeLevel(cfg.SmeltID)
    
    local gun = Schemes.Gun:Get(cfg.ConsignmentStyle)
    local bullet_List = Schemes.Bullet:GetByBulletId(gun.BulletId)
    local trans = m.objList.Obj_Model.transform
	for c = trans.childCount - 1, 0, -1 do
		if trans:GetChild(c) then
			GameObject.Destroy(trans:GetChild(c).gameObject)
		end
	end
    local parameter
    ResMgr.LoadGameObjectAsync("model/uiSpine/"..bullet_List[1].Model, function (obj, parameter)
        local model = GameObject.Instantiate(obj, m.objList.Obj_Model.transform)
        
        -- 停止之前可能存在的动画
        if m.floatAnimation then
            m.floatAnimation:Kill()
            m.floatAnimation = nil
        end
        
        -- 创建上下浮动的动画
        local objModel = m.objList.Obj_Model
        if not tolua.isnull(objModel) then
            -- 确保先回到初始位置
            if m.objModelOriginalPos then
                objModel.transform.localPosition = Vector3(m.objModelOriginalPos.x, m.objModelOriginalPos.y, 0)
                
                -- 创建动画序列
                m.floatAnimation = DOTween.Sequence()
                
                -- 往上浮动到+30
                m.floatAnimation:Append(objModel.transform:DOLocalMoveY(m.objModelOriginalPos.y + 30, 1.5):SetEase(TweeningEase.InOutSine))
                
                -- 往下浮动到-30
                m.floatAnimation:Append(objModel.transform:DOLocalMoveY(m.objModelOriginalPos.y - 30, 1.5):SetEase(TweeningEase.InOutSine))
                
                -- 设置为无限循环
                m.floatAnimation:SetLoops(-1, TweeningLoopType.Yoyo)
            else
                -- 如果没有保存的初始位置，使用当前位置
                local originalPos = objModel.transform.localPosition
                
                -- 创建动画序列
                m.floatAnimation = DOTween.Sequence()
                
                -- 往上浮动到+30
                m.floatAnimation:Append(objModel.transform:DOLocalMoveY(originalPos.y + 30, 1.5):SetEase(TweeningEase.InOutSine))
                
                -- 往下浮动到-30
                m.floatAnimation:Append(objModel.transform:DOLocalMoveY(originalPos.y - 30, 1.5):SetEase(TweeningEase.InOutSine))
                
                -- 设置为无限循环
                m.floatAnimation:SetLoops(-1, TweeningLoopType.Yoyo)
            end
        end
    end, parameter)
end

--------------------------------------------------------------------
--更新角色
--------------------------------------------------------------------
function m.UpdateRole()
    if m.ItemRole ~= nil then
        m.ItemRole.Img_Select.gameObject:SetActive(false)    
    end

    local dataList = m.EquipWeaponIDList
    for i, v in ipairs(dataList) do
        m.Item_Role_List[i].UpdateData(v)
    end 
    
    m.UpdateTabButtonRed()
end

m.ItemRole = nil
m.curEquipID = nil
--------------------------------------------------------------------
--创建属性框
--------------------------------------------------------------------
function m.Creation_Item_Role(index)
    ---@class Item_Role
    local item = {}
    item.index = index
    item.com = m:CreateSubItem(m.objList.Grid_Tab, m.objList.Item_Tab)
    item.com.Img_Select.gameObject:SetActive(false)
    m:AddClick(item.com.Btn_Click, function()
        if m.ItemRole == item.com then return end
        if m.ItemRole ~= nil then
            m.ItemRole.Img_Select.gameObject:SetActive(false)
        end
        m.ItemRole = item.com
        m.ItemRole.Img_Select.gameObject:SetActive(true)
        m.ChangeUIModel(item.equipID)
        m.curEquipID = item.equipID
        m.equipID = item.equipID
        m.equipConfig = Schemes.Equipment:Get(m.equipID)
    --========================================================================
        m.XLSmeltID = m.equipConfig.SmeltBase    

        m.SelectBtnState(m.curIndex)
    end)

    m:AddClick(item.com.Btn_Battle, function()
        --m:Wear(item.equipID)

        GamePlayerData.ActorEquip:ReplaceEquipIndex(item.equipID, 1, 1)
        
        -- 执行【#逻辑值 200 X】，其中X值是本次操作出战的装备ID
        local chatContent = "#逻辑值 200 " .. item.equipID
        local msg = ChatMessage_pb.CS_Char_SendChat()
        msg.Channel = 12
        msg.Content = chatContent
        msg.ChatType = 0
        Premier.Instance:GetNetwork():SendFromLua(
            ENDPOINT.ENDPOINT_GAMECLIENT,
            ENDPOINT.ENDPOINT_GAMESERVER,
            MSG_MODULEID.MSG_MODULEID_CHAT,
            ChatMessage_pb.MSG_CHAT_SENDCHAT,
            msg:SerializeToString()
        )
        print("11111 装备出战-设置逻辑值200:", item.equipID, "命令:", chatContent)
        
        if m.ItemRole == item.com then return end
        if m.ItemRole ~= nil then
            m.ItemRole.Img_Select.gameObject:SetActive(false)
        end
        m.ItemRole = item.com
        m.ItemRole.Img_Select.gameObject:SetActive(true)
        m.ChangeUIModel(item.equipID)
        m.curEquipID = item.equipID
        m.equipID = item.equipID
        m.equipConfig = Schemes.Equipment:Get(m.equipID)
    --========================================================================
        m.XLSmeltID = m.equipConfig.SmeltBase    

        m.SelectBtnState(m.curIndex)
    end)
    ---更新数据
    ---@param equipID integer
    item.UpdateData = function(equipID)
        item.equipID = equipID
        if equipID then
            local cfg = Schemes.Equipment:Get(equipID)
            --local level = GamePlayerData.ActorEquipNew:GetEquipUpgradeLevel(cfg.SmeltBase)
            --local equipSmeltStar = Schemes.EquipSmeltStar:GetEquipSmeltStarCfg(cfg.SmeltBase, level)
            AtlasManager:AsyncGetSprite(cfg.IconID, item.com.Img_icon1)
            AtlasManager:AsyncGetSprite(cfg.IconID, item.com.Img_icon2)
            item.com.Txt_Name1.text = cfg.GoodsName
            item.com.Txt_Name2.text = cfg.GoodsName

            if GamePlayerData.ActorEquip:IsWear(equipID, 1) then
                item.com.Txt_Battle.text = "已出战"
            else
                item.com.Txt_Battle.text = "出战"
            end 

            item.com.gameObject:SetActive(true)
            -- local weaponsKnapsack = GamePlayerData.ActorEquip:GetWeaponsKnapsack(1) or {}
            -- local curEquipID = weaponsKnapsack[1] or 0
            if m.curEquipID == equipID and m.ItemRole == nil then                
                m.ItemRole = item.com
                m.curEquipID = equipID
                m.ItemRole.Img_Select.gameObject:SetActive(true)
            end
        else
            item.com.gameObject:SetActive(false)
        end

        item.UpdateRed()
    end

    item.UpdateRed = function()
        if m:Check_QHEquipRed(item.equipID) or m:Check_XLEquipRed(item.equipID) then
            item.com.Img_RedDot.gameObject:SetActive(true)
        else
            item.com.Img_RedDot.gameObject:SetActive(false)
        end
    end
    return item
end

function m:Wear(equipId)
    print("33333333 装备切换------开始切换装备到ID:", equipId)

    -- 获取装备配置验证
    local newEquipConfig = Schemes.Equipment:Get(equipId)
    if not newEquipConfig then
        print("33333333 装备切换------装备配置不存在，ID:", equipId)
        return
    end

    local newEquipWeaponConfig = Schemes.EquipWeapon:Get(equipId)
    if not newEquipWeaponConfig then
        print("33333333 装备切换------EquipWeapon配置不存在，ID:", equipId)
        return
    end

    -- 获取关联装备
    local associatedEquipIDs = m.GetAllAssociatedEquipIDs(equipId)
    print("33333333 装备切换------装备", equipId, "的关联装备:", table.concat(associatedEquipIDs, ","))

    -- 构建目标装备列表（主装备 + 所有关联装备）
    local targetEquipList = {equipId}
    for _, associatedID in ipairs(associatedEquipIDs) do
        table.insert(targetEquipList, associatedID)
    end

    print("33333333 装备切换------目标装备列表:", table.concat(targetEquipList, ","))

    -- 卸下所有当前装备
    for i = 1, #m.EquipWeaponIDList, 1 do
        if GamePlayerData.ActorEquip:IsWear(m.EquipWeaponIDList[i], 1) then
            --卸下装备
            GamePlayerData.ActorEquip:UnloadEquip(m.EquipWeaponIDList[i], 1)
        end
    end

    -- 延迟设置新装备（包括关联装备）
    m.smallTimer = Timer.New(function()
        print("33333333 装备切换------开始设置新装备列表")
        LogicValue.SetWeaponsKnapsack(targetEquipList)

        -- 确保gunID数据传递给战斗系统
        m.EnsureGunDataForEquipSwitch(targetEquipList, "装备切换")

        -- 设置逻辑值200
        local chatContent = "#逻辑值 200 " .. equipId
        local msg = ChatMessage_pb.CS_Char_SendChat()
        msg.Channel = 12
        msg.Content = chatContent
        msg.ChatType = 0
        Premier.Instance:GetNetwork():SendFromLua(
            ENDPOINT.ENDPOINT_GAMECLIENT,
            ENDPOINT.ENDPOINT_GAMESERVER,
            MSG_MODULEID.MSG_MODULEID_CHAT,
            ChatMessage_pb.MSG_CHAT_SENDCHAT,
            msg:SerializeToString()
        )
        print("33333333 装备切换------设置逻辑值200:", equipId, "命令:", chatContent)

        -- 延迟验证结果
        Timer.New(function()
            local updatedEquipIDs = GamePlayerData.ActorEquip:GetWeaponsKnapsack(1) or {}
            print("33333333 装备切换------切换后装备列表:", table.concat(updatedEquipIDs, ","))

            if #updatedEquipIDs == #targetEquipList then
                local allMatch = true
                for i, targetEquipID in ipairs(targetEquipList) do
                    if updatedEquipIDs[i] ~= targetEquipID then
                        allMatch = false
                        break
                    end
                end

                if allMatch then
                    print("33333333 装备切换------装备切换成功！")
                else
                    print("33333333 装备切换------装备切换可能存在问题，请检查")
                end
            else
                print("33333333 装备切换------装备切换可能存在问题，数量不匹配")
            end
        end, 0.5, 1):Start()
    end, 0.5, 1)
    m.smallTimer:Start()
end


--------------------------------------------------------------------
-- 注册点击事件
--------------------------------------------------------------------
function m.RegisterClickEvent()
    m:AddClick(m.objList.Btn1, function()
        m.SelectBtnState(1)
    end)
    m:AddClick(m.objList.Btn2, function()
        m.SelectBtnState(2)
    end)
    m:AddClick(m.objList.Btn_QH, function()
        m.EquipUpgrade(m.equipID)
    end)

    m:AddClick(m.objList.Btn_XL, function()
        m.UpgradeXL(m.XLSmeltID)
    end)
end

function m.SelectBtnState(index)
    m.curIndex = index
    m.objList.Img_Select1.gameObject:SetActive(index == 1)
    m.objList.Img_Select2.gameObject:SetActive(index == 2)
    m.objList.QHGroup.gameObject:SetActive(index == 1)
    m.objList.XLGroup.gameObject:SetActive(index == 2)
    if index == 1 then
        m.UpdateQHInfo()
    end

    if index == 2 then
        m.UpdateXLInfo()
    end

    m.UpdateTabButtonRed()
end

function m.UpdateTabButtonRed()
    if m:Check_QHEquipRed(m.equipID) then
        m.objList.Img_Red1.gameObject:SetActive(true)
    else
        m.objList.Img_Red1.gameObject:SetActive(false)
    end

    if m:Check_XLEquipRed(m.equipID) then
        m.objList.Img_Red2.gameObject:SetActive(true)
    else
        m.objList.Img_Red2.gameObject:SetActive(false)
    end
end

--------------------------------------------------------------------
-- 更新界面
--------------------------------------------------------------------
function m.UpdateView()
    local dataList = m.EquipWeaponIDList
    for i, v in ipairs(dataList) do
        m.Item_Role_List[i].UpdateData(v)
    end 

    print("==========UpdateView================")

    m.UpdateQHInfo()
    m.UpdateXLInfo()
    m.UpdateTabButtonRed()
end


--检测--Equip--红点
function m:Check_QHEquipRed(equipId)
    local equipment = Schemes.Equipment:Get(equipId)
    local entity = SkepModule:GetSkepByID(equipment.PacketID):GetEntityByGoodsID(equipment.ID)
    if not entity then
        warn('未找到装备实体 equipID=', equipID)
        return false
    end
    local quality = entity:GetProperty(EQUIPMENT_FIELD.EQUIPMENT_FIELD_QUALITY)
    local starNum = entity:GetProperty(EQUIPMENT_FIELD.EQUIPMENT_FIELD_STARNUM)
    local smelt = Schemes.EquipSmelt:Get(equipment.SmeltID, quality, starNum)
    if not smelt then
        warn('未找到装备升星 EquipSmelt 配置 SmeltID=', equipment.SmeltID, quality, starNum)
        return false
    end
    if smelt.LevelMaxExp == -1 then
        return false
    end
    if HelperL.IsLackGoods(smelt.GoodsID, smelt.GoodsNum, false, false) then 
        return false
    end

    if HelperL.IsLackGoods(4, smelt.CostMoney, false, false) then 
        return false
    end

    return true
end

function m:Check_XLEquipRed(equipId)  
    local cfg = Schemes.Equipment:Get(equipId)
    local level = GamePlayerData.ActorEquipNew:GetEquipUpgradeLevel(cfg.SmeltBase)
    local maxLevel = PropertyCompute.GetEquipSmeltStarMaxLevel(cfg.SmeltBase)
    if level >= maxLevel - 1 then
        return false
    end

    local count = HeroDataManager:GetLogicData(LOGIC_DATA.DATA_RECHARGEDRAW_COUNT)
    if count >= 8 then return false end

    local equipSmeltStar = Schemes.EquipSmeltStar:GetEquipSmeltStarCfg(cfg.SmeltBase, level + 1)
    if equipSmeltStar then
        if HelperL.IsLackGoods(equipSmeltStar.CostGoodsID1, equipSmeltStar.CostGoodsID1Num, false, false) then
            return false
        end
        
        if HelperL.IsLackGoods(equipSmeltStar.CostGoodsID2, equipSmeltStar.CostGoodsID2Num, false, false) then
            return false
        end
    end

    return true
end
--------------------------------------------------------------------
--显示强化信息
--------------------------------------------------------------------
function m.UpdateQHInfo()
    m.objList.Btn_QH.gameObject:SetActive(false)    
    local equipment = Schemes.Equipment:Get(m.equipID)
    local quality = equipment.Quality
    local starNum = equipment.StarNum
    local level = 0
    local entity = SkepModule:GetSkepByID(equipment.PacketID):GetEntityByGoodsID(equipment.ID)
    --装备已激活
    if entity then
        quality = entity:GetProperty(EQUIPMENT_FIELD.EQUIPMENT_FIELD_QUALITY)
        starNum = entity:GetProperty(EQUIPMENT_FIELD.EQUIPMENT_FIELD_STARNUM)
        level = quality * 10 + starNum + 1
    end
    m.curLevel = level
    print("level ===== "..level)
    print("equipment.SmeltID ===== "..equipment.SmeltID)

    local smelt = Schemes.EquipSmelt:Get(equipment.SmeltID, quality, starNum)
    print("smelt.LevelMaxExp ===== "..smelt.LevelMaxExp)
    if smelt.LevelMaxExp ~= -1 then
        m.objList.Obj_Cur:SetActive(true)
        --m.objList.Obj_Next:SetActive(true)
        local equipSmeltStar = Schemes.EquipSmeltStar:GetEquipSmeltStarCfg(equipment.SmeltID, level)
        local str = Schemes.CommonProp:Get(equipSmeltStar.PropId).Remark
        local list = HelperL.Split( str,"\\n")
        str = ""
        for i = 1, #list do
            if i < #list then
                str = str..list[i].."\n"
            else
                str =  str..list[i]
            end
        end
        m.objList.Txt_QHLevel.text = level
        m.objList.Txt_CurDesc.text = str
        local nextSmelt = Schemes.EquipSmelt:Get(equipment.SmeltID, smelt.NextQuality, smelt.NextStarNum)  
        equipSmeltStar = Schemes.EquipSmeltStar:GetEquipSmeltStarCfg(equipment.SmeltID, level+1)
        str = Schemes.CommonProp:Get(equipSmeltStar.PropId).Remark
        local list = HelperL.Split( str,"\\n")
        str = ""
        for i = 1, #list do
            if i < #list then
                str = str..list[i].."\n"
            else
                str =  str..list[i]
            end
        end      
        m.objList.Txt_NextDesc.text = str
        m.objList.Txt_QHExpend.gameObject:SetActive(true)
        m.objList.Txt_QHExpend1.gameObject:SetActive(true)
        m.objList.Btn_QH.gameObject:SetActive(true)
        AtlasManager:AsyncGetGoodsSprite(nextSmelt.GoodsID, m.objList.Img_QHExpend)
        AtlasManager:AsyncGetGoodsSprite(4, m.objList.Img_QHExpend1)
        local num1 = SkepModule:GetGoodsCount(nextSmelt.GoodsID)
        local num2 = SkepModule:GetGoodsCount(4)
        local bool1 = not HelperL.IsLackGoods(nextSmelt.GoodsID, nextSmelt.GoodsNum, false, false)
        local bool2 = not HelperL.IsLackGoods(4, nextSmelt.CostMoney, false, false)
        m.objList.Txt_QHExpend1.text = string.format("<color=%s>%s</color>/%s", bool2 and "#FFFFFF" or "#F1A92F",
            HelperL.GetChangeNum(num2), HelperL.GetChangeNum(nextSmelt.CostMoney))
        m.objList.Txt_QHExpend.text = string.format("<color=%s>%s</color>/%s", bool2 and "#FFFFFF" or "#F1A92F",
            HelperL.GetChangeNum(num1), HelperL.GetChangeNum(nextSmelt.GoodsNum))
        if bool1 and bool2 then
            --m.objList.Img_UpgradeRedDot.gameObject:SetActive(true)
            m.objList.Img_QHGray.gameObject:SetActive(false)
        else
            --m.objList.Img_Expend.gameObject:SetActive(true)
            --m.objList.Img_Gray.gameObject:SetActive(true)
            m.objList.Img_QHGray.gameObject:SetActive(true)
        end
       -- m.objList.Obj_Cur.gameObject.transform.localPosition = Vector3(-253,-362,0)
        m.objList.Txt_MaxDesc.gameObject:SetActive(false)
    else
        m.objList.Obj_Cur:SetActive(true)
        --m.objList.Obj_Cur.gameObject.transform.localPosition = Vector3(0,-362,0)
        m.objList.Obj_Next:SetActive(false)
        m.objList.Txt_MaxDesc.gameObject:SetActive(true)
        m.objList.Txt_QHExpend.gameObject:SetActive(false)
        m.objList.Txt_QHExpend1.gameObject:SetActive(false)

        local equipSmeltStar = Schemes.EquipSmeltStar:GetEquipSmeltStarCfg(equipment.SmeltID, level)
        local str = Schemes.CommonProp:Get(equipSmeltStar.PropId).Remark
        local list = HelperL.Split( str,"\\n")
        str = ""
        for i = 1, #list do
            if i < #list then
                str = str..list[i].."\n"
            else
                str =  str..list[i]
            end
        end
        m.objList.Txt_CurDesc.text = str
    end
end

function m.UpdateXLInfo()    
    local starLvl = GamePlayerData.ActorEquipNew:GetEquipUpgradeLevel(m.XLSmeltID)
    warn("天赋升级:未找到升星配置 starLvl=", starLvl)
    local equipSmeltStar = Schemes.EquipSmeltStar:GetEquipSmeltStarCfg(m.XLSmeltID, starLvl)
    if equipSmeltStar == nil then
        warn("天赋升级:未找到升星配置 smeltID=", m.XLSmeltID, starLvl)
        return
    end

    local bool1 = HelperL.IsLackGoods(
        equipSmeltStar.CostGoodsID1, equipSmeltStar.CostGoodsID1Num, false, false)
    -- local bool2 = HelperL.IsLackGoods(
    --     equipSmeltStar.CostGoodsID2, equipSmeltStar.CostGoodsID2Num, false, false)
    local str = Schemes.CommonProp:Get(equipSmeltStar.PropId).Remark
    local list = HelperL.Split( str,"\\n")
    str = ""
    for i = 1, #list do
        if i < #list then
            str = str..list[i].."\n"
        else
            str =  str..list[i]
        end
    end
    m.objList.Txt_XLPro.text = str
    
    local num = SkepModule:GetGoodsCount(equipSmeltStar.CostGoodsID1)
    local count = HeroDataManager:GetLogicData(LOGIC_DATA.DATA_RECHARGEDRAW_COUNT)  
    if count >= 8 or bool1 then
        m.objList.Img_XLGray.gameObject:SetActive(true)
    else
        m.objList.Img_XLGray.gameObject:SetActive(false)
    end
    --m.objList.Img_XLGray.gameObject:SetActive(bool1 or count >= 8)
    AtlasManager:AsyncGetGoodsSprite(equipSmeltStar.CostGoodsID1, m.objList.Img_XLExpend)
    --AtlasManager:AsyncGetGoodsSprite(equipSmeltStar.CostGoodsID2, m.objList.Img_XLExpend1)
    m.objList.Txt_XLExpend.text = string.format("<color=%s>%s/%s</color>", color,HelperL.GetChangeNum(num), HelperL.GetChangeNum(equipSmeltStar.CostGoodsID1Num))
    --m.objList.Txt_XLExpend1.text = string.format("<color=%s>%s/%s</color>", color,HelperL.GetChangeNum(num1), HelperL.GetChangeNum(equipSmeltStar.CostGoodsID2Num))
    m.objList.Txt_Num.text = "今日剩余次数："..(8 - count)

end
--------------------------------------------------------------------
---装备升星
---@param equipID integer 装备ID
--------------------------------------------------------------------
function m.EquipUpgrade(equipId)
    local curTime = os.clock()
    if curTime - m.lastTime < 1 then
        print('点击太快了，请1秒后再点击！')
        return
    end
    m.lastTime = curTime
    
    local equipment = Schemes.Equipment:Get(equipId)
    local entity = SkepModule:GetSkepByID(equipment.PacketID):GetEntityByGoodsID(equipment.ID)
    if not entity then
        warn('未找到装备实体 equipID=', equipID)
        return
    end
    local quality = entity:GetProperty(EQUIPMENT_FIELD.EQUIPMENT_FIELD_QUALITY)
    local starNum = entity:GetProperty(EQUIPMENT_FIELD.EQUIPMENT_FIELD_STARNUM)
    local smelt = Schemes.EquipSmelt:Get(equipment.SmeltID, quality, starNum)
    if not smelt then
        warn('未找到装备升星 EquipSmelt 配置 SmeltID=', equipment.SmeltID, quality, starNum)
        return
    end
    if smelt.LevelMaxExp == -1 then
        return
    end
    if HelperL.IsLackGoods(smelt.GoodsID, smelt.GoodsNum, false) then 
        return 
    end

    if HelperL.IsLackGoods(4, smelt.CostMoney, false) then 
        return 
    end
    local str_req = string.format(
        "LuaRequestEquipStarUp?equip=%s&type=%s&bindflag=%s&AutoBuy=%s&LoopTimes=%d&WishType=%s",
        entity.uid, 0, 0, 0, 1, 2)
    LuaModule.RunLuaRequest(str_req, m.RequestCallback1)
end


--------------------------------------------------------------------
--请求回调
--------------------------------------------------------------------
function m.RequestCallback1(resultCode, content)
    if resultCode == RESULT_CODE_BASE.RESULT_COMMON_SUCCEED[1] then
        
        HelperL.PlayVFX(VFX_Type.QH_FX)
        HelperL.ShowMessage(TipType.FlowText, '强化等级+1。')
    else
        ResultCode:DefaultShowResultCode(resultCode, content)
    end
end



--------------------------------------------------------------------
--请求回调
--------------------------------------------------------------------
function m.RequestXLCallback(resultCode, content)
    if resultCode == RESULT_CODE_BASE.RESULT_COMMON_SUCCEED[1] then
        --m.UpdateView()  
        HelperL.PlayVFX(VFX_Type.PYJS_FX)
        HelperL.ShowMessage(TipType.FlowText, '洗练成功。')
        local count = HeroDataManager:GetLogicData(LOGIC_DATA.DATA_RECHARGEDRAW_COUNT)
        local str_req2 = string.format('LuaRequestSetLogicData?logicID=%d&logicValue=%d', LOGIC_DATA.DATA_RECHARGEDRAW_COUNT,count+1)
        LuaModule.RunLuaRequest(str_req2, function(resultCode2, content2)
            print("resultCode2 = ", resultCode2)
            if resultCode2 == RESULT_CODE_BASE.RESULT_COMMON_SUCCEED[1] then
                m.UpdateXLInfo()    
            end
        end)
    else
        ResultCode:DefaultShowResultCode(resultCode, content)
    end
end

--------------------------------------------------------------------
--xl升级
---@param smeltID integer 升星ID
--------------------------------------------------------------------
function m.UpgradeXL(smeltID)
    local curTime = Premier.Instance:GetServerTime()
    if curTime - m.lastTime < 1 then
        print('天赋升级点太快了1秒后尝试！')
        return
    end
    m.lastTime = curTime

    local starLvl = GamePlayerData.ActorEquipNew:GetEquipUpgradeLevel(smeltID)
    local equipSmeltStar = Schemes.EquipSmeltStar:GetEquipSmeltStarCfg(smeltID, starLvl)
    if equipSmeltStar == nil then
        warn("天赋升级:未找到升星配置 smeltID=", smeltID, starLvl)
        return
    end

    if HelperL.IsLackGoods(equipSmeltStar.CostGoodsID1, equipSmeltStar.CostGoodsID1Num, false) then 
        return 
    end
    local count = HeroDataManager:GetLogicData(LOGIC_DATA.DATA_RECHARGEDRAW_COUNT)
    if count >= 8 then return end
    
    m.lastStarLvl = starLvl
    m.lastSmeltID = smeltID

    local form = {}
    form["smeltID"] = smeltID
    form["loopTimes"] = 1
    LuaModuleNew.SendRequest(LuaRequestID.NewCardAddExp2, form, m.RequestXLCallback)
end

--------------------------------------------------------------------
-- 窗口关闭时清理资源
--------------------------------------------------------------------
function m:OnClose()
    -- 停止模型浮动动画，防止内存泄漏
    if m.floatAnimation then
        m.floatAnimation:Kill()
        m.floatAnimation = nil
    end
end

--------------------------------------------------------------------
-- 获取装备的所有关联装备ID（支持EffectID2和EffectID3多层关联）
--------------------------------------------------------------------
function m.GetAllAssociatedEquipIDs(equipId)
    local associatedEquipIDs = {}
    local currentEquipId = equipId
    local visitedEquipIDs = {} -- 防止循环引用
    local maxDepth = 10 -- 最大关联深度
    local currentDepth = 0

    print("33333333 装备关联检查------开始获取装备", equipId, "的所有关联装备")

    while currentDepth < maxDepth do
        -- 循环引用检测
        if visitedEquipIDs[currentEquipId] then
            print("33333333 装备关联检查------检测到循环引用，停止查找关联装备，当前ID=", currentEquipId)
            break
        end
        visitedEquipIDs[currentEquipId] = true

        -- 获取装备配置
        local equipWeaponConfig = Schemes.EquipWeapon:Get(currentEquipId)
        if not equipWeaponConfig then
            print("33333333 装备关联检查------未找到EquipWeapon配置，装备ID=", currentEquipId)
            break
        end

        local nextEquipId = nil
        local hasAssociated = false

        -- 优先检查EffectID2
        if equipWeaponConfig.EffectID2 and equipWeaponConfig.EffectID2 ~= 0 then
            local associatedId = equipWeaponConfig.EffectID2
            table.insert(associatedEquipIDs, associatedId)
            print("33333333 装备关联检查------找到EffectID2关联装备", associatedId, "(关联于装备", currentEquipId, ")")
            if not nextEquipId then
                nextEquipId = associatedId
            end
            hasAssociated = true
        end

        -- 检查EffectID3
        if equipWeaponConfig.EffectID3 and equipWeaponConfig.EffectID3 ~= 0 then
            local associatedId = equipWeaponConfig.EffectID3
            table.insert(associatedEquipIDs, associatedId)
            print("33333333 装备关联检查------找到EffectID3关联装备", associatedId, "(关联于装备", currentEquipId, ")")
            if not nextEquipId then
                nextEquipId = associatedId
            end
            hasAssociated = true
        end

        if hasAssociated and nextEquipId then
            currentEquipId = nextEquipId
            currentDepth = currentDepth + 1
            print("33333333 装备关联检查------继续查找装备", nextEquipId, "的关联装备，当前深度=", currentDepth)
        else
            print("33333333 装备关联检查------装备", currentEquipId, "无关联装备，停止查找")
            break
        end
    end

    if currentDepth >= maxDepth then
        print("33333333 装备关联检查------达到最大关联深度", maxDepth, "，停止查找")
    end

    print("33333333 装备关联检查------装备", equipId, "共找到", #associatedEquipIDs, "个关联装备:", table.concat(associatedEquipIDs, ","))
    return associatedEquipIDs
end

--------------------------------------------------------------------
-- 确保gunID数据传递给战斗系统（装备切换专用）
--------------------------------------------------------------------
function m.EnsureGunDataForEquipSwitch(equipList, triggerType)
    print("33333333", triggerType, "------开始确保gunID数据传递给战斗系统")

    local gunDataList = {}
    for _, equipID in ipairs(equipList) do
        -- 获取装备配置
        local equipConfig = Schemes.Equipment:Get(equipID)
        if equipConfig and equipConfig.ConsignmentStyle and equipConfig.ConsignmentStyle > 0 then
            local gunID = equipConfig.ConsignmentStyle
            print("33333333", triggerType, "------装备ID", equipID, "对应gunID:", gunID)

            -- 获取gun配置验证
            local gunConfig = Schemes.Gun:Get(gunID)
            if gunConfig then
                table.insert(gunDataList, {
                    equipID = equipID,
                    gunID = gunID,
                    goodsID = gunConfig.GoodsID
                })
                print("33333333", triggerType, "------gunID", gunID, "配置验证成功，goodsID:", (gunConfig.GoodsID or 0))
            else
                print("33333333", triggerType, "------警告：gunID", gunID, "配置不存在")
            end
        else
            print("33333333", triggerType, "------警告：装备ID", equipID, "没有有效的ConsignmentStyle")
        end
    end

    print("33333333", triggerType, "------gunID数据传递完成，共处理", #gunDataList, "个装备")

    -- 确保战斗系统能正确接收到所有关联装备的gunID
    if #gunDataList > 0 then
        print("33333333", triggerType, "------所有装备对应的gunID已准备就绪，战斗系统可以正常使用这些装备的技能")
    end
end

return m
