﻿using System;
using System.Linq;
using System.Threading;

using Apq.Unity3D.UnityHelpers;

using Cysharp.Threading.Tasks;

using DG.Tweening;

using HotScripts;

using UnityEngine;

using X.PB;

namespace View
{
    /// <summary>
    ///     怪物阵营的子弹:围击炸弹
    /// </summary>
    public class MonsterSiegeBomb : MonsterLocusBullet
    {
        public Vector3 TargetPosition { get; set; }

        /// <inheritdoc />
        public override async UniTask OnAfterInitView()
        {
            await base.OnAfterInitView();

            // 发射时不让怪物移动
            MonsterThing.Monster.MonsterMoveAI.StopMoveAI();

            float bulletLocusDuration = (float)GunThing.GetTotalDouble(PropType.BulletLocusDuration).FirstOrDefault();
            if (bulletLocusDuration > 0)
            {
                // 落点预示
                EffectMgr.Instance.ShowEffect(EffectPath.BornCircle, TargetPosition, 2, null, bulletLocusDuration)
                    .Forget();

                await UniTask.Delay(TimeSpan.FromSeconds(bulletLocusDuration));
            }
        }

        /// <inheritdoc />
        public override async UniTaskVoid TurnToPool()
        {
            try
            {
                await UniTask.WaitForEndOfFrame(SingletonMgr.Instance.GlobalMgr);
                
                gameObject.SetActive(false);

                // base.TurnToPool().Forget();
                // 隐藏导弹发射器(坐标系对象)
                // MissileLocusGenerator.MissileEjector.gameObject.SetActive(false);

                // 延时1分钟后置为null
                await UniTask.Delay(TimeSpan.FromMinutes(1));
                Thing = null;
            }
            catch (OperationCanceledException) { throw; }
            catch (MissingReferenceException) { }
            catch (Exception ex)
            {
                Debug.LogException(ex);
            }
            //catch { }
        }

        #region 爆炸

        /// <summary>
        ///     在目标位置爆炸后归还到子弹池
        /// </summary>
        /// <param name="token"></param>
        public void DoExplose(CancellationToken token)
        {
            // 爆炸声音
            string exploseSound = BulletThing.CdExecutor.Thing.GetTotalString(PropType.ExploseSound).FirstOrDefault();
            AudioPlayer.Instance.PlaySound(exploseSound).Forget();
            // 爆炸特效
            string exploseEffect = BulletThing.CdExecutor.Thing.GetTotalString(PropType.ExploseEffect).FirstOrDefault();
            // 爆炸半径
            float exploseRadius =
                (float)BulletThing.CdExecutor.Thing.GetTotalDouble(PropType.ExploseRadius).FirstOrDefault();

            if (!string.IsNullOrWhiteSpace(exploseEffect))
            {
                EffectMgr.Instance
                    .ShowEffect(EffectMgr.Instance.GetEffectPath(exploseEffect), TargetPosition, exploseRadius)
                    .Forget();
            }

            // 找出被炸到的敌人
            if (SingletonMgr.Instance.BattleMgr.Actor.CircularArea2D.IsIntersect(new CircularArea2D
                {
                    Center = TargetPosition, Radius = exploseRadius
                }))
            {
                (double damage, bool isCritical) =
                    Helper.CalcDamage(BulletThing, 0, 1, SingletonMgr.Instance.BattleMgr.Actor);

                // 受击方先接受枪携带的Buff
                SingletonMgr.Instance.BattleMgr.Actor.ReceiveBuffByBulletHit(BulletThing.CdExecutor.Thing,
                    BuffRecvType.Explose);

                // 敌人接受伤害
                SingletonMgr.Instance.BattleMgr.Actor.TakeHit(BulletThing.CdExecutor.Thing, damage, isCritical);
            }

            // 爆炸后出生新怪
            SingletonMgr.Instance.BattleMgr.StageRound.MonsterSpawner.BornMonster(
                TargetPosition, 3,
                BulletThing.CdExecutor.Thing.GetTotalLong(PropType.ExploseMonsterList).ConvertAll(x => (int)x),
                BulletThing.CdExecutor.Thing.GetTotalLong(PropType.ExploseMonsterCountList).ConvertAll(x => (int)x)
            );

            // 归还子弹池
            TurnToPool().Forget();
        }

        #endregion

        #region 移动

        public override void StartMove()
        {
            base.StartMove();

            // 允许怪物移动
            MonsterThing.Monster.MonsterMoveAI.StartMove();
        }

        /// <summary>
        ///     任务实现
        /// </summary>
        public override async UniTaskVoid DoTask_Move(CancellationToken token)
        {
            try
            {
                await UniTask.SwitchToMainThread();
                // 待移动的向量(方向和长度)
                Vector3 dir = TargetPosition - transform.position;
                float distance = dir.magnitude;
                // 速度
                float speed = (float)BulletThing.CdExecutor.Thing.GetTotalDouble(PropType.BulletSpeed).FirstOrDefault();
                float duration = distance / speed;
                transform.DOMove(TargetPosition, duration).OnComplete(() =>
                {
                    DoExplose(token);
                });
            }
            catch (OperationCanceledException)
            {
                throw;
            }
            catch (MissingReferenceException)
            {
            }
            catch (Exception ex)
            {
                Debug.LogException(ex);
            }
            finally
            {
                OnMoveEnd();
            }
        }

        #endregion
    }
}