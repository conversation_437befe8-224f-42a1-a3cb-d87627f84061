// ReSharper disable InconsistentNaming

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;

using Apq.Extension;
using Apq.Unity3D.UnityHelpers;

using Cysharp.Threading.Tasks;

using DataStructure;

using Props;

using RxEventsM2V;

using Thing;

using UniRx;

using UnityEngine;

using View;

using X.PB;

namespace ThingCdExecutors
{
    /// <summary>
    ///     角色阵营的抛物线子弹射击
    /// </summary>
    public class ThingCdExecutor_6 : ActorGunCdExecutor
    {
        /// <inheritdoc />
        public override async UniTaskVoid DoShoot(CancellationToken token)
        {
            Debug.Log($"111111111 ShootMethod=6 DoShoot开始 - Actor:{Actor?.GetHashCode()} Thing:{Thing?.GetHashCode()} ShootMethod:6");
            
            // 使用新的目标选择逻辑
            var (targetEnemy, targetPos) = FindParabolicTarget();

            // 确保有目标位置，即使没有找到敌人也要有默认位置
            Debug.Log($"111111111 抛物线射击目标确定 - Actor:{Actor?.GetHashCode()} Target:{targetEnemy?.GetHashCode()} TargetPos:{targetPos}");

            // 按一轮攻击的持续时长预设结束时间
            base.DoShoot(token).Forget();

            await UniTask.SwitchToMainThread();

            // 确保此方法用于角色的枪
            if (Actor == null)
            {
                Debug.Log($"111111111 抛物线射击失败 - Actor为空");
                return;
            }

            CancellationTokenSource cts_Skill = CancellationTokenSource.CreateLinkedTokenSource(
                CTS_Shooter.Token
                , Actor.ThingBehaviour.GetCancellationTokenOnDestroy()
            );

            try
            {
                // 射击的基准方向始终是向X轴正方向
                Vector3 shootDir = Vector3.right;

                int shootTimes = (int)Thing.GetTotalLong(PropType.BurstShootTimes).FirstOrDefault();
                List<double> burstDelayList = Thing.GetTotalDouble(PropType.BurstDelayList);
                List<long> burstBulletCountList = Thing.GetTotalLong(PropType.BurstBulletCountList);
                // 各轮射击角度的ID列表
                List<int> burstAnglesIds =
                    Thing.GetTotalLong(PropType.BurstAnglesIdList).Select(x => (int)x).ToList();

                Debug.Log($"111111111 抛物线射击配置 - ShootTimes:{shootTimes} DelayCount:{burstDelayList.Count} BulletCountList:{string.Join(",", burstBulletCountList)} AnglesIds:{string.Join(",", burstAnglesIds)} ShootDir:{shootDir}");

                // 根据配置延时后射击
                _ = burstDelayList.Take(shootTimes).Select((x, i) =>
                {
                    if (burstBulletCountList.Count <= i)
                    {
                        //Debug.Log($"88888 抛物线射击跳过 - 第{i}轮缺少子弹数量配置");
                        return i;
                    }

                    long bulletQty = burstBulletCountList[i];

                    //Debug.Log($"88888 抛物线射击启动第{i}轮 - Delay:{x}s BulletQty:{bulletQty} TargetPos:{targetPos} AngleId:{burstAnglesIds.IndexOf_ByCycle(i)} (轮次间延时:{x}s, 轮内{bulletQty}颗子弹同时发射)");

                    // BurstDelayList用于不同轮次之间的延时，同一轮内的多颗子弹同时发射
                    BurstOne(cts_Skill.Token, (float)x, targetEnemy, targetPos, shootDir, bulletQty,
                            burstAnglesIds.IndexOf_ByCycle(i))
                        .Forget();
                    return i;
                }).ToList();
            }
            catch (Exception ex)
            {
                //Debug.LogError($"88888 抛物线射击异常 - {ex.Message} StackTrace:{ex.StackTrace}");
            }
        }

        /// <summary>
        /// 计算抛物线子弹的目标坐标
        /// </summary>
        /// <param name="target">目标敌人</param>
        /// <returns>计算后的目标坐标</returns>
        private Vector3 CalculateParabolicTarget(ThingBase target)
        {
            // 角色当前位置
            Vector3 actorPos = Actor.Position;
            
            // 枪的射程
            float gunRange = (float)Thing.GetTotalDouble(PropType.GunRange).FirstOrDefault();
            
            // 基础终点：角色X坐标+8（向右射击的最小距离）
            float baseTargetX = actorPos.x + 8f;
            
            // 目标怪物的X坐标
            float monsterX = target.Position.x;
            
            // 终点X坐标：在角色X+8和目标怪物X之间
            float targetX = Mathf.Max(baseTargetX, monsterX);
            
            // 限制在射程范围内
            float maxX = actorPos.x + gunRange;
            targetX = Mathf.Min(targetX, maxX);
            
            // 终点坐标：Y轴保持角色当前Y坐标，X轴使用计算的目标X（X是射击距离变量）
            return new Vector3(targetX, actorPos.y, actorPos.z);
        }

        /// <summary>
        /// 查找最适合的抛物线攻击目标
        /// </summary>
        /// <returns>目标怪物和目标坐标</returns>
        private (ThingBase target, Vector3 targetPos) FindParabolicTarget()
        {
            // 角色当前位置
            Vector3 actorPos = Actor.Position;
            
            Debug.Log($"111111111 V64.9角色坐标调试 - Actor.Position: {actorPos}");
            
            // 使用Actor.Position作为角色位置（保持原有逻辑）
            float gunRange = (float)Thing.GetTotalDouble(PropType.GunRange).FirstOrDefault();
            float minTargetX = actorPos.x + 8f; // 最小目标X坐标（向右射击的最小距离）
            float maxTargetX = actorPos.x + gunRange; // 最大目标X坐标

            // 获取所有活跃的怪物
            var allMonsters = SingletonMgr.Instance.BattleMgr.Monsters
                .Where(m => m != null && m.ThingBehaviour != null && m.ThingBehaviour.gameObject.activeInHierarchy)
                .ToList();

            Debug.Log($"111111111 V64.9抛物线目标搜索 - 总怪物数:{allMonsters.Count}");

            if (allMonsters.Count == 0)
            {
                // 没有怪物，使用GunRange最大值作为默认目标位置
                Vector3 defaultTarget = new Vector3(maxTargetX, actorPos.y, actorPos.z);
                Debug.Log($"111111111 V64.9无怪物默认目标 - 目标坐标:{defaultTarget} X距离:{gunRange:F1}");
                return (null, defaultTarget);
            }

            // 过滤满足X坐标条件的怪物（在射击方向上）
            var validMonsters = allMonsters
                .Where(m => m.Position.x >= minTargetX && m.Position.x <= maxTargetX)
                .ToList();

            Debug.Log($"111111111 V64.9抛物线目标搜索 - 总怪物数:{allMonsters.Count} X范围内怪物数:{validMonsters.Count}");
            
            // 从满足X坐标条件的怪物中选择与角色X坐标距离最近的
            ThingBase bestTarget = null;
            float minDistance = float.MaxValue;
            foreach (var monster in validMonsters)
            {
                float xDistance = Mathf.Abs(monster.Position.x - actorPos.x);
                if (xDistance < minDistance)
                {
                    minDistance = xDistance;
                    bestTarget = monster;
                }
            }
            
            Debug.Log($"111111111 V64.9抛物线目标选择 - 最近怪物:{bestTarget?.GetHashCode()} XDistance:{minDistance:F1} MonsterPos:{bestTarget?.Position}");

            Vector3 targetPos;
            if (bestTarget != null)
            {
                // 计算目标坐标：Y为角色Y坐标，X为限制在范围内的怪物X坐标
                float targetX = Mathf.Clamp(bestTarget.Position.x, minTargetX, maxTargetX);
                targetPos = new Vector3(targetX, actorPos.y, actorPos.z);
                
                Debug.Log($"111111111 V64.9最终目标位置 - 怪物:{bestTarget.GetHashCode()} 目标坐标:{targetPos} X距离:{Mathf.Abs(targetX - actorPos.x):F1}");
            }
            else
            {
                // 没有找到怪物，使用GunRange最大值作为默认目标位置
                targetPos = new Vector3(maxTargetX, actorPos.y, actorPos.z);
                Debug.Log($"111111111 V64.9默认目标位置 - 目标坐标:{targetPos} X距离:{Mathf.Abs(maxTargetX - actorPos.x):F1}");
            }

            return (bestTarget, targetPos);
        }

        /// <summary>
        ///     发射一轮
        /// </summary>
        /// <param name="token"></param>
        /// <param name="delay">延时:秒</param>
        /// <param name="attackBaseDirFollowThing">攻击基准方向跟随哪个物件(没值则取移动方向)</param>
        /// <param name="trackPos">定点位置</param>
        /// <param name="shootBaseDir">射击的基准方向</param>
        /// <param name="bulletQty">发射的子弹数量</param>
        /// <param name="anglesPropId">往哪个角度发射</param>
        private async UniTaskVoid BurstOne(CancellationToken token, float delay, ThingBase attackBaseDirFollowThing,
            Vector3? trackPos, Vector3 shootBaseDir, float bulletQty, int anglesPropId)
        {
            try
            {
                await UniTask.Delay(TimeSpan.FromSeconds(delay), cancellationToken: token);

                if (token.IsCancellationRequested)
                {
                    return;
                }

                // 没有发射角度就不射击
                if (!SingletonMgr.Instance.GlobalMgr.CommonPropCfg.TryGetValue(anglesPropId, out CommonProp anglesProp))
                {
                    return;
                }

                // 开火声音
                MessageBroker.Default.Publish(new PlayShootSound { Shooter = this });

                List<float> angles = anglesProp.DoubleValues
                    .Select(x => (float)x).ToList();

                for (int z = 0; z < bulletQty; z++)
                {
                    float angle = angles.IndexOf_ByCycle(z);
                    int maxPenetrateTimes = (int)Thing.GetTotalLong(PropType.MaxPenetrateTimes).FirstOrDefault();
                    int maxBounceTimes = (int)Thing.GetTotalLong(PropType.MaxBounceTimes).FirstOrDefault();
                    int maxSeparateTimes = (int)Thing.GetTotalLong(PropType.MaxSeparateTimes).FirstOrDefault();

                    // V64.10 根据角度计算不同的目标点
                    Vector3? bulletTrackPos = null;
                    if (trackPos.HasValue)
                    {
                        Vector3 actorPos = Actor.Position;
                        Vector3 zeroAngleTarget = trackPos.Value; // 0度角的目标点（敌人位置）
                        
                        if (Mathf.Abs(angle) < 0.1f) // 0度角子弹
                        {
                            // 0度角子弹飞向目标敌人
                            bulletTrackPos = zeroAngleTarget;
                            Debug.Log($"111111111 V64.10子弹角度0度 - 第{z}颗 角度:{angle:F1}° 目标:敌人位置{zeroAngleTarget}");
                        }
                        else // 其他角度子弹
                        {
                            // 计算以角色为圆心、到0度目标点距离为半径的圆上的目标点
                            float radius = Vector3.Distance(actorPos, zeroAngleTarget);
                            
                            // 将角度转换为弧度并应用到XY平面
                            float angleRad = angle * Mathf.Deg2Rad;
                            
                            // 计算圆周上的目标点（在XY平面上）
                            float targetX = actorPos.x + radius * Mathf.Cos(angleRad);
                            float targetY = actorPos.y + radius * Mathf.Sin(angleRad);
                            float targetZ = actorPos.z; // Z坐标保持与角色相同
                            
                            bulletTrackPos = new Vector3(targetX, targetY, targetZ);
                            Debug.Log($"111111111 V64.10子弹角度{angle:F1}度 - 第{z}颗 半径:{radius:F1} 目标:圆周位置{bulletTrackPos.Value}");
                        }
                    }

                    // 发射方向依次按配置的角度旋转
                    BulletThing bullet = Thing.CreateBullet(this, attackBaseDirFollowThing, bulletTrackPos, angle,
                        maxPenetrateTimes, maxBounceTimes, maxSeparateTimes);
                    
                    Vector3 dir_1 = shootBaseDir.RotateAround(Vector3.forward, angle);
                    bullet.Position = Actor.Position;
                    bullet.MoveDirection_Straight.Value = dir_1;

                    // V64.10 抛物线轨迹设置
                    if (bulletTrackPos.HasValue)
                    {
                        bullet.TrackPosition = bulletTrackPos.Value;
                        Debug.Log($"111111111 V64.10抛物线子弹创建 - 第{z}颗 角度:{angle:F1}° 起点:{bullet.Position} 目标:{bulletTrackPos.Value} X距离:{Mathf.Abs(bulletTrackPos.Value.x - bullet.Position.x):F1}");
                    }

                    MessageBroker.Default.Publish(new BornBullet
                    {
                        Bullet = bullet, PositionPre = bullet.Position - dir_1
                    });
                }
            }
            catch (OperationCanceledException) { throw; }
            catch (MissingReferenceException) { }
            catch (Exception ex)
            {
                Debug.LogException(ex);
            }
        }
    }
} 