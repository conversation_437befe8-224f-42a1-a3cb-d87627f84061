local luaID = ('UIRecharge')

local UIRecharge = {}

-- 初始化
function UIRecharge:OnCreate()
	self.objList.Txt_Title.text = GetGameText(luaID, 1)
	self.objList.Txt_Investment.text = GetGameText(luaID, 6)
	self.objList.Txt_Start.text = string.format(GetGameText(luaID, 7), 288)
	self.objList.Txt_End.text = string.format(GetGameText(luaID, 8), 2880)		
	self.objList.Btn_Return.onClick:AddListenerEx(self.OnClickReturn)	
	self.rechargeItem = self.objList.RechargeItem
	self.itemContainer = self.objList.Container.transform
	self.goodsContent = self.objList.GoodsContent.transform
	self.btnList = { self.objList.Btn_Return}
	
	self.schemeItems = self:GetShowItems()
	self.rechargeItemList = {}

	self.sevenDaysInvestItems = {}
    for k,v in ipairs(Schemes.SevenDaysInvest.items) do
        if v and v.Type == 1 then
            table.insert(self.sevenDaysInvestItems, v)
        end
    end
	self.sevenDaysConfig = self.sevenDaysInvestItems[1]
    self.prizeList = self.sevenDaysConfig.PrizeID
	self.prizeItemList = {}
	for i = 1, 7 do
		local prizeItem = {}
		prizeItem.index = i
		prizeItem.gameObject = self.objList['PrizeItem'..i]
		local objTrans = prizeItem.gameObject:GetRectTransform()
		Helper.FillLuaComps(objTrans, prizeItem)
		prizeItem.gameObject.name = 'PrizeItem'..i
		prizeItem.objTrans = objTrans
		table.insert(self.btnList, prizeItem.Btn_Click)		
		table.insert(self.prizeItemList, prizeItem)
		prizeItem.Btn_Click.onClick:AddListenerEx(function () 
			self:OnClickSevenDaysIcon(i)
		end)
	end
	return true
end

-- 点击阅览奖励
function UIRecharge:OnClickSevenDaysIcon(index)
	if #self.prizeList < index then return end
	local prizeID = self.prizeList[index]
	if not prizeID then return end
	local curTime = HelperL.GetServerTime()
	local investTimes = HeroDataManager:GetLogicData(self.sevenDaysConfig.TimeSaveID) -- 七日投资时间 
	local curDay = HelperL.CalculationIntervalDays(investTimes, curTime)
	local param1, param2
	if curDay >= index then
		if HeroDataManager:GetLogicBit(self.sevenDaysConfig.GetSaveID, index-1) == 0 then
			param1 = {txt = GetGameText(luaID, 9), callback = nil, enabled = true}
			param2 = {txt = GetGameText(luaID, 11), callback = function() self:InvestMoney(2) end, enabled = true}
		else
			param2 = {txt = GetGameText(luaID, 10), callback = nil, enabled = true}
		end
	else
		param2 = {txt = GetGameText(luaID, 10), callback = nil, enabled = true}
	end	
	UIManager:OpenWnd(WndID.ShowGoodInfo, prizeID, GetGameText(luaID, 12), param1, param2)
end

-- 点击返回按钮
function UIRecharge.OnClickReturn()
	local self = UIRecharge
	self:CloseSelf()
end

-- 窗口开启
function UIRecharge:OnOpen()
	self:CreateRechargeItems()
	self:RefreshSevenDaysInvestItems()
end

--初始化
function UIRecharge:RefreshSevenDaysInvestItems()   
	local hero = EntityModule.hero
	if not hero then
		return
	end 
    local curTime = HelperL.GetServerTime()
    local actTime = ActivityManager:GetActivityBeginTime(self.sevenDaysConfig.GetSaveID) 
    local investTimes = HeroDataManager:GetLogicData(self.sevenDaysConfig.TimeSaveID) -- 七日投资时间 
    
    local curDay = HelperL.CalculationIntervalDays(investTimes, curTime)
    if investTimes == 0 then
        self.objList.Txt_Investment.text = GetGameText(luaID, 13)
		HelperL.SetImageGray(self.objList.Btn_Investment:GetComponent('Image'), false)
    else
        self.objList.Txt_Investment.text = GetGameText(luaID, 14)
		HelperL.SetImageGray(self.objList.Btn_Investment:GetComponent('Image'), true)
    end
    for k, v in ipairs(self.prizeItemList) do
        if curDay >= v.index then
            if HeroDataManager:GetLogicBit(self.sevenDaysConfig.GetSaveID, v.index-1) == 0 then --可领取
				HelperL.SetImageGray(v.Img_Icon, false)
            else --已领取 
                HelperL.SetImageGray(v.Img_Icon, true)
            end
        else --未达到
			HelperL.SetImageGray(v.Img_Icon, true)
        end
    end
end

--点击领取/投资按钮
function UIRecharge:OnClickInvest()
	local investTimes = HeroDataManager:GetLogicData(self.sevenDaysConfig.TimeSaveID) -- 七日投资时间 
    if investTimes == 0 then return end
    if investTimes == 1 then
		local scheme = Schemes.RechargeCard.Get(self.sevenDaysConfig.CardID)
		if not scheme then return end
		local bitData = EntityModule.hero.logicLC:Get(LOGIC_DATA.DATA_SEVENTDAYS_INVEST_BUYFLAG)
		local flag = HeroDataManager:GetLogicBit(LOGIC_DATA.DATA_SEVENTDAYS_INVEST_BUYFLAG, self.sevenDaysConfig.ID - 1)
		if flag == 1 then
			self:InvestMoney(1)
			return
		end
		if self.sevenDaysConfig.CostDiamond == 0 and self.sevenDaysConfig.CardID ~= 0 then --需充值
			HelperL.Recharge(self.sevenDaysConfig.CardID)
		elseif self.sevenDaysConfig.CostDiamond ~= 0 and self.sevenDaysConfig.CardID == 0 then --需扣元宝
			self:InvestMoney(1)
		elseif self.sevenDaysConfig.CostDiamond ~= 0 and self.sevenDaysConfig.CardID ~= 0 then --充值且扣元宝
			HelperL.Recharge(self.sevenDaysConfig.CardID)
		end
    end
end


--请求扣除元宝(opType:1.投资; 2.领取奖励)
function UIRecharge:InvestMoney(opType)
    LuaModule.RunLuaRequest(string.format('LuaRequestSevenDaysInvest?id=%d&opType=%d', self.sevenDaysConfig.ID, opType), function(result)
        if result ~= RESULT_CODE.RESULT_COMMON_SUCCEED.id then
            HelperL.AddAMessageTip(HelperL.GetResultCodeStr(result))
            return
        end
    end)
end

function UIRecharge:GetShowItems()
	local items = {}
	for i,v in ipairs(Schemes.RechargeCard.items) do
		if v.CardType == 2 then			
			local uiInfo = {
				ID = v.ID,
				Character1 = v.Character1,
				Pic1 = v.Pic1,
				Bg = v.Bg,
				Pic2 = v.Pic2,
				Character2 = v.Character2,
				Description = v.Description,
				CardName = v.CardName,
				FirstRMB = v.FirstRMB,
				FirstDiamond = v.FirstDiamond,
				Diamond = v.Diamond,
				LenovoID = v.LenovoID,
				Diamond = v.Diamond,
				IOSID = v.IOSID,
				coolpayID = v.coolpayID,
				OnestoreID = v.OnestoreID,
				GoogleID = v.GoogleID,
				IgaworksID = v.IgaworksID,
			}
			table.insert(items, uiInfo)
		end
	end
	return items
end

-- 窗口开启
function UIRecharge:CreateRechargeItems()
	local index = 1
	local desc = ''
	local startScale = Vector3(0, 0, 0)
	for k, v in ipairs(self.schemeItems) do
		local rechargeItem = self.rechargeItemList[index]
		if not rechargeItem then
			rechargeItem = {}
			rechargeItem.index = index
			local obj = GameObject.Instantiate(self.rechargeItem, self.itemContainer)
			local objTrans = obj:GetRectTransform()
			Helper.FillLuaComps(objTrans, rechargeItem)
			rechargeItem.gameObject = obj
			rechargeItem.gameObject.name = 'rechargeItem'..rechargeItem.index
			rechargeItem.objTrans = objTrans
			table.insert(self.rechargeItemList, rechargeItem)
			table.insert(self.btnList, rechargeItem.Btn_Buy)
		end
		index = index + 1
		rechargeItem.Txt_Buy.text = string.format(GetGameText(luaID, 2), v.FirstRMB)
		rechargeItem.Txt_Get.text = string.format(GetGameText(luaID, 3), v.Diamond)
		rechargeItem.Txt_Give.text = string.format(GetGameText(luaID, 4), v.Diamond)
		rechargeItem.Txt_Rate.text = GetGameText(luaID, 5)
		--rechargeItem.Img_Icon.spriteName = v.Bg
		rechargeItem.Btn_Buy.onClick:AddListenerEx(function () self:OnClickRechargeBtn(v) end)
		rechargeItem.gameObject:SetActive(true)
		rechargeItem.objTrans:DOKill()
		rechargeItem.objTrans.localScale = startScale

	end
	
	if self.aniSeq then
		self.aniSeq:Kill()
		self.aniSeq = nil
	end
	
	-- 开始自定义动画
	local seq = DOTween.Sequence()
	local addTime = 0
	local toScale = Vector3(1, 1, 1)
	for i, v in ipairs(self.rechargeItemList) do
		if v.gameObject.activeSelf then
			local trans = v.gameObject.transform
			seq:Insert(addTime, trans:DOScale(toScale, 0.7):SetEase(TweeningEase.OutBack, 1.5))
			addTime = addTime + 0.16
		end
	end
	self.aniSeq = seq			
end

-- function UIRecharge:CreateGoods()
-- 	for v, k do
-- 	local goodsItem = CreateSingleGoods(self.goodsContent)				
-- 	goodsItem:SetItemData(goodsID, goodsNum)
-- 	goodsItem:SetVisible(true)
-- end

function UIRecharge.OnClickRechargeBtn(config)
	HelperL.Recharge(config.ID)
end

-- 窗口关闭
function UIRecharge:OnClose()
	
end


-- 窗口销毁
function UIRecharge:OnDestroy()
	-- 释放内存
	for i, btn in ipairs(self.btnList) do
		btn.onClick:RemoveAllListeners()
		btn.onClick:Invoke()
	end
end
return UIRecharge