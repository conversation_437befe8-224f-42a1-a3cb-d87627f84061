--[[
********************************************************************
    created:	
    author :	
    purpose:    主界面
*********************************************************************
--]]

local luaID = ('UICompensation')
local m = {}

--------------------------------------------------------------------
-- 订阅事件列表
--------------------------------------------------------------------
function m.<PERSON><PERSON><PERSON>List()
    return {

    }
end

--------------------------------------------------------------------
-- 初始化
--------------------------------------------------------------------
function m.OnCreate()
    m.objList.Btn_AllGet.gameObject:SetActive(false)
    m.objList.Txt_Headline.text = GetGameText(luaID, 1)
    m.RegisterClickEvent()
    return true
end

--------------------------------------------------------------------
-- 窗口开启
--------------------------------------------------------------------
function m.OnOpen()
    m.UpdateView()
end

--------------------------------------------------------------------
--注册点击事件
--------------------------------------------------------------------
function m.RegisterClickEvent()
    m.objList.Btn_Close.onClick:AddListenerEx(function()
        m:CloseSelf()
    end)

    m.objList.Btn_AllGet.onClick:AddListenerEx(function()
        if m.adverID == -1 then
            return
        end
        AdvertisementManager.GetAdAward(m.adverID, function(bool, adID)
            m:CloseSelf()
        end)
    end)
end

--------------------------------------------------------------------
-- 更新界面
--------------------------------------------------------------------
function m.UpdateView()
    local content = GetGameText(luaID, 2)
    if STREAMING_DATA and STREAMING_DATA.COMPENSATION_AFFICHE then
        content = STREAMING_DATA.COMPENSATION_AFFICHE
    end
    m.objList.Txt_Content.text = content

    m.adverID = -1
    local level = EntityModule.hero:GetProperty(CREATURE_FIELD.CREATURE_FIELD_LEVEL)
    local list = Schemes.CommonText:GetByTextType(5)
    for i, v in ipairs(list) do
        if level >= v.RankMin and level <= v.RankMax then
            m.adverID = v.ID
            break
        end
    end

    local state = HelperL.GetAdverState(m.adverID)
    m.objList.Btn_AllGet.gameObject:SetActive(state == 1)
end

return m
