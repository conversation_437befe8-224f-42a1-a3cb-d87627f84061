# 逻辑值操作规则文档

## 1. 逻辑值修改的正确方法

### 1.1 推荐方法：聊天命令方式
```lua
-- 发送聊天命令格式：#逻辑值 [逻辑值ID] [设置值]
local chatContent = "#逻辑值 200 " .. equipID
local msg = ChatMessage_pb.CS_Char_SendChat()
msg.Channel = 12
msg.Content = chatContent
msg.ChatType = 0
Premier.Instance:GetNetwork():SendFromLua(
    ENDPOINT.ENDPOINT_GAMECLIENT,
    ENDPOINT.ENDPOINT_GAMESERVER,
    MSG_MODULEID.MSG_MODULEID_CHAT,
    ChatMessage_pb.MSG_CHAT_SENDCHAT,
    msg:SerializeToString()
)
```

### 1.2 禁止使用的方法
```lua
-- ❌ 不推荐：直接调用LogicValue.SetIntByIndex会返回false
local success = LogicValue.SetIntByIndex(200, 1, equipID)  -- 返回false，设置失败
```

### 1.3 设置成功验证
- 聊天命令方式能够成功设置逻辑值
- 参考UITestMode.lua中的实现方式
- 设置后可以通过获取方法验证是否生效

## 2. 逻辑值获取的正确方法

### 2.1 获取逻辑值数据
```lua
-- 获取逻辑值200的当前值
local logicValue200 = HeroDataManager:GetLogicData(LOGIC_DATA.DATA_TRAIN_TOTALNUM)
```

### 2.2 常用逻辑值定义
```lua
-- 在DLogicData.lua中定义：
DATA_TRAIN_TOTALNUM = 200, --帮贡最大数
```

## 3. 装备系统相关操作

### 3.1 设置出战装备
```lua
-- 设置装备出战：装备ID, 位置1, 状态1
GamePlayerData.ActorEquip:ReplaceEquipIndex(equipID, 1, 1)
```

### 3.2 获取当前出战装备
```lua
-- 获取当前出战装备列表
local weaponsKnapsack = GamePlayerData.ActorEquip:GetWeaponsKnapsack(1) or {}
local curEquipID = weaponsKnapsack[1] or 0
```

## 4. 实际应用场景

### 4.1 装备出战时设置逻辑值
```lua
-- 在装备出战按钮点击时
local chatContent = "#逻辑值 200 " .. equipID
-- [发送聊天命令代码]
print("11111 装备出战-设置逻辑值200:", equipID, "命令:", chatContent)
```

### 4.2 登录时根据逻辑值设置装备
```lua
-- 角色等级=1时，设置默认装备ID
if heroLevel == 1 then
    local chatContent = "#逻辑值 200 300020"
    -- [发送聊天命令代码]
end

-- 角色等级≥1时，根据逻辑值200设置出战装备
if heroLevel >= 1 then
    Timer.New(function()
        local logicValue200 = HeroDataManager:GetLogicData(LOGIC_DATA.DATA_TRAIN_TOTALNUM)
        if logicValue200 and logicValue200 > 0 then
            GamePlayerData.ActorEquip:ReplaceEquipIndex(logicValue200, 1, 1)
        end
    end, 1, 1):Start()
end
```

## 5. 日志规范

### 5.1 操作日志前缀
- 所有相关操作使用日志前缀：`11111`
- 便于快速定位和调试

### 5.2 日志示例
```lua
print("11111 装备出战-设置逻辑值200:", equipID, "命令:", chatContent)
print("11111 登录延迟1秒-读取逻辑值200:", logicValue200)
print("11111 登录延迟1秒-根据逻辑值200设置出战装备ID:", logicValue200)
```

## 6. 注意事项

### 6.1 逻辑值200的特性
- 逻辑值200对应DATA_TRAIN_TOTALNUM（帮贡最大数）
- 该值的改变只能通过玩家手动操作出战装备触发
- 系统自动设置装备不会影响逻辑值200的值

### 6.2 数据一致性
- 设置前后需要验证数据的正确性
- 确保装备ID有效性检查
- 保证系统稳定性

### 6.3 兼容性要求
- 修改时保持与现有系统的兼容性
- 不影响其他功能模块
- 遵循项目代码风格和规范

## 7. 错误处理

### 7.1 常见问题
- 直接调用LogicValue.SetIntByIndex返回false
- 解决方案：使用聊天命令方式

### 7.2 调试方法
- 查看日志输出确认设置是否成功
- 通过获取方法验证数据是否正确保存
- 对比UITestMode.lua中的正确实现方式 