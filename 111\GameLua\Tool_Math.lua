---
--- Generated by <PERSON><PERSON><PERSON>(https://github.com/EmmyLua)
--- Created by Administrator.
--- DateTime: 2023/8/1 22:06
---

---@class MathTool
local this = {}
--
----- 获得一个数上某位的值
-----@param num number @目标数
-----@param index number @第几位
--function this.GetValueOfBinaryBit(num, index)
--    num = bit.rshift(num, index)
--    return num % 2
--end

function this.prepareBit()
    this.data32 = { }
    for i = 1, 32 do
        this.data32[i] = 2 ^(32 - i)
    end
end

this.prepareBit()

function this.GetValueOfBinaryBit(arg, index)
    local tr = {}
    for i = 1, 32 do
        if arg >= this.data32[i] then
            tr[i] = 1
            arg = arg - this.data32[i]
        else
            tr[i] = 0
        end
    end
    return tr[32 - index + 1] or 0
end

return this