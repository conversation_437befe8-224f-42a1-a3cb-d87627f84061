---排行榜数据
---@class RankData
---@field ActorID integer
---@field ActorName string
---@field RankNo integer
---@field Level integer
---@field Vocation integer
---@field Value1 integer
---@field Value2 integer
---@field VipLevel integer
---@field BlueVIPLevel integer
---@field BlueVIPType  integer
---@field Genre integer

---排行榜消息
---@class SC_Rank_GetRankList
---@field RankType integer
---@field SelfRank integer
---@field SelfRankValue integer
---@field RankList RankData[]


-- 排行榜模块
require "RankMessage_pb"
-- local luaID = ('RankModule')

--排行榜类型
RANK_TYPE = {
	RANK_TYPE_POWER = 1,                  -- 战斗力
	RANK_TYPE_MONEY = 2,                  -- 金钱
	RANK_TYPE_MAXHP = 3,                  -- 最大生命
	RANK_TYPE_PHYSICS_ATTACK = 4,         -- 物理攻击
	RANK_TYPE_MAGIC_ATTACK = 5,           -- 法术攻击
	RANK_TYPE_PHYSICS_DEFENSE = 6,        -- 物理防御
	RANK_TYPE_MAGIC_DEFENSE = 7,          -- 法术防御
	RANK_TYPE_LEVEL = 8,                  -- 等级
	RANK_TYPE_CHARM = 9,                  -- 魅力
	RANK_TYPE_SOCIETYVIT = 10,             -- 帮会活跃
	RANK_TYPE_ECTYPETOWER = 11,           -- 副本塔
	RANK_TYPE_WILDBATTLEKILLNUM = 12,     -- 楚汉战场击杀数
	RANK_TYPE_WILDBATTLECONTKILL = 13,    -- 楚汉战场最高连斩数
	RANK_TYPE_WILDBATTLEKILLNUM_PD = 14,  -- 楚汉战场每日击杀数
	RANK_TYPE_WILDBATTLECONTKILL_PD = 15, -- 楚汉战场每日最高连斩数
	RANK_TYPE_MOUNTPOWER = 16,            -- 坐骑战力
	RANK_TYPE_WINGPOWER = 17,             -- 仙器战力
	RANK_TYPE_NEWWINGPOWER = 18,          -- 翅膀战力
	RANK_TYPE_SOCIETYPOWER = 19,          -- 帮会战力
	RANK_TYPE_REAL_LEVEL = 20,            -- 真实玩家等级榜
	RANK_TYPE_REAL_POWER = 21,            -- 真实玩家战力榜
	RANK_TYPE_REAL_RECHARGE = 22,         -- 真实玩家充值榜
	RANK_TYPE_REILIWHEELPOWER = 23,       -- 星阵战力
	RANK_TYPE_HOLYLIGHTPOWER = 24,        -- 圣光战力
	RANK_TYPE_NEWWILDBATTLEKILLNUM_PD = 25, -- 新楚汉战场每日击杀数
	RANK_TYPE_NEWWILDBATTLECONTKILL_PD = 26, -- 新楚汉战场每日最高连斩数
	RANK_TYPE_STAGECHALLENGERECORD = 27,  -- 挑战关卡记录
	RANK_TYPE_MATCHRANKRECORD = 28,       -- 匹配段位记录
	RANK_TYPE_MATCHSCORERANKRECORD = 29,  -- 匹配积分记录
	RANK_TYPE_CATDAYKILLENIMY = 30,       -- 汪星人杀怪记录(生存排行)
}

RankModule = {}
---排行数据信息
---@type SC_Rank_GetRankList[]
RankModule.RankData = {}

function RankModule.Handle(action, data)
	local m
	if action == RankMessage_pb.MSG_RANK_GETLIST then --获取排行榜列表
		m = RankMessage_pb.SC_Rank_GetRankList()
		m:ParseFromString(data)
		RankModule.SC_Rank_GetRankList(m)
	elseif action == RankMessage_pb.MSG_RANK_CHANGE then --活动排行榜排名下降
		m = RankMessage_pb.SC_Rank_Change()
		m:ParseFromString(data)
		RankModule.SC_Rank_Change(m)
	elseif action == RankMessage_pb.MSG_RANK_GETMATCHRECORDDATA then --同步比赛排行相关数据
		m = RankMessage_pb.SC_Rank_GetMatchRecordData()
		m:ParseFromString(data)
		RankModule.SC_Rank_GetMatchRecordData(m)
	end
end

--------------------------------------------------------------------
---根据类型获取排行数据
---@param rankType integer 排行榜类型
--------------------------------------------------------------------
function RankModule.GetRankData(rankType)
	return RankModule.RankData[rankType]
end

--------------------------------------------------------------------
--获取记录数据
--------------------------------------------------------------------
function RankModule.GetMatchRecordData()
	return RankModule.MatchRecordData
end

--------------------------------------------------------------------
---同步排行榜数据
---@param m SC_Rank_GetRankList
--------------------------------------------------------------------
function RankModule.SC_Rank_GetRankList(m)
	RankModule.RankData[m.RankType] = m
	EventManager:Fire(EventID.OnRankingListUpdate, m)
end

--------------------------------------------------------------------
--同步排行榜排名下降
--------------------------------------------------------------------
function RankModule.SC_Rank_Change(m)
	--排行变动，重新请求排行数据
	RankModule.RequestGetRankList(m.RankType)
	EventManager:Fire(EventID.OnRankChange)
end

--------------------------------------------------------------------
--同步比赛排行相关数据
--------------------------------------------------------------------
function RankModule.SC_Rank_GetMatchRecordData(m)
	RankModule.MatchRecordData = m
	EventManager:Fire(EventID.OnMatchRecordDataUpdate)
end

--------------------------------------------------------------------
---请求排行榜列表
---@param rankType integer 排行榜类型
--------------------------------------------------------------------
function RankModule.RequestGetRankList(rankType)
	if rankType == RANK_TYPE.RANK_TYPE_CATDAYKILLENIMY then
		RankingModule.RequestRankList(rankType)
	else
		local m = RankMessage_pb.CS_Rank_GetRankList()
		m.RankType = rankType
		Premier.Instance:GetNetwork():SendFromLua(
			ENDPOINT.ENDPOINT_GAMECLIENT,
			ENDPOINT.ENDPOINT_GAMESERVER,
			MSG_MODULEID.MSG_MODULEID_RANK,
			RankMessage_pb.MSG_RANK_GETLIST,
			m:SerializeToString()
		)
	end
end

--------------------------------------------------------------------
--请求比赛排行相关数据
--------------------------------------------------------------------
function RankModule.RequestGetMatchRecordData()
	Premier.Instance:GetNetwork():SendFromLua(
		ENDPOINT.ENDPOINT_GAMECLIENT,
		ENDPOINT.ENDPOINT_GAMESERVER,
		MSG_MODULEID.MSG_MODULEID_RANK,
		RankMessage_pb.MSG_RANK_GETMATCHRECORDDATA,
		''
	)
end

Premier.Instance:RegisterLuaModule(MSG_MODULEID.MSG_MODULEID_RANK, 'RankModule.Handle')
