--主入口函数。从这里开始lua逻辑
function Main()
	print('logic start')
end

function OnApplicationQuit()
end

-- 目标服务器时区(默认东八区)
TargetTimeZone = 8 * 60 * 60

InitRequireData = {} --初始化的reuqire有哪些
for k, v in pairs(package.loaded) do
	InitRequireData[k] = true
end

-- 游戏开始通知
function Main_OnGameStart()
	math.randomseed(os.time())
	-- 初始化UI
	UIManager:Init()
	-- 初始化场景管理器
	SceneManager:Init()
	UIManager:OpenWnd(WndID.Login)
end

-- 每帧调用函数
function Main_Update()
	EntityModule:OnUpdate()
	UIManager:OnUpdate()
	StoreModule:OnUpdate()
end

-- 场景切换通知
function OnLevelWasLoaded(level)
	collectgarbage('collect')
	Time.timeSinceLevelLoad = 0
	if SceneManager then
		SceneManager:OnLevelWasLoaded()
	end
end

-- 断开链接调用函数
function Main_OnConnectClose()
	print('--------断开链接调用函数-------------')
	-- if GameModule then
	-- 	GameModule:OnConnectClose()
	-- end
end

-- 下载完毕调用函数
function Main_OnDownloadFinish(isSuccess, packType, packID, abName)
	if isSuccess == 0 then
		return
	end

	--[[
	if MixUICenter then
		MixUICenter.OnDownloadPackFile(packID)
	end

	if EntityModule then
		if packType and packType == 'model' then
			EntityModule.OnModelDownload(packID, abName)
		end
		if EntityModule.hero then
			EntityModule.CheckLevelDownloadPack()
		end
	end
	]]
end

-- 窗口大小更改通知
function Main_OnScreenSizeChanged()
end

-- c#调试调用函数
function Main_PrintTraceback()
	print(debug.traceback())
end

-- c#获取报错上报参数
function Main_GetLogReportParam()
	return ''
end

--激励视频播放完成回调
function Main_OnAdvertiseVideoPlayComplete(paramStr)

end

print('Lua入口')
RandomNum = Apq.RandomNum;
Resources = UnityEngine.Resources
GameObject = UnityEngine.GameObject
Camera = UnityEngine.Camera
Physics = UnityEngine.Physics
NavMesh = UnityEngine.AI.NavMesh
NavMeshPath = UnityEngine.AI.NavMeshPath
DOTween = DG.Tweening.DOTween
TweeningEase = DG.Tweening.Ease
TweeningLoopType = DG.Tweening.LoopType
SpriteAtlas = UnityEngine.U2D.SpriteAtlas
EventTrigger = UnityEngine.EventSystems.EventTrigger
EventTriggerType = UnityEngine.EventSystems.EventTriggerType
RectTransformUtility = UnityEngine.RectTransformUtility
DateTime = System.DateTime
CachedVector3 = Vector3.New(0, 0, 0)
CachedVector2 = Vector2.New(0, 0)
-- 默认为线性动画
DOTween.defaultEaseType = TweeningEase.Linear
-- 按钮通用处理
EventSystemInstance = UnityEngine.EventSystems.EventSystem.current
if PlayerPrefs == nil then
	PlayerPrefs = UnityEngine.PlayerPrefs
end


function Main_ButtonCommonFunc()
	SoundManager:PlaySound(1002)

	-- 通用变大效果
	--local selObj = EventSystemInstance.currentSelectedGameObject
	--if selObj and selObj.activeSelf then
	--	local trans = selObj.transform
	--	if not DOTween.IsTweening(trans, true) then
	--		trans:DOScale(Vector3(1.2, 1.2, 1.2), 0.1):SetLoops(2, TweeningLoopType.Yoyo)
	--	end
	--end
end

_NeedPreloadLua = {
}
_NeedPreloadLuaSize = #_NeedPreloadLua

require("dkjsonHelper");
require("HttpHelper");
require 'protobuf/protobuf'

--region 工具
require('Tool_Global')
StringL = require('StringL')
UI = require('Tool_UI')
MathL = require('Tool_Math')
stringx = require("pl_tablex")
class = require('pl_class')
--endregion 工具

require 'ActorDataCatalog'
require 'DGameEnum'
require 'UIEnum'
require 'LogicValueEnum'
require 'DGameText'
require 'DResultCode'
require 'DEntityField'
require 'DSchemeStruct'

require 'HttpReques'
require 'DivineWeaponDataLC'
require 'EquipGemPartDataLC'
require 'DLogicData'
require 'DMsgModule'
require 'EventManager'
require 'HelperL'
require 'PropertyCompute'
require 'Schemes'
require 'LoginModule'
require 'EntityModule'
require 'LuaModule'
require 'ChatModule'
require 'CountryModule'
require 'EmailModule'
require 'FriendModule'
require 'GameModule'
require 'BattleManager'

require 'DataService'
require 'RankModule'
require 'SkepModule'
require 'SocietyModule'
require 'StoreModule'
require 'WorldModule'
require 'UIWndBase'
require 'UIManager'
require 'SceneManager'
require 'ActivityManager'
require 'HeroDataManager'
require 'EntityBase'
require 'SkepBase'
require 'BuffLC'
require 'HPComp'
require 'MatchDataManager'
require 'GuideManager'
require 'PlayerPrefsManager'
require 'AtlasManager'
require 'SoundManager'
require 'RedDotCheckFunc'
require 'RedDotManager'
require 'AdvertisementManager'

require 'SlotItemFactory'
require 'ActorProp'
require 'SkillProp'
require 'PrefabricatedAnimation'
require 'WindowController'
require 'SilverAndexpFlyMeesage'
require 'RankingModule'
require 'FightAttribute'
require 'ComResInfo'
require 'UIRoleEquip'

require 'LogicValue'
require 'LuaModuleNew'
require 'GamePlayerData'

EventManager:Fire(EventID.GameInit)

print(_VERSION)
local NotchPhoneModel = require "NotchPhoneModel"
print('-----手机型号--------GetPhoneDeviceModel=', NotchPhoneModel.GetPhoneDeviceModel())
print('-----手机品牌--------GetPhoneBrand=', NotchPhoneModel.GetPhoneBrand())
print('-----手机型号--------是刘海屏=', NotchPhoneModel.IsNotchDetection())
