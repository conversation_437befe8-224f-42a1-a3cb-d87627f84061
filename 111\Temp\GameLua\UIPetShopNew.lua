--[[
********************************************************************
    created:    2024/03/16
    author :    李锦剑
    purpose:    宠物界面
*********************************************************************
--]]

local luaID = ('UIPetShopNew')
--最大出战数
local maxHoleNum = 3
local yb_goodsID = 11
---@class UIPetShopNew:UIWndBase
local m = {}
--------------------------------------------------------------------
-- 订阅事件列表
--------------------------------------------------------------------
function m:GetOpenEventList()
    return {
        [EventID.LogicDataChange] = m.UpdateView,
        [EventID.OnHeroPropChange] = m.UpdateView,
        [EventID.OnGoodsPropChange] = m.UpdateView,
        [EventID.OnSkepGoodsChange] = m.UpdateView,
        [EventID.UpdateListPets_OnStage] = m.UpdateListPets_OnStage,
    }
end

--------------------------------------------------------------------
--预制体自适应
--------------------------------------------------------------------
-- function m:AdaptScale()
-- end

--------------------------------------------------------------------
-- 初始化
--------------------------------------------------------------------
function m:OnCreate()
    --宠物出战列表
    m.saidekick1_ItemList = {}
    --宠物列表
    m.saidekick2_ItemList = {}
    m.AttributeItemList2 = {}
    m.PetsInStage = {}
    m.objList.Txt_Activate.text = GetGameText(luaID, 1)
    m.objList.Txt_Activate1.text = GetGameText(luaID, 1)
    m.objList.Txt_Cultivate.text = GetGameText(luaID, 2)
    m.objList.Txt_Cultivate1.text = GetGameText(luaID, 2)
    m.objList.Txt_ChuZhan.text = GetGameText(luaID, 3)
    m.objList.Txt_XiaZhen.text = GetGameText(luaID, 4)
    AtlasManager:AsyncGetGoodsSprite(yb_goodsID, m.objList.Img_GoodsIcon2)

    m:RegisterClickEvent()
    m.UpdateView()
    m.SelectSidekick2(1)

    m.Item_Fight = FightAttribute.New()
    m.Item_Fight.Init(m.objList.Item_Fight)
    return true
end

--------------------------------------------------------------------
-- 窗口开启
--------------------------------------------------------------------
function m:OnOpen()
    m.objList.Obj_JinJie.gameObject:SetActive(false)
    m.objList.Obj_Cultivate.gameObject:SetActive(false)
    m.objList.Img_ZSJNDes.gameObject:SetActive(false)
    HttpReques.SendRequest(ERequestID.ListPets_OnStage)
end

--------------------------------------------------------------------
--注册点击事件
--------------------------------------------------------------------
function m:RegisterClickEvent()
    m.objList.Btn_Close.onClick:AddListenerEx(function()
        m:CloseSelf()
    end)
    m.objList.Btn_Cultivate_Close.onClick:AddListenerEx(function()
        m.objList.Obj_Cultivate.gameObject:SetActive(false)
    end)
    m.objList.Btn_CloseJinJie.onClick:AddListenerEx(function()
        m.objList.Obj_JinJie.gameObject:SetActive(false)
    end)
    m.objList.Btn_Cultivate.onClick:AddListenerEx(function()
        m.UpdateCultivate()
        m.objList.Obj_Cultivate.gameObject:SetActive(true)
    end)
    m.objList.Btn_Cultivate2.onClick:AddListenerEx(m.OnRequestEquipStarUp)
    m.objList.Btn_ChuZhan.onClick:AddListenerEx(m.OnRequest_ChuZhan)
    m.objList.Btn_XiaZhen.onClick:AddListenerEx(function()
        if not m.selectSidekickIndex then return end
        m.OnRequest_XiaZhen(m.saidekick2_ItemList[m.selectSidekickIndex].data.ID)
    end)
    m.objList.Btn_Activate.onClick:AddListenerEx(m.OnRequestActiveMount)
    m.objList.Btn_Img_DesBG.onClick:AddListenerEx(function()
        if m.objList.Img_ZSJNDes.gameObject.activeSelf then
            m.objList.Img_ZSJNDes.gameObject:SetActive(false)
        else
            m.objList.Img_ZSJNDes.gameObject:SetActive(true)
        end
    end)
    m.objList.Btn_Box_Award.onClick:AddListenerEx(function()
        UIManager:OpenWnd(WndID.Shop, 3)
    end)

    m.objList.Btn_Explain.onClick:AddListenerEx(function()
        HelperL.OpenTipsWindow(8)
    end)
end

--------------------------------------------------------------------
-- 按钮通用音效
--------------------------------------------------------------------
function m.PlayBtnSound()
    SoundManager:PlaySound(1005)
end

--------------------------------------------------------------------
--更新界面
--------------------------------------------------------------------
function m.UpdateView()
    m.UpdateSidekick1()
    m.UpdateSidekick2()
    if m.objList.Obj_JinJie.gameObject.activeSelf then
        m.UpdateJinJie()
    end
    if m.objList.Obj_Cultivate.gameObject.activeSelf then
        m.UpdateCultivate()
    end
    m.UpdateViewAttribute()
    m.objList.Txt_GoodsValue2.text = HelperL.GetChangeNum(SkepModule:GetGoodsCount(yb_goodsID))
end

--------------------------------------------------------------------
--更新界面属性
--------------------------------------------------------------------
function m.UpdateViewAttribute()
    if not m.selectSidekickIndex then return end
    local data = m.saidekick2_ItemList[m.selectSidekickIndex].data
    local equipCfg = Schemes.Equipment:Get(data.ID)
    local quality = equipCfg.Quality
    local starNum = equipCfg.StarNum
    m.objList.Txt_PetName.text = equipCfg.GoodsName
    local entity = SkepModule:GetSkepByID(SKEPID.SKEPID_MOUNT):GetEntityByGoodsID(data.ID)
    if entity then
        quality = entity:GetProperty(EQUIPMENT_FIELD.EQUIPMENT_FIELD_QUALITY)
        starNum = entity:GetProperty(EQUIPMENT_FIELD.EQUIPMENT_FIELD_STARNUM)
    end

    local skillCfg = Schemes.CatSkill:Get(equipCfg.ConsignmentStyle)
    if skillCfg then
        m.objList.Txt_Desc2.text = string.format(GetGameText(luaID, 5), skillCfg.Name)
        m.objList.Txt_ZSJNDesc2.text = string.format(GetGameText(luaID, 6), skillCfg.Remark)
    else
        m.objList.Txt_Desc2.text = CommonTextID.NIL
        m.objList.Txt_ZSJNDesc2.text = ''
    end

    m.CreateAirMode(data.ID)

    local smeltSCfg = Schemes.EquipSmelt:Get(equipCfg.SmeltID, quality, starNum)
    if not smeltSCfg then return end
    local sprite
    if smeltSCfg.HP > 0 then
        m.objList.Txt_Desc1.text = string.format('%s：<color=#00ff00>%s</color>', GetAttributeTypeDesc(0), smeltSCfg.HP)
        sprite = '10004'
    else
        m.objList.Txt_Desc1.text = string.format('%s：<color=#00ff00>%s</color>', GetAttributeTypeDesc(3),
            smeltSCfg.PhysicsAttack)
        sprite = '10005'
    end
    AtlasManager:AsyncGetSprite(sprite, m.objList.Img_Icon1)
end

--------------------------------------------------------------------
--更新宠物孔
--------------------------------------------------------------------
function m.UpdateSidekick1()
    for i = 1, 3, 1 do
        if not m.saidekick1_ItemList[i] then
            m.saidekick1_ItemList[i] = m.CreationSidekick1(i)
        end
        m.saidekick1_ItemList[i].UpdateData(GamePlayerData.PetsInStage[i])
    end
end

--------------------------------------------------------------------
--更新伙伴背包
--------------------------------------------------------------------
function m.UpdateSidekick2()
    local index = 1
    for _, v in ipairs(Schemes.EquipMount.items) do
        if not m.saidekick2_ItemList[index] then
            m.saidekick2_ItemList[index] = m.Creation_Sidekick2(index)
        end
        m.saidekick2_ItemList[index].UpdateData(v)
        index = index + 1
    end
end

--------------------------------------------------------------------
--更新伙伴背包
--------------------------------------------------------------------
function m.UpdateListPets_OnStage(data)
    m.PetsInStage = {}
    for i, v in ipairs(data) do
        m.PetsInStage[v] = true
    end
    m.UpdateView()
end

--------------------------------------------------------------------
--创建属性框
--------------------------------------------------------------------
function m.Creation_Sidekick2(index)
    local item = {}
    item.index = index
    item.com = m:CreateSubItem(m.objList.Grid_Sidekick2, m.objList.Item_Sidekick2)
    item.com.Img_Select.gameObject:SetActive(false)
    item.com.Img_LockBg.gameObject:SetActive(false)
    item.com.Btn_Select.onClick:AddListenerEx(function()
        m.SelectSidekick2(item.index)
        m.objList.Obj_JinJie.gameObject:SetActive(true)
    end)

    ---更新数据
    ---@param data integer
    item.UpdateData = function(data)
        if data then
            item.data = data
            local equipCfg = Schemes.Equipment:Get(data.ID)
            local quality = equipCfg.Quality
            local starNum = equipCfg.StarNum
            HelperL.GetQualitySprite(equipCfg.QualityLevel, item.com.Img_Bg)
            AtlasManager:AsyncGetSprite(equipCfg.IconID, item.com.Img_Icon, true)
            HelperL.GetQualityLogo(equipCfg.QualityLevel, item.com.Img_QualityLogo, true)
            item.com.Txt_Lv.text = ''
            local entity = SkepModule:GetSkepByID(SKEPID.SKEPID_MOUNT):GetEntityByGoodsID(data.ID)
            if entity then
                item.com.Img_LockBg.gameObject:SetActive(false)
                item.com.Txt_Des.gameObject:SetActive(m.Is_ChuZhan(data.ID))
                item.com.Txt_Des.text = GetGameText(luaID, 7)
                quality = entity:GetProperty(EQUIPMENT_FIELD.EQUIPMENT_FIELD_QUALITY)
                starNum = entity:GetProperty(EQUIPMENT_FIELD.EQUIPMENT_FIELD_STARNUM)
                local num = (quality * 10 + starNum)
                if num > 0 then
                    item.com.Txt_Lv.text = '+' .. num
                end
            else
                item.com.Txt_Des.gameObject:SetActive(false)
                item.com.Img_LockBg.gameObject:SetActive(true)
            end

            --培养消耗
            local smeltCfg = Schemes.EquipSmelt:Get(equipCfg.SmeltID, quality, starNum)
            if smeltCfg then
                local goodsID = smeltCfg.GoodsID
                local goodsNum = SkepModule:GetGoodsCount(goodsID)
                local color = UI_COLOR.Red
                if goodsNum >= smeltCfg.GoodsNum then
                    color = UI_COLOR.White
                end
                item.com.Txt_Expend1.text = string.format("<color=%s>%s/%s</color>", color, goodsNum, smeltCfg.GoodsNum)
                AtlasManager:AsyncGetGoodsSprite(goodsID, item.com.Img_Expend1, true)
                item.com.Obj_Expend1.gameObject:SetActive(true)
            else
                item.com.Obj_Expend1.gameObject:SetActive(false)
            end

            item.com.Img_RedDot.gameObject:SetActive(RedDotCheckFunc:Check_UIPetShopNew(data.ID))
            item.com.gameObject:SetActive(true)
        else
            item.com.gameObject:SetActive(false)
        end
    end
    return item
end

--------------------------------------------------------------------
-- 创建细胞模型
--------------------------------------------------------------------
function m.CreateAirMode(equipID)
    if not m.ui2DModel then
        m.ui2DModel = HelperL.CreateRenderTextureAndModel(m.objList.Obj_ModelUI)
    end
    m.ui2DModel:CreateUIModel3(equipID)
end

--------------------------------------------------------------------
--选中伙伴事件
--------------------------------------------------------------------
function m.SelectSidekick2(index)
    -- if index == m.selectSidekickIndex then return end
    if m.selectSidekickIndex then
        m.saidekick2_ItemList[m.selectSidekickIndex].com.Img_Select.gameObject:SetActive(false)
    end
    m.selectSidekickIndex = index
    m.saidekick2_ItemList[index].com.Img_Select.gameObject:SetActive(true)
    m.UpdateJinJie()
    m.UpdateViewAttribute()
end

--------------------------------------------------------------------
--培养红点判断
--------------------------------------------------------------------
function m.Cultivate(ID)
    local equipCfg = Schemes.Equipment:Get(ID)
    local quality = equipCfg.Quality
    local starNum = equipCfg.StarNum
    local entity = SkepModule:GetSkepByID(SKEPID.SKEPID_MOUNT):GetEntityByGoodsID(ID)
    if entity then
        quality = entity:GetProperty(EQUIPMENT_FIELD.EQUIPMENT_FIELD_QUALITY)
        starNum = entity:GetProperty(EQUIPMENT_FIELD.EQUIPMENT_FIELD_STARNUM)
    end

    local smeltSCfg = Schemes.EquipSmelt:Get(equipCfg.SmeltID, quality, starNum)
    if smeltSCfg and smeltSCfg.LevelMaxExp ~= -1 then
        local goodsNum1 = SkepModule:GetGoodsCount(smeltSCfg.GoodsID)
        local goodsNum2 = SkepModule:GetGoodsCount(4)
        if goodsNum1 >= smeltSCfg.GoodsNum and goodsNum2 >= smeltSCfg.CostMoney then
            return true
        end
    end

    return false
end

--------------------------------------------------------------------
--出战判断
--------------------------------------------------------------------
function m.Is_ChuZhan(ID)
    return m.PetsInStage[ID] == true
end

--------------------------------------------------------------------
--可解锁宠物出战位判断
--------------------------------------------------------------------
function m.IsUnlockable(index)
    if index > m.GetHoleNum() then
        local cfg = Schemes.Gemstone:Get(index)
        if cfg.NeedGoods == 0 then
            return true
        end
        return SkepModule:GetGoodsCount(cfg.NeedGoods) >= cfg.GoodsNumber
    end
    return false
end

--------------------------------------------------------------------
--更新进阶界面
--------------------------------------------------------------------
function m.UpdateJinJie()
    if not m.selectSidekickIndex then return end
    local data = m.saidekick2_ItemList[m.selectSidekickIndex].data
    local equipCfg = Schemes.Equipment:Get(data.ID)
    m.objList.Txt_Name.text = equipCfg.GoodsName
    AtlasManager:AsyncGetSprite(equipCfg.IconID, m.objList.Img_CardIcon)
    HelperL.GetQualitySprite(equipCfg.QualityLevel, m.objList.Img_CardBg)
    HelperL.GetQualityLogo(equipCfg.QualityLevel, m.objList.Img_CardQuality)
    m.objList.Txt_Skill.text = ''
    m.objList.Txt_SkillDes.text = ''

    m.objList.Txt_Choose1.gameObject:SetActive(false)
    m.objList.Btn_XiaZhen.gameObject:SetActive(false)
    m.objList.Btn_ChuZhan.gameObject:SetActive(false)
    m.objList.Btn_Activate.gameObject:SetActive(false)
    m.objList.Btn_Cultivate.gameObject:SetActive(false)
    m.objList.Img_Activate_Gray.gameObject:SetActive(false)
    m.objList.Img_Activate_RedDot.gameObject:SetActive(false)

    local entity = SkepModule:GetSkepByID(SKEPID.SKEPID_MOUNT):GetEntityByGoodsID(data.ID)
    if entity then
        m.objList.Btn_Cultivate.gameObject:SetActive(true)
        print('-----更新进阶界面------', data.ID, entity.uid, m.Is_ChuZhan(data.ID))
        if m.Is_ChuZhan(data.ID) then
            m.objList.Btn_XiaZhen.gameObject:SetActive(true)
            m.objList.Txt_Choose1.gameObject:SetActive(true)
        else
            local num = #GamePlayerData.PetsInStage
            m.objList.Btn_ChuZhan.gameObject:SetActive(m.GetHoleNum() - num > 0)
        end
        m.objList.Img_Cultivate_RedDot.gameObject:SetActive(RedDotCheckFunc:Check_UIPetShopNew(data.ID))
    else
        m.objList.Btn_Activate.gameObject:SetActive(true)
        local cfg = Schemes.EquipMount:Get(data.ID)
        local goodsNum = SkepModule:GetGoodsCount(cfg.GoodsID1)
        print('-----------激活消耗=', "#加物品 " .. cfg.GoodsID1 .. " 10")
        local color = '#FFA500'
        --激活材料判断
        if goodsNum >= cfg.GoodsNum1 then
            color = '#00ff00'
            m.objList.Img_Activate_RedDot.gameObject:SetActive(true)
        else
            m.objList.Img_Activate_Gray.gameObject:SetActive(true)
        end
        m.objList.Txt_ActivateGoods.text = string.format("<color=#%s>%s/%s</color>", color, goodsNum, cfg.GoodsNum1)
        AtlasManager:AsyncGetSprite(equipCfg.IconID, m.objList.Img_ActivateGoods)
    end

    local skillCfg = Schemes.CatSkill:Get(equipCfg.ConsignmentStyle)
    if skillCfg then
        m.objList.Txt_Skill.text = skillCfg.Name
        m.objList.Txt_SkillDes.text = skillCfg.Remark
    else
        m.objList.Txt_Skill.text = CommonTextID.NIL
        m.objList.Txt_SkillDes.text = ''
    end
end

--------------------------------------------------------------------
--创建属性框
--------------------------------------------------------------------
function m.CreationAttribute2()
    local item = {}
    item.com = m:CreateSubItem(m.objList.Grid_Attribute2, m.objList.Item_Attribute2)
    ---更新数据
    ---@param data EquipSmeltAttribute
    item.UpdateData = function(data)
        if data then
            item.com.Txt_Value1.text = string.format("%s：%s", data.Name, data.Value .. data.Unit)
            if data.NextValue > data.Value then
                item.com.Txt_Value2.text = string.format("<color=#00FF00>%s</color>", data.NextValue .. data.Unit)
                item.com.gameObject:SetActive(true)
            else
                item.com.gameObject:SetActive(false)
            end
            item.com.gameObject:SetActive(true)
        else
            item.com.gameObject:SetActive(false)
        end
    end
    return item
end

--------------------------------------------------------------------
--更新培养界面
--------------------------------------------------------------------
function m.UpdateCultivate()
    if not m.selectSidekickIndex then return end
    local data = m.saidekick2_ItemList[m.selectSidekickIndex].data
    local equipCfg = Schemes.Equipment:Get(data.ID)
    AtlasManager:AsyncGetSprite(equipCfg.IconID, m.objList.Img_CardIcon2)
    HelperL.GetQualitySprite(equipCfg.QualityLevel, m.objList.Img_CardBg2)
    HelperL.GetQualityLogo(equipCfg.QualityLevel, m.objList.Img_CardQuality2)

    local quality = equipCfg.Quality
    local starNum = equipCfg.StarNum
    local entity = SkepModule:GetSkepByID(SKEPID.SKEPID_MOUNT):GetEntityByGoodsID(equipCfg.ID)
    if entity then
        quality = entity:GetProperty(EQUIPMENT_FIELD.EQUIPMENT_FIELD_QUALITY)
        starNum = entity:GetProperty(EQUIPMENT_FIELD.EQUIPMENT_FIELD_STARNUM)
    end
    m.objList.Txt_Name2.text = string.format('%s+%s', equipCfg.GoodsName, quality * 10 + starNum)
    local smeltScheme = Schemes.EquipSmelt:Get(equipCfg.SmeltID, quality, starNum)
    if not smeltScheme then return end
    local goods_id1 = smeltScheme.GoodsID
    local goods_id2 = 4
    local cost_goodsnum1 = smeltScheme.GoodsNum
    local cost_goodsnum2 = smeltScheme.CostMoney
    local goodsNum1 = SkepModule:GetGoodsCount(goods_id1)
    local goodsNum2 = SkepModule:GetGoodsCount(goods_id2)
    print('-----------培养消耗=', "#加物品 " .. goods_id1 .. " 10", "#加物品 " .. goods_id2 .. " 10")
    m.objList.Img_Cultivate_Gray2.gameObject:SetActive(false)
    if smeltScheme.LevelMaxExp == -1 then
        m.objList.Btn_FullLevel.gameObject:SetActive(true)
        m.objList.Btn_Cultivate2.gameObject:SetActive(false)
        m.objList.Txt_Cost.gameObject:SetActive(false)
    else
        m.objList.Btn_Cultivate2.gameObject:SetActive(true)
        m.objList.Btn_FullLevel.gameObject:SetActive(false)
        m.objList.Txt_Cost.gameObject:SetActive(true)
        if not (goodsNum1 >= cost_goodsnum1 and goodsNum2 >= cost_goodsnum2) then
            m.objList.Img_Cultivate_Gray2.gameObject:SetActive(true)
        end

        AtlasManager:AsyncGetGoodsSprite(goods_id1, m.objList.Img_Cost1)
        AtlasManager:AsyncGetGoodsSprite(goods_id2, m.objList.Img_Cost2)
        m.objList.Img_Cost2.gameObject:SetActive(cost_goodsnum2 > 0)
    end
    local color = goodsNum1 >= cost_goodsnum1 and UI_COLOR.White or UI_COLOR.Red
    m.objList.Txt_Cost1.text = string.format('<color=%s>%s/%s</color>', color, goodsNum1, cost_goodsnum1)
    local color2 = goodsNum2 >= cost_goodsnum2 and UI_COLOR.White or UI_COLOR.Red
    m.objList.Txt_Cost2.text = string.format('<color=%s>%s/%s</color>', color2, goodsNum2, cost_goodsnum2)

    local attrList = ActorProp.GetEquipSmeltAttribute(smeltScheme)
    local num = math.max(#m.AttributeItemList2, #attrList)
    for i = 1, num, 1 do
        if not m.AttributeItemList2[i] then
            m.AttributeItemList2[i] = m.CreationAttribute2()
        end
        m.AttributeItemList2[i].UpdateData(attrList[i])
    end
end

--------------------------------------------------------------------
--创建宠物出战框
--------------------------------------------------------------------
function m.CreationSidekick1(index)
    local item = {}
    item.index = index
    item.com = m:CreateSubItem(m.objList.Grid_Sidekick1, m.objList.Item_Sidekick1)
    item.com.Btn_Unlock.gameObject:SetActive(true)
    item.com.Btn_XiaZhen.gameObject:SetActive(false)
    item.com.Img_UnlockReddot.gameObject:SetActive(false)
    item.com.Btn_XiaZhen.onClick:AddListenerEx(function()
        m.OnRequest_XiaZhen(item.ID)
    end)
    item.com.Btn_Unlock.onClick:AddListenerEx(function()
        m.OnRequest_ActiveGemHole(item.index)
    end)
    ---更新数据
    item.UpdateData = function(ID)
        item.ID = ID
        if item.index <= m.GetHoleNum() then
            item.com.Btn_Unlock.gameObject:SetActive(false)
            item.com.Btn_XiaZhen.gameObject:SetActive(false)
            local equipCfg = Schemes.Equipment:Get(ID)
            if equipCfg then
                AtlasManager:AsyncGetSprite(equipCfg.IconID, item.com.Img_Icon)
                item.com.Btn_XiaZhen.gameObject:SetActive(true)
            end
        else
            item.com.Btn_Unlock.gameObject:SetActive(true)
            item.com.Img_UnlockReddot.gameObject:SetActive(m.IsUnlockable(item.index))
        end
    end
    return item
end

--------------------------------------------------------------------
--获取宠物出战位数
--------------------------------------------------------------------
function m.GetHoleNum()
    local equipCfg = Schemes.Equipment:Get(HelperL.GetSidekickID())
    if not equipCfg then
        return 0
    end
    local part = HelperL.GetEquipGemPart(equipCfg.SubType, 1)
    local gemData = EntityModule.hero.equipGemPartDataLC:Get(part)
    return gemData.HoleNum
end

--------------------------------------------------------------------
--请求回调
--------------------------------------------------------------------
function m.RequestCallBack(result, content)
    if result == RESULT_CODE_BASE.RESULT_COMMON_SUCCEED[1] then
        if m.RequestType == 1 then     -- 请求激活
            m.UpdateView()
        elseif m.RequestType == 2 then -- 请求培养
            --播放升级特效
            HelperL.PlayVFX()
        elseif m.RequestType == 3 then -- 请求激活出战位
            m.UpdateSidekick1()
        end
    else
        HelperL.ShowMessage(TipType.FlowText, ResultCode:GetResultCodeStr(result, content))
    end
end

--------------------------------------------------------------------
-- 请求激活
--------------------------------------------------------------------
function m.OnRequestActiveMount()
    if not m.selectSidekickIndex then return end
    local data = m.saidekick2_ItemList[m.selectSidekickIndex].data
    m.RequestType = 1
    local str_req = string.format("LuaRequestActiveMount?mountID=%s", data.ID)
    LuaModule.RunLuaRequest(str_req, m.RequestCallBack)
end

--------------------------------------------------------------------
-- 请求培养
--------------------------------------------------------------------
function m.OnRequestEquipStarUp()
    m.PlayBtnSound()
    if not m.selectSidekickIndex then return end
    local data = m.saidekick2_ItemList[m.selectSidekickIndex].data
    local entity = SkepModule:GetSkepByID(SKEPID.SKEPID_MOUNT):GetEntityByGoodsID(data.ID)
    if not entity then return end
    m.RequestType = 2
    local str_req = string.format(
        "LuaRequestEquipStarUp?equip=%s&type=%s&bindflag=%s&AutoBuy=%s&LoopTimes=%s&WishType=%s",
        entity.uid, 0, 0, 0, 1, 2)
    LuaModule.RunLuaRequest(str_req, m.RequestCallBack)
end

--------------------------------------------------------------------
-- 请求激活出战位
--------------------------------------------------------------------
function m.OnRequest_ActiveGemHole(index)
    local holeNum = m.GetHoleNum()
    if index <= holeNum then
        HelperL.ShowMessage(TipType.FlowText, '已解锁')
        return
    end
    if index > holeNum + 1 then
        HelperL.ShowMessage(TipType.FlowText, '请先解锁前一个')
        return
    end
    local cfg = Schemes.Gemstone:Get(index)
    --判断消耗
    if cfg.NeedGoods ~= 0 and HelperL.IsLackGoods(cfg.NeedGoods, cfg.GoodsNumber, false, true) then
        return
    end
    m.RequestType = 3
    local str_req = string.format("LuaRequestActiveGemHole?equipIndex=%s&holeIndex=%s", 1, index)
    LuaModule.RunLuaRequest(str_req, m.RequestCallBack)
end

--------------------------------------------------------------------
-- 请求出战
--------------------------------------------------------------------
function m.OnRequest_ChuZhan()
    if not m.selectSidekickIndex then return end
    local data = m.saidekick2_ItemList[m.selectSidekickIndex].data
    m.PetsInStage[data.ID] = true
    m.SavePets_OnStage()
end

--------------------------------------------------------------------
-- 请求下阵
--------------------------------------------------------------------
function m.OnRequest_XiaZhen(ID)
    m.PetsInStage[ID] = false
    m.SavePets_OnStage()
end

--------------------------------------------------------------------
-- 延迟保存
--------------------------------------------------------------------
function m.SavePets_OnStage()
    local petsInStage = {}
    for k, v in pairs(m.PetsInStage) do
        if v then
            table.insert(petsInStage, k)
        end
    end

    local actorData = LoginModule:GetSelectActorData()
    local form = {}
    form["ApiVersion"] = 'v1'
    form['PetsInStage'] = dkjsonHelper.encode(petsInStage)
    HttpReques.SendRequest(ERequestID.SavePets_OnStage, form)
end

return m
