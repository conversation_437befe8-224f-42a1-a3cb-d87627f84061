--[[
********************************************************************
    created:	2023/10/21
    author :	李锦剑
    purpose:    商店
*********************************************************************
--]]

local luaID = ('UIShop')
local o = {}
--商店刷新次数
local RefreshCountMax = 15
--命格商店ID
local FateShop = 2
--消耗类型转换物品ID
local CostType = {
	[1] = 3, --钻石
	[2] = 4, --金币
	[3] = 7, --声望
	[4] = 6, --帮贡(历练)
	[5] = 9, --功勋
	[6] = 10, --绑定元宝
	[7] = 11, --点券
	[8] = 5, --熔炼值
	[9] = 16, --夺宝券
	[10] = 18, --虚拟金币
}

-- 订阅事件列表
function o.GetOpenEventList()
	return {
		[EventID.StoreList] = o.UpdateView,
		[EventID.StoreBuyItem] = o.StoreBuyItem,
		[EventID.StoreUpdateFree] = o.UpdateView,
		[EventID.LogicDataChange] = o.UpdateView,
		[EventID.EntitySyncBuff] = o.UpdateView,
		[EventID.OnGoodsPropChange] = o.UpdateView,
	}
end

-- 初始化
function o.OnCreate()
	---@type StoreItem[]
	o.StoreItemList = {}
	o.ToggleItemList = {}
	o.Init()
	return true
end

-- 窗口开启
function o:OnOpen(storeID, index)
	o.storeID = storeID
	o.StorePaging = {}
	local itemID
	local cfg = Schemes.Store:Get(storeID)
	for i = 1, 10 do
		itemID = cfg['StoreItemsID' .. i]
		if itemID ~= 0 then
			table.insert(o.StorePaging, {
				StoreItemsID = itemID,
				Name = cfg['Name' .. i],
				Refresh = cfg['Refresh' .. i],
				ActivityID = cfg['ActivityID' .. i],
				NeedLevel = cfg['NeedLevel' .. i],
				MinStoreRank = cfg['MinStoreRank' .. i],
				StoreRankDiscount = cfg['StoreRankDiscount' .. i],
			})
		end
	end

	local num = math.max(#o.ToggleItemList, #o.StorePaging)
	for i = 1, num, 1 do
		if not o.ToggleItemList[i] then
			o.ToggleItemList[i] = o.CreationToggle(i)
		end
		o.ToggleItemList[i].UpdateData(o.StorePaging[i])
	end
	o.SelectToggle(index or 1)
end

-- 收到窗口消息
function o:OnWndMsgBase(index)
	o.SelectToggle(index or 1)
end

--初始化
function o.Init()
	o.objList.Btn_Close.onClick:AddListenerEx(function()
		o:CloseSelf()
	end)
end

--更新界面
function o.UpdateView()
	o.Store = StoreModule:GetStoreItemList(o.selectStoreID)
	if o.Store then
		table.sort(o.Store.ItemList, function(a, b)
			if a.SoldOut ~= b.SoldOut then
				return not a.SoldOut
			end
			-- if a.CostNum ~= 0 and b.CostNum ~= 0 then
			-- 	return a.GoodsID > b.GoodsID
			-- end
			return a.GoodsID > b.GoodsID
		end)

		local item
		local num = math.max(#o.Store.ItemList, #o.StoreItemList)
		for i = 1, num, 1 do
			if not o.StoreItemList[i] then
				o.StoreItemList[i] = o.CreationStoreItem()
			end
			item = o.StoreItemList[i]
			item.UpdateData(o.Store.ItemList[i], i)
		end
	end
end

--按钮选择
function o.SelectToggle(index)
	local item
	if o.selectIndex then
		item = o.ToggleItemList[o.selectIndex]
		item.com.Img_Bg1.gameObject:SetActive(true)
		item.com.Img_Bg2.gameObject:SetActive(false)
	end
	item = o.ToggleItemList[index]
	o.selectIndex = index
	o.selectStoreID = item.data.StoreItemsID
	item.com.Img_Bg1.gameObject:SetActive(false)
	item.com.Img_Bg2.gameObject:SetActive(true)
	StoreModule:RequestGetStoreList(o.selectStoreID)
end

---创建按钮
function o.CreationToggle(index)
	local item = {}
	item.index = index
	item.com = o:CreateSubItem(o.objList.Grid_Toggle, o.objList.Item_Toggle)
	item.com.Img_Bg1.gameObject:SetActive(true)
	item.com.Img_Bg2.gameObject:SetActive(false)
	item.com.Btn_Bg.onClick:AddListenerEx(function()
		o.SelectToggle(item.index)
	end)
	item.UpdateData = function(data)
		if data then
			item.data = data
			item.com.Txt_Name.text = data.Name
			item.com.gameObject:SetActive(true)
		else
			item.com.gameObject:SetActive(false)
		end
	end
	return item
end

-- 创建当个商品栏
function o.CreationStoreItem()
	---@class StoreItem
	local item = {}
	item.com = o:CreateSubItem(o.objList.Grid_Goods, o.objList.Item_Goods)
	item.com.Btn_Buy.onClick:AddListenerEx(function()
		-- o.OnPurchase(o.selectStoreID, item.data.ItemID, 1)
		UIManager:OpenWnd(WndID.PurchaseWindows, o.selectStoreID, 1, item.data)
	end)
	item.com.Btn_Buy2.onClick:AddListenerEx(function()
		HelperL.ShowMessage(TipType.FlowText, RESULT_CODE_BASE.RESULT_COMMON_LACK_GOODS[2])
	end)
	item.com.Btn_Ad.onClick:AddListenerEx(function()
		AdvertisementManager.ShowRewardAd(item.data.Discount, function(bool)
			if bool then
				o.OnPurchase(o.selectStoreID, item.data.ItemID, 1)
			end
		end)
	end)

	-- --每秒更新
	item.SecondUpdate = function()
		if not item.data or item.data.SoldOut or item.data.CostNum ~= 0 then return end
		local state = HelperL.GetAdverState(item.data.Discount)
		item.com.Btn_Ad.gameObject:SetActive(false)
		item.com.Btn_Grey.gameObject:SetActive(false)
		item.com.Txt_Grey.gameObject:SetActive(false)
		if state == 1 then
			item.com.Btn_Ad.gameObject:SetActive(true)
		elseif state == 2 then
			local commonText = Schemes.CommonText:Get(item.data.Discount)
			local time = EntityModule.hero.buffLC:GetBuffLeftTime(commonText.Param1)
			item.com.Txt_Grey.text = HelperL.GetTimeString(TimeStringType.FullAuto2, time)
			item.com.Btn_Grey.gameObject:SetActive(true)
			item.com.Txt_Grey.gameObject:SetActive(true)
		else
			item.com.Btn_Grey.gameObject:SetActive(true)
			item.com.Txt_Grey.gameObject:SetActive(true)
			item.com.Txt_Grey.text = CommonTextID.SOLD_OUT
		end
	end

	---更新数据/页面
	---@param data ItemData
	---@param index integer
	item.UpdateData = function(data, index)
		item.index = index
		item.data = data
		item.com.gameObject:SetActive(false)
		if data then
			local cfg = Schemes:GetGoodsConfig(data.GoodsID)
			if cfg then
				local level = EntityModule.hero:GetProperty(CREATURE_FIELD.CREATURE_FIELD_LEVEL)
				if o.selectStoreID ~= 1
					or data.PlayerLevel >= ((level * 0.1 - 1) * 10)
					and data.PlayerLevel <= ((level * 0.1) * 10)
				then
					if not item.goods then
						item.goods = CreateSingleGoods(item.com.Img_Goods)
						item.goods:SetSize(120, 120)
						item.goods:SetShowName(false)
						item.goods:SetShowNum(true)
						item.goods:SetVisible(true)
					end
					item.goods:SetItemData(data.GoodsID, data.GoodsNum)
					item.com.gameObject:SetActive(true)
				end
			end
			local num = SkepModule:GetGoodsCount(CostType[data.CostType])
			if num >= data.CostNum then
				item.com.Btn_Buy.gameObject:SetActive(true)
				item.com.Btn_Buy2.gameObject:SetActive(false)
			else
				item.com.Btn_Buy.gameObject:SetActive(false)
				item.com.Btn_Buy2.gameObject:SetActive(true)
			end
			item.com.Txt_CostNum.text = data.CostNum
			AtlasManager:AsyncGetGoodsSprite(CostType[data.CostType], item.com.Img_CostType)
			item.com.Txt_CostNum2.text = data.CostNum
			AtlasManager:AsyncGetGoodsSprite(CostType[data.CostType], item.com.Img_CostType2)

			item.com.Obj_AD.gameObject:SetActive(false)
			item.com.Obj_Buy.gameObject:SetActive(false)
			item.com.Btn_Buy.gameObject:SetActive(false)
			item.com.Btn_SoldOut.gameObject:SetActive(false)
			if data.SoldOut then
				item.com.Obj_Buy.gameObject:SetActive(true)
				item.com.Btn_SoldOut.gameObject:SetActive(true)
			else
				if o.selectStoreID == 7 or data.CostNum == 0 then
					item.com.Obj_AD.gameObject:SetActive(true)
					-- local num, toTime = Schemes.CommonText.GetAdToTimeByCardID(item.data.Discount)
					-- item.com.Txt_AdNum.text = string.format(GetGameText(luaID, 19), UI_COLOR.Red, num, toTime)
				else
					item.com.Obj_Buy.gameObject:SetActive(true)
					item.com.Btn_Buy.gameObject:SetActive(true)
				end
			end
		end
	end
	return item
end

--每秒更新
function o.OnSecondUpdate()
	for i, v in ipairs(o.StoreItemList) do
		v.SecondUpdate()
	end
end

--购买商品
function o.OnPurchase(storeID, itemID, itemNum)
	if o.storeID == FateShop then
		local skep = SkepModule:GetSkepByID(SKEPID.SKEPID_GEMPACK)
		if skep:IsFull() then
			HelperL.ShowMessage(TipType.FlowText, GetGameText(('UIFate'), 8))
			return
		end
	end
	StoreModule:StoreBuyItem(storeID, itemID, itemNum)
end

--刷新商店
function o.OnClickRefreshStore()
	if o.Store.RefreshNum + 1 <= RefreshCountMax then
		return
	end
	StoreModule:RequestRefreshStore(o.selectStoreID, 0)
end

---购买商品回调
---@param data SC_Store_BuyItem
function o.StoreBuyItem(data)
	if not data then return end
	if data.StoreID == o.selectStoreID then
		local Item
		for i, v in ipairs(o.Store.ItemList) do
			if v.ItemID == data.ItemID then
				Item = v
				break
			end
		end
		if not Item then return end
		local cfg = Schemes.Medicament:Get(Item.GoodsID)
		if not cfg then return end
		local color = HelperL.GetColorByQuality(cfg.Quality)
		if Item.GoodsID <= DEFINE.MAX_MEDICAMENT_ID then
			color = HelperL.GetColorByQuality(cfg.Quality)
		end
		HelperL.RewardHintUI(UIRewardHint_Type.Message, string.format(
			'<color=#%s>%s</color> + %s',
			color,
			cfg.GoodsName,
			Item.GoodsNum
		))
	end
end

return o
