--[[
********************************************************************
    created:    2024/04/18
    author :    李锦剑
    purpose:    开宝箱界面
*********************************************************************
--]]

local luaID = ('UISkillsIntroduction')

---@class UISkillsIntroduction:UIWndBase
local m = {}

--------------------------------------------------------------------
-- 订阅事件列表
--------------------------------------------------------------------
function m.Get<PERSON><PERSON>List()
    return {

    }
end

--------------------------------------------------------------------
--创建时事件
--------------------------------------------------------------------
function m.OnCreate()
    m.skillItemList = {}
    m.UpdateView()
    m.RegisterClickEvent()
    return true
end

--------------------------------------------------------------------
--打开时
--------------------------------------------------------------------
function m.OnOpen()

end

--------------------------------------------------------------------
--注册点击事件
--------------------------------------------------------------------
function m.RegisterClickEvent()
    m.objList.Btn_Close.onClick:AddListenerEx(function()
        m:CloseSelf()
    end)
end

--------------------------------------------------------------------
--界面更新
--------------------------------------------------------------------
function m.UpdateView()
    for index, value in ipairs(Schemes.EquipWeapon.items) do
        if not m.skillItemList[value.ID] then
            m.skillItemList[value.ID] = m.CreateSkillItem(index)
        end
        m.skillItemList[value.ID].UpdateData(value)
    end
end

--------------------------------------------------------------------
--创建技能框
--------------------------------------------------------------------
function m.CreateSkillItem(index)
    local item = {}
    item.index = index
    item.com = m:CreateSubItem(m.objList.Grid_Skill, m.objList.Item_Skill)
    item.UpdateData = function(data)
        if data then
            local equipCfg = Schemes.Equipment:Get(data.ID)
            local skillConfig = Schemes.CatSkill:Get(equipCfg.ConsignmentStyle)
            AtlasManager:AsyncGetSprite(skillConfig.Icon, item.com.Img_Icon1)

            local buffCfg = Schemes.Buff:Get(skillConfig.CatSkillComb)
            AtlasManager:AsyncGetSprite(buffCfg.Icon, item.com.Img_Icon2)

            local skillConfig2 = Schemes.CatSkill:Get(skillConfig.FinalSkillIDs[1])
            AtlasManager:AsyncGetSprite(skillConfig2.Icon, item.com.Img_Icon3)

            item.com.gameObject:SetActive(true)
        else
            item.com.gameObject:SetActive(false)
        end
    end
    return item
end

return m
