local luaID = ('UIMatchingTip')

local UIMatchingTip = {}

-- 初始化
function UIMatchingTip:OnCreate()
	self.objList.Btn_Close.onClick:AddListenerEx(self.OnClickClose)
	self.objList.Btn_Cancel.onClick:AddListenerEx(self.OnClickClose)
	self.btnList = { self.objList.Btn_Close, self.objList.Btn_Cancel}
	self.objList.Txt_Cancel.text = GetGameText(luaID, 2)
	self.objList.Txt_Desc.text = GetGameText(luaID, 1)

	self.gameTipList = {}
	table.insert(self.gameTipList, GetGameText(luaID, 3))
	table.insert(self.gameTipList, GetGameText(luaID, 4))
	table.insert(self.gameTipList, GetGameText(luaID, 5))
	table.insert(self.gameTipList, GetGameText(luaID, 6))
	
	return true
end

-- 窗口开启
function UIMatchingTip:OnOpen(title)
	self.matchingCount = 0
	self.flushListCount = 5
	if title then
		self.objList.Txt_Title.text = title
	end

	local tipIndex = math.random(1, #self.gameTipList)
	self.objList.Txt_GameTip.text = self.gameTipList[tipIndex]
end

function UIMatchingTip.OnClickClose()
	local self = UIMatchingTip
	MatchDataManager:RequestBeginMatch(0)
	self:CloseSelf()
end

-- 窗口关闭
function UIMatchingTip:OnClose()
end

-- 每秒更新
function UIMatchingTip:OnSecondUpdate()
	local matchingID = MatchDataManager:GetMatchingID()
	if matchingID > 0 and self.matchingCount then
		self.matchingCount = self.matchingCount + 1
		self.objList.Txt_Num.text = self.matchingCount
	end 
	
	self.flushListCount = self.flushListCount - 1
	if self.flushListCount <= 0 then
		MatchDataManager:RequestMatchList(1, 0)
		self.flushListCount = 5
	end
end

-- 窗口销毁
function UIMatchingTip:OnDestroy()
	-- 释放内存
	for i, btn in ipairs(self.btnList) do
		btn.onClick:RemoveAllListeners()
		btn.onClick:Invoke()
	end
end

return UIMatchingTip