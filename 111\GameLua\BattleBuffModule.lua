local luaID = ('BattleBuffModule')


local m = {}
m.buffDic = nil

--进入战斗，初始化buff
function m:InitBuff()
    m.buffDic = {}
    m.initialBuff = nil
end

-- 挑战模式 玩家的初始随机buff
function m.AddPlayerInitBuff(buffID)
    m.initialBuff = buffID

    local config = Schemes.Buff:Get(buffID)
    local buffEffect = Schemes.BuffEffect:Get(config.EffectID1)
    if buffEffect.Param1 == 28 then -- 主技能升满
        -- print("把主技能火之装备技能拉满")
        local mainWeaponSkillID
        local equipSkep = SkepModule.GetEquipSkep()
        local i = EQUIP_TYPE.EQUIP_TYPE_NORMALWEAPON
        if equipSkep[i] then
            local equipUID = equipSkep[i]
            local entity = EntityModule:GetEntity(equipUID)
            if entity then
                local equipID = entity:GetProperty(ENTITY_FIELD.ENTITY_FIELD_ID)
                -- print(i  .. "equipID ========== " .. equipID)
                local equipConfig = Schemes.Equipment:Get(equipID)
                if equipConfig then
                    if equipConfig.ConsignmentStyle > 0 then --装备技能
                        mainWeaponSkillID = equipConfig.ConsignmentStyle
                    end
                end
            end
        end
        if mainWeaponSkillID then
            for key, value in pairs(BattleSkillModule.skillList) do
                if value.SkillID == mainWeaponSkillID then
                    local skillConfig = Schemes.CatSkill:Get(mainWeaponSkillID)
                    while skillConfig.Next_Lv ~= 0 do
                        skillConfig = Schemes.CatSkill:Get(skillConfig.Next_Lv)
                    end
                    value.SkillID = skillConfig.ID
                    value.Level = skillConfig.Level
                end
            end
        end
    elseif buffEffect.Param1 == 29 then -- 随机几个技能升满
        local RandomDataBank = {}

        if #BattleManager.levelAllSkillIdsList > 0 then
            local curSkillBaseIDList = BattleSkillModule:GetBattleSkillBaseIDList()

            for key, value in pairs(BattleManager.levelAllSkillIdsList) do
                if type(value) == 'table' then
                    if not HelperL.tableContain(curSkillBaseIDList, value.SkillID) then
                        table.insert(RandomDataBank, value.SkillID)
                    end
                elseif type(value) == 'number' then
                    if not HelperL.tableContain(curSkillBaseIDList, value) then
                        table.insert(RandomDataBank, value)
                    end
                end
            end
        end

        local randResult = {}
        local rIndex, rID
        local randomCount = 0
        while #randResult < buffEffect.Param3 do
            randomCount = randomCount + 1
            if randomCount > 200 then
                break
            end
            rIndex = Mathf.Ceil(math.random(1, #RandomDataBank))
            rID = RandomDataBank[rIndex]
            if not HelperL.tableContain(randResult, rID) then
                table.insert(randResult, rID)
            end       
        end
        -- print("随机结果")
        --HelperL.Dump(randResult)


        for index, value in ipairs(randResult) do
            -- local curBuff = Schemes.Buff:Get(value)
            -- if curBuff then
            --     local buffInfo = {}
            --     while curBuff.NextID ~= 0 do
            --         curBuff = Schemes.Buff:Get(curBuff.NextID)
            --     end
            --     buffInfo.BuffID = curBuff.ID
            --     buffInfo.Level = curBuff.Level
            --     buffInfo.BaseID = value
            --     buffInfo.LifeTime = os.time() + config.KeepTime/1000
            --     buffInfo.IsUpgradeBuff = true
            --     print("添加BUF成功")
            --     m.buffDic[value] = buffInfo
            --     BattleSkillModule:AndCDBuff(value, buffInfo.Level)
            -- end

            local curskill = Schemes.CatSkill:Get(value)
            if curskill then
                local skillInfo = {}
                while curskill.Next_Lv ~= 0 do
                    curskill = Schemes.CatSkill:Get(curskill.Next_Lv)
                end
                skillInfo.SkillID = curskill.ID
                skillInfo.CoolTime = curskill.CD
                skillInfo.CoolCD = curskill.CD

                if BattleSkillModule.buffEffConfig then
                    skillInfo.CoolCD = curskill.CD * (1 - BattleSkillModule.buffEffConfig.Param3 / 10000)
                    if skillInfo.CoolCD < curskill.MinCD then
                        skillInfo.CoolCD = curskill.MinCD
                    end
                end
                skillInfo.Level = curskill.Level
                skillInfo.Using = false
                --skillInfo.BaseID = value
                skillInfo.Type = curskill.Type
                table.insert(BattleSkillModule.skillList, skillInfo)
            end
        end
    end
end

--添加升级 buff
function m:AddUpgradeBuff(buffID)
    local config = Schemes.Buff:Get(buffID)
    if config then
        local buffInfo = {}
        buffInfo.BuffID = buffID
        buffInfo.Level = 1
        buffInfo.Type = config.Type
        buffInfo.LifeTime = os.time() + config.KeepTime / 1000
        buffInfo.IsUpgradeBuff = true
        -- print("添加BUF成功")
        --HelperL.Dump(buffInfo)
        m.buffDic[buffID] = buffInfo

        --通知添加buff
        --Add baseBuffID，buffInfo.Level
        BattleSkillModule:AndCDBuff(buffID)
    end
end

function m:GetBattleBuffTypeList()
    local result = {}
    local buffList = m:GetBattleBuffList()
    for key, value in pairs(buffList) do
        if HelperL.ArrayIndexOf(result, value.Type) == -1 then
            table.insert(result, value.Type)
        end
    end
    return result
end

--添加普通 buff
function m:AddCommonBuff(buffID)
    local config = Schemes.Buff:Get(buffID)
    if not config then return end
    local buffInfo = m.buffDic[baseBuffID]
    if not buffInfo then
        buffInfo = {}
        buffInfo.BuffID = buffID
        buffInfo.Level = config.Level
        buffInfo.Type = config.Type
        buffInfo.LifeTime = os.time() + config.KeepTime / 1000
        buffInfo.IsUpgradeBuff = false
        buffInfo.Config = Schemes.Buff:Get(buffID)
        m.buffDic[config.Type] = buffInfo
        --通知添加buff
        --Add baseBuffID，buffInfo.Level
    else
        if config.Overlay == 0 then
            if config.Level >= buffInfo.Level then
                buffInfo.Level = config.Level
                buffInfo.LifeTime = os.time() + config.KeepTime / 1000
            else
                buffInfo.LifeTime = os.time() + buffInfo.Config.KeepTime / 1000
            end
        elseif config.Overlay == 1 then
            buffInfo.Level = buffInfo.Level + config.Level
            if m.buffDic[baseBuffID].Level >= config.MaxLevel then
                buffInfo.Level = config.MaxLevel
            end
            buffInfo.LifeTime = os.time() + config.KeepTime / 1000
        elseif config.Overlay == 2 then
            buffInfo.Level = buffInfo.Level + config.Level
            if m.buffDic[baseBuffID].Level >= config.MaxLevel then
                buffInfo.Level = config.MaxLevel
            end
            buffInfo.LifeTime = os.time() + (buffInfo.Config.KeepTime + config.KeepTime) / 1000
        elseif config.Overlay == 3 then
            if config.Level >= buffInfo.Level then
                buffInfo.Level = config.Level
                buffInfo.LifeTime = os.time() + (buffInfo.Config.KeepTime + config.KeepTime) / 1000
            else
                buffInfo.LifeTime = os.time() + buffInfo.Config.KeepTime / 1000
            end
        end

        --通知更新buff
        --Update baseBuffID，buffInfo.Level
    end
end

--升级buff
function m:ReceiveBuff(buffInfo)
    if BattleManager.skillAndBuffFull then
        -- print("1传到c#" .. buffInfo.BuffID)
        EntityModule.luaToCshape:ReceiveBuff(buffInfo.BuffID)
        return
    end
    local config = Schemes.Buff:Get(buffInfo.BuffID)
    if not config then return end
    local old = m:GetBuffInfo(buffInfo.Type)
    -- print("3传到c#" .. config.ID)
    EntityModule.luaToCshape:ReceiveBuff(config.ID)
    if old then
        m.buffDic[buffInfo.Type].BuffID = config.ID;
        m.buffDic[buffInfo.Type].Level = config.Level
        m.buffDic[buffInfo.Type].LifeTime = os.time() + config.KeepTime / 1000
    else
        m:AddUpgradeBuff(buffInfo.BuffID)
    end
    EventManager:Fire(EventID.UpgradeSkill, 2, buffInfo)
end

function m:OnUpdate()
    -- 暂时不移除
    -- for k, v in ipairs(m.buffDic) do
    --     if v and os.time() >= v.LifeTime then
    --         --通知移除buff
    --         --Remove buffInfo.BaseID
    --         BattleSkillModule:RemoveCDBuff(v.BaseID)
    --         m.buffDic[k] = nil
    --     end
    -- end
end

--是否有技能相同的突破组合
function m:IsHaveSkillComb(combID)
    if not m.buffDic then
        return false
    end

    for k, v in ipairs(m.buffDic) do
        if v and v.Config and v.Config.CombID == combID then
            return true
        end
    end
    return false
end

function m:hadBuff(baseID)
    for k, v in pairs(m.buffDic) do
        if v.BaseID == baseID then
            return true
        end
    end
    return false
end

function m:GetBattleBuffList()
    local tempList = {}
    for k, v in pairs(m.buffDic) do
        if v then -- and os.time() < v.LifeTime then   --   暂时去掉时间
            table.insert(tempList, v)
        end
    end
    return tempList
end

function m.GetBattleBuffNum()
    local curBuffList = m:GetBattleBuffList()
    return #curBuffList
end

--获取buff总等级
function m:GetBuffTotalLevel()
    local totalLevel = 0
    for k, v in ipairs(m.buffDic) do
        if v and os.time() < v.LifeTime then
            totalLevel = totalLevel + v.Level
        end
    end
    return totalLevel
end

--获取buff
function m:GetBuffInfo(buffType)
    for key, value in pairs(m.buffDic) do
        local buffinfo = value
        if buffinfo.Type == buffType then
            return buffinfo
        end
    end
    return nil
end

function m:GetEmptyBuffInfo(baseBuffID)
    local buffInfo = {}
    buffInfo.BuffID = baseBuffID
    buffInfo.Level = 0
    buffInfo.BaseID = baseBuffID
    buffInfo.Config = Schemes.Buff:Get(baseBuffID)
    buffInfo.ID = baseBuffID
    return buffInfo
end

--结束战斗
function m:EndBattle()
    m.buffDic = nil
end

BattleBuffModule = m
