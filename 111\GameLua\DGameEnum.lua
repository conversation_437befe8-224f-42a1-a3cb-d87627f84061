﻿-- 公共枚举定义

-- 提示类型
TipType =
{
	FlowText = 1, -- 飘字
	MoveText = 2, -- 移动标签
	MsgBoxType1 = 3, -- 提示框类型1
	MsgBoxType2 = 4, -- 提示框类型2
	MsgBoxType3 = 5, -- 提示框类型3
	MsgBoxType4 = 6, -- 提示框类型4
	MsgBoxType5 = 7, -- 说明框类型
}

-- 提示飘字类型
TipFlyType =
{
	Res1 = 1,  -- 金矿
	Res2 = 2,  -- 竹叶
	SkillUse = 3, -- 人使用技能
}

-- 提示位置类型
PosType = {
	WorldPos = 1,
	ScreenPos = 2,
	UIPos = 3,
	UIWorldPos = 4,
}

-- 飘字方向类型
TipDirection = {
	ToLeft = 1,
	ToUp = 2,
	ToRight = 3,
	ToDown = 4,

	FromLeft = 1001,
	FromUp = 1002,
	FromRight = 1003,
	FromDown = 1004,
}

DEFINE =
{
	MAX_CREATE_ACTOR_NUM = 3, -- 最大可创建细胞数量
	MAX_CURRENCY_ID = 30,   -- 货币最大ID
	MIN_MEDICAMENT_ID = 1000, -- 药品最小ID
	MAX_MEDICAMENT_ID = 9999, -- 药品最大ID
	MIN_EQUIPMENT_ID = 100000, -- 装备最小ID
	MAX_EQUIPMENT_ID = 999999, -- 装备最大ID
	MIN_SPIRIT_ID = 310000, -- 灵件最小ID
	MAX_SPIRIT_ID = 314999, -- 灵件最大ID
	MAX_TAKEN_WEAPON_NUM = 5, -- 最大可携带火之装备数量(客户端的改5个，服务器保留原来的3个)
	MAX_PLAYER_LEVEL = 150, -- 最大细胞等级
	MAX_BUFF_LEVEL = 300,   -- 最大Buff等级
}

POWER_COEF =
{
	POWER_COEF_HP = 1,                -- 每点生命上限增加战斗力
	POWER_COEF_ATK = 2,               -- 每点攻击增加战斗力
	POWER_COEF_DEF = 2,               -- 每点防御增加战斗力
	POWER_COEF_PATK = 1,              -- 每点物理攻击增加战斗力
	POWER_COEF_MATK = 1,              -- 每点魔法攻击增加战斗力
	POWER_COEF_PDEF = 1,              -- 每点物理防御增加战斗力
	POWER_COEF_MDEF = 1,              -- 每点魔法防御增加战斗力
	POWER_COEF_CRITICAL_STRIKE = 5,   -- 每点暴击增加战斗力
	POWER_COEF_ANTI_CRITICAL_STRIKE = 5, -- 每点防暴增加战斗力
	POWER_COEF_PARRY = 5,             -- 每点格挡增加战斗力
	POWER_COEF_ANTI_PARRY = 5,        -- 每点穿透增加战斗力
	POWER_COEF_DODGE = 5,             -- 每点闪避增加战斗力
	POWER_COEF_HIT = 5,               -- 每点命中增加战斗力
	POWER_COEF_ARMOR = 0,             -- 每点护甲增加战斗力
	POWER_COEF_ANTI_ARMOR = 0,        -- 每点破甲增加战斗力
	POWER_COEF_DAMAGE_REDUCTION = 2,  -- 每点减伤增加战斗力
	POWER_COEF_DAMAGE_ADD = 2,        -- 每点伤害加深增加战斗力
	POWER_COEF_SPD = 0,               -- 每点移动速度增加战斗力
	POWER_COEF_ATK_DEF = 4,           -- 每点全攻全防增加战斗力
	POWER_COEF_RATE = 1,              -- 战斗力计算放大倍数
}

-- 装备品质
QUALITY =
{
	QUALITY_WHITE = 0, -- 白色品质
	QUALITY_GREEN = 1, -- 绿色品质
	QUALITY_BLUE = 2, -- 蓝色品质
	QUALITY_PINK = 3, -- 紫色品质
	QUALITY_GOLD = 4, -- 金色品质
	QUALITY_ORANGE = 5, -- 橙色品质
	QUALITY_RED = 6, -- 红色品质
	QUALITY_MAX = 7,
};

-- 背包篮子ID
---@enum SKEPID
-- 背包篮子ID
SKEPID =
{
	NONE = 0,
	SKEPID_EQUIP = 1,            -- 装备栏(流派1-剑)
	SKEPID_PACKET = 2,           -- 背包栏
	SKEPID_WEAPON = 3,           -- 体魄栏
	SKEPID_FASHION = 4,          -- 时装栏
	SKEPID_GEMPACK = 5,          -- 宝石背包(旧)  命魂(新)
	SKEPID_GEMSTORAGE = 6,       -- 宝石仓库
	SKEPID_WAREHOUSE = 7,        -- 仓库
	SKEPID_GODWEAPON = 8,        -- 新体魄栏(流派1-剑)
	SKEPID_GODWEAPONSPIRIT = 9,  -- 灵件(流派1-剑)
	SKEPID_MONSTERCARD = 10,     -- 怪物卡牌
	SKEPID_MOUNT = 11,           -- 坐骑
	SKEPID_MOUNTEQUIP = 12,      -- 坐骑装备
	SKEPID_WING = 13,            -- 仙器
	SKEPID_WINGEQUIP = 14,       -- 仙器器魂
	SKEPID_ACTIPACK = 15,        -- 活动背包
	SKEPID_TASKPACKET = 16,      -- 任务背包栏
	SKEPID_NEWWING = 17,         -- 新翅膀背包
	SKEPID_MOUNTEQUIPPACKET = 18, -- 坐骑装备背包
	SKEPID_EQUIP2 = 19,          -- 装备栏(流派2-笔)
	SKEPID_GODWEAPON2 = 20,      -- 新体魄栏(流派2-笔)
	SKEPID_GODWEAPONSPIRIT2 = 21, -- 灵件(流派2-笔)
	SKEPID_WINGEQUIPEXTRA = 22,  -- 仙器装备
	SKEPID_WINGEQUIPPACKET = 23, -- 仙器装备背包
	SKEPID_NEWWINGEQUIP = 24,    -- 羽翼装备
	SKEPID_NEWWINGEQUIPPACKET = 25, -- 羽翼装备背包
	SKEPID_WINGEQUIP2 = 26,      -- 仙器器魂(流派2-笔)
	SKEPID_AVATAR = 27,          -- 通用外显装备
	SKEPID_EQUIP3 = 28,          -- 装备栏(流派3)
	SKEPID_GODWEAPON3 = 29,      -- 新体魄栏(流派3)
	SKEPID_GODWEAPONSPIRIT3 = 30, -- 灵件(流派3)
	SKEPID_WINGEQUIP3 = 31,      -- 仙器器魂(流派3)
	SKEPID_AVATAREQUIPA = 32,    -- 通用外显附加装备A
	SKEPID_AVATAREQUIPPACKETA = 33, -- 通用外显附加装备背包A
	SKEPID_AVATAREQUIPB = 34,    -- 通用外显附加装备B
	SKEPID_AVATAREQUIPPACKETB = 35, -- 通用外显附加装备背包B
	SKEPID_EQUIP4 = 36,          -- 装备栏(流派4)
	SKEPID_GODWEAPON4 = 37,      -- 新体魄栏(流派4)
	SKEPID_GODWEAPONSPIRIT4 = 38, -- 灵件(流派4)
	SKEPID_WINGEQUIP4 = 39,      -- 仙器器魂(流派4)
	MAX = 39,
}

-- 装备类型
EQUIP_TYPE =
{
	EQUIP_TYPE_NULL          = 0,

	--装备栏第一套装备（8个，从1到11）（时装和废弃的除外）
	EQUIP_TYPE_HEAD          = 1, -- 装备-头饰1
	EQUIP_TYPE_ARMOUR        = 2, -- 装备-衣服2
	EQUIP_TYPE_SHOES         = 3, -- 装备-鞋子3
	EQUIP_TYPE_PANTS         = 4, -- 装备-裤子4（后面改成护腕了）
	EQUIP_TYPE_RING          = 5, -- 装备-水之装备5
	EQUIP_TYPE_NECKLACE      = 6, -- 装备-暗之装备6
	EQUIP_TYPE_WEAPON        = 7, -- 装备-体魄7（废弃）
	EQUIP_TYPE_FASHION       = 8, -- 装备-时装8
	EQUIP_TYPE_CLOAK         = 9, -- 装备-披风9（废弃）
	EQUIP_TYPE_NORMALWEAPON  = 10, -- 装备-火之装备10
	EQUIP_TYPE_BELT          = 11, -- 装备-雷之装备11

	EQUIP_TYPE_GODWEAPON     = 12, -- 装备-新体魄12
	EQUIP_TYPE_SPIRIT1       = 13, -- 装备-灵件1 13
	EQUIP_TYPE_SPIRIT2       = 14, -- 装备-灵件2 14
	EQUIP_TYPE_SPIRIT3       = 15, -- 装备-灵件3 15
	EQUIP_TYPE_SPIRIT4       = 16, -- 装备-灵件4 16
	EQUIP_TYPE_SPIRIT5       = 17, -- 装备-灵件5 17
	EQUIP_TYPE_SPIRIT6       = 18, -- 装备-灵件6 18
	EQUIP_TYPE_MONSTERCARD   = 19, -- 怪物卡牌
	EQUIP_TYPE_MOUNT         = 20, -- 坐骑
	EQUIP_TYPE_MOUNTEQUIP1   = 21, -- 坐骑装备1
	EQUIP_TYPE_MOUNTEQUIP2   = 22, -- 坐骑装备2
	EQUIP_TYPE_MOUNTEQUIP3   = 23, -- 坐骑装备3
	EQUIP_TYPE_MOUNTEQUIP4   = 24, -- 坐骑装备4
	EQUIP_TYPE_WING          = 25, -- 仙器
	EQUIP_TYPE_WINGEQUIP     = 26, -- 仙器器魂
	EQUIP_TYPE_NEWWING       = 27, -- 羽翼

	--装备栏第二套装备（8个，从28到35）
	EQUIP_TYPE_HEAD2         = 28, -- 装备-头饰 28
	EQUIP_TYPE_ARMOUR2       = 29, -- 装备-衣服 29
	EQUIP_TYPE_SHOES2        = 30, -- 装备-鞋子 30
	EQUIP_TYPE_CUFF2         = 31, -- 装备-护腕 31
	EQUIP_TYPE_RING2         = 32, -- 装备-水之装备 32
	EQUIP_TYPE_NECKLACE2     = 33, -- 装备-暗之装备 33
	EQUIP_TYPE_NORMALWEAPON2 = 34, -- 装备-火之装备 34
	EQUIP_TYPE_BELT2         = 35, -- 装备-雷之装备 35

	EQUIP_TYPE_WINGEQUIP1    = 36, -- 仙器装备1 36
	EQUIP_TYPE_WINGEQUIP2    = 37, -- 仙器装备2 37
	EQUIP_TYPE_WINGEQUIP3    = 38, -- 仙器装备3 38
	EQUIP_TYPE_WINGEQUIP4    = 39, -- 仙器装备4 39
	EQUIP_TYPE_NEWWINGEQUIP1 = 40, -- 翅膀装备1 40
	EQUIP_TYPE_NEWWINGEQUIP2 = 41, -- 翅膀装备2 41
	EQUIP_TYPE_NEWWINGEQUIP3 = 42, -- 翅膀装备3 42
	EQUIP_TYPE_NEWWINGEQUIP4 = 43, -- 翅膀装备4 43

	EQUIP_TYPE_AVATAR        = 44, -- 通用外显 44

	--装备栏第三套装备（8个，从45到52）
	EQUIP_TYPE_HEAD3         = 45, -- 装备-头饰 45
	EQUIP_TYPE_ARMOUR3       = 46, -- 装备-衣服 46
	EQUIP_TYPE_SHOES3        = 47, -- 装备-鞋子 47
	EQUIP_TYPE_CUFF3         = 48, -- 装备-护腕 48
	EQUIP_TYPE_RING3         = 49, -- 装备-水之装备 49
	EQUIP_TYPE_NECKLACE3     = 50, -- 装备-暗之装备 50
	EQUIP_TYPE_NORMALWEAPON3 = 51, -- 装备-火之装备 51
	EQUIP_TYPE_BELT3         = 52, -- 装备-雷之装备 52

	EQUIP_TYPE_AVATAREQUIPA1 = 53, -- 通用外显附加装备A1 53
	EQUIP_TYPE_AVATAREQUIPA2 = 54, -- 通用外显附加装备A2 54
	EQUIP_TYPE_AVATAREQUIPA3 = 55, -- 通用外显附加装备A3 55
	EQUIP_TYPE_AVATAREQUIPA4 = 56, -- 通用外显附加装备A4 56

	EQUIP_TYPE_AVATAREQUIPB1 = 57, -- 通用外显附加装备B1 57
	EQUIP_TYPE_AVATAREQUIPB2 = 58, -- 通用外显附加装备B2 58
	EQUIP_TYPE_AVATAREQUIPB3 = 59, -- 通用外显附加装备B3 59
	EQUIP_TYPE_AVATAREQUIPB4 = 60, -- 通用外显附加装备B4 60

	EQUIP_TYPE_MAXID         = 61,
}

-- 聊天频道
CHAT_CHANNEL =
{
	CHAT_CHANNEL_NULL = 0,
	CHAT_CHANNEL_ALL = 0,       -- 全部
	CHAT_CHANNEL_BROADCAST = 1, -- 系统广播
	CHAT_CHANNEL_SYSTEM = 2,    -- 系统频道
	CHAT_CHANNEL_TRUMPET = 3,   -- 小喇叭
	CHAT_CHANNEL_WORLD = 4,     -- 世界频道
	CHAT_CHANNEL_SOCIETY = 5,   -- 公会频道
	CHAT_CHANNEL_COUNTRY = 6,   -- 国家频道
	CHAT_CHANNEL_TEAM = 7,      -- 队伍
	CHAT_CHANNEL_ROOM = 8,      -- 房间
	CHAT_CHANNEL_HELP = 9,      -- 求组
	CHAT_CHANNEL_GLOBALTRUMP = 10, -- 全服喇叭频道
	CHAT_CHANNEL_WARBAND = 11,  -- 战队
	CHAT_CHANNEL_SCENE = 12,    -- 附近
	CHAT_CHANNEL_MAXID = 13,
}

-- 聊天类型
CHAT_TYPE =
{
	NORMAL = 0,
	RED_BAG = 1,
	VOICE = 2,
};

CONST_VALUE =
{
	CONST_VALUE_NULL = 0,                         -- 空常数
	CONST_PLAYER_BORN_MAPID = 1,                  -- 人物出生地图ID
	CONST_SAVEDB_INTERVAL = 2,                    -- 人物存盘时间间隔
	CONST_DESTROY_DELAY = 3,                      -- 销毁细胞对象延迟时间
	CONST_BROADCAST_TIME = 4,                     -- 停服广播持续时间
	CONST_BROADCAST_INTERVAL = 5,                 -- 停服广播时间间隔
	CONST_KICKOUT_NUM_ONCE = 6,                   -- 停服每秒踢出几个玩家
	CONST_WORLD_CHAT_LEVEL = 7,                   -- 世界聊天等级限制
	CONST_WORLD_CHAT_INTERVAL = 8,                -- 世界聊天发送间隔
	CONST_TRUMPET_COST_DIAMOND = 9,               -- 发送小喇叭消耗钻石
	CONST_TRUMPET_SPARE_WHEEL = 10,               -- 发送小喇叭消耗替代物
	CONST_PLAYER_LOGOUT_DELAY = 11,               -- 人物下线延迟时间
	CONST_SIGNIN_LEVEL_LIMIT = 12,                -- 每日签到等级限制
	CONST_RESIGNIN_COST_DIAMOND = 13,             -- 每日签到补签钻石消耗
	CONST_ARENAGAME_OPENLEVEL = 14,               -- 竞技场玩法开放等级
	CONST_ARENAGAME_INTERVAL = 15,                -- 竞技场被动挑战时间间隔
	CONST_ARENAGAME_TIMES_PRICE = 16,             -- 竞技场挑购买次数价格
	CONST_REVIVE_COST_DIAMOND = 17,               -- 副本复活钻石消耗
	CONST_ENERGY_RECOVERVALUE = 18,               -- 体力恢复值
	CONST_ENERGY_CANHOLD_MAX = 19,                -- 玩家可持有体力上限
	CONST_ENERGY_BUYONE_VALUE = 20,               -- 购买一次获得的体力值
	CONST_ENERGY_FETCH_VALUE = 21,                -- 购买领取体力获得的值
	CONST_BABEL_RESET_TIME = 22,                  -- 通天塔每天重置最大次数
	CONST_BRANCH_REFRESH_COST = 23,               -- 刷新日常任务消耗钻石数
	CONST_BRANCH_BUYROUND_COST = 24,              -- 购买一轮日常消耗钻石数
	CONST_CHIEF_INTERVAL = 25,                    -- 副本霸主挑战时间间隔
	CONST_LOTTERY_FREETIMES = 26,                 -- 每日金币抽奖免费次数
	CONST_LOTTERY_COSTMONEY = 27,                 -- 单次金币抽奖价格
	CONST_LOTTERY_COSTDIAMOND = 28,               -- 单次钻石抽奖价格
	CONST_ARENAGAME_COOLTIME = 29,                -- 竞技场主动挑战冷却时间
	CONST_ARENAGAME_COOLCOST = 30,                -- 竞技场消除冷却时间花费
	CONST_ECTYPE_SWEEPITEM = 31,                  -- 扫荡券物品ID
	CONST_ECTYPE_SWEEPPRICE = 32,                 -- 扫荡券单价
	CONST_MAIL_LISTLENGTH = 33,                   -- 邮箱长度
	CONST_HELP_COST_DIAMOND = 34,                 -- 求助消耗元宝
	CONST_ESCORT_LIST_NUM = 35,                   -- 押镖镖车列表最大数目
	CONST_ESCORT_ATTACK_TIMES = 36,               -- 押镖挑战最大次数
	CONST_ESCORT_ATTACK_DURATION = 37,            -- 押镖挑战时间间隔
	CONST_ESCORT_BEROBBED_TIMES = 38,             -- 押镖一次最大被劫次数
	CONST_ESCORT_DONE_TIMES = 39,                 -- 押镖最大次数
	CONST_ESCORT_ONCE_TIME = 40,                  -- 押镖时长
	CONST_ESCORT_ATTACKCD_COST = 41,              -- 押镖挑战清CD消耗
	CONST_ESCORT_REFRESH_COST = 42,               -- 押镖刷新星级消耗
	CONST_ESCORT_BEATTACK_DURATION = 43,          -- 押镖被挑战时间间隔
	CONST_ESCORT_ALMOST_DONW_TIME = 44,           -- 押镖最后4分钟不能被挑战
	CONST_TD_LEVEL_LIMIT = 45,                    -- TD等级限制
	CONST_SOCIETY_MEMBERTIME = 46,                -- 帮会成员信息列表更新间隔
	CONST_SOCIETY_SAVEDB_INTERVAL = 47,           -- 帮会存盘时间间隔
	CONST_SOCIETY_CREATELEVEL = 48,               -- 创建帮会需要等级
	CONST_SOCIETY_JOINLEVEL = 49,                 -- 加入帮会需要等级
	CONST_SOCIETY_CREATECOST = 50,                -- 创建帮会需要元宝
	CONST_SOCIETY_INCVITALITY = 51,               -- 上线增加活跃度
	CONST_SOCIETY_DECVITALITY = 52,               -- 每天下降活跃度百分比
	CONST_SOCIETY_VITALITYLIMIT = 53,             -- 活跃度小于1000时自动解散帮会
	CONST_SOCIETY_SUCCEEDTIME = 54,               -- 帮主离线两周可以继任帮主
	CONST_SOCIETY_SECONDLEADERNUM = 55,           -- 副帮主人数
	CONST_SOCIETY_THIRDLEADERNUM = 56,            -- 长老人数
	CONST_SOCIETY_SUCCEEDCOST = 57,               -- 继承帮主扣帮会帮贡
	CONST_SOCIETY_GETLISTLEN = 58,                -- 玩家获取帮会列表长度
	CONST_SOCIETY_VITALITYALARM = 59,             -- 活跃度小于1500时提醒将会解散帮会
	CONST_SOCIETY_FEASTDURATION = 60,             -- 帮会宴会时长
	CONST_SOCIETY_FEASTADD = 61,                  -- 帮会宴会恢复时长
	CONST_SOCIETY_FEASTTIMES = 62,                -- 帮会宴会最大次数
	CONST_SOCIETY_MENBERNUM = 63,                 -- 帮会人数<=40
	CONST_SOCIETY_INFOLEN = 64,                   -- 宣言字数<=40
	CONST_LOGIN_LEVEL_LIMIT = 65,                 -- 累计登陆的等级限制
	CONST_EXPBOSS_DONENUM = 66,                   -- 十二灵场每日次数
	CONST_WORLD_SYNCTIME = 67,                    -- 时间同步间隔
	CONST_ESCORT_REFRESH_VIP = 68,                -- 押镖刷新VIP等级
	CONST_CORNUCOPIA_REFRESH_VIP = 69,            -- 庄园刷新VIP等级
	CONST_AUTO_FIGHT_LEVEL = 70,                  -- 自动战斗等级
	CONST_CORNUCOPIA_GET_DURATION = 71,           -- 庄园每次埋宝的时间间隔
	CONST_ESCORT_FULLREFRESH_COST = 72,           -- 押镖刷新满星消耗
	CONST_ESCORT_FULLREFRESH_VIP = 73,            -- 押镖刷新满星VIP等级
	CONST_CORNUCOPIA_FULLREFRESH_COST = 74,       -- 庄园刷新满星消耗
	CONST_CORNUCOPIA_FULLREFRESH_VIP = 75,        -- 庄园刷新满星VIP等级
	CONST_GOLDBOSS_SOCIETYPRIZE_NUM = 76,         -- 黄金BOSS帮会奖励数量
	CONST_GOLDBOSS_ENTERTIME = 77,                -- 黄金BOSS主动进入间隔
	CONST_BATTLEGAME_CALLINTERVAL = 78,           -- 帮会战斗召唤间隔
	CONST_BATTLEGAME_CALLVALIDTIME = 79,          -- 帮会战斗召唤有效时间
	CONST_VIPSCORE_TIME_INTERVAL = 80,            -- vip隔多长时间加经验
	CONST_PRACTICEGAME_DURATION = 81,             -- 帮会修炼玩法时长
	CONST_PRACTICEGAME_BASEADDMORAL = 82,         -- 帮会修炼玩法基础增加修为
	CONST_PRACTICEGAME_PLAYERADDMORAL = 83,       -- 帮会修炼玩法购买增加个人修为
	CONST_PRACTICEGAME_SOCIETYADDMORAL = 84,      -- 帮会修炼玩法购买增加帮会修为
	CONST_PRACTICEGAME_OPENTIMES = 85,            -- 帮会修炼玩法开启次数
	CONST_MELEE_MINLEVEL = 86,                    -- 大乱斗的等级限制
	CONST_SOCIETY_INIT_VITALITY = 87,             -- 帮会初始活跃度
	CONST_SOCIETY_INIT_FEASTLEAVE = 88,           -- 帮会初始宴会次数
	CONST_MAIL_REFRESH_INTERVAL = 89,             -- 邮箱强制刷新时间间隔
	CONST_BRANCH_TASK_BEST_VIPLIMIT = 90,         -- 循环任务VIP8以上才能刷新到五星
	CONST_BRANCH_TASK_BEST_COST = 91,             -- 循环任务VIP8以上，100元宝，直接刷新到五星
	CONST_LEVELUP_NOTE = 92,                      -- 提示升级等级
	CONST_LEVELUP_POPUP = 93,                     -- 弹框提示升级等级
	CONST_FARM_KING_PERCENT = 94,                 -- 农场国王加成
	CONST_FARM_PROTECTED_TIME = 95,               -- 被抢保护时间
	CONST_FARM_ROBBEDTIMES_MAX = 96,              -- 最大被抢差
	CONST_FARM_ROBTIMES_MAX = 97,                 -- 抢劫次数最大保存值
	CONST_FARM_RECOVERROB_MAX = 98,               -- 抢劫次数最大恢复值
	CONST_FARM_RECOVERROB_DURATION = 99,          -- 抢劫次数恢复时间
	CONST_FARM_GET_PROTECTED = 100,               -- 农场收获前保护时间
	CONST_FARM_PLANT_VIP = 101,                   -- 农场VIP4能多种2个坑
	CONST_FARM_PLANT_VIPADD = 102,                -- 农场VIP4能多种2个坑
	CONST_FARM_PLANTNUM_LEVEL1 = 103,             -- 1级农场基本坑数
	CONST_FARM_PLANTNUM_LEVEL2 = 104,             -- 2级农场基本坑数
	CONST_FARM_PLANTNUM_LEVEL3 = 105,             -- 3级农场基本坑数
	CONST_FARM_PLANTNUM_LEVEL4 = 106,             -- 4级农场基本坑数
	CONST_MELEEGAME_ENTER_INTERVAL = 107,         -- 大乱斗进场间隔
	CONST_GAMESUGGEST_MAXLENGTH = 108,            -- 游戏建议最大字数
	CONST_GAMESUGGEST_MINLENGTH = 109,            -- 游戏建议至少字数
	CONST_LOTTERY_COSTDIAMONDLOGIN = 110,         --单次钻石登陆抽奖价格
	CONST_SUCCESSIONLOGIN_FIRST_LEVEL = 111,      --累计登陆抽奖次数达到200次
	CONST_SUCCESSIONLOGIN_SECOND_LEVEL = 112,     --累计登陆抽奖次数达到500次
	CONST_SUCCESSIONLOGIN_THREE_LEVEL = 113,      --累计登陆抽奖次数达到10000
	CONST_SELECT_COUNTRY_LEVEL_LIMIT = 114,       -- 玩家选择国家阵营等级
	CONST_PACKET_DEFAULT_SIZE = 115,              -- 背包格子默认数量
	CONST_WAREHORSE_DEFAULT_SIZE = 116,           -- 默认仓库格子数量
	CONST_PACKET_TIDY_TIME_INTERVAL = 117,        -- 背包刷新cd时间
	CONST_SOCIETY_SUCCEEDTRAIN = 118,             -- 继任帮主帮贡最低要求
	CONST_SOCIETY_PRIZEDISTRIBUTE = 119,          --帮会分配间隔小时
	CONST_SOCIETY_DAYCOSTFOUNDBASE = 120,         --帮会每天扣资金基数
	CONST_SOCIETY_DISTRIBUTECOSTFOUNDBASE = 121,  --帮会奖励分配扣资金基数
	CONST_SOCIETY_MSGCACHENUM = 122,              --帮会消息记录数量
	CONST_SOCIETY_MAXAPPLYNUM = 123,              --帮会申请记录数量
	CONST_SOCIETY_DISTRIBUTENUM = 124,            --帮会分配记录数量
	CONST_EQUIPSPACIALSKILL_CD1 = 125,            --装备特技CD1
	CONST_EQUIPSPACIALSKILL_CD2 = 126,            --装备特技CD2
	CONST_EQUIPSPACIALSKILL_COSTGOOD = 127,       --装备特技CD2内消耗物
	CONST_EQUIPSPACIALSKILL_DAYTIMES = 128,       --装备特技每天次数
	CONST_BORNMAP_ID = 129,                       --新手村地图ID
	CONST_REVIVE_COST_ITEM = 130,                 --复活消耗物品
	CONST_REVIVE_COST_MAXNUM = 131,               --复活消耗物品上限
	CONST_SOCIETY_MAXEVENTINFO = 132,             --帮会记录最大值
	CONST_WORLDLEVELUP_ADD = 133,                 --实际开服时间分钟+加数值A
	CONST_SOCIETY_ACTVCOLLECT_DURATION = 134,     --帮会采集活动开启持续时间(秒)
	CONST_ECTYPE_CRYSTALCAVE_ID = 135,            --欢乐水晶副本ID
	CONST_SOCIETY_CONTRIBUTION = 136,             --帮会捐献记录数量
	CONST_REDBAGITEM_DIAMOND = 137,               --红包道具抵元宝数量
	CONST_REDBAG_TIMES_LIMIT_WORLD = 138,         --世界红包领取次数限制
	CONST_REDBAG_TIMES_LIMIT_SOCIETY = 139,       --帮会红包领取次数限制
	CONST_REDBAG_SOCIETY_MIN_DIAMOND = 140,       --帮会红包最低发送元宝数量
	CONST_REDBAG_VALID_TIME = 141,                --红包有效时间
	CONST_ONTIME_VIPSCORE_PRIZE = 142,            --在线x分钟增加X点VIP经验
	CONST_ONTIME_VIPSCORE_EVERYDAY = 143,         --每日登陆增肌x点vip经验
	CONST_CONSUME_DIAMOND_VIP_SCORE = 144,        --消费1个元宝加x点vip经验
	CONST_CHARGE_DIAMOND_VIP_SCORE = 145,         --充值1个元宝加x点vip经验
	CONST_GEMCREATE_FREECOUNT = 146,              --猎魂免费次数
	CONST_SKILL_MAX = 150,                        --神印天赋最大值
	CONST_PLAYER_PROTECT_LEVEL = 151,             --新手保护等级
	CONST_BANK_UPSTAR = 152,                      --钱庄刷星消耗元宝数
	CONST_CHAT_LEVEL = 153,                       --聊天等级
	CONST_CHAT_VIPLEVEL = 154,                    --聊天vip等级
	CONST_CUSTOMIZETITLE_STAERID = 155,           --自定义称号开始ID
	CONST_CREATE_SOCIETY_DIAMOND = 156,           --创建帮会消耗元宝
	CONST_REDUCE_ALCOHOL = 157,                   --每分钟减少醉酒
	CONST_PLAYER_CHANGENAME_GOODSID = 158,        --改名卡物品ID
	CONST_TURNTABLE_FREE_TIMES = 159,             --转盘免费次数
	CONST_MAX_AURA = 160,                         --灵气上限
	CONST_NEWWING_PROPERTY_MAXLEVEL = 161,        --属性丹最高等级
	CONST_INHERITRETURN_GOODSID = 162,            --装备传承返还材料开始ID
	CONST_ALCHEMY_CHANGE_COST = 163,              --设置帮会丹药BUFF消耗的帮派资金
	CONST_ALCHEMY_FREESPEEDUP_MAXTIME = 164,      --最大炼丹普通帮助次数
	CONST_ALCHEMY_COSTSPEEDUP_MAXTIME = 165,      --最大炼丹元宝帮助次数
	CONST_ALCHEMY_CHANGE_MAXWEEKTIME = 166,       --炼丹BUFF转换一周最大次数
	CONST_PALACEBOSS_MAXTIRED = 167,              --地宫BOSS的最大疲劳值
	CONST_ALCHEMY_PERDAY_MAXTIME = 168,           --每日炼丹最大次数限制
	CONST_HMY_SOLDIER_INIT_SCORE = 169,           --鸿门宴副本士兵积分或货币的初始值
	CONST_EAST_CITY_JACKPOT_INIT_DIAMOND = 170,   --全民寻宝初始奖池元宝数量
	CONST_EAST_CITY_DRAW_MAX_COUNT = 171,         --全民寻宝全服抽奖次数限制
	CONST_EAST_CITY_JACKPOT_ADD_VALUE = 172,      --全民寻宝每抽一次奖增加的元宝
	CONST_EAST_CITY_DRAW_COUNT1 = 173,            --全民寻宝第N次抽奖数值阶段1
	CONST_EAST_CITY_DRAW_COUNT2 = 174,            --全民寻宝第N次抽奖数值阶段2
	CONST_EAST_CITY_DRAW_COUNT3 = 175,            --全民寻宝第N次抽奖数值阶段3
	CONST_EAST_CITY_DRAW_COUNT4 = 176,            --全民寻宝第N次抽奖数值阶段4
	CONST_EAST_CITY_PRIZE_COUNT1 = 177,           --全民寻宝全服第N次奖励次数1
	CONST_EAST_CITY_PRIZE_COUNT2 = 178,           --全民寻宝全服第N次奖励次数2
	CONST_EAST_CITY_PRIZE_COUNT3 = 179,           --全民寻宝全服第N次奖励次数3
	CONST_SOCIETY_RED_PACKET_MAX = 180,           --帮会红包每次活动能发的最大数量
	CONST_SOCIETY_RP_START_INTERVAL = 181,        --帮会红包活动开启后间隔几秒后发红包
	CONST_EAST_CITY_ONCE_DIAMOND = 182,           --全民寻宝单次抽奖花费元宝
	CONST_EAST_CITY_TEN_DIAMOND = 183,            --全民寻宝十次抽奖花费元宝
	CONST_LL_PAGODA_ONCE_DIAMOND = 184,           --玲珑宝塔单次抽奖花费元宝
	CONST_LL_PAGODA_TEN_DIAMOND = 185,            --玲珑宝塔十次抽奖花费元宝
	CONST_LL_PAGODA_FIFTY_DIAMOND = 186,          --玲珑宝塔五十次抽奖花费元宝
	CONST_SEVEN_COLOR_EGGS_DIAMOND = 187,         --七彩扭蛋需要充值或消费的元宝
	CONST_BATTLESTATUS_TIME = 188,                --战斗状态持续时间
	CONST_ANSWER_RIGHT_ADD_SCORE = 189,           --每一道题答对增加的积分
	CONST_ANSWER_RANK_ADD_SCORE_1 = 190,          --每一道题排名第一额外增加的积分
	CONST_ANSWER_RANK_ADD_SCORE_2 = 191,          --每一道题排名第二额外增加的积分
	CONST_ANSWER_RANK_ADD_SCORE_3 = 192,          --每一道题排名第三额外增加的积分
	CONST_ANSWER_RANK_ADD_SCORE_OTHER = 193,      --每一道题排名第三之后增加的积分
	CONST_ANSWER_PLAYER_OPEN_LEVEL = 194,         --答题活动玩家开放等级
	CONST_COMMONZONE_DEFAULTSCENE = 195,          --公共区默认场景
	CONST_PLAYER_CHANGE_SEX_GOODSID = 196,        --变性卡物品ID
	CONST_EAST_CITY_PRIZE_COUNT4 = 197,           --全民寻宝全服第N次奖励次数4
	CONST_EAST_CITY_PRIZE_COUNT5 = 198,           --全民寻宝全服第N次奖励次数5
	CONST_EAST_CITY_PRIZE_COUNT6 = 199,           --全民寻宝全服第N次奖励次数6
	CONST_EAST_CITY_PRIZE_COUNT7 = 200,           --全民寻宝全服第N次奖励次数7
	CONST_USE_EQUIPSKILL = 201,                   --使用装备技能
	CONST_SEVEN_COLOR_ONCE_DIAMOND = 202,         --七彩扭蛋单次抽奖花费元宝
	CONST_SEVEN_COLOR_TEN_DIAMOND = 203,          --七彩扭蛋十次抽奖花费元宝
	CONST_SEVEN_COLOR_HUNDRED_DIAMOND = 204,      --七彩扭蛋一百次抽奖花费元宝
	CONST_LUCKY_DRAW_ONCE_DIAMOND = 205,          --欢乐抽奖单次抽奖花费元宝
	CONST_LUCKY_DRAW_TEN_DIAMOND = 206,           --欢乐抽奖十次抽奖花费元宝
	CONST_SURVIVAL_CHALLENGE_DIAMOND = 207,       --生存挑战重置元宝			(金币副本重置需要钻石*重置次数)
	CONST_SURVIVAL_CHALLENGE_LEVEL = 208,         --生存挑战开启等级			(金币副本重置次数)
	CONST_SURVIVAL_CHALLENGE_FREE_TIMES = 209,    --生存挑战最大免费次数		(金币副本每天免费次数)
	CONST_SURVIVAL_CHALLENGE_ECTYPEID = 210,      --生存挑战副本ID			(生存副本每天挑战次数)
	CONST_USE_GENRE = 211,                        --是否使用流派				(生存副本重置消耗道具)
	CONST_GENRE_COOLINGTIME = 212,                --流派冷却时间
	CONST_FREE_USE_GENRE_LEVEL = 213,             --免费使用流派的等级
	CONST_CHANGEGENRE_BUFFID = 214,               --切换流派增加的BUFFID
	CONST_DIVINE_TREE_TIME_ONLINE = 215,          --神树献礼免费次数时间间隔（秒）
	CONST_DIVINE_TREE_TIMES_MAX = 216,            --神树献礼免费次数最大数值（最多256次）
	CONST_DIVINE_TREE_ONCE_DIAMOND = 217,         --神树献礼浇水一次消耗元宝
	CONST_DIVINE_TREE_TEN_DIAMOND = 218,          --神树献礼浇水十次消耗元宝
	CONST_COUNTRY_COUNT_D_VALUE = 219,            --两国阵营人数满足必定进入某国的差值
	CONST_LIMIT_STORE_BUY_FLAG = 220,             --限制商店部分商品的购买数量标志（0：不限制；1：限制）
	CONST_LIMIT_STORE_BUY_NUM = 221,              --限制商店部分商品的购买数量
	CONST_CREATE_SOCIETY_RECHARGE_FLAG = 222,     --创建帮会需要充值人民币的标志（0：不需要；1：需要）
	CONST_CREATE_SOCIETY_RECHARGE_VALUE = 223,    --创建帮会需要充值的人民币
	CONST_CREATE_SOCIETY_DIAMOND_VALUE = 224,     --创建帮会需要消耗的元宝
	CONST_CHAT_RECHARGE_FLAG = 225,               --聊天发言需要充值人民币的标志（0：不需要；1：需要）
	CONST_CHAT_RECHARGE_VALUE = 226,              --聊天发言需要充值的人民币（世界频道）
	CONST_CHAT_LEVEL_VALUE = 227,                 --聊天发言需要玩家等级（世界频道）
	CONST_JADEITE_MINE_DIAMOND_ONCE = 228,        --翡翠矿场一次采矿消耗元宝
	CONST_JADEITE_MINE_DIAMOND_UPDATE = 229,      --翡翠矿场刷新矿场消耗元宝
	CONST_JADEITE_MINE_FREE_MAX = 230,            --翡翠矿场免费最大次数
	CONST_JADEITE_MINE_INTERVAL = 231,            --翡翠矿场刷新时间间隔（秒）
	CONST_GODDESS_PRAY_ONCE_DIAMOND = 232,        --女神祈愿一次消耗元宝
	CONST_GODDESS_PRAY_TEN_DIAMOND = 233,         --女神祈愿十次消耗元宝
	CONST_GODDESS_PRAY_FREE_MAX = 234,            --女神祈愿免费最大次数
	CONST_GUESS_FINGER_NORMAL_DIAMOND = 235,      --猜拳消耗元宝
	CONST_GUESS_FINGER_WIN_DIAMOND = 236,         --猜拳必胜元宝
	CONST_GUESS_FINGER_FREE_MAX = 237,            --猜拳免费最大次数
	CONST_NEWBIEECTYPE_ID = 238,                  --新手副本ID
	CONST_MRAAY_RANK_DISCOUNT = 239,              --结婚排名活动礼包打折率（1~10）
	CONST_MRAAY_RANK_DAY_NUM = 240,               --结婚排名活动每日名额值
	CONST_EMPEROR_TREASURE_COST_DIAMOND = 241,    --天帝鉴宝消耗元宝
	CONST_EMPEROR_TREASURE_CHANGE_DIAMOND = 242,  --天帝换宝消耗元宝
	CONST_EMPEROR_TREASURE_TIMES_1 = 243,         --天帝鉴宝倍数一（全不同）
	CONST_EMPEROR_TREASURE_TIMES_2 = 244,         --天帝鉴宝倍数二（两相同）
	CONST_EMPEROR_TREASURE_TIMES_3 = 245,         --天帝鉴宝倍数三（全相同）
	CONST_EMPEROR_TREASURE_TIME_ONLINE = 246,     --天帝鉴宝免费次数时间间隔（秒）
	CONST_EMPEROR_TREASURE_TIMES_MAX = 247,       --天帝鉴宝免费次数最大数值（最多255次）
	CONST_MARKET_TAX = 248,                       --寄售税收比例（0-100）
	CONST_WARBAND_CREATELEVEL = 249,              --创建战队需要等级
	CONST_WARBAND_JOINLEVEL = 250,                --加入战队需要等级
	CONST_WARBAND_CREATECOST = 251,               --创建战队需要银两
	CONST_MARKET_RECHARGELIMIT = 252,             --购买寄售所需充值额度
	CONST_WARBAND_RECHARGE_MESSAGE = 253,         --战队充值记录日志数量
	CONST_WARBAND_RECHARGEPRIZE_EACH = 254,       --战队每累充多少能领奖
	CONST_WARBAND_PERSONALRECHARGEPRIZE_MAX = 255, --战队个人累充最多能领奖次数
	CONST_LUCKYGIRL_RECHARGE_TEN = 256,           --幸运女神当天累充10元，免费抽奖次数增加5次
	CONST_LUCKYGIRL_RECHARGE_FIFTY = 257,         --幸运女神当天累充50元，免费抽奖次数增加20次
	CONST_LUCKYGIRL_RECHARGE_HUNDRED = 258,       --幸运女神当天累充100元，免费抽奖次数增加50次
	CONST_RECHARGE_DAYFIRSTGIFTRATE = 259,        --填每日超值礼包额外给元宝比例(10000=1)
	CONST_DIAMOND_COST_VIPSCORE_RATE = 260,       -- 消耗元宝增加vip积分比例
	CONST_MARKET_SINGLEGOODSID_LIMIT = 261,       -- 寄售同一人同种物品最多购买次数
	CONST_MARKET_ACTORLEVEL_LIMIT = 262,          -- 寄售细胞等级限制
	CONST_MARKET_SELL_WARBAND_ACTV_LIMIT = 263,   -- 寄售卖出战队累计活跃限制
	CONST_MARKET_BUY_WARBAND_ACTV_LIMIT = 264,    -- 购买寄售所需战队累计活跃
	CONST_LOOPALLFINISH_LEVEL_LIMIT = 265,        -- 每日必做跑环一键完成的等级限制
	CONST_VIP_TARGET_LEVEL = 266,                 -- VIP存储池目标等级
	CONST_ROBOT_AIMPLAYERACTIVE = 267,            -- 是否用陪玩机器人
	CONST_ROBOT_BALANCEACTIVE = 268,              -- 是否用平衡机器人
	CONST_ROBOT_REPLYFLOWERSTHANKS_LEVEL = 269,   -- 机器人回复送花100%回复感谢等级
	CONST_ROBOT_REPLYFLOWERSTHANKS_RATE = 270,    -- 机器人回复送花回复感谢概率
	CONST_ROBOT_LOGINSENDFLOWERS_LEVEL = 271,     -- 玩家登录多少秒机器人送花等级
	CONST_FIRSTRECHARGE_PRIZEID = 272,            -- 首冲奖励ID
	CONST_PVE_BASECHALLENGE_COUNT = 273,          -- 训练场每日基础次数
	CONST_PVE_CANBUYTIMES = 274,                  -- 训练场每日最多购买次数
	CONST_PVE_COOLCOST = 275,                     -- 训练场消除冷却时间花费
	CONST_SOCIETY_ECTYPETOWER_HLEPMAXCOUNT = 276, -- 帮会副本塔协助最大次数
	CONST_LUCKYFRUITS_PRIZEID = 277,              -- 幸运水果奖励ID
	CONST_LUCKYFRUITS_DRAW_COSTMONEY = 278,       -- 幸运水果抽奖银两消耗
	CONST_LUCKYFRUITS_DRAW_COSTDIAMOND = 279,     -- 幸运水果抽奖元宝消耗
	CONST_LUCKYFRUITS_DRAW_TEN_RATE = 280,        -- 幸运水果抽奖十次折扣
	CONST_LUCKYFRUITS_DRAW_HUNDRED_RATE = 281,    -- 幸运水果抽奖百次折扣
	CONST_LUCKYFRUITS_DRAW_MONEY_MAXCOUNT = 282,  -- 幸运水果银两抽奖最大次数
	CONST_BUY_EXPLOITMINECOST = 283,              -- 增加刷矿次数需要元宝
	CONST_BUY_PLUNDERMINECOST = 284,              --增加挑战次数需要元宝
	CONST_REFRESH_MINELEVELCOST = 285,            --刷星需要元宝
	CONST_MAX_EXPLOITMINE_COUNT_DAY = 286,        --每人每天可开采矿脉数
	CONST_MAX_OCCUPY_MINE_COUNT_DAY = 287,        --每人每天可占领矿脉个数
	CONST_MAX_FREEREFRESH_MINE_COUNT_DAY = 288,   --每人每天可免费刷矿次数
	CONST_MAX_FREECHANLLENGEMINE_COUNT_DAY = 289, --每天可免费挑战次数
	CONST_MAX_ADDCHANLLENGEMINE_COUNT_DAY = 290,  --每人每天可增加挑战次数：5
	CONST_MAX_BECHANLLENGEMINE_COUNT_DAY = 291,   --每个矿可被挑战次数：3
	CONST_CHANLLENGE_CLEARTIME_COST = 292,        --消除冷却1分钟冷却需要元宝：1
	CONST_MAX_CHANLLENGE_ADDCOORTIME = 293,       --挑战1次增加冷却时间：10分钟
	CONST_MAX_TAKEBACK_ADDCOORTIME = 294,         --抢回一次增加冷却时长：22分
	CONST_BATTLEFIELD_SCENEID = 295,              --中立战场BOSS出生场景ID
	CONST_DICE_ROLLER_FREE_TIMES = 296,           --摇骰子免费次数
	CONST_DICE_ROLLER_BUY_TIMES = 297,            --摇骰子购买次数
	CONST_DICE_ROLLER_NORMAL_PRIZEID = 298,       --摇骰子正常奖励ID
	CONST_DICE_ROLLER_SPECIAL_PRIZEID = 299,      --摇骰子特殊奖励ID
	CONST_WARBANDECTYPE_OPEN_COOLDOWN = 300,      -- 战队副本开启增加冷却时间
	CONST_WARBANDECTYPE_CLEAN_COOLDOWN_COST = 301, -- 战队副本清除冷却时间消耗元宝
	CONST_WARBANDECTYPE_MAX_COOLDOWN = 302,       -- 战队副本最大冷却时间
	CONST_NEWWILDBATTLE_CANBUYTIMES = 303,        -- 楚汉战场每日能购买次数
	CONST_MARKET_OPENDAY_LIMIT = 304,             -- 寄售开服天数限制
	CONST_SMELT_FORGE_RATE = 305,                 -- 强化升级倍数1
	CONST_SMELT_FORGE_RATE2 = 306,                -- 强化升级倍数2
	CONST_RECHARGESTORE_ACTIVELIMIT = 307,        -- 自选商城真实充值限制
	CONST_SUBSIST_BASECHALLENGE_COUNT = 308,      -- 生存爬塔每日基础次数
	CONST_SUBSIST_CANBUYTIMES = 309,              -- 生存爬塔每日能购买次数
	CONST_SUBSIST_COOLCOST = 310,                 -- 生存爬塔消除冷却时间花费
	CONST_CREATE_JOIN_SOCIETY_COOLTIME = 311,     -- 创建帮会或加入帮会冷却时间
	CONST_CONFIDANT_ACTIVITY_OPENFLAG = 312,      -- 同心知己是否开启(1开启0不开)
	CONST_CREATE_FAKESOCIETY_CONDITION = 313,     -- 创建伪帮会条件(国家需要有几个真实帮会才创建)
	CONST_CREATE_FAKESOCIETY_COUNT = 314,         -- 创建伪帮会数量(-1表示不创建、0照常、大于0具体数量)
	CONST_RECHARGESTORE_OPENDAYLIMIT = 315,       -- 自选商城开服天数限制
	CONST_SEVEN_ELEMENTS_DRAW_MONEY_LIMIT = 316,  -- 元素祭坛铜币银两抽奖次数上限
	CONST_SEVEN_ELEMENTS_DRAW_DIAMOND_LIMIT = 317, -- 元素祭坛抽奖元宝抽奖次数上限
	CONST_SEVEN_ELEMENTS_DRAW_COSTMONEY = 318,    -- 元素祭坛抽奖银两消耗
	CONST_SEVEN_ELEMENTS_DRAW_ONE_COSTDIAMOND = 319, -- 元素祭坛单次抽奖元宝消耗
	CONST_SEVEN_ELEMENTS_DRAW_TEN_COSTDIAMOND = 320, -- 元素祭坛十次抽奖元宝消耗
	CONST_SOCIETY_NEWSERVERDAYCOUNT = 321,        -- 爆破猫技能每次恢复时间
	CONST_SOCIETY_NEWSERVERLEAVELIMIT = 322,      -- 爆破猫技能能量恢复值速度
	CONST_ACTIVITYNEWSERVER_RANK_FLAG = 323,      -- 爆破猫技能能量恢复上限百分比开始射击
	CONST_LUCKY_HAPPENED_FREE_PROB = 324,         -- 幸运降临免费概率
	CONST_LUCKY_GIRL_FREE_PROB = 325,             -- 幸运女神免费概率
	CONST_ENERGY_ADDINTERVAL = 326,               -- 加体力间隔时间
	CONST_ADVERTISE_PROVIDEPRIZE = 327,           -- 每天看完广告多给这个奖励ID一次
	CONST_ADVERTISE_LOCKBOX = 328,                -- 每天有2次机会看广告立即解锁宝箱
	CONST_ADVERTISE_UPGRADECARD = 329,            -- 每天每张卡有5次看视频广告升级
	CONST_ADVERTISE_SEGMENTSTAR = 330,            -- 看广告段位赛升级
	CONST_DIAMOND_SEGMENTSTAR = 331,              -- 消耗元宝段位赛升级
	CONST_MONSTER_CHECKINTERVAL = 332,            -- 怪物状态检测时间间隔
	CONST_TOWERBATTLE_ATTACKINTERVAL = 333,       -- 塔防攻击时间间隔
	CONST_ADVERTISE_ADDCARDLEVEL = 334,           -- 卡牌看广告加等级
	CONST_STORE_ADVERTISEREFRESHCOUNT = 335,      -- 商店广告最大刷新次数
	CONST_BRUSTCAT_CHALLENGECOUNT = 336,          -- 挑战次数
	CONST_BRUSTCAT_TOTALTIME = 337,               -- 挑战时间

	CONST_VALUE_MAXID,                            -- 最大常数ID
}




VIPID =
{
	VIPID_VALUE_NULL = 0,
	VIPID_LEVEL_EXP = 1,                    -- VIP等级对应经验
	VIPID_EVERYDAY_PRIZE = 2,               -- VIP每日礼包奖励ID
	VIPID_LEVEL_PRIZE = 3,                  -- VIP等级礼包奖励ID
	VIPID_MIDAS_TOUCH_TIMES = 4,            -- 玩家使用点金手次数
	VIPID_REFRESH_MONEYSTORE = 5,           -- 银两商店刷新次数
	VIPID_REFRESH_PRESTIGESTORE = 6,        -- 声望商店刷新次数
	VIPID_REFRESH_CLANSTORE = 7,            -- 帮会商店刷新次数
	VIPID_REFRESH_SECRETSTORE1 = 8,         -- 神秘商店1刷新次数
	VIPID_REFRESH_SECRETSTORE2 = 9,         -- 神秘商店2刷新次数
	VIPID_REFRESH_SECRETSTORE3 = 10,        -- 神秘商店3刷新次数
	VIPID_BOSS_SUMMON_TIMES = 11,           -- 召唤世界Boss次数
	VIPID_BRANCH_CANBRIBE = 12,             -- 日常任务可钻石完成
	VIPID_BRANCH_FREEREFRESH = 13,          -- 日常任务免费刷新次数
	VIPID_BRANCH_CANBUYROUND = 14,          -- 日常任务可购买的轮次
	VIPID_ARENAGAME_BUYTIMES = 15,          -- 竞技场购买轮数
	VIPID_BANK_EXCHANGE_TIME = 16,          -- 无用
	VIPID_SOCIETY_BUYREVIVE = 17,           -- 帮会复活购买次数
	VIPID_PRACTICE_BUYADDMORAL = 18,        -- 帮会修炼购买次数
	VIPID_ONLINE_BROAD_CAST = 19,           -- 上线全服公告
	VIPID_ESCORT_QUALITY = 20,              -- 押镖刷新初始品质-蓝色粮草车
	VIPID_LOOP_TASK_STAR = 21,              -- 每日跑环任务永久3星以上
	VIPID_STORE_DISCOUNT = 22,              -- 商城物品打折
	VIPID_FREE_OPEN_STORE = 23,             -- 远程药店
	VIPID_SOCIETY_DISTRIBUTE = 24,          -- 帮会贡献捐献
	VIPID_UP_STAR = 25,                     -- 装备升级成功率
	VIPID_ECTYPE_RESET = 26,                -- 单人副本重置次数
	VIPID_FREE_FLY = 27,                    -- 免费飞行
	VIPID_HUNT_SOUL = 28,                   -- 免费猎魂次数
	VIPID_EQUIP_DOWNGRADE = 29,             -- 装备强化失败不会降级
	VIPID_STORE = 30,                       -- VIP商城
	VIPID_FREE_POLISH_1 = 31,               -- 免费突变次数
	VIPID_FREE_POLISH_2 = 32,               -- 免费精炼次数
	VIPID_GODWEAPON_UPSTAR = 33,            -- 体魄升级成功率
	VIPID_SOCIETY_AUTOLOOP = 34,            -- 帮会跑环自动任务(商店)
	VIPID_MARKET_BINDRATE = 35,             -- 寄售绑定元宝概率
	VIPID_SOCIETY_ECTYPETOWER_RESETTIMES = 36, -- 帮会副本塔可重置次数
	VIPID_ECTYPETOWER_RESETTIMES = 37,      -- 副本塔可重置次数
	VIPID_TURNTABLES_BUYTIMES = 38,         -- 全民转盘购买次数
	VIPID_ENERGY_CANBUYTIMES = 39,          -- 每天可购买体力次数
	VIPID_MATERIALECTYPE_RESETTIMES = 40,   -- 重置材料副本次数
	VIPID_ADD_EFFECTID = 41,                -- 额外效果ID
	VIPID_SKEPSIZEMAX = 42,                 -- 背包全开
	VIPID_TASKMULTIPRIZE = 43,              -- 跑环奖励多倍
	VIPID_WARESIZEMAX = 44,                 -- 仓库全开
	VIPID_EXP_ADDITION = 45,                -- 经验加成
	VIPID_MONEY_ADDITION = 46,              -- 银两加成
	VIPID_EVERYDAY_EXTRA_EXP = 47,          -- 每日登录额外经验
	VIPID_EVERYDAY_EXTRA_MONEY = 48,        -- 每日登录额外银两
	VIPID_EVERYDAY_GIFT_VALUE = 49,         -- VIP每日礼包奖励价值元宝
	VIPID_BOSS_ECTYPETOWER_RESETTIMES = 50, -- BOSS爬塔可重置次数
	VIPID_ACTIVITY_COUNTLIMIT1 = 51,        -- VIP等级抽奖次数限制
	VIPID_ACTIVITY_COUNTLIMIT2 = 52,        -- VIP等级抽奖次数限制
	VIPID_ACTIVITY_COUNTLIMIT3 = 53,        -- VIP等级抽奖次数限制
	VIPID_MARKET_EVERYDAY_BUYNUM_LIMIT = 54, -- 寄售每日购买金额限制
	VIPID_LEVEL_PRIZE_COST = 55,            -- VIP等级礼包奖励消耗元宝
	VIPID_VALUE_MAXID,
}

SDK_SUBMIT_ROLEDATA_EVENTTYPE = {
	SELECT_SERVER = 1,
	CREATE_ROLE = 2,
	ENTER_GAME = 3,
	LEVEL_UP = 4,
	EXIT_GAME = 5,
}

RECHARGE_TYPE = {
	RECHARGE_CARD = 1, --充值卡
	FIRST_GIFT = 2,    --超值礼包、次充
	MONTH_CARD = 3,    --月卡
	EVERY_DAY_CARD = 4, --每日特惠
	EVERY_WEEK_CARD = 5, --每周特惠
	EVERY_MONTH_CARD = 6, --每月特惠
	INVESTMENT = 7,    --投资
	REMOVE_AD = 8,     --清除广告
	FUNDATION = 9,     --基金
	EQUIP_SLOT = 10,   --槽位
	LEVEL_GIFT = 11,   --关卡超值礼包
}
UMENG_EVENT_TYPE = {
	USER_REGISTER = 1,      --注册
	CUR_STAGE = 2,          --当前最高关卡
	BATTLE_COUNT = 3,       --挑战次数
	BATTLE_RESULT_SUCCESS = 4, --挑战结果成功
	BATTLE_RESULT_FAILD = 5, --挑战结果失败
}
TASK_STATUS =
{
	TASK_STATUS_NULL = 0,   -- 无效任务
	TASK_STATUS_BRANCH = 1, -- 日常任务，生命周期内没接受过
	TASK_STATUS_ROUND = 2,  -- 生命周期内有接受过，但因为是多步或多次的任务需要保存，任务状态重置为没接受
	TASK_STATUS_ACCEPTED = 3, -- 接受状态
	TASK_STATUS_COMPLETED = 4, -- 完成状态
	TASK_STATUS_MAXID = 5,
}
TaskType =
{
	TaskType_Null = 0,
	TaskType_KillTargetMonster = 1,
	TaskType_DramaEctype = 2,
	TaskType_DailyEctype = 3,
	TaskType_JoinActivity = 4,
	TaskType_KillLevelMonster = 5,
	TaskType_Talk = 6,
	TaskType_TimeTalk = 7,
	TaskType_TimePass = 8,
	TaskType_ActorLevel = 9,
	TaskType_FriendShip = 10,
	TaskType_KillPlayer = 11,
	TaskType_Escort = 12,
	TaskType_TalkEctype = 13,
	TaskType_CollectGood = 14,
	TaskType_TalkThenTransmit = 15,
	TaskType_UpGrade = 16,
	TaskType_DoDailyTask = 17,
	TaskType_OperateCollectGood = 18,
	TaskType_EveryDayUpgrade = 19,
	TaskType_EnterScene = 20,
	TaskType_NewbieTarget = 21,
	TaskType_LoginDay = 22,
	TaskType_BuyRechargeCard = 23,
	TaskType_ActorPower = 24,
	TaskType_LieMingAndQiFuCount = 25,
	TaskType_FuBenId = 26,
}

--奖品内容类型
PrizeContentType = {
	--解析奖励ID
	ID = 1,
	--解析奖励字符串1 (物品ID+物品数量|物品ID+物品数量)
	STRING = 2,
	--解析奖励字符串2 (物品IDx物品数量|物品IDx物品数量) --开宝箱十连抽
	STRING2 = 3,
	--解析奖励字符串3 (物品ID;物品数量|物品ID;物品数量)
	STRING3 = 4,
}
--属性等级
ActorPropLevelType = {
	Health = 1,      --生命
	Attack = 2,      --攻击
	Critical = 3,    --暴击
	CriticalDamage = 4, --暴击伤害
	AttackSpeed = 5, --攻击速度
}

--细胞属性装备ID和解锁条件
---@type { equipID:integer, unlockConditions:integer, }[]
EquipConfigIdList = {
	[ActorPropLevelType.Health] = { equipID = 600001, unlockConditions = 0, },
	[ActorPropLevelType.Attack] = { equipID = 600002, unlockConditions = 4, },
	[ActorPropLevelType.Critical] = { equipID = 600003, unlockConditions = 1000, },
	[ActorPropLevelType.CriticalDamage] = { equipID = 600004, unlockConditions = 100, },
	[ActorPropLevelType.AttackSpeed] = { equipID = 600005, unlockConditions = 100, },
}
