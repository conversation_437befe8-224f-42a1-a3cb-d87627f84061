-- Generated By protoc-gen-lua Do not Edit
local protobuf = require "protobuf/protobuf"
module('StoreMessage_pb')
local pb = {}


pb.MSG_STORE_ACTIONID = protobuf.EnumDescriptor();
pb.MSG_STORE_ACTIONID_MSG_STORE_NONE_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_STORE_ACTIONID_MSG_STORE_GETLIST_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_STORE_ACTIONID_MSG_STORE_BUYITEM_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_STORE_ACTIONID_MSG_STORE_REFRESH_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_STORE_ACTIONID_MSG_STORE_MARKET_GETLIST_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_STORE_ACTIONID_MSG_STORE_BOOTH_GETBOOTH_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_STORE_ACTIONID_MSG_STORE_MARKET_GETSELF_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_STORE_ACTIONID_MSG_STORE_GET_ITEM_PRICE_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_STORE_ACTIONID_MSG_STORE_MARKET_ADDSELLITEM_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_STORE_ACTIONID_MSG_STORE_MARKET_REMOVESELLITEM_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_STORE_ACTIONID_MSG_STORE_MARKET_BUYSELLITEM_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_STORE_ACTIONID_MSG_STORE_RECHARGE_REQUESTGOODS_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_STORE_ACTIONID_MSG_STORE_RECHARGE_GETDATA_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_STORE_ACTIONID_MSG_STORE_MARKET_TRADERECORD_ENUM = protobuf.EnumValueDescriptor();
pb.CS_STORE_GETITEMLIST = protobuf.Descriptor();
pb.CS_STORE_GETITEMLIST_STOREID_FIELD = protobuf.FieldDescriptor();
pb.SC_STORE_GETITEMLIST = protobuf.Descriptor();
pb.SC_STORE_GETITEMLIST_ITEMDATA = protobuf.Descriptor();
pb.SC_STORE_GETITEMLIST_ITEMDATA_ITEMID_FIELD = protobuf.FieldDescriptor();
pb.SC_STORE_GETITEMLIST_ITEMDATA_GOODSID_FIELD = protobuf.FieldDescriptor();
pb.SC_STORE_GETITEMLIST_ITEMDATA_GOODSNUM_FIELD = protobuf.FieldDescriptor();
pb.SC_STORE_GETITEMLIST_ITEMDATA_COSTTYPE_FIELD = protobuf.FieldDescriptor();
pb.SC_STORE_GETITEMLIST_ITEMDATA_COSTNUM_FIELD = protobuf.FieldDescriptor();
pb.SC_STORE_GETITEMLIST_ITEMDATA_SOLDOUT_FIELD = protobuf.FieldDescriptor();
pb.SC_STORE_GETITEMLIST_ITEMDATA_DISCOUNT_FIELD = protobuf.FieldDescriptor();
pb.SC_STORE_GETITEMLIST_ITEMDATA_BINDFLAG_FIELD = protobuf.FieldDescriptor();
pb.SC_STORE_GETITEMLIST_ITEMDATA_VIP_FIELD = protobuf.FieldDescriptor();
pb.SC_STORE_GETITEMLIST_ITEMDATA_PLAYERLEVEL_FIELD = protobuf.FieldDescriptor();
pb.SC_STORE_GETITEMLIST_ITEMDATA_SOCIETYLEVEL_FIELD = protobuf.FieldDescriptor();
pb.SC_STORE_GETITEMLIST_ITEMDATA_NEEDRECHARGE_FIELD = protobuf.FieldDescriptor();
pb.SC_STORE_GETITEMLIST_ITEMDATA_LIMITACTORDAY_FIELD = protobuf.FieldDescriptor();
pb.SC_STORE_GETITEMLIST_ITEMDATA_NUMSLIMITTYPE_FIELD = protobuf.FieldDescriptor();
pb.SC_STORE_GETITEMLIST_ITEMDATA_NUMSLIMITDAY_FIELD = protobuf.FieldDescriptor();
pb.SC_STORE_GETITEMLIST_ITEMDATA_NUMSISBUY_FIELD = protobuf.FieldDescriptor();
pb.SC_STORE_GETITEMLIST_ITEMDATA_NUMSGROUPISBUY_FIELD = protobuf.FieldDescriptor();
pb.SC_STORE_GETITEMLIST_STOREID_FIELD = protobuf.FieldDescriptor();
pb.SC_STORE_GETITEMLIST_ITEMLIST_FIELD = protobuf.FieldDescriptor();
pb.SC_STORE_GETITEMLIST_REFRESHID_FIELD = protobuf.FieldDescriptor();
pb.SC_STORE_GETITEMLIST_REFRESHTIME_FIELD = protobuf.FieldDescriptor();
pb.SC_STORE_GETITEMLIST_REFRESHNUM_FIELD = protobuf.FieldDescriptor();
pb.SC_STORE_GETITEMLIST_REFRESHFREECOUNT_FIELD = protobuf.FieldDescriptor();
pb.CS_STORE_BUYITEM = protobuf.Descriptor();
pb.CS_STORE_BUYITEM_STOREID_FIELD = protobuf.FieldDescriptor();
pb.CS_STORE_BUYITEM_ITEMID_FIELD = protobuf.FieldDescriptor();
pb.CS_STORE_BUYITEM_ITEMNUM_FIELD = protobuf.FieldDescriptor();
pb.SC_STORE_BUYITEM = protobuf.Descriptor();
pb.SC_STORE_BUYITEM_RESULT_FIELD = protobuf.FieldDescriptor();
pb.SC_STORE_BUYITEM_STOREID_FIELD = protobuf.FieldDescriptor();
pb.SC_STORE_BUYITEM_ITEMID_FIELD = protobuf.FieldDescriptor();
pb.SC_STORE_BUYITEM_SOLDOUT_FIELD = protobuf.FieldDescriptor();
pb.SC_STORE_BUYITEM_PARAM_FIELD = protobuf.FieldDescriptor();
pb.SC_STORE_BUYITEM_NUMSISBUY_FIELD = protobuf.FieldDescriptor();
pb.SC_STORE_BUYITEM_NUMSGROUPISBUY_FIELD = protobuf.FieldDescriptor();
pb.CS_STORE_REFRESH = protobuf.Descriptor();
pb.CS_STORE_REFRESH_STOREID_FIELD = protobuf.FieldDescriptor();
pb.CS_STORE_REFRESH_ADVERTISEREFRESH_FIELD = protobuf.FieldDescriptor();
pb.SC_STORE_REFRESH = protobuf.Descriptor();
pb.SC_STORE_REFRESH_RESULT_FIELD = protobuf.FieldDescriptor();
pb.CS_MARKET_GETLIST = protobuf.Descriptor();
pb.CS_MARKET_GETLIST_PAGEVALUE_FIELD = protobuf.FieldDescriptor();
pb.CS_MARKET_GETLIST_PAGESIZE_FIELD = protobuf.FieldDescriptor();
pb.CS_MARKET_GETLIST_MAINTYPE_FIELD = protobuf.FieldDescriptor();
pb.CS_MARKET_GETLIST_SUBTYPE_FIELD = protobuf.FieldDescriptor();
pb.CS_MARKET_GETLIST_SEARCHGOODSID_FIELD = protobuf.FieldDescriptor();
pb.SC_MARKET_GETLIST = protobuf.Descriptor();
pb.SC_MARKET_GETLIST_ITEMDATA = protobuf.Descriptor();
pb.SC_MARKET_GETLIST_ITEMDATA_SELLID_FIELD = protobuf.FieldDescriptor();
pb.SC_MARKET_GETLIST_ITEMDATA_PRICE_FIELD = protobuf.FieldDescriptor();
pb.SC_MARKET_GETLIST_ITEMDATA_NUMPROP_FIELD = protobuf.FieldDescriptor();
pb.SC_MARKET_GETLIST_ITEMDATA_ENDTIME_FIELD = protobuf.FieldDescriptor();
pb.SC_MARKET_GETLIST_PAGEVALUE_FIELD = protobuf.FieldDescriptor();
pb.SC_MARKET_GETLIST_TOTALITEMCOUNT_FIELD = protobuf.FieldDescriptor();
pb.SC_MARKET_GETLIST_MAINTYPE_FIELD = protobuf.FieldDescriptor();
pb.SC_MARKET_GETLIST_SUBTYPE_FIELD = protobuf.FieldDescriptor();
pb.SC_MARKET_GETLIST_SEARCHGOODSID_FIELD = protobuf.FieldDescriptor();
pb.SC_MARKET_GETLIST_ITEMLIST_FIELD = protobuf.FieldDescriptor();
pb.CS_MARKET_GETBOOTH = protobuf.Descriptor();
pb.CS_MARKET_GETBOOTH_ACTORID_FIELD = protobuf.FieldDescriptor();
pb.SC_MARKET_BOOTH = protobuf.Descriptor();
pb.SC_MARKET_BOOTH_ITEMDATA = protobuf.Descriptor();
pb.SC_MARKET_BOOTH_ITEMDATA_SELLID_FIELD = protobuf.FieldDescriptor();
pb.SC_MARKET_BOOTH_ITEMDATA_GOODSID_FIELD = protobuf.FieldDescriptor();
pb.SC_MARKET_BOOTH_ITEMDATA_GOODSNUM_FIELD = protobuf.FieldDescriptor();
pb.SC_MARKET_BOOTH_ITEMDATA_PRICE_FIELD = protobuf.FieldDescriptor();
pb.SC_MARKET_BOOTH_ITEMDATA_NUMPROP_FIELD = protobuf.FieldDescriptor();
pb.SC_MARKET_BOOTH_ACTORID_FIELD = protobuf.FieldDescriptor();
pb.SC_MARKET_BOOTH_BOOTHNAME_FIELD = protobuf.FieldDescriptor();
pb.SC_MARKET_BOOTH_ITEMLIST_FIELD = protobuf.FieldDescriptor();
pb.CS_MARKET_GETSELFLIST = protobuf.Descriptor();
pb.SC_MARKET_GETSELFLIST = protobuf.Descriptor();
pb.SC_MARKET_GETSELFLIST_ITEMDATA = protobuf.Descriptor();
pb.SC_MARKET_GETSELFLIST_ITEMDATA_SELLID_FIELD = protobuf.FieldDescriptor();
pb.SC_MARKET_GETSELFLIST_ITEMDATA_PRICE_FIELD = protobuf.FieldDescriptor();
pb.SC_MARKET_GETSELFLIST_ITEMDATA_ENDTIME_FIELD = protobuf.FieldDescriptor();
pb.SC_MARKET_GETSELFLIST_ITEMDATA_NUMPROP_FIELD = protobuf.FieldDescriptor();
pb.SC_MARKET_GETSELFLIST_ITEMLIST_FIELD = protobuf.FieldDescriptor();
pb.CS_STORE_GETITEMPRICE = protobuf.Descriptor();
pb.CS_STORE_GETITEMPRICE_STOREID_FIELD = protobuf.FieldDescriptor();
pb.CS_STORE_GETITEMPRICE_ITEMID_FIELD = protobuf.FieldDescriptor();
pb.SC_STORE_GETITEMPRICE = protobuf.Descriptor();
pb.SC_STORE_GETITEMPRICE_STOREID_FIELD = protobuf.FieldDescriptor();
pb.SC_STORE_GETITEMPRICE_ITEMID_FIELD = protobuf.FieldDescriptor();
pb.SC_STORE_GETITEMPRICE_GOODSID_FIELD = protobuf.FieldDescriptor();
pb.SC_STORE_GETITEMPRICE_GOODSNUM_FIELD = protobuf.FieldDescriptor();
pb.SC_STORE_GETITEMPRICE_COSTTYPE_FIELD = protobuf.FieldDescriptor();
pb.SC_STORE_GETITEMPRICE_COSTNUM_FIELD = protobuf.FieldDescriptor();
pb.CS_STORE_MARKET_ADDSELLITEM = protobuf.Descriptor();
pb.CS_STORE_MARKET_ADDSELLITEM_GOODSUID_FIELD = protobuf.FieldDescriptor();
pb.CS_STORE_MARKET_ADDSELLITEM_GOODSNUM_FIELD = protobuf.FieldDescriptor();
pb.CS_STORE_MARKET_ADDSELLITEM_PRICE_FIELD = protobuf.FieldDescriptor();
pb.SC_STORE_MARKET_ADDSELLITEM = protobuf.Descriptor();
pb.SC_STORE_MARKET_ADDSELLITEM_RESULT_FIELD = protobuf.FieldDescriptor();
pb.CS_STORE_MARKET_REMOVESELLITEM = protobuf.Descriptor();
pb.CS_STORE_MARKET_REMOVESELLITEM_SELLID_FIELD = protobuf.FieldDescriptor();
pb.CS_STORE_MARKET_REMOVESELLITEM_GOODSNUM_FIELD = protobuf.FieldDescriptor();
pb.SC_STORE_MARKET_REMOVESELLITEM = protobuf.Descriptor();
pb.SC_STORE_MARKET_REMOVESELLITEM_RESULT_FIELD = protobuf.FieldDescriptor();
pb.CS_STORE_MARKET_BUYSELLITEM = protobuf.Descriptor();
pb.CS_STORE_MARKET_BUYSELLITEM_SELLID_FIELD = protobuf.FieldDescriptor();
pb.CS_STORE_MARKET_BUYSELLITEM_GOODSNUM_FIELD = protobuf.FieldDescriptor();
pb.SC_STORE_MARKET_BUYSELLITEM = protobuf.Descriptor();
pb.SC_STORE_MARKET_BUYSELLITEM_RESULT_FIELD = protobuf.FieldDescriptor();
pb.CS_STORE_RECHARGE_REQUESTGOODS = protobuf.Descriptor();
pb.CS_STORE_RECHARGE_REQUESTGOODS_REQUESTTYPE_FIELD = protobuf.FieldDescriptor();
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM1_FIELD = protobuf.FieldDescriptor();
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM2_FIELD = protobuf.FieldDescriptor();
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM3_FIELD = protobuf.FieldDescriptor();
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM4_FIELD = protobuf.FieldDescriptor();
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM5_FIELD = protobuf.FieldDescriptor();
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM6_FIELD = protobuf.FieldDescriptor();
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM7_FIELD = protobuf.FieldDescriptor();
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM8_FIELD = protobuf.FieldDescriptor();
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM9_FIELD = protobuf.FieldDescriptor();
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM10_FIELD = protobuf.FieldDescriptor();
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM11_FIELD = protobuf.FieldDescriptor();
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM12_FIELD = protobuf.FieldDescriptor();
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM13_FIELD = protobuf.FieldDescriptor();
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM14_FIELD = protobuf.FieldDescriptor();
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM15_FIELD = protobuf.FieldDescriptor();
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM16_FIELD = protobuf.FieldDescriptor();
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM17_FIELD = protobuf.FieldDescriptor();
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM18_FIELD = protobuf.FieldDescriptor();
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM19_FIELD = protobuf.FieldDescriptor();
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM20_FIELD = protobuf.FieldDescriptor();
pb.CS_STORE_RECHARGE_REQUESTGOODS_NEEDRECHARGE_FIELD = protobuf.FieldDescriptor();
pb.CS_STORE_RECHARGE_REQUESTGOODS_EXTRAGOODSNUM_FIELD = protobuf.FieldDescriptor();
pb.SC_STORE_RECHARGE_REQUESTGOODS = protobuf.Descriptor();
pb.SC_STORE_RECHARGE_REQUESTGOODS_RESULT_FIELD = protobuf.FieldDescriptor();
pb.SC_STORE_RECHARGE_GETDATA = protobuf.Descriptor();
pb.SC_STORE_RECHARGE_GETDATA_BEGINTIME_FIELD = protobuf.FieldDescriptor();
pb.SC_STORE_RECHARGE_GETDATA_STATUS_FIELD = protobuf.FieldDescriptor();
pb.SC_STORE_RECHARGE_GETDATA_PRICE_FIELD = protobuf.FieldDescriptor();
pb.SC_STORE_RECHARGE_GETDATA_GOODSID_FIELD = protobuf.FieldDescriptor();
pb.SC_STORE_RECHARGE_GETDATA_GOODSNUM_FIELD = protobuf.FieldDescriptor();
pb.SC_STORE_RECHARGE_GETDATA_GOODSIDEX_FIELD = protobuf.FieldDescriptor();
pb.SC_STORE_RECHARGE_GETDATA_GOODSNUMEX_FIELD = protobuf.FieldDescriptor();
pb.SC_STORE_RECHARGE_GETDATA_GOODSBINDFLAG_FIELD = protobuf.FieldDescriptor();
pb.CS_STORE_MARKET_TRADERECORD = protobuf.Descriptor();
pb.SC_STORE_MARKET_TRADERECORD = protobuf.Descriptor();
pb.SC_STORE_MARKET_TRADERECORD_RECORDDATA = protobuf.Descriptor();
pb.SC_STORE_MARKET_TRADERECORD_RECORDDATA_OWNERACTORID_FIELD = protobuf.FieldDescriptor();
pb.SC_STORE_MARKET_TRADERECORD_RECORDDATA_BUYERACTORID_FIELD = protobuf.FieldDescriptor();
pb.SC_STORE_MARKET_TRADERECORD_RECORDDATA_GOODSID_FIELD = protobuf.FieldDescriptor();
pb.SC_STORE_MARKET_TRADERECORD_RECORDDATA_GOODSNUM_FIELD = protobuf.FieldDescriptor();
pb.SC_STORE_MARKET_TRADERECORD_RECORDDATA_TIME_FIELD = protobuf.FieldDescriptor();
pb.SC_STORE_MARKET_TRADERECORD_RECORDDATA_PRICE_FIELD = protobuf.FieldDescriptor();
pb.SC_STORE_MARKET_TRADERECORD_RECORDLIST_FIELD = protobuf.FieldDescriptor();
pb.SC_STORE_MARKET_TRADERECORD_ACTORID_FIELD = protobuf.FieldDescriptor();

pb.MSG_STORE_ACTIONID_MSG_STORE_NONE_ENUM.name = "MSG_STORE_NONE"
pb.MSG_STORE_ACTIONID_MSG_STORE_NONE_ENUM.index = 0
pb.MSG_STORE_ACTIONID_MSG_STORE_NONE_ENUM.number = 0
pb.MSG_STORE_ACTIONID_MSG_STORE_GETLIST_ENUM.name = "MSG_STORE_GETLIST"
pb.MSG_STORE_ACTIONID_MSG_STORE_GETLIST_ENUM.index = 1
pb.MSG_STORE_ACTIONID_MSG_STORE_GETLIST_ENUM.number = 1
pb.MSG_STORE_ACTIONID_MSG_STORE_BUYITEM_ENUM.name = "MSG_STORE_BUYITEM"
pb.MSG_STORE_ACTIONID_MSG_STORE_BUYITEM_ENUM.index = 2
pb.MSG_STORE_ACTIONID_MSG_STORE_BUYITEM_ENUM.number = 2
pb.MSG_STORE_ACTIONID_MSG_STORE_REFRESH_ENUM.name = "MSG_STORE_REFRESH"
pb.MSG_STORE_ACTIONID_MSG_STORE_REFRESH_ENUM.index = 3
pb.MSG_STORE_ACTIONID_MSG_STORE_REFRESH_ENUM.number = 3
pb.MSG_STORE_ACTIONID_MSG_STORE_MARKET_GETLIST_ENUM.name = "MSG_STORE_MARKET_GETLIST"
pb.MSG_STORE_ACTIONID_MSG_STORE_MARKET_GETLIST_ENUM.index = 4
pb.MSG_STORE_ACTIONID_MSG_STORE_MARKET_GETLIST_ENUM.number = 4
pb.MSG_STORE_ACTIONID_MSG_STORE_BOOTH_GETBOOTH_ENUM.name = "MSG_STORE_BOOTH_GETBOOTH"
pb.MSG_STORE_ACTIONID_MSG_STORE_BOOTH_GETBOOTH_ENUM.index = 5
pb.MSG_STORE_ACTIONID_MSG_STORE_BOOTH_GETBOOTH_ENUM.number = 5
pb.MSG_STORE_ACTIONID_MSG_STORE_MARKET_GETSELF_ENUM.name = "MSG_STORE_MARKET_GETSELF"
pb.MSG_STORE_ACTIONID_MSG_STORE_MARKET_GETSELF_ENUM.index = 6
pb.MSG_STORE_ACTIONID_MSG_STORE_MARKET_GETSELF_ENUM.number = 7
pb.MSG_STORE_ACTIONID_MSG_STORE_GET_ITEM_PRICE_ENUM.name = "MSG_STORE_GET_ITEM_PRICE"
pb.MSG_STORE_ACTIONID_MSG_STORE_GET_ITEM_PRICE_ENUM.index = 7
pb.MSG_STORE_ACTIONID_MSG_STORE_GET_ITEM_PRICE_ENUM.number = 8
pb.MSG_STORE_ACTIONID_MSG_STORE_MARKET_ADDSELLITEM_ENUM.name = "MSG_STORE_MARKET_ADDSELLITEM"
pb.MSG_STORE_ACTIONID_MSG_STORE_MARKET_ADDSELLITEM_ENUM.index = 8
pb.MSG_STORE_ACTIONID_MSG_STORE_MARKET_ADDSELLITEM_ENUM.number = 9
pb.MSG_STORE_ACTIONID_MSG_STORE_MARKET_REMOVESELLITEM_ENUM.name = "MSG_STORE_MARKET_REMOVESELLITEM"
pb.MSG_STORE_ACTIONID_MSG_STORE_MARKET_REMOVESELLITEM_ENUM.index = 9
pb.MSG_STORE_ACTIONID_MSG_STORE_MARKET_REMOVESELLITEM_ENUM.number = 10
pb.MSG_STORE_ACTIONID_MSG_STORE_MARKET_BUYSELLITEM_ENUM.name = "MSG_STORE_MARKET_BUYSELLITEM"
pb.MSG_STORE_ACTIONID_MSG_STORE_MARKET_BUYSELLITEM_ENUM.index = 10
pb.MSG_STORE_ACTIONID_MSG_STORE_MARKET_BUYSELLITEM_ENUM.number = 11
pb.MSG_STORE_ACTIONID_MSG_STORE_RECHARGE_REQUESTGOODS_ENUM.name = "MSG_STORE_RECHARGE_REQUESTGOODS"
pb.MSG_STORE_ACTIONID_MSG_STORE_RECHARGE_REQUESTGOODS_ENUM.index = 11
pb.MSG_STORE_ACTIONID_MSG_STORE_RECHARGE_REQUESTGOODS_ENUM.number = 12
pb.MSG_STORE_ACTIONID_MSG_STORE_RECHARGE_GETDATA_ENUM.name = "MSG_STORE_RECHARGE_GETDATA"
pb.MSG_STORE_ACTIONID_MSG_STORE_RECHARGE_GETDATA_ENUM.index = 12
pb.MSG_STORE_ACTIONID_MSG_STORE_RECHARGE_GETDATA_ENUM.number = 13
pb.MSG_STORE_ACTIONID_MSG_STORE_MARKET_TRADERECORD_ENUM.name = "MSG_STORE_MARKET_TRADERECORD"
pb.MSG_STORE_ACTIONID_MSG_STORE_MARKET_TRADERECORD_ENUM.index = 13
pb.MSG_STORE_ACTIONID_MSG_STORE_MARKET_TRADERECORD_ENUM.number = 16
pb.MSG_STORE_ACTIONID.name = "MSG_STORE_ACTIONID"
pb.MSG_STORE_ACTIONID.full_name = ".MSG_STORE_ACTIONID"
pb.MSG_STORE_ACTIONID.values = {pb.MSG_STORE_ACTIONID_MSG_STORE_NONE_ENUM,pb.MSG_STORE_ACTIONID_MSG_STORE_GETLIST_ENUM,pb.MSG_STORE_ACTIONID_MSG_STORE_BUYITEM_ENUM,pb.MSG_STORE_ACTIONID_MSG_STORE_REFRESH_ENUM,pb.MSG_STORE_ACTIONID_MSG_STORE_MARKET_GETLIST_ENUM,pb.MSG_STORE_ACTIONID_MSG_STORE_BOOTH_GETBOOTH_ENUM,pb.MSG_STORE_ACTIONID_MSG_STORE_MARKET_GETSELF_ENUM,pb.MSG_STORE_ACTIONID_MSG_STORE_GET_ITEM_PRICE_ENUM,pb.MSG_STORE_ACTIONID_MSG_STORE_MARKET_ADDSELLITEM_ENUM,pb.MSG_STORE_ACTIONID_MSG_STORE_MARKET_REMOVESELLITEM_ENUM,pb.MSG_STORE_ACTIONID_MSG_STORE_MARKET_BUYSELLITEM_ENUM,pb.MSG_STORE_ACTIONID_MSG_STORE_RECHARGE_REQUESTGOODS_ENUM,pb.MSG_STORE_ACTIONID_MSG_STORE_RECHARGE_GETDATA_ENUM,pb.MSG_STORE_ACTIONID_MSG_STORE_MARKET_TRADERECORD_ENUM}
pb.CS_STORE_GETITEMLIST_STOREID_FIELD.name = "StoreID"
pb.CS_STORE_GETITEMLIST_STOREID_FIELD.full_name = ".CS_Store_GetItemList.StoreID"
pb.CS_STORE_GETITEMLIST_STOREID_FIELD.number = 1
pb.CS_STORE_GETITEMLIST_STOREID_FIELD.index = 0
pb.CS_STORE_GETITEMLIST_STOREID_FIELD.label = 2
pb.CS_STORE_GETITEMLIST_STOREID_FIELD.has_default_value = false
pb.CS_STORE_GETITEMLIST_STOREID_FIELD.default_value = 0
pb.CS_STORE_GETITEMLIST_STOREID_FIELD.type = 13
pb.CS_STORE_GETITEMLIST_STOREID_FIELD.cpp_type = 3

pb.CS_STORE_GETITEMLIST.name = "CS_Store_GetItemList"
pb.CS_STORE_GETITEMLIST.full_name = ".CS_Store_GetItemList"
pb.CS_STORE_GETITEMLIST.nested_types = {}
pb.CS_STORE_GETITEMLIST.enum_types = {}
pb.CS_STORE_GETITEMLIST.fields = {pb.CS_STORE_GETITEMLIST_STOREID_FIELD}
pb.CS_STORE_GETITEMLIST.is_extendable = false
pb.CS_STORE_GETITEMLIST.extensions = {}
pb.SC_STORE_GETITEMLIST_ITEMDATA_ITEMID_FIELD.name = "ItemID"
pb.SC_STORE_GETITEMLIST_ITEMDATA_ITEMID_FIELD.full_name = ".SC_Store_GetItemList.ItemData.ItemID"
pb.SC_STORE_GETITEMLIST_ITEMDATA_ITEMID_FIELD.number = 1
pb.SC_STORE_GETITEMLIST_ITEMDATA_ITEMID_FIELD.index = 0
pb.SC_STORE_GETITEMLIST_ITEMDATA_ITEMID_FIELD.label = 2
pb.SC_STORE_GETITEMLIST_ITEMDATA_ITEMID_FIELD.has_default_value = false
pb.SC_STORE_GETITEMLIST_ITEMDATA_ITEMID_FIELD.default_value = 0
pb.SC_STORE_GETITEMLIST_ITEMDATA_ITEMID_FIELD.type = 5
pb.SC_STORE_GETITEMLIST_ITEMDATA_ITEMID_FIELD.cpp_type = 1

pb.SC_STORE_GETITEMLIST_ITEMDATA_GOODSID_FIELD.name = "GoodsID"
pb.SC_STORE_GETITEMLIST_ITEMDATA_GOODSID_FIELD.full_name = ".SC_Store_GetItemList.ItemData.GoodsID"
pb.SC_STORE_GETITEMLIST_ITEMDATA_GOODSID_FIELD.number = 2
pb.SC_STORE_GETITEMLIST_ITEMDATA_GOODSID_FIELD.index = 1
pb.SC_STORE_GETITEMLIST_ITEMDATA_GOODSID_FIELD.label = 2
pb.SC_STORE_GETITEMLIST_ITEMDATA_GOODSID_FIELD.has_default_value = false
pb.SC_STORE_GETITEMLIST_ITEMDATA_GOODSID_FIELD.default_value = 0
pb.SC_STORE_GETITEMLIST_ITEMDATA_GOODSID_FIELD.type = 5
pb.SC_STORE_GETITEMLIST_ITEMDATA_GOODSID_FIELD.cpp_type = 1

pb.SC_STORE_GETITEMLIST_ITEMDATA_GOODSNUM_FIELD.name = "GoodsNum"
pb.SC_STORE_GETITEMLIST_ITEMDATA_GOODSNUM_FIELD.full_name = ".SC_Store_GetItemList.ItemData.GoodsNum"
pb.SC_STORE_GETITEMLIST_ITEMDATA_GOODSNUM_FIELD.number = 3
pb.SC_STORE_GETITEMLIST_ITEMDATA_GOODSNUM_FIELD.index = 2
pb.SC_STORE_GETITEMLIST_ITEMDATA_GOODSNUM_FIELD.label = 2
pb.SC_STORE_GETITEMLIST_ITEMDATA_GOODSNUM_FIELD.has_default_value = false
pb.SC_STORE_GETITEMLIST_ITEMDATA_GOODSNUM_FIELD.default_value = 0
pb.SC_STORE_GETITEMLIST_ITEMDATA_GOODSNUM_FIELD.type = 5
pb.SC_STORE_GETITEMLIST_ITEMDATA_GOODSNUM_FIELD.cpp_type = 1

pb.SC_STORE_GETITEMLIST_ITEMDATA_COSTTYPE_FIELD.name = "CostType"
pb.SC_STORE_GETITEMLIST_ITEMDATA_COSTTYPE_FIELD.full_name = ".SC_Store_GetItemList.ItemData.CostType"
pb.SC_STORE_GETITEMLIST_ITEMDATA_COSTTYPE_FIELD.number = 4
pb.SC_STORE_GETITEMLIST_ITEMDATA_COSTTYPE_FIELD.index = 3
pb.SC_STORE_GETITEMLIST_ITEMDATA_COSTTYPE_FIELD.label = 2
pb.SC_STORE_GETITEMLIST_ITEMDATA_COSTTYPE_FIELD.has_default_value = false
pb.SC_STORE_GETITEMLIST_ITEMDATA_COSTTYPE_FIELD.default_value = 0
pb.SC_STORE_GETITEMLIST_ITEMDATA_COSTTYPE_FIELD.type = 5
pb.SC_STORE_GETITEMLIST_ITEMDATA_COSTTYPE_FIELD.cpp_type = 1

pb.SC_STORE_GETITEMLIST_ITEMDATA_COSTNUM_FIELD.name = "CostNum"
pb.SC_STORE_GETITEMLIST_ITEMDATA_COSTNUM_FIELD.full_name = ".SC_Store_GetItemList.ItemData.CostNum"
pb.SC_STORE_GETITEMLIST_ITEMDATA_COSTNUM_FIELD.number = 5
pb.SC_STORE_GETITEMLIST_ITEMDATA_COSTNUM_FIELD.index = 4
pb.SC_STORE_GETITEMLIST_ITEMDATA_COSTNUM_FIELD.label = 2
pb.SC_STORE_GETITEMLIST_ITEMDATA_COSTNUM_FIELD.has_default_value = false
pb.SC_STORE_GETITEMLIST_ITEMDATA_COSTNUM_FIELD.default_value = 0
pb.SC_STORE_GETITEMLIST_ITEMDATA_COSTNUM_FIELD.type = 5
pb.SC_STORE_GETITEMLIST_ITEMDATA_COSTNUM_FIELD.cpp_type = 1

pb.SC_STORE_GETITEMLIST_ITEMDATA_SOLDOUT_FIELD.name = "SoldOut"
pb.SC_STORE_GETITEMLIST_ITEMDATA_SOLDOUT_FIELD.full_name = ".SC_Store_GetItemList.ItemData.SoldOut"
pb.SC_STORE_GETITEMLIST_ITEMDATA_SOLDOUT_FIELD.number = 6
pb.SC_STORE_GETITEMLIST_ITEMDATA_SOLDOUT_FIELD.index = 5
pb.SC_STORE_GETITEMLIST_ITEMDATA_SOLDOUT_FIELD.label = 2
pb.SC_STORE_GETITEMLIST_ITEMDATA_SOLDOUT_FIELD.has_default_value = false
pb.SC_STORE_GETITEMLIST_ITEMDATA_SOLDOUT_FIELD.default_value = false
pb.SC_STORE_GETITEMLIST_ITEMDATA_SOLDOUT_FIELD.type = 8
pb.SC_STORE_GETITEMLIST_ITEMDATA_SOLDOUT_FIELD.cpp_type = 7

pb.SC_STORE_GETITEMLIST_ITEMDATA_DISCOUNT_FIELD.name = "Discount"
pb.SC_STORE_GETITEMLIST_ITEMDATA_DISCOUNT_FIELD.full_name = ".SC_Store_GetItemList.ItemData.Discount"
pb.SC_STORE_GETITEMLIST_ITEMDATA_DISCOUNT_FIELD.number = 7
pb.SC_STORE_GETITEMLIST_ITEMDATA_DISCOUNT_FIELD.index = 6
pb.SC_STORE_GETITEMLIST_ITEMDATA_DISCOUNT_FIELD.label = 2
pb.SC_STORE_GETITEMLIST_ITEMDATA_DISCOUNT_FIELD.has_default_value = false
pb.SC_STORE_GETITEMLIST_ITEMDATA_DISCOUNT_FIELD.default_value = 0
pb.SC_STORE_GETITEMLIST_ITEMDATA_DISCOUNT_FIELD.type = 5
pb.SC_STORE_GETITEMLIST_ITEMDATA_DISCOUNT_FIELD.cpp_type = 1

pb.SC_STORE_GETITEMLIST_ITEMDATA_BINDFLAG_FIELD.name = "BindFlag"
pb.SC_STORE_GETITEMLIST_ITEMDATA_BINDFLAG_FIELD.full_name = ".SC_Store_GetItemList.ItemData.BindFlag"
pb.SC_STORE_GETITEMLIST_ITEMDATA_BINDFLAG_FIELD.number = 8
pb.SC_STORE_GETITEMLIST_ITEMDATA_BINDFLAG_FIELD.index = 7
pb.SC_STORE_GETITEMLIST_ITEMDATA_BINDFLAG_FIELD.label = 2
pb.SC_STORE_GETITEMLIST_ITEMDATA_BINDFLAG_FIELD.has_default_value = false
pb.SC_STORE_GETITEMLIST_ITEMDATA_BINDFLAG_FIELD.default_value = 0
pb.SC_STORE_GETITEMLIST_ITEMDATA_BINDFLAG_FIELD.type = 5
pb.SC_STORE_GETITEMLIST_ITEMDATA_BINDFLAG_FIELD.cpp_type = 1

pb.SC_STORE_GETITEMLIST_ITEMDATA_VIP_FIELD.name = "VIP"
pb.SC_STORE_GETITEMLIST_ITEMDATA_VIP_FIELD.full_name = ".SC_Store_GetItemList.ItemData.VIP"
pb.SC_STORE_GETITEMLIST_ITEMDATA_VIP_FIELD.number = 9
pb.SC_STORE_GETITEMLIST_ITEMDATA_VIP_FIELD.index = 8
pb.SC_STORE_GETITEMLIST_ITEMDATA_VIP_FIELD.label = 1
pb.SC_STORE_GETITEMLIST_ITEMDATA_VIP_FIELD.has_default_value = false
pb.SC_STORE_GETITEMLIST_ITEMDATA_VIP_FIELD.default_value = 0
pb.SC_STORE_GETITEMLIST_ITEMDATA_VIP_FIELD.type = 5
pb.SC_STORE_GETITEMLIST_ITEMDATA_VIP_FIELD.cpp_type = 1

pb.SC_STORE_GETITEMLIST_ITEMDATA_PLAYERLEVEL_FIELD.name = "PlayerLevel"
pb.SC_STORE_GETITEMLIST_ITEMDATA_PLAYERLEVEL_FIELD.full_name = ".SC_Store_GetItemList.ItemData.PlayerLevel"
pb.SC_STORE_GETITEMLIST_ITEMDATA_PLAYERLEVEL_FIELD.number = 10
pb.SC_STORE_GETITEMLIST_ITEMDATA_PLAYERLEVEL_FIELD.index = 9
pb.SC_STORE_GETITEMLIST_ITEMDATA_PLAYERLEVEL_FIELD.label = 1
pb.SC_STORE_GETITEMLIST_ITEMDATA_PLAYERLEVEL_FIELD.has_default_value = false
pb.SC_STORE_GETITEMLIST_ITEMDATA_PLAYERLEVEL_FIELD.default_value = 0
pb.SC_STORE_GETITEMLIST_ITEMDATA_PLAYERLEVEL_FIELD.type = 5
pb.SC_STORE_GETITEMLIST_ITEMDATA_PLAYERLEVEL_FIELD.cpp_type = 1

pb.SC_STORE_GETITEMLIST_ITEMDATA_SOCIETYLEVEL_FIELD.name = "SocietyLevel"
pb.SC_STORE_GETITEMLIST_ITEMDATA_SOCIETYLEVEL_FIELD.full_name = ".SC_Store_GetItemList.ItemData.SocietyLevel"
pb.SC_STORE_GETITEMLIST_ITEMDATA_SOCIETYLEVEL_FIELD.number = 11
pb.SC_STORE_GETITEMLIST_ITEMDATA_SOCIETYLEVEL_FIELD.index = 10
pb.SC_STORE_GETITEMLIST_ITEMDATA_SOCIETYLEVEL_FIELD.label = 1
pb.SC_STORE_GETITEMLIST_ITEMDATA_SOCIETYLEVEL_FIELD.has_default_value = false
pb.SC_STORE_GETITEMLIST_ITEMDATA_SOCIETYLEVEL_FIELD.default_value = 0
pb.SC_STORE_GETITEMLIST_ITEMDATA_SOCIETYLEVEL_FIELD.type = 5
pb.SC_STORE_GETITEMLIST_ITEMDATA_SOCIETYLEVEL_FIELD.cpp_type = 1

pb.SC_STORE_GETITEMLIST_ITEMDATA_NEEDRECHARGE_FIELD.name = "NeedRecharge"
pb.SC_STORE_GETITEMLIST_ITEMDATA_NEEDRECHARGE_FIELD.full_name = ".SC_Store_GetItemList.ItemData.NeedRecharge"
pb.SC_STORE_GETITEMLIST_ITEMDATA_NEEDRECHARGE_FIELD.number = 12
pb.SC_STORE_GETITEMLIST_ITEMDATA_NEEDRECHARGE_FIELD.index = 11
pb.SC_STORE_GETITEMLIST_ITEMDATA_NEEDRECHARGE_FIELD.label = 1
pb.SC_STORE_GETITEMLIST_ITEMDATA_NEEDRECHARGE_FIELD.has_default_value = false
pb.SC_STORE_GETITEMLIST_ITEMDATA_NEEDRECHARGE_FIELD.default_value = 0
pb.SC_STORE_GETITEMLIST_ITEMDATA_NEEDRECHARGE_FIELD.type = 5
pb.SC_STORE_GETITEMLIST_ITEMDATA_NEEDRECHARGE_FIELD.cpp_type = 1

pb.SC_STORE_GETITEMLIST_ITEMDATA_LIMITACTORDAY_FIELD.name = "LimitActorDay"
pb.SC_STORE_GETITEMLIST_ITEMDATA_LIMITACTORDAY_FIELD.full_name = ".SC_Store_GetItemList.ItemData.LimitActorDay"
pb.SC_STORE_GETITEMLIST_ITEMDATA_LIMITACTORDAY_FIELD.number = 13
pb.SC_STORE_GETITEMLIST_ITEMDATA_LIMITACTORDAY_FIELD.index = 12
pb.SC_STORE_GETITEMLIST_ITEMDATA_LIMITACTORDAY_FIELD.label = 2
pb.SC_STORE_GETITEMLIST_ITEMDATA_LIMITACTORDAY_FIELD.has_default_value = false
pb.SC_STORE_GETITEMLIST_ITEMDATA_LIMITACTORDAY_FIELD.default_value = 0
pb.SC_STORE_GETITEMLIST_ITEMDATA_LIMITACTORDAY_FIELD.type = 5
pb.SC_STORE_GETITEMLIST_ITEMDATA_LIMITACTORDAY_FIELD.cpp_type = 1

pb.SC_STORE_GETITEMLIST_ITEMDATA_NUMSLIMITTYPE_FIELD.name = "NumsLimitType"
pb.SC_STORE_GETITEMLIST_ITEMDATA_NUMSLIMITTYPE_FIELD.full_name = ".SC_Store_GetItemList.ItemData.NumsLimitType"
pb.SC_STORE_GETITEMLIST_ITEMDATA_NUMSLIMITTYPE_FIELD.number = 14
pb.SC_STORE_GETITEMLIST_ITEMDATA_NUMSLIMITTYPE_FIELD.index = 13
pb.SC_STORE_GETITEMLIST_ITEMDATA_NUMSLIMITTYPE_FIELD.label = 1
pb.SC_STORE_GETITEMLIST_ITEMDATA_NUMSLIMITTYPE_FIELD.has_default_value = false
pb.SC_STORE_GETITEMLIST_ITEMDATA_NUMSLIMITTYPE_FIELD.default_value = 0
pb.SC_STORE_GETITEMLIST_ITEMDATA_NUMSLIMITTYPE_FIELD.type = 5
pb.SC_STORE_GETITEMLIST_ITEMDATA_NUMSLIMITTYPE_FIELD.cpp_type = 1

pb.SC_STORE_GETITEMLIST_ITEMDATA_NUMSLIMITDAY_FIELD.name = "NumsLimitDay"
pb.SC_STORE_GETITEMLIST_ITEMDATA_NUMSLIMITDAY_FIELD.full_name = ".SC_Store_GetItemList.ItemData.NumsLimitDay"
pb.SC_STORE_GETITEMLIST_ITEMDATA_NUMSLIMITDAY_FIELD.number = 15
pb.SC_STORE_GETITEMLIST_ITEMDATA_NUMSLIMITDAY_FIELD.index = 14
pb.SC_STORE_GETITEMLIST_ITEMDATA_NUMSLIMITDAY_FIELD.label = 1
pb.SC_STORE_GETITEMLIST_ITEMDATA_NUMSLIMITDAY_FIELD.has_default_value = false
pb.SC_STORE_GETITEMLIST_ITEMDATA_NUMSLIMITDAY_FIELD.default_value = 0
pb.SC_STORE_GETITEMLIST_ITEMDATA_NUMSLIMITDAY_FIELD.type = 5
pb.SC_STORE_GETITEMLIST_ITEMDATA_NUMSLIMITDAY_FIELD.cpp_type = 1

pb.SC_STORE_GETITEMLIST_ITEMDATA_NUMSISBUY_FIELD.name = "NumsIsBuy"
pb.SC_STORE_GETITEMLIST_ITEMDATA_NUMSISBUY_FIELD.full_name = ".SC_Store_GetItemList.ItemData.NumsIsBuy"
pb.SC_STORE_GETITEMLIST_ITEMDATA_NUMSISBUY_FIELD.number = 16
pb.SC_STORE_GETITEMLIST_ITEMDATA_NUMSISBUY_FIELD.index = 15
pb.SC_STORE_GETITEMLIST_ITEMDATA_NUMSISBUY_FIELD.label = 1
pb.SC_STORE_GETITEMLIST_ITEMDATA_NUMSISBUY_FIELD.has_default_value = false
pb.SC_STORE_GETITEMLIST_ITEMDATA_NUMSISBUY_FIELD.default_value = 0
pb.SC_STORE_GETITEMLIST_ITEMDATA_NUMSISBUY_FIELD.type = 5
pb.SC_STORE_GETITEMLIST_ITEMDATA_NUMSISBUY_FIELD.cpp_type = 1

pb.SC_STORE_GETITEMLIST_ITEMDATA_NUMSGROUPISBUY_FIELD.name = "NumsGroupIsBuy"
pb.SC_STORE_GETITEMLIST_ITEMDATA_NUMSGROUPISBUY_FIELD.full_name = ".SC_Store_GetItemList.ItemData.NumsGroupIsBuy"
pb.SC_STORE_GETITEMLIST_ITEMDATA_NUMSGROUPISBUY_FIELD.number = 17
pb.SC_STORE_GETITEMLIST_ITEMDATA_NUMSGROUPISBUY_FIELD.index = 16
pb.SC_STORE_GETITEMLIST_ITEMDATA_NUMSGROUPISBUY_FIELD.label = 1
pb.SC_STORE_GETITEMLIST_ITEMDATA_NUMSGROUPISBUY_FIELD.has_default_value = false
pb.SC_STORE_GETITEMLIST_ITEMDATA_NUMSGROUPISBUY_FIELD.default_value = 0
pb.SC_STORE_GETITEMLIST_ITEMDATA_NUMSGROUPISBUY_FIELD.type = 5
pb.SC_STORE_GETITEMLIST_ITEMDATA_NUMSGROUPISBUY_FIELD.cpp_type = 1

pb.SC_STORE_GETITEMLIST_ITEMDATA.name = "ItemData"
pb.SC_STORE_GETITEMLIST_ITEMDATA.full_name = ".SC_Store_GetItemList.ItemData"
pb.SC_STORE_GETITEMLIST_ITEMDATA.nested_types = {}
pb.SC_STORE_GETITEMLIST_ITEMDATA.enum_types = {}
pb.SC_STORE_GETITEMLIST_ITEMDATA.fields = {pb.SC_STORE_GETITEMLIST_ITEMDATA_ITEMID_FIELD, pb.SC_STORE_GETITEMLIST_ITEMDATA_GOODSID_FIELD, pb.SC_STORE_GETITEMLIST_ITEMDATA_GOODSNUM_FIELD, pb.SC_STORE_GETITEMLIST_ITEMDATA_COSTTYPE_FIELD, pb.SC_STORE_GETITEMLIST_ITEMDATA_COSTNUM_FIELD, pb.SC_STORE_GETITEMLIST_ITEMDATA_SOLDOUT_FIELD, pb.SC_STORE_GETITEMLIST_ITEMDATA_DISCOUNT_FIELD, pb.SC_STORE_GETITEMLIST_ITEMDATA_BINDFLAG_FIELD, pb.SC_STORE_GETITEMLIST_ITEMDATA_VIP_FIELD, pb.SC_STORE_GETITEMLIST_ITEMDATA_PLAYERLEVEL_FIELD, pb.SC_STORE_GETITEMLIST_ITEMDATA_SOCIETYLEVEL_FIELD, pb.SC_STORE_GETITEMLIST_ITEMDATA_NEEDRECHARGE_FIELD, pb.SC_STORE_GETITEMLIST_ITEMDATA_LIMITACTORDAY_FIELD, pb.SC_STORE_GETITEMLIST_ITEMDATA_NUMSLIMITTYPE_FIELD, pb.SC_STORE_GETITEMLIST_ITEMDATA_NUMSLIMITDAY_FIELD, pb.SC_STORE_GETITEMLIST_ITEMDATA_NUMSISBUY_FIELD, pb.SC_STORE_GETITEMLIST_ITEMDATA_NUMSGROUPISBUY_FIELD}
pb.SC_STORE_GETITEMLIST_ITEMDATA.is_extendable = false
pb.SC_STORE_GETITEMLIST_ITEMDATA.extensions = {}
pb.SC_STORE_GETITEMLIST_ITEMDATA.containing_type = pb.SC_STORE_GETITEMLIST
pb.SC_STORE_GETITEMLIST_STOREID_FIELD.name = "StoreID"
pb.SC_STORE_GETITEMLIST_STOREID_FIELD.full_name = ".SC_Store_GetItemList.StoreID"
pb.SC_STORE_GETITEMLIST_STOREID_FIELD.number = 1
pb.SC_STORE_GETITEMLIST_STOREID_FIELD.index = 0
pb.SC_STORE_GETITEMLIST_STOREID_FIELD.label = 2
pb.SC_STORE_GETITEMLIST_STOREID_FIELD.has_default_value = false
pb.SC_STORE_GETITEMLIST_STOREID_FIELD.default_value = 0
pb.SC_STORE_GETITEMLIST_STOREID_FIELD.type = 5
pb.SC_STORE_GETITEMLIST_STOREID_FIELD.cpp_type = 1

pb.SC_STORE_GETITEMLIST_ITEMLIST_FIELD.name = "ItemList"
pb.SC_STORE_GETITEMLIST_ITEMLIST_FIELD.full_name = ".SC_Store_GetItemList.ItemList"
pb.SC_STORE_GETITEMLIST_ITEMLIST_FIELD.number = 2
pb.SC_STORE_GETITEMLIST_ITEMLIST_FIELD.index = 1
pb.SC_STORE_GETITEMLIST_ITEMLIST_FIELD.label = 3
pb.SC_STORE_GETITEMLIST_ITEMLIST_FIELD.has_default_value = false
pb.SC_STORE_GETITEMLIST_ITEMLIST_FIELD.default_value = {}
pb.SC_STORE_GETITEMLIST_ITEMLIST_FIELD.message_type = pb.SC_STORE_GETITEMLIST_ITEMDATA
pb.SC_STORE_GETITEMLIST_ITEMLIST_FIELD.type = 11
pb.SC_STORE_GETITEMLIST_ITEMLIST_FIELD.cpp_type = 10

pb.SC_STORE_GETITEMLIST_REFRESHID_FIELD.name = "RefreshID"
pb.SC_STORE_GETITEMLIST_REFRESHID_FIELD.full_name = ".SC_Store_GetItemList.RefreshID"
pb.SC_STORE_GETITEMLIST_REFRESHID_FIELD.number = 3
pb.SC_STORE_GETITEMLIST_REFRESHID_FIELD.index = 2
pb.SC_STORE_GETITEMLIST_REFRESHID_FIELD.label = 1
pb.SC_STORE_GETITEMLIST_REFRESHID_FIELD.has_default_value = false
pb.SC_STORE_GETITEMLIST_REFRESHID_FIELD.default_value = 0
pb.SC_STORE_GETITEMLIST_REFRESHID_FIELD.type = 5
pb.SC_STORE_GETITEMLIST_REFRESHID_FIELD.cpp_type = 1

pb.SC_STORE_GETITEMLIST_REFRESHTIME_FIELD.name = "RefreshTime"
pb.SC_STORE_GETITEMLIST_REFRESHTIME_FIELD.full_name = ".SC_Store_GetItemList.RefreshTime"
pb.SC_STORE_GETITEMLIST_REFRESHTIME_FIELD.number = 4
pb.SC_STORE_GETITEMLIST_REFRESHTIME_FIELD.index = 3
pb.SC_STORE_GETITEMLIST_REFRESHTIME_FIELD.label = 1
pb.SC_STORE_GETITEMLIST_REFRESHTIME_FIELD.has_default_value = false
pb.SC_STORE_GETITEMLIST_REFRESHTIME_FIELD.default_value = 0
pb.SC_STORE_GETITEMLIST_REFRESHTIME_FIELD.type = 5
pb.SC_STORE_GETITEMLIST_REFRESHTIME_FIELD.cpp_type = 1

pb.SC_STORE_GETITEMLIST_REFRESHNUM_FIELD.name = "RefreshNum"
pb.SC_STORE_GETITEMLIST_REFRESHNUM_FIELD.full_name = ".SC_Store_GetItemList.RefreshNum"
pb.SC_STORE_GETITEMLIST_REFRESHNUM_FIELD.number = 5
pb.SC_STORE_GETITEMLIST_REFRESHNUM_FIELD.index = 4
pb.SC_STORE_GETITEMLIST_REFRESHNUM_FIELD.label = 1
pb.SC_STORE_GETITEMLIST_REFRESHNUM_FIELD.has_default_value = false
pb.SC_STORE_GETITEMLIST_REFRESHNUM_FIELD.default_value = 0
pb.SC_STORE_GETITEMLIST_REFRESHNUM_FIELD.type = 5
pb.SC_STORE_GETITEMLIST_REFRESHNUM_FIELD.cpp_type = 1

pb.SC_STORE_GETITEMLIST_REFRESHFREECOUNT_FIELD.name = "RefreshFreeCount"
pb.SC_STORE_GETITEMLIST_REFRESHFREECOUNT_FIELD.full_name = ".SC_Store_GetItemList.RefreshFreeCount"
pb.SC_STORE_GETITEMLIST_REFRESHFREECOUNT_FIELD.number = 6
pb.SC_STORE_GETITEMLIST_REFRESHFREECOUNT_FIELD.index = 5
pb.SC_STORE_GETITEMLIST_REFRESHFREECOUNT_FIELD.label = 1
pb.SC_STORE_GETITEMLIST_REFRESHFREECOUNT_FIELD.has_default_value = false
pb.SC_STORE_GETITEMLIST_REFRESHFREECOUNT_FIELD.default_value = 0
pb.SC_STORE_GETITEMLIST_REFRESHFREECOUNT_FIELD.type = 5
pb.SC_STORE_GETITEMLIST_REFRESHFREECOUNT_FIELD.cpp_type = 1

pb.SC_STORE_GETITEMLIST.name = "SC_Store_GetItemList"
pb.SC_STORE_GETITEMLIST.full_name = ".SC_Store_GetItemList"
pb.SC_STORE_GETITEMLIST.nested_types = {pb.SC_STORE_GETITEMLIST_ITEMDATA}
pb.SC_STORE_GETITEMLIST.enum_types = {}
pb.SC_STORE_GETITEMLIST.fields = {pb.SC_STORE_GETITEMLIST_STOREID_FIELD, pb.SC_STORE_GETITEMLIST_ITEMLIST_FIELD, pb.SC_STORE_GETITEMLIST_REFRESHID_FIELD, pb.SC_STORE_GETITEMLIST_REFRESHTIME_FIELD, pb.SC_STORE_GETITEMLIST_REFRESHNUM_FIELD, pb.SC_STORE_GETITEMLIST_REFRESHFREECOUNT_FIELD}
pb.SC_STORE_GETITEMLIST.is_extendable = false
pb.SC_STORE_GETITEMLIST.extensions = {}
pb.CS_STORE_BUYITEM_STOREID_FIELD.name = "StoreID"
pb.CS_STORE_BUYITEM_STOREID_FIELD.full_name = ".CS_Store_BuyItem.StoreID"
pb.CS_STORE_BUYITEM_STOREID_FIELD.number = 1
pb.CS_STORE_BUYITEM_STOREID_FIELD.index = 0
pb.CS_STORE_BUYITEM_STOREID_FIELD.label = 2
pb.CS_STORE_BUYITEM_STOREID_FIELD.has_default_value = false
pb.CS_STORE_BUYITEM_STOREID_FIELD.default_value = 0
pb.CS_STORE_BUYITEM_STOREID_FIELD.type = 13
pb.CS_STORE_BUYITEM_STOREID_FIELD.cpp_type = 3

pb.CS_STORE_BUYITEM_ITEMID_FIELD.name = "ItemID"
pb.CS_STORE_BUYITEM_ITEMID_FIELD.full_name = ".CS_Store_BuyItem.ItemID"
pb.CS_STORE_BUYITEM_ITEMID_FIELD.number = 2
pb.CS_STORE_BUYITEM_ITEMID_FIELD.index = 1
pb.CS_STORE_BUYITEM_ITEMID_FIELD.label = 2
pb.CS_STORE_BUYITEM_ITEMID_FIELD.has_default_value = false
pb.CS_STORE_BUYITEM_ITEMID_FIELD.default_value = 0
pb.CS_STORE_BUYITEM_ITEMID_FIELD.type = 13
pb.CS_STORE_BUYITEM_ITEMID_FIELD.cpp_type = 3

pb.CS_STORE_BUYITEM_ITEMNUM_FIELD.name = "ItemNum"
pb.CS_STORE_BUYITEM_ITEMNUM_FIELD.full_name = ".CS_Store_BuyItem.ItemNum"
pb.CS_STORE_BUYITEM_ITEMNUM_FIELD.number = 3
pb.CS_STORE_BUYITEM_ITEMNUM_FIELD.index = 2
pb.CS_STORE_BUYITEM_ITEMNUM_FIELD.label = 2
pb.CS_STORE_BUYITEM_ITEMNUM_FIELD.has_default_value = false
pb.CS_STORE_BUYITEM_ITEMNUM_FIELD.default_value = 0
pb.CS_STORE_BUYITEM_ITEMNUM_FIELD.type = 13
pb.CS_STORE_BUYITEM_ITEMNUM_FIELD.cpp_type = 3

pb.CS_STORE_BUYITEM.name = "CS_Store_BuyItem"
pb.CS_STORE_BUYITEM.full_name = ".CS_Store_BuyItem"
pb.CS_STORE_BUYITEM.nested_types = {}
pb.CS_STORE_BUYITEM.enum_types = {}
pb.CS_STORE_BUYITEM.fields = {pb.CS_STORE_BUYITEM_STOREID_FIELD, pb.CS_STORE_BUYITEM_ITEMID_FIELD, pb.CS_STORE_BUYITEM_ITEMNUM_FIELD}
pb.CS_STORE_BUYITEM.is_extendable = false
pb.CS_STORE_BUYITEM.extensions = {}
pb.SC_STORE_BUYITEM_RESULT_FIELD.name = "Result"
pb.SC_STORE_BUYITEM_RESULT_FIELD.full_name = ".SC_Store_BuyItem.Result"
pb.SC_STORE_BUYITEM_RESULT_FIELD.number = 1
pb.SC_STORE_BUYITEM_RESULT_FIELD.index = 0
pb.SC_STORE_BUYITEM_RESULT_FIELD.label = 2
pb.SC_STORE_BUYITEM_RESULT_FIELD.has_default_value = false
pb.SC_STORE_BUYITEM_RESULT_FIELD.default_value = 0
pb.SC_STORE_BUYITEM_RESULT_FIELD.type = 13
pb.SC_STORE_BUYITEM_RESULT_FIELD.cpp_type = 3

pb.SC_STORE_BUYITEM_STOREID_FIELD.name = "StoreID"
pb.SC_STORE_BUYITEM_STOREID_FIELD.full_name = ".SC_Store_BuyItem.StoreID"
pb.SC_STORE_BUYITEM_STOREID_FIELD.number = 2
pb.SC_STORE_BUYITEM_STOREID_FIELD.index = 1
pb.SC_STORE_BUYITEM_STOREID_FIELD.label = 2
pb.SC_STORE_BUYITEM_STOREID_FIELD.has_default_value = false
pb.SC_STORE_BUYITEM_STOREID_FIELD.default_value = 0
pb.SC_STORE_BUYITEM_STOREID_FIELD.type = 13
pb.SC_STORE_BUYITEM_STOREID_FIELD.cpp_type = 3

pb.SC_STORE_BUYITEM_ITEMID_FIELD.name = "ItemID"
pb.SC_STORE_BUYITEM_ITEMID_FIELD.full_name = ".SC_Store_BuyItem.ItemID"
pb.SC_STORE_BUYITEM_ITEMID_FIELD.number = 3
pb.SC_STORE_BUYITEM_ITEMID_FIELD.index = 2
pb.SC_STORE_BUYITEM_ITEMID_FIELD.label = 2
pb.SC_STORE_BUYITEM_ITEMID_FIELD.has_default_value = false
pb.SC_STORE_BUYITEM_ITEMID_FIELD.default_value = 0
pb.SC_STORE_BUYITEM_ITEMID_FIELD.type = 13
pb.SC_STORE_BUYITEM_ITEMID_FIELD.cpp_type = 3

pb.SC_STORE_BUYITEM_SOLDOUT_FIELD.name = "SoldOut"
pb.SC_STORE_BUYITEM_SOLDOUT_FIELD.full_name = ".SC_Store_BuyItem.SoldOut"
pb.SC_STORE_BUYITEM_SOLDOUT_FIELD.number = 4
pb.SC_STORE_BUYITEM_SOLDOUT_FIELD.index = 3
pb.SC_STORE_BUYITEM_SOLDOUT_FIELD.label = 2
pb.SC_STORE_BUYITEM_SOLDOUT_FIELD.has_default_value = false
pb.SC_STORE_BUYITEM_SOLDOUT_FIELD.default_value = false
pb.SC_STORE_BUYITEM_SOLDOUT_FIELD.type = 8
pb.SC_STORE_BUYITEM_SOLDOUT_FIELD.cpp_type = 7

pb.SC_STORE_BUYITEM_PARAM_FIELD.name = "Param"
pb.SC_STORE_BUYITEM_PARAM_FIELD.full_name = ".SC_Store_BuyItem.Param"
pb.SC_STORE_BUYITEM_PARAM_FIELD.number = 5
pb.SC_STORE_BUYITEM_PARAM_FIELD.index = 4
pb.SC_STORE_BUYITEM_PARAM_FIELD.label = 2
pb.SC_STORE_BUYITEM_PARAM_FIELD.has_default_value = false
pb.SC_STORE_BUYITEM_PARAM_FIELD.default_value = 0
pb.SC_STORE_BUYITEM_PARAM_FIELD.type = 13
pb.SC_STORE_BUYITEM_PARAM_FIELD.cpp_type = 3

pb.SC_STORE_BUYITEM_NUMSISBUY_FIELD.name = "NumsIsBuy"
pb.SC_STORE_BUYITEM_NUMSISBUY_FIELD.full_name = ".SC_Store_BuyItem.NumsIsBuy"
pb.SC_STORE_BUYITEM_NUMSISBUY_FIELD.number = 6
pb.SC_STORE_BUYITEM_NUMSISBUY_FIELD.index = 5
pb.SC_STORE_BUYITEM_NUMSISBUY_FIELD.label = 2
pb.SC_STORE_BUYITEM_NUMSISBUY_FIELD.has_default_value = false
pb.SC_STORE_BUYITEM_NUMSISBUY_FIELD.default_value = 0
pb.SC_STORE_BUYITEM_NUMSISBUY_FIELD.type = 13
pb.SC_STORE_BUYITEM_NUMSISBUY_FIELD.cpp_type = 3

pb.SC_STORE_BUYITEM_NUMSGROUPISBUY_FIELD.name = "NumsGroupIsBuy"
pb.SC_STORE_BUYITEM_NUMSGROUPISBUY_FIELD.full_name = ".SC_Store_BuyItem.NumsGroupIsBuy"
pb.SC_STORE_BUYITEM_NUMSGROUPISBUY_FIELD.number = 7
pb.SC_STORE_BUYITEM_NUMSGROUPISBUY_FIELD.index = 6
pb.SC_STORE_BUYITEM_NUMSGROUPISBUY_FIELD.label = 2
pb.SC_STORE_BUYITEM_NUMSGROUPISBUY_FIELD.has_default_value = false
pb.SC_STORE_BUYITEM_NUMSGROUPISBUY_FIELD.default_value = 0
pb.SC_STORE_BUYITEM_NUMSGROUPISBUY_FIELD.type = 13
pb.SC_STORE_BUYITEM_NUMSGROUPISBUY_FIELD.cpp_type = 3

pb.SC_STORE_BUYITEM.name = "SC_Store_BuyItem"
pb.SC_STORE_BUYITEM.full_name = ".SC_Store_BuyItem"
pb.SC_STORE_BUYITEM.nested_types = {}
pb.SC_STORE_BUYITEM.enum_types = {}
pb.SC_STORE_BUYITEM.fields = {pb.SC_STORE_BUYITEM_RESULT_FIELD, pb.SC_STORE_BUYITEM_STOREID_FIELD, pb.SC_STORE_BUYITEM_ITEMID_FIELD, pb.SC_STORE_BUYITEM_SOLDOUT_FIELD, pb.SC_STORE_BUYITEM_PARAM_FIELD, pb.SC_STORE_BUYITEM_NUMSISBUY_FIELD, pb.SC_STORE_BUYITEM_NUMSGROUPISBUY_FIELD}
pb.SC_STORE_BUYITEM.is_extendable = false
pb.SC_STORE_BUYITEM.extensions = {}
pb.CS_STORE_REFRESH_STOREID_FIELD.name = "StoreID"
pb.CS_STORE_REFRESH_STOREID_FIELD.full_name = ".CS_Store_Refresh.StoreID"
pb.CS_STORE_REFRESH_STOREID_FIELD.number = 1
pb.CS_STORE_REFRESH_STOREID_FIELD.index = 0
pb.CS_STORE_REFRESH_STOREID_FIELD.label = 2
pb.CS_STORE_REFRESH_STOREID_FIELD.has_default_value = false
pb.CS_STORE_REFRESH_STOREID_FIELD.default_value = 0
pb.CS_STORE_REFRESH_STOREID_FIELD.type = 13
pb.CS_STORE_REFRESH_STOREID_FIELD.cpp_type = 3

pb.CS_STORE_REFRESH_ADVERTISEREFRESH_FIELD.name = "AdvertiseRefresh"
pb.CS_STORE_REFRESH_ADVERTISEREFRESH_FIELD.full_name = ".CS_Store_Refresh.AdvertiseRefresh"
pb.CS_STORE_REFRESH_ADVERTISEREFRESH_FIELD.number = 2
pb.CS_STORE_REFRESH_ADVERTISEREFRESH_FIELD.index = 1
pb.CS_STORE_REFRESH_ADVERTISEREFRESH_FIELD.label = 2
pb.CS_STORE_REFRESH_ADVERTISEREFRESH_FIELD.has_default_value = false
pb.CS_STORE_REFRESH_ADVERTISEREFRESH_FIELD.default_value = 0
pb.CS_STORE_REFRESH_ADVERTISEREFRESH_FIELD.type = 13
pb.CS_STORE_REFRESH_ADVERTISEREFRESH_FIELD.cpp_type = 3

pb.CS_STORE_REFRESH.name = "CS_Store_Refresh"
pb.CS_STORE_REFRESH.full_name = ".CS_Store_Refresh"
pb.CS_STORE_REFRESH.nested_types = {}
pb.CS_STORE_REFRESH.enum_types = {}
pb.CS_STORE_REFRESH.fields = {pb.CS_STORE_REFRESH_STOREID_FIELD, pb.CS_STORE_REFRESH_ADVERTISEREFRESH_FIELD}
pb.CS_STORE_REFRESH.is_extendable = false
pb.CS_STORE_REFRESH.extensions = {}
pb.SC_STORE_REFRESH_RESULT_FIELD.name = "Result"
pb.SC_STORE_REFRESH_RESULT_FIELD.full_name = ".SC_Store_Refresh.Result"
pb.SC_STORE_REFRESH_RESULT_FIELD.number = 1
pb.SC_STORE_REFRESH_RESULT_FIELD.index = 0
pb.SC_STORE_REFRESH_RESULT_FIELD.label = 2
pb.SC_STORE_REFRESH_RESULT_FIELD.has_default_value = false
pb.SC_STORE_REFRESH_RESULT_FIELD.default_value = 0
pb.SC_STORE_REFRESH_RESULT_FIELD.type = 13
pb.SC_STORE_REFRESH_RESULT_FIELD.cpp_type = 3

pb.SC_STORE_REFRESH.name = "SC_Store_Refresh"
pb.SC_STORE_REFRESH.full_name = ".SC_Store_Refresh"
pb.SC_STORE_REFRESH.nested_types = {}
pb.SC_STORE_REFRESH.enum_types = {}
pb.SC_STORE_REFRESH.fields = {pb.SC_STORE_REFRESH_RESULT_FIELD}
pb.SC_STORE_REFRESH.is_extendable = false
pb.SC_STORE_REFRESH.extensions = {}
pb.CS_MARKET_GETLIST_PAGEVALUE_FIELD.name = "PageValue"
pb.CS_MARKET_GETLIST_PAGEVALUE_FIELD.full_name = ".CS_Market_GetList.PageValue"
pb.CS_MARKET_GETLIST_PAGEVALUE_FIELD.number = 1
pb.CS_MARKET_GETLIST_PAGEVALUE_FIELD.index = 0
pb.CS_MARKET_GETLIST_PAGEVALUE_FIELD.label = 1
pb.CS_MARKET_GETLIST_PAGEVALUE_FIELD.has_default_value = false
pb.CS_MARKET_GETLIST_PAGEVALUE_FIELD.default_value = 0
pb.CS_MARKET_GETLIST_PAGEVALUE_FIELD.type = 5
pb.CS_MARKET_GETLIST_PAGEVALUE_FIELD.cpp_type = 1

pb.CS_MARKET_GETLIST_PAGESIZE_FIELD.name = "PageSize"
pb.CS_MARKET_GETLIST_PAGESIZE_FIELD.full_name = ".CS_Market_GetList.PageSize"
pb.CS_MARKET_GETLIST_PAGESIZE_FIELD.number = 2
pb.CS_MARKET_GETLIST_PAGESIZE_FIELD.index = 1
pb.CS_MARKET_GETLIST_PAGESIZE_FIELD.label = 1
pb.CS_MARKET_GETLIST_PAGESIZE_FIELD.has_default_value = false
pb.CS_MARKET_GETLIST_PAGESIZE_FIELD.default_value = 0
pb.CS_MARKET_GETLIST_PAGESIZE_FIELD.type = 5
pb.CS_MARKET_GETLIST_PAGESIZE_FIELD.cpp_type = 1

pb.CS_MARKET_GETLIST_MAINTYPE_FIELD.name = "MainType"
pb.CS_MARKET_GETLIST_MAINTYPE_FIELD.full_name = ".CS_Market_GetList.MainType"
pb.CS_MARKET_GETLIST_MAINTYPE_FIELD.number = 3
pb.CS_MARKET_GETLIST_MAINTYPE_FIELD.index = 2
pb.CS_MARKET_GETLIST_MAINTYPE_FIELD.label = 1
pb.CS_MARKET_GETLIST_MAINTYPE_FIELD.has_default_value = false
pb.CS_MARKET_GETLIST_MAINTYPE_FIELD.default_value = 0
pb.CS_MARKET_GETLIST_MAINTYPE_FIELD.type = 5
pb.CS_MARKET_GETLIST_MAINTYPE_FIELD.cpp_type = 1

pb.CS_MARKET_GETLIST_SUBTYPE_FIELD.name = "SubType"
pb.CS_MARKET_GETLIST_SUBTYPE_FIELD.full_name = ".CS_Market_GetList.SubType"
pb.CS_MARKET_GETLIST_SUBTYPE_FIELD.number = 4
pb.CS_MARKET_GETLIST_SUBTYPE_FIELD.index = 3
pb.CS_MARKET_GETLIST_SUBTYPE_FIELD.label = 1
pb.CS_MARKET_GETLIST_SUBTYPE_FIELD.has_default_value = false
pb.CS_MARKET_GETLIST_SUBTYPE_FIELD.default_value = 0
pb.CS_MARKET_GETLIST_SUBTYPE_FIELD.type = 5
pb.CS_MARKET_GETLIST_SUBTYPE_FIELD.cpp_type = 1

pb.CS_MARKET_GETLIST_SEARCHGOODSID_FIELD.name = "SearchGoodsID"
pb.CS_MARKET_GETLIST_SEARCHGOODSID_FIELD.full_name = ".CS_Market_GetList.SearchGoodsID"
pb.CS_MARKET_GETLIST_SEARCHGOODSID_FIELD.number = 5
pb.CS_MARKET_GETLIST_SEARCHGOODSID_FIELD.index = 4
pb.CS_MARKET_GETLIST_SEARCHGOODSID_FIELD.label = 1
pb.CS_MARKET_GETLIST_SEARCHGOODSID_FIELD.has_default_value = false
pb.CS_MARKET_GETLIST_SEARCHGOODSID_FIELD.default_value = 0
pb.CS_MARKET_GETLIST_SEARCHGOODSID_FIELD.type = 5
pb.CS_MARKET_GETLIST_SEARCHGOODSID_FIELD.cpp_type = 1

pb.CS_MARKET_GETLIST.name = "CS_Market_GetList"
pb.CS_MARKET_GETLIST.full_name = ".CS_Market_GetList"
pb.CS_MARKET_GETLIST.nested_types = {}
pb.CS_MARKET_GETLIST.enum_types = {}
pb.CS_MARKET_GETLIST.fields = {pb.CS_MARKET_GETLIST_PAGEVALUE_FIELD, pb.CS_MARKET_GETLIST_PAGESIZE_FIELD, pb.CS_MARKET_GETLIST_MAINTYPE_FIELD, pb.CS_MARKET_GETLIST_SUBTYPE_FIELD, pb.CS_MARKET_GETLIST_SEARCHGOODSID_FIELD}
pb.CS_MARKET_GETLIST.is_extendable = false
pb.CS_MARKET_GETLIST.extensions = {}
pb.SC_MARKET_GETLIST_ITEMDATA_SELLID_FIELD.name = "SellID"
pb.SC_MARKET_GETLIST_ITEMDATA_SELLID_FIELD.full_name = ".SC_Market_GetList.ItemData.SellID"
pb.SC_MARKET_GETLIST_ITEMDATA_SELLID_FIELD.number = 1
pb.SC_MARKET_GETLIST_ITEMDATA_SELLID_FIELD.index = 0
pb.SC_MARKET_GETLIST_ITEMDATA_SELLID_FIELD.label = 2
pb.SC_MARKET_GETLIST_ITEMDATA_SELLID_FIELD.has_default_value = false
pb.SC_MARKET_GETLIST_ITEMDATA_SELLID_FIELD.default_value = 0
pb.SC_MARKET_GETLIST_ITEMDATA_SELLID_FIELD.type = 5
pb.SC_MARKET_GETLIST_ITEMDATA_SELLID_FIELD.cpp_type = 1

pb.SC_MARKET_GETLIST_ITEMDATA_PRICE_FIELD.name = "Price"
pb.SC_MARKET_GETLIST_ITEMDATA_PRICE_FIELD.full_name = ".SC_Market_GetList.ItemData.Price"
pb.SC_MARKET_GETLIST_ITEMDATA_PRICE_FIELD.number = 2
pb.SC_MARKET_GETLIST_ITEMDATA_PRICE_FIELD.index = 1
pb.SC_MARKET_GETLIST_ITEMDATA_PRICE_FIELD.label = 2
pb.SC_MARKET_GETLIST_ITEMDATA_PRICE_FIELD.has_default_value = false
pb.SC_MARKET_GETLIST_ITEMDATA_PRICE_FIELD.default_value = 0
pb.SC_MARKET_GETLIST_ITEMDATA_PRICE_FIELD.type = 5
pb.SC_MARKET_GETLIST_ITEMDATA_PRICE_FIELD.cpp_type = 1

pb.SC_MARKET_GETLIST_ITEMDATA_NUMPROP_FIELD.name = "NumProp"
pb.SC_MARKET_GETLIST_ITEMDATA_NUMPROP_FIELD.full_name = ".SC_Market_GetList.ItemData.NumProp"
pb.SC_MARKET_GETLIST_ITEMDATA_NUMPROP_FIELD.number = 3
pb.SC_MARKET_GETLIST_ITEMDATA_NUMPROP_FIELD.index = 2
pb.SC_MARKET_GETLIST_ITEMDATA_NUMPROP_FIELD.label = 3
pb.SC_MARKET_GETLIST_ITEMDATA_NUMPROP_FIELD.has_default_value = false
pb.SC_MARKET_GETLIST_ITEMDATA_NUMPROP_FIELD.default_value = {}
pb.SC_MARKET_GETLIST_ITEMDATA_NUMPROP_FIELD.type = 5
pb.SC_MARKET_GETLIST_ITEMDATA_NUMPROP_FIELD.cpp_type = 1

pb.SC_MARKET_GETLIST_ITEMDATA_ENDTIME_FIELD.name = "EndTime"
pb.SC_MARKET_GETLIST_ITEMDATA_ENDTIME_FIELD.full_name = ".SC_Market_GetList.ItemData.EndTime"
pb.SC_MARKET_GETLIST_ITEMDATA_ENDTIME_FIELD.number = 4
pb.SC_MARKET_GETLIST_ITEMDATA_ENDTIME_FIELD.index = 3
pb.SC_MARKET_GETLIST_ITEMDATA_ENDTIME_FIELD.label = 2
pb.SC_MARKET_GETLIST_ITEMDATA_ENDTIME_FIELD.has_default_value = false
pb.SC_MARKET_GETLIST_ITEMDATA_ENDTIME_FIELD.default_value = 0
pb.SC_MARKET_GETLIST_ITEMDATA_ENDTIME_FIELD.type = 5
pb.SC_MARKET_GETLIST_ITEMDATA_ENDTIME_FIELD.cpp_type = 1

pb.SC_MARKET_GETLIST_ITEMDATA.name = "ItemData"
pb.SC_MARKET_GETLIST_ITEMDATA.full_name = ".SC_Market_GetList.ItemData"
pb.SC_MARKET_GETLIST_ITEMDATA.nested_types = {}
pb.SC_MARKET_GETLIST_ITEMDATA.enum_types = {}
pb.SC_MARKET_GETLIST_ITEMDATA.fields = {pb.SC_MARKET_GETLIST_ITEMDATA_SELLID_FIELD, pb.SC_MARKET_GETLIST_ITEMDATA_PRICE_FIELD, pb.SC_MARKET_GETLIST_ITEMDATA_NUMPROP_FIELD, pb.SC_MARKET_GETLIST_ITEMDATA_ENDTIME_FIELD}
pb.SC_MARKET_GETLIST_ITEMDATA.is_extendable = false
pb.SC_MARKET_GETLIST_ITEMDATA.extensions = {}
pb.SC_MARKET_GETLIST_ITEMDATA.containing_type = pb.SC_MARKET_GETLIST
pb.SC_MARKET_GETLIST_PAGEVALUE_FIELD.name = "PageValue"
pb.SC_MARKET_GETLIST_PAGEVALUE_FIELD.full_name = ".SC_Market_GetList.PageValue"
pb.SC_MARKET_GETLIST_PAGEVALUE_FIELD.number = 1
pb.SC_MARKET_GETLIST_PAGEVALUE_FIELD.index = 0
pb.SC_MARKET_GETLIST_PAGEVALUE_FIELD.label = 1
pb.SC_MARKET_GETLIST_PAGEVALUE_FIELD.has_default_value = false
pb.SC_MARKET_GETLIST_PAGEVALUE_FIELD.default_value = 0
pb.SC_MARKET_GETLIST_PAGEVALUE_FIELD.type = 5
pb.SC_MARKET_GETLIST_PAGEVALUE_FIELD.cpp_type = 1

pb.SC_MARKET_GETLIST_TOTALITEMCOUNT_FIELD.name = "TotalItemCount"
pb.SC_MARKET_GETLIST_TOTALITEMCOUNT_FIELD.full_name = ".SC_Market_GetList.TotalItemCount"
pb.SC_MARKET_GETLIST_TOTALITEMCOUNT_FIELD.number = 2
pb.SC_MARKET_GETLIST_TOTALITEMCOUNT_FIELD.index = 1
pb.SC_MARKET_GETLIST_TOTALITEMCOUNT_FIELD.label = 1
pb.SC_MARKET_GETLIST_TOTALITEMCOUNT_FIELD.has_default_value = false
pb.SC_MARKET_GETLIST_TOTALITEMCOUNT_FIELD.default_value = 0
pb.SC_MARKET_GETLIST_TOTALITEMCOUNT_FIELD.type = 5
pb.SC_MARKET_GETLIST_TOTALITEMCOUNT_FIELD.cpp_type = 1

pb.SC_MARKET_GETLIST_MAINTYPE_FIELD.name = "MainType"
pb.SC_MARKET_GETLIST_MAINTYPE_FIELD.full_name = ".SC_Market_GetList.MainType"
pb.SC_MARKET_GETLIST_MAINTYPE_FIELD.number = 3
pb.SC_MARKET_GETLIST_MAINTYPE_FIELD.index = 2
pb.SC_MARKET_GETLIST_MAINTYPE_FIELD.label = 1
pb.SC_MARKET_GETLIST_MAINTYPE_FIELD.has_default_value = false
pb.SC_MARKET_GETLIST_MAINTYPE_FIELD.default_value = 0
pb.SC_MARKET_GETLIST_MAINTYPE_FIELD.type = 5
pb.SC_MARKET_GETLIST_MAINTYPE_FIELD.cpp_type = 1

pb.SC_MARKET_GETLIST_SUBTYPE_FIELD.name = "SubType"
pb.SC_MARKET_GETLIST_SUBTYPE_FIELD.full_name = ".SC_Market_GetList.SubType"
pb.SC_MARKET_GETLIST_SUBTYPE_FIELD.number = 4
pb.SC_MARKET_GETLIST_SUBTYPE_FIELD.index = 3
pb.SC_MARKET_GETLIST_SUBTYPE_FIELD.label = 1
pb.SC_MARKET_GETLIST_SUBTYPE_FIELD.has_default_value = false
pb.SC_MARKET_GETLIST_SUBTYPE_FIELD.default_value = 0
pb.SC_MARKET_GETLIST_SUBTYPE_FIELD.type = 5
pb.SC_MARKET_GETLIST_SUBTYPE_FIELD.cpp_type = 1

pb.SC_MARKET_GETLIST_SEARCHGOODSID_FIELD.name = "SearchGoodsID"
pb.SC_MARKET_GETLIST_SEARCHGOODSID_FIELD.full_name = ".SC_Market_GetList.SearchGoodsID"
pb.SC_MARKET_GETLIST_SEARCHGOODSID_FIELD.number = 5
pb.SC_MARKET_GETLIST_SEARCHGOODSID_FIELD.index = 4
pb.SC_MARKET_GETLIST_SEARCHGOODSID_FIELD.label = 1
pb.SC_MARKET_GETLIST_SEARCHGOODSID_FIELD.has_default_value = false
pb.SC_MARKET_GETLIST_SEARCHGOODSID_FIELD.default_value = 0
pb.SC_MARKET_GETLIST_SEARCHGOODSID_FIELD.type = 5
pb.SC_MARKET_GETLIST_SEARCHGOODSID_FIELD.cpp_type = 1

pb.SC_MARKET_GETLIST_ITEMLIST_FIELD.name = "ItemList"
pb.SC_MARKET_GETLIST_ITEMLIST_FIELD.full_name = ".SC_Market_GetList.ItemList"
pb.SC_MARKET_GETLIST_ITEMLIST_FIELD.number = 6
pb.SC_MARKET_GETLIST_ITEMLIST_FIELD.index = 5
pb.SC_MARKET_GETLIST_ITEMLIST_FIELD.label = 3
pb.SC_MARKET_GETLIST_ITEMLIST_FIELD.has_default_value = false
pb.SC_MARKET_GETLIST_ITEMLIST_FIELD.default_value = {}
pb.SC_MARKET_GETLIST_ITEMLIST_FIELD.message_type = pb.SC_MARKET_GETLIST_ITEMDATA
pb.SC_MARKET_GETLIST_ITEMLIST_FIELD.type = 11
pb.SC_MARKET_GETLIST_ITEMLIST_FIELD.cpp_type = 10

pb.SC_MARKET_GETLIST.name = "SC_Market_GetList"
pb.SC_MARKET_GETLIST.full_name = ".SC_Market_GetList"
pb.SC_MARKET_GETLIST.nested_types = {pb.SC_MARKET_GETLIST_ITEMDATA}
pb.SC_MARKET_GETLIST.enum_types = {}
pb.SC_MARKET_GETLIST.fields = {pb.SC_MARKET_GETLIST_PAGEVALUE_FIELD, pb.SC_MARKET_GETLIST_TOTALITEMCOUNT_FIELD, pb.SC_MARKET_GETLIST_MAINTYPE_FIELD, pb.SC_MARKET_GETLIST_SUBTYPE_FIELD, pb.SC_MARKET_GETLIST_SEARCHGOODSID_FIELD, pb.SC_MARKET_GETLIST_ITEMLIST_FIELD}
pb.SC_MARKET_GETLIST.is_extendable = false
pb.SC_MARKET_GETLIST.extensions = {}
pb.CS_MARKET_GETBOOTH_ACTORID_FIELD.name = "ActorID"
pb.CS_MARKET_GETBOOTH_ACTORID_FIELD.full_name = ".CS_Market_GetBooth.ActorID"
pb.CS_MARKET_GETBOOTH_ACTORID_FIELD.number = 1
pb.CS_MARKET_GETBOOTH_ACTORID_FIELD.index = 0
pb.CS_MARKET_GETBOOTH_ACTORID_FIELD.label = 2
pb.CS_MARKET_GETBOOTH_ACTORID_FIELD.has_default_value = false
pb.CS_MARKET_GETBOOTH_ACTORID_FIELD.default_value = 0
pb.CS_MARKET_GETBOOTH_ACTORID_FIELD.type = 13
pb.CS_MARKET_GETBOOTH_ACTORID_FIELD.cpp_type = 3

pb.CS_MARKET_GETBOOTH.name = "CS_Market_GetBooth"
pb.CS_MARKET_GETBOOTH.full_name = ".CS_Market_GetBooth"
pb.CS_MARKET_GETBOOTH.nested_types = {}
pb.CS_MARKET_GETBOOTH.enum_types = {}
pb.CS_MARKET_GETBOOTH.fields = {pb.CS_MARKET_GETBOOTH_ACTORID_FIELD}
pb.CS_MARKET_GETBOOTH.is_extendable = false
pb.CS_MARKET_GETBOOTH.extensions = {}
pb.SC_MARKET_BOOTH_ITEMDATA_SELLID_FIELD.name = "SellID"
pb.SC_MARKET_BOOTH_ITEMDATA_SELLID_FIELD.full_name = ".SC_Market_Booth.ItemData.SellID"
pb.SC_MARKET_BOOTH_ITEMDATA_SELLID_FIELD.number = 1
pb.SC_MARKET_BOOTH_ITEMDATA_SELLID_FIELD.index = 0
pb.SC_MARKET_BOOTH_ITEMDATA_SELLID_FIELD.label = 2
pb.SC_MARKET_BOOTH_ITEMDATA_SELLID_FIELD.has_default_value = false
pb.SC_MARKET_BOOTH_ITEMDATA_SELLID_FIELD.default_value = 0
pb.SC_MARKET_BOOTH_ITEMDATA_SELLID_FIELD.type = 13
pb.SC_MARKET_BOOTH_ITEMDATA_SELLID_FIELD.cpp_type = 3

pb.SC_MARKET_BOOTH_ITEMDATA_GOODSID_FIELD.name = "GoodsID"
pb.SC_MARKET_BOOTH_ITEMDATA_GOODSID_FIELD.full_name = ".SC_Market_Booth.ItemData.GoodsID"
pb.SC_MARKET_BOOTH_ITEMDATA_GOODSID_FIELD.number = 2
pb.SC_MARKET_BOOTH_ITEMDATA_GOODSID_FIELD.index = 1
pb.SC_MARKET_BOOTH_ITEMDATA_GOODSID_FIELD.label = 2
pb.SC_MARKET_BOOTH_ITEMDATA_GOODSID_FIELD.has_default_value = false
pb.SC_MARKET_BOOTH_ITEMDATA_GOODSID_FIELD.default_value = 0
pb.SC_MARKET_BOOTH_ITEMDATA_GOODSID_FIELD.type = 13
pb.SC_MARKET_BOOTH_ITEMDATA_GOODSID_FIELD.cpp_type = 3

pb.SC_MARKET_BOOTH_ITEMDATA_GOODSNUM_FIELD.name = "GoodsNum"
pb.SC_MARKET_BOOTH_ITEMDATA_GOODSNUM_FIELD.full_name = ".SC_Market_Booth.ItemData.GoodsNum"
pb.SC_MARKET_BOOTH_ITEMDATA_GOODSNUM_FIELD.number = 3
pb.SC_MARKET_BOOTH_ITEMDATA_GOODSNUM_FIELD.index = 2
pb.SC_MARKET_BOOTH_ITEMDATA_GOODSNUM_FIELD.label = 2
pb.SC_MARKET_BOOTH_ITEMDATA_GOODSNUM_FIELD.has_default_value = false
pb.SC_MARKET_BOOTH_ITEMDATA_GOODSNUM_FIELD.default_value = 0
pb.SC_MARKET_BOOTH_ITEMDATA_GOODSNUM_FIELD.type = 13
pb.SC_MARKET_BOOTH_ITEMDATA_GOODSNUM_FIELD.cpp_type = 3

pb.SC_MARKET_BOOTH_ITEMDATA_PRICE_FIELD.name = "Price"
pb.SC_MARKET_BOOTH_ITEMDATA_PRICE_FIELD.full_name = ".SC_Market_Booth.ItemData.Price"
pb.SC_MARKET_BOOTH_ITEMDATA_PRICE_FIELD.number = 4
pb.SC_MARKET_BOOTH_ITEMDATA_PRICE_FIELD.index = 3
pb.SC_MARKET_BOOTH_ITEMDATA_PRICE_FIELD.label = 2
pb.SC_MARKET_BOOTH_ITEMDATA_PRICE_FIELD.has_default_value = false
pb.SC_MARKET_BOOTH_ITEMDATA_PRICE_FIELD.default_value = 0
pb.SC_MARKET_BOOTH_ITEMDATA_PRICE_FIELD.type = 13
pb.SC_MARKET_BOOTH_ITEMDATA_PRICE_FIELD.cpp_type = 3

pb.SC_MARKET_BOOTH_ITEMDATA_NUMPROP_FIELD.name = "NumProp"
pb.SC_MARKET_BOOTH_ITEMDATA_NUMPROP_FIELD.full_name = ".SC_Market_Booth.ItemData.NumProp"
pb.SC_MARKET_BOOTH_ITEMDATA_NUMPROP_FIELD.number = 5
pb.SC_MARKET_BOOTH_ITEMDATA_NUMPROP_FIELD.index = 4
pb.SC_MARKET_BOOTH_ITEMDATA_NUMPROP_FIELD.label = 3
pb.SC_MARKET_BOOTH_ITEMDATA_NUMPROP_FIELD.has_default_value = false
pb.SC_MARKET_BOOTH_ITEMDATA_NUMPROP_FIELD.default_value = {}
pb.SC_MARKET_BOOTH_ITEMDATA_NUMPROP_FIELD.type = 5
pb.SC_MARKET_BOOTH_ITEMDATA_NUMPROP_FIELD.cpp_type = 1

pb.SC_MARKET_BOOTH_ITEMDATA.name = "ItemData"
pb.SC_MARKET_BOOTH_ITEMDATA.full_name = ".SC_Market_Booth.ItemData"
pb.SC_MARKET_BOOTH_ITEMDATA.nested_types = {}
pb.SC_MARKET_BOOTH_ITEMDATA.enum_types = {}
pb.SC_MARKET_BOOTH_ITEMDATA.fields = {pb.SC_MARKET_BOOTH_ITEMDATA_SELLID_FIELD, pb.SC_MARKET_BOOTH_ITEMDATA_GOODSID_FIELD, pb.SC_MARKET_BOOTH_ITEMDATA_GOODSNUM_FIELD, pb.SC_MARKET_BOOTH_ITEMDATA_PRICE_FIELD, pb.SC_MARKET_BOOTH_ITEMDATA_NUMPROP_FIELD}
pb.SC_MARKET_BOOTH_ITEMDATA.is_extendable = false
pb.SC_MARKET_BOOTH_ITEMDATA.extensions = {}
pb.SC_MARKET_BOOTH_ITEMDATA.containing_type = pb.SC_MARKET_BOOTH
pb.SC_MARKET_BOOTH_ACTORID_FIELD.name = "ActorID"
pb.SC_MARKET_BOOTH_ACTORID_FIELD.full_name = ".SC_Market_Booth.ActorID"
pb.SC_MARKET_BOOTH_ACTORID_FIELD.number = 1
pb.SC_MARKET_BOOTH_ACTORID_FIELD.index = 0
pb.SC_MARKET_BOOTH_ACTORID_FIELD.label = 2
pb.SC_MARKET_BOOTH_ACTORID_FIELD.has_default_value = false
pb.SC_MARKET_BOOTH_ACTORID_FIELD.default_value = 0
pb.SC_MARKET_BOOTH_ACTORID_FIELD.type = 13
pb.SC_MARKET_BOOTH_ACTORID_FIELD.cpp_type = 3

pb.SC_MARKET_BOOTH_BOOTHNAME_FIELD.name = "BoothName"
pb.SC_MARKET_BOOTH_BOOTHNAME_FIELD.full_name = ".SC_Market_Booth.BoothName"
pb.SC_MARKET_BOOTH_BOOTHNAME_FIELD.number = 2
pb.SC_MARKET_BOOTH_BOOTHNAME_FIELD.index = 1
pb.SC_MARKET_BOOTH_BOOTHNAME_FIELD.label = 2
pb.SC_MARKET_BOOTH_BOOTHNAME_FIELD.has_default_value = false
pb.SC_MARKET_BOOTH_BOOTHNAME_FIELD.default_value = ""
pb.SC_MARKET_BOOTH_BOOTHNAME_FIELD.type = 9
pb.SC_MARKET_BOOTH_BOOTHNAME_FIELD.cpp_type = 9

pb.SC_MARKET_BOOTH_ITEMLIST_FIELD.name = "ItemList"
pb.SC_MARKET_BOOTH_ITEMLIST_FIELD.full_name = ".SC_Market_Booth.ItemList"
pb.SC_MARKET_BOOTH_ITEMLIST_FIELD.number = 3
pb.SC_MARKET_BOOTH_ITEMLIST_FIELD.index = 2
pb.SC_MARKET_BOOTH_ITEMLIST_FIELD.label = 3
pb.SC_MARKET_BOOTH_ITEMLIST_FIELD.has_default_value = false
pb.SC_MARKET_BOOTH_ITEMLIST_FIELD.default_value = {}
pb.SC_MARKET_BOOTH_ITEMLIST_FIELD.message_type = pb.SC_MARKET_BOOTH_ITEMDATA
pb.SC_MARKET_BOOTH_ITEMLIST_FIELD.type = 11
pb.SC_MARKET_BOOTH_ITEMLIST_FIELD.cpp_type = 10

pb.SC_MARKET_BOOTH.name = "SC_Market_Booth"
pb.SC_MARKET_BOOTH.full_name = ".SC_Market_Booth"
pb.SC_MARKET_BOOTH.nested_types = {pb.SC_MARKET_BOOTH_ITEMDATA}
pb.SC_MARKET_BOOTH.enum_types = {}
pb.SC_MARKET_BOOTH.fields = {pb.SC_MARKET_BOOTH_ACTORID_FIELD, pb.SC_MARKET_BOOTH_BOOTHNAME_FIELD, pb.SC_MARKET_BOOTH_ITEMLIST_FIELD}
pb.SC_MARKET_BOOTH.is_extendable = false
pb.SC_MARKET_BOOTH.extensions = {}
pb.CS_MARKET_GETSELFLIST.name = "CS_Market_GetSelfList"
pb.CS_MARKET_GETSELFLIST.full_name = ".CS_Market_GetSelfList"
pb.CS_MARKET_GETSELFLIST.nested_types = {}
pb.CS_MARKET_GETSELFLIST.enum_types = {}
pb.CS_MARKET_GETSELFLIST.fields = {}
pb.CS_MARKET_GETSELFLIST.is_extendable = false
pb.CS_MARKET_GETSELFLIST.extensions = {}
pb.SC_MARKET_GETSELFLIST_ITEMDATA_SELLID_FIELD.name = "SellID"
pb.SC_MARKET_GETSELFLIST_ITEMDATA_SELLID_FIELD.full_name = ".SC_Market_GetSelfList.ItemData.SellID"
pb.SC_MARKET_GETSELFLIST_ITEMDATA_SELLID_FIELD.number = 1
pb.SC_MARKET_GETSELFLIST_ITEMDATA_SELLID_FIELD.index = 0
pb.SC_MARKET_GETSELFLIST_ITEMDATA_SELLID_FIELD.label = 2
pb.SC_MARKET_GETSELFLIST_ITEMDATA_SELLID_FIELD.has_default_value = false
pb.SC_MARKET_GETSELFLIST_ITEMDATA_SELLID_FIELD.default_value = 0
pb.SC_MARKET_GETSELFLIST_ITEMDATA_SELLID_FIELD.type = 5
pb.SC_MARKET_GETSELFLIST_ITEMDATA_SELLID_FIELD.cpp_type = 1

pb.SC_MARKET_GETSELFLIST_ITEMDATA_PRICE_FIELD.name = "Price"
pb.SC_MARKET_GETSELFLIST_ITEMDATA_PRICE_FIELD.full_name = ".SC_Market_GetSelfList.ItemData.Price"
pb.SC_MARKET_GETSELFLIST_ITEMDATA_PRICE_FIELD.number = 2
pb.SC_MARKET_GETSELFLIST_ITEMDATA_PRICE_FIELD.index = 1
pb.SC_MARKET_GETSELFLIST_ITEMDATA_PRICE_FIELD.label = 2
pb.SC_MARKET_GETSELFLIST_ITEMDATA_PRICE_FIELD.has_default_value = false
pb.SC_MARKET_GETSELFLIST_ITEMDATA_PRICE_FIELD.default_value = 0
pb.SC_MARKET_GETSELFLIST_ITEMDATA_PRICE_FIELD.type = 5
pb.SC_MARKET_GETSELFLIST_ITEMDATA_PRICE_FIELD.cpp_type = 1

pb.SC_MARKET_GETSELFLIST_ITEMDATA_ENDTIME_FIELD.name = "EndTime"
pb.SC_MARKET_GETSELFLIST_ITEMDATA_ENDTIME_FIELD.full_name = ".SC_Market_GetSelfList.ItemData.EndTime"
pb.SC_MARKET_GETSELFLIST_ITEMDATA_ENDTIME_FIELD.number = 3
pb.SC_MARKET_GETSELFLIST_ITEMDATA_ENDTIME_FIELD.index = 2
pb.SC_MARKET_GETSELFLIST_ITEMDATA_ENDTIME_FIELD.label = 2
pb.SC_MARKET_GETSELFLIST_ITEMDATA_ENDTIME_FIELD.has_default_value = false
pb.SC_MARKET_GETSELFLIST_ITEMDATA_ENDTIME_FIELD.default_value = 0
pb.SC_MARKET_GETSELFLIST_ITEMDATA_ENDTIME_FIELD.type = 5
pb.SC_MARKET_GETSELFLIST_ITEMDATA_ENDTIME_FIELD.cpp_type = 1

pb.SC_MARKET_GETSELFLIST_ITEMDATA_NUMPROP_FIELD.name = "NumProp"
pb.SC_MARKET_GETSELFLIST_ITEMDATA_NUMPROP_FIELD.full_name = ".SC_Market_GetSelfList.ItemData.NumProp"
pb.SC_MARKET_GETSELFLIST_ITEMDATA_NUMPROP_FIELD.number = 4
pb.SC_MARKET_GETSELFLIST_ITEMDATA_NUMPROP_FIELD.index = 3
pb.SC_MARKET_GETSELFLIST_ITEMDATA_NUMPROP_FIELD.label = 3
pb.SC_MARKET_GETSELFLIST_ITEMDATA_NUMPROP_FIELD.has_default_value = false
pb.SC_MARKET_GETSELFLIST_ITEMDATA_NUMPROP_FIELD.default_value = {}
pb.SC_MARKET_GETSELFLIST_ITEMDATA_NUMPROP_FIELD.type = 5
pb.SC_MARKET_GETSELFLIST_ITEMDATA_NUMPROP_FIELD.cpp_type = 1

pb.SC_MARKET_GETSELFLIST_ITEMDATA.name = "ItemData"
pb.SC_MARKET_GETSELFLIST_ITEMDATA.full_name = ".SC_Market_GetSelfList.ItemData"
pb.SC_MARKET_GETSELFLIST_ITEMDATA.nested_types = {}
pb.SC_MARKET_GETSELFLIST_ITEMDATA.enum_types = {}
pb.SC_MARKET_GETSELFLIST_ITEMDATA.fields = {pb.SC_MARKET_GETSELFLIST_ITEMDATA_SELLID_FIELD, pb.SC_MARKET_GETSELFLIST_ITEMDATA_PRICE_FIELD, pb.SC_MARKET_GETSELFLIST_ITEMDATA_ENDTIME_FIELD, pb.SC_MARKET_GETSELFLIST_ITEMDATA_NUMPROP_FIELD}
pb.SC_MARKET_GETSELFLIST_ITEMDATA.is_extendable = false
pb.SC_MARKET_GETSELFLIST_ITEMDATA.extensions = {}
pb.SC_MARKET_GETSELFLIST_ITEMDATA.containing_type = pb.SC_MARKET_GETSELFLIST
pb.SC_MARKET_GETSELFLIST_ITEMLIST_FIELD.name = "ItemList"
pb.SC_MARKET_GETSELFLIST_ITEMLIST_FIELD.full_name = ".SC_Market_GetSelfList.ItemList"
pb.SC_MARKET_GETSELFLIST_ITEMLIST_FIELD.number = 1
pb.SC_MARKET_GETSELFLIST_ITEMLIST_FIELD.index = 0
pb.SC_MARKET_GETSELFLIST_ITEMLIST_FIELD.label = 3
pb.SC_MARKET_GETSELFLIST_ITEMLIST_FIELD.has_default_value = false
pb.SC_MARKET_GETSELFLIST_ITEMLIST_FIELD.default_value = {}
pb.SC_MARKET_GETSELFLIST_ITEMLIST_FIELD.message_type = pb.SC_MARKET_GETSELFLIST_ITEMDATA
pb.SC_MARKET_GETSELFLIST_ITEMLIST_FIELD.type = 11
pb.SC_MARKET_GETSELFLIST_ITEMLIST_FIELD.cpp_type = 10

pb.SC_MARKET_GETSELFLIST.name = "SC_Market_GetSelfList"
pb.SC_MARKET_GETSELFLIST.full_name = ".SC_Market_GetSelfList"
pb.SC_MARKET_GETSELFLIST.nested_types = {pb.SC_MARKET_GETSELFLIST_ITEMDATA}
pb.SC_MARKET_GETSELFLIST.enum_types = {}
pb.SC_MARKET_GETSELFLIST.fields = {pb.SC_MARKET_GETSELFLIST_ITEMLIST_FIELD}
pb.SC_MARKET_GETSELFLIST.is_extendable = false
pb.SC_MARKET_GETSELFLIST.extensions = {}
pb.CS_STORE_GETITEMPRICE_STOREID_FIELD.name = "StoreID"
pb.CS_STORE_GETITEMPRICE_STOREID_FIELD.full_name = ".CS_Store_GetItemPrice.StoreID"
pb.CS_STORE_GETITEMPRICE_STOREID_FIELD.number = 1
pb.CS_STORE_GETITEMPRICE_STOREID_FIELD.index = 0
pb.CS_STORE_GETITEMPRICE_STOREID_FIELD.label = 2
pb.CS_STORE_GETITEMPRICE_STOREID_FIELD.has_default_value = false
pb.CS_STORE_GETITEMPRICE_STOREID_FIELD.default_value = 0
pb.CS_STORE_GETITEMPRICE_STOREID_FIELD.type = 5
pb.CS_STORE_GETITEMPRICE_STOREID_FIELD.cpp_type = 1

pb.CS_STORE_GETITEMPRICE_ITEMID_FIELD.name = "ItemID"
pb.CS_STORE_GETITEMPRICE_ITEMID_FIELD.full_name = ".CS_Store_GetItemPrice.ItemID"
pb.CS_STORE_GETITEMPRICE_ITEMID_FIELD.number = 2
pb.CS_STORE_GETITEMPRICE_ITEMID_FIELD.index = 1
pb.CS_STORE_GETITEMPRICE_ITEMID_FIELD.label = 2
pb.CS_STORE_GETITEMPRICE_ITEMID_FIELD.has_default_value = false
pb.CS_STORE_GETITEMPRICE_ITEMID_FIELD.default_value = 0
pb.CS_STORE_GETITEMPRICE_ITEMID_FIELD.type = 5
pb.CS_STORE_GETITEMPRICE_ITEMID_FIELD.cpp_type = 1

pb.CS_STORE_GETITEMPRICE.name = "CS_Store_GetItemPrice"
pb.CS_STORE_GETITEMPRICE.full_name = ".CS_Store_GetItemPrice"
pb.CS_STORE_GETITEMPRICE.nested_types = {}
pb.CS_STORE_GETITEMPRICE.enum_types = {}
pb.CS_STORE_GETITEMPRICE.fields = {pb.CS_STORE_GETITEMPRICE_STOREID_FIELD, pb.CS_STORE_GETITEMPRICE_ITEMID_FIELD}
pb.CS_STORE_GETITEMPRICE.is_extendable = false
pb.CS_STORE_GETITEMPRICE.extensions = {}
pb.SC_STORE_GETITEMPRICE_STOREID_FIELD.name = "StoreID"
pb.SC_STORE_GETITEMPRICE_STOREID_FIELD.full_name = ".SC_Store_GetItemPrice.StoreID"
pb.SC_STORE_GETITEMPRICE_STOREID_FIELD.number = 1
pb.SC_STORE_GETITEMPRICE_STOREID_FIELD.index = 0
pb.SC_STORE_GETITEMPRICE_STOREID_FIELD.label = 2
pb.SC_STORE_GETITEMPRICE_STOREID_FIELD.has_default_value = false
pb.SC_STORE_GETITEMPRICE_STOREID_FIELD.default_value = 0
pb.SC_STORE_GETITEMPRICE_STOREID_FIELD.type = 5
pb.SC_STORE_GETITEMPRICE_STOREID_FIELD.cpp_type = 1

pb.SC_STORE_GETITEMPRICE_ITEMID_FIELD.name = "ItemID"
pb.SC_STORE_GETITEMPRICE_ITEMID_FIELD.full_name = ".SC_Store_GetItemPrice.ItemID"
pb.SC_STORE_GETITEMPRICE_ITEMID_FIELD.number = 2
pb.SC_STORE_GETITEMPRICE_ITEMID_FIELD.index = 1
pb.SC_STORE_GETITEMPRICE_ITEMID_FIELD.label = 2
pb.SC_STORE_GETITEMPRICE_ITEMID_FIELD.has_default_value = false
pb.SC_STORE_GETITEMPRICE_ITEMID_FIELD.default_value = 0
pb.SC_STORE_GETITEMPRICE_ITEMID_FIELD.type = 5
pb.SC_STORE_GETITEMPRICE_ITEMID_FIELD.cpp_type = 1

pb.SC_STORE_GETITEMPRICE_GOODSID_FIELD.name = "GoodsID"
pb.SC_STORE_GETITEMPRICE_GOODSID_FIELD.full_name = ".SC_Store_GetItemPrice.GoodsID"
pb.SC_STORE_GETITEMPRICE_GOODSID_FIELD.number = 3
pb.SC_STORE_GETITEMPRICE_GOODSID_FIELD.index = 2
pb.SC_STORE_GETITEMPRICE_GOODSID_FIELD.label = 2
pb.SC_STORE_GETITEMPRICE_GOODSID_FIELD.has_default_value = false
pb.SC_STORE_GETITEMPRICE_GOODSID_FIELD.default_value = 0
pb.SC_STORE_GETITEMPRICE_GOODSID_FIELD.type = 5
pb.SC_STORE_GETITEMPRICE_GOODSID_FIELD.cpp_type = 1

pb.SC_STORE_GETITEMPRICE_GOODSNUM_FIELD.name = "GoodsNum"
pb.SC_STORE_GETITEMPRICE_GOODSNUM_FIELD.full_name = ".SC_Store_GetItemPrice.GoodsNum"
pb.SC_STORE_GETITEMPRICE_GOODSNUM_FIELD.number = 4
pb.SC_STORE_GETITEMPRICE_GOODSNUM_FIELD.index = 3
pb.SC_STORE_GETITEMPRICE_GOODSNUM_FIELD.label = 2
pb.SC_STORE_GETITEMPRICE_GOODSNUM_FIELD.has_default_value = false
pb.SC_STORE_GETITEMPRICE_GOODSNUM_FIELD.default_value = 0
pb.SC_STORE_GETITEMPRICE_GOODSNUM_FIELD.type = 5
pb.SC_STORE_GETITEMPRICE_GOODSNUM_FIELD.cpp_type = 1

pb.SC_STORE_GETITEMPRICE_COSTTYPE_FIELD.name = "CostType"
pb.SC_STORE_GETITEMPRICE_COSTTYPE_FIELD.full_name = ".SC_Store_GetItemPrice.CostType"
pb.SC_STORE_GETITEMPRICE_COSTTYPE_FIELD.number = 5
pb.SC_STORE_GETITEMPRICE_COSTTYPE_FIELD.index = 4
pb.SC_STORE_GETITEMPRICE_COSTTYPE_FIELD.label = 2
pb.SC_STORE_GETITEMPRICE_COSTTYPE_FIELD.has_default_value = false
pb.SC_STORE_GETITEMPRICE_COSTTYPE_FIELD.default_value = 0
pb.SC_STORE_GETITEMPRICE_COSTTYPE_FIELD.type = 5
pb.SC_STORE_GETITEMPRICE_COSTTYPE_FIELD.cpp_type = 1

pb.SC_STORE_GETITEMPRICE_COSTNUM_FIELD.name = "CostNum"
pb.SC_STORE_GETITEMPRICE_COSTNUM_FIELD.full_name = ".SC_Store_GetItemPrice.CostNum"
pb.SC_STORE_GETITEMPRICE_COSTNUM_FIELD.number = 6
pb.SC_STORE_GETITEMPRICE_COSTNUM_FIELD.index = 5
pb.SC_STORE_GETITEMPRICE_COSTNUM_FIELD.label = 2
pb.SC_STORE_GETITEMPRICE_COSTNUM_FIELD.has_default_value = false
pb.SC_STORE_GETITEMPRICE_COSTNUM_FIELD.default_value = 0
pb.SC_STORE_GETITEMPRICE_COSTNUM_FIELD.type = 5
pb.SC_STORE_GETITEMPRICE_COSTNUM_FIELD.cpp_type = 1

pb.SC_STORE_GETITEMPRICE.name = "SC_Store_GetItemPrice"
pb.SC_STORE_GETITEMPRICE.full_name = ".SC_Store_GetItemPrice"
pb.SC_STORE_GETITEMPRICE.nested_types = {}
pb.SC_STORE_GETITEMPRICE.enum_types = {}
pb.SC_STORE_GETITEMPRICE.fields = {pb.SC_STORE_GETITEMPRICE_STOREID_FIELD, pb.SC_STORE_GETITEMPRICE_ITEMID_FIELD, pb.SC_STORE_GETITEMPRICE_GOODSID_FIELD, pb.SC_STORE_GETITEMPRICE_GOODSNUM_FIELD, pb.SC_STORE_GETITEMPRICE_COSTTYPE_FIELD, pb.SC_STORE_GETITEMPRICE_COSTNUM_FIELD}
pb.SC_STORE_GETITEMPRICE.is_extendable = false
pb.SC_STORE_GETITEMPRICE.extensions = {}
pb.CS_STORE_MARKET_ADDSELLITEM_GOODSUID_FIELD.name = "GoodsUID"
pb.CS_STORE_MARKET_ADDSELLITEM_GOODSUID_FIELD.full_name = ".CS_Store_Market_AddSellItem.GoodsUID"
pb.CS_STORE_MARKET_ADDSELLITEM_GOODSUID_FIELD.number = 1
pb.CS_STORE_MARKET_ADDSELLITEM_GOODSUID_FIELD.index = 0
pb.CS_STORE_MARKET_ADDSELLITEM_GOODSUID_FIELD.label = 1
pb.CS_STORE_MARKET_ADDSELLITEM_GOODSUID_FIELD.has_default_value = false
pb.CS_STORE_MARKET_ADDSELLITEM_GOODSUID_FIELD.default_value = 0
pb.CS_STORE_MARKET_ADDSELLITEM_GOODSUID_FIELD.type = 4
pb.CS_STORE_MARKET_ADDSELLITEM_GOODSUID_FIELD.cpp_type = 4

pb.CS_STORE_MARKET_ADDSELLITEM_GOODSNUM_FIELD.name = "GoodsNum"
pb.CS_STORE_MARKET_ADDSELLITEM_GOODSNUM_FIELD.full_name = ".CS_Store_Market_AddSellItem.GoodsNum"
pb.CS_STORE_MARKET_ADDSELLITEM_GOODSNUM_FIELD.number = 2
pb.CS_STORE_MARKET_ADDSELLITEM_GOODSNUM_FIELD.index = 1
pb.CS_STORE_MARKET_ADDSELLITEM_GOODSNUM_FIELD.label = 1
pb.CS_STORE_MARKET_ADDSELLITEM_GOODSNUM_FIELD.has_default_value = false
pb.CS_STORE_MARKET_ADDSELLITEM_GOODSNUM_FIELD.default_value = 0
pb.CS_STORE_MARKET_ADDSELLITEM_GOODSNUM_FIELD.type = 13
pb.CS_STORE_MARKET_ADDSELLITEM_GOODSNUM_FIELD.cpp_type = 3

pb.CS_STORE_MARKET_ADDSELLITEM_PRICE_FIELD.name = "Price"
pb.CS_STORE_MARKET_ADDSELLITEM_PRICE_FIELD.full_name = ".CS_Store_Market_AddSellItem.Price"
pb.CS_STORE_MARKET_ADDSELLITEM_PRICE_FIELD.number = 3
pb.CS_STORE_MARKET_ADDSELLITEM_PRICE_FIELD.index = 2
pb.CS_STORE_MARKET_ADDSELLITEM_PRICE_FIELD.label = 1
pb.CS_STORE_MARKET_ADDSELLITEM_PRICE_FIELD.has_default_value = false
pb.CS_STORE_MARKET_ADDSELLITEM_PRICE_FIELD.default_value = 0
pb.CS_STORE_MARKET_ADDSELLITEM_PRICE_FIELD.type = 13
pb.CS_STORE_MARKET_ADDSELLITEM_PRICE_FIELD.cpp_type = 3

pb.CS_STORE_MARKET_ADDSELLITEM.name = "CS_Store_Market_AddSellItem"
pb.CS_STORE_MARKET_ADDSELLITEM.full_name = ".CS_Store_Market_AddSellItem"
pb.CS_STORE_MARKET_ADDSELLITEM.nested_types = {}
pb.CS_STORE_MARKET_ADDSELLITEM.enum_types = {}
pb.CS_STORE_MARKET_ADDSELLITEM.fields = {pb.CS_STORE_MARKET_ADDSELLITEM_GOODSUID_FIELD, pb.CS_STORE_MARKET_ADDSELLITEM_GOODSNUM_FIELD, pb.CS_STORE_MARKET_ADDSELLITEM_PRICE_FIELD}
pb.CS_STORE_MARKET_ADDSELLITEM.is_extendable = false
pb.CS_STORE_MARKET_ADDSELLITEM.extensions = {}
pb.SC_STORE_MARKET_ADDSELLITEM_RESULT_FIELD.name = "Result"
pb.SC_STORE_MARKET_ADDSELLITEM_RESULT_FIELD.full_name = ".SC_Store_Market_AddSellItem.Result"
pb.SC_STORE_MARKET_ADDSELLITEM_RESULT_FIELD.number = 1
pb.SC_STORE_MARKET_ADDSELLITEM_RESULT_FIELD.index = 0
pb.SC_STORE_MARKET_ADDSELLITEM_RESULT_FIELD.label = 1
pb.SC_STORE_MARKET_ADDSELLITEM_RESULT_FIELD.has_default_value = false
pb.SC_STORE_MARKET_ADDSELLITEM_RESULT_FIELD.default_value = 0
pb.SC_STORE_MARKET_ADDSELLITEM_RESULT_FIELD.type = 13
pb.SC_STORE_MARKET_ADDSELLITEM_RESULT_FIELD.cpp_type = 3

pb.SC_STORE_MARKET_ADDSELLITEM.name = "SC_Store_Market_AddSellItem"
pb.SC_STORE_MARKET_ADDSELLITEM.full_name = ".SC_Store_Market_AddSellItem"
pb.SC_STORE_MARKET_ADDSELLITEM.nested_types = {}
pb.SC_STORE_MARKET_ADDSELLITEM.enum_types = {}
pb.SC_STORE_MARKET_ADDSELLITEM.fields = {pb.SC_STORE_MARKET_ADDSELLITEM_RESULT_FIELD}
pb.SC_STORE_MARKET_ADDSELLITEM.is_extendable = false
pb.SC_STORE_MARKET_ADDSELLITEM.extensions = {}
pb.CS_STORE_MARKET_REMOVESELLITEM_SELLID_FIELD.name = "SellID"
pb.CS_STORE_MARKET_REMOVESELLITEM_SELLID_FIELD.full_name = ".CS_Store_Market_RemoveSellItem.SellID"
pb.CS_STORE_MARKET_REMOVESELLITEM_SELLID_FIELD.number = 1
pb.CS_STORE_MARKET_REMOVESELLITEM_SELLID_FIELD.index = 0
pb.CS_STORE_MARKET_REMOVESELLITEM_SELLID_FIELD.label = 1
pb.CS_STORE_MARKET_REMOVESELLITEM_SELLID_FIELD.has_default_value = false
pb.CS_STORE_MARKET_REMOVESELLITEM_SELLID_FIELD.default_value = 0
pb.CS_STORE_MARKET_REMOVESELLITEM_SELLID_FIELD.type = 13
pb.CS_STORE_MARKET_REMOVESELLITEM_SELLID_FIELD.cpp_type = 3

pb.CS_STORE_MARKET_REMOVESELLITEM_GOODSNUM_FIELD.name = "GoodsNum"
pb.CS_STORE_MARKET_REMOVESELLITEM_GOODSNUM_FIELD.full_name = ".CS_Store_Market_RemoveSellItem.GoodsNum"
pb.CS_STORE_MARKET_REMOVESELLITEM_GOODSNUM_FIELD.number = 2
pb.CS_STORE_MARKET_REMOVESELLITEM_GOODSNUM_FIELD.index = 1
pb.CS_STORE_MARKET_REMOVESELLITEM_GOODSNUM_FIELD.label = 1
pb.CS_STORE_MARKET_REMOVESELLITEM_GOODSNUM_FIELD.has_default_value = false
pb.CS_STORE_MARKET_REMOVESELLITEM_GOODSNUM_FIELD.default_value = 0
pb.CS_STORE_MARKET_REMOVESELLITEM_GOODSNUM_FIELD.type = 13
pb.CS_STORE_MARKET_REMOVESELLITEM_GOODSNUM_FIELD.cpp_type = 3

pb.CS_STORE_MARKET_REMOVESELLITEM.name = "CS_Store_Market_RemoveSellItem"
pb.CS_STORE_MARKET_REMOVESELLITEM.full_name = ".CS_Store_Market_RemoveSellItem"
pb.CS_STORE_MARKET_REMOVESELLITEM.nested_types = {}
pb.CS_STORE_MARKET_REMOVESELLITEM.enum_types = {}
pb.CS_STORE_MARKET_REMOVESELLITEM.fields = {pb.CS_STORE_MARKET_REMOVESELLITEM_SELLID_FIELD, pb.CS_STORE_MARKET_REMOVESELLITEM_GOODSNUM_FIELD}
pb.CS_STORE_MARKET_REMOVESELLITEM.is_extendable = false
pb.CS_STORE_MARKET_REMOVESELLITEM.extensions = {}
pb.SC_STORE_MARKET_REMOVESELLITEM_RESULT_FIELD.name = "Result"
pb.SC_STORE_MARKET_REMOVESELLITEM_RESULT_FIELD.full_name = ".SC_Store_Market_RemoveSellItem.Result"
pb.SC_STORE_MARKET_REMOVESELLITEM_RESULT_FIELD.number = 1
pb.SC_STORE_MARKET_REMOVESELLITEM_RESULT_FIELD.index = 0
pb.SC_STORE_MARKET_REMOVESELLITEM_RESULT_FIELD.label = 1
pb.SC_STORE_MARKET_REMOVESELLITEM_RESULT_FIELD.has_default_value = false
pb.SC_STORE_MARKET_REMOVESELLITEM_RESULT_FIELD.default_value = 0
pb.SC_STORE_MARKET_REMOVESELLITEM_RESULT_FIELD.type = 13
pb.SC_STORE_MARKET_REMOVESELLITEM_RESULT_FIELD.cpp_type = 3

pb.SC_STORE_MARKET_REMOVESELLITEM.name = "SC_Store_Market_RemoveSellItem"
pb.SC_STORE_MARKET_REMOVESELLITEM.full_name = ".SC_Store_Market_RemoveSellItem"
pb.SC_STORE_MARKET_REMOVESELLITEM.nested_types = {}
pb.SC_STORE_MARKET_REMOVESELLITEM.enum_types = {}
pb.SC_STORE_MARKET_REMOVESELLITEM.fields = {pb.SC_STORE_MARKET_REMOVESELLITEM_RESULT_FIELD}
pb.SC_STORE_MARKET_REMOVESELLITEM.is_extendable = false
pb.SC_STORE_MARKET_REMOVESELLITEM.extensions = {}
pb.CS_STORE_MARKET_BUYSELLITEM_SELLID_FIELD.name = "SellID"
pb.CS_STORE_MARKET_BUYSELLITEM_SELLID_FIELD.full_name = ".CS_Store_Market_BuySellItem.SellID"
pb.CS_STORE_MARKET_BUYSELLITEM_SELLID_FIELD.number = 1
pb.CS_STORE_MARKET_BUYSELLITEM_SELLID_FIELD.index = 0
pb.CS_STORE_MARKET_BUYSELLITEM_SELLID_FIELD.label = 1
pb.CS_STORE_MARKET_BUYSELLITEM_SELLID_FIELD.has_default_value = false
pb.CS_STORE_MARKET_BUYSELLITEM_SELLID_FIELD.default_value = 0
pb.CS_STORE_MARKET_BUYSELLITEM_SELLID_FIELD.type = 13
pb.CS_STORE_MARKET_BUYSELLITEM_SELLID_FIELD.cpp_type = 3

pb.CS_STORE_MARKET_BUYSELLITEM_GOODSNUM_FIELD.name = "GoodsNum"
pb.CS_STORE_MARKET_BUYSELLITEM_GOODSNUM_FIELD.full_name = ".CS_Store_Market_BuySellItem.GoodsNum"
pb.CS_STORE_MARKET_BUYSELLITEM_GOODSNUM_FIELD.number = 2
pb.CS_STORE_MARKET_BUYSELLITEM_GOODSNUM_FIELD.index = 1
pb.CS_STORE_MARKET_BUYSELLITEM_GOODSNUM_FIELD.label = 1
pb.CS_STORE_MARKET_BUYSELLITEM_GOODSNUM_FIELD.has_default_value = false
pb.CS_STORE_MARKET_BUYSELLITEM_GOODSNUM_FIELD.default_value = 0
pb.CS_STORE_MARKET_BUYSELLITEM_GOODSNUM_FIELD.type = 13
pb.CS_STORE_MARKET_BUYSELLITEM_GOODSNUM_FIELD.cpp_type = 3

pb.CS_STORE_MARKET_BUYSELLITEM.name = "CS_Store_Market_BuySellItem"
pb.CS_STORE_MARKET_BUYSELLITEM.full_name = ".CS_Store_Market_BuySellItem"
pb.CS_STORE_MARKET_BUYSELLITEM.nested_types = {}
pb.CS_STORE_MARKET_BUYSELLITEM.enum_types = {}
pb.CS_STORE_MARKET_BUYSELLITEM.fields = {pb.CS_STORE_MARKET_BUYSELLITEM_SELLID_FIELD, pb.CS_STORE_MARKET_BUYSELLITEM_GOODSNUM_FIELD}
pb.CS_STORE_MARKET_BUYSELLITEM.is_extendable = false
pb.CS_STORE_MARKET_BUYSELLITEM.extensions = {}
pb.SC_STORE_MARKET_BUYSELLITEM_RESULT_FIELD.name = "Result"
pb.SC_STORE_MARKET_BUYSELLITEM_RESULT_FIELD.full_name = ".SC_Store_Market_BuySellItem.Result"
pb.SC_STORE_MARKET_BUYSELLITEM_RESULT_FIELD.number = 1
pb.SC_STORE_MARKET_BUYSELLITEM_RESULT_FIELD.index = 0
pb.SC_STORE_MARKET_BUYSELLITEM_RESULT_FIELD.label = 1
pb.SC_STORE_MARKET_BUYSELLITEM_RESULT_FIELD.has_default_value = false
pb.SC_STORE_MARKET_BUYSELLITEM_RESULT_FIELD.default_value = 0
pb.SC_STORE_MARKET_BUYSELLITEM_RESULT_FIELD.type = 13
pb.SC_STORE_MARKET_BUYSELLITEM_RESULT_FIELD.cpp_type = 3

pb.SC_STORE_MARKET_BUYSELLITEM.name = "SC_Store_Market_BuySellItem"
pb.SC_STORE_MARKET_BUYSELLITEM.full_name = ".SC_Store_Market_BuySellItem"
pb.SC_STORE_MARKET_BUYSELLITEM.nested_types = {}
pb.SC_STORE_MARKET_BUYSELLITEM.enum_types = {}
pb.SC_STORE_MARKET_BUYSELLITEM.fields = {pb.SC_STORE_MARKET_BUYSELLITEM_RESULT_FIELD}
pb.SC_STORE_MARKET_BUYSELLITEM.is_extendable = false
pb.SC_STORE_MARKET_BUYSELLITEM.extensions = {}
pb.CS_STORE_RECHARGE_REQUESTGOODS_REQUESTTYPE_FIELD.name = "RequestType"
pb.CS_STORE_RECHARGE_REQUESTGOODS_REQUESTTYPE_FIELD.full_name = ".CS_Store_Recharge_RequestGoods.RequestType"
pb.CS_STORE_RECHARGE_REQUESTGOODS_REQUESTTYPE_FIELD.number = 1
pb.CS_STORE_RECHARGE_REQUESTGOODS_REQUESTTYPE_FIELD.index = 0
pb.CS_STORE_RECHARGE_REQUESTGOODS_REQUESTTYPE_FIELD.label = 1
pb.CS_STORE_RECHARGE_REQUESTGOODS_REQUESTTYPE_FIELD.has_default_value = false
pb.CS_STORE_RECHARGE_REQUESTGOODS_REQUESTTYPE_FIELD.default_value = 0
pb.CS_STORE_RECHARGE_REQUESTGOODS_REQUESTTYPE_FIELD.type = 5
pb.CS_STORE_RECHARGE_REQUESTGOODS_REQUESTTYPE_FIELD.cpp_type = 1

pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM1_FIELD.name = "Item1"
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM1_FIELD.full_name = ".CS_Store_Recharge_RequestGoods.Item1"
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM1_FIELD.number = 2
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM1_FIELD.index = 1
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM1_FIELD.label = 1
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM1_FIELD.has_default_value = false
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM1_FIELD.default_value = 0
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM1_FIELD.type = 5
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM1_FIELD.cpp_type = 1

pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM2_FIELD.name = "Item2"
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM2_FIELD.full_name = ".CS_Store_Recharge_RequestGoods.Item2"
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM2_FIELD.number = 3
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM2_FIELD.index = 2
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM2_FIELD.label = 1
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM2_FIELD.has_default_value = false
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM2_FIELD.default_value = 0
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM2_FIELD.type = 5
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM2_FIELD.cpp_type = 1

pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM3_FIELD.name = "Item3"
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM3_FIELD.full_name = ".CS_Store_Recharge_RequestGoods.Item3"
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM3_FIELD.number = 4
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM3_FIELD.index = 3
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM3_FIELD.label = 1
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM3_FIELD.has_default_value = false
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM3_FIELD.default_value = 0
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM3_FIELD.type = 5
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM3_FIELD.cpp_type = 1

pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM4_FIELD.name = "Item4"
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM4_FIELD.full_name = ".CS_Store_Recharge_RequestGoods.Item4"
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM4_FIELD.number = 5
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM4_FIELD.index = 4
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM4_FIELD.label = 1
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM4_FIELD.has_default_value = false
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM4_FIELD.default_value = 0
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM4_FIELD.type = 5
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM4_FIELD.cpp_type = 1

pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM5_FIELD.name = "Item5"
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM5_FIELD.full_name = ".CS_Store_Recharge_RequestGoods.Item5"
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM5_FIELD.number = 6
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM5_FIELD.index = 5
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM5_FIELD.label = 1
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM5_FIELD.has_default_value = false
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM5_FIELD.default_value = 0
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM5_FIELD.type = 5
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM5_FIELD.cpp_type = 1

pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM6_FIELD.name = "Item6"
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM6_FIELD.full_name = ".CS_Store_Recharge_RequestGoods.Item6"
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM6_FIELD.number = 7
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM6_FIELD.index = 6
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM6_FIELD.label = 1
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM6_FIELD.has_default_value = false
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM6_FIELD.default_value = 0
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM6_FIELD.type = 5
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM6_FIELD.cpp_type = 1

pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM7_FIELD.name = "Item7"
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM7_FIELD.full_name = ".CS_Store_Recharge_RequestGoods.Item7"
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM7_FIELD.number = 8
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM7_FIELD.index = 7
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM7_FIELD.label = 1
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM7_FIELD.has_default_value = false
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM7_FIELD.default_value = 0
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM7_FIELD.type = 5
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM7_FIELD.cpp_type = 1

pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM8_FIELD.name = "Item8"
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM8_FIELD.full_name = ".CS_Store_Recharge_RequestGoods.Item8"
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM8_FIELD.number = 9
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM8_FIELD.index = 8
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM8_FIELD.label = 1
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM8_FIELD.has_default_value = false
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM8_FIELD.default_value = 0
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM8_FIELD.type = 5
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM8_FIELD.cpp_type = 1

pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM9_FIELD.name = "Item9"
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM9_FIELD.full_name = ".CS_Store_Recharge_RequestGoods.Item9"
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM9_FIELD.number = 10
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM9_FIELD.index = 9
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM9_FIELD.label = 1
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM9_FIELD.has_default_value = false
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM9_FIELD.default_value = 0
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM9_FIELD.type = 5
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM9_FIELD.cpp_type = 1

pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM10_FIELD.name = "Item10"
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM10_FIELD.full_name = ".CS_Store_Recharge_RequestGoods.Item10"
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM10_FIELD.number = 11
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM10_FIELD.index = 10
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM10_FIELD.label = 1
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM10_FIELD.has_default_value = false
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM10_FIELD.default_value = 0
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM10_FIELD.type = 5
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM10_FIELD.cpp_type = 1

pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM11_FIELD.name = "Item11"
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM11_FIELD.full_name = ".CS_Store_Recharge_RequestGoods.Item11"
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM11_FIELD.number = 12
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM11_FIELD.index = 11
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM11_FIELD.label = 1
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM11_FIELD.has_default_value = false
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM11_FIELD.default_value = 0
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM11_FIELD.type = 5
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM11_FIELD.cpp_type = 1

pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM12_FIELD.name = "Item12"
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM12_FIELD.full_name = ".CS_Store_Recharge_RequestGoods.Item12"
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM12_FIELD.number = 13
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM12_FIELD.index = 12
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM12_FIELD.label = 1
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM12_FIELD.has_default_value = false
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM12_FIELD.default_value = 0
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM12_FIELD.type = 5
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM12_FIELD.cpp_type = 1

pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM13_FIELD.name = "Item13"
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM13_FIELD.full_name = ".CS_Store_Recharge_RequestGoods.Item13"
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM13_FIELD.number = 14
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM13_FIELD.index = 13
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM13_FIELD.label = 1
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM13_FIELD.has_default_value = false
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM13_FIELD.default_value = 0
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM13_FIELD.type = 5
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM13_FIELD.cpp_type = 1

pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM14_FIELD.name = "Item14"
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM14_FIELD.full_name = ".CS_Store_Recharge_RequestGoods.Item14"
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM14_FIELD.number = 15
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM14_FIELD.index = 14
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM14_FIELD.label = 1
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM14_FIELD.has_default_value = false
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM14_FIELD.default_value = 0
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM14_FIELD.type = 5
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM14_FIELD.cpp_type = 1

pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM15_FIELD.name = "Item15"
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM15_FIELD.full_name = ".CS_Store_Recharge_RequestGoods.Item15"
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM15_FIELD.number = 16
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM15_FIELD.index = 15
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM15_FIELD.label = 1
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM15_FIELD.has_default_value = false
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM15_FIELD.default_value = 0
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM15_FIELD.type = 5
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM15_FIELD.cpp_type = 1

pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM16_FIELD.name = "Item16"
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM16_FIELD.full_name = ".CS_Store_Recharge_RequestGoods.Item16"
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM16_FIELD.number = 17
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM16_FIELD.index = 16
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM16_FIELD.label = 1
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM16_FIELD.has_default_value = false
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM16_FIELD.default_value = 0
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM16_FIELD.type = 5
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM16_FIELD.cpp_type = 1

pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM17_FIELD.name = "Item17"
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM17_FIELD.full_name = ".CS_Store_Recharge_RequestGoods.Item17"
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM17_FIELD.number = 18
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM17_FIELD.index = 17
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM17_FIELD.label = 1
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM17_FIELD.has_default_value = false
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM17_FIELD.default_value = 0
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM17_FIELD.type = 5
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM17_FIELD.cpp_type = 1

pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM18_FIELD.name = "Item18"
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM18_FIELD.full_name = ".CS_Store_Recharge_RequestGoods.Item18"
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM18_FIELD.number = 19
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM18_FIELD.index = 18
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM18_FIELD.label = 1
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM18_FIELD.has_default_value = false
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM18_FIELD.default_value = 0
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM18_FIELD.type = 5
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM18_FIELD.cpp_type = 1

pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM19_FIELD.name = "Item19"
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM19_FIELD.full_name = ".CS_Store_Recharge_RequestGoods.Item19"
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM19_FIELD.number = 20
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM19_FIELD.index = 19
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM19_FIELD.label = 1
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM19_FIELD.has_default_value = false
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM19_FIELD.default_value = 0
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM19_FIELD.type = 5
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM19_FIELD.cpp_type = 1

pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM20_FIELD.name = "Item20"
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM20_FIELD.full_name = ".CS_Store_Recharge_RequestGoods.Item20"
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM20_FIELD.number = 21
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM20_FIELD.index = 20
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM20_FIELD.label = 1
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM20_FIELD.has_default_value = false
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM20_FIELD.default_value = 0
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM20_FIELD.type = 5
pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM20_FIELD.cpp_type = 1

pb.CS_STORE_RECHARGE_REQUESTGOODS_NEEDRECHARGE_FIELD.name = "NeedRecharge"
pb.CS_STORE_RECHARGE_REQUESTGOODS_NEEDRECHARGE_FIELD.full_name = ".CS_Store_Recharge_RequestGoods.NeedRecharge"
pb.CS_STORE_RECHARGE_REQUESTGOODS_NEEDRECHARGE_FIELD.number = 22
pb.CS_STORE_RECHARGE_REQUESTGOODS_NEEDRECHARGE_FIELD.index = 21
pb.CS_STORE_RECHARGE_REQUESTGOODS_NEEDRECHARGE_FIELD.label = 1
pb.CS_STORE_RECHARGE_REQUESTGOODS_NEEDRECHARGE_FIELD.has_default_value = false
pb.CS_STORE_RECHARGE_REQUESTGOODS_NEEDRECHARGE_FIELD.default_value = 0
pb.CS_STORE_RECHARGE_REQUESTGOODS_NEEDRECHARGE_FIELD.type = 5
pb.CS_STORE_RECHARGE_REQUESTGOODS_NEEDRECHARGE_FIELD.cpp_type = 1

pb.CS_STORE_RECHARGE_REQUESTGOODS_EXTRAGOODSNUM_FIELD.name = "ExtraGoodsNum"
pb.CS_STORE_RECHARGE_REQUESTGOODS_EXTRAGOODSNUM_FIELD.full_name = ".CS_Store_Recharge_RequestGoods.ExtraGoodsNum"
pb.CS_STORE_RECHARGE_REQUESTGOODS_EXTRAGOODSNUM_FIELD.number = 23
pb.CS_STORE_RECHARGE_REQUESTGOODS_EXTRAGOODSNUM_FIELD.index = 22
pb.CS_STORE_RECHARGE_REQUESTGOODS_EXTRAGOODSNUM_FIELD.label = 1
pb.CS_STORE_RECHARGE_REQUESTGOODS_EXTRAGOODSNUM_FIELD.has_default_value = false
pb.CS_STORE_RECHARGE_REQUESTGOODS_EXTRAGOODSNUM_FIELD.default_value = 0
pb.CS_STORE_RECHARGE_REQUESTGOODS_EXTRAGOODSNUM_FIELD.type = 5
pb.CS_STORE_RECHARGE_REQUESTGOODS_EXTRAGOODSNUM_FIELD.cpp_type = 1

pb.CS_STORE_RECHARGE_REQUESTGOODS.name = "CS_Store_Recharge_RequestGoods"
pb.CS_STORE_RECHARGE_REQUESTGOODS.full_name = ".CS_Store_Recharge_RequestGoods"
pb.CS_STORE_RECHARGE_REQUESTGOODS.nested_types = {}
pb.CS_STORE_RECHARGE_REQUESTGOODS.enum_types = {}
pb.CS_STORE_RECHARGE_REQUESTGOODS.fields = {pb.CS_STORE_RECHARGE_REQUESTGOODS_REQUESTTYPE_FIELD, pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM1_FIELD, pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM2_FIELD, pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM3_FIELD, pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM4_FIELD, pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM5_FIELD, pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM6_FIELD, pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM7_FIELD, pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM8_FIELD, pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM9_FIELD, pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM10_FIELD, pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM11_FIELD, pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM12_FIELD, pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM13_FIELD, pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM14_FIELD, pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM15_FIELD, pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM16_FIELD, pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM17_FIELD, pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM18_FIELD, pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM19_FIELD, pb.CS_STORE_RECHARGE_REQUESTGOODS_ITEM20_FIELD, pb.CS_STORE_RECHARGE_REQUESTGOODS_NEEDRECHARGE_FIELD, pb.CS_STORE_RECHARGE_REQUESTGOODS_EXTRAGOODSNUM_FIELD}
pb.CS_STORE_RECHARGE_REQUESTGOODS.is_extendable = false
pb.CS_STORE_RECHARGE_REQUESTGOODS.extensions = {}
pb.SC_STORE_RECHARGE_REQUESTGOODS_RESULT_FIELD.name = "Result"
pb.SC_STORE_RECHARGE_REQUESTGOODS_RESULT_FIELD.full_name = ".SC_Store_Recharge_RequestGoods.Result"
pb.SC_STORE_RECHARGE_REQUESTGOODS_RESULT_FIELD.number = 1
pb.SC_STORE_RECHARGE_REQUESTGOODS_RESULT_FIELD.index = 0
pb.SC_STORE_RECHARGE_REQUESTGOODS_RESULT_FIELD.label = 1
pb.SC_STORE_RECHARGE_REQUESTGOODS_RESULT_FIELD.has_default_value = false
pb.SC_STORE_RECHARGE_REQUESTGOODS_RESULT_FIELD.default_value = 0
pb.SC_STORE_RECHARGE_REQUESTGOODS_RESULT_FIELD.type = 5
pb.SC_STORE_RECHARGE_REQUESTGOODS_RESULT_FIELD.cpp_type = 1

pb.SC_STORE_RECHARGE_REQUESTGOODS.name = "SC_Store_Recharge_RequestGoods"
pb.SC_STORE_RECHARGE_REQUESTGOODS.full_name = ".SC_Store_Recharge_RequestGoods"
pb.SC_STORE_RECHARGE_REQUESTGOODS.nested_types = {}
pb.SC_STORE_RECHARGE_REQUESTGOODS.enum_types = {}
pb.SC_STORE_RECHARGE_REQUESTGOODS.fields = {pb.SC_STORE_RECHARGE_REQUESTGOODS_RESULT_FIELD}
pb.SC_STORE_RECHARGE_REQUESTGOODS.is_extendable = false
pb.SC_STORE_RECHARGE_REQUESTGOODS.extensions = {}
pb.SC_STORE_RECHARGE_GETDATA_BEGINTIME_FIELD.name = "BeginTime"
pb.SC_STORE_RECHARGE_GETDATA_BEGINTIME_FIELD.full_name = ".SC_Store_Recharge_GetData.BeginTime"
pb.SC_STORE_RECHARGE_GETDATA_BEGINTIME_FIELD.number = 1
pb.SC_STORE_RECHARGE_GETDATA_BEGINTIME_FIELD.index = 0
pb.SC_STORE_RECHARGE_GETDATA_BEGINTIME_FIELD.label = 1
pb.SC_STORE_RECHARGE_GETDATA_BEGINTIME_FIELD.has_default_value = false
pb.SC_STORE_RECHARGE_GETDATA_BEGINTIME_FIELD.default_value = 0
pb.SC_STORE_RECHARGE_GETDATA_BEGINTIME_FIELD.type = 5
pb.SC_STORE_RECHARGE_GETDATA_BEGINTIME_FIELD.cpp_type = 1

pb.SC_STORE_RECHARGE_GETDATA_STATUS_FIELD.name = "Status"
pb.SC_STORE_RECHARGE_GETDATA_STATUS_FIELD.full_name = ".SC_Store_Recharge_GetData.Status"
pb.SC_STORE_RECHARGE_GETDATA_STATUS_FIELD.number = 2
pb.SC_STORE_RECHARGE_GETDATA_STATUS_FIELD.index = 1
pb.SC_STORE_RECHARGE_GETDATA_STATUS_FIELD.label = 1
pb.SC_STORE_RECHARGE_GETDATA_STATUS_FIELD.has_default_value = false
pb.SC_STORE_RECHARGE_GETDATA_STATUS_FIELD.default_value = 0
pb.SC_STORE_RECHARGE_GETDATA_STATUS_FIELD.type = 5
pb.SC_STORE_RECHARGE_GETDATA_STATUS_FIELD.cpp_type = 1

pb.SC_STORE_RECHARGE_GETDATA_PRICE_FIELD.name = "Price"
pb.SC_STORE_RECHARGE_GETDATA_PRICE_FIELD.full_name = ".SC_Store_Recharge_GetData.Price"
pb.SC_STORE_RECHARGE_GETDATA_PRICE_FIELD.number = 3
pb.SC_STORE_RECHARGE_GETDATA_PRICE_FIELD.index = 2
pb.SC_STORE_RECHARGE_GETDATA_PRICE_FIELD.label = 1
pb.SC_STORE_RECHARGE_GETDATA_PRICE_FIELD.has_default_value = false
pb.SC_STORE_RECHARGE_GETDATA_PRICE_FIELD.default_value = 0
pb.SC_STORE_RECHARGE_GETDATA_PRICE_FIELD.type = 5
pb.SC_STORE_RECHARGE_GETDATA_PRICE_FIELD.cpp_type = 1

pb.SC_STORE_RECHARGE_GETDATA_GOODSID_FIELD.name = "GoodsID"
pb.SC_STORE_RECHARGE_GETDATA_GOODSID_FIELD.full_name = ".SC_Store_Recharge_GetData.GoodsID"
pb.SC_STORE_RECHARGE_GETDATA_GOODSID_FIELD.number = 4
pb.SC_STORE_RECHARGE_GETDATA_GOODSID_FIELD.index = 3
pb.SC_STORE_RECHARGE_GETDATA_GOODSID_FIELD.label = 3
pb.SC_STORE_RECHARGE_GETDATA_GOODSID_FIELD.has_default_value = false
pb.SC_STORE_RECHARGE_GETDATA_GOODSID_FIELD.default_value = {}
pb.SC_STORE_RECHARGE_GETDATA_GOODSID_FIELD.type = 5
pb.SC_STORE_RECHARGE_GETDATA_GOODSID_FIELD.cpp_type = 1

pb.SC_STORE_RECHARGE_GETDATA_GOODSNUM_FIELD.name = "GoodsNum"
pb.SC_STORE_RECHARGE_GETDATA_GOODSNUM_FIELD.full_name = ".SC_Store_Recharge_GetData.GoodsNum"
pb.SC_STORE_RECHARGE_GETDATA_GOODSNUM_FIELD.number = 5
pb.SC_STORE_RECHARGE_GETDATA_GOODSNUM_FIELD.index = 4
pb.SC_STORE_RECHARGE_GETDATA_GOODSNUM_FIELD.label = 3
pb.SC_STORE_RECHARGE_GETDATA_GOODSNUM_FIELD.has_default_value = false
pb.SC_STORE_RECHARGE_GETDATA_GOODSNUM_FIELD.default_value = {}
pb.SC_STORE_RECHARGE_GETDATA_GOODSNUM_FIELD.type = 5
pb.SC_STORE_RECHARGE_GETDATA_GOODSNUM_FIELD.cpp_type = 1

pb.SC_STORE_RECHARGE_GETDATA_GOODSIDEX_FIELD.name = "GoodsIDEx"
pb.SC_STORE_RECHARGE_GETDATA_GOODSIDEX_FIELD.full_name = ".SC_Store_Recharge_GetData.GoodsIDEx"
pb.SC_STORE_RECHARGE_GETDATA_GOODSIDEX_FIELD.number = 6
pb.SC_STORE_RECHARGE_GETDATA_GOODSIDEX_FIELD.index = 5
pb.SC_STORE_RECHARGE_GETDATA_GOODSIDEX_FIELD.label = 1
pb.SC_STORE_RECHARGE_GETDATA_GOODSIDEX_FIELD.has_default_value = false
pb.SC_STORE_RECHARGE_GETDATA_GOODSIDEX_FIELD.default_value = 0
pb.SC_STORE_RECHARGE_GETDATA_GOODSIDEX_FIELD.type = 5
pb.SC_STORE_RECHARGE_GETDATA_GOODSIDEX_FIELD.cpp_type = 1

pb.SC_STORE_RECHARGE_GETDATA_GOODSNUMEX_FIELD.name = "GoodsNumEx"
pb.SC_STORE_RECHARGE_GETDATA_GOODSNUMEX_FIELD.full_name = ".SC_Store_Recharge_GetData.GoodsNumEx"
pb.SC_STORE_RECHARGE_GETDATA_GOODSNUMEX_FIELD.number = 7
pb.SC_STORE_RECHARGE_GETDATA_GOODSNUMEX_FIELD.index = 6
pb.SC_STORE_RECHARGE_GETDATA_GOODSNUMEX_FIELD.label = 1
pb.SC_STORE_RECHARGE_GETDATA_GOODSNUMEX_FIELD.has_default_value = false
pb.SC_STORE_RECHARGE_GETDATA_GOODSNUMEX_FIELD.default_value = 0
pb.SC_STORE_RECHARGE_GETDATA_GOODSNUMEX_FIELD.type = 5
pb.SC_STORE_RECHARGE_GETDATA_GOODSNUMEX_FIELD.cpp_type = 1

pb.SC_STORE_RECHARGE_GETDATA_GOODSBINDFLAG_FIELD.name = "GoodsBindFlag"
pb.SC_STORE_RECHARGE_GETDATA_GOODSBINDFLAG_FIELD.full_name = ".SC_Store_Recharge_GetData.GoodsBindFlag"
pb.SC_STORE_RECHARGE_GETDATA_GOODSBINDFLAG_FIELD.number = 8
pb.SC_STORE_RECHARGE_GETDATA_GOODSBINDFLAG_FIELD.index = 7
pb.SC_STORE_RECHARGE_GETDATA_GOODSBINDFLAG_FIELD.label = 1
pb.SC_STORE_RECHARGE_GETDATA_GOODSBINDFLAG_FIELD.has_default_value = false
pb.SC_STORE_RECHARGE_GETDATA_GOODSBINDFLAG_FIELD.default_value = 0
pb.SC_STORE_RECHARGE_GETDATA_GOODSBINDFLAG_FIELD.type = 5
pb.SC_STORE_RECHARGE_GETDATA_GOODSBINDFLAG_FIELD.cpp_type = 1

pb.SC_STORE_RECHARGE_GETDATA.name = "SC_Store_Recharge_GetData"
pb.SC_STORE_RECHARGE_GETDATA.full_name = ".SC_Store_Recharge_GetData"
pb.SC_STORE_RECHARGE_GETDATA.nested_types = {}
pb.SC_STORE_RECHARGE_GETDATA.enum_types = {}
pb.SC_STORE_RECHARGE_GETDATA.fields = {pb.SC_STORE_RECHARGE_GETDATA_BEGINTIME_FIELD, pb.SC_STORE_RECHARGE_GETDATA_STATUS_FIELD, pb.SC_STORE_RECHARGE_GETDATA_PRICE_FIELD, pb.SC_STORE_RECHARGE_GETDATA_GOODSID_FIELD, pb.SC_STORE_RECHARGE_GETDATA_GOODSNUM_FIELD, pb.SC_STORE_RECHARGE_GETDATA_GOODSIDEX_FIELD, pb.SC_STORE_RECHARGE_GETDATA_GOODSNUMEX_FIELD, pb.SC_STORE_RECHARGE_GETDATA_GOODSBINDFLAG_FIELD}
pb.SC_STORE_RECHARGE_GETDATA.is_extendable = false
pb.SC_STORE_RECHARGE_GETDATA.extensions = {}
pb.CS_STORE_MARKET_TRADERECORD.name = "CS_Store_Market_TradeRecord"
pb.CS_STORE_MARKET_TRADERECORD.full_name = ".CS_Store_Market_TradeRecord"
pb.CS_STORE_MARKET_TRADERECORD.nested_types = {}
pb.CS_STORE_MARKET_TRADERECORD.enum_types = {}
pb.CS_STORE_MARKET_TRADERECORD.fields = {}
pb.CS_STORE_MARKET_TRADERECORD.is_extendable = false
pb.CS_STORE_MARKET_TRADERECORD.extensions = {}
pb.SC_STORE_MARKET_TRADERECORD_RECORDDATA_OWNERACTORID_FIELD.name = "OwnerActorID"
pb.SC_STORE_MARKET_TRADERECORD_RECORDDATA_OWNERACTORID_FIELD.full_name = ".SC_Store_Market_TradeRecord.RecordData.OwnerActorID"
pb.SC_STORE_MARKET_TRADERECORD_RECORDDATA_OWNERACTORID_FIELD.number = 1
pb.SC_STORE_MARKET_TRADERECORD_RECORDDATA_OWNERACTORID_FIELD.index = 0
pb.SC_STORE_MARKET_TRADERECORD_RECORDDATA_OWNERACTORID_FIELD.label = 1
pb.SC_STORE_MARKET_TRADERECORD_RECORDDATA_OWNERACTORID_FIELD.has_default_value = false
pb.SC_STORE_MARKET_TRADERECORD_RECORDDATA_OWNERACTORID_FIELD.default_value = 0
pb.SC_STORE_MARKET_TRADERECORD_RECORDDATA_OWNERACTORID_FIELD.type = 5
pb.SC_STORE_MARKET_TRADERECORD_RECORDDATA_OWNERACTORID_FIELD.cpp_type = 1

pb.SC_STORE_MARKET_TRADERECORD_RECORDDATA_BUYERACTORID_FIELD.name = "BuyerActorID"
pb.SC_STORE_MARKET_TRADERECORD_RECORDDATA_BUYERACTORID_FIELD.full_name = ".SC_Store_Market_TradeRecord.RecordData.BuyerActorID"
pb.SC_STORE_MARKET_TRADERECORD_RECORDDATA_BUYERACTORID_FIELD.number = 2
pb.SC_STORE_MARKET_TRADERECORD_RECORDDATA_BUYERACTORID_FIELD.index = 1
pb.SC_STORE_MARKET_TRADERECORD_RECORDDATA_BUYERACTORID_FIELD.label = 1
pb.SC_STORE_MARKET_TRADERECORD_RECORDDATA_BUYERACTORID_FIELD.has_default_value = false
pb.SC_STORE_MARKET_TRADERECORD_RECORDDATA_BUYERACTORID_FIELD.default_value = 0
pb.SC_STORE_MARKET_TRADERECORD_RECORDDATA_BUYERACTORID_FIELD.type = 5
pb.SC_STORE_MARKET_TRADERECORD_RECORDDATA_BUYERACTORID_FIELD.cpp_type = 1

pb.SC_STORE_MARKET_TRADERECORD_RECORDDATA_GOODSID_FIELD.name = "GoodsID"
pb.SC_STORE_MARKET_TRADERECORD_RECORDDATA_GOODSID_FIELD.full_name = ".SC_Store_Market_TradeRecord.RecordData.GoodsID"
pb.SC_STORE_MARKET_TRADERECORD_RECORDDATA_GOODSID_FIELD.number = 3
pb.SC_STORE_MARKET_TRADERECORD_RECORDDATA_GOODSID_FIELD.index = 2
pb.SC_STORE_MARKET_TRADERECORD_RECORDDATA_GOODSID_FIELD.label = 1
pb.SC_STORE_MARKET_TRADERECORD_RECORDDATA_GOODSID_FIELD.has_default_value = false
pb.SC_STORE_MARKET_TRADERECORD_RECORDDATA_GOODSID_FIELD.default_value = 0
pb.SC_STORE_MARKET_TRADERECORD_RECORDDATA_GOODSID_FIELD.type = 5
pb.SC_STORE_MARKET_TRADERECORD_RECORDDATA_GOODSID_FIELD.cpp_type = 1

pb.SC_STORE_MARKET_TRADERECORD_RECORDDATA_GOODSNUM_FIELD.name = "GoodsNum"
pb.SC_STORE_MARKET_TRADERECORD_RECORDDATA_GOODSNUM_FIELD.full_name = ".SC_Store_Market_TradeRecord.RecordData.GoodsNum"
pb.SC_STORE_MARKET_TRADERECORD_RECORDDATA_GOODSNUM_FIELD.number = 4
pb.SC_STORE_MARKET_TRADERECORD_RECORDDATA_GOODSNUM_FIELD.index = 3
pb.SC_STORE_MARKET_TRADERECORD_RECORDDATA_GOODSNUM_FIELD.label = 1
pb.SC_STORE_MARKET_TRADERECORD_RECORDDATA_GOODSNUM_FIELD.has_default_value = false
pb.SC_STORE_MARKET_TRADERECORD_RECORDDATA_GOODSNUM_FIELD.default_value = 0
pb.SC_STORE_MARKET_TRADERECORD_RECORDDATA_GOODSNUM_FIELD.type = 5
pb.SC_STORE_MARKET_TRADERECORD_RECORDDATA_GOODSNUM_FIELD.cpp_type = 1

pb.SC_STORE_MARKET_TRADERECORD_RECORDDATA_TIME_FIELD.name = "Time"
pb.SC_STORE_MARKET_TRADERECORD_RECORDDATA_TIME_FIELD.full_name = ".SC_Store_Market_TradeRecord.RecordData.Time"
pb.SC_STORE_MARKET_TRADERECORD_RECORDDATA_TIME_FIELD.number = 5
pb.SC_STORE_MARKET_TRADERECORD_RECORDDATA_TIME_FIELD.index = 4
pb.SC_STORE_MARKET_TRADERECORD_RECORDDATA_TIME_FIELD.label = 1
pb.SC_STORE_MARKET_TRADERECORD_RECORDDATA_TIME_FIELD.has_default_value = false
pb.SC_STORE_MARKET_TRADERECORD_RECORDDATA_TIME_FIELD.default_value = 0
pb.SC_STORE_MARKET_TRADERECORD_RECORDDATA_TIME_FIELD.type = 5
pb.SC_STORE_MARKET_TRADERECORD_RECORDDATA_TIME_FIELD.cpp_type = 1

pb.SC_STORE_MARKET_TRADERECORD_RECORDDATA_PRICE_FIELD.name = "Price"
pb.SC_STORE_MARKET_TRADERECORD_RECORDDATA_PRICE_FIELD.full_name = ".SC_Store_Market_TradeRecord.RecordData.Price"
pb.SC_STORE_MARKET_TRADERECORD_RECORDDATA_PRICE_FIELD.number = 6
pb.SC_STORE_MARKET_TRADERECORD_RECORDDATA_PRICE_FIELD.index = 5
pb.SC_STORE_MARKET_TRADERECORD_RECORDDATA_PRICE_FIELD.label = 1
pb.SC_STORE_MARKET_TRADERECORD_RECORDDATA_PRICE_FIELD.has_default_value = false
pb.SC_STORE_MARKET_TRADERECORD_RECORDDATA_PRICE_FIELD.default_value = 0
pb.SC_STORE_MARKET_TRADERECORD_RECORDDATA_PRICE_FIELD.type = 5
pb.SC_STORE_MARKET_TRADERECORD_RECORDDATA_PRICE_FIELD.cpp_type = 1

pb.SC_STORE_MARKET_TRADERECORD_RECORDDATA.name = "RecordData"
pb.SC_STORE_MARKET_TRADERECORD_RECORDDATA.full_name = ".SC_Store_Market_TradeRecord.RecordData"
pb.SC_STORE_MARKET_TRADERECORD_RECORDDATA.nested_types = {}
pb.SC_STORE_MARKET_TRADERECORD_RECORDDATA.enum_types = {}
pb.SC_STORE_MARKET_TRADERECORD_RECORDDATA.fields = {pb.SC_STORE_MARKET_TRADERECORD_RECORDDATA_OWNERACTORID_FIELD, pb.SC_STORE_MARKET_TRADERECORD_RECORDDATA_BUYERACTORID_FIELD, pb.SC_STORE_MARKET_TRADERECORD_RECORDDATA_GOODSID_FIELD, pb.SC_STORE_MARKET_TRADERECORD_RECORDDATA_GOODSNUM_FIELD, pb.SC_STORE_MARKET_TRADERECORD_RECORDDATA_TIME_FIELD, pb.SC_STORE_MARKET_TRADERECORD_RECORDDATA_PRICE_FIELD}
pb.SC_STORE_MARKET_TRADERECORD_RECORDDATA.is_extendable = false
pb.SC_STORE_MARKET_TRADERECORD_RECORDDATA.extensions = {}
pb.SC_STORE_MARKET_TRADERECORD_RECORDDATA.containing_type = pb.SC_STORE_MARKET_TRADERECORD
pb.SC_STORE_MARKET_TRADERECORD_RECORDLIST_FIELD.name = "RecordList"
pb.SC_STORE_MARKET_TRADERECORD_RECORDLIST_FIELD.full_name = ".SC_Store_Market_TradeRecord.RecordList"
pb.SC_STORE_MARKET_TRADERECORD_RECORDLIST_FIELD.number = 1
pb.SC_STORE_MARKET_TRADERECORD_RECORDLIST_FIELD.index = 0
pb.SC_STORE_MARKET_TRADERECORD_RECORDLIST_FIELD.label = 3
pb.SC_STORE_MARKET_TRADERECORD_RECORDLIST_FIELD.has_default_value = false
pb.SC_STORE_MARKET_TRADERECORD_RECORDLIST_FIELD.default_value = {}
pb.SC_STORE_MARKET_TRADERECORD_RECORDLIST_FIELD.message_type = pb.SC_STORE_MARKET_TRADERECORD_RECORDDATA
pb.SC_STORE_MARKET_TRADERECORD_RECORDLIST_FIELD.type = 11
pb.SC_STORE_MARKET_TRADERECORD_RECORDLIST_FIELD.cpp_type = 10

pb.SC_STORE_MARKET_TRADERECORD_ACTORID_FIELD.name = "ActorID"
pb.SC_STORE_MARKET_TRADERECORD_ACTORID_FIELD.full_name = ".SC_Store_Market_TradeRecord.ActorID"
pb.SC_STORE_MARKET_TRADERECORD_ACTORID_FIELD.number = 2
pb.SC_STORE_MARKET_TRADERECORD_ACTORID_FIELD.index = 1
pb.SC_STORE_MARKET_TRADERECORD_ACTORID_FIELD.label = 1
pb.SC_STORE_MARKET_TRADERECORD_ACTORID_FIELD.has_default_value = false
pb.SC_STORE_MARKET_TRADERECORD_ACTORID_FIELD.default_value = 0
pb.SC_STORE_MARKET_TRADERECORD_ACTORID_FIELD.type = 5
pb.SC_STORE_MARKET_TRADERECORD_ACTORID_FIELD.cpp_type = 1

pb.SC_STORE_MARKET_TRADERECORD.name = "SC_Store_Market_TradeRecord"
pb.SC_STORE_MARKET_TRADERECORD.full_name = ".SC_Store_Market_TradeRecord"
pb.SC_STORE_MARKET_TRADERECORD.nested_types = {pb.SC_STORE_MARKET_TRADERECORD_RECORDDATA}
pb.SC_STORE_MARKET_TRADERECORD.enum_types = {}
pb.SC_STORE_MARKET_TRADERECORD.fields = {pb.SC_STORE_MARKET_TRADERECORD_RECORDLIST_FIELD, pb.SC_STORE_MARKET_TRADERECORD_ACTORID_FIELD}
pb.SC_STORE_MARKET_TRADERECORD.is_extendable = false
pb.SC_STORE_MARKET_TRADERECORD.extensions = {}

CS_Market_GetBooth = protobuf.Message(pb.CS_MARKET_GETBOOTH)
CS_Market_GetList = protobuf.Message(pb.CS_MARKET_GETLIST)
CS_Market_GetSelfList = protobuf.Message(pb.CS_MARKET_GETSELFLIST)
CS_Store_BuyItem = protobuf.Message(pb.CS_STORE_BUYITEM)
CS_Store_GetItemList = protobuf.Message(pb.CS_STORE_GETITEMLIST)
CS_Store_GetItemPrice = protobuf.Message(pb.CS_STORE_GETITEMPRICE)
CS_Store_Market_AddSellItem = protobuf.Message(pb.CS_STORE_MARKET_ADDSELLITEM)
CS_Store_Market_BuySellItem = protobuf.Message(pb.CS_STORE_MARKET_BUYSELLITEM)
CS_Store_Market_RemoveSellItem = protobuf.Message(pb.CS_STORE_MARKET_REMOVESELLITEM)
CS_Store_Market_TradeRecord = protobuf.Message(pb.CS_STORE_MARKET_TRADERECORD)
CS_Store_Recharge_RequestGoods = protobuf.Message(pb.CS_STORE_RECHARGE_REQUESTGOODS)
CS_Store_Refresh = protobuf.Message(pb.CS_STORE_REFRESH)
MSG_STORE_BOOTH_GETBOOTH = 5
MSG_STORE_BUYITEM = 2
MSG_STORE_GETLIST = 1
MSG_STORE_GET_ITEM_PRICE = 8
MSG_STORE_MARKET_ADDSELLITEM = 9
MSG_STORE_MARKET_BUYSELLITEM = 11
MSG_STORE_MARKET_GETLIST = 4
MSG_STORE_MARKET_GETSELF = 7
MSG_STORE_MARKET_REMOVESELLITEM = 10
MSG_STORE_MARKET_TRADERECORD = 16
MSG_STORE_NONE = 0
MSG_STORE_RECHARGE_GETDATA = 13
MSG_STORE_RECHARGE_REQUESTGOODS = 12
MSG_STORE_REFRESH = 3
SC_Market_Booth = protobuf.Message(pb.SC_MARKET_BOOTH)
SC_Market_Booth.ItemData = protobuf.Message(pb.SC_MARKET_BOOTH_ITEMDATA)
SC_Market_GetList = protobuf.Message(pb.SC_MARKET_GETLIST)
SC_Market_GetList.ItemData = protobuf.Message(pb.SC_MARKET_GETLIST_ITEMDATA)
SC_Market_GetSelfList = protobuf.Message(pb.SC_MARKET_GETSELFLIST)
SC_Market_GetSelfList.ItemData = protobuf.Message(pb.SC_MARKET_GETSELFLIST_ITEMDATA)
SC_Store_BuyItem = protobuf.Message(pb.SC_STORE_BUYITEM)
SC_Store_GetItemList = protobuf.Message(pb.SC_STORE_GETITEMLIST)
SC_Store_GetItemList.ItemData = protobuf.Message(pb.SC_STORE_GETITEMLIST_ITEMDATA)
SC_Store_GetItemPrice = protobuf.Message(pb.SC_STORE_GETITEMPRICE)
SC_Store_Market_AddSellItem = protobuf.Message(pb.SC_STORE_MARKET_ADDSELLITEM)
SC_Store_Market_BuySellItem = protobuf.Message(pb.SC_STORE_MARKET_BUYSELLITEM)
SC_Store_Market_RemoveSellItem = protobuf.Message(pb.SC_STORE_MARKET_REMOVESELLITEM)
SC_Store_Market_TradeRecord = protobuf.Message(pb.SC_STORE_MARKET_TRADERECORD)
SC_Store_Market_TradeRecord.RecordData = protobuf.Message(pb.SC_STORE_MARKET_TRADERECORD_RECORDDATA)
SC_Store_Recharge_GetData = protobuf.Message(pb.SC_STORE_RECHARGE_GETDATA)
SC_Store_Recharge_RequestGoods = protobuf.Message(pb.SC_STORE_RECHARGE_REQUESTGOODS)
SC_Store_Refresh = protobuf.Message(pb.SC_STORE_REFRESH)

