-- Generated By protoc-gen-lua Do not Edit
local protobuf = require "protobuf/protobuf"
module('ChatMessage_pb')
local pb = {}


pb.MSG_CHAT_ACTIONID = protobuf.EnumDescriptor();
pb.MSG_CHAT_ACTIONID_MSG_CHAT_NONE_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_CHAT_ACTIONID_MSG_CHAT_SENDCHAT_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_CHAT_ACTIONID_MSG_CHAT_ERRORRESULT_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_CHAT_ACTIONID_MSG_CHAT_SHOWPRIZES_ENUM = protobuf.EnumValueDescriptor();
pb.CS_CHAR_SENDCHAT = protobuf.Descriptor();
pb.CS_CHAR_SENDCHAT_CHANNEL_FIELD = protobuf.FieldDescriptor();
pb.CS_CHAR_SENDCHAT_CONTENT_FIELD = protobuf.FieldDescriptor();
pb.CS_CHAR_SENDCHAT_CHATTYPE_FIELD = protobuf.FieldDescriptor();
pb.SC_CHAR_SENDCHAT = protobuf.Descriptor();
pb.SC_CHAR_SENDCHAT_CHANNEL_FIELD = protobuf.FieldDescriptor();
pb.SC_CHAR_SENDCHAT_SENDERID_FIELD = protobuf.FieldDescriptor();
pb.SC_CHAR_SENDCHAT_SENDERNAME_FIELD = protobuf.FieldDescriptor();
pb.SC_CHAR_SENDCHAT_CONTENT_FIELD = protobuf.FieldDescriptor();
pb.SC_CHAR_SENDCHAT_BROADCASTPOS_FIELD = protobuf.FieldDescriptor();
pb.SC_CHAR_SENDCHAT_BROADCASTSUBPOS_FIELD = protobuf.FieldDescriptor();
pb.SC_CHAR_SENDCHAT_BROADCASTPARAM_FIELD = protobuf.FieldDescriptor();
pb.SC_CHAR_SENDCHAT_LEVEL_FIELD = protobuf.FieldDescriptor();
pb.SC_CHAR_SENDCHAT_VIP_FIELD = protobuf.FieldDescriptor();
pb.SC_CHAR_SENDCHAT_CHATTYPE_FIELD = protobuf.FieldDescriptor();
pb.SC_CHAR_SENDCHAT_SENDERVOCATION_FIELD = protobuf.FieldDescriptor();
pb.SC_CHAR_SENDCHAT_SENDERCOUNTRY_FIELD = protobuf.FieldDescriptor();
pb.SC_CHAR_SENDCHAT_SENDERZONE_FIELD = protobuf.FieldDescriptor();
pb.SC_CHAR_SENDCHAT_TITLE_FIELD = protobuf.FieldDescriptor();
pb.SC_CHAR_SENDCHAT_ANSWERFLAG_FIELD = protobuf.FieldDescriptor();
pb.SC_CHAR_SENDCHAT_SENDERBLUEVIPLEVEL_FIELD = protobuf.FieldDescriptor();
pb.SC_CHAR_SENDCHAT_SENDERBLUEVIPTYPE_FIELD = protobuf.FieldDescriptor();
pb.SC_CHAR_ERRORRESULT = protobuf.Descriptor();
pb.SC_CHAR_ERRORRESULT_SHOWMODE_FIELD = protobuf.FieldDescriptor();
pb.SC_CHAR_ERRORRESULT_RESULTCODE_FIELD = protobuf.FieldDescriptor();
pb.SC_CHAR_ERRORRESULT_ERRORSTRING_FIELD = protobuf.FieldDescriptor();
pb.SC_CHAR_SHOWPRIZES = protobuf.Descriptor();
pb.SC_CHAR_SHOWPRIZES_FORMTYPE_FIELD = protobuf.FieldDescriptor();
pb.SC_CHAR_SHOWPRIZES_PRIZES_FIELD = protobuf.FieldDescriptor();
pb.SC_CHAR_SHOWPRIZES_TITLE_FIELD = protobuf.FieldDescriptor();
pb.SC_CHAR_SHOWPRIZES_CONTENT_FIELD = protobuf.FieldDescriptor();

pb.MSG_CHAT_ACTIONID_MSG_CHAT_NONE_ENUM.name = "MSG_CHAT_NONE"
pb.MSG_CHAT_ACTIONID_MSG_CHAT_NONE_ENUM.index = 0
pb.MSG_CHAT_ACTIONID_MSG_CHAT_NONE_ENUM.number = 0
pb.MSG_CHAT_ACTIONID_MSG_CHAT_SENDCHAT_ENUM.name = "MSG_CHAT_SENDCHAT"
pb.MSG_CHAT_ACTIONID_MSG_CHAT_SENDCHAT_ENUM.index = 1
pb.MSG_CHAT_ACTIONID_MSG_CHAT_SENDCHAT_ENUM.number = 1
pb.MSG_CHAT_ACTIONID_MSG_CHAT_ERRORRESULT_ENUM.name = "MSG_CHAT_ERRORRESULT"
pb.MSG_CHAT_ACTIONID_MSG_CHAT_ERRORRESULT_ENUM.index = 2
pb.MSG_CHAT_ACTIONID_MSG_CHAT_ERRORRESULT_ENUM.number = 2
pb.MSG_CHAT_ACTIONID_MSG_CHAT_SHOWPRIZES_ENUM.name = "MSG_CHAT_SHOWPRIZES"
pb.MSG_CHAT_ACTIONID_MSG_CHAT_SHOWPRIZES_ENUM.index = 3
pb.MSG_CHAT_ACTIONID_MSG_CHAT_SHOWPRIZES_ENUM.number = 3
pb.MSG_CHAT_ACTIONID.name = "MSG_CHAT_ACTIONID"
pb.MSG_CHAT_ACTIONID.full_name = ".MSG_CHAT_ACTIONID"
pb.MSG_CHAT_ACTIONID.values = {pb.MSG_CHAT_ACTIONID_MSG_CHAT_NONE_ENUM,pb.MSG_CHAT_ACTIONID_MSG_CHAT_SENDCHAT_ENUM,pb.MSG_CHAT_ACTIONID_MSG_CHAT_ERRORRESULT_ENUM,pb.MSG_CHAT_ACTIONID_MSG_CHAT_SHOWPRIZES_ENUM}
pb.CS_CHAR_SENDCHAT_CHANNEL_FIELD.name = "Channel"
pb.CS_CHAR_SENDCHAT_CHANNEL_FIELD.full_name = ".CS_Char_SendChat.Channel"
pb.CS_CHAR_SENDCHAT_CHANNEL_FIELD.number = 1
pb.CS_CHAR_SENDCHAT_CHANNEL_FIELD.index = 0
pb.CS_CHAR_SENDCHAT_CHANNEL_FIELD.label = 1
pb.CS_CHAR_SENDCHAT_CHANNEL_FIELD.has_default_value = false
pb.CS_CHAR_SENDCHAT_CHANNEL_FIELD.default_value = 0
pb.CS_CHAR_SENDCHAT_CHANNEL_FIELD.type = 13
pb.CS_CHAR_SENDCHAT_CHANNEL_FIELD.cpp_type = 3

pb.CS_CHAR_SENDCHAT_CONTENT_FIELD.name = "Content"
pb.CS_CHAR_SENDCHAT_CONTENT_FIELD.full_name = ".CS_Char_SendChat.Content"
pb.CS_CHAR_SENDCHAT_CONTENT_FIELD.number = 2
pb.CS_CHAR_SENDCHAT_CONTENT_FIELD.index = 1
pb.CS_CHAR_SENDCHAT_CONTENT_FIELD.label = 1
pb.CS_CHAR_SENDCHAT_CONTENT_FIELD.has_default_value = false
pb.CS_CHAR_SENDCHAT_CONTENT_FIELD.default_value = ""
pb.CS_CHAR_SENDCHAT_CONTENT_FIELD.type = 9
pb.CS_CHAR_SENDCHAT_CONTENT_FIELD.cpp_type = 9

pb.CS_CHAR_SENDCHAT_CHATTYPE_FIELD.name = "ChatType"
pb.CS_CHAR_SENDCHAT_CHATTYPE_FIELD.full_name = ".CS_Char_SendChat.ChatType"
pb.CS_CHAR_SENDCHAT_CHATTYPE_FIELD.number = 3
pb.CS_CHAR_SENDCHAT_CHATTYPE_FIELD.index = 2
pb.CS_CHAR_SENDCHAT_CHATTYPE_FIELD.label = 1
pb.CS_CHAR_SENDCHAT_CHATTYPE_FIELD.has_default_value = false
pb.CS_CHAR_SENDCHAT_CHATTYPE_FIELD.default_value = 0
pb.CS_CHAR_SENDCHAT_CHATTYPE_FIELD.type = 13
pb.CS_CHAR_SENDCHAT_CHATTYPE_FIELD.cpp_type = 3

pb.CS_CHAR_SENDCHAT.name = "CS_Char_SendChat"
pb.CS_CHAR_SENDCHAT.full_name = ".CS_Char_SendChat"
pb.CS_CHAR_SENDCHAT.nested_types = {}
pb.CS_CHAR_SENDCHAT.enum_types = {}
pb.CS_CHAR_SENDCHAT.fields = {pb.CS_CHAR_SENDCHAT_CHANNEL_FIELD, pb.CS_CHAR_SENDCHAT_CONTENT_FIELD, pb.CS_CHAR_SENDCHAT_CHATTYPE_FIELD}
pb.CS_CHAR_SENDCHAT.is_extendable = false
pb.CS_CHAR_SENDCHAT.extensions = {}
pb.SC_CHAR_SENDCHAT_CHANNEL_FIELD.name = "Channel"
pb.SC_CHAR_SENDCHAT_CHANNEL_FIELD.full_name = ".SC_Char_SendChat.Channel"
pb.SC_CHAR_SENDCHAT_CHANNEL_FIELD.number = 1
pb.SC_CHAR_SENDCHAT_CHANNEL_FIELD.index = 0
pb.SC_CHAR_SENDCHAT_CHANNEL_FIELD.label = 1
pb.SC_CHAR_SENDCHAT_CHANNEL_FIELD.has_default_value = false
pb.SC_CHAR_SENDCHAT_CHANNEL_FIELD.default_value = 0
pb.SC_CHAR_SENDCHAT_CHANNEL_FIELD.type = 13
pb.SC_CHAR_SENDCHAT_CHANNEL_FIELD.cpp_type = 3

pb.SC_CHAR_SENDCHAT_SENDERID_FIELD.name = "SenderID"
pb.SC_CHAR_SENDCHAT_SENDERID_FIELD.full_name = ".SC_Char_SendChat.SenderID"
pb.SC_CHAR_SENDCHAT_SENDERID_FIELD.number = 2
pb.SC_CHAR_SENDCHAT_SENDERID_FIELD.index = 1
pb.SC_CHAR_SENDCHAT_SENDERID_FIELD.label = 1
pb.SC_CHAR_SENDCHAT_SENDERID_FIELD.has_default_value = false
pb.SC_CHAR_SENDCHAT_SENDERID_FIELD.default_value = 0
pb.SC_CHAR_SENDCHAT_SENDERID_FIELD.type = 13
pb.SC_CHAR_SENDCHAT_SENDERID_FIELD.cpp_type = 3

pb.SC_CHAR_SENDCHAT_SENDERNAME_FIELD.name = "SenderName"
pb.SC_CHAR_SENDCHAT_SENDERNAME_FIELD.full_name = ".SC_Char_SendChat.SenderName"
pb.SC_CHAR_SENDCHAT_SENDERNAME_FIELD.number = 3
pb.SC_CHAR_SENDCHAT_SENDERNAME_FIELD.index = 2
pb.SC_CHAR_SENDCHAT_SENDERNAME_FIELD.label = 1
pb.SC_CHAR_SENDCHAT_SENDERNAME_FIELD.has_default_value = false
pb.SC_CHAR_SENDCHAT_SENDERNAME_FIELD.default_value = ""
pb.SC_CHAR_SENDCHAT_SENDERNAME_FIELD.type = 9
pb.SC_CHAR_SENDCHAT_SENDERNAME_FIELD.cpp_type = 9

pb.SC_CHAR_SENDCHAT_CONTENT_FIELD.name = "Content"
pb.SC_CHAR_SENDCHAT_CONTENT_FIELD.full_name = ".SC_Char_SendChat.Content"
pb.SC_CHAR_SENDCHAT_CONTENT_FIELD.number = 4
pb.SC_CHAR_SENDCHAT_CONTENT_FIELD.index = 3
pb.SC_CHAR_SENDCHAT_CONTENT_FIELD.label = 1
pb.SC_CHAR_SENDCHAT_CONTENT_FIELD.has_default_value = false
pb.SC_CHAR_SENDCHAT_CONTENT_FIELD.default_value = ""
pb.SC_CHAR_SENDCHAT_CONTENT_FIELD.type = 9
pb.SC_CHAR_SENDCHAT_CONTENT_FIELD.cpp_type = 9

pb.SC_CHAR_SENDCHAT_BROADCASTPOS_FIELD.name = "BroadCastPos"
pb.SC_CHAR_SENDCHAT_BROADCASTPOS_FIELD.full_name = ".SC_Char_SendChat.BroadCastPos"
pb.SC_CHAR_SENDCHAT_BROADCASTPOS_FIELD.number = 5
pb.SC_CHAR_SENDCHAT_BROADCASTPOS_FIELD.index = 4
pb.SC_CHAR_SENDCHAT_BROADCASTPOS_FIELD.label = 1
pb.SC_CHAR_SENDCHAT_BROADCASTPOS_FIELD.has_default_value = false
pb.SC_CHAR_SENDCHAT_BROADCASTPOS_FIELD.default_value = 0
pb.SC_CHAR_SENDCHAT_BROADCASTPOS_FIELD.type = 13
pb.SC_CHAR_SENDCHAT_BROADCASTPOS_FIELD.cpp_type = 3

pb.SC_CHAR_SENDCHAT_BROADCASTSUBPOS_FIELD.name = "BroadCastSubPos"
pb.SC_CHAR_SENDCHAT_BROADCASTSUBPOS_FIELD.full_name = ".SC_Char_SendChat.BroadCastSubPos"
pb.SC_CHAR_SENDCHAT_BROADCASTSUBPOS_FIELD.number = 6
pb.SC_CHAR_SENDCHAT_BROADCASTSUBPOS_FIELD.index = 5
pb.SC_CHAR_SENDCHAT_BROADCASTSUBPOS_FIELD.label = 1
pb.SC_CHAR_SENDCHAT_BROADCASTSUBPOS_FIELD.has_default_value = false
pb.SC_CHAR_SENDCHAT_BROADCASTSUBPOS_FIELD.default_value = 0
pb.SC_CHAR_SENDCHAT_BROADCASTSUBPOS_FIELD.type = 13
pb.SC_CHAR_SENDCHAT_BROADCASTSUBPOS_FIELD.cpp_type = 3

pb.SC_CHAR_SENDCHAT_BROADCASTPARAM_FIELD.name = "BroadCastParam"
pb.SC_CHAR_SENDCHAT_BROADCASTPARAM_FIELD.full_name = ".SC_Char_SendChat.BroadCastParam"
pb.SC_CHAR_SENDCHAT_BROADCASTPARAM_FIELD.number = 7
pb.SC_CHAR_SENDCHAT_BROADCASTPARAM_FIELD.index = 6
pb.SC_CHAR_SENDCHAT_BROADCASTPARAM_FIELD.label = 1
pb.SC_CHAR_SENDCHAT_BROADCASTPARAM_FIELD.has_default_value = false
pb.SC_CHAR_SENDCHAT_BROADCASTPARAM_FIELD.default_value = 0
pb.SC_CHAR_SENDCHAT_BROADCASTPARAM_FIELD.type = 13
pb.SC_CHAR_SENDCHAT_BROADCASTPARAM_FIELD.cpp_type = 3

pb.SC_CHAR_SENDCHAT_LEVEL_FIELD.name = "Level"
pb.SC_CHAR_SENDCHAT_LEVEL_FIELD.full_name = ".SC_Char_SendChat.Level"
pb.SC_CHAR_SENDCHAT_LEVEL_FIELD.number = 8
pb.SC_CHAR_SENDCHAT_LEVEL_FIELD.index = 7
pb.SC_CHAR_SENDCHAT_LEVEL_FIELD.label = 1
pb.SC_CHAR_SENDCHAT_LEVEL_FIELD.has_default_value = false
pb.SC_CHAR_SENDCHAT_LEVEL_FIELD.default_value = 0
pb.SC_CHAR_SENDCHAT_LEVEL_FIELD.type = 13
pb.SC_CHAR_SENDCHAT_LEVEL_FIELD.cpp_type = 3

pb.SC_CHAR_SENDCHAT_VIP_FIELD.name = "VIP"
pb.SC_CHAR_SENDCHAT_VIP_FIELD.full_name = ".SC_Char_SendChat.VIP"
pb.SC_CHAR_SENDCHAT_VIP_FIELD.number = 9
pb.SC_CHAR_SENDCHAT_VIP_FIELD.index = 8
pb.SC_CHAR_SENDCHAT_VIP_FIELD.label = 1
pb.SC_CHAR_SENDCHAT_VIP_FIELD.has_default_value = false
pb.SC_CHAR_SENDCHAT_VIP_FIELD.default_value = 0
pb.SC_CHAR_SENDCHAT_VIP_FIELD.type = 13
pb.SC_CHAR_SENDCHAT_VIP_FIELD.cpp_type = 3

pb.SC_CHAR_SENDCHAT_CHATTYPE_FIELD.name = "ChatType"
pb.SC_CHAR_SENDCHAT_CHATTYPE_FIELD.full_name = ".SC_Char_SendChat.ChatType"
pb.SC_CHAR_SENDCHAT_CHATTYPE_FIELD.number = 10
pb.SC_CHAR_SENDCHAT_CHATTYPE_FIELD.index = 9
pb.SC_CHAR_SENDCHAT_CHATTYPE_FIELD.label = 1
pb.SC_CHAR_SENDCHAT_CHATTYPE_FIELD.has_default_value = false
pb.SC_CHAR_SENDCHAT_CHATTYPE_FIELD.default_value = 0
pb.SC_CHAR_SENDCHAT_CHATTYPE_FIELD.type = 13
pb.SC_CHAR_SENDCHAT_CHATTYPE_FIELD.cpp_type = 3

pb.SC_CHAR_SENDCHAT_SENDERVOCATION_FIELD.name = "SenderVocation"
pb.SC_CHAR_SENDCHAT_SENDERVOCATION_FIELD.full_name = ".SC_Char_SendChat.SenderVocation"
pb.SC_CHAR_SENDCHAT_SENDERVOCATION_FIELD.number = 11
pb.SC_CHAR_SENDCHAT_SENDERVOCATION_FIELD.index = 10
pb.SC_CHAR_SENDCHAT_SENDERVOCATION_FIELD.label = 1
pb.SC_CHAR_SENDCHAT_SENDERVOCATION_FIELD.has_default_value = false
pb.SC_CHAR_SENDCHAT_SENDERVOCATION_FIELD.default_value = 0
pb.SC_CHAR_SENDCHAT_SENDERVOCATION_FIELD.type = 13
pb.SC_CHAR_SENDCHAT_SENDERVOCATION_FIELD.cpp_type = 3

pb.SC_CHAR_SENDCHAT_SENDERCOUNTRY_FIELD.name = "SenderCountry"
pb.SC_CHAR_SENDCHAT_SENDERCOUNTRY_FIELD.full_name = ".SC_Char_SendChat.SenderCountry"
pb.SC_CHAR_SENDCHAT_SENDERCOUNTRY_FIELD.number = 12
pb.SC_CHAR_SENDCHAT_SENDERCOUNTRY_FIELD.index = 11
pb.SC_CHAR_SENDCHAT_SENDERCOUNTRY_FIELD.label = 1
pb.SC_CHAR_SENDCHAT_SENDERCOUNTRY_FIELD.has_default_value = false
pb.SC_CHAR_SENDCHAT_SENDERCOUNTRY_FIELD.default_value = 0
pb.SC_CHAR_SENDCHAT_SENDERCOUNTRY_FIELD.type = 13
pb.SC_CHAR_SENDCHAT_SENDERCOUNTRY_FIELD.cpp_type = 3

pb.SC_CHAR_SENDCHAT_SENDERZONE_FIELD.name = "SenderZone"
pb.SC_CHAR_SENDCHAT_SENDERZONE_FIELD.full_name = ".SC_Char_SendChat.SenderZone"
pb.SC_CHAR_SENDCHAT_SENDERZONE_FIELD.number = 13
pb.SC_CHAR_SENDCHAT_SENDERZONE_FIELD.index = 12
pb.SC_CHAR_SENDCHAT_SENDERZONE_FIELD.label = 1
pb.SC_CHAR_SENDCHAT_SENDERZONE_FIELD.has_default_value = false
pb.SC_CHAR_SENDCHAT_SENDERZONE_FIELD.default_value = 0
pb.SC_CHAR_SENDCHAT_SENDERZONE_FIELD.type = 13
pb.SC_CHAR_SENDCHAT_SENDERZONE_FIELD.cpp_type = 3

pb.SC_CHAR_SENDCHAT_TITLE_FIELD.name = "Title"
pb.SC_CHAR_SENDCHAT_TITLE_FIELD.full_name = ".SC_Char_SendChat.Title"
pb.SC_CHAR_SENDCHAT_TITLE_FIELD.number = 14
pb.SC_CHAR_SENDCHAT_TITLE_FIELD.index = 13
pb.SC_CHAR_SENDCHAT_TITLE_FIELD.label = 1
pb.SC_CHAR_SENDCHAT_TITLE_FIELD.has_default_value = false
pb.SC_CHAR_SENDCHAT_TITLE_FIELD.default_value = 0
pb.SC_CHAR_SENDCHAT_TITLE_FIELD.type = 13
pb.SC_CHAR_SENDCHAT_TITLE_FIELD.cpp_type = 3

pb.SC_CHAR_SENDCHAT_ANSWERFLAG_FIELD.name = "AnswerFlag"
pb.SC_CHAR_SENDCHAT_ANSWERFLAG_FIELD.full_name = ".SC_Char_SendChat.AnswerFlag"
pb.SC_CHAR_SENDCHAT_ANSWERFLAG_FIELD.number = 15
pb.SC_CHAR_SENDCHAT_ANSWERFLAG_FIELD.index = 14
pb.SC_CHAR_SENDCHAT_ANSWERFLAG_FIELD.label = 1
pb.SC_CHAR_SENDCHAT_ANSWERFLAG_FIELD.has_default_value = false
pb.SC_CHAR_SENDCHAT_ANSWERFLAG_FIELD.default_value = 0
pb.SC_CHAR_SENDCHAT_ANSWERFLAG_FIELD.type = 13
pb.SC_CHAR_SENDCHAT_ANSWERFLAG_FIELD.cpp_type = 3

pb.SC_CHAR_SENDCHAT_SENDERBLUEVIPLEVEL_FIELD.name = "SenderBlueVIPLevel"
pb.SC_CHAR_SENDCHAT_SENDERBLUEVIPLEVEL_FIELD.full_name = ".SC_Char_SendChat.SenderBlueVIPLevel"
pb.SC_CHAR_SENDCHAT_SENDERBLUEVIPLEVEL_FIELD.number = 16
pb.SC_CHAR_SENDCHAT_SENDERBLUEVIPLEVEL_FIELD.index = 15
pb.SC_CHAR_SENDCHAT_SENDERBLUEVIPLEVEL_FIELD.label = 1
pb.SC_CHAR_SENDCHAT_SENDERBLUEVIPLEVEL_FIELD.has_default_value = false
pb.SC_CHAR_SENDCHAT_SENDERBLUEVIPLEVEL_FIELD.default_value = 0
pb.SC_CHAR_SENDCHAT_SENDERBLUEVIPLEVEL_FIELD.type = 13
pb.SC_CHAR_SENDCHAT_SENDERBLUEVIPLEVEL_FIELD.cpp_type = 3

pb.SC_CHAR_SENDCHAT_SENDERBLUEVIPTYPE_FIELD.name = "SenderBlueVIPType"
pb.SC_CHAR_SENDCHAT_SENDERBLUEVIPTYPE_FIELD.full_name = ".SC_Char_SendChat.SenderBlueVIPType"
pb.SC_CHAR_SENDCHAT_SENDERBLUEVIPTYPE_FIELD.number = 17
pb.SC_CHAR_SENDCHAT_SENDERBLUEVIPTYPE_FIELD.index = 16
pb.SC_CHAR_SENDCHAT_SENDERBLUEVIPTYPE_FIELD.label = 1
pb.SC_CHAR_SENDCHAT_SENDERBLUEVIPTYPE_FIELD.has_default_value = false
pb.SC_CHAR_SENDCHAT_SENDERBLUEVIPTYPE_FIELD.default_value = 0
pb.SC_CHAR_SENDCHAT_SENDERBLUEVIPTYPE_FIELD.type = 13
pb.SC_CHAR_SENDCHAT_SENDERBLUEVIPTYPE_FIELD.cpp_type = 3

pb.SC_CHAR_SENDCHAT.name = "SC_Char_SendChat"
pb.SC_CHAR_SENDCHAT.full_name = ".SC_Char_SendChat"
pb.SC_CHAR_SENDCHAT.nested_types = {}
pb.SC_CHAR_SENDCHAT.enum_types = {}
pb.SC_CHAR_SENDCHAT.fields = {pb.SC_CHAR_SENDCHAT_CHANNEL_FIELD, pb.SC_CHAR_SENDCHAT_SENDERID_FIELD, pb.SC_CHAR_SENDCHAT_SENDERNAME_FIELD, pb.SC_CHAR_SENDCHAT_CONTENT_FIELD, pb.SC_CHAR_SENDCHAT_BROADCASTPOS_FIELD, pb.SC_CHAR_SENDCHAT_BROADCASTSUBPOS_FIELD, pb.SC_CHAR_SENDCHAT_BROADCASTPARAM_FIELD, pb.SC_CHAR_SENDCHAT_LEVEL_FIELD, pb.SC_CHAR_SENDCHAT_VIP_FIELD, pb.SC_CHAR_SENDCHAT_CHATTYPE_FIELD, pb.SC_CHAR_SENDCHAT_SENDERVOCATION_FIELD, pb.SC_CHAR_SENDCHAT_SENDERCOUNTRY_FIELD, pb.SC_CHAR_SENDCHAT_SENDERZONE_FIELD, pb.SC_CHAR_SENDCHAT_TITLE_FIELD, pb.SC_CHAR_SENDCHAT_ANSWERFLAG_FIELD, pb.SC_CHAR_SENDCHAT_SENDERBLUEVIPLEVEL_FIELD, pb.SC_CHAR_SENDCHAT_SENDERBLUEVIPTYPE_FIELD}
pb.SC_CHAR_SENDCHAT.is_extendable = false
pb.SC_CHAR_SENDCHAT.extensions = {}
pb.SC_CHAR_ERRORRESULT_SHOWMODE_FIELD.name = "ShowMode"
pb.SC_CHAR_ERRORRESULT_SHOWMODE_FIELD.full_name = ".SC_Char_ErrorResult.ShowMode"
pb.SC_CHAR_ERRORRESULT_SHOWMODE_FIELD.number = 1
pb.SC_CHAR_ERRORRESULT_SHOWMODE_FIELD.index = 0
pb.SC_CHAR_ERRORRESULT_SHOWMODE_FIELD.label = 1
pb.SC_CHAR_ERRORRESULT_SHOWMODE_FIELD.has_default_value = false
pb.SC_CHAR_ERRORRESULT_SHOWMODE_FIELD.default_value = 0
pb.SC_CHAR_ERRORRESULT_SHOWMODE_FIELD.type = 13
pb.SC_CHAR_ERRORRESULT_SHOWMODE_FIELD.cpp_type = 3

pb.SC_CHAR_ERRORRESULT_RESULTCODE_FIELD.name = "ResultCode"
pb.SC_CHAR_ERRORRESULT_RESULTCODE_FIELD.full_name = ".SC_Char_ErrorResult.ResultCode"
pb.SC_CHAR_ERRORRESULT_RESULTCODE_FIELD.number = 2
pb.SC_CHAR_ERRORRESULT_RESULTCODE_FIELD.index = 1
pb.SC_CHAR_ERRORRESULT_RESULTCODE_FIELD.label = 1
pb.SC_CHAR_ERRORRESULT_RESULTCODE_FIELD.has_default_value = false
pb.SC_CHAR_ERRORRESULT_RESULTCODE_FIELD.default_value = 0
pb.SC_CHAR_ERRORRESULT_RESULTCODE_FIELD.type = 13
pb.SC_CHAR_ERRORRESULT_RESULTCODE_FIELD.cpp_type = 3

pb.SC_CHAR_ERRORRESULT_ERRORSTRING_FIELD.name = "ErrorString"
pb.SC_CHAR_ERRORRESULT_ERRORSTRING_FIELD.full_name = ".SC_Char_ErrorResult.ErrorString"
pb.SC_CHAR_ERRORRESULT_ERRORSTRING_FIELD.number = 3
pb.SC_CHAR_ERRORRESULT_ERRORSTRING_FIELD.index = 2
pb.SC_CHAR_ERRORRESULT_ERRORSTRING_FIELD.label = 1
pb.SC_CHAR_ERRORRESULT_ERRORSTRING_FIELD.has_default_value = false
pb.SC_CHAR_ERRORRESULT_ERRORSTRING_FIELD.default_value = ""
pb.SC_CHAR_ERRORRESULT_ERRORSTRING_FIELD.type = 9
pb.SC_CHAR_ERRORRESULT_ERRORSTRING_FIELD.cpp_type = 9

pb.SC_CHAR_ERRORRESULT.name = "SC_Char_ErrorResult"
pb.SC_CHAR_ERRORRESULT.full_name = ".SC_Char_ErrorResult"
pb.SC_CHAR_ERRORRESULT.nested_types = {}
pb.SC_CHAR_ERRORRESULT.enum_types = {}
pb.SC_CHAR_ERRORRESULT.fields = {pb.SC_CHAR_ERRORRESULT_SHOWMODE_FIELD, pb.SC_CHAR_ERRORRESULT_RESULTCODE_FIELD, pb.SC_CHAR_ERRORRESULT_ERRORSTRING_FIELD}
pb.SC_CHAR_ERRORRESULT.is_extendable = false
pb.SC_CHAR_ERRORRESULT.extensions = {}
pb.SC_CHAR_SHOWPRIZES_FORMTYPE_FIELD.name = "FormType"
pb.SC_CHAR_SHOWPRIZES_FORMTYPE_FIELD.full_name = ".SC_Char_ShowPrizes.FormType"
pb.SC_CHAR_SHOWPRIZES_FORMTYPE_FIELD.number = 1
pb.SC_CHAR_SHOWPRIZES_FORMTYPE_FIELD.index = 0
pb.SC_CHAR_SHOWPRIZES_FORMTYPE_FIELD.label = 2
pb.SC_CHAR_SHOWPRIZES_FORMTYPE_FIELD.has_default_value = false
pb.SC_CHAR_SHOWPRIZES_FORMTYPE_FIELD.default_value = 0
pb.SC_CHAR_SHOWPRIZES_FORMTYPE_FIELD.type = 13
pb.SC_CHAR_SHOWPRIZES_FORMTYPE_FIELD.cpp_type = 3

pb.SC_CHAR_SHOWPRIZES_PRIZES_FIELD.name = "Prizes"
pb.SC_CHAR_SHOWPRIZES_PRIZES_FIELD.full_name = ".SC_Char_ShowPrizes.Prizes"
pb.SC_CHAR_SHOWPRIZES_PRIZES_FIELD.number = 2
pb.SC_CHAR_SHOWPRIZES_PRIZES_FIELD.index = 1
pb.SC_CHAR_SHOWPRIZES_PRIZES_FIELD.label = 2
pb.SC_CHAR_SHOWPRIZES_PRIZES_FIELD.has_default_value = false
pb.SC_CHAR_SHOWPRIZES_PRIZES_FIELD.default_value = ""
pb.SC_CHAR_SHOWPRIZES_PRIZES_FIELD.type = 9
pb.SC_CHAR_SHOWPRIZES_PRIZES_FIELD.cpp_type = 9

pb.SC_CHAR_SHOWPRIZES_TITLE_FIELD.name = "Title"
pb.SC_CHAR_SHOWPRIZES_TITLE_FIELD.full_name = ".SC_Char_ShowPrizes.Title"
pb.SC_CHAR_SHOWPRIZES_TITLE_FIELD.number = 3
pb.SC_CHAR_SHOWPRIZES_TITLE_FIELD.index = 2
pb.SC_CHAR_SHOWPRIZES_TITLE_FIELD.label = 2
pb.SC_CHAR_SHOWPRIZES_TITLE_FIELD.has_default_value = false
pb.SC_CHAR_SHOWPRIZES_TITLE_FIELD.default_value = ""
pb.SC_CHAR_SHOWPRIZES_TITLE_FIELD.type = 9
pb.SC_CHAR_SHOWPRIZES_TITLE_FIELD.cpp_type = 9

pb.SC_CHAR_SHOWPRIZES_CONTENT_FIELD.name = "Content"
pb.SC_CHAR_SHOWPRIZES_CONTENT_FIELD.full_name = ".SC_Char_ShowPrizes.Content"
pb.SC_CHAR_SHOWPRIZES_CONTENT_FIELD.number = 4
pb.SC_CHAR_SHOWPRIZES_CONTENT_FIELD.index = 3
pb.SC_CHAR_SHOWPRIZES_CONTENT_FIELD.label = 2
pb.SC_CHAR_SHOWPRIZES_CONTENT_FIELD.has_default_value = false
pb.SC_CHAR_SHOWPRIZES_CONTENT_FIELD.default_value = ""
pb.SC_CHAR_SHOWPRIZES_CONTENT_FIELD.type = 9
pb.SC_CHAR_SHOWPRIZES_CONTENT_FIELD.cpp_type = 9

pb.SC_CHAR_SHOWPRIZES.name = "SC_Char_ShowPrizes"
pb.SC_CHAR_SHOWPRIZES.full_name = ".SC_Char_ShowPrizes"
pb.SC_CHAR_SHOWPRIZES.nested_types = {}
pb.SC_CHAR_SHOWPRIZES.enum_types = {}
pb.SC_CHAR_SHOWPRIZES.fields = {pb.SC_CHAR_SHOWPRIZES_FORMTYPE_FIELD, pb.SC_CHAR_SHOWPRIZES_PRIZES_FIELD, pb.SC_CHAR_SHOWPRIZES_TITLE_FIELD, pb.SC_CHAR_SHOWPRIZES_CONTENT_FIELD}
pb.SC_CHAR_SHOWPRIZES.is_extendable = false
pb.SC_CHAR_SHOWPRIZES.extensions = {}

CS_Char_SendChat = protobuf.Message(pb.CS_CHAR_SENDCHAT)
MSG_CHAT_ERRORRESULT = 2
MSG_CHAT_NONE = 0
MSG_CHAT_SENDCHAT = 1
MSG_CHAT_SHOWPRIZES = 3
SC_Char_ErrorResult = protobuf.Message(pb.SC_CHAR_ERRORRESULT)
SC_Char_SendChat = protobuf.Message(pb.SC_CHAR_SENDCHAT)
SC_Char_ShowPrizes = protobuf.Message(pb.SC_CHAR_SHOWPRIZES)

