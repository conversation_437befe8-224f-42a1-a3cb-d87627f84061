using System.Linq;
using System.Threading;

using Cysharp.Threading.Tasks;
using UnityEngine;
using CsvTables;

using Thing;

using View;

namespace ThingCdExecutors
{
    /// <summary>
    ///     玩家阵营的枪的执行器
    /// </summary>
    public class ActorGunCdExecutor : GunCdExecutor
    {
        /// <summary>
        ///     枪属于哪个玩家角色
        /// </summary>
        public ActorThing Actor => GunThing.Owner as ActorThing;

        /// <inheritdoc />
        public override async UniTaskVoid DoShoot(CancellationToken token)
        {
            // // V52.1 获取当前技能信息
            string skillName = GunThing.CsvRow_Gun.Value?.Name ?? "未知技能";
            int currentPriority = GetCurrentAttackPriority();
            float globalCooldown = GetCurrentGlobalCooldown();

            Debug.Log($"V52.1 技能尝试发射: {skillName}, 优先级={currentPriority}, 公共CD={globalCooldown}秒");

            // // V52.1 使用新的技能发射协调机制
            if (Actor != null && !Actor.RequestSkillFire(skillName, currentPriority, GunThing, globalCooldown))
            {
                Debug.Log($"V52.1 技能发射请求被拒绝: {skillName}");
                return;
            }

            // 找一个敌人作为攻击方向
            var distanceEnemy = Thing.FindEnemy();

            // 没有攻击目标，不发子弹
            if (distanceEnemy?.Thing2 == null)
            {
                // 根据移动状态播放相应动画
                if (SingletonMgr.Instance.BattleMgr.PlayerActor.PlayerMove.IsMoving)
                {
                    SingletonMgr.Instance.BattleMgr.PlayerActor.PlayAnimation("move01", true, false);
                }
                else
                {
                    SingletonMgr.Instance.BattleMgr.PlayerActor.PlayAnimation("idle01", true, false);
                }
                return;
            }

            // // V55.0 移除原有的OfferWeight动画播放逻辑，现在完全由GameManager.HandleAttackActionFromIcon统一处理
            // 注意：攻击动作和移动动作的播放现在由GameManager.HandleAttackActionFromIcon根据gun表OfferWeight字段统一控制

            // 设置角色朝向
            var shootDir = (distanceEnemy.Thing2.Position - Actor.Position);
            SingletonMgr.Instance.BattleMgr.PlayerActor.SetSpineModelDirection(shootDir.x > 0 ? -1 : 1);

            Debug.Log($"V52.1 技能通过所有检查，开始发射: {skillName}, 优先级={currentPriority}");

            // 调用基类的DoShoot方法
            base.DoShoot(token).Forget();
        }

        /// <summary>
        // /// V52.0 获取当前技能的攻击动作优先级
        /// 通过解析子弹配置中的Icon字段获取
        /// </summary>
        /// <returns>攻击动作优先级，默认返回0</returns>
        private int GetCurrentAttackPriority()
        {
            try
            {
                // 获取子弹ID
                int bulletId = (int)GunThing.GetTotalLong(X.PB.PropType.BulletId).FirstOrDefault();
                if (bulletId <= 0) return 0;

                // 获取子弹配置
                if (!SingletonMgr.Instance.CsvLoaderMgr.GetOrAddLoader<BulletCsv>().Dic.TryGetValue(bulletId, out var bulletCfg))
                    return 0;

                // 解析Icon字段获取优先级
                string iconConfig = bulletCfg.Icon;
                if (string.IsNullOrWhiteSpace(iconConfig) || iconConfig == "0") return 0;

                string[] parameters = iconConfig.Split(';');
                if (parameters.Length < 4) return 0;

                if (int.TryParse(parameters[3], out int priority))
                {
                    return priority;
                }
            }
            catch (System.Exception ex)
            {
                Debug.LogWarning($"V52.0 获取攻击动作优先级失败: {ex.Message}");
            }

            return 0; // 默认优先级
        }

        /// <summary>
        // /// V52.1 获取当前技能的公共CD时长
        /// 通过解析子弹配置中的Icon字段获取
        /// </summary>
        /// <returns>公共CD时长（秒），默认返回0</returns>
        private float GetCurrentGlobalCooldown()
        {
            try
            {
                // 获取子弹ID
                int bulletId = (int)GunThing.GetTotalLong(X.PB.PropType.BulletId).FirstOrDefault();
                if (bulletId <= 0) return 0;

                // 获取子弹配置
                if (!SingletonMgr.Instance.CsvLoaderMgr.GetOrAddLoader<BulletCsv>().Dic.TryGetValue(bulletId, out var bulletCfg))
                    return 0;

                // 解析Icon字段获取公共CD
                string iconConfig = bulletCfg.Icon;
                if (string.IsNullOrWhiteSpace(iconConfig) || iconConfig == "0") return 0;

                string[] parameters = iconConfig.Split(';');
                if (parameters.Length < 8) return 0; // 需要第8个参数（公共CD）

                if (float.TryParse(parameters[7], out float globalCooldown))
                {
                    return globalCooldown;
                }
            }
            catch (System.Exception ex)
            {
                Debug.LogWarning($"V52.1 获取公共CD失败: {ex.Message}");
            }

            return 0; // 默认无公共CD
        }

        /// <inheritdoc />
        protected override void OnAfterShoot()
        {
            SingletonMgr.Instance.BattleMgr.PlayerActor.PlayerMove.MoveDuration = 0;
        }

        /// <inheritdoc />
        public override void ClearShooter()
        {
            // 从所有怪物的 被锁列表 中移除该执行者
            _ = SingletonMgr.Instance.BattleMgr.Monsters.Select(enemy =>
            {
                enemy.LockedByShooters.Remove(this);
                return enemy;
            }).ToList();
        }
    }
}