--[[
********************************************************************
    created:	2024/06/19
    author :	李锦剑
    purpose:    Lua请求处理(模拟服务器处理Lua请求)
*********************************************************************
--]]

--lua请求ID
---@enum LuaRequestID
--lua请求ID
LuaRequestID = {
    --请求卡牌添加经验，参数：{equipID:integer(装备ID), loopTimes:integer(循环次数，用来实现多次升级)}
    NewCardAddExp = 1,
    --请求保存副本进度，参数：{stageID:integer(副本ID), isSuccess:boolean(是否通关), roundNo:integer(刷怪波数)}
    SaveEctypeProgress = 2,
    --请求领取副本宝箱奖励，参数：{stageID:integer(副本ID), index:integer(第几个宝箱)}
    GetEctypeTreasureBoxRewards = 3,
    --请求卡牌添加经验2，参数：{equipID:integer(装备ID), loopTimes:integer(循环次数，用来实现多次升级)}
    NewCardAddExp2 = 4,
    --请求领取签到奖励，参数 {ID:integer(PrizesevenSignin.csv表ID)}
    GetPrizesevenSignin = 5,
    --请求领取任务奖励，参数 {taskID:integer(Task.csv表ID)}
    GetTaskAward = 6,
}

--Lua请求地址
local luaRequestUrl = {
    [LuaRequestID.NewCardAddExp] = "LuaRequestNewCardAddExp",
    [LuaRequestID.SaveEctypeProgress] = "LuaRequestSaveEctypeProgress",
    [LuaRequestID.GetEctypeTreasureBoxRewards] = "LuaRequestGetEctypeTreasureBoxRewards",
    [LuaRequestID.NewCardAddExp2] = "LuaRequestNewCardAddExp2",
    [LuaRequestID.GetPrizesevenSignin] = "LuaRequestGetPrizesevenSignin",
    [LuaRequestID.GetTaskAward] = "LuaRequestGetTaskAward",
}

--Lua请求描述(给报错打印用)
local luaRequestDescribe = {
    [LuaRequestID.NewCardAddExp] = "请求卡牌添加经验",
    [LuaRequestID.SaveEctypeProgress] = "请求保存副本进度",
    [LuaRequestID.GetEctypeTreasureBoxRewards] = "请求领取副本宝箱奖励",
    [LuaRequestID.NewCardAddExp2] = "请求卡牌添加经验2",
    [LuaRequestID.GetPrizesevenSignin] = "请求领取签到奖励",
    [LuaRequestID.GetTaskAward] = "请求领取任务奖励",
}


---Lua功能模块
local luaFunctionalModule = require "LuaFunctionalModule"
--返回码
local code = RESULT_CODE_BASE

local o = {}
LuaModuleNew = o

---------------------------------------------------------
---发送lua请求(模拟：向服务器发请求)
---@param luaRequest string
---@param callback ?fun(resultCode:integer, content:string, callbackParam:any) 回调函数
---@param callbackParam ?any 回调参数
---------------------------------------------------------
function o.RunLuaRequest(luaRequest, callback, callbackParam)
    -- print("发送lua请求新 luaRequest", luaRequest)
    o.PlayerLuaRequest(luaRequest, callback or ResultCode.ShowResultCodeCallback, callbackParam)
end

---------------------------------------------------------
-- 处理Lua请求(模拟：服务器处理请求)
---@param luaRequest string
---@param callback fun(resultCode:integer, content:string, callbackParam:any) 回调函数
---@param callbackParam any 回调参数
---------------------------------------------------------
function o.PlayerLuaRequest(luaRequest, callback, callbackParam)
    --获取请求解析数据
    local parseData = o.ParseLuaRequest(luaRequest, callback, callbackParam)
    if parseData == nil then
        callback(code.RESULT_COMMON_ERROR[1], "")
        return
    end
    --获取请求功能函数
    local requestFunction = luaFunctionalModule[parseData.FuncName]
    if type(requestFunction) ~= 'function' then
        callback(code.RESULT_COMMON_ERROR[1], "")
        return
    end
    --执行请求函数
    requestFunction(parseData)
end

---------------------------------------------------------
-- 解析Lua请求语句
---@param luaRequest string
---@param callback fun(resultCode:integer, content:string, callbackParam:any) 回调函数
---@param callbackParam any 回调参数
---@return nil|ParseData
---------------------------------------------------------
function o.ParseLuaRequest(luaRequest, callback, callbackParam)
    if string.sub(luaRequest, 1, 10) ~= "LuaRequest" then
        return nil
    end

    local bodys = HelperL.Split(luaRequest, "?")
    if #bodys > 2 then
        return nil
    end

    --解析参数
    ---@type string[]
    local paramTable = {}
    if #bodys == 2 then
        local params = HelperL.Split(bodys[2], "&")
        local items
        for _, param in pairs(params) do
            items = HelperL.Split(param, "=")
            paramTable[items[1]] = items[2]
        end
    end

    --解析数据
    ---@class ParseData
    local parseData         = {}
    --请求链接
    parseData.Request       = luaRequest
    --方法名
    parseData.FuncName      = bodys[1]
    --请求参数原字符串
    parseData.paramString   = bodys[2]
    --请求参数
    parseData.ParamTable    = paramTable
    --回调参数
    parseData.CallbackParam = callbackParam
    --原回调函数
    parseData._callback     = callback
    --二次封装回调函数
    ---@type fun(self:ParseData, resultCode:integer, content:string)
    parseData.Callback      = function(self, resultCode, content)
        if type(self._callback) == 'function' then
            self._callback(resultCode or code.RESULT_COMMON_SUCCEED[1], content or "", self.CallbackParam)
        end
    end


    ---获取请求的参数
    ---@param self ParseData
    ---@param paramName string 参数名字
    ---@return string|nil
    parseData.GetRequestParam        = function(self, paramName)
        local value = self.ParamTable[tostring(paramName)]
        if value == nil then
            warn(self.Request .. "Param[" .. paramName .. "] not exist")
        end
        return value
    end

    -- 获取请求的数值参数(非nil)
    ---@param self ParseData
    ---@param paramName string 参数名字
    ---@param default ?integer 默认值
    ---@return integer
    parseData.GetRequestNumberParam  = function(self, paramName, default)
        local value = self:GetRequestParam(paramName)
        return math.floor(tonumber(value or default) or 0)
    end

    -- 获取请求的数值参数(非nil)
    ---@param self ParseData
    ---@param paramName string 参数名字
    ---@param default ?integer 默认值
    ---@return integer
    parseData.GetRequestFloatParam   = function(self, paramName, default)
        local value = self:GetRequestParam(paramName)
        return tonumber(value or default) or 0
    end

    -- 获取请求的字符串参数(非nil)
    ---@param self ParseData
    ---@param paramName string 参数名字
    ---@param default ?string 默认值
    ---@return string
    parseData.GetRequestStringParam  = function(self, paramName, default)
        local value = self:GetRequestParam(paramName)
        if value == nil then
            return default or ""
        end
        return value
    end

    -- 获取请求的boolean参数(非nil)
    ---@param self ParseData
    ---@param paramName string 参数名字
    ---@param default ?boolean 默认值
    ---@return boolean
    parseData.GetRequestBooleanParam = function(self, paramName, default)
        local value = self:GetRequestParam(paramName)
        if value == nil then
            return default == true
        end
        return value == "true"
    end

    --设置只读
    return setmetatable({}, { __index = parseData, __newindex = function(t, k, v) end })
end

--------------------------------------------------------------------
---发送lua请求
---@param requestID LuaRequestID 请求ID
---@param form ?table 请求参数
---@param callback ?fun(resultCode:integer, content:string, callbackParam?:any) 回调函数
---@param callbackParam ?any 回调参数
--------------------------------------------------------------------
function o.SendRequest(requestID, form, callback, callbackParam)
    callback = callback or ResultCode.ShowResultCodeCallback

    --获取Lua请求地址
    local url = luaRequestUrl[tonumber(requestID) or 0]
    if url == nil then
        callback(code.RESULT_COMMON_ERROR[1], "", callbackParam)
        return
    end

    --拼接参数
    local content = ''
    if form ~= nil then
        for k, v in pairs(form) do
            if content == '' then
                content = tostring(k) .. '=' .. tostring(v)
            else
                content = content .. '&' .. tostring(k) .. '=' .. tostring(v)
            end
        end
    end

    local luaRequest = url .. '?' .. content
    local des = luaRequestDescribe[tonumber(requestID) or 0] or ''
    print(string.format("(lua请求)%s=%s", des, luaRequest))

    --模拟发送到服务器
    o.PlayerLuaRequest(luaRequest, callback, callbackParam)
end
