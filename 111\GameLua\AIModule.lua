-- 行为逻辑处理
local luaID = ('AIModule')

require "AIMessage_pb"

local CacheParamType = 
{
	MsgPack = 1,
	GroundEffect = 2,
	DamageEffect = 3,
	
	Max = 3
}

AIModule = {}
AIModule.name = 'AIModule'
AIModule.commands = HelperL.NewQueue():New()

AIModule.paramCacheList = {}
for i = 1, CacheParamType.Max do
	AIModule.paramCacheList[i] = {}
end

local cacheVector3 = Vector3.New(0, 0, 0)

-- 处理位置同步包
local function DoMsgAIPOSITION(m)
	if m.IsStand ~= 0 then
		-- 目前只是处理瞬移而已，先无视ai方面发的校正位置消息
		return true
	end
	
	local e = EntityModule:GetEntity(m.EntityUID)
	if e == nil or e.model == nil or tolua.isnull(e.model) then return true end
	-- if e:GetProperty(PLAYER_FIELD.PLAYER_FIELD_DIE) > 0 then return true end
	local targetPos = cacheVector3:Set(m.DestX, m.DestY, m.DestZ)
	--local hit, hPos = Helper.GetAdjustPosition(targetPos, nil)
	--if not hit then
	--	warn('DoMsgAIPOSITION 找不到寻路点',targetPos.x,targetPos.y,targetPos.z)
	--	return true
	--end
	--targetPos = hPos
	
	--e.modelTrans.position = targetPos
	e.modelTrans:DOKill()
	e.modelTrans:DOMove(targetPos, 0.35):SetEase(TweeningEase.OutCubic)
	return true
end

local subTargetField = { 'SubGrid1', 'SubGrid2', 'SubGrid3', 'SubGrid4', }
local subTargetTypeField = { 'SubTargetType1', 'SubTargetType2', 'SubTargetType3', 'SubTargetType4', }
-- 处理攻击同步包
local function DoMsgAIATTACK(m)
	local e = EntityModule:GetEntity(m.EntityUID)
	if e == nil or e.model == nil or tolua.isnull(e.model) then return true end
	if e:GetProperty(PLAYER_FIELD.PLAYER_FIELD_DIE) > 0 then return true end
	local trans = nil
	local t = EntityModule:GetEntity(m.Target)
	if t ~= nil then trans = t.modelTrans end
	e.lastSkill = m.SerialNumber
	e.lastSkillTime = UnityEngine.Time.unscaledTime
	e.lastSkillParam = 0
	
	local skillID = m.Id
	local si = Schemes.Skill:Get(skillID)
	if not si then
		return true
	end
	
	--if trans then
	--	Helper.FlatLookat(e.modelTrans, trans)
	--end
	
	e:PlayAnimation(si.AttackAnimation)
	if si.BeginSound > 0 and e.aniComp then
		local soundScheme = Schemes.Sound:Get(si.BeginSound)
		if soundScheme ~= nil then
			if SoundManager:GetXmSoundValue() == 0 then
				e.aniComp:PlaySound(soundScheme.File)
			end
		else
			warn('DoMsgAIATTACK 未配置的技能声音ID ', si.ID, si.BeginSound)
		end
	end
	AIModule:CreateSkillEffect(e, si, trans)
	
	if trans then
		AIModule:CreateGroundEffect(e, m.Id, trans, trans.position, m.SerialNumber)
	end

	for i, v in ipairs(subTargetField) do
		if m[v] then
			--[[
			local subTarget = EntityModule:GetEntity(m[v])
			if subTarget ~= nil then
				local subTrans = subTarget.modelTrans
				if subTrans then
					AIModule:CreateGroundEffect(e, m.Id, subTrans, subTrans.position, m.SerialNumber)
				end
			end
			]]
			
			local gridData = TowerBattleManager:GetFieldGrid(m[v])
			if gridData and gridData.config then
				local gridConfig = gridData.config
				local moveType = m[subTargetTypeField[i]]
				if moveType and moveType > 0 then
					moveType = (moveType % 2) + 1
				end
				AIModule:CreateGroundEffect(e, m.Id, nil, Vector3(gridConfig.PosX, gridConfig.PosY, gridConfig.PosZ), m.SerialNumber, moveType)
			end
		end
	end
	return true
end

-- 处理攻击地点同步包
local function DoMsgAIATTACKPOS(m)
	local e = EntityModule:GetEntity(m.EntityUID)
	if e == nil or e.model == nil or tolua.isnull(e.model) then return true end
	if e:GetProperty(PLAYER_FIELD.PLAYER_FIELD_DIE) > 0 then return true end
	e.lastSkill = m.SerialNumber
	e.lastSkillTime = UnityEngine.Time.unscaledTime
	e.lastSkillParam = 0
	
	local skillID = e.transSkill or m.Id
	local si = Schemes.Skill:Get(skillID)
	if not si then
		return true
	end
	
	local vecAng = e.modelTrans.eulerAngles
	vecAng.y = m.Orientation
	e.modelTrans.eulerAngles = vecAng
	Vector3.Recycle(vecAng)
	
	e:PlayAnimation(si.AttackAnimation)
	if si.BeginSound > 0 and e.aniComp then
		local soundScheme = Schemes.Sound:Get(si.BeginSound)
		if soundScheme ~= nil then
			if SoundManager:GetXmSoundValue() == 0 then
				e.aniComp:PlaySound(soundScheme.File)
			end
		else
			warn('DoMsgAIATTACKPOS 未配置的技能声音ID ', si.ID, si.BeginSound)
		end
	end
	AIModule:CreateSkillEffect(e, si, nil)
	
	AIModule:CreateGroundEffect(e, m.Id, nil, Vector3(m.DestX, m.DestY, m.DestZ), m.SerialNumber)
	return true
end

-- 处理朝向同步包
local function DoMsgAITURNFACE(m)
	local e = EntityModule:GetEntity(m.EntityUID)
	if e == nil or e.model == nil then return true end
	e.modelTrans.eulerAngles = cacheVector3:Set(0, m.Orientation, 0)
	return true
end

-- 处理伤害同步包
local function DoMsgAIDAMAGE(m)
	local e = EntityModule:GetEntity(m.EntityUID)
	--if e == nil then return true end
	local self = AIModule
	local curTime = UnityEngine.Time.unscaledTime
	for _, v in ipairs(m.Victims) do
		local p = EntityModule:GetEntity(v.UID)
		if p ~= nil then
			local si = Schemes.Skill:Get(m.SkillId)
			local delay = 0
			local isFly = false
			local flySkill = nil
			if e and si then
				if e.lastSkill == m.SerialNumber then
					if si.CastEffectType and si.CastEffectType[1] == 1 then
						-- 飞行光效特殊处理成飞到了再飘字
						isFly = true
						flySkill = e.lastSkill
					else
						local deltaTime = curTime - e.lastSkillTime
						if deltaTime < m.SkillTime then
							delay = m.SkillTime - deltaTime
							if delay > 5 then
								warn('DoMsgAIDAMAGE delay > 5?? ', m.SkillId)
								delay = 5
							end
						end
					end
				end
			end
			
			if isFly then
				local param = self:GetCacheParam(CacheParamType.DamageEffect)
				param[1] = -1
				param[2] = m.EntityUID
				param[3] = v
				param[4] = si
				param[5] = m.SkillIndex + 1
				param[6] = flySkill
				table.insert(self.aiDamageDataCache, param)
			elseif delay < 0.01 then
				self:DoDamageEffect(e, v, p, si)
			else
				local param = self:GetCacheParam(CacheParamType.DamageEffect)
				param[1] = curTime + delay
				param[2] = m.EntityUID
				param[3] = v
				param[4] = si
				table.insert(self.aiDamageDataCache, param)
			end
		end
	end
	return true
end

-- 处理陷阱移动包
local function DoMsgAITRAPMOVE(m)
	local e = EntityModule:GetEntity(m.EntityUID)
	if e == nil or e.model == nil then return true end
	if SceneManager.isLoading then return true end
	if tolua.isnull(e.modelTrans) then return true end
	
	e.modelTrans.position = cacheVector3:Set(m.CurX, m.CurY, m.CurZ)
	e.modelTrans.eulerAngles = cacheVector3:Set(0, m.Face, 0)
	e.moveData = m
	return true
end

-- 处理显示特效包
local function DoMsgAISHOWFX(m)
	local targetLoc = Vector3(m.DestX, m.DestY, m.DestZ)
	EntityModule:ShowFx(nil, m.FxID, targetLoc, m.Orientation)
	Vector3.Recycle(targetLoc)
	return true
end

-- 处理特殊状态同步包
local function DoMsgAIEXSTATUS(m)
	local e = EntityModule:GetEntity(m.EntityUID)
	if e and e.buffLC then
		e.buffLC:SetExStatus(m.StatusType, m.HasStatus)
		
		-- 更新头顶
		if m.StatusType == ExStatus.NoDamage then
			if e.hpComp then
				e.hpComp:UpdateTarget()
			end
		end
	end
	return true
end

function AIModule.Handle(action, data)
	local self = AIModule
	-- 收到每个包都更新心跳时间
	GameModule:OnReceiveHeartBeat()

	if action == AIMessage_pb.MSG_AI_POSITION then
		local m = AIMessage_pb.SC_AI_POSITION()
		m:ParseFromString(data)
		local param = self:GetCacheParam(CacheParamType.MsgPack)
		param[1] = DoMsgAIPOSITION
		param[2] = m
		self.commands:pushLast(param)
	elseif action == AIMessage_pb.MSG_AI_RUN then
		--local m = AIMessage_pb.SC_AI_RUN()
		--m:ParseFromString(data)
		--local param = self:GetCacheParam(CacheParamType.MsgPack)
		--param[1] = DoMsgAIRUN
		--param[2] = m
		--self.commands:pushLast(param)
	elseif action == AIMessage_pb.MSG_AI_ATTACK then
		local m = AIMessage_pb.SC_AI_ATTACK()
		m:ParseFromString(data)
		local param = self:GetCacheParam(CacheParamType.MsgPack)
		param[1] = DoMsgAIATTACK
		param[2] = m
		self.commands:pushLast(param)
	elseif action == AIMessage_pb.MSG_AI_ATTACK_POS then
		local m = AIMessage_pb.SC_AI_ATTACK_POS()
		m:ParseFromString(data)
		local param = self:GetCacheParam(CacheParamType.MsgPack)
		param[1] = DoMsgAIATTACKPOS
		param[2] = m
		self.commands:pushLast(param)
	elseif action == AIMessage_pb.MSG_AI_TURNFACE then
		local m = AIMessage_pb.SC_AI_TURNFACE()
		m:ParseFromString(data)
		local param = self:GetCacheParam(CacheParamType.MsgPack)
		param[1] = DoMsgAITURNFACE
		param[2] = m
		self.commands:pushLast(param)
	elseif action == AIMessage_pb.MSG_AI_DAMAGE then
		local m = AIMessage_pb.SC_AI_DAMAGE()
		m:ParseFromString(data)
		local param = self:GetCacheParam(CacheParamType.MsgPack)
		param[1] = DoMsgAIDAMAGE
		param[2] = m
		self.commands:pushLast(param)
	elseif action == AIMessage_pb.MSG_AI_TRAPMOVE then
		local m = AIMessage_pb.SC_AI_TRAPMOVE()
		m:ParseFromString(data)
		local param = self:GetCacheParam(CacheParamType.MsgPack)
		param[1] = DoMsgAITRAPMOVE
		param[2] = m
		self.commands:pushLast(param)
	elseif action == AIMessage_pb.MSG_AI_SHOWFX then
		local m = AIMessage_pb.SC_AI_SHOWFX()
		m:ParseFromString(data)
		local param = self:GetCacheParam(CacheParamType.MsgPack)
		param[1] = DoMsgAISHOWFX
		param[2] = m
		self.commands:pushLast(param)
	elseif action == AIMessage_pb.MSG_AI_EXSTATUS then
		local m = AIMessage_pb.SC_AI_EXSTATUS()
		m:ParseFromString(data)
		local param = self:GetCacheParam(CacheParamType.MsgPack)
		param[1] = DoMsgAIEXSTATUS
		param[2] = m
		self.commands:pushLast(param)
	end
end

-- 处理地面特效
AIModule.groundEffectCache = {}
function AIModule:GroundEffect_Update()
	local groundEffectCache = self.groundEffectCache
	local size = #groundEffectCache
	if size == 0 then
		return
	end
	
	local curTime = UnityEngine.Time.unscaledTime
	for i = size, 1, -1 do
		local cache = groundEffectCache[i]
		if curTime > cache.delay then
			table.remove(groundEffectCache, i)
			self:DoCastEffectFunc(cache)
			cache.targetTrans = nil
			self:AddCacheParam(CacheParamType.GroundEffect, cache)
		end
	end
end

AIModule.aiDamageDataCache = {}
function AIModule:DamageEffect_Update()
	local aiDamageDataCache = self.aiDamageDataCache
	local damSize = #aiDamageDataCache
	if damSize > 0 then
		local curTime = UnityEngine.Time.unscaledTime
		for i = damSize, 1, -1 do
			local canEffect = false
			local cache = aiDamageDataCache[i]
			if cache[1] < 0 then
				-- 飞行光效等通知
				canEffect = true
				local e = EntityModule:GetEntity(cache[2])
				if e and e.lastSkill == cache[6] and e.aniComp and e.aniComp:GetCurAniName() ~= 'stand' then
					if curTime - e.lastSkillTime < 10 then
						if e.lastSkillParam < cache[5] then
							canEffect = false
						end
					end
				end
			elseif curTime > cache[1] then
				canEffect = true
			end
			
			if canEffect then
				table.remove(aiDamageDataCache, i)
				local e = EntityModule:GetEntity(cache[2])
				local v = cache[3]
				local p = EntityModule:GetEntity(v.UID)
				if p ~= nil then
					self:DoDamageEffect(e, v, p, cache[4])
				end
				self:AddCacheParam(CacheParamType.DamageEffect, cache)
			end
		end
	end
end

function AIModule:Trap_Update()
	local list = EntityModule:GetTrapList()
	for i, v in pairs(list) do
		local moveData = v.moveData
		if moveData then
			if not tolua.isnull(v.modelTrans) then
				local deltaTime = UnityEngine.Time.deltaTime
				local moveSpeed = moveData.MoveSpeed
				if moveData.MoveType == 1 then
					-- 直线往前
					local curPos = v.modelTrans.position
					local dir = v.modelTrans.forward
					dir.x = dir.x * moveSpeed * deltaTime
					dir.z = dir.z * moveSpeed * deltaTime
					curPos.x = curPos.x + dir.x
					curPos.z = curPos.z + dir.z
					v.modelTrans.position = curPos
					
					Vector3.Recycle(curPos)
					Vector3.Recycle(dir)
				elseif moveData.MoveType == 2 then
					-- 绕原点转圈
					local radius = moveData.Radius
					local curAngle = moveData.FloatParam1
					curAngle = curAngle + moveSpeed * deltaTime
					if curAngle < 0 then
						curAngle = curAngle + 360
					elseif curAngle >= 360 then
						curAngle = curAngle - 360
					end
					moveData.FloatParam1 = curAngle
					
					local x = radius * math.sin(curAngle * math.pi / 180)
					local z = radius * math.cos(curAngle * math.pi / 180)
					local targetPos = Vector3(moveData.BornX + x, moveData.BornY, moveData.BornZ + z)
					v.modelTrans.position = targetPos
					
					Vector3.Recycle(targetPos)
				else
					v.moveData = nil
				end
			else
				v.moveData = nil
			end
		end
	end
end

function AIModule:Update()
	-- 载入场景期间不处理
	if SceneManager.isLoading then return true end
	
	-- 每帧处理20个包
	for i = 1, 20 do
		local param = self.commands:popFirst()
		if not param then
			break
		end
		
		param[1](param[2], param[3])
		self:AddCacheParam(CacheParamType.MsgPack, param)
	end
	
	self:Trap_Update()
	self:DamageEffect_Update()
	self:GroundEffect_Update()
end

function AIModule.OnFollowEffectExplode(strParam, intParam)
	local self = AIModule
	if not strParam or not intParam then
		return
	end
	local entity = EntityModule:GetEntity(strParam)
	if not entity then
		return
	end
	
	if entity.lastSkill ~= intParam then
		return
	end
	
	if not entity.lastSkillParam then
		entity.lastSkillParam = 1
	else
		entity.lastSkillParam = entity.lastSkillParam + 1
	end
end

function AIModule:DoCastEffectFunc(param)
	local entity = EntityModule:GetEntity(param.entityUID)
	if not entity then
		return
	end
	
	local skillID = param.skillID
	local targetTrans = param.targetTrans
	local targetPos = param.targetPos
	local index = param.index
	local serial = param.serial
	local obj = entity.model
	if not obj or tolua.isnull(obj) then
		return
	end
	local si = Schemes.Skill:Get(skillID)
	if not si or not si.CastEffectID then
		return
	end

	local effectID = si.CastEffectID[index]
	local effectConfig = Schemes.FxConfig:Get(effectID)
	if not effectConfig then
		print('DoCastEffectFunc no fxconfig', effectID)
		return
	end
	
	--local isValidPos, newPos = Helper.GetAdjustPosition(targetPos, nil)
	--if isValidPos then
	--	targetPos = newPos
	--end
	
	local poolType = effectConfig.PoolType
	if effectConfig.PoolType == 2 then
		poolType = 1
	end
	
	local effectType = si.CastEffectType[index]
	if effectType == 0 or effectType == 3 then
		local effectObj = FxManager.CreateFx(effectConfig.FxName, poolType, entity.modelTrans.localPosition, 0, entity.modelTrans, effectConfig.AttachPoint)
		if effectObj == nil then
			--print('DoCastEffectFunc 创建光效失败 技能'..skillID..'  光效'..effectConfig.FxName)
			return
		end
		if effectType == 0 then
			effectObj.transform.position = targetPos
		end

	elseif effectType == 1 then
		local mode = 0
		
		local motionConfig = Schemes.CreatureMotionConfig:Get(si.MotionID)
		if motionConfig and motionConfig.DamageType > 2 or not targetTrans or tolua.isnull(targetTrans) then
			--print('DoCastEffectFunc 创建追踪光效，但没有目标 技能'..skillID..'  光效'..effectConfig.FxName)
			if tolua.isnull(entity.modelTrans) then
				return
			end
			mode = 1
		end

		local effectObj = FxManager.CreateFx(effectConfig.FxName, poolType, entity.modelTrans.localPosition, 0, entity.modelTrans, 'world')
		if effectObj == nil then
			--print('DoCastEffectFunc 创建光效失败 技能'..skillID..'  光效'..effectConfig.FxName)
			return
		end
		local srcPos = entity.modelTrans.position
		if effectConfig.AttachPoint and string.len(effectConfig.AttachPoint) > 1 then
			local srcBone = nil
			if effectConfig.AttachPoint == 'root' then
				srcBone = entity.modelTrans
			else
				srcBone = Helper.GetChild(entity.modelTrans, effectConfig.AttachPoint)
			end
			if srcBone then
				srcPos = srcBone.position
				if mode == 0 then
					local tarBone = nil
					local tarAttach = effectConfig.StringParam2
					if tarAttach == 'root' then
						tarBone = targetTrans
					else
						if string.len(tarAttach) <= 1 then
							tarAttach = 'Bip001 Spine1'
						end
						tarBone = Helper.GetChild(targetTrans, tarAttach)
					end
					if tarBone then
						targetTrans = tarBone
					end
				end
			end
		end
		srcPos.y = srcPos.y + effectConfig.FloatParam2
		effectObj.transform.position = srcPos
		-- 挂追踪comp
		local followComp = effectObj:GetComponent('FollowEffectComp')
		if not followComp then
			followComp = effectObj.gameObject:AddComponent(typeof(FollowEffectComp))
			if not followComp then
				print('DoCastEffectFunc 创建followComp失败 技能'..skillID..'  光效'..effectConfig.FxName)
				return
			end
		end

		followComp.yOffset = effectConfig.FloatParam2
		if mode == 0 then
			followComp:Init(targetTrans, effectConfig.FloatParam1, 0.5, 5.0)
		else
			followComp:InitEx(targetPos.x, targetPos.y, targetPos.z, effectConfig.FloatParam1, 0.1, 5.0)
			Vector3.Recycle(locForward)
			Vector3.Recycle(locEnd)
		end
		followComp.autoRotate = true
		if entity.tag == 'Hero' then
			followComp.isHighPriority = true
		end
		followComp.endStayTime = (effectConfig.IntParam1 * 0.001)
		followComp.explodeEffectName = effectConfig.StringParam1
		followComp.strParam = param.entityUID
		followComp.intParam = serial
		entity.lastSkillParam = 0
		Vector3.Recycle(srcPos)

	elseif effectType == 2 then
		local effectObj = FxManager.CreateFx(effectConfig.FxName, poolType, entity.modelTrans.localPosition, 0, entity.modelTrans, 'world')
		if effectObj == nil then
			--print('DoCastEffectFunc 创建光效失败 技能'..skillID..'  光效'..effectConfig.FxName)
			return
		end
		effectObj.transform.position = targetPos

	elseif effectType == 4 then
		if not targetPos then
			print('DoCastEffectFunc 创建光效失败 not targetPos ', debug.traceback())
			return
		end
		local effectObj = FxManager.CreateFx(effectConfig.FxName, poolType, entity.modelTrans.localPosition, 0, entity.modelTrans, 'world')
		if effectObj == nil then
			--print('DoCastEffectFunc 创建光效失败 技能'..skillID..'  光效'..effectConfig.FxName)
			return
		end

		local moveType = param.moveType
		local effectTrans = effectObj.transform
		local srcPos = effectTrans.position
		srcPos.y = srcPos.y + effectConfig.FloatParam2
		effectObj.transform.position = srcPos
		local moveDelta = targetPos - srcPos
		local moveDistance = moveDelta:Magnitude()
		local moveTime = 1
		if effectConfig.FloatParam1 > 0 then
			moveTime = moveDistance / effectConfig.FloatParam1
		end
		local completeFunc = function ()
			if tolua.isnull(effectTrans) then
				return
			end
			effectTrans:DOKill()
			FxManager.RemoveFx(effectTrans.gameObject)
			if string.len(effectConfig.StringParam1) > 0 then
				FxManager.CreateFx(effectConfig.StringParam1, 0, effectTrans.position, 0)
			end
		end
		effectTrans:DOBlendableMoveBy(moveDelta, moveTime):OnComplete(completeFunc)
		if moveType and moveType > 0 then
			local offsetDir = Vector3.Cross(moveDelta, CachedVector3:Set(0, 1, 0))
			if moveType == 1 then
				offsetDir = -offsetDir
			end
			offsetDir:SetNormalize()
			local offsetLen = 1
			offsetDir = offsetDir * offsetLen
			effectTrans:DOBlendableMoveBy(offsetDir, moveTime * 0.5):SetEase(TweeningEase.OutQuad)
			effectTrans:DOBlendableMoveBy(-offsetDir, moveTime * 0.5):SetEase(TweeningEase.InQuad):SetDelay(moveTime * 0.5)
		end
		
	else
		print('DoCastEffectFunc 技能的CastEffectType=', effectType)
		return
	end
end

function AIModule:CreateGroundEffect(entity, skillID, targetTrans, targetPos, serialNumber, moveType)
	if targetPos == nil then
		print('CreateGroundEffect targetPos == nil')
		return
	end

	local si = Schemes.Skill:Get(skillID)
	if si and si.CastEffectID then
		local curTime = UnityEngine.Time.unscaledTime
		for i, v in ipairs(si.CastEffectID) do
			if v > 0 then
				local param = self:GetCacheParam(CacheParamType.GroundEffect)
				param.entityUID = entity.uid
				param.skillID = skillID
				param.targetTrans = targetTrans
				param.targetPos = targetPos
				param.index = i
				param.serial = serialNumber
				param.moveType = moveType
				if si.CastEffectDelay[i] and si.CastEffectDelay[i] > 0 then
					param.delay = curTime + si.CastEffectDelay[i]
					table.insert(self.groundEffectCache, param)
				else
					self:DoCastEffectFunc(param)
				end
			end
		end
	end
end

function AIModule:CreateSkillEffect(entity, si, targetTrans)
	if entity == nil or si == nil then
		return
	end
	
	if si.AttackEffect1 > 0 then
		EntityModule:ShowFx(entity, si.AttackEffect1)
	end
	
	if si.AttackEffect2 > 0 then
		EntityModule:ShowFx(entity, si.AttackEffect2)
	end
end

-- 处理伤害同步包
AIModule.damageFlagText = {
	[1] = GetGameText(luaID, 1),
	[2] = GetGameText(luaID, 2),
	[3] = GetGameText(luaID, 3),
	[4] = GetGameText(luaID, 3),
	[5] = GetGameText(luaID, 4),
	[7] = GetGameText(luaID, 5),
}
function AIModule:DoDamageEffect(e, v, p, si)
	if not EntityModule.hero or not p.modelTrans then
		return
	end
	
	local asm = p.aniComp
	if asm and v.Blood < 0 then
		if si then
			if string.len(si.BehitEffect) > 1 then
				FxManager.CreateFx(si.BehitEffect, 0, p.modelTrans.localPosition, 0, p.modelTrans, 'Bip001 Spine1')
			end
			
			if string.len(si.BehitAnimation) > 1 then
				asm:PlayAnimation(si.BehitAnimation)
			end
			
			-- 受击音效
			if si.HitSound > 0 then
				SoundManager:PlaySound(si.HitSound)
			end
		end
	end
	if p:GetProperty(PLAYER_FIELD.PLAYER_FIELD_DIE) > 0 and v.Blood < 0 then
		if asm then
			asm:PlayAnimation('die')
		end
	end
	if v.Blood > 0 then
		HelperL.ShowMessage(TipType.MoveText, string.format('<color=#30ff30>+%s</color>', v.Blood), PosType.WorldPos, p.modelTrans.localPosition, TipDirection.ToUp)
	elseif v.Blood < 0 then
		HelperL.ShowMessage(TipType.MoveText, string.format('<color=#ff3030>%s</color>', v.Blood), PosType.WorldPos, p.modelTrans.localPosition, TipDirection.ToUp)
	end
	
	local flagText = AIModule.damageFlagText[v.Flag]
	if flagText then
		HelperL.ShowMessage(TipType.MoveText, flagText, PosType.WorldPos, p.modelTrans.localPosition, TipDirection.ToRight)
	end
end

function AIModule:AddCacheParam(paramType, param)
	local paramList = self.paramCacheList[paramType]
	if not paramList then
		paramList = {}
		self.paramCacheList[paramType] = paramList
	end
	
	table.insert(paramList, param)
end

function AIModule:GetCacheParam(paramType)
	local paramList = self.paramCacheList[paramType]
	if not paramList then
		paramList = {}
		self.paramCacheList[paramType] = paramList
	end
	
	local size = #paramList
	if size <= 0 then
		return {}
	end
	
	local result = paramList[size]
	paramList[size] = nil
	return result
end

function AIModule.OnDestroyEntity(uid, entity)
	local self = AIModule
	local aiDamageDataCache = self.aiDamageDataCache
	if aiDamageDataCache[1] then
		local needUpdate = false
		for i, v in ipairs(aiDamageDataCache) do
			if uid == v[2] or (v[3] and uid == v[3].UID) then
				v[1] = 0
				needUpdate = true
			end
		end

		if needUpdate then
			self:DamageEffect_Update()
		end
	end
end

Premier.Instance:RegisterLuaModule(MSG_MODULEID.MSG_MODULEID_AI, 'AIModule.Handle')

EventManager:Subscribe(EventID.OnDestroyEntity, AIModule.OnDestroyEntity)

-- 走Beat的话有报错时会终止。重要Update还是直接调吧
-- AIModule.updateHandle = UpdateBeat:CreateListener(AIModule.Update, AIModule)
-- UpdateBeat:AddListener(AIModule.updateHandle)