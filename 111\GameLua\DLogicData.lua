-- 逻辑数据定义
LOGIC_DATA = {
	DATA_LAST_DAYRESET_TIME                 = 1, -- 最后每日清0时间(0)
	DATA_LAST_WEEKRESET_TIME                = 2, -- 最后每周清0时间(0)
	DATA_LAST_MONTHRESET_TIME               = 3, -- 最后每月清0时间(0)
	DATA_WORLD_CHAR_LAST_TIME               = 4, -- 上一次在世界频道发言时间(0)
	DATA_TRUNK_TASK_CUR_SERIAL              = 5, -- 主线任务已完成序号(1)
	DATA_GOLDHAND_USE_TIMES                 = 6, -- 今天使用点金手次数(0)
	DATA_WEEK_SIGNIN_BITS                   = 7, -- 每日签到保存的数据位集(1)
	DATA_ARENAGAME_TODAYTIMES               = 8, -- 玩家今天可进入竞技场次数(1)
	DATA_ARENAGAME_BUYTIMES                 = 9, -- 玩家今天购买进入竞技场次数(1)
	DATA_ARENAGAME_TOPRANK                  = 10, -- 玩家竞技场中最高的排名(1)
	DATA_DIAMONDDRAW_JACKPOT                = 11, -- 元宝抽奖奖池(1)
	DATA_TREASURESNATCH_THROW_COUNT         = 12, -- 夺宝奇兵当前投色子的次数(1)
	DATA_TREASUREHOUSE_DATA                 = 13, -- 宝阁抽奖次数(地宝阁2|天宝阁1|仙宝阁0)(1)
	DATA_TEAM_FIRST_APPLY_OR_INVITE         = 14, -- 好友邀请|帮会成员邀请|好友申请|帮会成员申请(组队设置：优先接收帮会或好友的玩家)(1)
	DATA_HOLIDAYMAKE_DAYCOUNT2              = 15, -- 节日制作活动每日次数(1)
	DATA_EAST_CITY_DAY_INFO                 = 16, -- 神树献礼免费次数|生存挑战免费次数|玲珑宝塔免费次数|全民寻宝免费次数(1)
	DATA_EAST_CITY_PRIZE_FLAG               = 17, -- 全民寻宝宝箱领取标志(1)
	DATA_EAST_CITY_CLICK_TIME               = 18, -- 全民寻宝参与抽奖的时间(1)
	DATA_EAST_CITY_COST_TIME                = 19, -- 全民寻宝付费抽奖的时间(1)
	DATA_CHIEF_FIRST_KILL_HAVEGET           = 20, -- 首杀成就是否领取(1)
	DATA_CHIEF_FIRST_KILL_FLAG              = 21, -- 首领首杀标志(1)
	DATA_FOLLOWNPC_USECOUNT                 = 22, -- 跟随功能使用加奖励buff次数
	DATA_ENERGY_BUYTIMES                    = 23, -- 今日购买体力次数(1)
	DATA_ACTVONLINEPRIZE_STACKTIME          = 24, -- 在线奖励活动积累时间
	DATA_SOCIETY_ECTYPETOWER_CURSTAGE       = 25, -- 帮会副本塔-当前关卡 (主线闯关关卡)
	-- DATA_SOCIETY_ECTYPETOWER_HIGHSTAGE      = 26, -- 帮会副本塔-历史最高关卡
	
	DATA_SOCIETY_ECTYPETOWER_HIGHSTAGE      = 26, --1,广告祈福等级，2，广告探测等级，3广告突变等级
	DATA_SOCIETY_ECTYPETOWER_DAYRESETTIMES  = 27, -- 帮会副本塔-当日重置次数 (主线闯关次数)
	DATA_ACTIVITY_STACKCOUNT_1              = 28, -- 找回次数积累(前日积累次数|当前数值找回次数|当前资源找回次数)
	DATA_SOCIETY_ECTYPETOWER_TODAY_MAXSTAGE = 29, -- 帮会副本塔, 当日最高关卡
	DATA_SOCIETY_ECTYPETOWER_PRIZE          = 30, -- 帮会副本塔奖励领取标记
	DATA_BRANCH_ROUND_DATASET               = 31, -- 今日完成、购买任务轮数(1)
	DATA_MIDAS_TOUCH_USETIMES               = 32, -- 钱庄次数(1)
	DATA_LOTTERY_MONEY_NEXTTIME             = 33, -- 下一次免费金币抽奖时间(1)
	DATA_LOTTERY_MONEY_TODAYTIMES           = 34, -- 免费金币抽奖进入使用次数(1)
	DATA_LOTTERY_DIAMOND_NEXTTIME           = 35, -- 下一次免费钻石抽奖时间(1)
	DATA_LOTTERY_DIAMOND_FRISTTIME          = 36, -- 首次钻石抽奖时间记录(1)
	DATA_INIT_ACTOR_EQUIP_TIME              = 37, -- 初始化细胞装备时间(0)
	DATA_ACTVONLINEPRIZE_JOINTIME           = 38, -- 在线奖励活动最近参与时间
	DATA_ACTVONLINEPRIZE_GOTFLAG            = 39, -- 在线奖励活动领取标志
	DATA_LOGIN_GETTIME                      = 40, -- 累计登陆奖励领取情况(可领取天数|已领取天数)(1)
	DATA_TRUNK_TASK_ACCEPT_SERIAL           = 41, -- 主线任务已接受序号(1)
	DATA_SOLOGAME_WINSTREAK_PRIZE           = 42, -- 1v1 胜利奖励领取标记
	DATA_REDBAG_LIMITTIMES                  = 43, -- 世界红包领取次数限制
	DATA_REDBAG_SOCIETY_LIMITTIMES          = 44, -- 帮会红包领取次数限制
	DATA_SOCIETY_TRAIN_CONSUME              = 45, -- 每天消耗帮贡数值
	DATA_DAY_CONSUME_DIAMOND                = 46, -- 每天消耗元宝数值
	DATA_LOGIN_CONTINUE_DAYS                = 47, -- 连续登录天数
	DATA_ECTYPE_RESETTIMES1                 = 48, -- 副本今天已重置次数1
	DATA_ECTYPE_RESETTIMES2                 = 49, -- 副本今天已重置次数2
	DATA_ECTYPE_TODAYENTERECTYPETIMES       = 50, -- 当日进入副本次数
	DATA_ESCORTGAME_GOTPRIZEFLAG            = 51, -- 押镖活动当日领取奖励标志
	DATA_EAST_CITY_DRAW_TIMES               = 52, -- 全民寻宝抽奖累计次数(1)
	DATA_SMASHEGG_DATA1                     = 53, -- 砸蛋数据(累计次数的奖励是否已领(7-10位)+是否出过彩蛋(6位)+金蛋彩蛋数据(0-5位))
	DATA_SMASHEGG_DATA2                     = 54, -- 砸蛋数据(砸蛋中过的物品(0-3字节))
	DATA_SMASHEGG_DATA3                     = 55, -- 砸蛋数据(砸蛋中过的物品(0-1字节)+使用免费砸蛋次数(2字节))
	DATA_SMASHEGG_DATA4                     = 56, -- 每天砸蛋次数
	DATA_SMASHEGG_DATA5                     = 57, -- 砸蛋数据(砸蛋参与时间)
	DATA_NEWBIE_TARGETPRIZE_HAVEGET2        = 58, -- 新手目标奖励是否领取
	DATA_NEWBIE_TARGETPRIZE_HAVEGET1        = 59, -- 新手目标奖励是否领取
	DATA_TIMESHOPPING                       = 60, -- 限时特购礼包购买记录
	DATA_SPECIAL_ACTION_RECORD              = 61, -- 特殊行为记录
	DATA_GUIDEUI_RECORD                     = 62, -- 引导UI记录
	DATA_VIP_GET_PERDAY_GIFT                = 63, -- 每日VIP礼包(0vip礼包+1军衔礼包+2爱情宝盒+3周签到+4特权免费礼包+567仙宝阁+8910特权到期发邮件+特权礼包(17位开始))
	DATA_FOLLOWNPC_TIME                     = 64, -- 当日跟随npc时间(秒)
	DATA_ACTIVITY_GIFT_GET                  = 65, -- 活动礼包是否领取(媒体礼包|体验礼包)(1)
	DATA_SAVEBIT_NORESET                    = 66, -- 按位存不清零的数据(012特权到期发邮件)
	DATA_GUESS_FINGER_COUNT                 = 67, -- 猜拳活动期间累计次数(1) ( 爆破猫巡逻)
	DATA_SEVEN_COLOR_EGGS_CLICK_TIME        = 68, -- 七彩扭蛋参与扭蛋时间 ( 爆破猫巡逻)
	DATA_SEVEN_COLOR_EGGS_DRAW_TIMES        = 69, -- 七彩扭蛋累计扭蛋次数 ( 爆破猫巡逻)
	DATA_NEWBIE_TARGETPRIZE_HAVEGET4        = 70, -- 新手目标奖励是否领取( 爆破猫巡逻)
	DATA_FIRSTCHARGE_TIME                   = 71, -- 首冲时间(1)
	DATA_FIRSTCHARGE_DATA                   = 72, -- 超值礼包礼包是否领取(1)
	DATA_RECHARGECARD_VALIDTIME             = 73, -- 充值卡的有效时间(0)
	DATA_RECHARGECARD_CARDID                = 74, -- 充值卡ID(月、季、终身卡)(0)
	DATA_RECHARGECARD_GIFTGOT               = 75, -- 充值卡赠送礼包是否领取(1)
	DATA_RECHARGECARD_LIMITTIME             = 76, -- 充值卡买一送一限制次数(1)
	DATA_NEWBIE_TARGETPRIZE_HAVEGET5        = 77, -- 新手目标奖励是否领取
	DATA_NEWBIE_TARGETPRIZE_HAVEGET6        = 78, -- 新手目标奖励是否领取
	DATA_NEWBIE_TARGETPRIZE_HAVEGET7        = 79, -- 新手目标奖励是否领取
	DATA_PRIZETIME_CANGET                   = 80, -- 限时礼包是否可领取(1)
	DATA_PRIZETIME_HAVEGET                  = 81, -- 限时礼包是否已领取(1)
	DATA_LAST_RECHAREGE_TIME                = 82, -- 最近的一次充值时间(1)
	DATA_CONTINUE_RECHARGE_DAYS             = 83, -- 连续充值天数(1)
	DATA_CONTINUE_RECHARGE_PRIZES           = 84, -- 连续充值天数奖励领取标记(1)
	DATA_CONTINUE_RECHARGE_LASTTIME         = 85, -- 连续充值上一次参加活动的开启时间
	DATA_DAYRECHARGE_VALUE                  = 86, -- 当日充值金额
	DATA_RECHARGEDRAW_COUNT                 = 87, -- 充值抽奖次数
	DATA_COUNTRYWORSHIP_TIMES               = 88, -- 国王膜拜次数
	DATA_HOLIDAYMAKE_DAYCOUNT1              = 89, -- 节日制作活动每日次数(1)
	DATA_OFFLINETIME                        = 90, -- 离线时长
	DATA_NEWBIE_TARGETPRIZE_HAVEGET8        = 91, -- 新手目标奖励是否领取
	DATA_RECHARGESTORE_GETPRIZEFLAG         = 92, -- 充值返利商店奖励领取标志
	DATA_GUIDELOG_GUIDEID                   = 93, -- 引导记录引导ID(1)
	DATA_GUIDELOG_STEPID                    = 94, -- 引导记录步骤ID(1)
	DATA_SOCIETY_DEVELOPMENT_PRIZE          = 95, -- 帮会建设可领奖励， 根据位判断
	DATA_REDBAG_SOCIETY_TOTALTIMES          = 96, -- 领取帮派红包的总次数
	DATA_GIFTFLOWER_TOTALTIMES              = 97, -- 送花的总次数
	DATA_ENTERWAR_TOTALTIMES                = 98, -- 参与战场次数
	DATA_FRIENDBLESS_GAVEPRIZECOUNT         = 99, -- 当日发送好友附魔奖励次数
	DATA_FRIENDBLESS_RECEIVEPRIZECOUNT      = 100, -- 当日收到好友附魔奖励次数
	DATA_STACKCOUNT_1                       = 101, -- 粮草护送|军情刺探|火烧连营|每日历练积累次数
	DATA_STACKCOUNT_2                       = 102, -- 帮会采集|国家战备|金钱副本|每日跑环积累次数
	DATA_STACKCOUNT_3                       = 103, -- 坐骑副本|升级副本|强化副本|仙器副本积累次数
	DATA_STACKCOUNT_4                       = 104, -- 体魄副本|羽翼副本|巨鹿之战|城阳之战积累次数
	DATA_STACKCOUNT_5                       = 105, -- 鸿门宴|经验副本积累次数
	DATA_ESCORT_DAY_ROBTIMES                = 106, -- 当日劫镖次数
	DATA_COUNTRY_WILDFIGHT_PRIZE_TIMES      = 107, -- 中立区杀人奖励次数
	DATA_ECTYPETOWER_CURSTAGE               = 108, -- 副本塔-当前关卡
	DATA_ECTYPETOWER_HIGHSTAGE              = 109, -- 副本塔-最高关卡
	DATA_ECTYPETOWER_DAYRESETTIMES          = 110, -- 副本塔-当日重置次数
	DATA_ECTYPETOWER_HIGHSTAGETIME          = 111, -- 副本塔-最高关卡到达时间
	DATA_LEVELSTEPECTYPE_CURSTEP            = 112, -- 当前转生副本阶段
	DATA_SCENE_ENTERRECORD                  = 113, -- 进入场景记录
	DATA_LOGINPRIZE_GOTFLAG                 = 114, -- 登录礼包领取标记
	DATA_FUND_ISBUY                         = 115, -- 基金是否购买标记
	DATA_FUND_GETPRIZE                      = 116, -- 基金奖励领取标记
	DATA_NEWSERVERRANKPRIZE_GETFLAG         = 117, -- 新服排名奖励领取标记
	DATA_ACTIVITY_JOINFLAG                  = 118, -- 当日参与活动标记
	DATA_LASTTIME_LUCKY_DRAW                = 119, -- 上一次欢乐抽奖的时间
	DATA_LUCKY_DRAW_COUNT                   = 120, -- 欢乐抽奖累计抽奖次数
	DATA_LUCKY_DRAW_COST_DIAMOND            = 121, -- 欢乐抽奖期间花费的元宝
	DATA_ACTIVITYCOUNT_VALUE                = 122, -- 今日活跃度
	DATA_ACTIVITYCOUNT_PRIZEGETFLAG         = 123, -- 活跃度奖励领取标记
	DATA_VIPLEVEL_PRIZE                     = 124, -- vip等级礼包
	DATA_DOWNLOADPACK_PRIZEGETFLAG          = 125, -- 下载热更包礼包领取标记
	DATA_BANK_STAR                          = 126, -- 钱庄星级
	DATA_LIMIT_SPIKE_PRIZE_FLAG             = 127, -- 限时秒杀奖励领取标志（从0~31来索引）(1)
	DATA_LINGLONG_PAGODA_TIME               = 128, -- 玲珑宝塔玩家参与抽奖时间
	DATA_LINGLONG_PAGODA_STATE              = 129, -- 玲珑宝塔进度记录（权杖解封等级|CSV表格索引ID|当前哪个格子|当前第几层）(1)
	DATA_LINGLONG_PAGODA_DRAW_TIMES         = 130, -- 玲珑宝塔累计抽奖次数(1)
	DATA_TITLE_SLOT_NUM                     = 131, -- 称号位数量
	DATA_ARENAGAME_GETPRIZEFLAG             = 132, -- 竞技场每日领取奖励
	DATA_ACTIVITY_VIP_GIFT_GET              = 133, -- VIP礼包是否领取(1)改成每日特惠
	DATA_ACTIVITY_DAY_BUYRECORD             = 134, -- 活动日购买记录(升级日|强化日|坐骑日|仙器日|体魄日|突变日|羽翼日|猎魂日)(1)
	DATA_TASK_BRANCH_RESETCOUNT             = 135, -- 日常任务重置次数(0深入敌腹)
	DATA_STORE_BUYCOUNT                     = 136, -- 商城购买物品次数(0元宝商城1金币商城)
	DATA_FIRSTRECHARGE_NUM                  = 137, -- 超值礼包多少钱(1)
	DATA_ANYRECHARGE_GET                    = 138, -- 任意充值礼包领取(1)
	DATA_EXPWELFARE_GET                     = 139, -- 今天已经获得的经验福利
	DATA_EXPWELFARE_NOGET                   = 140, -- 今天未获得的经验福利
	DATA_ACTIVITY_POWERRANK_GIFT_GET        = 141, -- 新服实战排行礼包是否领取(1)
	DATA_ACTIVITY_WEEKEND_GIFT              = 142, -- 双休礼包是否领取(1)
	DATA_COUNTRY_LASTBETRAYTIME             = 143, -- 上次叛国时间
	DATA_COUNTRYWELFARE_GET                 = 144, -- 国家福利(1)
	DATA_DAYGOTTARGETPRIZE_FLAG             = 145, -- 当日是否获得过特定奖励标志
	DATA_STORE1_LASTRESET                   = 146, -- 商店1上次重置刷新次数时间(1)
	DATA_STORE2_LASTRESET                   = 147, -- 商店2上次重置刷新次数时间(1)
	DATA_STORE3_LASTRESET                   = 148, -- 商店3上次重置刷新次数时间(1)
	DATA_STORE4_LASTRESET                   = 149, -- 商店4上次重置刷新次数时间(1)
	DATA_STORE5_LASTRESET                   = 150, -- 商店5上次重置刷新次数时间(1)
	DATA_STORE6_LASTRESET                   = 151, -- 商店6上次重置刷新次数时间(1)
	DATA_STORE7_LASTRESET                   = 152, -- 商店7上次重置刷新次数时间(1)
	DATA_STORE8_LASTRESET                   = 153, -- 商店8上次重置刷新次数时间(1)
	DATA_WARABND_RANDECTYPEPRIZE_FLAG       = 154, -- 战队随机副本奖励领取标记
	DATA_BIGRECHARGE_DATA                   = 155, -- 单笔大充值数据(1)
	DATA_PRIZEDAYRECHARGE_CANGET            = 156, -- 每日累计充值是否可领取(1)
	DATA_PRIZEDAYRECHARGE                   = 157, -- 每日累计充值金额(1)
	DATA_PRIZE_TOTAL_RECHARGE               = 158, -- 累计充值金额(0)
	DATA_PRIZE_TOTAL_RECHARGE_TIME          = 159, -- 记录充值时累计充值活动的开始时间(0)
	DATA_NEWBIE_TARGETPRIZE_HAVEGET3        = 160, -- 新手目标奖励是否领取(1)
	DATA_COSTPRIZE_CANGET                   = 161, -- 累计消费奖励可领(1)
	DATA_COSTPRIZE_HADGOT                   = 162, -- 累计消费奖励已领(1)

	DATA_DIGMINE_LASTTIME                   = 163, -- 上一次挖宝的时间(1)
	DATA_DIGMINE_TIMES                      = 164, -- 挖宝次数(1)
	DATA_LOGIN_TIMES                        = 165, --上一次登陆时间(1)
	DATA_ONLINEPRIZE_CANGET                 = 166, --在线时长奖励是否可领取(1)
	DATA_LOGIN_TOTALTIME                    = 167, --累计在线时长(1)

	DATA_HOLIDAY_DRAW_FREE_TIMES            = 168, --节日抽奖免费次数（1）
	DATA_WILDBATTLE_TIMES                   = 169, --楚汉战场次数(当日进入地宫次数|当日总次数|当日可用次数)(1)
	DATA_WILDBATTLE_KILLNUM                 = 170, --楚汉战场总击杀数(1)
	DATA_WILDBATTLE_CONTINUEKILL            = 171, --楚汉战场最高连斩数(1)
	DATA_WILDBATTLE_KILLNUM_PERDAY          = 172, --楚汉战场每日总击杀数(1)
	DATA_WILDBATTLE_CONTKILL_PERDAY         = 173, --楚汉战场每日最高连斩数(1)
	DATA_WILDBATTLE_CURTIME                 = 174, --楚汉战场当前数据时间戳(0)
	DATA_ACTIVITY_GIFT_GET1                 = 175, --活动礼包是否领取(媒体礼包|体验礼包)(1)DATA_ACTIVITY_GIFT_GET不够用的追加
	DATA_COSTDIAMOND_TIME                   = 177, --累计消费时间(1)

	DATA_PACKET_VALID_SIZE                  = 178, -- 背包可用格子
	DATA_WAREHOUSE_VALID_SIZE               = 179, -- 仓库可用格子

	DATA_TEAM_INVITE_MIN_LEVEL_LIMIT        = 180, -- 自动接受邀请最小等级限制
	DATA_TEAM_INVITE_MAX_LEVEL_LIMIT        = 181, -- 自动接受邀请最大等级限制
	DATA_TEAM_APPLY_MIN_LEVEL_LIMIT         = 182, -- 自动接受申请最小等级限制
	DATA_TEAM_APPLY_MAX_LEVEL_LIMIT         = 183, -- 自动接受申请最大等级限制
	DATA_FRIEND_APPLY_LIMIT                 = 184, -- 好友申请设置

	DATA_AUTOADDHP_PERCENT                  = 185, -- 血量低于%x自动回血(1)
	DATA_HANDADDHP_COSTID                   = 186, -- 手动回血消耗药品ID(1)
	DATA_AUTOADDHP_COSTID                   = 187, -- 自动回血消耗药品ID(1)
	DATA_LIFESKILL_ACTIVEFLAG               = 188, -- 生活技能激活标记 按位读(1)
	DATA_SPACIALSKILL_USEDTIMES             = 189, -- 装备特技今日使用次数(1)
	DATA_DAY_RELIVE_TIMES                   = 190, -- 今日复活次数(1)
	DATA_COUNTRY_CHAR_LAST_TIME             = 191, --上一次在国家频道发言时间(0)
	DATA_GUIDELOG_EVENTID                   = 192, -- 记录引导事件ID
	DATA_GUIDEOPEN_ID                       = 193, -- 引导功能按钮开启记录
	DATA_TEAM_APPLY_MIN_POWER_LIMIT         = 194, -- 自动接受申请最小战力限制
	DATA_TEAM_INVITE_MIN_POWER_LIMIT        = 195, -- 自动接受邀请最小战力限制
	DATA_SOCIETR_ACTIVITYCOLLECT            = 196, --帮会采集活动次数
	--添加成就逻辑字段
	DATA_LOGIN_TOTALDAYS                    = 197, --玩家累计登录天数
	DATA_MONEY_TOTALNUM                     = 198, --财富最大值
	DATA_PARY_TOTALNUM                      = 199, --祈福累计数
	DATA_TRAIN_TOTALNUM                     = 200, --帮贡最大数
	DATA_RINGTASK_TOTALNUM                  = 201, --跑环任务累计完成数


	DATA_LOGINCHIEVE_HAVEGET = 202,      --登录成就是否领取
	DATA_LEVELACHIEVE_HAVEGET = 203,     --等级成就是否领取
	DATA_POWERCHIEVE_HAVEGET = 204,      --战力成就是否领取
	DATA_EQUIPMENT_HAVEGET = 205,        --装备强化等级成就是否领取
	DATA_SOCTETY_HAVEGET = 206,          --帮会成就是否领取
	DATA_DVINEEQUIPMENT_HAVEGET = 207,   --体魄成就是否领取
	DATA_HORSE_HAVEGET = 208,            --坐骑成就是否领取
	DATA_WING_HAVEGET = 209,             --挂件成就是否领取
	DATA_EQUIPMENTSTAR_HAVEGET = 210,    --装备升级成就是否领取
	DATA_MULTIPLE_RECHARGE_SUM = 211,    --多重充值金额
	DATA_MULTIPLE_RECHARGE_GET = 212,    --多重充值领取次数
	DATA_TREASURESNATCH_GRID_INFO = 213, --扔色子的次数|特殊宝箱获取到的数量|特殊宝箱随机的起始位置(1)
	DATA_TREASURESNATCH_GRID_FLAG = 214, --格子里的奖励是否被获取过了(1)
	DATA_DIAMONDDRAW_COUNT = 215,        --元宝抽奖次数(1)
	DATA_PRIZELOOP_HAVEGET = 216,        --阵营每日奖励是否领取 0-未领取  1-领取
	DATA_GEMCREATE_FREECOUNT = 217,      --猎魂今日已经免费使用次数|突变、精炼|帮会通天塔今日已重置次数
	DATA_THREEVSTHREE_WIN_PRIZE = 218,   --3v3宝箱奖励领取标记(1)
	DATA_LAST_TIME_PLAY_SOLO = 219,      -- 上一次参加1v1的活动时间
	DATA_LAST_TIME_PLAY_THREEVSTHREE = 220, -- 上一次参加3v3的活动时间
	DATA_TEAMSYSTEMREDBAG_TIMES = 221,   -- 组队系统红包领取次数
	DATA_SOCIETYSYSTEMREDBAG_TIMES = 222, -- 帮会系统红包领取次数
	DATA_TREASUREHUNT_DATA = 223,        -- 0：重置次数(1) + 1：体力数 + 2：行列 + 3：提示宝箱序号
	DATA_TREASUREHUNT_BOX = 224,         -- 寻宝活动的宝箱位置(1)
	DATA_TREASUREHUNT_BOMB = 225,        -- 寻宝活动的地雷位置(1)
	DATA_TREASUREHUNT_WALKPATH = 226,    -- 寻宝活动的走过路径(1)
	DATA_TREASURESNATCH_DATA = 227,      -- 当前位置|当前免费次数|最多可重置次数(1)
	DATA_CAN_CUSTOMIZETITLE = 228,       -- 是否能定制称号(1)
	DATA_LOGIN_ACTIVITY_DATA = 229,      -- 登录有礼活动数据(登录天数+是否领取奖励)(1)
	DATA_LOGIN_ACTIVITY_TIME = 230,      -- 记录登录有礼活动时间
	DATA_RECHARGE_RETURN_DATA = 231,     -- 充值返现活动是否领取(高16存是否可领，低16位存是否领取)(1)
	DATA_RECHARGE_RETURN_TIME = 232,     -- 记录充值返现活动时间
	DATA_ACTIVITY_VIP_GIFT_GET1 = 233,   -- 每日特惠
	DATA_LAST_COSTDIAMOND_TIME = 234,    -- 最近一次消费元宝时间
	DATA_PERDAY_COSTDIAMOND_GET = 235,   -- 每日累计消费是否已领取(1)
	DATA_PERDAY_COSTDIAMOND_NUM = 236,   -- 每日累计消费元宝数量(1)
	DATA_LOGIC_SOCIETY_ALCOHOL_TIMES = 237, -- 醉酒数据
	DATA_PRIVILEGECARD_TIME1 = 238,      -- 特权卡(贵族)有效期
	DATA_PRIVILEGECARD_TIME2 = 239,      -- 特权卡(王者)有效期
	DATA_PRIVILEGECARD_TIME3 = 240,      -- 特权卡(至尊)有效期
	DATA_SEVENDAYS_INVEST_GET = 241,     -- 七日投资是否领取(1)
	DATA_SEVENDAYS_INVEST_TIME = 242,    -- 七日投资时间_绑定元宝(1)
	DATA_TURNTABLE_USETIMES = 243,       -- (全民转盘免费使用次数:index 0|全民转盘购买次数:index 1)
	DATA_AURA_ACTIVE_WEAPON_STARS1 = 244, -- 灵气激活火之装备的星星
	DATA_AURA_ACTIVE_WEAPON_STARS2 = 245, -- 灵气激活火之装备的星星
	DATA_HOLIDAY_LOGIN_DATA = 246,       -- 节日登录有礼活动数据(登录天数+是否领取奖励)(1)
	DATA_HOLIDAY_LOGIN_TIME = 247,       -- 记录节日登录有礼活动时间
	DATA_AURA_ACTIVE_WEAPON_STARS3 = 248, -- 灵气激活火之装备的星星
	DATA_FESTIVAL_LIMIT_BUY = 249,       -- 节日特购保存
	DATA_FESTIVAL_LIMIT_BUY_JOINTIME = 250, -- 节日特购参加时间
	DATA_ALCHEMY_SPEEDUP_TIMES = 251,    -- 炼丹帮助次数（普通帮助次数|元宝帮助次数）
	DATA_TREASUREHUNT_STEP = 252,        -- 寻宝活动的历史步数(1)
	DATA_TREASUREHUNT_INFO = 253,        -- 寻宝活动的综合信息(1)
	DATA_SEVENDAY_BUYRECORD = 254,       -- 七日目标购买记录
	DATA_RANK_OTHER_DATA = 255,          -- 排行榜的其他数据(通天塔下线时的排名+竞技场下线时的排名)
	DATA_SUPPERZZLE_ITEMID1 = 256,       -- 对对碰第一排物品对应表ID(字节1-4)
	DATA_SUPPERZZLE_ITEMID2 = 257,       -- 对对碰第二排物品对应表ID(字节5-8)
	DATA_SUPPERZZLE_ITEMID3 = 258,       -- 对对碰第三排物品对应表ID(字节9-12)
	DATA_SUPPERZZLE_ITEMID4 = 259,       -- 对对碰第四排物品对应表ID(字节13-16)
	DATA_SUPPERZZLE_GETSIGN = 260,       -- 对对碰操作记录(翻过牌记录0-15位 + 是否领取配对奖励16位开始)
	DATA_SUPPERZZLE_JOINTIME = 261,      -- 对对碰参与时间
	DATA_SUPPERZZLE_MATCHTIMES = 262,    -- 对对碰配对次数
	DATA_SUPPERZZLE_RECHARGE = 263,      -- 对对碰期间充值金额
	DATA_RECHARGEBUY_ACTVTIME1 = 264,    -- 充值直销活动1开启时间
	DATA_RECHARGEBUY_ACTVTIME2 = 265,    -- 充值直销活动2开启时间
	DATA_RECHARGEBUY_ACTVTIME3 = 266,    -- 充值直销活动3开启时间
	DATA_RECHARGEBUY_ACTVCOUNT1 = 267,   -- 充值直销活动购买次数1(索引0-3)
	DATA_RECHARGEBUY_ACTVCOUNT2 = 268,   -- 充值直销活动购买次数2(索引0-3)
	DATA_RECHARGEBUY_ACTVCOUNT3 = 269,   -- 充值直销活动购买次数3(索引0-3)
	DATA_RECHARGEBUY_ACTVCOUNT4 = 270,   -- 充值直销活动购买次数4(索引0-3)
	DATA_LUCKYFLOP_ITEMID1 = 271,        -- 幸运翻牌随机到的6档物品(字节1-4)
	DATA_LUCKYFLOP_ITEMID2 = 272,        -- 幸运翻牌随机到的6档物品(字节5-6)
	DATA_LUCKYFLOP_RECORD = 273,         -- 翻过的牌面记录(000000)
	DATA_LUCKYFLOP_TIMES = 274,          -- 是否领取次数奖励(000000预留6个)+总翻牌次数(/1000000)--每日刷新
	DATA_LUCKYFLOP_JOINTIME = 275,       -- 幸运翻牌参与时间
	DATA_LUCKYFLOP_REFRESHTIME = 276,    -- 幸运翻牌刷新时间
	DATA_LUCKYFLOP_TOTALNUM = 277,       -- 幸运翻牌总翻牌次数(是否中过稀有物品(/1000000000) + 总次数(%1000000000))
	DATA_SEVENDAYS_INVEST_GET1 = 278,    -- 七日投资是否领取(1)
	DATA_SEVENDAYS_INVEST_TIME1 = 279,   -- 七日投资时间_坐骑(1)
	DATA_SEVENDAYS_INVEST_TIME2 = 280,   -- 七日投资时间_仙器(1)
	DATA_SEVENDAYS_INVEST_TIME3 = 281,   -- 七日投资时间_羽翼(1)
	DATA_SEVENDAYS_INVEST_TIME4 = 282,   -- 七日投资时间_体魄(1)
	DATA_SEVENDAYS_INVEST_TIME5 = 283,   -- 七日投资时间_升级(1)
	DATA_SEVENDAYS_INVEST_TIME6 = 284,   -- 七日投资时间_强化(1)
	DATA_DIVINE_TREE_DAY_INFO = 286,     -- 每日重置数据（女神祈愿免费次数|翡翠矿场免费次数|限时秒杀次数|神树当前等级）(1)
	DATA_DIVINE_TREE_GET_FLAG = 287,     -- 神树献礼等级礼包领取标志（从0~31来索引）(1)
	DATA_DIVINE_TREE_MATURITY = 288,     -- 神树献礼成熟度(1)
	DATA_HOLIDAYMAKE_DAYCOUNT3 = 289,    -- 节日制作活动每日次数
	DATA_JADEITE_MINE_NUM_1 = 290,       -- 翡翠矿石数量一（对应两种矿石）(1)
	DATA_JADEITE_MINE_NUM_2 = 291,       -- 翡翠矿石数量二（对应两种矿石）(1)
	DATA_JADEITE_MINE_NUM_3 = 292,       -- 翡翠矿石数量三（对应两种矿石）(1)
	DATA_JADEITE_MINE_NUM_4 = 293,       -- 翡翠矿石数量四（对应两种矿石）(1)
	DATA_JADEITE_MINE_NUM_5 = 294,       -- 翡翠矿石数量五（对应两种矿石）(1)
	DATA_JADEITE_MINE_NUM_6 = 295,       -- 翡翠矿石数量六（对应两种矿石）(1)
	DATA_JADEITE_MINE_DATA = 296,        -- 翡翠矿石四种矿位数据（对应四个字节）(1)
	DATA_JADEITE_MINE_UPDATE_TIME = 297, -- 翡翠矿石刷新的时间(1)
	DATA_JADEITE_MINE_PLAY_TIME = 298,   -- 翡翠矿石参与的时间
	DATA_JADEITE_MINE_COUNT = 299,       -- 翡翠矿石采矿次数
	DATA_HOLIDAYMAKE_DAYCOUNT4 = 300,    -- 节日制作活动每日次数
	DATA_HOLIDAYMAKE_DAYCOUNT5 = 301,    -- 节日制作活动每日次数
	DATA_GODDESS_PRAY_PLAY_TIME = 302,   -- 女神祈愿参与的时间
	DATA_GODDESS_PRAY_DIAMOND = 303,     -- 女神祈愿宝钻数量(1)
	DATA_GUESS_FINGER_DAY_INFO = 304,    -- 每日重置数据（保留|猜拳当日次数|天帝鉴宝免费次数|猜拳免费次数）(1)
	DATA_GUESS_FINGER_GET_FLAG = 305,    -- 猜拳奖励领取标志（从0~31来索引）(1)
	DATA_GUESS_FINGER_STATE = 306,       -- 猜拳活动数据（最高层数累计次数（两字节，配表）|结婚排名奖励标志|猜拳当前层数）(1)
	DATA_GUESS_FINGER_PLAY_TIME = 307,   -- 猜拳活动参与的时间
	DATA_MARRY_RANK_PLAY_TIME = 308,     -- 结婚排名活动参与的时间
	DATA_RANDOMSALE_GROUPINDEX = 309,    -- 随机礼包限购物品购买次数
	DATA_EMPEROR_TREASURE_COUNT = 310,   -- 天帝鉴宝累计次数
	DATA_EMPEROR_TREASURE_GOODS = 311,   -- 天帝鉴宝物品数据（奖励倍数|右边表格ID|中间表格ID|左边表格ID）(1)
	DATA_EMPEROR_TREASURE_PLAY_TIME = 312, -- 天帝鉴宝活动参与的时间
	DATA_SEVENDAYS_INVEST_GET2 = 313,    -- 七日投资是否领取(1)
	DATA_SEVENDAYS_INVEST_TIME7 = 314,   -- 七日投资时间_灵轮(1)
	DATA_SEVENDAYS_INVEST_TIME8 = 315,   -- 七日投资时间_天命(1)
	DATA_MARLBORO_PAVILION_PLAY_TIME = 316, -- 万宝阁活动参与的时间
	DATA_MARLBORO_PAVILION_SCORE = 317,  -- 万宝阁活动积分
	DATA_GOODS_USE_LIMIT_COUNT_1 = 318,  -- 道具使用次数1(1)
	DATA_GOODS_USE_LIMIT_COUNT_2 = 319,  -- 道具使用次数2(1)
	DATA_GOODS_USE_LIMIT_COUNT_3 = 320,  -- 道具使用次数3(1)
	DATA_GOODS_USE_LIMIT_COUNT_4 = 321,  -- 道具使用次数4(1)
	DATA_GOODS_USE_LIMIT_COUNT_5 = 322,  -- 道具使用次数5(1)
	DATA_HOLIDAYMAKE_DAYCOUNT6 = 323,    -- 节日制作活动每日次数
	DATA_RECHARGEBUY_ACTVTIME4 = 324,    --充值直销活动4开启时间
	DATA_RECHARGEBUY_ACTVCOUNT5 = 325,   -- 充值直销活动购买次数5(索引0-3)
	DATA_RECHARGEBUY_ACTVCOUNT6 = 326,   -- 充值直销活动购买次数6(索引0-3)
	DATA_WARBAND_ACTVCOUNTGOTFLAG = 327, -- 战队活跃度获得标记

	DATA_WARBAND_ACTVCOUNTVALUESAVE1 = 328, --广告祈福经验
	DATA_WARBAND_ACTVCOUNTVALUESAVE2 = 329, --广告探测经验
	DATA_WARBAND_ACTVCOUNTVALUESAVE3 = 330, --广告突变经验
	-- DATA_WARBAND_ACTVCOUNTVALUESAVE1 = 328,     -- 战队活跃度次数记录1
	-- DATA_WARBAND_ACTVCOUNTVALUESAVE2 = 329,     -- 战队活跃度次数记录2
	-- DATA_WARBAND_ACTVCOUNTVALUESAVE3 = 330,     -- 战队活跃度次数记录3

	DATA_WARBAND_DAILYPRIZEGOTFLAG = 331,       -- 战队日常奖励领取标记
	DATA_STACKPRIZE_GOTFLAG = 332,              -- 积累奖励领取标记
	DATA_WARBAND_RECHARGEPRIZEFLAG = 333,       -- 战队充值奖励领取标志
	DATA_WARBAND_TOTALRECHARGEPRIZE_COUNT = 334, -- 战队个人累充已领取次数和能领取次数（已领取次数（两字节）|能领取次数（两字节））
	DATA_STACKPRIZEONCE_GOTFLAG = 335,          -- 积累唯一奖励领取标记
	DATA_STACKPRIZEONCE_GOTFLAG2 = 336,         -- 积累唯一奖励领取标记2
	DATA_IDENTITY_STACKTIME = 337,              -- 身份验证防沉迷在线累积时间
	DATA_SOCIETY_DAILYPRIZEGOTFLAG = 338,       -- 帮会每日奖励领取标记(活跃度)
	DATA_SOCIETY_DAILYPRIZELOGINGOTFLAG = 339,  -- 帮会每日登录领取标记
	DATA_SOCIETY_RECHARGEPRIZEFLAG = 340,       -- 帮会充值奖励领取标志
	DATA_SOCIETY_TOTALRECHARGEPRIZE_COUNT = 341, -- 帮会个人累充已领取次数和能领取次数（已领取总次数|已领取个人的次数|能领取个人的次数）
	DATA_EXPWELFARE_GIVE_TIME = 342,            -- 经验福利赠送时长
	DATA_LOTTERY_LEVEL = 343,                   -- 祈福等级（当前等级|当前经验数）
	DATA_GEMCREATE_LEVEL = 344,                 -- 探测等级（当前等级|当前经验数）
	DATA_LEVELLIMITGIFT_FLAG = 345,             -- 等级限购礼包购买标志
	DATA_LOVEBOX_FLAG = 346,                    -- 爱情宝箱标志（目前用作是否购买，后面相关标志可在此扩展）
	DATA_ENTER_WEDDING_COUNT = 347,             -- 参加婚宴次数
	DATA_GODDESS_PRAY_PLAY_COUNT = 348,         -- 女神祈愿次数
	DATA_DIVINE_TREE_PLAY_COUNT = 349,          -- 神树献礼次数
	DATA_VIPSTORAGEPOOL_FLAG = 350,             -- VIP存储池领取标志
	DATA_MANDATORY_DIVORCE_COUNT = 351,         -- 强制离婚次数
	DATA_WISHLIST_GETSIGN = 352,                --心愿操作记录(许愿记录1-3个字节, 开服首冲次数 25位, 已许愿次数26-28位)-- 超出这个值必须修改数据库接口
	DATA_VIPPUSHGIFT_FLAG = 353,                -- 等级限购礼包购买标志
	DATA_PRIZE_GET_FLAG = 354,                  --奖励获取标志(创角奖励 0位, 千元元宝充值卡随机奖励 1位)
	DATA_CONTINUE_TOTAL_RECHARGE_DAYS = 355,    --连续累充记录(68元充值天数 1字节，奖励领取标志24-30位，累计天数标志 31位)
	DATA_CONFIDANTANSWER_DAY = 362,             -- 知己答题每日奖励
	DATA_CONFIDANTANSWER_HISTORY = 363,         -- 知己答题历史奖励
	DATA_WARBAND_RECHARGNEW_BUY = 364,          -- 战队充值新礼包是否已购买,每日清零(类型2是否购买|类型1是否购买)
	DATA_WARBAND_RECHARGNEW_GIFT = 365,         -- 战队充值新礼包(是否已领取类型2|是否已领取类型1|可否领取类型2|可否领取类型1)
	DATA_REILIWHEEL_HAVEGET = 367,              -- 星阵成就是否领取
	DATA_HOLYLIGHT_HAVEGET = 368,               -- 圣光成就是否领取
	DATA_NATIOANLDAY_RECHARG_VALUE = 369,       --贺中秋迎国庆活动期间充值金额
	DATA_NATIOANLDAY_HAVEGET = 370,             --贺中秋迎国庆奖励领取标志
	DATA_ARENA_RANK_HAVEGET = 371,              -- 竞技场冲榜(0-15位是否领取，16-31位对应是否激活)
	DATA_LUCKYFLOP_DAY_INFO = 372,              -- 幸运翻牌免费次数
	DATA_NEWWILDBATTLE_TIMES = 373,             -- 新楚汉战场次数(当日已进入次数|当日已购买次数)
	DATA_PVEGAME_SIGN = 374,                    -- 训练场购买次数|进入次数|是否冷却中
	DATA_PVEGAME_BOSS_KILLED_FLAG = 375,        -- 训练场怪物被击杀标志
	DATA_PVEGAME_BOSS_CURHP = 376,              -- 训练场怪物当前血量
	DATA_PVEGAME_CDTIME = 377,                  -- 训练场CD时间
	DATA_PVEGAME_PRIZESTART_TIME = 378,         -- 训练场累计奖励时间
	DATA_RECHARGEBUY_ACTVTIME5 = 379,           -- 充值直销活动5开启时间(银两特惠)
	DATA_RECHARGEBUY_ACTVCOUNT7 = 380,          -- 充值直销活动购买次数7(索引0-3)(银两特惠)
	DATA_USER_BIND_TIME = 381,                  -- 每次绑定玩家的时间
	DATA_VISTOR_IDENTITY_STACKTIME = 382,       -- 当日在线累积时间
	DATA_DAILY_INITIAL_POWER = 383,             -- 每日初始战力
	DATA_DIAMOND_BUYGIFT_BUYFLAG1 = 384,        -- 元宝购礼包购买标志1
	DATA_DIAMOND_BUYGIFT_BUYFLAG2 = 385,        -- 元宝购礼包购买标志2
	DATA_SEVENTDAYS_INVEST_BUYFLAG = 386,       -- 七日投资卡购买标志
	DATA_STORE_DISCOUNT_RANKEXP = 387,          -- 商店等级经验
	DATA_REFININGBODY_LEVEL = 388,              -- 炼体
	DATA_SOCIETY_ECTYPETOWER_HLEPCOUNT = 389,   -- 帮会副本塔协助次数
	DATA_ACTIV_LOGIN_CONTINUE_DAYS = 390,       -- 超级签到活动登陆天数(0~1)|是否领取(2~3)
	DATA_ACTIV_RECHARGE_CONTINUE_DAYS = 391,    -- 超级签到活动充值天数(0~1)|是否领取(2~3)
	DATA_LUCKYFRUITS_JOINTIME = 392,            -- 幸运水果参与时间(体验礼包购买时间)
	DATA_LUCKYFRUITS_GETFLAG = 393,             -- 幸运水果奖励领取标志(体验礼包励领取标志)
	DATA_LUCKYFRUITS_APPEAR_COUNT1 = 394,       -- 幸运水果1-3 出现三个水果一致的次数(%1000|/1000%1000|/1000000%1000)
	DATA_LUCKYFRUITS_APPEAR_COUNT2 = 395,       -- 幸运水果4-6 出现三个水果一致的次数(%1000|/1000%1000|/1000000%1000)
	DATA_LUCKYFRUITS_APPEAR_COUNT3 = 396,       -- 幸运水果7-9 出现三个水果一致的次数(%1000|/1000%1000|/1000000%1000)
	DATA_ACTIV_RECHARGE_CONTINUE_TIME = 397,    -- 超级签到活动上一次充值时间
	DATA_EQUIPADDITIONEFFECT_VALUE = 398,       -- 装备强化升级目标激活到的索引值(0升级|1强化)
	DATA_DICE_ROLLER_JOINTIME = 399,            -- 摇骰子参与时间
	DATA_DICE_ROLLER_TIMES_SIGN = 400,          -- 摇骰子次数记录(摇的次数%1000|摇特殊值(牌型6)的次数/1000%1000|购买次数/1000000%1000)
	DATA_TRAININGGROUND_COOLINGTIME_CLEAR = 401, -- 1-2 练武场挑战冷却时间是否清除 练武场抢回冷却时间是否清除
	DATA_TRAININGGROUND_CHALLENGEMINE_SIGN = 402, -- 练武场次数记录(开采次数(0)|挑战次数(1)|刷新次数(2)|购买挑战次数(3))
	DATA_TRAININGGROUND_COOLINGTIME = 403,      -- 练武场挑战冷却时间 (Cat每日挑战关卡)
	DATA_TRAININGGROUND_ROBBACKCOOLINGTIME = 404, -- 练武场抢回冷却时间 (Cat每日挑战关卡次数)
	DATA_LOGIN_ONLINETIME = 405,                -- 累计在线时长(不重置)
	DATA_BOSS_ECTYPETOWER_CURSTAGE = 406,       -- BOSS爬塔-当前关卡 (Cat挑战关卡)
	DATA_BOSS_ECTYPETOWER_HIGHSTAGE = 407,      -- BOSS爬塔-历史最高关卡 (Cat挑战关卡次数)
	DATA_BOSS_ECTYPETOWER_DAYRESETTIMES = 408,  -- BOSS爬塔-当日重置次数 (Cat金币副本关卡)
	DATA_BOSS_ECTYPETOWER_HIGHSTAGETIME = 409,  -- BOSS爬塔-最高关卡到达时间 (Cat金币副本关卡次数)
	DATA_SUPPER_WELFARE = 410,                  -- 超级福利(送VIP)
	DATA_NEWWILDBATTLE_KILLNUM_PERDAY = 411,    -- 新楚汉战场每日总击杀数
	DATA_NEWWILDBATTLE_CONTKILL_PERDAY = 412,   -- 新楚汉战场每日最高连斩数
	DATA_NEWWILDBATTLE_CURTIME = 413,           -- 新楚汉战场当前数据时间戳
	DATA_RECHARGESTORE_BEGINTIME = 414,         -- 充值商店活动开始时间
	DATA_RECHARGESTORE_TOTALRECHARGE = 415,     -- 充值商店总计充值金额
	DATA_RECHARGESTORE_CACHERECHARGE = 416,     -- 充值商店申请缓存充值金额
	DATA_RECHARGE_CURREALRECHARGE = 417,        -- 当前记录总真实重置额度
	DATA_SUBSISTTOWER_SIGN = 418,               -- 生存爬塔关卡数 | 关卡对应的波数 | 购买次数 | 进入次数 | 是否冷却中(索引0-3)
	DATA_SUBSISTTOWER_CDTIME = 419,             -- 生存爬塔CD时间
	DATA_SUBSISTTOWER_PRIZESTART_TIME = 420,    -- 生存爬塔累计奖励时间
	DATA_SUBSISTTOWER_FIRST_ENTERLEVEL_TIME = 421, -- 生存爬塔第一次进入每个关卡时间
	DATA_WARBAND_LEAVE_TIME = 422,              -- 离开战队时间戳
	DATA_MARKET_EVERYDAY_BUYNUM = 423,          -- 寄售每日购买金额
	DATA_DAZZLING_MOUNT_INVEST_TIME = 424,      -- 炫酷坐骑投资时间
	DATA_GORGEOUS_WING_INVEST_TIME = 425,       -- 华丽披风投资时间
	DATA_SPECIFY_NEWWING_INVEST_TIME = 426,     -- 金鹏展翅投资时间
	DATA_PERFECT_REILIWHEEL_INVEST_TIME = 427,  -- 完美星阵投资时间
	DATA_HONOUR_HOLYLIGHT_INVEST_TIME = 428,    -- 荣耀圣光投资时间
	DATA_THREE_INVEST_GETFLAG = 429,            -- 三日投资领取标志
	DATA_DAY_KILLMONSTER_NUM = 430,             -- 当日击杀怪物数量
	DATA_PLAYER_VIP_MAXLEVEL = 431,             -- vip最大等级
	DATA_FUNCTION_OPEN_GETFLAG1 = 432,          -- 功能开放领取标志1
	DATA_FUNCTION_OPEN_GETFLAG2 = 433,          -- 功能开放领取标志2
	DATA_NEWBIE_TARGETPRIZE_HAVEGET9 = 434,     -- 新手目标奖励是否领取
	DATA_SUPPERZZLE_DRAWTIMES = 435,            -- 对对碰已翻牌次数
	DATA_EMPEROR_TREASURE_EXCHANCOUNT = 436,    -- 天帝换宝累计次数
	DATA_EMPEROR_TREASURE_LASTTIMES = 437,      -- 天帝鉴宝剩余次数
	DATA_GODDESS_PRAY_LASTTIMES = 438,          -- 女神祈愿剩余次数
	DATA_GUESS_FINGER_LASTTIMES = 439,          -- 猜拳剩余次数
	DATA_JADEITE_MINE_LASTTIMES = 440,          -- 翡翠矿场剩余次数
	DATA_LINGLONG_PAGODA_LASTTIMES = 441,       -- 玲珑宝塔剩余次数
	DATA_LUCKYFLOP_LASTTIMES = 442,             -- 幸运翻牌剩余次数
	DATA_LUCKY_LASTTIMES = 443,                 -- 欢乐抽奖剩余次数
	DATA_SEVEN_COLOR_EGGS_LASTTIMES = 444,      -- 七彩扭蛋剩余次数
	DATA_SMASHEGG_LASTTIMES = 445,              -- 疯狂砸蛋剩余次数
	DATA_EAST_CITY_LASTTIMES = 446,             -- 全民寻宝剩余次数
	DATA_DIVINE_TREE_LASTTIMES = 447,           -- 神树献礼剩余次数
	DATA_SUPPERZZLE_LASTTIMES = 448,            -- 对对碰剩余次数
	DATA_TREASUREBOWL_BUYFLAG = 449,            -- 聚宝盆购买标志
	DATA_TREASUREBOWL_JOINTIME = 450,           -- 聚宝盆的参与时间
	DATA_SELF_SELECT_DRAW_JOINTIME = 451,       -- 自选抽奖参与时间
	DATA_SELF_SELECT_DRAW_SIGN = 452,           -- 自选抽奖操作记录(可抽奖次数|已抽奖次数)
	DATA_SELF_SELECT_DRAW_SIGN1 = 453,          -- 自选抽奖选中奖励1
	DATA_SELF_SELECT_DRAW_SIGN2 = 454,          -- 自选抽奖选中奖励2
	DATA_SELF_SELECT_DRAW_SIGN3 = 455,          -- 自选抽奖选中奖励3
	DATA_SELF_SELECT_DRAW_SIGN4 = 456,          -- 自选抽奖选中奖励4
	DATA_SELF_SELECT_DRAW_SIGN5 = 457,          -- 自选抽奖选中奖励5
	DATA_SELF_SELECT_DRAW_GETSIGN = 458,        -- 自选抽奖获取奖励
	DATA_NEXTDAYPRIZE_DATACOUNT = 459,          -- 隔日奖励(当日计算充值次数|当日已用免费次数|可抽次数|可领数量)
	DATA_NEXTDAYPRIZE_NEXTFREETIME = 460,       -- 隔日奖励下次免费时间
	DATA_CAN_CUSTOMIZETITLE2 = 461,             -- 是否能定制称号
	DATA_SEVEN_ELEMENTS_JOINTIME = 462,         -- 元素祭坛参与时间
	DATA_SEVEN_ELEMENTS_FRAGMENT1 = 463,        -- 元素祭坛碎片数量1(0-1)
	DATA_SEVEN_ELEMENTS_FRAGMENT2 = 464,        -- 元素祭坛碎片数量2(0-1)
	DATA_SEVEN_ELEMENTS_FRAGMENT3 = 465,        -- 元素祭坛碎片数量3(0-1)
	DATA_SEVEN_ELEMENTS_FRAGMENT4 = 466,        -- 元素祭坛碎片数量4(0-1)
	DATA_SEVEN_ELEMENTS_FRAGMENT5 = 467,        -- 元素祭坛碎片数量5(0-1)
	DATA_SEVEN_ELEMENTS_FRAGMENT6 = 468,        -- 元素祭坛碎片数量6(0-1)
	DATA_SEVEN_ELEMENTS_FRAGMENT7 = 469,        -- 元素祭坛碎片数量7(0-1)
	DATA_SEVEN_ELEMENTS_SCORE1 = 470,           -- 元素祭坛积分(活动结束重置)
	DATA_SEVEN_ELEMENTS_SCORE2 = 471,           -- 元素祭坛积分(活动结束不重置)
	DATA_SEVEN_ELEMENTS_DUSTCOUNT = 472,        -- 元素祭坛元素之尘数量
	DATA_SEVEN_ELEMENTS_GETSIGN = 473,          -- 元素祭坛宝箱领取标志
	DATA_NEW_SURVIVAL_CHALLENGE_JOINTIME = 474, -- 新生存挑战-参与时间
	DATA_NEW_SURVIVAL_CHALLENGE_HIGHSTAGE = 475, -- 新生存挑战-最高关卡
	DATA_ENTER_ECTYPE_TIMES_SIGN = 476,         -- 进入副本次数记录(新生存挑战|蛮夷入侵)
	DATA_NEXTDAYPRIZE_DATACOUNT2 = 477,         -- 隔日奖励充值次数(可领取充值次数)
	DATA_GUIDE_LOG_IDFLAG1 = 478,               -- 新手引导记录标志1
	DATA_GUIDE_LOG_IDFLAG2 = 479,               -- 新手引导记录标志2
	DATA_TREASUREBOX_UNLOCKID = 480,            -- 当前解锁宝箱ID
	DATA_TREASUREBOX_UNLOCKTIME = 481,          -- 宝箱解锁开始时间
	DATA_ENERGY_CHECKTIME = 482,                -- 上次加体力时间
	DATA_SEGMENT_FIRSTSIGN = 483,               -- 段位礼包首次获得标志
	DATA_SEGMENT_AWARDSIGN = 484,               -- 段位礼包领取标志
	DATA_ADVERTISE_AWARDFLAG = 485,             -- 汪星人--祈福广告等级经验--看广告礼包领取标志（对应每个礼包）
	DATA_SEGMENT_FIRSTSIGN1 = 486,              -- 汪星人--探测广告等级经验--段位礼包首次获得标志1
	DATA_SEGMENT_AWARDSIGN1 = 487,              -- 汪星人--突变广告等级经验--段位礼包领取标志1
	DATA_Cat_Friend_1 = 488,                    -- 宠物购买记录1
	DATA_Cat_Friend_2 = 489,                    -- 宠物购买记录2
	DATA_Cat_Friend_3 = 490,                    -- 宠物购买记录3
	DATA_Cat_Friend_4 = 491,                    -- 宠物购买记录4
	DATA_Cat_Friend_5 = 492,                    -- 宠物购买记录5
	DATA_Cat_Friend_6 = 493,                    -- 宠物购买记录6
	DATA_POLISH_LEVEL = 494,                    -- 突变等级
	DATA_NEWECTYPE_MAXLEVEL1 = 495,
	DATA_NEWECTYPE_MAXLEVEL2 = 496,
	DATA_NEWECTYPE_MAXLEVEL3 = 497,
	DATA_NEWECTYPE_MAXLEVEL4 = 498,
	DATA_NEWECTYPE_MAXLEVEL5 = 499,
	DATA_NEWECTYPE_MAXLEVEL6 = 500,
	DATA_NEWECTYPE_MAXLEVEL7 = 501,
	DATA_NEWECTYPE_MAXLEVEL8 = 502,
	DATA_NEWECTYPE_MAXLEVEL9 = 503,
	DATA_NEWECTYPE_MAXLEVEL10 = 504,
	DATA_GOLDCHALLENGE_MAXLEVEL = 505, --金币副本记录新增副本最高关卡
	DATA_AUTO_LOTTEYCOUNT = 506,    --自动祈福次数
	DATA_AUTO_GEMCOUNT = 507,       --自动探测次数
	DATA_AUTO_POLISHCOUNT = 508,    --自动突变次数
	DATA_ECTYPESEVEN_MAXKILLNUM = 509,

	-- 超出这个值必须修改数据库接口
	DATA_LOGICDATA_MAXID = 512,
};
