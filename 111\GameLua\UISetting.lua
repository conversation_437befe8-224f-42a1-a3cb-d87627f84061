local luaID = ('UISetting')

local m = {}
local switchData = {
	{ Name = 'xmSound',              Text = GetGameText(luaID, 2), Icon = 'sz_tb_01' },
	-- { Name = 'SYSTEM_MOVE_BUFF',     Text = GetGameText(luaID, 3), Icon = '' },
	{ Name = 'xmMusic',              Text = GetGameText(luaID, 4), Icon = 'sz_tb_02' },
	--{ Name = 'SYSTEM_MOBILE_VIBATE', Text = GetGameText(luaID, 5), Icon = 'sz_tb_03' },
	-- { Name = 'SYSTEM_AUTO_SHOOT',    Text = GetGameText(luaID, 10), Icon = '' },
}
local switchList = {}
-- 初始化
function m.OnCreate()
	m.objList.Txt_Title.text = GetGameText(luaID, 1)
	m.objList.Txt_ExitGame.text = GetGameText(luaID, 6)
	m.objList.Txt_XiuFuYiChang.text = GetGameText(luaID, 7)
	m.objList.Txt_FanKuiWenTi.text = GetGameText(luaID, 8)
	m.objList.Txt_ActivationCode.text = GetGameText(luaID, 11)
	m.objList.Txt_Version.text = string.format(GetGameText(luaID, 9), Helper.GetBigVer())

	m.closeType = 1
	for i, v in ipairs(switchData) do
		table.insert(switchList, m.CreateSettingItem(v))
	end

	-- m.objList.Btn_ActivationCode.gameObject:SetActive(GameLuaAPI.GetPlatform() ~= "ios")
	-- m.objList.Btn_Update.gameObject:SetActive(SWITCH and SWITCH.UPDATE_APK and GameLuaAPI.GetPlatform() ~= "webgl")

	m.objList.Btn_ActivationCode.gameObject:SetActive(false)
	m.objList.Btn_Update.gameObject:SetActive(false)

	m.RegisterClickEvent()
	return true
end

--------------------------------------------------------------------
-- 注册点击事件
--------------------------------------------------------------------
function m.RegisterClickEvent()
	m.objList.Btn_Quit.onClick:AddListenerEx(function()
		if m.closeType == 1 then
			m:CloseSelf()
		else
			m.objList.ActivationPanel.gameObject:SetActive(false)
			m.objList.SettingPanel.gameObject:SetActive(true)
			m.closeType = 1
		end
	end)
	m.objList.Btn_ActivationCode.onClick:AddListenerEx(m.OnClickActivationPanel)
	m.objList.Btn_GetCode.onClick:AddListenerEx(m.GetActvCodePrize)
	m.objList.Btn_GetCodeYaoQingMa.onClick:AddListenerEx(m.GetActvCodeYaoQingPrize)
	m.objList.Btn_Update.onClick:AddListenerEx(m.UpdateApk)
end

-- 窗口开启
function m.OnOpen()
	m.objList.ActivationPanel.gameObject:SetActive(false)
	m.objList.SettingPanel.gameObject:SetActive(true)
end

--创建设置栏
function m.CreateSettingItem(data)
	local item = {}
	item.data = data
	item.com = m:CreateSubItem(m.objList.TogContenter, m.objList.Item_Switch)
	item.com.Txt_Name.text = data.Text
	AtlasManager:AsyncGetSprite(data.Icon, item.com.Img_Icon)
	item.Update = function()
		local isOpen = m.SwitchIsOpen(item.data.Name)
		item.com.Img_Open.gameObject:SetActive(isOpen)
		item.com.Txt_Open.text = (isOpen and GetGameText(luaID, 15) or '') .. GetGameText(luaID, 16)
		item.com.Img_Close.gameObject:SetActive(not isOpen)
		item.com.Txt_Close.text = ((not isOpen) and GetGameText(luaID, 15) or '') .. GetGameText(luaID, 17)
	end
	item.com.Btn_Switch.onClick:AddListenerEx(function()
		m.SetSetting(item.data.Name)
		item.Update()
	end)
	item.Update()
	return item
end

--开关是否开启
function m.SwitchIsOpen(name)
	--震动需特殊处理
	if name == 'SYSTEM_MOBILE_VIBATE' then
		local num = PlayerPrefs.GetInt(name, 1)
		return num == 1
	end
	local num = PlayerPrefs.GetInt(name, 0)
	return num == 0
end

--开关设置
function m.SetSetting(name)
	if name == 'xmSound' then
		SoundManager:SetXmSound()
	elseif name == 'xmMusic' then
		SoundManager:SetMusic()
	else
		PlayerPrefsManager:SetInt(name, PlayerPrefsManager:GetInt(name) == 1 and 0 or 1)
	end
end

function m.OnClickActivationPanel()
	m.objList.SettingPanel.gameObject:SetActive(false)
	m.objList.ActivationPanel.gameObject:SetActive(true)
	m.closeType = 2
end

-- 领取激活码礼包按钮
function m.GetActvCodePrize()
	local uiInput = m.objList.Inp_Code.text
	if string.len(uiInput) < 5 then
		HelperL.ShowMessage(TipType.FlowText, GetGameText(luaID, 12))
		return
	end

	local function OnGetActvCodeCallback(result, value)
		if result ~= RESULT_CODE.RESULT_COMMON_SUCCEED then
			HelperL.ShowMessage(TipType.FlowText, ResultCode:GetResultCodeStr(result, value))
		else
			m.objList.Inp_Code.text = ''
			HelperL.ShowMessage(TipType.FlowText, GetGameText(luaID, 13))
		end
	end

	LuaModule.RunLuaRequest('LuaRequestGetActivityGift?giftID=0&code=' .. uiInput, OnGetActvCodeCallback)
end

-- 领取邀请码礼包按钮
function m.GetActvCodeYaoQingPrize()
	local uiInput = m.objList.Inp_CodeYaoQingMa.text
	if string.len(uiInput) < 5 then
		HelperL.ShowMessage(TipType.FlowText, GetGameText(luaID, 12))
		return
	end

	local isGet = HeroDataManager:GetLogicBit(LOGIC_DATA.DATA_REDBAG_SOCIETY_LIMITTIMES, 20)
	if isGet == 1 then
		HelperL.ShowMessage(TipType.FlowText, GetGameText(luaID, 14))
		return
	end

	local cs = GameMessage_pb.CS_INVITE_ACCEPTINVITELI()
	cs.InviteCode = uiInput
	Premier.Instance:GetNetwork():SendFromLua(
		ENDPOINT.ENDPOINT_GAMECLIENT,
		ENDPOINT.ENDPOINT_GAMESERVER,
		MSG_MODULEID.MSG_MODULEID_GAME,
		GameMessage_pb.MSG_INVITE_ACCEPTINVITELI,
		cs:SerializeToString()
	)
	m.objList.Inp_CodeYaoQingMa.text = ''
end

--更新Apk
function m.UpdateApk()
	GameLuaAPI.UpdateApk()
end

--注销登陆
function m.OnLoginOutClick()
	LoginModule.Logout()
end

--切换账号
function m.SwitchAccount()
	PlayerPrefsManager:SetString("UserName", '')
	m.OnLoginOutClick()
end

return m
