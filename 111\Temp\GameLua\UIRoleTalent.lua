--[[
********************************************************************
    created:	2024/06/14
    author :	李锦剑
    purpose:    角色天赋
*********************************************************************
--]]

local luaID = "UIRoleTalent"
--显示天赋数量
local ShowTalentNum = 100

---@class UIRoleTalent:UIWndBase
local m = {}

--------------------------------------------------------------------
-- 订阅事件列表
--------------------------------------------------------------------
function m.GetOpenEventList()
    return {
        [EventID.StoreList] = m.UpdateView,
        [EventID.LogicDataChange] = m.LogicDataChange,
        [EventID.OnGoodsPropChange] = m.UpdateView,
        [EventID.ActorDataChanged] = m.UpdateView,
    }
end

--------------------------------------------------------------------
-- 初始化
--------------------------------------------------------------------
function m.OnCreate()
    m.SmeltID = 1
    local list = Schemes.EquipSmeltStarData:GetByOprateType(RoleTalent_OprateType)
    if list and #list > 0 then
        m.SmeltID = list[1].EquipSmeltStarId
    end
    m.lastTime = 0
    ---@type Item_Talent[]
    m.Item_Talent_List = {}
    m.Item_Talent_X = m.objList.Item_Talent1:GetRectTransform().sizeDelta.x

    ---@type Item_Talent_JD[]
    m.Item_Talent_JD_List = {}

    local equipSmeltList = Schemes.EquipSmeltStar:GetBySmeltID(m.SmeltID) or {}
    ShowTalentNum = math.min(ShowTalentNum, (#equipSmeltList) - 1)
    for i = 1, ShowTalentNum, 1 do
        m.Item_Talent_List[i] = m.Create_Item_Talent(i, equipSmeltList[i + 1])
    end
    m.RegisterClickEvent()    

    -- m.objList.ImgIcon.transform:DOLocalMoveY(m.objList.ImgIcon.transform.localPosition.y - 20, 1):SetLoops(-1,
	-- 	DG.Tweening.LoopType.Yoyo)
    --延迟刷新
    local timer = Timer.New(m.UpdateView, 1, 1)
    timer:Start()
    return true
end

function m.LogicDataChange()
    local starLvl = GamePlayerData.ActorEquipNew:GetEquipUpgradeLevel(m.lastSmeltID)
    print("starLvl ===== ",starLvl)
    print("m.lastStarLvl ===== ",m.lastStarLvl)
    if starLvl > m.lastStarLvl then
        HelperL.ShowMessage(TipType.FlowText, '星篆升级成功，等级+1。')
    end
    m.UpdateTalentInof()
end

--------------------------------------------------------------------
-- 窗口开启
--------------------------------------------------------------------
function m:OnOpen()
    m.UpdateView()
end

--------------------------------------------------------------------
-- 注册点击事件
--------------------------------------------------------------------
function m.RegisterClickEvent()
    m:AddClick(m.objList.Btn_Close, m.CloseUI)
    m:AddClick(m.objList.Btn_Upgrade, function()
        m.Upgrade(m.SmeltID)
    end)

    m:AddClick(m.objList.Btn_Help, function()
		SoundManager:PlaySound(7111)
		UIManager:OpenWnd(WndID.RuleTip, GetGameText(luaID, 6))
	end)
end

--------------------------------------------------------------------
-- 更新界面
--------------------------------------------------------------------
function m.UpdateView()
    local index = 1
    for i, v in ipairs(m.Item_Talent_List) do
        if v.UpdateData() == 2 then
            index = i
        end
    end

    m.UpdateTalentInof()
end

--------------------------------------------------------------------
--显示天赋信息
--------------------------------------------------------------------
function m.UpdateTalentInof()
    m.objList.Btn_Upgrade.gameObject:SetActive(false)
    m.objList.Txt_Expend.gameObject:SetActive(false)
    m.objList.Txt_Unlock.text = ''

    local maxLevel = PropertyCompute.GetEquipSmeltStarMaxLevel(m.SmeltID)
    local level = GamePlayerData.ActorEquipNew:GetEquipUpgradeLevel(m.SmeltID)
    local exp = GamePlayerData.ActorEquipNew:GetEquipUpgradeEXP(m.SmeltID)
    local equipSme = Schemes.EquipSmeltStar:GetEquipSmeltStarCfg(m.SmeltID, level)
    if not equipSme then
        error('EquipSmeltStar is nil, SmeltID=' .. m.SmeltID .. ' level=' .. level)
        return
    end

    local jd = 0
    if level == maxLevel then
        m.objList.Txt_Unlock.text = CommonTextID.IS_FULL_LEVEL
        jd = 1
    else
        local bool1 = HelperL.IsLackGoods(equipSme.CostGoodsID1, equipSme.CostGoodsID1Num, false, false)
        -- local bool2 = HelperL.IsLackGoods(equipSme.CostGoodsID2, equipSme.CostGoodsID2Num, false, false)
        local num = SkepModule:GetGoodsCount(equipSme.CostGoodsID1)
        local color = (not bool1) and "#fff300" or UI_COLOR.Red
        local goodsName = HelperL.GetGoodsName(equipSme.CostGoodsID1)
        m.objList.Txt_Expend.text = string.format("<color=%s>%s消耗：%s/%s</color>", color, goodsName,
            HelperL.GetChangeNum(num), equipSme.CostGoodsID1Num)
        AtlasManager:AsyncGetGoodsSprite(equipSme.CostGoodsID1, m.objList.Img_Expend)
        m.objList.Btn_Upgrade.gameObject:SetActive(true)
        m.objList.Txt_Expend.gameObject:SetActive(true)
        HelperL.SetImageGray(m.objList.Btn_Upgrade, bool1)

        local tempSme2 = Schemes.EquipSmeltStar:GetEquipSmeltStarCfg(equipSme.SmeltID, equipSme.StarLvl + 1)
        local value = tempSme2.StarExp - equipSme.StarExp
        if value > 0 then
            jd = (exp - equipSme.StarExp) / (tempSme2.StarExp - equipSme.StarExp)            
        end
    end
    if jd <= 0 then
        jd = 0.05
    end
    m.succRate = jd * 100
    m.objList.Txt_JD.text = GetGameText(luaID, 4) .. (jd * 100) .. "%"
    m.objList.Txt_TalentName.text = string.format(GetGameText(luaID, 5), level)
    local starLvl = GamePlayerData.ActorEquipNew:GetEquipUpgradeLevel(m.SmeltID)
    local equipSmeltStar = Schemes.EquipSmeltStar:GetEquipSmeltStarCfg(m.SmeltID, starLvl)

    m.objList.Txt_TalentContent.text = equipSmeltStar.Remark
    AtlasManager:AsyncGetSprite(equipSme.Icon or "10005", m.objList.Img_TalentIcon)

    for i = 1, 10, 1 do
        if not m.Item_Talent_JD_List[i] then
            m.Item_Talent_JD_List[i] = m.Create_Item_Talent_JD(i)
        end
        m.Item_Talent_JD_List[i].UpdateView(jd)
    end
end

--------------------------------------------------------------------
--获取激活状态，1：已激活，2：可激活，3：条件未达成(先激活上一星)
---@param equipSme EquipSmeltStarCfg
--------------------------------------------------------------------
function m.GetActiveState(equipSme)
    if equipSme then
        -- local maxLevel = PropertyCompute.GetEquipSmeltStarMaxLevel(m.SmeltID)
        local level = GamePlayerData.ActorEquipNew:GetEquipUpgradeLevel(m.SmeltID)
        --已激活
        if equipSme.StarLvl <= level then
            return 1
        end
        --可激活
        if equipSme.StarLvl == (level + 1) then
            return 2
        end
    end
    --条件未达成(先激活上一星)
    return 3
end

--------------------------------------------------------------------
--创角天赋框
---@param index integer
---@param equipSme EquipSmeltStarCfg
--------------------------------------------------------------------
function m.Create_Item_Talent(index, equipSme)
    ---@class Item_Talent
    local item = {}
    item.index = index
    item.equipSme = equipSme
    item.com = m:CreateSubItem(m.objList.Grid_Talent, m.objList.Item_Talent1)
    -- if (index % 2) ~= 0 then
    --     item.com = m:CreateSubItem(m.objList.Grid_Talent, m.objList.Item_Talent2)
    -- else
    --     item.com = m:CreateSubItem(m.objList.Grid_Talent, m.objList.Item_Talent2)
    -- end

    --item.com.Obj_JD.gameObject:SetActive(index ~= ShowTalentNum)
    item.com.Img_Bg2.gameObject:SetActive(false)

    m:AddClick(item.com.Btn_Click, function()
        -- m.UpdateTalentInof()
    end)
    ---更新数据
    item.UpdateData = function()
        local state = m.GetActiveState(item.equipSme)
        --设置默认状态
        item.com.Obj_Bg.gameObject:SetActive(true)
        item.com.Obj_Select.gameObject:SetActive(false)
        item.com.Img_Red.gameObject:SetActive(false)
        HelperL.SetImageGray(item.com.Img_Icon, true)
        item.com.Img_Bg2.gameObject:SetActive(false)
        item.com.Img_Upgrade.gameObject:SetActive(false)
        item.com.Txt_Desc.gameObject:SetActive(true)
        item.com.Txt_Desc1.gameObject:SetActive(false)

        AtlasManager:AsyncGetSprite(item.equipSme.Icon or "10005", item.com.Img_Icon1)
        AtlasManager:AsyncGetSprite(item.equipSme.Icon or "10005", item.com.Img_Icon2)
        --已激活/可激活
        if state ~= 3 then
            --已激活
            if state == 1 then
                item.com.Obj_Bg.gameObject:SetActive(false)
                item.com.Obj_Select.gameObject:SetActive(true)
                item.com.Txt_Desc.gameObject:SetActive(false)
                item.com.Txt_Desc1.gameObject:SetActive(true)
                HelperL.SetImageGray(item.com.Img_Icon, false)
                item.com.Img_Bg2.gameObject:SetActive(item.index ~= ShowTalentNum)
            else
                --可激活
                local bool1 = HelperL.IsLackGoods(item.equipSme.CostGoodsID1, item.equipSme.CostGoodsID1Num, false, false)
                local bool2 = HelperL.IsLackGoods(item.equipSme.CostGoodsID2, item.equipSme.CostGoodsID2Num, false, false)
                -- item.com.Img_Red.gameObject:SetActive(not bool1 and not bool2)
                item.com.Img_Upgrade.gameObject:SetActive(true)
            end
        else
            item.com.Img_Upgrade.gameObject:SetActive(true)
        end
        item.com.Txt_Level1.text = item.equipSme.StarLvl .. CommonTextID.LEVEL
        item.com.Txt_Level2.text = item.com.Txt_Level1.text
        item.com.Txt_Desc.text = Schemes.CommonProp:Get(item.equipSme.PropId).Name
        item.com.Txt_Desc1.text = Schemes.CommonProp:Get(item.equipSme.PropId).Name

        return state
    end
    return item
end

--------------------------------------------------------------------
--创角天赋框
---@param index integer
--------------------------------------------------------------------
function m.Create_Item_Talent_JD(index)
    ---@class Item_Talent_JD
    local item = {}
    item.index = index
    item.com = m:CreateSubItem(m.objList.Grid_Talent_JD, m.objList['Item_Talent_JD' .. index], false, false)
    ---更新界面
    item.UpdateView = function(jd)
        if (jd * 100) >= (item.index * 10) then
            item.com.Img_Icon1.gameObject:SetActive(false)
            item.com.Img_Icon2.gameObject:SetActive(true)
        else
            item.com.Img_Icon1.gameObject:SetActive(true)
            item.com.Img_Icon2.gameObject:SetActive(false)
        end
    end
    return item
end

--------------------------------------------------------------------
--请求回调
--------------------------------------------------------------------
function m.RequestCallback(resultCode, content)
    if resultCode == RESULT_CODE_BASE.RESULT_COMMON_SUCCEED[1] then
        local valueList = HelperL.Split(content, ':')
        local value = tonumber(valueList[2])
        if m.succValue == 5 then
            value = value - 5
        end
        m.UpdateView()
        local starLvl = GamePlayerData.ActorEquipNew:GetEquipUpgradeLevel(m.lastSmeltID)
        -- if m.lastStarLvl == starLvl then
        --     HelperL.ShowMessage(TipType.FlowText, string.format('<color=#FFA2FF> 附魔成功率提升%s</color>',value)..'<color=#FFA2FF>%!</color>')
        -- end
        HelperL.PlayVFX()
    else
        ResultCode:DefaultShowResultCode(resultCode, content)
    end
end

--------------------------------------------------------------------
--天赋升级
---@param smeltID integer 升星ID
--------------------------------------------------------------------
function m.Upgrade(smeltID)
    local curTime = os.clock()
    if curTime - m.lastTime < 0.3 then
        print('天赋升级点太快了1秒后尝试！')
        return
    end
    m.lastTime = curTime

    local starLvl = GamePlayerData.ActorEquipNew:GetEquipUpgradeLevel(smeltID)
    local equipSmeltStar = Schemes.EquipSmeltStar:GetEquipSmeltStarCfg(smeltID, starLvl)
    if equipSmeltStar == nil then
        warn("天赋升级:未找到升星配置 smeltID=", smeltID, starLvl)
        return
    end

    if HelperL.IsLackGoods(equipSmeltStar.CostGoodsID1, equipSmeltStar.CostGoodsID1Num, false) then return end
    if HelperL.IsLackGoods(equipSmeltStar.CostGoodsID2, equipSmeltStar.CostGoodsID2Num, false) then return end

    m.lastStarLvl = starLvl
    m.lastSmeltID = smeltID

    local form = {}
    form["smeltID"] = smeltID
    form["loopTimes"] = 1
    form["succRate"] = m.succRate
    m.succValue = m.succRate
    
    LuaModuleNew.SendRequest(LuaRequestID.NewCardAddExp2, form, m.RequestCallback)
end

--------------------------------------------------------------------
-- 关闭界面
--------------------------------------------------------------------
function m.CloseUI()
    m:CloseSelf()
end

return m
