-- 登录UI
local luaID = ('UIShowGoodInfo')

local UIShowGoodInfo = {}

-- 初始化
function UIShowGoodInfo:OnCreate()
	self.objList.Btn_Close.onClick:AddListenerEx(self.OnClickClose)
	self.objList.Btn_Cancel.onClick:AddListenerEx(self.OnClickCancel)
	self.objList.Btn_Ok.onClick:AddListenerEx(self.OnClickOk)
	self.objList.Btn_Advertise.onClick:AddListenerEx(self.OnClickAdvertise)
	self.btnList = { self.objList.Btn_Close, self.objList.Btn_Cancel, self.objList.Btn_Ok }
	self.goodContent = self.objList.GoodContent.transform
	self.itemList = {}
	return true
end

-- 窗口开启
function UIShowGoodInfo:OnOpen(prizeID, title, param1, param2, param3)
	self.prizeID = prizeID
	self.param1 = param1
	self.param2 = param2
	self.param3 = param3
	self.title = title
	self:UpdateView()
	self:OnSecondUpdate()
end

-- 更新视图
function UIShowGoodInfo:UpdateView()
	local prize = Schemes.PrizeTable:GetPrize(self.prizeID)
	if prize == nil then return end
	local prizeList = Schemes.PrizeTable:GetPrizeGoods(prize)
	if prizeList == nil then return end
	for k, v in ipairs(self.itemList) do
		v.gameObject:SetActive(false)
	end
	for i=1 , #prizeList do
		local item = self.itemList[i]
		if not item then
			item = CreateSingleGoods(self.goodContent)
			table.insert(self.itemList, item)
		end
		item:SetItemData(prizeList[i].ID, prizeList[i].Num)
		item:SetSize(130,130)
		item:SetVisible(true)
	end
	if not self.param1 then
		self.objList.Btn_Cancel.gameObject:SetActive(false)
	else
		self.objList.Btn_Cancel.gameObject:SetActive(true)
		self.objList.Txt_Cancel.text = self.param1.txt
		self.objList.Btn_Cancel.enabled = self.param1.enabled
		if self.param1.enabled then
			HelperL.SetImageGray(self.objList.Btn_Cancel:GetComponent('Image'), false)
		else
			HelperL.SetImageGray(self.objList.Btn_Cancel:GetComponent('Image'), true)
		end
	end
	if not self.param2 then
		self.objList.Btn_Ok.gameObject:SetActive(false)
	else
		self.objList.Btn_Ok.gameObject:SetActive(true)
		self.objList.Txt_Ok.text = self.param2.txt
		self.objList.Btn_Ok.enabled = self.param2.enabled
		if self.param2.enabled then
			HelperL.SetImageGray(self.objList.Btn_Ok:GetComponent('Image'), false)
		else
			HelperL.SetImageGray(self.objList.Btn_Ok:GetComponent('Image'), true)
		end
	end

	if not self.param3 then
		self.objList.Btn_Advertise.gameObject:SetActive(false)
	else
		self.objList.Btn_Advertise.gameObject:SetActive(true)
		self.objList.Txt_Advertise.text = self.param3.txt
		self.objList.Btn_Advertise.enabled = self.param3.enabled
		if self.param3.enabled then
			HelperL.SetImageGray(self.objList.Btn_Advertise:GetComponent('Image'), false)
		else
			HelperL.SetImageGray(self.objList.Btn_Advertise:GetComponent('Image'), true)
		end
	end
	self.objList.Txt_Title.text = self.title
end

function UIShowGoodInfo.OnClickClose()
	local self = UIShowGoodInfo
	self:CloseSelf()
end

function UIShowGoodInfo.OnClickCancel()
	local self = UIShowGoodInfo
	if self.param1 and self.param1.callback then
		self.param1.callback()
	end
	self:CloseSelf()
end

function UIShowGoodInfo.OnClickOk()
	local self = UIShowGoodInfo
	if self.param2 and self.param2.callback then
		self.param2.callback()
	end
	self:CloseSelf()
end

function UIShowGoodInfo.OnClickAdvertise()
	local self = UIShowGoodInfo
	if self.param3 and self.param3.callback then
		self.param3.callback()
	end
	self:CloseSelf()
end

-- 窗口关闭
function UIShowGoodInfo:OnClose()
end

-- 每秒更新
function UIShowGoodInfo:OnSecondUpdate()
	-- local timeCounter = AdvertisementManager:GetVideoTimeCounter()
	-- if timeCounter <= 0 then
	-- 	self.objList.Txt_Advertise.text = GetGameText(luaID, 1)
	-- 	self.objList.Img_AdverRed.gameObject:SetActive(true)
	-- 	HelperL.SetImageGray(self.objList.Btn_Advertise:GetComponent('Image'), false)
	-- else
	-- 	self.objList.Txt_Advertise.text = string.format(GetGameText(luaID, 2), timeCounter)
	-- 	self.objList.Img_AdverRed.gameObject:SetActive(false)
	-- 	HelperL.SetImageGray(self.objList.Btn_Advertise:GetComponent('Image'), true)
	-- end
end

-- 窗口销毁
function UIShowGoodInfo:OnDestroy()
	-- 释放内存
	for i, btn in ipairs(self.btnList) do
		btn.onClick:RemoveAllListeners()
		btn.onClick:Invoke()
	end
end

return UIShowGoodInfo