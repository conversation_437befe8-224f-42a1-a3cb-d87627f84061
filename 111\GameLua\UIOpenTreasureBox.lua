--[[
********************************************************************
    created:    2023/08/08
    author :    李锦剑
    purpose:    开宝箱界面
*********************************************************************
--]]

local luaID = ('UIOpenTreasureBox')
local m = {}

local boxIconList = {
	[BOX_EQUIP_ID[1]] = {
		bg = 'xsd_bxbjt_1',
		icon = 'xsd_bxtb_1'
	},
	[BOX_EQUIP_ID[2]] = {
		bg = 'xsd_bxbjt_2',
		icon = 'xsd_bxtb_2'
	},
}

-- 订阅事件列表
function m.GetOpenEventList()
	return {
		[EventID.StoreList] = m.UpdateView,
		[EventID.StoreUpdateFree] = m.UpdateView,
		[EventID.LogicDataChange] = m.UpdateView,
		[EventID.EntitySyncBuff] = m.UpdateView,
		[EventID.OnGoodsPropChange] = m.UpdateView,
		[EventID.OnHeroPropChange] = m.UpdateView,
		[EventID.ActorDataChanged] = m.UpdateView,
	}
end

--------------------------------------------------------------------
-- 初始化
--------------------------------------------------------------------
function m.OnCreate()
	---@type Item_Box1[]
	m.Item_Box_List = {}
	local num = math.max(#BOX_EQUIP_ID, #m.Item_Box_List)
	for i = 1, 1, 1 do
		if not m.Item_Box_List[i] then
			m.Item_Box_List[i] = m.Creation_Item_Box(m.objList.Grid_BoxShop, i)
		end
		m.Item_Box_List[i].UpdateData(PropertyCompute.GetBoxEquipData(BOX_EQUIP_ID[i]))
	end

	m.objList.Txt_Desc.text = GetGameText(luaID, 21)
    m.comResInfo = ComResInfo.New()
    m.comResInfo.BindView(m.objList.Img_Res)

	m.RegisterClickEvent()
	return true
end

--------------------------------------------------------------------
-- 窗口开启
--------------------------------------------------------------------
function m:OnOpen()
	m.UpdateView()
end

--------------------------------------------------------------------
-- 注册点击事件
--------------------------------------------------------------------
function m.RegisterClickEvent()
	m:AddClick(m.objList.Btn_Close, function()
		m:CloseSelf()
	end)
end

--------------------------------------------------------------------
--更新界面
--------------------------------------------------------------------
function m.UpdateView()
	for i, v in ipairs(m.Item_Box_List) do
		v.UpdateData(PropertyCompute.GetBoxEquipData(BOX_EQUIP_ID[i]))
	end
end

--------------------------------------------------------------------
-- 创建宝箱栏
--------------------------------------------------------------------
function m.Creation_Item_Box(parent, index)
	---@class Item_Box1
	local item = {}
	item.index = index
	item.obj_list = {}

	item.com = m.objList.ItemBox.gameObject
	m.GetChildComponent(m.objList.ItemBox.gameObject.transform, item.obj_list)
	m:AddClick(item.obj_list.Btn_Buy, function()
		SoundManager:PlaySound(7111)
		if item.data == nil then return end
		m.BuyBox(item.data.equipID, 1)
	end)

	--item.com = m:CreateSubItem(parent, m.objList.Item_Box)
	--item.com.Img_RedDotBuy.gameObject:SetActive(false)
	-- m:AddClick(item.com.Btn_Click, function()
	-- 	if item.data == nil then return end
	-- 	UIManager:OpenWnd(WndID.BuyBox, item.data.equipID)
	-- end)
	-- m:AddClick(item.com.Btn_Buy, function()
	-- 	SoundManager:PlaySound(7111)
	-- 	if item.data == nil then return end
	-- 	m.BuyBox(item.data.equipID, 1)
	-- end)
	m:AddClick(item.obj_list.Btn_Help, function()
		SoundManager:PlaySound(7111)
		UIManager:OpenWnd(WndID.RuleTip, GetGameText(luaID, 21), GetGameText(luaID, 22))
	end)

	---更新宝箱数据
	---@param data BoxEquipData|nil
	item.UpdateData = function(data)
		item.data = data
		if data then
			-- local equipment = Schemes.Equipment:Get(data.equipID)
			item.obj_list.Txt_Name.text = data.name
			local boxIcon = boxIconList[data.equipID]
			--AtlasManager:AsyncGetSprite(boxIcon.bg, item.obj_list.Img_Bg)
			AtlasManager:AsyncGetSprite(boxIcon.icon, item.obj_list.Img_Goods)
			local boxEquipSmelt = data.boxEquipSmeltList[data.level]
			local num = SkepModule:GetGoodsCount(boxEquipSmelt.expendID)
			local isLackGoods = num < boxEquipSmelt.expendNum
			AtlasManager:AsyncGetGoodsSprite(boxEquipSmelt.expendID, item.obj_list.Img_CostType)
			local color = isLackGoods and UI_COLOR.Red or UI_COLOR.White
			item.obj_list.Txt_CostNum.text = string.format("<color=%s>%s</color>", color, boxEquipSmelt.expendNum)
			item.obj_list.Txt_Hint.text = GetGameText(luaID, 23)--string.format(GetGameText(luaID, 5), m.GetBoxQualityName(data.quality))
			item.obj_list.Img_RedDotBuy.gameObject:SetActive(not isLackGoods and data.equipID ~= BOX_EQUIP_ID[1])
			HelperL.SetImageGray(item.obj_list.Btn_Buy, isLackGoods)

			local adID = BOX_EQUIP_AD_ID[data.equipID]
			local cfgAD = Schemes.CommonText:Get(adID)
			num = HeroDataManager:GetLogicByte(cfgAD.Param4, cfgAD.Param5)
			color = num >= cfgAD.DayTime and UI_COLOR.Red or UI_COLOR.White
			item.obj_list.Txt_Hint2.text = string.format(GetGameText(luaID, 19), color, num, cfgAD.DayTime)

			item.com.gameObject:SetActive(true)
		else
			item.com.gameObject:SetActive(false)
		end
	end
	return item
end

--------------------------------------------------------------------
---宝箱购买
---@param equipID integer 宝箱ID
---@param buyType integer 购买类型, 1:普通购买, 2:免费
--------------------------------------------------------------------
function m.BuyBox(equipID, buyType)
	local boxEquipData = PropertyCompute.GetBoxEquipData(equipID)
	if not boxEquipData then return end

	local expendID  = boxEquipData.boxEquipSmelt.expendID
	local expendNum = boxEquipData.boxEquipSmelt.expendNum
	if buyType == 1 then
		if HelperL.IsLackGoods(expendID, expendNum, false) then
			return
		end
	elseif buyType == 2 then
		if HelperL.GetAdverHint(boxEquipData.adID, true) then
			return
		end
	else
		warn('类型不存在 buyType=', buyType)
		return
	end

	local emptyCount = SkepModule:GetSkepByID(SKEPID.SKEPID_PACKET):EmptyCount()
    if emptyCount < 180 then
        HelperL.ShowMessage(TipType.FlowText, GetGameText(luaID, 20))
        return
    end
	local data = {
		type = NotarizeWindowsType.Windows1,
		titleContent = '购买提示',
		content = string.format(GetGameText(luaID, 24), expendNum),
		okCallback = function()
			if buyType == 1 then
				UIManager:OpenWnd(WndID.OpenBox, equipID, buyType)
			elseif buyType == 2 then
				AdvertisementManager.ShowRewardAd(boxEquipData.adID, function(bool, adID)
					local equipID2, buyType2 = equipID, buyType
					if bool then
						UIManager:OpenWnd(WndID.OpenBox, equipID2, buyType2)
					end
				end)
			end
		end,
	}
	HelperL.NotarizeUI(data)

	-- print("buyType =============",buyType)
	-- if buyType == 1 then
	-- 	--UIManager:OpenWnd(WndID.OpenBox, equipID, buyType)
	-- 	m.BuyBoxEnd(equipID,buyType)
	-- elseif buyType == 2 then
	-- 	AdvertisementManager.ShowRewardAd(boxEquipData.adID, function(bool, adID)
	-- 		local equipID2, buyType2 = equipID, buyType
	-- 		if bool then
	-- 			UIManager:OpenWnd(WndID.OpenBox, equipID2, buyType2)
	-- 		end
	-- 	end)
	-- end
end


--------------------------------------------------------------------
-- 购买宝箱
--------------------------------------------------------------------
function m.BuyBoxEnd(equipID,buyType)
    local boxEquipData = PropertyCompute.GetBoxEquipData(equipID)
    if not boxEquipData then return end
	local prizeID = boxEquipData.boxEquipSmelt.prizeID
    --获取随机奖励
    m.prizeGoodsList = Schemes.PrizeTable:GetRandomPrize(prizeID)
    if not m.prizeGoodsList or #m.prizeGoodsList == 0 then
        warn('随机奖励获取失败 prizeID=', prizeID)
        return
    end
	
    local expendID  = boxEquipData.boxEquipSmelt.expendID
    local expendNum = boxEquipData.boxEquipSmelt.expendNum
    if buyType == 1 then
        if HelperL.IsLackGoods(expendID, expendNum, false) then
            return
        end
    elseif buyType == 2 then
        local cfgAD = Schemes.CommonText:Get(boxEquipData.adID)
        local num = HeroDataManager:GetLogicByte(cfgAD.Param4, cfgAD.Param5)
        if num >= cfgAD.DayTime then
            HelperL.ShowMessage(TipType.FlowText, RESULT_CODE_BASE.RESULT_COMMON_LACK_DAYTIMES[2])
            return
        end
    else
        warn('类型不存在 buyType=', m.buyType)
        return
    end

    local adID = BOX_EQUIP_AD_ID[boxEquipData.equipID]
    local cfgAD = Schemes.CommonText:Get(adID)
    local num = HeroDataManager:GetLogicByte(cfgAD.Param4, cfgAD.Param5)
    if num >= cfgAD.DayTime then
        HelperL.ShowMessage(TipType.FlowText, RESULT_CODE_BASE.RESULT_COMMON_LACK_DAYTIMES[2])
        return
    end

    AdvertisementManager.GetAdAward(adID, function(result, content, code)
        local goodInfo = ''
        for i, v in ipairs(m.prizeGoodsList) do
            if goodInfo == '' then
                goodInfo = v.ID .. ';' .. v.Num
            else
                goodInfo = goodInfo .. '|' .. v.ID .. ';' .. v.Num
            end
        end
        local costInfo = expendID .. ';' .. expendNum
        local form = {}
        form["equipID"] = equipID
        form["loopTimes"] = 1
        LuaModuleNew.SendRequest(LuaRequestID.NewCardAddExp, form, function(resultCode2, content2)
            if resultCode2 == RESULT_CODE_BASE.RESULT_COMMON_SUCCEED[1] then
                if buyType == 1 then     --1:普通购买
                    HelperL.RequestDirectGiveGoodsy(goodInfo, costInfo, m.RequestCallback)
                elseif buyType == 2 then --2:免费
                    HelperL.RequestDirectGiveGoodsy(goodInfo, '0', m.RequestCallback)
                end
            else
                ResultCode.ShowResultCodeCallback(resultCode2, content2)
            end
        end)
    end)
end

--------------------------------------------------------------------
---获取宝箱品质名称
---@param quality integer 品质
---@return string
--------------------------------------------------------------------
function m.GetBoxQualityName(quality)
	if quality == 1 then  --普通
		return GetGameText(luaID, 6)
	elseif quality == 2 then --高级
		return GetGameText(luaID, 7)
	elseif quality >= 3 then --稀有，最大品质
		return GetGameText(luaID, 8)
	else                  --默认普通
		return GetGameText(luaID, 6)
	end
end

return m
