-- 窗口基类
--[[
	子类可响应函数列表：
	OnCreate			创建时
	CanOpen				是否可打开
	OnOpen				打开时
	OnOpenAniEnd		打开动画结束
	OnWndMsg			收到窗口消息
	CanClose			是否可关闭
	OnClose				关闭时
	OnDestroy			销毁时
	GetOpenEventList	开启期间订阅事件列表
	GetCreateEventList	创建后订阅事件列表
]]

-- local luaID = ('UIWndBase')

---窗口基类
---@class UIWndBase
---@field wndID integer 窗口ID
---@field uiconfig UIConfig UI配置
---@field gameObject any 对象
---@field transform any 变换组件
---@field rectTrans any UI变换组件
---@field isOpening boolean 是开放
---@field isLoad boolean 是加载预制体中(微信)
---@field wndRedDotList table 红点集合
---@field slowBeginCount integer 慢速开始计数
---@field OnCreate fun() 创建时
---@field CanOpen fun(...) 是否可打开
---@field OnOpen fun(...) 打开时
---@field OnOpenAniEnd fun() 打开动画结束
---@field OnWndMsg fun(...) 收到窗口消息
---@field CanClose fun():boolean,string 是否可关闭
---@field OnClose fun() 关闭时
---@field OnDestroy	 fun() 销毁时
---@field GetOpenEventList fun():table 开启期间订阅事件列表
---@field GetCreateEventList fun():table 创建后订阅事件列表
---@field AdaptScale fun() 预制体适配
---@field OnSecondUpdate fun() 每秒更新
---@field OnFrameUpdate fun() 每帧更新
---@field GetResourceList fun():string[] 获取预加载资源列表
local m = {}
UIWndBase = m

m.__index = m

---初始化
---@param uiconfig UIConfig 窗口配置
---@return boolean
function m:Create(uiconfig)
	if string.len(uiconfig.Prefab) <= 1 then
		warn(string.format('UIWndBase:Create 读取预制体失败 ID=%s  Prefab=%s', uiconfig.ID, uiconfig.Prefab))
		return false
	end

	local prefab = HotResManager.ReadUI(uiconfig.Prefab)
	if tolua.isnull(prefab) then
		warn('UIWndBase:Create 读取预制体失败 ', uiconfig.ID, uiconfig.Prefab)
		return false
	end

	local parent = UIManager:GetUIContainer(uiconfig.SortOrder)
	local uiObj = GameObject.Instantiate(prefab, parent.transform)
	if not uiObj then
		warn('UIWndBase:Create 创建预制体失败 ', uiconfig.ID)
		return false
	end

	local uiTrans = uiObj.transform
	Helper.ResetTransform(uiTrans)

	local uiCanvas = uiObj:GetComponent('Canvas')
	if not uiCanvas then
		uiCanvas = uiObj:AddComponent(typeof(UnityEngine.Canvas))
		uiObj:AddComponent(typeof(UnityEngine.UI.GraphicRaycaster))
	end
	if uiconfig.ID ~= 3 then
		uiCanvas.overrideSorting = false
	end
	self.uiCanvas = uiCanvas

	uiObj:SetActive(false)

	self.parent = parent.transform
	self.transform = uiTrans
	self.gameObject = uiObj
	self.rectTrans = uiObj:GetRectTransform()
	self.uiconfig = uiconfig
	self.isOpening = false
	local aniObj = uiTrans:Find("ContentAni")
	if aniObj ~= nil then
		self.rectScaleTrans = uiTrans:Find("ContentAni"):GetRectTransform()
	end

	-- 根据界面控件命名前缀，动态获取各种组件类型控件对象填充到objList里去
	local objList = {}
	self.wndRedDotList = {}
	m.GetChildComponent(uiObj.transform, objList)
	--组件集合
	self.objList = objList
	--子物体组件集合
	self.objListChild = {}
	--收集按钮组件(使用 UIWndBase.GetChildComponent 函数收集)
	self.__ButtonList = {}
	--收集按钮组件(使用 UIWndBase:AddClick 函数收集)
	self.__AddClick_ButtonList = {}

	--大背景图适配
	local img, obj
	for i = 1, 9, 1 do
		obj = objList['Img_bjtb_0' .. i]
		if obj then
			img = obj.gameObject:GetComponent('Image')
			if img then
				-- img:SetNativeSize()
				HelperL.AdaptScale(img, 6)
			end
		end
	end

	--预制体适配
	if self.AdaptScale then
		self:AdaptScale()
	end

	--刘海屏适配
	if HelperL.IsNotchDetection() then
		if objList.NotchDetection then
			local childRect = objList.NotchDetection.transform:GetRectTransform()
			childRect.sizeDelta = Vector2(childRect.sizeDelta.x, childRect.sizeDelta.y + 80)
		end
		if objList.NotchDetection2 then
			local childRect = objList.NotchDetection2.transform:GetRectTransform()
			local vector4 = childRect:GetRectTransformVector4()
			vector4.y = vector4.y + 80
			childRect:SetRectTransformVector4(vector4)
		end
	end

	-- 子类触发
	if self.OnCreate and not self:OnCreate() then
		return false
	end

	if self.GetCreateEventList then
		local createEventList = self:GetCreateEventList()
		if createEventList then
			for i, v in pairs(createEventList) do
				EventManager:Subscribe(i, v)
			end
		end
	end

	--记录窗口是否开启过
	RedDotCheckFunc.WindowOpen[self.uiconfig.ID] = true

	if objList.Item then
		objList.Item.gameObject:SetActive(false)
	end

	-- if objList.Txt_Title then
	-- 	local name = GetActivityName(self.uiconfig.ID)
	-- 	if name ~= '' then
	-- 		objList.Txt_Title.text = name
	-- 	end
	-- end

	EventManager:Fire(EventID.WindowCreated, uiconfig)
	return true
end

-- 是否可打开
function m:CheckOpenLimitType(limitType, limitParam1)
	if not limitType or limitType == 0 then
		return true
	end

	return true
end

-- 是否可打开
function m:CanOpenBase(...)
	if self.uiconfig then
		if self.uiconfig.OpenLimitType1 > 0 then
			local canOpen, msg = self:CheckOpenLimitType(self.uiconfig.OpenLimitType1, self.uiconfig.OpenLimitParam1)
			if not canOpen then
				return false, msg
			end
		end
		if self.uiconfig.OpenLimitType2 > 0 then
			local canOpen, msg = self:CheckOpenLimitType(self.uiconfig.OpenLimitType2, self.uiconfig.OpenLimitParam2)
			if not canOpen then
				return false, msg
			end
		end
	end
	if self.CanOpen then
		return self:CanOpen(...)
	end
	return true, ''
end

-- 是否已打开
function m:IsOpen()
	return self.isOpening
end

-- 是加载中
function m:IsLoad()
	return self.isLoad == true
end

-- 是否已打开
function m:GetWndID()
	if not self.uiconfig then
		return -1
	end
	return self.uiconfig.ID
end

-- 打开处理
---@param parent integer 父节点
---@param openCloseBut ?boolean 是否显示关闭按钮
---@param ... unknown 参数
---@return UIWndBase
function m:OnOpenBase(parent, openCloseBut, ...)
	parent = parent or self.parent
	if not tolua.isnull(parent) then
		self.transform:SetParent(UIManager.uiPool.transform, false)
		--重新设置父节点，更新层级(让最后打开的UI显示在前面)
		self.transform:SetParent(parent.transform, false)
	end

	-- if self.wndID ~= WndID.RoleAssets then
	-- 	UIManager:AutoOpenRoleAssets()
	-- end

	if self.isOpening then
		-- 已开启的窗口
		if self.OnOpen then
			self:OnOpen(...)
		end
		return self
	end

	self.isOpening = true
	self.rectTrans.localScale = Vector3(1, 1, 1)
	UIManager:AddOpenWndList(self.uiconfig.MainType, self.uiconfig.ID)

	if self.GetOpenEventList then
		local openEventList = self:GetOpenEventList()
		if openEventList then
			for i, v in pairs(openEventList) do
				EventManager:Subscribe(i, v)
			end
		end
	end

	if self.objList.Btn_Close then
		self.objList.Btn_Close.gameObject:SetActive(openCloseBut ~= false)
	end

	if self.OnOpen then
		self:OnOpen(...)
	end

	if self.rectScaleTrans ~= nil then
		self:DoWndAnimation(self.OnOpenAniEnd)
	else
		if self.OnOpenAniEnd then
			self.OnOpenAniEnd()
		end
	end

	EventManager:Fire(EventID.WindowOpen, self.uiconfig.ID)
	return self
end

-- 执行窗口动画
function m:DoWndAnimation(onAniEndFunc)
	self.rectScaleTrans:DOKill(true)

	self.rectScaleTrans.localScale = Vector3(0.4, 0.4, 0.4)
	local tween = self.rectScaleTrans:DOScale(Vector3(1, 1, 1), 0.3):SetEase(TweeningEase.Linear, 0.3)
	if onAniEndFunc then
		tween:OnComplete(onAniEndFunc)
	end
	return true
end

-- 收到窗口消息
function m:OnWndMsgBase(msg, ...)
	if self.OnWndMsg then
		self:OnWndMsg(msg, ...)
	end
end

-- 是否可关闭
function m:CanCloseBase()
	if self.CanClose then
		return self:CanClose()
	end
	return true, ''
end

-- 关闭处理
function m:OnCloseBase(isRemoveRedDot)
	if not self.isOpening then
		warn('UIWndBase:OnCloseBase 重复关闭UI ', self.wndID)
		return
	end

	if self.OnClose then
		self:OnClose()
	end

	if self.GetOpenEventList then
		local openEventList = self:GetOpenEventList()
		if openEventList then
			for i, v in pairs(openEventList) do
				EventManager:UnSubscribe(i, v)
			end
		end
	end

	-- 关闭窗口时统一默认清除所有红点
	if isRemoveRedDot ~= false then
		self:RemoveAllWndRedDot()
	end

	self.isOpening = false

	-- if UIManager:GetUseWndCloseAni() and self:DoWndAnimation(self.uiconfig.CloseAniType, self.uiconfig.CloseAniParam1, self.uiconfig.CloseAniParam2, self.uiconfig.CloseAniParam3,
	-- 		function()
	-- 			self:OnCloseAniEnd()
	-- 		end)
	-- then
	-- 	-- 等关闭动画结束再隐藏
	-- else
		self:OnCloseAniEnd()
	-- end

	UIManager:RemoveOpenWndList(self.uiconfig.MainType, self.uiconfig.ID)

	EventManager:Fire(EventID.WindowClose, self.uiconfig.ID)
end

-- 关闭动画结束
function m:OnCloseAniEnd()
	if self.gameObject then
		self.gameObject:SetActive(false)
		self.transform:SetParent(UIManager.uiPool.transform, false)
	end
end

-- 销毁处理
function m:OnDestroyBase()
	if self:IsOpen() then
		warn('UIWndBase:OnDestroyBase 销毁一个开启中的窗口？', self.uiconfig.ID)
		return false
	end

	if self.OnDestroy then
		self:OnDestroy()
	end

	if self.GetCreateEventList then
		local createEventList = self:GetCreateEventList()
		if createEventList then
			for i, v in pairs(createEventList) do
				EventManager:UnSubscribe(i, v)
			end
		end
	end

	--清除按钮点击事件
	for _, but in pairs(self.__ButtonList) do
		if not tolua.isnull(but) then
			but.onClick:RemoveAllListeners()
		end
	end
	for _, but in pairs(self.__AddClick_ButtonList) do
		if not tolua.isnull(but) then
			but.onClick:RemoveAllListeners()
		end
	end

	self.objList = nil
	-- self.objListChild = nil
	self.__ButtonList = nil
	self.wndRedDotList = nil
	self.__AddClick_ButtonList = nil

	if self.gameObject then
		GameObject.Destroy(self.gameObject)
	end
	return true
end

-- 设置红点
function m:SetWndRedDot(parent, offset)
	if not parent then
		error('UIWndBase:SetWndRedDot invalid param' .. tostring(parent))
		return nil
	end

	self.wndRedDotList[parent.transform] = true
	return RedDotManager:SetRedDot(parent.transform, offset)
end

-- 移除红点
function m:RemoveWndRedDot(parent)
	if not parent then
		warn('UIWndBase:RemoveWndRedDot not parent', debug.traceback())
		return
	end

	self.wndRedDotList[parent] = nil
	RedDotManager:RemoveRedDot(parent)
end

-- 移除所有红点
function m:RemoveAllWndRedDot()
	if next(self.wndRedDotList) ~= nil then
		for parent, v in pairs(self.wndRedDotList) do
			RedDotManager:RemoveRedDot(parent)
		end

		self.wndRedDotList = {}
	end
end

---辅助函数：创建子物件
---@param parent any 父节点
---@param itemPrefab any 克隆对象
---@param isInstantiate ?boolean 是否进行克隆，默认：true
---@param isResetPos ?boolean 是否重置坐标为 Vector3(0, 0, 0)，默认：true
---@return table
function m:CreateSubItem(parent, itemPrefab, isInstantiate, isResetPos)
	local item = {}

	--进行克隆
	if isInstantiate ~= false then
		item.gameObject = GameObject.Instantiate(itemPrefab.gameObject, parent.transform)
	else
		itemPrefab.transform:SetParent(parent.transform, false)
		item.gameObject = itemPrefab.gameObject
	end

	item.transform = item.gameObject.transform
	if isResetPos ~= false then
		item.transform.localPosition = Vector3(0, 0, 0)
	end
	self.GetChildComponent(item.transform, item)
	item.gameObject:SetActive(true)

	--收集子物体组件
	self:CollectChild(parent, item)
	return item
end

---辅助函数：收集子物体组件
function m:CollectChild(parent, item)
	local name = tostring(parent.gameObject.name)
	local index = tostring(parent.transform.childCount)

	-- if not self.objListChild then
	-- 	self.objListChild = {}
	-- end
	--收集子物体组件
	if not self.objListChild[name] then
		self.objListChild[name] = {}
	end
	self.objListChild[name][index] = item
end

-- 辅助函数：关闭自身
function m:CloseSelf()
	UIManager:CloseWndByID(self.uiconfig.ID)
	-- if self.wndID ~= WndID.RoleAssets then
	-- 	UIManager:AutoOpenRoleAssets()
	-- end
end

---添加按钮事件
---@param obj any 组件/对象
---@param clickEvents fun() 点击事件
---@param soundID ?integer 音效ID，0无音效，默认：1005
function m:AddClick(obj, clickEvents, soundID)
	local but = obj.gameObject:GetComponent("Button")
	if but == nil then
		warn('获取不到Button组件', debug.traceback())
		return
	end

	local name = but.gameObject.name
	if soundID == nil then
		local _start = string.find(string.lower(name), string.lower("Close"))
		--判断是否为关闭按钮
		if _start ~= nil then
			soundID = SoundID.CommonCloseButtonSound
		else
			soundID = SoundID.CommonButtonSound
		end
	end

	--清除所有事件
	but.onClick:RemoveAllListeners()
	--添加事件
	if soundID == 0 then
		but.onClick:AddListenerEx(clickEvents)
	else
		but.onClick:AddListenerEx(function()
			local _soundID = soundID
			local _clickEvents = clickEvents
			--播放按钮音效
			SoundManager:PlaySound(_soundID)
			--执行按钮事件
			if type(_clickEvents) == "function" then
				_clickEvents()
			end
		end)
	end

	--收集按钮
	if self.__ButtonList == nil or self.__ButtonList[but] == nil then
		if self.__AddClick_ButtonList == nil then
			self.__AddClick_ButtonList = {}
		end
		self.__AddClick_ButtonList[but] = but
	end
end

---获取对象和子对象组件
---@param trans integer
---@param objList table
function m.GetChildComponent(trans, objList)
	if tolua.isnull(trans) then
		warn("FillLuaComps param==null")
		return
	end
	trans = trans.transform
	if trans.transform.childCount == 0 then
		return
	end

	local curName, strList, comName, objName
	for i = 0, trans.childCount - 1 do
		local childObj = trans:GetChild(i).gameObject
		curName = childObj.name
		strList = HelperL.Split(curName, '_')
		comName = strList[1]
		if HelperL.StartsWith(curName, "Dir_") then
			objList[curName] = childObj
			objName = string.sub(curName, 5, #curName)
			if childObj.transform.childCount > 0 then
				objList[objName] = objList[objName] or {}
				m.GetChildComponent(childObj, objList[objName])
			end
		elseif HelperL.StartsWith(curName, "Item_") then
			--用于克隆，不进行子子对象获取
			objList[curName] = childObj
		elseif HelperL.StartsWith(curName, "No_") then
			--不进行子子对象获取
			objList[curName] = childObj
		else
			local comp = childObj
			if comName == 'Txt' then
				comp = childObj:GetComponent("Text")
			elseif comName == 'Btn' then
				comp = childObj:GetComponent("UIButton") or childObj:GetComponent("Button")
			elseif comName == 'Img' then
				comp = childObj:GetComponent("Image")
			elseif comName == 'RawImg' then
				comp = childObj:GetComponent("RawImage")
			elseif comName == 'Sld' then
				comp = childObj:GetComponent("Slider")
			elseif comName == 'Inp' then
				comp = childObj:GetComponent("InputField")
			elseif comName == 'Tog' then
				comp = childObj:GetComponent("Toggle")
			elseif comName == 'TogGup' then
				comp = childObj:GetComponent("ToggleGroup")
			elseif comName == 'Rct' then
				comp = childObj:GetComponent("RectTransform")
			elseif comName == 'TMP' then
				comp = childObj:GetComponent("TMPro.TMP_Text")
			elseif comName == 'Cam' then
				comp = childObj:GetComponent("Camera")
			elseif comName == 'Scr' then
				comp = childObj:GetComponent("ScrollRect")
			end
			if comp then
				objList[curName] = comp
				--收集按钮
				if comName == 'Btn' then
					if objList.__ButtonList == nil then
						objList.__ButtonList = {}
					end
					objList.__ButtonList[comp] = comp
				end
			else
				objList[curName] = childObj
			end

			if childObj.transform.childCount > 0 then
				m.GetChildComponent(childObj, objList)
			end
		end
	end
end
