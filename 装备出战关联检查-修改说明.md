# 装备出战关联检查功能实现说明

## 版本：V2.0
**修改日期：** 2024年12月19日
**更新内容：** 新增EffectID2字段支持，实现EffectID2和EffectID3双字段关联出战规则

## V2.0版本重大更新

### 需求变更
根据用户需求，在现有EffectID3装备关联出战规则基础上，新增EffectID2字段支持：
1. EffectID2和EffectID3字段都可以配置关联装备
2. 主装备出战时，会自动让EffectID2和EffectID3配置的装备都出战
3. 支持多层关联：A(EffectID2→B, EffectID3→C) → B(EffectID2→D) → C(EffectID3→E)
4. 其他规则保持不变（防循环检测、唯一性保证等）

### 技术实现变更

#### 1. 核心函数修改
**涉及文件和函数：**
- `banhao10/Temp/GameLua/UIMainTitle.lua` - `GetAllAssociatedEquipIDsForLogin`
- `banhao10/Temp/GameLua/UIRoleEquip.lua` - `GetAllAssociatedEquipIDs`
- `banhao10/Temp/GameLua/BattleManager.lua` - `GetAllAssociatedEquipIDs`
- `Assets/Temp/GameLua/UIRoleEquip.lua` - `GetAllAssociatedEquipIDs`
- `Assets/Temp/GameLua/BattleManager.lua` - `GetAllAssociatedEquipIDs`

#### 2. 关联检测逻辑升级
**修改前（仅EffectID3）：**
```lua
if equipWeaponConfig.EffectID3 and equipWeaponConfig.EffectID3 ~= 0 then
    local associatedId = equipWeaponConfig.EffectID3
    table.insert(associatedEquipIDs, associatedId)
    currentEquipId = associatedId
else
    break
end
```

**修改后（支持EffectID2和EffectID3）：**
```lua
local nextEquipId = nil
local hasAssociated = false

-- 优先检查EffectID2
if equipWeaponConfig.EffectID2 and equipWeaponConfig.EffectID2 ~= 0 then
    local associatedId = equipWeaponConfig.EffectID2
    table.insert(associatedEquipIDs, associatedId)
    if not nextEquipId then
        nextEquipId = associatedId
    end
    hasAssociated = true
end

-- 检查EffectID3
if equipWeaponConfig.EffectID3 and equipWeaponConfig.EffectID3 ~= 0 then
    local associatedId = equipWeaponConfig.EffectID3
    table.insert(associatedEquipIDs, associatedId)
    if not nextEquipId then
        nextEquipId = associatedId
    end
    hasAssociated = true
end

if hasAssociated and nextEquipId then
    currentEquipId = nextEquipId
else
    break
end
```

#### 3. 日志系统优化
**关联装备发现日志：**
- 新增：`"找到EffectID2关联装备"`
- 新增：`"找到EffectID3关联装备"`
- 优化：登录后装备信息显示格式 `"装备X->关联[ID2:Y,ID3:Z]"`

#### 4. 优先级机制
- **多层关联时**：优先使用EffectID2指向的装备作为下一层搜索起点
- **同时存在时**：EffectID2和EffectID3的装备都会加入关联列表
- **防循环检测**：visitedEquipIDs表同时追踪所有访问过的装备ID

### 功能特性总结

#### V2.0新特性
✅ **双字段支持**：同时支持EffectID2和EffectID3配置关联装备  
✅ **多重关联**：单个主装备可关联多个装备（通过EffectID2和EffectID3）  
✅ **多层链式**：EffectID2和EffectID3的装备也可以有自己的关联装备  
✅ **智能优先级**：EffectID2优先作为下一层搜索起点  
✅ **完整兼容**：保持对现有EffectID3配置的完全兼容  

#### 保持不变的特性
✅ **循环检测**：防止A→B→A的死循环  
✅ **唯一性保证**：同一时间只有1件主装备出战  
✅ **触发时机**：游戏启动检查、装备切换、战斗前检查  
✅ **日志系统**：完整的调试日志（"33333333"、"2222"前缀）

### 使用示例

#### 配置示例
```
装备A (主装备, GroupID<100):
  EffectID2: 装备B
  EffectID3: 装备C

装备B (关联装备):
  EffectID3: 装备D

装备C (关联装备):
  EffectID2: 装备E
```

#### 出战结果
当装备A出战时，会自动出战：装备A、装备B、装备C、装备D、装备E

#### 日志输出示例
```
33333333 登录装备状态规范化------找到EffectID2关联装备: 装备B
33333333 登录装备状态规范化------找到EffectID3关联装备: 装备C
33333333 登录装备状态规范化------找到EffectID3关联装备: 装备D
33333333 登录装备状态规范化------找到EffectID2关联装备: 装备E
11111 登录后-关联装备信息: 装备A->关联[ID2:装备B,ID3:装备C]
```

### 测试要点

#### V2.0版本测试用例
1. **单EffectID2配置**：测试只配置EffectID2的装备关联
2. **单EffectID3配置**：测试只配置EffectID3的装备关联（兼容性）
3. **双字段配置**：测试同时配置EffectID2和EffectID3的装备
4. **多层混合关联**：测试EffectID2→EffectID3→EffectID2的复杂链式关联
5. **循环防护**：测试EffectID2和EffectID3形成的循环引用检测

### 向后兼容性
✅ **配置兼容**：现有只使用EffectID3的装备配置继续正常工作  
✅ **逻辑兼容**：现有装备出战逻辑保持不变  
✅ **日志兼容**：保留原有日志格式，新增详细字段信息  

---

## 版本：V1.1
**修改日期：** 2024年12月19日
**更新内容：** 增强日志功能，使用2222222前缀确认关联装备出战状态

## 需求描述
在进副本前实现装备出战关联检查：
1. 每次进副本前，先判断当前出战的装备ID，是有两件装备出战还是1件
2. 如果有两件装备出战，则直接进战斗，不用做什么处理
3. 如果只有一件装备出战，则根据装备ID→EquipWeapon表字段EffectID3配置的装备ID，也设置为出战
4. 只做关联出战检查和设置，不做其他任何修改，不影响其他任何数据和判断

## 修改文件
**文件路径：** `Assets\Temp\GameLua\BattleManager.lua`

## V1.1版本更新内容

### 问题诊断
用户反馈：进战斗再退出，没有看到关联装备变成出战状态，仍然只有一件装备出战

### 解决方案
1. **增强日志系统：** 使用"2222222"前缀的详细日志
2. **增加检查点：** 检查关联装备是否已经出战，避免重复设置
3. **最终确认：** 在进入战斗场景前最终确认装备出战状态

### 详细日志输出
使用"2222222"前缀的日志包括：
- **2222222 装备出战检查------** 进副本时的装备检查
- **2222222 恢复战斗进度-装备出战检查------** 恢复战斗进度时的装备检查  
- **2222222 进入战斗最终检查------** 进入战斗场景前的最终确认

### 新增检查逻辑
```lua
-- 检查关联装备是否已经出战
local isAssociatedAlreadyWorn = false
for _, wornEquipID in ipairs(currentEquipIDs) do
    if wornEquipID == associatedEquipID then
        isAssociatedAlreadyWorn = true
        break
    end
end

print('2222 装备出战检查------关联装备是否已出战=', isAssociatedAlreadyWorn)

if not isAssociatedAlreadyWorn then
    -- 将关联装备设置为出战
    GamePlayerData.ActorEquip:ReplaceEquipIndex(associatedEquipID, 2, 1)
    print('2222 装备出战检查------已设置关联装备', associatedEquipID, '为出战')
    
    -- 再次检查出战装备列表确认
    local updatedEquipIDs = GamePlayerData.ActorEquip:GetWeaponsKnapsack(1) or {}
    print('2222 装备出战检查------更新后出战装备列表=', json.encode(updatedEquipIDs))
else
    print('2222 装备出战检查------关联装备已经出战，无需重复设置')
end
```

## 修改内容

### 1. EnterBattle方法修改
在装备数量检查后，装备不足判断前，新增装备出战关联检查逻辑：

- ✅ 统计实际出战装备数量（排除0值）
- ✅ 检查主装备的EquipWeapon配置
- ✅ 检查EffectID3字段是否有关联装备ID
- ✅ 检查关联装备是否已经出战
- ✅ 设置关联装备出战到第2个槽位
- ✅ 确认设置结果

### 2. OnConfirmBattleProgress方法修改
在恢复战斗进度时，添加相同的装备关联检查逻辑

### 3. RequestEnterBattleScene方法修改
添加进入战斗前的最终装备出战状态确认

## 调试要点

通过查看"2222"前缀的日志，可以确认：

1. **装备配置读取：** 确认能否正确读取EquipWeapon表配置
2. **EffectID3值：** 确认关联装备ID是否正确
3. **关联装备检查：** 确认关联装备是否已经在出战列表中
4. **设置操作：** 确认`ReplaceEquipIndex`调用是否成功
5. **最终状态：** 确认设置后的装备列表是否包含关联装备

## 可能的问题排查

### 1. 装备配置问题
```
2222 装备出战检查------主装备ID= [装备ID]
2222 装备出战检查------找到装备配置，EffectID3= [关联装备ID或nil]
```

### 2. 关联装备设置问题
```
2222 装备出战检查------关联装备是否已出战= false
2222 装备出战检查------已设置关联装备 [关联装备ID] 为出战
2222 装备出战检查------更新后出战装备列表= [包含两件装备的JSON数组]
```

### 3. 最终确认问题
```
2222 进入战斗最终检查------最终出战装备数量= 2
2222 进入战斗最终检查------最终出战装备列表= [最终装备列表]
```

## 符合项目规范
✅ **命名规范：** 遵循项目Lua代码命名风格
✅ **注释规范：** 添加中文注释说明逻辑
✅ **错误处理：** 添加必要的nil值和边界检查
✅ **日志规范：** 使用统一的"2222"前缀便于调试
✅ **最小修改：** 只修改必要部分，不影响其他功能
✅ **调试友好：** 详细的日志输出便于问题排查

## 功能逻辑说明

### 1. 检查时机
- **正常进副本：** 在EnterBattle方法中，装备数量检查之后，装备数量不足判断之前
- **恢复战斗进度：** 在OnConfirmBattleProgress方法中，获取关卡配置之后，进入战斗场景之前

### 2. 检查逻辑
1. **获取当前出战装备列表：** 通过`GamePlayerData.ActorEquip:GetWeaponsKnapsack(1)`获取
2. **统计实际出战装备数量：** 排除值为0的空槽位
3. **判断装备数量：** 
   - 如果有2件或以上装备出战，无需处理
   - 如果只有1件装备出战，检查是否有关联装备

### 3. 关联装备处理
1. **查询EquipWeapon表：** 通过`Schemes.EquipWeapon:Get(mainEquipID)`获取装备配置
2. **检查EffectID3字段：** 判断是否有关联装备ID且不为0
3. **设置关联装备出战：** 使用`GamePlayerData.ActorEquip:ReplaceEquipIndex(associatedEquipID, 2, 1)`将关联装备设置到第2个槽位
4. **更新装备数量：** 重新获取出战装备数量供后续逻辑使用

### 4. 日志输出
为便于调试，添加了详细的日志输出：
- 实际出战装备数量
- 出战装备列表
- 关联装备发现和设置过程
- 更新后的装备数量

## 影响范围
**最小化修改原则：** 只在必要位置添加装备关联检查，不修改任何其他逻辑
**兼容性保证：** 不影响现有装备系统、战斗系统等其他功能
**数据安全：** 仅设置装备出战状态，不修改装备属性、等级等数据

## 测试要点
1. **单装备出战有关联：** 测试只出战一件有EffectID3配置的装备，确保关联装备自动出战
2. **单装备出战无关联：** 测试只出战一件无EffectID3或EffectID3为0的装备，确保无额外处理
3. **双装备出战：** 测试已出战两件装备的情况，确保不进行任何处理
4. **恢复战斗进度：** 测试从战斗进度恢复时的装备检查逻辑
5. **装备数量检查：** 确保关联装备设置后，装备数量检查逻辑正常工作

## 符合项目规范
✅ **命名规范：** 遵循项目Lua代码命名风格
✅ **注释规范：** 添加中文注释说明逻辑
✅ **错误处理：** 添加必要的nil值和边界检查
✅ **日志规范：** 使用统一的日志前缀便于调试
✅ **最小修改：** 只修改必要部分，不影响其他功能 