# 装备出战关联检查功能实现说明

## 版本：V2.0
**修改日期：** 2024年12月19日
**更新内容：** 新增EffectID2字段支持，实现EffectID2和EffectID3双字段关联出战规则

## V2.0版本重大更新

### 需求变更
根据用户需求，在现有EffectID3装备关联出战规则基础上，新增EffectID2字段支持：
1. EffectID2和EffectID3字段都可以配置关联装备
2. 主装备出战时，会自动让EffectID2和EffectID3配置的装备都出战
3. 支持多层关联：A(EffectID2→B, EffectID3→C) → B(EffectID2→D) → C(EffectID3→E)
4. 其他规则保持不变（防循环检测、唯一性保证等）

### 技术实现变更

#### 1. 核心函数修改
**涉及文件和函数：**
- `banhao10/Temp/GameLua/UIMainTitle.lua` - `GetAllAssociatedEquipIDsForLogin`
- `banhao10/Temp/GameLua/UIRoleEquip.lua` - `GetAllAssociatedEquipIDs`
- `banhao10/Temp/GameLua/BattleManager.lua` - `GetAllAssociatedEquipIDs`
- `Assets/Temp/GameLua/UIRoleEquip.lua` - `GetAllAssociatedEquipIDs`
- `Assets/Temp/GameLua/BattleManager.lua` - `GetAllAssociatedEquipIDs`

#### 2. 关联检测逻辑升级
**修改前（仅EffectID3）：**
```lua
if equipWeaponConfig.EffectID3 and equipWeaponConfig.EffectID3 ~= 0 then
    local associatedId = equipWeaponConfig.EffectID3
    table.insert(associatedEquipIDs, associatedId)
    currentEquipId = associatedId
else
    break
end
```

**修改后（支持EffectID2和EffectID3）：**
```lua
local nextEquipId = nil
local hasAssociated = false

-- 优先检查EffectID2
if equipWeaponConfig.EffectID2 and equipWeaponConfig.EffectID2 ~= 0 then
    local associatedId = equipWeaponConfig.EffectID2
    table.insert(associatedEquipIDs, associatedId)
    if not nextEquipId then
        nextEquipId = associatedId
    end
    hasAssociated = true
end

-- 检查EffectID3
if equipWeaponConfig.EffectID3 and equipWeaponConfig.EffectID3 ~= 0 then
    local associatedId = equipWeaponConfig.EffectID3
    table.insert(associatedEquipIDs, associatedId)
    if not nextEquipId then
        nextEquipId = associatedId
    end
    hasAssociated = true
end

if hasAssociated and nextEquipId then
    currentEquipId = nextEquipId
else
    break
end
```

#### 3. 日志系统优化
**关联装备发现日志：**
- 新增：`"找到EffectID2关联装备"`
- 新增：`"找到EffectID3关联装备"`
- 优化：登录后装备信息显示格式 `"装备X->关联[ID2:Y,ID3:Z]"`

#### 4. 优先级机制
- **多层关联时**：优先使用EffectID2指向的装备作为下一层搜索起点
- **同时存在时**：EffectID2和EffectID3的装备都会加入关联列表
- **防循环检测**：visitedEquipIDs表同时追踪所有访问过的装备ID

### 功能特性总结

#### V2.0新特性
✅ **双字段支持**：同时支持EffectID2和EffectID3配置关联装备  
✅ **多重关联**：单个主装备可关联多个装备（通过EffectID2和EffectID3）  
✅ **多层链式**：EffectID2和EffectID3的装备也可以有自己的关联装备  
✅ **智能优先级**：EffectID2优先作为下一层搜索起点  
✅ **完整兼容**：保持对现有EffectID3配置的完全兼容  

#### 保持不变的特性
✅ **循环检测**：防止A→B→A的死循环  
✅ **唯一性保证**：同一时间只有1件主装备出战  
✅ **触发时机**：游戏启动检查、装备切换、战斗前检查  
✅ **日志系统**：完整的调试日志（"33333333"、"2222"前缀）

### 使用示例

#### 配置示例
```
装备A (主装备, GroupID<100):
  EffectID2: 装备B
  EffectID3: 装备C

装备B (关联装备):
  EffectID3: 装备D

装备C (关联装备):
  EffectID2: 装备E
```

#### 出战结果
当装备A出战时，会自动出战：装备A、装备B、装备C、装备D、装备E

#### 日志输出示例
```
33333333 登录装备状态规范化------找到EffectID2关联装备: 装备B
33333333 登录装备状态规范化------找到EffectID3关联装备: 装备C
33333333 登录装备状态规范化------找到EffectID3关联装备: 装备D
33333333 登录装备状态规范化------找到EffectID2关联装备: 装备E
11111 登录后-关联装备信息: 装备A->关联[ID2:装备B,ID3:装备C]
```

### 测试要点

#### V2.0版本测试用例
1. **单EffectID2配置**：测试只配置EffectID2的装备关联
2. **单EffectID3配置**：测试只配置EffectID3的装备关联（兼容性）
3. **双字段配置**：测试同时配置EffectID2和EffectID3的装备
4. **多层混合关联**：测试EffectID2→EffectID3→EffectID2的复杂链式关联
5. **循环防护**：测试EffectID2和EffectID3形成的循环引用检测

### 向后兼容性
✅ **配置兼容**：现有只使用EffectID3的装备配置继续正常工作  
✅ **逻辑兼容**：现有装备出战逻辑保持不变  
✅ **日志兼容**：保留原有日志格式，新增详细字段信息  

---

## 版本：V1.1
**修改日期：** 2024年12月19日
**更新内容：** 增强日志功能，使用2222222前缀确认关联装备出战状态

## 需求描述
在进副本前实现装备出战关联检查：
1. 每次进副本前，先判断当前出战的装备ID，是有两件装备出战还是1件
2. 如果有两件装备出战，则直接进战斗，不用做什么处理
3. 如果只有一件装备出战，则根据装备ID→EquipWeapon表字段EffectID3配置的装备ID，也设置为出战
4. 只做关联出战检查和设置，不做其他任何修改，不影响其他任何数据和判断

## 修改文件
**文件路径：** `Assets\Temp\GameLua\BattleManager.lua`

## V1.1版本更新内容

### 问题诊断
用户反馈：进战斗再退出，没有看到关联装备变成出战状态，仍然只有一件装备出战

### 解决方案
1. **增强日志系统：** 使用"2222222"前缀的详细日志
2. **增加检查点：** 检查关联装备是否已经出战，避免重复设置
3. **最终确认：** 在进入战斗场景前最终确认装备出战状态

### 详细日志输出
使用"2222222"前缀的日志包括：
- **2222222 装备出战检查------** 进副本时的装备检查
- **2222222 恢复战斗进度-装备出战检查------** 恢复战斗进度时的装备检查  
- **2222222 进入战斗最终检查------** 进入战斗场景前的最终确认

### 新增检查逻辑
```lua
-- 检查关联装备是否已经出战
local isAssociatedAlreadyWorn = false
for _, wornEquipID in ipairs(currentEquipIDs) do
    if wornEquipID == associatedEquipID then
        isAssociatedAlreadyWorn = true
        break
    end
end

print('2222 装备出战检查------关联装备是否已出战=', isAssociatedAlreadyWorn)

if not isAssociatedAlreadyWorn then
    -- 将关联装备设置为出战
    GamePlayerData.ActorEquip:ReplaceEquipIndex(associatedEquipID, 2, 1)
    print('2222 装备出战检查------已设置关联装备', associatedEquipID, '为出战')
    
    -- 再次检查出战装备列表确认
    local updatedEquipIDs = GamePlayerData.ActorEquip:GetWeaponsKnapsack(1) or {}
    print('2222 装备出战检查------更新后出战装备列表=', json.encode(updatedEquipIDs))
else
    print('2222 装备出战检查------关联装备已经出战，无需重复设置')
end
```

## 修改内容

### 1. EnterBattle方法修改
在装备数量检查后，装备不足判断前，新增装备出战关联检查逻辑：

- ✅ 统计实际出战装备数量（排除0值）
- ✅ 检查主装备的EquipWeapon配置
- ✅ 检查EffectID3字段是否有关联装备ID
- ✅ 检查关联装备是否已经出战
- ✅ 设置关联装备出战到第2个槽位
- ✅ 确认设置结果

### 2. OnConfirmBattleProgress方法修改
在恢复战斗进度时，添加相同的装备关联检查逻辑

### 3. RequestEnterBattleScene方法修改
添加进入战斗前的最终装备出战状态确认

## 调试要点

通过查看"2222"前缀的日志，可以确认：

1. **装备配置读取：** 确认能否正确读取EquipWeapon表配置
2. **EffectID3值：** 确认关联装备ID是否正确
3. **关联装备检查：** 确认关联装备是否已经在出战列表中
4. **设置操作：** 确认`ReplaceEquipIndex`调用是否成功
5. **最终状态：** 确认设置后的装备列表是否包含关联装备

## 可能的问题排查

### 1. 装备配置问题
```
2222 装备出战检查------主装备ID= [装备ID]
2222 装备出战检查------找到装备配置，EffectID3= [关联装备ID或nil]
```

### 2. 关联装备设置问题
```
2222 装备出战检查------关联装备是否已出战= false
2222 装备出战检查------已设置关联装备 [关联装备ID] 为出战
2222 装备出战检查------更新后出战装备列表= [包含两件装备的JSON数组]
```

### 3. 最终确认问题
```
2222 进入战斗最终检查------最终出战装备数量= 2
2222 进入战斗最终检查------最终出战装备列表= [最终装备列表]
```

## 符合项目规范
✅ **命名规范：** 遵循项目Lua代码命名风格
✅ **注释规范：** 添加中文注释说明逻辑
✅ **错误处理：** 添加必要的nil值和边界检查
✅ **日志规范：** 使用统一的"2222"前缀便于调试
✅ **最小修改：** 只修改必要部分，不影响其他功能
✅ **调试友好：** 详细的日志输出便于问题排查

## 功能逻辑说明

### 1. 检查时机
- **正常进副本：** 在EnterBattle方法中，装备数量检查之后，装备数量不足判断之前
- **恢复战斗进度：** 在OnConfirmBattleProgress方法中，获取关卡配置之后，进入战斗场景之前

### 2. 检查逻辑
1. **获取当前出战装备列表：** 通过`GamePlayerData.ActorEquip:GetWeaponsKnapsack(1)`获取
2. **统计实际出战装备数量：** 排除值为0的空槽位
3. **判断装备数量：** 
   - 如果有2件或以上装备出战，无需处理
   - 如果只有1件装备出战，检查是否有关联装备

### 3. 关联装备处理
1. **查询EquipWeapon表：** 通过`Schemes.EquipWeapon:Get(mainEquipID)`获取装备配置
2. **检查EffectID3字段：** 判断是否有关联装备ID且不为0
3. **设置关联装备出战：** 使用`GamePlayerData.ActorEquip:ReplaceEquipIndex(associatedEquipID, 2, 1)`将关联装备设置到第2个槽位
4. **更新装备数量：** 重新获取出战装备数量供后续逻辑使用

### 4. 日志输出
为便于调试，添加了详细的日志输出：
- 实际出战装备数量
- 出战装备列表
- 关联装备发现和设置过程
- 更新后的装备数量

## 影响范围
**最小化修改原则：** 只在必要位置添加装备关联检查，不修改任何其他逻辑
**兼容性保证：** 不影响现有装备系统、战斗系统等其他功能
**数据安全：** 仅设置装备出战状态，不修改装备属性、等级等数据

## 测试要点
1. **单装备出战有关联：** 测试只出战一件有EffectID3配置的装备，确保关联装备自动出战
2. **单装备出战无关联：** 测试只出战一件无EffectID3或EffectID3为0的装备，确保无额外处理
3. **双装备出战：** 测试已出战两件装备的情况，确保不进行任何处理
4. **恢复战斗进度：** 测试从战斗进度恢复时的装备检查逻辑
5. **装备数量检查：** 确保关联装备设置后，装备数量检查逻辑正常工作

## 符合项目规范
✅ **命名规范：** 遵循项目Lua代码命名风格
✅ **注释规范：** 添加中文注释说明逻辑
✅ **错误处理：** 添加必要的nil值和边界检查
✅ **日志规范：** 使用统一的日志前缀便于调试
✅ **最小修改：** 只修改必要部分，不影响其他功能

---

## 装备数据传递给战斗系统完整代码段

### 关键问题解答
**问题**: 如何确保关联装备的技能能在战斗中正常使用？
**答案**: 通过完整的数据传递链路，从Lua装备数据获取到C#战斗系统应用。

### 核心数据传递路径

#### 1. Lua侧装备数据获取
**文件**: `ActorProp.lua`
```lua
-- 获取所有出战装备的属性列表
function ActorProp.GetWeapons(onlyWear)
    local lst = {}
    for i, v in ipairs(Schemes.EquipWeapon.items) do
        -- 钱袋和背包空格子总是出战
        if v.GroupID >= 1000 and v.EffectID5 == 0 then
            local weaponProp = ActorProp.GetWeaponProp(v.ID)
            if weaponProp then table.insert(lst, weaponProp) end
        else
            -- 根据onlyWear参数决定是否只获取出战装备
            local weaponProp = ActorProp.GetWeaponProp(v.ID, onlyWear)
            if weaponProp then table.insert(lst, weaponProp) end
        end
    end
    return lst
end

-- 获取单个装备的属性
function ActorProp.GetWeaponProp(goodsID, needWear)
    if HelperL.IsEuipType(goodsID) then
        local equipment = Schemes.Equipment:Get(goodsID)
        if equipment then
            if not needWear or GamePlayerData.ActorEquip:IsWear(equipment.ID, 1) then
                local entity = SkepModule:GetSkepByID(equipment.PacketID):GetEntityByGoodsID(equipment.ID)
                return ActorProp.ReadEquipProp(entity)
            end
        end
    end
    return nil
end
```

#### 2. Lua到C#数据传递
**文件**: `DataService.lua`
```lua
-- C#调用的数据传递接口
function m.GetWeapons(onlyWear)
    return dkjsonHelper.encode(ActorProp.GetWeapons(onlyWear))
end

function m.GetWeaponProp(goodsID, needWear)
    return dkjsonHelper.encode(ActorProp.GetWeaponProp(goodsID, needWear))
end

function m.ReadEquipProp(goodsID)
    local equip = nil
    local equipment = Schemes.Equipment:Get(goodsID)
    if equipment then
        local packsack = SkepModule:GetSkepByID(equipment.PacketID)
        if packsack then
            equip = packsack:GetEntityByGoodsID(equipment.ID)
        end
    end
    return dkjsonHelper.encode(ActorProp.ReadEquipProp(equip))
end
```

**文件**: `LuaDataSrvClient.cs`
```csharp
// C#侧获取装备数据的关键接口
public List<EquipProps> GetWeapons(bool onlyWear)
{
    var json = LuaManager.Instance.LuaState_.Invoke<bool, string>(
        "DataService.GetWeapons", onlyWear, true);
    return JsonConvert.DeserializeObject<List<EquipProps>>(json);
}

public EquipProps GetWeaponProp(int goodsID, bool needWear = false)
{
    var json = LuaManager.Instance.LuaState_.Invoke<int, bool, string>(
        "DataService.GetWeaponProp", goodsID, needWear, true);
    return JsonConvert.DeserializeObject<EquipProps>(json);
}
```

#### 3. 战斗系统装备初始化
**文件**: `GameManager.cs`
```csharp
// 战斗管理器初始化玩家角色
public async UniTaskVoid InitPlayerActor()
{
    // 创建角色数据对象
    Actor = new ActorThing
    {
        ActorId = LuaDataSrvClient.Instance.GetActorID(), 
        ActorProp = LuaDataSrvClient.Instance.GetPlayerProp()
    };
    
    // 关键：传递出战装备数据到战斗系统
    Actor.InitWeapons(LuaDataSrvClient.Instance.GetWeapons(true));

    // 等待属性表加载完成
    await UniTask.WaitUntil(() => SingletonMgr.Instance.GlobalMgr.CommonPropCfgLoaded.Value);

    // 读取战斗进度
    Actor.ReadProgress();
}
```

**文件**: `ActorThing_Props.cs`
```csharp
// 初始化角色装备列表
public ActorThing InitWeapons(List<EquipProps> weapons)
{
    Weapons = weapons;  // 存储装备属性列表，包含关联装备

    // 初始化商店枪械权重列表
    GunIds_Store = SingletonMgr.Instance.CsvLoaderMgr.GetOrAddLoader<GunCsv>().Pb.CSVTable
        .Where(x => !x.IsHidden && CanOfferGun(x))
        .Select(x =>
        {
            WeightingItem<GunCfg.Types.CSVRow> rtn = new WeightingItem<GunCfg.Types.CSVRow> { Value = x };
            rtn.Weighting = rtn.Value.OfferWeight;
            return rtn;
        }).ToList();

    return this;
}
```

#### 4. 装备到枪械实体转换
**文件**: `ActorThing.cs`
```csharp
// 根据装备列表重新创建枪械实体
public override void ReCreateGuns(List<GunItem> gunsInBag, bool apply = false)
{
    Guns.Where(p => p.IsHidden).ToList().ForEach(g => g.StopCdExecutor());
    Guns.Clear();

    // 为每件出战装备创建对应的隐藏枪械
    Weapons.ToList().ForEach(w =>
    {
        var csvRow_Equip = SingletonMgr.Instance.CsvLoaderMgr.GetOrAddLoader<EquipmentCsv>().Dic[w.GoodsID];
        var gunItem = new GunItem
        {
            GoodsId = w.GoodsID,
            GunId = csvRow_Equip.ConsignmentStyle,  // Equipment.ConsignmentStyle → Gun.Id 映射
            GunLvl = 1,
            CsvRow_Gun = SingletonMgr.Instance.CsvLoaderMgr.GetOrAddLoader<GunCsv>().Dic[csvRow_Equip.ConsignmentStyle],
            StarCount = LuaDataSrvClient.Instance.GetWeaponProp(w.GoodsID).SmeltStarCount,
        };
        
        if (gunItem.CsvRow_Gun.GunType is ActionType.Attack or ActionType.Mattack or ActionType.Hp or ActionType.Armor)
        {
            var (gun, _) = AddGun(gunItem, apply);
            gun.Camp = Camp;
            gun.IsHidden = true;  // 装备对应的枪械设为隐藏
        }
    });

    // 处理背包中的可见枪械
    gunsInBag.Where(g => g.GunId > 0).ToList().ForEach(g =>
    {
        var (gun, _) = AddGun(g, apply);
        gun.Camp = Camp;
    });
}
```

#### 5. 枪械属性应用和技能数据传递
**文件**: `GunThing.cs`
```csharp
// 枪械初始化方法
public GunThing InitFromCsv(int gunId, int gunLvl, int weaponLvl)
{
    if (gunId <= 0 || gunLvl <= 0) return this;
    
    // 设置枪械等级和武器等级
    ThingLvl.Value = gunLvl;
    WeaponLvl.Value = weaponLvl;
    
    // 加载枪械配置行，包含技能数据
    CsvRow_Gun.Value = SingletonMgr.Instance.CsvLoaderMgr.GetOrAddLoader<GunCsv>().Dic[gunId];
    
    return this;
}

// 装备属性应用到枪械实体
public override List<CommonProp> FindInherentPropRows()
{
    List<CommonProp> rtn = new();

    // 角色的枪械处理
    if (Actor != null)
    {
        // 从Lua获取装备的计算后属性
        CreaturePropBase weaponProp =
            LuaDataSrvClient.Instance.GetWeaponProp(CsvRow_Gun.Value.GoodsID)?.EquipProp ??
            LuaDataSrvClient.Instance.ReadStarProp(CsvRow_Gun.Value.GoodsID, -1);
        
        if (weaponProp != null)
        {
            // 将装备属性转换为枪械属性并应用
            // 这里包括攻击力、防御力、HP等所有属性
            // 以及技能相关的属性数据
        }
    }
    
    return rtn;
}
```

#### 6. 战斗中装备数据访问
**文件**: `UIFightBag.cs`
```csharp
// 获取战斗背包中的枪械列表（包括关联装备的枪械）
public List<GunThing> GunsInBag => SingletonMgr.Instance.BattleMgr.Actor.GunsInBag.ToList()

// 计算装备提供的HP值示例
private int GetHpValueByActorEquipment()
{
    int hp = 0;
    foreach (var gGun in SingletonMgr.Instance.BattleMgr.Actor.GunsInBag.ToList()
                 .Where(gun => gun.CsvRow_Gun.Value.Id > 0))
    {
        if (gGun.CsvRow_Gun.Value.GunType == ActionType.Hp || gGun.CsvRow_Gun.Value.GunType == ActionType.Armor)
        {
            // 通过装备ID获取最新属性并计算HP值
            hp += Convert.ToInt32(LuaDataSrvClient.Instance.ReadEquipProp(gGun.CsvRow_Gun.Value.GoodsID).EquipProp.HP * Math.Pow(1.5f, gGun.ThingLvl.Value - 1));
        }
    }
    return hp + 100;
}
```

### 关联装备技能可用性保证

#### 完整数据链路
1. **关联检查确保出战**: 关联装备检查机制确保关联装备正确出战
2. **Lua数据获取**: `ActorProp.GetWeapons(true)` 获取所有出战装备属性
3. **C#数据传递**: `LuaDataSrvClient.GetWeapons()` 传递到战斗系统
4. **枪械实体创建**: `ReCreateGuns()` 为每个装备创建枪械实体
5. **属性正确应用**: `GunThing.FindInherentPropRows()` 应用装备属性
6. **技能数据访问**: 通过枪械配置访问技能和子弹数据

#### 关键映射关系
- **Equipment.ConsignmentStyle** → **Gun.Id**: 装备映射到枪械配置
- **Gun.BulletId** → **Bullet配置**: 枪械映射到技能/子弹配置
- **装备出战状态** → **枪械IsHidden属性**: 关联装备的枪械为隐藏状态但功能完整

#### 数据存储位置
**文件**: `ActorDataCatalog.cs`
```csharp
public enum ActorDataCatalog
{
    // 装备出战数据存储ID
    WeaponsInStage = 8512,
}
```

通过这个完整的数据传递链路，确保关联装备的技能在战斗中能够正常使用：
- 关联装备正确出战 ✅
- 装备数据完整传递 ✅  
- 枪械实体正确创建 ✅
- 属性正确应用 ✅
- 技能数据可访问 ✅ 