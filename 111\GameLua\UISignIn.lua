--[[
********************************************************************
    created:    2024/07/23
    author :    李锦剑
    purpose:    签到
*********************************************************************
--]]
local luaID = ('UISignIn')

--累计奖励显示数量
local AccumulativeShowNum = 5

---@class UISignIn:UIWndBase
local m = {}

--------------------------------------------------------------------
-- 订阅事件列表
--------------------------------------------------------------------
function m:GetOpenEventList()
    return {
        [EventID.LogicDataChange] = m.UpdateView,
        [EventID.EntitySyncBuff] = m.UpdateView,
        [EventID.OnGoodsPropChange] = m.UpdateView,
        [EventID.ActorDataChanged] = m.UpdateView,
        [EventID.OnHeroPropChange] = m.UpdateView,
    }
end

--------------------------------------------------------------------
-- 初始化
--------------------------------------------------------------------
function m:OnCreate()
    --签到数据
    m.signinDataList1 = Schemes.PrizesevenSignin:GetByPrizeType(1) or {}
    ---@type Item_SignIn[]
    m.Item_SignIn_List = {}
    for i = 1, 7 do
        m.Item_SignIn_List[i] = m.Create_Item_SignIn(i)
        m.Item_SignIn_List[i].SetData(m.signinDataList1[i])
    end

    --累计签到数据
    m.signinDataList2 = Schemes.PrizesevenSignin:GetByPrizeType(2) or {}
    ---@type Item_Accumulative[]
    m.Item_Accumulative_List = {}
    for i = 1, 5 do
        m.Item_Accumulative_List[i] = m.Create_Item_Accumulative(i)
    end

    m.RegisterClickEvent()
    return true
end

--------------------------------------------------------------------
-- 窗口开启
--------------------------------------------------------------------
function m:OnOpen()
    m.UpdateView()
end

--------------------------------------------------------------------
-- 注册点击事件
--------------------------------------------------------------------
function m.RegisterClickEvent()
    m:AddClick(m.objList.Btn_Close, function()
        m:CloseSelf()
    end)
    --m:AddClick(m.objList.Btn_SignIn, m.OnSignIn)
end

--------------------------------------------------------------------
--更新界面
--------------------------------------------------------------------
function m.UpdateView()
    for i, v in ipairs(m.Item_SignIn_List) do
        v.UpdateView()
    end

    local time = HelperL.GetServerTime()
    --签到时间
    --local signInTime = GamePlayerData.ActorActivity:GetSignInData(SignInData_Index.SignInTime)
    --判断是否已签到
    -- if HelperL.is_same_day(time, signInTime) then
    --     m.objList.Txt_SignIn.text = GetGameText(luaID, 2)
    --     m.objList.Img_SignInGray.gameObject:SetActive(true)
    --     m.objList.Img_RedDotSignIn.gameObject:SetActive(false)
    -- else
    --     m.objList.Txt_SignIn.text = GetGameText(luaID, 3)
    --     m.objList.Img_SignInGray.gameObject:SetActive(false)
    --     m.objList.Img_RedDotSignIn.gameObject:SetActive(true)
    -- end

    --累计奖励，已领取最大序号
    local maxSort = GamePlayerData.ActorActivity:GetSignInData(SignInData_Index.SignInCumulativeMaxSort)
    local num = math.floor(maxSort / AccumulativeShowNum) * AccumulativeShowNum
    local isGetNum = 0
    local cfg
    --累计签到次数
    local signInCumulativeNumber = GamePlayerData.ActorActivity:GetSignInData(SignInData_Index.SignInCumulativeNumber)
    for i, v in ipairs(m.Item_Accumulative_List) do
        cfg = m.signinDataList2[i + num]
        v.UpdateView(cfg)
        if cfg and signInCumulativeNumber >= cfg.ReceivePremissParam then
            isGetNum = isGetNum + 1
        end
    end
    m.objList.Sld_Days.value = isGetNum / AccumulativeShowNum
    m.objList.Txt_Days.text = string.format(GetGameText(luaID, 4), signInCumulativeNumber)
end

--------------------------------------------------------------------
--领取签到奖励
---@param cfg PrizesevenSigninCfg
--------------------------------------------------------------------
function m.GetSignInReward(cfg)
    if not cfg then
        return
    end

    if cfg.PrizeType == 1 then
        local time = HelperL.GetServerTime()
        --签到时间
        local signInTime = HeroDataManager:GetLogicData(LOGIC_DATA.DATA_HORSE_HAVEGET)
        --local signInTime = GamePlayerData.ActorActivity:GetSignInData(LOGIC_DATA.DATA_HORSE_HAVEGET)
        --判断是否已签到
        if HelperL.is_same_day(time, signInTime) then
            HelperL.ShowMessage(TipType.FlowText, GetGameText(luaID, 2))
            return
        end
    elseif cfg.PrizeType == 2 then
        local lv = GamePlayerData.LogicValue:GetIntByIndex(cfg.ActorDataCatalog, cfg.Sort)
        if lv ~= 0 then
            HelperL.ShowMessage(TipType.FlowText, CommonTextID.IS_GET)
            return
        end
        local signInCumulativeNumber = GamePlayerData.ActorActivity:GetSignInData(SignInData_Index
            .SignInCumulativeNumber)
        if signInCumulativeNumber < cfg.ReceivePremissParam then
            HelperL.ShowMessage(TipType.FlowText, CommonTextID.NOT_SATISFIED_RECEIVE_CONDITION)
            return
        end
    end

    local form = {}
    form["SigninID"] = cfg.Id
    LuaModuleNew.SendRequest(LuaRequestID.GetPrizesevenSignin, form, function(resultCode, content)
        if resultCode == RESULT_CODE_BASE.RESULT_COMMON_SUCCEED[1] then

        else
            HelperL.ShowMessage(TipType.FlowText, ResultCode:GetResultCodeStr(resultCode, content))
        end
    end)
end

--------------------------------------------------------------------
--签到
--------------------------------------------------------------------
function m.OnSignIn()
    --签到次数
    local signInNumber = HeroDataManager:GetLogicData(LOGIC_DATA.DATA_WEEK_SIGNIN_BITS)
    --local signInNumber = GamePlayerData.ActorActivity:GetSignInData(SignInData_Index.SignInNumber)
    local cfg = m.signinDataList1[signInNumber + 1]
    m.GetSignInReward(cfg)
end

--------------------------------------------------------------------
---创建签到框
---@param index integer
---@return Item_SignIn
--------------------------------------------------------------------
function m.Create_Item_SignIn(index)
    ---@class Item_SignIn
    local item = {}
    item.index = index
    item.GoodsList = {}
    --if index == 7 then
       -- item.com = m:CreateSubItem(m.objList.Obj_SignIn7, m.objList.Item_SignIn7, false)
    --else
       item.com = m:CreateSubItem(m.objList.Grid_SignIn, m.objList.Item_SignIn7)
   -- end
    item.com.Txt_Headline.text = string.format(GetGameText(luaID, 1), ToChineseNumbers(index))
    item.com.Img_Bg1.gameObject:SetActive(true)
    item.com.Img_Bg2.gameObject:SetActive(false)
    item.com.Img_Bg3.gameObject:SetActive(false)
    m:AddClick(item.com.Btn_SignIn, function()
        m.OnSignIn()
    end)

    ---设置数据
    ---@param cfg PrizesevenSigninCfg
    item.SetData = function(cfg)
        item.cfg = cfg
        if cfg then
            local goodList = Schemes.PrizeTable:GetGoodsList(cfg.PrizeId) or {}
            local num = math.max(#goodList, #item.GoodsList)
            for i = 1, num, 1 do
                if not item.GoodsList[i] then
                    item.GoodsList[i] = _GAddSlotItem(item.com.Grid_Goods)
                end
                if goodList[i] then
                    item.GoodsList[i]:SetItemID(goodList[i].id)
                    item.GoodsList[i]:SetCount(goodList[i].num)
                    item.GoodsList[i]:SetActive(true)
                else
                    item.GoodsList[i]:SetActive(false)
                end
            end
            item.com.gameObject:SetActive(true)
        else
            item.com.gameObject:SetActive(false)
        end
    end

    ---更新界面
    item.UpdateView = function()
        if not item.cfg then return end

        item.com.Img_Bg1.gameObject:SetActive(false)
        item.com.Img_Bg2.gameObject:SetActive(false)
        item.com.Img_Bg3.gameObject:SetActive(false)
        item.com.Img_yqd.gameObject:SetActive(false)
        item.com.Btn_SignIn.gameObject:SetActive(false)
        item.com.Txt_Hint.text = ''

        local lv = GamePlayerData.LogicValue:GetIntByIndex(item.cfg.ActorDataCatalog, item.cfg.Sort)
        local signInNumber = HeroDataManager:GetLogicData(LOGIC_DATA.DATA_WEEK_SIGNIN_BITS)
        print("lv ------------- "..lv)
        if signInNumber >=  item.index then
            --已领取
            item.com.Img_Bg2.gameObject:SetActive(true)
            item.com.Img_yqd.gameObject:SetActive(true)
            --item.com.Txt_Hint.text = CommonTextID.IS_GET
        else
            local time = HelperL.GetServerTime()
            --签到时间
            local signInTime = HeroDataManager:GetLogicData(LOGIC_DATA.DATA_HORSE_HAVEGET)
            --local signInTime = GamePlayerData.ActorActivity:GetSignInData(SignInData_Index.SignInTime)
            --签到次数
            
            --local signInNumber = GamePlayerData.ActorActivity:GetSignInData(SignInData_Index.SignInNumber)
        print("signInNumber ------------- "..signInNumber)
            --判断是否已签到
            if not HelperL.is_same_day(time, signInTime) and signInNumber + 1 == item.cfg.ReceivePremissParam then
                --条件满足，可领取
                item.com.Img_Bg3.gameObject:SetActive(true)
                item.com.Btn_SignIn.gameObject:SetActive(true)
                --item.com.Txt_Hint.text = GetGameText(luaID, 3)
            else
                --条件不满足
                item.com.Img_Bg1.gameObject:SetActive(true)
                --item.com.gameObject:SetActive(false)
            end
        end
    end

    return item
end

--------------------------------------------------------------------
---创建累计签到奖励框
---@param index integer
---@return Item_Accumulative
--------------------------------------------------------------------
function m.Create_Item_Accumulative(index)
    ---@class Item_Accumulative
    local item = {}
    item.index = index
    item.com = m:CreateSubItem(m.objList.Grid_Accumulative, m.objList.Item_Accumulative)
    item.com.Img_Bg1.gameObject:SetActive(true)
    item.com.Img_Bg2.gameObject:SetActive(false)

    m:AddClick(item.com.Btn_SignIn, function()
        m.GetSignInReward(item.cfg)
    end)

    ---更新界面
    ---@param cfg PrizesevenSigninCfg
    item.UpdateView = function(cfg)
        item.cfg = cfg
        if cfg then
            item.com.Txt_Days.text = cfg.ReceivePremissParam

            item.com.Img_Bg1.gameObject:SetActive(false)
            item.com.Img_Bg2.gameObject:SetActive(false)
            item.com.Img_RedDot.gameObject:SetActive(false)
            item.com.Img_Select.gameObject:SetActive(false)
            HelperL.SetImageGray(item.com.Img_DaysBg, false)

            local lv = GamePlayerData.LogicValue:GetIntByIndex(item.cfg.ActorDataCatalog, item.cfg.Sort)
            if lv ~= 0 then
                --已领取
                item.com.Img_Bg2.gameObject:SetActive(true)
                HelperL.SetImageGray(item.com.Img_DaysBg, true)
            else
                local signInCumulativeNumber = GamePlayerData.ActorActivity:GetSignInData(SignInData_Index
                    .SignInCumulativeNumber)
                if signInCumulativeNumber >= item.cfg.ReceivePremissParam then
                    --条件满足，可领取
                    item.com.Img_RedDot.gameObject:SetActive(true)
                    item.com.Img_Bg1.gameObject:SetActive(true)
                    item.com.Img_Select.gameObject:SetActive(true)
                else
                    --条件不满足
                    item.com.Img_Bg1.gameObject:SetActive(true)
                end
            end
            item.com.gameObject:SetActive(true)
        else
            item.com.gameObject:SetActive(false)
        end
    end
    return item
end

--------------------------------------------------------------------
-- 窗口页面关闭
--------------------------------------------------------------------
function m.CloseUI()
    m:CloseSelf()
end

return m
