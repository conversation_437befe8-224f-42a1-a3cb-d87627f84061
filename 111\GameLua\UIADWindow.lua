--[[
********************************************************************
    created:	
    author :	
    purpose:    PC看广告界面
*********************************************************************
--]]

local countDownTime = 13
---@class UIADWindow:UIWndBase
local m = {}
--------------------------------------------------------------------
-- 初始化
--------------------------------------------------------------------
function m.OnCreate()
    m.<PERSON>()
    return true
end

--------------------------------------------------------------------
-- 窗口开启
--------------------------------------------------------------------
function m:OnOpen(adverID, callback, isAdCfg, openClose)
    local actorID = LoginModule:GetSelectActorData().ActorID
    LuaModule.RunLuaRequest(string.format('LuaRequestStartSeeAdvertise?AdverID=%s', actorID), nil)

    m.adverID = adverID
    m.callback = callback
    m.isAdCfg = isAdCfg
    m.objList.Btn_Close.enabled = openClose ~= false
    m.objList.Txt_Hint.gameObject:SetActive(openClose ~= false)

    --倒计时时间
    m.time = countDownTime
    m.objList.Txt_Time.text = m.time
    if m.timer then
        m.timer:Stop()
        m.timer = nil
    end
    m.timer = Timer.New(m.UpdateView, 1, m.time, nil, nil, m.CloseUI)
    m.timer:Start()
end

--------------------------------------------------------------------
-- 注册点击事件
--------------------------------------------------------------------
function m.RegisterClickEvent()
    m.objList.Btn_Close.onClick:AddListenerEx(function()
        m.CloseUI()
    end)
end

--------------------------------------------------------------------
-- 更新界面
--------------------------------------------------------------------
function m.UpdateView()
    m.time = m.time - 1
    m.objList.Txt_Time.text = m.time
end

--------------------------------------------------------------------
-- 关闭界面
--------------------------------------------------------------------
function m.CloseUI()
    m.timer:Stop()
    m.timer = nil
    m:CloseSelf()
    AdvertisementManager.GetAdAward(m.adverID, m.callback, m.isAdCfg)
end

return m
