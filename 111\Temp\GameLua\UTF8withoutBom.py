import os

for fileName in os.listdir("."):
    splitedName = os.path.splitext(fileName)
    if fileName.find(".") > 0 and splitedName[1] == '.lua':
        fullFileName = os.path.join(os.getcwd(), fileName)
        print(fullFileName)
        s = open(fullFileName, mode='r', encoding='utf-8-sig').read() # UTF-8 with BOM
        open(fullFileName, mode='w', encoding='utf-8').write(s) # UTF-8 without BOM
