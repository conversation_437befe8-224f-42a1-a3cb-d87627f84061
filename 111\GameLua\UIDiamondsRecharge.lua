local luaID = ('UIDiamondsRecharge')

local UIDiamondsRecharge = {}

-- 初始化
function UIDiamondsRecharge:OnCreate(objList)
	self.objList = objList
	self.item = self.objList.RechargeItem
	self.item.gameObject:SetActive(false)
	self.objList.Txt_Title.text = GetGameText(luaID, 6)
	self.itemsContent = self.objList.DiamondsContainer:GetRectTransform()
	self.btnList = {}
	self.buyItemList = {}
	self.buyConfigList = {}
	self.discontEmptyItemList = {}
	for k, v in ipairs(Schemes.RechargeCard.items) do
		if v.LenovoID == RECHARGE_TYPE.RECHARGE_CARD then	
			table.insert(self.buyConfigList, v)
		end
	end

	for k, v in ipairs(self.buyConfigList) do
		local item = {}
		local obj = GameObject.Instantiate(self.item, self.itemsContent)
		local objTrans = obj:GetRectTransform()
		Helper.FillLuaComps(objTrans, item)
		item.gameObject = obj
		item.objTrans = objTrans
		item.config = v
		table.insert(self.buyItemList, item)
		table.insert(self.btnList, item.Btn_BuyRecharge)
		AtlasManager:AsyncGetSprite(item.config.FirstPic1, item.Img_RechargeIcon)
		item.Btn_BuyRecharge.onClick:AddListenerEx(function () 
			self:OnClickBuy(item)
		end)
		
		if v.Diamond<=0 then
			item.Txt_RechargeGet.text = v.CardName
		else
			item.Txt_RechargeGet.text = string.format(GetGameText(luaID, 4), v.Diamond)
		end
		
		item.Txt_BuyRecharge.text = '￥' .. v.FirstRMB/100
		item.gameObject:SetActive(true)
		
		--local rechargetime = HeroDataManager:GetLogicData(LOGIC_DATA.DATA_FIRSTCHARGE_TIME)
		--if rechargetime > 0 then--已完成超值礼包
			--item.Txt_RechargeDoubleDesc.gameObject:SetActive(false)
			--item.Img_limt.gameObject:SetActive(false)
		--else
			--item.Txt_RechargeDoubleDesc.gameObject:SetActive(true)
			--item.Txt_RechargeDoubleDesc.text = GetGameText(luaID, 5)
			--item.Img_limt.gameObject:SetActive(true)
		--end
	end
	self:AddEmptyDiscountItem()
	return true 
end
function UIDiamondsRecharge:AddEmptyDiscountItem()
	local numcount = #self.buyItemList
	--print(numcount)
	local discontEmptyItemList = self.discontEmptyItemList
	for i=1,8-numcount do
		local discountItem = discontEmptyItemList[i]
		if not discountItem then
			local discountItem = {}
			local obj = GameObject.Instantiate(self.item, self.itemsContent)
			local objTrans = obj:GetRectTransform()
			Helper.FillLuaComps(objTrans, discountItem)
			discountItem.gameObject = obj
			discountItem.objTrans = objTrans
			table.insert(discontEmptyItemList, discountItem)
			local AllNode = obj:GetRectTransform()
			for i = 1, AllNode.transform.childCount do
				AllNode.transform:GetChild(i - 1).gameObject:SetActive(false)
			end
			discountItem.gameObject:SetActive(true)
			discountItem.Img_empty.gameObject:SetActive(true)
		end

	end
	self.discontEmptyItemList = discontEmptyItemList
end

function UIDiamondsRecharge:UpdateView()	
	--local rechargetime = HeroDataManager:GetLogicData(LOGIC_DATA.DATA_FIRSTCHARGE_TIME)
	--print('rechargetime:'..rechargetime)
	for k, v in ipairs(self.buyItemList) do
		local config = v.config
		if HeroDataManager:GetLogicBit(config.SaveParam1, config.SaveParam2) == 0 then--未购买
			v.Txt_RechargeDesc.text = config.FirstCharacter1
			v.Txt_RechargeMore.text = config.FirstCharacter2
			v.Txt_RechargeDoubleDesc.gameObject:SetActive(true)
			v.Txt_RechargeDoubleDesc.text = GetGameText(luaID, 5)
			v.Img_limt.gameObject:SetActive(true)
		else--已购买过
			v.Txt_RechargeDoubleDesc.gameObject:SetActive(false)
			v.Img_limt.gameObject:SetActive(false)
			v.Txt_RechargeDesc.text = config.Character1
			v.Txt_RechargeMore.text = config.Character2
			if config.Character2 == '0' then
				v.Img_RechargeMore.gameObject:SetActive(false)
			else
				v.Img_RechargeMore.gameObject:SetActive(true)
			end

			if config.Character1 == '0' then
				v.Img_RechargeDesc.gameObject:SetActive(false)
			else
				v.Img_RechargeDesc.gameObject:SetActive(true)
			end
		end
		if config.CardType == 6 then--红蓝水晶
			v.Txt_RechargeDoubleDesc.gameObject:SetActive(false)
			v.Img_limt.gameObject:SetActive(false)
		end
		--if rechargetime > 0 then--已完成超值礼包
			--v.Txt_RechargeDoubleDesc.gameObject:SetActive(false)
			--v.Img_limt.gameObject:SetActive(false)
		--else
			--v.Txt_RechargeDoubleDesc.gameObject:SetActive(true)
			--v.Txt_RechargeDoubleDesc.text = GetGameText(luaID, 5)
			--v.Img_limt.gameObject:SetActive(true)
		--end
				
	end
end

-- 点击领取奖励
function UIDiamondsRecharge:OnClickBuy(item)
	if not item then return end	
	HelperL.Recharge(item.config.ID)
end

 
-- 窗口关闭
function UIDiamondsRecharge:OnClose()
	
end

-- 每秒更新
function UIDiamondsRecharge:OnSecondUpdate()
	
end


-- 窗口销毁
function UIDiamondsRecharge:OnDestroy()
	-- 释放内存
	for i, btn in ipairs(self.btnList) do
		btn.onClick:RemoveAllListeners()
		btn.onClick:Invoke()
	end
end
return UIDiamondsRecharge