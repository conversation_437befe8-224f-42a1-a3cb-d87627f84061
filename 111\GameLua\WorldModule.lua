-- 地图模块
require "WorldMessage_pb"

local luaID = ('StoreModule')

WorldModule = {}

function WorldModule.Handle(action, data)
	--登陆
	if action == WorldMessage_pb.MSG_WORLD_LOADSCENE then
		local m = WorldMessage_pb.SC_World_LoadScene()
		m:ParseFromString(data)
		Premier.Instance:SetServerTime(m.ServerTime)
		WorldModule.SC_World_LoadScene(m)
	elseif action == WorldMessage_pb.MSG_WORLD_DESTORYSCENE then
		local m = WorldMessage_pb.SC_WORLD_DESTORYSCENE()
		m:ParseFromString(data)
	elseif action == WorldMessage_pb.MSG_WORLD_STARTECTYPE then
		local m = WorldMessage_pb.SC_World_StratEctype()
		m:ParseFromString(data)
	elseif action == WorldMessage_pb.MSG_WORLD_ECTYPEINFO then
		local m = WorldMessage_pb.SC_World_EctypeInfo()
		m:ParseFromString(data)
	elseif action == WorldMessage_pb.MSG_WORLD_SCENE_STATICS then
		local m = WorldMessage_pb.SC_World_Scene_Statics()
		m:ParseFromString(data)
	end
end

function WorldModule.SC_World_LoadScene(m)
end

function WorldModule.OnlevelWasLoaded()
end

Premier.Instance:RegisterLuaModule(MSG_MODULEID.MSG_MODULEID_WORLD, "WorldModule.Handle")
