-- buff组件
local luaID = ('BuffLC')

BuffLCCache = BuffLCCache or {}
local buffLCCache = BuffLCCache

local _BuffEffectAddPropTypeName =
{
	GetGameText(luaID, 1),
	GetGameText(luaID, 2),
	GetGameText(luaID, 3),
	GetGameText(luaID, 4),
	GetGameText(luaID, 5),
	GetGameText(luaID, 6),
	GetGameText(luaID, 7),
	GetGameText(luaID, 8),
	GetGameText(luaID, 9),
	GetGameText(luaID, 10),
	GetGameText(luaID, 11),
	GetGameText(luaID, 12),
	GetGameText(luaID, 13),
	GetGameText(luaID, 14),
	GetGameText(luaID, 15),
	GetGameText(luaID, 16),
	GetGameText(luaID, 17),
	GetGameText(luaID, 18),
	'',
	'',
	GetGameText(luaID, 19),
	GetGameText(luaID, 20),
}

--------------------------------------------------------------------
-- 全局：获取属性名称
--------------------------------------------------------------------
function _GGetEffectName(effectType)
	return GetGameText(luaID, effectType)
end

local function CalcEffectLevelValue(level, baseValue, lvValue1, lvValue2, lvValue3)
	return baseValue + level * lvValue1 + math.floor(level / 5) * lvValue2 + math.floor(level / 10) * lvValue3
end

-- 改变属性效果开始
local function EffectChangePropStart(buffLC, effectScheme, buffLv, isInit)
	if not isInit then
		-- 飘字
		local propType = _BuffEffectAddPropTypeName[effectScheme.Param1]
		if propType == nil then
			warn('EffectChangePropStart unknown propType '..effectScheme.Param1..'  '..effectScheme.ID)
			return 0
		end
		
		if propType == '' then
			return 0
		end
		
		local propValue = CalcEffectLevelValue(buffLv, effectScheme.Param3, effectScheme.Param4, effectScheme.Param5, effectScheme.Param6)
		if propValue == 0 then
			warn('EffectChangePropStart value=0 '..effectScheme.Param1..'  '..effectScheme.ID)
			return 0
		end
		
		if effectScheme.Param2 == 2 then
			propValue = math.floor(propValue / 100)
		else
			propValue = math.floor(propValue)
		end
		
		local color = nil
		local valueText = nil
		if propValue > 0 then
			color = '19FF19'
			valueText = '+'..propValue
		else
			color = 'E71F19'
			valueText = tostring(propValue)
		end
		if effectScheme.Param2 == 2 then
			valueText = valueText..'%'
		end
		HelperL.ShowMessage(TipType.MoveText, string.format('<color=#%s>%s%s</color>', color, propType, valueText), PosType.WorldPos, buffLC.master.modelTrans.localPosition, TipDirection.ToRight)
	end
end

ExStatus =
{
	BanMove = 0,
	BanSkill = 1,
	BanAllSkill = 2,
	BanUseGoods = 3,
	RandRun = 4,
	NoDamage = 5,
	Escape = 6,
}

-- 变形效果开始
local function EffectMorphStart(buffLC, effectScheme, buffLv, isInit)
	local entity = buffLC.master
	if entity.model == nil or entity.prefab == nil then
		warn('buff变形效果：找不到模型')
		return
	end

	local morphType = effectScheme.Param1
	if morphType == 1 then
		-- 换模型
	elseif morphType == 2 then
		-- 改变大小
		if effectScheme.Param2 == 0 then return end
		if not entity.creatureID then return end
		local scale = 1.0
		local ci = Schemes.Creature:Get(entity.creatureID)
		if ci then
			scale = ci.Scale
		end
		local rate = scale * effectScheme.Param2 / 10000.0
		local morphTime = effectScheme.Param3 / 1000.0
		if isInit then
			-- 创建时已有的buff则略过渐变动画
			morphTime = 0
		end
		entity.modelTrans:DOKill()
		entity.modelTrans:DOScale(rate, morphTime)
	elseif morphType == 3 then
		-- 变色
	else
		warn('buff变形效果：不支持的子类型')
	end
end

-- 变形效果结束
local function EffectMorphEnd(buffLC, effectScheme)
	local entity = buffLC.master
	if entity.model == nil then
		return
	end

	local morphType = effectScheme.Param1
	if morphType == 1 then
		-- 换模型，尚未支持
	elseif morphType == 2 then
		-- 改变大小
		if effectScheme.Param2 == 0 then return true end
		local morphTime = effectScheme.Param4 / 1000.0
		if isInit then
			-- 创建时已有的buff则略过渐变动画
			morphTime = 0
		end
		entity.modelTrans:DOKill()
		local scale = 1.0
		local ci = Schemes.Creature:Get(entity.creatureID)
		if ci then
			scale = ci.Scale
		end
		entity.modelTrans:DOScale(scale, morphTime)
	elseif morphType == 3 then
		-- 变色
	end
end

local _EffectStartFunc =
{
	[1] = EffectChangePropStart,
	[2] = nil,
	[3] = nil,
	[4] = nil,
	[5] = nil,
	[6] = nil,
	[7] = nil,
	[8] = EffectMorphStart,
	[9] = nil,
}
local _EffectEndFunc =
{
	[1] = nil,
	[2] = nil,
	[3] = nil,
	[4] = nil,
	[5] = nil,
	[6] = nil,
	[7] = nil,
	[8] = EffectMorphEnd,
	[9] = nil,
}

local function BuffLC_Build(self, buffData)
	if buffData == nil then
		warn('BuffLC:Build buffData == nil')
		return
	end

	for i,v in ipairs(buffData.BuffInfoList) do
		self:SetBuffData(v.BuffID, v.RemainingTime, v.BuffLv, true)
	end
	for i,v in ipairs(buffData.ExStatusList) do
		self:SetExStatus(v, 1)
	end
end

local function BuffItem_GetBuffLeftTime(self)
	if self.leftTime < 0 then
		return -1
	end
	local curTime = UnityEngine.Time.unscaledTime
	local passTime = curTime - self.startTime
	if passTime > self.leftTime then
		return 0
	end

	return self.leftTime - passTime
end

local function BuffLC_SetBuffData(self, buffID, leftTime, buffLv, isInit)
	if not buffLv or buffLv <= 0 then
		buffLv = 1
	end

	if self.buffList[buffID] ~= nil then
		if leftTime ~= 0 then
			local curTime = UnityEngine.Time.unscaledTime
			local curBuff = self.buffList[buffID]
			curBuff.leftTime = leftTime / 1000.0
			curBuff.level = buffLv
			curBuff.startTime = curTime
		else
			self:OnBuffEnd(buffID)
			self.buffList[buffID] = nil
			
			if #self.recentBuffEffect < 2 then
				self:FlushBuffEffect()
			end
		end
	else
		if leftTime ~= 0 then
			local curTime = UnityEngine.Time.unscaledTime
			local buffScheme = Schemes.Buff:Get(buffID)
			self.buffList[buffID] = { id = buffID, scheme = buffScheme, leftTime = leftTime / 1000.0, level = buffLv, startTime = curTime, GetBuffLeftTime = BuffItem_GetBuffLeftTime, }
			self:OnBuffStart(buffID, buffLv, isInit)
		else
			local buffScheme = Schemes.Buff:Get(buffID)
			if buffScheme then
				if not isInit and buffScheme.FlowText > 0 then
					-- 飘名字
					if self.master.grid and self.master.grid > 0 then
						EventManager:Fire(EventID.BuffGridFlowText, buffScheme.Name, self.master)
					else
						HelperL.ShowMessage(TipType.MoveText, buffScheme.Name, PosType.WorldPos, self.master.modelTrans.localPosition, TipDirection.ToRight)
					end
				end
			end
		
			return
		end
	end

	-- 通知buff更新
	EventManager:Fire(EventID.EntitySyncBuff, self, buffID)
end

-- 获取buff剩余时间
local function BuffLC_GetBuffLeftTime(self, buffID)
	local buffObj = self.buffList[buffID]
	if buffObj == nil then
		return 0
	end
	return buffObj:GetBuffLeftTime()
end

-- 获取buff等级
local function BuffLC_GetBuffLevel(self, buffID)
	local buffObj = self.buffList[buffID]
	if buffObj == nil then
		return 0
	end
	return buffObj.level
end

-- 获取是否有指定buff
local function BuffLC_HasBuff(self, buffID)
	local buffObj = self.buffList[buffID]
	if buffObj == nil then
		return false
	end
	return true
end

-- 通过组ID获取BuffID
local function BuffLC_GetBuffIDByGroupID(self, groupID)
	for id, buffData in pairs(self.buffList) do
		if buffData.scheme.Group == groupID then
			return id
		end
	end
	return 0
end

-- 获取指定范围内当前有的buff组ID
local function BuffLC_GetGroupIDByGroupRange(self, minGroup, maxGroup)
	for id, buffData in pairs(self.buffList) do
		if buffData.scheme.Group >= minGroup and buffData.scheme.Group <= maxGroup then
			return id
		end
	end
	return 0
end

local function DestoryBuffEffect(self, buffID)
	local buffData = self.buffList[buffID]
	if buffData ~= nil then
		if buffData.effectObj ~= nil then
			FxManager.RemoveFx(buffData.effectObj)
			buffData.effectObj = nil
			
			for i, v in ipairs(self.recentBuffEffect) do
				if v == buffID then
					table.remove(self.recentBuffEffect, i)
					break
				end
			end
		end
	end
end

local function AddBuffEffect(self, buffID, buffScheme)
	if not buffScheme or buffScheme.Caster == '0' then
		return
	end
	
	local effectObj = FxManager.CreateFx(buffScheme.Caster, 1, self.master.modelTrans.localPosition, 0, self.master.modelTrans, buffScheme.Bone)
	if effectObj then
		self.buffList[buffID].effectObj = effectObj
		
		table.insert(self.recentBuffEffect, buffID)
		if #self.recentBuffEffect > 2 then
			local removeEffectID = self.recentBuffEffect[1]
			DestoryBuffEffect(self, removeEffectID)
		end
	end
end

-- buff开始
local function BuffLC_OnBuffStart(self, buffID, buffLv, isInit)
	if self.master == nil or self.master.model == nil then
		warn('OnBuffStart entity = nil '..buffID)
		return false
	end

	local buffScheme = Schemes.Buff:Get(buffID)
	if not buffScheme then
		warn('OnBuffStart 找不到buff配置 '..buffID)
		return
	end

	if self.master.modelData and #self.master.modelData > 0 then
		AddBuffEffect(self, buffID, buffScheme)
	end

	if not isInit and buffScheme.FlowText > 0 then
		-- 飘名字
		if self.master.grid and self.master.grid > 0 then
			EventManager:Fire(EventID.BuffGridFlowText, buffScheme.Name, self.master)
		else
			HelperL.ShowMessage(TipType.MoveText, buffScheme.Name, PosType.WorldPos, self.master.modelTrans.localPosition, TipDirection.ToRight)
		end
	end

	for i = 1, 5 do
		local effectID = buffScheme['EffectID'..i]
		if effectID > 0 then
			local effectScheme = Schemes.BuffEffect:Get(effectID)
			if effectScheme then
				local func = _EffectStartFunc[effectScheme.GroupID]
				if func ~= nil then
					func(self, effectScheme, buffLv, isInit)
				end
			else
				warn('OnBuffStart 找不到效果配置 '..effectID)
			end
		end
	end
end

-- buff结束
local function BuffLC_OnBuffEnd(self, buffID)
	if self.master == nil or self.master.model == nil then
		warn('OnBuffEnd entity = nil '..buffID)
		return false
	end

	local buffScheme = Schemes.Buff:Get(buffID)
	if not buffScheme then
		warn('OnBuffEnd 找不到buff配置 '..buffID)
		return
	end

	DestoryBuffEffect(self, buffID)

	for i = 1, 5 do
		local effectID = buffScheme['EffectID'..i]
		if effectID > 0 then
			local effectScheme = Schemes.BuffEffect:Get(effectID)
			if effectScheme then
				local func = _EffectEndFunc[effectScheme.GroupID]
				if func ~= nil then
					func(self, effectScheme)
				end
			else
				warn('OnBuffEnd 找不到效果配置 '..effectID)
			end
		end
	end
end

local function BuffLC_OnDestroy(self)
	if self.master and self.master.model then
		for id, buffData in pairs(self.buffList) do
			self:OnBuffEnd(id)
			self.buffList[id] = nil
		end
		self.recentBuffEffect = {}
		self.exStatusList = {}
		self.changeColorEffectCount = 0
	else
		warn('BuffLC_OnDestroy not self.master')
	end

	table.insert(buffLCCache, self)
end

local function BuffLC_FlushBuffEffect(self)
	if self.master and self.master.model then
		for id, buffData in pairs(self.buffList) do
			if buffData.effectObj == nil then
				AddBuffEffect(self, buffData.id, buffData.scheme)
				if #self.recentBuffEffect >= 2 then
					return
				end
			end
		end
	end
end

local function BuffLC_SetExStatus(self, statusType, hasStatus)
	self.exStatusList[statusType] = hasStatus
	return false
end

local function BuffLC_CheckExStatus(self, statusType)
	local hasStatus = self.exStatusList[statusType]
	if not hasStatus then
		return false
	end
	return (hasStatus > 0)
end

function BuffLC_New(entity)
	local cacheSize = #buffLCCache
	if cacheSize > 0 then
		local result = buffLCCache[cacheSize]
		table.remove(buffLCCache)
		result.master = entity
		return result
	end
	local o = {
		master = entity,
		buffList = {},
		recentBuffEffect = {},
		exStatusList = {},
		changeColorEffectCount = 0,
		Build = BuffLC_Build,
		SetBuffData = BuffLC_SetBuffData,
		GetBuffLeftTime = BuffLC_GetBuffLeftTime,
		GetBuffLevel = BuffLC_GetBuffLevel,
		HasBuff = BuffLC_HasBuff,
		GetBuffIDByGroupID = BuffLC_GetBuffIDByGroupID,
		GetGroupIDByGroupRange = BuffLC_GetGroupIDByGroupRange,
		OnBuffStart = BuffLC_OnBuffStart,
		OnBuffEnd = BuffLC_OnBuffEnd,
		OnDestroy = BuffLC_OnDestroy,
		FlushBuffEffect = BuffLC_FlushBuffEffect,
		SetExStatus = BuffLC_SetExStatus,
		CheckExStatus = BuffLC_CheckExStatus,
	}

	return o
end