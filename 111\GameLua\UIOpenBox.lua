--[[
********************************************************************
    created:	2024/05/25
    author :	李锦剑
    purpose:    开宝箱界面
*********************************************************************
--]]

local luaID = 'UIOpenBox'
local boxIconList = {
    [BOX_EQUIP_ID[1]] = {
        bg = 'xsd_bxbjt_1',
        icon = 'xsd_bxtb_1',
    },
    [BOX_EQUIP_ID[2]] = {
        bg = 'xsd_bxbjt_2',
        icon = 'xsd_bxtb_2',
    },
}
--临时全局变量表
local __temp

---宝箱购买界面
---@class UIOpenBox:UIWndBase
local m = {}

--------------------------------------------------------------------
-- 初始化
--------------------------------------------------------------------
function m.OnCreate()
    m.Reset()
    m.RegisterClickEvent()
    m.BoxEquipLevel = PropertyCompute.GetBoxEquipLevel(BOX_EQUIP_ID[1])
    return true
end

--------------------------------------------------------------------
-- 窗口开启
--------------------------------------------------------------------
function m:OnOpen(equipID, buyType)
    m.equipID = equipID
    --购买类型, 1:普通购买, 2:免费
    m.buyType = buyType
    -- SoundManager:PlaySound(7110)
    m.Reset()
    m.UpdateView()
end

--------------------------------------------------------------------
-- 注册点击事件
--------------------------------------------------------------------
function m.RegisterClickEvent()
    m:AddClick(m.objList.Btn_Close, m.CloseUI)
end

--------------------------------------------------------------------
--重置界面
--------------------------------------------------------------------
function m.Reset()
    m.objList.Img_Bg.gameObject:SetActive(false)
    m.objList.Img_Title.gameObject:SetActive(false)
    m.objList.Obj_Root.gameObject:SetActive(false)
    m.objList.Btn_Close.gameObject:SetActive(false)
end

--------------------------------------------------------------------
--更新界面
--------------------------------------------------------------------
function m.UpdateView()
    local boxEquipData = PropertyCompute.GetBoxEquipData(m.equipID)
    if not boxEquipData then return end
    local prizeID = boxEquipData.boxEquipSmelt.prizeID
    --获取随机奖励
    m.prizeGoodsList = Schemes.PrizeTable:GetRandomPrize(prizeID)
    if not m.prizeGoodsList or #m.prizeGoodsList == 0 then
        warn('随机奖励获取失败 prizeID=', prizeID)
        return
    end
    local goodsTypeNum = 0
    local prizeGoodsList = Schemes.PrizeTable:GetGoodsList(prizeID)
    if prizeGoodsList then
        local medicament
        for i, v in ipairs(prizeGoodsList) do
            medicament = Schemes.Medicament:Get(v.id)
            --判断是随机物品
            if medicament and medicament.UseMenu == 4 then
                goodsTypeNum = goodsTypeNum + v.num
            else
                goodsTypeNum = goodsTypeNum + 1
            end
        end
    end

    m.objList.Txt_Title.text = boxEquipData.name
    m.objList.Txt_Num.text = goodsTypeNum
    local boxIcon = boxIconList[boxEquipData.equipID]
    AtlasManager:AsyncGetSprite(boxIcon.icon, m.objList.Img_Goods, false, function(sprite, image, param)
        m.objList.Obj_Root.gameObject:SetActive(true)
    end)
end

--------------------------------------------------------------------
-- 购买宝箱
--------------------------------------------------------------------
function m.BuyBox(equipID)
    local boxEquipData = PropertyCompute.GetBoxEquipData(equipID)
    if not boxEquipData then return end
    local expendID  = boxEquipData.boxEquipSmelt.expendID
    local expendNum = boxEquipData.boxEquipSmelt.expendNum
    if m.buyType == 1 then
        if HelperL.IsLackGoods(expendID, expendNum, false) then
            return
        end
    elseif m.buyType == 2 then
        local cfgAD = Schemes.CommonText:Get(boxEquipData.adID)
        local num = HeroDataManager:GetLogicByte(cfgAD.Param4, cfgAD.Param5)
        if num >= cfgAD.DayTime then
            HelperL.ShowMessage(TipType.FlowText, RESULT_CODE_BASE.RESULT_COMMON_LACK_DAYTIMES[2])
            return
        end
    else
        warn('类型不存在 buyType=', m.buyType)
        return
    end

    local adID = BOX_EQUIP_AD_ID[boxEquipData.equipID]
    local cfgAD = Schemes.CommonText:Get(adID)
    local num = HeroDataManager:GetLogicByte(cfgAD.Param4, cfgAD.Param5)
    if num >= cfgAD.DayTime then
        HelperL.ShowMessage(TipType.FlowText, RESULT_CODE_BASE.RESULT_COMMON_LACK_DAYTIMES[2])
        return
    end

    AdvertisementManager.GetAdAward(adID, function(result, content, code)
        local goodInfo = ''
        for i, v in ipairs(m.prizeGoodsList) do
            if goodInfo == '' then
                goodInfo = v.ID .. ';' .. v.Num
            else
                goodInfo = goodInfo .. '|' .. v.ID .. ';' .. v.Num
            end
        end
        local costInfo = expendID .. ';' .. expendNum
        local form = {}
        form["equipID"] = equipID
        form["loopTimes"] = 1
        LuaModuleNew.SendRequest(LuaRequestID.NewCardAddExp, form, function(resultCode2, content2)
            if resultCode2 == RESULT_CODE_BASE.RESULT_COMMON_SUCCEED[1] then
                if m.buyType == 1 then     --1:普通购买
                    HelperL.RequestDirectGiveGoodsy(goodInfo, costInfo, m.RequestCallback)
                elseif m.buyType == 2 then --2:免费
                    HelperL.RequestDirectGiveGoodsy(goodInfo, '0', m.RequestCallback)
                end
            else
                ResultCode.ShowResultCodeCallback(resultCode2, content2)
            end
        end)
    end)
end

--------------------------------------------------------------------
-- 发奖励回调
--------------------------------------------------------------------
function m.RequestCallback(resultCode, content)
    -- if resultCode == RESULT_CODE_BASE.RESULT_COMMON_SUCCEED[1] then
    --     PropertyCompute.GetBoxEquipLevel(m.equipID)
    -- end
    -- local level = PropertyCompute.GetBoxEquipLevel(BOX_EQUIP_ID[1])
    -- if m.BoxEquipLevel ~= level then
    --     m.BoxEquipLevel = level
    --     UIManager:OpenWnd(WndID.BoxLevel, level)
    -- end
end

--------------------------------------------------------------------
-- 关闭界面
--------------------------------------------------------------------
function m.CloseUI()
    m.BuyBox(m.equipID)
    m.Reset()
    m:CloseSelf()
end

return m
