--[[
********************************************************************
    created:    2024/01/30
    author :    李锦剑
    purpose:    实名认证、防沉谜界面
*********************************************************************
--]]

-- local luaID = ('UICertification')
local m = {}

--------------------------------------------------------------------
-- 初始化
--------------------------------------------------------------------
function m.OnCreate()
    m.objList.Obj_UI.gameObject:SetActive(false)
    m.<PERSON>()
    return true
end

--------------------------------------------------------------------
-- 打开时
--------------------------------------------------------------------
function m:OnOpen(callback)
    m.objList.Obj_UI.gameObject:SetActive(false)
    print('-----获取当天星期几-------', m.GetWeekday())
    print('-----获取当天时间戳-------', m.GetTimestamp(20, 0, 0))
    m.callback = callback
    m.GetUserIdNo()
    
    -- 每次打开时隐藏Progress1节点
    if m.objList.Progress1 and m.objList.Progress1.gameObject then
        m.objList.Progress1.gameObject:SetActive(false)
    end
end

--------------------------------------------------------------------
--注册点击事件
--------------------------------------------------------------------
function m.RegisterClickEvent()
    m.objList.Btn_Submit.onClick:AddListenerEx(m.OnSubmit)    

    m.objList.Btn_Close.onClick:AddListenerEx(function()
		-- 只隐藏Obj_UI节点，不关闭整个预制体
        m.objList.Obj_UI.gameObject:SetActive(false)
        
        -- 激活进度条节点
        m.objList.Progress1.gameObject:SetActive(true)
        
        -- 初始化进度条
        m.objList.Img_Fill.fillAmount = 0
        
        -- 计时器变量
        m.loadingTime = 0
        m.totalLoadingTime = 4 -- 总时长4秒--实名认证返回登陆界面---下一个项目改成转圈圈4秒
        
        -- 创建计时器，每帧更新进度条
        m.loadingTimer = Timer.New(function()
            m.loadingTime = m.loadingTime + Time.deltaTime
            
            -- 更新进度条
            local progress = math.min(m.loadingTime / m.totalLoadingTime, 1)
            m.objList.Img_Fill.fillAmount = progress
            
            -- 进度完成后关闭界面
            if progress >= 1 then
                -- 取消计时器
                m.loadingTimer:Stop()
                m.loadingTimer = nil
                
                -- 关闭预制体，返回登录界面
                m:CloseSelf()
            end
        end, 0, -1)
        
        -- 启动计时器
        m.loadingTimer:Start()
	end)
end

--------------------------------------------------------------------
--获取当天星期几
--------------------------------------------------------------------
function m.GetWeekday()
    local dateTable = os.date("*t")
    local weekday = dateTable.wday
    return (weekday == 1) and 7 or weekday - 1
end

--------------------------------------------------------------------
---获取当天指定时间的时间戳
---@param hour integer 时
---@param min integer 分
---@param sec integer 秒
---@return integer
--------------------------------------------------------------------
function m.GetTimestamp(hour, min, sec)
    -- 获取当前日期和时间
    local now = os.date("*t")
    -- 设置时间为20点
    now.hour = hour or 0
    now.min = min or 0
    now.sec = sec or 0
    -- 获取当天20点的时间戳
    local timestampAt20 = os.time(now)
    print(timestampAt20)
    return timestampAt20
end

--------------------------------------------------------------------
-- 获取用户实名认证信息
--------------------------------------------------------------------
function m.GetUserIdNo()
    local form = {}
    form["ApiVersion"] = 'v1'
    form["UserID"] = LoginModule:GetUserID()
    HttpReques.SendRequest(ERequestID.GetUserIdNo, form, function(data, param)
        if data.Value and data.Value.Verified then
            m.UserCanLogin(data.Value)
            return
        --else
            --HelperL.ShowMessage(TipType.FlowText, data.Message)
        end
        m.objList.Obj_UI.gameObject:SetActive(true)
    end)
end

--------------------------------------------------------------------
--提交身份证信息
--------------------------------------------------------------------
function m.OnSubmit()
    local Name = m.objList.Txt_Name2.text
    local IdCard = m.objList.Txt_IdentityCard2.text
    if Name == '' or IdCard == '' then
        HelperL.ShowMessage(TipType.FlowText, '名字或身份证不能为空！')
        return
    end

    local form = {}
    form["ApiVersion"] = 'v1'
    form["UserID"] = LoginModule:GetUserID()
    form["Name"] = Name
    form["IdCard"] = IdCard
    HttpReques.SendRequest(ERequestID.IdNameVerify, form, function(data, param)
        if data.Value and data.Value.Verified then
            --认证成功
            m.UserCanLogin(data.Value)
            HelperL.ShowMessage(TipType.FlowText, '实名认证成功！')
        else
            HelperL.ShowMessage(TipType.FlowText, '信息错误，实名认证失败！')
        end
    end)
end

--------------------------------------------------------------------
--用户可以登录
--------------------------------------------------------------------
function m.UserCanLogin(info)
    local UserID = tostring(LoginModule:GetUserID())
	-- print('-----防沉迷--测试--UserID---------', UserID, SWITCH.SCREEN_TIME_TEST, SCREEN_TIME_TEST_LIST[UserID])
	--防沉迷--测试
	if SWITCH and SWITCH.SCREEN_TIME_TEST then
		if SCREEN_TIME_TEST_LIST and SCREEN_TIME_TEST_LIST[UserID] then
			local time = tonumber(SCREEN_TIME_TEST_LIST[UserID]) or 5
			Timer.New(function()
				HelperL.ScreenTime(function(isCheck)
					--GameLuaAPI.QuitGame()
				end,nil,false)
			end, time, 1):Start()
		end
	end

    if EntityModule.InitVerifyInfo(info) or (SWITCH and SWITCH.CANCEL_RESTRICTION_OF_LOG_IN_MINORS) then
        m.OnCloseUI()
        return
    end
    
    local form = {}
    form["ApiVersion"] = 'v1'
    HttpReques.SendRequest(ERequestID.UserCanLogin, form, function(data, param)
        if data and data.Value and data.Value.CanLogin then
            EntityModule.InitLoginInfo(data.Value)
            m.OnCloseUI()
            return
        end
        m:CloseSelf()
        HelperL.ScreenTime(function()
            --GameLuaAPI.SDKLoginOut()

            ----测试
            --m.OnCloseUI()
        end,nil,false)
    end)
end

--------------------------------------------------------------------
--关闭UI
--------------------------------------------------------------------
function m.OnCloseUI()
    m:CloseSelf()
    if m.callback then
        m.callback()
    end
end

return m
