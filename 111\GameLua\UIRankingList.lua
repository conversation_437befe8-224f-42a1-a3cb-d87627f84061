--[[
********************************************************************
    created:	2023/12/05
    author :	李锦剑
    purpose:    排行榜界面
*********************************************************************
--]]

local luaID = ('UIRankingList')

local m = {}

--------------------------------------------------------------------
-- 订阅事件列表
--------------------------------------------------------------------
function m.<PERSON><PERSON><PERSON>List()
	return {
		[EventID.OnRankingListUpdate] = m.UpdateView,
	}
end

--------------------------------------------------------------------
-- 创建时
--------------------------------------------------------------------
function m.OnCreate()
	--切换按钮集合
	m.ToggleItemList = {}
	---@type Item_Ranking[] 排行榜列表
	m.RankingItemList = {}

	m.Init()
	m.RegisterClickEvent()
	return true
end

--------------------------------------------------------------------
-- 初始化
--------------------------------------------------------------------
function m.Init()
	for i, v in ipairs(RankingModule.RankingType) do
		if not m.ToggleItemList[i] then
			m.ToggleItemList[i] = m.CreateToggleItem(i)
		end
		m.ToggleItemList[i].UpdateData(v)
	end
end

--------------------------------------------------------------------
-- 注册点击事件
--------------------------------------------------------------------
function m.RegisterClickEvent()
	m.objList.Btn_Close.onClick:AddListenerEx(m.OnClickClose)
end

--------------------------------------------------------------------
-- 窗口开启
--------------------------------------------------------------------
function m:OnOpen()
	m.objList.Txt_Title2.text = GetGameText(luaID, 35)
	m.SelectToggleButton(m.selectToggleIndex or 1)
end

--------------------------------------------------------------------
--- 更新界面
---@param rankData SC_Rank_GetRankList
--------------------------------------------------------------------
function m.UpdateView(rankData)
	-- m.objList.Txt_Title2.text = GetGameText(luaID, 35)
	local rankList = {}
	local rankingType = RankingModule.RankingType[m.selectToggleIndex]
	if rankData ~= nil and rankData.RankType == m.selectRankType then
		-- if rankData.SelfRank > 0 then
		-- 	local str = string.format(GetGameText(luaID, 36),
		-- 		rankingType.title,
		-- 		rankData.SelfRankValue
		-- 	)
		-- 	m.objList.Txt_Title2.text = str .. string.format(GetGameText(luaID, 34), rankData.SelfRank)
		-- else
		RankingModule.RequestSelfRank(rankingType.type, function(data)
			local str = string.format(GetGameText(luaID, 36),
				rankingType.title,
				data.SelfRankValue
			)
			local bool = true
			if m.selectRankType == RANK_TYPE.RANK_TYPE_CATDAYKILLENIMY then
				bool = data.SelfRankValue > 10000
			end
			if data.SelfRank > 0 and bool then
				str = str .. string.format(GetGameText(luaID, 34), data.SelfRank)
			else
				str = str .. GetGameText(luaID, 35)
			end
			m.objList.Txt_Title2.text = str
		end)
		-- end
		rankList = rankData.RankList
	end
	local num = math.max(#m.RankingItemList, #rankList)
	for i = 1, num, 1 do
		if not m.RankingItemList[i] then
			m.RankingItemList[i] = m.CreateRankingItem(i)
		end
		m.RankingItemList[i].UpdateData(rankList[i])
	end
end

--------------------------------------------------------------------
-- 切换按钮点击事件
--------------------------------------------------------------------
function m.SelectToggleButton(index)
	-- if index == m.selectToggleIndex then return end
	local item
	if m.selectToggleIndex then
		item = m.ToggleItemList[m.selectToggleIndex]
		item.com.Img_Bg1.gameObject:SetActive(true)
		item.com.Img_Bg2.gameObject:SetActive(false)
	end
	m.selectToggleIndex = index
	item = m.ToggleItemList[m.selectToggleIndex]
	--选择排行榜类型
	m.selectRankType = item.data.type
	item.com.Img_Bg1.gameObject:SetActive(false)
	item.com.Img_Bg2.gameObject:SetActive(true)
	m.objList.Txt_Value.text = item.data.title

	-- local rankData = RankingModule.GetRankData(item.data.type)
	-- if rankData then
	-- 	m.UpdateView(rankData)
	-- else
	RankingModule.RequestRankList(item.data.type)
	-- end
end

--------------------------------------------------------------------
-- 创建切换按钮
--------------------------------------------------------------------
function m.CreateToggleItem(index)
	local item = {}
	item.index = index
	item.com = m:CreateSubItem(m.objList.Grid_Toggle, m.objList.Item_Toggle)
	item.com.Img_Bg1.gameObject:SetActive(true)
	item.com.Img_Bg2.gameObject:SetActive(false)
	item.com.gameObject:GetComponent("Button").onClick:AddListenerEx(function()
		m.SelectToggleButton(item.index)
	end)
	---更新数据
	---@param data {type:integer, name :string}
	item.UpdateData = function(data)
		item.data = data
		if data then
			item.com.Txt_Name1.text = data.name
			item.com.Txt_Name2.text = data.name
			item.com.gameObject:SetActive(true)
		else
			item.com.gameObject:SetActive(false)
		end
	end
	return item
end

--------------------------------------------------------------------
---创建排行框
--------------------------------------------------------------------
function m.CreateRankingItem(index)
	---@class Item_Ranking 排行框
	local item = {}
	item.index = index
	item.com = m:CreateSubItem(m.objList.Grid_Ranking, m.objList.Item_Ranking)
	---更新数据
	---@param data RankData
	item.UpdateData = function(data)
		if data then
			if data.RankNo <= 3 then
				AtlasManager:AsyncGetSprite('phb_bjk_0' .. data.RankNo, item.com.Img_Bg)
				AtlasManager:AsyncGetSprite('phb_pm_0' .. data.RankNo, item.com.Img_Ranking)
				item.com.Txt_Ranking.text = ''
			else
				AtlasManager:AsyncGetSprite('phb_bjk_04', item.com.Img_Bg)
				AtlasManager:AsyncGetSprite('phb_pm_04', item.com.Img_Ranking)
				item.com.Txt_Ranking.text = data.RankNo
			end
			item.com.Img_Ranking:SetNativeSize()
			item.com.Txt_Name.text = data.ActorName
			item.com.Txt_Value.text = data.Value1
			item.com.gameObject:SetActive(true)
		else
			item.com.gameObject:SetActive(false)
		end
	end
	return item
end

--------------------------------------------------------------------
-- 点击返回按钮
--------------------------------------------------------------------
function m.OnClickClose()
	m:CloseSelf()
end

return m
