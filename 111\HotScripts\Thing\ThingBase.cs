// ReSharper disable InconsistentNaming
// ReSharper disable CollectionNeverQueried.Global
// ReSharper disable UnusedAutoPropertyAccessor.Global

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;

using Apq.ChangeBubbling;
using Apq.Extension;
using Apq.Unity3D.Extension;
using Apq.Unity3D.UnityHelpers;
using Apq.Utils;

using Cysharp.Threading.Tasks;

using DataStructure;

using Props;

using RxEventsM2V;

using ThingCdExecutors;

using UniRx;

using UnityEngine;

using View;

using X.PB;

namespace Thing
{
    /// <summary>
    ///     �������
    /// </summary>
    public abstract partial class ThingBase : BubblingDic<PropType, CommonProp>, IDisposable
    {
        protected ThingBase(object key = default, IBubbleNode parent = null)
        {
            Parent = parent;
            Key = key;

            Area2D = new BubblingList<IArea2D>(nameof(Area2D), this);
            ThingLvl = new BubblingList<int>(nameof(ThingLvl), this);
            Hp = new BubblingList<double>(nameof(Hp), this);
            Armor = new BubblingList<double>(nameof(Armor), this);
            MoveDirection_Straight = new BubblingList<Vector3>(nameof(MoveDirection_Straight), this);
            // MapCols = new(nameof(MapCols), this);
            RotationTowards = new BubblingList<Vector3>(nameof(RotationTowards), this);
            AttachedProps = new BubblingList<CommonProp>(nameof(AttachedProps), this);
            HoistedProps = new BubblingList<HoistedProp>(nameof(HoistedProps), this);
            TotalProps = new BubblingDic<PropType, AggProp>(nameof(TotalProps), this);
            CarriedBuffs_HitForInjurer = new BubblingList<BuffThing>(nameof(CarriedBuffs_HitForInjurer), this);
            CarriedBuffs_KilledForAttacker = new BubblingList<BuffThing>(nameof(CarriedBuffs_KilledForAttacker), this);
            Guns = new BubblingList<GunThing>(nameof(Guns), this);
            Buffs = new BubblingList<BuffThing>(nameof(Buffs), this);
            LockedByShooters = new BubblingList<ThingCdExecutor>(nameof(LockedByShooters), this);
            LockedByBullets = new BubblingList<BulletThing>(nameof(LockedByBullets), this);

            ThingLvl.Value = 1;

            Hp.Changed += Hp_Changed;
            Armor.Changed += Armor_Changed;
            ThingLvl.Changed += ThingLvl_Changed;
        }

        /// <summary>
        ///     �������
        /// </summary>
        public virtual ThingType ThingType { get; set; }

        /// <summary>
        ///     ���������Ӫ
        /// </summary>
        public virtual Camp Camp { get; set; }

        /// <summary>
        ///     �����ΨһID ( Clone/CopyTo ������������ )
        /// </summary>
        public Guid Guid { get; set; } = Guid.NewGuid();

        /// <summary>
        ///     ������
        /// </summary>
        public ThingBase Owner { get; set; }

        /// <summary>
        ///     ����ȼ�
        /// </summary>
        public BubblingList<int> ThingLvl { get; set; }

        /// <summary>
        ///     ��ǰѪ��
        /// </summary>
        public BubblingList<double> Hp { get; }

        /// <summary>
        ///     ��ǰ����
        /// </summary>
        public BubblingList<double> Armor { get; }

        /// <summary>
        ///     ���ռ�õı�����������(��������ϵ)
        /// </summary>
        /// <remarks>��Clone������</remarks>
        public List<BagPosition> PosInBag { get; } = new();

        /// <summary>
        ///     ֱ���˶�����(��һ��)
        /// </summary>
        public BubblingList<Vector3> MoveDirection_Straight { get; }

        /// <summary>
        ///     ��ת����
        /// </summary>
        public BubblingList<Vector3> RotationTowards { get; }

        /// <summary>
        ///     �Ѹ������
        /// </summary>
        public int ReviveCount { get; set; }

        /// <summary>
        ///     ��ȡ�뾶
        /// </summary>
        public float TotalProp_Radius => (float)GetTotalDouble(PropType.Radius).FirstOrDefault();

        /// <summary>
        ///     ��ȡ���Ѫ��
        /// </summary>
        public double TotalProp_MaxHp => GetTotalDouble(PropType.MaxHp).FirstOrDefault();

        /// <summary>
        ///     ��ȡ���
        /// </summary>
        public float TotalProp_GunRange => (float)GetTotalDouble(PropType.GunRange).FirstOrDefault();

        /// <summary>
        ///     �Ƿ񱻱���
        /// </summary>
        public bool IsFreeze { get; set; }

        protected virtual void ThingLvl_Changed(ChangeEventArgs e)
        {
        }

        protected virtual void Hp_Changed(ChangeEventArgs e)
        {
            // ���Ѫ���仯
            MessageBroker.Default.Publish(new ThingHp
            {
                Thing = this,
                OriginalValue = e.OriginalValue is double originalValue ? originalValue : 0,
                NewValue = e.NewValue is double newValue ? newValue : 0
            });

            // Hp��ɺ�С��ֵʱ��Ϊ����
            if (Hp.Value <= float.Epsilon)
            {
                MessageBroker.Default.Publish(new ThingDead { Thing = this });
            }
        }

        protected virtual void Armor_Changed(ChangeEventArgs e)
        {
            // ������ױ仯
            MessageBroker.Default.Publish(new ThingArmor
            {
                Thing = this,
                OriginalValue = e.OriginalValue is double originalValue ? originalValue : 0,
                NewValue = e.NewValue is double newValue ? newValue : 0
            });
        }

        /// <summary>
        /// ������Ϣ:�������
        /// </summary>
        public virtual async UniTaskVoid PublishMsg_ThingDead(float delay)
        {
            await UniTask.Delay(TimeSpan.FromSeconds(delay));
            
            MessageBroker.Default.Publish(new ThingDead { Thing = this });
        }

        /// <summary>
        ///     �Ƿ�ӵ��ָ����װ�������Ǿ�����
        /// </summary>
        public virtual bool HasEquip(int GoodsId, int starExp = 0)
        {
            return false;
        }

        /// <summary>
        ///     �ܷ����Ը����ڸ����
        /// </summary>
        public abstract bool CanAttach(CommonProp prop);

        /// <summary>
        ///     �ܷ�����Ӧ���ڸ����
        /// </summary>
        /// <remarks>����ֻ�ж����Ƿ�Ӧ���ڱ�����(û��������)��Buff������</remarks>
        public virtual bool CanApply(CommonProp prop)
        {
            // ������Buff�ϵ�����,Ӧ�����������
            if (prop.AttachedThing is BuffThing buff &&
                prop.ApplyType == ApplyType.BuffBearer && buff.Bearer == this)
            {
                return true;
            }

            // Ӧ���ڸ�����
            if (prop.ApplyType == ApplyType.Attached)
            {
                if (Util.IsEquals(prop.AttachedThing, this))
                {
                    return true;
                }
            }

            return false;
        }

        /// <summary>
        ///     ���Ӹ��ŵ�����(�Ѵ����򲻼�)(��¡)
        /// </summary>
        public virtual CommonProp AddAttachedProp(CommonProp prop)
        {
            CommonProp c = AttachedProps.FirstOrDefault(p => p.Guid == prop.Guid);
            if (c != null)
            {
                return c;
            }

            c = (CommonProp)prop.Clone();
            c.Parent = AttachedProps;
            c.AttachedThing = this;

            AttachedProps.Add(c);
            return c;
        }

        /// <summary>
        ///     ��������õķ����ҳ�һ������
        /// </summary>
        public virtual DistanceThing FindEnemy()
        {
            return new DistanceThing { Thing1 = this, Distance = float.PositiveInfinity };
        }

        /// <summary>
        ///     �����˺�
        /// </summary>
        /// <returns>�Ƿ�����</returns>
        public virtual bool TakeHit(ThingBase attacker, double damage, bool isCritical = false)
        {
            double maxHp = GetTotalDouble(PropType.MaxHp).FirstOrDefault();

            // �ȼ�����
            double newArmor = Armor.Value - damage;
            newArmor = Math.Clamp(newArmor, 0, maxHp);
            // �����ѵֿ۵��˺�
            double armorReduce = Armor.Value - newArmor;
            Armor.Value = newArmor;

            // �ټ�Ѫ��
            double hpReduce = damage - armorReduce;
            if (hpReduce > 0)
            {
                double oldHp = Hp.Value;
                double newHp = Hp.Value - hpReduce;
                newHp = Math.Clamp(newHp, 0, maxHp);
                hpReduce = oldHp - newHp;
                Hp.Value = newHp;
            }

            MessageBroker.Default.Publish(new HitThingCells
            {
                Thing = this, IsCritical = isCritical, Inc_Armor = armorReduce, Inc_Hp = hpReduce
                // Cells = cells
            });

            return Hp.Value <= float.Epsilon;
        }

        /// <summary>
        ///     �����ӵ�(����)
        /// </summary>
        /// <param name="shooter">ִ����</param>
        /// <param name="attackBaseDirFollowThing">������׼��������ĸ����(ûֵ��ȡ�ƶ�����)</param>
        /// <param name="trackPos">����λ��</param>
        /// <param name="angle">�ӵ������빥����׼����ĽǶ�</param>
        /// <param name="penetrateTimes">ʣ�ഩ͸����</param>
        /// <param name="bounceTimes">ʣ�෴������</param>
        /// <param name="separateTimes">ʣ����Ѵ���</param>
        /// <param name="monsters">���ѳ���ʱ���ϸ��ӵ���������Щ����(���ӵ����ٹ�����Щ����)</param>
        public virtual BulletThing CreateBullet(ThingCdExecutor shooter, ThingBase attackBaseDirFollowThing,
            Vector3? trackPos, float angle, int penetrateTimes, int bounceTimes, int separateTimes,
            ICollection<MonsterThing> monsters = null
        )
        {
            BulletThing bullet = new BulletThing("�ӵ�") { CdExecutor = shooter, Camp = shooter.Thing.Camp }.InitFromCsv(
                (int)shooter.Thing.GetTotalLong(PropType.BulletId).FirstOrDefault(),
                ThingLvl.Value);
            bullet.Angle = angle;
            bullet.PenetrateTimes.Value = penetrateTimes;
            bullet.BounceTimes.Value = bounceTimes;
            bullet.SeparateTimes.Value = separateTimes;
            bullet.SeparateFrom.AddRange(monsters);

            // �����ӵ����ŵ�����(��ս������)
            bullet.ReloadAttachedProps();

            // �ӵ���ȡ����
            bullet.PickProps();

            // �ӵ���������
            _ = bullet.HoistedProps.Select(h => h.ReHoist()).ToList();

            // �ӵ�����������(��Я����Buff����ʵ�ʲ���Я��Buff)
            bullet.ReCalcTotalProps();
            return bullet;
        }

        // /// <summary>
        // /// �ڵ�ͼ��ռ����Щ��(��������ϵ)
        // /// </summary>
        // public BubblingList<int> MapCols { get; }

        #region ��Ϊ����Ŀ��ʱ

        /// <summary>
        ///     ��Ϊ����Щִ���ߵĹ���Ŀ��
        /// </summary>
        public BubblingList<ThingCdExecutor> LockedByShooters { get; }

        /// <summary>
        ///     ��Ϊ����Щ�ӵ��Ĺ���Ŀ��
        /// </summary>
        public BubblingList<BulletThing> LockedByBullets { get; }

        #endregion

        #region IDisposable

        protected bool disposedValue;

        /// <param name="disposing">ָ���ͷ�����{true:�йܶ���,false:δ�йܶ���}</param>
        protected virtual void Dispose(bool disposing)
        {
            if (disposedValue)
            {
                return;
            }

            if (disposing)
            {
                Hp.Changed -= Hp_Changed;
                Armor.Changed -= Armor_Changed;
                ThingLvl.Changed -= ThingLvl_Changed;
                StopCdExecutor();
            }

            // �ͷ�δ�йܵ���Դ(δ�йܵĶ���)����д�ս���
            // �������ֶ�����Ϊ null
            disposedValue = true;
        }

        // // TODO: ����"Dispose(bool disposing)"ӵ�������ͷ�δ�й���Դ�Ĵ���ʱ������ս���
        // ~PersistentClient()
        // {
        //     // ��Ҫ���Ĵ˴��롣�뽫�����������"Dispose(bool disposing)"������
        //     Dispose(false);
        // }

        public void Dispose()
        {
            // ��Ҫ���Ĵ˴��롣�뽫�����������"Dispose(bool disposing)"������
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        #endregion

        #region ����

        /// <summary>
        ///     ��������Ľ������
        /// </summary>
        public ThingBehaviour ThingBehaviour { get; set; }

        /// <summary>
        ///     ��ʼ������ǰ
        /// </summary>
        public Action<ThingBehaviour> Provider_BeforeInitView { get; set; }

        /// <summary>
        ///     ��ʼ�������
        /// </summary>
        public Action<ThingBehaviour> Provider_AfterInitView { get; set; }

        /// <summary>
        ///     ���λ��(��������)
        /// </summary>
        public Vector3 Position { get; set; }

        /// <summary>
        ///     �����ʼλ��
        /// </summary>
        public Vector3 PositionInit { get; set; }

        /// <summary>
        ///     ����ڽ����ϸ��ǵ�����
        /// </summary>
        public BubblingList<IArea2D> Area2D { get; }

        /// <summary>
        ///     �Ƿ�����ĳ����
        /// </summary>
        public virtual bool CheckCollision(Vector2 P1)
        {
            return Vector2.Distance(P1, Position) < TotalProp_Radius;
        }

        /// <summary>
        ///     �Ƿ�����Բ������
        /// </summary>
        public virtual bool CheckCollision(Vector2 P1, float radius)
        {
            float dis = Position.CalcDistance2D_SolidCircleToSolidCircle(
                TotalProp_Radius, P1, radius);
            return dis <= 0;
        }

        /// <summary>
        ///     �������λ��(�����油)
        /// </summary>
        public virtual Vector3 CalcPositionInit()
        {
            // return PositionInit.FirstOrDefault();
            return PositionInit;
        }

        #endregion

        #region Executor

        /// <summary>
        ///     �����ȡ������:��Cdʱ����ѭ��
        /// </summary>
        public CancellationTokenSource CTS_CdExecutor { get; set; } = new();

        /// <summary>
        ///     ����ִ����:��Cdʱ����ѭ��
        /// </summary>
        /// <param name="ms">�ӳ������ĺ�����</param>
        public async UniTaskVoid StartCdExecutor(int ms = 0)
        {
            CTS_CdExecutor.Cancel();
            await UniTask.NextFrame();
            CTS_CdExecutor = new CancellationTokenSource();
            await UniTask.Delay(ms);

            Task_CdExecutor(CTS_CdExecutor.Token).Forget();
        }

        /// <summary>
        ///     ִֹͣ����:��Cdʱ����ѭ��
        /// </summary>
        public virtual void StopCdExecutor()
        {
            CTS_CdExecutor.Cancel();
        }

        /// <summary>
        ///     �Ƿ���ͣ ��Cdʱ����ѭ��
        /// </summary>
        public bool SuspendCdExecutor { get; set; }

        /// <summary>
        ///     ����ʵ��:��Cdʱ����ѭ��(�������Ѫ���ػ���)
        /// </summary>
        /// <param name="token">CTS_CdExecutor.Token</param>
        protected virtual async UniTaskVoid Task_CdExecutor(CancellationToken token)
        {
            try
            {
                for (;; await UniTask.NextFrame())
                {
                    if (token.IsCancellationRequested)
                    {
                        break;
                    }

                    if (Time.deltaTime <= 0)
                    {
                        continue;
                    }

                    // ����ͣ�ˣ����ܸɻ�
                    if (SuspendCdExecutor)
                    {
                        continue;
                    }

                    // �������ˣ����ܸɻ�
                    if (IsFreeze)
                    {
                        continue;
                    }

                    double cd = GetTotalDouble(PropType.Cd).FirstOrDefault();
// ;
                    
                    if (this is GunThing { Actor: not null } gun)
                    {
                        // ��ɫ��Ѫ����������Ѫ���ݶ��е�����λ��
                        double hpPct = gun.Actor.Hp.Value / gun.Actor.TotalProp_MaxHp;
                        int idx = gun.Actor.GetTotalDouble(PropType.HpPctGradientList).Where(x => x >= hpPct)
                            .Select((_, i) => i).LastOrDefault();

                        cd *= 1 + gun.GetTotalDouble(PropType.CdPctGradientList).IndexOfOrFirstOrDefault(idx);

                        // �ƶ�ʱ�����ݶ��е�����λ��
                        PlayerMove playerMove = gun.Actor.ThingBehaviour.GetComponent<PlayerMove>();
                        idx = gun.GetTotalDouble(PropType.MoveDurationGradientList)
                            .Where(x => x <= playerMove.MoveDuration)
                            .Select((_, i) => i).LastOrDefault();
                        cd *= 1 + gun.GetTotalDouble(PropType.CdPctGradientListByMoveDuration).IndexOfOrFirstOrDefault(idx);
                    }

                    double minCd = GetTotalDouble(PropType.MinCd).FirstOrDefault();
// ;
                    
                    if (cd < minCd)
                    {
                        cd = minCd;
                    }

                    // CDʱ��̫С�;ܾ�ִ��
                    if (cd <= float.Epsilon)
                    {
// ;
                        continue;
                    }

// ;

                    // ����Cd��ʱ
                    StartCdTiming(cd, token).Forget();

                    // ��ʼ�ɻ�
                    DoCdExecutor(token);
                    if (token.IsCancellationRequested)
                    {
                        break;
                    }

// ;
                    await UniTask.Delay(TimeSpan.FromSeconds(cd), cancellationToken: token);
// ;
                }
            }
            catch (OperationCanceledException)
            {
                throw;
            }
            catch (MissingReferenceException)
            {
                // ignore
            }
            catch (Exception ex)
            {
                Debug.LogException(ex);
            }
        }

        ///// <summary>
        ///// Cd��ʱ�Ƿ�����ͣ
        ///// </summary>
        //protected bool CdPause { get; set; }

        /// <summary>
        ///     ����Cd��ʱ
        /// </summary>
        protected async UniTaskVoid StartCdTiming(double cdMax, CancellationToken token)
        {
            MessageBroker.Default.Publish(new ThingCd { Thing = this, CdMax = cdMax, Cd = 0 });
            await UniTask.SwitchToMainThread();

            //try
            //{
            //	double cd = 0;
            //	for (; cd <= cdMax; await UniTask.NextFrame())
            //	{
            //		if (token.IsCancellationRequested) break;
            //		if (CdPause) continue;

            //		MessageBroker.Default.Publish(new ThingCd
            //		{
            //			Thing = this,
            //			CdMax = cdMax,
            //			Cd = cd,
            //		});

            //		cd += Time.deltaTime;
            //	}
            //}
            //catch (OperationCanceledException)
            //{
            //	throw;
            //}
            //catch (MissingReferenceException)
            //{
            //	// ignore
            //}
            //catch (Exception ex)
            //{
            //	Debug.LogException(ex);
            //}
        }

        /// <summary>
        ///     ��Cdʱ��ѭ����ִ����ÿ�ָɵĻ�
        /// </summary>
        protected virtual void DoCdExecutor(CancellationToken token)
        {
            // �������
            if (Hp.Value <= float.Epsilon)
            {
                // ��������
                CTS_CdExecutor.Cancel();
                return;
            }

            // ����ִ���߲�����
            ThingCdExecutor shooter = CreateCdExecutor();
            shooter?.StartExecutor(token).Forget();
        }

        /// <summary>
        ///     创建按Cd时长循环执行的执行器(一轮)(功能完成之后就销毁)
        /// </summary>
        protected ThingCdExecutor CreateCdExecutor()
        {
            try
            {
                long shootMethod = GetTotalLong(PropType.ShootMethod).FirstOrDefault();
                
                            // // ===== V42.0：启用新的简化追击导弹功能 =====
            if (shootMethod == 7)
            {
                // Debug.Log($"=== V42.0 使用新的简化追击导弹功能 (ShootMethod={shootMethod}) ===");
            }
            // // ===== V42.0 追击导弹功能 =====
            
            // ===== 圆弧追击子弹功能 (ShootMethod=9) =====
            if (shootMethod == 9)
            {
                Debug.Log($"=== 使用圆弧追击子弹功能 (ShootMethod={shootMethod}) ===");
            }
            // ===== 圆弧追击子弹功能 =====
                
                string clsName = $"ThingCdExecutors.ThingCdExecutor_{shootMethod}";
                
                // // V42.0 调试日志 - 只为追击导弹显示
                if (shootMethod == 7)
                {
                    // Debug.Log($"=== V42.0 创建CD执行器 - ShootMethod={shootMethod}, 类名={clsName} ===");
                }
                
                // 圆弧追击子弹调试日志
                if (shootMethod == 9)
                {
                    Debug.Log($"=== 创建圆弧追击CD执行器 - ShootMethod={shootMethod}, 类名={clsName} ===");
                }
                
                Type csCls = SingletonMgr.Instance.TypeCache.GetType(clsName) ?? typeof(ThingCdExecutor);
                
                if (shootMethod == 7)
                {
                    // Debug.Log($"=== V42.0 找到类型 - {csCls?.Name} ===");
                }
                
                if (shootMethod == 9)
                {
                    Debug.Log($"=== 圆弧追击找到类型 - {csCls?.Name} ===");
                }
                
                if (Activator.CreateInstance(csCls) is not ThingCdExecutor cdExecutor)
                {
                    throw new Exception(
                        $"未实现此执行者: ShootMethod = {shootMethod}");
                }

                cdExecutor.Thing = this;
                
                if (shootMethod == 7)
                {
                    // Debug.Log($"=== V42.0 CD执行器创建成功 - {cdExecutor.GetType().Name} ===");
                }
                
                if (shootMethod == 9)
                {
                    Debug.Log($"=== 圆弧追击CD执行器创建成功 - {cdExecutor.GetType().Name} ===");
                }

                return cdExecutor;
            }
            catch (OperationCanceledException)
            {
                throw;
            }
            catch (MissingReferenceException)
            {
                // ignore
            }
            catch (Exception ex)
            {
                Debug.LogException(ex);
            }

            return null;
        }

        #endregion

        #region ����

        /// <summary>
        ///     �Ƿ񱻻���
        /// </summary>
        public bool IsHitBack { get; set; }

        /// <summary>
        ///     �����ٶ�
        /// </summary>
        public float HitBackSpeed { get; set; }

        #endregion
    }
}
