
ComResInfo = {}
--
function ComResInfo.New()
    local m = {}

    m.property = {
        PHYSICAL_POWER = 2, --体力
        DIAMOND = 3,        --钻石
        GOLD = 4,           --金币
    }

    
    m.BindView = function(view)
        m.gameObject = view
        m.objList = {}
        Helper.FillLuaComps(m.gameObject.transform, m.objList)
        m.objList.Btn_Gold.onClick:AddListenerEx(m.OnClickGold)
        m.objList.Btn_ZS.onClick:AddListenerEx(m.OnClickZS)
        m.objList.Btn_TL.onClick:AddListenerEx(m.OnClickTL)
        m.UpdateView()
        m.Subscribe()
    end

    m.OnClickGold = function()
        m.OnClickProperty(m.property.GOLD)
    end

    m.OnClickZS = function()
        m.OnClickProperty(m.property.DIAMOND)    
    end

    m.OnClickTL = function()
        m.OnClickProperty(m.property.PHYSICAL_POWER)    
    end

    m.UpdateView = function()
        m.objList.Txt_Gold.text = HelperL.GetChangeNum(SkepModule:GetGoodsCount(m.property.GOLD))
        m.objList.Txt_ZS.text = HelperL.GetChangeNum(SkepModule:GetGoodsCount(m.property.DIAMOND))
        m.objList.Txt_TL.text = HelperL.GetChangeNum(SkepModule:GetGoodsCount(m.property.PHYSICAL_POWER))
    end

    m.OnClickProperty = function(id)
        SoundManager:PlaySound(1005)
        local level = EntityModule.hero:GetProperty(CREATURE_FIELD.CREATURE_FIELD_LEVEL)
        if id == m.property.PHYSICAL_POWER then
            UIManager:OpenWnd(WndID.PurchasePhysicalPower)
        elseif id == m.property.DIAMOND then
            UIManager:OpenWnd(WndID.Recharge, 2)
        elseif id == m.property.GOLD then
            --UIManager:OpenWnd(WndID.CatPatrol)
            
            UIManager:OpenWnd(WndID.YQSMoney)
        end
    end

    --注册事件
    m.Subscribe = function()
        EventManager:Subscribe(EventID.LogicDataChange, m.UpdateView)
        EventManager:Subscribe(EventID.EntitySyncBuff, m.UpdateView)
        EventManager:Subscribe(EventID.OnGoodsPropChange, m.UpdateView)
        EventManager:Subscribe(EventID.OnHeroPropChange, m.UpdateView)
    end

    --取消注册事件
    m.UnSubscribe = function()
        EventManager:UnSubscribe(EventID.LogicDataChange, m.UpdateView)
        EventManager:UnSubscribe(EventID.EntitySyncBuff, m.UpdateView)
        EventManager:UnSubscribe(EventID.OnGoodsPropChange, m.UpdateView)
        EventManager:UnSubscribe(EventID.OnHeroPropChange, m.UpdateView)
    end
    return m
end
