﻿namespace ViewModel
{
	/// <summary>
	/// 回合奖励项
	/// </summary>
	public class MissionWaveInfoItem
	{
        /// <summary>
        /// 回合数
        /// </summary>
        public int RoundNo { get; set; }
        /// <summary>
        /// 金币数
        /// </summary>
        public int Coins { get; set; }
        /// <summary>
        /// 回合失败时的奖励
        /// </summary>
        public int BattleBrushEnemyId { get; set; }
        /// <summary>
        /// 回合的地图Id
        /// </summary>
        public int MapId { get; set; }
	}
}
