
DivineWeaponDataLC = {}
function DivineWeaponDataLC.New()
    local o = {}

    function o:Set(uidList)
        self.uidList = uidList
    end

    function o:Get()
        local genre = 1
        local hero = EntityModule.hero
        if hero then
            genre = hero:GetProperty(PLAYER_FIELD.PLAYER_FIELD_GENRE)
        end
        return self.uidList[genre]
    end

    function o:GetByGenre(genre)
        return self.uidList[genre]
    end

   return o
end
