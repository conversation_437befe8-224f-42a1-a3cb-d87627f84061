--[[
********************************************************************
    created:    2024/04/11
    author :    李锦剑
    purpose:    角色信息界面
*********************************************************************
--]]

local luaID = ('UIRoleInfo')

---角色信息界面
---@class UIRoleInfo:UIWndBase
local m = {}
--角色框
---@type Item_Role[]
m.Item_Role_List = {}
local switchDataList = {
    --音乐
    { Name = 'Music_Volume',       Text = GetGameText(luaID, 5), Icon = 'sz_tb_01' },
    --音效
    { Name = 'SoundEffect_Volume', Text = GetGameText(luaID, 6), Icon = 'sz_tb_02' },
}
-------------------------------------------
-- 订阅事件列表
-------------------------------------------
function m.GetOpenEventList()
    return {
        [EventID.LogicDataChange] = m.UpdateView,
        [EventID.OnHeroPropChange] = m.UpdateView,
        [EventID.OnSkepGoodsChange] = m.UpdateView,
        [EventID.OnRankingListUpdate] = m.UpdateView,
        [EventID.ChangeNameEvent] = m.UpdateViewName,
    }
end

-------------------------------------------
--创建时事件
-------------------------------------------
function m.OnCreate()
    m.RegisterClickEvent()
    m.EquipWeaponIDList = {}
    for i, v in ipairs(Schemes.EquipWeapon.items) do
        if v.GroupID < 100 then
            table.insert(m.EquipWeaponIDList, v.ID)
        end
    end
    ---@type Item_Switch[]
    m.Item_Switch_List = {}

    ---@type {Name:string, Text:string, Icon:string}[]


    for i, v in ipairs(switchDataList) do
        if not m.Item_Switch_List[i] then
            m.Item_Switch_List[i] = m.Creation_Item_Switch(m.objList.Grid_Switch, i)
        end
        m.Item_Switch_List[i].UpdateData(v)
    end
    m.ShowBulletInfo(1)
    return true
end

-------------------------------------------
--打开时
-------------------------------------------
function m.OnOpen()
    m.UpdateView()
    m.UpdateRole()
    --m.UpdateUIModel()
end

-------------------------------------------
--注册点击事件
-------------------------------------------
function m.RegisterClickEvent()
    m.objList.Btn_Close.onClick:AddListenerEx(m.OnCloseSelf)
    m.objList.Btn_Click1.onClick:AddListenerEx(m.OpenRole)
    m.objList.Btn_Click2.onClick:AddListenerEx(m.OpenSetting)
    m.objList.Btn_Exit.onClick:AddListenerEx(m.ChangeServer)
    m.objList.Btn_Rename.onClick:AddListenerEx(m.ChangeName)
    
end

function m.ChangeName()
    UIManager:OpenWnd(WndID.ChangeName)
end

function UpdateViewName()
    local hero = EntityModule.hero
    m.objList.Txt_Name.text = hero.name
end
-------------------------------------------
--更新界面
-------------------------------------------
function m.UpdateView()
    local hero = EntityModule.hero
    m.objList.Txt_Name.text = hero.name
    m.objList.Txt_UID.text = LoginModule:GetUserID()

    local lv = hero:GetProperty(CREATURE_FIELD.CREATURE_FIELD_LEVEL)
    local exp = hero:GetProperty(PLAYER_FIELD.PLAYER_FIELD_CUREXP)
    local maxExp = hero:GetProperty(PLAYER_FIELD.PLAYER_FIELD_MAXEXP)

    local weaponsKnapsack = GamePlayerData.ActorEquip:GetWeaponsKnapsack(1) or {}
    local curEquipID = weaponsKnapsack[1] or 0
    --AtlasManager:AsyncGetSprite(HelperL.GetGunIcon(curEquipID), m.objList.Img_RoleIcon, true)

    local content = string.format("等级：%s\n经验：%s/%s", lv, HelperL.GetChangeNum(exp), HelperL.GetChangeNum(maxExp))

    content = content .. string.format("\n金币：%s", HelperL.GetChangeNum(SkepModule:GetGoodsCount(4)))


    content = content .. string.format("\n钻石：%s", HelperL.GetChangeNum(SkepModule:GetGoodsCount(3)))


    content = content .. string.format("\n体力：%s", HelperL.GetChangeNum(SkepModule:GetGoodsCount(2)))

    m.objList.Txt_Content.text = content

    local equipment = Schemes.Equipment:Get(curEquipID)
    local gun = Schemes.Gun:Get(equipment.ConsignmentStyle)
    AtlasManager:AsyncGetSprite(HelperL.GetGunIcon(curEquipID), m.objList.Img_Head, true)
    --local levelText = EntityModule.hero:GetProperty(CREATURE_FIELD.CREATURE_FIELD_LEVEL)
    --m.objList.Txt_Level.text = string.format('<color=#fecd57>%s级</color>', levelText)
    m.objList.Txt_PassLevel.text = gun.Remark
end

--------------------------------------------------------------------
--更新角色模型
--------------------------------------------------------------------
function m.UpdateUIModel()
    local weaponsKnapsack = GamePlayerData.ActorEquip:GetWeaponsKnapsack(1) or {}
    local equipID = weaponsKnapsack[1] or 0
    local cfg = Schemes.Equipment:Get(equipID)
    -- local level = GamePlayerData.ActorEquipNew:GetEquipUpgradeLevel(cfg.SmeltID)
    -- m.objList.Txt_Level.text = GetGameText(luaID, 6) .. level .. CommonTextID.LEVEL
    -- m.objList.Txt_EquipName.text = cfg.GoodsName
    --AtlasManager:AsyncGetSprite(HelperL.GetGunIcon(equipID), m.objList.Img_EquipIcon, true)

    --暂时屏蔽

    local gun = Schemes.Gun:Get(cfg.ConsignmentStyle)
    local bullet_List = Schemes.Bullet:GetByBulletId(gun.BulletId)
    local trans = m.objList.Obj_Model.transform
    for c = trans.childCount - 1, 0, -1 do
        if trans:GetChild(c) then
            GameObject.Destroy(trans:GetChild(c).gameObject)
        end
    end
    local parameter
    ResMgr.LoadGameObjectAsync("model/uiSpine/" .. bullet_List[1].Model, function(obj, parameter)
        local model = GameObject.Instantiate(obj, m.objList.Obj_Model.transform)
    end, parameter)
end

--------------------------------------------------------------------
--更新角色
--------------------------------------------------------------------
function m.UpdateRole()
    -- if not m.objList.Obj_Role.gameObject.activeSelf then
    --     return
    --
    local dataList = m.EquipWeaponIDList
    for i, v in ipairs(dataList) do
        if not m.Item_Role_List[i] then
            m.Item_Role_List[i] = m.Creation_Item_Role(i)
        end
        m.Item_Role_List[i].UpdateData(v)
    end
end

m.ItemRole = nil
m.curEquipID = nil
--------------------------------------------------------------------
--创建属性框
--------------------------------------------------------------------
function m.Creation_Item_Role(index)
    ---@class Item_Role
    local item = {}
    item.index = index
    item.com = m:CreateSubItem(m.objList.Grid_Tab, m.objList.Item_Tab)
    ---更新数据
    ---@param equipID integer
    item.UpdateData = function(equipID)
        item.equipID = equipID

        local weaponsKnapsack = GamePlayerData.ActorEquip:GetWeaponsKnapsack(1) or {}
        local curEquipID = weaponsKnapsack[1] or 0
        local cfg1 = Schemes.Equipment:Get(curEquipID)
        local level1 = GamePlayerData.ActorEquipNew:GetEquipUpgradeLevel(cfg1.SmeltID)
        m.objList.Txt_Level.text = string.format('<color=#fecd57>%s级</color>', level1)

        if equipID then
            local cfg = Schemes.Equipment:Get(equipID)
            
            AtlasManager:AsyncGetSprite(HelperL.GetGunIcon(equipID), item.com.Img_icon1, true)
            AtlasManager:AsyncGetSprite(HelperL.GetGunIcon(equipID), item.com.Img_icon2, true)
            local level = GamePlayerData.ActorEquipNew:GetEquipUpgradeLevel(cfg.SmeltID)
            item.com.Txt_Name1.text = cfg.GoodsName
            item.com.Txt_Name2.text = cfg.GoodsName
            item.com.Txt_Lv1.text = level .. "级"
            item.com.gameObject:SetActive(true)
            local weaponsKnapsack = GamePlayerData.ActorEquip:GetWeaponsKnapsack(1) or {}
            local curEquipID = weaponsKnapsack[1] or 0
            m.ChangeUIModel(equipID, item.com.Obj_Model.transform)

            -- if curEquipID == equipID then
            m.ItemRole = item.com
            m.curEquipID = equipID
            --  m.ItemRole.Img_Select.gameObject:SetActive(true)
            if GamePlayerData.ActorEquip:IsWear(equipID, 1) then
                item.com.Txt_Battle.text = "已出战"
                item.com.Txt_Battle1.text = "已出战"
                item.com.Txt_Battle1.gameObject:SetActive(true)
                item.com.Img_Battle.gameObject:SetActive(true)
            else
                local level = GamePlayerData.ActorEquipNew:GetEquipUpgradeLevel(cfg.SmeltID)
                --item.com.Btn_Battle.gameObject:SetActive(false)
                item.com.Txt_Battle.text = "出战"
                item.com.Txt_Battle1.text = "出战"
                item.com.Txt_Battle1.gameObject:SetActive(false)
                if level > 0 then
                    item.com.Img_Battle.gameObject:SetActive(false)
                else
                    item.com.Img_Battle.gameObject:SetActive(true)
                end
            end
            --end
        else
            item.com.gameObject:SetActive(false)
        end
    end

    item.UpdateDataInfo = function(equipID)

        item.equipID = equipID
        if equipID then
            local cfg = Schemes.Equipment:Get(equipID)
            local level = GamePlayerData.ActorEquipNew:GetEquipUpgradeLevel(cfg.SmeltID)
            item.com.Txt_Lv1.text = level .. "级"
            if GamePlayerData.ActorEquip:IsWear(equipID, 1) then
                item.com.Txt_Battle.text = "已出战"
                item.com.Txt_Battle1.text = "已出战"
                item.com.Txt_Battle1.gameObject:SetActive(true)
                item.com.Img_Battle.gameObject:SetActive(true)
            else
                local level = GamePlayerData.ActorEquipNew:GetEquipUpgradeLevel(cfg.SmeltID)
                --item.com.Btn_Battle.gameObject:SetActive(false)
                item.com.Txt_Battle.text = "出战"
                item.com.Txt_Battle1.text = "出战"
                item.com.Txt_Battle1.gameObject:SetActive(false)
                if level > 0 then
                    item.com.Img_Battle.gameObject:SetActive(false)
                else
                    item.com.Img_Battle.gameObject:SetActive(true)
                end
            end
        end
    end
    return item
end

--------------------------------------------------------------------
--更新角色模型
--------------------------------------------------------------------
function m.ChangeUIModel(equipID, modeltransform)
    local cfg = Schemes.Equipment:Get(equipID)
    local level = GamePlayerData.ActorEquipNew:GetEquipUpgradeLevel(cfg.SmeltID)
    --m.objList.Txt_Level.text = GetGameText(luaID, 6) .. level .. CommonTextID.LEVEL
    --m.objList.Txt_EquipName.text = cfg.GoodsName
    --暂时屏蔽

    local gun = Schemes.Gun:Get(cfg.ConsignmentStyle)
    local bullet_List = Schemes.Bullet:GetByBulletId(gun.BulletId)
    local trans = modeltransform
    for c = trans.childCount - 1, 0, -1 do
        if trans:GetChild(c) then
            GameObject.Destroy(trans:GetChild(c).gameObject)
        end
    end
    local parameter
    ResMgr.LoadGameObjectAsync("model/uiSpine/" .. bullet_List[1].Model, function(obj, parameter)
        local model = GameObject.Instantiate(obj, modeltransform)
    end, parameter)
end

--------------------------------------------------------------------
--显示Tab信息
---@param index integer
--------------------------------------------------------------------
function m.ShowBulletInfo(index)
    local item
    if index == 1 then
        m.objList.Img_Bg.gameObject:SetActive(false)
        m.objList.Img_Select.gameObject:SetActive(true)
        m.objList.Img_Bg2.gameObject:SetActive(true)
        m.objList.Img_Select2.gameObject:SetActive(false)
    elseif index == 2 then
        m.objList.Img_Bg.gameObject:SetActive(true)
        m.objList.Img_Select.gameObject:SetActive(false)
        m.objList.Img_Bg2.gameObject:SetActive(false)
        m.objList.Img_Select2.gameObject:SetActive(true)
    end
end

--------------------------------------------------------------------
---创建设置框
---@param parent integer
---@param index integer
---@return Item_Switch
--------------------------------------------------------------------
function m.Creation_Item_Switch(parent, index)
    ---@class Item_Switch
    local item = {}
    item.index = index
    item.com = m:CreateSubItem(parent, m.objList.Item_Switch)
    -- item.com.Btn_Set.onClick:AddListener(function()
    --     if PlayerPrefs.GetFloat(switchDataList[item.index].Name, 1.0) == 1.0 then
    --         m.SettingSwitch(0, switchDataList[item.index].Name)
    --     else
    --         m.SettingSwitch(1.0, switchDataList[item.index].Name)
    --     end
    --     if PlayerPrefs.GetFloat(switchDataList[item.index].Name, 1.0) == 1.0 then
    --         item.com.Img_Set.gameObject.transform.localPosition = Vector3.New(-30,0,0)
    --     else
    --         item.com.Img_Set.gameObject.transform.localPosition = Vector3.New(30,0,0)
    --     end
    -- end)
    item.com.Sld_Slider.onValueChanged:AddListener(function(value)
        m.SettingSwitch(value, switchDataList[item.index].Name)
    end)
    ---更新商品数据
    ---@param data {Name:string, Text:string, Icon:string}
    item.UpdateData = function(data)
        item.data = data
        if data then
            --item.com.Sld_Set.value = PlayerPrefs.GetFloat(data.Name, 1.0)
            -- if PlayerPrefs.GetFloat(data.Name, 1.0) == 1.0 then
            --     item.com.Img_Set.gameObject.transform.localPosition = Vector3.New(-30,0,0)
            -- else
            --     item.com.Img_Set.gameObject.transform.localPosition = Vector3.New(30,0,0)
            -- end
            item.com.Sld_Slider.value = PlayerPrefs.GetFloat(data.Name, 1.0)
            item.com.Txt_Switch.text = data.Text
            --AtlasManager:AsyncGetSprite(data.Icon, item.com.Img_Icon)
            item.com.gameObject:SetActive(true)
        else
            item.com.gameObject:SetActive(false)
        end
    end
    return item
end

--------------------------------------------------------------------
---设置开关
---@param value number
---@param name string
--------------------------------------------------------------------
function m.SettingSwitch(value, name)
    if name == 'Music_Volume' then
        SoundManager:SetVolume_Music(value)
    elseif name == 'SoundEffect_Volume' then
        SoundManager:SetVolume_SoundEffect(value)
    end
end

function m.OpenRole()
    m.objList.Img_Role1.gameObject:SetActive(true)
    m.objList.Img_Setting.gameObject:SetActive(false)
    m.ShowBulletInfo(1)
end

function m.OpenSetting()
    m.objList.Img_Role1.gameObject:SetActive(false)
    m.objList.Img_Setting.gameObject:SetActive(true)
    m.ShowBulletInfo(2)
end

function m.ChangeServer()
    GameLuaAPI.QuitGame()
end

-------------------------------------------
--关闭时
-------------------------------------------
function m.OnCloseSelf()
    LuaToCshapeManager.Instance:PauseOrResumeBattle(1)
    m:CloseSelf()
end

return m
