﻿using UnityEngine;

namespace View
{
	public class HealthBar : MonoBehaviour
	{
		public SpriteRenderer health;
		public GameObject bar;
        public Transform barOutline;
        private Transform armorBar;
        private Transform armorBarOutline;
        private float armorActiveTime = 0;
        string tweenID = "";

		public string TweenID => tweenID;

		[HideInInspector] public float ScaleRatio;

		private void Awake()
		{
            //GetComponent<SpriteRenderer>().enabled = false;
			tweenID = "HB" + gameObject.GetInstanceID().ToString();
            barOutline = transform.Find("BarOutline");
            armorBar = transform.Find("ArmorBar");
            armorBarOutline = transform.Find("ArmorBarOutline");
            //armorBar.gameObject.SetActive(false);
            //armorBarOutline.gameObject.SetActive(false);
            //barOutline.gameObject.SetActive(false);
        }

        private void Update()
        {
            if(bar != null && bar.activeSelf == false)
            {
                //Debug.LogError("=================================");
                bar.SetActive(true);
                bar.transform.localScale = new Vector2(1, 1);
            }

            if(armorActiveTime > 0)
            {
                armorActiveTime -= Time.deltaTime;
                if (armorActiveTime <= 0) 
                { 
                    //armorBar.gameObject.SetActive(false);
                    //armorBarOutline.gameObject.SetActive(false);
                }
            }
        }


        public void SetDisplayHealth(float percentage)
		{
			if (percentage < 0)
			{
				percentage = 0;
			}
			if (percentage > 1)
			{
				percentage = 1;
			}
			percentage -= 0.01f;
			bar.transform.localScale = new Vector2(percentage, 1);
			//bar.transform.position = new Vector2(transform.position.x + health.bounds.size.x, transform.position.y);
			//bar->setPosition(health->getPosition().x + health->getBoundingBox().size.width, health->getPosition().y);

		}

        public void SetDisplayArmorBar(float percentage)
        {
            if (armorBar == null) Debug.LogError(gameObject.name + "没护甲组件");
            armorBar.gameObject.SetActive(true);
            //armorBarOutline.gameObject.SetActive(true);
            armorActiveTime = 3;
            if (percentage < 0)
            {
                percentage = 0;
            }
            if (percentage > 1)
            {
                percentage = 1;
            }
            percentage -= 0.01f;
            armorBar.localScale = new Vector2(percentage, 1);
        }
    }

}
