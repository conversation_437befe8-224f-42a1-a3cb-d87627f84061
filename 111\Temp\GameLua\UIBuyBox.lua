--[[
********************************************************************
    created:	2024/05/25
    author :	李锦剑
    purpose:    宝箱购买界面
*********************************************************************
--]]

local luaID = 'UIBuyBox'

local boxIconList = {
    [BOX_EQUIP_ID[1]] = {
        bg = 'gmbx_bxbj_1',
        icon = 'xsd_bxtb_1'
    },
    [BOX_EQUIP_ID[2]] = {
        bg = 'gmbx_bxbj_2',
        icon = 'xsd_bxtb_2'
    },
}

---宝箱购买界面
---@class UIBuyBox:UIWndBase
local m = {}
--------------------------------------------------------------------
-- 订阅事件列表
--------------------------------------------------------------------
function m.GetOpenEventList()
    return {
        [EventID.StoreList] = m.UpdateView,
        [EventID.StoreUpdateFree] = m.UpdateView,
        [EventID.LogicDataChange] = m.UpdateView,
        [EventID.EntitySyncBuff] = m.UpdateView,
        [EventID.OnGoodsPropChange] = m.UpdateView,
        [EventID.OnHeroPropChange] = m.UpdateView,
    }
end

--------------------------------------------------------------------
-- 初始化
--------------------------------------------------------------------
function m.OnCreate()
    ---@type Item_Goods3[]
    m.Item_Goods_List = {}
    ---@type SlotItem[]
    m.SlotItem_List = {}
    m.RegisterClickEvent()
    return true
end

--------------------------------------------------------------------
-- 窗口开启
--------------------------------------------------------------------
function m:OnOpen(equipID)
    m.boxEquipData = PropertyCompute.GetBoxEquipData(equipID)
    m.UpdateView()
end

--------------------------------------------------------------------
-- 注册点击事件
--------------------------------------------------------------------
function m.RegisterClickEvent()
    m:AddClick(m.objList.Btn_Close, function()
        m:CloseSelf()
    end)
    -- m:AddClick(m.objList.Btn_Ad, function()
    --     if not m.boxEquipData then return end
    --     m.BuyBox(m.boxEquipData.equipID, 2)
    -- end)
    m:AddClick(m.objList.Btn_Buy, function()
        if not m.boxEquipData then return end
        m.BuyBox(m.boxEquipData.equipID, 1)
    end)
end

--------------------------------------------------------------------
--更新界面
--------------------------------------------------------------------
function m.UpdateView()
    local prizeGoodsList = {}
    if m.boxEquipData then
        m.objList.Txt_Title.text = m.boxEquipData.name
        local boxIcon = boxIconList[m.boxEquipData.equipID]
        AtlasManager:AsyncGetSprite(boxIcon.icon, m.objList.Img_Goods)
        AtlasManager:AsyncGetSprite(boxIcon.bg, m.objList.Img_Bg)

        local boxEquipSmelt = m.boxEquipData.boxEquipSmeltList[m.boxEquipData.level]
        local goodsNum = SkepModule:GetGoodsCount(boxEquipSmelt.expendID)
        local isLackGoods = goodsNum < boxEquipSmelt.expendNum
        AtlasManager:AsyncGetGoodsSprite(boxEquipSmelt.expendID, m.objList.Img_CostType)
        local color = isLackGoods and UI_COLOR.Red or UI_COLOR.White
        m.objList.Txt_CostNum.text = string.format("<color=%s>%s</color>", color, boxEquipSmelt.expendNum)
        HelperL.SetImageGray(m.objList.Btn_Buy, isLackGoods)
        prizeGoodsList = Schemes.PrizeTable:GetGoodsList(boxEquipSmelt.prizeID) or {}

        -- local state = HelperL.GetAdverState(m.boxEquipData.adID)
        -- HelperL.SetImageGray(m.objList.Btn_Ad, state ~= 1)
    end

    local num = math.max(#prizeGoodsList, #m.Item_Goods_List)
    for i = 1, num, 1 do
        if not m.Item_Goods_List[i] then
            m.Item_Goods_List[i] = m.Creation_Item_Goods(m.objList.Grid_Box, i)
        end
        m.Item_Goods_List[i].UpdateData(prizeGoodsList[i])
    end
end

--------------------------------------------------------------------
-- 创建商品栏
--------------------------------------------------------------------
function m.Creation_Item_Goods(parent, index)
    ---@class Item_Goods3
    local item = {}
    item.index = index
    item.com = m:CreateSubItem(parent, m.objList.Item_Goods)
    m:AddClick(item.com.Btn_Click, function()
        if not item.data then return end
        m.ShowRandomRewards(item.data.id)
    end)

    ---更新数据
    ---@param data PrizeParse
    item.UpdateData = function(data)
        item.data = data
        if data then
            AtlasManager:AsyncGetGoodsSprite(data.id, item.com.Img_Icon)
            item.com.Txt_Amount.text = data.num
            local quaLevel = 0
            if data.id <= DEFINE.MAX_MEDICAMENT_ID then
                quaLevel = Schemes.Medicament:Get(data.id).Quality
            else
                quaLevel = Schemes.Equipment:Get(data.id).QualityLevel
            end
            AtlasManager:AsyncGetSprite(HelperL.GetImageByQuality(quaLevel), item.com.Img_Bg)
            item.com.gameObject:SetActive(true)
        else
            item.com.gameObject:SetActive(false)
        end
    end
    return item
end

--------------------------------------------------------------------
-- 显示随机奖励
--------------------------------------------------------------------
function m.ShowRandomRewards(id)
    local medicament = Schemes.Medicament:Get(id)
    if not medicament or medicament.UseMenu ~= 4 then
        return
    end
    UIManager:OpenWnd(WndID.BoxProbability, id)
end

--------------------------------------------------------------------
---宝箱购买
---@param equipID integer 宝箱ID
---@param buyType integer 购买类型, 1:普通购买, 2:免费
--------------------------------------------------------------------
function m.BuyBox(equipID, buyType)
    local boxEquipData = PropertyCompute.GetBoxEquipData(equipID)
    if not boxEquipData then return end
    local expendID  = boxEquipData.boxEquipSmelt.expendID
    local expendNum = boxEquipData.boxEquipSmelt.expendNum
    if buyType == 1 then
        if HelperL.IsLackGoods(expendID, expendNum, false) then
            return
        end
    elseif buyType == 2 then
        if HelperL.GetAdverHint(boxEquipData.adID, true) then
            return
        end
    else
        warn('类型不存在 buyType=', buyType)
        return
    end

    if buyType == 1 then
        UIManager:OpenWnd(WndID.OpenBox, equipID, buyType)
    elseif buyType == 2 then
        AdvertisementManager.ShowRewardAd(boxEquipData.adID, function(bool, adID)
            local equipID2, buyType2 = equipID, buyType
            if bool then
                UIManager:OpenWnd(WndID.OpenBox, equipID2, buyType2)
            end
        end)
    end
    m:CloseSelf()
end

return m
