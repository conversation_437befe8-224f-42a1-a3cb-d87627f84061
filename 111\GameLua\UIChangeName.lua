
local luaID = 'UIChangeName'
---@class UIChangeName:UIWndBase
local m = {}

--------------------------------------------------------------------
-- 初始化
--------------------------------------------------------------------
function m.OnCreate() 
    m.RegisterClickEvent()
    return true
end

--------------------------------------------------------------------
-- 窗口开启
--------------------------------------------------------------------
function m:OnOpen()
	m.objList.Inp_Name.text = EntityModule.hero.name
end


--------------------------------------------------------------------
-- 注册点击事件
--------------------------------------------------------------------
function m.RegisterClickEvent()
    m:AddClick(m.objList.Btn_Close, function()
        m:CloseSelf()
    end)
    
    m:AddClick(m.objList.Btn_OK, function()
        if m.objList.Inp_Name.text == EntityModule.hero.name then
            HelperL.ShowMessage(TipType.FlowText, "请重新输入名字！")
            return
        end

        local _, bool = SensitiveWord.Check(m.objList.Inp_Name.text)
        if bool then
            HelperL.ShowMessage(TipType.FlowText, GetGameText("UILogin", 23))
            m.objList.Inp_Name.text = EntityModule.hero.name
            return
        end	

        if SkepModule:GetGoodsCount(3) < 50 then
            HelperL.ShowMessage(TipType.FlowText, "灵晶不足！")
            return
        end

        local data = {
			type = NotarizeWindowsType.Windows1,
			titleContent = '改名提示',
			content = string.format("是否确认消耗%s灵晶修改名字?", 50),
			okCallback = function()				
				local str_req = string.format("LuaRequestChangeName?name=%s", m.objList.Inp_Name.text)
                LuaModule.RunLuaRequest(str_req, function(resultCode, content)
                    if resultCode ~= RESULT_CODE.RESULT_COMMON_SUCCEED then
                        HelperL.ShowMessage(TipType.FlowText, ResultCode:GetResultCodeStr(resultCode, content))
                    else
                        -- local goodInfo = '0'
                        -- local costInfo = "3;50"
                        -- EntityModule.hero.name = m.objList.Inp_Name.text
                        -- EventManager:Fire(EventID.ChangeNameEvent)
                        -- --扣消耗
                        -- HelperL.RequestDirectGiveGoodsy(goodInfo, costInfo, function(resultCode, content)
                        --     --扣消耗--失败
                        --     print("resultCode ======= ",resultCode)
                        --     if resultCode ~= 0 then
                        --         return
                        --     end
                            m:CloseSelf()        
                        -- end, false)
                        
                    end
                end)
			end,
		}
		HelperL.NotarizeUI(data)

        
    end)
	
end

    

return m
