-- 全局匹配管理
local luaID = ('MatchDataManager')

MatchDataManager = {}
MatchDataManager.matchData = {}
MatchDataManager.matchList = {}

-- 获取匹配状态
function MatchDataManager:GetMatchingID()
	local data = self.matchData
	if data and data.matchStatus and data.matchStatus > 0 then
		return data.matchID
	end
	return 0
end

-- 获取匹配数据
function MatchDataManager:GetMatchData()
	return self.matchData
end

-- 获取匹配列表
function MatchDataManager:GetMatchList(targetType)
	return self.matchList[targetType]
end

-- 获取匹配列表数据
function MatchDataManager:GetMatchListData(targetType, matchID)
	local list = self.matchList[targetType]
	if not list then
		return nil
	end
	
	for i, v in ipairs(list) do
		if v.MatchID == matchID then
			return v
		end
	end
	
	return nil
end

-- 收到开始匹配结果消息
function MatchDataManager:OnRecvBeginMatchResult(m)
	ResultCode:DefaultShowResultCode(m.Result, nil)
end

-- 收到匹配状态消息
function MatchDataManager:OnRecvMatchStatus(m)
	local data = self.matchData
	if m.MatchStatus == 0 then
		-- 清空匹配数据
		data.matchID = 0
		data.matchStatus = 0
		data.matchParam1 = 0
		data.matchParam2 = 0
		data.matchParam3 = 0
		data.matchParam4 = 0
		data.matchParam5 = 0
		data.matchParam6 = 0
		data.matchParam7 = 0
		data.matchParam8 = 0
		data.matchParam9 = 0
		data.matchParam10 = 0
		data.matchParam11 = 0
		data.matchParam12 = 0
		data.matchParam13 = 0
		data.matchParam14 = 0
		data.matchParam15 = 0
		data.matchParam16 = 0
		data.matchParam17 = 0
		data.matchParam18 = 0
	else
		data.matchID = m.MatchID
		data.matchStatus = m.MatchStatus
		data.matchParam1 = m.MatchParam1
		data.matchParam2 = m.MatchParam2
		data.matchParam3 = m.MatchParam3
		data.matchParam4 = m.MatchParam4
		data.matchParam5 = m.MatchParam5
		data.matchParam6 = m.MatchParam6
		data.matchParam7 = m.MatchParam7
		data.matchParam8 = m.MatchParam8
		data.matchParam9 = m.MatchParam9
		data.matchParam10 = m.MatchParam10
		data.matchParam11 = m.MatchParam11
		data.matchParam12 = m.MatchParam12
		data.matchParam13 = m.MatchParam13
		data.matchParam14 = m.MatchParam14
		data.matchParam15 = m.MatchParam15
		data.matchParam16 = m.MatchParam16
		data.matchParam17 = m.MatchParam17
		data.matchParam18 = m.MatchParam18
	end
	
	EventManager:Fire(EventID.UpdateMatchStatus, data.matchID)
end

-- 收到匹配列表消息
function MatchDataManager:OnRecvMatchList(m)
	self.matchList[m.TargetType] = m.MatchList
	
	EventManager:Fire(EventID.UpdateMatchList, m.TargetType)
end

-- 请求开始匹配
function MatchDataManager:RequestBeginMatch(matchID, matchParam1, matchParam2, matchParam3, matchParam4, matchParam5, matchParam6, matchParam7, matchParam8, matchParam9, matchParam10, matchParam11, matchParam12,
											matchParam13, matchParam14, matchParam15, matchParam16, matchParam17, matchParam18)
	matchParam1 = matchParam1 or 0
	matchParam2 = matchParam2 or 0
	matchParam3 = matchParam3 or 0
	matchParam4 = matchParam4 or 0
	matchParam5 = matchParam5 or 0
	matchParam6 = matchParam6 or 0
	matchParam7 = matchParam7 or 0
	matchParam8 = matchParam8 or 0
	matchParam9 = matchParam9 or 0
	matchParam10 = matchParam10 or 0
	matchParam11 = matchParam11 or 0
	matchParam12 = matchParam12 or 0
	matchParam13 = matchParam13 or 0
	matchParam14 = matchParam14 or 0
	matchParam15 = matchParam15 or 0
	matchParam16 = matchParam16 or 0
	matchParam17 = matchParam17 or 0
	matchParam18 = matchParam18 or 0
	
	local m = GameMessage_pb.CS_Match_BeginMatch()
	m.MatchID = matchID
	m.MatchParam1 = matchParam1
	m.MatchParam2 = matchParam2
	m.MatchParam3 = matchParam3
	m.MatchParam4 = matchParam4
	m.MatchParam5 = matchParam5
	m.MatchParam6 = matchParam6
	m.MatchParam7 = matchParam7
	m.MatchParam8 = matchParam8
	m.MatchParam9 = matchParam9
	m.MatchParam10 = matchParam10
	m.MatchParam11 = matchParam11
	m.MatchParam12 = matchParam12
	m.MatchParam13 = matchParam13
	m.MatchParam14 = matchParam14
	m.MatchParam15 = matchParam15
	m.MatchParam16 = matchParam16
	m.MatchParam17 = matchParam17
	m.MatchParam18 = matchParam18
	Premier.Instance:GetNetwork():SendFromLua(ENDPOINT.ENDPOINT_GAMECLIENT,
									ENDPOINT.ENDPOINT_GAMESERVER,
									MSG_MODULEID.MSG_MODULEID_GAME,
									GameMessage_pb.MSG_MATCH_BEGINMATCH,
									m:SerializeToString())
end

-- 请求匹配列表
function MatchDataManager:RequestMatchList(targetType, targetParam1)
	targetParam1 = targetParam1 or 0
	local m = GameMessage_pb.CS_Match_SyncMatchList()
	m.TargetType = targetType
	m.TargetParam1 = targetParam1
	Premier.Instance:GetNetwork():SendFromLua(ENDPOINT.ENDPOINT_GAMECLIENT,
									ENDPOINT.ENDPOINT_GAMESERVER,
									MSG_MODULEID.MSG_MODULEID_GAME,
									GameMessage_pb.MSG_MATCH_SYNCMATCHLIST,
									m:SerializeToString())
end