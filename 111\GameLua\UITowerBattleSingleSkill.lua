-- 塔防单个技能物件
local luaID = ('UITowerBattleSingleSkill')

-- 初始化物件
local itemPrefab = nil
local function InitSingleSkillItem(self, parent)
	if not itemPrefab then
		itemPrefab = HotResManager.ReadUI('ui/Main/TowerBattleSingleSkill')
		if not itemPrefab then
			warn('InitSingleTowerItem 找不到技能图标预制体')
			return false
		end
	end
	
	if not parent then
		warn('InitSingleSkillItem 未提供父节点')
		return false
	end
	
	local itemObj = GameObject.Instantiate(itemPrefab, parent)
	local itemTrans = itemObj.transform
	local objList = {}
	Helper.FillLuaComps(itemTrans, objList)
	self.gameObject = itemObj
	self.transform = itemTrans
	self.objList = objList
	
	self.mask = objList.Img_Mask
	self.frameAniObj = self.objList.Img_Effect.gameObject
	self.frameAniComp = self.frameAniObj:GetComponent('SpriteAnimationComp')
	self.rectTrans = itemObj:GetRectTransform()
	self.transContent = objList.Rct_Content
	
	self.isValid = true
	self.showCost = true
	
	return true
end

-- 设置物件数据
local function SetItemData(self, itemID)
	if self.config and self.config.ID == itemID then
		-- 不需要重复设置
		return
	end
	local config = Schemes.TowerBattleObject:Get(itemID)
	self.config = config
	local showCostObj = false
	if config then
		-- config类型=TowerBattleObject
		local resColor = HelperL.GetTowerResTypeColor(config.CostResType)
		self.objList.Txt_Cost.text = string.format('<color=#%s>%s</color>', resColor, config.CostResValue)
		if config.CostResType > 0 then
			showCostObj = self.showCost
		else
			showCostObj = false
		end
		
		self.objList.Img_Icon.sprite = AtlasManager:GetSprite(config.ShowIcon)
	else
		self.objList.Txt_Cost.text = ''
		showCostObj = false
	end
	if self.objList.CostObj.activeSelf ~= showCostObj then
		self.objList.CostObj:SetActive(showCostObj)
	end
end

-- 设置物件是否可用
local function SetItemValid(self, isValid)
	if self.isValid == isValid then
		return
	end
	HelperL.SetImageGray(self.objList.Img_BG, not isValid)
	HelperL.SetImageGray(self.objList.Img_Icon, not isValid)
	HelperL.SetImageGray(self.objList.Img_CostIcon, not isValid)
	self.isValid = isValid
end

-- 获取物件是否可用
local function IsItemValid(self)
	return self.isValid
end

-- 设置物件是否可用
local function SetItemDark(self, isDark)
	if self.isDark == isDark then
		return
	end
	self.objList.Img_Dark.gameObject:SetActive(isDark)
	self.isDark = isDark
end

-- 获取物件是否可用
local function IsItemDark(self)
	return self.isDark
end

-- 设置是否显示消耗
local function SetShowCost(self, isShow)
	self.showCost = isShow
	if self.objList.CostObj.activeSelf ~= isShow then
		self.objList.CostObj:SetActive(isShow)
	end
end

-- 设置是否显示等级
local function SetShowLevel(self, isShow)
	-- 技能物件不需要
end

-- 设置帧动画
local function SetFrameAnimation(self, useAni, aniType)
	if not useAni then
		self.frameAniObj:SetActive(false)
	else
		if self.frameAniComp then
			-- 设置prefix
			if aniType == 1 then
				self.frameAniComp.prefixName = 'A01_'
				self.frameAniComp.framePerSecond = 12
			end
		end
		self.frameAniObj:SetActive(true)
	end
end

-- 是否在播放帧动画
local function IsFrameAnimating(self)
	return self.frameAniObj.activeSelf
end

-- 显示高亮图片
local function ShowHighLightEffect(self, isShow)
	if self.objList.Img_Light.gameObject.activeSelf ~= isShow then
		self.objList.Img_Light.gameObject:SetActive(isShow)
		self.objList.Img_BG.gameObject:SetActive(not isShow)
	end
end

-- 设置是否显示
local function SetVisible(self, isVisible)
	if self.gameObject.activeSelf ~= isVisible then
		self.gameObject:SetActive(isVisible)
	end
end

-- 设置大小
local function SetSize(self, width, height)
	local layout = self.gameObject:GetComponent('LayoutElement')
	if layout then
		layout.preferredWidth = width
		layout.preferredHeight = height
	end
	self.rectTrans.sizeDelta = Vector2(width, height)
end

-- 设置物件封面图
local function SetCoverSprite(self, spriteName)
	if self.coverSpriteName == spriteName then return end
	self.coverSpriteName = spriteName
	--
	AtlasManager:AsyncGetSprite(spriteName, self.objList.Img_Cover, false, function(sprite, image, param)
		if sprite then
			image.sprite = sprite
			image.enabled = true
			image:SetNativeSize()
		else
			image.enabled = false
		end
	end)
end

-- 获取物件封面图
local function GetCoverSprite(self)
	return self.coverSpriteName
end

function CreateTowerBattleSingleSkill(parent)
	local item = {}
	item.Init = InitSingleSkillItem
	item.SetItemData = SetItemData
	item.SetItemValid = SetItemValid
	item.IsItemValid = IsItemValid
	item.SetItemDark = SetItemDark
	item.IsItemDark = IsItemDark
	item.SetShowCost = SetShowCost
	item.SetShowLevel = SetShowLevel
	item.SetFrameAnimation = SetFrameAnimation
	item.IsFrameAnimating = IsFrameAnimating
	item.ShowHighLightEffect = ShowHighLightEffect
	item.SetVisible = SetVisible
	item.SetSize = SetSize
	item.SetCoverSprite = SetCoverSprite
	item.GetCoverSprite = GetCoverSprite

	if not item:Init(parent) then
		warn('CreateTowerBattleSingleSkill 初始化失败')
		return nil
	end

	return item
end
