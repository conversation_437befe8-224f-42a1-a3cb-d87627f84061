-- 辅助类
local luaID = ('HelperL')

HelperL = {}
--自动开宝箱属性类型
HelperL.EQUIP_TYPE = 0

function HelperL.ClearGameData()
	local heroUID = nil
	if EntityModule.hero ~= nil then
		heroUID = EntityModule.hero.uid
	end
	local uidList = {}
	for k, v in pairs(EntityModule:GetEntityList()) do
		if v ~= nil and v.uid ~= nil and v.uid ~= heroUID then
			table.insert(uidList, v.uid)
		end
	end
	if EntityModule.hero ~= nil then
		table.insert(uidList, heroUID)
	end

	for i, v in ipairs(uidList) do
		EntityModule:DestroyEntity(v)
	end
	EntityModule.hero = nil

	HeroDataManager:ClearData()

	UnityEngine.Canvas.ForceUpdateCanvases()
end

-- 显示提示
function HelperL.ShowMessage(tipType, content, param1, param2, param3, param4, param5, param6, param7)
	print("content ================ ",content)
	if content == "" or content == "服务器内部错误!" then return end
	Main_PrintTraceback()
	UIManager:OpenWnd(WndID.TipManager, tipType, content, param1, param2, param3, param4, param5, param6, param7)
end

---显示奖励
---@overload fun(type:integer, prizeContent:string)
---@param type integer 解析类型，1解析奖励ID，2解析奖励字符串
---@param prizeContent string 奖励内容
---@param tipDesc? string 奖励描述
---@param callback? fun() 回调
function HelperL.DisplayReward(type, prizeContent, tipDesc, callback)
	if prizeContent == '' or prizeContent == '0' then
		return
	end
	UIManager:OpenWnd(WndID.DisplayReward, type, prizeContent, tipDesc, callback)
end

---单个奖励
---@param id integer 物品ID
---@param num integer 物品数量
---@param callback? fun() 回调
function HelperL.DisplayOneReward(id, num, callback)
	HelperL.DisplayReward(PrizeContentType.STRING, id .. '+' .. num, nil, callback)
end

---奖励提示框
---@overload fun(tipType:integer, prizeContent:string)
---@param tipType integer 提示框类型，1拖拽，2飘字
---@param prizeContent string 奖励内容
---@param tipDesc string 奖励描述
function HelperL.ShowPrizeTipBox(tipType, prizeContent, tipDesc)
	-- print('-------奖励提示框----------', tipType, prizeContent, tipDesc)
	if not prizeContent or prizeContent == '' then return end
	local prizeInfo = HelperL.Split(prizeContent, '|')
	--所有奖励的物品
	local cfgs = {}
	--所有id和数量
	local allIds = {}
	local allnums = {}
	--飘字的id和数量
	local showmessageids = {}
	local showmessagenums = {}
	--弹窗的id和数量
	local DisplayRewardids = {}
	local DisplayRewardnums = {}
	--奖励显示在一个弹窗的
	local DisplayRewardprizeContent = ''
	--所有弹窗(一个一个显示和一次性显示的)
	local DisplayRewardprizeContents = {}
	--是否所有奖励表BoxSurface都=0
	--print("tipType:"..tipType)
	local allBoxSurface0 = true
	for i, v in ipairs(prizeInfo) do
		local goodsInfo, id, num, cfg
		goodsInfo = HelperL.Split(v, '+')
		if goodsInfo[1] and goodsInfo[2] then
			id = tonumber(goodsInfo[1]) or 0
			num = tonumber(goodsInfo[2]) or 0
			table.insert(allIds, id)
			table.insert(allnums, num)
			cfg = Schemes:GetGoodsConfig(id)
			if cfg then
				if cfg.BoxSurface ~= '0' then
					allBoxSurface0 = false
				end
				table.insert(cfgs, cfg)
			else
				error("Medicament.csv no found ID=" .. id)
			end
		end
	end
	--所有奖励表BoxSurface都=0
	if allBoxSurface0 then
		--print("allBoxSurface0")
		--类型1 弹窗(奖励显示在一个弹窗)
		if tipType == 1 then
			HelperL.DisplayReward(2, prizeContent, tipDesc)
			--类型2 飘字
		elseif tipType == 2 then
			HelperL.ShowRewardMessageById(allIds, allnums)
		end
	else
		--类型1
		if tipType == 0 then
			--奖励表BoxSurface=1 弹窗(奖励一个一个显示)
			for i = 1, #cfgs do
				if cfgs[i].BoxSurface == '1' then
					table.insert(DisplayRewardids, allIds[i])
					table.insert(DisplayRewardnums, allnums[i])
				end
			end
		elseif tipType == 1 then
			for i = 1, #cfgs do
				--弹窗(奖励显示在一个弹窗)
				if cfgs[i].BoxSurface == '0' then
					if DisplayRewardprizeContent == '' then
						DisplayRewardprizeContent = allIds[i] .. "+" .. allnums[i]
					else
						DisplayRewardprizeContent = DisplayRewardprizeContent .. '|' .. allIds[i] .. "+" .. allnums[i]
					end
					--弹窗(奖励一个一个显示)
				elseif cfgs[i].BoxSurface == '1' then
					table.insert(DisplayRewardids, allIds[i])
					table.insert(DisplayRewardnums, allnums[i])
				end
			end
		elseif tipType == 2 then
			for i = 1, #cfgs do
				--飘字
				if cfgs[i].BoxSurface == '0' then
					table.insert(showmessageids, allIds[i])
					table.insert(showmessagenums, allnums[i])
					--弹窗(奖励一个一个显示)
				elseif cfgs[i].BoxSurface == '1' then
					table.insert(DisplayRewardids, allIds[i])
					table.insert(DisplayRewardnums, allnums[i])
				end
			end
		end
		for i = 1, #DisplayRewardids do
			--print("DisplayRewardids"..i..":"..DisplayRewardids[i])
			table.insert(DisplayRewardprizeContents, DisplayRewardids[i] .. '+' .. DisplayRewardnums[i])
		end
		--print("DisplayRewardprizeContent:"..DisplayRewardprizeContent)
		if DisplayRewardprizeContent ~= '' then
			table.insert(DisplayRewardprizeContents, DisplayRewardprizeContent)
		end
		HelperL.DisplayRewardRank(1, DisplayRewardprizeContents, tipDesc)
		HelperL.ShowRewardMessageById(showmessageids, showmessagenums)
	end
end

function HelperL.DisplayRewardRank(index, DisplayRewardprizeContents, tipDesc)
	if index <= #DisplayRewardprizeContents then
		HelperL.DisplayReward(2, DisplayRewardprizeContents[index], tipDesc, function()
			HelperL.DisplayRewardRank(index + 1, DisplayRewardprizeContents, tipDesc)
		end)
	end
end

--解析奖励显示飘字
function HelperL.ShowRewardMessageById(ids, nums)
	local id, num, cfg, color
	local content = ''
	for i = 1, #ids do
		if ids[i] and nums[i] then
			id = ids[i]
			num = math.floor(nums[i] * HelperL.GetGoodsAdd(id))
			cfg = Schemes:GetGoodsConfig(id)
			color = num > 0 and UI_COLOR.White or UI_COLOR.Red
			if cfg then
				if content == '' then
					content = string.format(GetGameText(luaID, 36), color, cfg.GoodsName, num)
				else
					content = content
						.. '\n'
						.. string.format(GetGameText(luaID, 36), color, cfg.GoodsName, num)
				end
				HelperL.PlayCollectAnimation(id, num)
			end
		end
	end
	HelperL.RewardHintUI(UIRewardHint_Type.Message, content)
end

--解析奖励显示飘字
function HelperL.ShowRewardMessage(prizeContent)
	if not prizeContent or prizeContent == '' then return end
	local prizeInfo = HelperL.Split(prizeContent, '|')
	local goodsInfo, id, num, cfg, color
	local content = ''
	for i, v in ipairs(prizeInfo) do
		goodsInfo = HelperL.Split(v, '+')
		if goodsInfo[1] and goodsInfo[2] then
			id = tonumber(goodsInfo[1]) or 0
			num = math.floor((tonumber(goodsInfo[2]) or 0) * HelperL.GetGoodsAdd(id))
			cfg = Schemes:GetGoodsConfig(id)
			color = num > 0 and UI_COLOR.White or UI_COLOR.Red
			if cfg then
				if HelperL.IsEuipType(id) or cfg.BoxSurface ~= '1' then
					if content == '' then
						content = string.format(GetGameText(luaID, 36), color, cfg.GoodsName, num)
					else
						content = content
							.. '\n'
							.. string.format(GetGameText(luaID, 36), color, cfg.GoodsName, num)
					end
				end
				HelperL.PlayCollectAnimation(id, num)
			end
		end
	end
	HelperL.RewardHintUI(UIRewardHint_Type.Message, content)
end

--收集动画开始节点集合
HelperL.PlayCollectAni_StartNodeList = {}
--收集动画触发次数
-- HelperL.PlayCollectAni_Num = 0
--可播放获得动画物品ID集合
local playAnimationGoodsIdList = {
	[3] = true, --元宝
	[9] = true, --功勋
}
---播放资源收集动画
function HelperL.PlayCollectAnimation(goodsID, goodsNum)
	if not playAnimationGoodsIdList[goodsID] then return end
	---@type UIRoleAssets
	local ui = UIManager:GetUIWndBase(WndID.RoleAssets)
	if not ui or not ui.gameObject.activeSelf then return end
	local item = ui.propertyButtonList[goodsID]
	if item == nil or tolua.isnull(item.com.gameObject) then return end
	local startNode = UIManager.uiCanvas.gameObject
	if #HelperL.PlayCollectAni_StartNodeList > 0 then
		startNode = table.remove(HelperL.PlayCollectAni_StartNodeList, 1)
	end
	local endNode = item.com.gameObject
	local cfg = Schemes:GetGoodsConfig(goodsID)
	PrefabricatedAnimation.CollectAnimation(startNode, endNode, cfg.IconID)
end

-- 类型添加属性
function HelperL.ClassAddVar(class, var)
	if not class or not var then
		warn('HelperL.ClassAddVar', class, var)
		return
	end

	-- 加个随机数防小人
	local varField = var .. math.random(1, 999)
	class[var .. 'Field'] = varField
	class['Set' .. var] = function(self, param) self[varField] = param end
	class['Get' .. var] = function(self) return self[varField] end
end

-- 模型对象返还到缓存
function HelperL.AddObjToModelPool(obj, fileName)
	if not fileName then
		GameObject.Destroy(obj)
		return
	end
	obj:SetActive(false)
	obj.transform:SetParent(EntityModule.modelCacheContainerTrans, false)
	Helper.AddToModelPool(obj, fileName)
end

-- 设置图片是否变灰
function HelperL.SetImageGray(image, isGray)
	if not image then
		return
	end
	image = image.transform:GetComponent("Image")
	if not image then
		return
	end
	if isGray then
		UIManager:GetSlotMaterial(UIMaterialType.Gray, image)
	else
		image.material = nil
	end
end

-- 设置图片是材质
function HelperL.SetImageMaterial(image, quality)
	if not image then return end
	if quality and quality >= 3 then
		UIManager:GetSlotMaterial(quality, image)
		image.gameObject:SetActive(true)
	else
		image.gameObject:SetActive(false)
	end
end

-- 获取时间字符串分秒格式
TimeStringType = {
	FullAuto1 = 1,
	FullAuto1NoSec = 2,
	FullAuto2 = 3,
	FullAuto3 = 4, -- 大于1天，显示天数；少于1天，显示小时；不足一小时，显示分钟
	FullAuto4 = 5,
}

function HelperL.GetTimeString(formatType, timeValue)
	if not formatType or not timeValue then
		warn('HelperL.GetTimeString invalid param', formatType, timeValue)
		return nil
	end

	if timeValue < 0 then
		timeValue = 0
	end

	local result = nil
	if formatType == TimeStringType.FullAuto1 or formatType == TimeStringType.FullAuto1NoSec then
		-- dd天hh小时mm分ss秒
		local dd = math.floor(timeValue / 86400)
		timeValue = timeValue - dd * 86400
		local hh = math.floor(timeValue / 3600)
		timeValue = timeValue - hh * 3600
		local mm = math.floor(timeValue / 60)
		timeValue = timeValue - mm * 60
		local ss = timeValue
		if formatType == TimeStringType.FullAuto1NoSec then
			ss = 0
		end
		if dd > 0 then
			if hh == 0 and mm == 0 and ss == 0 then
				result = string.format('%d%s', dd, GetGameText(luaID, 4))
			elseif mm == 0 and ss == 0 then
				result = string.format('%d%s%d%s', dd, GetGameText(luaID, 4), hh, GetGameText(luaID, 1))
			elseif ss == 0 then
				result = string.format('%d%s%d%s%02d%s', dd, GetGameText(luaID, 4), hh, GetGameText(luaID, 1), mm,
					GetGameText(luaID, 2))
			else
				result = string.format('%d%s%d%s%02d%s%02d%s', dd, GetGameText(luaID, 4), hh, GetGameText(luaID, 1), mm,
					GetGameText(luaID, 2), ss, GetGameText(luaID, 3))
			end
		elseif hh > 0 then
			if mm == 0 and ss == 0 then
				result = string.format('%d%s', hh, GetGameText(luaID, 1))
			elseif ss == 0 then
				result = string.format('%d%s%02d%s', hh, GetGameText(luaID, 1), mm, GetGameText(luaID, 2))
			else
				result = string.format('%d%s%02d%s%02d%s', hh, GetGameText(luaID, 1), mm, GetGameText(luaID, 2), ss,
					GetGameText(luaID, 3))
			end
		elseif mm > 0 then
			if ss == 0 and formatType == TimeStringType.FullAuto1NoSec then
				result = string.format('%d%s', mm, GetGameText(luaID, 2))
			else
				result = string.format('%d%s%02d%s', mm, GetGameText(luaID, 2), ss, GetGameText(luaID, 3))
			end
		else
			result = string.format('%d%s', ss, GetGameText(luaID, 3))
		end
	elseif formatType == TimeStringType.FullAuto2 or formatType == TimeStringType.FullAuto4 then
		-- dd:hh:mm:ss
		local dd = math.floor(timeValue / 86400)
		timeValue = timeValue - dd * 86400
		local hh = math.floor(timeValue / 3600)
		timeValue = timeValue - hh * 3600
		local mm = math.floor(timeValue / 60)
		timeValue = timeValue - mm * 60
		local ss = timeValue
		if dd > 0 then
			result = string.format('%d天:%d时:%02d分:%02d秒', dd, hh, mm, ss)
		elseif hh > 0 or formatType == TimeStringType.FullAuto4 then
			result = string.format('%d时:%02d分:%02d秒', hh, mm, ss)
		else
			result = string.format('%02d分:%02d秒', mm, ss)
		end
	elseif formatType == TimeStringType.FullAuto3 then
		-- dd:hh:mm:ss
		local dd = math.floor(timeValue / 86400) -- 天数
		timeValue = timeValue - dd * 86400
		local hh = math.floor(timeValue / 3600) -- 小时
		timeValue = timeValue - hh * 3600
		local mm = math.floor(timeValue / 60) -- 分钟
		timeValue = timeValue - mm * 60
		local ss = timeValue               -- 秒
		if dd > 0 then                     -- 只显示天数
			result = string.format('%d%s', dd, GetGameText(luaID, 4))
		elseif hh > 0 then                 -- 只显示小时
			result = string.format('%d%s', hh, GetGameText(luaID, 1))
		else                               -- 只显示分钟
			result = string.format('%d%s', mm, GetGameText(luaID, 2))
		end
	else
		warn('HelperL.GetTimeString unknown formatType', formatType)
	end
	return result
end

---字符串分割
---@param inputstr string 源字符串
---@param sep string 分隔字符串
---@return string[]
function HelperL.Split(inputstr, sep)
	if sep == nil then
		sep = "%s"
	end
	local t = {}
	for str in string.gmatch((inputstr or ""), "([^" .. sep .. "]+)") do
		table.insert(t, str)
	end
	return t
end

---匹配字符串开头
---@param inputstr string 源字符串
---@param sep string 匹配字符串
---@return boolean
function HelperL.StartsWith(inputstr, sep)
	inputstr = tostring(inputstr)
	sep = tostring(sep)
	return string.sub(inputstr, 1, #sep) == sep
end

---匹配字符串结尾
---@param inputstr string 源字符串
---@param sep string 匹配字符串
---@return boolean
function HelperL.EndsWith(inputstr, sep)
	inputstr = tostring(inputstr)
	sep = tostring(sep)
	local i = (#inputstr - #sep) + 1
	return string.sub(inputstr, i, #inputstr) == sep
end

function HelperL.NewQueue()
	local Q = {}
	Q.__index = Q
	function Q:New()
		local q = { first = 0, last = -1, n = 0 }
		setmetatable(q, Q)
		return q
	end

	function Q:pushLast(value)
		local last = self.last + 1
		self.last = last
		self[last] = value
		self.n = self.n + 1
	end

	function Q:peekFirst()
		return self[self.first]
	end

	function Q:popFirst()
		local first = self.first
		if first > self.last then return nil end
		local value = self[first]
		self[first] = nil
		self.first = first + 1
		self.n = self.n - 1
		return value
	end

	function Q:iter()
		local index = self.first - 1
		return function()
			index = index + 1
			return self[index]
		end
	end

	function Q:Count()
		return self.n
	end

	function Q:IsEmpty()
		return self.n == 0
	end

	return Q
end

--------------------------------------------------------------------
-- 判断是装备
--------------------------------------------------------------------
function HelperL.IsEuipType(goodsID)
	return (goodsID >= DEFINE.MIN_EQUIPMENT_ID) and (goodsID <= DEFINE.MAX_EQUIPMENT_ID)
end

--------------------------------------------------------------------
-- 是否是宠物伙伴
--------------------------------------------------------------------
function HelperL.IsPet(goodsID)
	local max = goodsID / 1000
	if math.floor(max) == 5 then return true end
	return false
end

--------------------------------------------------------------------
-- 是否是宝箱
--------------------------------------------------------------------
function HelperL.IsBox(goodsID)
	local schemeItem = Schemes.Medicament:Get(goodsID)
	if schemeItem then
		if tonumber(schemeItem.BoxSurface) == '1' then
			return true
		end
	end

	return false
end

--------------------------------------------------------------------
-- 数值转换成万或亿显示
--------------------------------------------------------------------
function HelperL.TransNumToStr(value, type)
	local str, str1 = "", ""
	if value >= 100000000 then
		str = "" .. math.floor(value / 100000000)
		if value % 100000000 > 0 then
			str1 = string.format('.%02d', math.floor(value % 100000000 / 1000000))
		end
		str1 = str1 .. GetGameText(luaID, 14)
	elseif value >= 1000000 or (type and type == 1 and value >= 10000) then
		str = "" .. math.floor(value / 10000) .. GetGameText(luaID, 15)
	else
		str = "" .. value
	end
	return (str .. str1)
end

---在界面上创建3D模型，每次创建都只有一个模型，其余删除
---@param parent integer 父节点
---@param creatureID integer 模型ID
---@param isSlowAction? boolean 开启缓动动画，默认：true
function HelperL.CreateUIModel(parent, creatureID, isSlowAction)
	local creature = Schemes.Creature:Get(creatureID)
	if not creature then
		warn('HelperL.CreateUIModel 读表 Creature 失败 creature=', creatureID)
		return
	end
	if tolua.isnull(parent) then
		warn('HelperL.CreateUIModel 父节点为空')
		return
	end
	-- 销毁前面创建的所有模型
	local trans = parent.transform
	for c = trans.childCount - 1, 0, -1 do
		if trans:GetChild(c) then
			GameObject.Destroy(trans:GetChild(c).gameObject)
		end
	end

	-- 3D模型载入完毕
	local modelLoaded = function(objModel)
		HelperL.ChildChangeLayer(objModel)
		-- 模型位置
		objModel.transform.localPosition = Vector3(creature.PosX, creature.PosY, creature.PosZ)
		-- 模型比例
		objModel.transform.localScale = Vector3.one * creature.Scale * 100
		-- 模型旋转
		objModel.transform:Rotate(creature.RotX, creature.RotY, creature.RotZ)
		-- 模型皮肤
		-- local spineGraphic = objModel:GetComponent(typeof(Spine.Unity.SkeletonGraphic))
		-- if spineGraphic then
		-- 	spineGraphic.initialSkinName = creature.ModelSkin
		-- 	spineGraphic:Initialize(true)
		-- end
		-- HelperL.addDragRotate(objModel)
	end

	--添加浮动动画
	if isSlowAction ~= false then
		parent = GameObject.Instantiate(HotResManager.ReadUI('ui/Common/UIModel'), parent.transform)
	end

	-- 加载3D模型
	local fileName = creature.UIPrefab
	local result = HotResManager.ReadModelAsync(parent, fileName, modelLoaded, 0, true)
	if not result then
		warn('HelperL.CreateUIModel 加载模型 ' .. creature.UIPrefab .. ' 失败' ..
			" creatureID = " .. creatureID)
	end
end

--设置层级
function HelperL.ChildChangeLayer(objModel)
	if objModel.transform.childCount > 0 then
		for i = 0, objModel.transform.childCount - 1 do
			objModel.transform:GetChild(i).gameObject.layer = Layer.UI
			HelperL.ChildChangeLayer(objModel.transform:GetChild(i))
		end
	end
end

-- 鼠标旋转模型
function HelperL.addDragRotate(root)
	local dragFunc = function(eventData)
		local offsetPos = eventData.position
		root.transform:Rotate(Vector3(0, 1, 0), -offsetPos.y / Time.deltaTime)
	end
	local rectTrans = root:GetRectTransform()
	local eventTrigger = root:AddComponent(typeof(EventTrigger))
	eventTrigger:AddListener(EventTriggerType.BeginDrag, dragFunc)
	eventTrigger:AddListener(EventTriggerType.Drag, dragFunc)
	eventTrigger:AddListener(EventTriggerType.EndDrag, dragFunc)
	eventTrigger:AddListener(EventTriggerType.PointerClick, dragFunc)
end

function HelperL.CreateUIModelByPath(parent, path, pos, scale, angel, fallback)
	-- 3D模型载入完毕
	local modelLoaded = function(objModel)
		-- 销毁前面创建的所有模型
		local trans = parent.transform
		for c = trans.childCount - 1, 0, -1 do
			if trans:GetChild(c).gameObject ~= objModel then
				GameObject.Destroy(trans:GetChild(c).gameObject)
			end
		end
		HelperL.ChildChangeLayer(objModel)
		-- 模型位置
		objModel.transform.localPosition = pos
		-- 模型比例
		objModel.transform.localScale = scale
		-- 模型旋转
		objModel.transform:Rotate(angel)
		-- 模型皮肤
		local spineGraphic = objModel:GetComponent(typeof(Spine.Unity.SkeletonGraphic))
		if spineGraphic then
			spineGraphic.initialSkinName = creature.ModelSkin
			spineGraphic:Initialize(true)
		end
		if fallback then fallback(objModel) end
		--HelperL.addDragRotate(objModel)
	end
	-- 加载3D模型
	local result = HotResManager.ReadModelAsync(parent, path, modelLoaded, 0, true)
	if not result then
		warn('HelperL.CreateUIModel 加载模型 ' .. path .. ' 失败')
	end
end

--------------------------------------------------------------------
-- 品质背景
--------------------------------------------------------------------
function HelperL.GetImageByQuality(quality)
	quality = tonumber(quality) or 0
	if quality == QUALITY.QUALITY_WHITE then
		return "kuang_0"
	elseif quality == QUALITY.QUALITY_GREEN then
		return "kuang_1"
	elseif quality == QUALITY.QUALITY_BLUE then
		return "kuang_2"
	elseif quality == QUALITY.QUALITY_PINK then
		return "kuang_3"
	elseif quality == QUALITY.QUALITY_GOLD then
		return "kuang_4"
	elseif quality == QUALITY.QUALITY_ORANGE then
		return "kuang_5"
	elseif quality >= QUALITY.QUALITY_RED then
		return "kuang_6"
	end
	return "kuang_0"
end

--------------------------------------------------------------------
-- 品质背景图片
--------------------------------------------------------------------
function HelperL.GetQualitySprite(quality, image, isSize)
	AtlasManager:AsyncGetSprite(HelperL.GetImageByQuality(quality), image, isSize)
end

--------------------------------------------------------------------
--获取品质log
--------------------------------------------------------------------
function HelperL.GetQualityLogo(quality, image, isSize)
	local name = "yz_E"
	if quality == QUALITY.QUALITY_WHITE then
		name = "yz_E"
	elseif quality == QUALITY.QUALITY_GREEN then
		name = "yz_D"
	elseif quality == QUALITY.QUALITY_BLUE then
		name = "yz_C"
	elseif quality == QUALITY.QUALITY_PINK then
		name = "yz_B"
	elseif quality == QUALITY.QUALITY_GOLD then
		name = "yz_A"
	elseif quality == QUALITY.QUALITY_ORANGE then
		name = "yz_S"
	elseif quality == QUALITY.QUALITY_RED then
		name = "yz_SS"
	elseif quality > QUALITY.QUALITY_RED then
		name = "yz_SSS"
	end
	AtlasManager:AsyncGetSprite(name, image, isSize)
end

--------------------------------------------------------------------
-- 品质颜色
--------------------------------------------------------------------
function HelperL.GetColorByQuality(quality)
	quality = tonumber(quality) or 0
	if quality == QUALITY.QUALITY_WHITE then
		return "FFFFFF"
	elseif quality == QUALITY.QUALITY_GREEN then
		return "00C75B"
	elseif quality == QUALITY.QUALITY_BLUE then
		return "00ABC7"
	elseif quality == QUALITY.QUALITY_PINK then
		return "D200FF"
	elseif quality == QUALITY.QUALITY_GOLD then
		return "C77800"
	elseif quality == QUALITY.QUALITY_ORANGE then
		return "FF7E00"
	elseif quality >= QUALITY.QUALITY_RED then
		return "FF3434"
	end
	return "FFFFFF"
end

--------------------------------------------------------------------
-- 品质颜色
--------------------------------------------------------------------
function HelperL.GetColorByQualityEquip(quality)
	quality = tonumber(quality) or 0
	if quality == QUALITY.QUALITY_WHITE then
		return "FFFFFF"
	elseif quality == QUALITY.QUALITY_GREEN then
		return "00C75B"
	elseif quality == QUALITY.QUALITY_BLUE then
		return "00ABC7"
	elseif quality == QUALITY.QUALITY_PINK then
		return "D200FF"
	elseif quality == QUALITY.QUALITY_GOLD then
		return "C77800"
	elseif quality == QUALITY.QUALITY_ORANGE then
		return "FF7E00"
	elseif quality >= QUALITY.QUALITY_RED then
		return "FF3434"
	end
	return "FFFFFF"
end

function HelperL.GetQualityColorRGBA(quality)
	quality = tonumber(quality) or 0
	local color = Color(1, 1, 1, 1)
	if quality == QUALITY.QUALITY_WHITE then
		color = Color(228 / 255, 225 / 255, 239 / 255, 1)
	elseif quality == QUALITY.QUALITY_GREEN then
		color = Color(45 / 255, 251 / 255, 11 / 255, 1)
	elseif quality == QUALITY.QUALITY_BLUE then
		color = Color(11 / 255, 160 / 255, 250 / 255, 1)
	elseif quality == QUALITY.QUALITY_PINK then
		color = Color(248 / 255, 76 / 255, 246 / 255, 1)
	elseif quality == QUALITY.QUALITY_GOLD then
		color = Color(251 / 255, 196 / 255, 9 / 255, 1)
	elseif quality == QUALITY.QUALITY_ORANGE then
		color = Color(234 / 255, 99 / 255, 8 / 255, 1)
	elseif quality >= QUALITY.QUALITY_RED then
		color = Color(164 / 255, 23 / 255, 138 / 255, 1)
	end
	return color
end

--------------------------------------------------------------------
-- 品质名称
--------------------------------------------------------------------
function HelperL.GetNameByQuality(quality)
	quality = tonumber(quality) or 0
	local quaName = ''
	if quality == QUALITY.QUALITY_WHITE then
		quaName = GetGameText(luaID, 42)
	elseif quality == QUALITY.QUALITY_GREEN then
		quaName = GetGameText(luaID, 43)
	elseif quality == QUALITY.QUALITY_BLUE then
		quaName = GetGameText(luaID, 44)
	elseif quality == QUALITY.QUALITY_PINK then
		quaName = GetGameText(luaID, 45)
	elseif quality == QUALITY.QUALITY_GOLD then
		quaName = GetGameText(luaID, 46)
	elseif quality == QUALITY.QUALITY_ORANGE then
		quaName = GetGameText(luaID, 47)
	elseif quality >= QUALITY.QUALITY_RED then
		quaName = GetGameText(luaID, 48)
	else
		quaName = GetGameText(luaID, 42)
	end
	return quaName
end

--------------------------------------------------------------------
-- 属性图标：攻击、生命、防御等
--------------------------------------------------------------------
function HelperL.GetImageByProType(proType)
	if proType == 1 then   -- 生命
		return "AP-LIFE-SMALL"
	elseif proType == 2 then -- 攻击
		return "AP-ATACK-SMALL"
	elseif proType == 3 then -- 防御
		return "AP-DEF-SMALL"
	elseif proType == 4 then -- 物攻
		return "AP-ATACK-SMALL"
	elseif proType == 5 then -- 物防
		return "AP-ATACK-SMALL"
	elseif proType == 6 then -- 法攻
		return "AP-ATACK-SMALL"
	elseif proType == 7 then -- 法防
		return "AP-ATACK-SMALL"
	elseif proType == 8 then -- 暴击
		return "AP-ATACK-SMALL"
	elseif proType == 9 then -- 防暴
		return "AP-ATACK-SMALL"
	elseif proType == 10 then -- 穿透
		return "AP-ATACK-SMALL"
	elseif proType == 11 then -- 格挡
		return "AP-ATACK-SMALL"
	elseif proType == 12 then -- 命中
		return "AP-ATACK-SMALL"
	elseif proType == 13 then -- 闪避
		return "AP-ATACK-SMALL"
	elseif proType == 14 then -- 护甲
		return "AP-ATACK-SMALL"
	elseif proType == 15 then -- 破甲
		return "AP-ATACK-SMALL"
	elseif proType == 16 then -- 伤害加深
		return "AP-ATACK-SMALL"
	elseif proType == 17 then -- 减免
		return "AP-ATACK-SMALL"
	elseif proType == 18 then -- 移动速度
		return "AP-ATACK-SMALL"
	elseif proType == 19 then -- 吸血
		return "AP-ATACK-SMALL"
	elseif proType == 20 then -- 伤害反弹
		return "AP-ATACK-SMALL"
	else
		return "AP-ATACK-SMALL"
	end
end

-- 显示等待UI
function HelperL.ShowBlackWaitingUI(backType, waitTime, timeCallback, desc)
	if SceneManager.curSceneName ~= 'GameScene' or (EntityModule ~= nil and EntityModule.luaToCshape ~= nil and EntityModule.luaToCshape.GameOverMovileIsShow == true) then
		UIManager:OpenWnd(WndID.OverlayWaiting, backType, waitTime, timeCallback, desc)
	end
end

-- 关闭等待UI
function HelperL.CloseBlackWaitingUI()
	if UIManager:IsWndOpen(WndID.OverlayWaiting) then
		UIManager:CloseWndByID(WndID.OverlayWaiting)
	end
end

-- 未满8周岁用户无法获得充值消费服务。
-- 8周岁以上未满16周岁的用户，单次充值金额不得超过50元人民币，每月充值金额累计不得超过200元人民币。
-- 16周岁以上未满18周岁的用户，单次充值金额不得超过100元人民币，每月充值金额累计不得超过400元人民币。
local RECHARGE_AMOUNT = {
	{ Age1 = 12, Age2 = 16, einmal = 50,  week = 200, month = 200 },
	{ Age1 = 16, Age2 = 18, einmal = 100, week = 400, month = 400 },
}

--请求充值
function HelperL.Recharge(cardID)
	-- if not SWITCH.RECHARGE then
	-- 	HelperL.ShowMessage(TipType.FlowText, '功能未开启，看广告可获得充值！')
	-- 	return
	-- end

	local scheme = Schemes.RechargeCard:Get(cardID)
	if not scheme then
		warn('充值表为配置 cardID =', cardID)
		return
	end

	--未成年判断
	local Age = EntityModule.VerifyInfo.Age
	local einmal = scheme.FirstRMB / 100
	local week = EntityModule.VerifyInfo.ChargeWeek
	local month = EntityModule.VerifyInfo.ChargeMonth
	print('------年龄-----Age=', Age)
	print('------充值金额-----einmal=', einmal)
	print('------每周充值金额-----week=', week)
	print('------每月充值金额-----month=', month)
	if Age < 12 then
		---@type NotarizeWindowsDatq
		local data = {
			type = NotarizeWindowsType.Windows3,
			content = GetGameText(luaID, 50),
		}
		HelperL.NotarizeUI(data)
		return
	end
	if Age < 18 then
		for i, v in ipairs(RECHARGE_AMOUNT) do
			if Age >= v.Age1 and Age < v.Age2 then
				if einmal > v.einmal then
					---@type NotarizeWindowsDatq
					local data = {
						type = NotarizeWindowsType.Windows3,
						content = string.format(GetGameText(luaID, 51), v.Age1, v.Age2, v.einmal, v.month),
					}
					HelperL.NotarizeUI(data)
					return
				end
				if week > v.week or month > v.month then
					---@type NotarizeWindowsDatq
					local data = {
						type = NotarizeWindowsType.Windows3,
						content = GetGameText(luaID, 52),
					}
					HelperL.NotarizeUI(data)
					return
				end
			end
		end
	end

	local cfg = Schemes.CommonText:Get(scheme.VipScore)
	if not cfg then
		warn('广告表为配置 cardID =', cardID)
		return
	end

	local rechargeCallback = function(resultCode, content)
		local _scheme = scheme
		local _einmal = einmal
		if resultCode ~= RESULT_CODE_BASE.RESULT_COMMON_SUCCEED[1] then
			HelperL.ShowMessage(TipType.FlowText, ResultCode:GetResultCodeStr(resultCode, content))
		else
			HelperL.ShowMessage(TipType.FlowText, "充值成功！")
			local form = {}
			form["UserID"] = LoginModule:GetUserID()
			form["ActorID"] = LoginModule:GetSelectActorData().ActorID
			form["ActorLvl"] = EntityModule.hero:GetProperty(CREATURE_FIELD.CREATURE_FIELD_LEVEL)
			form["FaceValue"] = _scheme.FirstRMB
			form["RmbValue"] = _einmal
			form["RmbPay"] = _einmal
			HttpReques.SendRequest(ERequestID.SaveLog_Charge, form, nil, nil, "GET")
		end
	end

	local ok_fun = function()
		local _scheme = scheme
		LuaModule.RunLuaRequest(
			string.format('LuaRequestSeeAdvertiseUseBuff?AdverID=%s', _scheme.VipScore),
			rechargeCallback
		)
	end

	---@type NotarizeWindowsDatq
	local data = {
		type = NotarizeWindowsType.Windows1,
		titleContent = '充值提示',
		content = string.format('本次需要花费%d元购买%s，请确认是否立即购买？', einmal,scheme.CardName),
		okCallback = ok_fun,
	}
	HelperL.NotarizeUI(data)


	---暂时屏蔽
	--[[
	-- local zoneID = LoginModule:GetZoneID()
	-- local zoneData = nil
	-- for i, v in pairs(Zones) do
	-- 	if v.id == zoneID then
	-- 		zoneData = v
	-- 		break
	-- 	end
	-- end
	-- if not zoneData then
	-- 	return
	-- end

	-- local callBackUrl = {
	-- 	[GameChannel.Channel_XiongMaoXiuXian_quick] = "http://**************/charge/key?name=wdzyoppo",
	-- 	[GameChannel.Channel_Youyi] = "http://***************:8090/Charge/CallBack?name=baopomao1",
	-- }
	-- local actorID = EntityModule.hero:GetProperty(ENTITY_FIELD.ENTITY_FIELD_ID)

	-- local itemName = string.gmatch(scheme.CardName, "%](.+)%[")
	-- itemName = itemName()

	-- if GameChannel.Channel_Youyi == GameLuaAPI.ChannelID then
	-- 	local uuid = GameLuaAPI.getUUID()
	-- 	local extend_params = 'baopomao1-' .. tostring(actorID) .. '-' .. tostring(scheme.ID)
	-- 	GameLuaAPI.Recharge(
	-- 		tostring(uuid),
	-- 		--tostring(scheme.FirstRMB/10000),--todo测试FirstRMB/100
	-- 		tostring(scheme.FirstRMB / 100),
	-- 		tostring(scheme.ID),
	-- 		tostring(scheme.CardName),
	-- 		tostring(actorID),
	-- 		tostring(EntityModule.hero.name),
	-- 		tostring(zoneData.id),
	-- 		tostring(zoneData.name),
	-- 		EntityModule.hero:GetProperty(CREATURE_FIELD.CREATURE_FIELD_LEVEL),
	-- 		callBackUrl[GameLuaAPI.ChannelID] or '', extend_params)
	-- elseif GameChannel.Channel_Yimei == GameLuaAPI.ChannelID then
	-- 	local uuid = GameLuaAPI.getUUID()
	-- 	local extend_params = tostring(actorID) .. '-' .. tostring(scheme.ID) .. '-' .. uuid
	-- 	GameLuaAPI.Recharge(
	-- 		tostring(extend_params),
	-- 		--tostring(scheme.FirstRMB/10000),--todo测试FirstRMB/100
	-- 		tostring(scheme.FirstRMB / 100),
	-- 		tostring(scheme.ID),
	-- 		tostring(scheme.CardName),
	-- 		tostring(actorID),
	-- 		tostring(EntityModule.hero.name),
	-- 		tostring(zoneData.id),
	-- 		tostring(zoneData.name),
	-- 		EntityModule.hero:GetProperty(CREATURE_FIELD.CREATURE_FIELD_LEVEL),
	-- 		callBackUrl[GameLuaAPI.ChannelID] or '', extend_params)
	-- else
	-- 	GameLuaAPI.Recharge(
	-- 		scheme.FirstRMB,
	-- 		actorID,
	-- 		scheme.CardName,
	-- 		scheme.CardName,
	-- 		EntityModule.hero.name,
	-- 		tostring(scheme.coolpayID),
	-- 		itemName,
	-- 		actorID .. '-' .. scheme.ID,
	-- 		scheme.LenovoID,
	-- 		1,
	-- 		callBackUrl[GameLuaAPI.ChannelID] or '',
	-- 		301,
	-- 		301,
	-- 		scheme.IOSID)
	-- end
	-- HelperL.ShowBlackWaitingUI(1, 1, nil, '请稍等')
	--]]
end

--计算间隔天数
function HelperL.CalculationIntervalDays(startTime, curTime)
	if startTime > curTime or startTime == 0 then return 0 end
	local startDate = os.date('*t', startTime)
	startTime = startTime - startDate.hour * 60 * 60 - startDate.min * 60 - startDate.sec
	local curDate = os.date('*t', curTime)
	curTime = curTime - curDate.hour * 60 * 60 - curDate.min * 60 - curDate.sec
	return math.floor((curTime - startTime) / 86400) + 1
end

-- 计算属性ID和等级
function HelperL.GetPropEffectIDAndLevel(effectID)
	effectID = tonumber(effectID) or 0
	if effectID == 0 then
		return 0, 0
	end
	return math.floor(effectID / 10000), (effectID % 10000)
end

--计算属性值
function HelperL.GetPropEffectValue(effectItem, level)
	if not effectItem then
		return 0
	end
	level = level or 0
	local LevelValueRate = effectItem.LevelValueRate or 0
	local EffectParam = effectItem.EffectParam or 0
	if AttributeShowType[effectItem.EffectType] == 2 then
		return (level * LevelValueRate + EffectParam)
	end
	return math.floor(level * LevelValueRate + EffectParam)
end

-- 是否购买过指定充值卡
function HelperL.HadBoughtCardID(cardID)
	local rechargeCard = Schemes.RechargeCard:Get(cardID)
	if not rechargeCard then
		return false
	end
	return (HeroDataManager:GetLogicBit(rechargeCard.SaveParam1, rechargeCard.SaveParam2) > 0)
end

HelperL.noAdRechargeCardID = 24
-- 是否可去除广告
function HelperL.IsNoAD()
	for i, v in ipairs(Schemes.PrivilegeCard.items) do
		if HelperL.HadBoughtCardID(v.Price) then
			return true
		end
	end
	return false
end

-- 获取开服天数
function HelperL.GetServerOpenDays()
	if not EntityModule.hero then
		return 0
	end

	local serverOpenTime = EntityModule.hero.ServerOpenTime
	local nowTime = HelperL.GetServerTime()
	local serverOpenTM = os.date('*t', serverOpenTime)
	local nowTM = os.date('*t', nowTime)
	serverOpenTime = serverOpenTime - serverOpenTM.hour * 60 * 60 - serverOpenTM.min * 60 - serverOpenTM.sec
	nowTime = nowTime - nowTM.hour * 60 * 60 - nowTM.min * 60 - nowTM.sec

	local result = math.floor((nowTime - serverOpenTime) / 86400) + 1
	return result
end

-- 获取开服周数
function HelperL.GetServerOpenWeeks()
	if not EntityModule.hero then
		return 0
	end

	local serverOpenTime = EntityModule.hero.ServerOpenTime
	local nowTime = HelperL.GetServerTime()
	local serverOpenTM = os.date('*t', serverOpenTime)
	local nowTM = os.date('*t', nowTime)
	serverOpenTime = serverOpenTime - serverOpenTM.hour * 60 * 60 - serverOpenTM.min * 60 - serverOpenTM.sec
	nowTime = nowTime - nowTM.hour * 60 * 60 - nowTM.min * 60 - nowTM.sec
	local openDays = math.floor((nowTime - serverOpenTime) / 86400) + 1
	local plusDay = (serverOpenTM.wday == 1) and 6 or (serverOpenTM.wday - 2)
	openDays = openDays + plusDay
	return math.floor((openDays - 1) / 7) + 1;
end

-- 获取开服月数
function HelperL.GetServerOpenMonths()
	if not EntityModule.hero then
		return 0
	end

	local serverOpenTime = EntityModule.hero.ServerOpenTime
	local nowTime = HelperL.GetServerTime()
	local serverOpenTM = os.date('*t', serverOpenTime)
	local nowTM = os.date('*t', nowTime)
	local result = (nowTM.year - serverOpenTM.year - 1) * 12 + (12 - serverOpenTM.month + 1) + (nowTM.month)
	if serverOpenTM.day > 15 and result > 1 then
		result = result - 1
	end
	return result
end

--是否购买了月卡
function HelperL.IsBuyPrivilegeCard(id)
	local nowtime = HelperL.GetServerTime()
	local cfg = Schemes.PrivilegeCard:Get(id)
	if cfg then
		local endtime = HeroDataManager:GetLogicData(cfg.SaveLogicID)
		print('----是否购买了月卡-------', cfg.SaveLogicID, endtime)
		if endtime then
			if nowtime < endtime then
				return true
			end
		end
	end
	return false
end

--月卡还剩多少天
function HelperL.PrivilegeCardShengYu(id)
	local nowtime = HelperL.GetServerTime()
	local cfg = Schemes.PrivilegeCard:Get(id)
	if cfg then
		local endtime = HeroDataManager:GetLogicData(cfg.SaveLogicID)
		if endtime then
			return (endtime - nowtime) / 3600 / 24
		end
	end
	return 0
end

--是否购买了月卡
function HelperL.IsBuyPrivilege()
	return HelperL.IsBuyPrivilegeCard(1) or HelperL.IsBuyPrivilegeCard(2)
end

--- 是否购买了月卡
function HelperL.IsBuyYueKa()
	return HelperL.IsBuyPrivilegeCard(1)
end

--- 是否购买了超级月卡
function HelperL.IsBuyChaoJiYueKa()
	return HelperL.IsBuyPrivilegeCard(2)
end

--- 是否购买了免广告卡
function HelperL.IsBuyMianGaungGaoYueKa()
	return HelperL.IsBuyPrivilegeCard(3)
end

function HelperL.SDKSubmitRoleData(eventType, actorID, actorName)
	local zoneID = LoginModule:GetZoneID()
	local zoneData = nil
	for i, v in pairs(Zones) do
		if v.id == zoneID then
			zoneData = v
			break
		end
	end
	if not zoneData then
		return
	end
	--local o = EntityModule.hero
	local o = nil
	if o then
		local keyname = ""
		if eventType == SDK_SUBMIT_ROLEDATA_EVENTTYPE.ENTER_GAME then
			keyname = "enterServer"
		elseif eventType == SDK_SUBMIT_ROLEDATA_EVENTTYPE.LEVEL_UP then
			keyname = "levelup"
		end

		if eventType == SDK_SUBMIT_ROLEDATA_EVENTTYPE.ENTER_GAME or eventType == SDK_SUBMIT_ROLEDATA_EVENTTYPE.LEVEL_UP then
			GameLuaAPI.submitRoleData(keyname, tostring(zoneData.id),
				zoneData.name,
				tostring(o:GetProperty(ENTITY_FIELD.ENTITY_FIELD_ID)),
				o.name,
				o:GetProperty(CREATURE_FIELD.CREATURE_FIELD_LEVEL),
				o.ActorCreateTime,
				0,
				o:GetProperty(PLAYER_FIELD.PLAYER_FIELD_VIPLEVEL),
				'',
				0,
				eventType)
		end
	else
		if eventType == SDK_SUBMIT_ROLEDATA_EVENTTYPE.SELECT_SERVER then
			GameLuaAPI.submitRoleData("", tostring(zoneData.id),
				zoneData.name,
				'0',
				'',
				0,
				0,
				0,
				0,
				'',
				0,
				SDK_SUBMIT_ROLEDATA_EVENTTYPE.SELECT_SERVER)
		elseif eventType == SDK_SUBMIT_ROLEDATA_EVENTTYPE.CREATE_ROLE then
			if actorID and actorName then
				HelperL.DeleteGuideData()
				local serverTime = HelperL.GetServerTime()
				--print('zoneData.id:'..tostring(zoneData.id))
				--print('zoneData.name:'..tostring(zoneData.name))
				--print('actorID:'..tostring(actorID))
				--print('actorName:'..tostring(actorName))
				--print('serverTime:'..tostring(serverTime))
				GameLuaAPI.submitRoleData("createrole",
					tostring(zoneData.id),
					tostring(zoneData.name),
					tostring(actorID),
					tostring(actorName),
					1,
					serverTime,
					0,
					1,
					'',
					0,
					SDK_SUBMIT_ROLEDATA_EVENTTYPE.CREATE_ROLE)
			end
		end
	end
end

function HelperL.DeleteGuideData()
	local beginIndex = GuideManager.EventType.BeginGameGuide
	local endIndex = GuideManager.EventType.PatrolGuide
	for i = beginIndex, endIndex do
		local newGuide = 'newGuide' .. i
		PlayerPrefsManager:DeleteKey(newGuide)
		local resultGuide = 'Result' .. i
		PlayerPrefsManager:DeleteKey(resultGuide)
		local guideCount = 'GuideCount' .. i
		PlayerPrefsManager:DeleteKey(guideCount)
	end
end

--计算2个时间相差多少天
function HelperL.getDateNum(timeNow, timeNext)
	local ret = 0
	if timeNow and timeNext then
		local now = os.date("*t", timeNow)
		local next = os.date("*t", timeNext)

		if now and next then
			local num1 = os.time({ year = now.year, month = now.month, day = now.day })
			local num2 = os.time({ year = next.year, month = next.month, day = next.day })
			if num1 and num2 then
				ret = math.abs(num1 - num2) / (3600 * 24)
			end
		end
	end
	return ret + 1
end

--获取今天星期几
---@param time? integer 默认为服务器时间
---@return integer 1-7
function HelperL.getWeekDay(time)
	time = time or HelperL.GetServerTime()
	local weekdays = os.date("*t", time).wday
	if weekdays == 1 then
		weekdays = 7
	else
		weekdays = weekdays - 1
	end
	return weekdays
end

---午夜倒计时
function HelperL.countdown_to_midnight(now)
	now = now or os.time()
	local time = os.date("*t", now)
	local toTime = os.time({ year = time.year, month = time.month, day = time.day + 1, hour = 0, min = 0, sec = 0 })
	return toTime - now
end

-- 更换出战后服务器不会推送新的细胞,要重新登陆才会用，这里临时保存
HelperL.clientTempAirplaneUID = 0
--获取圣物的生物ID(细胞，如果没有装备就是用这个装备的技能)
function HelperL.GetWeaponEquipID()
	local entity = EntityModule:GetEntityList()[HelperL.clientTempAirplaneUID]
	if entity ~= nil then
		local itemID = entity:GetProperty(ENTITY_FIELD.ENTITY_FIELD_ID)
		return itemID
	end
	return 0
end

-- 获取第一火之纹章的ID
function HelperL.GetMainWeaponID()
	local eUID = SkepModule.GetEquipSkep()[EQUIP_TYPE.EQUIP_TYPE_NORMALWEAPON]
	local entity = EntityModule:GetEntity(eUID)
	if entity ~= nil then
		local itemID = entity:GetProperty(ENTITY_FIELD.ENTITY_FIELD_ID)
		return itemID
	end
	return 0
end

-- 获取第二火之纹章的ID
function HelperL.GetSecondWeaponID()
	local eUID = SkepModule.GetEquipSkep()[EQUIP_TYPE.EQUIP_TYPE_SHOES]
	local entity = EntityModule:GetEntity(eUID)
	if entity ~= nil then
		local itemID = entity:GetProperty(ENTITY_FIELD.ENTITY_FIELD_ID)
		return itemID
	end
	return 0
end

-- 获取伙伴的ID（是装备的一种）
function HelperL.GetSidekickID()
	local eUID = SkepModule.GetEquipSkep()[EQUIP_TYPE.EQUIP_TYPE_SPIRIT1]
	local entity = EntityModule:GetEntity(eUID)
	if entity ~= nil then
		local itemID = entity:GetProperty(ENTITY_FIELD.ENTITY_FIELD_ID)
		--print("这个是玩家带的宠物的id" .. itemID)
		return itemID
	end
	return 0
end

--------------------------------------------------------------------
-- 显示物品查看界面
--------------------------------------------------------------------
function HelperL.OnShowTips(GoodTips)
	UIManager:OpenWnd(WndID.GoodTips, GoodTips.itemID)
end

-- 在数组中查找值的索引位置。如果key不为空，则是通过每个成员的属性名去取值。
function HelperL.ArrayIndexOf(ary, val, key)
	for i, v in ipairs(ary) do
		v = dkjsonHelper.decode(v); --cs过来的json需要
		if (key == nil) then
			if (v == val) then
				return i
			end
		else
			if (v[key] == val) then
				return i
			end
		end
	end
	return -1
end

-- number数组是否为空或仅包含一个0值
function HelperL.ArrayIsEmptyOrZero1(ary)
	if ary == nil or #ary == 0 then
		return true;
	end

	if (#ary > 1) then return false; end

	-- 运行到这里则数组只有一项
	return ary[1] == 0;
end

--- 如果子项不存在数组中,则加入数组结尾。如果key不为空，则是通过每个成员的属性名去判断。
---@param ary table
---@param val integer	单个子项
function HelperL.ArrayAddIfNotExists(ary, val, key)
	if (HelperL.ArrayIndexOf(ary, val, key) == -1) then
		table.insert(ary, val);
	end
end

--- 如果子项不存在数组中,则加入数组结尾。如果key不为空，则是通过每个成员的属性名去判断。
---@param ary table
---@param items table	多个子项的数组
function HelperL.ArrayAddRangeIfNotExists(ary, items, key)
	for _, v in ipairs(items) do
		HelperL.ArrayAddIfNotExists(ary, v, key)
	end
end

--- 将C#的数组转为Lua的数组。(C#从0开始，Lua从1开始)
---@param csAry integer
function HelperL.CsArray2LuaArray(csAry)
	local rtn = {};
	for i, v in ipairs(csAry) do
		rtn[i + 1] = csAry[i];
	end
	return rtn;
end

--- (浅)复制t
---@param t table
function HelperL.tableClone(t)
	local rtn = {};
	for k, v in pairs(t) do
		rtn[k] = v;
	end
	return rtn;
end

---comment 对象(或数组)中是否存在指定键
---@param tab table
---@param key integer
---@return boolean
function HelperL.tableContainKey(tab, key)
	for k, _ in pairs(tab) do
		if k == key then
			return true
		end
	end
	return false
end

---comment 对象(或数组)中是否存在指定值
---@param tab table
---@param val integer
---@param key integer 用tab成员的Key属性代替成员去判断相等
---@return boolean
function HelperL.tableContain(tab, val, key)
	for k, v in pairs(tab) do
		if key == nil then
			if v == val then
				return true
			end
		else
			if v[key] == val then
				return true
			end
		end
	end
	return false
end

--- 在table的成员中 按成员的属性名 查找第一个属性值相等的成员
---@param o table
---@param pName integer
---@param pValue integer
function HelperL.tableFindFirstMemberByKey(o, pName, pValue)
	for k, v in pairs(o) do
		if (pValue == nil and v[pName] == nil) then
			return v
		elseif (pValue ~= nil and v[pName] == pValue) then
			return v
		end
	end
	return nil
end

-- 从数组(子项类型为table)中,按权重(Priority)，随机取一条
function HelperL.PriorityRandom(ary)
	-- 数组[index, value]
	local rtn = { -1 };

	-- 取权重之和
	local pMax = 0;
	for _, v in ipairs(ary) do
		pMax = pMax + v.Priority;
	end

	-- [1,pMax]
	-- local rnd = math.random(pMax);
	local rnd = RandomNum.RandomInt(1, pMax + 1);

	-- 按顺序累计权重和,当它>=随机数的时候,就是随机结果
	local sumWeighting = 0;
	for i, v in ipairs(ary) do
		sumWeighting = sumWeighting + v.Priority;
		if (sumWeighting >= rnd) then
			rtn = { i, v }
			break;
		end
	end

	return rtn;
end

--获取装备子类型 1-16
function HelperL.GetEquipGemPart(equipType, num)
	local index = 0
	if equipType <= 6 then
		index = equipType
	elseif equipType == 10 then
		index = 7
	elseif equipType == 11 then
		index = 8
	end
	index = index + (num - 1) * 8
	return index
end

function HelperL.RechargeCallback(msg)
	if msg then print('msg:' .. msg) end
	local cardID = tonumber(msg)
	if cardID == 0 then --充值失败
		EventManager:Fire(EventID.OnRechargeSDKCallback, cardID, 0)
		return
	end
	local rechargeItem = Schemes.RechargeCard:Get(cardID)
	if rechargeItem then
		EventManager:Fire(EventID.OnRechargeSDKCallback, cardID, rechargeItem.LenovoID)
	end
end

-- 是否弹窗过（福利礼包、月卡、基金）
HelperL.hadPopUpDialogBattle = false
HelperL.needPopUpAffterBattle = false

function HelperL.CheckPopUpAffterBattle()
	-- local maxStage = HeroDataManager:GetLogicData(LOGIC_DATA.DATA_SOCIETY_ECTYPETOWER_CURSTAGE)
	-- if maxStage == 3 then
	-- 	-- HelperL.hadPopUpDialogBattle = true
	-- 	-- HelperL.PopUpBuyDialog()

	-- 	-- 福利礼包
	-- 	if not HelperL.HadBoughtCardID(20) then
	-- 		UIManager:OpenWnd(WndID.FirstRecharge, 20)
	-- 	end
	-- end
end

function HelperL.PopUpBuyDialog()
	-- -- 福利礼包
	-- local isBuy = HelperL.HadBoughtCardID(20)
	-- if not isBuy then
	-- 	UIManager:OpenWnd(WndID.FirstRecharge, 20)
	-- 	return
	-- end
	-- -- 月卡
	-- isBuy = HelperL.HadBoughtCardID(80)
	-- if not isBuy then
	-- 	UIManager:OpenWnd(WndID.MonthCard)
	-- 	return
	-- end
	-- -- 基金
	-- isBuy = HelperL.HadBoughtCardID(71)
	-- if not isBuy then
	-- 	isBuy = HelperL.HadBoughtCardID(72)
	-- end
	-- if not isBuy then
	-- 	UIManager:OpenWnd(WndID.SevenDayInvestment)
	-- 	return
	-- end
end

-- 数字转换
function HelperL.ExChangeNum(num)
	if num >= 1000000000 then
		return HelperL.Round(num / 1000000000, 2) .. 'B'
	elseif num >= 1000000 then
		return HelperL.Round(num / 1000000, 2) .. 'M'
	elseif num >= 1000 then
		return HelperL.Round(num / 1000, 2) .. 'K'
	end
	return num
end

-- 数字单位转换
function HelperL.GetChangeNum(num)
	--检测是否是数字
	num = tonumber(num)
	if num == nil then return 0 end

	--单位
	local units = {
		[3] = '千',
		[4] = '万',
		[8] = '亿',
		[12] = 'F',
		[16] = 'G',
		[20] = 'H',
		[24] = 'I',
		[28] = 'J',
		[32] = 'K',
	}

	--去除小数点、负号，获取数字长度
	local len = string.len(math.abs(math.floor(num))) - 1
	if len >= 4 then
		for i = len, 4, -1 do
			if units[i] then
				return HelperL.Round(num / (10 ^ i), 2) .. units[i]
			end
		end
	end
	return num
end

-- 数字单位转换
function HelperL.GetChangeNumWan(num)
	--检测是否是数字
	num = tonumber(num)
	if num == nil then return 0 end

	--单位
	local units = {
		[3] = '千',
		[4] = '万',
		[8] = '亿',
		[12] = 'F',
		[16] = 'G',
		[20] = 'H',
		[24] = 'I',
		[28] = 'J',
		[32] = 'K',
	}

	--去除小数点、负号，获取数字长度
	local len = string.len(math.abs(math.floor(num))) - 1
	if len >= 4 then
		for i = len, 4, -1 do
			if units[i] then
				return HelperL.Round(num / (10 ^ i), 2) .. units[i]
			end
		end
	end
	return num
end

---保留小数点位数
---@param num integer
---@param precision? integer 小数点位数，小于等于0取整，默认：0
---@param isRound? boolean 启用四舍五入
---@return number
function HelperL.Round(num, precision, isRound)
	--检测是否是数字
	num = tonumber(num) or 0
	if num == 0 then return 0 end

	--转换为字符串后检查是否是整数
	if tostring(num):find("%.") == nil then return num end

	--取整
	precision = math.floor(tonumber(precision) or 0)
	if precision <= 0 then return math.floor(num) end

	local mult = 10 ^ precision
	--启用四舍五入
	if isRound == true then
		return (math.floor(num * mult + 0.5) / mult)
	end
	--正常保留小数点位数
	return (math.floor(num * mult) / mult)
end

--转行替换
function HelperL.GetContenBRText(content)
	if not content then return '' end
	local str = string.gsub(content, '<br>', '\n')
	return str
end

--友盟统计
function HelperL.SendUMengEvent(event_type, param1, param2, param3)
	if GameChannel.Channel_Yimei == GameLuaAPI.ChannelID then
		local action_name = 'Other'
		if event_type == UMENG_EVENT_TYPE.USER_REGISTER then
			action_name = 'UserRegister'
		elseif event_type == UMENG_EVENT_TYPE.CUR_STAGE then
			action_name = 'MaxStage'
		elseif event_type == UMENG_EVENT_TYPE.BATTLE_COUNT then
			action_name = 'BattleCount'
		elseif event_type == UMENG_EVENT_TYPE.BATTLE_RESULT_SUCCESS then
			action_name = 'BattleResultSuccess'
		elseif event_type == UMENG_EVENT_TYPE.BATTLE_RESULT_FAILD then
			action_name = 'BattleResultFaild'
		end
		GameLuaAPI.SendUMengEvent(action_name, param1, param2, param3)
	end
end

---获取自适应缩放
---param type? integer 1筛选最小比例适配,2筛选最大比例适配,3宽度缩放比例,4高度缩放比例,
---5筛选最小放大比例,6筛选最大放大比例,默认：类型1
function HelperL.GetAdaptScale(type)
	--宽度 缩放比例
	local width = UnityEngine.Screen.width / ScreenSize.width
	--高度 缩放比例
	local height = UnityEngine.Screen.height / ScreenSize.height
	--最小放大
	local scale_w = width / height
	--最大放大
	local scale_h = height / width
	--最小缩放
	local minScale = width < height and width or height
	--最大缩放
	local maxScale = width > height and width or height
	if type == 1 then  --1筛选最小比例适配
		return minScale
	elseif type == 2 then --2筛选最大比例适配
		return maxScale
	elseif type == 3 then --3宽度缩放比例
		return width
	elseif type == 4 then --4高度缩放比例
		return height
	elseif type == 5 then --5筛选最小放大比例
		return scale_w < scale_h and scale_w or scale_h
	elseif type == 6 then --6筛选最大放大比例
		return scale_w > scale_h and scale_w or scale_h
	end
	return 1
end

---UI自适应 等比缩放 (UI适配)
---param root any
---param type ? integer 1筛选最小比例适配,2筛选最大比例适配,3宽度缩放比例,4高度缩放比例,
---5筛选最小放大比例,6筛选最大放大比例,默认：类型1
function HelperL.AdaptScale(root, type)
	if not tolua.isnull(root) then
		local scale = HelperL.GetAdaptScale(type)
		root.transform.localScale = Vector3.one * scale
	end
end

--判断是刘海屏
function HelperL.IsNotchDetection()
	-- return (UnityEngine.Screen.height / UnityEngine.Screen.width) > 2

	local NotchPhoneModel = require "NotchPhoneModel"
	return NotchPhoneModel.IsNotchDetection()
end

---缩放并自适应宽度
---@param root integer
function HelperL.AdaptScale_Width(root, isScale)
	if not tolua.isnull(root) then
		local itemRect = root:GetRectTransform()
		if itemRect then
			local scale = HelperL.GetAdaptScale(1)
			local width = HelperL.GetAdaptScale(3)
			if isScale == true then
				itemRect.localScale = Vector3.one * scale
			end
			if width > scale then
				itemRect.sizeDelta = Vector2(itemRect.sizeDelta.x * (width / scale), itemRect.sizeDelta.y)
			end
		end
	end
end

---缩放并自适应宽度
---@param root integer
function HelperL.AdaptScale_Height(root, isScale)
	if not tolua.isnull(root) then
		local itemRect = root:GetRectTransform()
		if itemRect then
			local scale = HelperL.GetAdaptScale(1)
			local height = HelperL.GetAdaptScale(4)
			if isScale == true then
				itemRect.localScale = Vector3.one * scale
			end
			if height > scale then
				itemRect.sizeDelta = Vector2(itemRect.sizeDelta.x, itemRect.sizeDelta.y * (height / scale))
			end
		end
	end
end

---缩放并自适应大小
---@param root integer
function HelperL.AdaptScaleAndSize(root)
	if not tolua.isnull(root) then
		local itemRect = root:GetRectTransform()
		if itemRect then
			local scale = HelperL.GetAdaptScale(1)
			local width = HelperL.GetAdaptScale(3)
			local height = HelperL.GetAdaptScale(4)
			local sizeDelta_x = itemRect.sizeDelta.x
			local sizeDelta_y = itemRect.sizeDelta.y
			--等比缩放适配
			itemRect.localScale = Vector3.one * scale
			--适配宽度
			if width > scale then
				sizeDelta_x = sizeDelta_x * (width / scale)
			end
			--适配高度
			if height > scale then
				sizeDelta_y = sizeDelta_y * (height / scale)
			end
			itemRect.sizeDelta = Vector2(sizeDelta_x, sizeDelta_y)
		end
	end
end

-- 获取属性字符串
function HelperL.GetPropEffectString(propType, propValue)
	local str = GetAttributeTypeDesc(propType)
	if not propValue or str == '' then
		warn('找不到属性类型 ' .. tostring(propType))
		return ''
	end
	if AttributeShowType[propType] == 1 then
		return GetAttributeTypeDesc(propType) .. " +" .. propValue
	elseif AttributeShowType[propType] == 2 then
		return string.format('%s +%s%%', GetAttributeTypeDesc(propType), math.floor(propValue / 100))
	end

	warn('未定义的属性显示类型 ' .. tostring(propType))
	return ''
end

HelperL.BeginLogin = false --开始登录

---获取广告状态
---@param adverID integer|string 广告ID
---@return integer 1可观看，2冷却中，3今日次数已用完，4任务已完成/不存在
function HelperL.GetAdverState(adverID)
	local commonText = Schemes.CommonText:Get(adverID)
	if not commonText then return 4 end
	--判断总次数(广告任务是否已完成)
	local num = HeroDataManager:GetLogicByte(commonText.Param2, commonText.Param3)
	if commonText.ToTime ~= 0 and num >= commonText.ToTime then return 4 end
	--判断今日次数
	num = HeroDataManager:GetLogicByte(commonText.Param4, commonText.Param5)
	if num >= commonText.DayTime then return 3 end
	--判断时间间隔
	if EntityModule.hero.buffLC:HasBuff(commonText.Param1) then
		local time = EntityModule.hero.buffLC:GetBuffLeftTime(commonText.Param1)
		if time > 0 then return 2 end
	end
	return 1
end

---通关充值卡ID获取广告状态
---@param cardID integer 充值卡ID
---@return integer 1可观看，2冷却中，3今日次数已用完，4任务已完成/不存在
function HelperL.GetAdverStateByCardID(cardID)
	local state = 4
	local rechargeCard = Schemes.RechargeCard:Get(cardID)
	if rechargeCard then
		state = HelperL.GetAdverState(rechargeCard.Description)
	end
	return state
end

---广告状态消息提示
---@param adverID integer
---@param isLog? boolean 开启提示
---@return boolean 广告状态是否可观看
function HelperL.GetAdverHint(adverID, isLog)
	local state = HelperL.GetAdverState(adverID)
	local str = GetGameText(luaID, 40)
	if state == 2 then  --2冷却中
		str = GetGameText(luaID, 37)
	elseif state == 3 then --3今日次数已用完
		str = GetGameText(luaID, 38)
	elseif state == 4 then --4任务已完成
		str = GetGameText(luaID, 39)
	end
	if isLog == true and state ~= 1 then
		HelperL.ShowMessage(TipType.FlowText, str)
	end
	return state ~= 1
end

---请求充值，先判断在充值
---@param cardID integer 充值卡ID
function HelperL.RechargeAndJudge(cardID)
	local isBuyCard = HelperL.HadBoughtCardID(cardID)
	if isBuyCard then
		HelperL.ShowMessage(TipType.FlowText, CommonTextID.IS_PURCHASE)
	else
		HelperL.Recharge(cardID)
	end
end

---通过物品ID获取充值卡ID
---@param goodsID integer 物品ID
---@param fun ?fun(cardID:number):boolean 自定义充值卡判断
---@return integer
function HelperL.GetGoodsIDByCardID(goodsID, fun)
	local cfg = Schemes.Medicament:Get(goodsID)
	if cfg then
		for _, v in ipairs(cfg.FastUpgradeCardID) do
			if fun then --自定义判断
				if fun(v) then
					return v
				end
			elseif HelperL.GetAdverStateByCardID(v) == 1 then --有充值卡可看广告
				return v
			end
		end
	else
		warn('物品不存在 goodsID=', goodsID)
	end
	return 0
end

---通用商店跳转
---@param goodsID integer 物品ID
---@return boolean 商店跳转是否成功
function HelperL.CommonShop(goodsID)
	local cardID = HelperL.GetGoodsIDByCardID(goodsID)
	if cardID == 0 then return false end

	--商店跳转成功
	if cardID == 20 then --福利礼包界面
		UIManager:OpenWnd(WndID.FirstRecharge)
	else              --通用商店界面
		UIManager:OpenWnd(WndID.RecommendCommodities, cardID)
	end
	return true
end

---购买物品跳转
---@param goodsID integer 物品ID
---@return boolean 商店跳转是否成功
function HelperL.BuyGoodsSkip(goodsID)
	--食物
	if goodsID == 2 then
		UIManager:OpenWnd(WndID.PurchasePhysicalPower)
	else
		return HelperL.CommonShop(goodsID)
	end
	return true
end

---判断物品不足
---@param goodsID integer 物品ID
---@param amount integer 物品数量
---@param isOpenShop? boolean 是否开启--商店跳转，默认：true
---@param isOpenLog? boolean 是否开启--提示框(优先级低于--商店跳转)，默认：true
---@return boolean
function HelperL.IsLackGoods(goodsID, amount, isOpenShop, isOpenLog)
	amount = tonumber(amount) or 0
	--物品数量足够
	if SkepModule:GetGoodsCount(goodsID) >= amount then
		return false
	end

	--没开启商店跳转，就使用文字提示
	if isOpenShop == false or not HelperL.BuyGoodsSkip(goodsID) then
		if isOpenLog ~= false then
			local cfg = Schemes:GetGoodsConfig(goodsID)
			HelperL.ShowMessage(TipType.FlowText, cfg.GoodsName .. GetGameText(luaID, 41))
			print('-----------判断物品不足=', "#加物品 " .. goodsID .. " " .. amount + 50, debug.traceback())
		end
	end

	return true
end

--- 获取物品名称
---@param goodsID integer 物品ID
---@return string
function HelperL.GetGoodsName(goodsID)
	local cfg
	if HelperL.IsEuipType(goodsID) then
		cfg = Schemes.Equipment:Get(goodsID)
	else
		cfg = Schemes.Medicament:Get(goodsID)
	end
	if cfg then
		return cfg.GoodsName or ''
	end
	return ''
end

HelperL.InviteActorLevel = 30
---邀请玩家数量
---@param level? integer
---@return integer
function HelperL.InviteNum(level)
	if not level then
		level = HelperL.InviteActorLevel
	end
	local num = 0
	for i, v in ipairs(EntityModule.InviteDataList) do
		if v.ActorLevel >= level then
			num = num + 1
		end
	end
	return num
end

---铜钱加成提示框
---@param ok_fun ?fun(isCheck:boolean) 确认事件
---@param cancel_fun ?fun(isCheck:boolean) 取消事件
function HelperL.IsShowOkWindow(ok_fun, cancel_fun)
	-- local luaID2 = ('UIDisplayReward')
	-- local commonText = Schemes.CommonText:Get(114)
	-- local num = HeroDataManager:GetLogicByte(commonText.Param4, commonText.Param5)
	-- local level = EntityModule.hero:GetProperty(CREATURE_FIELD.CREATURE_FIELD_LEVEL)
	-- if level >= 10 and num < commonText.DayTime then
	-- 	commonText = Schemes.CommonText:Get(UIMainBuffDataList[1].adID)
	-- 	local isBuff = EntityModule.hero.buffLC:HasBuff(commonText.Param1)
	-- 	if not isBuff then
	-- 		---@type NotarizeWindowsDatq
	-- 		local data = {
	-- 			type = NotarizeWindowsType.Windows1,
	-- 			title = GetGameText(luaID2, 6),
	-- 			content = GetGameText(luaID2, 5),
	-- 			okCallback = function(isCheck)
	-- 				local _ok_fun = ok_fun
	-- 				if _ok_fun then _ok_fun(isCheck) end
	-- 				if isCheck then
	-- 					LuaModule.RunLuaRequest(string.format('LuaRequestSeeAdvertiseUseBuff?AdverID=%s', 114))
	-- 				end
	-- 				UIManager:OpenWnd(WndID.Buff)
	-- 			end,
	-- 			cancelCallback = function(isCheck)
	-- 				local _cancel_fun = cancel_fun
	-- 				if _cancel_fun then _cancel_fun(isCheck) end
	-- 				if isCheck then
	-- 					LuaModule.RunLuaRequest(string.format('LuaRequestSeeAdvertiseUseBuff?AdverID=%s', 114))
	-- 				end
	-- 			end,
	-- 		}
	-- 		HelperL.NotarizeUI(data)
	-- 		return
	-- 	end
	-- end
	if cancel_fun then cancel_fun(false) end
end

---创建定时器
---@param func fun() 执行事件
---@param duration number 执行时间间隔(单位/秒)
---@param loop number 执行次数，-1一直执行
---@param endCallBack? fun() 结束回调
function HelperL.NewTimer(func, duration, loop, endCallBack)
	---@class Timer_2 定时器封装
	local m = {}
	--执行事件
	m.func = func
	--执行时间间隔(单位/秒)
	m.duration = duration
	--执行次数，-1一直执行
	m.loop = loop
	--结束回调
	m.endCallBack = endCallBack
	--结束回调2
	m.endCallBack2 = function()
		if m.endCallBack then
			m.endCallBack()
		end
		m.Reset()
		m.running = false
	end
	--定时器
	m.timer = Timer.New(m.func, m.duration, m.loop, nil, nil, m.endCallBack2)
	--执行中
	m.running = false
	---重置
	---@param func2? fun() 执行事件
	---@param duration2? number 执行时间间隔(单位/秒)
	---@param loop2? number 执行次数，-1一直执行
	m.Reset = function(func2, duration2, loop2)
		m.timer:Stop()
		m.timer:Reset(func2 or m.func, duration2 or m.duration, loop2 or m.loop, nil, nil, m.endCallBack2)
	end
	--启动
	m.Start = function()
		--防止重复执行
		if m.running then return end
		m.running = true
		m.Reset()
		m.timer:Start()
	end
	--停止
	m.Stop = function()
		m.Reset()
		m.running = false
	end
	return m
end

---获取物品加成
---@param goodsID integer 物品ID
function HelperL.GetGoodsAdd(goodsID)
	local addNum = 1
	if goodsID == 1 then --经验加成
		local pow = ActorProp.GetPlayerFieldsByClient()
		addNum = 1 + ((tonumber(pow.Result.AcquireExp) or 0) / 10000)
	elseif goodsID == 4 then --银币加成
		local buffID = Schemes.CommonText:Get(100).Param1
		local bool = EntityModule.hero.buffLC:HasBuff(buffID)
		if bool then
			local pow = ActorProp.GetPlayerFieldsByClient()
			addNum = 1 + ((tonumber(pow.Result.AcquireGold) or 0) / 10000)
		end
	end
	return addNum
end

--- 按C++时间戳(数值)获取对应的C#时间(DateTime)
---@param cppTicks integer
function HelperL.ToDateTimeFromCppDateTime(cppTicks)
	local cppBegin = System.DateTime.New(1970, 1, 1, 8, 0, 0);
	local rtn = cppBegin.AddSeconds(cppTicks);
	return rtn;
end

--- 获取C#时间对应的C++时间戳(数值)
---@param dt integer
function HelperL.GetCppDateTime(dt)
	local cppBegin = System.DateTime.New(1970, 1, 1, 8, 0, 0);
	local ts = System.DateTime.__sub(dt, cppBegin);
	return ts.TotalSeconds;
end

--- (C#时间戳)转(C++时间戳)
---@param time integer
function HelperL.GetCppDateTime2(time)
	local dt = DateTime.New(time)
	local cppBegin = System.DateTime.New(1970, 1, 1, 8, 0, 0);
	local ts = System.DateTime.__sub(dt, cppBegin);
	return ts.TotalSeconds;
end

---打开网页
function HelperL.OpenURL(url)
	if not url then return end
	url = string.gsub(url, " ", "")
	if url == '' then return end
	if not string.find(url, "https://") and string.find(url, "http://") then return end
	Helper.OpenURL(url)
end

--直接发放奖励的验证key
function HelperL.GetDirectPrizeKeys()
	if #LoginModule.getDirectPrizeKeys < 100 then
		return 0
	end
	local curTime = HelperL.GetServerTime()
	local index = tonumber(curTime) % 100 + 1
	local key = LoginModule.getDirectPrizeKeys[index]
	local actorID = EntityModule.hero:GetProperty(ENTITY_FIELD.ENTITY_FIELD_ID)
	local C = (actorID % 1000) * key
	return C
end

---直接向服务器请求发放奖励或物品
---@param awardType integer 奖励类型，1：物品列表，2：奖励ID
---@param goodInfo string|integer 发放信息
---@param costInfo ?string 消耗信息
---@param fun ?fun(resultCode:integer, content:string) 回调函数
---@param isDisplayReward ?boolean 是否显示奖励，默认：true
function HelperL.RequestDirectGiveGoods(awardType, goodInfo, costInfo, fun, isDisplayReward)
	if goodInfo == nil or goodInfo == '' then
		goodInfo = '0'
	end
	if costInfo == nil or costInfo == '' then
		costInfo = '0'
	end
	if goodInfo == '0' and costInfo == '0' then
		if type(fun) == "function" then
			fun(RESULT_CODE_BASE.RESULT_COMMON_SUCCEED[1], goodInfo)
		end
		return
	end
	local C2 = HelperL.GetDirectPrizeKeys()
	if C2 == 0 then return end
	local goodInfo2 = string.format("%d;%d", 17, 1)
	local costInfo2 = '0'
	local str_req2 = string.format('LuaRequestDirectGiveGoodsy?Key=%d&GoodInfo=%s&CostInfo=%s', C2, goodInfo2, costInfo2)
	LuaModule.RunLuaRequest(str_req2, function(resultCode2, content2)
		if resultCode2 == RESULT_CODE_BASE.RESULT_COMMON_SUCCEED[1] then
			local C = HelperL.GetDirectPrizeKeys()
			if C == 0 then return end
			local type3, goodInfo3, costInfo3, fun3 = awardType, goodInfo, costInfo, fun
			local isDisplayReward3 = isDisplayReward
			if type3 == 1 then
				local str_req = string.format('LuaRequestDirectGiveGoodsy?Key=%d&GoodInfo=%s&CostInfo=%s', C,
					goodInfo3 or 0, costInfo3 or 0)
				LuaModule.RunLuaRequest(str_req, function(resultCode, content)
					local fun4 = fun3
					local isDisplayReward4 = isDisplayReward3
					if resultCode == RESULT_CODE_BASE.RESULT_COMMON_SUCCEED[1] then
						if isDisplayReward4 ~= false then
							HelperL.DisplayReward(PrizeContentType.STRING3, content)
						end
					else
						-- ResultCode.ShowResultCodeCallback(resultCode, content)
					end

					if type(fun4) == "function" then
						fun4(resultCode, content)
					end
				end)
			else
				local str_req = string.format('LuaRequestDirectGivePrizes?Key=%d&PrizeId=%s&CostInfo=%s', C,
					goodInfo3 or 0, costInfo3 or 0)
				LuaModule.RunLuaRequest(str_req, fun3)
			end
		end
	end
	)
end

--直接向服务器请求发放物品
function HelperL.GetDirectGoods(goodsList, multi)
	local beishu = 1
	if multi then beishu = multi end
	local goodInfo = ""
	for i, v in ipairs(goodsList) do
		if goodInfo == "" then
			goodInfo = string.format("%d;%d", v.ID, v.Num * beishu)
		else
			goodInfo = goodInfo .. "|" .. string.format("%d;%d", v.ID, v.Num * beishu)
		end
	end
	HelperL.RequestDirectGiveGoods(1, goodInfo, '0')
end

---直接向服务器请求发放物品和扣除消耗物品
---@param goodInfo string 发放物品
---@param costInfo string 扣除消耗物品
---@param fun ?fun(resultCode:integer, content:string) 回调函数
---@param isDisplayReward ?boolean 是否显示奖励，默认：true
function HelperL.RequestDirectGiveGoodsy(goodInfo, costInfo, fun, isDisplayReward)
	HelperL.RequestDirectGiveGoods(1, goodInfo, costInfo, fun, isDisplayReward)
end

--直接向服务器请求发放奖励表的奖励
---@param prizeId integer 奖励id
---@param costInfo ?string 消耗物品
---@param fun ?fun(resultCode:integer, content:string) 回调函数
---@param isDisplayReward ?boolean 是否显示奖励，默认：true
function HelperL.GetDirectPrize(prizeId, costInfo, fun, isDisplayReward)
	local prizeGoodsList, prizeGoodsNum = Schemes.PrizeTable:GetRandomPrize(prizeId, true, 1)
	if prizeGoodsList == nil then
		warn('--------随机奖励错误--------prizeId=', prizeId)
		return
	end
	local goodInfo = HelperL.TableAttributeConcatenation(prizeGoodsList, { "ID", "Num" })
	if goodInfo == nil then
		warn('--------奖励拼接错误--------奖励长度=', #prizeGoodsList)
		return
	end
	HelperL.RequestDirectGiveGoods(1, goodInfo, costInfo, fun, isDisplayReward)
end

---获取装备战力
---@param entity integer 装备实体
---@return integer
function HelperL.GetEquipmentFight(entity)
	if not entity then
		return 0
	end
	local goodsID = entity:GetProperty(ENTITY_FIELD.ENTITY_FIELD_ID)
	local attr_list = {}
	for i = EQUIPMENT_FIELD.EQUIPMENT_FIELD_EFFECTID1, EQUIPMENT_FIELD.EQUIPMENT_FIELD_EFFECTID5, 1 do
		local p = ActorProp.GetAttributeData(entity:GetProperty(i), i, goodsID)
		if p then
			local tp = ActorProp.EffectType_Prop_First(p.Type)
			if tp then
				table.insert(attr_list, { key = tp.ActorProp, value = p.Value, type = p.Type })
			end
		end
	end

	local total_hp = 0
	local total_attack = 0
	for _, v in pairs(attr_list) do
		if v.key == "HP" then
			total_hp = total_hp + v.value
		end
		if v.key == "Attack" or v.key == "PhysicsAttack" then
			total_attack = total_attack + v.value
		end
	end
	return math.floor(total_hp * 0.5 + total_attack)
end

---是推荐装备
---@param entity any 装备实体
---@return boolean
function HelperL.IsRecommendedEquipment(entity)
	if not entity then
		return false
	end
	local goodsID = entity:GetProperty(ENTITY_FIELD.ENTITY_FIELD_ID)
	if goodsID <= DEFINE.MAX_MEDICAMENT_ID then
		return false
	end
	local schemeItem = Schemes:GetGoodsConfig(goodsID)
	local subType = schemeItem.SubType
	local equipSkep = SkepModule.GetEquipSkep()
	local wear_entity = EntityModule:GetEntity(equipSkep[subType])
	if wear_entity then
		local fight = HelperL.GetEquipmentFight(entity)
		local wear_fight = HelperL.GetEquipmentFight(wear_entity)
		if fight <= wear_fight then
			return false
		end
	end

	return true
end

---创建模型
---@param parent any 父节点
---@param rawImageSize ?integer 宽度,默认：500
---@param renderTextureSize ?integer 高度,默认：1000
function HelperL.CreateRenderTextureAndModel(parent, rawImageSize, renderTextureSize)
	---@class UI2DModel
	local item = {}
	--父节点
	item.parent = parent and parent.transform or nil
	--RenderTexture组件--宽度
	item.width = width or 500
	--RenderTexture组件--高度
	item.height = height or 500

	--装备ID
	item.equipID = nil
	--模型ID
	item.creatureID = nil
	--模型路径
	item.modelPath = nil
	--组件列表
	item.objList = {}
	--RenderTexture组件
	item.tex = nil
	--2D模型节点
	item.ui2DModel = nil
	--模型对象
	item.model = nil
	--是初始化
	item.isInit = false

	--加载回调
	---@type fun(param:UI2DModel)|nil
	item.loadedCallback = nil
	--开启缓动动画，默认：true
	---@type boolean|nil
	item.isSlowAction = false
	-- 模型位置
	item.pos = Vector3.zero
	-- 模型比例
	item.scale = Vector3.one
	-- 模型旋转
	item.angle = Vector3.zero

	---加载模型回调
	---@param obj integer 模型对象
	---@param self UI2DModel
	item.ModelLoaded = function(obj, self)
		if self.model then
			GameObject.Destroy(self.model)
		end

		self.model = GameObject.Instantiate(obj, self.objList.Obj_Model.transform)
		HelperL.ChildChangeLayer(self.model)
		-- 模型位置
		self.objList.Obj_Model.transform.localPosition = self.pos or Vector3.zero
		-- 模型比例
		self.objList.Obj_Model.transform.localScale = self.scale or Vector3.one
		-- 模型旋转
		self.objList.Obj_Model.transform:Rotate(self.angle or Vector3.zero)

		if type(self.loadedCallback) == "function" then self.loadedCallback(self) end
	end

	---初始化
	---@param obj integer 模型对象
	---@param self UI2DModel
	item.Init = function(obj, self)
		self.isInit = true
		self.ui2DModel = GameObject.Instantiate(obj, self.parent)
		Helper.FillLuaComps(self.ui2DModel.transform, self.objList)
		self.objList.RawImg_Model:GetRectTransform().sizeDelta = Vector2(self.width, self.height)
		self.tex = UnityEngine.RenderTexture.New(self.width, self.height, 16, UnityEngine.RenderTextureFormat.ARGB32)
		self.objList.Cam_Model.targetTexture = self.tex
		self.objList.RawImg_Model.texture = self.tex
		ResMgr.LoadGameObjectAsync(self.modelPath, self.ModelLoaded, self)
	end


	---创建模型(模型路径)
	---@param self UI2DModel
	---@param modelPath string 模型路径
	---@param isSlowAction ?boolean 开启缓动动画，默认：true
	---@param loadedCallback ?fun(param:UI2DModel) 加载回调
	item.CreateUIModel1 = function(self, modelPath, loadedCallback, isSlowAction, pos, scale, angle)
		if modelPath == self.modelPath then return end
		self.modelPath = modelPath

		self.pos = pos
		self.scale = scale
		self.angle = angle
		self.isSlowAction = isSlowAction
		self.loadedCallback = loadedCallback

		if self.isInit then
			ResMgr.LoadGameObjectAsync(self.modelPath, self.ModelLoaded, self)
		else
			ResMgr.LoadGameObjectAsync("ui/Common/UI2DModel", self.Init, self)
		end
	end

	---创建模型(模型ID)
	---@param self UI2DModel
	---@param creatureID integer 模型ID
	---@param loadedCallback ?fun(param:UI2DModel) 加载回调
	---@param isSlowAction ?boolean 开启缓动动画，默认：true
	item.CreateUIModel2 = function(self, creatureID, loadedCallback, isSlowAction)
		if creatureID == self.creatureID then return end
		local creature = Schemes.Creature:Get(creatureID)
		if not creature then
			warn('读表 Creature 失败 creature=', creatureID)
			return
		end
		self.creatureID = creatureID
		local _scale = creature.Scale > 0 and creature.Scale or 1
		local pos = Vector3(creature.PosX, creature.PosY, creature.PosZ)
		local scale = Vector3.one * (_scale * 60)
		local angle = Vector3(creature.RotX, creature.RotY, creature.RotZ)
		self:CreateUIModel1(creature.UIPrefab, loadedCallback, isSlowAction, pos, scale, angle)
	end

	---创建模型(装备ID)
	---@param self UI2DModel
	---@param equipID integer 装备ID
	---@param loadedCallback ?fun(param:UI2DModel) 加载回调
	---@param isSlowAction ?boolean 开启缓动动画，默认：true
	item.CreateUIModel3 = function(self, equipID, loadedCallback, isSlowAction)
		if equipID == self.equipID then return end
		local cfg = Schemes.Equipment:Get(equipID)
		if not cfg then
			warn('读表 Equipment 失败 equipID=', equipID)
			return
		end
		self.equipID = equipID
		self:CreateUIModel2(cfg.RightWeapon, loadedCallback, isSlowAction)
	end

	return item
end

---获取用餐阶段，1早餐，2午餐，3晚餐，4夜宵
function HelperL.LunchPhase()
	-- 获取当前时间的小时数（24小时制）
	local current_hour = tonumber(os.date("%H")) or 0
	if current_hour >= 6 and current_hour < 12 then
		return 1
	elseif current_hour >= 12 and current_hour < 18 then
		return 2
	elseif current_hour >= 18 and current_hour < 22 then
		return 3
	elseif current_hour >= 22 and current_hour < 24 then
		return 4
	end
	return 0
end

---获取提示内容
---@param ID integer|string 提示表ID(Schemes.Tips)
function HelperL.GetTipsContent(ID)
	local tipsCfg = Schemes.Tips:Get(ID)
	if not tipsCfg then
		warn('读Tips表失败 ID=', ID)
		return ''
	end
	local str, _ = string.gsub(tipsCfg.Tips, "<br>", "\n")
	str, _ = string.gsub(str, "<P><P>", "\n")
	str, _ = string.gsub(str, " ", "　")
	return str
end

---打开提示窗口
---@param ID integer 提示表ID(Schemes.Tips)
function HelperL.OpenTipsWindow(ID)
	local str = HelperL.GetTipsContent(ID)
	-- UIManager:OpenWnd(WndID.RuleTip, str)

	---@type NotarizeWindowsDatq
	local data = {
		type = NotarizeWindowsType.Windows6,
		content = str,
	}
	HelperL.NotarizeUI(data)
end

--特效池子
HelperL._vfxPool = {}
--特效加载状态，1加载中，2加载完成
HelperL._vfxLoadState = {}

---播放特效，每种类型特效对象只有一个，重复播放就打断重播
---@param type1 ?integer 特效类型(类型：VFX_Type)，默认值：1
function HelperL.PlayVFX(type1)
	type1 = type1 or 1
	--播放已经加载完成的特效
	if HelperL._vfxLoadState[type1] == 2 and HelperL._vfxPool[type1] then
		HelperL._vfxPool[type1].gameObject:SetActive(false)
		HelperL._vfxPool[type1].gameObject:SetActive(true)
		return
	end
	--特效正在加载中，等待加载完成
	if HelperL._vfxLoadState[type1] == 1 then
		return
	end
	HelperL._vfxLoadState[type1] = 1
	--获取特效路径
	local fxPath = nil
	if type1 == VFX_Type.UPGRADE_FX then
		fxPath = 'UI_JiaoSe_QiangHua'
	end
	if fxPath == nil then
		return
	end
	--加载特效
	ResMgr.LoadGameObjectAsync("fx/" .. fxPath, function(obj, type2)
		if tolua.isnull(obj) then
			return
		end
		HelperL._vfxLoadState[type2] = 2
		HelperL._vfxPool[type2] = GameObject.Instantiate(obj, UIManager.vfxRoot.transform)
		HelperL._vfxPool[type2].gameObject:SetActive(false)
		HelperL._vfxPool[type2].gameObject:SetActive(true)
	end, type1)
end

---数组属性拼接
---@param list integer[] 数组列表
---@param names ?string[] key列表
---@param char1 ?string 小分割符，默认：";"
---@param char2 ?string 大分割符，默认："|"
---@param default ?string 默认值(value==nil时使用)，默认："0"
---@return string|nil
function HelperL.TableAttributeConcatenation(list, names, char1, char2, default)
	local content = nil
	if list then
		char1 = char1 or ";"
		char2 = char2 or "|"
		default = default or "0"
		local str
		for i, tab in ipairs(list) do
			--普通数组类型列表
			if type(tab) ~= "table" then
				if content == nil then
					content = tostring(tab)
				else
					content = content .. char1 .. tostring(tab)
				end
			else
				if names and (#names > 0) then
					str = ""
					for _, k in ipairs(names) do
						if str == "" then
							str = tostring(tab[k] or default)
						else
							str = str .. char1 .. tostring(tab[k] or default)
						end
					end
				else
					str = ""
					for k, v in pairs(tab) do
						if str == "" then
							str = (v or default)
						else
							str = str .. char1 .. (v or default)
						end
					end
				end

				if i == 1 then
					content = str
				else
					content = content .. char2 .. str
				end
			end
		end
	end
	return content
end

---获取枪图标
---@param equipID integer 装备ID
---@return string
function HelperL.GetGunIcon(equipID)
	local equipment = Schemes.Equipment:Get(equipID)
	local quality = equipment.Quality
	local starNum = equipment.StarNum
	local level = 1
	local entity = SkepModule:GetSkepByID(equipment.PacketID):GetEntityByGoodsID(equipment.ID)
	--装备已激活
	if entity then
		quality = entity:GetProperty(EQUIPMENT_FIELD.EQUIPMENT_FIELD_QUALITY)
		starNum = entity:GetProperty(EQUIPMENT_FIELD.EQUIPMENT_FIELD_STARNUM)
		level = quality * 10 + starNum + 1
	end
	local gun = Schemes.Gun:Get(equipment.ConsignmentStyle)
	level = math.max(math.floor(level / 2), 1)
	if level > gun.LvlIcons.Length then
		level = gun.LvlIcons.Length
	end
	return gun.LvlIcons[level - 1]
end

---防沉迷弹窗
---@param fun fun(isCheck: boolean)
function HelperL.ScreenTime(fun, content, isRet)
	---@type NotarizeWindowsDatq
	local data = {
		type = NotarizeWindowsType.Windows3,
		content = content or GetGameText(luaID, 49),
		okCallback = fun,
		isFCM = isRet,
	}
	HelperL.NotarizeUI(data)
end

---计算装备升星等级
---@param entity any
---@return unknown
function HelperL.CalculateLevel(entity)
	if entity then
		local quality = entity:GetProperty(EQUIPMENT_FIELD.EQUIPMENT_FIELD_QUALITY)
		local starNum = entity:GetProperty(EQUIPMENT_FIELD.EQUIPMENT_FIELD_STARNUM)
		local ex = entity:GetProperty(EQUIPMENT_FIELD.EQUIPMENT_FIELD_CUSTOM)
		return (starNum * 100 + quality * 1000 + ex)
	end
	return 0
end

---奖励提示
---@param _type UIRewardHint_Type 提示类型
---@param ... unknown 参数
function HelperL.RewardHintUI(_type, ...)
	UIManager:OpenWnd(WndID.RewardHint, _type, ...)
end

---确认弹窗
---@param data NotarizeWindowsDatq 弹窗数据
function HelperL.NotarizeUI(data)
	UIManager:OpenWnd(WndID.NotarizeWindows, data)
end

---判断是否是同一天
---@param timestamp1 integer 时间戳1
---@param timestamp2 integer 时间戳2
---@return boolean
function HelperL.is_same_day(timestamp1, timestamp2)
	timestamp1 = tonumber(timestamp1) or 0
	timestamp2 = tonumber(timestamp2) or 0
	if timestamp1 == timestamp2 then
		return true
	end
	local date1 = os.date("*t", timestamp1)
	local date2 = os.date("*t", timestamp2)
	return date1.year == date2.year and date1.month == date2.month and date1.day == date2.day
end

---获取服务器时间
---@return integer
function HelperL.GetServerTime()
	-- --本地时间
	-- return os.time()
	--服务器时间
	return Premier.Instance:GetServerTime()
end


---防沉迷确认弹窗
---@param data NotarizeWindowsDatq 弹窗数据
function HelperL.NotarizeFCMUI(data)
	if data.isFCM == true and EntityModule.luaToCshape.IsFighting == true then
		SoundManager:SetVolume_Music(0)
		LuaToCshapeManager.Instance:FightQuit()
	end
	UIManager:OpenWnd(WndID.FCMDialogue, data)
end


---判断是否是未成年,是否能玩游戏
---@return bool
function HelperL.IsCanGame()
	local nowTime = HelperL.GetServerTime()
	local dateTable = os.date('*t', nowTime)
    local weekday = dateTable.wday
    local day = (weekday == 1) and 7 or weekday - 1
	if day ~= 5 and day ~= 6 and day ~= 7 then
		return false
	end

	if dateTable.hour ~= 20 then
		return false
	end

	return true
end

HelperL.smallTimer = nil
function HelperL.CreateSmallRoleTime()
	HelperL.smallTimer= Timer.New(function()
		local nowTime = HelperL.GetServerTime()
		local dateTable = os.date('*t', nowTime)
		if dateTable.hour ~= 20 then
			HelperL.smallTimer:Stop()
			local data = {
				type = NotarizeWindowsType.Windows3,
				content = GetGameText(luaID, 49) .. GetGameText(luaID, 56),
				isFCM = true,
			}
			HelperL.NotarizeFCMUI(data)
		end
	end, 3, -1)
	HelperL.smallTimer:Start()
end






-- 得到所有装备激活的buff用于战斗中的加成
function HelperL.GetAllEquipmentBuff()
	local result = {}
	local equipSkep = SkepModule.GetEquipSkep()
	for i = 1, 11 do
		if equipSkep[i] then
			local equipUID = equipSkep[i]
			local entity = EntityModule:GetEntity(equipUID)
			if entity then
				local equipID = entity:GetProperty(ENTITY_FIELD.ENTITY_FIELD_ID)
				-- print(i  .. "当前装备的Buff " .. equipID)
				local equipConfig = Schemes.Equipment:Get(equipID)
				if equipConfig then
					local QualityLevel = equipConfig.QualityLevel
					local contrastList = HelperL.Split(equipConfig.EffectCoef1White, ';')
					local openList = {}
					for index, value in ipairs(contrastList) do
						if QualityLevel >= tonumber(value) then
							table.insert(openList, index)
						end
					end
					local list = HelperL.Split(equipConfig.AddEffect1, ';')
					for _, id in ipairs(openList) do
						if list[id] then
							-- print("激活的" .. list[id])
							local equipEffectConfig = Schemes.EquipEffect:Get(tonumber(list[id]))
							if equipEffectConfig then
								if result[equipEffectConfig.EffectType] == nil then
									result[equipEffectConfig.EffectType] = equipEffectConfig.EffectParam
								else
									result[equipEffectConfig.EffectType] = result[equipEffectConfig.EffectType] +
										equipEffectConfig.EffectParam
								end
							end
						end
					end
				end
			end
		end
	end
	local resultStr = ""
	for key, value in pairs(result) do
		resultStr = resultStr .. tostring(key) .. ';' .. tostring(value) .. '|'
	end
	return resultStr
end