﻿--[[
********************************************************************
    created:	
    author :	
    purpose:    全局红点管理
*********************************************************************
--]]

--自定义红点ID
RedDotCheckType = {
	FuBenRed = 10019,         --单个副本检测
	SurvivalRankingList = 10020, -- 生存排行榜
}

---@class RedDotManager 全局红点管理
local m = {}
---@type RedDotItem[]
m.dotByParent = {}
---@type RedDotItem[]
m.dotCacheList = {}
m.dotParamCacheList = {}
m.dotDefaultLoc = Vector2.zero

---单个红点物件添加检测参数函数
---@param self RedDotItem
---@param checkType integer 
---@param checkParam1 any
---@param checkParam2 any
---@param checkParam3 any
local function RedDotAddCheckParamFunc(self, checkType, checkParam1, checkParam2, checkParam3)
	if not self then
		error('RedDotAddCheckParamFunc')
		return
	end

	if not m.redDotCheckFunc[checkType] then
		warn('RedDotAddCheckParamFunc 未定义的红点检测类型', checkType)
		return
	end

	local item = nil
	for i, v in ipairs(self.checkList) do
		if v[1] == checkType then
			item = v
			if v[2] == checkParam1 and v[3] == checkParam2 and v[4] == checkParam3 then
				-- 无改动不需处理
				return
			end
			break
		end
	end

	if not item then
		local cacheList = m.dotParamCacheList
		local cacheCount = #cacheList
		if cacheCount > 0 then
			item = cacheList[cacheCount]
			cacheList[cacheCount] = nil
		else
			item = {}
		end
		table.insert(self.checkList, item)
	end

	item[1] = checkType
	item[2] = checkParam1
	item[3] = checkParam2
	item[4] = checkParam3

	-- 下次更新时检测红点
	self.needCheck = true
	m.needUpdate = true
end

-- 单个红点物件清除检测参数函数
local function RedDotClearCheckParamFunc(dotItem)
	if not dotItem then
		error('RedDotClearCheckParamFunc')
		return
	end

	local count = #dotItem.checkList
	if count <= 0 then
		return
	end

	for i = count, 1, -1 do
		local item = dotItem.checkList[i]
		dotItem.checkList[i] = nil
		item[4] = nil
		item[3] = nil
		item[2] = nil
		item[1] = nil
		local cacheList = m.dotParamCacheList
		local cacheCount = #cacheList
		m.dotParamCacheList[cacheCount + 1] = item
	end
end

--获取红点预制体
function m.GetRedDotPrefab()
	m.redDotPrefab = HotResManager.ReadUI('ui/Common/RedDot')
	m.GetRedDotPrefab = function()
		return m.redDotPrefab
	end
	return m.redDotPrefab
end

-- 添加红点
function m:GetRedDotCheck(wndID, ...)
	local fun = self.redDotCheckFunc[wndID]
	if fun and fun(self, ...) then
		return true
	end
	return false
end

-- 添加红点
function m:SetRedDot(parent, offset)
	if not parent then
		error('m:CreateRedDot invalid param', parent)
		return nil
	end

	---@class RedDotItem
	local dotItem = self.dotByParent[parent]
	if not dotItem then
		if #self.dotCacheList > 0 then
			dotItem = table.remove(self.dotCacheList)
			dotItem.trans:SetParent(parent, false)
		else
			dotItem = {}
			dotItem.checkList = {}
			dotItem.obj = GameObject.Instantiate(m.GetRedDotPrefab(), parent)
			dotItem.trans = dotItem.obj:GetRectTransform()
			dotItem.AddCheckParam = RedDotAddCheckParamFunc
			dotItem.ClearCheckParam = RedDotClearCheckParamFunc
		end

		self.dotByParent[parent] = dotItem
	else
		-- 已有，改参数就行
		if tolua.isnull(dotItem.obj) then
			dotItem.obj = GameObject.Instantiate(m.GetRedDotPrefab(), parent)
			dotItem.trans = dotItem.obj:GetRectTransform()
		else
			dotItem.trans:SetParent(parent, false)
		end
	end
	dotItem.parent = parent
	dotItem.trans.anchoredPosition = offset or self.dotDefaultLoc
	dotItem.obj:SetActive(false)
	dotItem.needCheck = false

	return dotItem
end

-- 移除红点
function m:RemoveRedDot(parent)
	if not parent then
		warn('m:RemoveRedDot not parent', debug.traceback())
		return
	end

	local dotItem = self.dotByParent[parent]
	if not dotItem then
		warn('m:RemoveRedDot not dotItem', debug.traceback())
		return
	end
	dotItem.parent = nil
	dotItem:ClearCheckParam()
	if dotItem.obj then
		dotItem.obj:SetActive(false)
	else
		dotItem.obj = nil
	end
	if dotItem.trans then
		dotItem.trans:SetParent(EntityModule.modelUIContainerTrans, false)
	end

	table.insert(self.dotCacheList, dotItem)

	self.dotByParent[parent] = nil
end

-- 检测单个红点
function m:CheckSingleRedDot(dotItem)
	if dotItem then
		for i, v in ipairs(dotItem.checkList) do
			if UIManager:JudgeOpenLevel(v[1], false) then
				local checkFunc = self.redDotCheckFunc[v[1]]
				if checkFunc then
					if checkFunc(self, v[2], v[3], v[4]) then
						return true
					end
				end
			end
		end
	end
	return false
end

m.needUpdateTypeList = {}
-- 定期更新红点
function m.OnDotUpdate()
	if next(m.needUpdateTypeList) ~= nil then
		m.needUpdate = true
	end
	if m.needUpdate then
		for parent, dotItem in pairs(m.dotByParent) do
			local needCheck = dotItem.needCheck
			if not needCheck then
				for i, v in ipairs(dotItem.checkList) do
					if m.needUpdateTypeList[v[1]] then
						needCheck = true
						break
					end
				end
			end

			if needCheck then
				dotItem.needCheck = false
				if dotItem.obj then
					dotItem.obj:SetActive(m:CheckSingleRedDot(dotItem))
				end
			end
		end

		for i, v in pairs(m.needUpdateTypeList) do
			m.needUpdateTypeList[i] = nil
		end

		m.needUpdate = false
	end
end

-- 篮子物品更新
function m.OnSkepGoodsChange()
	m.needUpdateTypeList[WndID.AirPlaneNew] = true
	m.needUpdateTypeList[WndID.PetShopNew] = true
	m.needUpdateTypeList[WndID.GameEctype] = true
	m.needUpdateTypeList[WndID.SurvivalEctype] = true
	m.needUpdateTypeList[WndID.EquipKnapsack] = true
	m.needUpdateTypeList[WndID.EquipWeapon] = true
	m.needUpdateTypeList[WndID.MainEctype] = true

	m.needUpdateTypeList[WndID.PaTaFuBen] = true
	m.needUpdateTypeList[WndID.MiJingFuBen] = true
	m.needUpdateTypeList[WndID.RoleTalent] = true
	m.needUpdateTypeList[WndID.TreasuredTricks] = true
	m.needUpdateTypeList[WndID.ShengHun] = true
	m.needUpdateTypeList[WndID.XinFa] = true
	m.needUpdateTypeList[WndID.EquipSynthesis] = true
	m.needUpdateTypeList[WndID.GoldCaveEctypt] = true
	m.needUpdateTypeList[WndID.ForbiddenAreaEctype] = true
	m.needUpdateTypeList[WndID.MiJingFuBen] = true
	m.needUpdateTypeList[WndID.LeaderEctype] = true
	m.needUpdateTypeList[WndID.EliteEctype] = true
	m.needUpdateTypeList[WndID.EquipWeaponInfo] = true
	m.needUpdateTypeList[WndID.MonthCard] = true
	m.needUpdateTypeList[WndID.RoleEquip] = true
end

-- 物品属性更新
function m.OnGoodsPropChange(uid)
	m.needUpdateTypeList[WndID.EquipKnapsack] = true
	m.needUpdateTypeList[WndID.EquipWeapon] = true

	m.needUpdateTypeList[WndID.PaTaFuBen] = true
	m.needUpdateTypeList[WndID.MiJingFuBen] = true
	m.needUpdateTypeList[WndID.RoleTalent] = true
	m.needUpdateTypeList[WndID.TreasuredTricks] = true
	m.needUpdateTypeList[WndID.ShengHun] = true
	m.needUpdateTypeList[WndID.XinFa] = true
	m.needUpdateTypeList[WndID.EquipSynthesis] = true
	m.needUpdateTypeList[WndID.GoldCaveEctypt] = true
	m.needUpdateTypeList[WndID.ForbiddenAreaEctype] = true
	m.needUpdateTypeList[WndID.MiJingFuBen] = true
	m.needUpdateTypeList[WndID.LeaderEctype] = true
	m.needUpdateTypeList[WndID.EliteEctype] = true
	m.needUpdateTypeList[WndID.EquipWeaponInfo] = true
	m.needUpdateTypeList[WndID.MonthCard] = true
	m.needUpdateTypeList[WndID.RoleEquip] = true
end

-- 商店变更处理
function m.OnStoreList()
	m.needUpdateTypeList[WndID.Welfare] = true
	m.needUpdateTypeList[WndID.Shop] = true
	m.needUpdateTypeList[WndID.PetShopNew] = true
	m.needUpdateTypeList[WndID.EquipKnapsack] = true
	m.needUpdateTypeList[WndID.ShopNew] = true
	m.needUpdateTypeList[WndID.EquipWeapon] = true
	m.needUpdateTypeList[WndID.MainEctype] = true

	m.needUpdateTypeList[WndID.PaTaFuBen] = true
	m.needUpdateTypeList[WndID.MiJingFuBen] = true
	m.needUpdateTypeList[WndID.RoleTalent] = true
	m.needUpdateTypeList[WndID.TreasuredTricks] = true
	m.needUpdateTypeList[WndID.ShengHun] = true
	m.needUpdateTypeList[WndID.XinFa] = true
	m.needUpdateTypeList[WndID.EquipSynthesis] = true
	m.needUpdateTypeList[WndID.GoldCaveEctypt] = true
	m.needUpdateTypeList[WndID.ForbiddenAreaEctype] = true
	m.needUpdateTypeList[WndID.MiJingFuBen] = true
	m.needUpdateTypeList[WndID.LeaderEctype] = true
	m.needUpdateTypeList[WndID.EliteEctype] = true
	m.needUpdateTypeList[WndID.EquipWeaponInfo] = true
	m.needUpdateTypeList[WndID.MonthCard] = true
	m.needUpdateTypeList[WndID.RoleEquip] = true
end

-- 商店购买处理
function m.OnStoreBuyItem()
	m.needUpdateTypeList[WndID.Shop] = true
	m.needUpdateTypeList[WndID.PetShopNew] = true
	m.needUpdateTypeList[WndID.EquipKnapsack] = true
	m.needUpdateTypeList[WndID.ShopNew] = true
	m.needUpdateTypeList[WndID.EquipWeapon] = true
	m.needUpdateTypeList[WndID.MainEctype] = true

	m.needUpdateTypeList[WndID.PaTaFuBen] = true
	m.needUpdateTypeList[WndID.MiJingFuBen] = true
	m.needUpdateTypeList[WndID.RoleTalent] = true
	m.needUpdateTypeList[WndID.TreasuredTricks] = true
	m.needUpdateTypeList[WndID.ShengHun] = true
	m.needUpdateTypeList[WndID.XinFa] = true
	m.needUpdateTypeList[WndID.EquipSynthesis] = true
	m.needUpdateTypeList[WndID.GoldCaveEctypt] = true
	m.needUpdateTypeList[WndID.ForbiddenAreaEctype] = true
	m.needUpdateTypeList[WndID.MiJingFuBen] = true
	m.needUpdateTypeList[WndID.LeaderEctype] = true
	m.needUpdateTypeList[WndID.EliteEctype] = true
	m.needUpdateTypeList[WndID.EquipWeaponInfo] = true
	m.needUpdateTypeList[WndID.MonthCard] = true
	m.needUpdateTypeList[WndID.RoleEquip] = true
end

-- 广告播放处理
function m.OnAdvertisePlayComplete(inspireType)

end

-- 广告播放开启
function m.OnAdvertisePlayOpen(inspireType)

end

-- 广告播放冷却时间结束
function m.OnAdvertiseCoolTimeEnd(inspireType)
	m.needUpdateTypeList[WndID.SevenDayInvestment] = true
end

-- 逻辑值变化处理
function m.OnLogicDataChange()
	m.needUpdateTypeList[WndID.SevenDayInvestment] = true
	m.needUpdateTypeList[WndID.FirstRecharge] = true
	m.needUpdateTypeList[WndID.ChaoJiChongZhi] = true
	m.needUpdateTypeList[WndID.BrustCatChallenge] = true
	m.needUpdateTypeList[WndID.SignIn] = true
	m.needUpdateTypeList[WndID.LuckDraw] = true
	m.needUpdateTypeList[WndID.EveryDayTask] = true
	m.needUpdateTypeList[WndID.Shop] = true
	m.needUpdateTypeList[WndID.MonthCard] = true
	m.needUpdateTypeList[WndID.RecommendCommodities] = true
	m.needUpdateTypeList[WndID.RecommendCommodities2] = true
	m.needUpdateTypeList[WndID.RecommendCommodities3] = true
	m.needUpdateTypeList[WndID.StageReward] = true
	m.needUpdateTypeList[WndID.Equip] = true
	m.needUpdateTypeList[WndID.EquipBox] = true
	m.needUpdateTypeList[WndID.EquipUpgrade] = true
	m.needUpdateTypeList[WndID.RoleNew] = true
	m.needUpdateTypeList[WndID.PetShopNew] = true
	m.needUpdateTypeList[WndID.CatPatrol] = true
	m.needUpdateTypeList[WndID.GameEctype] = true
	m.needUpdateTypeList[WndID.SurvivalEctype] = true
	m.needUpdateTypeList[WndID.AirPlaneNew] = true
	m.needUpdateTypeList[WndID.EquipKnapsack] = true
	m.needUpdateTypeList[WndID.ShopNew] = true
	m.needUpdateTypeList[WndID.EquipWeapon] = true
	m.needUpdateTypeList[WndID.MainEctype] = true

	m.needUpdateTypeList[WndID.PaTaFuBen] = true
	m.needUpdateTypeList[WndID.MiJingFuBen] = true
	m.needUpdateTypeList[WndID.RoleTalent] = true
	m.needUpdateTypeList[WndID.TreasuredTricks] = true
	m.needUpdateTypeList[WndID.ShengHun] = true
	m.needUpdateTypeList[WndID.XinFa] = true
	m.needUpdateTypeList[WndID.EquipSynthesis] = true
	m.needUpdateTypeList[WndID.GoldCaveEctypt] = true
	m.needUpdateTypeList[WndID.ForbiddenAreaEctype] = true
	m.needUpdateTypeList[WndID.MiJingFuBen] = true
	m.needUpdateTypeList[WndID.LeaderEctype] = true
	m.needUpdateTypeList[WndID.EliteEctype] = true
	m.needUpdateTypeList[WndID.WelfareInvestment] = true
	m.needUpdateTypeList[WndID.EquipWeaponInfo] = true
	m.needUpdateTypeList[WndID.RoleEquip] = true
end

-- 主角属性变化通知
function m.OnHeroPropChange()
	m.needUpdateTypeList[WndID.Welfare] = true
	m.needUpdateTypeList[WndID.LuckDraw] = true
	m.needUpdateTypeList[WndID.RoleNew] = true
	m.needUpdateTypeList[WndID.AirPlaneNew] = true
	m.needUpdateTypeList[WndID.PetShopNew] = true
	m.needUpdateTypeList[WndID.Buff] = true
	m.needUpdateTypeList[WndID.GameEctype] = true
	m.needUpdateTypeList[WndID.SurvivalEctype] = true
	m.needUpdateTypeList[WndID.FirstRecharge] = true
	m.needUpdateTypeList[WndID.ChaoJiChongZhi] = true
	m.needUpdateTypeList[WndID.Shop] = true
	m.needUpdateTypeList[WndID.MonthCard] = true
	m.needUpdateTypeList[WndID.RecommendCommodities] = true
	m.needUpdateTypeList[WndID.RecommendCommodities2] = true
	m.needUpdateTypeList[WndID.RecommendCommodities3] = true
	m.needUpdateTypeList[WndID.Equip] = true
	m.needUpdateTypeList[WndID.EquipBox] = true
	m.needUpdateTypeList[WndID.EquipUpgrade] = true
	m.needUpdateTypeList[WndID.CatPatrol] = true
	m.needUpdateTypeList[WndID.GivePhysicalPower] = true
	m.needUpdateTypeList[WndID.EquipKnapsack] = true
	m.needUpdateTypeList[WndID.ShopNew] = true
	m.needUpdateTypeList[WndID.EquipWeapon] = true
	m.needUpdateTypeList[WndID.MainEctype] = true

	m.needUpdateTypeList[WndID.PaTaFuBen] = true
	m.needUpdateTypeList[WndID.MiJingFuBen] = true
	m.needUpdateTypeList[WndID.RoleTalent] = true
	m.needUpdateTypeList[WndID.TreasuredTricks] = true
	m.needUpdateTypeList[WndID.ShengHun] = true
	m.needUpdateTypeList[WndID.XinFa] = true
	m.needUpdateTypeList[WndID.EquipSynthesis] = true
	m.needUpdateTypeList[WndID.GoldCaveEctypt] = true
	m.needUpdateTypeList[WndID.ForbiddenAreaEctype] = true
	m.needUpdateTypeList[WndID.MiJingFuBen] = true
	m.needUpdateTypeList[WndID.LeaderEctype] = true
	m.needUpdateTypeList[WndID.EliteEctype] = true
	m.needUpdateTypeList[WndID.EquipWeaponInfo] = true
	m.needUpdateTypeList[WndID.RoleEquip] = true
end

--更新邮件列表
function m.OnGetEmailList()
	m.needUpdateTypeList[WndID.Email] = true
	m.needUpdateTypeList[WndID.EquipKnapsack] = true
end

--匹配列表更新
function m.OnUpdateMatchList()

end

--塔防当日大关战斗次数更新
function m.OnTowerBattlecDayMainCount()

end

--buff数据更新
function m.EntitySyncBuff(_self, buffID)
	m.needUpdateTypeList[WndID.Welfare] = true
	m.needUpdateTypeList[WndID.DiscountGift] = true
	m.needUpdateTypeList[WndID.SevenDayInvestment] = true
	m.needUpdateTypeList[WndID.MonthCard] = true
	m.needUpdateTypeList[WndID.StageReward] = true
	m.needUpdateTypeList[WndID.CatPatrol] = true
	m.needUpdateTypeList[WndID.RoleNew] = true
	m.needUpdateTypeList[WndID.AirPlaneNew] = true
	m.needUpdateTypeList[WndID.OpenTreasureBox] = true
	m.needUpdateTypeList[WndID.RecommendCommodities] = true
	m.needUpdateTypeList[WndID.RecommendCommodities2] = true
	m.needUpdateTypeList[WndID.RecommendCommodities3] = true
	m.needUpdateTypeList[WndID.Shop] = true
	m.needUpdateTypeList[WndID.GameEctype] = true
	m.needUpdateTypeList[WndID.SurvivalEctype] = true
	m.needUpdateTypeList[WndID.ShopNew] = true
	m.needUpdateTypeList[WndID.MainEctype] = true
end

--窗口打开
function m.WindowOpen(wndID)
	m.needUpdateTypeList[WndID.OpenTreasureBox] = true
	m.needUpdateTypeList[WndID.ADFree] = true
	m.needUpdateTypeList[WndID.Invitation] = true
	m.needUpdateTypeList[WndID.StageReward] = true
end

--排行榜数据更新
function m.OnRankingListUpdate()
	m.needUpdateTypeList[WndID.SurvivalEctype] = true
end

-- 更新任务数据
function m.UpdateActorTaskData()
	m.needUpdateTypeList[WndID.TaskInterface] = true
end

-- 实体模块数据更新
function m.EntityModuleDataUpdate()
	m.needUpdateTypeList[WndID.TaskInterface] = true
end

-- 更新副本数据
function m.UpdateGameEctypeData()
	m.needUpdateTypeList[WndID.TaskInterface] = true
	m.needUpdateTypeList[WndID.MainEctype] = true
end

-- 更新--更新副本分数
function m.UpdateGameEctypeStageScoreData()
	m.needUpdateTypeList[WndID.MainEctype] = true
end

--(dataCatalog) 角色数据已更改
function m.ActorDataChanged()
	m.needUpdateTypeList[WndID.CatPatrol] = true
	m.needUpdateTypeList[WndID.EquipWeapon] = true
	m.needUpdateTypeList[WndID.MainEctype] = true
	m.needUpdateTypeList[WndID.SignIn] = true
	m.needUpdateTypeList[WndID.EquipSynthesis] = true
	m.needUpdateTypeList[WndID.EquipWeaponInfo] = true
	m.needUpdateTypeList[WndID.RoleEquip] = true
end

--(ServerLoaded, LocalLoaded) 角色数据已加载
function m.ActorDataLoaded()
	m.needUpdateTypeList[WndID.CatPatrol] = true
end

------------------------------------------------------
--红点检测函数已迁移到  RedDotCheckFunc.lua
------------------------------------------------------

-- 初始化--注册红点检测函数
function m:Init()
	local r = RedDotCheckFunc
	---@type fun(self, ...)[]
	self.redDotCheckFunc = {
		--普通窗口红点函数
		[WndID.ADFree] = r.ADFreeRedDot,
		[WndID.Email] = r.EmailRedDot,
		[WndID.Welfare] = r.CheckWelfare,
		[WndID.SevenDayInvestment] = r.CheckSevenDaysInvest,
		[WndID.FirstRecharge] = r.CheckFirstCharge,
		[WndID.ChaoJiChongZhi] = r.CheckChaoJiChongZhi,
		[WndID.SignIn] = r.SignInRedDot,
		[WndID.DiscountGift] = r.CheckDiscountGift,
		[WndID.StageReward] = r.CheckStageReward,
		[WndID.MonthCard] = r.CheckMonthCard,
		[WndID.Invitation] = r.CheckInvitation,
		[WndID.Fate] = r.CheckFate,
		[WndID.Equip] = r.CheckEquipForging,
		[WndID.EquipBox] = r.CheckEquipBox,
		[WndID.EquipUpgrade] = r.CheckEquipStarNumber,
		[WndID.RoleNew] = r.CheckRole,
		[WndID.PetShopNew] = r.Check_UIPetShopNew,
		[WndID.AirPlaneNew] = r.CheckRoleRole,
		[WndID.Shop] = r.CheckShop,
		[WndID.OpenTreasureBox] = r.CheckOpenTreasureBox,
		[WndID.Buff] = r.CheckBuff,
		[WndID.GameEctype] = r.CheckGameEctype,
		[WndID.SurvivalEctype] = r.CheckSurvivalEctype,
		[WndID.CatPatrol] = r.CatPatrolRed,
		[WndID.RecommendCommodities] = r.RecommendCommoditiesRedDot,
		[WndID.RecommendCommodities2] = r.RecommendCommoditiesRedDot,
		[WndID.RecommendCommodities3] = r.RecommendCommoditiesRedDot,
		[WndID.EquipKnapsack] = r.Check_EquipKnapsack,
		[WndID.GivePhysicalPower] = r.CheckGivePhysicalPower,
		[WndID.TaskInterface] = r.Check_UITaskInterface,
		[WndID.ShopNew] = r.Check_UIShopNew,
		[WndID.EquipWeapon] = r.Check_UIEquipWeapon,
		[WndID.MainEctype] = r.Check_UIMainEctype,

		[WndID.PaTaFuBen] = r.Check_UIPaTaFuBen,
		[WndID.WelfareInvestment] = r.Check_WelfareInvestment,
		[WndID.MiJingFuBen] = r.Check_UIMiJingFuBen,
		[WndID.RoleTalent] = r.Check_UIRoleTalent,
		[WndID.TreasuredTricks] = r.Check_UITreasuredTricks,
		[WndID.ShengHun] = r.Check_UIShengHun,
		[WndID.XinFa] = r.Check_UIXinFa,
		[WndID.EliteEctype] = r.Check_UIEliteEctype,
		[WndID.LeaderEctype] = r.Check_UILeaderEctype,
		[WndID.EquipSynthesis] = r.Check_UIEquipSynthesis,
		[WndID.GoldCaveEctypt] = r.Check_UIGoldCaveEctype,
		[WndID.ForbiddenAreaEctype] = r.Check_UIForbiddenAreaEctype,
		[WndID.EquipWeaponInfo] = r.Check_UIEquipWeaponInfo,
		[WndID.RoleEquip] = r.Check_EquipKnapsack_ShengJiJueSe,

		--自定义红点函数
		[RedDotCheckType.FuBenRed] = r.FuBenRed,
		[RedDotCheckType.SurvivalRankingList] = r.CheckSurvivalRankingList,
	}
end

m:Init()
m.updateTimer = Timer.New(m.OnDotUpdate, 1, -1)
m.updateTimer:Start()

RedDotManager = m

EventManager:Subscribe(EventID.OnSkepGoodsChange, m.OnSkepGoodsChange)
EventManager:Subscribe(EventID.OnGoodsPropChange, m.OnGoodsPropChange)
EventManager:Subscribe(EventID.StoreList, m.OnStoreList)
EventManager:Subscribe(EventID.StoreBuyItem, m.OnStoreBuyItem)
EventManager:Subscribe(EventID.StoreUpdateFree, m.OnStoreBuyItem)
EventManager:Subscribe(EventID.AdvertisePlayComplete, m.OnAdvertisePlayComplete)
EventManager:Subscribe(EventID.LogicDataChange, m.OnLogicDataChange)
EventManager:Subscribe(EventID.OnAdvertiseCoolTimeEnd, m.OnAdvertiseCoolTimeEnd)
EventManager:Subscribe(EventID.AdvertisePlayOpen, m.OnAdvertisePlayOpen)
EventManager:Subscribe(EventID.UpdateMatchList, m.OnUpdateMatchList)
EventManager:Subscribe(EventID.TowerBattlecDayMainCount, m.OnTowerBattlecDayMainCount)
EventManager:Subscribe(EventID.OnHeroPropChange, m.OnHeroPropChange)
EventManager:Subscribe(EventID.EntitySyncBuff, m.EntitySyncBuff)
EventManager:Subscribe(EventID.WindowOpen, m.WindowOpen)
EventManager:Subscribe(EventID.OnRankingListUpdate, m.OnRankingListUpdate)
EventManager:Subscribe(EventID.UpdateActorTaskData, m.UpdateActorTaskData)
EventManager:Subscribe(EventID.EntityModuleDataUpdate, m.EntityModuleDataUpdate)
EventManager:Subscribe(EventID.UpdateGameEctypeData, m.UpdateGameEctypeData)
EventManager:Subscribe(EventID.UpdateGameEctypeStageScoreData, m.UpdateGameEctypeStageScoreData)
EventManager:Subscribe(EventID.ActorDataChanged, m.ActorDataChanged)
EventManager:Subscribe(EventID.ActorDataLoaded, m.ActorDataLoaded)


--邮件相关
EventManager:Subscribe(EventID.OnGetEmailList, m.OnGetEmailList)
EventManager:Subscribe(EventID.OnGetEmailInfo, m.OnGetEmailList)
EventManager:Subscribe(EventID.OnDeleteMail, m.OnGetEmailList)
EventManager:Subscribe(EventID.OnGetEmailAdjunct, m.OnGetEmailList)
EventManager:Subscribe(EventID.OnNewEmail, m.OnGetEmailList)
