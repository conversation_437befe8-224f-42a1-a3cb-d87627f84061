local luaID = ('BattleSkillModule')

BattleSkillModule = {}
local self=BattleSkillModule

self.skillList = nil
-- self.groupCDDic = nil --公共组CD时间
-- self.energyRecovering = false --为true能量恢复中
self.energyRecoverTimer = 0
-- self.buffEffConfig = nil
--进入战斗，初始化技能
function BattleSkillModule:InitSkill()
    self.skillList = {}
    -- self.groupCDDic = {} --公共组CD时间
    -- self.energyRecovering = false
    self.energyRecoverTimer = 0
    -- self.buffEffConfig = nil
    self.recoveryTime = Schemes.ConstValue:Get(CONST_VALUE.CONST_SOCIETY_NEWSERVERDAYCOUNT)
    self.recoveryDot = Schemes.ConstValue:Get(CONST_VALUE.CONST_SOCIETY_NEWSERVERLEAVELIMIT)
    self.recoveryRate = Schemes.ConstValue:Get(CONST_VALUE.CONST_ACTIVITYNEWSERVER_RANK_FLAG)
	--self:AddSkill(4071)--测试技能，直接手动添加技能id
end

--添加技能
function BattleSkillModule:AddSkill(skillType)
    local cancle = EventManager:Fire(EventID.BeforeAddSkill, skillType);
    if (cancle) then return end
    local json_skill = EntityModule.luaToCshape:AddSkill(skillType);
    local skill = dkjsonHelper.decode(json_skill);
    EventManager:Fire(EventID.AfterAddSkillEffect, skill);
    return skill;
end

--添加技能效果(升级)
function BattleSkillModule:AddSkillEffect(skillType, sillEffectID)
    local cancle = EventManager:Fire(EventID.BeforeAddSkillEffect, skillType, sillEffectID);
    if(cancle) then return end
    local json_skill = EntityModule.luaToCshape:AddSkillEffect(skillType, sillEffectID);
    local skill = dkjsonHelper.decode(json_skill);
    EventManager:Fire(EventID.AfterAddSkillEffect, skill, sillEffectID);
    return skill;
end

function BattleSkillModule:OnUpdate()
    -- if self.energyRecovering then--能量恢复中不使用技能
    --     self.energyRecoverTimer = self.energyRecoverTimer + Time.deltaTime
    --     if self.energyRecoverTimer >= self.recoveryTime/1000 then
    --         BattleManager:UpdateCurrEnergy(self.recoveryDot)
    --         self.energyRecoverTimer = 0
    --         print("能量恢复 = " .. BattleManager:GetCurrEnergy())
    --         if BattleManager:GetCurrEnergy() >= (BattleManager:GetMaxEnergy() * (self.recoveryRate/100)) then
    --             print("能量恢复完成")
    --             self.energyRecovering = false
    --         end
    --     end
    --     return
    -- end
    -- for k,o in pairs(self.groupCDDic) do
	-- 	if o ~= nil then     
    --         o.GroupCDCounter = o.GroupCDCounter + Time.deltaTime         
    --     end
    -- end

    --print("BattleSkillModule:OnUpdate -- skillList=", json.encode(self.skillList))
	local isEnemyEnterScope = EntityModule.luaToCshape:GetEnmeyEnterScope()
    -- 每一帧使用技能暂时屏蔽，后面在重做
    for i = 1, #self.skillList do
        local skillInfo = self.skillList[i]
        if not skillInfo.CoolTime then
            skillInfo.CoolTime = Time.deltaTime
        else
            skillInfo.CoolTime = skillInfo.CoolTime + Time.deltaTime
        end

        if self:CanUse(skillInfo) then
            if (skillInfo.NeedEnemy) then
                if isEnemyEnterScope and self:UseSkill(skillInfo) then
                    skillInfo.CoolTime = 0
                end
            else
                if self:UseSkill(skillInfo) then
                    skillInfo.CoolTime = 0
                end
            end
        end
    end
end

--使用技能
function BattleSkillModule:UseSkill(skillInfo)
    local hero = EntityModule.hero
    if not hero then
        return false
    end
    if not skillInfo then
        return false
    end
    local config = Schemes.CatSkill:Get(skillInfo.SkillID)
    if not config or config.AutoFire == 0 then
        return false
    end

    -- if config.Consume_Type == 1 then
    --     if BattleManager:GetCurrEnergy() < config.Consum_Num then
    --         return false
    --     end
    --     BattleManager:UpdateCurrEnergy(-config.Consum_Num)
    -- elseif config.Consume_Type == 2 then
    --     if BattleManager:GetCurrFury() < config.Consum_Num then
    --         return false
    --     end
    --     BattleManager:UpdateCurrFury(-config.Consum_Num)
    -- end

    -- print("skillInfo.CoolTime == " .. skillInfo.CoolTime .. " config.CD = " ..config.CD .. " skillInfo.CoolCD = " ..skillInfo.CoolCD)
    -- self.groupCDDic[skillInfo.GroupCDID].GroupCDCounter = 0
    --print("使用技能：".. skillInfo.SkillID)
    --使用技能 调用C#接口
    EntityModule.luaToCshape:LuaControlPlayerCallSkill(skillInfo.SkillID)

    return true
end

--能不能使用技能
function BattleSkillModule:CanUse(skillInfo)
    -- print("是否可以使用技能")
    local hero = EntityModule.hero
	if not hero then
		return false
	end
    if not skillInfo then
        return false
    end
    local config = Schemes.CatSkill:Get(skillInfo.SkillID)
    if not config then
        return false
    end

    if skillInfo.CoolTime < skillInfo.CoolCD/1000 then
        return false
    end
    return true
end

function BattleSkillModule:AndCDBuff(buffID)
    local buffConfig = Schemes.Buff:Get(buffID)
    if not buffConfig then return end
    local buffEffConfig = Schemes.BuffEffect:Get(buffConfig.EffectID1)
    if not buffEffConfig then return end
    if buffEffConfig.Param1 ~= 23 then return end
    self.buffEffConfig = buffEffConfig
    for i = 1, #self.skillList do
        local skillInfo = self.skillList[i]
        if skillInfo then
            local config = Schemes.CatSkill:Get(skillInfo.SkillID)
            if config then
                skillInfo.CoolCD = config.CD * (1 - self.buffEffConfig.Param3/10000)
                if skillInfo.CoolCD < config.MinCD then
                    skillInfo.CoolCD = config.MinCD
                end
            end
        end
    end
end

function BattleSkillModule:RemoveCDBuff(baseBuffID)
    local buffConfig = Schemes.Buff:Get(baseBuffID)
    if not buffConfig then return end
    local buffEffConfig = Schemes.BuffEffect:Get(buffConfig.EffectID1)
    if not buffEffConfig then return end
    if buffEffConfig.Param1 ~= 23 then return end
    self.buffEffConfig = nil
    for i = 1, #self.skillList do
        local skillInfo = self.skillList[i]
        if skillInfo then
            local config = Schemes.CatSkill:Get(skillInfo.SkillID)
            if config then
                skillInfo.CoolCD = config.CD
            end
        end
    end
end

function BattleSkillModule:GetBattleSkillList()
    return self.skillList
end

function BattleSkillModule:GetBattleSkillBaseIDList()
    local result = {}
    for key, value in pairs(self.skillList) do
        -- local data = Schemes.CatSkill:Get(value.Type)
        if not HelperL.tableContain(result, value.BaseID) then
            table.insert(result, value.BaseID)
        end
    end
    return result
end



--获取技能总等级
function BattleSkillModule:GetSkillTotalLevel()
    local totalLevel = 0
    for i = 1, #self.skillList do
        local skillInfo = self.skillList[i]
        totalLevel = totalLevel + skillInfo.Level
    end
    return totalLevel
end

--获取技能配置
function BattleSkillModule:GetSkillInfo(skillID)
    for i = 1, #self.skillList do
        local skillInfo = self.skillList[i]
        if skillInfo.BaseID == skillID then
            return skillInfo
        end
    end
    return nil
end

--获取技能配置:仅含 ID、类型、等级
function BattleSkillModule:GetEmptySkillInfo(skillID)
    local skillInfo = {}
    local skillConfig = Schemes.CatSkill:Get(skillID)
    skillInfo.SkillID = skillID
    skillInfo.Type = skillConfig.Type
    skillInfo.MinCD = skillConfig.MinCD
    skillInfo.SkillEffectIDs = skillConfig.BuffIDs
    skillInfo.SkillLvl = 0
    -- skillInfo.Level = skillConfig.Level > 1 and skillConfig.Level or 0
    -- skillInfo.Using = false
    -- skillInfo.BaseID = skillInfo.Level == 0 and skillID or (skillID - skillConfig.Level)
    -- skillInfo.ID = skillID
    -- skillInfo.Type = skillConfig.Type

    --print("skillInfo=", json.encode(skillInfo))
    return skillInfo
end

--拥有多少个六星技能
function BattleSkillModule:GetSixStarSkillCount()
    local sixStar = 0
    for i = 1, #self.skillList do
        local skillInfo = self.skillList[i]
        if skillInfo.Level == 6 then
            sixStar = sixStar + 1
        end
    end
    return sixStar
end

function BattleSkillModule:GetSkillCount()
    return #self.skillList
end

--结束战斗时清空技能列表
function BattleSkillModule:EndBattle()
    self.skillList = nil
    -- self.groupCDDic = nil
end
--是否拥有技能
function BattleSkillModule:HadSkillByID(skillID)
	if not skillID then return false end
	for i = 1, #self.skillList do
		local skillInfo = self.skillList[i]
		if skillInfo.BaseID == skillID then
			return true
		end
	end
	return false
end
--是否拥有buff对应的技能
function BattleSkillModule:HadSkillByBuffID(buffID)
	local needBuffID = 0
	if not buffID then return needBuffID end
	for i = 1, #self.skillList do
		local skillInfo = self.skillList[i]
		local maxID = BattleManager.GetSkillMaxID(skillInfo.BaseID)
		--print('skillInfo.BaseID='..skillInfo.BaseID)
		maxID = tonumber(maxID) - 1
		if maxID > 0 then
			local skillConfig = Schemes.CatSkill:Get(maxID)
			local needBuff = skillConfig.CatSkillComb
			-- print("needBuff" .. needBuff)
			if needBuff ~= 0 and needBuff == buffID then
				needBuffID = skillInfo.BaseID
				return needBuffID
			end
		end
	end
	if needBuffID == 0 then
		local curType = 0
		for _, v in pairs(Schemes.CatSkill.items) do
			if curType~=v.Type then
				local maxID = BattleManager.GetSkillMaxID(v.ID)
				maxID = tonumber(maxID) - 1
				local skillConfig = Schemes.CatSkill:Get(maxID)
				
				if skillConfig then 
					local needBuff = skillConfig.CatSkillComb
					--if needBuff then  print('maxID'..maxID..'   needBuff' .. needBuff) end
					if needBuff ~= 0 and needBuff == buffID then
						needBuffID = maxID
						return needBuffID
					end
				end
				curType = v.Type
			end
		end
	end

	return needBuffID
end