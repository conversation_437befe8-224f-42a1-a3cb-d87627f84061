--------------------------------------------------------------------
-- 文件名:	.\GameLua\SlotItemFactory.lua
-- 版  权:	(C) DDF Studio
-- 创建人:	吴奇达
-- 日  期:	2022/12/07 21:03
-- 描  述:	界面槽
--------------------------------------------------------------------

local luaID = ('SlotItem')

---@class SlotItem
local classSlot = {} -- 槽类实现	

--------------------------------------------------------------------
-- 全局：复制一份界面槽，默认只显示图标，其他自己调用接口
--------------------------------------------------------------------

classSlot.__index = classSlot

---创建物品槽
---@param parent integer 父节点
---@return SlotItem
function _GAddSlotItem(parent)
	---@type SlotItem
	local slot = {}
	setmetatable(slot, classSlot)
	slot:Init(parent)
	return slot
end

--------------------------------------------------------------------
-- 是否点击可弹出查看窗口
--------------------------------------------------------------------
function classSlot:EnableClick(flag)
	self.enableClick = flag
	self.Btn_Click.interactable = flag
end

--------------------------------------------------------------------
-- 是否只显示查看窗口
--------------------------------------------------------------------
function classSlot:SetJustShow(justShow)
	self.justShow = justShow
end

--------------------------------------------------------------------
-- 设置大小
--------------------------------------------------------------------
function classSlot:SetSize(width, height)
	self.width = width or self.width
	self.height = height or self.height
	local rectTrans = self.Btn_Click:GetRectTransform()
	rectTrans.sizeDelta = Vector2(self.width, self.height)
	local vfx_trans = self.Img_vfx:GetRectTransform()
	vfx_trans.sizeDelta = Vector2(self.width + 40, self.height + 40)
end

function classSlot:SetIconSize(width, height)
	local w = width or self.width
	local h = height or self.height
	local rectTrans = self.Img_Icon:GetRectTransform()
	rectTrans.sizeDelta = Vector2(w, h)
end

function classSlot:SetBGSize(width, height)
	local w = width or self.width
	local h = height or self.height
	local rectTrans = self.Img_Bg:GetRectTransform()
	rectTrans.sizeDelta = Vector2(w, h)
end

--------------------------------------------------------------------
-- 设置品质
--------------------------------------------------------------------
function classSlot:SetQuaImg(quaImgID)
	AtlasManager:AsyncGetSprite(quaImgID or "kuang_0", self.Img_Bg)
end

--------------------------------------------------------------------
-- 设置图标
--------------------------------------------------------------------
function classSlot:SetIcon(iconID)
	if iconID and tostring(iconID) ~= "0" then
		AtlasManager:AsyncGetSprite(iconID, self.Img_Icon, false, function(sprite, image, param)
			if sprite then
				image.sprite = sprite
				-- image:SetNativeSize()
				image.gameObject:SetActive(true)
			else
				image.gameObject:SetActive(false)
			end
		end)
		self.Img_Icon.gameObject:SetActive(true)
	else
		self.Img_Icon.gameObject:SetActive(false)
	end
end

--------------------------------------------------------------------
-- 设置名称
--------------------------------------------------------------------
function classSlot:SetName(name)
	self.Txt_Name.text = name or ''
end

--------------------------------------------------------------------
-- 显示名称(自动获取物品名称)
---comment
---@param bool boolean 是否显示
---@param _type ?integer 1：居中(默认)，2：顶部
--------------------------------------------------------------------
function classSlot:ShowName(bool, _type)
	self.Txt_Des.text = ''
	self.Txt_Name.text = ''
	if bool then
		local cfg = Schemes:GetGoodsConfig(self.itemID)
		if cfg then
			if _type == 2 then
				self.Txt_Name.text = cfg.GoodsName
			else
				self.Txt_Des.text = cfg.GoodsName
			end
		end
	end
end

--------------------------------------------------------------------
-- 设置描述
--------------------------------------------------------------------
function classSlot:SetDes(des)
	self.Txt_Des.text = des or ''
end

--------------------------------------------------------------------
-- 设置描述2
--------------------------------------------------------------------
function classSlot:SetDes2(des)
	self.Txt_Des2.text = des or ''
end

--------------------------------------------------------------------
-- 设置描述3
--------------------------------------------------------------------
function classSlot:SetDes3(des)
	self.Txt_Des3.text = des or ''
end

--------------------------------------------------------------------
-- 设置数量
--------------------------------------------------------------------
function classSlot:SetCount(num)
	self.Txt_Count.text = num or ''
end

--------------------------------------------------------------------
-- 显示数量(自动获取物品数量)
--------------------------------------------------------------------
function classSlot:ShowCount(bool)
	if bool and self.itemID then
		self.Txt_Count.text = SkepModule:GetGoodsCount(self.itemID)
	else
		self.Txt_Count.text = ''
	end
end

--------------------------------------------------------------------
-- 设置红点
--------------------------------------------------------------------
function classSlot:SetRedDot(isShow)
	self.RedDot.gameObject:SetActive(isShow)
end

--------------------------------------------------------------------
-- 设置物品装备ID：直接设置实体会自动显示物品信息，只是纯图标
--------------------------------------------------------------------
function classSlot:SetItemID(itemID)
	self:Reset()
	local cfg = Schemes:GetGoodsConfig(itemID)
	--判断物品id是否存在
	if cfg then
		self.itemID = itemID
		--获取背包篮子
		local skeps = SkepModule:GetGoodsIdBySkep(itemID)
		if skeps then
			self.entity = skeps:GetEntityByGoodsID(itemID)
		end
	end
	self:UpdateView()
end

--------------------------------------------------------------------
-- 设置实体对象：nil 将清空slot所有显示信息
--------------------------------------------------------------------
function classSlot:SetEntity(entity)
	self:Reset()
	if entity then
		----物品ID
		self.itemID = entity:GetProperty(ENTITY_FIELD.ENTITY_FIELD_ID)
		----物品实体
		self.entity = entity
	end
	self:UpdateView()
end

--------------------------------------------------------------------
-- 显示等级
--------------------------------------------------------------------
function classSlot:ShowLevel(isShow)
	self.Txt_Level.gameObject:SetActive(isShow)
end

--------------------------------------------------------------------
-- 显示箭头
--------------------------------------------------------------------
function classSlot:ShowArrows(isShow)
	self.Img_Arrows.gameObject:SetActive(isShow)
end

--------------------------------------------------------------------
-- 显示/隐藏
--------------------------------------------------------------------
function classSlot:SetActive(isShow)
	self.gameObject:SetActive(isShow)
end

--------------------------------------------------------------------
-- 初始化
---@param parent any
--------------------------------------------------------------------
function classSlot:Init(parent)
	if self.isInit == true then return end

	--已初始化过
	self.isInit = true
	local obj = GameObject.Instantiate(HotResManager.ReadUI('ui/Common/SlotItem'), parent.transform)
	obj.transform.localPosition = Vector3.zero
	local objTrans = obj:GetRectTransform()
	Helper.FillLuaComps(objTrans, self)
	self.gameObject = obj
	self.transform = objTrans

	self.justShow = true
	obj:SetActive(true)

	-- 默认可点击
	self:EnableClick(true)
	-- 点击图标查看物品信息界面
	self.Btn_Click.onClick:AddListenerEx(function()
		self:OnShowTips()
	end)

	self:Reset()
end

--------------------------------------------------------------------
--重置预制体文本
--------------------------------------------------------------------
function classSlot:Reset()
	self.Txt_Count.text = ''
	self.Txt_Name.text = ''
	self.Txt_Des.text = ''
	self.Txt_Des2.text = ''
	self.Txt_Des3.text = ''
	self.Img_Arrows.gameObject:SetActive(false)
	self.Img_vfx.gameObject:SetActive(false)
	self.RedDot.gameObject:SetActive(false)
	self.Img_Select.gameObject:SetActive(false)
	self.Img_Icon.gameObject:SetActive(false)
	self.Txt_Level.gameObject:SetActive(false)
	self.itemID = nil
	self.entity = nil
	self:SetStateIcon(nil)
end

--------------------------------------------------------------------
--不需要点击图标查看物品信息界面
--------------------------------------------------------------------
function classSlot:RemoveBtnListener()
	self.Btn_Click.onClick:RemoveAllListeners();
end

--------------------------------------------------------------------
-- 刷新显示
--------------------------------------------------------------------
function classSlot:UpdateView()
	local iconImg = nil -- 清空图标
	local quaLevel = 0
	if self.itemID then
		local schemeItem = Schemes:GetGoodsConfig(self.itemID)
		if schemeItem then
			iconImg = schemeItem.IconID
			if self.itemID <= DEFINE.MAX_MEDICAMENT_ID then
				quaLevel = schemeItem.Quality
			else
				quaLevel = schemeItem.QualityLevel
			end
			HelperL.SetImageMaterial(self.Img_vfx, quaLevel)
			self.Txt_Level.text = schemeItem.UseLevel .. CommonTextID.LEVEL
		end
	end

	self:SetQuaImg(HelperL.GetImageByQuality(quaLevel))
	self:SetIcon(iconImg)
	-- self:SetSize()
end

--------------------------------------------------------------------
-- 显示物品查看界面
--------------------------------------------------------------------
function classSlot:OnShowTips()
	if not self.itemID then
		return
	end
	if type(self.clickEvent) == "function" then
		self.clickEvent(self.itemID, self.clickEventData)
	else
		UIManager:OpenWnd(WndID.GoodTips, self.itemID)
	end
end

--------------------------------------------------------------------
---设置点击事件
---@param fun fun(itemID:integer, data?:any) 点击事件
---@param data? any 点击事件参数
--------------------------------------------------------------------
function classSlot:SetClick(fun, data)
	self.clickEvent = fun
	self.clickEventData = data
	self:EnableClick(true)
end

--------------------------------------------------------------------
-- 设置选中
---@param bool boolean 是否选中
--------------------------------------------------------------------
function classSlot:SetSelect(bool)
	self.Img_Select.gameObject:SetActive(bool == true)
end

--------------------------------------------------------------------
---设置状态图标
---@param icon string|nil 图标
---@param isSize ?boolean 是自适应大小
--------------------------------------------------------------------
function classSlot:SetStateIcon(icon, isSize)
	if icon and icon ~= "" and icon ~= "0" then
		AtlasManager:AsyncGetSprite(icon, self.Img_StateIcon, isSize)
		self.Img_State.gameObject:SetActive(true)
	else
		self.Img_State.gameObject:SetActive(false)
	end
end
