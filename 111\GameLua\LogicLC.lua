
LogicLC = {}
local bit = require 'bit'
local function GetEveryDayFirstScheme(  )
	-- body
	for i,v in ipairs(Schemes.PrizeTime.items) do
		if v.Condition == 18 then
			return v
		end
	end
end

local function GetContinueRechargeScheme(  )
	local o = {}
	for i,v in ipairs(Schemes.PrizeTime.items) do
		if v.Condition == 19 then
			o[v.ConditionX] = v
		end
	end
	return o
end
local chapterResetTimeIndex = 
{
	[20] = 0,
	[21] = 1,
	[22] = 2,
	[23] = 3,
	[24] = 4,
	[25] = 5,
	[26] = 6,
}
function LogicLC.New()
	local o = {}
	o.props = {}
	o.cooltimelist = {}
	function o:Set(k, v)
		self.props[k] = v
		if k == LOGIC_DATA.DATA_PACKET_VALID_SIZE then
			EventManager.Fire(EventID.PacketSizeChange, v)
		elseif k == LOGIC_DATA.DATA_WAREHOUSE_VALID_SIZE then
			EventManager.Fire(EventID.WarehouseSizeChange, v)
		elseif k == LOGIC_DATA.DATA_TRUNK_TASK_CUR_SERIAL then
			if EntityModule.hero and EntityModule.hero.heroTaskLC then
				EntityModule.hero.heroTaskLC.OnTaskSerialUpdate()
			end
		elseif k == LOGIC_DATA.DATA_LAST_DAYRESET_TIME then
			if EntityModule.hero and EntityModule.hero.heroTaskLC then
				EntityModule.hero.heroTaskLC.OnTaskSerialUpdate()
			end
		end
		EventManager:Fire(EventID.LogicDataChange)
	end

	function o:Get(k)
		local data = self.props[k]
		if data then
			return data
		end
		return 0
	end

	function o:SetCoolTime(k, v)
		local cooldownScheme = Schemes.Cooldown:Get(k)
		if cooldownScheme == nil then
			print('SetCoolTime scheme=nil, id='..k)
			return
		end
		self.cooltimelist[k] = { leftTime=v, beginTime=UnityEngine.Time.time, totalTime=cooldownScheme.Time }
		if cooldownScheme.PublicID > 0 then
			local publicCDScheme = Schemes.Cooldown:Get(cooldownScheme.PublicID)
			if publicCDScheme then
				self:SetCoolTime(cooldownScheme.PublicID, publicCDScheme.Time)
			end
		end
		EventManager.Fire(EventID.HeroUpdateCooldown, k)
	end

	function o:IsCooling(k)
		local info = self.cooltimelist[k]
		if info == nil then
			local cooldownScheme = Schemes.Cooldown:Get(k)
			if cooldownScheme ~= nil and cooldownScheme.PublicID > 0 then
				return self:IsCooling(cooldownScheme.PublicID)
			end
			return false
		end

		local curTime = UnityEngine.Time.time
		if info.leftTime < (curTime - info.beginTime) * 1000 then
			self.cooltimelist[k] = nil
			return false
		end

		return true
	end

	function o:GetLeftCoolTime(k)
		local info = self.cooltimelist[k]
		if info == nil then
			local cooldownScheme = Schemes.Cooldown:Get(k)
			if cooldownScheme ~= nil and cooldownScheme.PublicID > 0 then
				return self:GetLeftCoolTime(cooldownScheme.PublicID)
			end
			return 0, 0
		end

		local curTime = UnityEngine.Time.time
		if info.leftTime < (curTime - info.beginTime) * 1000 then
			self.cooltimelist[k] = nil
			return 0, 0
		end

		local leftTime = info.leftTime - (curTime - info.beginTime) * 1000
		local rate = 0
		if info.totalTime > 0 then
			rate = leftTime / info.totalTime
		end
		return leftTime, rate
	end
	-- 计算免费猎魂次数
	function o:GetGemDrawFreeTimes(  )
		local constFreeTimes = Schemes.ConstValue:Get(CONST_VALUE.CONST_GEMCREATE_FREECOUNT)
		local heroViplv = EntityModule.hero.propertyLC:GetProperty(PLAYER_FIELD.PLAYER_FIELD_VIPLEVEL)
		local heroVipStr = 'Vip'..heroViplv
		local vipAddTimes = Schemes.VipValue:Get(VIPID.VIPID_HUNT_SOUL)
		if vipAddTimes[heroVipStr] then
			constFreeTimes = constFreeTimes + vipAddTimes[heroVipStr]
		end
		return constFreeTimes
	end
	-- 猎魂免费使用次数
	function o:GetGemDrawUseTimes(  )
		return self:GetLogicByte(LOGIC_DATA.DATA_GEMCREATE_FREECOUNT, 3)		
	end
	-- 洗炼免费使用次数
	function o:GetPolish1UseTimes(  )
		return self:GetLogicByte(LOGIC_DATA.DATA_GEMCREATE_FREECOUNT, 2)
	end
	-- 精炼免费使用次数
	function o:GetPolish2UseTimes(  )
		return self:GetLogicByte(LOGIC_DATA.DATA_GEMCREATE_FREECOUNT, 1)
	end
	-- 帮会副本今日已重置次数
	function o:GetSocietyTowerTodayResetTimes()
		return self:GetLogicByte(LOGIC_DATA.DATA_GEMCREATE_FREECOUNT, 0)
	end

	function o:HadBoughtCardID( cardID )
		local rechargeCardScheme = Schemes.RechargeCard:Get(cardID)
		if not rechargeCardScheme then
			return true
		end
		local value = self:Get(rechargeCardScheme.SaveParam1)
		if bit.band(bit.lshift(1, rechargeCardScheme.SaveParam2), value) ~= 0 then
			return true
		end
		return false
	end
	function o:HadBoughtCountryFund(  )
		return self:HadBoughtCardID(100)
	end

	--- 是否可领取每日充值奖励
	function o:CanGetEveryDayPrize(  )
		o.prizeTimeScheme = o.prizeTimeScheme or GetEveryDayFirstScheme()
		if not o.prizeTimeScheme then
			warn(" Schemes.PrizeTime. GetEveryDayFirstScheme 无配置")
		end
		local value = self:Get(o.prizeTimeScheme.SaveID2) or 0
		return bit.band(bit.lshift(1, o.prizeTimeScheme.SaveIndex), value) ~= 0
	end
	--- 是否可以领取连续充值奖励
	function o:CanGetContinueDaysPrize( days )
		-- body
		if days < 1 or days > 7 or days == 1 or days == 4 or days == 6 then return false end
		self.continueDaysConfig = self.continueDaysConfig or GetContinueRechargeScheme()
		local continueDays = self:Get( LOGIC_DATA.DATA_CONTINUE_RECHARGE_DAYS)
		if continueDays < days then return false end
		local prizeTimeScheme = self.continueDaysConfig[days]
		if not prizeTimeScheme then
			warn(string.format(" Schemes.PrizeTime %d 无配置 ", days ) )
			return false
		end
		local value = self:Get( prizeTimeScheme.SaveID2)
		return bit.band(bit.lshift(1, prizeTimeScheme.SaveIndex), value) ~= 0
	end
	-- 获取副本今日重置次数
	function o:GetTodayEctypeResetTime(ectypeScheme)
		local index = chapterResetTimeIndex[ectypeScheme.ID]
		if not index then
			warn("该副本不支持重置次数"..ectypeScheme.EctypeID)
			return 0
		end
		local logicID = index >= 4 and LOGIC_DATA.DATA_ECTYPE_RESETTIMES2 or LOGIC_DATA.DATA_ECTYPE_RESETTIMES1
		local value = self:Get(logicID)
		index = index % 4
		local data = bit.band(bit.lshift(255, index * 8), value)
		return bit.rshift(data, index * 8)
	end
	-- 获取醉酒度
	function o:GetAlcoholDegree()
		return self.alcoholDegree or 0
	end

	function o:GetLogicByte(logicID, index)
		if index < 0 or index > 4 then
			warn(" logic lc  get logic byte")
			return 0
		end
		local value = self:Get(logicID)
		local data = bit.band(bit.lshift(255, index * 8), value)
		return bit.rshift(data, index * 8)
	end
	return o
end
