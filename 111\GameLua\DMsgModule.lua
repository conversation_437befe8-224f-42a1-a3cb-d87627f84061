-- 基本模块定义
ENDPOINT = {
	ENDPOINT_NONE = 0,      -- 未定义
	ENDPOINT_GAMESERVER = 1, -- 游戏服务器
	ENDPOINT_GAMECLIENT = 2, -- 游戏客户端
	ENDPOINT_CENTRESERVER = 3, -- 中心服务器
	ENDPOINT_CENTRECLIENT = 4, -- 中心客户端
	ENDPOINT_AGENTSERVER = 5, -- 代理服务器
	ENDPOINT_AGENTCLIENT = 6, -- 代理客户端
	ENDPOINT_DATASERVER = 7, -- 数据服务器
	ENDPOINT_DATACLIENT = 8, -- 数据客户端
	ENDPOINT_LOGINSERVER = 9, -- 登录服务器
	ENDPOINT_GSPROXY = 10, -- 转发器
	ENDPOINT_MAXID = 11,    -- 最大值
}

MSG_MODULEID = {
	MSG_MODULEID_NONE = 0,     -- 未定义
	MSG_MODULEID_CENTRE = 1,   -- 中心服模块
	MSG_MODULEID_AGENT = 2,    -- 代理服模块
	MSG_MODULEID_DATABASE = 3, -- 数据服模块
	MSG_MODULEID_LOGIN = 4,    -- 登录服模块
	MSG_MODULEID_GAMEBEGIN = 10, -- 主游戏模块
	MSG_MODULEID_ENTITY = 11,  -- 实体模块
	MSG_MODULEID_WORLD = 12,   -- 世界模块
	MSG_MODULEID_SKEP = 13,    -- 篮子模块
	MSG_MODULEID_LUA = 14,     -- Lua模块
	MSG_MODULEID_SKILL = 15,   -- 技能模块
	MSG_MODULEID_CHAT = 16,    -- 聊天模块
	MSG_MODULEID_EMAIL = 17,   -- 邮件模块
	MSG_MODULEID_FRIEND = 18,  -- 好友模块
	MSG_MODULEID_STORE = 19,   -- 商店模块
	MSG_MODULEID_GAME = 20,    -- 玩法模块
	MSG_MODULEID_RANK = 21,    -- 排行榜模块
	MSG_MODULEID_SOCIETY = 22, -- 帮会模块
	MSG_MODULEID_COUNTRY = 23, -- 国家模块
	MSG_MODULEID_AI = 24,      -- AI模块
	MSG_MODULEID_GLOBALGAME = 26, -- 跨服模块
	MSG_MODULEID_HB = 27,    -- 心跳模块
	MSG_MODULEID_MAXID = 28    -- 最大模块ID
};
