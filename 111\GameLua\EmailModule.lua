--[[
********************************************************************
    created:
    author :
    purpose:    邮件模块
*********************************************************************
--]]


---邮件数据
---@class EmailData
---@field EmailID integer 邮件ID
---@field Title string 标题
---@field SendTime integer 发送时间
---@field ValidTime integer 有效时间
---@field MarkRead integer 是否已读，0表示未读，1表示已读，2表示已领取
---@field HasAdjunct boolean 是否有附件


---邮件附件数据
---@class AdjunctData
---@field NumProp integer[] 数量
---@field BindFlag integer[] 绑定


---邮件内容数据
---@class EmailContent
---@field EmailID integer 邮件ID
---@field Title string 标题
---@field Content string 内容
---@field AdjunctList AdjunctData[] 附件


require "EmailMessage_pb"

EmailModule = {}
---邮件列表
---@type EmailData[]
EmailModule.emailList = {}
---邮件列表2(字典类型)
---@type EmailData[]
EmailModule.emailList2 = nil
---邮件附件列表
---@type EmailContent[]
EmailModule.adjunctList = {}

function EmailModule.Handle(action, data)
	if action == EmailMessage_pb.MSG_EMAIL_GETLIST then
		local m = EmailMessage_pb.SC_Email_GetEmailList()
		m:ParseFromString(data)
		EmailModule.SC_Email_GetEmailList(m)
	elseif action == EmailMessage_pb.MSG_EMAIL_READEMAIL then
		local m = EmailMessage_pb.SC_Email_ReadMail()
		m:ParseFromString(data)
		EmailModule.SC_Email_ReadMail(m)
	elseif action == EmailMessage_pb.MSG_EMAIL_DELETE then
		local m = EmailMessage_pb.SC_Email_DeleteEmail()
		m:ParseFromString(data)
		EmailModule.SC_Email_DeleteEmail(m)
	elseif action == EmailMessage_pb.MSG_EMAIL_ADJUNCT then
		local m = EmailMessage_pb.SC_Email_GetAdjunct()
		m:ParseFromString(data)
		EmailModule.SC_Email_GetAdjunct(m)
	elseif action == EmailMessage_pb.MSG_EMAIL_NEWEMAIL then
		--TODO 显示红点用
		EmailModule.RequestGetEmailList(0, 0)
		EventManager:Fire(EventID.OnNewEmail)
	end
end

---获取邮件数据列表
---@return EmailData[]
function EmailModule.GetEmailList()
	return EmailModule.emailList
end

---根据ID获取邮件
---@param emailID integer
---@return EmailData
function EmailModule.GetEmailById(emailID)
	if EmailModule.emailList2 == nil then
		EmailModule.emailList2 = {}
		for _, v in ipairs(EmailModule.emailList) do
			EmailModule.emailList2[v.EmailID] = v
		end
	end
	return EmailModule.emailList2[emailID]
end

---读取邮件内容
---@param emailID integer
---@return EmailContent
function EmailModule.GetEmailAdjunct(emailID)
	return EmailModule.adjunctList[emailID]
end

---获取邮件列表--回应
---@param m {EmailNum:integer, EmailList:EmailData[]}
function EmailModule.SC_Email_GetEmailList(m)
	EmailModule.emailList = m.EmailList
	EmailModule.emailList2 = nil
	EventManager:Fire(EventID.OnGetEmailList)
end

---读取邮件内容--回应
---@param m EmailContent
function EmailModule.SC_Email_ReadMail(m)
	EmailModule.adjunctList[m.EmailID] = m
	local data = EmailModule.GetEmailById(m.EmailID)
	if data then
		if data.MarkRead == 0 then
			data.MarkRead = 1
		end
	end
	EventManager:Fire(EventID.OnGetEmailInfo)
end

---删除邮件--回应
---@param m {EmailID:integer, Result:integer}
function EmailModule.SC_Email_DeleteEmail(m)
	if m.Result == RESULT_CODE_BASE.RESULT_COMMON_SUCCEED[1] then
		for i, v in ipairs(EmailModule.emailList) do
			if v.EmailID == m.EmailID then
				table.remove(EmailModule.emailList, i)
				EmailModule.emailList2[v.EmailID] = nil
				print('-----删除邮件--回应----3------', v.EmailID, #EmailModule.emailList)
				break
			end
		end
		EmailModule.adjunctList[m.EmailID] = nil
	end
	EventManager:Fire(EventID.OnDeleteMail)
end

--提取邮件附件--回应
---@param m {EmailID:integer, Result:integer}
function EmailModule.SC_Email_GetAdjunct(m)
	if m.Result == RESULT_CODE_BASE.RESULT_COMMON_SUCCEED[1] then
		local data = EmailModule.GetEmailById(m.EmailID)
		if data then
			data.MarkRead = 2
		end
	end
	EventManager:Fire(EventID.OnGetEmailAdjunct)
end

--请求邮件列表
function EmailModule.RequestGetEmailList(getTime, refresh)
	local m = EmailMessage_pb.CS_Email_GetEmailList()
	m.GetTime = getTime or 0
	m.Refresh = refresh or 0
	Premier.Instance:GetNetwork():SendFromLua(
		ENDPOINT.ENDPOINT_GAMECLIENT,
		ENDPOINT.ENDPOINT_GAMESERVER,
		MSG_MODULEID.MSG_MODULEID_EMAIL,
		EmailMessage_pb.MSG_EMAIL_GETLIST,
		m:SerializeToString()
	)
end

--请求获取单个邮件信息
function EmailModule.RequestGetEmailInfo(emailId)
	local m = EmailMessage_pb.CS_Email_ReadMail()
	m.EmailID = emailId
	Premier.Instance:GetNetwork():SendFromLua(
		ENDPOINT.ENDPOINT_GAMECLIENT,
		ENDPOINT.ENDPOINT_GAMESERVER,
		MSG_MODULEID.MSG_MODULEID_EMAIL,
		EmailMessage_pb.MSG_EMAIL_READEMAIL,
		m:SerializeToString()
	)
end

--请求删除邮件
function EmailModule.RequestDelectEmail(emailId)
	local m = EmailMessage_pb.CS_Email_DeleteEmail()
	m.EmailID = emailId
	Premier.Instance:GetNetwork():SendFromLua(
		ENDPOINT.ENDPOINT_GAMECLIENT,
		ENDPOINT.ENDPOINT_GAMESERVER,
		MSG_MODULEID.MSG_MODULEID_EMAIL,
		EmailMessage_pb.MSG_EMAIL_DELETE,
		m:SerializeToString()
	)
end

--请求获取邮件附件
function EmailModule.RequestGetMailAdjunct(emailId)
	local m = EmailMessage_pb.CS_Email_GetAdjunct()
	m.EmailID = emailId
	Premier.Instance:GetNetwork():SendFromLua(
		ENDPOINT.ENDPOINT_GAMECLIENT,
		ENDPOINT.ENDPOINT_GAMESERVER,
		MSG_MODULEID.MSG_MODULEID_EMAIL,
		EmailMessage_pb.MSG_EMAIL_ADJUNCT,
		m:SerializeToString()
	)
end

Premier.Instance:RegisterLuaModule(MSG_MODULEID.MSG_MODULEID_EMAIL, 'EmailModule.Handle')
