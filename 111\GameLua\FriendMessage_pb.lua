-- Generated By protoc-gen-lua Do not Edit
local protobuf = require "protobuf/protobuf"
module('FriendMessage_pb')
local pb = {}


pb.MSG_FRIEND_ACTIONID = protobuf.EnumDescriptor();
pb.MSG_FRIEND_ACTIONID_MSG_FRIEND_NONE_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_FRIEND_ACTIONID_MSG_FRIEND_GETLIST_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_FRIEND_ACTIONID_MSG_FRIEND_ADD_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_FRIEND_ACTIONID_MSG_FRIEND_DELETE_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_FRIEND_ACTIONID_MSG_FRIEND_SENDCHAT_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_FRIEND_ACTIONID_MSG_FRIEND_SENDCHATRETURN_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_FRIEND_ACTIONID_MSG_FRIEND_RECOMMEND_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_FRIEND_ACTIONID_MSG_OTHERPLAYER_INFO_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_FRIEND_ACTIONID_MSG_CHATOFFLINE_LIST_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_FRIEND_ACTIONID_MSG_DELETE_CHATOFFLINE_LIST_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_FRIEND_ACTIONID_MSG_FRIEND_ADD_REPONSE_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_FRIEND_ACTIONID_MSG_FRIEND_ADD_RESULT_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_FRIEND_ACTIONID_MSG_FRIEND_ADD_OPT_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_FRIEND_ACTIONID_MSG_FRIEND_VALUE_SYNC_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_FRIEND_ACTIONID_MSG_FRIEND_QUERY_BY_NAME_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_FRIEND_ACTIONID_MSG_BLACK_LIST_ADD_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_FRIEND_ACTIONID_MSG_FRIEND_QUERY_BEFORECHAT_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_FRIEND_ACTIONID_MSG_QUERY_INFORMATION_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_FRIEND_ACTIONID_MSG_FRIENDBLESS_UPGRADENOTICE_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_FRIEND_ACTIONID_MSG_FRIENDBLESS_SENDBLESS_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_FRIEND_ACTIONID_MSG_FRIEND_VALUE_PRIZE_ENUM = protobuf.EnumValueDescriptor();
pb.FRIENDDATA = protobuf.Descriptor();
pb.FRIENDDATA_FRIENDID_FIELD = protobuf.FieldDescriptor();
pb.FRIENDDATA_NAME_FIELD = protobuf.FieldDescriptor();
pb.FRIENDDATA_RELATIONTYPE_FIELD = protobuf.FieldDescriptor();
pb.FRIENDDATA_LEVEL_FIELD = protobuf.FieldDescriptor();
pb.FRIENDDATA_VOCATION_FIELD = protobuf.FieldDescriptor();
pb.FRIENDDATA_POWER_FIELD = protobuf.FieldDescriptor();
pb.FRIENDDATA_ISONLINE_FIELD = protobuf.FieldDescriptor();
pb.FRIENDDATA_FRIENDVALUE_FIELD = protobuf.FieldDescriptor();
pb.FRIENDDATA_VIP_FIELD = protobuf.FieldDescriptor();
pb.FRIENDDATA_COUNTRY_FIELD = protobuf.FieldDescriptor();
pb.FRIENDDATA_BLUEVIPLEVEL_FIELD = protobuf.FieldDescriptor();
pb.FRIENDDATA_BLUEVIPTYPE_FIELD = protobuf.FieldDescriptor();
pb.FRIENDDATA_TACITVALUE_FIELD = protobuf.FieldDescriptor();
pb.OTHERPLAYERDATA = protobuf.Descriptor();
pb.OTHERPLAYERDATA_OTHERPLAYER_FIELD = protobuf.FieldDescriptor();
pb.CS_FRIEND_GETFRIENDLIST = protobuf.Descriptor();
pb.SC_FRIEND_GETFRIENDLIST = protobuf.Descriptor();
pb.SC_FRIEND_GETFRIENDLIST_FRIENDNUM_FIELD = protobuf.FieldDescriptor();
pb.SC_FRIEND_GETFRIENDLIST_FRIENDLIST_FIELD = protobuf.FieldDescriptor();
pb.CS_FRIEND_ADD = protobuf.Descriptor();
pb.CS_FRIEND_ADD_ENTITYUID_FIELD = protobuf.FieldDescriptor();
pb.CS_FRIEND_ADD_RELATIONTYPE_FIELD = protobuf.FieldDescriptor();
pb.SC_FRIEND_ADD_RESPONSE = protobuf.Descriptor();
pb.SC_FRIEND_ADD_RESPONSE_FRIENDID_FIELD = protobuf.FieldDescriptor();
pb.SC_FRIEND_ADD_RESPONSE_ACTORNAME_FIELD = protobuf.FieldDescriptor();
pb.SC_FRIEND_ADD_RESPONSE_LEVEL_FIELD = protobuf.FieldDescriptor();
pb.SC_FRIEND_ADD_RESPONSE_VOCATION_FIELD = protobuf.FieldDescriptor();
pb.SC_FRIEND_ADD_RESPONSE_COUNTRY_FIELD = protobuf.FieldDescriptor();
pb.SC_FRIEND_ADD_RESPONSE_POWER_FIELD = protobuf.FieldDescriptor();
pb.SC_FRIEND_ADD_RESPONSE_VIP_FIELD = protobuf.FieldDescriptor();
pb.SC_FRIEND_ADD_RESPONSE_RELATIONTYPE_FIELD = protobuf.FieldDescriptor();
pb.SC_FRIEND_ADD_RESPONSE_BLUEVIPLEVEL_FIELD = protobuf.FieldDescriptor();
pb.SC_FRIEND_ADD_RESPONSE_BLUEVIPTYPE_FIELD = protobuf.FieldDescriptor();
pb.CS_FRIEND_ADD_OPT = protobuf.Descriptor();
pb.CS_FRIEND_ADD_OPT_ENTITYUID_FIELD = protobuf.FieldDescriptor();
pb.CS_FRIEND_ADD_OPT_RELATIONTYPE_FIELD = protobuf.FieldDescriptor();
pb.CS_FRIEND_ADD_OPT_OPT_FIELD = protobuf.FieldDescriptor();
pb.SC_FRIEND_ADD_RESULT = protobuf.Descriptor();
pb.SC_FRIEND_ADD_RESULT_RESULT_FIELD = protobuf.FieldDescriptor();
pb.SC_FRIEND_ADD_RESULT_RELATIONTYPE_FIELD = protobuf.FieldDescriptor();
pb.SC_FRIEND_ADD_RESULT_FRIENDLIST_FIELD = protobuf.FieldDescriptor();
pb.CS_FRIEND_DELETE = protobuf.Descriptor();
pb.CS_FRIEND_DELETE_FRIENDID_FIELD = protobuf.FieldDescriptor();
pb.CS_FRIEND_DELETE_RELATIONTYPE_FIELD = protobuf.FieldDescriptor();
pb.SC_FRIEND_DELETE = protobuf.Descriptor();
pb.SC_FRIEND_DELETE_FRIENDID_FIELD = protobuf.FieldDescriptor();
pb.SC_FRIEND_DELETE_RELATIONTYPE_FIELD = protobuf.FieldDescriptor();
pb.SC_FRIEND_DELETE_RESULT_FIELD = protobuf.FieldDescriptor();
pb.CS_FRIEND_SENDCHAT = protobuf.Descriptor();
pb.CS_FRIEND_SENDCHAT_FRIENDID_FIELD = protobuf.FieldDescriptor();
pb.CS_FRIEND_SENDCHAT_CONTENT_FIELD = protobuf.FieldDescriptor();
pb.SC_FRIEND_SENDCHAT = protobuf.Descriptor();
pb.SC_FRIEND_SENDCHAT_FRIENDDATA_FIELD = protobuf.FieldDescriptor();
pb.SC_FRIEND_SENDCHAT_CONTENT_FIELD = protobuf.FieldDescriptor();
pb.SC_FRIEND_SENDCHATRETURN = protobuf.Descriptor();
pb.SC_FRIEND_SENDCHATRETURN_RESULT_FIELD = protobuf.FieldDescriptor();
pb.SC_FRIEND_SENDCHATRETURN_FRIENDID_FIELD = protobuf.FieldDescriptor();
pb.SC_FRIEND_SENDCHATRETURN_CONTENT_FIELD = protobuf.FieldDescriptor();
pb.CS_FRIEND_RECOMMEND = protobuf.Descriptor();
pb.SC_FRIEND_RECOMMEND = protobuf.Descriptor();
pb.SC_FRIEND_RECOMMEND_FRIENDNUM_FIELD = protobuf.FieldDescriptor();
pb.SC_FRIEND_RECOMMEND_FRIENDLIST_FIELD = protobuf.FieldDescriptor();
pb.CS_OTHER_PLAYER = protobuf.Descriptor();
pb.CS_OTHER_PLAYER_OTHERPLAYERID_FIELD = protobuf.FieldDescriptor();
pb.SC_CHATOFFLINE_LIST = protobuf.Descriptor();
pb.SC_CHATOFFLINE_LIST_CHATOFFLINEDATA = protobuf.Descriptor();
pb.SC_CHATOFFLINE_LIST_CHATOFFLINEDATA_SENDACTORID_FIELD = protobuf.FieldDescriptor();
pb.SC_CHATOFFLINE_LIST_CHATOFFLINEDATA_SENDACTORNAME_FIELD = protobuf.FieldDescriptor();
pb.SC_CHATOFFLINE_LIST_CHATOFFLINEDATA_SENDTIME_FIELD = protobuf.FieldDescriptor();
pb.SC_CHATOFFLINE_LIST_CHATOFFLINEDATA_TALKCONTENT_FIELD = protobuf.FieldDescriptor();
pb.SC_CHATOFFLINE_LIST_CHATOFFLINEDATA_LEVEL_FIELD = protobuf.FieldDescriptor();
pb.SC_CHATOFFLINE_LIST_CHATOFFLINEDATA_VOCATION_FIELD = protobuf.FieldDescriptor();
pb.SC_CHATOFFLINE_LIST_CHATOFFLINEDATA_POWER_FIELD = protobuf.FieldDescriptor();
pb.SC_CHATOFFLINE_LIST_CHATOFFLINEDATA_ISONLINE_FIELD = protobuf.FieldDescriptor();
pb.SC_CHATOFFLINE_LIST_CHATOFFLINEDATA_VIPLEVEL_FIELD = protobuf.FieldDescriptor();
pb.SC_CHATOFFLINE_LIST_CHATOFFLINEDATA_COUNTRY_FIELD = protobuf.FieldDescriptor();
pb.SC_CHATOFFLINE_LIST_CHATOFFLINEDATA_RELATIONTYPE_FIELD = protobuf.FieldDescriptor();
pb.SC_CHATOFFLINE_LIST_CHATOFFLINEDATA_FRIENDVALUE_FIELD = protobuf.FieldDescriptor();
pb.SC_CHATOFFLINE_LIST_CHATNUM_FIELD = protobuf.FieldDescriptor();
pb.SC_CHATOFFLINE_LIST_CHATOFFLINEDATA_FIELD = protobuf.FieldDescriptor();
pb.CS_DELETE_CHATOFFLINE_LIST = protobuf.Descriptor();
pb.SC_FRIEND_VALUE_SYNC = protobuf.Descriptor();
pb.SC_FRIEND_VALUE_SYNC_MASTERID_FIELD = protobuf.FieldDescriptor();
pb.SC_FRIEND_VALUE_SYNC_FRIENDID_FIELD = protobuf.FieldDescriptor();
pb.SC_FRIEND_VALUE_SYNC_FRIENDVALUE_FIELD = protobuf.FieldDescriptor();
pb.SC_FRIEND_VALUE_SYNC_TACITVALUE_FIELD = protobuf.FieldDescriptor();
pb.CS_FRIEND_QUERY_BY_NAME = protobuf.Descriptor();
pb.CS_FRIEND_QUERY_BY_NAME_NAME_FIELD = protobuf.FieldDescriptor();
pb.SC_FRIEND_QUERY_RESULT = protobuf.Descriptor();
pb.SC_FRIEND_QUERY_RESULT_ENTITYUID_FIELD = protobuf.FieldDescriptor();
pb.SC_FRIEND_QUERY_RESULT_NAME_FIELD = protobuf.FieldDescriptor();
pb.SC_FRIEND_QUERY_RESULT_LEVEL_FIELD = protobuf.FieldDescriptor();
pb.SC_FRIEND_QUERY_RESULT_VOCATION_FIELD = protobuf.FieldDescriptor();
pb.SC_FRIEND_QUERY_RESULT_POWER_FIELD = protobuf.FieldDescriptor();
pb.SC_FRIEND_QUERY_RESULT_ISONLINE_FIELD = protobuf.FieldDescriptor();
pb.SC_FRIEND_QUERY_RESULT_VIP_FIELD = protobuf.FieldDescriptor();
pb.SC_FRIEND_QUERY_RESULT_COUNTRY_FIELD = protobuf.FieldDescriptor();
pb.CS_BLACK_LIST_ADD = protobuf.Descriptor();
pb.CS_BLACK_LIST_ADD_NAME_FIELD = protobuf.FieldDescriptor();
pb.CS_FRIEND_QUERY_BEFORECHAT = protobuf.Descriptor();
pb.CS_FRIEND_QUERY_BEFORECHAT_ACTORID_FIELD = protobuf.FieldDescriptor();
pb.SC_FRIEND_QUERY_BEFORECHAT = protobuf.Descriptor();
pb.SC_FRIEND_QUERY_BEFORECHAT_ACTORID_FIELD = protobuf.FieldDescriptor();
pb.SC_FRIEND_QUERY_BEFORECHAT_NAME_FIELD = protobuf.FieldDescriptor();
pb.SC_FRIEND_QUERY_BEFORECHAT_LEVEL_FIELD = protobuf.FieldDescriptor();
pb.SC_FRIEND_QUERY_BEFORECHAT_VOCATION_FIELD = protobuf.FieldDescriptor();
pb.SC_FRIEND_QUERY_BEFORECHAT_POWER_FIELD = protobuf.FieldDescriptor();
pb.SC_FRIEND_QUERY_BEFORECHAT_ISONLINE_FIELD = protobuf.FieldDescriptor();
pb.SC_FRIEND_QUERY_BEFORECHAT_VIP_FIELD = protobuf.FieldDescriptor();
pb.SC_FRIEND_QUERY_BEFORECHAT_COUNTRY_FIELD = protobuf.FieldDescriptor();
pb.CS_QUERY_PLAYER_INFO = protobuf.Descriptor();
pb.CS_QUERY_PLAYER_INFO_ACTORID_FIELD = protobuf.FieldDescriptor();
pb.SC_QUERY_PLAYER_INFO = protobuf.Descriptor();
pb.SC_QUERY_PLAYER_INFO_PLAYERFIELD = protobuf.Descriptor();
pb.SC_QUERY_PLAYER_INFO_PLAYERFIELD_PROPVALUE_FIELD = protobuf.FieldDescriptor();
pb.SC_QUERY_PLAYER_INFO_EQUIPMENT = protobuf.Descriptor();
pb.SC_QUERY_PLAYER_INFO_EQUIPMENT_EQUIPSLOT_FIELD = protobuf.FieldDescriptor();
pb.SC_QUERY_PLAYER_INFO_EQUIPMENT_EQUIPID_FIELD = protobuf.FieldDescriptor();
pb.SC_QUERY_PLAYER_INFO_GODWEAPONSPIRIT = protobuf.Descriptor();
pb.SC_QUERY_PLAYER_INFO_GODWEAPONSPIRIT_POS_FIELD = protobuf.FieldDescriptor();
pb.SC_QUERY_PLAYER_INFO_GODWEAPONSPIRIT_SPIRITID_FIELD = protobuf.FieldDescriptor();
pb.SC_QUERY_PLAYER_INFO_MOUNTEQUIP = protobuf.Descriptor();
pb.SC_QUERY_PLAYER_INFO_MOUNTEQUIP_EQUIPSLOT_FIELD = protobuf.FieldDescriptor();
pb.SC_QUERY_PLAYER_INFO_MOUNTEQUIP_EQUIPID_FIELD = protobuf.FieldDescriptor();
pb.SC_QUERY_PLAYER_INFO_ACTORID_FIELD = protobuf.FieldDescriptor();
pb.SC_QUERY_PLAYER_INFO_POWER_FIELD = protobuf.FieldDescriptor();
pb.SC_QUERY_PLAYER_INFO_VIP_FIELD = protobuf.FieldDescriptor();
pb.SC_QUERY_PLAYER_INFO_NAME_FIELD = protobuf.FieldDescriptor();
pb.SC_QUERY_PLAYER_INFO_VOCATION_FIELD = protobuf.FieldDescriptor();
pb.SC_QUERY_PLAYER_INFO_LEVEL_FIELD = protobuf.FieldDescriptor();
pb.SC_QUERY_PLAYER_INFO_SOCIETY_FIELD = protobuf.FieldDescriptor();
pb.SC_QUERY_PLAYER_INFO_PK_FIELD = protobuf.FieldDescriptor();
pb.SC_QUERY_PLAYER_INFO_PLAYERFIELDLIST_FIELD = protobuf.FieldDescriptor();
pb.SC_QUERY_PLAYER_INFO_EQUIPMENTLIST_FIELD = protobuf.FieldDescriptor();
pb.SC_QUERY_PLAYER_INFO_GODWEAPONID_FIELD = protobuf.FieldDescriptor();
pb.SC_QUERY_PLAYER_INFO_GODWEAPONSPIRITLIST_FIELD = protobuf.FieldDescriptor();
pb.SC_QUERY_PLAYER_INFO_MOUNTID_FIELD = protobuf.FieldDescriptor();
pb.SC_QUERY_PLAYER_INFO_MOUNTEQUIPLIST_FIELD = protobuf.FieldDescriptor();
pb.SC_QUERY_PLAYER_INFO_WINGID_FIELD = protobuf.FieldDescriptor();
pb.SC_FRIENDBLESS_UPGRADENOTICE = protobuf.Descriptor();
pb.SC_FRIENDBLESS_UPGRADENOTICE_ACTORID_FIELD = protobuf.FieldDescriptor();
pb.SC_FRIENDBLESS_UPGRADENOTICE_ACTORCOUNTRY_FIELD = protobuf.FieldDescriptor();
pb.SC_FRIENDBLESS_UPGRADENOTICE_ACTORVOCATION_FIELD = protobuf.FieldDescriptor();
pb.SC_FRIENDBLESS_UPGRADENOTICE_ACTORLEVEL_FIELD = protobuf.FieldDescriptor();
pb.SC_FRIENDBLESS_UPGRADENOTICE_ACTORNAME_FIELD = protobuf.FieldDescriptor();
pb.SC_FRIENDBLESS_SENDBLESS = protobuf.Descriptor();
pb.SC_FRIENDBLESS_SENDBLESS_TARGETTYPE_FIELD = protobuf.FieldDescriptor();
pb.SC_FRIENDBLESS_SENDBLESS_ACTORID_FIELD = protobuf.FieldDescriptor();
pb.SC_FRIENDBLESS_SENDBLESS_ACTORCOUNTRY_FIELD = protobuf.FieldDescriptor();
pb.SC_FRIENDBLESS_SENDBLESS_ACTORVOCATION_FIELD = protobuf.FieldDescriptor();
pb.SC_FRIENDBLESS_SENDBLESS_ACTORNAME_FIELD = protobuf.FieldDescriptor();
pb.SC_FRIENDBLESS_SENDBLESS_BLESSTYPE_FIELD = protobuf.FieldDescriptor();
pb.SC_FRIENDBLESS_SENDBLESS_PRIZEVALUE_FIELD = protobuf.FieldDescriptor();
pb.SC_FRIENDBLESS_SENDBLESS_RANDTIP_FIELD = protobuf.FieldDescriptor();
pb.SC_FRIEND_VALUE_PRIZE = protobuf.Descriptor();
pb.SC_FRIEND_VALUE_PRIZE_FRIENDNAME_FIELD = protobuf.FieldDescriptor();
pb.SC_FRIEND_VALUE_PRIZE_FRIENDCOUNTRY_FIELD = protobuf.FieldDescriptor();
pb.SC_FRIEND_VALUE_PRIZE_FRIENDVALUE_FIELD = protobuf.FieldDescriptor();
pb.SC_FRIEND_VALUE_PRIZE_PRIZEID_FIELD = protobuf.FieldDescriptor();

pb.MSG_FRIEND_ACTIONID_MSG_FRIEND_NONE_ENUM.name = "MSG_FRIEND_NONE"
pb.MSG_FRIEND_ACTIONID_MSG_FRIEND_NONE_ENUM.index = 0
pb.MSG_FRIEND_ACTIONID_MSG_FRIEND_NONE_ENUM.number = 0
pb.MSG_FRIEND_ACTIONID_MSG_FRIEND_GETLIST_ENUM.name = "MSG_FRIEND_GETLIST"
pb.MSG_FRIEND_ACTIONID_MSG_FRIEND_GETLIST_ENUM.index = 1
pb.MSG_FRIEND_ACTIONID_MSG_FRIEND_GETLIST_ENUM.number = 1
pb.MSG_FRIEND_ACTIONID_MSG_FRIEND_ADD_ENUM.name = "MSG_FRIEND_ADD"
pb.MSG_FRIEND_ACTIONID_MSG_FRIEND_ADD_ENUM.index = 2
pb.MSG_FRIEND_ACTIONID_MSG_FRIEND_ADD_ENUM.number = 2
pb.MSG_FRIEND_ACTIONID_MSG_FRIEND_DELETE_ENUM.name = "MSG_FRIEND_DELETE"
pb.MSG_FRIEND_ACTIONID_MSG_FRIEND_DELETE_ENUM.index = 3
pb.MSG_FRIEND_ACTIONID_MSG_FRIEND_DELETE_ENUM.number = 3
pb.MSG_FRIEND_ACTIONID_MSG_FRIEND_SENDCHAT_ENUM.name = "MSG_FRIEND_SENDCHAT"
pb.MSG_FRIEND_ACTIONID_MSG_FRIEND_SENDCHAT_ENUM.index = 4
pb.MSG_FRIEND_ACTIONID_MSG_FRIEND_SENDCHAT_ENUM.number = 4
pb.MSG_FRIEND_ACTIONID_MSG_FRIEND_SENDCHATRETURN_ENUM.name = "MSG_FRIEND_SENDCHATRETURN"
pb.MSG_FRIEND_ACTIONID_MSG_FRIEND_SENDCHATRETURN_ENUM.index = 5
pb.MSG_FRIEND_ACTIONID_MSG_FRIEND_SENDCHATRETURN_ENUM.number = 5
pb.MSG_FRIEND_ACTIONID_MSG_FRIEND_RECOMMEND_ENUM.name = "MSG_FRIEND_RECOMMEND"
pb.MSG_FRIEND_ACTIONID_MSG_FRIEND_RECOMMEND_ENUM.index = 6
pb.MSG_FRIEND_ACTIONID_MSG_FRIEND_RECOMMEND_ENUM.number = 6
pb.MSG_FRIEND_ACTIONID_MSG_OTHERPLAYER_INFO_ENUM.name = "MSG_OTHERPLAYER_INFO"
pb.MSG_FRIEND_ACTIONID_MSG_OTHERPLAYER_INFO_ENUM.index = 7
pb.MSG_FRIEND_ACTIONID_MSG_OTHERPLAYER_INFO_ENUM.number = 7
pb.MSG_FRIEND_ACTIONID_MSG_CHATOFFLINE_LIST_ENUM.name = "MSG_CHATOFFLINE_LIST"
pb.MSG_FRIEND_ACTIONID_MSG_CHATOFFLINE_LIST_ENUM.index = 8
pb.MSG_FRIEND_ACTIONID_MSG_CHATOFFLINE_LIST_ENUM.number = 8
pb.MSG_FRIEND_ACTIONID_MSG_DELETE_CHATOFFLINE_LIST_ENUM.name = "MSG_DELETE_CHATOFFLINE_LIST"
pb.MSG_FRIEND_ACTIONID_MSG_DELETE_CHATOFFLINE_LIST_ENUM.index = 9
pb.MSG_FRIEND_ACTIONID_MSG_DELETE_CHATOFFLINE_LIST_ENUM.number = 9
pb.MSG_FRIEND_ACTIONID_MSG_FRIEND_ADD_REPONSE_ENUM.name = "MSG_FRIEND_ADD_REPONSE"
pb.MSG_FRIEND_ACTIONID_MSG_FRIEND_ADD_REPONSE_ENUM.index = 10
pb.MSG_FRIEND_ACTIONID_MSG_FRIEND_ADD_REPONSE_ENUM.number = 10
pb.MSG_FRIEND_ACTIONID_MSG_FRIEND_ADD_RESULT_ENUM.name = "MSG_FRIEND_ADD_RESULT"
pb.MSG_FRIEND_ACTIONID_MSG_FRIEND_ADD_RESULT_ENUM.index = 11
pb.MSG_FRIEND_ACTIONID_MSG_FRIEND_ADD_RESULT_ENUM.number = 11
pb.MSG_FRIEND_ACTIONID_MSG_FRIEND_ADD_OPT_ENUM.name = "MSG_FRIEND_ADD_OPT"
pb.MSG_FRIEND_ACTIONID_MSG_FRIEND_ADD_OPT_ENUM.index = 12
pb.MSG_FRIEND_ACTIONID_MSG_FRIEND_ADD_OPT_ENUM.number = 12
pb.MSG_FRIEND_ACTIONID_MSG_FRIEND_VALUE_SYNC_ENUM.name = "MSG_FRIEND_VALUE_SYNC"
pb.MSG_FRIEND_ACTIONID_MSG_FRIEND_VALUE_SYNC_ENUM.index = 13
pb.MSG_FRIEND_ACTIONID_MSG_FRIEND_VALUE_SYNC_ENUM.number = 13
pb.MSG_FRIEND_ACTIONID_MSG_FRIEND_QUERY_BY_NAME_ENUM.name = "MSG_FRIEND_QUERY_BY_NAME"
pb.MSG_FRIEND_ACTIONID_MSG_FRIEND_QUERY_BY_NAME_ENUM.index = 14
pb.MSG_FRIEND_ACTIONID_MSG_FRIEND_QUERY_BY_NAME_ENUM.number = 14
pb.MSG_FRIEND_ACTIONID_MSG_BLACK_LIST_ADD_ENUM.name = "MSG_BLACK_LIST_ADD"
pb.MSG_FRIEND_ACTIONID_MSG_BLACK_LIST_ADD_ENUM.index = 15
pb.MSG_FRIEND_ACTIONID_MSG_BLACK_LIST_ADD_ENUM.number = 15
pb.MSG_FRIEND_ACTIONID_MSG_FRIEND_QUERY_BEFORECHAT_ENUM.name = "MSG_FRIEND_QUERY_BEFORECHAT"
pb.MSG_FRIEND_ACTIONID_MSG_FRIEND_QUERY_BEFORECHAT_ENUM.index = 16
pb.MSG_FRIEND_ACTIONID_MSG_FRIEND_QUERY_BEFORECHAT_ENUM.number = 16
pb.MSG_FRIEND_ACTIONID_MSG_QUERY_INFORMATION_ENUM.name = "MSG_QUERY_INFORMATION"
pb.MSG_FRIEND_ACTIONID_MSG_QUERY_INFORMATION_ENUM.index = 17
pb.MSG_FRIEND_ACTIONID_MSG_QUERY_INFORMATION_ENUM.number = 17
pb.MSG_FRIEND_ACTIONID_MSG_FRIENDBLESS_UPGRADENOTICE_ENUM.name = "MSG_FRIENDBLESS_UPGRADENOTICE"
pb.MSG_FRIEND_ACTIONID_MSG_FRIENDBLESS_UPGRADENOTICE_ENUM.index = 18
pb.MSG_FRIEND_ACTIONID_MSG_FRIENDBLESS_UPGRADENOTICE_ENUM.number = 18
pb.MSG_FRIEND_ACTIONID_MSG_FRIENDBLESS_SENDBLESS_ENUM.name = "MSG_FRIENDBLESS_SENDBLESS"
pb.MSG_FRIEND_ACTIONID_MSG_FRIENDBLESS_SENDBLESS_ENUM.index = 19
pb.MSG_FRIEND_ACTIONID_MSG_FRIENDBLESS_SENDBLESS_ENUM.number = 19
pb.MSG_FRIEND_ACTIONID_MSG_FRIEND_VALUE_PRIZE_ENUM.name = "MSG_FRIEND_VALUE_PRIZE"
pb.MSG_FRIEND_ACTIONID_MSG_FRIEND_VALUE_PRIZE_ENUM.index = 20
pb.MSG_FRIEND_ACTIONID_MSG_FRIEND_VALUE_PRIZE_ENUM.number = 20
pb.MSG_FRIEND_ACTIONID.name = "MSG_FRIEND_ACTIONID"
pb.MSG_FRIEND_ACTIONID.full_name = ".MSG_FRIEND_ACTIONID"
pb.MSG_FRIEND_ACTIONID.values = {pb.MSG_FRIEND_ACTIONID_MSG_FRIEND_NONE_ENUM,pb.MSG_FRIEND_ACTIONID_MSG_FRIEND_GETLIST_ENUM,pb.MSG_FRIEND_ACTIONID_MSG_FRIEND_ADD_ENUM,pb.MSG_FRIEND_ACTIONID_MSG_FRIEND_DELETE_ENUM,pb.MSG_FRIEND_ACTIONID_MSG_FRIEND_SENDCHAT_ENUM,pb.MSG_FRIEND_ACTIONID_MSG_FRIEND_SENDCHATRETURN_ENUM,pb.MSG_FRIEND_ACTIONID_MSG_FRIEND_RECOMMEND_ENUM,pb.MSG_FRIEND_ACTIONID_MSG_OTHERPLAYER_INFO_ENUM,pb.MSG_FRIEND_ACTIONID_MSG_CHATOFFLINE_LIST_ENUM,pb.MSG_FRIEND_ACTIONID_MSG_DELETE_CHATOFFLINE_LIST_ENUM,pb.MSG_FRIEND_ACTIONID_MSG_FRIEND_ADD_REPONSE_ENUM,pb.MSG_FRIEND_ACTIONID_MSG_FRIEND_ADD_RESULT_ENUM,pb.MSG_FRIEND_ACTIONID_MSG_FRIEND_ADD_OPT_ENUM,pb.MSG_FRIEND_ACTIONID_MSG_FRIEND_VALUE_SYNC_ENUM,pb.MSG_FRIEND_ACTIONID_MSG_FRIEND_QUERY_BY_NAME_ENUM,pb.MSG_FRIEND_ACTIONID_MSG_BLACK_LIST_ADD_ENUM,pb.MSG_FRIEND_ACTIONID_MSG_FRIEND_QUERY_BEFORECHAT_ENUM,pb.MSG_FRIEND_ACTIONID_MSG_QUERY_INFORMATION_ENUM,pb.MSG_FRIEND_ACTIONID_MSG_FRIENDBLESS_UPGRADENOTICE_ENUM,pb.MSG_FRIEND_ACTIONID_MSG_FRIENDBLESS_SENDBLESS_ENUM,pb.MSG_FRIEND_ACTIONID_MSG_FRIEND_VALUE_PRIZE_ENUM}
pb.FRIENDDATA_FRIENDID_FIELD.name = "FriendID"
pb.FRIENDDATA_FRIENDID_FIELD.full_name = ".FriendData.FriendID"
pb.FRIENDDATA_FRIENDID_FIELD.number = 1
pb.FRIENDDATA_FRIENDID_FIELD.index = 0
pb.FRIENDDATA_FRIENDID_FIELD.label = 1
pb.FRIENDDATA_FRIENDID_FIELD.has_default_value = false
pb.FRIENDDATA_FRIENDID_FIELD.default_value = 0
pb.FRIENDDATA_FRIENDID_FIELD.type = 13
pb.FRIENDDATA_FRIENDID_FIELD.cpp_type = 3

pb.FRIENDDATA_NAME_FIELD.name = "Name"
pb.FRIENDDATA_NAME_FIELD.full_name = ".FriendData.Name"
pb.FRIENDDATA_NAME_FIELD.number = 2
pb.FRIENDDATA_NAME_FIELD.index = 1
pb.FRIENDDATA_NAME_FIELD.label = 1
pb.FRIENDDATA_NAME_FIELD.has_default_value = false
pb.FRIENDDATA_NAME_FIELD.default_value = ""
pb.FRIENDDATA_NAME_FIELD.type = 9
pb.FRIENDDATA_NAME_FIELD.cpp_type = 9

pb.FRIENDDATA_RELATIONTYPE_FIELD.name = "RelationType"
pb.FRIENDDATA_RELATIONTYPE_FIELD.full_name = ".FriendData.RelationType"
pb.FRIENDDATA_RELATIONTYPE_FIELD.number = 3
pb.FRIENDDATA_RELATIONTYPE_FIELD.index = 2
pb.FRIENDDATA_RELATIONTYPE_FIELD.label = 1
pb.FRIENDDATA_RELATIONTYPE_FIELD.has_default_value = false
pb.FRIENDDATA_RELATIONTYPE_FIELD.default_value = 0
pb.FRIENDDATA_RELATIONTYPE_FIELD.type = 13
pb.FRIENDDATA_RELATIONTYPE_FIELD.cpp_type = 3

pb.FRIENDDATA_LEVEL_FIELD.name = "Level"
pb.FRIENDDATA_LEVEL_FIELD.full_name = ".FriendData.Level"
pb.FRIENDDATA_LEVEL_FIELD.number = 4
pb.FRIENDDATA_LEVEL_FIELD.index = 3
pb.FRIENDDATA_LEVEL_FIELD.label = 1
pb.FRIENDDATA_LEVEL_FIELD.has_default_value = false
pb.FRIENDDATA_LEVEL_FIELD.default_value = 0
pb.FRIENDDATA_LEVEL_FIELD.type = 13
pb.FRIENDDATA_LEVEL_FIELD.cpp_type = 3

pb.FRIENDDATA_VOCATION_FIELD.name = "Vocation"
pb.FRIENDDATA_VOCATION_FIELD.full_name = ".FriendData.Vocation"
pb.FRIENDDATA_VOCATION_FIELD.number = 5
pb.FRIENDDATA_VOCATION_FIELD.index = 4
pb.FRIENDDATA_VOCATION_FIELD.label = 1
pb.FRIENDDATA_VOCATION_FIELD.has_default_value = false
pb.FRIENDDATA_VOCATION_FIELD.default_value = 0
pb.FRIENDDATA_VOCATION_FIELD.type = 13
pb.FRIENDDATA_VOCATION_FIELD.cpp_type = 3

pb.FRIENDDATA_POWER_FIELD.name = "Power"
pb.FRIENDDATA_POWER_FIELD.full_name = ".FriendData.Power"
pb.FRIENDDATA_POWER_FIELD.number = 6
pb.FRIENDDATA_POWER_FIELD.index = 5
pb.FRIENDDATA_POWER_FIELD.label = 1
pb.FRIENDDATA_POWER_FIELD.has_default_value = false
pb.FRIENDDATA_POWER_FIELD.default_value = 0
pb.FRIENDDATA_POWER_FIELD.type = 13
pb.FRIENDDATA_POWER_FIELD.cpp_type = 3

pb.FRIENDDATA_ISONLINE_FIELD.name = "IsOnLine"
pb.FRIENDDATA_ISONLINE_FIELD.full_name = ".FriendData.IsOnLine"
pb.FRIENDDATA_ISONLINE_FIELD.number = 7
pb.FRIENDDATA_ISONLINE_FIELD.index = 6
pb.FRIENDDATA_ISONLINE_FIELD.label = 1
pb.FRIENDDATA_ISONLINE_FIELD.has_default_value = false
pb.FRIENDDATA_ISONLINE_FIELD.default_value = false
pb.FRIENDDATA_ISONLINE_FIELD.type = 8
pb.FRIENDDATA_ISONLINE_FIELD.cpp_type = 7

pb.FRIENDDATA_FRIENDVALUE_FIELD.name = "FriendValue"
pb.FRIENDDATA_FRIENDVALUE_FIELD.full_name = ".FriendData.FriendValue"
pb.FRIENDDATA_FRIENDVALUE_FIELD.number = 8
pb.FRIENDDATA_FRIENDVALUE_FIELD.index = 7
pb.FRIENDDATA_FRIENDVALUE_FIELD.label = 1
pb.FRIENDDATA_FRIENDVALUE_FIELD.has_default_value = false
pb.FRIENDDATA_FRIENDVALUE_FIELD.default_value = 0
pb.FRIENDDATA_FRIENDVALUE_FIELD.type = 5
pb.FRIENDDATA_FRIENDVALUE_FIELD.cpp_type = 1

pb.FRIENDDATA_VIP_FIELD.name = "Vip"
pb.FRIENDDATA_VIP_FIELD.full_name = ".FriendData.Vip"
pb.FRIENDDATA_VIP_FIELD.number = 9
pb.FRIENDDATA_VIP_FIELD.index = 8
pb.FRIENDDATA_VIP_FIELD.label = 1
pb.FRIENDDATA_VIP_FIELD.has_default_value = false
pb.FRIENDDATA_VIP_FIELD.default_value = 0
pb.FRIENDDATA_VIP_FIELD.type = 5
pb.FRIENDDATA_VIP_FIELD.cpp_type = 1

pb.FRIENDDATA_COUNTRY_FIELD.name = "Country"
pb.FRIENDDATA_COUNTRY_FIELD.full_name = ".FriendData.Country"
pb.FRIENDDATA_COUNTRY_FIELD.number = 10
pb.FRIENDDATA_COUNTRY_FIELD.index = 9
pb.FRIENDDATA_COUNTRY_FIELD.label = 1
pb.FRIENDDATA_COUNTRY_FIELD.has_default_value = false
pb.FRIENDDATA_COUNTRY_FIELD.default_value = 0
pb.FRIENDDATA_COUNTRY_FIELD.type = 5
pb.FRIENDDATA_COUNTRY_FIELD.cpp_type = 1

pb.FRIENDDATA_BLUEVIPLEVEL_FIELD.name = "BlueVIPLevel"
pb.FRIENDDATA_BLUEVIPLEVEL_FIELD.full_name = ".FriendData.BlueVIPLevel"
pb.FRIENDDATA_BLUEVIPLEVEL_FIELD.number = 11
pb.FRIENDDATA_BLUEVIPLEVEL_FIELD.index = 10
pb.FRIENDDATA_BLUEVIPLEVEL_FIELD.label = 1
pb.FRIENDDATA_BLUEVIPLEVEL_FIELD.has_default_value = false
pb.FRIENDDATA_BLUEVIPLEVEL_FIELD.default_value = 0
pb.FRIENDDATA_BLUEVIPLEVEL_FIELD.type = 5
pb.FRIENDDATA_BLUEVIPLEVEL_FIELD.cpp_type = 1

pb.FRIENDDATA_BLUEVIPTYPE_FIELD.name = "BlueVIPType"
pb.FRIENDDATA_BLUEVIPTYPE_FIELD.full_name = ".FriendData.BlueVIPType"
pb.FRIENDDATA_BLUEVIPTYPE_FIELD.number = 12
pb.FRIENDDATA_BLUEVIPTYPE_FIELD.index = 11
pb.FRIENDDATA_BLUEVIPTYPE_FIELD.label = 1
pb.FRIENDDATA_BLUEVIPTYPE_FIELD.has_default_value = false
pb.FRIENDDATA_BLUEVIPTYPE_FIELD.default_value = 0
pb.FRIENDDATA_BLUEVIPTYPE_FIELD.type = 5
pb.FRIENDDATA_BLUEVIPTYPE_FIELD.cpp_type = 1

pb.FRIENDDATA_TACITVALUE_FIELD.name = "TacitValue"
pb.FRIENDDATA_TACITVALUE_FIELD.full_name = ".FriendData.TacitValue"
pb.FRIENDDATA_TACITVALUE_FIELD.number = 13
pb.FRIENDDATA_TACITVALUE_FIELD.index = 12
pb.FRIENDDATA_TACITVALUE_FIELD.label = 1
pb.FRIENDDATA_TACITVALUE_FIELD.has_default_value = false
pb.FRIENDDATA_TACITVALUE_FIELD.default_value = 0
pb.FRIENDDATA_TACITVALUE_FIELD.type = 5
pb.FRIENDDATA_TACITVALUE_FIELD.cpp_type = 1

pb.FRIENDDATA.name = "FriendData"
pb.FRIENDDATA.full_name = ".FriendData"
pb.FRIENDDATA.nested_types = {}
pb.FRIENDDATA.enum_types = {}
pb.FRIENDDATA.fields = {pb.FRIENDDATA_FRIENDID_FIELD, pb.FRIENDDATA_NAME_FIELD, pb.FRIENDDATA_RELATIONTYPE_FIELD, pb.FRIENDDATA_LEVEL_FIELD, pb.FRIENDDATA_VOCATION_FIELD, pb.FRIENDDATA_POWER_FIELD, pb.FRIENDDATA_ISONLINE_FIELD, pb.FRIENDDATA_FRIENDVALUE_FIELD, pb.FRIENDDATA_VIP_FIELD, pb.FRIENDDATA_COUNTRY_FIELD, pb.FRIENDDATA_BLUEVIPLEVEL_FIELD, pb.FRIENDDATA_BLUEVIPTYPE_FIELD, pb.FRIENDDATA_TACITVALUE_FIELD}
pb.FRIENDDATA.is_extendable = false
pb.FRIENDDATA.extensions = {}
pb.OTHERPLAYERDATA_OTHERPLAYER_FIELD.name = "OtherPlayer"
pb.OTHERPLAYERDATA_OTHERPLAYER_FIELD.full_name = ".OtherPlayerData.OtherPlayer"
pb.OTHERPLAYERDATA_OTHERPLAYER_FIELD.number = 1
pb.OTHERPLAYERDATA_OTHERPLAYER_FIELD.index = 0
pb.OTHERPLAYERDATA_OTHERPLAYER_FIELD.label = 1
pb.OTHERPLAYERDATA_OTHERPLAYER_FIELD.has_default_value = false
pb.OTHERPLAYERDATA_OTHERPLAYER_FIELD.default_value = 0
pb.OTHERPLAYERDATA_OTHERPLAYER_FIELD.type = 13
pb.OTHERPLAYERDATA_OTHERPLAYER_FIELD.cpp_type = 3

pb.OTHERPLAYERDATA.name = "OtherPlayerData"
pb.OTHERPLAYERDATA.full_name = ".OtherPlayerData"
pb.OTHERPLAYERDATA.nested_types = {}
pb.OTHERPLAYERDATA.enum_types = {}
pb.OTHERPLAYERDATA.fields = {pb.OTHERPLAYERDATA_OTHERPLAYER_FIELD}
pb.OTHERPLAYERDATA.is_extendable = false
pb.OTHERPLAYERDATA.extensions = {}
pb.CS_FRIEND_GETFRIENDLIST.name = "CS_Friend_GetFriendList"
pb.CS_FRIEND_GETFRIENDLIST.full_name = ".CS_Friend_GetFriendList"
pb.CS_FRIEND_GETFRIENDLIST.nested_types = {}
pb.CS_FRIEND_GETFRIENDLIST.enum_types = {}
pb.CS_FRIEND_GETFRIENDLIST.fields = {}
pb.CS_FRIEND_GETFRIENDLIST.is_extendable = false
pb.CS_FRIEND_GETFRIENDLIST.extensions = {}
pb.SC_FRIEND_GETFRIENDLIST_FRIENDNUM_FIELD.name = "FriendNum"
pb.SC_FRIEND_GETFRIENDLIST_FRIENDNUM_FIELD.full_name = ".SC_Friend_GetFriendList.FriendNum"
pb.SC_FRIEND_GETFRIENDLIST_FRIENDNUM_FIELD.number = 1
pb.SC_FRIEND_GETFRIENDLIST_FRIENDNUM_FIELD.index = 0
pb.SC_FRIEND_GETFRIENDLIST_FRIENDNUM_FIELD.label = 1
pb.SC_FRIEND_GETFRIENDLIST_FRIENDNUM_FIELD.has_default_value = false
pb.SC_FRIEND_GETFRIENDLIST_FRIENDNUM_FIELD.default_value = 0
pb.SC_FRIEND_GETFRIENDLIST_FRIENDNUM_FIELD.type = 13
pb.SC_FRIEND_GETFRIENDLIST_FRIENDNUM_FIELD.cpp_type = 3

pb.SC_FRIEND_GETFRIENDLIST_FRIENDLIST_FIELD.name = "FriendList"
pb.SC_FRIEND_GETFRIENDLIST_FRIENDLIST_FIELD.full_name = ".SC_Friend_GetFriendList.FriendList"
pb.SC_FRIEND_GETFRIENDLIST_FRIENDLIST_FIELD.number = 2
pb.SC_FRIEND_GETFRIENDLIST_FRIENDLIST_FIELD.index = 1
pb.SC_FRIEND_GETFRIENDLIST_FRIENDLIST_FIELD.label = 3
pb.SC_FRIEND_GETFRIENDLIST_FRIENDLIST_FIELD.has_default_value = false
pb.SC_FRIEND_GETFRIENDLIST_FRIENDLIST_FIELD.default_value = {}
pb.SC_FRIEND_GETFRIENDLIST_FRIENDLIST_FIELD.message_type = pb.FRIENDDATA
pb.SC_FRIEND_GETFRIENDLIST_FRIENDLIST_FIELD.type = 11
pb.SC_FRIEND_GETFRIENDLIST_FRIENDLIST_FIELD.cpp_type = 10

pb.SC_FRIEND_GETFRIENDLIST.name = "SC_Friend_GetFriendList"
pb.SC_FRIEND_GETFRIENDLIST.full_name = ".SC_Friend_GetFriendList"
pb.SC_FRIEND_GETFRIENDLIST.nested_types = {}
pb.SC_FRIEND_GETFRIENDLIST.enum_types = {}
pb.SC_FRIEND_GETFRIENDLIST.fields = {pb.SC_FRIEND_GETFRIENDLIST_FRIENDNUM_FIELD, pb.SC_FRIEND_GETFRIENDLIST_FRIENDLIST_FIELD}
pb.SC_FRIEND_GETFRIENDLIST.is_extendable = false
pb.SC_FRIEND_GETFRIENDLIST.extensions = {}
pb.CS_FRIEND_ADD_ENTITYUID_FIELD.name = "EntityUID"
pb.CS_FRIEND_ADD_ENTITYUID_FIELD.full_name = ".CS_Friend_Add.EntityUID"
pb.CS_FRIEND_ADD_ENTITYUID_FIELD.number = 1
pb.CS_FRIEND_ADD_ENTITYUID_FIELD.index = 0
pb.CS_FRIEND_ADD_ENTITYUID_FIELD.label = 1
pb.CS_FRIEND_ADD_ENTITYUID_FIELD.has_default_value = false
pb.CS_FRIEND_ADD_ENTITYUID_FIELD.default_value = 0
pb.CS_FRIEND_ADD_ENTITYUID_FIELD.type = 4
pb.CS_FRIEND_ADD_ENTITYUID_FIELD.cpp_type = 4

pb.CS_FRIEND_ADD_RELATIONTYPE_FIELD.name = "RelationType"
pb.CS_FRIEND_ADD_RELATIONTYPE_FIELD.full_name = ".CS_Friend_Add.RelationType"
pb.CS_FRIEND_ADD_RELATIONTYPE_FIELD.number = 2
pb.CS_FRIEND_ADD_RELATIONTYPE_FIELD.index = 1
pb.CS_FRIEND_ADD_RELATIONTYPE_FIELD.label = 1
pb.CS_FRIEND_ADD_RELATIONTYPE_FIELD.has_default_value = false
pb.CS_FRIEND_ADD_RELATIONTYPE_FIELD.default_value = 0
pb.CS_FRIEND_ADD_RELATIONTYPE_FIELD.type = 13
pb.CS_FRIEND_ADD_RELATIONTYPE_FIELD.cpp_type = 3

pb.CS_FRIEND_ADD.name = "CS_Friend_Add"
pb.CS_FRIEND_ADD.full_name = ".CS_Friend_Add"
pb.CS_FRIEND_ADD.nested_types = {}
pb.CS_FRIEND_ADD.enum_types = {}
pb.CS_FRIEND_ADD.fields = {pb.CS_FRIEND_ADD_ENTITYUID_FIELD, pb.CS_FRIEND_ADD_RELATIONTYPE_FIELD}
pb.CS_FRIEND_ADD.is_extendable = false
pb.CS_FRIEND_ADD.extensions = {}
pb.SC_FRIEND_ADD_RESPONSE_FRIENDID_FIELD.name = "FriendID"
pb.SC_FRIEND_ADD_RESPONSE_FRIENDID_FIELD.full_name = ".SC_Friend_Add_Response.FriendID"
pb.SC_FRIEND_ADD_RESPONSE_FRIENDID_FIELD.number = 1
pb.SC_FRIEND_ADD_RESPONSE_FRIENDID_FIELD.index = 0
pb.SC_FRIEND_ADD_RESPONSE_FRIENDID_FIELD.label = 1
pb.SC_FRIEND_ADD_RESPONSE_FRIENDID_FIELD.has_default_value = false
pb.SC_FRIEND_ADD_RESPONSE_FRIENDID_FIELD.default_value = 0
pb.SC_FRIEND_ADD_RESPONSE_FRIENDID_FIELD.type = 13
pb.SC_FRIEND_ADD_RESPONSE_FRIENDID_FIELD.cpp_type = 3

pb.SC_FRIEND_ADD_RESPONSE_ACTORNAME_FIELD.name = "ActorName"
pb.SC_FRIEND_ADD_RESPONSE_ACTORNAME_FIELD.full_name = ".SC_Friend_Add_Response.ActorName"
pb.SC_FRIEND_ADD_RESPONSE_ACTORNAME_FIELD.number = 2
pb.SC_FRIEND_ADD_RESPONSE_ACTORNAME_FIELD.index = 1
pb.SC_FRIEND_ADD_RESPONSE_ACTORNAME_FIELD.label = 1
pb.SC_FRIEND_ADD_RESPONSE_ACTORNAME_FIELD.has_default_value = false
pb.SC_FRIEND_ADD_RESPONSE_ACTORNAME_FIELD.default_value = ""
pb.SC_FRIEND_ADD_RESPONSE_ACTORNAME_FIELD.type = 9
pb.SC_FRIEND_ADD_RESPONSE_ACTORNAME_FIELD.cpp_type = 9

pb.SC_FRIEND_ADD_RESPONSE_LEVEL_FIELD.name = "Level"
pb.SC_FRIEND_ADD_RESPONSE_LEVEL_FIELD.full_name = ".SC_Friend_Add_Response.Level"
pb.SC_FRIEND_ADD_RESPONSE_LEVEL_FIELD.number = 3
pb.SC_FRIEND_ADD_RESPONSE_LEVEL_FIELD.index = 2
pb.SC_FRIEND_ADD_RESPONSE_LEVEL_FIELD.label = 1
pb.SC_FRIEND_ADD_RESPONSE_LEVEL_FIELD.has_default_value = false
pb.SC_FRIEND_ADD_RESPONSE_LEVEL_FIELD.default_value = 0
pb.SC_FRIEND_ADD_RESPONSE_LEVEL_FIELD.type = 13
pb.SC_FRIEND_ADD_RESPONSE_LEVEL_FIELD.cpp_type = 3

pb.SC_FRIEND_ADD_RESPONSE_VOCATION_FIELD.name = "Vocation"
pb.SC_FRIEND_ADD_RESPONSE_VOCATION_FIELD.full_name = ".SC_Friend_Add_Response.Vocation"
pb.SC_FRIEND_ADD_RESPONSE_VOCATION_FIELD.number = 4
pb.SC_FRIEND_ADD_RESPONSE_VOCATION_FIELD.index = 3
pb.SC_FRIEND_ADD_RESPONSE_VOCATION_FIELD.label = 1
pb.SC_FRIEND_ADD_RESPONSE_VOCATION_FIELD.has_default_value = false
pb.SC_FRIEND_ADD_RESPONSE_VOCATION_FIELD.default_value = 0
pb.SC_FRIEND_ADD_RESPONSE_VOCATION_FIELD.type = 13
pb.SC_FRIEND_ADD_RESPONSE_VOCATION_FIELD.cpp_type = 3

pb.SC_FRIEND_ADD_RESPONSE_COUNTRY_FIELD.name = "Country"
pb.SC_FRIEND_ADD_RESPONSE_COUNTRY_FIELD.full_name = ".SC_Friend_Add_Response.Country"
pb.SC_FRIEND_ADD_RESPONSE_COUNTRY_FIELD.number = 5
pb.SC_FRIEND_ADD_RESPONSE_COUNTRY_FIELD.index = 4
pb.SC_FRIEND_ADD_RESPONSE_COUNTRY_FIELD.label = 1
pb.SC_FRIEND_ADD_RESPONSE_COUNTRY_FIELD.has_default_value = false
pb.SC_FRIEND_ADD_RESPONSE_COUNTRY_FIELD.default_value = 0
pb.SC_FRIEND_ADD_RESPONSE_COUNTRY_FIELD.type = 5
pb.SC_FRIEND_ADD_RESPONSE_COUNTRY_FIELD.cpp_type = 1

pb.SC_FRIEND_ADD_RESPONSE_POWER_FIELD.name = "Power"
pb.SC_FRIEND_ADD_RESPONSE_POWER_FIELD.full_name = ".SC_Friend_Add_Response.Power"
pb.SC_FRIEND_ADD_RESPONSE_POWER_FIELD.number = 6
pb.SC_FRIEND_ADD_RESPONSE_POWER_FIELD.index = 5
pb.SC_FRIEND_ADD_RESPONSE_POWER_FIELD.label = 1
pb.SC_FRIEND_ADD_RESPONSE_POWER_FIELD.has_default_value = false
pb.SC_FRIEND_ADD_RESPONSE_POWER_FIELD.default_value = 0
pb.SC_FRIEND_ADD_RESPONSE_POWER_FIELD.type = 13
pb.SC_FRIEND_ADD_RESPONSE_POWER_FIELD.cpp_type = 3

pb.SC_FRIEND_ADD_RESPONSE_VIP_FIELD.name = "Vip"
pb.SC_FRIEND_ADD_RESPONSE_VIP_FIELD.full_name = ".SC_Friend_Add_Response.Vip"
pb.SC_FRIEND_ADD_RESPONSE_VIP_FIELD.number = 7
pb.SC_FRIEND_ADD_RESPONSE_VIP_FIELD.index = 6
pb.SC_FRIEND_ADD_RESPONSE_VIP_FIELD.label = 1
pb.SC_FRIEND_ADD_RESPONSE_VIP_FIELD.has_default_value = false
pb.SC_FRIEND_ADD_RESPONSE_VIP_FIELD.default_value = 0
pb.SC_FRIEND_ADD_RESPONSE_VIP_FIELD.type = 5
pb.SC_FRIEND_ADD_RESPONSE_VIP_FIELD.cpp_type = 1

pb.SC_FRIEND_ADD_RESPONSE_RELATIONTYPE_FIELD.name = "RelationType"
pb.SC_FRIEND_ADD_RESPONSE_RELATIONTYPE_FIELD.full_name = ".SC_Friend_Add_Response.RelationType"
pb.SC_FRIEND_ADD_RESPONSE_RELATIONTYPE_FIELD.number = 8
pb.SC_FRIEND_ADD_RESPONSE_RELATIONTYPE_FIELD.index = 7
pb.SC_FRIEND_ADD_RESPONSE_RELATIONTYPE_FIELD.label = 1
pb.SC_FRIEND_ADD_RESPONSE_RELATIONTYPE_FIELD.has_default_value = false
pb.SC_FRIEND_ADD_RESPONSE_RELATIONTYPE_FIELD.default_value = 0
pb.SC_FRIEND_ADD_RESPONSE_RELATIONTYPE_FIELD.type = 13
pb.SC_FRIEND_ADD_RESPONSE_RELATIONTYPE_FIELD.cpp_type = 3

pb.SC_FRIEND_ADD_RESPONSE_BLUEVIPLEVEL_FIELD.name = "BlueVIPLevel"
pb.SC_FRIEND_ADD_RESPONSE_BLUEVIPLEVEL_FIELD.full_name = ".SC_Friend_Add_Response.BlueVIPLevel"
pb.SC_FRIEND_ADD_RESPONSE_BLUEVIPLEVEL_FIELD.number = 9
pb.SC_FRIEND_ADD_RESPONSE_BLUEVIPLEVEL_FIELD.index = 8
pb.SC_FRIEND_ADD_RESPONSE_BLUEVIPLEVEL_FIELD.label = 1
pb.SC_FRIEND_ADD_RESPONSE_BLUEVIPLEVEL_FIELD.has_default_value = false
pb.SC_FRIEND_ADD_RESPONSE_BLUEVIPLEVEL_FIELD.default_value = 0
pb.SC_FRIEND_ADD_RESPONSE_BLUEVIPLEVEL_FIELD.type = 5
pb.SC_FRIEND_ADD_RESPONSE_BLUEVIPLEVEL_FIELD.cpp_type = 1

pb.SC_FRIEND_ADD_RESPONSE_BLUEVIPTYPE_FIELD.name = "BlueVIPType"
pb.SC_FRIEND_ADD_RESPONSE_BLUEVIPTYPE_FIELD.full_name = ".SC_Friend_Add_Response.BlueVIPType"
pb.SC_FRIEND_ADD_RESPONSE_BLUEVIPTYPE_FIELD.number = 10
pb.SC_FRIEND_ADD_RESPONSE_BLUEVIPTYPE_FIELD.index = 9
pb.SC_FRIEND_ADD_RESPONSE_BLUEVIPTYPE_FIELD.label = 1
pb.SC_FRIEND_ADD_RESPONSE_BLUEVIPTYPE_FIELD.has_default_value = false
pb.SC_FRIEND_ADD_RESPONSE_BLUEVIPTYPE_FIELD.default_value = 0
pb.SC_FRIEND_ADD_RESPONSE_BLUEVIPTYPE_FIELD.type = 5
pb.SC_FRIEND_ADD_RESPONSE_BLUEVIPTYPE_FIELD.cpp_type = 1

pb.SC_FRIEND_ADD_RESPONSE.name = "SC_Friend_Add_Response"
pb.SC_FRIEND_ADD_RESPONSE.full_name = ".SC_Friend_Add_Response"
pb.SC_FRIEND_ADD_RESPONSE.nested_types = {}
pb.SC_FRIEND_ADD_RESPONSE.enum_types = {}
pb.SC_FRIEND_ADD_RESPONSE.fields = {pb.SC_FRIEND_ADD_RESPONSE_FRIENDID_FIELD, pb.SC_FRIEND_ADD_RESPONSE_ACTORNAME_FIELD, pb.SC_FRIEND_ADD_RESPONSE_LEVEL_FIELD, pb.SC_FRIEND_ADD_RESPONSE_VOCATION_FIELD, pb.SC_FRIEND_ADD_RESPONSE_COUNTRY_FIELD, pb.SC_FRIEND_ADD_RESPONSE_POWER_FIELD, pb.SC_FRIEND_ADD_RESPONSE_VIP_FIELD, pb.SC_FRIEND_ADD_RESPONSE_RELATIONTYPE_FIELD, pb.SC_FRIEND_ADD_RESPONSE_BLUEVIPLEVEL_FIELD, pb.SC_FRIEND_ADD_RESPONSE_BLUEVIPTYPE_FIELD}
pb.SC_FRIEND_ADD_RESPONSE.is_extendable = false
pb.SC_FRIEND_ADD_RESPONSE.extensions = {}
pb.CS_FRIEND_ADD_OPT_ENTITYUID_FIELD.name = "EntityUID"
pb.CS_FRIEND_ADD_OPT_ENTITYUID_FIELD.full_name = ".CS_Friend_Add_Opt.EntityUID"
pb.CS_FRIEND_ADD_OPT_ENTITYUID_FIELD.number = 1
pb.CS_FRIEND_ADD_OPT_ENTITYUID_FIELD.index = 0
pb.CS_FRIEND_ADD_OPT_ENTITYUID_FIELD.label = 1
pb.CS_FRIEND_ADD_OPT_ENTITYUID_FIELD.has_default_value = false
pb.CS_FRIEND_ADD_OPT_ENTITYUID_FIELD.default_value = 0
pb.CS_FRIEND_ADD_OPT_ENTITYUID_FIELD.type = 4
pb.CS_FRIEND_ADD_OPT_ENTITYUID_FIELD.cpp_type = 4

pb.CS_FRIEND_ADD_OPT_RELATIONTYPE_FIELD.name = "RelationType"
pb.CS_FRIEND_ADD_OPT_RELATIONTYPE_FIELD.full_name = ".CS_Friend_Add_Opt.RelationType"
pb.CS_FRIEND_ADD_OPT_RELATIONTYPE_FIELD.number = 2
pb.CS_FRIEND_ADD_OPT_RELATIONTYPE_FIELD.index = 1
pb.CS_FRIEND_ADD_OPT_RELATIONTYPE_FIELD.label = 1
pb.CS_FRIEND_ADD_OPT_RELATIONTYPE_FIELD.has_default_value = false
pb.CS_FRIEND_ADD_OPT_RELATIONTYPE_FIELD.default_value = 0
pb.CS_FRIEND_ADD_OPT_RELATIONTYPE_FIELD.type = 4
pb.CS_FRIEND_ADD_OPT_RELATIONTYPE_FIELD.cpp_type = 4

pb.CS_FRIEND_ADD_OPT_OPT_FIELD.name = "Opt"
pb.CS_FRIEND_ADD_OPT_OPT_FIELD.full_name = ".CS_Friend_Add_Opt.Opt"
pb.CS_FRIEND_ADD_OPT_OPT_FIELD.number = 3
pb.CS_FRIEND_ADD_OPT_OPT_FIELD.index = 2
pb.CS_FRIEND_ADD_OPT_OPT_FIELD.label = 1
pb.CS_FRIEND_ADD_OPT_OPT_FIELD.has_default_value = false
pb.CS_FRIEND_ADD_OPT_OPT_FIELD.default_value = 0
pb.CS_FRIEND_ADD_OPT_OPT_FIELD.type = 13
pb.CS_FRIEND_ADD_OPT_OPT_FIELD.cpp_type = 3

pb.CS_FRIEND_ADD_OPT.name = "CS_Friend_Add_Opt"
pb.CS_FRIEND_ADD_OPT.full_name = ".CS_Friend_Add_Opt"
pb.CS_FRIEND_ADD_OPT.nested_types = {}
pb.CS_FRIEND_ADD_OPT.enum_types = {}
pb.CS_FRIEND_ADD_OPT.fields = {pb.CS_FRIEND_ADD_OPT_ENTITYUID_FIELD, pb.CS_FRIEND_ADD_OPT_RELATIONTYPE_FIELD, pb.CS_FRIEND_ADD_OPT_OPT_FIELD}
pb.CS_FRIEND_ADD_OPT.is_extendable = false
pb.CS_FRIEND_ADD_OPT.extensions = {}
pb.SC_FRIEND_ADD_RESULT_RESULT_FIELD.name = "Result"
pb.SC_FRIEND_ADD_RESULT_RESULT_FIELD.full_name = ".SC_Friend_Add_Result.Result"
pb.SC_FRIEND_ADD_RESULT_RESULT_FIELD.number = 1
pb.SC_FRIEND_ADD_RESULT_RESULT_FIELD.index = 0
pb.SC_FRIEND_ADD_RESULT_RESULT_FIELD.label = 1
pb.SC_FRIEND_ADD_RESULT_RESULT_FIELD.has_default_value = false
pb.SC_FRIEND_ADD_RESULT_RESULT_FIELD.default_value = 0
pb.SC_FRIEND_ADD_RESULT_RESULT_FIELD.type = 13
pb.SC_FRIEND_ADD_RESULT_RESULT_FIELD.cpp_type = 3

pb.SC_FRIEND_ADD_RESULT_RELATIONTYPE_FIELD.name = "RelationType"
pb.SC_FRIEND_ADD_RESULT_RELATIONTYPE_FIELD.full_name = ".SC_Friend_Add_Result.RelationType"
pb.SC_FRIEND_ADD_RESULT_RELATIONTYPE_FIELD.number = 2
pb.SC_FRIEND_ADD_RESULT_RELATIONTYPE_FIELD.index = 1
pb.SC_FRIEND_ADD_RESULT_RELATIONTYPE_FIELD.label = 1
pb.SC_FRIEND_ADD_RESULT_RELATIONTYPE_FIELD.has_default_value = false
pb.SC_FRIEND_ADD_RESULT_RELATIONTYPE_FIELD.default_value = 0
pb.SC_FRIEND_ADD_RESULT_RELATIONTYPE_FIELD.type = 13
pb.SC_FRIEND_ADD_RESULT_RELATIONTYPE_FIELD.cpp_type = 3

pb.SC_FRIEND_ADD_RESULT_FRIENDLIST_FIELD.name = "FriendList"
pb.SC_FRIEND_ADD_RESULT_FRIENDLIST_FIELD.full_name = ".SC_Friend_Add_Result.FriendList"
pb.SC_FRIEND_ADD_RESULT_FRIENDLIST_FIELD.number = 3
pb.SC_FRIEND_ADD_RESULT_FRIENDLIST_FIELD.index = 2
pb.SC_FRIEND_ADD_RESULT_FRIENDLIST_FIELD.label = 1
pb.SC_FRIEND_ADD_RESULT_FRIENDLIST_FIELD.has_default_value = false
pb.SC_FRIEND_ADD_RESULT_FRIENDLIST_FIELD.default_value = nil
pb.SC_FRIEND_ADD_RESULT_FRIENDLIST_FIELD.message_type = pb.FRIENDDATA
pb.SC_FRIEND_ADD_RESULT_FRIENDLIST_FIELD.type = 11
pb.SC_FRIEND_ADD_RESULT_FRIENDLIST_FIELD.cpp_type = 10

pb.SC_FRIEND_ADD_RESULT.name = "SC_Friend_Add_Result"
pb.SC_FRIEND_ADD_RESULT.full_name = ".SC_Friend_Add_Result"
pb.SC_FRIEND_ADD_RESULT.nested_types = {}
pb.SC_FRIEND_ADD_RESULT.enum_types = {}
pb.SC_FRIEND_ADD_RESULT.fields = {pb.SC_FRIEND_ADD_RESULT_RESULT_FIELD, pb.SC_FRIEND_ADD_RESULT_RELATIONTYPE_FIELD, pb.SC_FRIEND_ADD_RESULT_FRIENDLIST_FIELD}
pb.SC_FRIEND_ADD_RESULT.is_extendable = false
pb.SC_FRIEND_ADD_RESULT.extensions = {}
pb.CS_FRIEND_DELETE_FRIENDID_FIELD.name = "FriendID"
pb.CS_FRIEND_DELETE_FRIENDID_FIELD.full_name = ".CS_Friend_Delete.FriendID"
pb.CS_FRIEND_DELETE_FRIENDID_FIELD.number = 1
pb.CS_FRIEND_DELETE_FRIENDID_FIELD.index = 0
pb.CS_FRIEND_DELETE_FRIENDID_FIELD.label = 1
pb.CS_FRIEND_DELETE_FRIENDID_FIELD.has_default_value = false
pb.CS_FRIEND_DELETE_FRIENDID_FIELD.default_value = 0
pb.CS_FRIEND_DELETE_FRIENDID_FIELD.type = 13
pb.CS_FRIEND_DELETE_FRIENDID_FIELD.cpp_type = 3

pb.CS_FRIEND_DELETE_RELATIONTYPE_FIELD.name = "RelationType"
pb.CS_FRIEND_DELETE_RELATIONTYPE_FIELD.full_name = ".CS_Friend_Delete.RelationType"
pb.CS_FRIEND_DELETE_RELATIONTYPE_FIELD.number = 2
pb.CS_FRIEND_DELETE_RELATIONTYPE_FIELD.index = 1
pb.CS_FRIEND_DELETE_RELATIONTYPE_FIELD.label = 1
pb.CS_FRIEND_DELETE_RELATIONTYPE_FIELD.has_default_value = false
pb.CS_FRIEND_DELETE_RELATIONTYPE_FIELD.default_value = 0
pb.CS_FRIEND_DELETE_RELATIONTYPE_FIELD.type = 13
pb.CS_FRIEND_DELETE_RELATIONTYPE_FIELD.cpp_type = 3

pb.CS_FRIEND_DELETE.name = "CS_Friend_Delete"
pb.CS_FRIEND_DELETE.full_name = ".CS_Friend_Delete"
pb.CS_FRIEND_DELETE.nested_types = {}
pb.CS_FRIEND_DELETE.enum_types = {}
pb.CS_FRIEND_DELETE.fields = {pb.CS_FRIEND_DELETE_FRIENDID_FIELD, pb.CS_FRIEND_DELETE_RELATIONTYPE_FIELD}
pb.CS_FRIEND_DELETE.is_extendable = false
pb.CS_FRIEND_DELETE.extensions = {}
pb.SC_FRIEND_DELETE_FRIENDID_FIELD.name = "FriendID"
pb.SC_FRIEND_DELETE_FRIENDID_FIELD.full_name = ".SC_Friend_Delete.FriendID"
pb.SC_FRIEND_DELETE_FRIENDID_FIELD.number = 1
pb.SC_FRIEND_DELETE_FRIENDID_FIELD.index = 0
pb.SC_FRIEND_DELETE_FRIENDID_FIELD.label = 1
pb.SC_FRIEND_DELETE_FRIENDID_FIELD.has_default_value = false
pb.SC_FRIEND_DELETE_FRIENDID_FIELD.default_value = 0
pb.SC_FRIEND_DELETE_FRIENDID_FIELD.type = 13
pb.SC_FRIEND_DELETE_FRIENDID_FIELD.cpp_type = 3

pb.SC_FRIEND_DELETE_RELATIONTYPE_FIELD.name = "RelationType"
pb.SC_FRIEND_DELETE_RELATIONTYPE_FIELD.full_name = ".SC_Friend_Delete.RelationType"
pb.SC_FRIEND_DELETE_RELATIONTYPE_FIELD.number = 2
pb.SC_FRIEND_DELETE_RELATIONTYPE_FIELD.index = 1
pb.SC_FRIEND_DELETE_RELATIONTYPE_FIELD.label = 1
pb.SC_FRIEND_DELETE_RELATIONTYPE_FIELD.has_default_value = false
pb.SC_FRIEND_DELETE_RELATIONTYPE_FIELD.default_value = 0
pb.SC_FRIEND_DELETE_RELATIONTYPE_FIELD.type = 13
pb.SC_FRIEND_DELETE_RELATIONTYPE_FIELD.cpp_type = 3

pb.SC_FRIEND_DELETE_RESULT_FIELD.name = "Result"
pb.SC_FRIEND_DELETE_RESULT_FIELD.full_name = ".SC_Friend_Delete.Result"
pb.SC_FRIEND_DELETE_RESULT_FIELD.number = 3
pb.SC_FRIEND_DELETE_RESULT_FIELD.index = 2
pb.SC_FRIEND_DELETE_RESULT_FIELD.label = 1
pb.SC_FRIEND_DELETE_RESULT_FIELD.has_default_value = false
pb.SC_FRIEND_DELETE_RESULT_FIELD.default_value = 0
pb.SC_FRIEND_DELETE_RESULT_FIELD.type = 13
pb.SC_FRIEND_DELETE_RESULT_FIELD.cpp_type = 3

pb.SC_FRIEND_DELETE.name = "SC_Friend_Delete"
pb.SC_FRIEND_DELETE.full_name = ".SC_Friend_Delete"
pb.SC_FRIEND_DELETE.nested_types = {}
pb.SC_FRIEND_DELETE.enum_types = {}
pb.SC_FRIEND_DELETE.fields = {pb.SC_FRIEND_DELETE_FRIENDID_FIELD, pb.SC_FRIEND_DELETE_RELATIONTYPE_FIELD, pb.SC_FRIEND_DELETE_RESULT_FIELD}
pb.SC_FRIEND_DELETE.is_extendable = false
pb.SC_FRIEND_DELETE.extensions = {}
pb.CS_FRIEND_SENDCHAT_FRIENDID_FIELD.name = "FriendID"
pb.CS_FRIEND_SENDCHAT_FRIENDID_FIELD.full_name = ".CS_Friend_SendChat.FriendID"
pb.CS_FRIEND_SENDCHAT_FRIENDID_FIELD.number = 1
pb.CS_FRIEND_SENDCHAT_FRIENDID_FIELD.index = 0
pb.CS_FRIEND_SENDCHAT_FRIENDID_FIELD.label = 1
pb.CS_FRIEND_SENDCHAT_FRIENDID_FIELD.has_default_value = false
pb.CS_FRIEND_SENDCHAT_FRIENDID_FIELD.default_value = 0
pb.CS_FRIEND_SENDCHAT_FRIENDID_FIELD.type = 13
pb.CS_FRIEND_SENDCHAT_FRIENDID_FIELD.cpp_type = 3

pb.CS_FRIEND_SENDCHAT_CONTENT_FIELD.name = "Content"
pb.CS_FRIEND_SENDCHAT_CONTENT_FIELD.full_name = ".CS_Friend_SendChat.Content"
pb.CS_FRIEND_SENDCHAT_CONTENT_FIELD.number = 2
pb.CS_FRIEND_SENDCHAT_CONTENT_FIELD.index = 1
pb.CS_FRIEND_SENDCHAT_CONTENT_FIELD.label = 1
pb.CS_FRIEND_SENDCHAT_CONTENT_FIELD.has_default_value = false
pb.CS_FRIEND_SENDCHAT_CONTENT_FIELD.default_value = ""
pb.CS_FRIEND_SENDCHAT_CONTENT_FIELD.type = 9
pb.CS_FRIEND_SENDCHAT_CONTENT_FIELD.cpp_type = 9

pb.CS_FRIEND_SENDCHAT.name = "CS_Friend_SendChat"
pb.CS_FRIEND_SENDCHAT.full_name = ".CS_Friend_SendChat"
pb.CS_FRIEND_SENDCHAT.nested_types = {}
pb.CS_FRIEND_SENDCHAT.enum_types = {}
pb.CS_FRIEND_SENDCHAT.fields = {pb.CS_FRIEND_SENDCHAT_FRIENDID_FIELD, pb.CS_FRIEND_SENDCHAT_CONTENT_FIELD}
pb.CS_FRIEND_SENDCHAT.is_extendable = false
pb.CS_FRIEND_SENDCHAT.extensions = {}
pb.SC_FRIEND_SENDCHAT_FRIENDDATA_FIELD.name = "FriendData"
pb.SC_FRIEND_SENDCHAT_FRIENDDATA_FIELD.full_name = ".SC_Friend_SendChat.FriendData"
pb.SC_FRIEND_SENDCHAT_FRIENDDATA_FIELD.number = 1
pb.SC_FRIEND_SENDCHAT_FRIENDDATA_FIELD.index = 0
pb.SC_FRIEND_SENDCHAT_FRIENDDATA_FIELD.label = 1
pb.SC_FRIEND_SENDCHAT_FRIENDDATA_FIELD.has_default_value = false
pb.SC_FRIEND_SENDCHAT_FRIENDDATA_FIELD.default_value = nil
pb.SC_FRIEND_SENDCHAT_FRIENDDATA_FIELD.message_type = pb.FRIENDDATA
pb.SC_FRIEND_SENDCHAT_FRIENDDATA_FIELD.type = 11
pb.SC_FRIEND_SENDCHAT_FRIENDDATA_FIELD.cpp_type = 10

pb.SC_FRIEND_SENDCHAT_CONTENT_FIELD.name = "Content"
pb.SC_FRIEND_SENDCHAT_CONTENT_FIELD.full_name = ".SC_Friend_SendChat.Content"
pb.SC_FRIEND_SENDCHAT_CONTENT_FIELD.number = 2
pb.SC_FRIEND_SENDCHAT_CONTENT_FIELD.index = 1
pb.SC_FRIEND_SENDCHAT_CONTENT_FIELD.label = 1
pb.SC_FRIEND_SENDCHAT_CONTENT_FIELD.has_default_value = false
pb.SC_FRIEND_SENDCHAT_CONTENT_FIELD.default_value = ""
pb.SC_FRIEND_SENDCHAT_CONTENT_FIELD.type = 9
pb.SC_FRIEND_SENDCHAT_CONTENT_FIELD.cpp_type = 9

pb.SC_FRIEND_SENDCHAT.name = "SC_Friend_SendChat"
pb.SC_FRIEND_SENDCHAT.full_name = ".SC_Friend_SendChat"
pb.SC_FRIEND_SENDCHAT.nested_types = {}
pb.SC_FRIEND_SENDCHAT.enum_types = {}
pb.SC_FRIEND_SENDCHAT.fields = {pb.SC_FRIEND_SENDCHAT_FRIENDDATA_FIELD, pb.SC_FRIEND_SENDCHAT_CONTENT_FIELD}
pb.SC_FRIEND_SENDCHAT.is_extendable = false
pb.SC_FRIEND_SENDCHAT.extensions = {}
pb.SC_FRIEND_SENDCHATRETURN_RESULT_FIELD.name = "Result"
pb.SC_FRIEND_SENDCHATRETURN_RESULT_FIELD.full_name = ".SC_Friend_SendChatReturn.Result"
pb.SC_FRIEND_SENDCHATRETURN_RESULT_FIELD.number = 1
pb.SC_FRIEND_SENDCHATRETURN_RESULT_FIELD.index = 0
pb.SC_FRIEND_SENDCHATRETURN_RESULT_FIELD.label = 1
pb.SC_FRIEND_SENDCHATRETURN_RESULT_FIELD.has_default_value = false
pb.SC_FRIEND_SENDCHATRETURN_RESULT_FIELD.default_value = 0
pb.SC_FRIEND_SENDCHATRETURN_RESULT_FIELD.type = 13
pb.SC_FRIEND_SENDCHATRETURN_RESULT_FIELD.cpp_type = 3

pb.SC_FRIEND_SENDCHATRETURN_FRIENDID_FIELD.name = "FriendID"
pb.SC_FRIEND_SENDCHATRETURN_FRIENDID_FIELD.full_name = ".SC_Friend_SendChatReturn.FriendID"
pb.SC_FRIEND_SENDCHATRETURN_FRIENDID_FIELD.number = 2
pb.SC_FRIEND_SENDCHATRETURN_FRIENDID_FIELD.index = 1
pb.SC_FRIEND_SENDCHATRETURN_FRIENDID_FIELD.label = 1
pb.SC_FRIEND_SENDCHATRETURN_FRIENDID_FIELD.has_default_value = false
pb.SC_FRIEND_SENDCHATRETURN_FRIENDID_FIELD.default_value = 0
pb.SC_FRIEND_SENDCHATRETURN_FRIENDID_FIELD.type = 13
pb.SC_FRIEND_SENDCHATRETURN_FRIENDID_FIELD.cpp_type = 3

pb.SC_FRIEND_SENDCHATRETURN_CONTENT_FIELD.name = "Content"
pb.SC_FRIEND_SENDCHATRETURN_CONTENT_FIELD.full_name = ".SC_Friend_SendChatReturn.Content"
pb.SC_FRIEND_SENDCHATRETURN_CONTENT_FIELD.number = 3
pb.SC_FRIEND_SENDCHATRETURN_CONTENT_FIELD.index = 2
pb.SC_FRIEND_SENDCHATRETURN_CONTENT_FIELD.label = 1
pb.SC_FRIEND_SENDCHATRETURN_CONTENT_FIELD.has_default_value = false
pb.SC_FRIEND_SENDCHATRETURN_CONTENT_FIELD.default_value = ""
pb.SC_FRIEND_SENDCHATRETURN_CONTENT_FIELD.type = 9
pb.SC_FRIEND_SENDCHATRETURN_CONTENT_FIELD.cpp_type = 9

pb.SC_FRIEND_SENDCHATRETURN.name = "SC_Friend_SendChatReturn"
pb.SC_FRIEND_SENDCHATRETURN.full_name = ".SC_Friend_SendChatReturn"
pb.SC_FRIEND_SENDCHATRETURN.nested_types = {}
pb.SC_FRIEND_SENDCHATRETURN.enum_types = {}
pb.SC_FRIEND_SENDCHATRETURN.fields = {pb.SC_FRIEND_SENDCHATRETURN_RESULT_FIELD, pb.SC_FRIEND_SENDCHATRETURN_FRIENDID_FIELD, pb.SC_FRIEND_SENDCHATRETURN_CONTENT_FIELD}
pb.SC_FRIEND_SENDCHATRETURN.is_extendable = false
pb.SC_FRIEND_SENDCHATRETURN.extensions = {}
pb.CS_FRIEND_RECOMMEND.name = "CS_Friend_Recommend"
pb.CS_FRIEND_RECOMMEND.full_name = ".CS_Friend_Recommend"
pb.CS_FRIEND_RECOMMEND.nested_types = {}
pb.CS_FRIEND_RECOMMEND.enum_types = {}
pb.CS_FRIEND_RECOMMEND.fields = {}
pb.CS_FRIEND_RECOMMEND.is_extendable = false
pb.CS_FRIEND_RECOMMEND.extensions = {}
pb.SC_FRIEND_RECOMMEND_FRIENDNUM_FIELD.name = "FriendNum"
pb.SC_FRIEND_RECOMMEND_FRIENDNUM_FIELD.full_name = ".SC_Friend_Recommend.FriendNum"
pb.SC_FRIEND_RECOMMEND_FRIENDNUM_FIELD.number = 1
pb.SC_FRIEND_RECOMMEND_FRIENDNUM_FIELD.index = 0
pb.SC_FRIEND_RECOMMEND_FRIENDNUM_FIELD.label = 1
pb.SC_FRIEND_RECOMMEND_FRIENDNUM_FIELD.has_default_value = false
pb.SC_FRIEND_RECOMMEND_FRIENDNUM_FIELD.default_value = 0
pb.SC_FRIEND_RECOMMEND_FRIENDNUM_FIELD.type = 13
pb.SC_FRIEND_RECOMMEND_FRIENDNUM_FIELD.cpp_type = 3

pb.SC_FRIEND_RECOMMEND_FRIENDLIST_FIELD.name = "FriendList"
pb.SC_FRIEND_RECOMMEND_FRIENDLIST_FIELD.full_name = ".SC_Friend_Recommend.FriendList"
pb.SC_FRIEND_RECOMMEND_FRIENDLIST_FIELD.number = 2
pb.SC_FRIEND_RECOMMEND_FRIENDLIST_FIELD.index = 1
pb.SC_FRIEND_RECOMMEND_FRIENDLIST_FIELD.label = 3
pb.SC_FRIEND_RECOMMEND_FRIENDLIST_FIELD.has_default_value = false
pb.SC_FRIEND_RECOMMEND_FRIENDLIST_FIELD.default_value = {}
pb.SC_FRIEND_RECOMMEND_FRIENDLIST_FIELD.message_type = pb.FRIENDDATA
pb.SC_FRIEND_RECOMMEND_FRIENDLIST_FIELD.type = 11
pb.SC_FRIEND_RECOMMEND_FRIENDLIST_FIELD.cpp_type = 10

pb.SC_FRIEND_RECOMMEND.name = "SC_Friend_Recommend"
pb.SC_FRIEND_RECOMMEND.full_name = ".SC_Friend_Recommend"
pb.SC_FRIEND_RECOMMEND.nested_types = {}
pb.SC_FRIEND_RECOMMEND.enum_types = {}
pb.SC_FRIEND_RECOMMEND.fields = {pb.SC_FRIEND_RECOMMEND_FRIENDNUM_FIELD, pb.SC_FRIEND_RECOMMEND_FRIENDLIST_FIELD}
pb.SC_FRIEND_RECOMMEND.is_extendable = false
pb.SC_FRIEND_RECOMMEND.extensions = {}
pb.CS_OTHER_PLAYER_OTHERPLAYERID_FIELD.name = "OtherPlayerID"
pb.CS_OTHER_PLAYER_OTHERPLAYERID_FIELD.full_name = ".CS_Other_Player.OtherPlayerID"
pb.CS_OTHER_PLAYER_OTHERPLAYERID_FIELD.number = 1
pb.CS_OTHER_PLAYER_OTHERPLAYERID_FIELD.index = 0
pb.CS_OTHER_PLAYER_OTHERPLAYERID_FIELD.label = 1
pb.CS_OTHER_PLAYER_OTHERPLAYERID_FIELD.has_default_value = false
pb.CS_OTHER_PLAYER_OTHERPLAYERID_FIELD.default_value = 0
pb.CS_OTHER_PLAYER_OTHERPLAYERID_FIELD.type = 13
pb.CS_OTHER_PLAYER_OTHERPLAYERID_FIELD.cpp_type = 3

pb.CS_OTHER_PLAYER.name = "CS_Other_Player"
pb.CS_OTHER_PLAYER.full_name = ".CS_Other_Player"
pb.CS_OTHER_PLAYER.nested_types = {}
pb.CS_OTHER_PLAYER.enum_types = {}
pb.CS_OTHER_PLAYER.fields = {pb.CS_OTHER_PLAYER_OTHERPLAYERID_FIELD}
pb.CS_OTHER_PLAYER.is_extendable = false
pb.CS_OTHER_PLAYER.extensions = {}
pb.SC_CHATOFFLINE_LIST_CHATOFFLINEDATA_SENDACTORID_FIELD.name = "SendActorID"
pb.SC_CHATOFFLINE_LIST_CHATOFFLINEDATA_SENDACTORID_FIELD.full_name = ".SC_ChatOffline_List.ChatofflineData.SendActorID"
pb.SC_CHATOFFLINE_LIST_CHATOFFLINEDATA_SENDACTORID_FIELD.number = 1
pb.SC_CHATOFFLINE_LIST_CHATOFFLINEDATA_SENDACTORID_FIELD.index = 0
pb.SC_CHATOFFLINE_LIST_CHATOFFLINEDATA_SENDACTORID_FIELD.label = 1
pb.SC_CHATOFFLINE_LIST_CHATOFFLINEDATA_SENDACTORID_FIELD.has_default_value = false
pb.SC_CHATOFFLINE_LIST_CHATOFFLINEDATA_SENDACTORID_FIELD.default_value = 0
pb.SC_CHATOFFLINE_LIST_CHATOFFLINEDATA_SENDACTORID_FIELD.type = 13
pb.SC_CHATOFFLINE_LIST_CHATOFFLINEDATA_SENDACTORID_FIELD.cpp_type = 3

pb.SC_CHATOFFLINE_LIST_CHATOFFLINEDATA_SENDACTORNAME_FIELD.name = "SendActorName"
pb.SC_CHATOFFLINE_LIST_CHATOFFLINEDATA_SENDACTORNAME_FIELD.full_name = ".SC_ChatOffline_List.ChatofflineData.SendActorName"
pb.SC_CHATOFFLINE_LIST_CHATOFFLINEDATA_SENDACTORNAME_FIELD.number = 2
pb.SC_CHATOFFLINE_LIST_CHATOFFLINEDATA_SENDACTORNAME_FIELD.index = 1
pb.SC_CHATOFFLINE_LIST_CHATOFFLINEDATA_SENDACTORNAME_FIELD.label = 1
pb.SC_CHATOFFLINE_LIST_CHATOFFLINEDATA_SENDACTORNAME_FIELD.has_default_value = false
pb.SC_CHATOFFLINE_LIST_CHATOFFLINEDATA_SENDACTORNAME_FIELD.default_value = ""
pb.SC_CHATOFFLINE_LIST_CHATOFFLINEDATA_SENDACTORNAME_FIELD.type = 9
pb.SC_CHATOFFLINE_LIST_CHATOFFLINEDATA_SENDACTORNAME_FIELD.cpp_type = 9

pb.SC_CHATOFFLINE_LIST_CHATOFFLINEDATA_SENDTIME_FIELD.name = "SendTime"
pb.SC_CHATOFFLINE_LIST_CHATOFFLINEDATA_SENDTIME_FIELD.full_name = ".SC_ChatOffline_List.ChatofflineData.SendTime"
pb.SC_CHATOFFLINE_LIST_CHATOFFLINEDATA_SENDTIME_FIELD.number = 3
pb.SC_CHATOFFLINE_LIST_CHATOFFLINEDATA_SENDTIME_FIELD.index = 2
pb.SC_CHATOFFLINE_LIST_CHATOFFLINEDATA_SENDTIME_FIELD.label = 1
pb.SC_CHATOFFLINE_LIST_CHATOFFLINEDATA_SENDTIME_FIELD.has_default_value = false
pb.SC_CHATOFFLINE_LIST_CHATOFFLINEDATA_SENDTIME_FIELD.default_value = 0
pb.SC_CHATOFFLINE_LIST_CHATOFFLINEDATA_SENDTIME_FIELD.type = 13
pb.SC_CHATOFFLINE_LIST_CHATOFFLINEDATA_SENDTIME_FIELD.cpp_type = 3

pb.SC_CHATOFFLINE_LIST_CHATOFFLINEDATA_TALKCONTENT_FIELD.name = "TalkContent"
pb.SC_CHATOFFLINE_LIST_CHATOFFLINEDATA_TALKCONTENT_FIELD.full_name = ".SC_ChatOffline_List.ChatofflineData.TalkContent"
pb.SC_CHATOFFLINE_LIST_CHATOFFLINEDATA_TALKCONTENT_FIELD.number = 4
pb.SC_CHATOFFLINE_LIST_CHATOFFLINEDATA_TALKCONTENT_FIELD.index = 3
pb.SC_CHATOFFLINE_LIST_CHATOFFLINEDATA_TALKCONTENT_FIELD.label = 1
pb.SC_CHATOFFLINE_LIST_CHATOFFLINEDATA_TALKCONTENT_FIELD.has_default_value = false
pb.SC_CHATOFFLINE_LIST_CHATOFFLINEDATA_TALKCONTENT_FIELD.default_value = ""
pb.SC_CHATOFFLINE_LIST_CHATOFFLINEDATA_TALKCONTENT_FIELD.type = 9
pb.SC_CHATOFFLINE_LIST_CHATOFFLINEDATA_TALKCONTENT_FIELD.cpp_type = 9

pb.SC_CHATOFFLINE_LIST_CHATOFFLINEDATA_LEVEL_FIELD.name = "Level"
pb.SC_CHATOFFLINE_LIST_CHATOFFLINEDATA_LEVEL_FIELD.full_name = ".SC_ChatOffline_List.ChatofflineData.Level"
pb.SC_CHATOFFLINE_LIST_CHATOFFLINEDATA_LEVEL_FIELD.number = 5
pb.SC_CHATOFFLINE_LIST_CHATOFFLINEDATA_LEVEL_FIELD.index = 4
pb.SC_CHATOFFLINE_LIST_CHATOFFLINEDATA_LEVEL_FIELD.label = 1
pb.SC_CHATOFFLINE_LIST_CHATOFFLINEDATA_LEVEL_FIELD.has_default_value = false
pb.SC_CHATOFFLINE_LIST_CHATOFFLINEDATA_LEVEL_FIELD.default_value = 0
pb.SC_CHATOFFLINE_LIST_CHATOFFLINEDATA_LEVEL_FIELD.type = 13
pb.SC_CHATOFFLINE_LIST_CHATOFFLINEDATA_LEVEL_FIELD.cpp_type = 3

pb.SC_CHATOFFLINE_LIST_CHATOFFLINEDATA_VOCATION_FIELD.name = "Vocation"
pb.SC_CHATOFFLINE_LIST_CHATOFFLINEDATA_VOCATION_FIELD.full_name = ".SC_ChatOffline_List.ChatofflineData.Vocation"
pb.SC_CHATOFFLINE_LIST_CHATOFFLINEDATA_VOCATION_FIELD.number = 6
pb.SC_CHATOFFLINE_LIST_CHATOFFLINEDATA_VOCATION_FIELD.index = 5
pb.SC_CHATOFFLINE_LIST_CHATOFFLINEDATA_VOCATION_FIELD.label = 1
pb.SC_CHATOFFLINE_LIST_CHATOFFLINEDATA_VOCATION_FIELD.has_default_value = false
pb.SC_CHATOFFLINE_LIST_CHATOFFLINEDATA_VOCATION_FIELD.default_value = 0
pb.SC_CHATOFFLINE_LIST_CHATOFFLINEDATA_VOCATION_FIELD.type = 13
pb.SC_CHATOFFLINE_LIST_CHATOFFLINEDATA_VOCATION_FIELD.cpp_type = 3

pb.SC_CHATOFFLINE_LIST_CHATOFFLINEDATA_POWER_FIELD.name = "Power"
pb.SC_CHATOFFLINE_LIST_CHATOFFLINEDATA_POWER_FIELD.full_name = ".SC_ChatOffline_List.ChatofflineData.Power"
pb.SC_CHATOFFLINE_LIST_CHATOFFLINEDATA_POWER_FIELD.number = 7
pb.SC_CHATOFFLINE_LIST_CHATOFFLINEDATA_POWER_FIELD.index = 6
pb.SC_CHATOFFLINE_LIST_CHATOFFLINEDATA_POWER_FIELD.label = 1
pb.SC_CHATOFFLINE_LIST_CHATOFFLINEDATA_POWER_FIELD.has_default_value = false
pb.SC_CHATOFFLINE_LIST_CHATOFFLINEDATA_POWER_FIELD.default_value = 0
pb.SC_CHATOFFLINE_LIST_CHATOFFLINEDATA_POWER_FIELD.type = 13
pb.SC_CHATOFFLINE_LIST_CHATOFFLINEDATA_POWER_FIELD.cpp_type = 3

pb.SC_CHATOFFLINE_LIST_CHATOFFLINEDATA_ISONLINE_FIELD.name = "IsOnLine"
pb.SC_CHATOFFLINE_LIST_CHATOFFLINEDATA_ISONLINE_FIELD.full_name = ".SC_ChatOffline_List.ChatofflineData.IsOnLine"
pb.SC_CHATOFFLINE_LIST_CHATOFFLINEDATA_ISONLINE_FIELD.number = 8
pb.SC_CHATOFFLINE_LIST_CHATOFFLINEDATA_ISONLINE_FIELD.index = 7
pb.SC_CHATOFFLINE_LIST_CHATOFFLINEDATA_ISONLINE_FIELD.label = 1
pb.SC_CHATOFFLINE_LIST_CHATOFFLINEDATA_ISONLINE_FIELD.has_default_value = false
pb.SC_CHATOFFLINE_LIST_CHATOFFLINEDATA_ISONLINE_FIELD.default_value = false
pb.SC_CHATOFFLINE_LIST_CHATOFFLINEDATA_ISONLINE_FIELD.type = 8
pb.SC_CHATOFFLINE_LIST_CHATOFFLINEDATA_ISONLINE_FIELD.cpp_type = 7

pb.SC_CHATOFFLINE_LIST_CHATOFFLINEDATA_VIPLEVEL_FIELD.name = "VipLevel"
pb.SC_CHATOFFLINE_LIST_CHATOFFLINEDATA_VIPLEVEL_FIELD.full_name = ".SC_ChatOffline_List.ChatofflineData.VipLevel"
pb.SC_CHATOFFLINE_LIST_CHATOFFLINEDATA_VIPLEVEL_FIELD.number = 9
pb.SC_CHATOFFLINE_LIST_CHATOFFLINEDATA_VIPLEVEL_FIELD.index = 8
pb.SC_CHATOFFLINE_LIST_CHATOFFLINEDATA_VIPLEVEL_FIELD.label = 1
pb.SC_CHATOFFLINE_LIST_CHATOFFLINEDATA_VIPLEVEL_FIELD.has_default_value = false
pb.SC_CHATOFFLINE_LIST_CHATOFFLINEDATA_VIPLEVEL_FIELD.default_value = 0
pb.SC_CHATOFFLINE_LIST_CHATOFFLINEDATA_VIPLEVEL_FIELD.type = 5
pb.SC_CHATOFFLINE_LIST_CHATOFFLINEDATA_VIPLEVEL_FIELD.cpp_type = 1

pb.SC_CHATOFFLINE_LIST_CHATOFFLINEDATA_COUNTRY_FIELD.name = "Country"
pb.SC_CHATOFFLINE_LIST_CHATOFFLINEDATA_COUNTRY_FIELD.full_name = ".SC_ChatOffline_List.ChatofflineData.Country"
pb.SC_CHATOFFLINE_LIST_CHATOFFLINEDATA_COUNTRY_FIELD.number = 10
pb.SC_CHATOFFLINE_LIST_CHATOFFLINEDATA_COUNTRY_FIELD.index = 9
pb.SC_CHATOFFLINE_LIST_CHATOFFLINEDATA_COUNTRY_FIELD.label = 1
pb.SC_CHATOFFLINE_LIST_CHATOFFLINEDATA_COUNTRY_FIELD.has_default_value = false
pb.SC_CHATOFFLINE_LIST_CHATOFFLINEDATA_COUNTRY_FIELD.default_value = 0
pb.SC_CHATOFFLINE_LIST_CHATOFFLINEDATA_COUNTRY_FIELD.type = 5
pb.SC_CHATOFFLINE_LIST_CHATOFFLINEDATA_COUNTRY_FIELD.cpp_type = 1

pb.SC_CHATOFFLINE_LIST_CHATOFFLINEDATA_RELATIONTYPE_FIELD.name = "RelationType"
pb.SC_CHATOFFLINE_LIST_CHATOFFLINEDATA_RELATIONTYPE_FIELD.full_name = ".SC_ChatOffline_List.ChatofflineData.RelationType"
pb.SC_CHATOFFLINE_LIST_CHATOFFLINEDATA_RELATIONTYPE_FIELD.number = 11
pb.SC_CHATOFFLINE_LIST_CHATOFFLINEDATA_RELATIONTYPE_FIELD.index = 10
pb.SC_CHATOFFLINE_LIST_CHATOFFLINEDATA_RELATIONTYPE_FIELD.label = 1
pb.SC_CHATOFFLINE_LIST_CHATOFFLINEDATA_RELATIONTYPE_FIELD.has_default_value = false
pb.SC_CHATOFFLINE_LIST_CHATOFFLINEDATA_RELATIONTYPE_FIELD.default_value = 0
pb.SC_CHATOFFLINE_LIST_CHATOFFLINEDATA_RELATIONTYPE_FIELD.type = 5
pb.SC_CHATOFFLINE_LIST_CHATOFFLINEDATA_RELATIONTYPE_FIELD.cpp_type = 1

pb.SC_CHATOFFLINE_LIST_CHATOFFLINEDATA_FRIENDVALUE_FIELD.name = "FriendValue"
pb.SC_CHATOFFLINE_LIST_CHATOFFLINEDATA_FRIENDVALUE_FIELD.full_name = ".SC_ChatOffline_List.ChatofflineData.FriendValue"
pb.SC_CHATOFFLINE_LIST_CHATOFFLINEDATA_FRIENDVALUE_FIELD.number = 12
pb.SC_CHATOFFLINE_LIST_CHATOFFLINEDATA_FRIENDVALUE_FIELD.index = 11
pb.SC_CHATOFFLINE_LIST_CHATOFFLINEDATA_FRIENDVALUE_FIELD.label = 1
pb.SC_CHATOFFLINE_LIST_CHATOFFLINEDATA_FRIENDVALUE_FIELD.has_default_value = false
pb.SC_CHATOFFLINE_LIST_CHATOFFLINEDATA_FRIENDVALUE_FIELD.default_value = 0
pb.SC_CHATOFFLINE_LIST_CHATOFFLINEDATA_FRIENDVALUE_FIELD.type = 5
pb.SC_CHATOFFLINE_LIST_CHATOFFLINEDATA_FRIENDVALUE_FIELD.cpp_type = 1

pb.SC_CHATOFFLINE_LIST_CHATOFFLINEDATA.name = "ChatofflineData"
pb.SC_CHATOFFLINE_LIST_CHATOFFLINEDATA.full_name = ".SC_ChatOffline_List.ChatofflineData"
pb.SC_CHATOFFLINE_LIST_CHATOFFLINEDATA.nested_types = {}
pb.SC_CHATOFFLINE_LIST_CHATOFFLINEDATA.enum_types = {}
pb.SC_CHATOFFLINE_LIST_CHATOFFLINEDATA.fields = {pb.SC_CHATOFFLINE_LIST_CHATOFFLINEDATA_SENDACTORID_FIELD, pb.SC_CHATOFFLINE_LIST_CHATOFFLINEDATA_SENDACTORNAME_FIELD, pb.SC_CHATOFFLINE_LIST_CHATOFFLINEDATA_SENDTIME_FIELD, pb.SC_CHATOFFLINE_LIST_CHATOFFLINEDATA_TALKCONTENT_FIELD, pb.SC_CHATOFFLINE_LIST_CHATOFFLINEDATA_LEVEL_FIELD, pb.SC_CHATOFFLINE_LIST_CHATOFFLINEDATA_VOCATION_FIELD, pb.SC_CHATOFFLINE_LIST_CHATOFFLINEDATA_POWER_FIELD, pb.SC_CHATOFFLINE_LIST_CHATOFFLINEDATA_ISONLINE_FIELD, pb.SC_CHATOFFLINE_LIST_CHATOFFLINEDATA_VIPLEVEL_FIELD, pb.SC_CHATOFFLINE_LIST_CHATOFFLINEDATA_COUNTRY_FIELD, pb.SC_CHATOFFLINE_LIST_CHATOFFLINEDATA_RELATIONTYPE_FIELD, pb.SC_CHATOFFLINE_LIST_CHATOFFLINEDATA_FRIENDVALUE_FIELD}
pb.SC_CHATOFFLINE_LIST_CHATOFFLINEDATA.is_extendable = false
pb.SC_CHATOFFLINE_LIST_CHATOFFLINEDATA.extensions = {}
pb.SC_CHATOFFLINE_LIST_CHATOFFLINEDATA.containing_type = pb.SC_CHATOFFLINE_LIST
pb.SC_CHATOFFLINE_LIST_CHATNUM_FIELD.name = "ChatNum"
pb.SC_CHATOFFLINE_LIST_CHATNUM_FIELD.full_name = ".SC_ChatOffline_List.ChatNum"
pb.SC_CHATOFFLINE_LIST_CHATNUM_FIELD.number = 1
pb.SC_CHATOFFLINE_LIST_CHATNUM_FIELD.index = 0
pb.SC_CHATOFFLINE_LIST_CHATNUM_FIELD.label = 1
pb.SC_CHATOFFLINE_LIST_CHATNUM_FIELD.has_default_value = false
pb.SC_CHATOFFLINE_LIST_CHATNUM_FIELD.default_value = 0
pb.SC_CHATOFFLINE_LIST_CHATNUM_FIELD.type = 13
pb.SC_CHATOFFLINE_LIST_CHATNUM_FIELD.cpp_type = 3

pb.SC_CHATOFFLINE_LIST_CHATOFFLINEDATA_FIELD.name = "chatofflineData"
pb.SC_CHATOFFLINE_LIST_CHATOFFLINEDATA_FIELD.full_name = ".SC_ChatOffline_List.chatofflineData"
pb.SC_CHATOFFLINE_LIST_CHATOFFLINEDATA_FIELD.number = 2
pb.SC_CHATOFFLINE_LIST_CHATOFFLINEDATA_FIELD.index = 1
pb.SC_CHATOFFLINE_LIST_CHATOFFLINEDATA_FIELD.label = 3
pb.SC_CHATOFFLINE_LIST_CHATOFFLINEDATA_FIELD.has_default_value = false
pb.SC_CHATOFFLINE_LIST_CHATOFFLINEDATA_FIELD.default_value = {}
pb.SC_CHATOFFLINE_LIST_CHATOFFLINEDATA_FIELD.message_type = pb.SC_CHATOFFLINE_LIST_CHATOFFLINEDATA
pb.SC_CHATOFFLINE_LIST_CHATOFFLINEDATA_FIELD.type = 11
pb.SC_CHATOFFLINE_LIST_CHATOFFLINEDATA_FIELD.cpp_type = 10

pb.SC_CHATOFFLINE_LIST.name = "SC_ChatOffline_List"
pb.SC_CHATOFFLINE_LIST.full_name = ".SC_ChatOffline_List"
pb.SC_CHATOFFLINE_LIST.nested_types = {pb.SC_CHATOFFLINE_LIST_CHATOFFLINEDATA}
pb.SC_CHATOFFLINE_LIST.enum_types = {}
pb.SC_CHATOFFLINE_LIST.fields = {pb.SC_CHATOFFLINE_LIST_CHATNUM_FIELD, pb.SC_CHATOFFLINE_LIST_CHATOFFLINEDATA_FIELD}
pb.SC_CHATOFFLINE_LIST.is_extendable = false
pb.SC_CHATOFFLINE_LIST.extensions = {}
pb.CS_DELETE_CHATOFFLINE_LIST.name = "CS_Delete_ChatOffline_List"
pb.CS_DELETE_CHATOFFLINE_LIST.full_name = ".CS_Delete_ChatOffline_List"
pb.CS_DELETE_CHATOFFLINE_LIST.nested_types = {}
pb.CS_DELETE_CHATOFFLINE_LIST.enum_types = {}
pb.CS_DELETE_CHATOFFLINE_LIST.fields = {}
pb.CS_DELETE_CHATOFFLINE_LIST.is_extendable = false
pb.CS_DELETE_CHATOFFLINE_LIST.extensions = {}
pb.SC_FRIEND_VALUE_SYNC_MASTERID_FIELD.name = "MasterID"
pb.SC_FRIEND_VALUE_SYNC_MASTERID_FIELD.full_name = ".SC_Friend_Value_Sync.MasterID"
pb.SC_FRIEND_VALUE_SYNC_MASTERID_FIELD.number = 1
pb.SC_FRIEND_VALUE_SYNC_MASTERID_FIELD.index = 0
pb.SC_FRIEND_VALUE_SYNC_MASTERID_FIELD.label = 1
pb.SC_FRIEND_VALUE_SYNC_MASTERID_FIELD.has_default_value = false
pb.SC_FRIEND_VALUE_SYNC_MASTERID_FIELD.default_value = 0
pb.SC_FRIEND_VALUE_SYNC_MASTERID_FIELD.type = 13
pb.SC_FRIEND_VALUE_SYNC_MASTERID_FIELD.cpp_type = 3

pb.SC_FRIEND_VALUE_SYNC_FRIENDID_FIELD.name = "FriendID"
pb.SC_FRIEND_VALUE_SYNC_FRIENDID_FIELD.full_name = ".SC_Friend_Value_Sync.FriendID"
pb.SC_FRIEND_VALUE_SYNC_FRIENDID_FIELD.number = 2
pb.SC_FRIEND_VALUE_SYNC_FRIENDID_FIELD.index = 1
pb.SC_FRIEND_VALUE_SYNC_FRIENDID_FIELD.label = 1
pb.SC_FRIEND_VALUE_SYNC_FRIENDID_FIELD.has_default_value = false
pb.SC_FRIEND_VALUE_SYNC_FRIENDID_FIELD.default_value = 0
pb.SC_FRIEND_VALUE_SYNC_FRIENDID_FIELD.type = 13
pb.SC_FRIEND_VALUE_SYNC_FRIENDID_FIELD.cpp_type = 3

pb.SC_FRIEND_VALUE_SYNC_FRIENDVALUE_FIELD.name = "FriendValue"
pb.SC_FRIEND_VALUE_SYNC_FRIENDVALUE_FIELD.full_name = ".SC_Friend_Value_Sync.FriendValue"
pb.SC_FRIEND_VALUE_SYNC_FRIENDVALUE_FIELD.number = 3
pb.SC_FRIEND_VALUE_SYNC_FRIENDVALUE_FIELD.index = 2
pb.SC_FRIEND_VALUE_SYNC_FRIENDVALUE_FIELD.label = 1
pb.SC_FRIEND_VALUE_SYNC_FRIENDVALUE_FIELD.has_default_value = false
pb.SC_FRIEND_VALUE_SYNC_FRIENDVALUE_FIELD.default_value = 0
pb.SC_FRIEND_VALUE_SYNC_FRIENDVALUE_FIELD.type = 5
pb.SC_FRIEND_VALUE_SYNC_FRIENDVALUE_FIELD.cpp_type = 1

pb.SC_FRIEND_VALUE_SYNC_TACITVALUE_FIELD.name = "TacitValue"
pb.SC_FRIEND_VALUE_SYNC_TACITVALUE_FIELD.full_name = ".SC_Friend_Value_Sync.TacitValue"
pb.SC_FRIEND_VALUE_SYNC_TACITVALUE_FIELD.number = 4
pb.SC_FRIEND_VALUE_SYNC_TACITVALUE_FIELD.index = 3
pb.SC_FRIEND_VALUE_SYNC_TACITVALUE_FIELD.label = 1
pb.SC_FRIEND_VALUE_SYNC_TACITVALUE_FIELD.has_default_value = false
pb.SC_FRIEND_VALUE_SYNC_TACITVALUE_FIELD.default_value = 0
pb.SC_FRIEND_VALUE_SYNC_TACITVALUE_FIELD.type = 5
pb.SC_FRIEND_VALUE_SYNC_TACITVALUE_FIELD.cpp_type = 1

pb.SC_FRIEND_VALUE_SYNC.name = "SC_Friend_Value_Sync"
pb.SC_FRIEND_VALUE_SYNC.full_name = ".SC_Friend_Value_Sync"
pb.SC_FRIEND_VALUE_SYNC.nested_types = {}
pb.SC_FRIEND_VALUE_SYNC.enum_types = {}
pb.SC_FRIEND_VALUE_SYNC.fields = {pb.SC_FRIEND_VALUE_SYNC_MASTERID_FIELD, pb.SC_FRIEND_VALUE_SYNC_FRIENDID_FIELD, pb.SC_FRIEND_VALUE_SYNC_FRIENDVALUE_FIELD, pb.SC_FRIEND_VALUE_SYNC_TACITVALUE_FIELD}
pb.SC_FRIEND_VALUE_SYNC.is_extendable = false
pb.SC_FRIEND_VALUE_SYNC.extensions = {}
pb.CS_FRIEND_QUERY_BY_NAME_NAME_FIELD.name = "Name"
pb.CS_FRIEND_QUERY_BY_NAME_NAME_FIELD.full_name = ".CS_Friend_Query_By_Name.Name"
pb.CS_FRIEND_QUERY_BY_NAME_NAME_FIELD.number = 1
pb.CS_FRIEND_QUERY_BY_NAME_NAME_FIELD.index = 0
pb.CS_FRIEND_QUERY_BY_NAME_NAME_FIELD.label = 1
pb.CS_FRIEND_QUERY_BY_NAME_NAME_FIELD.has_default_value = false
pb.CS_FRIEND_QUERY_BY_NAME_NAME_FIELD.default_value = ""
pb.CS_FRIEND_QUERY_BY_NAME_NAME_FIELD.type = 9
pb.CS_FRIEND_QUERY_BY_NAME_NAME_FIELD.cpp_type = 9

pb.CS_FRIEND_QUERY_BY_NAME.name = "CS_Friend_Query_By_Name"
pb.CS_FRIEND_QUERY_BY_NAME.full_name = ".CS_Friend_Query_By_Name"
pb.CS_FRIEND_QUERY_BY_NAME.nested_types = {}
pb.CS_FRIEND_QUERY_BY_NAME.enum_types = {}
pb.CS_FRIEND_QUERY_BY_NAME.fields = {pb.CS_FRIEND_QUERY_BY_NAME_NAME_FIELD}
pb.CS_FRIEND_QUERY_BY_NAME.is_extendable = false
pb.CS_FRIEND_QUERY_BY_NAME.extensions = {}
pb.SC_FRIEND_QUERY_RESULT_ENTITYUID_FIELD.name = "EntityUID"
pb.SC_FRIEND_QUERY_RESULT_ENTITYUID_FIELD.full_name = ".SC_Friend_Query_Result.EntityUID"
pb.SC_FRIEND_QUERY_RESULT_ENTITYUID_FIELD.number = 1
pb.SC_FRIEND_QUERY_RESULT_ENTITYUID_FIELD.index = 0
pb.SC_FRIEND_QUERY_RESULT_ENTITYUID_FIELD.label = 1
pb.SC_FRIEND_QUERY_RESULT_ENTITYUID_FIELD.has_default_value = false
pb.SC_FRIEND_QUERY_RESULT_ENTITYUID_FIELD.default_value = 0
pb.SC_FRIEND_QUERY_RESULT_ENTITYUID_FIELD.type = 4
pb.SC_FRIEND_QUERY_RESULT_ENTITYUID_FIELD.cpp_type = 4

pb.SC_FRIEND_QUERY_RESULT_NAME_FIELD.name = "Name"
pb.SC_FRIEND_QUERY_RESULT_NAME_FIELD.full_name = ".SC_Friend_Query_Result.Name"
pb.SC_FRIEND_QUERY_RESULT_NAME_FIELD.number = 2
pb.SC_FRIEND_QUERY_RESULT_NAME_FIELD.index = 1
pb.SC_FRIEND_QUERY_RESULT_NAME_FIELD.label = 1
pb.SC_FRIEND_QUERY_RESULT_NAME_FIELD.has_default_value = false
pb.SC_FRIEND_QUERY_RESULT_NAME_FIELD.default_value = ""
pb.SC_FRIEND_QUERY_RESULT_NAME_FIELD.type = 9
pb.SC_FRIEND_QUERY_RESULT_NAME_FIELD.cpp_type = 9

pb.SC_FRIEND_QUERY_RESULT_LEVEL_FIELD.name = "Level"
pb.SC_FRIEND_QUERY_RESULT_LEVEL_FIELD.full_name = ".SC_Friend_Query_Result.Level"
pb.SC_FRIEND_QUERY_RESULT_LEVEL_FIELD.number = 3
pb.SC_FRIEND_QUERY_RESULT_LEVEL_FIELD.index = 2
pb.SC_FRIEND_QUERY_RESULT_LEVEL_FIELD.label = 1
pb.SC_FRIEND_QUERY_RESULT_LEVEL_FIELD.has_default_value = false
pb.SC_FRIEND_QUERY_RESULT_LEVEL_FIELD.default_value = 0
pb.SC_FRIEND_QUERY_RESULT_LEVEL_FIELD.type = 13
pb.SC_FRIEND_QUERY_RESULT_LEVEL_FIELD.cpp_type = 3

pb.SC_FRIEND_QUERY_RESULT_VOCATION_FIELD.name = "Vocation"
pb.SC_FRIEND_QUERY_RESULT_VOCATION_FIELD.full_name = ".SC_Friend_Query_Result.Vocation"
pb.SC_FRIEND_QUERY_RESULT_VOCATION_FIELD.number = 4
pb.SC_FRIEND_QUERY_RESULT_VOCATION_FIELD.index = 3
pb.SC_FRIEND_QUERY_RESULT_VOCATION_FIELD.label = 1
pb.SC_FRIEND_QUERY_RESULT_VOCATION_FIELD.has_default_value = false
pb.SC_FRIEND_QUERY_RESULT_VOCATION_FIELD.default_value = 0
pb.SC_FRIEND_QUERY_RESULT_VOCATION_FIELD.type = 13
pb.SC_FRIEND_QUERY_RESULT_VOCATION_FIELD.cpp_type = 3

pb.SC_FRIEND_QUERY_RESULT_POWER_FIELD.name = "Power"
pb.SC_FRIEND_QUERY_RESULT_POWER_FIELD.full_name = ".SC_Friend_Query_Result.Power"
pb.SC_FRIEND_QUERY_RESULT_POWER_FIELD.number = 5
pb.SC_FRIEND_QUERY_RESULT_POWER_FIELD.index = 4
pb.SC_FRIEND_QUERY_RESULT_POWER_FIELD.label = 1
pb.SC_FRIEND_QUERY_RESULT_POWER_FIELD.has_default_value = false
pb.SC_FRIEND_QUERY_RESULT_POWER_FIELD.default_value = 0
pb.SC_FRIEND_QUERY_RESULT_POWER_FIELD.type = 13
pb.SC_FRIEND_QUERY_RESULT_POWER_FIELD.cpp_type = 3

pb.SC_FRIEND_QUERY_RESULT_ISONLINE_FIELD.name = "IsOnLine"
pb.SC_FRIEND_QUERY_RESULT_ISONLINE_FIELD.full_name = ".SC_Friend_Query_Result.IsOnLine"
pb.SC_FRIEND_QUERY_RESULT_ISONLINE_FIELD.number = 6
pb.SC_FRIEND_QUERY_RESULT_ISONLINE_FIELD.index = 5
pb.SC_FRIEND_QUERY_RESULT_ISONLINE_FIELD.label = 1
pb.SC_FRIEND_QUERY_RESULT_ISONLINE_FIELD.has_default_value = false
pb.SC_FRIEND_QUERY_RESULT_ISONLINE_FIELD.default_value = false
pb.SC_FRIEND_QUERY_RESULT_ISONLINE_FIELD.type = 8
pb.SC_FRIEND_QUERY_RESULT_ISONLINE_FIELD.cpp_type = 7

pb.SC_FRIEND_QUERY_RESULT_VIP_FIELD.name = "Vip"
pb.SC_FRIEND_QUERY_RESULT_VIP_FIELD.full_name = ".SC_Friend_Query_Result.Vip"
pb.SC_FRIEND_QUERY_RESULT_VIP_FIELD.number = 7
pb.SC_FRIEND_QUERY_RESULT_VIP_FIELD.index = 6
pb.SC_FRIEND_QUERY_RESULT_VIP_FIELD.label = 1
pb.SC_FRIEND_QUERY_RESULT_VIP_FIELD.has_default_value = false
pb.SC_FRIEND_QUERY_RESULT_VIP_FIELD.default_value = 0
pb.SC_FRIEND_QUERY_RESULT_VIP_FIELD.type = 5
pb.SC_FRIEND_QUERY_RESULT_VIP_FIELD.cpp_type = 1

pb.SC_FRIEND_QUERY_RESULT_COUNTRY_FIELD.name = "Country"
pb.SC_FRIEND_QUERY_RESULT_COUNTRY_FIELD.full_name = ".SC_Friend_Query_Result.Country"
pb.SC_FRIEND_QUERY_RESULT_COUNTRY_FIELD.number = 8
pb.SC_FRIEND_QUERY_RESULT_COUNTRY_FIELD.index = 7
pb.SC_FRIEND_QUERY_RESULT_COUNTRY_FIELD.label = 1
pb.SC_FRIEND_QUERY_RESULT_COUNTRY_FIELD.has_default_value = false
pb.SC_FRIEND_QUERY_RESULT_COUNTRY_FIELD.default_value = 0
pb.SC_FRIEND_QUERY_RESULT_COUNTRY_FIELD.type = 5
pb.SC_FRIEND_QUERY_RESULT_COUNTRY_FIELD.cpp_type = 1

pb.SC_FRIEND_QUERY_RESULT.name = "SC_Friend_Query_Result"
pb.SC_FRIEND_QUERY_RESULT.full_name = ".SC_Friend_Query_Result"
pb.SC_FRIEND_QUERY_RESULT.nested_types = {}
pb.SC_FRIEND_QUERY_RESULT.enum_types = {}
pb.SC_FRIEND_QUERY_RESULT.fields = {pb.SC_FRIEND_QUERY_RESULT_ENTITYUID_FIELD, pb.SC_FRIEND_QUERY_RESULT_NAME_FIELD, pb.SC_FRIEND_QUERY_RESULT_LEVEL_FIELD, pb.SC_FRIEND_QUERY_RESULT_VOCATION_FIELD, pb.SC_FRIEND_QUERY_RESULT_POWER_FIELD, pb.SC_FRIEND_QUERY_RESULT_ISONLINE_FIELD, pb.SC_FRIEND_QUERY_RESULT_VIP_FIELD, pb.SC_FRIEND_QUERY_RESULT_COUNTRY_FIELD}
pb.SC_FRIEND_QUERY_RESULT.is_extendable = false
pb.SC_FRIEND_QUERY_RESULT.extensions = {}
pb.CS_BLACK_LIST_ADD_NAME_FIELD.name = "Name"
pb.CS_BLACK_LIST_ADD_NAME_FIELD.full_name = ".CS_Black_List_Add.Name"
pb.CS_BLACK_LIST_ADD_NAME_FIELD.number = 1
pb.CS_BLACK_LIST_ADD_NAME_FIELD.index = 0
pb.CS_BLACK_LIST_ADD_NAME_FIELD.label = 1
pb.CS_BLACK_LIST_ADD_NAME_FIELD.has_default_value = false
pb.CS_BLACK_LIST_ADD_NAME_FIELD.default_value = ""
pb.CS_BLACK_LIST_ADD_NAME_FIELD.type = 9
pb.CS_BLACK_LIST_ADD_NAME_FIELD.cpp_type = 9

pb.CS_BLACK_LIST_ADD.name = "CS_Black_List_Add"
pb.CS_BLACK_LIST_ADD.full_name = ".CS_Black_List_Add"
pb.CS_BLACK_LIST_ADD.nested_types = {}
pb.CS_BLACK_LIST_ADD.enum_types = {}
pb.CS_BLACK_LIST_ADD.fields = {pb.CS_BLACK_LIST_ADD_NAME_FIELD}
pb.CS_BLACK_LIST_ADD.is_extendable = false
pb.CS_BLACK_LIST_ADD.extensions = {}
pb.CS_FRIEND_QUERY_BEFORECHAT_ACTORID_FIELD.name = "ActorID"
pb.CS_FRIEND_QUERY_BEFORECHAT_ACTORID_FIELD.full_name = ".CS_Friend_Query_BeforeChat.ActorID"
pb.CS_FRIEND_QUERY_BEFORECHAT_ACTORID_FIELD.number = 1
pb.CS_FRIEND_QUERY_BEFORECHAT_ACTORID_FIELD.index = 0
pb.CS_FRIEND_QUERY_BEFORECHAT_ACTORID_FIELD.label = 1
pb.CS_FRIEND_QUERY_BEFORECHAT_ACTORID_FIELD.has_default_value = false
pb.CS_FRIEND_QUERY_BEFORECHAT_ACTORID_FIELD.default_value = 0
pb.CS_FRIEND_QUERY_BEFORECHAT_ACTORID_FIELD.type = 13
pb.CS_FRIEND_QUERY_BEFORECHAT_ACTORID_FIELD.cpp_type = 3

pb.CS_FRIEND_QUERY_BEFORECHAT.name = "CS_Friend_Query_BeforeChat"
pb.CS_FRIEND_QUERY_BEFORECHAT.full_name = ".CS_Friend_Query_BeforeChat"
pb.CS_FRIEND_QUERY_BEFORECHAT.nested_types = {}
pb.CS_FRIEND_QUERY_BEFORECHAT.enum_types = {}
pb.CS_FRIEND_QUERY_BEFORECHAT.fields = {pb.CS_FRIEND_QUERY_BEFORECHAT_ACTORID_FIELD}
pb.CS_FRIEND_QUERY_BEFORECHAT.is_extendable = false
pb.CS_FRIEND_QUERY_BEFORECHAT.extensions = {}
pb.SC_FRIEND_QUERY_BEFORECHAT_ACTORID_FIELD.name = "ActorID"
pb.SC_FRIEND_QUERY_BEFORECHAT_ACTORID_FIELD.full_name = ".SC_Friend_Query_BeforeChat.ActorID"
pb.SC_FRIEND_QUERY_BEFORECHAT_ACTORID_FIELD.number = 1
pb.SC_FRIEND_QUERY_BEFORECHAT_ACTORID_FIELD.index = 0
pb.SC_FRIEND_QUERY_BEFORECHAT_ACTORID_FIELD.label = 1
pb.SC_FRIEND_QUERY_BEFORECHAT_ACTORID_FIELD.has_default_value = false
pb.SC_FRIEND_QUERY_BEFORECHAT_ACTORID_FIELD.default_value = 0
pb.SC_FRIEND_QUERY_BEFORECHAT_ACTORID_FIELD.type = 13
pb.SC_FRIEND_QUERY_BEFORECHAT_ACTORID_FIELD.cpp_type = 3

pb.SC_FRIEND_QUERY_BEFORECHAT_NAME_FIELD.name = "Name"
pb.SC_FRIEND_QUERY_BEFORECHAT_NAME_FIELD.full_name = ".SC_Friend_Query_BeforeChat.Name"
pb.SC_FRIEND_QUERY_BEFORECHAT_NAME_FIELD.number = 2
pb.SC_FRIEND_QUERY_BEFORECHAT_NAME_FIELD.index = 1
pb.SC_FRIEND_QUERY_BEFORECHAT_NAME_FIELD.label = 1
pb.SC_FRIEND_QUERY_BEFORECHAT_NAME_FIELD.has_default_value = false
pb.SC_FRIEND_QUERY_BEFORECHAT_NAME_FIELD.default_value = ""
pb.SC_FRIEND_QUERY_BEFORECHAT_NAME_FIELD.type = 9
pb.SC_FRIEND_QUERY_BEFORECHAT_NAME_FIELD.cpp_type = 9

pb.SC_FRIEND_QUERY_BEFORECHAT_LEVEL_FIELD.name = "Level"
pb.SC_FRIEND_QUERY_BEFORECHAT_LEVEL_FIELD.full_name = ".SC_Friend_Query_BeforeChat.Level"
pb.SC_FRIEND_QUERY_BEFORECHAT_LEVEL_FIELD.number = 3
pb.SC_FRIEND_QUERY_BEFORECHAT_LEVEL_FIELD.index = 2
pb.SC_FRIEND_QUERY_BEFORECHAT_LEVEL_FIELD.label = 1
pb.SC_FRIEND_QUERY_BEFORECHAT_LEVEL_FIELD.has_default_value = false
pb.SC_FRIEND_QUERY_BEFORECHAT_LEVEL_FIELD.default_value = 0
pb.SC_FRIEND_QUERY_BEFORECHAT_LEVEL_FIELD.type = 13
pb.SC_FRIEND_QUERY_BEFORECHAT_LEVEL_FIELD.cpp_type = 3

pb.SC_FRIEND_QUERY_BEFORECHAT_VOCATION_FIELD.name = "Vocation"
pb.SC_FRIEND_QUERY_BEFORECHAT_VOCATION_FIELD.full_name = ".SC_Friend_Query_BeforeChat.Vocation"
pb.SC_FRIEND_QUERY_BEFORECHAT_VOCATION_FIELD.number = 4
pb.SC_FRIEND_QUERY_BEFORECHAT_VOCATION_FIELD.index = 3
pb.SC_FRIEND_QUERY_BEFORECHAT_VOCATION_FIELD.label = 1
pb.SC_FRIEND_QUERY_BEFORECHAT_VOCATION_FIELD.has_default_value = false
pb.SC_FRIEND_QUERY_BEFORECHAT_VOCATION_FIELD.default_value = 0
pb.SC_FRIEND_QUERY_BEFORECHAT_VOCATION_FIELD.type = 13
pb.SC_FRIEND_QUERY_BEFORECHAT_VOCATION_FIELD.cpp_type = 3

pb.SC_FRIEND_QUERY_BEFORECHAT_POWER_FIELD.name = "Power"
pb.SC_FRIEND_QUERY_BEFORECHAT_POWER_FIELD.full_name = ".SC_Friend_Query_BeforeChat.Power"
pb.SC_FRIEND_QUERY_BEFORECHAT_POWER_FIELD.number = 5
pb.SC_FRIEND_QUERY_BEFORECHAT_POWER_FIELD.index = 4
pb.SC_FRIEND_QUERY_BEFORECHAT_POWER_FIELD.label = 1
pb.SC_FRIEND_QUERY_BEFORECHAT_POWER_FIELD.has_default_value = false
pb.SC_FRIEND_QUERY_BEFORECHAT_POWER_FIELD.default_value = 0
pb.SC_FRIEND_QUERY_BEFORECHAT_POWER_FIELD.type = 13
pb.SC_FRIEND_QUERY_BEFORECHAT_POWER_FIELD.cpp_type = 3

pb.SC_FRIEND_QUERY_BEFORECHAT_ISONLINE_FIELD.name = "IsOnLine"
pb.SC_FRIEND_QUERY_BEFORECHAT_ISONLINE_FIELD.full_name = ".SC_Friend_Query_BeforeChat.IsOnLine"
pb.SC_FRIEND_QUERY_BEFORECHAT_ISONLINE_FIELD.number = 6
pb.SC_FRIEND_QUERY_BEFORECHAT_ISONLINE_FIELD.index = 5
pb.SC_FRIEND_QUERY_BEFORECHAT_ISONLINE_FIELD.label = 1
pb.SC_FRIEND_QUERY_BEFORECHAT_ISONLINE_FIELD.has_default_value = false
pb.SC_FRIEND_QUERY_BEFORECHAT_ISONLINE_FIELD.default_value = false
pb.SC_FRIEND_QUERY_BEFORECHAT_ISONLINE_FIELD.type = 8
pb.SC_FRIEND_QUERY_BEFORECHAT_ISONLINE_FIELD.cpp_type = 7

pb.SC_FRIEND_QUERY_BEFORECHAT_VIP_FIELD.name = "Vip"
pb.SC_FRIEND_QUERY_BEFORECHAT_VIP_FIELD.full_name = ".SC_Friend_Query_BeforeChat.Vip"
pb.SC_FRIEND_QUERY_BEFORECHAT_VIP_FIELD.number = 7
pb.SC_FRIEND_QUERY_BEFORECHAT_VIP_FIELD.index = 6
pb.SC_FRIEND_QUERY_BEFORECHAT_VIP_FIELD.label = 1
pb.SC_FRIEND_QUERY_BEFORECHAT_VIP_FIELD.has_default_value = false
pb.SC_FRIEND_QUERY_BEFORECHAT_VIP_FIELD.default_value = 0
pb.SC_FRIEND_QUERY_BEFORECHAT_VIP_FIELD.type = 5
pb.SC_FRIEND_QUERY_BEFORECHAT_VIP_FIELD.cpp_type = 1

pb.SC_FRIEND_QUERY_BEFORECHAT_COUNTRY_FIELD.name = "Country"
pb.SC_FRIEND_QUERY_BEFORECHAT_COUNTRY_FIELD.full_name = ".SC_Friend_Query_BeforeChat.Country"
pb.SC_FRIEND_QUERY_BEFORECHAT_COUNTRY_FIELD.number = 8
pb.SC_FRIEND_QUERY_BEFORECHAT_COUNTRY_FIELD.index = 7
pb.SC_FRIEND_QUERY_BEFORECHAT_COUNTRY_FIELD.label = 1
pb.SC_FRIEND_QUERY_BEFORECHAT_COUNTRY_FIELD.has_default_value = false
pb.SC_FRIEND_QUERY_BEFORECHAT_COUNTRY_FIELD.default_value = 0
pb.SC_FRIEND_QUERY_BEFORECHAT_COUNTRY_FIELD.type = 5
pb.SC_FRIEND_QUERY_BEFORECHAT_COUNTRY_FIELD.cpp_type = 1

pb.SC_FRIEND_QUERY_BEFORECHAT.name = "SC_Friend_Query_BeforeChat"
pb.SC_FRIEND_QUERY_BEFORECHAT.full_name = ".SC_Friend_Query_BeforeChat"
pb.SC_FRIEND_QUERY_BEFORECHAT.nested_types = {}
pb.SC_FRIEND_QUERY_BEFORECHAT.enum_types = {}
pb.SC_FRIEND_QUERY_BEFORECHAT.fields = {pb.SC_FRIEND_QUERY_BEFORECHAT_ACTORID_FIELD, pb.SC_FRIEND_QUERY_BEFORECHAT_NAME_FIELD, pb.SC_FRIEND_QUERY_BEFORECHAT_LEVEL_FIELD, pb.SC_FRIEND_QUERY_BEFORECHAT_VOCATION_FIELD, pb.SC_FRIEND_QUERY_BEFORECHAT_POWER_FIELD, pb.SC_FRIEND_QUERY_BEFORECHAT_ISONLINE_FIELD, pb.SC_FRIEND_QUERY_BEFORECHAT_VIP_FIELD, pb.SC_FRIEND_QUERY_BEFORECHAT_COUNTRY_FIELD}
pb.SC_FRIEND_QUERY_BEFORECHAT.is_extendable = false
pb.SC_FRIEND_QUERY_BEFORECHAT.extensions = {}
pb.CS_QUERY_PLAYER_INFO_ACTORID_FIELD.name = "ActorID"
pb.CS_QUERY_PLAYER_INFO_ACTORID_FIELD.full_name = ".CS_Query_Player_Info.ActorID"
pb.CS_QUERY_PLAYER_INFO_ACTORID_FIELD.number = 1
pb.CS_QUERY_PLAYER_INFO_ACTORID_FIELD.index = 0
pb.CS_QUERY_PLAYER_INFO_ACTORID_FIELD.label = 1
pb.CS_QUERY_PLAYER_INFO_ACTORID_FIELD.has_default_value = false
pb.CS_QUERY_PLAYER_INFO_ACTORID_FIELD.default_value = 0
pb.CS_QUERY_PLAYER_INFO_ACTORID_FIELD.type = 13
pb.CS_QUERY_PLAYER_INFO_ACTORID_FIELD.cpp_type = 3

pb.CS_QUERY_PLAYER_INFO.name = "CS_Query_Player_Info"
pb.CS_QUERY_PLAYER_INFO.full_name = ".CS_Query_Player_Info"
pb.CS_QUERY_PLAYER_INFO.nested_types = {}
pb.CS_QUERY_PLAYER_INFO.enum_types = {}
pb.CS_QUERY_PLAYER_INFO.fields = {pb.CS_QUERY_PLAYER_INFO_ACTORID_FIELD}
pb.CS_QUERY_PLAYER_INFO.is_extendable = false
pb.CS_QUERY_PLAYER_INFO.extensions = {}
pb.SC_QUERY_PLAYER_INFO_PLAYERFIELD_PROPVALUE_FIELD.name = "propValue"
pb.SC_QUERY_PLAYER_INFO_PLAYERFIELD_PROPVALUE_FIELD.full_name = ".SC_Query_Player_Info.PlayerField.propValue"
pb.SC_QUERY_PLAYER_INFO_PLAYERFIELD_PROPVALUE_FIELD.number = 1
pb.SC_QUERY_PLAYER_INFO_PLAYERFIELD_PROPVALUE_FIELD.index = 0
pb.SC_QUERY_PLAYER_INFO_PLAYERFIELD_PROPVALUE_FIELD.label = 1
pb.SC_QUERY_PLAYER_INFO_PLAYERFIELD_PROPVALUE_FIELD.has_default_value = false
pb.SC_QUERY_PLAYER_INFO_PLAYERFIELD_PROPVALUE_FIELD.default_value = 0
pb.SC_QUERY_PLAYER_INFO_PLAYERFIELD_PROPVALUE_FIELD.type = 13
pb.SC_QUERY_PLAYER_INFO_PLAYERFIELD_PROPVALUE_FIELD.cpp_type = 3

pb.SC_QUERY_PLAYER_INFO_PLAYERFIELD.name = "PlayerField"
pb.SC_QUERY_PLAYER_INFO_PLAYERFIELD.full_name = ".SC_Query_Player_Info.PlayerField"
pb.SC_QUERY_PLAYER_INFO_PLAYERFIELD.nested_types = {}
pb.SC_QUERY_PLAYER_INFO_PLAYERFIELD.enum_types = {}
pb.SC_QUERY_PLAYER_INFO_PLAYERFIELD.fields = {pb.SC_QUERY_PLAYER_INFO_PLAYERFIELD_PROPVALUE_FIELD}
pb.SC_QUERY_PLAYER_INFO_PLAYERFIELD.is_extendable = false
pb.SC_QUERY_PLAYER_INFO_PLAYERFIELD.extensions = {}
pb.SC_QUERY_PLAYER_INFO_PLAYERFIELD.containing_type = pb.SC_QUERY_PLAYER_INFO
pb.SC_QUERY_PLAYER_INFO_EQUIPMENT_EQUIPSLOT_FIELD.name = "equipSlot"
pb.SC_QUERY_PLAYER_INFO_EQUIPMENT_EQUIPSLOT_FIELD.full_name = ".SC_Query_Player_Info.Equipment.equipSlot"
pb.SC_QUERY_PLAYER_INFO_EQUIPMENT_EQUIPSLOT_FIELD.number = 1
pb.SC_QUERY_PLAYER_INFO_EQUIPMENT_EQUIPSLOT_FIELD.index = 0
pb.SC_QUERY_PLAYER_INFO_EQUIPMENT_EQUIPSLOT_FIELD.label = 1
pb.SC_QUERY_PLAYER_INFO_EQUIPMENT_EQUIPSLOT_FIELD.has_default_value = false
pb.SC_QUERY_PLAYER_INFO_EQUIPMENT_EQUIPSLOT_FIELD.default_value = 0
pb.SC_QUERY_PLAYER_INFO_EQUIPMENT_EQUIPSLOT_FIELD.type = 13
pb.SC_QUERY_PLAYER_INFO_EQUIPMENT_EQUIPSLOT_FIELD.cpp_type = 3

pb.SC_QUERY_PLAYER_INFO_EQUIPMENT_EQUIPID_FIELD.name = "equipID"
pb.SC_QUERY_PLAYER_INFO_EQUIPMENT_EQUIPID_FIELD.full_name = ".SC_Query_Player_Info.Equipment.equipID"
pb.SC_QUERY_PLAYER_INFO_EQUIPMENT_EQUIPID_FIELD.number = 2
pb.SC_QUERY_PLAYER_INFO_EQUIPMENT_EQUIPID_FIELD.index = 1
pb.SC_QUERY_PLAYER_INFO_EQUIPMENT_EQUIPID_FIELD.label = 1
pb.SC_QUERY_PLAYER_INFO_EQUIPMENT_EQUIPID_FIELD.has_default_value = false
pb.SC_QUERY_PLAYER_INFO_EQUIPMENT_EQUIPID_FIELD.default_value = 0
pb.SC_QUERY_PLAYER_INFO_EQUIPMENT_EQUIPID_FIELD.type = 13
pb.SC_QUERY_PLAYER_INFO_EQUIPMENT_EQUIPID_FIELD.cpp_type = 3

pb.SC_QUERY_PLAYER_INFO_EQUIPMENT.name = "Equipment"
pb.SC_QUERY_PLAYER_INFO_EQUIPMENT.full_name = ".SC_Query_Player_Info.Equipment"
pb.SC_QUERY_PLAYER_INFO_EQUIPMENT.nested_types = {}
pb.SC_QUERY_PLAYER_INFO_EQUIPMENT.enum_types = {}
pb.SC_QUERY_PLAYER_INFO_EQUIPMENT.fields = {pb.SC_QUERY_PLAYER_INFO_EQUIPMENT_EQUIPSLOT_FIELD, pb.SC_QUERY_PLAYER_INFO_EQUIPMENT_EQUIPID_FIELD}
pb.SC_QUERY_PLAYER_INFO_EQUIPMENT.is_extendable = false
pb.SC_QUERY_PLAYER_INFO_EQUIPMENT.extensions = {}
pb.SC_QUERY_PLAYER_INFO_EQUIPMENT.containing_type = pb.SC_QUERY_PLAYER_INFO
pb.SC_QUERY_PLAYER_INFO_GODWEAPONSPIRIT_POS_FIELD.name = "pos"
pb.SC_QUERY_PLAYER_INFO_GODWEAPONSPIRIT_POS_FIELD.full_name = ".SC_Query_Player_Info.GodWeaponSpirit.pos"
pb.SC_QUERY_PLAYER_INFO_GODWEAPONSPIRIT_POS_FIELD.number = 1
pb.SC_QUERY_PLAYER_INFO_GODWEAPONSPIRIT_POS_FIELD.index = 0
pb.SC_QUERY_PLAYER_INFO_GODWEAPONSPIRIT_POS_FIELD.label = 1
pb.SC_QUERY_PLAYER_INFO_GODWEAPONSPIRIT_POS_FIELD.has_default_value = false
pb.SC_QUERY_PLAYER_INFO_GODWEAPONSPIRIT_POS_FIELD.default_value = 0
pb.SC_QUERY_PLAYER_INFO_GODWEAPONSPIRIT_POS_FIELD.type = 13
pb.SC_QUERY_PLAYER_INFO_GODWEAPONSPIRIT_POS_FIELD.cpp_type = 3

pb.SC_QUERY_PLAYER_INFO_GODWEAPONSPIRIT_SPIRITID_FIELD.name = "spiritID"
pb.SC_QUERY_PLAYER_INFO_GODWEAPONSPIRIT_SPIRITID_FIELD.full_name = ".SC_Query_Player_Info.GodWeaponSpirit.spiritID"
pb.SC_QUERY_PLAYER_INFO_GODWEAPONSPIRIT_SPIRITID_FIELD.number = 2
pb.SC_QUERY_PLAYER_INFO_GODWEAPONSPIRIT_SPIRITID_FIELD.index = 1
pb.SC_QUERY_PLAYER_INFO_GODWEAPONSPIRIT_SPIRITID_FIELD.label = 1
pb.SC_QUERY_PLAYER_INFO_GODWEAPONSPIRIT_SPIRITID_FIELD.has_default_value = false
pb.SC_QUERY_PLAYER_INFO_GODWEAPONSPIRIT_SPIRITID_FIELD.default_value = 0
pb.SC_QUERY_PLAYER_INFO_GODWEAPONSPIRIT_SPIRITID_FIELD.type = 13
pb.SC_QUERY_PLAYER_INFO_GODWEAPONSPIRIT_SPIRITID_FIELD.cpp_type = 3

pb.SC_QUERY_PLAYER_INFO_GODWEAPONSPIRIT.name = "GodWeaponSpirit"
pb.SC_QUERY_PLAYER_INFO_GODWEAPONSPIRIT.full_name = ".SC_Query_Player_Info.GodWeaponSpirit"
pb.SC_QUERY_PLAYER_INFO_GODWEAPONSPIRIT.nested_types = {}
pb.SC_QUERY_PLAYER_INFO_GODWEAPONSPIRIT.enum_types = {}
pb.SC_QUERY_PLAYER_INFO_GODWEAPONSPIRIT.fields = {pb.SC_QUERY_PLAYER_INFO_GODWEAPONSPIRIT_POS_FIELD, pb.SC_QUERY_PLAYER_INFO_GODWEAPONSPIRIT_SPIRITID_FIELD}
pb.SC_QUERY_PLAYER_INFO_GODWEAPONSPIRIT.is_extendable = false
pb.SC_QUERY_PLAYER_INFO_GODWEAPONSPIRIT.extensions = {}
pb.SC_QUERY_PLAYER_INFO_GODWEAPONSPIRIT.containing_type = pb.SC_QUERY_PLAYER_INFO
pb.SC_QUERY_PLAYER_INFO_MOUNTEQUIP_EQUIPSLOT_FIELD.name = "equipSlot"
pb.SC_QUERY_PLAYER_INFO_MOUNTEQUIP_EQUIPSLOT_FIELD.full_name = ".SC_Query_Player_Info.MountEquip.equipSlot"
pb.SC_QUERY_PLAYER_INFO_MOUNTEQUIP_EQUIPSLOT_FIELD.number = 1
pb.SC_QUERY_PLAYER_INFO_MOUNTEQUIP_EQUIPSLOT_FIELD.index = 0
pb.SC_QUERY_PLAYER_INFO_MOUNTEQUIP_EQUIPSLOT_FIELD.label = 1
pb.SC_QUERY_PLAYER_INFO_MOUNTEQUIP_EQUIPSLOT_FIELD.has_default_value = false
pb.SC_QUERY_PLAYER_INFO_MOUNTEQUIP_EQUIPSLOT_FIELD.default_value = 0
pb.SC_QUERY_PLAYER_INFO_MOUNTEQUIP_EQUIPSLOT_FIELD.type = 13
pb.SC_QUERY_PLAYER_INFO_MOUNTEQUIP_EQUIPSLOT_FIELD.cpp_type = 3

pb.SC_QUERY_PLAYER_INFO_MOUNTEQUIP_EQUIPID_FIELD.name = "equipID"
pb.SC_QUERY_PLAYER_INFO_MOUNTEQUIP_EQUIPID_FIELD.full_name = ".SC_Query_Player_Info.MountEquip.equipID"
pb.SC_QUERY_PLAYER_INFO_MOUNTEQUIP_EQUIPID_FIELD.number = 2
pb.SC_QUERY_PLAYER_INFO_MOUNTEQUIP_EQUIPID_FIELD.index = 1
pb.SC_QUERY_PLAYER_INFO_MOUNTEQUIP_EQUIPID_FIELD.label = 1
pb.SC_QUERY_PLAYER_INFO_MOUNTEQUIP_EQUIPID_FIELD.has_default_value = false
pb.SC_QUERY_PLAYER_INFO_MOUNTEQUIP_EQUIPID_FIELD.default_value = 0
pb.SC_QUERY_PLAYER_INFO_MOUNTEQUIP_EQUIPID_FIELD.type = 13
pb.SC_QUERY_PLAYER_INFO_MOUNTEQUIP_EQUIPID_FIELD.cpp_type = 3

pb.SC_QUERY_PLAYER_INFO_MOUNTEQUIP.name = "MountEquip"
pb.SC_QUERY_PLAYER_INFO_MOUNTEQUIP.full_name = ".SC_Query_Player_Info.MountEquip"
pb.SC_QUERY_PLAYER_INFO_MOUNTEQUIP.nested_types = {}
pb.SC_QUERY_PLAYER_INFO_MOUNTEQUIP.enum_types = {}
pb.SC_QUERY_PLAYER_INFO_MOUNTEQUIP.fields = {pb.SC_QUERY_PLAYER_INFO_MOUNTEQUIP_EQUIPSLOT_FIELD, pb.SC_QUERY_PLAYER_INFO_MOUNTEQUIP_EQUIPID_FIELD}
pb.SC_QUERY_PLAYER_INFO_MOUNTEQUIP.is_extendable = false
pb.SC_QUERY_PLAYER_INFO_MOUNTEQUIP.extensions = {}
pb.SC_QUERY_PLAYER_INFO_MOUNTEQUIP.containing_type = pb.SC_QUERY_PLAYER_INFO
pb.SC_QUERY_PLAYER_INFO_ACTORID_FIELD.name = "ActorID"
pb.SC_QUERY_PLAYER_INFO_ACTORID_FIELD.full_name = ".SC_Query_Player_Info.ActorID"
pb.SC_QUERY_PLAYER_INFO_ACTORID_FIELD.number = 1
pb.SC_QUERY_PLAYER_INFO_ACTORID_FIELD.index = 0
pb.SC_QUERY_PLAYER_INFO_ACTORID_FIELD.label = 1
pb.SC_QUERY_PLAYER_INFO_ACTORID_FIELD.has_default_value = false
pb.SC_QUERY_PLAYER_INFO_ACTORID_FIELD.default_value = 0
pb.SC_QUERY_PLAYER_INFO_ACTORID_FIELD.type = 13
pb.SC_QUERY_PLAYER_INFO_ACTORID_FIELD.cpp_type = 3

pb.SC_QUERY_PLAYER_INFO_POWER_FIELD.name = "Power"
pb.SC_QUERY_PLAYER_INFO_POWER_FIELD.full_name = ".SC_Query_Player_Info.Power"
pb.SC_QUERY_PLAYER_INFO_POWER_FIELD.number = 2
pb.SC_QUERY_PLAYER_INFO_POWER_FIELD.index = 1
pb.SC_QUERY_PLAYER_INFO_POWER_FIELD.label = 1
pb.SC_QUERY_PLAYER_INFO_POWER_FIELD.has_default_value = false
pb.SC_QUERY_PLAYER_INFO_POWER_FIELD.default_value = 0
pb.SC_QUERY_PLAYER_INFO_POWER_FIELD.type = 13
pb.SC_QUERY_PLAYER_INFO_POWER_FIELD.cpp_type = 3

pb.SC_QUERY_PLAYER_INFO_VIP_FIELD.name = "VIP"
pb.SC_QUERY_PLAYER_INFO_VIP_FIELD.full_name = ".SC_Query_Player_Info.VIP"
pb.SC_QUERY_PLAYER_INFO_VIP_FIELD.number = 3
pb.SC_QUERY_PLAYER_INFO_VIP_FIELD.index = 2
pb.SC_QUERY_PLAYER_INFO_VIP_FIELD.label = 1
pb.SC_QUERY_PLAYER_INFO_VIP_FIELD.has_default_value = false
pb.SC_QUERY_PLAYER_INFO_VIP_FIELD.default_value = 0
pb.SC_QUERY_PLAYER_INFO_VIP_FIELD.type = 13
pb.SC_QUERY_PLAYER_INFO_VIP_FIELD.cpp_type = 3

pb.SC_QUERY_PLAYER_INFO_NAME_FIELD.name = "Name"
pb.SC_QUERY_PLAYER_INFO_NAME_FIELD.full_name = ".SC_Query_Player_Info.Name"
pb.SC_QUERY_PLAYER_INFO_NAME_FIELD.number = 4
pb.SC_QUERY_PLAYER_INFO_NAME_FIELD.index = 3
pb.SC_QUERY_PLAYER_INFO_NAME_FIELD.label = 1
pb.SC_QUERY_PLAYER_INFO_NAME_FIELD.has_default_value = false
pb.SC_QUERY_PLAYER_INFO_NAME_FIELD.default_value = ""
pb.SC_QUERY_PLAYER_INFO_NAME_FIELD.type = 9
pb.SC_QUERY_PLAYER_INFO_NAME_FIELD.cpp_type = 9

pb.SC_QUERY_PLAYER_INFO_VOCATION_FIELD.name = "Vocation"
pb.SC_QUERY_PLAYER_INFO_VOCATION_FIELD.full_name = ".SC_Query_Player_Info.Vocation"
pb.SC_QUERY_PLAYER_INFO_VOCATION_FIELD.number = 5
pb.SC_QUERY_PLAYER_INFO_VOCATION_FIELD.index = 4
pb.SC_QUERY_PLAYER_INFO_VOCATION_FIELD.label = 1
pb.SC_QUERY_PLAYER_INFO_VOCATION_FIELD.has_default_value = false
pb.SC_QUERY_PLAYER_INFO_VOCATION_FIELD.default_value = 0
pb.SC_QUERY_PLAYER_INFO_VOCATION_FIELD.type = 13
pb.SC_QUERY_PLAYER_INFO_VOCATION_FIELD.cpp_type = 3

pb.SC_QUERY_PLAYER_INFO_LEVEL_FIELD.name = "Level"
pb.SC_QUERY_PLAYER_INFO_LEVEL_FIELD.full_name = ".SC_Query_Player_Info.Level"
pb.SC_QUERY_PLAYER_INFO_LEVEL_FIELD.number = 6
pb.SC_QUERY_PLAYER_INFO_LEVEL_FIELD.index = 5
pb.SC_QUERY_PLAYER_INFO_LEVEL_FIELD.label = 1
pb.SC_QUERY_PLAYER_INFO_LEVEL_FIELD.has_default_value = false
pb.SC_QUERY_PLAYER_INFO_LEVEL_FIELD.default_value = 0
pb.SC_QUERY_PLAYER_INFO_LEVEL_FIELD.type = 13
pb.SC_QUERY_PLAYER_INFO_LEVEL_FIELD.cpp_type = 3

pb.SC_QUERY_PLAYER_INFO_SOCIETY_FIELD.name = "Society"
pb.SC_QUERY_PLAYER_INFO_SOCIETY_FIELD.full_name = ".SC_Query_Player_Info.Society"
pb.SC_QUERY_PLAYER_INFO_SOCIETY_FIELD.number = 7
pb.SC_QUERY_PLAYER_INFO_SOCIETY_FIELD.index = 6
pb.SC_QUERY_PLAYER_INFO_SOCIETY_FIELD.label = 1
pb.SC_QUERY_PLAYER_INFO_SOCIETY_FIELD.has_default_value = false
pb.SC_QUERY_PLAYER_INFO_SOCIETY_FIELD.default_value = 0
pb.SC_QUERY_PLAYER_INFO_SOCIETY_FIELD.type = 13
pb.SC_QUERY_PLAYER_INFO_SOCIETY_FIELD.cpp_type = 3

pb.SC_QUERY_PLAYER_INFO_PK_FIELD.name = "PK"
pb.SC_QUERY_PLAYER_INFO_PK_FIELD.full_name = ".SC_Query_Player_Info.PK"
pb.SC_QUERY_PLAYER_INFO_PK_FIELD.number = 8
pb.SC_QUERY_PLAYER_INFO_PK_FIELD.index = 7
pb.SC_QUERY_PLAYER_INFO_PK_FIELD.label = 1
pb.SC_QUERY_PLAYER_INFO_PK_FIELD.has_default_value = false
pb.SC_QUERY_PLAYER_INFO_PK_FIELD.default_value = 0
pb.SC_QUERY_PLAYER_INFO_PK_FIELD.type = 13
pb.SC_QUERY_PLAYER_INFO_PK_FIELD.cpp_type = 3

pb.SC_QUERY_PLAYER_INFO_PLAYERFIELDLIST_FIELD.name = "playerFieldList"
pb.SC_QUERY_PLAYER_INFO_PLAYERFIELDLIST_FIELD.full_name = ".SC_Query_Player_Info.playerFieldList"
pb.SC_QUERY_PLAYER_INFO_PLAYERFIELDLIST_FIELD.number = 9
pb.SC_QUERY_PLAYER_INFO_PLAYERFIELDLIST_FIELD.index = 8
pb.SC_QUERY_PLAYER_INFO_PLAYERFIELDLIST_FIELD.label = 3
pb.SC_QUERY_PLAYER_INFO_PLAYERFIELDLIST_FIELD.has_default_value = false
pb.SC_QUERY_PLAYER_INFO_PLAYERFIELDLIST_FIELD.default_value = {}
pb.SC_QUERY_PLAYER_INFO_PLAYERFIELDLIST_FIELD.message_type = pb.SC_QUERY_PLAYER_INFO_PLAYERFIELD
pb.SC_QUERY_PLAYER_INFO_PLAYERFIELDLIST_FIELD.type = 11
pb.SC_QUERY_PLAYER_INFO_PLAYERFIELDLIST_FIELD.cpp_type = 10

pb.SC_QUERY_PLAYER_INFO_EQUIPMENTLIST_FIELD.name = "equipmentList"
pb.SC_QUERY_PLAYER_INFO_EQUIPMENTLIST_FIELD.full_name = ".SC_Query_Player_Info.equipmentList"
pb.SC_QUERY_PLAYER_INFO_EQUIPMENTLIST_FIELD.number = 10
pb.SC_QUERY_PLAYER_INFO_EQUIPMENTLIST_FIELD.index = 9
pb.SC_QUERY_PLAYER_INFO_EQUIPMENTLIST_FIELD.label = 3
pb.SC_QUERY_PLAYER_INFO_EQUIPMENTLIST_FIELD.has_default_value = false
pb.SC_QUERY_PLAYER_INFO_EQUIPMENTLIST_FIELD.default_value = {}
pb.SC_QUERY_PLAYER_INFO_EQUIPMENTLIST_FIELD.message_type = pb.SC_QUERY_PLAYER_INFO_EQUIPMENT
pb.SC_QUERY_PLAYER_INFO_EQUIPMENTLIST_FIELD.type = 11
pb.SC_QUERY_PLAYER_INFO_EQUIPMENTLIST_FIELD.cpp_type = 10

pb.SC_QUERY_PLAYER_INFO_GODWEAPONID_FIELD.name = "godWeaponID"
pb.SC_QUERY_PLAYER_INFO_GODWEAPONID_FIELD.full_name = ".SC_Query_Player_Info.godWeaponID"
pb.SC_QUERY_PLAYER_INFO_GODWEAPONID_FIELD.number = 11
pb.SC_QUERY_PLAYER_INFO_GODWEAPONID_FIELD.index = 10
pb.SC_QUERY_PLAYER_INFO_GODWEAPONID_FIELD.label = 1
pb.SC_QUERY_PLAYER_INFO_GODWEAPONID_FIELD.has_default_value = false
pb.SC_QUERY_PLAYER_INFO_GODWEAPONID_FIELD.default_value = 0
pb.SC_QUERY_PLAYER_INFO_GODWEAPONID_FIELD.type = 13
pb.SC_QUERY_PLAYER_INFO_GODWEAPONID_FIELD.cpp_type = 3

pb.SC_QUERY_PLAYER_INFO_GODWEAPONSPIRITLIST_FIELD.name = "godWeaponSpiritList"
pb.SC_QUERY_PLAYER_INFO_GODWEAPONSPIRITLIST_FIELD.full_name = ".SC_Query_Player_Info.godWeaponSpiritList"
pb.SC_QUERY_PLAYER_INFO_GODWEAPONSPIRITLIST_FIELD.number = 12
pb.SC_QUERY_PLAYER_INFO_GODWEAPONSPIRITLIST_FIELD.index = 11
pb.SC_QUERY_PLAYER_INFO_GODWEAPONSPIRITLIST_FIELD.label = 3
pb.SC_QUERY_PLAYER_INFO_GODWEAPONSPIRITLIST_FIELD.has_default_value = false
pb.SC_QUERY_PLAYER_INFO_GODWEAPONSPIRITLIST_FIELD.default_value = {}
pb.SC_QUERY_PLAYER_INFO_GODWEAPONSPIRITLIST_FIELD.message_type = pb.SC_QUERY_PLAYER_INFO_GODWEAPONSPIRIT
pb.SC_QUERY_PLAYER_INFO_GODWEAPONSPIRITLIST_FIELD.type = 11
pb.SC_QUERY_PLAYER_INFO_GODWEAPONSPIRITLIST_FIELD.cpp_type = 10

pb.SC_QUERY_PLAYER_INFO_MOUNTID_FIELD.name = "MountID"
pb.SC_QUERY_PLAYER_INFO_MOUNTID_FIELD.full_name = ".SC_Query_Player_Info.MountID"
pb.SC_QUERY_PLAYER_INFO_MOUNTID_FIELD.number = 13
pb.SC_QUERY_PLAYER_INFO_MOUNTID_FIELD.index = 12
pb.SC_QUERY_PLAYER_INFO_MOUNTID_FIELD.label = 1
pb.SC_QUERY_PLAYER_INFO_MOUNTID_FIELD.has_default_value = false
pb.SC_QUERY_PLAYER_INFO_MOUNTID_FIELD.default_value = 0
pb.SC_QUERY_PLAYER_INFO_MOUNTID_FIELD.type = 13
pb.SC_QUERY_PLAYER_INFO_MOUNTID_FIELD.cpp_type = 3

pb.SC_QUERY_PLAYER_INFO_MOUNTEQUIPLIST_FIELD.name = "mountEquipList"
pb.SC_QUERY_PLAYER_INFO_MOUNTEQUIPLIST_FIELD.full_name = ".SC_Query_Player_Info.mountEquipList"
pb.SC_QUERY_PLAYER_INFO_MOUNTEQUIPLIST_FIELD.number = 14
pb.SC_QUERY_PLAYER_INFO_MOUNTEQUIPLIST_FIELD.index = 13
pb.SC_QUERY_PLAYER_INFO_MOUNTEQUIPLIST_FIELD.label = 3
pb.SC_QUERY_PLAYER_INFO_MOUNTEQUIPLIST_FIELD.has_default_value = false
pb.SC_QUERY_PLAYER_INFO_MOUNTEQUIPLIST_FIELD.default_value = {}
pb.SC_QUERY_PLAYER_INFO_MOUNTEQUIPLIST_FIELD.message_type = pb.SC_QUERY_PLAYER_INFO_MOUNTEQUIP
pb.SC_QUERY_PLAYER_INFO_MOUNTEQUIPLIST_FIELD.type = 11
pb.SC_QUERY_PLAYER_INFO_MOUNTEQUIPLIST_FIELD.cpp_type = 10

pb.SC_QUERY_PLAYER_INFO_WINGID_FIELD.name = "WingID"
pb.SC_QUERY_PLAYER_INFO_WINGID_FIELD.full_name = ".SC_Query_Player_Info.WingID"
pb.SC_QUERY_PLAYER_INFO_WINGID_FIELD.number = 15
pb.SC_QUERY_PLAYER_INFO_WINGID_FIELD.index = 14
pb.SC_QUERY_PLAYER_INFO_WINGID_FIELD.label = 1
pb.SC_QUERY_PLAYER_INFO_WINGID_FIELD.has_default_value = false
pb.SC_QUERY_PLAYER_INFO_WINGID_FIELD.default_value = 0
pb.SC_QUERY_PLAYER_INFO_WINGID_FIELD.type = 13
pb.SC_QUERY_PLAYER_INFO_WINGID_FIELD.cpp_type = 3

pb.SC_QUERY_PLAYER_INFO.name = "SC_Query_Player_Info"
pb.SC_QUERY_PLAYER_INFO.full_name = ".SC_Query_Player_Info"
pb.SC_QUERY_PLAYER_INFO.nested_types = {pb.SC_QUERY_PLAYER_INFO_PLAYERFIELD, pb.SC_QUERY_PLAYER_INFO_EQUIPMENT, pb.SC_QUERY_PLAYER_INFO_GODWEAPONSPIRIT, pb.SC_QUERY_PLAYER_INFO_MOUNTEQUIP}
pb.SC_QUERY_PLAYER_INFO.enum_types = {}
pb.SC_QUERY_PLAYER_INFO.fields = {pb.SC_QUERY_PLAYER_INFO_ACTORID_FIELD, pb.SC_QUERY_PLAYER_INFO_POWER_FIELD, pb.SC_QUERY_PLAYER_INFO_VIP_FIELD, pb.SC_QUERY_PLAYER_INFO_NAME_FIELD, pb.SC_QUERY_PLAYER_INFO_VOCATION_FIELD, pb.SC_QUERY_PLAYER_INFO_LEVEL_FIELD, pb.SC_QUERY_PLAYER_INFO_SOCIETY_FIELD, pb.SC_QUERY_PLAYER_INFO_PK_FIELD, pb.SC_QUERY_PLAYER_INFO_PLAYERFIELDLIST_FIELD, pb.SC_QUERY_PLAYER_INFO_EQUIPMENTLIST_FIELD, pb.SC_QUERY_PLAYER_INFO_GODWEAPONID_FIELD, pb.SC_QUERY_PLAYER_INFO_GODWEAPONSPIRITLIST_FIELD, pb.SC_QUERY_PLAYER_INFO_MOUNTID_FIELD, pb.SC_QUERY_PLAYER_INFO_MOUNTEQUIPLIST_FIELD, pb.SC_QUERY_PLAYER_INFO_WINGID_FIELD}
pb.SC_QUERY_PLAYER_INFO.is_extendable = false
pb.SC_QUERY_PLAYER_INFO.extensions = {}
pb.SC_FRIENDBLESS_UPGRADENOTICE_ACTORID_FIELD.name = "ActorID"
pb.SC_FRIENDBLESS_UPGRADENOTICE_ACTORID_FIELD.full_name = ".SC_FriendBless_UpgradeNotice.ActorID"
pb.SC_FRIENDBLESS_UPGRADENOTICE_ACTORID_FIELD.number = 1
pb.SC_FRIENDBLESS_UPGRADENOTICE_ACTORID_FIELD.index = 0
pb.SC_FRIENDBLESS_UPGRADENOTICE_ACTORID_FIELD.label = 1
pb.SC_FRIENDBLESS_UPGRADENOTICE_ACTORID_FIELD.has_default_value = false
pb.SC_FRIENDBLESS_UPGRADENOTICE_ACTORID_FIELD.default_value = 0
pb.SC_FRIENDBLESS_UPGRADENOTICE_ACTORID_FIELD.type = 5
pb.SC_FRIENDBLESS_UPGRADENOTICE_ACTORID_FIELD.cpp_type = 1

pb.SC_FRIENDBLESS_UPGRADENOTICE_ACTORCOUNTRY_FIELD.name = "ActorCountry"
pb.SC_FRIENDBLESS_UPGRADENOTICE_ACTORCOUNTRY_FIELD.full_name = ".SC_FriendBless_UpgradeNotice.ActorCountry"
pb.SC_FRIENDBLESS_UPGRADENOTICE_ACTORCOUNTRY_FIELD.number = 2
pb.SC_FRIENDBLESS_UPGRADENOTICE_ACTORCOUNTRY_FIELD.index = 1
pb.SC_FRIENDBLESS_UPGRADENOTICE_ACTORCOUNTRY_FIELD.label = 1
pb.SC_FRIENDBLESS_UPGRADENOTICE_ACTORCOUNTRY_FIELD.has_default_value = false
pb.SC_FRIENDBLESS_UPGRADENOTICE_ACTORCOUNTRY_FIELD.default_value = 0
pb.SC_FRIENDBLESS_UPGRADENOTICE_ACTORCOUNTRY_FIELD.type = 5
pb.SC_FRIENDBLESS_UPGRADENOTICE_ACTORCOUNTRY_FIELD.cpp_type = 1

pb.SC_FRIENDBLESS_UPGRADENOTICE_ACTORVOCATION_FIELD.name = "ActorVocation"
pb.SC_FRIENDBLESS_UPGRADENOTICE_ACTORVOCATION_FIELD.full_name = ".SC_FriendBless_UpgradeNotice.ActorVocation"
pb.SC_FRIENDBLESS_UPGRADENOTICE_ACTORVOCATION_FIELD.number = 3
pb.SC_FRIENDBLESS_UPGRADENOTICE_ACTORVOCATION_FIELD.index = 2
pb.SC_FRIENDBLESS_UPGRADENOTICE_ACTORVOCATION_FIELD.label = 1
pb.SC_FRIENDBLESS_UPGRADENOTICE_ACTORVOCATION_FIELD.has_default_value = false
pb.SC_FRIENDBLESS_UPGRADENOTICE_ACTORVOCATION_FIELD.default_value = 0
pb.SC_FRIENDBLESS_UPGRADENOTICE_ACTORVOCATION_FIELD.type = 5
pb.SC_FRIENDBLESS_UPGRADENOTICE_ACTORVOCATION_FIELD.cpp_type = 1

pb.SC_FRIENDBLESS_UPGRADENOTICE_ACTORLEVEL_FIELD.name = "ActorLevel"
pb.SC_FRIENDBLESS_UPGRADENOTICE_ACTORLEVEL_FIELD.full_name = ".SC_FriendBless_UpgradeNotice.ActorLevel"
pb.SC_FRIENDBLESS_UPGRADENOTICE_ACTORLEVEL_FIELD.number = 4
pb.SC_FRIENDBLESS_UPGRADENOTICE_ACTORLEVEL_FIELD.index = 3
pb.SC_FRIENDBLESS_UPGRADENOTICE_ACTORLEVEL_FIELD.label = 1
pb.SC_FRIENDBLESS_UPGRADENOTICE_ACTORLEVEL_FIELD.has_default_value = false
pb.SC_FRIENDBLESS_UPGRADENOTICE_ACTORLEVEL_FIELD.default_value = 0
pb.SC_FRIENDBLESS_UPGRADENOTICE_ACTORLEVEL_FIELD.type = 5
pb.SC_FRIENDBLESS_UPGRADENOTICE_ACTORLEVEL_FIELD.cpp_type = 1

pb.SC_FRIENDBLESS_UPGRADENOTICE_ACTORNAME_FIELD.name = "ActorName"
pb.SC_FRIENDBLESS_UPGRADENOTICE_ACTORNAME_FIELD.full_name = ".SC_FriendBless_UpgradeNotice.ActorName"
pb.SC_FRIENDBLESS_UPGRADENOTICE_ACTORNAME_FIELD.number = 5
pb.SC_FRIENDBLESS_UPGRADENOTICE_ACTORNAME_FIELD.index = 4
pb.SC_FRIENDBLESS_UPGRADENOTICE_ACTORNAME_FIELD.label = 1
pb.SC_FRIENDBLESS_UPGRADENOTICE_ACTORNAME_FIELD.has_default_value = false
pb.SC_FRIENDBLESS_UPGRADENOTICE_ACTORNAME_FIELD.default_value = ""
pb.SC_FRIENDBLESS_UPGRADENOTICE_ACTORNAME_FIELD.type = 9
pb.SC_FRIENDBLESS_UPGRADENOTICE_ACTORNAME_FIELD.cpp_type = 9

pb.SC_FRIENDBLESS_UPGRADENOTICE.name = "SC_FriendBless_UpgradeNotice"
pb.SC_FRIENDBLESS_UPGRADENOTICE.full_name = ".SC_FriendBless_UpgradeNotice"
pb.SC_FRIENDBLESS_UPGRADENOTICE.nested_types = {}
pb.SC_FRIENDBLESS_UPGRADENOTICE.enum_types = {}
pb.SC_FRIENDBLESS_UPGRADENOTICE.fields = {pb.SC_FRIENDBLESS_UPGRADENOTICE_ACTORID_FIELD, pb.SC_FRIENDBLESS_UPGRADENOTICE_ACTORCOUNTRY_FIELD, pb.SC_FRIENDBLESS_UPGRADENOTICE_ACTORVOCATION_FIELD, pb.SC_FRIENDBLESS_UPGRADENOTICE_ACTORLEVEL_FIELD, pb.SC_FRIENDBLESS_UPGRADENOTICE_ACTORNAME_FIELD}
pb.SC_FRIENDBLESS_UPGRADENOTICE.is_extendable = false
pb.SC_FRIENDBLESS_UPGRADENOTICE.extensions = {}
pb.SC_FRIENDBLESS_SENDBLESS_TARGETTYPE_FIELD.name = "TargetType"
pb.SC_FRIENDBLESS_SENDBLESS_TARGETTYPE_FIELD.full_name = ".SC_FriendBless_SendBless.TargetType"
pb.SC_FRIENDBLESS_SENDBLESS_TARGETTYPE_FIELD.number = 1
pb.SC_FRIENDBLESS_SENDBLESS_TARGETTYPE_FIELD.index = 0
pb.SC_FRIENDBLESS_SENDBLESS_TARGETTYPE_FIELD.label = 1
pb.SC_FRIENDBLESS_SENDBLESS_TARGETTYPE_FIELD.has_default_value = false
pb.SC_FRIENDBLESS_SENDBLESS_TARGETTYPE_FIELD.default_value = 0
pb.SC_FRIENDBLESS_SENDBLESS_TARGETTYPE_FIELD.type = 5
pb.SC_FRIENDBLESS_SENDBLESS_TARGETTYPE_FIELD.cpp_type = 1

pb.SC_FRIENDBLESS_SENDBLESS_ACTORID_FIELD.name = "ActorID"
pb.SC_FRIENDBLESS_SENDBLESS_ACTORID_FIELD.full_name = ".SC_FriendBless_SendBless.ActorID"
pb.SC_FRIENDBLESS_SENDBLESS_ACTORID_FIELD.number = 2
pb.SC_FRIENDBLESS_SENDBLESS_ACTORID_FIELD.index = 1
pb.SC_FRIENDBLESS_SENDBLESS_ACTORID_FIELD.label = 1
pb.SC_FRIENDBLESS_SENDBLESS_ACTORID_FIELD.has_default_value = false
pb.SC_FRIENDBLESS_SENDBLESS_ACTORID_FIELD.default_value = 0
pb.SC_FRIENDBLESS_SENDBLESS_ACTORID_FIELD.type = 5
pb.SC_FRIENDBLESS_SENDBLESS_ACTORID_FIELD.cpp_type = 1

pb.SC_FRIENDBLESS_SENDBLESS_ACTORCOUNTRY_FIELD.name = "ActorCountry"
pb.SC_FRIENDBLESS_SENDBLESS_ACTORCOUNTRY_FIELD.full_name = ".SC_FriendBless_SendBless.ActorCountry"
pb.SC_FRIENDBLESS_SENDBLESS_ACTORCOUNTRY_FIELD.number = 3
pb.SC_FRIENDBLESS_SENDBLESS_ACTORCOUNTRY_FIELD.index = 2
pb.SC_FRIENDBLESS_SENDBLESS_ACTORCOUNTRY_FIELD.label = 1
pb.SC_FRIENDBLESS_SENDBLESS_ACTORCOUNTRY_FIELD.has_default_value = false
pb.SC_FRIENDBLESS_SENDBLESS_ACTORCOUNTRY_FIELD.default_value = 0
pb.SC_FRIENDBLESS_SENDBLESS_ACTORCOUNTRY_FIELD.type = 5
pb.SC_FRIENDBLESS_SENDBLESS_ACTORCOUNTRY_FIELD.cpp_type = 1

pb.SC_FRIENDBLESS_SENDBLESS_ACTORVOCATION_FIELD.name = "ActorVocation"
pb.SC_FRIENDBLESS_SENDBLESS_ACTORVOCATION_FIELD.full_name = ".SC_FriendBless_SendBless.ActorVocation"
pb.SC_FRIENDBLESS_SENDBLESS_ACTORVOCATION_FIELD.number = 4
pb.SC_FRIENDBLESS_SENDBLESS_ACTORVOCATION_FIELD.index = 3
pb.SC_FRIENDBLESS_SENDBLESS_ACTORVOCATION_FIELD.label = 1
pb.SC_FRIENDBLESS_SENDBLESS_ACTORVOCATION_FIELD.has_default_value = false
pb.SC_FRIENDBLESS_SENDBLESS_ACTORVOCATION_FIELD.default_value = 0
pb.SC_FRIENDBLESS_SENDBLESS_ACTORVOCATION_FIELD.type = 5
pb.SC_FRIENDBLESS_SENDBLESS_ACTORVOCATION_FIELD.cpp_type = 1

pb.SC_FRIENDBLESS_SENDBLESS_ACTORNAME_FIELD.name = "ActorName"
pb.SC_FRIENDBLESS_SENDBLESS_ACTORNAME_FIELD.full_name = ".SC_FriendBless_SendBless.ActorName"
pb.SC_FRIENDBLESS_SENDBLESS_ACTORNAME_FIELD.number = 5
pb.SC_FRIENDBLESS_SENDBLESS_ACTORNAME_FIELD.index = 4
pb.SC_FRIENDBLESS_SENDBLESS_ACTORNAME_FIELD.label = 1
pb.SC_FRIENDBLESS_SENDBLESS_ACTORNAME_FIELD.has_default_value = false
pb.SC_FRIENDBLESS_SENDBLESS_ACTORNAME_FIELD.default_value = ""
pb.SC_FRIENDBLESS_SENDBLESS_ACTORNAME_FIELD.type = 9
pb.SC_FRIENDBLESS_SENDBLESS_ACTORNAME_FIELD.cpp_type = 9

pb.SC_FRIENDBLESS_SENDBLESS_BLESSTYPE_FIELD.name = "BlessType"
pb.SC_FRIENDBLESS_SENDBLESS_BLESSTYPE_FIELD.full_name = ".SC_FriendBless_SendBless.BlessType"
pb.SC_FRIENDBLESS_SENDBLESS_BLESSTYPE_FIELD.number = 6
pb.SC_FRIENDBLESS_SENDBLESS_BLESSTYPE_FIELD.index = 5
pb.SC_FRIENDBLESS_SENDBLESS_BLESSTYPE_FIELD.label = 1
pb.SC_FRIENDBLESS_SENDBLESS_BLESSTYPE_FIELD.has_default_value = false
pb.SC_FRIENDBLESS_SENDBLESS_BLESSTYPE_FIELD.default_value = 0
pb.SC_FRIENDBLESS_SENDBLESS_BLESSTYPE_FIELD.type = 5
pb.SC_FRIENDBLESS_SENDBLESS_BLESSTYPE_FIELD.cpp_type = 1

pb.SC_FRIENDBLESS_SENDBLESS_PRIZEVALUE_FIELD.name = "PrizeValue"
pb.SC_FRIENDBLESS_SENDBLESS_PRIZEVALUE_FIELD.full_name = ".SC_FriendBless_SendBless.PrizeValue"
pb.SC_FRIENDBLESS_SENDBLESS_PRIZEVALUE_FIELD.number = 7
pb.SC_FRIENDBLESS_SENDBLESS_PRIZEVALUE_FIELD.index = 6
pb.SC_FRIENDBLESS_SENDBLESS_PRIZEVALUE_FIELD.label = 1
pb.SC_FRIENDBLESS_SENDBLESS_PRIZEVALUE_FIELD.has_default_value = false
pb.SC_FRIENDBLESS_SENDBLESS_PRIZEVALUE_FIELD.default_value = 0
pb.SC_FRIENDBLESS_SENDBLESS_PRIZEVALUE_FIELD.type = 5
pb.SC_FRIENDBLESS_SENDBLESS_PRIZEVALUE_FIELD.cpp_type = 1

pb.SC_FRIENDBLESS_SENDBLESS_RANDTIP_FIELD.name = "RandTip"
pb.SC_FRIENDBLESS_SENDBLESS_RANDTIP_FIELD.full_name = ".SC_FriendBless_SendBless.RandTip"
pb.SC_FRIENDBLESS_SENDBLESS_RANDTIP_FIELD.number = 8
pb.SC_FRIENDBLESS_SENDBLESS_RANDTIP_FIELD.index = 7
pb.SC_FRIENDBLESS_SENDBLESS_RANDTIP_FIELD.label = 1
pb.SC_FRIENDBLESS_SENDBLESS_RANDTIP_FIELD.has_default_value = false
pb.SC_FRIENDBLESS_SENDBLESS_RANDTIP_FIELD.default_value = 0
pb.SC_FRIENDBLESS_SENDBLESS_RANDTIP_FIELD.type = 5
pb.SC_FRIENDBLESS_SENDBLESS_RANDTIP_FIELD.cpp_type = 1

pb.SC_FRIENDBLESS_SENDBLESS.name = "SC_FriendBless_SendBless"
pb.SC_FRIENDBLESS_SENDBLESS.full_name = ".SC_FriendBless_SendBless"
pb.SC_FRIENDBLESS_SENDBLESS.nested_types = {}
pb.SC_FRIENDBLESS_SENDBLESS.enum_types = {}
pb.SC_FRIENDBLESS_SENDBLESS.fields = {pb.SC_FRIENDBLESS_SENDBLESS_TARGETTYPE_FIELD, pb.SC_FRIENDBLESS_SENDBLESS_ACTORID_FIELD, pb.SC_FRIENDBLESS_SENDBLESS_ACTORCOUNTRY_FIELD, pb.SC_FRIENDBLESS_SENDBLESS_ACTORVOCATION_FIELD, pb.SC_FRIENDBLESS_SENDBLESS_ACTORNAME_FIELD, pb.SC_FRIENDBLESS_SENDBLESS_BLESSTYPE_FIELD, pb.SC_FRIENDBLESS_SENDBLESS_PRIZEVALUE_FIELD, pb.SC_FRIENDBLESS_SENDBLESS_RANDTIP_FIELD}
pb.SC_FRIENDBLESS_SENDBLESS.is_extendable = false
pb.SC_FRIENDBLESS_SENDBLESS.extensions = {}
pb.SC_FRIEND_VALUE_PRIZE_FRIENDNAME_FIELD.name = "FriendName"
pb.SC_FRIEND_VALUE_PRIZE_FRIENDNAME_FIELD.full_name = ".SC_Friend_Value_Prize.FriendName"
pb.SC_FRIEND_VALUE_PRIZE_FRIENDNAME_FIELD.number = 1
pb.SC_FRIEND_VALUE_PRIZE_FRIENDNAME_FIELD.index = 0
pb.SC_FRIEND_VALUE_PRIZE_FRIENDNAME_FIELD.label = 1
pb.SC_FRIEND_VALUE_PRIZE_FRIENDNAME_FIELD.has_default_value = false
pb.SC_FRIEND_VALUE_PRIZE_FRIENDNAME_FIELD.default_value = ""
pb.SC_FRIEND_VALUE_PRIZE_FRIENDNAME_FIELD.type = 9
pb.SC_FRIEND_VALUE_PRIZE_FRIENDNAME_FIELD.cpp_type = 9

pb.SC_FRIEND_VALUE_PRIZE_FRIENDCOUNTRY_FIELD.name = "FriendCountry"
pb.SC_FRIEND_VALUE_PRIZE_FRIENDCOUNTRY_FIELD.full_name = ".SC_Friend_Value_Prize.FriendCountry"
pb.SC_FRIEND_VALUE_PRIZE_FRIENDCOUNTRY_FIELD.number = 2
pb.SC_FRIEND_VALUE_PRIZE_FRIENDCOUNTRY_FIELD.index = 1
pb.SC_FRIEND_VALUE_PRIZE_FRIENDCOUNTRY_FIELD.label = 1
pb.SC_FRIEND_VALUE_PRIZE_FRIENDCOUNTRY_FIELD.has_default_value = false
pb.SC_FRIEND_VALUE_PRIZE_FRIENDCOUNTRY_FIELD.default_value = 0
pb.SC_FRIEND_VALUE_PRIZE_FRIENDCOUNTRY_FIELD.type = 13
pb.SC_FRIEND_VALUE_PRIZE_FRIENDCOUNTRY_FIELD.cpp_type = 3

pb.SC_FRIEND_VALUE_PRIZE_FRIENDVALUE_FIELD.name = "FriendValue"
pb.SC_FRIEND_VALUE_PRIZE_FRIENDVALUE_FIELD.full_name = ".SC_Friend_Value_Prize.FriendValue"
pb.SC_FRIEND_VALUE_PRIZE_FRIENDVALUE_FIELD.number = 3
pb.SC_FRIEND_VALUE_PRIZE_FRIENDVALUE_FIELD.index = 2
pb.SC_FRIEND_VALUE_PRIZE_FRIENDVALUE_FIELD.label = 1
pb.SC_FRIEND_VALUE_PRIZE_FRIENDVALUE_FIELD.has_default_value = false
pb.SC_FRIEND_VALUE_PRIZE_FRIENDVALUE_FIELD.default_value = 0
pb.SC_FRIEND_VALUE_PRIZE_FRIENDVALUE_FIELD.type = 13
pb.SC_FRIEND_VALUE_PRIZE_FRIENDVALUE_FIELD.cpp_type = 3

pb.SC_FRIEND_VALUE_PRIZE_PRIZEID_FIELD.name = "PrizeID"
pb.SC_FRIEND_VALUE_PRIZE_PRIZEID_FIELD.full_name = ".SC_Friend_Value_Prize.PrizeID"
pb.SC_FRIEND_VALUE_PRIZE_PRIZEID_FIELD.number = 4
pb.SC_FRIEND_VALUE_PRIZE_PRIZEID_FIELD.index = 3
pb.SC_FRIEND_VALUE_PRIZE_PRIZEID_FIELD.label = 1
pb.SC_FRIEND_VALUE_PRIZE_PRIZEID_FIELD.has_default_value = false
pb.SC_FRIEND_VALUE_PRIZE_PRIZEID_FIELD.default_value = 0
pb.SC_FRIEND_VALUE_PRIZE_PRIZEID_FIELD.type = 13
pb.SC_FRIEND_VALUE_PRIZE_PRIZEID_FIELD.cpp_type = 3

pb.SC_FRIEND_VALUE_PRIZE.name = "SC_Friend_Value_Prize"
pb.SC_FRIEND_VALUE_PRIZE.full_name = ".SC_Friend_Value_Prize"
pb.SC_FRIEND_VALUE_PRIZE.nested_types = {}
pb.SC_FRIEND_VALUE_PRIZE.enum_types = {}
pb.SC_FRIEND_VALUE_PRIZE.fields = {pb.SC_FRIEND_VALUE_PRIZE_FRIENDNAME_FIELD, pb.SC_FRIEND_VALUE_PRIZE_FRIENDCOUNTRY_FIELD, pb.SC_FRIEND_VALUE_PRIZE_FRIENDVALUE_FIELD, pb.SC_FRIEND_VALUE_PRIZE_PRIZEID_FIELD}
pb.SC_FRIEND_VALUE_PRIZE.is_extendable = false
pb.SC_FRIEND_VALUE_PRIZE.extensions = {}

CS_Black_List_Add = protobuf.Message(pb.CS_BLACK_LIST_ADD)
CS_Delete_ChatOffline_List = protobuf.Message(pb.CS_DELETE_CHATOFFLINE_LIST)
CS_Friend_Add = protobuf.Message(pb.CS_FRIEND_ADD)
CS_Friend_Add_Opt = protobuf.Message(pb.CS_FRIEND_ADD_OPT)
CS_Friend_Delete = protobuf.Message(pb.CS_FRIEND_DELETE)
CS_Friend_GetFriendList = protobuf.Message(pb.CS_FRIEND_GETFRIENDLIST)
CS_Friend_Query_BeforeChat = protobuf.Message(pb.CS_FRIEND_QUERY_BEFORECHAT)
CS_Friend_Query_By_Name = protobuf.Message(pb.CS_FRIEND_QUERY_BY_NAME)
CS_Friend_Recommend = protobuf.Message(pb.CS_FRIEND_RECOMMEND)
CS_Friend_SendChat = protobuf.Message(pb.CS_FRIEND_SENDCHAT)
CS_Other_Player = protobuf.Message(pb.CS_OTHER_PLAYER)
CS_Query_Player_Info = protobuf.Message(pb.CS_QUERY_PLAYER_INFO)
FriendData = protobuf.Message(pb.FRIENDDATA)
MSG_BLACK_LIST_ADD = 15
MSG_CHATOFFLINE_LIST = 8
MSG_DELETE_CHATOFFLINE_LIST = 9
MSG_FRIENDBLESS_SENDBLESS = 19
MSG_FRIENDBLESS_UPGRADENOTICE = 18
MSG_FRIEND_ADD = 2
MSG_FRIEND_ADD_OPT = 12
MSG_FRIEND_ADD_REPONSE = 10
MSG_FRIEND_ADD_RESULT = 11
MSG_FRIEND_DELETE = 3
MSG_FRIEND_GETLIST = 1
MSG_FRIEND_NONE = 0
MSG_FRIEND_QUERY_BEFORECHAT = 16
MSG_FRIEND_QUERY_BY_NAME = 14
MSG_FRIEND_RECOMMEND = 6
MSG_FRIEND_SENDCHAT = 4
MSG_FRIEND_SENDCHATRETURN = 5
MSG_FRIEND_VALUE_PRIZE = 20
MSG_FRIEND_VALUE_SYNC = 13
MSG_OTHERPLAYER_INFO = 7
MSG_QUERY_INFORMATION = 17
OtherPlayerData = protobuf.Message(pb.OTHERPLAYERDATA)
SC_ChatOffline_List = protobuf.Message(pb.SC_CHATOFFLINE_LIST)
SC_ChatOffline_List.ChatofflineData = protobuf.Message(pb.SC_CHATOFFLINE_LIST_CHATOFFLINEDATA)
SC_FriendBless_SendBless = protobuf.Message(pb.SC_FRIENDBLESS_SENDBLESS)
SC_FriendBless_UpgradeNotice = protobuf.Message(pb.SC_FRIENDBLESS_UPGRADENOTICE)
SC_Friend_Add_Response = protobuf.Message(pb.SC_FRIEND_ADD_RESPONSE)
SC_Friend_Add_Result = protobuf.Message(pb.SC_FRIEND_ADD_RESULT)
SC_Friend_Delete = protobuf.Message(pb.SC_FRIEND_DELETE)
SC_Friend_GetFriendList = protobuf.Message(pb.SC_FRIEND_GETFRIENDLIST)
SC_Friend_Query_BeforeChat = protobuf.Message(pb.SC_FRIEND_QUERY_BEFORECHAT)
SC_Friend_Query_Result = protobuf.Message(pb.SC_FRIEND_QUERY_RESULT)
SC_Friend_Recommend = protobuf.Message(pb.SC_FRIEND_RECOMMEND)
SC_Friend_SendChat = protobuf.Message(pb.SC_FRIEND_SENDCHAT)
SC_Friend_SendChatReturn = protobuf.Message(pb.SC_FRIEND_SENDCHATRETURN)
SC_Friend_Value_Prize = protobuf.Message(pb.SC_FRIEND_VALUE_PRIZE)
SC_Friend_Value_Sync = protobuf.Message(pb.SC_FRIEND_VALUE_SYNC)
SC_Query_Player_Info = protobuf.Message(pb.SC_QUERY_PLAYER_INFO)
SC_Query_Player_Info.Equipment = protobuf.Message(pb.SC_QUERY_PLAYER_INFO_EQUIPMENT)
SC_Query_Player_Info.GodWeaponSpirit = protobuf.Message(pb.SC_QUERY_PLAYER_INFO_GODWEAPONSPIRIT)
SC_Query_Player_Info.MountEquip = protobuf.Message(pb.SC_QUERY_PLAYER_INFO_MOUNTEQUIP)
SC_Query_Player_Info.PlayerField = protobuf.Message(pb.SC_QUERY_PLAYER_INFO_PLAYERFIELD)

