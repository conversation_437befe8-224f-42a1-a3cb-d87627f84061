--[[
********************************************************************
    created:	2024/11/22
    author :	高
    purpose:    进化
*********************************************************************
--]]

local luaID = "UIXinFa"
--显示天赋数量
local ShowTalentNum = 99
---角色进化
---@class UIXinFa:UIWndBase
local m = {}

-- 订阅事件列表
function m.GetOpenEventList()
    return {
        [EventID.StoreList] = m.UpdateView,
        [EventID.LogicDataChange] = m.UpdateView,
        [EventID.OnGoodsPropChange] = m.UpdateView,
        [EventID.ActorDataChanged] = m.UpdateView,
    }
end

--------------------------------------------------------------------
-- 初始化
--------------------------------------------------------------------
function m.OnCreate()
    m.lastTime = 0
    local list = Schemes.EquipSmeltStarData:GetByOprateType(TreasuredTricks_OprateType) or {}
    m.SmeltID1 = 20001--list[1].EquipSmeltStarId 
    m.StarLvl1 = GamePlayerData.ActorEquipNew:GetEquipUpgradeLevel(m.SmeltID1)
    m.equipSmeltList1 = Schemes.EquipSmeltStar:GetBySmeltID(m.SmeltID1) or {}
    -- Group equipSmeltList into groups of three
    m.groupedList1 = {}
    for i = 2, #m.equipSmeltList1, 2 do
        local group = {}
        for j = 0, 1 do
            if m.equipSmeltList1[i + j] then
                table.insert(group, m.equipSmeltList1[i + j])
            end
        end
        if #group > 0 then
            table.insert(m.groupedList1, group)
        end
    end

    -- Create items for each group
    m.ItemTreasured1 = {}
    print("#m.groupedList ============== "..#m.groupedList1)
    for i, group in ipairs(m.groupedList1) do
        m.ItemTreasured1[i] = m.CreateGroup1(group)
        -- Update the group data
        m.ItemTreasured1[i].UpdateItems(i == #m.groupedList1)
    end

    m.SmeltID2 = 20002--list[1].EquipSmeltStarId 
    m.StarLvl2 = GamePlayerData.ActorEquipNew:GetEquipUpgradeLevel(m.SmeltID2)
    m.equipSmeltList2 = Schemes.EquipSmeltStar:GetBySmeltID(m.SmeltID2) or {}
    -- Group equipSmeltList into groups of three
    m.groupedList2 = {}
    for i = 2, #m.equipSmeltList2, 2 do
        local group = {}
        for j = 0, 1 do
            if m.equipSmeltList2[i + j] then
                table.insert(group, m.equipSmeltList2[i + j])
            end
        end
        if #group > 0 then
            table.insert(m.groupedList2, group)
        end
    end

    -- Create items for each group
    m.ItemTreasured2 = {}
    print("#m.groupedList2 ============== "..#m.groupedList2)
    for i, group in ipairs(m.groupedList2) do
        m.ItemTreasured2[i] = m.CreateGroup2(group)
        -- Update the group data
        m.ItemTreasured2[i].UpdateItems(i == #m.groupedList2)
    end

    m.SmeltID3 = 20003--list[1].EquipSmeltStarId 
    m.StarLvl3 = GamePlayerData.ActorEquipNew:GetEquipUpgradeLevel(m.SmeltID3)
    m.equipSmeltList3 = Schemes.EquipSmeltStar:GetBySmeltID(m.SmeltID3) or {}
    -- Group equipSmeltList into groups of three
    m.groupedList3 = {}
    for i = 2, #m.equipSmeltList3, 2 do
        local group = {}
        for j = 0, 1 do
            if m.equipSmeltList3[i + j] then
                table.insert(group, m.equipSmeltList3[i + j])
            end
        end
        if #group > 0 then
            table.insert(m.groupedList3, group)
        end
    end

    -- Create items for each group
    m.ItemTreasured3 = {}
    print("#m.groupedList3 ============== "..#m.groupedList3)
    for i, group in ipairs(m.groupedList3) do
        m.ItemTreasured3[i] = m.CreateGroup3(group)
        -- Update the group data
        m.ItemTreasured3[i].UpdateItems(i == #m.groupedList3)
    end
    m.objList.Btn_mask.gameObject:SetActive(false)
    m.RegisterClickEvent()
    return true
end


--------------------------------------------------------------------
--获取装备类型图
--------------------------------------------------------------------
function m.CreateGroup1(group)
    ---@class Item_Obj_Type
    local item = {}
    item.group = group   
    item.com = m:CreateSubItem(m.objList.Content1, m.objList.Item_TF1)
    -- item.com.Img_V.gameObject:SetActive(#group >= 3)
    m:AddClick(item.com.Btn_Click1, function()        
        if #group < 1 then return end
        local equipSme = group[1]
        if equipSme.StarLvl ~= m.StarLvl1+1 then
            item.com.Img_XX.gameObject:SetActive(true)
            m.UpdateTips(item,equipSme)
            return
        end

        local bool = HelperL.IsLackGoods(equipSme.CostGoodsID1, equipSme.CostGoodsID1Num, false)
        if bool == true then  
            item.com.Img_XX.gameObject:SetActive(true)
            m.UpdateTips(item,equipSme)
            return
        end
        m.Upgrade(m.SmeltID1)
    end)

    m:AddClick(item.com.Btn_Click2, function()        
        if #group < 2 then return end
        local equipSme = group[2]
        if equipSme.StarLvl ~= m.StarLvl1+1 then
            item.com.Img_XX.gameObject:SetActive(true)
            m.UpdateTips(item,equipSme)
            return
        end

        local bool = HelperL.IsLackGoods(equipSme.CostGoodsID1, equipSme.CostGoodsID1Num, false)
        if bool == true then  
            item.com.Img_XX.gameObject:SetActive(true)
            m.UpdateTips(item,equipSme)
            return
        end
        m.Upgrade(m.SmeltID1)
    end)

    m:AddClick(item.com.Btn_GB, function()        
        item.com.Img_XX.gameObject:SetActive(false)
    end)

    item.UpdateItems = function(isLast)   
        item.com.Img_Line1_1.gameObject:SetActive(false)
        item.com.Img_Line1_2.gameObject:SetActive(true)   
        if #group >= 1 then
            local equipSme = group[1]
            item.com.Img_Red1_1.gameObject:SetActive(false)
            item.com.Img_1_1.gameObject:SetActive(false)
            item.com.Img_1_2.gameObject:SetActive(false)
            item.com.Img_1_3.gameObject:SetActive(false)
            local state = m.GetActiveState(equipSme)
            -- --已激活/可激活
            if state ~= 3 then
                --已激活
                if state == 1 then
                    item.com.Img_1_2.gameObject:SetActive(true)
                else
                    --可激活                    
                    local bool = HelperL.IsLackGoods(equipSme.CostGoodsID1, equipSme.CostGoodsID1Num, false, false)
                    --local bool1 = HelperL.IsLackGoods(equipSme.CostGoodsID2, equipSme.CostGoodsID2Num, false, false)
                    item.com.Img_Red1_1.gameObject:SetActive(not bool)
                    item.com.Img_1_1.gameObject:SetActive(true)
                           
                    m.objList.Txt_Count1.gameObject:SetActive(true)        
                    m.objList.Good_Icon1.gameObject:SetActive(true) 
                    local num = SkepModule:GetGoodsCount(equipSme.CostGoodsID1)
                    local color = (not bool) and "#fff300" or UI_COLOR.Red
                    m.objList.Txt_Count1.text = string.format("<color=%s>%s</color>", color,
                        HelperL.GetChangeNum(num))
                    AtlasManager:AsyncGetGoodsSprite(equipSme.CostGoodsID1, m.objList.Good_Icon1)
                end
            else                
                item.com.Img_1_1.gameObject:SetActive(true)
            end
            item.com.Item1.gameObject:SetActive(true)
        else
            item.com.Item1.gameObject:SetActive(false)
        end

        if #group >= 2 then
            local equipSme = group[2]
            item.com.Img_Line1_1.gameObject:SetActive(true)
            item.com.Img_Red2_1.gameObject:SetActive(false)
            item.com.Img_2_1.gameObject:SetActive(false)
            item.com.Img_2_2.gameObject:SetActive(false)
            item.com.Img_2_3.gameObject:SetActive(false)
            --item.com.Txt_Lv1_1.text = equipSme.StarLvl
            --item.com.Txt_Lv1_2.text = equipSme.StarLvl
            local state = m.GetActiveState(equipSme)
            -- --已激活/可激活
            if state ~= 3 then
                --已激活
                if state == 1 then
                    item.com.Img_2_2.gameObject:SetActive(true)
                else
                    --可激活
                    local bool = HelperL.IsLackGoods(equipSme.CostGoodsID1, equipSme.CostGoodsID1Num, false, false)
                    --local bool1 = HelperL.IsLackGoods(equipSme.CostGoodsID2, equipSme.CostGoodsID2Num, false, false)
                    item.com.Img_Red2_1.gameObject:SetActive(not bool)
                    item.com.Img_2_1.gameObject:SetActive(true)
                    m.objList.Txt_Count1.gameObject:SetActive(true)        
                    m.objList.Good_Icon1.gameObject:SetActive(true) 
                    local num = SkepModule:GetGoodsCount(equipSme.CostGoodsID1)
                    local color = (not bool) and "#fff300" or UI_COLOR.Red
                    m.objList.Txt_Count1.text = string.format("<color=%s>%s</color>", color,
                        HelperL.GetChangeNum(num))
                    AtlasManager:AsyncGetGoodsSprite(equipSme.CostGoodsID1, m.objList.Good_Icon1)
                end
            else                
                item.com.Img_2_1.gameObject:SetActive(true)
            end
            item.com.Item2.gameObject:SetActive(true)
        else
            item.com.Item2.gameObject:SetActive(false)
        end
        if isLast then
            item.com.Img_Line1_2.gameObject:SetActive(false)
        end
    end

    return item
end

--------------------------------------------------------------------
--获取装备类型图
--------------------------------------------------------------------
function m.CreateGroup2(group)
    ---@class Item_Obj_Type
    local item = {}
    item.group = group   
    item.com = m:CreateSubItem(m.objList.Content2, m.objList.Item_TF2)
    -- item.com.Img_V.gameObject:SetActive(#group >= 3)
    m:AddClick(item.com.Btn_Click1, function()        
        if #group < 1 then return end
        local equipSme = group[1]
        if equipSme.StarLvl ~= m.StarLvl2+1 then
            item.com.Img_XX.gameObject:SetActive(true)
            m.UpdateTips(item,equipSme)
            return
        end

        local bool = HelperL.IsLackGoods(equipSme.CostGoodsID1, equipSme.CostGoodsID1Num, false)
        if bool == true then  
            item.com.Img_XX.gameObject:SetActive(true)
            m.UpdateTips(item,equipSme)
            return
        end
        m.Upgrade(m.SmeltID2)
    end)

    m:AddClick(item.com.Btn_Click2, function()        
        if #group < 2 then return end
        local equipSme = group[2]
        if equipSme.StarLvl ~= m.StarLvl2+1 then
            item.com.Img_XX.gameObject:SetActive(true)
            m.UpdateTips(item,equipSme)
            return
        end

        local bool = HelperL.IsLackGoods(equipSme.CostGoodsID1, equipSme.CostGoodsID1Num, false)
        if bool == true then  
            item.com.Img_XX.gameObject:SetActive(true)
            m.UpdateTips(item,equipSme)
            return
        end
        m.Upgrade(m.SmeltID2)
    end)

    m:AddClick(item.com.Btn_GB, function()        
        item.com.Img_XX.gameObject:SetActive(false)
    end)

    item.UpdateItems = function(isLast) 
        item.com.Img_Line2_1.gameObject:SetActive(false)
        item.com.Img_Line2_2.gameObject:SetActive(true)      
        if #group >= 1 then
            local equipSme = group[1]
            item.com.Img_Red1_1.gameObject:SetActive(false)
            item.com.Img_1_1.gameObject:SetActive(false)
            item.com.Img_1_2.gameObject:SetActive(false)
            item.com.Img_1_3.gameObject:SetActive(false)
            --item.com.Txt_Lv1_1.text = equipSme.StarLvl
            --item.com.Txt_Lv1_2.text = equipSme.StarLvl
            local state = m.GetActiveState(equipSme)
            -- --已激活/可激活
            if state ~= 3 then
                --已激活
                if state == 1 then
                    item.com.Img_1_2.gameObject:SetActive(true)
                else
                    --可激活       
                    local bool = HelperL.IsLackGoods(equipSme.CostGoodsID1, equipSme.CostGoodsID1Num, false, false)
                    --local bool1 = HelperL.IsLackGoods(equipSme.CostGoodsID2, equipSme.CostGoodsID2Num, false, false)
                    item.com.Img_Red1_1.gameObject:SetActive(not bool)
                    item.com.Img_1_1.gameObject:SetActive(true)
                    m.objList.Txt_Count2.gameObject:SetActive(true)        
                    m.objList.Good_Icon2.gameObject:SetActive(true) 
                    local num = SkepModule:GetGoodsCount(equipSme.CostGoodsID1)
                    local color = (not bool) and "#fff300" or UI_COLOR.Red
                    m.objList.Txt_Count2.text = string.format("<color=%s>%s</color>", color, HelperL.GetChangeNum(num))
                    AtlasManager:AsyncGetGoodsSprite(equipSme.CostGoodsID1, m.objList.Good_Icon2)
                end
            else                
                item.com.Img_1_1.gameObject:SetActive(true)
            end
            item.com.Item1.gameObject:SetActive(true)
        else
            item.com.Item1.gameObject:SetActive(false)
        end

        if #group >= 2 then
            local equipSme = group[2]
            item.com.Img_Line2_1.gameObject:SetActive(true)
            item.com.Img_Red2_1.gameObject:SetActive(false)
            item.com.Img_2_1.gameObject:SetActive(false)
            item.com.Img_2_2.gameObject:SetActive(false)
            item.com.Img_2_3.gameObject:SetActive(false)
            --item.com.Txt_Lv1_1.text = equipSme.StarLvl
            --item.com.Txt_Lv1_2.text = equipSme.StarLvl
            local state = m.GetActiveState(equipSme)
            -- --已激活/可激活
            if state ~= 3 then
                --已激活
                if state == 1 then
                    item.com.Img_2_2.gameObject:SetActive(true)
                else
                    --可激活                        
                    local bool = HelperL.IsLackGoods(equipSme.CostGoodsID1, equipSme.CostGoodsID1Num, false, false)
                    --local bool1 = HelperL.IsLackGoods(equipSme.CostGoodsID2, equipSme.CostGoodsID2Num, false, false)
                    item.com.Img_Red2_1.gameObject:SetActive(not bool)
                    item.com.Img_2_1.gameObject:SetActive(true)
                    m.objList.Txt_Count2.gameObject:SetActive(true)        
                    m.objList.Good_Icon2.gameObject:SetActive(true) 
                    local num = SkepModule:GetGoodsCount(equipSme.CostGoodsID1)
                    local color = (not bool) and "#fff300" or UI_COLOR.Red
                    m.objList.Txt_Count2.text = string.format("<color=%s>%s</color>", color,
                        HelperL.GetChangeNum(num))
                    AtlasManager:AsyncGetGoodsSprite(equipSme.CostGoodsID1, m.objList.Good_Icon2)
                end
            else                
                item.com.Img_2_1.gameObject:SetActive(true)
            end
            item.com.Item2.gameObject:SetActive(true)
        else
            item.com.Item2.gameObject:SetActive(false)
        end
        if isLast then
            item.com.Img_Line2_2.gameObject:SetActive(false)
        end
    end

    return item
end


--------------------------------------------------------------------
--获取装备类型图
--------------------------------------------------------------------
function m.CreateGroup3(group)
    ---@class Item_Obj_Type
    local item = {}
    item.group = group   
    item.com = m:CreateSubItem(m.objList.Content3, m.objList.Item_TF3)
    -- item.com.Img_V.gameObject:SetActive(#group >= 3)
    m:AddClick(item.com.Btn_Click1, function()        
        if #group < 1 then return end
        local equipSme = group[1]
        if equipSme.StarLvl ~= m.StarLvl3+1 then
            item.com.Img_XX.gameObject:SetActive(true)
            m.UpdateTips(item,equipSme)
            return
        end

        local bool = HelperL.IsLackGoods(equipSme.CostGoodsID1, equipSme.CostGoodsID1Num, false)
        if bool == true then  
            item.com.Img_XX.gameObject:SetActive(true)
            m.UpdateTips(item,equipSme)
            return
        end
        m.Upgrade(m.SmeltID3)
    end)

    m:AddClick(item.com.Btn_Click2, function()        
        if #group < 2 then return end
        local equipSme = group[2]
        if equipSme.StarLvl ~= m.StarLvl3+1 then
            item.com.Img_XX.gameObject:SetActive(true)
            m.UpdateTips(item,equipSme)
            return
        end

        local bool = HelperL.IsLackGoods(equipSme.CostGoodsID1, equipSme.CostGoodsID1Num, false)
        if bool == true then  
            item.com.Img_XX.gameObject:SetActive(true)
            m.UpdateTips(item,equipSme)
            return
        end
        m.Upgrade(m.SmeltID3)
    end)

    m:AddClick(item.com.Btn_GB, function()        
        item.com.Img_XX.gameObject:SetActive(false)
    end)

    item.UpdateItems = function(isLast)        
        item.com.Img_Line3_1.gameObject:SetActive(false)
        item.com.Img_Line3_2.gameObject:SetActive(true) 
        if #group >= 1 then
            local equipSme = group[1]
            item.com.Img_Red1_1.gameObject:SetActive(false)
            item.com.Img_1_1.gameObject:SetActive(false)
            item.com.Img_1_2.gameObject:SetActive(false)
            item.com.Img_1_3.gameObject:SetActive(false)
            --item.com.Txt_Lv1_1.text = equipSme.StarLvl
            --item.com.Txt_Lv1_2.text = equipSme.StarLvl
            local state = m.GetActiveState(equipSme)
            -- --已激活/可激活
            if state ~= 3 then
                --已激活
                if state == 1 then
                    item.com.Img_1_2.gameObject:SetActive(true)
                else
                    --可激活
                    local bool = HelperL.IsLackGoods(equipSme.CostGoodsID1, equipSme.CostGoodsID1Num, false, false)
                    --local bool1 = HelperL.IsLackGoods(equipSme.CostGoodsID2, equipSme.CostGoodsID2Num, false, false)
                    item.com.Img_Red1_1.gameObject:SetActive(not bool)
                    item.com.Img_1_1.gameObject:SetActive(true)
                    m.objList.Txt_Count3.gameObject:SetActive(true)        
                    m.objList.Good_Icon3.gameObject:SetActive(true) 
                    local num = SkepModule:GetGoodsCount(equipSme.CostGoodsID1)
                    local color = (not bool) and "#fff300" or UI_COLOR.Red
                    m.objList.Txt_Count3.text = string.format("<color=%s>%s</color>", color,
                         HelperL.GetChangeNum(num))
                    AtlasManager:AsyncGetGoodsSprite(equipSme.CostGoodsID1, m.objList.Good_Icon3)
                end
            else                
                item.com.Img_1_1.gameObject:SetActive(true)
            end
            item.com.Item1.gameObject:SetActive(true)
        else
            item.com.Item1.gameObject:SetActive(false)
        end

        if #group >= 2 then
            local equipSme = group[2]
            item.com.Img_Line3_1.gameObject:SetActive(true)
            item.com.Img_Red2_1.gameObject:SetActive(false)
            item.com.Img_2_1.gameObject:SetActive(false)
            item.com.Img_2_2.gameObject:SetActive(false)
            item.com.Img_2_3.gameObject:SetActive(false)
            --item.com.Txt_Lv1_1.text = equipSme.StarLvl
            --item.com.Txt_Lv1_2.text = equipSme.StarLvl
            local state = m.GetActiveState(equipSme)
            -- --已激活/可激活
            if state ~= 3 then
                --已激活
                if state == 1 then
                    item.com.Img_2_2.gameObject:SetActive(true)
                else
                    --可激活
                    local bool = HelperL.IsLackGoods(equipSme.CostGoodsID1, equipSme.CostGoodsID1Num, false, false)
                    --local bool1 = HelperL.IsLackGoods(equipSme.CostGoodsID2, equipSme.CostGoodsID2Num, false, false)
                    item.com.Img_Red2_1.gameObject:SetActive(not bool)
                    item.com.Img_2_1.gameObject:SetActive(true)
                    m.objList.Txt_Count3.gameObject:SetActive(true)        
                    m.objList.Good_Icon3.gameObject:SetActive(true) 
                    local num = SkepModule:GetGoodsCount(equipSme.CostGoodsID1)
                    local color = (not bool) and "#fff300" or UI_COLOR.Red
                    m.objList.Txt_Count3.text = string.format("<color=%s>%s</color>", color,
                        HelperL.GetChangeNum(num))
                    AtlasManager:AsyncGetGoodsSprite(equipSme.CostGoodsID1, m.objList.Good_Icon3)
                end
            else                
                item.com.Img_2_1.gameObject:SetActive(true)
            end
            item.com.Item2.gameObject:SetActive(true)
        else
            item.com.Item2.gameObject:SetActive(false)
        end
        if isLast then
            item.com.Img_Line3_2.gameObject:SetActive(false)
        end
    end

    return item
end
--------------------------------------------------------------------
-- 窗口开启
--------------------------------------------------------------------
function m:OnOpen()
    m.UpdateView()
end

--------------------------------------------------------------------
-- 注册点击事件
--------------------------------------------------------------------
function m.RegisterClickEvent()
    m:AddClick(m.objList.Btn_Close, m.CloseUI)
    -- m:AddClick(m.objList.Btn_mask, function()
    --     for i = 1, #m.groupedList1 do
    --         m.ItemTreasured1[i].com.Img_XX.gameObject:SetActive(false)
    --     end
    --     for i = 1, #m.groupedList2 do
    --         m.ItemTreasured2[i].com.Img_XX.gameObject:SetActive(false)
    --     end
    --     for i = 1, #m.groupedList3 do
    --         m.ItemTreasured3[i].com.Img_XX.gameObject:SetActive(false)
    --     end
    --     m.objList.Btn_mask.gameObject:SetActive(false)
    -- end)
    -- m.objList.ScrollView.onValueChanged:AddListener(function()
    --     m.objList.Img_XX.gameObject:SetActive(false)
    -- end)
end

--------------------------------------------------------------------
-- 更新界面
--------------------------------------------------------------------
function m.UpdateView()
    m.StarLvl1 = GamePlayerData.ActorEquipNew:GetEquipUpgradeLevel(m.SmeltID1)
    m.StarLvl2 = GamePlayerData.ActorEquipNew:GetEquipUpgradeLevel(m.SmeltID2)
    m.StarLvl3 = GamePlayerData.ActorEquipNew:GetEquipUpgradeLevel(m.SmeltID3)
    local maxLevel1 = PropertyCompute.GetEquipSmeltStarMaxLevel(m.SmeltID1)
    local maxLevel2 = PropertyCompute.GetEquipSmeltStarMaxLevel(m.SmeltID2)
    local maxLevel3 = PropertyCompute.GetEquipSmeltStarMaxLevel(m.SmeltID3)
    for i = 1, #m.groupedList1 do
        m.ItemTreasured1[i].UpdateItems(i==#m.groupedList1)
    end

    for i = 1, #m.groupedList2 do
        m.ItemTreasured2[i].UpdateItems(i==#m.groupedList2)
    end

    for i = 1, #m.groupedList3 do
        m.ItemTreasured3[i].UpdateItems(i==#m.groupedList3)
    end
    if m.StarLvl1 >=  maxLevel1 then
        m.objList.Txt_Count1.gameObject:SetActive(false)
        m.objList.Good_Icon1.gameObject:SetActive(false)
        m.objList.Txt_YMJ1.gameObject:SetActive(true)
    else
        m.objList.Txt_YMJ1.gameObject:SetActive(false)
    end
    if m.StarLvl2 >=  maxLevel2 then
        m.objList.Txt_Count2.gameObject:SetActive(false)
        m.objList.Good_Icon2.gameObject:SetActive(false)
        m.objList.Txt_YMJ2.gameObject:SetActive(true)
    else
        m.objList.Txt_YMJ2.gameObject:SetActive(false)
    end

    if m.StarLvl3 >=  maxLevel3 then
        m.objList.Txt_Count3.gameObject:SetActive(false)
        m.objList.Good_Icon3.gameObject:SetActive(false)
        m.objList.Txt_YMJ3.gameObject:SetActive(true)
    else
        m.objList.Txt_YMJ3.gameObject:SetActive(false)
    end
end

--------------------------------------------------------------------
--请求回调
--------------------------------------------------------------------
function m.RequestCallback(resultCode, content)
    if resultCode == RESULT_CODE_BASE.RESULT_COMMON_SUCCEED[1] then
        m.UpdateView()         
        HelperL.PlayVFX()
        HelperL.ShowMessage(TipType.FlowText, "灵根激活成功！")
    else
        ResultCode:DefaultShowResultCode(resultCode, content)
    end
end

--------------------------------------------------------------------
--天赋升级
---@param smeltID integer 升星ID
--------------------------------------------------------------------
function m.Upgrade(smeltID)
    local curTime = Premier.Instance:GetServerTime()
    if curTime - m.lastTime < 1 then
        print('天赋升级点太快了1秒后尝试！')
        return
    end
    m.lastTime = curTime
    print("smeltID ====== "..smeltID)

    local starLvl = GamePlayerData.ActorEquipNew:GetEquipUpgradeLevel(smeltID)
    --if starLvl == 0 then starLvl = 1 end
    local maxLevel = PropertyCompute.GetEquipSmeltStarMaxLevel(smeltID)
    if starLvl >= maxLevel then return end
    local equipSmeltStar = Schemes.EquipSmeltStar:GetEquipSmeltStarCfg(smeltID, starLvl+1)
    if equipSmeltStar == nil then
        warn("天赋升级:未找到升星配置 smeltID=", smeltID, starLvl+1)
        return
    end
    
    if HelperL.IsLackGoods(equipSmeltStar.CostGoodsID1, equipSmeltStar.CostGoodsID1Num, false) then 
        return 
    end

    -- if HelperL.IsLackGoods(equipSmeltStar.CostGoodsID2, equipSmeltStar.CostGoodsID2Num, false) then 
    --     return 
    -- end

    local form = {}
    form["smeltID"] = smeltID
    form["loopTimes"] = 1
    LuaModuleNew.SendRequest(LuaRequestID.NewCardAddExp2, form, m.RequestCallback)
end

function m.UpdateTips(item,equipSme)
    for i = 1, #m.groupedList1 do
        m.ItemTreasured1[i].com.Img_XX.gameObject:SetActive(false)
    end
    for i = 1, #m.groupedList2 do
        m.ItemTreasured2[i].com.Img_XX.gameObject:SetActive(false)
    end
    for i = 1, #m.groupedList3 do
        m.ItemTreasured3[i].com.Img_XX.gameObject:SetActive(false)
    end
    item.com.Img_XX.gameObject:SetActive(true)
    --m.objList.Btn_mask.gameObject:SetActive(true)
    local bool1 = HelperL.IsLackGoods(equipSme.CostGoodsID1, equipSme.CostGoodsID1Num, false, false)
    local num = SkepModule:GetGoodsCount(equipSme.CostGoodsID1)
    local color = (not bool1) and "#fff300" or UI_COLOR.Red
    item.com.Txt_Value1.text = string.format("<color=%s>%s</color>", color,
        HelperL.GetChangeNum(equipSme.CostGoodsID1Num))
    AtlasManager:AsyncGetGoodsSprite(equipSme.CostGoodsID1, item.com.GoodIcon1)
    item.com.Txt_Name.text = equipSme.Name;
    local str = Schemes.CommonProp:Get(equipSme.PropId).Remark
    item.com.Txt_Desc.text = string.gsub(str,'\\n','\n')
end


--------------------------------------------------------------------
--获取激活状态，1：已激活，2：可激活，3：条件未达成(先激活上一星)
---@param equipSme EquipSmeltStarCfg
--------------------------------------------------------------------
function m.GetActiveState(equipSme)
    if equipSme then
        -- local maxLevel = PropertyCompute.GetEquipSmeltStarMaxLevel(equipSme.SmeltID)
        local level = GamePlayerData.ActorEquipNew:GetEquipUpgradeLevel(equipSme.SmeltID)
        --已激活
        if equipSme.StarLvl <= level then
            return 1
        end
        --可激活
        if equipSme.StarLvl == (level + 1) then
            return 2
        end
    end
    --条件未达成(先激活上一星)
    return 3
end


--------------------------------------------------------------------
-- 关闭界面
--------------------------------------------------------------------
function m.CloseUI()
    for i = 1, #m.groupedList1 do
        m.ItemTreasured1[i].com.Img_XX.gameObject:SetActive(false)
    end
    for i = 1, #m.groupedList2 do
        m.ItemTreasured2[i].com.Img_XX.gameObject:SetActive(false)
    end
    for i = 1, #m.groupedList3 do
        m.ItemTreasured3[i].com.Img_XX.gameObject:SetActive(false)
    end
    m:CloseSelf()
end

return m
