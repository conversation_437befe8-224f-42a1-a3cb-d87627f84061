-- Generated By protoc-gen-lua Do not Edit
local protobuf = require "protobuf/protobuf"
module('AgentMessage_pb')
local pb = {}


pb.MSG_AGENT_ACTIONID = protobuf.EnumDescriptor();
pb.MSG_AGENT_ACTIONID_MSG_AGENT_NONE_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_AGENT_ACTIONID_MSG_AGENT_PLAYER_HEARTBEAT_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_AGENT_ACTIONID_MSG_AGENT_PLAYER_SOCKET_KICK_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_AGENT_ACTIONID_MSG_AGENT_PLAYER_GOTO_COMMONSCENE_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_AGENT_ACTIONID_MSG_AGENT_PLAYER_SAVE_AND_QUIT_SCENE_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_AGENT_ACTIONID_MSG_AGENT_PLAYER_SELECT_ZONE_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_AGENT_ACTIONID_MSG_AGENT_PLAYER_SWITCH_ZONE_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_AGENT_ACTIONID_MSG_AGENT_PLAYER_CHECK_STATUS_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_AGENT_ACTIONID_MSG_AGENT_SWITCH_ZONE_ENUM = protobuf.EnumValueDescriptor();
pb.AGENTKICKPLAYERREASON = protobuf.EnumDescriptor();
pb.AGENTKICKPLAYERREASON_AGENTKICKPLAYERREASON_NONE_ENUM = protobuf.EnumValueDescriptor();
pb.AGENTKICKPLAYERREASON_AGENTKICKPLAYERREASON_SWITCHZONE_ENUM = protobuf.EnumValueDescriptor();
pb.CA_AGENT_PLAYER_HEARTBEAT = protobuf.Descriptor();
pb.AC_AGENT_PLAYER_HEARTBEAT = protobuf.Descriptor();
pb.AC_AGENT_PLAYER_HEARTBEAT_SERVERTIME_FIELD = protobuf.FieldDescriptor();
pb.SA_AGENT_PLAYER_SOCKET_KICK = protobuf.Descriptor();
pb.SA_AGENT_PLAYER_SOCKET_KICK_CLIENTID_FIELD = protobuf.FieldDescriptor();
pb.SA_AGENT_PLAYER_SOCKET_KICK_REASON_FIELD = protobuf.FieldDescriptor();
pb.AS_AGENT_PLAYER_SOCKET_KICK = protobuf.Descriptor();
pb.AS_AGENT_PLAYER_SOCKET_KICK_CLIENTID_FIELD = protobuf.FieldDescriptor();
pb.AS_AGENT_PLAYER_SOCKET_KICK_REASON_FIELD = protobuf.FieldDescriptor();
pb.CA_AGENT_PLAYER_SELETE_ZONE = protobuf.Descriptor();
pb.CA_AGENT_PLAYER_SELETE_ZONE_ACTORID_FIELD = protobuf.FieldDescriptor();
pb.CA_AGENT_PLAYER_SELETE_ZONE_ACTORNAME_FIELD = protobuf.FieldDescriptor();
pb.CA_AGENT_PLAYER_SELETE_ZONE_USERID_FIELD = protobuf.FieldDescriptor();
pb.CA_AGENT_PLAYER_SELETE_ZONE_ZONEID_FIELD = protobuf.FieldDescriptor();
pb.CA_AGENT_PLAYER_SELETE_ZONE_TOKEN_FIELD = protobuf.FieldDescriptor();
pb.AS_LOGIN_SELETEACTOR = protobuf.Descriptor();
pb.AS_LOGIN_SELETEACTOR_CLIENTID_FIELD = protobuf.FieldDescriptor();
pb.AS_LOGIN_SELETEACTOR_USERID_FIELD = protobuf.FieldDescriptor();
pb.AS_LOGIN_SELETEACTOR_ACTORID_FIELD = protobuf.FieldDescriptor();
pb.AS_LOGIN_SELETEACTOR_ACTORNAME_FIELD = protobuf.FieldDescriptor();
pb.CA_AGENT_PLAYER_SWITCH_ZONE = protobuf.Descriptor();
pb.CA_AGENT_PLAYER_SWITCH_ZONE_ZONEID_FIELD = protobuf.FieldDescriptor();
pb.CS_CROSS_ZONE = protobuf.Descriptor();
pb.CS_CROSS_ZONE_ZONEID_FIELD = protobuf.FieldDescriptor();
pb.CS_CROSS_ZONE_MESSAGE_FIELD = protobuf.FieldDescriptor();
pb.AS_AGENT_PLAYER_CHECK_STATUS = protobuf.Descriptor();
pb.AS_AGENT_PLAYER_CHECK_STATUS_CLIENTID_FIELD = protobuf.FieldDescriptor();
pb.AS_AGENT_PLAYER_CHECK_STATUS_ACTORID_FIELD = protobuf.FieldDescriptor();
pb.AS_AGENT_PLAYER_CHECK_STATUS_ACTORNAME_FIELD = protobuf.FieldDescriptor();
pb.AS_AGENT_PLAYER_CHECK_STATUS_USERID_FIELD = protobuf.FieldDescriptor();
pb.AS_AGENT_PLAYER_CHECK_STATUS_ZONEID_FIELD = protobuf.FieldDescriptor();
pb.SA_AGENT_PLAYER_CHECK_STATUS = protobuf.Descriptor();
pb.SA_AGENT_PLAYER_CHECK_STATUS_CLIENTID_FIELD = protobuf.FieldDescriptor();
pb.SA_AGENT_PLAYER_CHECK_STATUS_ACTORID_FIELD = protobuf.FieldDescriptor();
pb.SA_AGENT_PLAYER_CHECK_STATUS_ACTORNAME_FIELD = protobuf.FieldDescriptor();
pb.SA_AGENT_PLAYER_CHECK_STATUS_USERID_FIELD = protobuf.FieldDescriptor();
pb.SA_AGENT_PLAYER_CHECK_STATUS_ZONEID_FIELD = protobuf.FieldDescriptor();
pb.SA_AGENT_PLAYER_CHECK_STATUS_STATUS_FIELD = protobuf.FieldDescriptor();
pb.SA_AGENT_SWITCH_ZONE = protobuf.Descriptor();
pb.SA_AGENT_SWITCH_ZONE_ACTORID_FIELD = protobuf.FieldDescriptor();
pb.SA_AGENT_SWITCH_ZONE_ZONEID_FIELD = protobuf.FieldDescriptor();
pb.SA_AGENT_SWITCH_ZONE_LASTZONEID_FIELD = protobuf.FieldDescriptor();
pb.AS_AGENT_SWITCH_ZONE = protobuf.Descriptor();
pb.AS_AGENT_SWITCH_ZONE_ACTORID_FIELD = protobuf.FieldDescriptor();
pb.AS_AGENT_SWITCH_ZONE_ZONEID_FIELD = protobuf.FieldDescriptor();

pb.MSG_AGENT_ACTIONID_MSG_AGENT_NONE_ENUM.name = "MSG_AGENT_NONE"
pb.MSG_AGENT_ACTIONID_MSG_AGENT_NONE_ENUM.index = 0
pb.MSG_AGENT_ACTIONID_MSG_AGENT_NONE_ENUM.number = 0
pb.MSG_AGENT_ACTIONID_MSG_AGENT_PLAYER_HEARTBEAT_ENUM.name = "MSG_AGENT_PLAYER_HEARTBEAT"
pb.MSG_AGENT_ACTIONID_MSG_AGENT_PLAYER_HEARTBEAT_ENUM.index = 1
pb.MSG_AGENT_ACTIONID_MSG_AGENT_PLAYER_HEARTBEAT_ENUM.number = 101
pb.MSG_AGENT_ACTIONID_MSG_AGENT_PLAYER_SOCKET_KICK_ENUM.name = "MSG_AGENT_PLAYER_SOCKET_KICK"
pb.MSG_AGENT_ACTIONID_MSG_AGENT_PLAYER_SOCKET_KICK_ENUM.index = 2
pb.MSG_AGENT_ACTIONID_MSG_AGENT_PLAYER_SOCKET_KICK_ENUM.number = 102
pb.MSG_AGENT_ACTIONID_MSG_AGENT_PLAYER_GOTO_COMMONSCENE_ENUM.name = "MSG_AGENT_PLAYER_GOTO_COMMONSCENE"
pb.MSG_AGENT_ACTIONID_MSG_AGENT_PLAYER_GOTO_COMMONSCENE_ENUM.index = 3
pb.MSG_AGENT_ACTIONID_MSG_AGENT_PLAYER_GOTO_COMMONSCENE_ENUM.number = 103
pb.MSG_AGENT_ACTIONID_MSG_AGENT_PLAYER_SAVE_AND_QUIT_SCENE_ENUM.name = "MSG_AGENT_PLAYER_SAVE_AND_QUIT_SCENE"
pb.MSG_AGENT_ACTIONID_MSG_AGENT_PLAYER_SAVE_AND_QUIT_SCENE_ENUM.index = 4
pb.MSG_AGENT_ACTIONID_MSG_AGENT_PLAYER_SAVE_AND_QUIT_SCENE_ENUM.number = 104
pb.MSG_AGENT_ACTIONID_MSG_AGENT_PLAYER_SELECT_ZONE_ENUM.name = "MSG_AGENT_PLAYER_SELECT_ZONE"
pb.MSG_AGENT_ACTIONID_MSG_AGENT_PLAYER_SELECT_ZONE_ENUM.index = 5
pb.MSG_AGENT_ACTIONID_MSG_AGENT_PLAYER_SELECT_ZONE_ENUM.number = 105
pb.MSG_AGENT_ACTIONID_MSG_AGENT_PLAYER_SWITCH_ZONE_ENUM.name = "MSG_AGENT_PLAYER_SWITCH_ZONE"
pb.MSG_AGENT_ACTIONID_MSG_AGENT_PLAYER_SWITCH_ZONE_ENUM.index = 6
pb.MSG_AGENT_ACTIONID_MSG_AGENT_PLAYER_SWITCH_ZONE_ENUM.number = 106
pb.MSG_AGENT_ACTIONID_MSG_AGENT_PLAYER_CHECK_STATUS_ENUM.name = "MSG_AGENT_PLAYER_CHECK_STATUS"
pb.MSG_AGENT_ACTIONID_MSG_AGENT_PLAYER_CHECK_STATUS_ENUM.index = 7
pb.MSG_AGENT_ACTIONID_MSG_AGENT_PLAYER_CHECK_STATUS_ENUM.number = 107
pb.MSG_AGENT_ACTIONID_MSG_AGENT_SWITCH_ZONE_ENUM.name = "MSG_AGENT_SWITCH_ZONE"
pb.MSG_AGENT_ACTIONID_MSG_AGENT_SWITCH_ZONE_ENUM.index = 8
pb.MSG_AGENT_ACTIONID_MSG_AGENT_SWITCH_ZONE_ENUM.number = 108
pb.MSG_AGENT_ACTIONID.name = "MSG_AGENT_ACTIONID"
pb.MSG_AGENT_ACTIONID.full_name = ".MSG_AGENT_ACTIONID"
pb.MSG_AGENT_ACTIONID.values = {pb.MSG_AGENT_ACTIONID_MSG_AGENT_NONE_ENUM,pb.MSG_AGENT_ACTIONID_MSG_AGENT_PLAYER_HEARTBEAT_ENUM,pb.MSG_AGENT_ACTIONID_MSG_AGENT_PLAYER_SOCKET_KICK_ENUM,pb.MSG_AGENT_ACTIONID_MSG_AGENT_PLAYER_GOTO_COMMONSCENE_ENUM,pb.MSG_AGENT_ACTIONID_MSG_AGENT_PLAYER_SAVE_AND_QUIT_SCENE_ENUM,pb.MSG_AGENT_ACTIONID_MSG_AGENT_PLAYER_SELECT_ZONE_ENUM,pb.MSG_AGENT_ACTIONID_MSG_AGENT_PLAYER_SWITCH_ZONE_ENUM,pb.MSG_AGENT_ACTIONID_MSG_AGENT_PLAYER_CHECK_STATUS_ENUM,pb.MSG_AGENT_ACTIONID_MSG_AGENT_SWITCH_ZONE_ENUM}
pb.AGENTKICKPLAYERREASON_AGENTKICKPLAYERREASON_NONE_ENUM.name = "AgentKickPlayerReason_None"
pb.AGENTKICKPLAYERREASON_AGENTKICKPLAYERREASON_NONE_ENUM.index = 0
pb.AGENTKICKPLAYERREASON_AGENTKICKPLAYERREASON_NONE_ENUM.number = 0
pb.AGENTKICKPLAYERREASON_AGENTKICKPLAYERREASON_SWITCHZONE_ENUM.name = "AgentKickPlayerReason_SwitchZone"
pb.AGENTKICKPLAYERREASON_AGENTKICKPLAYERREASON_SWITCHZONE_ENUM.index = 1
pb.AGENTKICKPLAYERREASON_AGENTKICKPLAYERREASON_SWITCHZONE_ENUM.number = 1
pb.AGENTKICKPLAYERREASON.name = "AgentKickPlayerReason"
pb.AGENTKICKPLAYERREASON.full_name = ".AgentKickPlayerReason"
pb.AGENTKICKPLAYERREASON.values = {pb.AGENTKICKPLAYERREASON_AGENTKICKPLAYERREASON_NONE_ENUM,pb.AGENTKICKPLAYERREASON_AGENTKICKPLAYERREASON_SWITCHZONE_ENUM}
pb.CA_AGENT_PLAYER_HEARTBEAT.name = "CA_Agent_Player_HeartBeat"
pb.CA_AGENT_PLAYER_HEARTBEAT.full_name = ".CA_Agent_Player_HeartBeat"
pb.CA_AGENT_PLAYER_HEARTBEAT.nested_types = {}
pb.CA_AGENT_PLAYER_HEARTBEAT.enum_types = {}
pb.CA_AGENT_PLAYER_HEARTBEAT.fields = {}
pb.CA_AGENT_PLAYER_HEARTBEAT.is_extendable = false
pb.CA_AGENT_PLAYER_HEARTBEAT.extensions = {}
pb.AC_AGENT_PLAYER_HEARTBEAT_SERVERTIME_FIELD.name = "ServerTime"
pb.AC_AGENT_PLAYER_HEARTBEAT_SERVERTIME_FIELD.full_name = ".AC_Agent_Player_HeartBeat.ServerTime"
pb.AC_AGENT_PLAYER_HEARTBEAT_SERVERTIME_FIELD.number = 1
pb.AC_AGENT_PLAYER_HEARTBEAT_SERVERTIME_FIELD.index = 0
pb.AC_AGENT_PLAYER_HEARTBEAT_SERVERTIME_FIELD.label = 2
pb.AC_AGENT_PLAYER_HEARTBEAT_SERVERTIME_FIELD.has_default_value = false
pb.AC_AGENT_PLAYER_HEARTBEAT_SERVERTIME_FIELD.default_value = 0
pb.AC_AGENT_PLAYER_HEARTBEAT_SERVERTIME_FIELD.type = 13
pb.AC_AGENT_PLAYER_HEARTBEAT_SERVERTIME_FIELD.cpp_type = 3

pb.AC_AGENT_PLAYER_HEARTBEAT.name = "AC_Agent_Player_HeartBeat"
pb.AC_AGENT_PLAYER_HEARTBEAT.full_name = ".AC_Agent_Player_HeartBeat"
pb.AC_AGENT_PLAYER_HEARTBEAT.nested_types = {}
pb.AC_AGENT_PLAYER_HEARTBEAT.enum_types = {}
pb.AC_AGENT_PLAYER_HEARTBEAT.fields = {pb.AC_AGENT_PLAYER_HEARTBEAT_SERVERTIME_FIELD}
pb.AC_AGENT_PLAYER_HEARTBEAT.is_extendable = false
pb.AC_AGENT_PLAYER_HEARTBEAT.extensions = {}
pb.SA_AGENT_PLAYER_SOCKET_KICK_CLIENTID_FIELD.name = "ClientID"
pb.SA_AGENT_PLAYER_SOCKET_KICK_CLIENTID_FIELD.full_name = ".SA_Agent_Player_Socket_Kick.ClientID"
pb.SA_AGENT_PLAYER_SOCKET_KICK_CLIENTID_FIELD.number = 1
pb.SA_AGENT_PLAYER_SOCKET_KICK_CLIENTID_FIELD.index = 0
pb.SA_AGENT_PLAYER_SOCKET_KICK_CLIENTID_FIELD.label = 1
pb.SA_AGENT_PLAYER_SOCKET_KICK_CLIENTID_FIELD.has_default_value = false
pb.SA_AGENT_PLAYER_SOCKET_KICK_CLIENTID_FIELD.default_value = 0
pb.SA_AGENT_PLAYER_SOCKET_KICK_CLIENTID_FIELD.type = 13
pb.SA_AGENT_PLAYER_SOCKET_KICK_CLIENTID_FIELD.cpp_type = 3

pb.SA_AGENT_PLAYER_SOCKET_KICK_REASON_FIELD.name = "Reason"
pb.SA_AGENT_PLAYER_SOCKET_KICK_REASON_FIELD.full_name = ".SA_Agent_Player_Socket_Kick.Reason"
pb.SA_AGENT_PLAYER_SOCKET_KICK_REASON_FIELD.number = 2
pb.SA_AGENT_PLAYER_SOCKET_KICK_REASON_FIELD.index = 1
pb.SA_AGENT_PLAYER_SOCKET_KICK_REASON_FIELD.label = 1
pb.SA_AGENT_PLAYER_SOCKET_KICK_REASON_FIELD.has_default_value = false
pb.SA_AGENT_PLAYER_SOCKET_KICK_REASON_FIELD.default_value = 0
pb.SA_AGENT_PLAYER_SOCKET_KICK_REASON_FIELD.type = 13
pb.SA_AGENT_PLAYER_SOCKET_KICK_REASON_FIELD.cpp_type = 3

pb.SA_AGENT_PLAYER_SOCKET_KICK.name = "SA_Agent_Player_Socket_Kick"
pb.SA_AGENT_PLAYER_SOCKET_KICK.full_name = ".SA_Agent_Player_Socket_Kick"
pb.SA_AGENT_PLAYER_SOCKET_KICK.nested_types = {}
pb.SA_AGENT_PLAYER_SOCKET_KICK.enum_types = {}
pb.SA_AGENT_PLAYER_SOCKET_KICK.fields = {pb.SA_AGENT_PLAYER_SOCKET_KICK_CLIENTID_FIELD, pb.SA_AGENT_PLAYER_SOCKET_KICK_REASON_FIELD}
pb.SA_AGENT_PLAYER_SOCKET_KICK.is_extendable = false
pb.SA_AGENT_PLAYER_SOCKET_KICK.extensions = {}
pb.AS_AGENT_PLAYER_SOCKET_KICK_CLIENTID_FIELD.name = "ClientID"
pb.AS_AGENT_PLAYER_SOCKET_KICK_CLIENTID_FIELD.full_name = ".AS_Agent_Player_Socket_Kick.ClientID"
pb.AS_AGENT_PLAYER_SOCKET_KICK_CLIENTID_FIELD.number = 1
pb.AS_AGENT_PLAYER_SOCKET_KICK_CLIENTID_FIELD.index = 0
pb.AS_AGENT_PLAYER_SOCKET_KICK_CLIENTID_FIELD.label = 1
pb.AS_AGENT_PLAYER_SOCKET_KICK_CLIENTID_FIELD.has_default_value = false
pb.AS_AGENT_PLAYER_SOCKET_KICK_CLIENTID_FIELD.default_value = 0
pb.AS_AGENT_PLAYER_SOCKET_KICK_CLIENTID_FIELD.type = 13
pb.AS_AGENT_PLAYER_SOCKET_KICK_CLIENTID_FIELD.cpp_type = 3

pb.AS_AGENT_PLAYER_SOCKET_KICK_REASON_FIELD.name = "Reason"
pb.AS_AGENT_PLAYER_SOCKET_KICK_REASON_FIELD.full_name = ".AS_Agent_Player_Socket_Kick.Reason"
pb.AS_AGENT_PLAYER_SOCKET_KICK_REASON_FIELD.number = 2
pb.AS_AGENT_PLAYER_SOCKET_KICK_REASON_FIELD.index = 1
pb.AS_AGENT_PLAYER_SOCKET_KICK_REASON_FIELD.label = 1
pb.AS_AGENT_PLAYER_SOCKET_KICK_REASON_FIELD.has_default_value = false
pb.AS_AGENT_PLAYER_SOCKET_KICK_REASON_FIELD.default_value = 0
pb.AS_AGENT_PLAYER_SOCKET_KICK_REASON_FIELD.type = 13
pb.AS_AGENT_PLAYER_SOCKET_KICK_REASON_FIELD.cpp_type = 3

pb.AS_AGENT_PLAYER_SOCKET_KICK.name = "AS_Agent_Player_Socket_Kick"
pb.AS_AGENT_PLAYER_SOCKET_KICK.full_name = ".AS_Agent_Player_Socket_Kick"
pb.AS_AGENT_PLAYER_SOCKET_KICK.nested_types = {}
pb.AS_AGENT_PLAYER_SOCKET_KICK.enum_types = {}
pb.AS_AGENT_PLAYER_SOCKET_KICK.fields = {pb.AS_AGENT_PLAYER_SOCKET_KICK_CLIENTID_FIELD, pb.AS_AGENT_PLAYER_SOCKET_KICK_REASON_FIELD}
pb.AS_AGENT_PLAYER_SOCKET_KICK.is_extendable = false
pb.AS_AGENT_PLAYER_SOCKET_KICK.extensions = {}
pb.CA_AGENT_PLAYER_SELETE_ZONE_ACTORID_FIELD.name = "ActorID"
pb.CA_AGENT_PLAYER_SELETE_ZONE_ACTORID_FIELD.full_name = ".CA_Agent_Player_Selete_Zone.ActorID"
pb.CA_AGENT_PLAYER_SELETE_ZONE_ACTORID_FIELD.number = 1
pb.CA_AGENT_PLAYER_SELETE_ZONE_ACTORID_FIELD.index = 0
pb.CA_AGENT_PLAYER_SELETE_ZONE_ACTORID_FIELD.label = 2
pb.CA_AGENT_PLAYER_SELETE_ZONE_ACTORID_FIELD.has_default_value = false
pb.CA_AGENT_PLAYER_SELETE_ZONE_ACTORID_FIELD.default_value = 0
pb.CA_AGENT_PLAYER_SELETE_ZONE_ACTORID_FIELD.type = 13
pb.CA_AGENT_PLAYER_SELETE_ZONE_ACTORID_FIELD.cpp_type = 3

pb.CA_AGENT_PLAYER_SELETE_ZONE_ACTORNAME_FIELD.name = "ActorName"
pb.CA_AGENT_PLAYER_SELETE_ZONE_ACTORNAME_FIELD.full_name = ".CA_Agent_Player_Selete_Zone.ActorName"
pb.CA_AGENT_PLAYER_SELETE_ZONE_ACTORNAME_FIELD.number = 2
pb.CA_AGENT_PLAYER_SELETE_ZONE_ACTORNAME_FIELD.index = 1
pb.CA_AGENT_PLAYER_SELETE_ZONE_ACTORNAME_FIELD.label = 2
pb.CA_AGENT_PLAYER_SELETE_ZONE_ACTORNAME_FIELD.has_default_value = false
pb.CA_AGENT_PLAYER_SELETE_ZONE_ACTORNAME_FIELD.default_value = ""
pb.CA_AGENT_PLAYER_SELETE_ZONE_ACTORNAME_FIELD.type = 9
pb.CA_AGENT_PLAYER_SELETE_ZONE_ACTORNAME_FIELD.cpp_type = 9

pb.CA_AGENT_PLAYER_SELETE_ZONE_USERID_FIELD.name = "UserID"
pb.CA_AGENT_PLAYER_SELETE_ZONE_USERID_FIELD.full_name = ".CA_Agent_Player_Selete_Zone.UserID"
pb.CA_AGENT_PLAYER_SELETE_ZONE_USERID_FIELD.number = 3
pb.CA_AGENT_PLAYER_SELETE_ZONE_USERID_FIELD.index = 2
pb.CA_AGENT_PLAYER_SELETE_ZONE_USERID_FIELD.label = 2
pb.CA_AGENT_PLAYER_SELETE_ZONE_USERID_FIELD.has_default_value = false
pb.CA_AGENT_PLAYER_SELETE_ZONE_USERID_FIELD.default_value = 0
pb.CA_AGENT_PLAYER_SELETE_ZONE_USERID_FIELD.type = 13
pb.CA_AGENT_PLAYER_SELETE_ZONE_USERID_FIELD.cpp_type = 3

pb.CA_AGENT_PLAYER_SELETE_ZONE_ZONEID_FIELD.name = "ZoneID"
pb.CA_AGENT_PLAYER_SELETE_ZONE_ZONEID_FIELD.full_name = ".CA_Agent_Player_Selete_Zone.ZoneID"
pb.CA_AGENT_PLAYER_SELETE_ZONE_ZONEID_FIELD.number = 4
pb.CA_AGENT_PLAYER_SELETE_ZONE_ZONEID_FIELD.index = 3
pb.CA_AGENT_PLAYER_SELETE_ZONE_ZONEID_FIELD.label = 2
pb.CA_AGENT_PLAYER_SELETE_ZONE_ZONEID_FIELD.has_default_value = false
pb.CA_AGENT_PLAYER_SELETE_ZONE_ZONEID_FIELD.default_value = 0
pb.CA_AGENT_PLAYER_SELETE_ZONE_ZONEID_FIELD.type = 5
pb.CA_AGENT_PLAYER_SELETE_ZONE_ZONEID_FIELD.cpp_type = 1

pb.CA_AGENT_PLAYER_SELETE_ZONE_TOKEN_FIELD.name = "Token"
pb.CA_AGENT_PLAYER_SELETE_ZONE_TOKEN_FIELD.full_name = ".CA_Agent_Player_Selete_Zone.Token"
pb.CA_AGENT_PLAYER_SELETE_ZONE_TOKEN_FIELD.number = 5
pb.CA_AGENT_PLAYER_SELETE_ZONE_TOKEN_FIELD.index = 4
pb.CA_AGENT_PLAYER_SELETE_ZONE_TOKEN_FIELD.label = 2
pb.CA_AGENT_PLAYER_SELETE_ZONE_TOKEN_FIELD.has_default_value = false
pb.CA_AGENT_PLAYER_SELETE_ZONE_TOKEN_FIELD.default_value = ""
pb.CA_AGENT_PLAYER_SELETE_ZONE_TOKEN_FIELD.type = 9
pb.CA_AGENT_PLAYER_SELETE_ZONE_TOKEN_FIELD.cpp_type = 9

pb.CA_AGENT_PLAYER_SELETE_ZONE.name = "CA_Agent_Player_Selete_Zone"
pb.CA_AGENT_PLAYER_SELETE_ZONE.full_name = ".CA_Agent_Player_Selete_Zone"
pb.CA_AGENT_PLAYER_SELETE_ZONE.nested_types = {}
pb.CA_AGENT_PLAYER_SELETE_ZONE.enum_types = {}
pb.CA_AGENT_PLAYER_SELETE_ZONE.fields = {pb.CA_AGENT_PLAYER_SELETE_ZONE_ACTORID_FIELD, pb.CA_AGENT_PLAYER_SELETE_ZONE_ACTORNAME_FIELD, pb.CA_AGENT_PLAYER_SELETE_ZONE_USERID_FIELD, pb.CA_AGENT_PLAYER_SELETE_ZONE_ZONEID_FIELD, pb.CA_AGENT_PLAYER_SELETE_ZONE_TOKEN_FIELD}
pb.CA_AGENT_PLAYER_SELETE_ZONE.is_extendable = false
pb.CA_AGENT_PLAYER_SELETE_ZONE.extensions = {}
pb.AS_LOGIN_SELETEACTOR_CLIENTID_FIELD.name = "ClientID"
pb.AS_LOGIN_SELETEACTOR_CLIENTID_FIELD.full_name = ".AS_Login_SeleteActor.ClientID"
pb.AS_LOGIN_SELETEACTOR_CLIENTID_FIELD.number = 1
pb.AS_LOGIN_SELETEACTOR_CLIENTID_FIELD.index = 0
pb.AS_LOGIN_SELETEACTOR_CLIENTID_FIELD.label = 2
pb.AS_LOGIN_SELETEACTOR_CLIENTID_FIELD.has_default_value = false
pb.AS_LOGIN_SELETEACTOR_CLIENTID_FIELD.default_value = 0
pb.AS_LOGIN_SELETEACTOR_CLIENTID_FIELD.type = 13
pb.AS_LOGIN_SELETEACTOR_CLIENTID_FIELD.cpp_type = 3

pb.AS_LOGIN_SELETEACTOR_USERID_FIELD.name = "UserID"
pb.AS_LOGIN_SELETEACTOR_USERID_FIELD.full_name = ".AS_Login_SeleteActor.UserID"
pb.AS_LOGIN_SELETEACTOR_USERID_FIELD.number = 2
pb.AS_LOGIN_SELETEACTOR_USERID_FIELD.index = 1
pb.AS_LOGIN_SELETEACTOR_USERID_FIELD.label = 2
pb.AS_LOGIN_SELETEACTOR_USERID_FIELD.has_default_value = false
pb.AS_LOGIN_SELETEACTOR_USERID_FIELD.default_value = 0
pb.AS_LOGIN_SELETEACTOR_USERID_FIELD.type = 13
pb.AS_LOGIN_SELETEACTOR_USERID_FIELD.cpp_type = 3

pb.AS_LOGIN_SELETEACTOR_ACTORID_FIELD.name = "ActorID"
pb.AS_LOGIN_SELETEACTOR_ACTORID_FIELD.full_name = ".AS_Login_SeleteActor.ActorID"
pb.AS_LOGIN_SELETEACTOR_ACTORID_FIELD.number = 3
pb.AS_LOGIN_SELETEACTOR_ACTORID_FIELD.index = 2
pb.AS_LOGIN_SELETEACTOR_ACTORID_FIELD.label = 2
pb.AS_LOGIN_SELETEACTOR_ACTORID_FIELD.has_default_value = false
pb.AS_LOGIN_SELETEACTOR_ACTORID_FIELD.default_value = 0
pb.AS_LOGIN_SELETEACTOR_ACTORID_FIELD.type = 13
pb.AS_LOGIN_SELETEACTOR_ACTORID_FIELD.cpp_type = 3

pb.AS_LOGIN_SELETEACTOR_ACTORNAME_FIELD.name = "ActorName"
pb.AS_LOGIN_SELETEACTOR_ACTORNAME_FIELD.full_name = ".AS_Login_SeleteActor.ActorName"
pb.AS_LOGIN_SELETEACTOR_ACTORNAME_FIELD.number = 4
pb.AS_LOGIN_SELETEACTOR_ACTORNAME_FIELD.index = 3
pb.AS_LOGIN_SELETEACTOR_ACTORNAME_FIELD.label = 2
pb.AS_LOGIN_SELETEACTOR_ACTORNAME_FIELD.has_default_value = false
pb.AS_LOGIN_SELETEACTOR_ACTORNAME_FIELD.default_value = ""
pb.AS_LOGIN_SELETEACTOR_ACTORNAME_FIELD.type = 9
pb.AS_LOGIN_SELETEACTOR_ACTORNAME_FIELD.cpp_type = 9

pb.AS_LOGIN_SELETEACTOR.name = "AS_Login_SeleteActor"
pb.AS_LOGIN_SELETEACTOR.full_name = ".AS_Login_SeleteActor"
pb.AS_LOGIN_SELETEACTOR.nested_types = {}
pb.AS_LOGIN_SELETEACTOR.enum_types = {}
pb.AS_LOGIN_SELETEACTOR.fields = {pb.AS_LOGIN_SELETEACTOR_CLIENTID_FIELD, pb.AS_LOGIN_SELETEACTOR_USERID_FIELD, pb.AS_LOGIN_SELETEACTOR_ACTORID_FIELD, pb.AS_LOGIN_SELETEACTOR_ACTORNAME_FIELD}
pb.AS_LOGIN_SELETEACTOR.is_extendable = false
pb.AS_LOGIN_SELETEACTOR.extensions = {}
pb.CA_AGENT_PLAYER_SWITCH_ZONE_ZONEID_FIELD.name = "ZoneID"
pb.CA_AGENT_PLAYER_SWITCH_ZONE_ZONEID_FIELD.full_name = ".CA_Agent_Player_Switch_Zone.ZoneID"
pb.CA_AGENT_PLAYER_SWITCH_ZONE_ZONEID_FIELD.number = 1
pb.CA_AGENT_PLAYER_SWITCH_ZONE_ZONEID_FIELD.index = 0
pb.CA_AGENT_PLAYER_SWITCH_ZONE_ZONEID_FIELD.label = 2
pb.CA_AGENT_PLAYER_SWITCH_ZONE_ZONEID_FIELD.has_default_value = false
pb.CA_AGENT_PLAYER_SWITCH_ZONE_ZONEID_FIELD.default_value = 0
pb.CA_AGENT_PLAYER_SWITCH_ZONE_ZONEID_FIELD.type = 13
pb.CA_AGENT_PLAYER_SWITCH_ZONE_ZONEID_FIELD.cpp_type = 3

pb.CA_AGENT_PLAYER_SWITCH_ZONE.name = "CA_Agent_Player_Switch_Zone"
pb.CA_AGENT_PLAYER_SWITCH_ZONE.full_name = ".CA_Agent_Player_Switch_Zone"
pb.CA_AGENT_PLAYER_SWITCH_ZONE.nested_types = {}
pb.CA_AGENT_PLAYER_SWITCH_ZONE.enum_types = {}
pb.CA_AGENT_PLAYER_SWITCH_ZONE.fields = {pb.CA_AGENT_PLAYER_SWITCH_ZONE_ZONEID_FIELD}
pb.CA_AGENT_PLAYER_SWITCH_ZONE.is_extendable = false
pb.CA_AGENT_PLAYER_SWITCH_ZONE.extensions = {}
pb.CS_CROSS_ZONE_ZONEID_FIELD.name = "ZoneId"
pb.CS_CROSS_ZONE_ZONEID_FIELD.full_name = ".CS_Cross_Zone.ZoneId"
pb.CS_CROSS_ZONE_ZONEID_FIELD.number = 1
pb.CS_CROSS_ZONE_ZONEID_FIELD.index = 0
pb.CS_CROSS_ZONE_ZONEID_FIELD.label = 2
pb.CS_CROSS_ZONE_ZONEID_FIELD.has_default_value = false
pb.CS_CROSS_ZONE_ZONEID_FIELD.default_value = 0
pb.CS_CROSS_ZONE_ZONEID_FIELD.type = 5
pb.CS_CROSS_ZONE_ZONEID_FIELD.cpp_type = 1

pb.CS_CROSS_ZONE_MESSAGE_FIELD.name = "Message"
pb.CS_CROSS_ZONE_MESSAGE_FIELD.full_name = ".CS_Cross_Zone.Message"
pb.CS_CROSS_ZONE_MESSAGE_FIELD.number = 3
pb.CS_CROSS_ZONE_MESSAGE_FIELD.index = 1
pb.CS_CROSS_ZONE_MESSAGE_FIELD.label = 2
pb.CS_CROSS_ZONE_MESSAGE_FIELD.has_default_value = false
pb.CS_CROSS_ZONE_MESSAGE_FIELD.default_value = ""
pb.CS_CROSS_ZONE_MESSAGE_FIELD.type = 12
pb.CS_CROSS_ZONE_MESSAGE_FIELD.cpp_type = 9

pb.CS_CROSS_ZONE.name = "CS_Cross_Zone"
pb.CS_CROSS_ZONE.full_name = ".CS_Cross_Zone"
pb.CS_CROSS_ZONE.nested_types = {}
pb.CS_CROSS_ZONE.enum_types = {}
pb.CS_CROSS_ZONE.fields = {pb.CS_CROSS_ZONE_ZONEID_FIELD, pb.CS_CROSS_ZONE_MESSAGE_FIELD}
pb.CS_CROSS_ZONE.is_extendable = false
pb.CS_CROSS_ZONE.extensions = {}
pb.AS_AGENT_PLAYER_CHECK_STATUS_CLIENTID_FIELD.name = "ClientID"
pb.AS_AGENT_PLAYER_CHECK_STATUS_CLIENTID_FIELD.full_name = ".AS_Agent_Player_Check_Status.ClientID"
pb.AS_AGENT_PLAYER_CHECK_STATUS_CLIENTID_FIELD.number = 1
pb.AS_AGENT_PLAYER_CHECK_STATUS_CLIENTID_FIELD.index = 0
pb.AS_AGENT_PLAYER_CHECK_STATUS_CLIENTID_FIELD.label = 1
pb.AS_AGENT_PLAYER_CHECK_STATUS_CLIENTID_FIELD.has_default_value = false
pb.AS_AGENT_PLAYER_CHECK_STATUS_CLIENTID_FIELD.default_value = 0
pb.AS_AGENT_PLAYER_CHECK_STATUS_CLIENTID_FIELD.type = 13
pb.AS_AGENT_PLAYER_CHECK_STATUS_CLIENTID_FIELD.cpp_type = 3

pb.AS_AGENT_PLAYER_CHECK_STATUS_ACTORID_FIELD.name = "ActorID"
pb.AS_AGENT_PLAYER_CHECK_STATUS_ACTORID_FIELD.full_name = ".AS_Agent_Player_Check_Status.ActorID"
pb.AS_AGENT_PLAYER_CHECK_STATUS_ACTORID_FIELD.number = 2
pb.AS_AGENT_PLAYER_CHECK_STATUS_ACTORID_FIELD.index = 1
pb.AS_AGENT_PLAYER_CHECK_STATUS_ACTORID_FIELD.label = 1
pb.AS_AGENT_PLAYER_CHECK_STATUS_ACTORID_FIELD.has_default_value = false
pb.AS_AGENT_PLAYER_CHECK_STATUS_ACTORID_FIELD.default_value = 0
pb.AS_AGENT_PLAYER_CHECK_STATUS_ACTORID_FIELD.type = 13
pb.AS_AGENT_PLAYER_CHECK_STATUS_ACTORID_FIELD.cpp_type = 3

pb.AS_AGENT_PLAYER_CHECK_STATUS_ACTORNAME_FIELD.name = "ActorName"
pb.AS_AGENT_PLAYER_CHECK_STATUS_ACTORNAME_FIELD.full_name = ".AS_Agent_Player_Check_Status.ActorName"
pb.AS_AGENT_PLAYER_CHECK_STATUS_ACTORNAME_FIELD.number = 3
pb.AS_AGENT_PLAYER_CHECK_STATUS_ACTORNAME_FIELD.index = 2
pb.AS_AGENT_PLAYER_CHECK_STATUS_ACTORNAME_FIELD.label = 1
pb.AS_AGENT_PLAYER_CHECK_STATUS_ACTORNAME_FIELD.has_default_value = false
pb.AS_AGENT_PLAYER_CHECK_STATUS_ACTORNAME_FIELD.default_value = ""
pb.AS_AGENT_PLAYER_CHECK_STATUS_ACTORNAME_FIELD.type = 9
pb.AS_AGENT_PLAYER_CHECK_STATUS_ACTORNAME_FIELD.cpp_type = 9

pb.AS_AGENT_PLAYER_CHECK_STATUS_USERID_FIELD.name = "UserID"
pb.AS_AGENT_PLAYER_CHECK_STATUS_USERID_FIELD.full_name = ".AS_Agent_Player_Check_Status.UserID"
pb.AS_AGENT_PLAYER_CHECK_STATUS_USERID_FIELD.number = 4
pb.AS_AGENT_PLAYER_CHECK_STATUS_USERID_FIELD.index = 3
pb.AS_AGENT_PLAYER_CHECK_STATUS_USERID_FIELD.label = 1
pb.AS_AGENT_PLAYER_CHECK_STATUS_USERID_FIELD.has_default_value = false
pb.AS_AGENT_PLAYER_CHECK_STATUS_USERID_FIELD.default_value = 0
pb.AS_AGENT_PLAYER_CHECK_STATUS_USERID_FIELD.type = 13
pb.AS_AGENT_PLAYER_CHECK_STATUS_USERID_FIELD.cpp_type = 3

pb.AS_AGENT_PLAYER_CHECK_STATUS_ZONEID_FIELD.name = "ZoneID"
pb.AS_AGENT_PLAYER_CHECK_STATUS_ZONEID_FIELD.full_name = ".AS_Agent_Player_Check_Status.ZoneID"
pb.AS_AGENT_PLAYER_CHECK_STATUS_ZONEID_FIELD.number = 5
pb.AS_AGENT_PLAYER_CHECK_STATUS_ZONEID_FIELD.index = 4
pb.AS_AGENT_PLAYER_CHECK_STATUS_ZONEID_FIELD.label = 1
pb.AS_AGENT_PLAYER_CHECK_STATUS_ZONEID_FIELD.has_default_value = false
pb.AS_AGENT_PLAYER_CHECK_STATUS_ZONEID_FIELD.default_value = 0
pb.AS_AGENT_PLAYER_CHECK_STATUS_ZONEID_FIELD.type = 13
pb.AS_AGENT_PLAYER_CHECK_STATUS_ZONEID_FIELD.cpp_type = 3

pb.AS_AGENT_PLAYER_CHECK_STATUS.name = "AS_Agent_Player_Check_Status"
pb.AS_AGENT_PLAYER_CHECK_STATUS.full_name = ".AS_Agent_Player_Check_Status"
pb.AS_AGENT_PLAYER_CHECK_STATUS.nested_types = {}
pb.AS_AGENT_PLAYER_CHECK_STATUS.enum_types = {}
pb.AS_AGENT_PLAYER_CHECK_STATUS.fields = {pb.AS_AGENT_PLAYER_CHECK_STATUS_CLIENTID_FIELD, pb.AS_AGENT_PLAYER_CHECK_STATUS_ACTORID_FIELD, pb.AS_AGENT_PLAYER_CHECK_STATUS_ACTORNAME_FIELD, pb.AS_AGENT_PLAYER_CHECK_STATUS_USERID_FIELD, pb.AS_AGENT_PLAYER_CHECK_STATUS_ZONEID_FIELD}
pb.AS_AGENT_PLAYER_CHECK_STATUS.is_extendable = false
pb.AS_AGENT_PLAYER_CHECK_STATUS.extensions = {}
pb.SA_AGENT_PLAYER_CHECK_STATUS_CLIENTID_FIELD.name = "ClientID"
pb.SA_AGENT_PLAYER_CHECK_STATUS_CLIENTID_FIELD.full_name = ".SA_Agent_Player_Check_Status.ClientID"
pb.SA_AGENT_PLAYER_CHECK_STATUS_CLIENTID_FIELD.number = 1
pb.SA_AGENT_PLAYER_CHECK_STATUS_CLIENTID_FIELD.index = 0
pb.SA_AGENT_PLAYER_CHECK_STATUS_CLIENTID_FIELD.label = 1
pb.SA_AGENT_PLAYER_CHECK_STATUS_CLIENTID_FIELD.has_default_value = false
pb.SA_AGENT_PLAYER_CHECK_STATUS_CLIENTID_FIELD.default_value = 0
pb.SA_AGENT_PLAYER_CHECK_STATUS_CLIENTID_FIELD.type = 13
pb.SA_AGENT_PLAYER_CHECK_STATUS_CLIENTID_FIELD.cpp_type = 3

pb.SA_AGENT_PLAYER_CHECK_STATUS_ACTORID_FIELD.name = "ActorID"
pb.SA_AGENT_PLAYER_CHECK_STATUS_ACTORID_FIELD.full_name = ".SA_Agent_Player_Check_Status.ActorID"
pb.SA_AGENT_PLAYER_CHECK_STATUS_ACTORID_FIELD.number = 2
pb.SA_AGENT_PLAYER_CHECK_STATUS_ACTORID_FIELD.index = 1
pb.SA_AGENT_PLAYER_CHECK_STATUS_ACTORID_FIELD.label = 1
pb.SA_AGENT_PLAYER_CHECK_STATUS_ACTORID_FIELD.has_default_value = false
pb.SA_AGENT_PLAYER_CHECK_STATUS_ACTORID_FIELD.default_value = 0
pb.SA_AGENT_PLAYER_CHECK_STATUS_ACTORID_FIELD.type = 13
pb.SA_AGENT_PLAYER_CHECK_STATUS_ACTORID_FIELD.cpp_type = 3

pb.SA_AGENT_PLAYER_CHECK_STATUS_ACTORNAME_FIELD.name = "ActorName"
pb.SA_AGENT_PLAYER_CHECK_STATUS_ACTORNAME_FIELD.full_name = ".SA_Agent_Player_Check_Status.ActorName"
pb.SA_AGENT_PLAYER_CHECK_STATUS_ACTORNAME_FIELD.number = 3
pb.SA_AGENT_PLAYER_CHECK_STATUS_ACTORNAME_FIELD.index = 2
pb.SA_AGENT_PLAYER_CHECK_STATUS_ACTORNAME_FIELD.label = 1
pb.SA_AGENT_PLAYER_CHECK_STATUS_ACTORNAME_FIELD.has_default_value = false
pb.SA_AGENT_PLAYER_CHECK_STATUS_ACTORNAME_FIELD.default_value = ""
pb.SA_AGENT_PLAYER_CHECK_STATUS_ACTORNAME_FIELD.type = 9
pb.SA_AGENT_PLAYER_CHECK_STATUS_ACTORNAME_FIELD.cpp_type = 9

pb.SA_AGENT_PLAYER_CHECK_STATUS_USERID_FIELD.name = "UserID"
pb.SA_AGENT_PLAYER_CHECK_STATUS_USERID_FIELD.full_name = ".SA_Agent_Player_Check_Status.UserID"
pb.SA_AGENT_PLAYER_CHECK_STATUS_USERID_FIELD.number = 4
pb.SA_AGENT_PLAYER_CHECK_STATUS_USERID_FIELD.index = 3
pb.SA_AGENT_PLAYER_CHECK_STATUS_USERID_FIELD.label = 1
pb.SA_AGENT_PLAYER_CHECK_STATUS_USERID_FIELD.has_default_value = false
pb.SA_AGENT_PLAYER_CHECK_STATUS_USERID_FIELD.default_value = 0
pb.SA_AGENT_PLAYER_CHECK_STATUS_USERID_FIELD.type = 13
pb.SA_AGENT_PLAYER_CHECK_STATUS_USERID_FIELD.cpp_type = 3

pb.SA_AGENT_PLAYER_CHECK_STATUS_ZONEID_FIELD.name = "ZoneID"
pb.SA_AGENT_PLAYER_CHECK_STATUS_ZONEID_FIELD.full_name = ".SA_Agent_Player_Check_Status.ZoneID"
pb.SA_AGENT_PLAYER_CHECK_STATUS_ZONEID_FIELD.number = 5
pb.SA_AGENT_PLAYER_CHECK_STATUS_ZONEID_FIELD.index = 4
pb.SA_AGENT_PLAYER_CHECK_STATUS_ZONEID_FIELD.label = 1
pb.SA_AGENT_PLAYER_CHECK_STATUS_ZONEID_FIELD.has_default_value = false
pb.SA_AGENT_PLAYER_CHECK_STATUS_ZONEID_FIELD.default_value = 0
pb.SA_AGENT_PLAYER_CHECK_STATUS_ZONEID_FIELD.type = 5
pb.SA_AGENT_PLAYER_CHECK_STATUS_ZONEID_FIELD.cpp_type = 1

pb.SA_AGENT_PLAYER_CHECK_STATUS_STATUS_FIELD.name = "Status"
pb.SA_AGENT_PLAYER_CHECK_STATUS_STATUS_FIELD.full_name = ".SA_Agent_Player_Check_Status.Status"
pb.SA_AGENT_PLAYER_CHECK_STATUS_STATUS_FIELD.number = 6
pb.SA_AGENT_PLAYER_CHECK_STATUS_STATUS_FIELD.index = 5
pb.SA_AGENT_PLAYER_CHECK_STATUS_STATUS_FIELD.label = 1
pb.SA_AGENT_PLAYER_CHECK_STATUS_STATUS_FIELD.has_default_value = false
pb.SA_AGENT_PLAYER_CHECK_STATUS_STATUS_FIELD.default_value = 0
pb.SA_AGENT_PLAYER_CHECK_STATUS_STATUS_FIELD.type = 13
pb.SA_AGENT_PLAYER_CHECK_STATUS_STATUS_FIELD.cpp_type = 3

pb.SA_AGENT_PLAYER_CHECK_STATUS.name = "SA_Agent_Player_Check_Status"
pb.SA_AGENT_PLAYER_CHECK_STATUS.full_name = ".SA_Agent_Player_Check_Status"
pb.SA_AGENT_PLAYER_CHECK_STATUS.nested_types = {}
pb.SA_AGENT_PLAYER_CHECK_STATUS.enum_types = {}
pb.SA_AGENT_PLAYER_CHECK_STATUS.fields = {pb.SA_AGENT_PLAYER_CHECK_STATUS_CLIENTID_FIELD, pb.SA_AGENT_PLAYER_CHECK_STATUS_ACTORID_FIELD, pb.SA_AGENT_PLAYER_CHECK_STATUS_ACTORNAME_FIELD, pb.SA_AGENT_PLAYER_CHECK_STATUS_USERID_FIELD, pb.SA_AGENT_PLAYER_CHECK_STATUS_ZONEID_FIELD, pb.SA_AGENT_PLAYER_CHECK_STATUS_STATUS_FIELD}
pb.SA_AGENT_PLAYER_CHECK_STATUS.is_extendable = false
pb.SA_AGENT_PLAYER_CHECK_STATUS.extensions = {}
pb.SA_AGENT_SWITCH_ZONE_ACTORID_FIELD.name = "ActorID"
pb.SA_AGENT_SWITCH_ZONE_ACTORID_FIELD.full_name = ".SA_Agent_Switch_Zone.ActorID"
pb.SA_AGENT_SWITCH_ZONE_ACTORID_FIELD.number = 1
pb.SA_AGENT_SWITCH_ZONE_ACTORID_FIELD.index = 0
pb.SA_AGENT_SWITCH_ZONE_ACTORID_FIELD.label = 1
pb.SA_AGENT_SWITCH_ZONE_ACTORID_FIELD.has_default_value = false
pb.SA_AGENT_SWITCH_ZONE_ACTORID_FIELD.default_value = 0
pb.SA_AGENT_SWITCH_ZONE_ACTORID_FIELD.type = 13
pb.SA_AGENT_SWITCH_ZONE_ACTORID_FIELD.cpp_type = 3

pb.SA_AGENT_SWITCH_ZONE_ZONEID_FIELD.name = "ZoneID"
pb.SA_AGENT_SWITCH_ZONE_ZONEID_FIELD.full_name = ".SA_Agent_Switch_Zone.ZoneID"
pb.SA_AGENT_SWITCH_ZONE_ZONEID_FIELD.number = 2
pb.SA_AGENT_SWITCH_ZONE_ZONEID_FIELD.index = 1
pb.SA_AGENT_SWITCH_ZONE_ZONEID_FIELD.label = 1
pb.SA_AGENT_SWITCH_ZONE_ZONEID_FIELD.has_default_value = false
pb.SA_AGENT_SWITCH_ZONE_ZONEID_FIELD.default_value = 0
pb.SA_AGENT_SWITCH_ZONE_ZONEID_FIELD.type = 13
pb.SA_AGENT_SWITCH_ZONE_ZONEID_FIELD.cpp_type = 3

pb.SA_AGENT_SWITCH_ZONE_LASTZONEID_FIELD.name = "LastZoneID"
pb.SA_AGENT_SWITCH_ZONE_LASTZONEID_FIELD.full_name = ".SA_Agent_Switch_Zone.LastZoneID"
pb.SA_AGENT_SWITCH_ZONE_LASTZONEID_FIELD.number = 3
pb.SA_AGENT_SWITCH_ZONE_LASTZONEID_FIELD.index = 2
pb.SA_AGENT_SWITCH_ZONE_LASTZONEID_FIELD.label = 1
pb.SA_AGENT_SWITCH_ZONE_LASTZONEID_FIELD.has_default_value = false
pb.SA_AGENT_SWITCH_ZONE_LASTZONEID_FIELD.default_value = 0
pb.SA_AGENT_SWITCH_ZONE_LASTZONEID_FIELD.type = 13
pb.SA_AGENT_SWITCH_ZONE_LASTZONEID_FIELD.cpp_type = 3

pb.SA_AGENT_SWITCH_ZONE.name = "SA_Agent_Switch_Zone"
pb.SA_AGENT_SWITCH_ZONE.full_name = ".SA_Agent_Switch_Zone"
pb.SA_AGENT_SWITCH_ZONE.nested_types = {}
pb.SA_AGENT_SWITCH_ZONE.enum_types = {}
pb.SA_AGENT_SWITCH_ZONE.fields = {pb.SA_AGENT_SWITCH_ZONE_ACTORID_FIELD, pb.SA_AGENT_SWITCH_ZONE_ZONEID_FIELD, pb.SA_AGENT_SWITCH_ZONE_LASTZONEID_FIELD}
pb.SA_AGENT_SWITCH_ZONE.is_extendable = false
pb.SA_AGENT_SWITCH_ZONE.extensions = {}
pb.AS_AGENT_SWITCH_ZONE_ACTORID_FIELD.name = "ActorID"
pb.AS_AGENT_SWITCH_ZONE_ACTORID_FIELD.full_name = ".AS_Agent_Switch_Zone.ActorID"
pb.AS_AGENT_SWITCH_ZONE_ACTORID_FIELD.number = 1
pb.AS_AGENT_SWITCH_ZONE_ACTORID_FIELD.index = 0
pb.AS_AGENT_SWITCH_ZONE_ACTORID_FIELD.label = 1
pb.AS_AGENT_SWITCH_ZONE_ACTORID_FIELD.has_default_value = false
pb.AS_AGENT_SWITCH_ZONE_ACTORID_FIELD.default_value = 0
pb.AS_AGENT_SWITCH_ZONE_ACTORID_FIELD.type = 13
pb.AS_AGENT_SWITCH_ZONE_ACTORID_FIELD.cpp_type = 3

pb.AS_AGENT_SWITCH_ZONE_ZONEID_FIELD.name = "ZoneID"
pb.AS_AGENT_SWITCH_ZONE_ZONEID_FIELD.full_name = ".AS_Agent_Switch_Zone.ZoneID"
pb.AS_AGENT_SWITCH_ZONE_ZONEID_FIELD.number = 2
pb.AS_AGENT_SWITCH_ZONE_ZONEID_FIELD.index = 1
pb.AS_AGENT_SWITCH_ZONE_ZONEID_FIELD.label = 1
pb.AS_AGENT_SWITCH_ZONE_ZONEID_FIELD.has_default_value = false
pb.AS_AGENT_SWITCH_ZONE_ZONEID_FIELD.default_value = 0
pb.AS_AGENT_SWITCH_ZONE_ZONEID_FIELD.type = 13
pb.AS_AGENT_SWITCH_ZONE_ZONEID_FIELD.cpp_type = 3

pb.AS_AGENT_SWITCH_ZONE.name = "AS_Agent_Switch_Zone"
pb.AS_AGENT_SWITCH_ZONE.full_name = ".AS_Agent_Switch_Zone"
pb.AS_AGENT_SWITCH_ZONE.nested_types = {}
pb.AS_AGENT_SWITCH_ZONE.enum_types = {}
pb.AS_AGENT_SWITCH_ZONE.fields = {pb.AS_AGENT_SWITCH_ZONE_ACTORID_FIELD, pb.AS_AGENT_SWITCH_ZONE_ZONEID_FIELD}
pb.AS_AGENT_SWITCH_ZONE.is_extendable = false
pb.AS_AGENT_SWITCH_ZONE.extensions = {}

AC_Agent_Player_HeartBeat = protobuf.Message(pb.AC_AGENT_PLAYER_HEARTBEAT)
AS_Agent_Player_Check_Status = protobuf.Message(pb.AS_AGENT_PLAYER_CHECK_STATUS)
AS_Agent_Player_Socket_Kick = protobuf.Message(pb.AS_AGENT_PLAYER_SOCKET_KICK)
AS_Agent_Switch_Zone = protobuf.Message(pb.AS_AGENT_SWITCH_ZONE)
AS_Login_SeleteActor = protobuf.Message(pb.AS_LOGIN_SELETEACTOR)
AgentKickPlayerReason_None = 0
AgentKickPlayerReason_SwitchZone = 1
CA_Agent_Player_HeartBeat = protobuf.Message(pb.CA_AGENT_PLAYER_HEARTBEAT)
CA_Agent_Player_Selete_Zone = protobuf.Message(pb.CA_AGENT_PLAYER_SELETE_ZONE)
CA_Agent_Player_Switch_Zone = protobuf.Message(pb.CA_AGENT_PLAYER_SWITCH_ZONE)
CS_Cross_Zone = protobuf.Message(pb.CS_CROSS_ZONE)
MSG_AGENT_NONE = 0
MSG_AGENT_PLAYER_CHECK_STATUS = 107
MSG_AGENT_PLAYER_GOTO_COMMONSCENE = 103
MSG_AGENT_PLAYER_HEARTBEAT = 101
MSG_AGENT_PLAYER_SAVE_AND_QUIT_SCENE = 104
MSG_AGENT_PLAYER_SELECT_ZONE = 105
MSG_AGENT_PLAYER_SOCKET_KICK = 102
MSG_AGENT_PLAYER_SWITCH_ZONE = 106
MSG_AGENT_SWITCH_ZONE = 108
SA_Agent_Player_Check_Status = protobuf.Message(pb.SA_AGENT_PLAYER_CHECK_STATUS)
SA_Agent_Player_Socket_Kick = protobuf.Message(pb.SA_AGENT_PLAYER_SOCKET_KICK)
SA_Agent_Switch_Zone = protobuf.Message(pb.SA_AGENT_SWITCH_ZONE)

