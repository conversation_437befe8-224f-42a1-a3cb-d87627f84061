--[[
   created:	2017/11/22
   author :	胡杰灵
   purpose:	主线任务（分页）
--]]
local luaID = ('UIMainTask')

local UIMainTask = {}
function UIMainTask:OnCreate(  )

	UIMainTask.gameObject.name = 'UIMainTask(clone)'
    UIMainTask.backBtn = UIMainTask.gameObject.transform:Find('Original/Back').gameObject
    local title1 = UIMainTask.gameObject.transform:Find('Original/CountryWarTitle'):GetComponent('Image')
    title1.gameObject:SetActive(true)
    local title2 = UIMainTask.gameObject.transform:Find('Original/KingWarTitle').gameObject
	title2:SetActive(false)
	
	--title1.atlas = HelperL.GetAtlas('Athletics')
	--title1.spriteName = 'RWbiaoti01_1'

	local prefab = HotResManager.ReadUI('ui/Task3D/prefab/TrunkPart')
	UIMainTask.childgameObject =  GameObject.Instantiate(prefab,UIMainTask.gameObject.transform)
	UIMainTask.childgameObject.transform.localPosition = Vector3(0,-35,0)
	local childTrans = UIMainTask.childgameObject.transform

	UIMainTask.trunkChaptertaskLab = childTrans:Find('BG/Right/task'):GetComponent('Text')
	UIMainTask.trunkChaptertaskLab.text=GetGameText(luaID,11)
	UIMainTask.trunkChapterNameLab = childTrans:Find('L/ChapterName/Label'):GetComponent('Text')
	UIMainTask.trunkChapterDesLab = childTrans:Find('L/Content'):GetComponent('Text')
	UIMainTask.trunkProgress = childTrans:Find('L/ProgressBar'):GetComponent('Slider')
	UIMainTask.trunkProgressLab = childTrans:Find('L/ProgressBar/Label'):GetComponent('Text')
	UIMainTask.trunkRunBtn = childTrans:Find('R/RunBtn').gameObject
	UIMainTask.trunktRunLab = childTrans:Find('R/RunBtn/Label'):GetComponent('Text')
	UIMainTask.trunkNameLab = childTrans:Find('R/TaskName/Label'):GetComponent('Text')
	UIMainTask.trunkDesLab = childTrans:Find('R/TaskDes/Label'):GetComponent('Text')
	UIMainTask.trunkTrackLab = childTrans:Find('R/Track/Label'):GetComponent('Text')
	UIMainTask.trunkPrizeNode = childTrans:Find('R/Prize/ListNode').gameObject
	UIMainTask.levelLimit = childTrans:Find('R/LevelLimit'):GetComponent('Text')
	--预制体字
	local RObj = childTrans:Find('R').gameObject.transform
	RObj:Find('TaskDes'):GetComponent('Text').text = GetGameText(luaID,7)
	RObj:Find('Track'):GetComponent('Text').text = GetGameText(luaID,8)
	RObj:Find('Prize'):GetComponent('Text').text = GetGameText(luaID,9)
	UIMainTask.trunkRunBtn.transform:Find('Label'):GetComponent('Text').text = GetGameText(luaID,10)
	UIMainTask.backBtn.gameObject:GetComponent('Button').onClick:AddListenerEx(function()
		UIMainTask.Close()
	end)
	UIMainTask.OnEnable( )
	return true
end

--关闭时候处理(由父节点调用)
function UIMainTask.Close()
	UIMainTask.UnRegisterEvent()
	local self = UIMainTask
    self:CloseSelf()
end
--注册事件
function UIMainTask.RegisterEvent()

end
--注销事件
function UIMainTask.UnRegisterEvent()
	
end

--------------------------------------------------------------------
-- 窗口销毁
--------------------------------------------------------------------
function UIMainTask:OnDestroy()

end

--激活
function UIMainTask.OnEnable( )
	UIMainTask.UpdateView()
end
--隐藏
function UIMainTask.OnDisable()

end
function UIMainTask:SetActive(isTrue)
	if isTrue then
        UIMainTask.RegisterEvent()
    else
		UIMainTask.UnRegisterEvent()
    end
    UIMainTask.gameObject:SetActive(isTrue)
end
function UIMainTask.UpdateView()
	UIMainTask:SetActive(true)
	local trunkTask = nil
	local hero = EntityModule.hero
	local taskPart = hero.heroTaskLC
	for k,v in pairs(taskPart.idToItem) do
		if v.status ~= TASK_STATUS.TASK_STATUS_COMPLETED
		and v.taskScheme.Style == ECTaskStyle.Trunk then
			trunkTask = v
		end
	end

	if trunkTask == nil then
		local taskScheme = nil
		local taskSerial = HeroDataManager:GetLogicData(LOGIC_DATA.DATA_TRUNK_TASK_CUR_SERIAL)
		local curTask = Schemes.Task:Get(taskSerial, 1)
		if not curTask then
			curTask = Schemes.Task:Get(1, 1)
			if curTask == nil then error("task配置错误ID="..1) return end
			taskScheme = curTask
		else
			local nextTaskID = curTask.FollowTask
			if nextTaskID == 0 then
				HelperL.ShowMessage(TipType.FlowText, GetGameText(luaID, 1))
				taskScheme = curTask
				--已经完成所有主线
			else
				local FollowTask = Schemes.Task:Get(nextTaskID, 1)
				if FollowTask == nil then error("task配置错误ID="..nextTaskID) return end
				taskScheme = FollowTask
			end
		end
		UIMainTask.trunkNameLab.text = taskScheme.Name
		UIMainTask.trunkDesLab.text = taskScheme.Describe
		UIMainTask.trunkChapterNameLab.text = taskScheme.ChapterName
		UIMainTask.trunkChapterDesLab.text = taskScheme.ChapterDescribe
		UIMainTask.trunkProgress.value = taskScheme.ChapterOrder / taskScheme.ChapterAll
		UIMainTask.trunkProgressLab.text = string.format('%d/%d',taskScheme.ChapterOrder, taskScheme.ChapterAll)
		if UIMainTask.trunkPrize ~= nil then GameObject.Destroy(UIMainTask.trunkPrize.gameObject) end
		--UIMainTask.trunkPrize = UICommonPrize.New(taskScheme.PrizeID)
		--UIMainTask.trunkPrize:AddToParent(UIMainTask.trunkPrizeNode)
		UIMainTask.trunkRunBtn.gameObject:SetActive(false)
		local actorLevel = EntityModule.hero.propertyLC:GetProperty(CREATURE_FIELD.CREATURE_FIELD_LEVEL)
		UIMainTask.levelLimit.gameObject:SetActive(true)
		local minLv = taskScheme.MinLevel
		if actorLevel < minLv then
			UIMainTask.levelLimit.text = string.format(GetGameText(luaID, 2), minLv)
		else
			UIMainTask.levelLimit.text = GetGameText(luaID, 3)
		end
		return
	end
	UIMainTask.trunkNameLab.text = trunkTask.taskScheme.Name
	UIMainTask.trunkDesLab.text = trunkTask.taskScheme.Describe
	UIMainTask.trunkChapterNameLab.text = trunkTask.taskScheme.ChapterName
	UIMainTask.trunkChapterDesLab.text = trunkTask.taskScheme.ChapterDescribe
	UIMainTask.trunkProgress.value = trunkTask.taskScheme.ChapterOrder / trunkTask.taskScheme.ChapterAll
	UIMainTask.trunkProgressLab.text = string.format('%d/%d',trunkTask.taskScheme.ChapterOrder, trunkTask.taskScheme.ChapterAll)
	if UIMainTask.trunkPrize ~= nil then GameObject.Destroy(UIMainTask.trunkPrize.gameObject) end
	--UIMainTask.trunkPrize = UICommonPrize.New(trunkTask.taskScheme.PrizeID)
	--UIMainTask.trunkPrize:AddToParent(UIMainTask.trunkPrizeNode)

	local status = trunkTask:GetClientStatus()
	local taskItem = taskPart.GetItem(trunkTask.branchID, trunkTask.taskID)
	if status == ECTaskStatus.CanComplete then
		UIMainTask.trunktRunLab.text = GetGameText(luaID, 4)
		UIMainTask.trunkTrackLab.text = trunkTask.taskScheme.Track3
		UIMainTask.trunkRunBtn.gameObject:GetComponent('Button').onClick:AddListenerEx(function()
			--taskItem:WantToTurnInTask()
			--local taskPart = hero.heroTaskLC
			taskPart.SendLuaTurnInTask(trunkTask.branchID, trunkTask.taskID,trunkTask.step,UIMainTask.FinishTaskCallBack)
			UIMainTask.Close()
		end)
	elseif status == ECTaskStatus.Doing then
		UIMainTask.trunktRunLab.text = GetGameText(luaID, 5)
		UIMainTask.trunkTrackLab.text = trunkTask.taskScheme.Track2
		UIMainTask.trunkRunBtn.gameObject:GetComponent('Button').onClick:AddListenerEx(function()
			local mapID = taskPart.GetMapID(trunkTask.taskScheme.WayMapID)
			local tagMapID = taskPart.CalcMapID(0, mapID)
			if tagMapID ~= 0 then
				local autoFight = IsTaskNavigationNeedAutoFight(trunkTask.taskScheme)
				local doNPC = taskPart.GetDoNPC(trunkTask.taskScheme)
				doNPC = tonumber(doNPC)
				if doNPC ~= 0 then
					EntityModule.hero.navigationLG:NavigateToNPC(doNPC)
				else
					local doMapPoint = HelperL.PBString2Vector3(trunkTask.taskScheme.WayCoordinate)
					EntityModule.hero.navigationLG:SetWorldDestination(mapID, doMapPoint, autoFight,_,_,trunkTask.taskID * 256 + trunkTask.branchID)
				end
				UIMainTask.Close()
			end
		end)
	elseif status == ECTaskStatus.CanAccept then
		UIMainTask.trunktRunLab.text = GetGameText(luaID, 6)
		UIMainTask.trunkTrackLab.text = trunkTask.taskScheme.Track1
		UIMainTask.trunkRunBtn.gameObject:GetComponent('Button').onClick:AddListenerEx(function()
			taskItem:WantToAccept()
			UIMainTask.Close()
		end)
	end

end

function UIMainTask.FinishTaskCallBack(result)
 if result == RESULT_CODE.RESULT_COMMON_SUCCEED then

    else
        HelperL.ShowMessage(TipType.FlowText, ResultCode:GetResultCodeStr(result, content))
    end
	
end

return UIMainTask
