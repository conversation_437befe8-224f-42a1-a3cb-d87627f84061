--[[
********************************************************************
    created:    2023/08/29
    author :    李锦剑
    purpose:    命格界面
*********************************************************************
--]]

local luaID = ('UIFate')

--背景框
local kuang_bg = {
    'kuang_1',
    'kuang_2',
    'kuang_3',
    'kuang_4',
    'kuang_5',
}

---@class UIFate:UIWndBase
local m = {}

--------------------------------------------------------------------
-- 订阅事件列表
--------------------------------------------------------------------
function m.GetOpenEventList()
    return {
        [EventID.LogicDataChange] = m.UpdateView,
        [EventID.OnHeroPropChange] = m.UpdateView,
        [EventID.OnGoodsCreate] = m.UpdateView,
        [EventID.MSG_SKEP_USEGOODS] = m.UpdateView,
    }
end

--------------------------------------------------------------------
-- 初始化
--------------------------------------------------------------------
function m.OnCreate()
    m.attributeList = {}
    m.objList.FateHint.gameObject:SetActive(false)
    ---@type FateEquipItem[]
    m.FateItem_List = {}
    ---@type FateEquipItem[]
    m.FatePlayerSlot_List = {}
    m.RegisterClickEvent()
    return true
end

--------------------------------------------------------------------
-- 打开时
--------------------------------------------------------------------
function m.OnOpen()
    m.UpdateView()
end

--------------------------------------------------------------------
-- 关闭时
--------------------------------------------------------------------
function m.OnClose()

end

--------------------------------------------------------------------
--注册点击事件
--------------------------------------------------------------------
function m.RegisterClickEvent()
    m.objList.Btn_Close.onClick:AddListenerEx(function()
        m:CloseSelf()
    end)
    m.objList.Btn_Quit.onClick:AddListenerEx(function()
        m.objList.FateHint.gameObject:SetActive(false)
    end)
    m.objList.Btn_QuickCollect.onClick:AddListenerEx(function()
        m.QuickCollect()
    end)
end

--------------------------------------------------------------------
-- 创建菌落框
--------------------------------------------------------------------
function m.CreateFateItem(index, parent)
    ---@class FateEquipItem
    local item = {}
    item.index = index
    item.com = m:CreateSubItem(parent, m.objList.Item_Fate)
    item.com.Btn_Select.onClick:AddListenerEx(function()
        m.ShowFateHint(item.entity)
    end)
    --- 刷新
    ---@param entity table|nil @实体数据
    item.UpdateData = function(entity, isShowName)
        item.entity = entity
        item.isShowName = isShowName
        if not entity then
            item.com.Img_Icon.gameObject:SetActive(false)
            item.com.Txt_FateBuff.gameObject:SetActive(false)
        else
            item.com.Img_Icon.gameObject:SetActive(true)
            item.com.Txt_FateBuff.gameObject:SetActive(true)
            item.cfg = Schemes:GetGoodsConfig(entity:GetProperty(ENTITY_FIELD.ENTITY_FIELD_ID))
            if item.cfg then
                AtlasManager:AsyncGetSprite(item.cfg.IconID, item.com.Img_Icon)
                local color = HelperL.GetColorByQuality(item.cfg.QualityLevel)
                if isShowName then
                    item.com.Txt_FateBuff.text = string.format("<color=#%s>%s</color>", color, item.cfg.GoodsName)
                else
                    local data = ActorProp.GetEquipSmeltLevel(entity)
                    local lv = data.level .. CommonTextID.LEVEL
                    item.com.Txt_FateBuff.text = string.format(
                        "<color=#%s>%s %s</color>",
                        color,
                        m.GetActorPropDesc(item.entity),
                        lv
                    )
                end
            end
        end
    end
    return item
end

--------------------------------------------------------------------
-- 获取属性描述
--------------------------------------------------------------------
function m.GetActorPropDesc(entity)
    local str = ''
    if entity then
        local list = m.GetEquipSmeltProperty(entity)
        for _, v in ipairs(list) do
            if str == '' then
                str = v.value .. '\n' .. v.desc
            else
                str = str .. '\n' .. v.value .. '\n' .. v.desc
            end
        end
    end
    return str
end

--------------------------------------------------------------------
-- 更新界面
--------------------------------------------------------------------
function m.UpdateView()
    -- 菌落临时篮子（狩猎到且未拾取的、临时的）
    ---@type SkepBase
    local skep = SkepModule:GetSkepByID(SKEPID.SKEPID_GEMPACK)
    for i = 1, 18, 1 do
        if not m.FateItem_List[i] then
            m.FateItem_List[i] = m.CreateFateItem(i, m.objList.Grid_FateItem)
        end
        m.FateItem_List[i].UpdateData(EntityModule:GetEntity(skep[i - 1]), true)
    end

    -- 寻找已出战的实例
    ---@type SkepBase
    local skep2 = SkepModule.GetFateEquipmentSkep()
    for i = 1, 8, 1 do
        if not m.FatePlayerSlot_List[i] then
            m.FatePlayerSlot_List[i] = m.CreateFateItem(i, m.objList.Item_FatePlayerSlot.transform:GetChild(i - 1))
        end
        m.FatePlayerSlot_List[i].UpdateData(EntityModule:GetEntity(skep2[i - 1]), false)
    end
end

--------------------------------------------------------------------
--显示命魂Tisp
--------------------------------------------------------------------
function m.ShowFateHint(entity)
    m.objList.FateHint.gameObject:SetActive(false)
    if not entity then return end
    local cfg = Schemes:GetGoodsConfig(entity:GetProperty(ENTITY_FIELD.ENTITY_FIELD_ID))
    if not cfg then return end
    AtlasManager:AsyncGetSprite(cfg.IconID, m.objList.Img_FateIcon)
    AtlasManager:AsyncGetSprite(kuang_bg[cfg.QualityLevel] or kuang_bg[1], m.objList.Img_FateBg)
    local data = ActorProp.GetEquipSmeltLevel(entity)
    local lv = data.level .. CommonTextID.LEVEL
    local color = HelperL.GetColorByQuality(cfg.QualityLevel)
    local content = string.format("<color=#%s>%s</color>", color, cfg.GoodsName)
    content = content .. '\n' .. string.format(GetGameText(luaID, 14), lv)
    local list = m.GetEquipSmeltProperty(entity)
    for i, v in ipairs(list) do
        content = content .. '\n' .. string.format('%s   <color=#FFA500>%s</color>', v.desc, v.value)
    end
    local exp = entity:GetProperty(EQUIPMENT_FIELD.EQUIPMENT_FIELD_SMELTEXP)    --强化经验
    local quality = entity:GetProperty(EQUIPMENT_FIELD.EQUIPMENT_FIELD_QUALITY) -- 品质
    local starNum = entity:GetProperty(EQUIPMENT_FIELD.EQUIPMENT_FIELD_STARNUM) -- 星数
    local smeltCfg = Schemes.EquipSmelt:Get(cfg.SmeltID, quality, starNum)      -- 当前锻造配置
    if not smeltCfg then return end
    content = content .. '\n'
        .. string.format(GetGameText(luaID, 15), data.exp, data.maxExp)
    m.objList.Txt_Attribute.text = content
    m.objList.FateHint.gameObject:SetActive(true)
end

--------------------------------------------------------------------
---获取属性描述
---@param entity any
--------------------------------------------------------------------
function m.GetEquipSmeltProperty(entity)
    local list = {}
    if entity then
        local data = ActorProp.GetEquipSmeltLevel(entity)
        local id = entity:GetProperty(ENTITY_FIELD.ENTITY_FIELD_ID)                -- 物品Id
        local cfg = Schemes.Equipment:Get(id)                                      -- 物品配置
        if cfg then
            local quality = 0                                                      -- 品质
            local starNum = data.level - 1                                         -- 星数
            local smeltCfg = Schemes.EquipSmelt:Get(cfg.SmeltID, quality, starNum) -- 当前锻造配置
            if smeltCfg then
                local eEffect, _name, _value
                for i, effectID in ipairs(smeltCfg.EquipEffectID) do
                    eEffect = Schemes.EquipEffect:Get(math.floor(effectID / 10000))
                    if eEffect then
                        _name = GetAttributeTypeDesc(eEffect.EffectType)
                        _value = ActorProp.GetAttributeShowText(eEffect.EffectType, eEffect.EffectParam)

                        table.insert(list, { desc = _name, value = _value, })
                    end
                end
            end
        end
    end
    return list
end

--------------------------------------------------------------------
--- 一键拾取
--------------------------------------------------------------------
function m.QuickCollect()
    -- 灵珠临时篮子（狩猎到且未拾取的、临时的）
    local skep = SkepModule:GetSkepByID(SKEPID.SKEPID_GEMPACK)
    local num = 0
    -- 准备请求所需参数
    local getGoods = ""
    for i = 0, skep.indexMaxsize do
        local entity = EntityModule:GetEntity(skep[i]) -- 灵珠临时篮子中，对应实力的Id
        if entity then
            local entityUID = entity.uid
            local goodsId = entity:GetProperty(ENTITY_FIELD.ENTITY_FIELD_ID)
            local goodsNum = entity:GetProperty(GOODS_FIELD.GOODS_FIELD_QTY)
            local goodsStr = string.format("%sx%sx%s", entityUID, goodsId, goodsNum)
            if goodsStr == "" then
                getGoods = goodsStr
            else
                getGoods = getGoods .. "+" .. goodsStr
            end
            num = num + 1
        end
    end
    if num == 0 then
        HelperL.ShowMessage(TipType.FlowText, GetGameText(luaID, 13))
        return
    end
    local genre = EntityModule.hero:GetProperty(PLAYER_FIELD.PLAYER_FIELD_GENRE)
    -- 发送请求
    local request = string.format("LuaRequestGemGet?getGoods=%s&genre=%s", getGoods, genre)
    LuaModule.RunLuaRequest(request, m.QuickCollectRequestCallback)
end

--------------------------------------------------------------------
--- 一键拾取请求回调
--------------------------------------------------------------------
function m.QuickCollectRequestCallback(result, content)
    if result == RESULT_CODE_BASE.RESULT_COMMON_SUCCEED[1] then
        m.UpdateView()
    else
        HelperL.ShowMessage(TipType.FlowText, ResultCode:GetResultCodeStr(result))
    end
end

return m
