﻿local luaID = ('UIWelfareCard')

---@class UIWelfareCard: UIWndBase
local m = {}

local nameList = {"艾伦卡", "卡基卡", "莉娜卡", "风之卡","毒之卡","光之卡","地之卡"}
-- 订阅事件列表
function m.GetOpenEventList()
    return {
        [EventID.LogicDataChange] = m.UpdateView,
    }
end

--------------------------------------------------------------------
-- 初始化
--------------------------------------------------------------------
function m.OnCreate()
    m.invsetConfig = Schemes.SevenDaysInvest:Get(3)
    m.adID2 = Schemes.RechargeCard:Get(m.invsetConfig.CardID).Description
    m.NeedLevelList = m.invsetConfig.NeedLevel
    m.InvestItemList = {}
    for i = 1, 7, 1 do
        m.InvestItemList[i] = m.CreateInvest(i)
    end

    m.comResInfo = ComResInfo.New()
    m.comResInfo.BindView(m.objList.Com_Res)

    m.RegisterClickEvent()
    return true
end

-- 窗口开启
function m.OnOpen()
    m.UpdateView()
end

-- 更新界面
function m.UpdateView()
    for i, v in ipairs(m.InvestItemList) do
        v.UpdateView()
    end

    m.objList.Btn_Buy.gameObject:SetActive(false)
    m.objList.Btn_Buyed.gameObject:SetActive(false)

    local investTimes = HeroDataManager:GetLogicData(m.invsetConfig.TimeSaveID)
    if investTimes == 0 then
        local card = Schemes.RechargeCard:Get(m.invsetConfig.CardID)
        local price = string.format(GetGameText(luaID, 1), card.FirstRMB / 100)
        m.objList.Btn_Buy.gameObject:SetActive(true)
        m.objList.Txt_Buy.text = price
    else
        m.objList.Btn_Buyed.gameObject:SetActive(true)
    end
end

-- 请求扣除元宝(opType:1.投资; 2.领取奖励)
function m.InvestOperation(id, opType)
    if not id or id == 0 then return end
    LuaModule.RunLuaRequest(string.format('LuaRequestSevenDaysInvest?id=%d&opType=%d', id, opType),
        function(resultCode, content)
            if resultCode ~= RESULT_CODE.RESULT_COMMON_SUCCEED then
                HelperL.ShowMessage(TipType.FlowText, ResultCode:GetResultCodeStr(resultCode, content))
            else
                m.UpdateView()
            end
        end
    )
end

-- 注册点击事件
function m.RegisterClickEvent()
    m.objList.Btn_Close.onClick:AddListenerEx(function()
        m:CloseSelf()
    end)

    -------------购买-------------
    m.objList.Btn_Buy.onClick:AddListenerEx(function()
        HelperL.Recharge(m.invsetConfig.CardID)
    end)
end

-- 创建奖励列表
function m.CreateInvest(index)
    local item = {}
    local prizeID = m.invsetConfig.PrizeID[index]
    item.index = index
    item.objList = m:CreateSubItem(m.objList.Grid_Invest, m.objList.Item_Item)
    item.objList.Txt_Name.text = nameList[index]--m.NeedLevelList[index]
    --item.objList.Img_Red.gameObject:SetActive(false)
    m.CreateSingleGoods(item.objList.Award, prizeID)
    item.objList.Btn_Get.onClick:AddListenerEx(function()
        m.InvestOperation(m.invsetConfig.ID, 2)
    end)
    item.UpdateView = function()
        local state = m.GetState(m.invsetConfig, item.index)
        if state == 3 then
            item.objList.Btn_Get.gameObject:SetActive(false)
            item.objList.Btn_Grey.gameObject:SetActive(true)
            item.objList.Txt_Grey.text = "已领取"
        else
            if state == 2 then
                item.objList.Btn_Get.gameObject:SetActive(false)
                item.objList.Btn_Grey.gameObject:SetActive(true)
                item.objList.Txt_Grey.text = "领取"
            else
                item.objList.Btn_Get.gameObject:SetActive(true)
                item.objList.Btn_Grey.gameObject:SetActive(false)
                --item.objList.Img_Red.gameObject:SetActive(true)
            end
        end
    end
    return item
end

-- 获取领取状态，1可领取，2不可领取，3已领取
function m.GetState(cfg, index)
    if HeroDataManager:GetLogicBit(cfg.GetSaveID, cfg.StartIndex + index - 1) ~= 0 then
        return 3
    end
    local curDay = m.GetCurDay(cfg)
    if curDay < index then
        return 2
    end
    return 1
end

-- 获取天数
function m.GetCurDay(config)
    local curTime = HelperL.GetServerTime()
    if not config then return 0 end
    local investTimes = HeroDataManager:GetLogicData(config.TimeSaveID)
    if investTimes == 0 then return 0 end
    local curDay = HelperL.CalculationIntervalDays(investTimes, curTime)
    if curDay > 7 then
        curDay = 7
    end
    if curDay <= 0 then
        curDay = 1
    end
    return curDay
end

-- 创建奖励商品
function m.CreateSingleGoods(parent, prizeID)
    local prizeList = Schemes.PrizeTable:GetGoodsList(prizeID)
    if not prizeList then return end
    for i = 1, #prizeList do
        local item = _GAddSlotItem(parent)
        item:SetItemID(prizeList[i].id)
        item:SetCount(prizeList[i].num)
    end    
end
return m
