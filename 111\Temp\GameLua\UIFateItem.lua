---
--- Generated by <PERSON><PERSON><PERSON>(https://github.com/EmmyLua)
--- Created by Administrator.
--- DateTime: 2023/7/22 16:47
--- 【菌落界面】菌落Item
---

local UIItemBase = require('Base_UI_Item')
--背景框
local kuang_bg = {
    'kuang_1',
    'kuang_2',
    'kuang_3',
    'kuang_4',
    'kuang_5',
}

---@class UIFateItem:UIItemBase
---@field _base UIItemBase
local this = class(UIItemBase)

---@private
function this:_init(go)
    self:Init(go)
end

function this:Init(go)
    self:baseInit(go)
    self:SetLuaFileName("UIFate")
end

--- 重置Item
function this:Reset(entity)
    -- 实力Id相同，无需刷新
    if self.entity ~= nil and entity ~= nil and self.entity.uid == entity.uid then
        self.entity = nil
        return
    end
    -- 物品Id
    local goodsId, goodsCfg
    local quality
    if entity then
        self.entity = entity
        goodsId = entity:GetProperty(ENTITY_FIELD.ENTITY_FIELD_ID)
        goodsCfg = Schemes:GetGoodsConfig(goodsId)
        quality = goodsCfg.QualityLevel
    end
    -- 图片
    self:SetSprite(goodsCfg)
    -- 名字
    self:SetName(goodsCfg)
    -- 背景
    self:SetBackgroundImage(quality)
end

---@private
function this:IsEmpty()
    local entity = self.entity
    local goodsId, goodsCfg
    if entity ~= nil then
        goodsId = entity:GetProperty(ENTITY_FIELD.ENTITY_FIELD_ID)
        goodsCfg = Schemes:GetGoodsConfig(goodsId)
    end
    return self.entity == nil or goodsCfg == nil
end

---@private
function this:SetSprite(goodsCfg)
    self.objList.Img_Icon.gameObject:SetActive(false)
    if goodsCfg then
        AtlasManager:AsyncGetSprite(goodsCfg.IconID, self.objList.Img_Icon, false, function (sprite, image, param)
            image.gameObject:SetActive(sprite ~= nil)
        end)
    end
end

---@private
function this:SetName(goodsCfg)
    self.objList.TMP_FateName.gameObject:SetActive(false)
    if goodsCfg then
        local str = goodsCfg.GoodsName
        self.objList.Txt_FateBuff.text = StringL.SetColoredText(str, HelperL.GetColorByQuality(goodsCfg.QualityLevel))
    end
    self.objList.Img_FateBuff.gameObject:SetActive(goodsCfg ~= nil)
end

--- 设置背景图片
function this:SetBackgroundImage(quality)
    AtlasManager:AsyncGetSprite(kuang_bg[quality or 1] or kuang_bg[1], self.objList.Img_Bg, false, function (sprite, image, param)
        image.gameObject:SetActive(sprite ~= nil)
    end)
end

---@private
function this:AddListener()
    local btn = self.gameObject:GetComponent("Button")
    self:AddClickEvent(btn, function()
        if self.entity then
            EventManager:Fire(EventID.ShowFateHint, self.entity)
        end
    end)
end

--- 销毁
function this:OnDestroy()

end

return this
