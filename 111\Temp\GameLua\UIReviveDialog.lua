--[[
********************************************************************
    created:    2024/05/30
    author :    李锦剑
    purpose:    复活界面
*********************************************************************
--]]

local luaID = 'UIReviveDialog'

--倒计时时间
local countDownTime = 10

---@class UIReviveDialog:UIWndBase
local m = {}

--------------------------------------------------------------------
-- 订阅事件列表
--------------------------------------------------------------------
-- function m:GetOpenEventList()
-- 	return {

-- 	}
-- end

--------------------------------------------------------------------
-- 创建窗口
--------------------------------------------------------------------
function m:OnCreate()
	m.RegisterClickEvent()
	return true
end

--------------------------------------------------------------------
-- 窗口开启
--------------------------------------------------------------------
function m:OnOpen()
	-- m.objList.Txt_Revive.text = ''
	m.TimeNum = countDownTime
	m.objList.Txt_time.text = m.TimeNum
	m.timer = Timer.New(m.UpdateView, 1, countDownTime, true, nil, m.OnCancel)
	m.timer:Start()
	SoundManager:PlaySound(SoundID.ReviveDialog)
	LuaToCshapeManager.Instance:PauseOrResumeBattle(0)
end

--------------------------------------------------------------------
--注册点击事件
--------------------------------------------------------------------
function m.RegisterClickEvent()
	m.objList.Btn_Close.onClick:AddListenerEx(m.OnCancel)
	m.objList.Btn_Cancel.onClick:AddListenerEx(m.OnCancel)
	m.objList.Btn_AD.onClick:AddListenerEx(m.Revive)
end

--------------------------------------------------------------------
-- 更新界面
--------------------------------------------------------------------
function m.UpdateView()
	m.TimeNum = m.TimeNum - 1
	m.objList.Txt_time.text = m.TimeNum
end

--------------------------------------------------------------------
-- 复活
--------------------------------------------------------------------
function m.Revive()
	m.timer:Stop()
	AdvertisementManager.ShowRewardAd(GameAdvertisementID.ReviveGame, function(bool)
		if bool then
			LuaToCshapeManager.Instance:ActorRevive()
		else
			LuaToCshapeManager.Instance:SettleAccounts(false)
		end
		m.OnCloseUI()
	end, true)
end

--------------------------------------------------------------------
-- 取消复活
--------------------------------------------------------------------
function m.OnCancel()
	LuaToCshapeManager.Instance:SettleAccounts(false)
	m.OnCloseUI()
end

--------------------------------------------------------------------
-- 关闭界面
--------------------------------------------------------------------
function m.OnCloseUI()
	if m.timer then
		m.timer:Stop()
		m.timer = nil
	end
	m:CloseSelf()
	LuaToCshapeManager.Instance:PauseOrResumeBattle(1)
end

return m
