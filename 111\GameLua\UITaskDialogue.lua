--[[
********************************************************************
    created:    2024/01/15
    author :    李锦剑
    purpose:    任务对话
*********************************************************************
--]]

-- local luaID = ('UITaskDialogue')
--玩家图标
local playerIcon = '[PLAYER_ICON]'
--玩家名字
local playerName = '[PLAYER_NAME]'
--特殊对话
local specialDialogue = -1

local m = {}
--------------------------------------------------------------------
-- 订阅事件列表
--------------------------------------------------------------------
-- function m.GetOpenEventList()
-- 	return {

-- 	}
-- end

--------------------------------------------------------------------
-- 初始化
--------------------------------------------------------------------
function m.OnCreate()
	m.RegisterClickEvent()
	m.objList.Img_HeadPortrait:SetNativeSize()
	return true
end

--------------------------------------------------------------------
---窗口开启
--------------------------------------------------------------------
function m:OnOpen(taskID, callback, isWayDialogue)
	local wayDialogue = ''
	local task = Schemes.Task:Get(taskID)
	if not task then
		error('---------任务列表为空----------')
		m.CloseUI()
		return
	end

	m.taskID = taskID

	wayDialogue = task.GiveDialogue
	--if taskID == 1 then
	--	wayDialogue = task.WayDialogue
	--end

	--对话内容改成WayDialogue描述内容
	if isWayDialogue then
		wayDialogue = task.WayDialogue
	end
	m.callback = callback
	m.progress = 1
	m.taskName = task.Name
	--对话内容列表
	m.dialogueContent = {}
	local list = HelperL.Split(wayDialogue, ';')
	for i, v in ipairs(list) do
		table.insert(m.dialogueContent, m.ParsingContent(v))
	end
	m.objList.Img_HeadPortrait:SetNativeSize()

	m.UpdateView()
end

--------------------------------------------------------------------
--注册点击事件
--------------------------------------------------------------------
function m.RegisterClickEvent()
	m.objList.Btn_GoOn.onClick:AddListenerEx(m.OnGoOnDialogue)
	m.objList.Btn_Close.onClick:AddListenerEx(m.CloseUI)
end

--------------------------------------------------------------------
--继续对话
--------------------------------------------------------------------
function m.OnGoOnDialogue()
	m.progress = m.progress + 1
	if m.progress > #m.dialogueContent then
		m.CloseUI()
	else
		m.UpdateView()
	end
end

--------------------------------------------------------------------
--解析内容
--------------------------------------------------------------------
function m.ParsingContent(str)
	local list = HelperL.Split(str, '|')
	local icon = list[2]
	local name = list[3]
	if not icon or icon == playerIcon then
		-- local cfg = Schemes.Equipment:Get(HelperL.GetWeaponEquipID())
		-- if not cfg then
		-- 	cfg = Schemes.Equipment:Get(300020)
		-- end
		-- icon = cfg.IconID
		icon = 'PLAYER_ICON_001'
	end
	if not name or name == playerName then
		name = EntityModule.hero.name
	end
	local data = {}
	data.content = list[1]
	data.icon = icon
	data.name = name
	return data
end

--------------------------------------------------------------------
-- 更新界面
--------------------------------------------------------------------
function m.UpdateView()
	local data = m.dialogueContent[m.progress]
	m.objList.Txt_Content.text = data.content
	-- m.objList.Img_HeadPortrait.sprite = AtlasManager:GetSprite(data.icon)
	-- m.objList.Img_HeadPortrait:SetNativeSize()
	print("data.icon ===== =" , data.icon)
	AtlasManager:AsyncGetSprite(data.icon, m.objList.Img_HeadPortrait)
	m.objList.Img_HeadPortrait:SetNativeSize()
	m.objList.Txt_Name.text = data.name .. " :"
	m.objList.Txt_TaskName.text = m.taskName
    m.objList.Btn_Close.gameObject:SetActive(false)

	--获取大小
	local width = m.objList.Txt_Name.preferredWidth
	local height = m.objList.Txt_Name.preferredHeight
	--local size = m.objList.Img_NameBg.transform:GetRectTransform().sizeDelta
	--m.objList.Img_NameBg.transform:GetRectTransform().sizeDelta = Vector2(width + 100, size.y)
end

--------------------------------------------------------------------
-- 更新界面
--------------------------------------------------------------------
function m.CloseUI()
	m:CloseSelf()
	if type(m.callback) == "function" then
		m.callback()
	end
end

return m
