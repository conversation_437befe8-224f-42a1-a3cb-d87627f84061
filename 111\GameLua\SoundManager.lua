--[[
********************************************************************
    created:	
    author :	
    purpose:    全局声音管理
*********************************************************************
--]]

--声音ID
SoundID = {
	---------------音乐-----------------------

	Login                  = 1, --音乐--登入界面
	Main                   = 1, --音乐--主界面

	---------------音效-----------------------

	CommonButtonSound      = 8000, --音效--按钮通用
	CommonCloseButtonSound = 8001, --音效--关闭按钮通用
	Upgrade                = 8002, --音效--升级升星成功
	GetAewarded            = 8003, --音效--获得奖励（包括十连抽）
	Success                = 8004, --音效--胜利
	Failure                = 8005, --音效--失败
	ReviveDialog           = 8006, --音效--复活界面
	Synthesis              = 8007, --音效--战斗背包装备合成
	Drag                   = 8008, --音效--战斗背包装备取放
	RefreshSkill           = 8009, --音效--战斗刷新3选1
	SkillUI                  = 8010, --音效--战斗弹出3选1界面
	AddGold                = 8011, --音效--战斗增加金币
}

-- 全局声音管理
---@class SoundManager
local m = {}
SoundManager = m
m.soundCache = {}

-- 初始化
function m.InitSoundElement()
	local pivot = GameObject.New()
	m.soundElementPivot = pivot
	m.soundElements = {}
	pivot.name = 'SoundElement'
	--声音开关状态--0开--1关
	m.xmSoundValue = PlayerPrefs.GetInt('xmSound', 0)
	--音效音量
	m.SoundEffect_Volume = PlayerPrefs.GetFloat("SoundEffect_Volume", 1.0)
	UnityEngine.Object.DontDestroyOnLoad(pivot)
	for i = 1, 20 do
		local soundObj = GameObject.New()
		soundObj.name = 'Sound' .. i
		soundObj.transform:SetParent(pivot.transform, false)
		local comp = soundObj:AddComponent(System.Type.GetType('UnityEngine.AudioSource, UnityEngine'))
		comp.mute = m.xmSoundValue == 1
		comp.loop = false
		comp.playOnAwake = false
		comp.volume = m.SoundEffect_Volume
		table.insert(m.soundElements, comp)
	end

	--音乐开关状态--0开--1关
	m.xmMusicValue = PlayerPrefs.GetInt('xmMusic', 0)
	--音乐音量
	m.Music_Volume = PlayerPrefs.GetFloat("Music_Volume", 1.0)
	local musicObj = GameObject.New()
	musicObj.name = 'Music'
	musicObj.transform:SetParent(pivot.transform, false)
	local musicComp = musicObj:AddComponent(System.Type.GetType('UnityEngine.AudioSource, UnityEngine'))
	musicComp.mute = m.xmMusicValue == 1
	musicComp.loop = true
	musicComp.playOnAwake = false
	musicComp.volume = m.Music_Volume
	m.musicComp = musicComp
end

--声音开关状态：0开  1关
function m:GetXmSoundValue()
	return m.xmSoundValue
end

--音乐开关状态：0开  1关
function m:GetXmMusicValue()
	return m.xmMusicValue
end

--设置声音开关：0开  1关
function m:SetXmSound()
	if m.xmSoundValue == 1 then
		m.xmSoundValue = 0
		PlayerPrefsManager:SetInt('xmSound', 0)
		for k, v in ipairs(m.soundElements) do
			v.mute = false
		end
	else
		m.xmSoundValue = 1
		PlayerPrefsManager:SetInt('xmSound', 1)
		for k, v in ipairs(m.soundElements) do
			v.mute = true
		end
	end
end

--设置音乐开关：0开  1关
function m:SetMusic()
	if m.xmMusicValue == 1 then
		m.xmMusicValue = 0
		PlayerPrefsManager:SetInt('xmMusic', 0)
		m.musicComp.mute = false
	else
		m.xmMusicValue = 1
		PlayerPrefsManager:SetInt('xmMusic', 1)
		m.musicComp.mute = true
	end
end

---设置--音乐音量
---@param value number
function m:SetVolume_Music(value)
	value = tonumber(value)
	if value == nil then
		return
	end
	m.musicComp.volume = value
	--保存音量
	PlayerPrefs.SetFloat("Music_Volume", value)
end

---设置--音效音量
---@param value number
function m:SetVolume_SoundEffect(value)
	value = tonumber(value)
	if value == nil then
		return
	end
	for k, v in ipairs(m.soundElements) do
		v.volume = value
	end
	--保存音量
	PlayerPrefs.SetFloat("SoundEffect_Volume", value)
end

---重置音乐
---@param on_off any
function m:ResetValue(on_off)
	m.xmSoundValue = on_off
	for k, v in ipairs(m.soundElements) do
		v.mute = on_off ~= 0
	end

	m.xmMusicValue = on_off
	m.musicComp.mute = on_off ~= 0

	PlayerPrefsManager:SetInt('xmSound', on_off)
	PlayerPrefsManager:SetInt('xmMusic', on_off)
end

-- 播放音效
function m:PlaySound(soundID, delayTime, loop)
	soundID = tonumber(soundID) or 0
	if soundID == 0 then return end
	local soundScheme = Schemes.Sound:Get(soundID)
	if soundScheme == nil or soundScheme.File == '0' then
		warn('PlaySound 未配置的声音ID ', soundID)
		return
	end

	local audioComp = nil
	for i, v in ipairs(m.soundElements) do
		if not v.isPlaying then
			audioComp = v
			break
		end
	end

	if audioComp == nil then
		warn('PlaySound 找不到可用音源')
		return
	end

	audioComp.time = 0
	audioComp.loop = loop == true
	-- audioComp.volume = soundScheme.Volume

	local param = {}
	param.path = "Assets/Temp/sound/" .. soundScheme.File
	param.delayTime = delayTime
	param.audioComp = audioComp
	ResMgr.LoadAudioClipAsync(param.path, function(clip, _param)
		if clip == nil then
			warn('PlaySound 配置声音文件找不到 ', soundID)
			return
		end

		_param.audioComp.clip = clip
		if _param.delayTime then
			_param.audioComp:PlayDelayed(_param.delayTime)
		else
			_param.audioComp:Play()
		end
	end, param)
end

-- 停止音效
function m:StopSound(soundID)
	if not soundID or soundID == 0 then return end

	local soundScheme = Schemes.Sound:Get(soundID)
	if soundScheme == nil then
		warn('PlaySound 未配置的声音ID ', soundID)
		return false
	end

	if soundScheme.File == '0' then return end
	local param = {}
	param.soundID = soundID
	param.path = "Assets/Temp/sound/" .. soundScheme.File
	ResMgr.LoadAudioClipAsync(param.path, function(clip, _param)
		local audioComp = nil
		for i, v in ipairs(m.soundElements) do
			if v.isPlaying then
				if v.clip == clip then
					audioComp = v
					break
				end
			end
		end

		if audioComp then
			audioComp:Stop()
		end
	end, param)
end

-- 播放音乐
function m:PlayMusic(soundID)
	if not m.musicComp or not soundID or soundID == 0 then return end
	if m.MusicSoundID == soundID then
		return
	end
	m.MusicSoundID = soundID
	local soundScheme = Schemes.Sound:Get(soundID)
	if soundScheme == nil then
		warn('PlayMusic 未配置的声音ID ', soundID)
		return
	end

	if soundScheme.File == '0' then return end

	-- m.musicComp.volume = soundScheme.Volume

	local param = {}
	param.soundID = soundID
	param.audioComp = m.musicComp
	param.path = "Assets/Temp/sound/" .. soundScheme.File
	print("------播放音乐----------", soundID, param.path)
	ResMgr.LoadAudioClipAsync(param.path, function(clip, _param)
		if clip == nil then
			warn('PlaySound 配置声音文件找不到 ', _param.soundID)
			return
		end
		_param.audioComp.clip = clip
		_param.audioComp:Play()
	end, param)
end

function m:StopMusic()
	m.musicComp:Pause()
end

-- C#播放音效
function m.CSharpPlaySound(soundID)
	m:PlaySound(soundID, 0, false)
end

-- C#播放音乐
function m.CSharpPlayMusic(soundID)
	m:PlayMusic(soundID)
end

-- C#暂停音乐
function m.CSharpPauseMusic()
	m.musicComp:Pause()
end

---设置--音乐音量
---@param value number
function m.SetVolumeMusic(value)
	value = tonumber(value)
	if value == nil then
		return
	end
	m.musicComp.volume = value
	--保存音量
	PlayerPrefs.SetFloat("Music_Volume", value)
end

---设置设置--音效音量
---@param value number
function m.SetVolumeSoundEffect(value)
	value = tonumber(value)
	if value == nil then
		return
	end
	for k, v in ipairs(m.soundElements) do
		v.volume = value
	end
	--保存音量
	PlayerPrefs.SetFloat("SoundEffect_Volume", value)
end

EventManager:Subscribe(EventID.GameInit, m.InitSoundElement)
