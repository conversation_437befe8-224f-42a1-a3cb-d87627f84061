--[[
********************************************************************
    created:	2023/10/18
    author :	李锦剑
    purpose:    概率信息
*********************************************************************
--]]

local luaID     = ('UIProbabilityInfo')

local colorList = {
    Color(1, 1, 1, 1),
    Color(0, 1, 0, 1),
    Color(0, 1, 1, 1),
    Color(1, 0, 1, 1),
    Color(1, 1, 0, 1),
    Color(1, 140 / 255, 0, 1),
    Color(1, 0, 0, 1),
}

local lvList    = {
    [100] = LOGIC_DATA.DATA_COUNTRYWORSHIP_TIMES,
    [200] = LOGIC_DATA.DATA_HOLIDAYMAKE_DAYCOUNT1,
    --[300] = LOGIC_DATA.DATA_LOTTERY_LEVEL,
}

local m         = {}

--------------------------------------------------------------------
-- 订阅事件列表
--------------------------------------------------------------------
function m:GetOpenEventList()
    return {
        [EventID.LogicDataChange] = m.UpdateView,
        [EventID.OnHeroPropChange] = m.UpdateView,
    }
end

--------------------------------------------------------------------
-- 初始化
--------------------------------------------------------------------
function m.OnCreate()
    m.selectLevel = 1
    ---@type Item_Info1[]
    m.itemList = {}
    m.RegisterClickEvent()
    return true
end

--------------------------------------------------------------------
-- 窗口开启
--------------------------------------------------------------------
function m:OnOpen(id)
    if id ~= m.lotteryLevelID then
        m.lotteryLevelID = id
        m.dataList = {}
        for i, v in ipairs(Schemes.LotteryLevel.items) do
            if v.ID >= id and v.ID <= id + 98 then
                table.insert(m.dataList, v)
            end
        end
    end
    local lv    = lvList[m.lotteryLevelID]
    local level = HeroDataManager:GetLogicWord(lv, 0)
    if level <= 0 then
        level = 1
    end
    m.OnClick(level)
end

--------------------------------------------------------------------
-- 更新界面
--------------------------------------------------------------------
function m.UpdateView()
    local lv                        = lvList[m.lotteryLevelID]
    -- local level                     = HeroDataManager:GetLogicWord(lv, 0)
    local exp                       = HeroDataManager:GetLogicWord(lv, 1)
    local cfg                       = Schemes.LotteryLevel:Get(m.selectLevel + m.lotteryLevelID)
    m.objList.Sld_ProgressBar.value = exp / cfg.LevelEXP
    m.objList.Sld_ProgressBar.gameObject:SetActive(cfg.LevelEXP ~= 0)
    m.objList.Txt_Exp.text   = string.format('%s/%s', exp, cfg.LevelEXP)
    m.objList.Txt_Level.text = string.format(GetGameText(luaID, 20), m.selectLevel)
end

--------------------------------------------------------------------
-- 箭头点击事件
--------------------------------------------------------------------
function m.OnClick(index)
    if index < 1 then index = 1 end
    if index > #m.dataList then index = #m.dataList end
    m.selectIndex = index
    --概率
    m.probabilityList = {}
    local descList = HelperL.Split(m.dataList[index].Desc, ';')
    for i = 5, #descList, 1 do
        table.insert(m.probabilityList, descList[i])
    end
    local len = #m.probabilityList
    local num = math.max(len, #m.itemList)
    for i = 1, num, 1 do
        if not m.itemList[i] then
            m.itemList[i] = m.CreateItem(i)
        end
        m.itemList[i].UpdateView(m.probabilityList[i])
        m.itemList[i].ShowSplit(len > 7 and i ~= len and i % 7 == 0)
    end
    m.selectLevel = index
    m.UpdateView()
end

--------------------------------------------------------------------
-- 创建信息栏
--------------------------------------------------------------------
function m.CreateItem(index)
    ---@class Item_Info1
    local item = {}
    item.index = index
    local num = index % 7
    if num == 0 then
        num = 7
    end
    item.com = m:CreateSubItem(m.objList.Grid_Content, m.objList.Item_Info)
    item.com.Txt_Name.text = GetGameText(luaID, num)
    item.com.Txt_Name.color = colorList[num]
    item.com.Img_Type.color = colorList[num]
    item.com.Txt_Probability.color = colorList[num]
    item.UpdateView = function(data)
        if data then
            item.com.Txt_Probability.text = HelperL.Round(data, 2) .. '%'
            item.com.gameObject:SetActive(true)
        else
            item.com.gameObject:SetActive(false)
        end
    end
    item.ShowSplit = function(bool)
        item.com.Img_Split.gameObject:SetActive(bool)
    end

    return item
end

--------------------------------------------------------------------
-- 注册点击事件
--------------------------------------------------------------------
function m.RegisterClickEvent()
    m.objList.Btn_Close.onClick:AddListenerEx(function()
        m:CloseSelf()
    end)
    m.objList.Btn_Arrows1.onClick:AddListenerEx(function()
        m.OnClick(m.selectIndex - 1)
    end)
    m.objList.Btn_Arrows2.onClick:AddListenerEx(function()
        m.OnClick(m.selectIndex + 1)
    end)
end

return m
