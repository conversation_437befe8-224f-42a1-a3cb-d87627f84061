-- 登录UI
local luaID = ('UIFunctionOpen')

local UIFunctionOpen = {}

-- 初始化
function UIFunctionOpen:OnCreate()
	self.objList.Txt_Goto.text = GetGameText(luaID, 2)
	local eventTrigger = self.objList.Img_Mask.gameObject:AddComponent(typeof(EventTrigger))
	eventTrigger:AddListener(EventTriggerType.PointerClick, function(pointerEventData) 
		self:OnClickHandle() 
	end)
	self.itemContainer = self.objList.MoveIcon.MoveContainer.transform
	self.completeIconDis = false
	self.bigVec = Vector3(1.2,1.2,1.2)
	self.smaVec = Vector3(1,1,1)
	self.funItemList = {}
	return true
end

function UIFunctionOpen:StartFunctionAnimation()
	self.objList.Img_Title.gameObject:SetActive(false)
	self.objList.Txt_Goto.gameObject:SetActive(false)
	self.objList.Img_Mask.gameObject:SetActive(false)
	self.objList.Center.gameObject:SetActive(false)

	local delayTime = TimerEx.New(self.PlayAnimation, 0.1, 1, self.funItemList[1])
	delayTime:Start()
end

function UIFunctionOpen.PlayAnimation(funItem)
	local self = UIFunctionOpen
	
	local endPos = funItem.focusObj.gameObject.transform.position
	local screenPos = RectTransformUtility.WorldToScreenPoint(UIManager:GetUICamera(), endPos)
	local result, localPoint = RectTransformUtility.ScreenPointToLocalPointInRectangle(self.itemContainer, screenPos, UIManager:GetUICamera(), nil)
	if result then
		tarAnchoredPosition = localPoint
	end
	funItem.objTrans:DOKill(true)
	funItem.onAniEndFunc = function()
		funItem.quence = DOTween.Sequence()
		funItem.quence:Append(funItem.objTrans:DOScale(self.bigVec,0.3):SetEase(TweeningEase.Linear, 1))
		funItem.quence:Append(funItem.objTrans:DOScale(self.smaVec,0.3):SetEase(TweeningEase.Linear, 1))
		funItem.quence:AppendInterval(0.2)
		funItem.quence:SetLoops(-1, TweeningLoopType.Restart)
	end
	local tween = funItem.objTrans:DOAnchorPos(Vector2(tarAnchoredPosition.x, tarAnchoredPosition.y), 1)
	tween:OnComplete(funItem.onAniEndFunc)

	local delayTime = TimerEx.New(function() 
		funItem.focusObj.gameObject.transform.localScale = Vector3(1, 1, 1)
		funItem.gameObject:SetActive(false)
		EventManager:Fire(EventID.OnFunctionOpen, self.funConfig.GuideEventID)
		TowerBattleManager:RequestPauseBattle(0, TowerBattlePauseType.Player)
		UIManager:CloseWndByID(WndID.FunctionOpen)
		FunctionOpenManager.IsOpenFun = false
		FunctionOpenManager:OpenNextFunction()
	end, 2.4, 1, funItem)
	delayTime:Start()
end

-- 点击返回按钮
function UIFunctionOpen:OnClickHandle()
	local self = UIFunctionOpen
	if self.completeIconDis == false then
		return 
	end
	self:StartFunctionAnimation()
end

-- 窗口开启
function UIFunctionOpen:OnOpen(funConfig)
	TowerBattleManager:RequestPauseBattle(1, TowerBattlePauseType.Player)
	self.objList.Img_Title.gameObject:SetActive(true)
	self.objList.Txt_Goto.gameObject:SetActive(true)
	self.objList.Img_Mask.gameObject:SetActive(true)
	self.objList.Center.gameObject:SetActive(true)
	self:UpdateFunctionItems(funConfig)
end

-- 窗口开启
function UIFunctionOpen:UpdateFunctionItems(funConfig)
	self.funConfig = funConfig
	self.completeIconDis = false
	local startScale = Vector3(0, 0, 0)
	local itemPrefab = self.objList.MoveIcon.FunctionItem
	local rootObj = UIManager:GetUIContainer()
	local funItem = self.funItemList[1]
	if not funItem then
		funItem = {}
		funItem.id = 1
		local obj = GameObject.Instantiate(itemPrefab, self.itemContainer)
		local objTrans = obj:GetRectTransform()
		Helper.FillLuaComps(objTrans, funItem)
		funItem.gameObject = obj
		funItem.gameObject.name = "funItem"..funItem.id
		funItem.objTrans = objTrans
		table.insert(self.funItemList, funItem)
	end
	if funItem.quence then
		funItem.quence:Kill()
		funItem.quence = nil
	end
	funItem.gameObject:SetActive(true)
	funItem.objTrans.anchoredPosition = Vector2(0, 280)
	funItem.objTrans.localScale = startScale
	local focusObj = rootObj:Find(self.funConfig.Path)
	if not focusObj then 
		warn("配置路径找不到! "..self.funConfig.Path)
		return
	end
	funItem.focusObj = focusObj
	focusObj.gameObject:SetActive(true)
	funItem.focusObj.gameObject.transform.localScale = startScale
	funItem.objTrans:DOKill()
	AtlasManager:AsyncGetSprite(self.funConfig.IconName, funItem.Img_Icon)

	self.objList.Txt_Title.text = self.funConfig.BeiZhu
	self.objList.Txt_Introduce.text = self.funConfig.Desc
	local bgTrans = funItem.Img_ItemBG.transform
	bgTrans:DOKill()
	bgTrans:DOLocalRotate(bgTrans.eulerAngles + Vector3(0, 0, 360), 2, DG.Tweening.RotateMode.FastBeyond360):SetEase(TweeningEase.Linear, 1):SetLoops(-1, TweeningLoopType.Restart)

	if self.aniSeq then
		self.aniSeq:Kill()
		self.aniSeq = nil
	end
	
	local seq = DOTween.Sequence()
	local addTime = 0
	local toScale = Vector3(1, 1, 1)
	for i, v in ipairs(self.funItemList) do
		if v.gameObject.activeSelf then
			local trans = v.objTrans
			seq:Insert(addTime, trans:DOScale(toScale, 0.7):SetEase(TweeningEase.OutBack, 1.5))
			addTime = addTime + 0.3
		end
	end
	self.aniSeq = seq
	local delayTime = TimerEx.New(function()
		self.completeIconDis = true
	end, addTime, 1)
	delayTime:Start()
end

-- 窗口关闭
function UIFunctionOpen:OnClose()
end

-- 窗口销毁
function UIFunctionOpen:OnDestroy()
end
return UIFunctionOpen