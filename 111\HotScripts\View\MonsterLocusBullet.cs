﻿// ReSharper disable ClassWithVirtualMembersNeverInherited.Global
// ReSharper disable InconsistentNaming

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;

using Apq.Unity3D.Extension;
using Apq.Unity3D.UnityHelpers;

using Cysharp.Threading.Tasks;

using DataStructure;

using DG.Tweening;

using HotScripts;

using RxEventsM2V;

using Thing;

using UnityEngine;

using X.PB;

namespace View
{
    /// <summary>
    ///     怪物阵营的子弹:直线轨迹预示
    /// </summary>
    public class MonsterLocusBullet : MonsterBulletBase
    {
        /// <inheritdoc />
        public override Vector3 CalcDir_Straight()
        {
            Vector3 rtn = base.CalcDir_Straight();
            if (BulletThing.Angle != 0)
            {
                rtn = rtn.RotateAround(Vector3.forward, BulletThing.Angle);
            }

            // 找出攻击的基准方向
            if (BulletThing.AttackBaseDirFollowThing != null)
            {
                // 起点跟随怪物
                transform.position = MonsterThing.Position;
                rtn = BulletThing.AttackBaseDirFollowThing.Position - transform.position;
                if (BulletThing.Angle != 0)
                {
                    rtn = rtn.RotateAround(Vector3.forward, BulletThing.Angle);
                }
            }

            return rtn;
        }

        /// <summary>
        ///     清空轨迹(销毁)
        /// </summary>
        public void ClearLocus()
        {
            try
            {
                for (int i = LocusRoot.transform.childCount - 1; i >= 0; i--)
                {
                    try
                    {
                        Destroy(LocusRoot.transform.GetChild(i).gameObject);
                    }
                    catch
                    {
                        // ignore
                    }
                }
            }
            catch
            {
                // ignore
            }
        }

        /// <summary>
        ///     生成并显示跟随敌人的轨迹
        /// </summary>
        public async UniTask ShowTrackEnemyLocus(Color bulletLocusColor, float bulletLocusWidth,
            double bulletLocusTrackEnemyDuration)
        {
            try
            {
                // 轨迹(跟随敌人)
                if (bulletLocusTrackEnemyDuration > 0)
                {
                    CancellationTokenSource cts_TrackEnd = new();
                    CancellationToken token_TrackEnd = cts_TrackEnd.Token;
                    cts_TrackEnd.CancelAfterSlim(TimeSpan.FromSeconds(bulletLocusTrackEnemyDuration));

                    for (;; await UniTask.NextFrame())
                    {
                        // 生成线段轨迹数据
                        await GenStraightLocus();
                        ShowLocus_One(bulletLocusColor, bulletLocusWidth).Forget();
                        if (token_TrackEnd.IsCancellationRequested)
                        {
                            break;
                        }
                    }
                }
            }
            catch (OperationCanceledException)
            {
                throw;
            }
            catch (MissingReferenceException)
            {
            }
            catch (Exception ex)
            {
                Debug.LogException(ex);
            }
            finally
            {
                ClearLocus();
            }
        }

        /// <summary>
        ///     显示最终轨迹(固定)
        /// </summary>
        public async UniTaskVoid ShowLocus(Color bulletLocusColor, float bulletLocusWidth, double bulletLocusDuration)
        {
            try
            {
                if (bulletLocusDuration > 0)
                {
                    CancellationTokenSource cts_LocusEnd = new();
                    CancellationToken token_LocusEnd = cts_LocusEnd.Token;
                    cts_LocusEnd.CancelAfterSlim(TimeSpan.FromSeconds(bulletLocusDuration));

                    ShowLocus_One(bulletLocusColor, bulletLocusWidth).Forget();
                    await UniTask.Delay(TimeSpan.FromSeconds(bulletLocusDuration),
                        cancellationToken: token_LocusEnd);
                }
            }
            catch (OperationCanceledException)
            {
                throw;
            }
            catch (MissingReferenceException)
            {
            }
            catch (Exception ex)
            {
                Debug.LogException(ex);
            }
            finally
            {
                ClearLocus();
            }
        }

        /// <summary>
        ///     显示一次轨迹
        /// </summary>
        public async UniTaskVoid ShowLocus_One(Color color, float width)
        {
            ClearLocus();
            LocusRoot.SetActive(true);
            
            // 每条轨迹线段显示为一个图片
            List<LineSegment> straightLocus = StraightLocus.ToList();
            foreach (LineSegment lineSegment in straightLocus)
            {
                try
                {
                    GameObject gObj = new("Locus")
                    {
                        transform =
                        {
                            parent = LocusRoot.transform,
                            position = lineSegment.PosMiddle,
                            right = lineSegment.DirNormal
                        }
                    };
                    SpriteRenderer sp = gObj.GetOrAddComponent<SpriteRenderer>();
                    sp.sprite = await ResMgrAsync.LoadResAsync<Sprite>("Assets/Temp/image/Sharp/White9X9.png");
                    sp.drawMode = SpriteDrawMode.Sliced;
                    sp.color = color;
                    sp.size = new Vector2(lineSegment.Length, width);
                }
                catch
                {
                    // ignore
                }
            }
        }

        #region 生成轨迹

        /// <summary>
        ///     生成子弹移动线段轨迹(按子弹方向，带反弹和转回)
        /// </summary>
        public virtual async UniTask GenStraightLocus()
        {
            StraightLocus.Clear();
            float bulletLife = (float)BulletThing.CdExecutor.Thing.GetTotalDouble(PropType.BulletLife).FirstOrDefault();
            if (bulletLife > 0)
            {
                float speed = (float)BulletThing.CdExecutor.Thing.GetTotalDouble(PropType.BulletSpeed).FirstOrDefault();
                if (speed > 0)
                {
                    Vector3 dir_1 = CalcDir_Straight();
                    // 初始移动轨迹(按移动方向、速度、时长计算出来的线段)
                    StraightLocus.Add(new LineSegment
                    {
                        PosStart = transform.position, Dir = speed * bulletLife * dir_1
                    });

                    // 移动这个长度之后方向逆转
                    float turnBackLength = 0f;
                    float turnBack = (float)BulletThing.CdExecutor.Thing.GetTotalDouble(PropType.TurnBack)
                        .FirstOrDefault();
                    if (turnBack > 0)
                    {
                        turnBackLength = turnBack * speed;
                    }

                    // 处理子弹移动轨迹中的反弹和转回
                    await DoBounceTurnBack(0, BulletThing.BounceTimes.Value, turnBackLength);
                }
            }
        }

        /// <summary>
        ///     处理子弹移动线段轨迹的反弹和转回
        /// </summary>
        public virtual async UniTask DoBounceTurnBack(float length, int bounceTimes,
            float turnBackLength = 0, bool hadTurnBack = false, Func<RectObstacleThing, bool> predicate = null)
        {
            bool throughObstacle = 0 != BulletThing.CdExecutor.Thing.GetTotalLong(PropType.BulletThroughObstacle)
                .FirstOrDefault();

            // 下一帧开始处理(这样每帧就只处理一条线段)
            // await UniTask.NextFrame();
            // 不可穿过的地形。默认：墙以上
            predicate ??= x => x.TerrainType >= TerrainType.Wall;

            // 只处理最后一个线段
            LineSegment line = StraightLocus.Last();

            //  不可飞越障碍物
            if (!throughObstacle)
            {
                // 射线检测 找出碰到的地形
                // ReSharper disable once Unity.PreferNonAllocApi
                RaycastHit2D[] hits = Physics2D.CircleCastAll(line.PosStart, BulletThing.TotalProp_Radius,
                    line.DirNormal,
                    line.Length, LayerMask.GetMask("Terrain"));
                if (hits is { Length: > 0 })
                {
                    // 碰到的第一个不可穿过的矩形障碍物
                    // ReSharper disable once IdentifierTypo
                    RaycastHit2D raycastHit2D = hits.FirstOrDefault(x =>
                        x && x.collider.gameObject &&
                        x.collider.gameObject.GetComponent<RectObstacle>() is
                            { RectObstacleThing: not null } rectObstacle &&
                        predicate.Invoke(rectObstacle.RectObstacleThing));
                    if (raycastHit2D)
                    {
                        // 反弹点选为接近此边(未碰到)的位置
                        Vector2 dir = raycastHit2D.point - (Vector2)line.PosStart;
                        // 可向前走的长度
                        float len = dir.magnitude - BulletThing.TotalProp_Radius - 0.3f;
                        if (len < 0)
                        {
                            len = 0;
                        }

                        // 反弹前已达到转回长度，则按转回处理
                        if (!hadTurnBack && turnBackLength > length && length + len >= turnBackLength)
                        {
                            len = turnBackLength - length;
                            length = turnBackLength;
                            // 剩余长度
                            float lenLeft = line.Length - len;
                            if (lenLeft > 0)
                            {
                                // 终点改为转回点
                                line.Dir = line.DirNormal * len;
                                // 加上转回的线段
                                LineSegment newLine = new() { PosStart = line.PosEnd, Dir = -line.DirNormal * lenLeft };
                                StraightLocus.Add(newLine);
                                hadTurnBack = true;
                            }
                        }
                        // 按反弹处理
                        else
                        {
                            // 剩余长度
                            float lenLeft = line.Length - len;
                            // 终点改为反弹点
                            line.Dir = line.DirNormal * len;

                            if (bounceTimes > 0 && lenLeft > 0)
                            {
                                // 反弹
                                bounceTimes--;
                                // 反弹后的方向
                                Vector3 bounceDir_1 = Vector3.Reflect(line.DirNormal, raycastHit2D.normal);
                                // 加上反弹后的线段
                                LineSegment newLine = new() { PosStart = line.PosEnd, Dir = bounceDir_1 * lenLeft };
                                StraightLocus.Add(newLine);
                            }
                            else
                            {
                                return;
                            }
                        }

                        // 继续处理反弹和转回
                        await DoBounceTurnBack(length, bounceTimes, turnBackLength, hadTurnBack, predicate);
                        return;
                    }
                }
            }

            // 没有碰到障碍物，但有转回
            if (turnBackLength > length && line.Length > turnBackLength)
            {
                length = turnBackLength;
                float lenLeft = line.Length - length;

                if (lenLeft > 0)
                {
                    // 终点改为转回点
                    line.Dir = line.DirNormal * turnBackLength;

                    // 加上转回的线段
                    StraightLocus.Add(new LineSegment { PosStart = line.PosEnd, Dir = -line.DirNormal * lenLeft });
                    // 继续处理反弹和转回
                    await DoBounceTurnBack(length, bounceTimes, turnBackLength, true, predicate);
                }
            }
        }

        /// <summary>
        ///     将线段轨迹拆分为时长轨迹(带反弹和转回)
        /// </summary>
        public virtual async UniTask GenStraightLocusInterval(float duration = 0.03f)
        {
            // 将线段轨迹拆分为时长轨迹
            if (StraightLocus.Count > 0)
            {
                StraightLocus_Duration.Locus.Clear();
                StraightLocus_Duration.Progress = 0;

                float speed = (float)BulletThing.CdExecutor.Thing.GetTotalDouble(PropType.BulletSpeed).FirstOrDefault();
                // 当前走到的位置
                Vector3 pos = StraightLocus.First().PosStart;
                // 步长
                float step = speed * duration;
                // 当前线段已移动的长度
                float length = 0f;
                // 当前线段索引号
                int lineIdx = 0;
                while (true)
                {
                    LineSegment line = StraightLocus[lineIdx];
                    if (length + step >= line.Length)
                    {
                        // 加入最后这小段
                        LineSegment s = new() { PosStart = pos, Dir = line.DirNormal * (line.Length - length) };
                        StraightLocus_Duration.Locus.Add(s);

                        // 换到下一线段
                        length = 0;
                        lineIdx++;
                        if (StraightLocus.Count <= lineIdx)
                        {
                            break;
                        }

                        pos = StraightLocus[lineIdx].PosStart;
                        // 下一帧再继续处理(这样每帧就只拆分一条线段)
                        await UniTask.NextFrame();
                    }
                    else
                    {
                        // 加入走过的这一小段
                        LineSegment s = new() { PosStart = pos, Dir = line.DirNormal * step };
                        StraightLocus_Duration.Locus.Add(s);

                        pos = s.PosEnd;
                        length += step;
                    }
                }
            }
        }

        #endregion

        #region 移动

        /// <summary>
        ///     生成子弹轨迹后开始自转和移动
        /// </summary>
        public async UniTaskVoid GenLocusThenStartMove()
        {
            // 先隐藏子弹图片
            ImgElement.SetActive(false);

            // 检查子弹是否一出生就碰到不可通过的障碍物
            List<ObstacleBase> obstacles = SingletonMgr.Instance.BattleMgr.MapMgr.Map.FindObstacles(new CircularArea2D
            {
                Center = transform.position,
                Radius = (float)BulletThing.CdExecutor.Thing.GetTotalDouble(PropType.BulletRadius).FirstOrDefault()
            });
            if (obstacles is { Count: > 0 } &&
                obstacles.Any(x => x.ObstacleThing is { TerrainType: >= TerrainType.Wall }))
            {
                // 显示子弹1秒后归还到子弹池
                ImgElement.SetActive(true);

                await UniTask.Delay(1000);

                TurnToPool().Forget();
                return;
            }

            #region 轨迹配置

            List<float> bulletLocusColor = BulletThing.CdExecutor.Thing.GetTotalDouble(PropType.BulletLocusColor)
                .ConvertAll(x => (float)x);
            if (bulletLocusColor.Count < 1)
            {
                bulletLocusColor.Add(255);
            }

            while (bulletLocusColor.Count < 3)
            {
                bulletLocusColor.Add(0);
            }

            if (bulletLocusColor.Count < 4)
            {
                bulletLocusColor.Add(1);
            }

            Color color_bulletLocus = new(bulletLocusColor[0], bulletLocusColor[1], bulletLocusColor[2],
                bulletLocusColor[3]);

            float bulletLocusWidth = (float)BulletThing.CdExecutor.Thing.GetTotalDouble(PropType.BulletLocusWidth)
                .FirstOrDefault();
            if (bulletLocusWidth < 0)
            {
                bulletLocusWidth = 0.3f;
            }

            double bulletLocusTrackEnemyDuration = BulletThing.CdExecutor.Thing
                .GetTotalDouble(PropType.BulletLocusTrackEnemyDuration).FirstOrDefault();

            double bulletLocusDuration = BulletThing.CdExecutor.Thing.GetTotalDouble(PropType.BulletLocusDuration)
                .FirstOrDefault();

            #endregion

            // 生成并显示跟随敌人的轨迹
            await ShowTrackEnemyLocus(color_bulletLocus, bulletLocusWidth, bulletLocusTrackEnemyDuration);

            // 生成最终轨迹(固定)
            await GenStraightLocus();
            // 显示最终轨迹
            ShowLocus(color_bulletLocus, bulletLocusWidth, bulletLocusDuration).Forget();

            float timeBegin = Time.time;
            await GenStraightLocusInterval(MoveInterval);

            double bulletLocusShootDelay = BulletThing.CdExecutor.Thing.GetTotalDouble(PropType.BulletLocusShootDelay)
                .FirstOrDefault();
            bulletLocusShootDelay -= Time.time - timeBegin;
            if (bulletLocusShootDelay > 0)
            {
                await UniTask.Delay(TimeSpan.FromSeconds(bulletLocusShootDelay));
            }

            // 显示子弹图片
            ImgElement.SetActive(true);

            // 开始自转
            StartRotate();
            // 开始移动
            StartMove();
        }

        // /// <summary>
        // /// 开始移动(计算轨迹后，按固定时长进行移动)
        // /// </summary>
        // /// <returns>是否已开始移动</returns>
        // public override void StartMove()
        // {
        //     StopMove();
        //     CTS_Move = new();
        //
        //     MoveDuration.Value = 0;
        //     DoTask_Move(CTS_Move.Token).Forget();
        // }

        /// <summary>
        ///     任务实现:按轨迹移动
        /// </summary>
        public override async UniTaskVoid DoTask_Move(CancellationToken token)
        {
            try
            {
                for (;; await UniTask.Delay(TimeSpan.FromSeconds(MoveInterval), cancellationToken: token))
                {
                    if (token.IsCancellationRequested)
                    {
                        return;
                    }

                    if (Time.deltaTime <= 0)
                    {
                        continue;
                    }

                    if (OnBeforeMoveOne())
                    {
                        return;
                    }

                    try
                    {
                        // var line = MoveOne_PositionMove(Time.deltaTime);
                        LineSegment line = MoveOne_PositionMove(MoveInterval);
                        if (OnAfterMoveOne(line))
                        {
                            return;
                        }

                        // 需要移动
                        if (line.Dir != Vector3.zero)
                        {
                            // 子弹朝向
                            transform.right = line.DirNormal;
                            // 子弹移动
                            transform.DOMove(line.PosEnd, MoveInterval);
                        }
                    }
                    catch (OperationCanceledException)
                    {
                        throw;
                    }
                    catch (Exception ex)
                    {
                        Debug.LogException(ex);
                    }
                }
            }
            catch (OperationCanceledException)
            {
                throw;
            }
            catch (MissingReferenceException)
            {
            }
            catch (Exception ex)
            {
                Debug.LogException(ex);
            }
            finally
            {
                OnMoveEnd();
            }
        }

        /// <inheritdoc />
        protected override bool OnBeforeMoveOne()
        {
            // 子弹销毁了或隐藏了，结束
            if (!this || !isActiveAndEnabled)
            {
                return true;
            }

            // // 达到子弹最大存活时长,结束
            // if (BulletThing.LifeBeginTime.Value +
            //     BulletThing.CdExecutor.Thing.GetTotalDouble(PropType.BulletLife).FirstOrDefault() <= Time.time)
            // {
            //     return true;
            // }

            return false;
        }

        /// <inheritdoc />
        protected override bool OnAfterMoveOne(LineSegment line)
        {
            if (BulletThing.NextHitEnemyTime.Value <= Time.time)
            {
                IList<HitThingCells> hitEnemies = DoCollideEnemies(line);
                // 如果击中了敌人
                if (hitEnemies is { Count: > 0 })
                {
                    // 击中的声音
                    AudioPlayer.Instance
                        .PlaySound(BulletThing.CdExecutor.Thing.GetTotalString(PropType.HitSound).FirstOrDefault())
                        .Forget();

                    BulletThing.NextHitEnemyTime.Value = Time.time +
                                                         (float)BulletThing.CdExecutor.Thing
                                                             .GetTotalDouble(PropType.HitCd).FirstOrDefault();

                    // 子弹击中敌人后，默认设为应该丢弃
                    BulletThing.ShouldDiscard.Value = true;

                    // 最多伤害 剩余穿透次数+1 个敌人
                    int penetrateTimes = BulletThing.PenetrateTimes.Value;
                    hitEnemies = hitEnemies.Take(penetrateTimes + 1).ToList();

                    OnHitEnemy(line, BulletThing.CdExecutor.Thing, hitEnemies);

                    // 如果子弹被设置为该丢弃了,则本方法结束
                    if (BulletThing.ShouldDiscard.Value)
                    {
                        // 返回true后,子弹会停止移动并被回收到子弹池
                        return true;
                    }
                }
            }

            // 如果子弹被设置为该丢弃了,则本方法结束
            if (BulletThing.ShouldDiscard.Value)
            {
                // 返回true后,子弹会停止移动并被回收到子弹池
                return true;
            }

            return false;
        }

        /// <inheritdoc />
        protected override void OnMoveEnd()
        {
            try
            {
                // 还给子弹池
                TurnToPool().Forget();
            }
            catch
            {
                // ignored
            }
        }

        /// <summary>
        ///     移动一次。从轨迹中找出这次直线移动的线段(或不动)
        /// </summary>
        /// <returns>这次直线移动经过的线段</returns>
        public override LineSegment MoveOne_PositionMove(float duration)
        {
            // 默认没有方向，就是不动
            LineSegment rtn = new() { PosStart = transform.position };
            MoveDuration.Value += duration;

            // 子弹被设置为停留状态，就不动了。
            if (ShouldStay)
            {
                return rtn;
            }

            // 移动时长超过停留区间的最大值，就应该停了
            List<double> stayPeriod = BulletThing.CdExecutor.Thing.GetTotalDouble(PropType.StayPeriod);
            if (!ShouldStay && stayPeriod.Count > 1 && MoveDuration.Value > stayPeriod[1])
            {
                // 下次就不动了。
                ShouldStay = true;
            }

            if (StraightLocus_Duration.Locus.Count <= StraightLocus_Duration.Progress)
            {
                // 轨迹走完，该丢弃了
                BulletThing.ShouldDiscard.Value = true;
                return rtn;
            }

            // 找出这次要移动的线段
            rtn = StraightLocus_Duration.Locus[StraightLocus_Duration.Progress];

            // 向前推进
            StraightLocus_Duration.Progress++;

            return rtn;
        }

        /// <summary>
        ///     子弹传送至终点并反弹
        /// </summary>
        public override void TranslateToEndPoint_PositionMove(StraightEndPoint_PositionMove endPoint)
        {
            // 物件传送至终点
            base.TranslateToEndPoint_PositionMove(endPoint);

            // 如果终点是不可通过的障碍物，要么反弹，要么销毁
            if (endPoint.MirrorInNormal != Vector3.zero)
            {
                if (BulletThing.BounceTimes.Value > 0)
                {
                    BulletThing.BounceTimes.Value--;
                    // 反弹后的方向
                    Thing.MoveDirection_Straight.Value = Vector3
                        .Reflect(Thing.MoveDirection_Straight.Value, endPoint.MirrorInNormal).normalized;
                    // 子弹朝向
                    transform.right = Thing.MoveDirection_Straight.Value;
                }
                else
                {
                    BulletThing.ShouldDiscard.Value = true;
                }
            }
        }

        #endregion
    }
}