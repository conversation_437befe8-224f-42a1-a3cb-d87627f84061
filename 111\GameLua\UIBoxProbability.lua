--[[
********************************************************************
    created:	2024/06/06
    author :	李锦剑
    purpose:    宝箱概率界面
*********************************************************************
--]]

local luaID = 'UIBoxProbability'

local boxIconList = {
    [BOX_EQUIP_ID[1]] = {
        bg = 'xsd_bxbjt_1',
        icon = 'xsd_bxtb_1'
    },
    [BOX_EQUIP_ID[2]] = {
        bg = 'xsd_bxbjt_2',
        icon = 'xsd_bxtb_2'
    },
}


---宝箱概率界面
---@class UIBoxProbability:UIWndBase
local m = {}
--------------------------------------------------------------------
-- 初始化
--------------------------------------------------------------------
function m.OnCreate()
    ---@type Item_Goods3[]
    m.Item_Goods_List = {}
    ---@type SlotItem[]
    m.SlotItem_List = {}
    m.RegisterClickEvent()
    return true
end

--------------------------------------------------------------------
-- 窗口开启
--------------------------------------------------------------------
function m:OnOpen(goodsID)
    m.goodsID = goodsID
    m.UpdateView()
end

--------------------------------------------------------------------
-- 注册点击事件
--------------------------------------------------------------------
function m.RegisterClickEvent()
    m:AddClick(m.objList.Btn_OK, function()
        m:CloseSelf()
    end)
    m:AddClick(m.objList.Btn_CloseRewards, function()
        m:CloseSelf()
    end)
end

--------------------------------------------------------------------
--更新界面
--------------------------------------------------------------------
function m.UpdateView()
    local medicament = Schemes.Medicament:Get(m.goodsID)
    if not medicament or medicament.UseMenu ~= 4 then
        m:CloseSelf()
        return
    end
    AtlasManager:AsyncGetGoodsSprite(m.goodsID, m.objList.Img_Goods)
    local prizeGoodsList = Schemes.PrizeTable:GetGoodsList(medicament.UseParam1)
    if not prizeGoodsList then
        m:CloseSelf()
        return
    end
    local num1 = #prizeGoodsList
    local num = math.max(num1, #m.SlotItem_List)
    local data, item
    for i = 1, num, 1 do
        if not m.SlotItem_List[i] then
            m.SlotItem_List[i] = _GAddSlotItem(m.objList.Grid_Goods)
        end
        data = prizeGoodsList[i]
        item = m.SlotItem_List[i]
        if data then
            item:SetItemID(data.id)
            item:SetActive(true)
        else
            item:SetActive(false)
        end
    end
    m.objList.Txt_Describe.text = string.format(GetGameText(luaID, 1), 100 / num1)
end

return m
