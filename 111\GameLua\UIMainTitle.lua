--[[
********************************************************************
    created:	
    author :	
    purpose:    主界面
*********************************************************************
--]]

---主按钮数据
---@class MainButtonData
---@field name string 按钮显示名称
---@field icon1 string 正常显示图标
---@field icon2 string 正常显示图标
---@field windowID integer 窗口ID
---@field isDefault ?boolean 是默认主按钮
---@field isNested ?boolean 是嵌套(嵌套到主界面里)，默认：false

---资产按钮数据
---@class PropertyButtonData
---@field id integer 唯一ID



local luaID = ('UIMainTitle')

--默认主按钮ID
local DefaultMainButton = 3
local EctypeType = 1

---主界面
---@class UIMainTitle:UIWndBase
local m = {}

--特惠礼包活动ID
local DiscountGift_ActvID = {
    ACTVID.ACTVID_RECHARGEBUY_1,
    ACTVID.ACTVID_RECHARGEBUY_2,
    ACTVID.ACTVID_RECHARGEBUY_3,
}

--------------------------------------------------------------------
-- 订阅事件列表
--------------------------------------------------------------------
function m.GetOpenEventList()
    return {
        [EventID.LogicDataChange] = m.UpdateView,
        [EventID.OnHeroPropChange] = m.UpdateView,
        [EventID.WindowOpen] = m.OnWindowOpenEvent,  -- V15.2修改：使用专门的窗口打开事件处理
        [EventID.StoreBuyItem] = m.UpdateView,
        [EventID.OnSkepGoodsChange] = m.UpdateView,
        [EventID.ActorDataChanged] = m.UpdateView,
        [EventID.EntitySyncBuff] = m.UpdateView,
        [EventID.OnGoodsPropChange] = m.UpdateView,

        [EventID.OpenMainButtonUI] = m.OnClickMainButton,
        [EventID.HeroPropertyUpdate_Silver] = m.HeroPropertyUpdate_Silver,
        [EventID.UpdateGameEctypeData] = m.UpdateView,
        [EventID.UpdateGameEctypeBoxData] = m.UpdateView,
        [EventID.ChangeNameEvent] = m.UpdateViewName,
    }
end

--------------------------------------------------------------------
--预制体自适应
--------------------------------------------------------------------
function m.AdaptScale()
    HelperL.AdaptScale_Width(m.objList.Item_Button)
end

--------------------------------------------------------------------
--资源预加载
--------------------------------------------------------------------
function m.GetResourceList()
    return {
        "Assets/Temp/ui/Common/RedDot.prefab",
        "Assets/Temp/ui/Common/MaterialManager.prefab",
        "Assets/Temp/ui/Tips/Empty.prefab",

        "Assets/Temp/ui/Main/WindowController.prefab",
        "Assets/Temp/ui/Common/SlotItem.prefab",
        "Assets/Temp/ui/Common/UIModel.prefab",
        "Assets/Temp/model/prefab/diaoluo/Spine_BaoXiang.prefab",

        "Assets/Temp/model/prefab/diaoluo/jinbi1.prefab",
        "Assets/Temp/model/prefab/diaoluo/Box2.prefab",
    }
end

--------------------------------------------------------------------
--创建时
--------------------------------------------------------------------
function m.OnCreate()
    RankingModule.UpdateRankingRefreshTime(30)

    --神龙
    m.objList.Btn_Test.gameObject:SetActive(GameLuaAPI.GetPlatform() == "unityEditor")
    m.objList.Img_UIAD1.gameObject:SetActive(false)
    ---@type Item_Box[]
    m.gameEctypeBox_ItemList = {}
    ---@type SlotItem[]
    m.AwardShow_ItemList = {}
    m.selectCatMainStageID = GamePlayerData.GameEctype:GetChallengeableID(EctypeType)
    m.lastCatMainStageID = m.selectCatMainStageID
    m.StageList = Schemes.CatMainStage:GetByFrontType(EctypeType)
    --设置震动默认关闭
    local num = PlayerPrefs.GetInt('SYSTEM_MOBILE_VIBATE_INT', 0)
    if num == 0 then
        PlayerPrefs.SetInt('SYSTEM_MOBILE_VIBATE_INT', 1)
        PlayerPrefs.SetInt("SYSTEM_MOBILE_VIBATE", 0)
    end
    m.Ismove = false

    m.activityGridList = {m.objList.GridBottom_Left,m.objList.Grid_Right}
    local activityButtonData = {
        ------------------------------活动按钮集合1------------------------------
    
    
        ------------------------------活动按钮集合2------------------------------
       -- { grid = 2, id = 1000, windowID = WndID.StoryIntroduce,       icon = 'f-154', name = "游戏背景", offset = Vector2(-40, -40), },
        --{ grid = 1, id = 1,   windowID = WndID.SettingNew, icon = 'zjm_25', name = "设置", offset = Vector2(-10, -10), },
        { grid = 1, id = 2,   windowID = WndID.ShopNew, icon = 'zjm_24', name = "商城", offset = Vector2(-10, -10), },
        { grid = 1, id = 3,   windowID = WndID.Recharge, icon = 'zjm_23', name = "充值", offset = Vector2(-10, -10), },
        { grid = 1, id = 107,   windowID = WndID.ChaoJiChongZhi,               icon = 'zjm_22', name = "礼包", offset = Vector2(-10, -10), },
        { grid = 1, id = 23,   windowID = WndID.YQSMoney, icon = 'zjm_21', name = "摇钱树", offset = Vector2(-10, -10), },
        { grid = 1, id = 25,   windowID = WndID.SignIn, icon = 'zjm_25', name = "签到", offset = Vector2(-10, -10), },
    
        { grid = 2, id = 98,  windowID = WndID.GoldCaveEctypt,  icon = 'zjm_16', name = "归墟秘境", offset = Vector2(-10, -10), },
        { grid = 2, id = 99,  windowID = WndID.LeaderEctype,   icon = 'zjm_30', name = "九霄神阵", offset = Vector2(-10, -10), },
        { grid = 2, id = 17,   windowID = WndID.ForbiddenAreaEctype, icon = 'zjm_15', name = "轮回天路", offset = Vector2(-10, -10), },
        { grid = 2, id = 106,  windowID = WndID.PaTaFuBen,    icon = 'zjm_14', name = "北境神殿", offset = Vector2(-10, -10), },
        { grid = 2, id = 108,   windowID = WndID.EliteEctype,    icon = 'zjm_13', name = "封天历练", offset = Vector2(-10, -10), },
    }
    
    ---主按钮集合
    ---@type MainButton[]
    m.mainButtonList = {}
    ---按钮数据
    ---@type MainButtonData[]
    local mainLeftButtonData = {
        {
            name = '天赋',
            icon1 = 'zjm_28',
            icon2 = 'zjm_icon_bg5',
            windowID = WndID.ShengHun,
        },
        {
            name = '灵根',
            icon1 = 'zjm_29',
            icon2 = 'zjm_icon_bg5',
            windowID = WndID.XinFa,
        },
        {
            name = '星篆',
            icon1 = 'zjm_27',
            icon2 = 'zjm_icon_bg4',
            windowID = WndID.RoleTalent,
        },
        {
            name = '伙伴',
            icon1 = 'zjm_26',
            icon2 = 'zjm_icon_bg7',
            windowID = WndID.RoleEquip,
        }
        -- {
        --     name = '体魄',
        --     icon1 = 'ff232rSUWR',
        --     icon2 = 'zjm_icon_bg3',
        --     windowID = WndID.EquipWeaponInfo,
        -- },       
        -- {
        --     name = '祈祷',
        --     icon1 = 'f67556',
        --     icon2 = 'zjm_icon_bg7',
        --     windowID = WndID.XinFa,
        -- }
    }
    --创建主按钮
    local index = 0
    for i, v in ipairs(mainLeftButtonData) do
        index = index + 1
        m.mainButtonList[index] = m.CreationMainButton(index, v, 0)
    end

    m.activityButtonList = {}
    --创建活动按钮
    for i, v in ipairs(activityButtonData) do
        table.insert(m.activityButtonList, m.CreationActivityButton(v))
    end
    
    m.comResInfo = ComResInfo.New()
    m.comResInfo.BindView(m.objList.Com_Res)


    m.Item_Ectype_List = {}
    for i = 1, 4 do
        m.Item_Ectype_List[i] = m.CreationItem(i, m.objList["MainItem"..i])        
    end
    m.Item_Left_List = {}
    m.RegisterClickEvent()
    m.Guide()
    return true
end


function UpdateViewName()
    local hero = EntityModule.hero
    m.objList.Txt_RoleName.text = hero.name
end

--------------------------------------------------------------------
-- V15.2版本：窗口打开事件处理，检测回到主界面时播放动画
--------------------------------------------------------------------
function m.OnWindowOpenEvent(wndID)
  ----  ----print("=== V15.2 OnWindowOpenEvent 窗口打开事件: wndID=" .. tostring(wndID) .. " ===")
    
    -- 先更新界面
    m.UpdateView()
    
    -- 检测是否是主界面打开
    if wndID == WndID.MainTitle then
   ----     ----print("=== V15.2 检测到主界面打开，准备播放动画 ===")
        
        -- 检查引导状态
        local isGuideActive = false
        if GuideManager then
            isGuideActive = GuideManager.Runing or GuideManager.isRunning
    ----        ----print("=== V15.2 引导检测：GuideManager.Runing=" .. tostring(GuideManager.Runing) .. ", GuideManager.isRunning=" .. tostring(GuideManager.isRunning) .. " ===")
        else
     ----       ----print("=== V15.2 引导检测：GuideManager 不存在 ===")
        end
        
        -- 播放动画（暂时强制播放以测试）
        if false then -- 临时设为false来强制播放动画
          ----  ----print("=== V15.2 引导进行中，跳过主界面动画 ===")
            m.ResetNodesState()
        else
         ----   ----print("=== V15.2 通过WindowOpen事件播放主界面动画 ===")
            -- 延迟一点播放，确保界面已经完全显示
            Timer.New(function()
                m.ResetNodesState()
                m.PlayEnterAnimation()
            end, 0.1, 1):Start()
        end
    end
end

--------------------------------------------------------------------
-- 打开时
--------------------------------------------------------------------
function m:OnOpen(id, index)
    ----print("=== V15.1 UIMainTitle OnOpen 开始调用 ===")
    --BattleManager.CheckBattleProgress()
    --播放背景音乐
    SoundManager:PlayMusic(SoundID.Main)
    --获取商店列表
    StoreModule:RequestGetStoreList(2)

    m.selectItemBoxIndex = nil
    m.objList.Obj_AwardShow.gameObject:SetActive(false)
    m.objList.Btn_UI1.gameObject:SetActive(false)
    --重新添加红点
    for _, v in pairs(m.activityButtonList) do
        v.SetRedDot()
    end
    --m:SetWndRedDot(m.objList.Btn_Ectype1,Vector2(-10, -10)):AddCheckParam(WndID.MiJingFuBen)
    -- m:SetWndRedDot(m.objList.Btn_Ectype2,Vector2(-10, -10)):AddCheckParam(WndID.LeaderEctype)
    -- m:SetWndRedDot(m.objList.Btn_Ectype3,Vector2(-10, -10)):AddCheckParam(WndID.EliteEctype)
    -- m:SetWndRedDot(m.objList.Btn_Ectype4,Vector2(-10, -10)):AddCheckParam(WndID.PaTaFuBen)
    -- m:SetWndRedDot(m.objList.Btn_Ectype5,Vector2(-10, -10)):AddCheckParam(WndID.GoldCaveEctypt)
    -- m:SetWndRedDot(m.objList.Btn_Ectype6,Vector2(-10, -10)):AddCheckParam(WndID.ForbiddenAreaEctype)
    
    ---主线任务
    UIManager:OpenWnd2(WndID.TaskInterface, m.objList.Obj_Task)
    m.UpdateView()
    
    -- V32.4-fix OnOpen中立即强制所有关键节点缩放为(1,1,1)
    ----print("=== V32.4-fix OnOpen开始强制缩放修复 ===")
    local criticalNodes = {
        {node = m.objList.Img_Line1, name = "Img_Line1"},
        {node = m.objList.Img_Line2, name = "Img_Line2"}, 
        {node = m.objList.Img_Line3, name = "Img_Line3"},
        {node = m.objList.MainItem1, name = "MainItem1"},
        {node = m.objList.MainItem2, name = "MainItem2"},
        {node = m.objList.MainItem3, name = "MainItem3"},
        {node = m.objList.MainItem4, name = "MainItem4"}
    }
    
    for _, nodeData in ipairs(criticalNodes) do
        local node = nodeData.node
        local nodeName = nodeData.name
        if node and node.transform then
            local currentScale = node.transform.localScale
        ----    ----print("=== V32.4-fix OnOpen检查节点 " .. nodeName .. " 当前缩放: (" .. currentScale.x .. "," .. currentScale.y .. "," .. currentScale.z .. ") ===")
            -- 强制设置为(1,1,1)，无论当前是什么值
            node.transform.localScale = Vector3(1, 1, 1)
         ----   ----print("=== V32.4-fix OnOpen强制设置节点 " .. nodeName .. " 缩放为(1,1,1) ===")
        end
    end
  ----  ----print("=== V32.4-fix OnOpen缩放修复完成 ===")
    
    -- V32.2-fix Android平台初始化检查：延迟确保节点显示
    Timer.New(function()
        m.EnsureNodesVisible()
    end, 0.2, 1):Start()
    
    -- V15.1版本更新：简化引导检测，每次回到主界面都播放动画
    local isGuideActive = false
    if GuideManager then
        -- 简化检测：只检查基本的引导运行状态
        isGuideActive = GuideManager.Runing or GuideManager.isRunning
    ----    ----print("=== V15.1 引导检测：GuideManager.Runing=" .. tostring(GuideManager.Runing) .. ", GuideManager.isRunning=" .. tostring(GuideManager.isRunning) .. " ===")
    else
      ----  ----print("=== V15.1 引导检测：GuideManager 不存在 ===")
    end
    
    -- 强制播放动画（暂时忽略引导检测以确保动画工作）
    if false then -- 临时设为false来强制播放动画
      ----  ----print("=== V15.1 引导进行中，跳过主界面动画 ===")
        -- 引导进行时确保节点正常显示，但不播放动画
        m.ResetNodesState()
    else
      ----  ----print("=== V15.1 强制播放主界面进入动画 ===")
        -- 重置节点状态，确保节点可见
        m.ResetNodesState()
        -- 播放进入动画
        m.PlayEnterAnimation()
    end
  ----  ----print("=== V15.1 UIMainTitle OnOpen 结束调用 ===")
end

--------------------------------------------------------------------
-- 创建副本界面
--------------------------------------------------------------------
function m.CreationItem(index, view)
    ---@class Item_Ectype
    local item = {}
    item.stageID = nil
    item.index = index
    item.com = {}
    Helper.FillLuaComps(view.gameObject.transform, item.com)
    item.com.Btn_Click.onClick:AddListenerEx(function()
        -- V16.0版本：只有MainItem1(index=1)点击时检查等级并播放动画
        if item.index == 1 then
            -- 检查角色等级
            local heroLevel = EntityModule.hero:GetProperty(CREATURE_FIELD.CREATURE_FIELD_LEVEL)
         ----   ----print("=== V16.0 MainItem1被点击，角色等级=" .. tostring(heroLevel) .. " ===")
            
            if heroLevel >= 10 then
                -- 10级及以上：播放消失动画
          ----      ----print("=== V16.0 角色等级>=10，播放明显的item1-4消失动画 ===")
                m.PlayMainItemsDisappearAnimation(item.cfg.ID)
            else
                -- 10级以下：直接打开界面
         ----       ----print("=== V16.0 角色等级<10，直接打开界面 ===")
                UIManager:OpenWnd(WndID.NormalEctype, item.cfg.ID)
            end
        else
            -- 其他MainItem按钮：直接打开界面
            UIManager:OpenWnd(WndID.NormalEctype, item.cfg.ID)
        end
    end)
    
    item.UpdateData = function(cfg)
        item.cfg = cfg
        if cfg then
            item.com.Img1.gameObject:SetActive(false)
            item.com.Img2.gameObject:SetActive(false)
            item.com.Txt_Level.gameObject:SetActive(false)
            item.com.Txt_Level1.gameObject:SetActive(false)
            item.com.Txt_ytg.gameObject:SetActive(false)
            item.com.Btn_Click.gameObject:SetActive(false)
            item.com.Txt_Level.text = cfg.Name
            item.com.Txt_Level1.text = cfg.Name
            local maxStage = GamePlayerData.GameEctype:GetProgress(EctypeType)
            local color = "#CCCCCC"            
            --已通关
            AtlasManager:AsyncGetSprite(cfg.Icon, item.com.EctypeIcon, true)
            if maxStage >= cfg.ID then 
                item.com.Img1.gameObject:SetActive(true)
                item.com.Txt_Level.gameObject:SetActive(true)
                item.com.Txt_ytg.gameObject:SetActive(true)
                item.com.Btn_Click.gameObject:SetActive(true)
                HelperL.SetImageGray(item.com.EctypeIcon,false)
            elseif (maxStage == 0 and item.index == 1) or (maxStage + 1) == cfg.ID then
                item.com.Img2.gameObject:SetActive(true)
                item.com.Btn_Click.gameObject:SetActive(true)
                item.com.Txt_Level.gameObject:SetActive(true)
                HelperL.SetImageGray(item.com.EctypeIcon,false)
                color = "#FFFFFF"
            else
                item.com.Txt_Level1.gameObject:SetActive(true)
                HelperL.SetImageGray(item.com.EctypeIcon,true)
            end
        end
    end
    return item
end

--------------------------------------------------------------------
-- 注册点击事件
--------------------------------------------------------------------
function m.RegisterClickEvent()
    m:AddClick(m.objList.Btn_GameLevel1, function()
        local stageID = m.selectCatMainStageID - 1
        local list = Schemes.CatMainStage:GetByFrontType(EctypeType)
        local minID = list[1].ID
        -- local maxID = list[#list].ID
        if stageID < minID then
            m.selectCatMainStageID = minID
        else
            m.selectCatMainStageID = stageID
        end
        m.UpdateView()
        m.selectItemBoxIndex = nil
        m.objList.Obj_AwardShow.gameObject:SetActive(false)
    end)
    m:AddClick(m.objList.Btn_GameLevel2, function()
        local stageID = m.selectCatMainStageID + 1
        local list = Schemes.CatMainStage:GetByFrontType(EctypeType)
        -- local minID = list[1].ID
        local maxID = list[#list].ID
        if stageID > maxID then
            m.selectCatMainStageID = maxID
        else
            m.selectCatMainStageID = stageID
        end
        m.UpdateView()
        m.selectItemBoxIndex = nil
        m.objList.Obj_AwardShow.gameObject:SetActive(false)
    end)

    m:AddClick(m.objList.Btn_Mask, function()
        m.selectItemBoxIndex = nil
        m.objList.Obj_AwardShow.gameObject:SetActive(false)
    end)
    m:AddClick(m.objList.Btn_Mask1, function()
        if m.Ismove then return end
        m.Ismove = true
        m.objList.Btn_Mask1.gameObject:SetActive(false)
        m.objList.GridTop_Right1.transform:DOLocalMoveX(100, 0.3)
        Timer.New(function()
            m.Ismove = false
        end,  0.3, 1):Start()
    end)
    m:AddClick(m.objList.Btn_Start, function()
        -- HelperL.IsShowOkWindow(nil, function(isCheck)
        --关掉手指引导
        EventManager:Fire(EventID.TaskShowHandGuide, 3)
        m.ChuZheng(m.selectCatMainStageID)
        -- end)
    end)

    m:AddClick(m.objList.Btn_StartGray, function()
        UIManager:OpenWnd(WndID.PurchasePhysicalPower)
    end)


    m:AddClick(m.objList.Btn_Test, function()
        UIManager:OpenWnd(WndID.TestMode)
    end)
    m:AddClick(m.objList.Btn_PlayerHud1, function()
        UIManager:OpenWnd(WndID.RoleInfo)
    end)
    m:AddClick(m.objList.Btn_Head, function()
        UIManager:OpenWnd(WndID.RoleInfo)
    end)
    m:AddClick(m.objList.Btn_Story, function()
        UIManager:OpenWnd(WndID.StoryIntroduce)
    end)
end

--------------------------------------------------------------------
--更新资产
--------------------------------------------------------------------
function m.UpdateProperty()
    if not EntityModule.hero then return end
    --头像
    local levelText = EntityModule.hero:GetProperty(CREATURE_FIELD.CREATURE_FIELD_LEVEL)
    local exp = EntityModule.hero:GetProperty(PLAYER_FIELD.PLAYER_FIELD_CUREXP)
    local maxExp = EntityModule.hero:GetProperty(PLAYER_FIELD.PLAYER_FIELD_MAXEXP)
    m.objList.Txt_Lv.text = string.format('<color=#fecd57>%s</color>', levelText)
    m.objList.Img_Fill.fillAmount = exp / maxExp
    m.objList.Txt_Ex.text = HelperL.GetChangeNum(exp) .. '/' .. HelperL.GetChangeNum(maxExp)
    m.objList.Txt_RoleName.text = EntityModule.hero.name
    local weaponsKnapsack = GamePlayerData.ActorEquip:GetWeaponsKnapsack(1) or {}
    local curEquipID = weaponsKnapsack[1] or 0
    AtlasManager:AsyncGetSprite(HelperL.GetGunIcon(curEquipID), m.objList.Img_Head, true)
    local cfg = Schemes.Equipment:Get(curEquipID)
    local level = GamePlayerData.ActorEquipNew:GetEquipUpgradeLevel(cfg.SmeltID)
    m.objList.Txt_Lv.text = level .. "级"
end

--------------------------------------------------------------------
-- 更新按钮
--------------------------------------------------------------------
function m.UpdateView()
    if not EntityModule.hero then return end
    --更新主界面按钮
    for k, v in pairs(m.mainButtonList) do
        v.UpdateButton()
    end

    for k, v in pairs(m.activityButtonList) do
        v.UpdateButton()
    end
    
    m.UpdateProperty();
    m.UpdateEctypeView();
    m.UpdateTaskView()
    m.UpdateActivityButton()
    
    -- V15.1版本修复：只有在动画没有播放时才强制确保节点可见，避免干扰动画
    if not m.isAnimationPlaying then
        m.ForceEnsureNodesVisible()
    else
        -- V32.3-fix 即使动画播放中，也要确保缩放正确（不影响动画，只修复缩放异常）
        local quickCheckNodes = {
            {node = m.objList.Img_Line1, name = "Img_Line1"},
            {node = m.objList.Img_Line2, name = "Img_Line2"}, 
            {node = m.objList.Img_Line3, name = "Img_Line3"},
            {node = m.objList.MainItem1, name = "MainItem1"},
            {node = m.objList.MainItem2, name = "MainItem2"},
            {node = m.objList.MainItem3, name = "MainItem3"},
            {node = m.objList.MainItem4, name = "MainItem4"}
        }
        for _, nodeData in ipairs(quickCheckNodes) do
            local node = nodeData.node
            local nodeName = nodeData.name
            if node and node.transform then
                local currentScale = node.transform.localScale
                -- 如果缩放异常小（不在动画正常范围内），强制修复
                if currentScale.x < 0.2 and currentScale.y < 0.2 then
                   ---- ----print("=== V32.3-fix 动画中发现异常缩放，强制修复: " .. nodeName .. " ===")
                    node.transform.localScale = Vector3(1, 1, 1)
                end
            end
        end
    end
    
    -- V32.3-fix Android平台强化修复：无论动画状态都要检查节点显示
    m.EnsureNodesVisible()
end

--------------------------------------------------------------------
---更新副本界面
--------------------------------------------------------------------
function m.UpdateEctypeView()
    m.maxStage = GamePlayerData.GameEctype:GetProgress(EctypeType) 
    local gapNum = math.floor(m.maxStage/4)
    local maxChap = math.ceil((m.maxStage+1)/4)
    m.realIndex = maxChap
    ----print("m.maxStage ===== ",m.maxStage)
    ----print("m.realIndex ===== ",m.realIndex)
    local maxCount = maxChap
    if maxCount > 4 then
        maxCount = 4
    end
    for i = 1, maxCount do
        if not m.Item_Left_List[i] then
            m.Item_Left_List[i] = m.Creation_Item_Left(m.objList.Grid_Left)
        end
        --m.Item_Left_List[i].UpdateView(i)
    end

    -- 选中当前项
    for _, leftItem in pairs(m.Item_Left_List) do
        if leftItem.com then
            leftItem.com.Img_Select.gameObject:SetActive(false)
        end
    end

    local value = maxChap
    for i = 1, maxCount do       
        m.Item_Left_List[i].UpdateView(value)
        value = value - 1
    end
    
    local startPos = gapNum * 4 + 1
    local endPos = gapNum * 4 + 4
    
    if endPos > #m.StageList then
        endPos = #m.StageList
        startPos = endPos - 3
    end
    for i = startPos, endPos do
        local item = m.Item_Ectype_List[i - startPos + 1]
        item.UpdateData(m.StageList[i])
    end
    
end


--------------------------------------------------------------------
-- 创建副本栏
--------------------------------------------------------------------
function m.Creation_Item_Left(parent)
    ---@class Item_Ectype
    local item = {}
    item.index = 0
    ---@type SlotItem[]
    --item.slotItem_List = {}
    item.com = m:CreateSubItem(parent, m.objList.Item_Left)
    item.com.Img_Select.gameObject:SetActive(false) 
    m:AddClick(item.com.Btn_Click, function()
        if not item.com then
            return
        end
        
        -- 选中当前项
        for _, leftItem in pairs(m.Item_Left_List) do
            if leftItem.com then
                leftItem.com.Img_Select.gameObject:SetActive(false)
            end
        end
        item.com.Img_Select.gameObject:SetActive(true)
        
        m.ClickUpdateEctypeView(item.index)
        
        -- V23.0版本：每次点击Grid_Left/Item_Left切换时播放MainItem1-4的动画
       ---- ----print("=== V23.0 检测到Grid_Left/Item_Left切换，播放MainItem1-4动画 ===")
        m.ResetNodesState()
        m.PlayEnterAnimation()
        
    end)
    item.UpdateView = function(index)  
        item.index = index  
        item.com.Txt_Name.text = "第"..index.."章"    
        if m.realIndex == item.index and m.maxStage < #m.StageList then
            item.com.Img_Select.gameObject:SetActive(true) 
        else
            if item.index < m.realIndex or m.maxStage == #m.StageList then
                
            else
            end
        end
    end
    return item 
end

function m.ClickUpdateEctypeView(index)   
    local value = index - 1
    local startPos = value * 4 + 1
    local endPos = value * 4 + 4
    
    if endPos > #m.StageList then
        endPos = #m.StageList
        startPos = endPos - 3
    end
    for i = startPos, endPos do
        local item = m.Item_Ectype_List[i - startPos + 1]
        item.UpdateData(m.StageList[i])
    end
end

--------------------------------------------------------------------
-- 更新界面
--------------------------------------------------------------------
function m.UpdateTaskView()
    local taskID = GamePlayerData.ActorTask:GetMainTaskID()

    ---完成所有任务
    m.objList.Obj_Task.gameObject:SetActive(taskID ~= -1)

    local taskCfg = Schemes.Task:Get(taskID)
    if taskCfg then
        local jd = GamePlayerData.ActorTask:GetTaskProgress(taskID)
        if jd >= taskCfg.Parameter2 then
            m.objList.Obj_Start.gameObject:SetActive(false)
        else
            m.objList.Obj_Start.gameObject:SetActive(true)
        end
    else
        m.objList.Obj_Start.gameObject:SetActive(true)
    end
end

--------------------------------------------------------------------
---领取副本宝箱奖励
---@param stageID integer
---@param index integer
--------------------------------------------------------------------
function m.GetGameEctypeBoxAward(stageID, index)
    local form = {}
    form["stageID"] = stageID
    form["index"] = index
    LuaModuleNew.SendRequest(LuaRequestID.GetEctypeTreasureBoxRewards, form)
end

--------------------------------------------------------------------
-- 出征
--------------------------------------------------------------------
function m.ChuZheng(stageID)
    local cfg = Schemes.CatMainStage:Get(stageID)
    if not cfg then
        return
    end
    --不能使用原有关闭
    -- m:CloseSelf()
    m.lastCatMainStageID = GamePlayerData.GameEctype:GetChallengeableID(EctypeType)
    -- 进入战斗
    BattleManager:EnterBattle(cfg.ID)
end

--------------------------------------------------------------------
--每秒更新
--------------------------------------------------------------------
function m.OnSecondUpdate()
    if m.propertyButtonList and #m.propertyButtonList > 0 then
        for _, v in pairs(m.propertyButtonList) do
            v.SecondUpdate()
        end
    end
end

--------------------------------------------------------------------
-- 添加红点
--------------------------------------------------------------------
function m.AddRedDot(parent, offset)
    if not m.redDotItem then
        m.redDotItem = HotResManager.ReadUI('ui/Common/RedDot')
    end
    local dotObj = GameObject.Instantiate(m.redDotItem.gameObject, parent.transform)
    dotObj:GetRectTransform().anchoredPosition = offset or CachedVector2:Set(-6, -6)
    return dotObj
end

--------------------------------------------------------------------
---创建主按钮
---@param data MainButtonData 数据源
---@return MainButton
--------------------------------------------------------------------
function m.CreationMainButton(index, data, direct)
    ---@class MainButton 主按钮
    local item = {}
    item.index = index
    --按钮数据
    item.data = data
    if direct == 0 then
        item.objList = m:CreateSubItem(m.objList.Grid_Main, m.objList.Item_Button)
    else
        item.objList = m:CreateSubItem(m.objList.GridTop, m.objList.Item_Button)
    end

    item.objList.Img_Bg1.gameObject:SetActive(true)
    item.objList.Txt_Name1.text = data.name
    --.objList.Txt_Name2.text = data.name
    AtlasManager:AsyncGetSprite(item.data.icon1, item.objList.Img_Icon1)
    --AtlasManager:AsyncGetSprite(item.data.icon2, item.objList.Img_Icon2)

    --红点
    item.redDot = m.AddRedDot(item.objList.gameObject)
    item.redDot.gameObject:SetActive(false)
    m:AddClick(item.objList.Btn_Click, function()
        if UIManager:JudgeOpenLevel(item.data.windowID, false) then
            UIManager:OpenWnd(item.data.windowID)
        else
            local openLevel = UIManager:GetOpenLevel(item.data.windowID)
            HelperL.ShowMessage(TipType.FlowText, string.format(GetGameText(luaID, 80), openLevel))
        end
    end)

    --更新按钮
    item.UpdateButton = function()
        --判断开放等级
        if UIManager:JudgeOpenLevel(item.data.windowID, false) then
            HelperL.SetImageGray(item.objList.Img_Icon1, false)
            item.redDot.gameObject:SetActive(RedDotManager:GetRedDotCheck(item.data.windowID))
        else
            HelperL.SetImageGray(item.objList.Img_Icon1, true)
            item.redDot.gameObject:SetActive(false)
        end
    end
    return item
end

--------------------------------------------------------------------
---创建活动按钮
---@param data ActivityButtonData 按钮数据
---@return ActivityButton
--------------------------------------------------------------------
function m.CreationActivityButton(data)
    local parent = m.activityGridList[data.grid]
    ---@class ActivityButton 活动按钮
    local item = {}
    --按钮数据
    item.data = data
    item.objList = m:CreateSubItem(parent, m.objList.Item_Activity)
    item.objList.Txt_Name.text = data.name
    AtlasManager:AsyncGetSprite(data.icon, item.objList.Img_Icon)
    m:AddClick(item.objList.Btn_Click, function()
        if item.data.windowID == 10000 then
            if m.Ismove then return end
            m.Ismove = true
            if m.objList.GridTop_Right1.gameObject.transform.localPosition.x == 100 then
                m.objList.Btn_Mask1.gameObject:SetActive(true)
                m.objList.GridTop_Right1.transform:DOLocalMoveX(-800, 0.3)
                Timer.New(function()
                    m.Ismove = false
                end,  0.3, 1):Start()
            else
                m.objList.Btn_Mask1.gameObject:SetActive(false)
                m.objList.GridTop_Right1.transform:DOLocalMoveX(100, 0.3)
                Timer.New(function()
                    m.Ismove = false
                end,  0.3, 1):Start()
            end
        else
            if m.Ismove then return end
            if UIManager:JudgeOpenLevel(item.data.windowID, false) then
                UIManager:OpenWnd(item.data.windowID, m.GetOpenArgs(item.data))
            else
                local openLevel = UIManager:GetOpenLevel(item.data.windowID)
                HelperL.ShowMessage(TipType.FlowText, string.format(GetGameText(luaID, 80), openLevel))
            end
            
            m.objList.Btn_Mask1.gameObject:SetActive(false)
            m.objList.GridTop_Right1.transform:DOLocalMoveX(100, 0.3)
            Timer.New(function()
                m.Ismove = false
            end,  0.3, 1):Start()
            if item.data.windowID == 73 then
                --关掉手指引导
                EventManager:Fire(EventID.TaskShowHandGuide, 3)
            end
        end
    end)

    --更新按钮
    item.UpdateButton = function()
        --判断开放等级
        if UIManager:JudgeOpenLevel(item.data.windowID, false) then
            HelperL.SetImageGray(item.objList.Img_Icon, false)
        else
            HelperL.SetImageGray(item.objList.Img_Icon, true)
        end
    end

    --设置红点
    item.SetRedDot = function()
        m:SetWndRedDot(item.objList.gameObject, data.offset)
            :AddCheckParam(item.data.windowID, m.GetRedDotArgs(item.data))
    end
    return item
end

--------------------------------------------------------------------
---获取打开窗口参数
---@param data ActivityButtonData
---@return ...
--------------------------------------------------------------------
function m.GetOpenArgs(data)
    if data.id == 103 then
        return 7
    elseif data.id == 4 then
        return 25
    elseif data.id == 5 then
        return 26
    elseif data.id == 7 then
        local cardID2 = 0
        local cardID = HelperL.GetGoodsIDByCardID(3, function(id)
            local state = HelperL.GetAdverStateByCardID(id)
            if state == 1 then
                return true
            elseif state == 2 then
                if cardID2 == 0 then
                    cardID2 = id
                end
            end
            return false
        end)
        return cardID ~= 0 and cardID or cardID2
    elseif data.id == 8 then
        local commonText = Schemes.CommonText:Get(140)
        return commonText.Text, UIRecommendCommodities_Type.ATTENTION
    end
    return nil
end


--------------------------------------------------------------------
---获取红点检测参数
---@param data ActivityButtonData
---@return ...
--------------------------------------------------------------------
function m.GetRedDotArgs(data)
    if data.id == 4 then
        return 25
    elseif data.id == 5 then
        return 26
    elseif data.id == 7 then
        return -1
    elseif data.id == 8 then
        local commonText = Schemes.CommonText:Get(140)
        return commonText.Text
    elseif data.id == 103 then
        return 7
    end
    return nil
end

--------------------------------------------------------------------
-- 更新按钮
--------------------------------------------------------------------
function m.UpdateActivityButton()
    --更新活动按钮
    for k, v in pairs(m.activityButtonList) do
        --v.objList.gameObject:SetActive(m.CanBbeCreated(v.data))
    end
end

--------------------------------------------------------------------
---可创建按钮
---@param data ActivityButtonData
---@return boolean
--------------------------------------------------------------------
function m.CanBbeCreated(data)
    --判断创角时间(新玩家)
    local createTime = EntityModule.hero.ActorCreateTime
    if data.creationTime1 and createTime < data.creationTime1 then
        return false
    end

    --判断创角时间(老玩家)
    if data.creationTime2 and createTime > data.creationTime2 then
        return false
    end

    --判断活动时间
    local time = HelperL.GetServerTime()
    if data.activityTime and time > data.activityTime then
        return false
    end

    --判断开放等级
    if not UIManager:JudgeOpenLevel(data.windowID, false) then
        return false
    end

    if data.id == 1 then
        for _, v in ipairs(DiscountGift_ActvID) do
            if ActivityManager:IsActivityOpen(v) then
                return true
            end
        end
        return false
    elseif data.id == 2 then
        return not HelperL.HadBoughtCardID(20)
    elseif data.id == 4 then
        return not HelperL.HadBoughtCardID(25)
    elseif data.id == 5 then
        return not HelperL.HadBoughtCardID(26)
    elseif data.id == 6 then
        return true --SWITCH.SIGN_IN == true
    elseif data.id == 7 then
        --超值礼包已购买
        if HelperL.HadBoughtCardID(20) then
            local cardID2 = 0
            local cardID = HelperL.GetGoodsIDByCardID(3, function(id)
                local state = HelperL.GetAdverStateByCardID(id)
                if state == 1 then
                    return true
                elseif state == 2 then
                    if cardID2 == 0 then
                        cardID2 = id
                    end
                end
                return false
            end)
            return cardID ~= 0 or cardID2 ~= 0
        end
        return false
    elseif data.id == 8 then
        local state = HelperL.GetAdverState(140)
        return SWITCH.ATTENTION_URL == true and state == 1
    elseif data.id == 9 then
        local list = Schemes.CommonText:GetByTextType(5)
        local state = HelperL.GetAdverState(list[1].ID)
        return state == 1
    elseif data.id == 10 then
        return (HelperL.GetAdverState(16) == 1 and GameLuaAPI.GameChannel == GameLuaAPI.eChannel.eChannel_Douyin)
    elseif data.id == 101 then
        return not HelperL.IsNoAD()
          elseif data.id == 102 then
        return m.JudgeCatFriend()
    elseif data.id == 301 then
        local commonText = Schemes.CommonText:Get(116)
        --判断今日次数
        local num = HeroDataManager:GetLogicByte(commonText.Param4, commonText.Param5)
        if num < commonText.DayTime then
            local condition = HelperL.LunchPhase()
            if condition == 0 then
                return false
            end
            if num >= condition then
                return false
            end
        else
            return false
        end
    end
    return true
end

--------------------------------------------------------------------
--邀请界面奖励判断
--------------------------------------------------------------------
function m.JudgeCatFriend()
    for i, v in ipairs(Schemes.CatFriend.items) do
        --有奖励未领取或条件未达成
        if HeroDataManager:GetLogicBit(v.Save1, v.Save2) == 0 then
            return true
        end
    end
    return false
end

--------------------------------------------------------------------
-- 主按钮点击事件
--------------------------------------------------------------------
function m.OnClickMainButton(index)
    local item = m.mainButtonList[index]
    UIManager:OpenWnd(item.data.windowID)
end

--------------------------------------------------------------------
-- 更新资产
--------------------------------------------------------------------
function m.HeroPropertyUpdate_Silver(newValue, diff, PropID)
    if diff == 0 then return end
    if PropID == PLAYER_FIELD.PLAYER_FIELD_DIAMOND then
        AddSilverExpMessageQueue(GetGameText(luaID, 81), diff)
    end
end


--------------------------------------------------------------------
--新手引导,登录显示
--------------------------------------------------------------------
function m.Guide()
    if HelperL.BeginLogin then
        HelperL.BeginLogin = false
        local Age = EntityModule.VerifyInfo.Age
        if Age < 18 then
            local can, day = HelperL.IsCanGame()
            if can == true then
                local data = {
                    type = NotarizeWindowsType.Windows6,
                    content = GetGameText("HelperL", 49),
                }
                HelperL.NotarizeFCMUI(data)
                HelperL.CreateSmallRoleTime()
            else
                if day ~= 5 and day ~= 6 and day ~= 7 then
                    local data = {
                        type = NotarizeWindowsType.Windows3,
                        content = GetGameText("HelperL", 54),
                        isFCM = true,
                    }
                    HelperL.NotarizeFCMUI(data)
                else
                    local data = {
                        type = NotarizeWindowsType.Windows3,
                        content = GetGameText("HelperL", 49) .. GetGameText("HelperL", 55),
                        isFCM = true,
                    }
                    HelperL.NotarizeFCMUI(data)
                end                
            end
        else
            local actorID = EntityModule.hero:GetProperty(ENTITY_FIELD.ENTITY_FIELD_ID)
            if  PlayerPrefsManager:GetInt("StoryIntroduce"..actorID, 0) == 0 then
                PlayerPrefsManager:SetInt("StoryIntroduce"..actorID, 1)
                UIManager:OpenWnd(WndID.StoryIntroduce)
            end 
        end
        local heroLevel = EntityModule.hero:GetProperty(CREATURE_FIELD.CREATURE_FIELD_LEVEL)
        
        -- 角色等级=1时，执行一次【#逻辑值 200 300020】
        if heroLevel == 1 then
            local chatContent = "#逻辑值 200 300020"
            local msg = ChatMessage_pb.CS_Char_SendChat()
            msg.Channel = 12
            msg.Content = chatContent
            msg.ChatType = 0
            Premier.Instance:GetNetwork():SendFromLua(
                ENDPOINT.ENDPOINT_GAMECLIENT,
                ENDPOINT.ENDPOINT_GAMESERVER,
                MSG_MODULEID.MSG_MODULEID_CHAT,
                ChatMessage_pb.MSG_CHAT_SENDCHAT,
                msg:SerializeToString()
            )
            ----print("11111 角色等级1-强行修改默认出战装备ID=300020，命令:", chatContent)  -- 这是强行修改默认出战装备ID=300020
        end
        
        -- 角色等级大于等于1时，延迟1秒后根据逻辑值200设置出战装备
        if heroLevel >= 1 then
            Timer.New(function()
                -- ================================
                -- V1.2版本：完整的装备状态规范化逻辑
                -- ================================
                print("33333333 登录装备状态规范化------开始执行")
                
                local logicValue200 = HeroDataManager:GetLogicData(LOGIC_DATA.DATA_TRAIN_TOTALNUM)
                print("33333333 登录装备状态规范化------读取逻辑值200:", logicValue200)
                
                -- 获取当前所有出战装备
                local currentEquipIDs = GamePlayerData.ActorEquip:GetWeaponsKnapsack(1) or {}
                print("33333333 登录装备状态规范化------当前出战装备列表:", json.encode(currentEquipIDs))
                
                -- 统计当前主装备(GroupID<100)数量
                local mainEquipList = {}
                local otherEquipList = {}
                
                for _, equipID in ipairs(currentEquipIDs) do
                    if equipID and equipID ~= 0 then
                        local equipWeaponConfig = Schemes.EquipWeapon:Get(equipID)
                        if equipWeaponConfig then
                            if equipWeaponConfig.GroupID < 100 then
                                table.insert(mainEquipList, equipID)
                            else
                                table.insert(otherEquipList, equipID)
                            end
                        end
                    end
                end
                
                print("33333333 登录装备状态规范化------当前主装备数量:", #mainEquipList, "列表:", json.encode(mainEquipList))
                print("33333333 登录装备状态规范化------当前其他装备数量:", #otherEquipList, "列表:", json.encode(otherEquipList))
                
                -- 确定目标主装备ID
                local targetMainEquipID = 300020 -- 默认装备ID
                
                if logicValue200 and logicValue200 > 0 then
                    local equipConfig = Schemes.Equipment:Get(logicValue200)
                    local equipWeaponConfig = Schemes.EquipWeapon:Get(logicValue200)
                    
                    if equipConfig and equipWeaponConfig and equipWeaponConfig.GroupID < 100 then
                        targetMainEquipID = logicValue200
                        print("33333333 登录装备状态规范化------使用逻辑值200作为目标主装备:", targetMainEquipID)
                    else
                        print("33333333 登录装备状态规范化------逻辑值200无效或不是主装备，使用默认装备")
                    end
                else
                    print("33333333 登录装备状态规范化------逻辑值200无效，使用默认装备")
                end
                
                -- 获取多层关联装备
                local function GetAllAssociatedEquipIDsForLogin(equipId)
                    local associatedEquipIDs = {}
                    local currentEquipId = equipId
                    local visitedEquipIDs = {}
                    
                    while true do
                        if visitedEquipIDs[currentEquipId] then
                            print("33333333 登录装备状态规范化------检测到循环引用，停止查找关联装备")
                            break
                        end
                        visitedEquipIDs[currentEquipId] = true
                        
                        local equipWeaponConfig = Schemes.EquipWeapon:Get(currentEquipId)
                        if not equipWeaponConfig then
                            break
                        end
                        
                        if equipWeaponConfig.EffectID3 and equipWeaponConfig.EffectID3 ~= 0 then
                            local associatedId = equipWeaponConfig.EffectID3
                            table.insert(associatedEquipIDs, associatedId)
                            print("33333333 登录装备状态规范化------找到关联装备:", associatedId)
                            currentEquipId = associatedId
                        else
                            break
                        end
                    end
                    
                    return associatedEquipIDs
                end
                
                local targetAssociatedEquipIDs = GetAllAssociatedEquipIDsForLogin(targetMainEquipID)
                print("33333333 登录装备状态规范化------目标关联装备列表:", json.encode(targetAssociatedEquipIDs))
                
                -- 构建正确的出战装备列表
                local correctEquipList = {targetMainEquipID}
                for _, associatedId in ipairs(targetAssociatedEquipIDs) do
                    table.insert(correctEquipList, associatedId)
                end
                
                print("33333333 登录装备状态规范化------正确的出战装备列表:", json.encode(correctEquipList))
                
                -- 检查当前状态是否已经正确
                local needUpdate = false
                
                -- 1. 检查主装备数量是否为1且是目标装备
                if #mainEquipList ~= 1 or mainEquipList[1] ~= targetMainEquipID then
                    needUpdate = true
                    print("33333333 登录装备状态规范化------主装备状态不正确，需要更新")
                end
                
                -- 2. 检查关联装备是否正确
                if not needUpdate then
                    local currentAssociatedInWear = {}
                    for _, equipID in ipairs(otherEquipList) do
                        local equipWeaponConfig = Schemes.EquipWeapon:Get(equipID)
                        if equipWeaponConfig and equipWeaponConfig.GroupID >= 100 and equipWeaponConfig.GroupID < 1000 then
                            table.insert(currentAssociatedInWear, equipID)
                        end
                    end
                    
                    -- 比较关联装备列表
                    if #currentAssociatedInWear ~= #targetAssociatedEquipIDs then
                        needUpdate = true
                        print("33333333 登录装备状态规范化------关联装备数量不匹配，需要更新")
                    else
                        for _, targetId in ipairs(targetAssociatedEquipIDs) do
                            local found = false
                            for _, currentId in ipairs(currentAssociatedInWear) do
                                if currentId == targetId then
                                    found = true
                                    break
                                end
                            end
                            if not found then
                                needUpdate = true
                                print("33333333 登录装备状态规范化------关联装备不匹配，需要更新")
                                break
                            end
                        end
                    end
                end
                
                if needUpdate then
                    print("33333333 登录装备状态规范化------开始更新装备状态")
                    
                    -- 使用LogicValue.SetWeaponsKnapsack一次性设置正确的装备列表
                    LogicValue.SetWeaponsKnapsack(correctEquipList)
                    
                    print("33333333 登录装备状态规范化------装备状态更新完成")
                    
                    -- 验证更新结果
                    Timer.New(function()
                        local verifyEquipIDs = GamePlayerData.ActorEquip:GetWeaponsKnapsack(1) or {}
                        print("33333333 登录装备状态规范化------更新后验证装备列表:", json.encode(verifyEquipIDs))
                        
                        -- 统计验证结果
                        local verifyMainCount = 0
                        local verifyAssociatedCount = 0
                        for _, equipID in ipairs(verifyEquipIDs) do
                            if equipID and equipID ~= 0 then
                                local equipWeaponConfig = Schemes.EquipWeapon:Get(equipID)
                                if equipWeaponConfig then
                                    if equipWeaponConfig.GroupID < 100 then
                                        verifyMainCount = verifyMainCount + 1
                                    elseif equipWeaponConfig.GroupID >= 100 and equipWeaponConfig.GroupID < 1000 then
                                        verifyAssociatedCount = verifyAssociatedCount + 1
                                    end
                                end
                            end
                        end
                        
                        print("33333333 登录装备状态规范化------验证结果: 主装备数量=" .. verifyMainCount .. " 关联装备数量=" .. verifyAssociatedCount)
                        
                        if verifyMainCount == 1 and verifyAssociatedCount == #targetAssociatedEquipIDs then
                            print("33333333 登录装备状态规范化------装备状态规范化成功！")
                        else
                            print("33333333 登录装备状态规范化------装备状态规范化可能存在问题，请检查")
                        end
                    end, 0.5, 1):Start()
                else
                    print("33333333 登录装备状态规范化------当前装备状态已经正确，无需更新")
                end
                
                print("33333333 登录装备状态规范化------逻辑完成")
            end, 1, 1):Start()
        end
        
        -- 玩家登录游戏后，立即打印一次#逻辑值 200的值，后面每5秒打印1次，执行1分钟
        local logicValue200 = HeroDataManager:GetLogicData(LOGIC_DATA.DATA_TRAIN_TOTALNUM)
        ----print("11111 登录后-逻辑值200当前值:", logicValue200)
        
        -- 同时打印当前出战装备和关联装备信息
        local currentEquipIDs = GamePlayerData.ActorEquip:GetWeaponsKnapsack(1) or {}
        local actualEquipCount = 0
        local currentEquipList = {}
        local associatedEquipInfo = {}
        
        -- 统计实际出战装备（排除0值）
        for _, equipID in ipairs(currentEquipIDs) do
            if equipID and equipID ~= 0 then
                actualEquipCount = actualEquipCount + 1
                table.insert(currentEquipList, equipID)
                
                -- 检查每个出战装备的关联装备
                local equipWeaponConfig = Schemes.EquipWeapon:Get(equipID)
                if equipWeaponConfig and equipWeaponConfig.EffectID3 and equipWeaponConfig.EffectID3 ~= 0 then
                    table.insert(associatedEquipInfo, string.format("装备%d->关联%d", equipID, equipWeaponConfig.EffectID3))
                else
                    table.insert(associatedEquipInfo, string.format("装备%d->无关联", equipID))
                end
            end
        end
        
        ----print("11111 登录后-当前出战装备数量:", actualEquipCount)
        ----print("11111 登录后-当前出战装备列表:", json.encode(currentEquipList))
        ----print("11111 登录后-关联装备信息:", table.concat(associatedEquipInfo, "，"))
        
        -- 定时器：每5秒打印1次#逻辑值 200的值，执行1分钟（12次）
        local printCount = 0
        local maxPrintCount = 12  -- 1分钟 / 5秒 = 12次
        m.logicValueTimer = Timer.New(function()
            printCount = printCount + 1
            local currentValue = HeroDataManager:GetLogicData(LOGIC_DATA.DATA_TRAIN_TOTALNUM)
            ----print("11111 定时打印-逻辑值200值:", currentValue, "第", printCount, "次")
            
            -- 每次也打印当前出战装备信息
            local timerEquipIDs = GamePlayerData.ActorEquip:GetWeaponsKnapsack(1) or {}
            local timerEquipCount = 0
            local timerEquipList = {}
            for _, equipID in ipairs(timerEquipIDs) do
                if equipID and equipID ~= 0 then
                    timerEquipCount = timerEquipCount + 1
                    table.insert(timerEquipList, equipID)
                end
            end
            ----print("11111 定时打印-当前出战装备:", json.encode(timerEquipList))
            
            if printCount >= maxPrintCount then
                if m.logicValueTimer then
                    m.logicValueTimer:Stop()
                    m.logicValueTimer = nil
                end
            end
        end, 5, maxPrintCount)
        m.logicValueTimer:Start()
        
        if heroLevel < 2 and GuideManager:GetCurGuideType() == 0 then --开始
            -- UIManager:OpenWnd(WndID.RuleTip, GetGameText(luaID, 85), GetGameText(luaID, 84), function()
            UIManager:OpenWnd(WndID.TaskDialogue, 1, function()
                --m.StartGuide(GuideManager.EventType.BeginGameGuide)
                GuideManager:OnGuideStart(1)
            end, true)
            -- end)
            return
        end
    end

    
    -- m.counter = 0
    -- m.smallTimer= Timer.New(function()	
    --     m.counter = m.counter + 1
    --     ----print("-----------m.counter------------------------------------",m.counter)
    --     if m.counter == 20 then	
    --         local luaID2 = ('HelperL')
    --         local data = {
    --             type = NotarizeWindowsType.Windows3,
    --             content = GetGameText(luaID2, 49) .. GetGameText(luaID2, 56),
    --             isFCM = true,
    --         }
    --         HelperL.NotarizeFCMUI(data)
    --     end
	-- end, 1, 20)
	-- m.smallTimer:Start()
end

--------------------------------------------------------------------
-- 重置节点状态，确保节点可见
--------------------------------------------------------------------
function m.ResetNodesState()
   ---- ----print("=== ResetNodesState 开始重置节点状态 ===")
    
    -- V15.1版本：重置时也清除动画播放标志
    m.isAnimationPlaying = false
    
    -- 初始化原始位置存储表（如果不存在）
    if not m.originalPositions then
        m.originalPositions = {}
    end
    
    -- 重置MainLine下的线条节点（Img_Line1到Img_Line3）
    local lineNodes = {
        {m.objList.Img_Line1, "Img_Line1"},
        {m.objList.Img_Line2, "Img_Line2"}, 
        {m.objList.Img_Line3, "Img_Line3"}
    }
    
    for i, nodeData in ipairs(lineNodes) do
        local lineNode = nodeData[1]
        local nodeName = nodeData[2]
        if lineNode and lineNode.transform then
         ----   ----print(string.format("重置线条节点: %s", nodeName))
            
            -- 停止所有动画
            lineNode.transform:DOKill()
            
            -- 保存和恢复原始位置
            if not m.originalPositions[nodeName] then
                m.originalPositions[nodeName] = lineNode.transform.localPosition
            else
                lineNode.transform.localPosition = m.originalPositions[nodeName]
            end
            
            -- 重置缩放
            lineNode.transform.localScale = Vector3(1, 1, 1)
            
            -- 移除并重新创建CanvasGroup，确保透明度正确
            local oldCanvasGroup = lineNode.gameObject:GetComponent('CanvasGroup')
            if oldCanvasGroup then
                GameObject.DestroyImmediate(oldCanvasGroup)
            end
            local newCanvasGroup = lineNode.gameObject:AddComponent(typeof(UnityEngine.CanvasGroup))
            newCanvasGroup.alpha = 1.0
            newCanvasGroup.interactable = true
            newCanvasGroup.blocksRaycasts = true
            
            -- 确保节点激活
            lineNode.gameObject:SetActive(true)
            
          ----  ----print(string.format("%s 重置完成: Active=%s, Scale=%s, Alpha=%s", 
          ----      nodeName, 
          ----      tostring(lineNode.gameObject.activeSelf),
          ----      tostring(lineNode.transform.localScale),
          ----      tostring(newCanvasGroup.alpha)
          ----  ))
        else
        ----    ----print(string.format("警告: %s 节点不存在", nodeName))
        end
    end
    
    -- 重置MainEctype下的关卡按钮节点（MainItem1到MainItem4）
    local itemNodes = {
        {m.objList.MainItem1, "MainItem1"},
        {m.objList.MainItem2, "MainItem2"},
        {m.objList.MainItem3, "MainItem3"},
        {m.objList.MainItem4, "MainItem4"}
    }
    
    for i, nodeData in ipairs(itemNodes) do
        local itemNode = nodeData[1]
        local nodeName = nodeData[2]
        if itemNode and itemNode.transform then
            ----print(string.format("重置关卡节点: %s", nodeName))
            
            -- 停止所有动画
            itemNode.transform:DOKill()
            
            -- 保存和恢复原始位置
            if not m.originalPositions then
                m.originalPositions = {}
            end
            if not m.originalPositions[nodeName] then
                m.originalPositions[nodeName] = itemNode.transform.localPosition
            else
                itemNode.transform.localPosition = m.originalPositions[nodeName]
            end
            
            -- 重置缩放
            itemNode.transform.localScale = Vector3(1, 1, 1)
            
            -- 移除并重新创建CanvasGroup，确保透明度正确
            local oldCanvasGroup = itemNode.gameObject:GetComponent('CanvasGroup')
            if oldCanvasGroup then
                GameObject.DestroyImmediate(oldCanvasGroup)
            end
            local newCanvasGroup = itemNode.gameObject:AddComponent(typeof(UnityEngine.CanvasGroup))
            newCanvasGroup.alpha = 1.0
            newCanvasGroup.interactable = true
            newCanvasGroup.blocksRaycasts = true
            
            -- 确保节点激活
            itemNode.gameObject:SetActive(true)
            
        ----    ----print(string.format("%s 重置完成: Active=%s, Scale=%s, Alpha=%s", 
        ----        nodeName, 
        ----        tostring(itemNode.gameObject.activeSelf),
        ----        tostring(itemNode.transform.localScale),
        ----        tostring(newCanvasGroup.alpha)
        ----    ))
        else
        ----    ----print(string.format("警告: %s 节点不存在", nodeName))
        end
    end
    
    ----print("=== ResetNodesState 重置完成 ===")
end

--------------------------------------------------------------------
-- 强制确保节点可见（更激进的重置方法）
--------------------------------------------------------------------
function m.ForceEnsureNodesVisible()
    -- 直接检查并强制设置节点状态，不管之前的状态如何
    local allNodes = {
        {m.objList.Img_Line1, "Img_Line1"},
        {m.objList.Img_Line2, "Img_Line2"}, 
        {m.objList.Img_Line3, "Img_Line3"},
        {m.objList.MainItem1, "MainItem1"},
        {m.objList.MainItem2, "MainItem2"},
        {m.objList.MainItem3, "MainItem3"},
        {m.objList.MainItem4, "MainItem4"}
    }
    
    for i, nodeData in ipairs(allNodes) do
        local node = nodeData[1]
        local nodeName = nodeData[2]
        if node then
            -- 强制激活节点
            if not node.gameObject.activeSelf then
            ----    ----print(string.format("强制激活节点: %s", nodeName))
                node.gameObject:SetActive(true)
            end
            
            if node.transform then
                -- 强制重置缩放
                if node.transform.localScale ~= Vector3(1, 1, 1) then
                    ----print(string.format("强制重置缩放: %s", nodeName))
                    node.transform.localScale = Vector3(1, 1, 1)
                end
                
                -- 检查并修复CanvasGroup
                local canvasGroup = node.gameObject:GetComponent('CanvasGroup')
                if canvasGroup and canvasGroup.alpha < 1.0 then
              ----      ----print(string.format("强制重置透明度: %s (原透明度: %s)", nodeName, tostring(canvasGroup.alpha)))
                    canvasGroup.alpha = 1.0
                    canvasGroup.interactable = true
                    canvasGroup.blocksRaycasts = true
                end
            end
        end
    end
end

--------------------------------------------------------------------
-- 播放进入动画
--------------------------------------------------------------------
function m.PlayEnterAnimation()
    ----print("=== V15.1 PlayEnterAnimation 开始播放动画 ===")
    
    -- V15.1版本：设置动画播放标志，防止UpdateView干扰
    m.isAnimationPlaying = true
    
    -- 动画参数配置
    local animDuration = 0.4  -- 动画持续时间
    local staggerDelay = 0.1 -- 错开延迟时间
    local scaleStart = 0.3    -- 初始缩放
    local scaleEnd = 1.0      -- 结束缩放
    local moveDistance = 50   -- 移动距离
    local fadeStart = 0.0     -- 初始透明度
    local fadeEnd = 1.0       -- 结束透明度
    
    -- 计算动画总时长，用于清除动画播放标志
    local totalAnimationTime = (3 * staggerDelay) + animDuration + 0.2 -- 最长的动画时间
    
    -- MainLine下的线条动画（Img_Line1到Img_Line3）
    local lineNodes = {
        m.objList.Img_Line1,
        m.objList.Img_Line2, 
        m.objList.Img_Line3
    }
    
    for i, lineNode in ipairs(lineNodes) do
        if lineNode and lineNode.transform then
            local delay = (i - 1) * staggerDelay
            
            -- 停止之前的动画
            lineNode.transform:DOKill()
            
            -- 获取原始位置和缩放（从存储表中或当前值）
            local nodeName = "Img_Line" .. i
            if not m.originalPositions then
                m.originalPositions = {}
            end
            if not m.originalPositions[nodeName] then
                m.originalPositions[nodeName] = lineNode.transform.localPosition
            end
            local originalPos = m.originalPositions[nodeName]
            local originalScale = lineNode.transform.localScale
            
            -- 初始化状态：缩小、透明、位置偏移
            lineNode.transform.localScale = Vector3(scaleStart, scaleStart, 1)
            lineNode.transform.localPosition = Vector3(originalPos.x, originalPos.y - moveDistance, originalPos.z)
            
            -- 设置初始透明度
            local canvasGroup = lineNode.gameObject:GetComponent('CanvasGroup')
            if not canvasGroup then
                canvasGroup = lineNode.gameObject:AddComponent(typeof(UnityEngine.CanvasGroup))
            end
            canvasGroup.alpha = fadeStart
            
            -- 创建动画序列
            local sequence = DOTween.Sequence()
            sequence:AppendInterval(delay)
            
            -- 同时播放缩放、移动、透明度动画
            sequence:Append(lineNode.transform:DOScale(originalScale, animDuration):SetEase(TweeningEase.OutBack))
            sequence:Join(lineNode.transform:DOLocalMove(originalPos, animDuration):SetEase(TweeningEase.OutCubic))
            sequence:Join(canvasGroup:DOFade(fadeEnd, animDuration):SetEase(TweeningEase.OutCubic))
        end
    end
    
    -- MainEctype下的关卡按钮动画（MainItem1到MainItem4）
    local itemNodes = {
        m.objList.MainItem1,
        m.objList.MainItem2,
        m.objList.MainItem3,
        m.objList.MainItem4
    }
    
    for i, itemNode in ipairs(itemNodes) do
        if itemNode and itemNode.transform then
            local delay = (i - 1) * staggerDelay + 0.2 -- 在线条动画后开始
            
            -- 停止之前的动画
            itemNode.transform:DOKill()
            
            -- 获取原始位置和缩放（从存储表中或当前值）
            local nodeName = "MainItem" .. i
            if not m.originalPositions then
                m.originalPositions = {}
            end
            if not m.originalPositions[nodeName] then
                m.originalPositions[nodeName] = itemNode.transform.localPosition
            end
            local originalPos = m.originalPositions[nodeName]
            local originalScale = itemNode.transform.localScale
            
            -- 初始化状态：缩小、透明、位置偏移
            itemNode.transform.localScale = Vector3(scaleStart, scaleStart, 1)
            
            -- 添加随机偏移
            local randomOffset = Vector3(
                (math.random() - 0.5) * moveDistance * 2,
                (math.random() - 0.5) * moveDistance * 2,
                0
            )
            itemNode.transform.localPosition = Vector3(
                originalPos.x + randomOffset.x, 
                originalPos.y + randomOffset.y, 
                originalPos.z
            )
            
            -- 设置初始透明度
            local canvasGroup = itemNode.gameObject:GetComponent('CanvasGroup')
            if not canvasGroup then
                canvasGroup = itemNode.gameObject:AddComponent(typeof(UnityEngine.CanvasGroup))
            end
            canvasGroup.alpha = fadeStart
            
            -- 创建动画序列
            local sequence = DOTween.Sequence()
            sequence:AppendInterval(delay)
            
            -- 同时播放缩放、移动、透明度动画，添加弹性效果
            sequence:Append(itemNode.transform:DOScale(originalScale, animDuration):SetEase(TweeningEase.OutBack))
            sequence:Join(itemNode.transform:DOLocalMove(originalPos, animDuration):SetEase(TweeningEase.OutBack))
            sequence:Join(canvasGroup:DOFade(fadeEnd, animDuration):SetEase(TweeningEase.OutCubic))
            
            -- 添加悬浮效果（可选）
            sequence:OnComplete(function()
                if itemNode and itemNode.transform then
                    -- 轻微的悬浮动画
                    itemNode.transform:DOLocalMoveY(originalPos.y + 5, 2.0)
                        :SetEase(TweeningEase.InOutSine)
                        :SetLoops(-1, TweeningLoopType.Yoyo)
                end
                
                -- V15.1版本：最后一个动画完成时清除动画播放标志
                if i == #itemNodes then
                    Timer.New(function()
                        m.isAnimationPlaying = false
                        ----print("=== V15.1 动画播放完成，清除播放标志 ===")
                    end, 0.1, 1):Start()
                end
            end)
        end
    end
end

--------------------------------------------------------------------
-- 界面关闭时清理动画状态
--------------------------------------------------------------------
function m.OnClose()
    ----print("=== OnClose 开始清理界面状态 ===")
    
    -- V15.1版本：清除动画播放标志
    m.isAnimationPlaying = false
    
    -- 清理动画状态，防止节点在下次打开时不显示
    local allNodes = {
        {m.objList.Img_Line1, "Img_Line1"},
        {m.objList.Img_Line2, "Img_Line2"}, 
        {m.objList.Img_Line3, "Img_Line3"},
        {m.objList.MainItem1, "MainItem1"},
        {m.objList.MainItem2, "MainItem2"},
        {m.objList.MainItem3, "MainItem3"},
        {m.objList.MainItem4, "MainItem4"}
    }
    
    for i, nodeData in ipairs(allNodes) do
        local node = nodeData[1]
        local nodeName = nodeData[2]
        if node then
            ----print(string.format("清理节点: %s", nodeName))
            
            if node.transform then
                -- 停止所有DOTween动画
                node.transform:DOKill()
                
                -- 重置到正常状态
                node.transform.localScale = Vector3(1, 1, 1)
                if m.originalPositions and m.originalPositions[nodeName] then
                    node.transform.localPosition = m.originalPositions[nodeName]
                end
            end
            
            -- 移除CanvasGroup，让节点回到原始状态
            local canvasGroup = node.gameObject:GetComponent('CanvasGroup')
            if canvasGroup then
                GameObject.DestroyImmediate(canvasGroup)
            end
            
            -- 确保节点激活
            node.gameObject:SetActive(true)
        end
    end
    
    -- 清理定时器
    if m.logicValueTimer then
        m.logicValueTimer:Stop()
        m.logicValueTimer = nil
    end
    
    ----print("=== OnClose 清理完成 ===")
end

--------------------------------------------------------------------
-- 界面销毁时的清理（备用方法）
--------------------------------------------------------------------
function m.OnDestroy()
  ----  ----print("=== OnDestroy 开始销毁清理 ===")
    m.OnClose() -- 调用关闭清理
end

--------------------------------------------------------------------
-- V16.0版本：MainItem1-4消失动画（明显效果）
--------------------------------------------------------------------
function m.PlayMainItemsDisappearAnimation(stageID)
  ----  ----print("=== V16.0 开始播放MainItem1-4明显消失动画，stageID=" .. tostring(stageID) .. " ===")
    
    if not stageID then
    ----    ----print("=== V16.0 错误：stageID参数无效 ===")
        return
    end
    
    -- 禁用所有MainItem按钮，防止重复点击
    m.DisableMainItemButtons()
    
    -- 获取需要播放动画的MainItem节点
    local mainItemNodes = {
        {node = m.objList.MainItem1, name = "MainItem1"},
        {node = m.objList.MainItem2, name = "MainItem2"},
        {node = m.objList.MainItem3, name = "MainItem3"},
        {node = m.objList.MainItem4, name = "MainItem4"}
    }
    
   ---- ----print("=== V16.0 开始播放明显的消失动画 ===")
    
    -- 动画参数 - 更明显的效果
    local animationDuration = 1.0 -- 1秒动画时长
    local completedCount = 0
    local totalNodes = #mainItemNodes
    
    for i, nodeData in ipairs(mainItemNodes) do
        local node = nodeData.node
        local nodeName = nodeData.name
        
        if node and node.transform then
         ----   ----print("=== V16.0 开始节点消失动画: " .. nodeName .. " ===")
            
            -- 停止之前的动画
            node.transform:DOKill()
            
            -- 创建CanvasGroup控制透明度
            local canvasGroup = node.gameObject:GetComponent('CanvasGroup')
            if not canvasGroup then
                canvasGroup = node.gameObject:AddComponent(typeof(UnityEngine.CanvasGroup))
            end
            
            -- 创建更明显的消失动画序列
            local sequence = DOTween.Sequence()
            
            -- 添加延迟，让动画有先后顺序
            local delay = (i - 1) * 0.1 -- 每个节点间隔0.1秒
            sequence:AppendInterval(delay)
            
            -- 阶段1：震撼放大（0.3秒）
            sequence:Append(node.transform:DOScale(Vector3(1.5, 1.5, 1), animationDuration * 0.3):SetEase(TweeningEase.OutBack))
            
            -- 阶段2：快速旋转+缩小+透明（0.7秒）
            sequence:Append(node.transform:DOScale(Vector3(0, 0, 1), animationDuration * 0.7):SetEase(TweeningEase.InBack))
            sequence:Join(node.transform:DORotate(Vector3(0, 0, 1080), animationDuration * 0.7):SetEase(TweeningEase.InCubic)) -- 3圈旋转
            sequence:Join(canvasGroup:DOFade(0, animationDuration * 0.7):SetEase(TweeningEase.InCubic))
            
            -- 动画完成回调
            sequence:OnComplete(function()
                completedCount = completedCount + 1
             ----   ----print("=== V16.0 节点消失动画完成: " .. nodeName .. " (" .. completedCount .. "/" .. totalNodes .. ") ===")
                
                -- 所有动画完成后执行回调
                if completedCount >= totalNodes then
             ----       ----print("=== V16.0 所有节点消失动画完成，延迟0.2秒后打开界面 ===")
                    Timer.New(function()
                        -- 恢复按钮状态
                        m.EnableMainItemButtons()
                        
                        -- 动画完全结束后再打开界面
                ----        ----print("=== V16.0 动画完全结束，现在打开界面: stageID=" .. tostring(stageID) .. " ===")
                        UIManager:OpenWnd(WndID.NormalEctype, stageID)
                    end, 0.2, 1):Start()
                end
            end)
        else
         ----   ----print("=== V16.0 警告：节点无效: " .. nodeName .. " ===")
            completedCount = completedCount + 1
        end
    end
    
  ----  ----print("=== V16.0 所有明显消失动画已启动 ===")
end

--------------------------------------------------------------------
-- V16.0版本：禁用所有MainItem按钮
--------------------------------------------------------------------
function m.DisableMainItemButtons()
  ----  ----print("=== V16.0 禁用所有MainItem按钮 ===")
    for i = 1, #m.Item_Ectype_List do
        local item = m.Item_Ectype_List[i]
        if item and item.com and item.com.Btn_Click then
            item.com.Btn_Click.interactable = false
        end
    end
end

--------------------------------------------------------------------
-- V16.0版本：启用所有MainItem按钮  
--------------------------------------------------------------------
function m.EnableMainItemButtons()
  ----  ----print("=== V16.0 启用所有MainItem按钮 ===")
    for i = 1, #m.Item_Ectype_List do
        local item = m.Item_Ectype_List[i]
        if item and item.com and item.com.Btn_Click then
            item.com.Btn_Click.interactable = true
        end
    end
end

--------------------------------------------------------------------
-- V32.2-fix 针对Android平台的强化节点显示修复
--------------------------------------------------------------------
function m.EnsureNodesVisible()
    -- V32.2-fix 检查MainLine和MainEctype下的子节点，解决Android平台显示问题
    local checkNodes = {
        {node = m.objList.Img_Line1, name = "Img_Line1"},
        {node = m.objList.Img_Line2, name = "Img_Line2"}, 
        {node = m.objList.Img_Line3, name = "Img_Line3"},
        {node = m.objList.MainItem1, name = "MainItem1"},
        {node = m.objList.MainItem2, name = "MainItem2"},
        {node = m.objList.MainItem3, name = "MainItem3"},
        {node = m.objList.MainItem4, name = "MainItem4"}
    }
    
    -- V32.2-fix 先检查父节点确保激活
    if m.objList.MainLine and not m.objList.MainLine.gameObject.activeSelf then
        m.objList.MainLine.gameObject:SetActive(true)
    end
    if m.objList.MainEctype and not m.objList.MainEctype.gameObject.activeSelf then
        m.objList.MainEctype.gameObject:SetActive(true)
    end
    
    for _, nodeData in ipairs(checkNodes) do
        local node = nodeData.node
        local nodeName = nodeData.name
        
        if node then
            -- V32.2-fix 多重检查：激活状态、CanvasGroup透明度、Transform缩放
            local needsfix = false
            
            -- 检查1：节点激活状态
            if not node.gameObject.activeSelf then
                node.gameObject:SetActive(true)
                needsfix = true
            end
            
            -- 检查2：CanvasGroup透明度（Android平台常见问题）
            local canvasGroup = node.gameObject:GetComponent('CanvasGroup')
            if canvasGroup and canvasGroup.alpha <= 0.1 then
                canvasGroup.alpha = 1.0
                canvasGroup.interactable = true
                canvasGroup.blocksRaycasts = true
                needsfix = true
            end
            
            -- 检查3：Transform缩放不正确（Android平台常见问题）
            if node.transform then
                local currentScale = node.transform.localScale
           ----     ----print("=== V32.4-fix 检查节点 " .. nodeName .. " 当前缩放: (" .. currentScale.x .. "," .. currentScale.y .. "," .. currentScale.z .. ") ===")
                
                -- V32.4-fix 强制所有节点缩放为(1,1,1)，无论当前值是什么
                if currentScale.x ~= 1.0 or currentScale.y ~= 1.0 or currentScale.z ~= 1.0 then
                ----    ----print("=== V32.4-fix 强制重置节点缩放: " .. nodeName .. " 从(" .. currentScale.x .. "," .. currentScale.y .. "," .. currentScale.z .. ") 到(1,1,1) ===")
                    node.transform.localScale = Vector3(1, 1, 1)
                    needsfix = true
                else
                ----    ----print("=== V32.4-fix 节点 " .. nodeName .. " 缩放已正确 ===")
                end
            end
            
            -- V32.3-fix 如果需要修复，延迟再次检查确保修复生效
            if needsfix then
                Timer.New(function()
                    if node and node.gameObject then
                        -- 再次确保节点激活
                        if not node.gameObject.activeSelf then
                            node.gameObject:SetActive(true)
                        end
                        -- V32.4-fix 再次强制确保缩放正确
                        if node.transform then
                            local currentScale = node.transform.localScale
                       ----     ----print("=== V32.4-fix 延迟检查节点 " .. nodeName .. " 缩放: (" .. currentScale.x .. "," .. currentScale.y .. "," .. currentScale.z .. ") ===")
                            if currentScale.x ~= 1.0 or currentScale.y ~= 1.0 or currentScale.z ~= 1.0 then
                            ----    ----print("=== V32.4-fix 延迟二次修复节点缩放: " .. nodeName .. " 从(" .. currentScale.x .. "," .. currentScale.y .. "," .. currentScale.z .. ") 到(1,1,1) ===")
                                node.transform.localScale = Vector3(1, 1, 1)
                            else
                             ----   ----print("=== V32.4-fix 延迟检查节点 " .. nodeName .. " 缩放正确 ===")
                            end
                        end
                    end
                end, 0.1, 1):Start()
            end
        end
    end
end

return m
