﻿-- 游戏字符串定义文件
local gameText = {
	['UITipBestEquipment'] = {
		[1] = '战力',
		[2] = '自动装备',
		[3] = '等级：%s',
		[4] = '当前装备战力低于身上装备，替换后身上装备将消失，是否继续替换？',
		[5] = '是否分解战力高的装备',
		[6] = '当前装备',
		[7] = '可替换装备',
		[8] = '等级：%s/%s',
	},

	['UIEveryDayLevel'] = {
		[1] = '挑战;生存',
	},

	['SceneManager'] = {
		[1] = '正在加载场景！',
		[2] = '健康游戏忠告\n抵制不良游戏，拒绝盗版游戏。注意自我保护，谨防受骗上当。\n适度游戏益脑，沉迷游戏伤身。合理安排时间，享受健康生活。',
		[3] = '',
		[4] = '',
		[5] = '网络断开请重新登入！',
	},

	['HelperL'] = {
		[1] = '小时',
		[2] = '分',
		[3] = '秒',
		[4] = '天',
		[5] =
		'<color=#00ff00>投资</color>可以免费领取<color=#00ff00>海量灵晶</color>，<color=#00ff00>仙币</color>及<color=#00ff00>材料</color>',
		[6] = '想要获得<color=#efef30>传说熊猫</color>吗？\n<color=#efef30>传说熊猫</color>能一打五<color=#00ff00>普通熊猫</color>',
		[7] = '想要获得<color=#efef30>史诗熊猫</color>吗？\n<color=#efef30>史诗熊猫</color>能一打三<color=#00ff00>普通熊猫</color>',
		[8] = '想要获得<color=#efef30>超凡熊猫</color>吗？\n<color=#efef30>超凡熊猫</color>能一打十<color=#00ff00>普通熊猫</color>',
		[9] = '取消',
		[10] = '前往获取！',
		[11] =
		'<color=#FFA500>仙币不足！</color>\n<color=#00ff00>福利礼包</color>，<color=#00ff00>仙币商城</color>可获得仙币！购买<color=#00ff00>月卡</color>，战斗<color=#00ff00>仙币加成</color>最高<color=#00ff00>+55%</color>',
		[12] =
		'<color=#00ff00>福利礼包</color>，<color=#00ff00>仙币商城</color>可获得仙币！购买<color=#00ff00>月卡</color>，战斗<color=#00ff00>仙币加成</color>最高<color=#00ff00>+55%</color>',
		[13] = '获得新熊猫',
		[14] = '亿',
		[15] = '万',
		[16] = '装备属性ID',
		[17] = '生命',
		[18] = '攻击',
		[19] = '防御',
		[20] = '攻击',
		[21] = '法攻',
		[22] = '防御',
		[23] = '法防',
		[24] = '暴击',
		[25] = '防暴',
		[26] = '格挡',
		[27] = '穿透',
		[28] = '闪避',
		[29] = '命中',
		[30] = '护甲',
		[31] = '破甲',
		[32] = '伤害减免',
		[33] = '伤害加深',
		[34] = '爆伤',
		[35] = '生命',
		[36] = '获得 <color=%s>%s * %s</color>',
		[37] = '冷却中',
		[38] = '今日次数已用完！',
		[39] = '任务已完成！',
		[40] = '观看',
		[41] = '不足！',
		[42] = '白',
		[43] = '绿',
		[44] = '蓝',
		[45] = '紫',
		[46] = '金',
		[47] = '橙',
		[48] = '红',
		[49] =
		'您当前为未成年账号，根据国家新闻出版署\n《关于防止未成年人沉迷网络游戏的通知》和《关于进一步严格管理切实防止未成年人沉迷网络游戏的通知》，您可在周五、周六、周日及法定节假日20点-21点登录游戏，请合理安排时间。',
		[50] = '未满12周岁的用户，无法获得充值消费服务。',
		[51] = '%s周岁以上未满%s周岁的用户，单次充值金额不得超过%s元人民币，每月充值金额累计不得超过%s元人民币。',
		[52] = '您目前为未成年人账号，已经被纳入防沉迷系统。根据国家新闻出版署《关于防止未成年人沉迷网络游戏的通知》和《关于进一步严格管理切实防止未成年人沉迷网络游戏的通知》，您已超出支付上限，无法继续充值。',
		[53] = '\n您今日游戏时长已用完，请下线休息！',
		[54] =
		'您当前为未成年账号，根据国家新闻出版署《关于防止未成年人沉迷网络游戏的通知》和《关于进一步严格管理切实防止未成年人沉迷网络游戏的通知》，您可在周五、周六、周日及法定节假日20点-21点登录游戏，请合理安排时间。当前时间无法游玩，请下线休息!',
		[55] = '\n当前时间无法游玩，请下线休息！',
		[56] = '\n今日游戏时间已用完，请下线休息!',
	},

	
	--确认窗口
	['UIFCMWindows'] = {
		[1] = '提示',
		[2] = '今天不在提示',
		[3] = '',
		[4] = '',
		[5] = '',
		[6] = '',
	},

	['UIManager'] = {
		[1] = '%s级开启！',
	},

	['UIWndBase'] = {
	},

	['UILogin'] = {
		[1] = '记住账号',
		[2] = '进入游戏',
		[3] = '用户名不能为空！',
		[4] = '１、《封天霸业》是一款东方玄幻题材的角色扮演游戏。故事发生在天穹碎裂的九洲大陆，玩家将扮演三位身负特殊使命的主角，在永夜降临的世界中展开冒险，对抗黑暗势力，寻找拯救世界的方法。\n２、《封天霸业》适用于年满12周岁及以上的用户，建议未成年人在家长监护下进行游戏，并鼓励家长管理未成年人的游戏行为。\n３、本游戏不会与现实生活相混淆。在游戏战斗中通过操控角色攻击围攻上来的敌人，锻炼了用户的反应能力。本游戏无基于文字与语音的社交系统。 \n４、游戏内拥有用户实名认证系统，认证为未成年人的用户将受到以下限制：根据国家新闻出版署《关于防止未成年人沉迷网络游戏的通知》和《关于进一步严格管理　切实防止未成年人沉迷网络游戏的通知》和游戏运营规划安排，未满12周岁的用户不能充值；12周岁以上但未满16周岁的未成年用户，单次充值金额不得超过50元人民币，每月充值金额累计不得超过200元人民币；16周岁以上的未成年用户，单次充值金额不得超过100元人民币，每月充值金额累计不得超过400元人民币。未成年人用户仅可以在周五、周六、周日和法定节假日每日20时至21时进行游戏，其他时间均不得使用。',
		[5] = '确定',
		[6] = '请先登录账号！',
		[7] = '请先给自己取个名字吧！',
		[8] = '请先输入角色名！',
		[9] = '创建角色！',
		[10] = '角色名最多七个字！',
		[11] = '',
		[12] = '开始',
		[13] = '选项',
		[14] = '退出',
		[15] = '音乐',
		[16] = '音效',
		[17] = '重置',
		[18] = '返回',
		[19] = '打开',
		[20] = '关闭',
		[21] = '点击屏幕进入游戏！',
		[22] = '密码不能为空！',
		[23] = '当前名字存在敏感字符，请重新输入！',
		[24] = '适龄提示',
	},

	['UITipManager'] = {
	},

	['UIDisplayReward'] = {
		[1] = '获得奖励',
		[2] = '获得 <color=%s>%s * %s</color>',
		[3] = '提示',
		[4] = '今天不在提示',
		[5] = '<color=#00FF00>附魔</color>可获得<color=#00FF00>35分钟多倍银币</color>奖励！是否前往获得附魔？',
		[6] = '未获得附魔',
		[7] = '再领%d倍',
		[8] = '<color=#00ff00>额外再获得%s倍奖励</color>',
	},

	['LoginModule'] = {
		[1] = '进入游戏失败，请重新登录',
		[2] = '你已被封号，将于%s解封！',
		[3] = '连接服务器失败',
		[4] = '删除角色成功',
	},

	['EntityModule'] = {
	},

	['AIModule'] = {
		[1] = '<color=#30ff30>闪避</color>',
		[2] = '<color=#ff3030>暴击</color>',
		[3] = '<color=#30ff30>格挡</color>',
		[4] = '<color=#30ff30>吸血</color>',
		[5] = '<color=#ff3030>反弹伤害</color>',
	},

	['ChatModule'] = {
		[1] = '[FFFF00]【世界】[-]',
		[2] = '[00FF00]【军团】[-]',
		[3] = '[FFA500]【系统】[-]',
		[4] = '[E31DC9]【喇叭】[-]',
		[5] = '[0041FF]【阵营】[-]',
		[6] = '请输入聊天内容',
		[7] = '系统',
		[8] = '[00ff00]【团众】[-]',
		[9] = '[ffff00]【军团团长】[-]',
		[10] = '[00ff00]【军团副团长】[-]',
		[11] = '[00ff00]【军团长老】[-]',
		[12] = '语音转化失败',
		[13] = '[e71f19]语音消息[-]',
		[14] = '[cccccc]此消息为语音消息，请联系客服获取最新安装包！[-]',
	},

	['CountryModule'] = {
	},

	['EmailModule'] = {
	},

	['FriendModule'] = {
	},

	['GameModule'] = {
		[1] = '连接中…',
	},

	['RankModule'] = {
	},

	['SkepModule'] = {
		[1] = '该物品不能直接使用',
	},

	['SocietyModule'] = {
	},

	['StoreModule'] = {
	},

	['WorldModule'] = {
	},

	['UIMainTitle'] = {
		[1] = '功能未开启！',
		[2] = '请先通关第%s关。',
		[3] = '尚未解锁！',
		[4] = '条件不满足，功能未开启！',
		[5] = '<size=20><color=#00ff60>点击解锁！</color></size>',
		[6] = '<size=20><color=#00ff60>点击打开！</color></size>',
		[7] = '同时只能解锁一个宝箱！',
		[8] = '<color=#efef30>立即打开宝箱！</color>',
		[9] = '确定',
		[10] = '取消',
		[11] = '<color=#efef30>立即打开！</color>',
		[12] = '魂力：%s',
		[13] = '购买%s魂力',
		[14] = '购买',
		[15] = '看广告',
		[16] = '今日购买魂力次数已用完！\n<color=#30ef30>看广告</color>、<color=#30ef30>挑战模式</color>可获得魂力值！',
		[17] = '立即解锁！',
		[18] = '灵晶解锁！',
		[19] = '免费解锁！',
		[20] = '<color=#efef30>你可以立即解锁！</color>',
		[21] = '<color=#efef30>免费立即解锁！</color>',
		[22] = '宝箱规则！',
		[23] = '熊猫',
		[24] = '福利',
		[25] = '战斗',
		[26] = '活动',
		[27] = '排行榜',
		[28] = '<color=#00ff00>灵晶 +%d</color>',
		[29] = '<color=#00ff00>仙币 +%d</color>',
		[30] = '<color=#00ff00>魂力 +%d</color>',
		[31] = '%d段%d星',
		[32] = '招财猫',
		[33] = '秘密来电',
		[34] = '月卡',
		[35] = '投资',
		[36] = '每日特惠',
		[37] = '超值礼包',
		[38] = '排行',
		[39] = '手册',
		[40] = '充值',
		[41] = '签到',
		[42] = '超值商城',
		[43] = '坦克夺宝',
		[44] = '高级召唤',
		[45] = '角色',
		[46] = '部件',
		[47] = '实验室',
		[48] = '商店',
		[49] = '设置',
		[50] = '邮件',
		[51] = '日常',
		[52] = '成就',
		[53] = '任务',
		[54] = '',
		[55] = '十连抽',
		[56] = '角色',
		[57] = '购买',
		[58] = '开战',
		[59] = '出击',
		[60] = '回到总部',
		[61] = '进入岛屿',
		[62] = '回到地图',
		[63] = '生存',
		[64] = '挑战',
		[65] = '仙币',
		[66] = '巡逻',
		[67] = '角色',
		[68] = '伙伴',
		[69] = '通关主线第%d关后解锁!',
		[70] = '<color=#FFA500>灵晶不足!</color>',
		[71] = '<color=#FFA500>今日购买魂力次数已用完!</color>',
		[72] = '<color=#FFA500>今日看广告次数已用完!</color>',
		[73] = '通关可领取！',
		[74] = '伙伴',
		[75] = '免广告',
		[76] = '宝箱',
		[77] = '角色',
		[78] = 'Buff',
		[79] = '章节礼包',
		[80] = '通关%s关开启!',
		[81] = '灵晶',
		[82] = '体魄直购!',
		[83] = '伙伴直购',
		[84] = '',
		[85] = '',
		[86] = '',
		[87] = "第%s波",
	},

	['UILevelSelect'] = {
		[1] = '第一章',
		[2] = '返回',
		[3] = '上一章',
		[4] = '下一章',
		[5] = '关卡未解锁',
		[6] = '预告',
		[7] = '特点：%s',
		[8] = '攻击：%s',
		[9] = '获得：<color=#30ff30>第%d章第%d关</color>',
		[10] = '出现：<color=#30ff30>第%d章第%d关</color>',
		[11] = '通关剧情模式%s%s开放',
	},

	['UITowerBattleMain'] = {
		[1] = '布阵',
		[2] = '立即战斗',
		[3] = '%s(第%s波)',
		[4] = '确定要退出关卡吗？',
		[5] = '资源不够不能用',
		[6] = '剩余时间：',
		[7] = '功能未开启',
		[8] = '确认',
		[9] = '取消',
		[10] = '已达到数量上限',
		[11] = '<color=#FFA500>本关不可使用熊猫</color>',
		[12] = '引导中不能退出',
		[13] = '重新布阵',
		[14] = '开始战斗',
		[15] = '战斗进行中,不能操作',
		[16] = '未获得',
		[17] = '按住地图上已经出动的熊猫拖动可以改变站位',
		[18] = '<color=#FFA500>不可出战</color>',
		[19] = '<color=#FFA500>没有此类熊猫</color>',
		[20] = '正确布阵<color=#30ef30>不保证能赢</color>，但可<color=#30ef30>提高胜率</color>。您可自己尝试其他更好的布阵',
		[21] = '出战熊猫数量不足，不能开始战斗\n点击下方熊猫头像，操作熊猫出战',
		[22] = '<color=#00ff00>已出战</color>',
		[23] = '<color=#B6BABA>已阵亡</color>',
		[24] = '<color=#FFA500>已参与过上一回合战斗的熊猫不能取下！</color>',
		[25] = '<color=#FF00FF>已出战</color>',
		[26] = '一键布阵',
		[27] = '布阵诀窍：\n把防御熊猫移到前面，保护自己的攻击熊猫\n优先打掉对方所有的攻击熊猫，可以提高胜率',
	},

	['UITowerBattleResult'] = {
		[1] = '确定',
		[2] = '<color=#efef30>传说熊猫</color>属性比普通熊猫高最少<color=#efef30>6倍</color>！',
		[3] = '刷新<color=#efef30>仙币商城</color>可有概率<color=#efef30>3折</color>买仙币！',
		[4] = '刷新<color=#efef30>材料商店</color>可免费领材料！',
		[5] = '<color=#FFA2FF>提示：兵种搭配是获胜的关键！\n您可以尝试换一些兵种再挑战！</color>',
		[6] = '时间到',
		[7] = '<color=#efef30>购买月卡，</color>获得仙币最高<color=#efef30>+55%</color>！',
		[8] = '星',
		[9] = '返回',
		[10] = '下一关！',
		[11] = '熊猫升级！',
		[12] = '继续挑战！',
		[15] = '<color=#FFA500>段位挑战失败，降一星！</color>',
		[16] = '不降星！',
		[17] = '看广告不降星！',
		[18] = '返回<color=#FFFF00FF>(降一星)！</color>',
		[19] = '灵晶不足！',
		[20] = '三倍领取！',
		[21] = '布阵非常重要，正确的布阵胜率大幅提高！',
		[22] = '升级和突破，提升熊猫大量属性！',
		[23] = '尝试不同的熊猫搭配！',
		[24] = '升级可激活熊猫特殊附加效果！',
		[25] = '你可以通过以下方式提升实力！',
		[26] = '看广告！',
		[27] = '取消',
		[28] = '剩余魂力不足\n<color=#30ef30>灵晶购买</color>、<color=#30ef30>看广告</color>、<color=#30ef30>挑战模式！</color>可获得魂力值',
		[29] = '前往购买！',
		[30] = '购买%s魂力！',
		[31] = '购买',
		[32] = '购买次数已用完！',
		[33] = '魂力不足！',
		[34] = '点击立即前往！',
		[35] = '今日挑战次数已用完！',
		[36] = '挑战',
		[37] = '今日免费次数已用完，继续挑战需%d',
		[38] = '灵晶不足！',
	},

	['UITowerBattleMatch'] = {
		[1] = '返回',
		[2] = '卡组设置',
		[3] = '开始匹配',
		[4] = '人数：<color=#30ff30>%d</color>',
		[5] = '胜率：%.1f%%<color=#30ff30>(%d胜/%d败)</color>',
		[6] = '匹配中',
		[7] = '1v1即时对战',
		[8] = '取消匹配',
		[9] =
		'1.获胜一场，<color=#30ef30>段位+1星</color>；\n2.失败一场，<color=#30ef30>段位-1星</color>；\n3.每个段位<color=#30ef30>首次宝箱奖励</color>可获得<color=#30ef30>熊猫激活碎片</color>。\n4.<color=#30ef30>奖励</color>：大量仙币、熊猫碎片、升级材料',
		[10] = '<color=#00ff00>奖励礼包</color>',
		[11] = '奖励预览',
		[12] = '取消',
		[13] = '确定',
		[14] = '领取',
		[15] = '<color=#ffffff>已领取</color>',
		[16] = '<color=#00ff00>可领取</color>',
		[17] = '<color=#6b6b6b>未达成</color>',
		[18] = '段位赛游戏规则说明',
		[19] = '(%d/%d)',
		[20] = '今日挑战次数已用完！',
		[21] =
		'玩法规则：\n1. 战斗分为<color=#30ef30>多个回合</color>。\n2. <color=#30ef30>每回合</color>之间有<color=#30ef30>一定时间暂停</color>，可进行<color=#30ef30>布阵</color>操作。\n3. <color=#30ef30>每只熊猫</color>只能<color=#30ef30>出战一次</color>。\n4. 战斗初始，<color=#30ef30>双方</color>均有<color=#30ef30>3颗红心</color>。\n5. <color=#30ef30>每回合</color>熊猫全部<color=#30ef30>阵亡</color>的一方，<color=#30ef30>损失1颗心</color>。\n6. <color=#30ef30>任意一方损失3颗心</color>，则<color=#30ef30>失败</color>，战斗结束！\n\n<color=#30ef30>奖励</color>：大量仙币、熊猫碎片、升级材料',
		[22] = '挑战',
		[23] = '今日免费次数已用完，继续挑战需%d',
		[24] = '灵晶不足！',
	},

	['UIStoryIntroduce'] = {
		[1] = '　　大荒历987年霜月，九洲大陆的天空如琉璃般碎裂，漆黑裂缝吞噬星光。被封印千年的魔主"罗睺"自归墟深渊破封而出，九幽魔气如潮水席卷三界。然而，这并非偶然——天界几位巨头为了维护自身统治，暗中操控着三界秩序，将三界生灵视为棋子。七日内，云中城的天皇玉座崩塌，青丘狐族化作灰烬，赤焰军十万将士折戟北境。天界、人界、冥界间的屏障接连瓦解，永夜降临，世间陷入无尽黑暗。\n　　唯有云梦泽，青丘最后的大祭司白璃以全族灵韵为引，化作光之结界，守护住最后一缕黎明火种。他发现了天界的阴谋——魔主的复苏正是天界为了重新洗牌三界格局而设下的局。传说中，当破军星再度闪耀，三位天命之人将集结，不仅要终结这场"永夜之劫"，更要执行一个前所未有的计划："封天计划"。这个计划的核心是要彻底摧毁天界几位巨头对三界的操控，打破千年来的枷锁，让三界重获自由。而一旦成功破除天界布下的万古死局，三界生灵将迎来真正的解放，破局之人，也能借此获得三界气运加持成就无上霸业！\n　　在九洲边缘的清风村，一个坐落在青翠山谷的小村庄，村民依靠狩猎与采集维生。这里居住着十八岁的游侠玄戈，一个天性孤傲的见习剑客。他的父母在他幼年时失踪，留给他一枚镶嵌在长刀柄上的星纹残铁。村里的老道士云游抚养他长大，教他玄武刀法与野外生存的技巧。玄戈对残铁的来历一无所知，但每当握紧刀柄，他总能感到一股炽热的力量，仿佛在指引他前往远方。\n　　与此同时，在云梦泽深处，青丘狐族大祭司白璃正承受着维持结界的巨大痛苦。作为天生的灵韵掌控者，他能感知到天界巨头对三界生灵的蔑视和操控。白璃心中燃烧着复仇的火焰——不仅要为族人报仇，更要让高高在上的天界巨头付出代价。然而，此时的他因为伤势过重，法力十不存一，只能等待恢复后再进行下一步。\n　　而在北境战场，赤焰军的最后一位将领赤鸢正率领残部进行最后的抵抗。这位英勇的女将军目睹了天界如何抛弃人族，如何将凡人当作消耗品。她的心中只有一个信念：人族必须自强，必须摆脱天界的枷锁，去建立属于自己的霸业。赤鸢的战意如烈火般炽热，她相信只有通过战斗才能赢得真正的自由。\n　　清风村的生活平静而单调，唯一的威胁是偶尔从归墟荒原徘徊而来的低级魔物。玄戈与村里的伙伴组成巡狩小队，负责清理这些威胁，守护村庄的安全。他梦想着有朝一日能离开村庄，探索九洲的广袤世界，寻找父母的下落。然而，老道士云游告诫他："外面的世界已被魔气笼罩，更可怕的是天界的操控。唯有天命选中的人才能打破这枷锁，执行传说中的封天计划，成就无上霸业。"\n　　一切在玄戈十八岁生日那天发生了剧变。一群散发九幽魔气的魔物突袭清风村，远比以往的敌人更加强大。巡狩小队节节败退，村庄陷入火海。玄戈在保护村民时被逼至村外的九霄神龛，一座供奉三神器的古老遗迹。神龛中央，一枚散发微光的"轮回镜"碎片悬浮在祭台上，散发出纯净的力量。\n　　危急时刻，一名自称七罪使徒的神秘来客出现，称轮回镜碎片是魔主复兴的关键。玄戈奋力抵抗，却被神秘来客的魔气重创。生死之际，他无意中触碰了镜片，一道耀眼的星光自他体内爆发，化作绯色光晕涤荡四方。魔物被净化，神秘来客仓皇逃离，而玄戈的身体发生了变化：他的瞳孔闪烁着星芒，眉间浮现出星辰印记。\n　　就在此时，白璃感应到了星光的召唤，不顾结界的危险赶到清风村。赤鸢也在北境战场上看到了这道星光，毅然放弃阵地，率领残部向南进发。三人在九霄神龛前相遇，命运的齿轮开始转动。\n　　云游赶到神龛，震惊地低语："破军星转世……你们是预言中的三位天命之人。"他告诉三人，玄戈体内的力量源于破军星的血脉，白璃掌控着天地灵韵，赤鸢拥有不屈的战魂，而轮回镜的觉醒意味着"封天计划"的序幕已经拉开。\n　　"封天计划，乃是上古时期三界先贤留下的破局之策。"云游严肃地说道，"天界自诩为三界主宰，设下层层枷锁束缚众生，让三界生灵永世为奴。而封天，就是要彻底封印天界的权柄，斩断他们操控三界的丝线，让天不再高高在上，让三界生灵重获自由。一旦成功破除这万古死局，三界生灵将迎来真正的解放，建立属于自己的盛世王朝。"\n　　"七罪使徒的袭击只是开始，他们的目标是收集三神器——"轮回镜""陨星双刃""焚天剑"，表面上是要唤醒魔主罗睺，实际上是天界的阴谋，想要借魔主之手清洗三界，重新建立更严密的统治。"\n　　白璃握紧双拳地说道："我族守护千年的宿命，终要在我们手中完成。封天计划，势在必行！"\n　　赤鸢拔出战刀："人族的尊严不容践踏，我们要用自己的力量破除这万古死局，成就无上霸业！"\n　　玄戈看着手中的星纹残铁，感受着体内澎湃的力量："那就让我们一起，执行这场前所未有的封天计划，成就真正的霸业！"\n　　三位天命之人的相遇，标志着封天计划的正式启动。他们将面对的不仅是魔主罗睺的威胁，更是天界千年来精心布置的死局。这是一场关乎三界命运的终极对决，也是三界生灵争取自由和尊严的破局之战。封天破局，成就霸业——封天霸业，由此开启！',
	},

	['UITowerBattlePrepare'] = {
		[1] = '',
		[2] = '出战卡组',
		[3] = '清空',
		[4] = '雇佣兵',
		[5] = '技能',
		[6] = '无',
		[7] = '信息',
		[8] = '可激活',
		[9] = '已获得熊猫',
		[10] = '未获得熊猫',
		[11] = '已获得技能',
		[12] = '未获得技能',
		[13] = '已满级',
		[14] = '获取激活',
		[15] = '普通(%d/%d)',
		[16] = '完美(%d/%d)',
		[17] = '史诗(%d/%d)',
		[18] = '传说(%d/%d)',
		[19] = '超凡(%d/%d)',
		[20] = '按住熊猫不动，拖动到下方开始布阵',
	},

	['UIFate'] = {
		[1] = '一键拾取',
		[2] = '自动探测',
		[3] = '返回',
		[4] = '免费',
		[5] = '声望不足，请获取声望！',
		[6] = '获得{0}并遇到{1}',
		[7] = '获得{0}',
		[8] = '临时背包已满',
		[9] = "停止探测",
		[10] =
		'突变广告等级：<color=#66FF99>%s级</color>\n增加自动次数：<color=#66FF99>%s次</color>\n突变消耗减少：<color=#FFA2FF>%s</color>\n下级需看广告：<color=#FFA2FF>%s/%s次</color>',
		[11] = '后看广告，免费召唤！',
		[12] = '自动探测次数不足！',
		[13] = '无可拾取的菌落',
		[14] = '等级     <color=#FFA500>%s</color>',
		[15] = '经验     <color=#00FF00>%s/%s</color>',
		[16] = '自动 <color=%s>+%s</color>次'
	},

	['UITowerBattleChooseDeck'] = {
		[1] = '返回',
		[2] = '布阵',
		[3] = '挑战',
		[4] = '开始匹配',
		[5] = '无',
		[6] = '看广告',
		[7] = '当前卡组没有熊猫可用于本关战斗，请前往重新布阵',
		[8] = '确定',
		[9] = '取消',
		[10] = '当前卡组部分熊猫不能用于本关战斗，是否前往重新布阵？',
		[11] = '布阵',
		[12] = '继续挑战',
		[13] = '需要魂力：<color=#%s>%s/%s</color>',
		[14] = '剩余魂力不足\n<color=#30ef30>灵晶购买</color>、<color=#30ef30>看广告</color>、<color=#30ef30>挑战模式</color>可获得魂力值',
		[15] = '前往购买',
		[16] = '购买%s魂力',
		[17] = '购买',
		[18] = '<color=#FFA500>本模式不可选择出战熊猫！</color>',
		[19] = '剩余次数:',
		[20] = '(%d/%d)',
		[21] = '匹配',
		[22] = '灵晶不足！',
		[23] =
		'玩法规则：\n1. 战斗分为<color=#30ef30>多个回合</color>。\n2. <color=#30ef30>每回合</color>之间有<color=#30ef30>一定时间暂停</color>，可进行<color=#30ef30>布阵</color>操作。\n3. <color=#30ef30>每只熊猫</color>只能<color=#30ef30>出战一次</color>。\n4. 战斗初始，<color=#30ef30>双方</color>均有<color=#30ef30>3颗红心</color>。\n5. <color=#30ef30>每回合</color>熊猫全部<color=#30ef30>阵亡</color>的一方，<color=#30ef30>损失1颗心</color>。\n6. <color=#30ef30>任意一方损失3颗心</color>，则<color=#30ef30>失败</color>，战斗结束！\n\n<color=#30ef30>奖励</color>：大量仙币、熊猫碎片、突破药水',
	},


	['UIFunctionOpen'] = {
		[1] = '新功能开启！',
		[2] = '点击任意处继续！',
	},

	['UICardShow'] = {
		[1] = '<color=white>确定</color>',
		[2] = '清除广告！',
	},

	['UICardTips'] = {
		[1] = '生命：<color=#00ff00>%s</color>%s',
		[2] = '攻击：<color=#00ff00>%s</color>%s',
		[3] = '防御：<color=#00ff00>%s</color>%s',
		[4] = '不会攻击',
		[5] = '<color=#00ff00>受击只掉1血</color>',
		[6] = '攻击速度：%s',
		[7] = '伤害次数：%s',
		[8] = '召唤冷却：<color=#00ff00>%s秒</color>',
		[9] = '召唤消耗：<color=#00ff00>%s</color>',
		[10] = '无',
		[11] = '<color=#00ff00>%s秒/次</color>',
		[12] = '<color=#00ff00>%s次</color>',
		[13] = '剧情模式：%s获得',
		[14] = '<color=#00ff00>第%d章第%d关</color>',
		[15] = '激活',
		[16] = '拥有升级丹：<color=#%s>%s/%s</color>',
		[17] = '激活成功',
		[18] = '升级',
		[19] = '升级成功',
		[20] = '%s级',
		[21] = '<color=#ef3030>升级属性在挑战模式中无效！</color>',
		[22] = '看视频升一级',
		[23] = '拥有碎片：<color=#%s>%s/%s</color>',
		[24] = '%ds后可观看',
		[25] = '操作冷却中',
	},

	['TowerBattleManager'] = {
		[1] = '通关剧情模式%s%s开放',
		[2] = '魂力不足！',
	},

	['UIFirstRecharge'] = {
		[1] = '超级超值礼包',
		[2] = '传说豪礼',
		[3] = '超值礼包%s送',
		[4] = '特价',
		[5] = '10倍\n返利',
		[6] = '仅在本界面充值有效',
		[7] = '第%d天',
		[8] = '购买新手宝箱',
		[9] = '剩余次数(%d/%d)',
		[10] = '充值直销狂欢礼包，获取充值卡ID失败',
		[11] = '领取',
		[12] = '已领取',
		[13] = '暴击率+30%\n无敌状态\n攻击一列',
		[14] = '总价<color=#ffff00>300元</color>只需<color=#ffff00>6元</color>即可连领三天',
		[15] =
		'只需<color=#ffff00>12元</color>立即获得价值<color=#ffff00>320元</color>的\n<color=#ffff00>传说</color>熊猫<color=#ffff00>升级</color>大礼',
		[16] = '已获得此奖励',
		[17] = '前往充值',
		[18] = '立即领取全套<color=#FF00FF>[杰出]</color>装备，再返10倍灵晶\n还有割草神器<color=#FF00FF>[光子炮]</color>',
		[19] = '看<color=%s>%s/%s</color>次广告免费激活'
	},

	['UIChaoJiChongZhi'] = {
		[1] = '超级超值礼包',
		[2] = '传说豪礼',
		[3] = '超值礼包%s送',
		[4] = '特价',
		[5] = '10倍\n返利',
		[6] = '仅在本界面充值有效',
		[7] = '第%d天',
		[8] = '购买新手宝箱',
		[9] = '剩余次数(%d/%d)',
		[10] = '充值直销狂欢礼包，获取充值卡ID失败',
		[11] = '领取',
		[12] = '已领取',
		[13] = '暴击率+30%\n无敌状态\n攻击一列',
		[14] = '总价<color=#ffff00>300元</color>只需<color=#ffff00>6元</color>即可连领三天',
		[15] ='只需<color=#ffff00>12元</color>立即获得价值<color=#ffff00>320元</color>的\n<color=#ffff00>传说</color>熊猫<color=#ffff00>升级</color>大礼',
		[16] = '已获得此奖励',
		[17] = '前往充值',
		[18] = '立即领取全套<color=#FF00FF>[杰出]</color>装备，再返10倍灵晶\n还有割草神器<color=#FF00FF>[光子炮]</color>',
		[19] = '看<color=%s>%s/%s</color>次广告免费激活',
		[20] = '霸天礼包',
		[21] = '惊天大礼',
		[22] = '特惠礼包',
		[23] = '天道福利',
		[24] = '每日签到'
		
	},

	['UIRecharge'] = {
		[1] = '<color=#15405e>充值</color>',
		[2] = '%d元',
		[3] = '%d灵晶',
		[4] = '再送%d',
		[5] = '双倍',
		[6] = '投资',
		[7] = '投资<color=#fffc00>%d钻</color>',
		[8] = '7天领<color=#fffc00>%d钻</color>',
		[9] = '取消',
		[10] = '确定',
		[11] = '领取',
		[12] = '奖励预览',
		[13] = '立即投资',
		[14] = '已投资',
	},

	['UIIndulgeTip'] = {
		[1] =
		'1）本游戏是一款休闲类游戏，适用于年满12周岁及以上的用户，建议未成年人在家长监护下使用游戏产品，并鼓励家长根据未成年人的实际情况管理其游戏行为。 2）本游戏不会与现实生活相混淆。游戏玩法基于较为简单的钓鱼玩法，鼓励玩家体验悠闲舒适的钓鱼生活 。 ',
	},

	['UIShowGoodInfo'] = {
		[1] = '立即领取',
		[2] = '%ds后可观看',
	},

	['UIMatchingTip'] = {
		[1] = '匹配中...',
		[2] = '取消',
		[3] = '看广告可<color=#30ef30>免费升级熊猫</color>',
		[4] = '熊猫<color=#30ef30>突破</color>后可增加<color=#30ef30>大量属性</color>',
		[5] = '熊猫<color=#30ef30>升级</color>可获得<color=#30ef30>绝版稀有属性</color>',
		[6] = '<color=#30ef30>公平竞技</color>玩法熊猫只有<color=#30ef30>基础属性</color>,<color=#30ef30>升级、强化等无效</color>！',
	},

	['UIRaceView'] = {
		[1] = '尚未解锁',
		[2] = '<size=20><color=#00ff60>点击解锁！</color></size>',
		[3] = '<size=20><color=#00ff60>点击打开！</color></size>',
		[4] = '同时只能解锁一个宝箱',
		[5] = '取消',
		[6] = '<color=#efef30>立即打开！</color>',
		[7] = '灵晶解锁',
		[8] = '免费解锁',
		[9] = '<color=#efef30>你可以立即解锁</color>',
		[10] = '<color=#efef30>免费立即解锁<color=#00ff00>(%d/%d)</color></color>',
		[11] =
		'宝箱说明：\n1.闯关赛、1v1对战和段位赛可获得<color=#00ff00>1-4阶仙币宝箱</color>。\n2.<color=#00ff00>同一时间</color>只能解锁<color=#00ff00>一个宝箱</color>。\n3.宝箱解锁时间结束后，需要<color=#00ff00>自行开启已解锁的宝箱</color>，并且<color=#00ff00>自行点击下一个未解锁</color>的宝箱才会开始解锁！\n \n奖励：\n 1.大量的<color=#00ff00>仙币</color>\n 2.大量的<color=#00ff00>普通·熊猫碎片</color>、大量的<color=#00BFFF>完美·熊猫碎片</color>\n 3.有几率获得<color=#FF00FF>史诗·熊猫碎片</color>和<color=#FFD700>传说·熊猫碎片</color>\n\n小贴士：可以使用<color=#00ff00>灵晶立即解锁</color>宝箱或者<color=#00ff00>观看广告免费</color>立即解锁宝箱！',
		[12] = '通关%s开放',
		[13] = '去广告',
		[14] = '测试',
		[15] = '超值礼包',
		[16] = '月卡',
		[17] = '传说礼包',
		[18] = '立即解锁',
		[19] = '充值',
		[20] = '签到',
		[21] = '投资',
		[22] = '商店',
		[23] = '变强礼包',
		[24] = '召唤',
	},

	['UICardOperation'] = {
		[1] = '升级',
		[2] = '突破',
		[3] = '升级',
		[4] = '普通',
		[5] = '完美',
		[6] = '史诗',
		[7] = '传说',
		[8] = '超凡',
		[9] = '未激活',
		[10] = '激活',
		[11] = '升级',
		[12] = '<color=#%s>%s(%s/%s)</color>',
		[13] = '<color=#%s>%d</color>',
		[14] = '突破',
		[15] = '类型',
		[16] = '物理',
		[17] = '法术',
		[18] = '普通',
		[19] = '攻击速度：',
		[20] = '伤害次数：',
		[21] = '<color=#00ff00>%s秒/次</color>',
		[22] = '生命',
		[23] = '攻击',
		[24] = '物理防御',
		[25] = '法术防御',
		[26] = '不会攻击',
		[27] = '命中',
		[28] = '闪避',
		[29] = '普通防御',
		[30] = '<color=#00ff00>受击只掉1血</color>',
		[31] = '已满级',
		[32] = '%d<color=#00ff00>(+%s)</color>',
		[33] = '<color=#00ff00>%s +%d</color>',
		[34] = '突破成功',
		[35] = '升级成功',
		[36] = '当前等级：强化+%d',
		[37] = '下一等级：强化+%d',
		[38] = '强化+%d',
		[39] = '激活成功',
		[40] = '已满级',
		[41] = '熊猫未激活',
		[42] = '熊猫说明',
		[43] = '免费无消耗！',
		[44] = '升%d级<color=#30ef30>(%d/%d)</color>',
		[45] = '%ds后可观看！',
		[46] = '点击这里关闭！',
		[47] = '请先突破再继续升级！',
		[48] = '请先升级再突破！',
		[49] = '冷却时间未结束！',
		[50] = '今日次数已用完！',
	},

	['UIRankingList'] = {
		[1] = '战力榜',
		[2] = '金钱榜',
		[3] = '生命榜',
		[4] = '物理榜',
		[5] = '法术榜',
		[6] = '物防榜',
		[7] = '法御榜',
		[8] = '等级榜',
		[9] = '魅力榜',
		[10] = '榜',
		[11] = '榜',
		[12] = '榜',
		[13] = '榜',
		[14] = '榜',
		[15] = '榜',
		[16] = '榜',
		[17] = '榜',
		[18] = '榜',
		[19] = '榜',
		[20] = '榜',
		[21] = '榜',
		[22] = '榜',
		[23] = '榜',
		[24] = '榜',
		[25] = '榜',
		[26] = '榜',
		[27] = '榜',
		[28] = '榜',
		[29] = '榜',
		[30] = '生存榜',
		[31] = '等级',
		[32] = '杀怪数',
		[34] = '(我的排行：%s)',
		[35] = '(未上榜)',
		[36] = '我的%s：%s    ',
	},

	['UIEctypeView'] = {
		[1] = '点击挑战%s',
		[2] =
		'挑战副本说明\n1.战队初始，<color=#00ff00>双方</color>均有<color=#00ff00>2颗红心</color>。\n2.<color=#00ff00>每回合</color>熊猫<color=#00ff00>全部阵亡</color>的一方，<color=#00ff00>损失</color>1颗红心。\n3.<color=#00ff00>任意一方损失2颗红心</color>，则<color=#00ff00>失败</color>，战斗结束！\n4.<color=#00ff00>布阵诀窍</color>：\n1）<color=#00ff00>保护</color>好自己的<color=#00ff00>攻击熊猫</color>，让其存活时间越久，胜率越大！\n2）<color=#00ff00>优先击败</color>对方的<color=#00ff00>攻击熊猫</color>，越快消灭对方的攻击熊猫，胜率越大！\n\n<color=#00ff00>奖励：</color>大量魂力、灵晶、突破药水',
		[3] =
		'烧脑模式说明\n1.本次战斗可用的熊猫由<color=#00ff00>系统选定</color>，<color=#00ff00>不能自由选择出战熊猫</color>！\n2.熊猫<color=#00ff00>升级</color>、<color=#00ff00>突破</color>、<color=#00ff00>强化</color>等<color=#00ff00>属性</color>，在此战斗中<color=#00ff00>无效</color>。每只熊猫的<color=#00ff00>属性</color>都是<color=#00ff00>初始数值</color>！\n3.通过<color=#00ff00>合理布阵</color>，<color=#00ff00>击败</color>对方所有熊猫，即可<color=#00ff00>获胜</color>！\n4.<color=#00ff00>布阵诀窍</color>：\n1）<color=#00ff00>保护</color>好自己的<color=#00ff00>攻击熊猫</color>，让其存活时间越久，胜率越大！\n2）<color=#00ff00>优先击败</color>对方的<color=#00ff00>攻击熊猫</color>，越快消灭对方的攻击熊猫，胜率越大！\n\n<color=#00ff00>奖励：</color>大量仙币、魂力',
		[4] = '正在准备中...',
		[5] = '未开启',
		[6] = '没有可以攻打的关卡！',
		[7] = '需通关%s才能挑战！',
		[8] = '通关%s开放！',
		[9] = '今日挑战次数已用完！',
		[10] = '取消',
		[11] = '挑战',
		[12] = '今日免费次数已用完，继续挑战需%d！',
		[13] = '灵晶不足！',
		[14] =
		'公平竞技玩法规则：\n1.战斗分为<color=#30ef30>多个回合</color>。\n2.<color=#30ef30>每回合</color>之间有<color=#30ef30>一定时间暂停</color>，可进行<color=#30ef30>布阵</color>操作。\n3.<color=#30ef30>每只熊猫</color>只能<color=#30ef30>出战一次</color>。\n4.战斗初始，<color=#30ef30>双方</color>均有<color=#30ef30>3颗红心</color>。\n5.<color=#30ef30>每回合</color>熊猫全部<color=#30ef30>阵亡</color>的一方，<color=#30ef30>损失1颗心</color>。\n6.<color=#30ef30>任意一方损失3颗心</color>，则<color=#30ef30>失败</color>，战斗结束！\n7.熊猫<color=#00ff00>升级</color>、<color=#00ff00>突破</color>、<color=#00ff00>强化</color>等<color=#00ff00>属性</color>，在此战斗中<color=#00ff00>无效</color>。每只熊猫的<color=#00ff00>属性</color>都是<color=#00ff00>初始数值</color>！\n\n<color=#30ef30>奖励</color>：大量仙币、熊猫碎片、升级材料',
		[15] = '点击挑战公平竞技',
	},

	['UIFormationGuide'] = {
		[1] = '点击这里关闭',
		[2] = '敌方',
		[3] = '我方',
	},

	['UICardGuide'] = {
		[1] = '熊猫介绍',
		[2] = '战斗说明',
		[3] = '站位推荐',
		[4] = '点击这里关闭',
	},

	['BuffLC'] = {
		[1] = '生命',
		[2] = '攻击',
		[3] = '防御',
		[4] = '攻击',
		[5] = '物防',
		[6] = '法攻',
		[7] = '法防',
		[8] = '暴击',
		[9] = '防暴',
		[10] = '穿透',
		[11] = '格挡',
		[12] = '命中',
		[13] = '闪避',
		[14] = '护甲',
		[15] = '能量',
		[16] = '伤害加深',
		[17] = '减免',
		[18] = '移动速度',
		[19] = '吸血',
		[20] = '伤害反弹',
	},

	['UIWelfare'] = {
		[1] = '变强礼包',
		[2] = '传说礼包',
		[3] = '购买成功！',
		[4] = '充值',
		[5] = '礼包',
		[6] = '看广告',
		[7] = '看<color=%s>%s/%s次</color>广告免费领取',
	},

	['UILimitedPurchase'] = {
		[1] = '限时特购',
		[2] = '原价%d  现价%d',
		[3] = '<color=#00ff00>%d:%d:%d</color>',
		[4] = '已购买',
		[5] = '购买',
		[6] = '活动尚未开启',
		[7] = '仅此\n一次',
		[8] =
		'限时特购说明：\n 1.购买后立刻获得<color=#FF00FF>史诗·熊猫*15</color>\n 2.<color=#30ef30>超值</color>折扣活动，<color=#30ef30>限时</color>购买，<color=#30ef30>仅此一次</color>！\n\n小贴士：一个<color=#FF00FF>史诗·熊猫</color>能打三个<color=#30ef30>普通·熊猫</color>,能提高\n副本获胜率！',
		[9] =
		'限时特购说明：\n 1.购买后立刻获得<color=#FFD700>传说·熊猫*20</color>\n 2.<color=#30ef30>超值</color>折扣活动，<color=#30ef30>限时</color>购买，<color=#30ef30>仅此一次</color>！\n\n小贴士：一个<color=#FFD700>传说·熊猫</color>能打五个<color=#30ef30>普通·熊猫</color>,能大大\n提高副本获胜率！',
		[10] =
		'限时特购说明：\n 1.购买后立刻获得<color=#FFA2FF>超凡·熊猫*40</color>\n 2.<color=#30ef30>超值</color>折扣活动，<color=#30ef30>限时</color>购买，<color=#30ef30>仅此一次</color>！\n\n小贴士：一个<color=#FFA2FF>传说·熊猫</color>能打十个<color=#30ef30>普通·熊猫</color>,能极大\n提高副本获胜率！',
		[11] = '确定',
		[12] = '温馨提示',
		[13] = '今日不能再购买！',
		[14] = '操作过于频繁，%d秒后再操作',
	},

	['UIAdvertiseMentGift'] = {
		[1] = '超级宝箱',
		[2] = '免费<color=#d8ff00>领取超级宝箱</color>',
		[3] = '<color=#ffec00>免费领取</color>',
		[4] = '奖励预览',
		[5] = '<color=#ffec00>已领取</color>',
		[6] = '领取',
		[7] = '立即领取',
		[8] = '%ds后可观看',
		[9] = '操作冷却中',
		[10] = '免费礼包',
		[11] = '免费次数%d/%d',
		[12] = '福利宝箱',
		[13] = '黄金宝箱',
		[14] = '超级宝箱',
		[15] = '最后1次必得<color=#FFFF37>史诗熊猫*10</color>',
		[16] = '已完成',
	},

	['UIGoldStore'] = {
		[1] = '仙币商城',
		[2] = '每日购买:%s/%s次',
		[3] = '每日购买:%s',
		[4] = '已售罄',
		[5] = '立即刷新(%d/%d)',
		[6] = '免费刷新',
		[7] = '%02d:%02d:%02d后免费',
		[8] = '冷却%d秒',
		[9] = '免费',
		[10] = '购买提示',
		[11] = '原价：%d',
		[12] = '刷新',
		[13] = '刷新',
	},

	['UICarnivalRechargeBuy'] = {
		[1] = '%d元',
		[2] = '确定',
		[3] = '已售罄，请明天再买！',
		[4] = '操作过于频繁，%d秒后再操作',
		[5] = '已售罄',
	},

	['UIPrivilegeRecharge'] = {
		[1] = '%d元',
		[2] = '月卡',
		[3] = '已购买',
		[4] = '战斗获得仙币 <color=#00ff00>+%s</color>\n今日可领取<color=#00ff00>%d</color>',
		[5] = '领取',
		[6] = '已领取',
	},

	['UIDiamondsRecharge'] = {
		[1] = '%d元',
		[2] = '灵晶',
		[3] = '首冲双倍',
		[4] = '%d灵晶',
		[5] = '超值礼包送双倍',
		[6] = '宝石商店',
	},

	['UIShop'] = {
		[1] = '仙币商城',
		[2] = '随机商城',
		[3] = '隐藏商店',
		[4] = '原价：%d',
		[5] = '每日观看:%s/%s次',
		[6] = '每日观看:%s',
		[7] = '免费刷新(%d)',
		[8] = '%d 刷新',
		[9] = '广告冷却中！',
		[10] = '免广告领取！',
		[11] = '看广告领取！',
		[12] = '今日刷新次数已用完！',
		[13] = '售罄',
		[19] = '看<color=%s>%s/%s</color>次广告免费激活！'
	},

	['UISevenDayInvestment'] = {
		[1] = '第%d天',
		[2] = '七日投资',
		[3] = '投资后，连领七天，累计返还',
		[4] = '点击查看更多...',
		[5] = '<color=#00FF00>领取</color>',
		[6] = '<color=#d8d8d8>已领取</color>',
		[7] = '%d元',
		[8] = '免费领取',
		[9] = '和价值',
		[10] = '元的材料',
		[11] = '点击这里关闭',
		[12] = '立即投资',
		[13] = '可领取',
		[14] = '购买后可在连续7天内，每天领取丰厚奖励！',
		[15] = '%d元',
		[16] = '领取',
		[17] = '看<color=%s>%s/%s</color>次广告免费激活'
	},

	['UIWelfareCard'] = {
		[1] = '%d元',
	},


	['UIShowBoxInfo'] = {
		[1] = '%d元',
		[2] = '至少包含',
	},

	['UIRuleTip'] = {
		[1] = '温馨提示',
	},

	['AdvertisementManager'] = {
		[1] = '看广告<color=#30ef30>三倍奖励</color>已发放！',
		[2] = '确定',
		[3] = '清除广告',
		[4] = '<color=#30ef30></color>！',
		[5] = '<color=#30ef30>三倍奖励</color>已发放！',
		[6] = '广告奖励任务已完成',
		[7] = '今日观看广告次数已用完！',
		[8] = '广告冷却中%s秒后重试',
		[9] = '请求广告失败',
		[10] = '广告正在准备中',
	},

	['UIClearAD'] = {
		[1] =
		'看广告<color=#ffff00>双倍</color>奖励\n看广告<color=#ffff00>升级</color>熊猫\n看广告<color=#ffff00>刷新</color>商店\n……\n所有此类功能都<color=#ffff00>不需要再看广告</color>，直接操作！',
		[2] = '68',
	},

	['UIDirectBuyGoods'] = {
		[1] = '超值大礼',
		[2] = '黄金礼包',
		[3] = '最后一次必得<color=#FFD700>史诗熊猫*10</color>',
		[4] = '<color=#00FF00>免费领取(%s/%s)</color>',
		[5] = '%s <color=#%s>%s*%s</color>',
		[6] = '<color=#FFD700>仅此\n  一次</color>',
		[7] = '<color=#%s>￥%s</color>',
		[8] = '点击这里关闭',
	},
	['UISignIn'] = {
		[1] = '第%s天',
		[2] = '已签到',
		[3] = '签到',
		[4] = '累计\n%s天',
		[5] = '签到奖励:',
		[6] = '',
		[7] = '',
		[8] = '',
		[9] = '',
		[10] = '',
	},

	['UIShowBuyTip'] = {
		[1] = '购买提示',
	},

	['UIShowUnlockBox'] = {
		[1] = '%d秒可观看',
	},

	['UILuckDraw'] = {
		[1] = '召唤一次',
		[2] = '召唤十次',
		[3] = '首次召唤十次必得',
		[4] = '%s后免费一次',
		[5] = '恭喜获得',
		[6] = '<color=#00ff00>%02d:%02d:%02d</color> <color=#E9E6DB>后免费</color>',
		[7] = '免费召唤',
		[8] = '灵晶不足，是否前往充值？',
		[9] = '及',
		[10] = '卡牌 X%d',
		[11] = '确定',
		[12] = '取消',
		[13] = '购买%s魂力',
		[14] = '购买',
		[15] = '奖励预览',
		[16] = '抽奖有机会获得以下奖励',
		[17] = '前往获取',
		[18] = '召唤十次必得',
		[19] = '史诗装备',
		--[20] = '杰出1;杰出2;杰出3;杰出4;杰出5;杰出6',
		[20] = '免费领取倒计时：',
		[21] = '剩余免费次数：',
		[22] = '%02d:%02d:%02d',
		[23] = '免费',
		[24] = ': 伙伴机械猫',
		[25] = '十连抽必得伙伴卡',
	},

	['UIBattleGuide'] = {
		[1] = '规则说明',
		[2] = '攻击顺序',
		[3] = '胜负规则',
		[4] = '其他说明',
		[5] =
		'1. 优先攻击<color=#00ff00>同一列</color>上的敌人\n1）如右图，熊猫优先攻击<color=#00ff00>②号</color>位置的敌人。\n2. 同列没敌人则攻击<color=#00ff00>邻近一列</color>的敌人。',
		[6] = '在规定时间内，击败所有敌人可获胜！',
		[7] =
		'1. 按住熊猫不放滑动，可移动熊猫位置。\n2. 布阵非常重要，正确布阵可极大提升胜率：\n1）<color=#00ff00>防御熊猫</color>放前面，保护自己的<color=#00ff00>攻击熊猫</color>\n2）<color=#00ff00>集火</color>优先攻击敌方的<color=#00ff00>攻击熊猫</color>，让对方攻击能力快速降低。',
		[8] =
		'1. 战斗开始，双方均有<color=#00ff00>%d颗红心</color>。\n2. 失败一回合损失<color=#00ff00>1颗红心</color>。\n3. 损失<color=#00ff00>%d颗红心</color>则失败结束！',
		[9] = '1. 暂停期间可操作熊猫布阵。\n2. 每只熊猫每场战斗只能上场出战一次。\n3. 参与过任意一回合战斗的熊猫，本次战斗\n不可再替换下场。',
		[10] =
		'1. 暂停期间可操作熊猫布阵。\n2. 每只熊猫每场战斗只能上场出战一次。\n3. 参与过任意一回合战斗的熊猫，本次战斗\n不可再替换下场。\n4. <color=#00ff00>公平竞技</color>模式中，熊猫的<color=#00ff00>升级、升级</color>属性\n无效，双方熊猫都是<color=#00ff00>初始属性。</color>',
		[11] =
		'1. <color=#00ff00>烧脑模式</color>中，出战熊猫由<color=#00ff00>系统指定</color>。\n2. 双方相同熊猫的属性、技能都完全相同。\n3. 此模式下<color=#00ff00>布阵</color>是决定胜负的<color=#00ff00>最关键因素！</color>',
		[12] = '击败对方所有熊猫即可获胜！',
		[13] = '点击这里关闭',
	},

	['UIEmail'] = {
		[1] = '系统',
		[2] = '领取附件',
		[3] = '<color=#00ff00>(已读)</color>',
		[4] = '<color=#00ff00>(已领取)</color>',
		[5] = '<color=#FFA500>(未领取)</color>',
		[6] = '背包格子已满，无法提取附件',
		[7] = '器魂仓库已满，无法提取附件',
		[8] = '已领取',
		[9] = '请先领取附件',
		[10] = '未选择邮件',
		[11] = '删除',
		[12] = '一键领取',
		[13] = '一键删除',
		[14] = '领取附件',
		[15] = '刷新',
		[16] = '<color=#00ff00>当前没有邮件！</color>',
		[17] = '提示:邮件有效期为7天过期后系统将自动删除，请及时领取！',
		[18] = '主     题:',
		[19] = '发件人: 系统\n该邮件将于%s过期',
		[20] = '邮件',
		[21] = '内容:',
		[22] = '您的背包空间不足，无法领取物品，请清理背包！',
		[23] = '发件人:',
		[24] = '客官，暂时没有收到邮件哦！',
		[25] = '<color=#FFA500>(未读)</color>',
		[26] = '<color=#00FF00>(已读)</color>',
		[27] = '邮件详情',
		[28] = '发件人: 系统',
		[29] = '阅读邮件',
		[30] = '已读邮件',
	},

	['UIEveryDayDo'] = {
		[1] = '日常;周常;七日登录;七日目标;成就',
		[2] = '前往',
		[3] = '领取',
		[4] = '已领取',
		[5] = '未达成',
		[6] = '成就未达成',
		[7] = 'richang;zhouchang;qiridenglu;qirimubiao;chengjiujiangli',
		[8] = 'diyitian;diertian;disantian;disitian;diwutian;diliutian;diqitian',
		[9] = 'richang (3);zhouchang (3);qirimubiao (2);qirijiangli;changjiujiangli',
		[10] = '第一天;第二天;第三天;第四天;第五天;第六天;第七天',
	},

	-- 主线关卡界面
	['UIBrustCatChallenge'] = {
		[1] = '试炼',
		[2] = '开始挑战',
		[3] = '<color=#535353>未开启</color>',
		[4] = '<color=#727171>已通过！</color>',
		[5] = '<color=#FFFFFF>当前关卡</color>',
		[6] = '通过<color=#F5D73D>%s</color>解锁',
	},

	-- 商店充值界面
	['UIStoreAll'] = {
		[1] = '月卡1',
		[2] = '月卡2',
		[3] = '月卡3',
		[4] = '购买%s元',
		[5] = '已购买',
		[6] = '还未解锁第%s关'
	},
	-- 主线奖励
	['UIStageReward'] = {
		[1] = '第%s章',
		[2] = '￥%s领取',
		[3] = '再看<color=%s>%s次</color>\n免费领取',
		[4] = '未通关',
		[5] = '',
		[6] = '',
	},

	-- 月卡分页界面
	['UIMonthCard'] = {
		[1] = '购买%d元',
		[2] = '月卡',
		[3] = '已购买',
		[4] = '战斗获得仙币 <color=#00ff00>+%s</color>\n今日可领取<color=#00ff00>%d</color>',
		[5] = '领取',
		[6] = '已领取',
		[7] = '购买奖励',
		[8] = '每日可领取',
		[9] = 'X%d',
		[10] = '激活',
		[11] = '免看广告得奖励',
		[12] = '可与其他月卡效果叠加',
		[13] = '未生效',
		[14] = '章节福利：通关第三章可激活，每日免费领取大量灵晶',
		[15] = '<color=#00ff00>已生效</color>',
		[16] = '看<color=%s>%s/%s</color>次广告后激活',
		[17] = '10倍福利',
		[18] = '剩余%d天',
	},

	-- 角色界面
	['UIAirplane'] = {
		[1] = '角色',
		[2] = '进化',
		[3] = '出战',
		[4] = '已出战',
		[5] = '出战机器',
		[6] = '%s +%s',
		[7] = '缺失',
		[8] = '<color=#%s>%s</color>',
		[9] = '生命',
		[10] = '攻击',
		[11] = '已满级',
		[12] = '+%s',
		[13] = '角色部件',
		[14] = '%s:<color=yellow>%s/%s</color>',
		[15] = '减伤',
		[16] = '升级',
		[17] = '激活',
		[18] = '%s%s/%s',
		[19] = '速度',
		[20] = '角色进化中……',
		[21] = '进化消耗:',
		[22] = '突破',
		[23] = '仙币不足！',
		[24] = '材料不足！',
		[25] = '角色突破中……',
		[26] = '突破概率：%d%%',
		[27] = '%s/%s',
		[28] = '获取途径',
		[29] = '激活后的英雄，战斗中均有几率随机到！',
	},

	['UIUpgradeSkillView'] = {
		[1] = '技能升级',
		[2] = '技能选择',
		[3] = '点击下方选择技能',
		[4] = '学习',
		[5] = '升级',
		[6] = '返回',
		[7] = '拥有技能:',
		[8] = '突破需要:',
		[9] = '每1关挑战中，只有1次选择满星技能的机会',
	},

	-- 角色背包
	['UIRole'] = {
		[1] = '角色',
		[2] = '强化',
		[3] = '未出战',
		[4] = '材料',
		[5] = '整理',
		[6] = '全部',
		[7] = '合成',
		[8] = '角色仓库',
		[9] = '合成数量：%s',
		[10] = '消耗仙币总量：%s',
		[11] = '仙币总量不足！',
		[12] = '合成%d次',
		[13] = '打开',
		[14] = '全部打开',
		[15] = '详情',
		[16] = '穿戴',
		[17] = '品质排序',
		[18] = '等级排序',
		[19] = '<size=36>%s</size>\n<size=24>id:%s  Lv:%d</size>',
		[20] = '宝箱',
		[21] = '拥有数量：%d',
		[22] = '只显示品质：',
		[23] = '批量合成',
		[24] = '批量分解',
		[25] = '开始合成',
		[26] = '开始分解',
		[27] = '获得仙币总量：%s',
		[28] = '2个相同品质的物品\n可以合成1个更高品质的物品',
		[29] = '多余的装备\n可以分解成装备图纸和仙币',
	},

	-- 界面槽
	['SlotItem'] = {
		[1] = '界面槽',
	},

	-- 翻页按钮
	['PageBtn'] = {
		[1] = '翻页',
	},

	-- 物品装备信息界面
	['UIGoodTips'] = {
		[1] = '确定',
		[2] = '等级：',
		[3] = '%s/%s',
		[4] = '%s +%s',
		[5] = '品质描述',
		[6] = '缺失',
		[7] = '装备',
		[8] = '升级',
		[9] = '一键升级',
		[10] = '进化',
		[11] = '卸下',
		[12] = '打开',
		[13] = '分解',
		[14] = '最大强化等级+%s',
		[15] = '点击任意位置关闭',
		[16] = '成功率：%s%%(进化失败不会爆装备)',
		[17] = '正在升级中...',
		[18] = '正在进化中...',
		[19] = '仙币不足！',
		[20] = '材料不足！',
		[21] = '分解可获得：',
		[22] = '合成',
		[23] = '合成需消耗：',
		[24] = '合成10次',
		[25] = '%sx%d',
		[26] = '全部打开',
		[27] = '品质加成',
		[28] = '对比',
		[29] = '退出对比',
		[30] = '<color=white>%s</color>',
		[31] = '<color=yellow>%s</color>',
		[32] = '<color=blue>%s</color>',
		[33] = '<color=#9932CD>%s</color>',
		[34] = '<color=#CD7F32>%s</color>',
		[35] = '<color=#E47833>%s</color>',
	},

	-- 特惠礼包
	['UIDiscountGift'] = {
		[1] = '变强礼包',
		[2] = '传说礼包',
		[3] = '购买成功！',
		--福利里包含多少个模块,就生成多少个Toggle
		[4] = '体验礼包',
		[5] = '助力礼包-没用',
		[6] = '助力礼包',
		[7] = '活动剩余时间：<color=#F7FFB7>%s</color>',
		[8] = '看<color=%s>%s次</color>广告免费领取',
	},


	['UICatPatrol'] = {
		[1] = '巡逻收益',
		[2] = '章节越高，收益越大',
		[3] = '%s/时',
		[4] = '巡逻时间：%s <color=#00FF00>(最大%s小时)</color>',
		[5] = '最长巡逻%d小时',
		[6] = '快速巡逻',
		[7] = '领取',
		[8] = '免费',
		[9] = '立即获得巨量巡逻收益：',
		[10] = '可用次数：%d',
		[11] = '最少巡逻时间为%d分钟',
		[12] = '今日快速巡逻次数已用完！',
		[13] = '免费',
		[14] = '今日快速巡逻广告次数已用完！',
		[15] = '今日次数已用完！',
		[16] = '<color=%s>%s</color>立即领取！',
		[17] = '<color=#00FF00>剩余：%s</color>',
	},

	['UIChallenge'] = {
		[1] = '仙币副本',
		[2] = '每日挑战副本',
		[3] = '%d分钟',
		[4] = '通关第%d章',
		[5] = '通关主线第%d关解锁',
		[6] = '剩余挑战次数:',
	},
	['UIBattleResultView'] = {
		[1] = '重来一次',
		[2] = '下一关',
		[3] = '退出',
	},
	['UIReviveDialog'] = {

	},
	['UIGamePauseView'] = {
		[1] = '注意',
		[2] = '现在离开不会获得任何奖励，并且不会返还魂力，确定要继续吗？',
		[3] = '点击空白区域可关闭窗口',
		[4] = '关闭界面返回战斗！',
		[5] = '击杀怪物：<color=#F5FF00>%d</color>',
		[6] = '怪物自爆：<color=#F5FF00>%d</color>',
		[7] =
		'<color=#F5FF00>攻略：</color>\n1. 每场战斗初期，尽可能把<color=#F5FF00>仙币加成</color>升高！\n2. 中后期才有更多局内仙币升级攻速、攻击、生命等属性！\n3.怪物撞击角色自爆<color=#F5FF00>可造成多倍伤害</color>，且不能获得<color=#F5FF00>局内仙币</color>！',
	},
	['UISetting'] = {
		[1] = '设置',
		[2] = '声音',
		[3] = '移动缓冲',
		[4] = '音乐',
		[5] = '震动',
		[6] = '退出游戏',
		[7] = '修复异常',
		[8] = '反馈问题',
		[9] = '版本号:%s',
		[10] = '自动射击',
		[11] = '兑换码',
		[12] = '礼包码不存在！',
		[13] = '礼包码兑换成功',
		[14] = '已使用过邀请码',
		[15] = '已',
		[16] = '开',
		[17] = '关',
	},
	['UIPetShop'] = {
		[1] = '确定',
		[2] = '请先开第二位置',
		[3] = '出战',
		[4] = '灵晶不足，是否前往充值？',
		[5] = '仙币不足，是否前往购买？',
		[6] = '取消',
		[7] = '是否激活%s伙伴',
		[8] = '已出战',
		[9] = '是否卸下第%s宠物',
		[10] = '%s元开启',
		[11] = '出战位置已满，请开启第二位置，或者下阵已出战伙伴',
		[12] = '进化',
		[13] = '攻击：%s',
		[14] = '%d/%d',
		[15] = '进化概率：%d%%',
		[16] = '点击空白处退出',
		[17] = '仙币不足！',
		[18] = '消耗物品不足！',
		[19] = '去开启',
		[20] = '激活 %d/%d',
		[21] = '前往获取',
		[22] = '激活失败\n背包里没有%s伙伴卡',
		[23] = '暂未开放',
		[24] = '解锁伙伴第%d出战位置\n需要消耗道具\n%s:%d/%d',
		[25] = '解锁',
		[26] = '购买',
		[27] = '通关主线第%d关后解锁',
	},
	-- 角色背包
	['UIRoleNew'] = {
		[1] = '角色',
		[2] = '装备',
		[3] = '未出战',
		[4] = '材料',
		[5] = '整理',
		[6] = '全部',
		[7] = '合成',
		[8] = '角色仓库',
		[9] = '合成数量：%s',
		[10] = '消耗仙币总量：%s',
		[11] = '仙币总量不足！',
		[12] = '合成%d次',
		[13] = '打开',
		[14] = '全部打开',
		[15] = '详情',
		[16] = '穿戴',
		[17] = '品质排序',
		[18] = '等级排序',
		[19] = '<size=36>%s</size>\n<size=24>id:%s  Lv:%d</size>',
		[20] = '宝箱',
		[21] = '拥有数量：%d',
		[22] = '只显示品质：',
		[23] = '批量合成',
		[24] = '批量分解',
		[25] = '开始合成',
		[26] = '开始分解',
		[27] = '获得仙币总量：%s',
		[28] = '2个相同品质的物品\n可以合成1个更高品质的物品',
		[29] = '多余的装备\n可以分解成装备图纸和仙币',
		[30] = '强化',
		[31] = '%s',
		[32] = '<color=#FFA2FF>%s</color>',
		[33] = '仙币不足！',
		[34] = '属性',
		[35] = '角色',
		[36] = '生命: <color=#00FF00>%s</color>',
		[37] = '攻击: <color=#00FF00>%s</color>',
		[38] = '反伤: <color=#00FF00>%s</color>',
		[39] = '攻击速度:<color=#00FF00>%s</color>',
		[40] = '吸血: <color=#00FF00>%s</color>',
		[41] = '暴击率: <color=#00FF00>%s</color>',
		[42] = '暴击伤害: <color=#00FF00>%s</color>',
		[43] = '加伤: <color=#00FF00>%s</color>',
		[44] = '减伤: <color=#00FF00>%s</color>',
		[45] = '获得银币: <color=#00FF00>%s</color>',
		[46] = '获得经验: <color=#00FF00>%s</color>',
		[47] = '对BOSS伤害: <color=#00FF00>%s</color>',
		[48] = '%s级解锁',
		[49] = '生命%s级开放',
		[50] = '攻击%s级开放',
		[51] = '暴击%s级开放',
		[52] = '爆伤%s级开放',
		[53] = '需先提升%s等级',
		[54] = '生命',
		[55] = '攻击',
		[56] = '暴击',
		[57] = '暴击伤害',
		[58] = '攻击速度',
	},
	-- 装备突变界面
	['UIEquipForging'] = {
		[1] = '属性%s',
		[2] = '<color=%s>%s</color>',
		[3] = '请选择装备！',
		[4] = '属性不能全锁定！',
		[5] = '物品不足！',
		[6] = '仙币不足！',
		[7] = '灵晶不足！',
		[8] = '自动突变！',
		[9] = '取消',
		[10] = '突变成功！',
		[11] = '开启自动突变！',
		[12] = '关闭自动突变！',
		[13] = '收看广告！',
		[14] = '收看广告，免费召唤！',
		[15] =
		'宝箱广告等级：<color=#66FF99>%s级</color>\n增加自动次数：<color=#66FF99>%s次</color>\n消耗增加：<color=#66FF99>+%s%%</color>\n<color=#%s>%s装</color>以上掉率：<color=#66FF99>+%s%%</color>\n下级需看广告：<color=#FFA2FF>%s/%s次</color>',
		[16] = '当前可自动突变：<color=%s>%s次</color>',
		[17] = '自动突变一条 %s 极品质属性',
		[18] = '突变',
		[19] = '自动突变次数不足！',
		[20] = '',
		[21] = '升级',
		[22] = '一键升级',
		[23] = '%d/%d',
		[24] = "<color=#ef3030>资源不足！</color>",
		[25] = '召唤一次',
		[26] = '召唤十次',
		[27] = '免费领取倒计时：',
		[28] = '%02d:%02d:%02d',
		[29] = '免费召唤',
		[30] = '+%s次',
		[31] = '增加次数',
		[32] = '收看广告，自动抽奖！',
		[33] =
		'突变广告等级：<color=#66FF99>%s级</color>\n增加自动次数：<color=#66FF99>%s次</color>\n突变消耗减少：<color=#FFA2FF>%s%%</color>\n下级需看广告：<color=#FFA2FF>%s/%s次</color>',
		[34] = '自动抽<color=#ef3030> %s </color>次',
		[35] = '自动抽奖',
		[36] = '抽奖中',
		[37] = '自动突变一条%s品质',
		[38] = '前一组属性有1条A以品质和2条S以上品质',
		[39] = '功能未开启',
		[40] = '自动抽<color=#30ef30> %s </color>次',
		[41] = '<color=#30ef30>消耗减</color><color=#ef3030> -%s%%</color>',
		[42] = '突变<color=#00FF00>不限次数</color>\n突变<color=#00FF00>消耗再-10%</color>',
		[43] = '等级:  <color=#58da6d>%s</color>',
		[44] = '%s:  <color=#58da6d>%s</color>',
		[45] = '件升级+',
		[46] = '件强化+',
		[47] = "件金色装备。",
		[48] = "件橙色装备。",
		[49] = '宝箱',
		[50] = '突变',
		[51] = '升级',
		[52] = ' 开箱<color=#ef3030>不限次数</color>\n开箱<color=#ef3030>消耗再-%s%%</color>',
		[53] = '自动抽无限次数',
		[54] = '<color=#109A84>当前部位：</color><color=#FFFF00>%s</color>',
		[55] = '<color=#109A84>额外加成：<color=#00FF00>%s</color></color>',
		[56] = '自动突变<color=%s>%s</color>次',
		[57] = '任意属性',
		[58] = '属性选择：<color=#66FF99>%s</color>',
		[59] = '需邀请%s个升到%s级的好友才能使用！',
		[60] = '自动抽奖次数不足',
		[61] = '有%s级品质属性未锁定，是否继续操作？',
		[62] = '2倍加速',
		[63] = '加速开箱',
		[64] = '广告等级: <color=#66FF99>%s级</color>    <color=#%s>%s装</color>以上掉率：<color=#66FF99>+%s%%</color>',
	},

	['UIMainBuff'] = {
		[1] = '附魔',
		[2] = '<color=#8e9bbe>%s</color> + <color=#ffffff>%s</color>',
		[3] = '银币加成',
		[4] = '攻击加成',
		[5] = '生命加成',
		[6] = '银币获得量增加',
		[7] = '攻击力增加',
		[8] = '总生命增加',
		[9] = '%s分钟',
		[10] = '<color=#%s>%s%</color>',
		[11] = '佩戴成功',
		[12] = '取下成功',
	},

	['Schemes'] = {
		[1] = '生命',
		[2] = '攻击',
		[3] = '防御',
		[4] = '物攻',
		[5] = '法攻',
		[6] = '物防',
		[7] = '法防',
		[8] = '暴击',
		[9] = '防暴',
		[10] = '格挡',
		[11] = '穿透',
		[12] = '闪避',
		[13] = '命中',
		[14] = '护甲',
		[15] = '破甲',
		[16] = '伤害减免',
		[17] = '伤害加深',
		[18] = '移动速度',
		[19] = '攻防全属性',
	},
	-- 属性定义
	['DSchemeStruct'] = {
		[1] = '生命',
		[2] = '攻击',
		[3] = '防御',
		[4] = '攻击',
		[5] = '法攻',
		[6] = '物防',
		[7] = '法防',
		[8] = '暴击',
		[9] = '防暴',
		[10] = '格挡',
		[11] = '穿透',
		[12] = '暴击',
		[13] = '命中',
		[14] = '护甲',
		[15] = '破甲',
		[16] = '伤害减免',
		[17] = '伤害加深',
		[18] = '移速',
		[19] = '暴击伤害',
		[20] = '生命觉醒',
		[21] = '攻击觉醒',
		[22] = '攻速',
		[23] = '物攻觉醒',
		[24] = '法攻觉醒',
		[25] = '物防觉醒',
		[26] = '法防觉醒',
		[27] = '全属性觉醒',
		[28] = '神器总属性',
		[29] = '神器总生命',
		[30] = '神器总物攻',
		[31] = '神器总法攻',
		[32] = '神器总物防',
		[33] = '神器总法防',
		[34] = '神器灵件总属性',
		[35] = '神器灵件总生命',
		[36] = '神器灵件总物攻',
		[37] = '神器灵件总法攻',
		[38] = '爆伤',
		[39] = '神器灵件总法防',
		[40] = '装备总属性',
		[41] = '装备总生命',
		[42] = '装备总物攻',
		[43] = '装备总法攻',
		[44] = '装备总物防',
		[45] = '装备总法防',
		[46] = '魔骑总属性',
		[47] = '魔骑总生命',
		[48] = '魔骑总物攻',
		[49] = '魔骑总法攻',
		[50] = '魔骑总物防',
		[51] = '魔骑总法防',
		[52] = '周日',
		[53] = '周一',
		[54] = '周二',
		[55] = '周三',
		[56] = '周四',
		[57] = '周五',
		[58] = '周六',
		[59] = '对人加伤',
		[60] = '对怪加伤',
		[61] = '杀怪经验',
		[62] = '杀怪铜币',
		[63] = '神器总属性',
		[64] = '神器总生命',
		[65] = '神器总物攻',
		[66] = '神器总法攻',
		[67] = '神器总物防',
		[68] = '神器总法防',
		[69] = '魔羽总属性',
		[70] = '魔羽总生命',
		[71] = '魔羽总物攻',
		[72] = '魔羽总法攻',
		[73] = '魔羽总物防',
		[74] = '魔羽总法防',
		[75] = '吸血',
		[76] = '伤害反弹',
		[77] = '特技',
	},


	-- 角色时装
	['UIAirplaneNew'] = {
		[1] = '角色',
		[2] = '培养',
		[3] = '装备',
		[4] = '已出战',
		[5] = '出战机器',
		[6] = '%s +%s',
		[7] = '缺失',
		[8] = '<color=#%s>%s</color>',
		[9] = '生命',
		[10] = '攻击',
		[11] = '已满级',
		[12] = '+%s',
		[13] = '角色部件',
		[14] = '%s:<color=yellow>%s/%s</color>',
		[15] = '减伤',
		[16] = '升级',
		[17] = '激活',
		[18] = '%s/%s',
		[19] = '速度',
		[20] = '角色进化中……',
		[21] = '进化消耗:',
		[22] = '突破',
		[23] = '仙币不足！',
		[24] = '材料不足！',
		[25] = '角色突破中……',
		[26] = '进化概率：%d%%',
		[27] = '%s/%s',
		[28] = '获取途径',
		[29] = '激活后的英雄，战斗中均有几率随机到！',
		[30] = '出战',
		[31] = '进化',
		[32] = '攻击：%s',
		[33] = '点击空白处退出',
		[34] = '<color=#959595>已出战</color>',
		[35] = '功能尚未开启',
		[36] = '前往获取',
		[39] = '吞噬',
		[40] = '<color=#00FF00>可激活</color>',
		[41] = '专属技能：<color=#00FF00>%s</color>',
		[42] = '属性加成：<color=#00FF00>%s</color>',
		[43] = '<color=%s>(%s/%s)</color>',
		[44] = '<color=#184139>激活</color>',
	},

	['UIPetShopNew'] = {
		[1] = '激活',
		[2] = '培养',
		[3] = '出战',
		[4] = '下阵',
		[5] = '专属技能：<color=#00FF00>%s</color>',
		[6] = '专属技能：\n<color=#00FF00>%s</color>',
		[7] = '<color=#00FF00>已出战</color>',
		[8] = '',
		[9] = '',
		[10] = '',
		[11] = '',
		[12] = '',
		[13] = '',
	},


	['UITaskShow'] = {
		[1] = '点击追踪',
		[2] = '[FFFFFF]任务[-]',
		[3] = '[9F9F9F]队伍[-]',
		[4] = '[9F9F9F]任务[-]',
		[5] = '[FFFFFF]队伍[-]',
		[6] = '更多任务',
		[7] = '[6495ED]首领[-]',
		[8] = '[FFFFFF]首领[-]',
		[9] = '[00FF00][u]前往组队[-][/u]',
		[10] = '当前排名:',
		[11] = '未上榜',
		[12] = '   当前伤害:',
		[13] = '伤害:',
		[14] = '伤害',
		[15] = '[3de530]组队挑战，一人击败首领，全队获得奖励！',
		[16] = '[FF2929]伤害排名越靠前，获[-][FFED00]橙装碎片越多[-][FF2929]，提升战力越多！[-]',
		[17] = '[FF2929]击败贵族首领获[-][FFED00]橙装碎片等珍稀材料[-][FF2929]，提升战力！[-]',
		[18] = '  首领        等级  魂力   状态',
	},

	['UITaskShowItem'] = {
		[1] = '[u]更多任务',
		[2] = '请先加入军团',
		[3] = '%s级',
		[4] = '[FFA500]请先加入队伍，才能发组队喊话！[-]',
		[5] = '前往组队',
		[6] = '发 送',
		[7] = '求组喊话',
		[8] = '军团喊话',
		[9] = '您还未加入军团，请先加入军团',
		[10] = '加入军团',
		[11] = '取消',
	},

	['UITaskShowTeamItem'] = {
		[1] = '%s级',
		[2] = '成功向队员发出召集!',
		[3] = '已向队友发出召集！',
		[4] = '立即传送',
		[5] = '跑步前往',
		[6] = '传送到队友[FFFF00]%s[-]附近!',
		[7] = '物品不足',
		[8] = '物品不足!',
		[9] = '该地图无法召集队员',
		[10] = '跟随',
		[11] = '跟随队长',
		[12] = '取消跟随',
		[13] = '级',
	},

	['HeroTaskLC'] = {
		[1] = '<color=#FFFF00>主</color>',
		[2] = '[E71F19]支',
		[3] = '[00FFFF]日',
		[4] = '[FF00FF]阵营',
		[5] = '[FFFF00]助',
		[6] = '[FF7B00]团',
		[7] = '[00FF00]环',
		[8] = '一键成功',
		[9] = '不能完成任务',
		[10] = '未接受任务',
	},
	--开宝箱
	['UIOpenTreasureBox'] = {
		[1] = "免费",
		[2] = "%s\n折",
		[3] = "今日剩余<color=%s>%s</color>次",
		[4] = "龙之预言",
		[5] = "随机获得%s",
		[6] = "<color=#00ff00>宝物</color>",
		[7] = "<color=#00FFFF>5件绿色</color>",
		[8] = "<color=#FFFF00>5件绿色</color>",
		[9] = "龙之预言",
		[10] = "希望宝箱",
		[11] = "免费倒计时： %s",
		[12] = "今日次数已用完！",
		[13] = "一点仙币",
		[14] = "一袋仙币",
		[15] = "一箱仙币",

		[16] = "随机商城",
		[17] = "仙币商城",
		[18] = "宝箱",

		[19] = "今日次数：<color=#%s>%s/%s</color>。",
		[20] = '装备背包中装备过多，请先清理装备背包！',
		[21] = "\n　龙之预言可随机获得宝物。\n　六种宝物随机概率均等。", ---的概率获得风之装备。\n　2．12.5%的概率获得毒之装备。\n　3．12.5%的概率获得光之装备。\n　4．12.5%的概率获得大地装备。\n　5．12.5%的概率获得水之装备。\n　6．12.5%的概率获得暗之装备。\n　7．12.5%的概率获得火之装备。\n　8．12.5%的概率获得雷之装备。
		[22] = "宝箱说明",
		[23] = "随机获得宝物。",
		[24] = "本次操作需花费<color=#0043bf>%s</color>灵晶，是否确认操作？",
	},
	--邀请码
	['UIInvitation'] = {
		[1] = '我的邀请码：<color=#00FF00>%s</color>',
		[2] = '\r\r本次活动期间，邀请来的好友，任意一人升级到%s级，即可免费领取价值%s元的豪华大礼！\n\n邀请1人升级%s级：<color=%s>%s/%s</color>',
		[3] = '免费领取<color=%s>(%s/%s)</color>',
		[4] = '%s\n\n已成功邀请：<color=%s>%s/%s</color>',
		[5] = '',
		[6] = '',
		[7] = '',
		[8] = '',
		[9] = '',
		[10] = '',
	},
	--快速升级TIPS
	['QuickUpgrade'] = {
		[1] = '快速升级',
		[2] = '[ffcc00]地宫挂机[-]双倍经验\n是否前往',
		[3] = '立即前往',
		[4] = '取消',
		[5] = '请先加入军团',
		[6] = '传送符不足，寻路前往',
	},

	['UIMainTask'] = {
		[1] = '你已经没有主线任务了',
		[2] = '[FFA500]需%s级接[-]',
		[3] = '已经完成所有主线',
		[4] = '[FFFFFFFF]完成任务',
		[5] = '[FFFFFFFF]立即前往',
		[6] = '[FFFFFFFF]接受任务',
		[7] = '任务描述',
		[8] = '完成任务',
		[9] = '任务奖励',
		[10] = '完成任务',
		[11] = '任务',
	},
	--游戏副本
	['UIGameEctype'] = {
		[1] = '%s级解锁  %s',
		[2] = '',
		[3] = '',
		[4] = '',
		[5] = '',
		[6] = '',
		[7] = '',
		[8] = '',
		[9] = '',
		[10] = '生存',
		[11] = "挑战",
		[12] = "生存模式",
		[13] = "生存模式内产出大量伙伴等稀有物品\n每天最多允许挑战3次\n每次挑战需要消耗1把生存之匙从\n来没有人能坚持到100波",
		[14] = "最高纪录:  <color=#58da6e>%s</color>      <color=#f789ff>( 全球排名%s )</color>",
		[15] = "购买【超级月卡】后才能使用此功能",
		[16] = "未上榜",
		[17] = "<color=#FFA2FF>未上榜，且击败怪物未超过%s只，没有奖励！</color>",
		[18] = '排名奖励每天可以领取1次！',
	},
	--清除广告
	['UIADFree'] = {
		[1] = '<color=#49536C>购买奖励：<color=#488ccc>%d灵晶</color></color>',
		[2] = '<color=#49536C>每日可领取：<color=#488ccc>%d灵晶</color></color>',
		[3] = '<color=#000000>今日已看广告次数：</color><color=%s>%s/%s</color>',
	},

	['SilverAndexpFlyMeesage'] = {
		[1] = '经验',
		[2] = '铜币',
		[3] = '修为',
		[4] = '点券',
		[5] = '功勋',
		[6] = '历练',
		[7] = '绑定灵晶',
		[8] = '魂力值',
		[9] = '强化经验',
		[10] = '祭炼',
		[11] = '灵晶',
		[12] = '仙币',
		[13] = '<color=yellow>%s</color>',
		[14] = '<color=#FFA500>%s</color>',
	},
	['UIAttributeUp'] = {
		[1] = '<color=#FFF900>攻略：</color>前期<color=#FFF900>仙币加成</color>越高，中后期<color=#FFF900>优势</color>越大！',
		[2] = '按住不放可自动升级',
		[3] = 'x%.1f',
		[4] = '点数：<color=yellow>%d</color>\n百分比：<color=yellow>%.2f%%</color>',
		[5] = 'LV.%s',
		[6] = '刷新(%s/%s)',
		[7] = '<color=#C1BCBC>停</color>',
		[8] = '<color=#FFDD00>拖动英雄可移动英雄位置</color>',
		[9] = '生存',
	},
	['UIProbabilityInfo'] = {
		[1] = '普通',
		[2] = '高级',
		[3] = '稀有',
		[4] = '史诗',
		[5] = '传说',
		[6] = '神话',
		[7] = '超越',
		[20] = '等级\n%s'
	},
	--装备升级
	['UIEquipUpgrade'] = {
		[1] = '装备',
		[2] = '卸下',
		[3] = '升级',
		[4] = '等级：%s/%s',
		[5] = '',
		[6] = '',
		[7] = '',
	},
	['UIRecommendCommodities'] = {
		[19] = '看<color=%s>%s/%s</color>次广告免费激活'
	},
	--战斗
	['BattleManager'] = {
		[1] = "携带体魄数量不足！",
		[2] = "发现有未完成的战斗，是否继续？",
	},
	['UIEquipBox'] = {
		[1] = '<color=#00FF00><color=%s>%s</color>/%s</color>',
		[2] = '开箱奖励',
		[3] = '自动<color=%s>(%s)</color>',
		[4] = '取消<color=%s>(%s)</color>',
		[15] =
			'宝箱广告等级：<color=#66FF99>%s级</color>\n'
			.. '增加自动次数：<color=#66FF99>%s次</color>\n'
			.. '消耗减少：<color=#FFA2FF>%s</color>\n'
			.. '下级需看广告：<color=#FFA2FF>%s/%s次</color>',
		[32] = '收看广告，自动抽奖！',
		[52] = '开箱<color=#ef3030>不限次数</color>\n'
			.. '开箱<color=#ef3030>消耗再-%s%%</color>',

	},
	['UISurvivalEctype'] = {
		[1] = "",
		[3] = "",
		[4] = "",
		[5] = "未上榜",
	},
	--维护补偿公告
	['UICompensation'] = {
		[1] = '维护补偿',
		[2] = '无公告内容',
	},
	--赠送魂力
	['UIGivePhysicalPower'] = {
		[1] = '早餐',
		[2] = '午餐',
		[3] = '晚餐',
		[4] = '夜宵',
		[5] = '祝您用餐愉快!',
	},
	--结算界面
	['UISettleAccounts'] = {
		[1] = '今日剩余次数<color=#00ff00>%s/%s</color>',
		[2] = '历经鏖战，击溃强敌！恭喜通关副本，荣耀与丰厚奖励已入账！',
		[3] = '激战未捷，遗憾退场！强敌虽悍，重整旗鼓，胜利终会属于勇者！',
		[4] = '',
		[5] = '',
	},
	--购买确认窗口
	['UIPurchaseWindows'] = {
		[1] = '购买物品',
	},
	--确认窗口
	['UINotarizeWindows'] = {
		[1] = '提示',
		[2] = '今天不在提示',
		[3] = '',
		[4] = '',
		[5] = '',
		[6] = '',
	},
	["UIPurchasePhysicalPower"] = {
		[1] = "今日已购买 %s/%s 次！",
		[2] = "是否花费              购买          ？"
	},

	["UIYQSMoney"] = {
		[1] = "今日已摇过 %s/%s 次！",
		[2] = "是否花费              购买          ？"
	},

	--新商店界面
	["UIShopNew"] = {
		[1] = "免费",
		[2] = "%s折",
		[3] = "今日剩余<color=%s>%s</color>次。",
		[4] = "宝箱",
		[5] = "必出%s装备",
		[6] = "<color=#00ff00>普通</color>",
		[7] = "<color=#00FFFF>高级</color>",
		[8] = "<color=#00FFFF>高级</color>",
		[9] = "宝藏",
		[10] = "至尊宝藏",
		[11] = "免费倒计时： %s",
		[12] = "今日次数已用完！",
		[13] = "一点仙币",
		[14] = "一袋仙币",
		[15] = "一箱仙币",

		[16] = "随机商城",
		[17] = "仙币商城",
		[18] = "充值",
		[19] = "                是否确认消耗%s灵晶购买该物品?",
	},

	--新商店界面
	["UIRechargeNew"] = {
		[1] = "免费",
		[2] = "%s折",
		[3] = "今日剩余<color=%s>%s</color>次。",
		[4] = "宝箱",
		[5] = "必出%s装备",
		[6] = "<color=#00ff00>普通</color>",
		[7] = "<color=#00FFFF>高级</color>",
		[8] = "<color=#00FFFF>高级</color>",
		[9] = "宝藏",
		[10] = "至尊宝藏",
		[11] = "免费倒计时： %s",
		[12] = "今日次数已用完！",
		[13] = "一点仙币",
		[14] = "一袋仙币",
		[15] = "一箱仙币",

		[16] = "随机商城",
		[17] = "仙币商城",
		[18] = "充值",
	},

	--新设置界面
	["UISettingNew"] = {
		[1] = '音乐',
		[2] = '音效',
	},
	--体魄界面
	["UIEquipWeapon"] = {
		[1] = '可出战',
		[2] = '通过章节%s解锁',
		[3] = '使用',
		[4] = '移除',
		[5] = '全部(%s/%s)',
		[6] = '未解锁',
		[7] = '已装备(%s/%s)',
		[8] = '<color=#00FF00>已出战</color>',
	},
	--体魄界面
	["UIEquipWeaponInfo"] = {
		[1] = '未解锁',
		[2] = '目标',
		[3] = '自身',
		[4] = '最近敌人',
		[5] = '随机目标',
		[6] = '最近友方',
		[7] = '生命',
		[8] = '攻击',
		[9] = '生命',
		[10] = '射程',
		[11] = '生命恢复',
		[12] = '叠甲',
		[13] = '需装备等级%s级',
		[14] = '%s级解锁：%s',
		[15] = '装备未激活',
		[16] = '%s级解锁',
		[17] = '合成%s阶',
		[18] = '复活回合',
		[19] = '等级%s',
	},

	--角色界面
	["UIRoleEquip"] = {
		[1] = '未解锁',
		[2] = '目标',
		[3] = '自身',
		[4] = '最近敌人',
		[5] = '随机目标',
		[6] = '最近友方',
		[7] = '生命',
		[8] = '攻击',
		[9] = '生命',
		[10] = '射程',
		[11] = '生命恢复',
		[12] = '叠甲',
		[13] = '需装备等级%s级',
		[14] = '%s级解锁：%s',
		[15] = '装备未激活',
		[16] = '%s级解锁',
		[17] = '合成%s阶',
		[18] = '复活回合',
		[19] = '等级%s',
		[20] = '解锁',
		[21] = '升级',
		[22] = '已满级',
		[23] = '<color=#00FF00>%s</color>',
	},
	--宝箱规则介绍
	["UIBoxRule"] = {
		[1] = "宝箱奖励",
		[2] = "开宝箱提升等级，增加宝箱奖励",
	},
	--日常副本
	["UIEverydayEctype"] = {
		[1] = "今日剩余挑战次数：%s",
		[2] = "挑战次数不足！",
		[3] = "挑战",
		[4] = "扫荡",
	},
	--活动副本
	["UIActivityEctype"] = {
		[1] = "今日剩余挑战次数：%s！",
		[2] = '%s级解锁！',
		[3] = "剩余时间：%s！",
		[4] = "极限挑战！",
		[5] = "击败怪物获得积分\n根据积分计算奖励，积分越多奖励越好\n每天根据积分排名发放排名奖励！",
		[6] = "进入挑战！",
	},
	--活动副本
	["UIGoldCaveEctype"] = {
		[1] = "剩余次数：%s",
		[2] = '%s级解锁！',
		[3] = "剩余时间：%s！",
		[4] = "极限挑战！",
		[5] = "击败怪物获得积分\n根据积分计算奖励，积分越多奖励越好\n每天根据积分排名发放排名奖励！",
		[6] = "进入挑战！",
		[7] = "挑战次数不足！",
	},
	--活动副本
	["UIForbiddenAreaEctype"] = {
		[1] = "剩余次数：%s",
		[2] = '%s级解锁！',
		[3] = "剩余时间：%s！",
		[4] = "极限挑战！",
		[5] = "击败怪物获得积分\n根据积分计算奖励，积分越多奖励越好\n每天根据积分排名发放排名奖励！",
		[6] = "进入挑战！",
		[7] = "今日挑战次数已用完",
	},
	--宝箱购买界面
	["UIBuyBox"] = {
		[1] = ""
	},
	--主线副本
	["UIMainEctype"] = {
		[1] = "<color=#FFA2FF>魂力恢复中</color>\n<color=#00FF00>前往免费领魂力</color>",
		[2] = "通关第%s章后解锁！",
		[3] = "第%s波",
	},
	--宝箱概率
	["UIBoxProbability"] = {
		[1] = "可能获得以下装备碎片：\n<color=#FF5000>每个装备的概率：%s%%！</color>"
	},
	--角色资产
	["UIRoleAssets"] = {

	},
	--看广告加速游戏
	["UIGameAcceleration"] = {
		[1] = "是否观看视频，在本局获得<color=#00FF00>2倍速</color>提升\n(再看<color=#00FF00>%s次</color>后，可<color=#00FF00>永久</color>生效)！",
		[2] = "获得2倍速提升！",
	},
	--秘境副本
	["UIPaTaFuBen"] = {
		[1] = "第%s关",
		[2] = "已通关",
		[3] = "挑战",
	},
	--爬塔副本
	["UIMiJingFuBen"] = {
		[1] = "角色等级%s开启！。",
		[2] = "请先通过第%s关！。",
		[3] = "　　在阿尔法诺瓦的五大洲中，有一些极其神秘的区域。这些区域充满了未知的危险。当然，危险往往也伴随着机遇，阿尔法诺瓦那些站在最顶端的强者，无一不是从秘境中获得过巨大好处！",
		[4] = "剩余次数：%s",
	},
	--角色天赋
	["UIRoleTalent"] = {
		[1] = "<color=#fff300>已激活！</color>",
		[2] = "<color=#FFFF00>需解锁上一级！</color>",
		[3] = "　　随着科技的高度发展，人类掌握了附魔改造技术。通过植入特殊的附魔模块来增强人体的生理机能和战斗能力。这项技术最初由塔拉大陆的科学家们开发，后来逐渐普及到了整个阿尔法诺瓦。",
		[4] = "成功率：",
		[5] = "附魔%s级",
		[6] = "星篆升级规则：\n1.星篆未升级属性属于未知状态！只有星篆升级后才能知道增加什么属性！\n2.材料足够后，可点击星篆升级按钮获得星篆升级属性。\n3.星篆等级越高，获得的星篆属性越多，伙伴越强大。",
	},
	--角色法宝
	["UITreasuredTricks"] = {
		[1] = "等级%s",
		[2] = "　　圣灵杖主体由一种未知的金属制成。杖身中央有一颗能够增加使用者精神力的水晶球。升级后的圣灵杖，可以极大增强使用者的实力！",
		[3] = "　　神明天书的封面由一种未知的材料制成，上面刻有复杂的龙之石图案，这些图案似乎能够随着光线的变化而变幻色彩。书页由一种轻薄而坚韧的材质制成，每一页都散发着淡淡的光芒，仿佛蕴含着无穷的知识。升级后的神明天书，可以极大增强使用者的实力！",
		[4] = "　　深渊魔方的外观如同一个由晶体构成的小型立方体，表面布满了复杂的龙之石和图案。这些图案在黑暗中会发出微弱的光芒，仿佛深渊魔方内部隐藏着不可见的力量。当持有者触摸到魔方时，这些图案会变得更为明亮，似乎与持有者产生了某种联系。升级后的神明天书，可以极大增强使用者的实力！",
		[5] = "通关主线%s章后解锁",
		[6] = "轩辕剑",
		[7] = "山河社稷图",
		[8] = "玲珑宝塔",
		[9] = "<color=#fff300>已激活</color>",
		[10] = "<color=#001FFF>需解锁上一级</color>",
	},

	--龙之石
	["UIShengHun"] = {
		[1] = "等级%s",
		[2] = "　　圣灵杖主体由一种未知的金属制成。杖身中央有一颗能够增加使用者精神力的水晶球。升级后的圣灵杖，可以极大增强使用者的实力！",
		[3] = "　　神明天书的封面由一种未知的材料制成，上面刻有复杂的龙之石图案，这些图案似乎能够随着光线的变化而变幻色彩。书页由一种轻薄而坚韧的材质制成，每一页都散发着淡淡的光芒，仿佛蕴含着无穷的知识。升级后的神明天书，可以极大增强使用者的实力！",
		[4] = "　　深渊魔方的外观如同一个由晶体构成的小型立方体，表面布满了复杂的龙之石和图案。这些图案在黑暗中会发出微弱的光芒，仿佛深渊魔方内部隐藏着不可见的力量。当持有者触摸到魔方时，这些图案会变得更为明亮，似乎与持有者产生了某种联系。升级后的神明天书，可以极大增强使用者的实力！",
		[5] = "通关主线%s章后解锁",
		[6] = "轩辕剑",
		[7] = "山河社稷图",
		[8] = "玲珑宝塔",
		[9] = "<color=#fff300>已激活</color>",
		[10] = "<color=#09FDEB>需解锁上一级</color>",
		[11] = "<color=#30ef30>激活成功！</color>",
		[12] = "<color=#30ef30>激活成功！</color>",
	},

	--祈祷
	["UIXinFa"] = {
		[1] = "等级%s",
		[2] = "　　圣灵杖主体由一种未知的金属制成。杖身中央有一颗能够增加使用者精神力的水晶球。升级后的圣灵杖，可以极大增强使用者的实力！",
		[3] = "　　神明天书的封面由一种未知的材料制成，上面刻有复杂的龙之石图案，这些图案似乎能够随着光线的变化而变幻色彩。书页由一种轻薄而坚韧的材质制成，每一页都散发着淡淡的光芒，仿佛蕴含着无穷的知识。升级后的神明天书，可以极大增强使用者的实力！",
		[4] = "　　深渊魔方的外观如同一个由晶体构成的小型立方体，表面布满了复杂的龙之石和图案。这些图案在黑暗中会发出微弱的光芒，仿佛深渊魔方内部隐藏着不可见的力量。当持有者触摸到魔方时，这些图案会变得更为明亮，似乎与持有者产生了某种联系。升级后的神明天书，可以极大增强使用者的实力！",
		[5] = "通关主线%s章后解锁。",
		[6] = "轩辕剑",
		[7] = "山河社稷图",
		[8] = "玲珑宝塔",
		[9] = "<color=#fff300>已激活。</color>",
		[10] = "<color=#001FFF>需解锁上一级。</color>",
		[11] = "<color=#30ef30>祈祷成功！</color>",
		[12] = "<color=#30ef30>+%s心法经验！</color>",
		[13] = "%s，遇到衰神，魂力<color=#%s>%s</color>。",
		[14] = "%s，运气不佳，魂力<color=#%s>+%s</color>。",
		[15] = "%s，有点运气，魂力<color=#%s>+%s</color>。",
		[16] = "%s，运气极佳，魂力<color=#%s>+%s</color>。",
		[17] = "祈祷说明：\n1、祈祷时，随机获得10~30点魂力；\n2、每次祈祷，需要消耗“祈祷石”;\n3、祈祷成功后，可增加祈祷进度;\n4、祈祷进度每次达到100后，祈祷等级+1;\n5、祈祷等级提升后，属性将会随机变化1次;\n6、祈祷等级越高，随机获得的属性越好。",
	},
	
	--角色信息
	["UIRoleInfo"] = {
		[1] = '个人信息',
		[2] = '等级：%s',
		[3] = '修改名字成功！',
		[4] = '第%s关',
		[5] = '音乐',
		[6] = '音效',
	},
	--精英副本
	["UIEliteEctype"] = {
		[1] = "副本介绍",
		[2] = "	强力首领来袭！！！\n所有怪物获得【狂暴效果加成】",
		[3] = "剩余次数：%s",
		[4] = "今日挑战次数已用完！",
		[5] = "请先通关第%s关！",
		[6] = "	强者不惧！不管面对的敌人如何强大，都勇往直前，从不退缩，这才是真正的强者！在这里，你将面对来自未知之地的强者的挑战！",
		[7] = "第%s关",
	},
	--首领副本
	["UILeaderEctype"] = {
		-- [1] = "挑战说明：",
		-- [2] = "强力首领来袭！！！\n所有怪物获得【狂暴效果加成】",
		-- [3] = "剩余次数：%s。",
		-- [4] = "今日挑战次数已用完！",
		-- [5] = "请先通关第%s关",
		[1] = "角色等级%s开启。",
		[2] = "请先通过第%s关。",
		[3] = "　　在阿尔法诺瓦的五大洲中，有一些极其神秘的区域。这些区域充满了未知的危险。当然，危险往往也伴随着机遇，阿尔法诺瓦那些站在最顶端的强者，无一不是从秘境中获得过巨大好处！",
		[4] = "今日剩余次数：%s",
	},
	--纹章背包
	["UIEquipKnapsack"] = {
		[1] = "基础属性：",
		[2] = "附加属性：",
		[3] = "<color=#CCCCCC>%s  (%s装激活)</color>",
		[4] = "攻击：",
		[5] = "生命：",
		[6] = "伙伴等级：",
		[7] = "解锁",
		[8] = "培养",
		[9] = "出战",
		[10] = "已出战",
		[11] = "培养成功！",
		[12] = "纹章攻击：",
		[13] = "纹章生命：",
		[14] = "解锁成功！",
		[15] = '生命',
		[16] = '攻击',
		[17] = '<color=#00FF00>%s</color>',
	},
	['SensitiveWord'] = {
		[1] = '这游戏真好玩',
		[2] = '我喜欢玩这游戏',
		[3] = '这是我玩过最好的游戏',
	},
}

function GetGameText(fileName, subID)
	if not fileName or not subID then
		error('GetGameText error1 ' .. tostring(fileName) .. ' ' .. tostring(subID))
		return ''
	end

	local textList = gameText[fileName]
	if not textList then
		error('GetGameText error2 ' .. tostring(fileName) .. ' ' .. tostring(subID))
		return ''
	end

	local result = textList[subID]
	if not result then
		error('GetGameText error3 ' .. tostring(fileName) .. ' ' .. tostring(subID))
		return ''
	end
	return result
end

--------------------------------------------------------------------
-- 通用文本
--------------------------------------------------------------------
CommonTextID = {
	OK = '确定',
	CANCEL = '取消',
	RETURN = '返回',
	CLOSE = '关闭',
	GET = '领取',
	IS_GET = '已领取',
	IS_PURCHASE = '已购买',
	ZW_MONEY = '元',
	SECOND = '秒',
	LEVEL = '级',
	GRADE = '等级',
	LACK_GOODS = '物品不足',
	SOLD_OUT = '售罄',
	IS_FULL_LEVEL = '已满级',
	NIL = '无',
	NOT_SATISFIED_RECEIVE_CONDITION = '不满足领取条件',
}

--中文数字1到10
local chineseNumbers = { '零', '一', '二', '三', '四', '五', '六', '七', '八', '九' }
--中文单位
local chineseUnits = { '', '十', '百', '千', '万', '十万', '百万', '千万', '亿', '十亿', '百亿', '千亿' }
--中文大写数字1到10
local bigChineseNumbers = { '零', '壹', '贰', '叁', '肆', '伍', '陆', '柒', '捌', '玖' }
--中文大写单位
local bigChineseUnits = { '', '拾', '佰', '仟', '万', '拾万', '佰万', '仟万', '亿', '拾亿', '佰亿', '仟亿' }
---转中文数字
---@overload fun(num: integer)
---@param num integer 现有范围int32数字
---@param isBig boolean 是中文大写
---@return string
function ToChineseNumbers(num, isBig)
	--数字
	local Numbers
	--单位
	local Units
	--是中文大写
	if isBig == true then
		Numbers = bigChineseNumbers
		Units = bigChineseUnits
	else
		Numbers = chineseNumbers
		Units = chineseUnits
	end
	--转为数字，并取整(去除小数)
	num = math.floor(tonumber(num) or 0)
	if num == 0 then return Numbers[1] end

	--负号
	local negative = ''
	--去除负号
	if num < 0 then
		num = math.abs(num)
		negative = '-' --'负'
	end

	--获取数字长度
	local len = string.len(num)
	local result = ''
	local digit, len2, c, u
	for i = 1, len, 1 do
		--获取一个字符，并转换为数字
		digit = tonumber(string.sub(num, i, i))
		--获取对应的中午数字
		c = Numbers[digit + 1]
		--获取对应单位
		u = Units[len - i + 1] or '_'

		--字符拼接
		if digit == 0 then
			len2 = string.len(result)
			--判断是否是以'零'结尾，防止出现两个'零'
			if string.sub(result, len2 - 2, len2) ~= Numbers[1] then
				result = result .. c
			end
		else
			result = result .. c .. u
		end
	end

	len2 = string.len(result)
	--去除末尾的'零'
	if string.sub(result, len2 - 2, len2) == Numbers[1] then
		result = string.sub(result, 1, len2 - 3)
	end

	--添加负号'-'
	return negative .. result
end

--装备类型(部位)描述
local equipTypeDescribe = {
	[1] = '蜜蜡', -- 装备-头饰
	[2] = '田黄', -- 装备-衣服
	[3] = '紫晶', -- 装备-鞋子
	[4] = '变石', -- 装备-护腕
	[5] = '宝钻', -- 装备-水之装备
	[6] = '火玉', -- 装备-暗之装备
	[10] = '锆石', -- 装备-火之装备
	[11] = '石英', -- 装备-雷之装备
}
---获取装备类型(部位)描述
---@param type integer 装备类型
---@return string
function GetEquipTypeDescribe(type)
	return equipTypeDescribe[type]
end

--装备属性描述
local equipAttributeDescribe = {
	[0] = '生命',
	[1] = '基础攻击',
	[2] = '生命恢复',
	[3] = '攻击',
	[4] = '伤害范围',
	[5] = '狂暴时长',
	[6] = '能量上限',
	[7] = '子弹伤害',
	[8] = '直接伤害',
	[9] = '伤害减免',
	[10] = '狂暴伤害',
	[11] = '射程',
	[12] = '子弹速度',
	[13] = 'XP值恢复',
	[14] = '能量上限',
	[15] = '减伤',
	[16] = '加伤',
	[17] = '移动速度',
	[18] = '基础生命',
	[19] = '调用BUFF',
	[20] = 'XP技能伤害',
	[21] = '暴击率',
	[23] = '攻击速度',
	[25] = '总生命',
	[27] = '总攻击',
	[45] = '爆伤伤害',
	[46] = 'BOSS伤害',
	[88] = '获得经验',
	[89] = '获得银币',
	[90] = '吸血',
	[91] = '反伤',
}

---获取装备属性描述
---@overload fun(type: integer)
---@param type integer 属性类型
---@return string 没有返回空字符 ''
function GetAttributeTypeDesc(type)
	return equipAttributeDescribe[type] or ''
end

--活动名称
local activityName = {
	-- [WndID.ClearAD] = '清除广告',
	[WndID.DirectBuyGoods] = '物品直购',
	[WndID.SignIn] = '签到',
	[WndID.EveryDayTask] = '每日任务',
	[WndID.Role] = '角色背包',
	[WndID.RankingList] = '排行榜',
	[WndID.Challege] = '挑战',
	[WndID.LuckDraw] = '抽奖',
	[WndID.PetShop] = '宠物商店',
	[WndID.Fate] = '命魂',
	[WndID.Buff] = 'Buff',
	[WndID.DiscountGift] = '特惠礼包',
	[WndID.FirstRecharge] = '首冲',
	[WndID.SevenDayInvestment] = '七日投资',
	[WndID.TimeLimit] = '限时放送',
	[WndID.RecommendCommodities] = '推荐商品',
	[WndID.MonthCard] = '月卡',
	[WndID.ADFree] = '免广告',
	[WndID.Invitation] = '邀请礼包',
	[WndID.Email] = '邮件',
	[WndID.Setting] = '设置',
	[WndID.Equip] = '突变',
	[WndID.RoleNew] = '属性',
	[WndID.AirPlaneNew] = '角色',
	[WndID.PetShopNew] = '伙伴',
	[WndID.Welfare] = '灵晶商店',
	[WndID.OpenTreasureBox] = '黄金宝藏',
	[WndID.StageReward] = '章节礼包',
	[WndID.Shop] = '商店',
	[WndID.EquipUpgrade] = '升级',
	[WndID.EquipBox] = '宝箱',
	[WndID.GameEctype] = '挑战',
	[WndID.SurvivalEctype] = '极限挑战',
	[WndID.EquipKnapsack] = '背包',
	[WndID.GivePhysicalPower] = '用餐',
	[WndID.RoleTalent] = '附魔',
	[WndID.EquipWeaponInfo] = '体魄详情：',
	[WndID.EquipWeapon] = '体魄',
	[WndID.TreasuredTricks] = '遗物',
	[WndID.ChaoJiChongZhi] = '超级充值',
}

---获取活动名称
---@param wndID integer 窗口ID
function GetActivityName(wndID)
	return activityName[wndID] or ''
end
