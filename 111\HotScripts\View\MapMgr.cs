﻿// ReSharper disable InconsistentNaming

using System.Collections.Generic;

using Apq.Unity3D.Extension;

using CsvTables;

using Cysharp.Threading.Tasks;

using UnityEngine;

namespace View
{
    /// <summary>
    /// 地图管理器
    /// </summary>
    public class MapMgr : MonoBehaviour
    {
        /// <summary>
        /// Maps节点
        /// </summary>
        public GameObject MapsObj { get; set; }
        
        /// <summary>
        /// 已加载的地图
        /// </summary>
        public Dictionary<int, Map> Maps { get; } = new();
        
        /// <summary>
        /// 当前地图
        /// </summary>
        public Map Map { get; set; }

        /// <summary>
        /// 预加载多个地图(地图未与关卡关联，隐藏在场景中)
        /// </summary>
        public async UniTask PreLoadMap(params int[] mapIds)
        {
            var csv_Map = SingletonMgr.Instance.CsvLoaderMgr.GetOrAddLoader<CatMainMapCsv>().Dic;
            foreach (var mapId in mapIds)
            {
                if (csv_Map.TryGetValue(mapId, out var csvRow_Map))
                {
                    var mapPrefab =
                        await ResMgrAsync.LoadResAsync<GameObject>(
                            $"Assets/Temp/model/Map/{csvRow_Map.MapName}.prefab");
                    var mapObj = Instantiate(mapPrefab, MapsObj.transform);
                    mapObj.SetActive(false);
                    var map = mapObj.GetOrAddComponent<Map>();
                    map.CsvRow_CatMainMap.Value = csvRow_Map;
                    Maps[mapId] = map;
                }
            }
        }
        
        /// <summary>
        /// 切换 地图、Npc管理器
        /// </summary>
        public async UniTask<Map> SwitchMap(int mapId)
        {
            if (!Maps.ContainsKey(mapId))
            {
                await PreLoadMap(mapId);
            }
            
            if (Maps.TryGetValue(mapId, out var map))
            {
                if (map == Map)
                {
                    return Map;
                }
                
                if (Map)
                {
                    Map.gameObject.SetActive(false);
                }
                Map = map;
                if (Map)
                {
                    Map.gameObject.SetActive(true);
                }

                return Map;
            }

            return null;
        }
    }
}
