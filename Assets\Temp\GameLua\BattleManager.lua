--[[
********************************************************************
    created:    2024/06/16
    author :
    purpose:    战斗管理
*********************************************************************
--]]

local luaID = ('BattleManager')

local m = {}
--战斗管理
BattleManager = m

--- 进入战斗（准备阶段）
function m:PrepareEnterBattle(stageId)
    --测试--快速通关，取消注释即可
    if SWITCH and SWITCH.FASTER_CUSTOMS_CLEARANCE then
        local form = {}
        form["stageID"] = stageId
        form["isSuccess"] = true
        form["roundNo"] = 100
        LuaModuleNew.SendRequest(LuaRequestID.SaveEctypeProgress, form, function(resultCode, content, callbackParam)
            if resultCode == RESULT_CODE_BASE.RESULT_COMMON_SUCCEED[1] then
                print("保存副本数据成功")
                -- 清除战斗进度
                if LuaToCshapeManager.Instance then
                    LuaToCshapeManager.Instance:ClearProgress()
                end
            else
                ResultCode.ShowResultCodeCallback(resultCode, content)
            end
        end)
        local actorID = EntityModule.hero:GetProperty(ENTITY_FIELD.ENTITY_FIELD_ID)
        PlayerPrefsManager:SetInt("CurGuideType"..actorID, 14)

        local catMainStage = Schemes.CatMainStage:Get(stageId)
        local str_req2 = string.format('LuaRequestSetLogicData?logicID=%d&logicValue=%d', EctypeSaveLogic[catMainStage.FrontType],stageId)
        LuaModule.RunLuaRequest(str_req2, function(resultCode2, content2)
            print("resultCode2 = ", resultCode2)
            if resultCode2 == RESULT_CODE_BASE.RESULT_COMMON_SUCCEED[1] then
            end
        end)

        return
    end

    m.stageId = stageId
    m.isInitiativeExit = false
    --关卡配置
    local cfg = Schemes.CatMainStage:Get(stageId)
    m.catMainStage = cfg
    m.mainType = cfg.FrontType

    --测试--直接进入战斗，取消注释即可
    -- if true then
    --     m.RequestEnterBattleScene()
    --     return
    -- end

    -- 进入战斗前执行装备关联检查
    m.CheckAndEnsureEquipAssociationForBattle()

    --已出战圣物数量
    local num = GamePlayerData.ActorEquip:GetWearEquipAmount(1)
    print('----携带装备数量------stageId=', stageId)
    print('----携带装备数量------num=', num)
    print('----携带装备数量------MaxSkillCount=', cfg.MaxSkillCount)
    --判断是否携带了指定数量的装备
    if num < cfg.MaxSkillCount then
        --提示 "携带装备数量不足"
        HelperL.ShowMessage(TipType.FlowText, GetGameText(luaID, 1))
        return
    end

    local expendList = HelperL.Split(cfg.Need, ';')
    --消耗物品ID
    local expID = tonumber(expendList[1]) or 0
    --消耗物品数量
    local expNum = tonumber(expendList[2]) or 0
    --广告物品ID
    -- local adID = tonumber(expendList[3]) or 0
    --消耗物品ID2
    local expID2 = tonumber(expendList[4]) or 0
    --消耗物品数量2
    local expNum2 = tonumber(expendList[5]) or 0

    --消耗判断
    if HelperL.IsLackGoods(expID, expNum, false) then
        return
    end
    if HelperL.IsLackGoods(expID2, expNum2, false) then
        return
    end

    local goodInfo = "0"
    local costInfo = string.format("%s;%s|%s;%s", expID, expNum, expID2, expNum2)
    --请求扣除消耗物品
    HelperL.RequestDirectGiveGoodsy(goodInfo, costInfo, function(resultCode, content)
        if resultCode == RESULT_CODE_BASE.RESULT_COMMON_SUCCEED[1] then
            -- 先清进度
            LuaToCshapeManager.Instance:ClearProgress()
            LogicValue.SetBattleProgress(BattleProgress_Index.RoundNo, 0)

            ---请求进入战斗场景
            m.RequestEnterBattleScene()
        else
            ResultCode.ShowResultCodeCallback(resultCode, content)
        end
    end)
end

-- 请求进入战斗成功后，开始给战斗场景传数据
function m:EnterBattle(mainType, stageId)
    m.mainType = mainType
    m.stageId = stageId
    -- local startLevel =  tostring(EntityModule.hero:GetProperty(CREATURE_FIELD.CREATURE_FIELD_LEVEL))
    -- print("玩家开始的战斗前的等级" .. startLevel)
    local maxStage = HeroDataManager:GetLogicData(LOGIC_DATA.DATA_SOCIETY_ECTYPETOWER_CURSTAGE)
    --local normalWeapon = HelperL.GetMainWeaponID()
    local normalWeapon = HelperL.GetWeaponEquipID()
    local allEquipBuff = HelperL.GetAllEquipmentBuff()
    print("stageId" .. stageId)
    print("主火之纹章" .. HelperL.GetMainWeaponID())
    print("细胞= " .. HelperL.GetWeaponEquipID())
    --print("HelperL.GetPlayerFieldsByClient() = " .. HelperL.GetPlayerFieldsByClient(false))
    print("HelperL.GetSecondWeaponID() = " .. HelperL.GetSecondWeaponID())
    print("HelperL.GetSidekickID() = " .. HelperL.GetSidekickID())
    print("HelperL.GetHoleGemList() = " .. m.GetHoleGemList())
    print("HelperL.GetAllEquipmentBuff() = " .. allEquipBuff)
    print("是否是首通 = " .. (HeroDataManager:GetLogicData(LOGIC_DATA.DATA_SOCIETY_ECTYPETOWER_CURSTAGE) + 1) == stageId)
    local aircraftSkin = HelperL.GetWeaponEquipID()
    local realWeapon
    if normalWeapon > 0 then
        realWeapon = normalWeapon
    else
        realWeapon = aircraftSkin
    end

    -- local playerFields = ActorProp.GetPlayerFieldsByClient();
    -- local strPlayerFields = "";
    -- for key, value in pairs(playerFields) do
    --     strPlayerFields = strPlayerFields .. "|" .. key .. ";" .. value;
    -- end

    local skillProps = json.encode(SkillProp.GetPlayerSkillPropByClient());
    print("装备给技能的提升:", skillProps);

    EntityModule.luaToCshape:LuaSendData(
        maxStage,
        stageId,
        aircraftSkin,
        realWeapon,
        HelperL.GetPlayerFieldsByActor(),
        skillProps,
        --strPlayerFields,
        --HelperL.GetSecondWeaponID(), --暂时屏蔽
        0,
        --m.GetHoleGemList(),
        '',
        EntityModule.hero:GetProperty(PLAYER_FIELD.PLAYER_FIELD_MONEY),
        allEquipBuff,
        (HeroDataManager:GetLogicData(LOGIC_DATA.DATA_SOCIETY_ECTYPETOWER_CURSTAGE) + 1) == stageId,
        HelperL.HadBoughtCardID(24)
    );
    EntityModule.luaToCshape.IsFighting = true;

    m.InitBattle(stageId)

    SceneManager:LoadSceneWithLoading('GameScene', true, false)
    UIManager:CloseWnd(WndType.Main)
    --   UIManager:CloseWndByID(WndID.TipBestEquipment)
    m.skillRefreshNum = 2
end

--检查战斗进度
function m.CheckBattleProgress()
    if (GamePlayerData.GameEctype.GetBattleProgress(BattleProgress_Index.RoundNo) > 0) then
        local _type =  NotarizeWindowsType.Windows5
        local heroLevel = EntityModule.hero:GetProperty(CREATURE_FIELD.CREATURE_FIELD_LEVEL)
        if heroLevel <= 3 then
            _type =  NotarizeWindowsType.Windows3
        end
        ---@type NotarizeWindowsDatq
        local data = {
            type = _type,
            content = GetGameText(luaID, 2),
            okCallback = m.OnConfirmBattleProgress,
            cancelCallback = m.OnCancelBattleProgress,
        }
        HelperL.NotarizeUI(data)
    end
end

-- 取消进入战斗
function m.OnCancelBattleProgress()
    -- 先清进度
    LuaToCshapeManager.Instance:ClearProgress()
    LogicValue.SetBattleProgress(BattleProgress_Index.RoundNo, 0)
end

-- 确认进入战斗
function m.OnConfirmBattleProgress()
    m.mainType = GamePlayerData.GameEctype.GetBattleProgress(BattleProgress_Index.StageType)
    m.stageId = GamePlayerData.GameEctype.GetBattleProgress(BattleProgress_Index.StageLvl)
    --关卡配置
    m.catMainStage = Schemes.CatMainStage:Get(m.stageId)

    ---请求进入战斗场景
    m.RequestEnterBattleScene()
end

---请求进入战斗场景
function m.RequestEnterBattleScene()
    print("设置即将开始的关卡", m.mainType, m.stageId)
    -- 设置即将开始的关卡
    LuaToCshapeManager.Instance:SetStage(m.mainType, m.stageId)

    -- 调用真正的EnterBattle函数传递数据到战斗系统
    m:EnterBattle(m.mainType, m.stageId)
end

---加载游戏场景事件
function m.OnGameSceneLoaded(sceneName)
    if sceneName == "GameScene" then
        --播放战斗音乐
        SoundManager:PlayMusic(m.catMainStage.MaxGold)

        ------------关闭界面-------------
        UIManager:CloseAllUI()

        ------------友盟统计-------------
        local actorData = LoginModule:GetSelectActorData()
        --挑战次数
        HelperL.SendUMengEvent(
            UMENG_EVENT_TYPE.BATTLE_COUNT,
            tostring(actorData.ActorName),
            tostring(actorData.ActorID),
            tostring(m.stageId)
        )
    end
end

---从战斗场景返回主场
---@param isSuccess boolean 是否成功
function m.BattleEnd(isSuccess)
    --上报友盟
    local maxStage = m.stageId
    local actorData = LoginModule:GetSelectActorData()
    local actorName = actorData.ActorName
    local actorID = actorData.ActorID
    --最高等级
    HelperL.SendUMengEvent(UMENG_EVENT_TYPE.CUR_STAGE, tostring(actorName), tostring(actorID), tostring(maxStage))
    if isSuccess then
        HelperL.SendUMengEvent(UMENG_EVENT_TYPE.BATTLE_RESULT_SUCCESS, tostring(actorName), tostring(actorID),
            tostring(maxStage)) --关卡挑战结果
    else
        HelperL.SendUMengEvent(UMENG_EVENT_TYPE.BATTLE_RESULT_FAILD, tostring(actorName), tostring(actorID),
            tostring(maxStage)) --关卡挑战结果
    end

    -- 这段代码可以提出来
    UIManager:CloseWndByID(WndID.UpgradeSkillView)
    EntityModule.luaToCshape.IsFighting = false

    SceneManager:LoadSceneWithLoading('GameLogin', true, false)
    UIManager.loginType = 2
    UIManager:OpenWnd(WndID.MainTitle)
    EventManager:Fire(EventID.Battle_End)
    EventManager:Fire(EventID.FreshenWorld)
end

--------------------------------------------------------------------
-- 装备出战关联系统 - 进入战斗时检查
--------------------------------------------------------------------
function m.CheckAndEnsureEquipAssociationForBattle()
    print("2222[BattleEquipAssociation] 开始进入战斗装备关联检查")

    -- 获取当前出战装备列表
    local currentEquipIDs = GamePlayerData.ActorEquip:GetWeaponsKnapsack(1) or {}
    print("2222[BattleEquipAssociation] 当前出战装备列表:", table.concat(currentEquipIDs, ","))

    -- 统计实际出战装备数量（排除0值）
    local actualEquipCount = 0
    local mainEquipID = 0
    for i, equipID in ipairs(currentEquipIDs) do
        if equipID and equipID > 0 then
            actualEquipCount = actualEquipCount + 1
            if i == 1 then
                mainEquipID = equipID
            end
        end
    end

    print("2222[BattleEquipAssociation] 实际出战装备数量:", actualEquipCount, "主装备ID:", mainEquipID)

    -- 只有1件装备时进行关联检查
    if actualEquipCount == 1 and mainEquipID > 0 then
        local associatedEquipIDs = m.GetAllAssociatedEquipIDs(mainEquipID)
        print("2222[BattleEquipAssociation] 主装备", mainEquipID, "的关联装备:", table.concat(associatedEquipIDs, ","))

        -- 设置所有关联装备为出战
        for _, associatedEquipID in ipairs(associatedEquipIDs) do
            -- 检查是否已出战
            local isAlreadyWorn = false
            for _, wornEquipID in ipairs(currentEquipIDs) do
                if wornEquipID == associatedEquipID then
                    isAlreadyWorn = true
                    break
                end
            end

            if not isAlreadyWorn then
                -- 使用SkepModule的正确装备穿戴方法
                local equipEntity = m.GetEquipEntityByID(associatedEquipID)
                if equipEntity then
                    SkepModule.PutOnEquipment(equipEntity.uid)
                    print("2222[BattleEquipAssociation] 已通过SkepModule设置关联装备", associatedEquipID, "为出战")
                else
                    -- 备用方案：使用原来的方法
                    GamePlayerData.ActorEquip:ReplaceEquipIndex(associatedEquipID, 2, 1)
                    print("2222[BattleEquipAssociation] 已通过备用方案设置关联装备", associatedEquipID, "为出战")
                end
            else
                print("2222[BattleEquipAssociation] 关联装备", associatedEquipID, "已经出战")
            end
        end

        -- 再次检查出战装备列表确认
        local updatedEquipIDs = GamePlayerData.ActorEquip:GetWeaponsKnapsack(1) or {}
        print("2222[BattleEquipAssociation] 更新后出战装备列表:", table.concat(updatedEquipIDs, ","))
    else
        print("2222[BattleEquipAssociation] 装备数量不为1或无主装备，跳过关联检查")
    end
end

--------------------------------------------------------------------
-- 递归获取装备的所有关联装备ID（支持EffectID2和EffectID3）
--------------------------------------------------------------------
function m.GetAllAssociatedEquipIDs(equipID, visitedEquipIDs, depth)
    visitedEquipIDs = visitedEquipIDs or {}
    depth = depth or 1

    -- 防止无限递归，最大深度10层
    if depth > 10 then
        print("2222[BattleEquipAssociation] 达到最大关联深度10层，停止递归")
        return {}
    end

    -- 防止循环关联
    if visitedEquipIDs[equipID] then
        print("2222[BattleEquipAssociation] 检测到循环关联，装备ID:", equipID)
        return {}
    end

    visitedEquipIDs[equipID] = true

    -- 获取装备配置
    local equipConfig = Schemes.EquipWeapon:Get(equipID)
    if not equipConfig then
        print("2222[BattleEquipAssociation] 装备配置不存在，装备ID:", equipID)
        return {}
    end

    local associatedEquipIDs = {}

    -- 检查EffectID2字段
    if equipConfig.EffectID2 and equipConfig.EffectID2 > 0 then
        local effectID2Config = Schemes.EquipWeapon:Get(equipConfig.EffectID2)
        if effectID2Config and effectID2Config.GroupID >= 100 then
            table.insert(associatedEquipIDs, equipConfig.EffectID2)
            print("2222[BattleEquipAssociation] 找到EffectID2关联装备:", equipConfig.EffectID2)

            -- 递归获取关联装备的关联装备
            local subAssociatedIDs = m.GetAllAssociatedEquipIDs(equipConfig.EffectID2, visitedEquipIDs, depth + 1)
            for _, subID in ipairs(subAssociatedIDs) do
                table.insert(associatedEquipIDs, subID)
            end
        end
    end

    -- 检查EffectID3字段
    if equipConfig.EffectID3 and equipConfig.EffectID3 > 0 then
        local effectID3Config = Schemes.EquipWeapon:Get(equipConfig.EffectID3)
        if effectID3Config and effectID3Config.GroupID >= 100 then
            table.insert(associatedEquipIDs, equipConfig.EffectID3)
            print("2222[BattleEquipAssociation] 找到EffectID3关联装备:", equipConfig.EffectID3)

            -- 递归获取关联装备的关联装备
            local subAssociatedIDs = m.GetAllAssociatedEquipIDs(equipConfig.EffectID3, visitedEquipIDs, depth + 1)
            for _, subID in ipairs(subAssociatedIDs) do
                table.insert(associatedEquipIDs, subID)
            end
        end
    end

    return associatedEquipIDs
end

--------------------------------------------------------------------
-- 根据装备ID获取装备实体
--------------------------------------------------------------------
function m.GetEquipEntityByID(equipID)
    -- 从神器篮子中查找
    local skepAir = SkepModule:GetSkepByID(SKEPID.SKEPID_GODWEAPON)
    if skepAir then
        local airEntity = skepAir:GetEntityByGoodsID(equipID)
        if airEntity then
            return airEntity
        end
    end

    -- 从装备背包中查找
    local equipSkep = SkepModule:GetSkepByID(SKEPID.SKEPID_EQUIPPACKET)
    if equipSkep then
        local equipEntity = equipSkep:GetEntityByGoodsID(equipID)
        if equipEntity then
            return equipEntity
        end
    end

    print("2222[BattleEquipAssociation] 未找到装备实体，装备ID:", equipID)
    return nil
end

--------------------------------------------------------------------
-- 初始化战斗
--------------------------------------------------------------------
function m.InitBattle(levelID)
    -- m.refreshMonsterConfigDic = {}
    m.refreshMonsterDataList = {}
    m.levelConfig = Schemes.CatMainStage:Get(levelID)
    m.refreshMonsterTimeList = m.levelConfig.RefreshTime
    m.battleTotalWaves = #m.refreshMonsterTimeList

    m.enterBattleAllDiamond = m.GetDiamond()
    -- print("钻石有多少啊！！！！" .. tostring(m.enterBattleAllDiamond))
    m.endBattleCoseDiamond = 0
    m.enterBattleAllReviveCoin = m.getGoodsNum(1285)
    m.endBattleCoseReviveCoin = 0
    m.frontType = m.levelConfig.FrontType
    m.showUpgradeSkillIndex = 0
    m.isInitiativeExit = false

    m.playerProDic = {}
    local hero = EntityModule.hero
    if hero then
        for i = 1, PLAYER_FIELD.PLAYER_FIELD_END - 1 do
            m.playerProDic[i] = hero:GetProperty(i)
        end
    end

    m.refreshExpRateDic = {}
    local expStrList = HelperL.Split(m.levelConfig.FightSkill, "|")
    for i = 1, #expStrList do
        local tempList = HelperL.Split(expStrList[i], ";")
        m.refreshExpRateDic[i] = {}
        m.refreshExpRateDic[i].TimeID = tonumber(tempList[1])
        m.refreshExpRateDic[i].XUp = tonumber(tempList[2])
        m.refreshExpRateDic[i].YUp = tonumber(tempList[3])
    end

    m.randomInfoList = {}
    m.catsKillLevelUp = Schemes.CatsKillLevelUp.items
    -- 等于0 的是没有三选一的
    m.levelAllSkillIdsList = {}
    -- m.levelAllBuffIdsList = {}
    m.uplimitBuffID = nil
    if m.levelConfig.ChapSkillID ~= 0 then
        m.csvRow_CatsKillSelect = Schemes.CatsKillSelect:Get(m.levelConfig.ChapSkillID)
        m.levelAllSkillIdsList = m:MergeTables({}, m.csvRow_CatsKillSelect.LearnSkill)
        -- m.levelAllBuffIdsList = m:MergeTables({}, m.csvRow_CatsKillSelect.LearnBuff)
        m.uplimitBuffID = m.csvRow_CatsKillSelect.Uplimit
    end

    BattleSkillModule:InitSkill()
    BattleBuffModule:InitBuff()
end

--------------------------------------------------------------------
-- 获取宝石列表
--------------------------------------------------------------------
function m.GetHoleGemList()
    local currentID = HelperL.GetSidekickID()
    local weaponItem = Schemes.Equipment:Get(currentID)
    local equipGemIdList = {}
    local result = ""
    if weaponItem then
        local part = HelperL.GetEquipGemPart(weaponItem.SubType, 1)
        local gemData = EntityModule.hero.equipGemPartDataLC:Get(part)
        for i = 1, 3 do
            --孔已开启
            if gemData.HoleNum >= i then
                if gemData.GemID[i] == 0 then
                    --未镶嵌宝石
                else
                    --镶嵌了宝石
                    table.insert(equipGemIdList, gemData.GemID[i])
                    if #equipGemIdList == 1 then
                        result = gemData.GemID[i]
                    else
                        result = result .. '|' .. gemData.GemID[i]
                    end
                end
            else
                --孔未开启
            end
        end
    end
    return result
end

--------------------------------------------------------------------
-- 获取钻石数量
--------------------------------------------------------------------
function m.GetDiamond()
    return EntityModule.hero:GetProperty(PLAYER_FIELD.PLAYER_FIELD_DIAMOND)
end

--------------------------------------------------------------------
-- 获取物品数量
--------------------------------------------------------------------
function m.getGoodsNum(goodsID)
    local goodsSkep = SkepModule:GetSkepByID(SKEPID.SKEPID_PACKET)
    if goodsSkep then
        return goodsSkep:GetGoodsNum(goodsID)
    end
    return 0
end

--------------------------------------------------------------------
-- 合并表格
--------------------------------------------------------------------
function m:MergeTables(target, source)
    for i, v in ipairs(source) do
        table.insert(target, v)
    end
    return target
end
