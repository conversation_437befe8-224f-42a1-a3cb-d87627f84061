--[[
********************************************************************
    created:    2024/06/16
    author :
    purpose:    战斗管理
*********************************************************************
--]]

local luaID = ('BattleManager')

local m = {}
--战斗管理
BattleManager = m

--- 进入战斗
function m:EnterBattle(stageId)
    --测试--快速通关，取消注释即可
    if SWITCH and SWITCH.FASTER_CUSTOMS_CLEARANCE then
        local form = {}
        form["stageID"] = stageId
        form["isSuccess"] = true
        form["roundNo"] = 100
        LuaModuleNew.SendRequest(LuaRequestID.SaveEctypeProgress, form, function(resultCode, content, callbackParam)
            if resultCode == RESULT_CODE_BASE.RESULT_COMMON_SUCCEED[1] then
                print("保存副本数据成功")
                -- 清除战斗进度
                if LuaToCshapeManager.Instance then
                    LuaToCshapeManager.Instance:ClearProgress()
                end
            else
                ResultCode.ShowResultCodeCallback(resultCode, content)
            end
        end)
        local actorID = EntityModule.hero:GetProperty(ENTITY_FIELD.ENTITY_FIELD_ID)
        PlayerPrefsManager:SetInt("CurGuideType"..actorID, 14)
        
        local catMainStage = Schemes.CatMainStage:Get(stageId)
        local str_req2 = string.format('LuaRequestSetLogicData?logicID=%d&logicValue=%d', EctypeSaveLogic[catMainStage.FrontType],stageId)
        LuaModule.RunLuaRequest(str_req2, function(resultCode2, content2)
            print("resultCode2 = ", resultCode2)
            if resultCode2 == RESULT_CODE_BASE.RESULT_COMMON_SUCCEED[1] then
            end
        end)

        return
    end

    m.stageId = stageId
    m.isInitiativeExit = false
    --关卡配置
    local cfg = Schemes.CatMainStage:Get(stageId)
    m.catMainStage = cfg
    m.mainType = cfg.FrontType

    --测试--直接进入战斗，取消注释即可
    -- if true then
    --     m.RequestEnterBattleScene()
    --     return
    -- end

    -- 进入战斗前检查并设置关联装备
    m.CheckAndSetAssociatedEquipmentsForBattle("进入战斗前检查")

    --已出战圣物数量
    local num = GamePlayerData.ActorEquip:GetWearEquipAmount(1)
    print('----携带装备数量------stageId=', stageId)
    print('----携带装备数量------num=', num)
    print('----携带装备数量------MaxSkillCount=', cfg.MaxSkillCount)
    --判断是否携带了指定数量的装备
    if num < cfg.MaxSkillCount then
        --提示 "携带装备数量不足"
        HelperL.ShowMessage(TipType.FlowText, GetGameText(luaID, 1))
        return
    end

    local expendList = HelperL.Split(cfg.Need, ';')
    --消耗物品ID
    local expID = tonumber(expendList[1]) or 0
    --消耗物品数量
    local expNum = tonumber(expendList[2]) or 0
    --广告物品ID
    -- local adID = tonumber(expendList[3]) or 0
    --消耗物品ID2
    local expID2 = tonumber(expendList[4]) or 0
    --消耗物品数量2
    local expNum2 = tonumber(expendList[5]) or 0

    --消耗判断
    if HelperL.IsLackGoods(expID, expNum, false) then
        return
    end
    if HelperL.IsLackGoods(expID2, expNum2, false) then
        return
    end

    local goodInfo = "0"
    local costInfo = string.format("%s;%s|%s;%s", expID, expNum, expID2, expNum2)
    --请求扣除消耗物品
    HelperL.RequestDirectGiveGoodsy(goodInfo, costInfo, function(resultCode, content)
        if resultCode == RESULT_CODE_BASE.RESULT_COMMON_SUCCEED[1] then
            -- 先清进度
            LuaToCshapeManager.Instance:ClearProgress()
            LogicValue.SetBattleProgress(BattleProgress_Index.RoundNo, 0)

            ---请求进入战斗场景
            m.RequestEnterBattleScene()
        else
            ResultCode.ShowResultCodeCallback(resultCode, content)
        end
    end)
end

--检查战斗进度
function m.CheckBattleProgress()
    if (GamePlayerData.GameEctype.GetBattleProgress(BattleProgress_Index.RoundNo) > 0) then
        local _type =  NotarizeWindowsType.Windows5
        local heroLevel = EntityModule.hero:GetProperty(CREATURE_FIELD.CREATURE_FIELD_LEVEL)
        if heroLevel <= 3 then
            _type =  NotarizeWindowsType.Windows3
        end
        ---@type NotarizeWindowsDatq
        local data = {
            type = _type,
            content = GetGameText(luaID, 2),
            okCallback = m.OnConfirmBattleProgress,
            cancelCallback = m.OnCancelBattleProgress,
        }
        HelperL.NotarizeUI(data)
    end
end

-- 取消进入战斗
function m.OnCancelBattleProgress()
    -- 先清进度
    LuaToCshapeManager.Instance:ClearProgress()
    LogicValue.SetBattleProgress(BattleProgress_Index.RoundNo, 0)
end

-- 确认进入战斗
function m.OnConfirmBattleProgress()
    m.mainType = GamePlayerData.GameEctype.GetBattleProgress(BattleProgress_Index.StageType)
    m.stageId = GamePlayerData.GameEctype.GetBattleProgress(BattleProgress_Index.StageLvl)
    --关卡配置
    m.catMainStage = Schemes.CatMainStage:Get(m.stageId)

    -- 恢复战斗进度前检查并设置关联装备
    m.CheckAndSetAssociatedEquipmentsForBattle("恢复战斗进度前检查")

    ---请求进入战斗场景
    m.RequestEnterBattleScene()
end

---请求进入战斗场景
function m.RequestEnterBattleScene()
    -- 进入战斗场景前最终检查装备状态
    m.CheckAndSetAssociatedEquipmentsForBattle("进入战斗场景前最终检查")

    print("设置即将开始的关卡", m.mainType, m.stageId)
    -- 设置即将开始的关卡
    LuaToCshapeManager.Instance:SetStage(m.mainType, m.stageId)

    ------------进入战斗-------------
    EntityModule.luaToCshape.IsFighting = true
    --注册--加载游戏场景事件
    EventManager:UnSubscribe(EventID.OnSceneLoaded, m.OnGameSceneLoaded)
    EventManager:Subscribe(EventID.OnSceneLoaded, m.OnGameSceneLoaded)
    --切换--战斗场景
    SceneManager:LoadSceneWithLoading('GameScene', true, false)
end

---加载游戏场景事件
function m.OnGameSceneLoaded(sceneName)
    if sceneName == "GameScene" then
        --播放战斗音乐
        SoundManager:PlayMusic(m.catMainStage.MaxGold)

        ------------关闭界面-------------
        UIManager:CloseAllUI()

        ------------友盟统计-------------
        local actorData = LoginModule:GetSelectActorData()
        --挑战次数
        HelperL.SendUMengEvent(
            UMENG_EVENT_TYPE.BATTLE_COUNT,
            tostring(actorData.ActorName),
            tostring(actorData.ActorID),
            tostring(m.stageId)
        )
    end
end

---从战斗场景返回主场
---@param isSuccess boolean 是否成功
function m.BattleEnd(isSuccess)
    --上报友盟
    local maxStage = m.stageId
    local actorData = LoginModule:GetSelectActorData()
    local actorName = actorData.ActorName
    local actorID = actorData.ActorID
    --最高等级
    HelperL.SendUMengEvent(UMENG_EVENT_TYPE.CUR_STAGE, tostring(actorName), tostring(actorID), tostring(maxStage))
    if isSuccess then
        HelperL.SendUMengEvent(UMENG_EVENT_TYPE.BATTLE_RESULT_SUCCESS, tostring(actorName), tostring(actorID),
            tostring(maxStage)) --关卡挑战结果
    else
        HelperL.SendUMengEvent(UMENG_EVENT_TYPE.BATTLE_RESULT_FAILD, tostring(actorName), tostring(actorID),
            tostring(maxStage)) --关卡挑战结果
    end

    -- 这段代码可以提出来
    UIManager:CloseWndByID(WndID.UpgradeSkillView)
    EntityModule.luaToCshape.IsFighting = false

    SceneManager:LoadSceneWithLoading('GameLogin', true, false)
    UIManager.loginType = 2
    UIManager:OpenWnd(WndID.MainTitle)
    EventManager:Fire(EventID.Battle_End)
    EventManager:Fire(EventID.FreshenWorld)
end

--------------------------------------------------------------------
-- 获取装备的所有关联装备ID（支持EffectID2和EffectID3多层关联）
--------------------------------------------------------------------
function m.GetAllAssociatedEquipIDs(equipId)
    local associatedEquipIDs = {}
    local currentEquipId = equipId
    local visitedEquipIDs = {} -- 防止循环引用
    local maxDepth = 10 -- 最大关联深度
    local currentDepth = 0

    print("2222222 战斗关联装备检查------开始获取装备", equipId, "的所有关联装备")

    while currentDepth < maxDepth do
        -- 循环引用检测
        if visitedEquipIDs[currentEquipId] then
            print("2222222 战斗关联装备检查------检测到循环引用，停止查找关联装备，当前ID=", currentEquipId)
            break
        end
        visitedEquipIDs[currentEquipId] = true

        -- 获取装备配置
        local equipWeaponConfig = Schemes.EquipWeapon:Get(currentEquipId)
        if not equipWeaponConfig then
            print("2222222 战斗关联装备检查------未找到EquipWeapon配置，装备ID=", currentEquipId)
            break
        end

        local nextEquipId = nil
        local hasAssociated = false

        -- 优先检查EffectID2
        if equipWeaponConfig.EffectID2 and equipWeaponConfig.EffectID2 ~= 0 then
            local associatedId = equipWeaponConfig.EffectID2
            table.insert(associatedEquipIDs, associatedId)
            print("2222222 战斗关联装备检查------找到EffectID2关联装备", associatedId, "(关联于装备", currentEquipId, ")")
            if not nextEquipId then
                nextEquipId = associatedId
            end
            hasAssociated = true
        end

        -- 检查EffectID3
        if equipWeaponConfig.EffectID3 and equipWeaponConfig.EffectID3 ~= 0 then
            local associatedId = equipWeaponConfig.EffectID3
            table.insert(associatedEquipIDs, associatedId)
            print("2222222 战斗关联装备检查------找到EffectID3关联装备", associatedId, "(关联于装备", currentEquipId, ")")
            if not nextEquipId then
                nextEquipId = associatedId
            end
            hasAssociated = true
        end

        if hasAssociated and nextEquipId then
            currentEquipId = nextEquipId
            currentDepth = currentDepth + 1
            print("2222222 战斗关联装备检查------继续查找装备", nextEquipId, "的关联装备，当前深度=", currentDepth)
        else
            print("2222222 战斗关联装备检查------装备", currentEquipId, "无关联装备，停止查找")
            break
        end
    end

    if currentDepth >= maxDepth then
        print("2222222 战斗关联装备检查------达到最大关联深度", maxDepth, "，停止查找")
    end

    print("2222222 战斗关联装备检查------装备", equipId, "共找到", #associatedEquipIDs, "个关联装备:", table.concat(associatedEquipIDs, ","))
    return associatedEquipIDs
end

--------------------------------------------------------------------
-- 战斗前检查并设置关联装备出战
--------------------------------------------------------------------
function m.CheckAndSetAssociatedEquipmentsForBattle(triggerType)
    print("2222222", triggerType, "------开始检查装备出战状态")

    -- 获取当前出战装备列表
    local currentEquipIDs = GamePlayerData.ActorEquip:GetWeaponsKnapsack(1) or {}
    local actualEquipCount = 0
    local mainEquipID = 0

    -- 统计实际出战装备数量（排除0值）
    for i, equipID in ipairs(currentEquipIDs) do
        if equipID and equipID > 0 then
            actualEquipCount = actualEquipCount + 1
            if i == 1 then
                mainEquipID = equipID
            end
        end
    end

    print("2222222", triggerType, "------当前出战装备列表:", table.concat(currentEquipIDs, ","))
    print("2222222", triggerType, "------实际出战装备数量:", actualEquipCount)
    print("2222222", triggerType, "------主装备ID:", mainEquipID)

    if mainEquipID == 0 then
        print("2222222", triggerType, "------无主装备，跳过关联检查")
        return
    end

    -- 获取主装备的所有关联装备
    local associatedEquipIDs = m.GetAllAssociatedEquipIDs(mainEquipID)

    if #associatedEquipIDs == 0 then
        print("2222222", triggerType, "------主装备", mainEquipID, "无关联装备，无需处理")
        return
    end

    -- 构建目标装备列表（主装备 + 所有关联装备）
    local targetEquipList = {mainEquipID}
    for _, associatedID in ipairs(associatedEquipIDs) do
        table.insert(targetEquipList, associatedID)
    end

    print("2222222", triggerType, "------目标装备列表:", table.concat(targetEquipList, ","))

    -- 检查当前装备列表是否与目标一致
    local needUpdate = false
    if #currentEquipIDs ~= #targetEquipList then
        needUpdate = true
        print("2222222", triggerType, "------装备数量不一致，需要更新")
    else
        -- 检查装备内容是否一致
        for i, targetEquipID in ipairs(targetEquipList) do
            if currentEquipIDs[i] ~= targetEquipID then
                needUpdate = true
                print("2222222", triggerType, "------装备内容不一致，位置", i, "当前:", currentEquipIDs[i], "目标:", targetEquipID)
                break
            end
        end
    end

    if needUpdate then
        print("2222222", triggerType, "------开始更新装备出战状态")
        LogicValue.SetWeaponsKnapsack(targetEquipList)

        -- 确保gunID数据传递给战斗系统
        m.EnsureGunDataForBattle(targetEquipList, triggerType)

        -- 延迟0.3秒验证结果
        Timer.New(function()
            local updatedEquipIDs = GamePlayerData.ActorEquip:GetWeaponsKnapsack(1) or {}
            print("2222222", triggerType, "------更新后装备列表:", table.concat(updatedEquipIDs, ","))

            if #updatedEquipIDs == #targetEquipList then
                local allMatch = true
                for i, targetEquipID in ipairs(targetEquipList) do
                    if updatedEquipIDs[i] ~= targetEquipID then
                        allMatch = false
                        break
                    end
                end

                if allMatch then
                    print("2222222", triggerType, "------装备关联设置成功！")
                else
                    print("2222222", triggerType, "------装备关联设置可能存在问题，请检查")
                end
            else
                print("2222222", triggerType, "------装备关联设置可能存在问题，数量不匹配")
            end
        end, 0.3, 1):Start()
    else
        print("2222222", triggerType, "------装备状态已正确，无需更新")
        -- 即使装备列表正确，也要确保gunID数据传递
        m.EnsureGunDataForBattle(targetEquipList, triggerType)
    end
end

--------------------------------------------------------------------
-- 确保gunID数据传递给战斗系统
--------------------------------------------------------------------
function m.EnsureGunDataForBattle(equipList, triggerType)
    print("2222222", triggerType, "------开始确保gunID数据传递给战斗系统")

    local gunDataList = {}
    for _, equipID in ipairs(equipList) do
        -- 获取装备配置
        local equipConfig = Schemes.Equipment:Get(equipID)
        if equipConfig and equipConfig.ConsignmentStyle and equipConfig.ConsignmentStyle > 0 then
            local gunID = equipConfig.ConsignmentStyle
            print("2222222", triggerType, "------装备ID", equipID, "对应gunID:", gunID)

            -- 获取gun配置验证
            local gunConfig = Schemes.Gun:Get(gunID)
            if gunConfig then
                table.insert(gunDataList, {
                    equipID = equipID,
                    gunID = gunID,
                    goodsID = gunConfig.GoodsID
                })
                print("2222222", triggerType, "------gunID", gunID, "配置验证成功，goodsID:", (gunConfig.GoodsID or 0))
            else
                print("2222222", triggerType, "------警告：gunID", gunID, "配置不存在")
            end
        else
            print("2222222", triggerType, "------警告：装备ID", equipID, "没有有效的ConsignmentStyle")
        end
    end

    print("2222222", triggerType, "------gunID数据传递完成，共处理", #gunDataList, "个装备")

    -- 这里可以添加额外的gunID数据传递逻辑，确保战斗系统能正确接收到所有关联装备的gunID
    -- 例如：通知战斗系统更新gun列表，或者设置特定的战斗参数
    if #gunDataList > 0 then
        print("2222222", triggerType, "------所有装备对应的gunID已准备就绪，战斗系统可以正常使用这些装备的技能")
    end
end
