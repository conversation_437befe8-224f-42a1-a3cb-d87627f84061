--[[
********************************************************************
    created:    2024/06/16
    author :
    purpose:    战斗管理
*********************************************************************
--]]

local luaID = ('BattleManager')

local m = {}
--战斗管理
BattleManager = m

-- 获取装备的所有关联装备ID（支持EffectID2和EffectID3双字段多层关联）
function GetAllAssociatedEquipIDs(equipId)
    local associatedEquipIDs = {}
    local currentEquipId = equipId
    local visitedEquipIDs = {} -- 防止循环引用

    while true do
        -- 循环引用检测
        if visitedEquipIDs[currentEquipId] then
            print("2222 检测到循环引用，停止查找关联装备，当前ID=" .. currentEquipId)
            break
        end
        visitedEquipIDs[currentEquipId] = true

        -- 获取装备配置
        local equipWeaponConfig = Schemes.EquipWeapon:Get(currentEquipId)
        if not equipWeaponConfig then
            print("2222 未找到EquipWeapon配置，装备ID=" .. currentEquipId)
            break
        end

        local nextEquipId = nil
        local hasAssociated = false

        -- 优先检查EffectID2
        if equipWeaponConfig.EffectID2 and equipWeaponConfig.EffectID2 ~= 0 then
            local associatedId = equipWeaponConfig.EffectID2
            table.insert(associatedEquipIDs, associatedId)
            print("2222 找到EffectID2关联装备 " .. associatedId .. " (关联于装备 " .. currentEquipId .. ")")
            if not nextEquipId then
                nextEquipId = associatedId
            end
            hasAssociated = true
        end

        -- 检查EffectID3
        if equipWeaponConfig.EffectID3 and equipWeaponConfig.EffectID3 ~= 0 then
            local associatedId = equipWeaponConfig.EffectID3
            table.insert(associatedEquipIDs, associatedId)
            print("2222 找到EffectID3关联装备 " .. associatedId .. " (关联于装备 " .. currentEquipId .. ")")
            if not nextEquipId then
                nextEquipId = associatedId
            end
            hasAssociated = true
        end

        if hasAssociated and nextEquipId then
            currentEquipId = nextEquipId -- 继续查找下一层关联
        else
            print("2222 装备 " .. currentEquipId .. " 无关联装备或EffectID2/EffectID3为0，停止查找")
            break
        end
    end

    return associatedEquipIDs
end

--- 进入战斗
function m:EnterBattle(stageId)
    --测试--快速通关，取消注释即可
    if SWITCH and SWITCH.FASTER_CUSTOMS_CLEARANCE then
        local form = {}
        form["stageID"] = stageId
        form["isSuccess"] = true
        form["roundNo"] = 100
        LuaModuleNew.SendRequest(LuaRequestID.SaveEctypeProgress, form, function(resultCode, content, callbackParam)
            if resultCode == RESULT_CODE_BASE.RESULT_COMMON_SUCCEED[1] then
                print("保存副本数据成功")
                -- 清除战斗进度
                if LuaToCshapeManager.Instance then
                    LuaToCshapeManager.Instance:ClearProgress()
                end
            else
                ResultCode.ShowResultCodeCallback(resultCode, content)
            end
        end)
        local actorID = EntityModule.hero:GetProperty(ENTITY_FIELD.ENTITY_FIELD_ID)
        PlayerPrefsManager:SetInt("CurGuideType"..actorID, 14)
        
        local catMainStage = Schemes.CatMainStage:Get(stageId)
        local str_req2 = string.format('LuaRequestSetLogicData?logicID=%d&logicValue=%d', EctypeSaveLogic[catMainStage.FrontType],stageId)
        LuaModule.RunLuaRequest(str_req2, function(resultCode2, content2)
            print("resultCode2 = ", resultCode2)
            if resultCode2 == RESULT_CODE_BASE.RESULT_COMMON_SUCCEED[1] then
            end
        end)

        return
    end

    m.stageId = stageId
    m.isInitiativeExit = false
    --关卡配置
    local cfg = Schemes.CatMainStage:Get(stageId)
    m.catMainStage = cfg
    m.mainType = cfg.FrontType

    --测试--直接进入战斗，取消注释即可
    -- if true then
    --     m.RequestEnterBattleScene()
    --     return
    -- end

    --已出战圣物数量
    local num = GamePlayerData.ActorEquip:GetWearEquipAmount(1)
    print('2222 战斗前检查------stageId=', stageId)
    print('2222 战斗前检查------num=', num)
    print('2222 战斗前检查------MaxSkillCount=', cfg.MaxSkillCount)

    -- 装备出战关联检查
    local currentEquipIDs = GamePlayerData.ActorEquip:GetWeaponsKnapsack(1) or {}
    local actualEquipCount = 0
    local currentEquipList = {}

    for _, equipID in ipairs(currentEquipIDs) do
        if equipID and equipID ~= 0 then
            actualEquipCount = actualEquipCount + 1
            table.insert(currentEquipList, equipID)
        end
    end

    print('2222 战斗前检查------实际出战装备数量=', actualEquipCount)
    print('2222 战斗前检查------出战装备列表=', json.encode(currentEquipList))

    -- 只有1件装备时进行关联检查
    if actualEquipCount == 1 then
        local mainEquipID = currentEquipList[1]
        print('2222 战斗前检查------检查主装备关联装备，主装备ID=', mainEquipID)

        local associatedEquipIDs = GetAllAssociatedEquipIDs(mainEquipID)
        print('2222 战斗前检查------找到关联装备数量=', #associatedEquipIDs)

        -- 设置所有关联装备为出战
        for _, associatedEquipID in ipairs(associatedEquipIDs) do
            -- 检查是否已出战
            local isAlreadyWorn = false
            for _, wornEquipID in ipairs(currentEquipIDs) do
                if wornEquipID == associatedEquipID then
                    isAlreadyWorn = true
                    break
                end
            end

            print('2222 战斗前检查------关联装备', associatedEquipID, '是否已出战=', isAlreadyWorn)

            if not isAlreadyWorn then
                GamePlayerData.ActorEquip:ReplaceEquipIndex(associatedEquipID, 2, 1)
                print('2222 战斗前检查------已设置关联装备', associatedEquipID, '为出战')
            else
                print('2222 战斗前检查------关联装备已经出战，无需重复设置')
            end
        end

        -- 重新获取装备数量
        num = GamePlayerData.ActorEquip:GetWearEquipAmount(1)
        print('2222 战斗前检查------关联装备设置后装备数量=', num)
    end

    --判断是否携带了指定数量的装备
    if num < cfg.MaxSkillCount then
        --提示 "携带装备数量不足"
        HelperL.ShowMessage(TipType.FlowText, GetGameText(luaID, 1))
        return
    end

    local expendList = HelperL.Split(cfg.Need, ';')
    --消耗物品ID
    local expID = tonumber(expendList[1]) or 0
    --消耗物品数量
    local expNum = tonumber(expendList[2]) or 0
    --广告物品ID
    -- local adID = tonumber(expendList[3]) or 0
    --消耗物品ID2
    local expID2 = tonumber(expendList[4]) or 0
    --消耗物品数量2
    local expNum2 = tonumber(expendList[5]) or 0

    --消耗判断
    if HelperL.IsLackGoods(expID, expNum, false) then
        return
    end
    if HelperL.IsLackGoods(expID2, expNum2, false) then
        return
    end

    local goodInfo = "0"
    local costInfo = string.format("%s;%s|%s;%s", expID, expNum, expID2, expNum2)
    --请求扣除消耗物品
    HelperL.RequestDirectGiveGoodsy(goodInfo, costInfo, function(resultCode, content)
        if resultCode == RESULT_CODE_BASE.RESULT_COMMON_SUCCEED[1] then
            -- 先清进度
            LuaToCshapeManager.Instance:ClearProgress()
            LogicValue.SetBattleProgress(BattleProgress_Index.RoundNo, 0)

            ---请求进入战斗场景
            m.RequestEnterBattleScene()
        else
            ResultCode.ShowResultCodeCallback(resultCode, content)
        end
    end)
end

--检查战斗进度
function m.CheckBattleProgress()
    if (GamePlayerData.GameEctype.GetBattleProgress(BattleProgress_Index.RoundNo) > 0) then
        local _type =  NotarizeWindowsType.Windows5
        local heroLevel = EntityModule.hero:GetProperty(CREATURE_FIELD.CREATURE_FIELD_LEVEL)
        if heroLevel <= 3 then
            _type =  NotarizeWindowsType.Windows3
        end
        ---@type NotarizeWindowsDatq
        local data = {
            type = _type,
            content = GetGameText(luaID, 2),
            okCallback = m.OnConfirmBattleProgress,
            cancelCallback = m.OnCancelBattleProgress,
        }
        HelperL.NotarizeUI(data)
    end
end

-- 取消进入战斗
function m.OnCancelBattleProgress()
    -- 先清进度
    LuaToCshapeManager.Instance:ClearProgress()
    LogicValue.SetBattleProgress(BattleProgress_Index.RoundNo, 0)
end

-- 确认进入战斗
function m.OnConfirmBattleProgress()
    m.mainType = GamePlayerData.GameEctype.GetBattleProgress(BattleProgress_Index.StageType)
    m.stageId = GamePlayerData.GameEctype.GetBattleProgress(BattleProgress_Index.StageLvl)
    --关卡配置
    m.catMainStage = Schemes.CatMainStage:Get(m.stageId)

    print('2222 恢复战斗进度------开始装备关联检查')

    -- 装备出战关联检查
    local currentEquipIDs = GamePlayerData.ActorEquip:GetWeaponsKnapsack(1) or {}
    local actualEquipCount = 0
    local currentEquipList = {}

    for _, equipID in ipairs(currentEquipIDs) do
        if equipID and equipID ~= 0 then
            actualEquipCount = actualEquipCount + 1
            table.insert(currentEquipList, equipID)
        end
    end

    print('2222 恢复战斗进度------实际出战装备数量=', actualEquipCount)
    print('2222 恢复战斗进度------出战装备列表=', json.encode(currentEquipList))

    -- 只有1件装备时进行关联检查
    if actualEquipCount == 1 then
        local mainEquipID = currentEquipList[1]
        print('2222 恢复战斗进度------检查主装备关联装备，主装备ID=', mainEquipID)

        local associatedEquipIDs = GetAllAssociatedEquipIDs(mainEquipID)
        print('2222 恢复战斗进度------找到关联装备数量=', #associatedEquipIDs)

        -- 设置所有关联装备为出战
        for _, associatedEquipID in ipairs(associatedEquipIDs) do
            -- 检查是否已出战
            local isAlreadyWorn = false
            for _, wornEquipID in ipairs(currentEquipIDs) do
                if wornEquipID == associatedEquipID then
                    isAlreadyWorn = true
                    break
                end
            end

            print('2222 恢复战斗进度------关联装备', associatedEquipID, '是否已出战=', isAlreadyWorn)

            if not isAlreadyWorn then
                GamePlayerData.ActorEquip:ReplaceEquipIndex(associatedEquipID, 2, 1)
                print('2222 恢复战斗进度------已设置关联装备', associatedEquipID, '为出战')
            else
                print('2222 恢复战斗进度------关联装备已经出战，无需重复设置')
            end
        end
    end

    ---请求进入战斗场景
    m.RequestEnterBattleScene()
end

---请求进入战斗场景
function m.RequestEnterBattleScene()
    print("2222 进入战斗场景------设置即将开始的关卡", m.mainType, m.stageId)

    -- 最终装备状态确认
    local finalEquipIDs = GamePlayerData.ActorEquip:GetWeaponsKnapsack(1) or {}
    print("2222 进入战斗场景------最终出战装备列表:", json.encode(finalEquipIDs))

    -- 设置即将开始的关卡
    LuaToCshapeManager.Instance:SetStage(m.mainType, m.stageId)

    ------------进入战斗-------------
    EntityModule.luaToCshape.IsFighting = true
    --注册--加载游戏场景事件
    EventManager:UnSubscribe(EventID.OnSceneLoaded, m.OnGameSceneLoaded)
    EventManager:Subscribe(EventID.OnSceneLoaded, m.OnGameSceneLoaded)
    --切换--战斗场景
    SceneManager:LoadSceneWithLoading('GameScene', true, false)
end

---加载游戏场景事件
function m.OnGameSceneLoaded(sceneName)
    if sceneName == "GameScene" then
        --播放战斗音乐
        SoundManager:PlayMusic(m.catMainStage.MaxGold)

        ------------关闭界面-------------
        UIManager:CloseAllUI()

        ------------友盟统计-------------
        local actorData = LoginModule:GetSelectActorData()
        --挑战次数
        HelperL.SendUMengEvent(
            UMENG_EVENT_TYPE.BATTLE_COUNT,
            tostring(actorData.ActorName),
            tostring(actorData.ActorID),
            tostring(m.stageId)
        )
    end
end

---从战斗场景返回主场
---@param isSuccess boolean 是否成功
function m.BattleEnd(isSuccess)
    --上报友盟
    local maxStage = m.stageId
    local actorData = LoginModule:GetSelectActorData()
    local actorName = actorData.ActorName
    local actorID = actorData.ActorID
    --最高等级
    HelperL.SendUMengEvent(UMENG_EVENT_TYPE.CUR_STAGE, tostring(actorName), tostring(actorID), tostring(maxStage))
    if isSuccess then
        HelperL.SendUMengEvent(UMENG_EVENT_TYPE.BATTLE_RESULT_SUCCESS, tostring(actorName), tostring(actorID),
            tostring(maxStage)) --关卡挑战结果
    else
        HelperL.SendUMengEvent(UMENG_EVENT_TYPE.BATTLE_RESULT_FAILD, tostring(actorName), tostring(actorID),
            tostring(maxStage)) --关卡挑战结果
    end

    -- 这段代码可以提出来
    UIManager:CloseWndByID(WndID.UpgradeSkillView)
    EntityModule.luaToCshape.IsFighting = false

    SceneManager:LoadSceneWithLoading('GameLogin', true, false)
    UIManager.loginType = 2
    UIManager:OpenWnd(WndID.MainTitle)
    EventManager:Fire(EventID.Battle_End)
    EventManager:Fire(EventID.FreshenWorld)
end
