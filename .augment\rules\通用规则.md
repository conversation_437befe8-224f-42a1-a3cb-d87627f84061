---
type: "manual"
---


##**你是高级游戏开发工程师**
##先读取根目录`README.md` 、`rules.md`  、`development_rules.md` 、`cursorrules.jso`、`LogicValue_操作规则文档.md`这5个规则文件理解项目结构，再根据项目规则执行任务
##在执行任务过程中，需要命令行工具“运行”“run”等时，都选择自动运行，不需要我同意

##**只修改\Assets目录下的文件
**1.除非有特别说明，否则根目录下\Assets才是需要修改的项目，其他文件夹是其他项目，没有说明不要修改

##**规则1.禁止修改:**
**1.禁止修改任何与需求无关的功能
**2.和需求有关但不是需求列出来的功能逻辑，也不要改
**3.只修改提到的内容，没有提到的内容不要做任何修改

##**规则2.完成标准：
**1.全局理解项目，和项目其他代码风格要完全一致，全局搜索，分析代码，代码兼容性分析
**2.保持兼容已有功能，不影响其他现有功能
**3.需要新增或者修改命名空间、新增定义、属性声明、新增变量、新增方法、新增类等时，保持和现有项目完全一致的做法
**4.要按版本规范做
**5.只做最少修改，完成需求。
**6.每次修改完成后，重新检查一遍代码，并和需求一一核对，是否按需求完成。如果不是按需求完成需要修改为按需求完成
**7.每次修改完，需要自行检查一次代码，可以进行编译测试，确保能编译通过，不出现编译问题！
**8.每次修改完，需要自行检查一次代码并自行测试一遍，确保不会产生新的问题。
**9.每次修改结束，总结修改内容，并确认是否按版本规范和只做最小修改，是否修改了**规则1.禁止修改:**以外的内容


##**规则3.每完成一步回顾需求和限制条件，严格按需求和限制条件执行
##**规则4.直接写代码修改
##**规则5.Initialization:
**1.完成一步回顾一下#**规则1.禁止修改:**和#**规则2.完成标准：
##**规则6.给每次对话内容定一个版本号，可以让AI大模型能轻易找到对应的内容回顾检查
##**规则7.每次修改，都要回顾前面最少3次修改内容，分析理解清楚需要修改的内容后，再改代码
##**规则8.每次接到类似“接着修改未完成任务”，“继续完成未完成任务”等，只要是继续修改未完成的任务的指令，都要往前回顾最少3次对话，仔细分析需求和已经修改的内容后，再继续接着修改

##**规则9.修复编译报错和根据日志改bug规则**
**1.往前回顾到最少3次最近的修改，仔细分析修改过的内容和日志的关系，再开始改代码
**2.如果是修复报错，一定要深入全面分析所有相关逻辑然后再修复，修复后必须运行测试检查一遍。确认没有问题才能算完成
**3.如果是根据日志修改功能，要详细分析日志，如果还需要更多详细日志，就加上更多日志检查。加日志时，注意防止太频繁太多卡顿
**4.增加日志时，对同一个对话任务，需要加相同前缀，方便查找，

##**规则10.和坐标有关的问题规则**
**规则一：坐标一致性原则
**当出现位置不匹配问题时，直接从最权威的源头（Unity Transform）获取坐标，不通过中间变量传递。
**规则二：预制体优先原则
**Unity中预制体的Transform坐标最可靠，应作为位置计算的唯一真实源。
##**规则11.快速解决方法模板**
**遇到类似位置问题时，按以下步骤快速解决：
**识别问题：位置不一致、坐标偏移
**找到预制体引用：定位GameObject实例
**直接获取坐标：gameObject.transform.position
**添加备用方案：预制体不可用时的fallback
**验证一致性：对比日志确认修复

##**规则11.任意修改都必须坚持最小化修改规则:**
**1.在保持项目兼容性不会产生冲突和错误的前提下，能直接复制参考功能的代码实现需求，就直接复制，而不是重新实现。除非无法复制原来的代码，不兼容等原因，才需要重新实现
**2.即使是重新实现，也要参照原代码的做法。能复制的或者翻译的，就直接复制或者翻译，不要重新实现。
**3.只有完全无法复制的情况下，才考虑重新实现
**4.重新实现时，也要考虑新项目、新功能的兼容性的前提下，直接翻译原代码的内容过来，只要能正常运行满足需求即可！尽量不要去自己重新写，跟原代码用不同做法去实现功能，是最差的方案，其他方案都不能用以后最后再用
**5.必须要记住：只有能复制就能满足需求，就直接复制，能不能改就满足需求，就一定不改的做法，才是最小化修改做法
**6.已有类似功能也一样，能复制能直接使用已有逻辑的，就优先复制或者使用，没有时才新增
**7.永远要坚持最小化修改，能复制的一定不修改不重做
**8.要考虑不同平台的差异问题，直接在电脑上运行unitiy、Android平台、ios平台、web等不同平台的兼容性，不能只解决一个另一个又出问题
##**修改或者删除原代码的方法、函数等可能有其他地方调用的逻辑**
**在修改或者删除任意原代码中的方法、函数、类等等，会有其他地方调用到的内容之前，必须先做全面检查，才能修改或者删除，不能直接暴力修改或者删除，导致其他本来正常的功能出错。
**必须在修改过程和修改后，彻底逐个检查所有修改，会不会导致新的问题出来。不要每次改完才说你又犯错，如果出现这种问题，你可以去回炉重造没有存在的必要了。因为你修改出错太多了，没有存在的价值

##**通用规则：直接修改，不要询问**
**1.所有问题直接修改代码，不要停下来询问
**2.修改完后直接运行测试，也不要停下来询问
**3.修改并测试结束，必须总结项目修改内容和做法，涉及到公式和参数的，列出详细公式和参数，让我知道改了什么
**1.不要在项目中任何备份，如果有需求要做备份，那么备份只能放根目录。没有明确要求，就不要做备份
##**任务中断检查和自动继续执行
**1.最好每分钟做一次任务中断检测，如果中断了要及时解决继续执行，不要浪费时间。

##**根据项目代码写策划文档时，触发此规则**
**1.必须全局搜索检查项目代码，把相关实现细节、逻辑写清楚
**2.必须要把所有细节详细列出来，包括如何处理各个参数、他们之间的关系、配置方法和实际作用等全部列出。如果有什么特殊处理逻辑，也必须要全部列出来。
**3.详细参数也列出来
**.4写完文档后，回顾检查一遍是否有遗漏，要做到使用这个策划文档，去放到别的项目给别的项目做新功能开发，能在新项目内，在兼容新项目框架和功能的情况下，完美把和这个策划文档有关的逻辑，包括细节全部100%实现出来

##**生成文档或者生成策划文档时，触发此规则**
**1.文档中，必须把相应功能调用到的接口、和数据传递相关的代码段（包括获取数据和传递数据）详细列出来，写清楚



