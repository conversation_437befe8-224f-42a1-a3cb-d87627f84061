-- 需要参考魔幻的UIEquipment  SlotItemFactory UIEquipmentSysthesis ItemGainWay

-- 装备UI
require "SlotItemFactory"
local luaID = ('UIEquipment')
local hero = EntityModule.hero
local UIEquipment = {}

-- 初始化
function UIEquipment:OnCreate()
    -- 获取格子的Image组件
	self.headWearSpri = self.objList.Headwear:GetComponent('Image')
	self.clothsSpri = self.objList.Cloths:GetComponent('Image')
	self.shoesSpri = self.objList.Shoes:GetComponent('Image')
	self.trouserSpri = self.objList.Trousers:GetComponent('Image')
	self.ringSpri = self.objList.Ring:GetComponent('Image')
	self.necklaceSpri = self.objList.Necklace:GetComponent('Image')
	self.normalWeaponSpri = self.objList.NormalWeapon:GetComponent('Image')
	self.beltSpri = self.objList.Belt:GetComponent('Image')

    -- 添加格子脚本
	local headWearScript = CreateEquipSlot(self.objList.Headwear)
	local slot1 = headWearScript:GetSkepPlace()
	local clothsScript = CreateEquipSlot(self.objList.Cloths)
	local slot2 = clothsScript:GetSkepPlace()
	local shoesScript = CreateEquipSlot(self.objList.Shoes)
	local slot3 = shoesScript:GetSkepPlace()
	local trouserScript = CreateEquipSlot(self.objList.Trousers)
	local slot4 = trouserScript:GetSkepPlace()
	local ringScript = CreateEquipSlot(self.objList.Ring)
	local slot5 = ringScript:GetSkepPlace()
	local necklaceScript = CreateEquipSlot(self.objList.Necklace)
	local slot6 = necklaceScript:GetSkepPlace()
	local normalWeaponScript = CreateEquipSlot(self.objList.NormalWeapon)
	local slot7 = normalWeaponScript:GetSkepPlace()
	local beltScript = CreateEquipSlot(self.objList.Belt)
	local slot8 = beltScript:GetSkepPlace()


    -- 保存脚本
	self.slotScriptManager = {}
	self.slotScriptManager[slot1] = headWearScript
	self.slotScriptManager[slot2] = clothsScript
	self.slotScriptManager[slot3] = shoesScript
	self.slotScriptManager[slot4] = trouserScript
	self.slotScriptManager[slot5] = ringScript
	self.slotScriptManager[slot6] = necklaceScript
	self.slotScriptManager[slot7] = normalWeaponScript
	self.slotScriptManager[slot8] = beltScript

    self.objList.Btn_Close.onClick:AddListener(self.OnClickClose)
    return true
end

-- 打开窗口
function UIEquipment:OnOpen()
	UIEquipment.UpdateEquipment()
    
end

function UIEquipment.UpdateEquipment(mes)
	if mes then
		UIEquipment.equipType = mes
	end 
	local equipSkep = SkepModule.GetEquipSkep()
	if equipSkep then
		for i = 1, 11 do
			if equipSkep[i] then
				local equipUID = equipSkep[i]
				local e = EntityModule:GetEntity(equipUID)
				-- 读表
				if e then
					local itemID = e:GetProperty(ENTITY_FIELD.ENTITY_FIELD_ID)
					local equipSchemeItem = e.equipScheme
					if equipSchemeItem then
						UIEquipment.slotScriptManager[i]:SetIconName(equipSchemeItem.IconID )
						local quality  = e.propertyLC:GetProperty(EQUIPMENT_FIELD.EQUIPMENT_FIELD_QUALITY)
						UIEquipment.slotScriptManager[i]:BindEntity(e)
						UIEquipment.slotScriptManager[i]:ShowLoopNum(itemID)
						UIEquipment.slotScriptManager[i].bindFlag:SetActive(true)
						UIEquipment.slotScriptManager[i]:SetBest(equipSchemeItem.TheBest)
						UIEquipment.slotScriptManager[i]:SetQualitySprite()
					end
				end
			end
		end
	end
end

-- 加载玩家模型
function UIEquipment.OnLoadPlayerMode()
    local entity = EntityModule:GetEntityList()[hero.divineWeaponDataLC:Get()]
    HelperL.Dump(entity)
    if entity ~= nil then
        local itemID = entity:GetProperty(ENTITY_FIELD.ENTITY_FIELD_ID)
        local equipentData = Schemes.Equipment:Get(itemID)
        local creature = Schemes.Creature:Get(equipentData.RightWeapon)
        local fileName = "model/prefab/player/player_ui" --creature.Prefab   creature 表里面的prefab目前只适合战斗的，后面要修改
        local abName = HotResManager.GetModelABName(fileName)
        print("abName--", abName)
        -- if not HotResManager.IsBundleDownloaded(abName) then
        --     -- 默认模型
        --     local fallbackCreature = Schemes.Creature:Get(self.defaultModelCreatureID)
        --     if fallbackCreature then
        --         fileName = fallbackCreature.Prefab
        --         -- o.isDefaultModel = true
        --     end
        -- end
        local result = HotResManager.ReadModelAsync(UITipManager.objList.Model, fileName, UITipManager.OnModelLoaded, 0, true)
        if not result then
            warn('EntityModule:OnUpdate 加载模型'..fileName..'失败')
            return
        end
    end
end

-- 模型载入完毕
function UIEquipment.OnModelLoaded(obj, uidParam, fileName)
    print("加载成功---") 
	-- local self = UIEquipment
	-- if not self:IsOpen() then
	-- 	HelperL.AddObjToModelPool(obj, fileName)
	-- 	return
	-- end

	-- local config = Schemes.TowerBattleModelCutScene:Get(self.cutSceneID)
	-- if not config then
	-- 	HelperL.AddObjToModelPool(obj, fileName)
	-- 	return
	-- end
	
	-- self:ClearCacheModel()
	
	-- Helper.SetLayer(obj, Layer.UI)
	-- self.curCacheModel = { obj, fileName }
	
	-- if self.aniComp then
	-- 	self.aniComp:UpdateAnimator()
	-- 	self.aniComp:PlayAnimation(config.AniName)
	-- end
end






-- 点击关闭按钮
function UIEquipment.OnClickClose()
	local self = UIEquipment
	self:CloseSelf()
end

return UIEquipment