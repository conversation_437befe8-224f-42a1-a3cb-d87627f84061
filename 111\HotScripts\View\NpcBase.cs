﻿// ReSharper disable InconsistentNaming

using Apq.Unity3D.Extension;

using Thing;

using UniRx;
using UniRx.Triggers;

using UnityEngine;

namespace View
{
    /// <summary>
    /// Npc界面
    /// </summary>
    public class NpcBase : CreatureBase
    {
        /// <summary>
        /// Npc物件(数据)
        /// </summary>
        public NpcThing NpcThing => Thing as NpcThing;

        /// <inheritdoc />
        public override bool IsMoving => false;

        /// <summary>
        /// 碰撞盒组件
        /// </summary>
        public CircleCollider2D CircleCollider2D { get; protected set; }

        public override void Awake()
        {
            // base.Awake();

            CircleCollider2D = gameObject.GetOrAddComponent<CircleCollider2D>();
        }

        public override void Start()
        {
            // base.Start();

            if (CircleCollider2D)
            {
                CircleCollider2D.OnTriggerEnter2DAsObservable().Where(_ => NpcThing != null).Subscribe(e =>
                {
                    var actor = e.GetComponent<PlayerActor>();
                    // 角色碰到了
                    if (actor)
                    {
                        Provider_ActorEnterTrigger?.Invoke();
                    }
                }).AddTo(this);
            }
        }

        /// <summary>
        /// 角色碰到触发器怎么办
        /// </summary>
        public System.Action Provider_ActorEnterTrigger { get; set; }
    }
}