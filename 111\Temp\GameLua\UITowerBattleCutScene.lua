-- 塔防过场UI
local luaID = ('UITowerBattleCutScene')

local UITowerBattleCutScene = {}

-- 初始化
function UITowerBattleCutScene:OnCreate()
	self.objList.Btn_BG.onClick:AddListenerEx(self.OnClickClose)
	
	return true
end

-- 点击关闭按钮
function UITowerBattleCutScene.OnClickClose()
	local self = UITowerBattleCutScene
	self:CloseSelf()
end

-- 窗口开启
function UITowerBattleCutScene:OnOpen(configID, nextWnd, nextParam)
	self.nextWnd = nextWnd
	self.nextParam = nextParam
	
	local config = Schemes.TowerBattleModelCutScene:Get(configID)
	if not config then
		warn('UITowerBattleCutScene:OnOpen configID =', configID)
		self:CloseSelf()
		return
	end
	
	local ci = Schemes.Creature:Get(config.CreatureID)
	if not ci then
		warn('UITowerBattleCutScene:OnOpen not ci', configID, config.CreatureID)
		self:CloseSelf()
		return
	end

	self.cutSceneID = configID
	
	self:ClearCacheModel()
	
	local fileName = ci.Prefab
	local result = HotResManager.ReadModelAsync(self.objList.ModelContainer, fileName, self.OnModelLoaded, 0, false)
	if not result then
		warn('UITowerBattleCutScene:OnOpen 加载模型失败', fileName)
	end
	
	local nodeTrans = self.objList.ModelContainer.transform
	nodeTrans.localScale = CachedVector3:Set(config.CreatureScale, config.CreatureScale, config.CreatureScale)
	if config.CreatureLoc[3] then
		nodeTrans.localPosition = CachedVector3:Set(config.CreatureLoc[1], config.CreatureLoc[2], config.CreatureLoc[3])
	end
	if config.CreatureRot[3] then
		nodeTrans.eulerAngles = CachedVector3:Set(config.CreatureRot[1], config.CreatureRot[2], config.CreatureRot[3])
	end
	
	local aniComp = self.objList.ModelContainer:GetComponent(typeof(AniComp))
	if not aniComp then
		aniComp = self.objList.ModelContainer:AddComponent(typeof(AniComp))
	end
	self.aniComp = aniComp
	
	self.objList.Txt_Show.text = ''
	local textCount = #config.ShowText
	local textIndex = math.random(1, textCount)
	local showText = config.ShowText[textIndex]
	if showText and string.len(showText) > 1 then
		showText = string.gsub(showText, '\\n', '\n')
		self.objList.Txt_Show:DOKill()
		if config.TextTime > 0 then
			self.objList.Txt_Show:DOText(showText, config.TextTime)
		else
			self.objList.Txt_Show.text = showText
		end
	else
		self.objList.Txt_Show:DOKill()
	end
	
	if config.SoundID > 0 then
		SoundManager:PlaySound(config.SoundID)
	end
	
	if self.endTimer ~= nil then
		self.endTimer:Stop()
		self.endTimer = nil
	end
	if config.CutTime > 0 then
		self.endTimer = Timer.New(self.OnTimerEnd, config.CutTime, 1)
		self.endTimer:Start()
	end
end

-- 窗口关闭
function UITowerBattleCutScene:OnClose()
	self:ClearCacheModel()
	
	if self.endTimer ~= nil then
		self.endTimer:Stop()
		self.endTimer = nil
	end
	
	if self.nextWnd then
		if self.nextParam then
			UIManager:OpenWnd(self.nextWnd, unpack(self.nextParam))
		else
			UIManager:OpenWnd(self.nextWnd)
		end
	end
end

-- 窗口销毁
function UITowerBattleCutScene:OnDestroy()
end

-- 时间结束处理
function UITowerBattleCutScene.OnTimerEnd()
	local self = UITowerBattleCutScene
	self:CloseSelf()
end

-- 模型载入完毕
function UITowerBattleCutScene.OnModelLoaded(obj, uidParam, fileName)
	local self = UITowerBattleCutScene
	if not self:IsOpen() then
		HelperL.AddObjToModelPool(obj, fileName)
		return
	end

	local config = Schemes.TowerBattleModelCutScene:Get(self.cutSceneID)
	if not config then
		HelperL.AddObjToModelPool(obj, fileName)
		return
	end
	
	self:ClearCacheModel()
	
	Helper.SetLayer(obj, Layer.UI)
	self.curCacheModel = { obj, fileName }
	
	if self.aniComp then
		self.aniComp:UpdateAnimator()
		self.aniComp:PlayAnimation(config.AniName)
	end
end

-- 清除模型
function UITowerBattleCutScene:ClearCacheModel()
	if self.curCacheModel then
		Helper.SetLayer(self.curCacheModel[1], Layer.Default)
		HelperL.AddObjToModelPool(self.curCacheModel[1], self.curCacheModel[2])
		self.curCacheModel = nil
	end
end

return UITowerBattleCutScene