local luaID = ('UITipBestEquipment')

---@class UITipBestEquipment:UIWndBase
local m = {}
--自动装备倒计时时间
local auto_time = 8

--------------------------------------------------------------------
-- 初始化
--------------------------------------------------------------------
function m:OnCreate()
    ---@type Item_Equip2[]
    m.Item_Equip = {}
    m.objList.Txt_Txt1.text = GetGameText(luaID, 2)
    m.RegisterClickEvent()
    return true
end

--------------------------------------------------------------------
-- 窗口开启
--------------------------------------------------------------------
function m:OnOpen(uid)
    m.uid = uid
    m.isRecommendedEquipment = false
    if not EntityModule:GetEntity(uid) then
        m.OnClickClose()
        return
    end
    m.UpdateView()
end

--------------------------------------------------------------------
--注册点击事件
--------------------------------------------------------------------
function m.RegisterClickEvent()
    m:AddClick(m.objList.Btn_Quit, m.OnClickClose)
    --穿戴
    m:AddClick(m.objList.Btn_Wear, function()
        m.EquipWear(m.uid)
        m:CloseSelf()
    end)
    --卸下
    m:AddClick(m.objList.Btn_Demount, function()
        m.EquipDemount(m.uid)
        m:CloseSelf()
    end)
    --分解
    m:AddClick(m.objList.Btn_Sell, function()
        if m.isRecommendedEquipment then
            ---@type NotarizeWindowsDatq
            local data = {
                type = NotarizeWindowsType.Windows3,
                content = GetGameText(luaID, 5),
                okCallback = function(okParam)
                    m.OnClickSell(m.uid)
                end
            }
            HelperL.NotarizeUI(data)
        else
            m.OnClickSell(m.uid)
        end
    end)
    m:AddClick(m.objList.Btn_Upgrade, function()
        local entity = EntityModule:GetEntity(m.uid)
        local equipID = entity:GetProperty(ENTITY_FIELD.ENTITY_FIELD_ID)
        local cfg = Schemes.Equipment:Get(equipID)
        local smeltID = cfg.SmeltID
        m.Upgrade(smeltID)
    end)
end

--------------------------------------------------------------------
--更新界面
--------------------------------------------------------------------
function m.UpdateView()
    local entity = EntityModule:GetEntity(m.uid)
    if not entity then
        error('装备实体未找到 entity is nil uid:' .. m.uid)
        m.OnClickClose()
        return
    end

    if m.IsWearEquip(m.uid) then
        m.OnClickClose()
        return
    end
    local list = { entity }
    local cfg = Schemes.Equipment:Get(entity:GetProperty(ENTITY_FIELD.ENTITY_FIELD_ID))
    local wear_entity = EntityModule:GetEntity(SkepModule.GetEquipSkep()[cfg.SubType])
    if wear_entity then
        table.insert(list, wear_entity)
    end
    m.objList.Obj_Root2.gameObject:SetActive(#list > 1)
    for i = 1, 2, 1 do
        if not m.Item_Equip[i] then
            m.Item_Equip[i] = m.Creation_Item_Equip(m.objList.Grid_Equip, i)
        end
        m.Item_Equip[i].UpdateData(list[i])
    end
end

--------------------------------------------------------------------
--分解请求回调
--------------------------------------------------------------------
function m.DecomposeRequestCallback(result, content)
    -- if result == RESULT_CODE_BASE.RESULT_COMMON_SUCCEED[1] then
    --     local str = ''
    --     local tempList = HelperL.Split(content, "|")
    --     for i, v in ipairs(tempList) do
    --         local list = HelperL.Split(v, ";")
    --         local cfg2 = Schemes:GetGoodsConfig(list[1])
    --         if str == '' then
    --             str = string.format("<color=#00FF00>%s</color>+%s", cfg2.GoodsName, list[2])
    --         else
    --             str = str .. string.format("\n<color=#00FF00>%s</color>+%s", cfg2.GoodsName, list[2])
    --         end
    --     end
    --     HelperL.RewardHintUI(UIRewardHint_Type.Message, str)
    -- end
end

--------------------------------------------------------------------
--装备分解
--------------------------------------------------------------------
function m.OnClickSell(uid)
    --单个装备分解  
    local data = {
		type = NotarizeWindowsType.Windows1,
		titleContent = '提示',
		content = "分解后装备将消失，是否确认分解装备?",
		okCallback = function()
			local entity = EntityModule:GetEntity(uid)
            if entity then
                local goodsID = entity:GetProperty(ENTITY_FIELD.ENTITY_FIELD_ID)
                local goodInfo = ""
                if goodsID <= DEFINE.MAX_MEDICAMENT_ID then
                    goodInfo = Schemes.Medicament:Get(goodsID).EctypeID
                else
                    goodInfo = Schemes.Equipment:Get(goodsID).ModelID
                end
                local costInfo = string.format('%s;%s', uid, 1)
                HelperL.RequestDirectGiveGoodsy(goodInfo, costInfo, m.DecomposeRequestCallback)
            end
            m.OnClickClose()
		end,
	}
	HelperL.NotarizeUI(data)	
end

--------------------------------------------------------------------
-- 关闭事件
--------------------------------------------------------------------
function m.OnClickClose()
    m:CloseSelf()
    m.uid = nil
end

--------------------------------------------------------------------
--创建属性框
--------------------------------------------------------------------
function m.Creation_Item_Attribute(parent, index, pro)
    ---@class Item_Attribute5
    local item = {}
    item.index = index
    item.com = m:CreateSubItem(parent, m.objList.Item_Attribute)
    item.com.Img_Type.color = HelperL.GetQualityColorRGBA(index + 1)
    if pro == 1 then
        item.com.Img_Type.gameObject:SetActive(false)
    end
    --- 更新数据
    ---@param _type integer 1激活，2未激活
    ---@param content string
    item.UpdateData = function(_type, content)
        if content and content ~= '' then
            -- item.com.Img_Type.gameObject:SetActive(false)
            item.com.Img_Lock.gameObject:SetActive(false)

            if _type == 1 then
                --激活
                -- item.com.Img_Type.gameObject:SetActive(true)
            elseif _type == 2 then
                --未激活
                item.com.Img_Lock.gameObject:SetActive(true)
                content = string.format('<color=#858585>%s</color>', content)
            elseif _type == 3 then

            end
            item.com.Txt_Des.text = content
            item.com.gameObject:SetActive(true)
        else
            item.com.gameObject:SetActive(false)
        end
    end
    return item
end

--------------------------------------------------------------------
--创建属性框
--------------------------------------------------------------------
function m.Creation_Item_Equip(parent, index)
    ---@class Item_Equip2
    local item = {}
    item.index = index
    ---@type Item_Attribute5[]
    item.Item_Attribute_List1 = {}
    ---@type Item_Attribute5[]
    item.Item_Attribute_List2 = {}

    item.com = m:CreateSubItem(parent, m.objList.Item_Equip)
    item.UpdateData = function(entity)
        if entity then
            local equipID = entity:GetProperty(ENTITY_FIELD.ENTITY_FIELD_ID)
            local cfg = Schemes.Equipment:Get(equipID)
            local smeltID = cfg.SmeltID
            item.com.Txt_Name.text = cfg.GoodsName
            item.com.Txt_Type.text = HelperL.GetNameByQuality(cfg.QualityLevel)

            if not item.equipItem then
                item.equipItem = _GAddSlotItem(item.com.Obj_Equip)
                item.equipItem:EnableClick(false)
            end
            item.equipItem:SetEntity(entity)
            local level = GamePlayerData.ActorEquipNew:GetEquipUpgradeLevel(smeltID)
            local maxLevel = PropertyCompute.GetEquipSmeltStarMaxLevel(smeltID)
            item.com.Txt_Level.text = string.format(GetGameText(luaID, 8), level, maxLevel)
            item.com.Txt_Describe.text = HelperL.GetContenBRText(cfg.TipsDes)
            item.com.Txt_TipsDes.text = HelperL.GetContenBRText(cfg.TipsDes)
            print("smeltID ===== ", smeltID)
            print("level ===== ", level)

            local starCfg = Schemes.EquipSmeltStar:GetEquipSmeltStarCfg(smeltID, level)
            if not starCfg then
                error(string.format('EquipSmeltStar配置错误 smeltID=%s   level=%s', smeltID, level))
                return
            end

            local nextStarCfg = Schemes.EquipSmeltStar:GetEquipSmeltStarCfg(smeltID, level+1)
            if nextStarCfg and starCfg then
                local exp = LogicValue.GetEquipDataLV2(smeltID, 1)
                item.com.Txt_Ex.text = (exp - starCfg.StarExp).."/"..(nextStarCfg.StarExp - starCfg.StarExp)
                item.com.Img_Fill.fillAmount = (exp - starCfg.StarExp)/(nextStarCfg.StarExp - starCfg.StarExp)
            end

            local num1 = SkepModule:GetGoodsCount(starCfg.CostGoodsID1)
            if starCfg.CostGoodsID1 > 0 then
                AtlasManager:AsyncGetGoodsSprite(starCfg.CostGoodsID1, item.com.Img_Expend1)
                local goodsName = Schemes:GetGoodsConfig(starCfg.CostGoodsID1).GoodsName
                local color1 = num1 >= starCfg.CostGoodsID1Num and '#FFFFFF' or '#FF8CFF'
                item.com.Txt_Describe1.text = string.format("%s：<color=%s>%s/%s</color>", goodsName, color1,
                    HelperL.GetChangeNum(num1), HelperL.GetChangeNum(starCfg.CostGoodsID1Num))
                item.com.Obj_Expend1.gameObject:SetActive(true)
            else
                item.com.Obj_Expend1.gameObject:SetActive(false)
            end

            -- local num2 = SkepModule:GetGoodsCount(starCfg.CostGoodsID2)
            -- if starCfg.CostGoodsID2 > 0 then
            --     AtlasManager:AsyncGetGoodsSprite(starCfg.CostGoodsID2, item.com.Img_Expend2)
            --     local color2 = num2 >= starCfg.CostGoodsID2Num and '#FFFFFF' or '#FF8CFF'
            --     item.com.Txt_Expend2.text = string.format("<color=%s>%s/%s</color>", color2, HelperL.GetChangeNum(num2),
            --         HelperL.GetChangeNum(starCfg.CostGoodsID2Num))
            --     item.com.Txt_Expend2.gameObject:SetActive(true)
            -- else
            --     item.com.Txt_Expend2.gameObject:SetActive(false)
            -- end

            local contentList = {}
            local attack = 0
            local hp = 0
            if cfg.QualityLevel < starCfg.AttackList.Length then
                attack = attack + starCfg.AttackList[cfg.QualityLevel]
            end
            if attack > 0 then
                table.insert(contentList, '攻击：' .. HelperL.GetChangeNum(attack))
            end
           
            if cfg.QualityLevel < starCfg.MaxHpList.Length then
                hp = hp + starCfg.MaxHpList[cfg.QualityLevel]
            end
            if hp > 0 then
                table.insert(contentList, '生命：' .. HelperL.GetChangeNum(hp))
            end
            local max1 = math.max(#contentList, #item.Item_Attribute_List1)
            for i = 1, max1, 1 do
                if not item.Item_Attribute_List1[i] then
                    item.Item_Attribute_List1[i] = m.Creation_Item_Attribute(item.com.Grid_Attribute1, i, 1)
                end
                item.Item_Attribute_List1[i].UpdateData(3, contentList[i])
            end

            local effectIDs = HelperL.Split(cfg.EffectTipsID, ';')
            local max2 = math.max(#effectIDs, #item.Item_Attribute_List2)
            local commonProp, _type, content
            for i = 1, max2, 1 do
                if not item.Item_Attribute_List2[i] then
                    item.Item_Attribute_List2[i] = m.Creation_Item_Attribute(item.com.Grid_Attribute2, i, 2)
                end
                content = ''
                commonProp = Schemes.CommonProp:Get(effectIDs[i])
                if commonProp then
                    content = commonProp.Remark
                end
                if cfg.QualityLevel >= i + 1 then
                    --激活
                    _type = 1
                else
                    --未激活
                    _type = 2
                end
                item.Item_Attribute_List2[i].UpdateData(_type, content)
            end

            item.com.gameObject:SetActive(true)
        else
            item.com.gameObject:SetActive(false)
        end
    end

    return item
end

--------------------------------------------------------------------
-- 是穿戴装备
---@param uid integer 实体唯一ID
---@return boolean
--------------------------------------------------------------------
function m.IsWearEquip(uid)
    local entity = EntityModule:GetEntity(uid)
    if entity then
        local equipID = entity:GetProperty(ENTITY_FIELD.ENTITY_FIELD_ID)
        local cfg = Schemes.Equipment:Get(equipID)
        local wear_entity = EntityModule:GetEntity(SkepModule.GetEquipSkep()[cfg.SubType])
        if wear_entity then
            return uid == wear_entity.uid
        end
    end

    return false
end

--------------------------------------------------------------------
-- 装备穿戴
---@param uid integer 实体唯一ID
--------------------------------------------------------------------
function m.EquipWear(uid)
    SkepModule.PutOnEquipment(uid)

    local entity = EntityModule:GetEntity(uid)
    local equipID = entity:GetProperty(ENTITY_FIELD.ENTITY_FIELD_ID)
    local cfg = Schemes.Equipment:Get(equipID)
    GamePlayerData.ActorEquipNew:SetEquipTypeQuality(equipID, cfg.QualityLevel)
end

--------------------------------------------------------------------
-- 装备脱下
---@param uid integer 实体唯一ID
--------------------------------------------------------------------
function m.EquipDemount(uid)
    SkepModule.PutOnEquipment(uid)

    local entity = EntityModule:GetEntity(uid)
    local equipID = entity:GetProperty(ENTITY_FIELD.ENTITY_FIELD_ID)
    -- local cfg = Schemes.Equipment:Get(equipID)
    GamePlayerData.ActorEquipNew:SetEquipTypeQuality(equipID, -1)
end

--------------------------------------------------------------------
--请求回调
--------------------------------------------------------------------
function m.RequestCallback(resultCode, content)
    if resultCode == RESULT_CODE_BASE.RESULT_COMMON_SUCCEED[1] then
        -- m.UpdateView()
        --播放升级特效
        HelperL.PlayVFX()
        SoundManager:PlaySound(SoundID.Upgrade)
    else
        ResultCode:DefaultShowResultCode(resultCode, content)
    end
end

--------------------------------------------------------------------
--升级
---@param smeltID integer 升星ID
--------------------------------------------------------------------
function m.Upgrade(smeltID)
    local curTime = os.clock()
    if curTime - m.lastTime < 0.3 then
        print('天赋升级点太快了1秒后尝试！')
        return
    end
    m.lastTime = curTime

    local starLvl = GamePlayerData.ActorEquipNew:GetEquipUpgradeLevel(smeltID)
    local equipSmeltStar = Schemes.EquipSmeltStar:GetEquipSmeltStarCfg(smeltID, starLvl)
    if equipSmeltStar == nil then
        warn("天赋升级:未找到升星配置 smeltID=", smeltID, starLvl)
        return
    end

    if HelperL.IsLackGoods(equipSmeltStar.CostGoodsID1, equipSmeltStar.CostGoodsID1Num, false) then return end
    if HelperL.IsLackGoods(equipSmeltStar.CostGoodsID2, equipSmeltStar.CostGoodsID2Num, false) then return end

    local form = {}
    form["smeltID"] = smeltID
    form["loopTimes"] = 1
    LuaModuleNew.SendRequest(LuaRequestID.NewCardAddExp2, form, m.RequestCallback)
end

return m
