--[[
********************************************************************
    created:	2024/06/06
    author :	李锦剑
    purpose:    巡逻界面
*********************************************************************
--]]

local luaID = ('UICatPatrol')
--体力物品ID
local PhysicalPowerGoodsID = 2

-- 117消耗体力快速巡逻
local adID1 = 117
-- 123看广告，同时消耗体力，快速巡逻
local adID2 = 123

---巡逻界面
---@class UICatPatrol:UIWndBase
local m = {}

--------------------------------------------------------------------
-- 订阅事件列表
--------------------------------------------------------------------
function m:GetOpenEventList()
	return {
		[EventID.StoreList] = m.UpdateView,
		[EventID.StoreUpdateFree] = m.UpdateView,
		[EventID.LogicDataChange] = m.UpdateView,
		[EventID.EntitySyncBuff] = m.UpdateView,
		[EventID.OnGoodsPropChange] = m.UpdateView,
		[EventID.OnHeroPropChange] = m.UpdateView,
	}
end

--------------------------------------------------------------------
-- 创建时
--------------------------------------------------------------------
function m:OnCreate()
	m.objList.Txt_Title.text = GetGameText(luaID, 1)
	m.objList.Txt_QuickCount.text = GetGameText(luaID, 1)
	m.objList.Txt_QuickDesc.text = string.format(GetGameText(luaID, 9), 6)
	m.objList.Txt_Title2.text = GetGameText(luaID, 12)

	---@type SlotItem[]
	m.goodsItemList = {}

	m.RegisterClickEvent()
	return true
end

--------------------------------------------------------------------
-- 打开时
--------------------------------------------------------------------
function m:OnOpen()
	m.UpdateView()
end

--------------------------------------------------------------------
-- 注册点击事件
--------------------------------------------------------------------
function m.RegisterClickEvent()
	m.objList.Btn_Close.onClick:AddListenerEx(function()
		m:CloseSelf()
	end)
	m:AddClick(m.objList.Btn_Power, m.GetAward)
end

--------------------------------------------------------------------
-- 更新界面
--------------------------------------------------------------------
function m.UpdateView()
	local maxStage = GamePlayerData.GameEctype:GetProgress(1)
	if maxStage <= 0 then
		maxStage = 1
	end
	for _, v in ipairs(Schemes.PrizePatrol.items) do
		if v and maxStage >= v.RecePram2 then
			m.patrolConfig = v
		end
	end
	if not m.patrolConfig then
		m.patrolConfig = Schemes.PrizePatrol:Get(1)
	end

	local prizeInfoList = HelperL.Split(m.patrolConfig.PrizeIDFastShow, "|")
	local num = math.max(#m.goodsItemList, #prizeInfoList)
	local strList, goodsiID, goodsNum
	for i = 1, num, 1 do
		if not m.goodsItemList[i] then
			m.goodsItemList[i] = _GAddSlotItem(m.objList.Grid_Goods)
		end
		strList = HelperL.Split(prizeInfoList[i], ";")
		goodsiID = tonumber(strList[1]) or 0
		goodsNum = tonumber(strList[2]) or 0
		if goodsiID > 0 then
			m.goodsItemList[i]:SetItemID(goodsiID)
			m.goodsItemList[i]:SetCount(goodsNum)
			m.goodsItemList[i]:SetActive(true)
		else
			m.goodsItemList[i]:SetActive(false)
		end
	end


	AtlasManager:AsyncGetGoodsSprite(PhysicalPowerGoodsID, m.objList.Img_Energy)
	m.objList.Txt_Energy.text = m.patrolConfig.PowerConsum

	local amount = SkepModule:GetGoodsCount(PhysicalPowerGoodsID)
	local bool = amount >= (tonumber(m.patrolConfig.PowerConsum) or 0)

	local cfgAD = Schemes.CommonText:Get(adID1)
	local lv = HeroDataManager:GetLogicByte(cfgAD.Param4, cfgAD.Param5)
	local num1 = cfgAD.DayTime - lv
	local color = num1 <= 0 and UI_COLOR.Red or '#16D24B'
	m.objList.Txt_EnergyNum.text = string.format("%s/%s", num1, cfgAD.DayTime)
	if num1 > 0 then
		m.objList.Btn_Power.gameObject:SetActive(true)
		HelperL.SetImageGray(m.objList.Btn_Power, not bool)
		m.objList.Img_RedDot1.gameObject:SetActive(bool)
	else
		m.objList.Btn_Power.gameObject:SetActive(false)
	end
	m.objList.Txt_Title2.gameObject:SetActive(num1 == 0)
end

--------------------------------------------------------------------
-- 获取奖励
--------------------------------------------------------------------
function m.GetAward()
	local cfgAD = Schemes.CommonText:Get(adID1)
	local lv = HeroDataManager:GetLogicByte(cfgAD.Param4, cfgAD.Param5)
	local num1 = cfgAD.DayTime - lv
	if num1 == 0 then
		HelperL.ShowMessage(TipType.FlowText, RESULT_CODE_BASE.RESULT_COMMON_LACK_DAYTIMES[2])
		return
	end
	local expendNum = tonumber(m.patrolConfig.PowerConsum) or 0
	if HelperL.IsLackGoods(PhysicalPowerGoodsID, expendNum, false) then
		return
	end
	if HelperL.GetAdverHint(adID1, true) then
		return
	end
	AdvertisementManager.GetAdAward(adID1, function(bool, adID, code)
		if bool then
			local prizeGoodsList = {}
			Schemes.PrizeTable.__RandomPrize2(m.patrolConfig.PrizeIDFastShow, 1, nil, prizeGoodsList)
			local goodInfo = HelperL.TableAttributeConcatenation(prizeGoodsList, { "ID", "Num" }) or "0"
			local costInfo = PhysicalPowerGoodsID .. ';' .. m.patrolConfig.PowerConsum
			HelperL.RequestDirectGiveGoodsy(goodInfo, costInfo)
		end
	end)
end

--------------------------------------------------------------------
-- 获取广告奖励
--------------------------------------------------------------------
function m.GetAdAward()
	local cfgAD2 = Schemes.CommonText:Get(adID2)
	local lv2 = HeroDataManager:GetLogicByte(cfgAD2.Param4, cfgAD2.Param5)
	local num2 = cfgAD2.DayTime - lv2
	if num2 == 0 then
		HelperL.ShowMessage(TipType.FlowText, RESULT_CODE_BASE.RESULT_COMMON_LACK_DAYTIMES[2])
		return
	end
	local expendNum = tonumber(m.patrolConfig.PowerConsum) or 0
	if HelperL.IsLackGoods(PhysicalPowerGoodsID, expendNum, false) then
		return
	end
	AdvertisementManager.ShowRewardAd(adID2, function(bool, adID, code)
		if bool then
			local prizeGoodsList = {}
			Schemes.PrizeTable.__RandomPrize2(m.patrolConfig.PrizeIDFastShow, 1, nil, prizeGoodsList)
			local goodInfo = HelperL.TableAttributeConcatenation(prizeGoodsList, { "ID", "Num" }) or "0"
			local costInfo = PhysicalPowerGoodsID .. ';' .. m.patrolConfig.PowerConsum
			HelperL.RequestDirectGiveGoodsy(goodInfo, costInfo)
		end
	end)
end

return m
