local ClassBase = require("Base_Class")

-- 物品篮子类
---@class EntityBase:ClassBase
---@field _base ClassBase
local this = class(ClassBase)

--- 实例化入口
function this:_init()
	self.props = {}
end

--- 创建（希望废除掉）
function this:Init()
	self.props = {}
end

--- 设置数据
---@param data @设置所需数据
function this:SetData(data)

end

--- 获取属性
function this:SetProperty(k, v)
	self.props[k] = v
end

--- 设置属性
function this:GetProperty(k)
	return self.props[k]
end

--- 获取模型动画名称
function this:GetModelAniName(ani)
	return ani
end

--- 播放动画
function this:PlayAnimation(aniName)
	local asm = self.aniComp
	if asm then
		asm:PlayAnimation(self:GetModelAniName(aniName))
	end
end

--- 销毁
function this:OnDestroy()

end

this.__index = this

-- 创建物品篮子
function CreateEntity()
	---@type EntityBase
	local entity = {}
	setmetatable(entity, this)
	entity:Init()
	return entity
end

return this
