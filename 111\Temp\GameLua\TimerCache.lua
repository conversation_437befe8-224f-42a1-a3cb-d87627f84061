---
--- Generated by <PERSON><PERSON><PERSON>(https://github.com/EmmyLua)
--- Created by Admin.
--- DateTime: 2023/8/1 10:36
--- 计时器工具（请参考Temp文件夹下的Timer.lua.bytes）
---

local TimerType = require("TimerType")
local TimerEx = require("TimerEx")

---@class TimerCache
local this = class()

function this:_init()
    self:Init()
end

---@private
function this:Init()
    self.cache = {}
end

--- 创建计时器
---@param _timerType TimerType @计时器类型
---@return TimerBase @计时器
local function CreateTimer(_timerType)
    local timer
    if _timerType == TimerType.TIMER then
        timer = TimerEx.Timer.New()
    elseif _timerType == TimerType.FRAME_TIMER then
        timer = TimerEx.FrameTimer.New()
    elseif _timerType == TimerType.CO_TIMER then
        timer = TimerEx.CoTimer.New()
    elseif _timerType == TimerType.QUEUE_TIMER then
        timer = TimerEx.QueueTimer.New()
    else
        error("计时器类型有误")
        return
    end
    timer.timerType = _timerType
    return timer
end

--- 获取一个计时器（从计时器缓存表中）
---@private
---@param _timerType TimerType @计时器类型
---@return TimerBase @计时器
function this:GetAvailableTimerFromCache(_timerType)
    _timerType = _timerType or TimerType.TIMER
    -- 寻找空闲计时器
    if self.cache then
        for _, timer in pairs(self.cache) do
            if (timer.timerType == _timerType) and (not timer.running) then
                return timer
            end
        end
    end
    -- 创建计时器
    local newTimer = CreateTimer(_timerType)
    self.cache[#self.cache + 1] = newTimer

    return newTimer
end

---@return Timer
function this:GetOneTimer()
    return self:GetAvailableTimerFromCache(TimerType.TIMER)
end

---@return FrameTimer
function this:GetOneFrameTimer()
    return self:GetAvailableTimerFromCache(TimerType.FRAME_TIMER)
end

---@return CoTimer
function this:GetOneCoTimer()
    return self:GetAvailableTimerFromCache(TimerType.CO_TIMER)
end

---@return QueueTimer
function this:GetOneTimerEx()
    return self:GetAvailableTimerFromCache(TimerType.QUEUE_TIMER)
end

function this:Clear()
    for _, timer in pairs(self.cache) do
        TryCall(timer.Stop, timer)
    end
end

return this