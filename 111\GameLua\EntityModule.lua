-- 实体模块
require 'EntityMessage_pb'
local luaID = ('EntityModule')

---@type table<string, Timer_2>
local TimerList = {}

---@class EntityModule 实体模块
EntityModule = {}
EntityModule.name = 'EntityModule'

---@private
EntityModule.entityList = {}

EntityModule.trapEntities = {}

CacheEntities = CacheEntities or { Monster = {}, Dummy = {}, NPC = {}, Collect = {}, Trap = {}, }
EntityModule.cacheEntities = CacheEntities

EntityModule.frameCreationQueue = HelperL.NewQueue():New()
EntityModule.defaultModelCreatureID = 0
-- 每个位置上最好的装备
EntityModule.baseEquipmentByPlace = {}
EntityModule.slotMap = { 1, 2, 3, 4, 5, 6, 10, 11 }
EntityModule.hadLogin = false
-- 当前登录已经提示过的装备，本次登录将不在提示
EntityModule.hadTipEquipent = {}
-- 玩家是否已出生
EntityModule.hadBorn = false;

MODEL_TYPE = {
	MODEL_TYPE_CREATURE = 1,
};

local cacheVector3 = Vector3.New(0, 0, 0)

EntityModule.lateCreationQueue = HelperL.NewQueue():New()
EntityModule.lateUpdateQueue = HelperL.NewQueue():New()
EntityModule.frameCreationQueue = HelperL.NewQueue():New()


local needUpdatePower
function EntityModule.Handle(action, data)
	-- 收到每个包都更新心跳时间
	-- GameModule:OnReceiveHeartBeat()
	--local isDataUpdate = false
	needUpdatePower = false
	if action == EntityMessage_pb.MSG_ENTITY_CREATE then
		needUpdatePower = true
		-- 创建实体
		local m = EntityMessage_pb.SC_Entity_CreateEntity()
		m:ParseFromString(data)
		EntityModule:SC_Entity_CreateEntity(m)
	elseif action == EntityMessage_pb.MSG_ENTITY_DESTROY then
		-- 销毁实体
		local m = EntityMessage_pb.SC_Entity_DestroyEntity()
		m:ParseFromString(data)
		EntityModule:SC_Entity_DestroyEntity(m)
	elseif action == EntityMessage_pb.MSG_ENTITY_SYNCPROP then
		needUpdatePower = true
		--isDataUpdate = true
		-- 同步实体属性
		local m = EntityMessage_pb.SC_Entity_SyncProp()
		m:ParseFromString(data)
		EntityModule:SC_Entity_SyncProp(m)
	elseif action == EntityMessage_pb.MSG_ENTITY_SYNCLOGICDATA then
		--isDataUpdate = true
		-- 同步逻辑数据
		local m = EntityMessage_pb.SC_Entity_SyncLogicData()
		m:ParseFromString(data)
		EntityModule:SC_Entity_SyncLogicData(m)
	elseif action == EntityMessage_pb.MSG_ENTITY_SYNCCOOLTIME then
		-- 同步冷却时间
		local m = EntityMessage_pb.SC_Entity_SyncCoolTime()
		m:ParseFromString(data)
		EntityModule:SC_Entity_SyncCoolTime(m)
	elseif action == EntityMessage_pb.MSG_ENTITY_CREATURESHOWFX then -- 显示生物光效
		local m = EntityMessage_pb.SC_Entity_CreatureShowFx()
		m:ParseFromString(data)
		EntityModule:SC_Entity_CreatureShowFx(m)
	elseif action == EntityMessage_pb.MSG_ENTITY_SYNBUFFDATA then
		local m = EntityMessage_pb.SC_Entity_Sync_BuffData()
		m:ParseFromString(data)
		EntityModule:UpdateEntityBuffData(m)
	elseif action == EntityMessage_pb.MSG_ENTITY_SYNCTOWERBATTLEDATA then
		local m = EntityMessage_pb.SC_Entity_SyncTowerBattleData()
		m:ParseFromString(data)
	elseif action == EntityMessage_pb.MSG_ENTITY_CREATUREPLAYSOUND then
		local m = EntityMessage_pb.SC_Entity_CreaturePlaySound()
		m:ParseFromString(data)
		EntityModule:SC_Entity_CreaturePlaySound(m)
	elseif action == EntityMessage_pb.MSG_ENTITY_SYNCCREATUREGRID then
		local m = EntityMessage_pb.SC_Entity_SyncCreatureGrid()
		m:ParseFromString(data)
	elseif action == EntityMessage_pb.MSG_ENTITY_SYNCGEMDATA then
		local m = EntityMessage_pb.SC_Entity_SyncGemData()
		m:ParseFromString(data)
		-- EntityModule.SC_Entity_SyncGemData(m)
	elseif action == EntityMessage_pb.MSG_ENTITY_SYNCEQUIPGEM then
		local m = EntityMessage_pb.SC_Entity_SyncEquipGem()
		m:ParseFromString(data)
		EntityModule.SC_Entity_SyncEquipGem(m)
	elseif action == EntityMessage_pb.MSG_ENTITY_SYNCTASKDATA then
		local m = EntityMessage_pb.SC_Entity_SyncTaskData()
		m:ParseFromString(data)
		EntityModule.SC_Entity_SyncTaskData(m)
		-- 同步任务
	elseif action == EntityMessage_pb.MSG_ENTITY_SYNCTASKITEM then
		local m = EntityMessage_pb.SC_Entity_SyncTaskItem()
		m:ParseFromString(data)
		EntityModule.SC_Entity_SyncTaskItem(m)
	elseif action == EntityMessage_pb.MSG_ENTITY_SYNCNAME then
		local m = EntityMessage_pb.SC_Entity_Sync_Name()
		m:ParseFromString(data)
		
		print("m.Name=============="..m.Name);
		local goodInfo = '0'
		local costInfo = "3;50"
		EntityModule.hero.name = m.Name
		EventManager:Fire(EventID.ChangeNameEvent)
		--扣消耗
		HelperL.RequestDirectGiveGoodsy(goodInfo, costInfo, function(resultCode, content)
			--扣消耗--失败
			if resultCode ~= 0 then
				return
			end    
		end, false)
	end

	-- --节省性能延迟1秒更新
	-- if isDataUpdate then
	if not TimerList.EntityModuleDataUpdate then
		TimerList.EntityModuleDataUpdate = HelperL.NewTimer(function()
			EventManager:Fire(EventID.EntityModuleDataUpdate)
		end, 1, 1)
		TimerList.EntityModuleDataUpdate.Start()
	else
		TimerList.EntityModuleDataUpdate.Start()
	end
	-- end

	if needUpdatePower then
		--更新战力
		if not TimerList.PowerUpdate then
			TimerList.PowerUpdate = HelperL.NewTimer(function()
				local rtn = ActorProp.GetPlayerFieldsByClient()
				if EntityModule.hero.FirePower ~= rtn.FirePower then
					if EntityModule.hero.FirePower then
						HelperL.RewardHintUI(UIRewardHint_Type.Power, EntityModule.hero.FirePower,
							rtn.FirePower)
					end
					EntityModule.hero.FirePower = rtn.FirePower
				end

				if EntityModule.hero.DefensePower ~= rtn.DefensePower then
					if EntityModule.hero.DefensePower then
						HelperL.RewardHintUI(UIRewardHint_Type.Defense, EntityModule.hero.DefensePower,
							rtn.DefensePower)
					end
					EntityModule.hero.DefensePower = rtn.DefensePower
				end
			end, 0.5, 1)
			TimerList.PowerUpdate.Start()
		else
			TimerList.PowerUpdate.Start()
		end
	end
end

-- 获取实体
function EntityModule:GetEntity(uid)
	if not uid then
		return nil
	end
	return self.entityList[uid]
end

-- 设置实体
function EntityModule:SetEntity(uid, entity)
	self.entityList[uid] = entity
end

-- 获取实体列表
function EntityModule:GetEntityList()
	return self.entityList
end

-- 获取陷阱实体列表
function EntityModule:GetTrapList()
	return self.trapEntities
end

-- 每帧调用
function EntityModule:OnUpdate()
	if self.frameCreationQueue:Count() > 0 then
		local t = self.frameCreationQueue:popFirst()

		if t.subType == 1 then
			-- 模型异步创建
			local o = self:GetEntity(t.uid)
			if o == nil or o.prefab == nil or tolua.isnull(o.prefab) then
				return
			end
			if not o.creatureID then
				return
			end
			local ci = Schemes.Creature:Get(o.creatureID)
			if not ci then
				return
			end

			local callback = nil
			if o.tag == 'Monster' then
				callback = self.OnMonsterModelLoaded
			elseif o.tag == 'Trap' then
				callback = self.OnTrapModelLoaded
			else
				warn('EntityModule:OnUpdate 未识别的类型', o.tag)
			end

			local fileName = ci.Prefab
			local abName = HotResManager.GetModelABName(fileName)
			if not HotResManager.IsBundleDownloaded(abName) then
				-- 默认模型
				local fallbackCreature = Schemes.Creature:Get(self.defaultModelCreatureID)
				if fallbackCreature then
					fileName = fallbackCreature.Prefab
					o.isDefaultModel = true
				end
			end

			local result = HotResManager.ReadModelAsync(o.prefab, fileName, callback, o.uid, true)
			if not result then
				warn('EntityModule:OnUpdate 加载模型' .. fileName .. '失败')
				return
			end
		end
	end
end

function EntityModule:SC_Entity_CreateEntity(m)
	-- 英雄
	if m:HasField('HeroEntity') then
		print("========SC_Entity_CreateEntity===========")
		self:CreateHero(m.HeroEntity)
	end

	-- 其他玩家
	for _, v in ipairs(m.DummyList) do
		self:CreateDummy(v)
	end

	-- 怪物
	for _, v in ipairs(m.MonsterList) do
		self:CreateMonster(v)
	end

	-- 陷阱
	for _, v in ipairs(m.TrapList) do
		self:CreateTrap(v)
	end

	-- NPC
	for _, v in ipairs(m.NpcList) do
		self:CreateNpc(v)
	end

	-- 采集物
	for _, v in ipairs(m.CollectList) do
		self:CreateCollect(v)
	end

	-- 物品
	for _, v in ipairs(m.MedicaList) do
		self:CreateGoods(v)
	end

	-- 装备
	for _, v in ipairs(m.EquipList) do
		self:CreateEquipment(v)
	end
end

function EntityModule:DestroyEntity(uid)
	local e = self:GetEntity(uid)
	if e ~= nil then
		EventManager:Fire(EventID.OnDestroyEntity, uid, e)
		if e.tag == 'Hero' then
			self.hero = nil
		end
		if e.tag == 'Trap' then
			self.trapEntities[uid] = nil
		end

		if e.model then
			if e.hpComp then
				e.hpComp:Destroy()
				e.hpComp = nil
			end
			if e.buffLC then
				e.buffLC:OnDestroy()
			end

			self:ClearEntityModelData(e)

			if e.tag == 'Monster' or e.tag == 'Dummy' or e.tag == 'NPC' or e.tag == 'Collect' or e.tag == 'Trap' then
				e.model:SetActive(false)
				e.model.name = e.tag
				e.modelTrans:SetParent(self.modelCacheContainerTrans)
				table.insert(self.cacheEntities[e.tag], e.model)
			else
				GameObject.Destroy(e.model)
			end
		end
		self:SetEntity(uid, nil)
	end
end

function EntityModule:SC_Entity_DestroyEntity(m)
	for _, v in ipairs(m.EntityUID) do
		local e = self:GetEntity(v)
		if e ~= nil then
			if e.entity_class == ENTITY_CLASS.ENTITY_CLASS_PLAYER or
				e.entity_class == ENTITY_CLASS.ENTITY_CLASS_MONSTER or
				e.entity_class == ENTITY_CLASS.ENTITY_CLASS_SCENENPC or
				e.entity_class == ENTITY_CLASS.ENTITY_CLASS_COLLECT or
				e.entity_class == ENTITY_CLASS.ENTITY_CLASS_TRAP then
				self:DestroyEntity(v)
			else
				self:SetEntity(v, nil)
				EventManager:Fire(EventID.OnDestroyEntity, v, e)
			end
		else
			warn('SC_Entity_DestroyEntity 实体不存在？', v)
		end
	end
end

-- 同步实体属性
function EntityModule:SC_Entity_SyncProp(m)
	---@type EntityBase
	local o = self:GetEntity(m.EntityUID)
	if o == nil then
		return
	end

	for _, v in ipairs(m.PropList) do
		local oldValue = o:GetProperty(v.PropID)
		if oldValue ~= v.Value then
			o:SetProperty(v.PropID, v.Value)
			--print('entity_class:'..o.entity_class..'   PropID:'..v.PropID..'   Value:'..v.Value)
			if o.entity_class == ENTITY_CLASS.ENTITY_CLASS_PLAYER or o.entity_class == ENTITY_CLASS.ENTITY_CLASS_MONSTER then
				if v.PropID == PLAYER_FIELD.PLAYER_FIELD_HP or v.PropID == PLAYER_FIELD.PLAYER_FIELD_MAXHP then
					EventManager:Fire(EventID.EntityPropertyUpdate_Hp, o.uid, oldValue, v.Value)
				elseif v.PropID == PLAYER_FIELD.PLAYER_FIELD_DIE then
					if v.Value > 0 then
						if o.creatureID then
							local ci = Schemes.Creature:Get(o.creatureID)
							if ci and ci.DieFxID > 0 then
								EntityModule:ShowFx(nil, ci.DieFxID, o.modelTrans.localPosition, 0)
							end
						end
					end
				end
			elseif o.entity_class == ENTITY_CLASS.ENTITY_CLASS_EQUIPMENT or o.entity_class == ENTITY_CLASS.ENTITY_CLASS_MEDICAMENT then
				--print('---OnGoodsPropChange---')
				EventManager:Fire(EventID.OnGoodsPropChange, o.uid, v.PropID, oldValue, v.Value)
			end

			-- 任务
			local isHero = false
			if EntityModule.hero then
				if EntityModule.hero.uid == m.EntityUID then
					isHero = true
				end
			end
			if isHero then
				-- print('-------触发飘字事件------------',v.Value, v.Value - oldValue, v.PropID)
				EventManager:Fire(EventID.HeroPropertyUpdate_Silver, v.Value, v.Value - oldValue, v.PropID) --触发飘字事件
			end
			if not EntityModule.hero then return end
			--local curExp1 = EntityModule.hero.propertyLC:GetProperty(PLAYER_FIELD.PLAYER_FIELD_CUREXP)
			if o.entity_class == ENTITY_CLASS.ENTITY_CLASS_PLAYER then
				if v.PropID == CREATURE_FIELD.CREATURE_FIELD_LEVEL then
					if isHero then
						if EntityModule.hero.heroTaskLC then
							EntityModule.hero.heroTaskLC.OnTaskSerialUpdate()
						end
						EventManager:Fire(EventID.EntityPropertyUpdate_Level, o.uid)
						EventManager:Fire(EventID.CheckGuideEvent, 2)
					end
				end
			end
		end
	end

	--延迟执行，提升性能
	if not TimerList.OnHeroPropChange then
		TimerList.OnHeroPropChange = HelperL.NewTimer(function()
			EventManager:Fire(EventID.OnHeroPropChange)
		end, 0.5, 1)
		TimerList.OnHeroPropChange.Start()
	else
		TimerList.OnHeroPropChange.Start()
	end
end

--同步宝石数据
function EntityModule.SC_Entity_SyncEquipGem(m)
	--EntityModule.hero.equipGemData = m
	if EntityModule.hero then
		for k, v in ipairs(m.EquipGemList) do
			local equipIndex = m.EquipIndex + (k - 1) * 8
			print('equipIndex:' .. equipIndex)
			EntityModule.hero.equipGemPartDataLC:Set(equipIndex, v)
		end
	else
		local t = {}
		t.d = m
		t.funcName = '同步宝石数据SyncEquipGem'
		t.LateUpdate = EntityModule.SC_Entity_SyncEquipGem
		EntityModule.lateUpdateQueue:pushLast(t)
	end
end

function EntityModule:SC_Entity_SyncLogicData(m)
	local isVariation = false
	for _, v in ipairs(m.DataList) do
		if HeroDataManager:GetLogicData(v.DataKey) ~= v.Value then
			isVariation = true
			HeroDataManager:SetLogicData(v.DataKey, v.Value)
		end
	end
	HeroDataManager:UpdateAllLogic()
	--有变动在更新，提升性能
	if isVariation then
		EventManager:Fire(EventID.LogicDataChange)
	end
end

function EntityModule:SC_Entity_SyncCoolTime(m)
	for _, v in ipairs(m.CoolTimeList) do
		HeroDataManager:SetCoolTime(v.CoolKey, v.Value)
	end
end

function EntityModule:ClearEntityModelData(entity)
	if entity.prefab and entity.modelData ~= nil then
		for i, v in ipairs(entity.modelData) do
			HelperL.AddObjToModelPool(v.obj, v.fileName)
		end
		entity.modelData = nil
	end
end

-- 创建英雄
function EntityModule:CreateHero(d)
	-- 告知转发器角色已上线
	local objNetwork = Premier.Instance:GetNetwork();
	objNetwork.PersistentConnection:SetActorLoggedIn():Forget();

	-- 已出生过就啥也不干
	if self.hadBorn then
		return;
	end

	self.hadBorn = true;
	--region 创建过程

	-- 创建玩家信息实体
	local HeroEntity = require("Entity_Hero")
	local o = HeroEntity()
	-- 若之前创建过，重新创建，销毁实体
	local isRecreate = false
	if self.hero then
		o = self.hero -- 存放上一细胞信息
		self:DestroyEntity(self.hero.uid)
		isRecreate = true
	end
	-- 更新数据
	o:SetData(d)
	-- 保存
	self:SetEntity(d.EntityUID, o)
	self.hero = o
	--endregion 创建过程
	--region 与玩家创建关联不大的操作
	-- 临时保存体魄的uid
	local genre = 1
	local hero = o
	if hero then
		genre = hero:GetProperty(PLAYER_FIELD.PLAYER_FIELD_GENRE)
	end
	HelperL.clientTempAirplaneUID = d.GodWeaponData.GodWeaponUID[genre]
	-- 若为重登，关闭【等待UI】
	if LoginModule.isReconnecting then
		LoginModule.isReconnecting = nil
		HelperL.CloseBlackWaitingUI()
	end

	StoreModule:RequestGetStoreList(1)
	StoreModule:RequestGetStoreList(2)
	MatchDataManager:RequestMatchList(1, 0)
	-- buff组件
	o.buffLC = BuffLC_New(o)
	o.buffLC:Build(d.BuffData)

	
	HeroDataManager:UpdateAllLogic()

	--TODO 实际上，以上那些操作，都因由自己模块去搞定，而不是写在EntityModule中，监听以下广播便是
	EventManager:Fire(EventID.OnHeroCreate)
	--请求直接发放奖励的验证key表
	LoginModule.getDirectPrizeKeys = { 467, 728, 377, 566, 800, 575, 917, 764, 935, 701, 368, 386, 305, 134, 773, 350, 647, 332, 323, 737, 809, 242, 161, 926, 746, 512, 944, 665, 341, 674, 548, 125, 998, 908, 638, 359, 521, 485, 971, 602, 503, 251, 530, 107, 269, 314, 224, 413, 197, 557, 890, 404, 539, 296, 593, 620, 791, 215, 278, 881, 656, 116, 611, 206, 854, 179, 836, 440, 170, 953, 962, 449, 395, 143, 710, 287, 719, 458, 476, 899, 152, 683, 782, 422, 431, 629, 980, 827, 260, 692, 989, 818, 863, 872, 755, 494, 233, 584, 188, 845 }
	LuaModule.RunLuaRequest(
		string.format('LuaRequestDirectGiveKey?AdverID=%s', LoginModule:GetSelectActorData().ActorID),
		function(code, content)
			if code == RESULT_CODE.RESULT_COMMON_SUCCEED then
				--print(content,"直接奖励keys")
				LoginModule.getDirectPrizeKeys = HelperL.Split(content, ";")
			end
		end)
end

--创建敌人、其他玩家实体
function EntityModule:CreateDummy(d)
	-- 创建GameObject
	local o = CreateEntity()
	-- 保存
	self:SetEntity(d.EntityUID, o)
	-- tag
	o.tag = "Dummy"
	-- 名字
	o.name = d.ActorName
	-- 实体类型
	o.entity_class = ENTITY_CLASS.ENTITY_CLASS_PLAYER
	-- uid
	o.uid = d.EntityUID
	-- 所属区
	o.zoneID = d.ZoneID
	for k, v in ipairs(d.NumProp) do
		o:SetProperty(k - 1, v)
	end

	--[[
	local modelTrans = nil
	local cachePool = self.cacheEntities[o.tag]
	local cacheSize = #cachePool
	if cacheSize > 0 then
		o.model = cachePool[cacheSize]
		table.remove(cachePool)
		
		modelTrans = o.model.transform
		local modelNode = modelTrans:Find('model')
		if not modelNode then
			warn('缓存模型里找不到model节点？')
			return
		end
		o.prefab = modelNode.gameObject
		o.prefab:SetActive(true)
	else
		o.model = GameObject.New()
		o.model.tag = "Dummy"
		
		modelTrans = o.model.transform
		o.prefab = GameObject.New()
		o.prefab.name = 'model'
		o.prefab.transform:SetParent(modelTrans, false)
	end
	]]

	EventManager:Fire(EventID.OnDummyCreate, o.uid)
end

function EntityModule.OnMonsterModelLoaded(obj, uidParam, fileName)
	local self = EntityModule
	local o = self:GetEntity(tostring(uidParam))
	if o == nil then
		HelperL.AddObjToModelPool(obj, fileName)
		return
	end

	if o.prefab == nil or tolua.isnull(o.prefab) then
		HelperL.AddObjToModelPool(obj, fileName)
		return
	end

	local pickBox = obj:GetComponent('BoxCollider')
	if pickBox then
		-- 不需要拾取
		pickBox.enabled = false
	end

	local ci = Schemes.Creature:Get(o.creatureID)
	if ci.ColorR ~= 1.0 or ci.ColorG ~= 1.0 or ci.ColorB ~= 1.0 or ci.ColorA ~= 1.0 then
		Helper.SetModelColorEx(o.prefab, ci.ColorR, ci.ColorG, ci.ColorB, ci.ColorA)
	end

	local asm = o.aniComp
	if asm then
		asm:UpdateAnimator()
		o:PlayAnimation('born')
		if o.config and o.config.BornSound and o.config.BornSound > 0 then
			local soundScheme = Schemes.Sound:Get(o.config.BornSound)
			if soundScheme ~= nil then
				if SoundManager:GetXmSoundValue() == 0 then
					asm:PlaySound(soundScheme.File)
				end
			else
				warn('OnMonsterModelLoaded 未配置的声音ID ', o.config.BornSound)
			end
		end
	end

	if o.modelData == nil then
		o.modelData = {}
	end
	table.insert(o.modelData, { modelType = MODEL_TYPE.MODEL_TYPE_CREATURE, fileName = fileName, obj = obj })

	-- 更新头顶
	if o.hpComp then
		o.hpComp:UpdateTarget()
	end

	-- 更新buff特效
	o.buffLC:FlushBuffEffect()
end

--创建怪物实体
function EntityModule:CreateMonster(d)
	-- 创建GameObject
	local o = CreateEntity()
	-- tag
	o.tag = 'Monster'
	-- 实体类型
	o.entity_class = ENTITY_CLASS.ENTITY_CLASS_MONSTER
	-- uid
	o.uid = d.EntityUID
	-- 名字
	o.name = d.Name
	-- 所属区
	o.zoneID = d.ZoneID
	-- 属性
	for k, v in ipairs(d.NumProp) do
		if k - 1 < PLAYER_FIELD.PLAYER_FIELD_BROADCAST then
			o:SetProperty(k - 1, v)
		else
			o:SetProperty(k - 1 + MONSTER_FIELD.MONSTER_FIELD_BROADCAST_BEGIN - PLAYER_FIELD.PLAYER_FIELD_BROADCAST, v)
		end
	end
	-- MonsterID
	o.monsterID = o:GetProperty(ENTITY_FIELD.ENTITY_FIELD_ID)
	-- 创建模型
	local config = Schemes.Monster:Get(o.monsterID)
	if not config then
		warn('CreateMonster 找不到怪物id配置 ', o.monsterID)
		return
	end
	o.grid = d.Grid
	o.config = config
	o.creatureID = o:GetProperty(MONSTER_FIELD.MONSTER_FIELD_SHOWMODEL)

	local modelTrans = nil
	local cachePool = self.cacheEntities[o.tag]
	local cacheSize = #cachePool
	if cacheSize > 0 then
		o.model = cachePool[cacheSize]
		table.remove(cachePool)

		modelTrans = o.model.transform
		local modelNode = modelTrans:Find('model')
		if not modelNode then
			warn('缓存模型里找不到model节点？')
			return
		end
		o.prefab = modelNode.gameObject
		o.prefab:SetActive(true)
	else
		o.model = GameObject.New()
		o.model.tag = 'Monster'

		modelTrans = o.model.transform
		o.prefab = GameObject.New()
		o.prefab.name = 'model'
		o.prefab.transform:SetParent(modelTrans, false)
	end

	o.modelTrans = modelTrans
	modelTrans:SetParent(self.modelContainerTrans)



	o.model.name = d.Name
	local aniComp = o.model:GetComponent(typeof(AniComp))
	if not aniComp then
		aniComp = o.model:AddComponent(typeof(AniComp))
	end
	o.aniComp = aniComp

	local ci = Schemes.Creature:Get(o.creatureID)
	modelTrans.position = cacheVector3:Set(d.PosX, d.PosY, d.PosZ)
	if ci then
		modelTrans.localScale = cacheVector3:Set(ci.Scale, ci.Scale, ci.Scale)
		o.nameHeight = ci.NameHeight
	end
	local monsterFace = o:GetProperty(MONSTER_FIELD.MONSTER_FIELD_BIRTH_FACE)
	modelTrans.eulerAngles = cacheVector3:Set(0, monsterFace, 0)
	o.model:SetActive(true)

	o.hpComp = CreateHpComp(o)

	-- 延迟创建模型
	if ci and ci.Prefab ~= '' and ci.Prefab ~= '0' then
		local frameQueueParam = { subType = 1, uid = o.uid }
		EntityModule.frameCreationQueue:pushLast(frameQueueParam)
	end

	-- 保存
	self:SetEntity(d.EntityUID, o)

	EventManager:Fire(EventID.OnMonsterCreate, o.uid)
end

function EntityModule.OnTrapModelLoaded(obj, uidParam, fileName)
	local o = EntityModule:GetEntity(tostring(uidParam))
	if o == nil then
		HelperL.AddObjToModelPool(obj, fileName)
		return
	end

	if o.prefab == nil or tolua.isnull(o.prefab) then
		HelperL.AddObjToModelPool(obj, fileName)
		return
	end

	if obj then
		local pickBox = obj:GetComponent('BoxCollider')
		if pickBox then
			-- 不需要拾取
			pickBox.enabled = false
		end
	end

	local ci = Schemes.Creature:Get(o.creatureID)
	if ci and obj then
		if o.modelData == nil then
			o.modelData = {}
		end
		table.insert(o.modelData,
			{ modelType = MODEL_TYPE.MODEL_TYPE_CREATURE, fileName = fileName, obj = obj, pScale = pScale })
	end

	local asm = o.aniComp
	if asm then
		asm:UpdateAnimator()
	end

	if o.effectID and o.effectID > 0 then
		EntityModule:ShowFx(o, o.effectID)
	end
end

--创建陷阱实体
function EntityModule:CreateTrap(d)
	-- 创建GameObject
	local o = CreateEntity()
	-- 保存
	self:SetEntity(d.EntityUID, o)
	self.trapEntities[d.EntityUID] = o
	-- tag
	o.tag = "Trap"
	-- 实体类型
	o.entity_class = ENTITY_CLASS.ENTITY_CLASS_TRAP
	-- uid
	o.uid = d.EntityUID
	-- 所属区
	o.zoneID = d.ZoneID
	for k, v in ipairs(d.NumProp) do
		if k - 1 < PLAYER_FIELD.PLAYER_FIELD_BROADCAST then
			o:SetProperty(k - 1, v)
		else
			o:SetProperty(k - 1 + TRAP_FIELD.TRAP_FIELD_BROADCAST_BEGIN - PLAYER_FIELD.PLAYER_FIELD_BROADCAST, v)
		end
	end
	-- TrapID
	o.trapID = o:GetProperty(ENTITY_FIELD.ENTITY_FIELD_ID)
	-- data
	o.moveData = d.MoveData

	local modelTrans = nil
	local cachePool = self.cacheEntities[o.tag]
	local cacheSize = #cachePool
	if cacheSize > 0 then
		o.model = cachePool[cacheSize]
		table.remove(cachePool)

		modelTrans = o.model.transform
		local modelNode = modelTrans:Find('model')
		if not modelNode then
			warn('缓存模型里找不到model节点？')
			return
		end
		o.prefab = modelNode.gameObject
		o.prefab:SetActive(true)
	else
		o.model = GameObject.New()
		o.model.tag = "Trap"

		modelTrans = o.model.transform
		o.prefab = GameObject.New()
		o.prefab.name = 'model'
		o.prefab.transform:SetParent(modelTrans, false)
	end

	-- 创建模型
	local config = Schemes.TrapConfig:Get(o.trapID)
	if not config then
		warn('CreateTrap 错误的陷阱id ' .. o.trapID)
		self:SetEntity(d.EntityUID, nil)
		return
	end

	o.name = config.Name
	o.trapType = config.Type
	o.effectID = config.FxID
	o.creatureID = config.CreatureID
	o.model.name = config.Name

	o.modelTrans = modelTrans
	modelTrans:SetParent(self.modelContainerTrans)
	modelTrans.position = cacheVector3:Set(d.PosX, d.PosY, d.PosZ)
	modelTrans.localScale = cacheVector3:Set(1, 1, 1)
	local trapFace = o:GetProperty(TRAP_FIELD.TRAP_FIELD_BIRTH_FACE)
	modelTrans.eulerAngles = cacheVector3:Set(0, trapFace, 0)

	o.model:SetActive(true)

	local aniComp = o.model:GetComponent(typeof(AniComp))
	if not aniComp then
		aniComp = o.model:AddComponent(typeof(AniComp))
	end
	o.aniComp = aniComp

	if o.creatureID > 0 then
		-- 延迟创建模型
		local frameQueueParam = { subType = 1, uid = o.uid }
		EntityModule.frameCreationQueue:pushLast(frameQueueParam)
	else
		-- 允许没有模型
		EntityModule.OnTrapModelLoaded(nil, o.uid, nil)
	end

	EventManager:Fire(EventID.OnTrapCreate, o.uid)
end

--创建NPC实体
function EntityModule:CreateNpc(d)
	-- 创建GameObject
	local o = CreateEntity()
	-- 保存
	self:SetEntity(d.EntityUID, o)
	-- tag
	o.tag = "NPC"

	-- 实体类型
	o.entity_class = ENTITY_CLASS.ENTITY_CLASS_SCENENPC
	-- uid
	o.uid = d.EntityUID
	-- 名字
	o.name = d.NpcName
	for k, v in ipairs(d.NumProp) do
		o:SetProperty(k - 1, v)
	end

	--[[
	local modelTrans = nil
	local cachePool = self.cacheEntities[o.tag]
	local cacheSize = #cachePool
	if cacheSize > 0 then
		o.model = cachePool[cacheSize]
		table.remove(cachePool)
		
		modelTrans = o.model.transform
		local modelNode = modelTrans:Find('model')
		if not modelNode then
			warn('缓存模型里找不到model节点？')
			return
		end
		o.prefab = modelNode.gameObject
		o.prefab:SetActive(true)
	else
		o.model = GameObject.New()
		o.model.tag = "NPC"
		
		modelTrans = o.model.transform
		o.prefab = GameObject.New()
		o.prefab.name = 'model'
		o.prefab.transform:SetParent(modelTrans, false)
	end
	]]

	EventManager:Fire(EventID.OnNpcCreate, o.uid)
end

--创建采集物实体
function EntityModule:CreateCollect(d)
	-- 创建GameObject
	local o = CreateEntity()
	-- 保存
	self:SetEntity(d.EntityUID, o)
	-- tag
	o.tag = "Collect"
	-- 名字
	o.name = d.CollectName
	-- 实体类型
	o.entity_class = ENTITY_CLASS.ENTITY_CLASS_COLLECT
	-- uid
	o.uid = d.EntityUID
	for k, v in ipairs(d.NumProp) do
		o:SetProperty(k - 1, v)
	end

	--[[
	local modelTrans = nil
	local cachePool = self.cacheEntities[o.tag]
	local cacheSize = #cachePool
	if cacheSize > 0 then
		o.model = cachePool[cacheSize]
		table.remove(cachePool)
		
		modelTrans = o.model.transform
		local modelNode = modelTrans:Find('model')
		if not modelNode then
			warn('缓存模型里找不到model节点？')
			return
		end
		o.prefab = modelNode.gameObject
		o.prefab:SetActive(true)
	else
		o.model = GameObject.New()
		o.model.tag = "Collect"
		
		modelTrans = o.model.transform
		o.prefab = GameObject.New()
		o.prefab.name = 'model'
		o.prefab.transform:SetParent(modelTrans, false)
	end
	]]

	EventManager:Fire(EventID.OnCollectCreate, o.uid)
end

--创建物品实体
function EntityModule:CreateGoods(d)
	-- 创建GameObject
	local o = CreateEntity()
	-- 保存
	self:SetEntity(d.EntityUID, o)

	o.name = '_Goods'
	-- 实体类型
	o.entity_class = ENTITY_CLASS.ENTITY_CLASS_MEDICAMENT
	-- uid
	o.uid = d.EntityUID
	--是否绑定 0未绑定 1绑定
	o.flags = d.Flags
	for k, v in ipairs(d.NumProp) do
		o:SetProperty(k - 1, v)
	end
	SkepModule:AutoUseGoods(o)
	EventManager:Fire(EventID.OnGoodsCreate, o.uid)
end

--创建装备实体
function EntityModule:CreateEquipment(d)
	local EquipmentEntity = require("Entity_Equipment")
	---@type EquipmentEntity
	local o = EquipmentEntity()
	o:SetData(d)
	-- 保存
	self:SetEntity(d.EntityUID, o)
	EventManager:Fire(EventID.OnGoodsCreate, o.uid)
end

function EntityModule:UpdateEntityBuffData(m)
	local entity = self:GetEntity(m.EntityUID)
	if entity == nil or entity.buffLC == nil then
		return
	end

	entity.buffLC:SetBuffData(m.BuffID, m.RemainingTime, m.BuffLv, false)
end

-- 生物播放光效
function EntityModule:SC_Entity_CreatureShowFx(m)
	local entity = self:GetEntity(m.EntityUID)
	if entity then
		self:ShowFx(entity, m.FxID)
	else
		warn('SC_Entity_CreatureShowFx no entity ', m.EntityUID, m.FxID)
	end
end

-- 生物播放光效
function EntityModule:ShowFx(entity, fxID, loc, angle)
	if not fxID or fxID <= 0 then
		return nil
	end
	local effectConfig = Schemes.FxConfig:Get(fxID)
	if not effectConfig then
		return nil
	end

	local poolType = effectConfig.PoolType
	local effectObj = nil
	if entity then
		effectObj = FxManager.CreateFx(effectConfig.FxName, poolType, entity.modelTrans.localPosition, 0,
			entity.modelTrans, effectConfig.AttachPoint)
	else
		effectObj = FxManager.CreateFx(effectConfig.FxName, poolType, loc, angle, nil, effectConfig.AttachPoint)
	end
	if effectObj and effectConfig.PoolType == 2 then
		effectObj.transform.localScale = cacheVector3:Set(effectConfig.FloatParam1, 1.0, effectConfig.FloatParam2)
	end
	return effectObj
end

-- 生物播放声音
function EntityModule:SC_Entity_CreaturePlaySound(m)
	local entity = self:GetEntity(m.EntityUID)
	if entity then
		local delayTime = m.DelayTime / 1000
		local asm = entity.aniComp
		if asm then
			local soundScheme = Schemes.Sound:Get(m.SoundID)
			if soundScheme == nil then
				warn('SC_Entity_CreaturePlaySound 未配置的声音ID ', m.SoundID)
				return
			end
			if SoundManager:GetXmSoundValue() == 0 then
				asm:PlaySound(soundScheme.File, delayTime)
			end
		else
			SoundManager:PlaySound(m.SoundID, delayTime)
		end
	else
		warn('SC_Entity_CreaturePlaySound no entity ', m.EntityUID, m.SoundID)
	end
end

-- 初始化
function EntityModule:Init()
	self.modelRoot = GameObject.New()
	self.modelRoot.name = 'ModelRoot'
	self.modelRootTrans = self.modelRoot.transform
	UnityEngine.Object.DontDestroyOnLoad(self.modelRoot)

	self.modelContainer = GameObject.New()
	self.modelContainer.name = 'ModelContainer'
	self.modelContainerTrans = self.modelContainer.transform
	self.modelContainerTrans:SetParent(self.modelRootTrans, false)

	self.modelUIContainer = GameObject.New()
	self.modelUIContainer.name = 'ModelUIContainer'
	local modelCanvas = self.modelUIContainer:AddComponent(typeof(UnityEngine.Canvas))
	modelCanvas.renderMode = UnityEngine.RenderMode.WorldSpace
	self.modelCanvas = modelCanvas
	self.modelUIContainer.layer = Layer.SceneUI
	self.modelUIContainerTrans = self.modelUIContainer.transform
	self.modelUIContainerTrans:SetParent(self.modelRootTrans, false)

	self.modelCacheContainer = GameObject.New()
	self.modelCacheContainer.name = 'ModelCacheContainer'
	self.modelCacheContainerTrans = self.modelCacheContainer.transform
	self.modelCacheContainerTrans:SetParent(self.modelRootTrans, false)

	self.fxContainer = GameObject.New()
	self.fxContainer.name = 'FxContainer'
	self.fxContainerTrans = self.fxContainer.transform
	self.fxContainerTrans:SetParent(self.modelRootTrans, false)
	FxManager.fxContainer = self.fxContainerTrans
	-- c#相关的
	local luaToCshapeManager = GameObject.New()
	luaToCshapeManager.name = 'luaToCshapeManager'
	luaToCshapeManager.transform:SetParent(self.modelRootTrans, false)
	self.luaToCshape = luaToCshapeManager:AddComponent(typeof(LuaToCshapeManager))
end

-- 关卡载入完成调用
function EntityModule:OnLevelWasLoaded()
	if self.modelCanvas then
		self.modelCanvas.worldCamera = SceneManager:GetMainCamera()
	end
end

function EntityModule.SC_Entity_SyncTaskData(m)
	local masterUID = m.MasterUID
	local e = EntityModule.entities[masterUID]

	if not e then return end
	if e == EntityModule.hero then
		EntityModule.hero.heroTaskLC:SetEscortUID(m.EscortUID)
		EntityModule.hero.heroTaskLC:SetOnEscort(m.OnEscort)
	else
		e.OnEscort = m.OnEscort
		e.EscortUID = m.EscortUID
		local enable = e.OnEscort == 0
		e.prefab:SetActive(enable)
		if enable then
			e.headNameComp:Enable()
			e.hpComp:Enable()
		else
			e.headNameComp:Disable()
			e.hpComp:Disable()
		end
	end
end

function EntityModule.SC_Entity_SyncTaskItem(m)
	if EntityModule.hero then
		-- EntityModule.hero.heroTaskLC.Update(m)
		for k, v in ipairs(m.ItemList) do
			local taskItem = Schemes.Task:Get(v.TaskID)
			if not taskItem then
				print('SC_Entity_SyncTaskItem 找不到任务配置', v.TaskID)
			elseif taskItem.StateIcon ~= 0 then
				local state = 1
				if v.Status == TASK_STATUS.TASK_STATUS_ACCEPTED then
					state = 0
				end
				local data = { TaskID = v.TaskID, State = state }
				EntityModule.hero.taskStateListLC.UpdataDate(data)
				EntityModule.hero.headNameComp:UpdateIcon()
			end
		end
	else
		local t = {}
		t.d = m
		t.funcName = '同步任务数据'
		t.LateUpdate = EntityModule.SC_Entity_SyncTaskItem
		EntityModule.lateUpdateQueue:pushLast(t)
	end
	-- body
end

function EntityModule.SC_Entity_Sync_TaskTimeTalk(m)
	if not EntityModule.hero then
		return
	end

	local key = m.TaskID * 256 + m.BranchID
	local taskItem = EntityModule.hero.heroTaskLC.idToItem[key]
	if not taskItem then
		return
	end

	if m.Result == 0 then
		-- 读条成功
		if taskItem.taskScheme.Parameter5 > 0 then
			GuideManager:TaskCameraShow(taskItem.taskScheme.Parameter5)
		end
	else
		-- 读条失败
		if taskItem.taskScheme.Parameter7 ~= '0' then
			HelperL.AddAMessageTip(taskItem.taskScheme.Parameter7)
		end
		local wayNpcID = EntityModule.hero.heroTaskLC.GetNpcID(taskItem.taskScheme.WayNPC)
		if NPCManager.lastTrigNpcID == wayNpcID then
			-- 自动继续读条
			EntityModule.hero.navigationLG:NavigateToNPC(wayNpcID)
		end
	end
end

--获取邀请列表
function EntityModule.CS_INVITE_GETINVITELIST()
	Premier.Instance:GetNetwork():SendFromLua(
		ENDPOINT.ENDPOINT_GAMECLIENT,
		ENDPOINT.ENDPOINT_GAMESERVER,
		MSG_MODULEID.MSG_MODULEID_GAME,
		GameMessage_pb.MSG_INVITE_GETINVITELIST,
		''
	)
end

--获取邀请码
function EntityModule.SC_INVITE_LOGINGETCODE(m)
	print('---------获取邀请码--------------', m.Code)
	--邀请码
	EntityModule.InvitationCode = m.Code
	if m.Code and m.Code ~= '' then
		PlayerPrefs.SetString("SC_INVITE_LOGINGETCODE_" .. EntityModule.hero.uid, m.Code)
	end
end

--获取邀请码
function EntityModule.GetInvitationCode()
	if not EntityModule.InvitationCode then
		return PlayerPrefs.GetString("SC_INVITE_LOGINGETCODE_" .. EntityModule.hero.uid, "")
	end
	return EntityModule.InvitationCode
end

---@class InviteData 邀请玩家数据
---@field ActorID integer
---@field ActorName string
---@field ActorLevel integer

---@type InviteData[]
EntityModule.InviteDataList = {}

--获取邀请玩家列表通知
function EntityModule.SC_INVITE_GETINVITELIST(m)
	EntityModule.InviteDataList = m.Info
	EventManager:Fire(EventID.UpdateInviteDataList, m.Info)
end

--初始化数据
function EntityModule.InitData()
	--获取邀请列表
	EntityModule.CS_INVITE_GETINVITELIST()
	--初始化排行榜数据
	RankingModule.Init()
	-- 添加角色数据(单例)
	EntityModule.ActorDataMgr = Helper.GetOrAddActorDataMgr();
end

--宝箱装备等级经验
EntityModule.BoxEquipLevelEXP = 0

---@class VerifyInfo
---@field Verified boolean 认证成功
---@field IdNoID integer 认证序号
---@field Age integer 年龄
---@field ChargeWeek integer 每周充值金额
---@field ChargeMonth integer 每月充值金额

---@type VerifyInfo
EntityModule.VerifyInfo = {
	Verified = false,
	IdNoID = 0,
	Age = 0,
	ChargeWeek = 0,
	ChargeMonth = 0
}

-- 获取用户实名认证信息
function EntityModule.GetUserIdNo()
	local form = {}
	form["ApiVersion"] = 'v1'
	form["UserID"] = LoginModule:GetUserID()
	HttpReques.SendRequest(ERequestID.GetUserIdNo, form)
end

---初始化认证信息
---@param info VerifyInfo
---@return boolean 是否成年
function EntityModule.InitVerifyInfo(info)
	if info and info.Verified then
		info.ChargeWeek         = info.ChargeWeek or 0
		info.ChargeMonth        = info.ChargeMonth or 0
		EntityModule.VerifyInfo = info
		if info.Age >= 18 then
			return true
		end
	end
	return false
end

---登入限制信息
---@class LoginInfo
---@field CanLogin boolean 可以登录
---@field EndTime integer 结束时间

---@type LoginInfo
EntityModule.LoginInfo = nil

---初始化登入限制信息
---@param info LoginInfo
function EntityModule.InitLoginInfo(info)
	if info then
		EntityModule.LoginInfo = info
		if EntityModule.onlineExamineTimer ~= nil then
			EntityModule.onlineExamineTimer:Stop()
			EntityModule.onlineExamineTimer = nil
		end
		EntityModule.onlineExamineTimer = Timer.New(EntityModule.OnlineExamine, 1, -1)
		EntityModule.onlineExamineTimer:Start()
	end
end

---在线时间检查
function EntityModule.OnlineExamine()
	local luaID2 = ('HelperL')
	local nowSysTime = HelperL.GetServerTime()
	if nowSysTime >= EntityModule.LoginInfo.EndTime then
		if EntityModule.onlineExamineTimer ~= nil then
			EntityModule.onlineExamineTimer:Stop()
			EntityModule.onlineExamineTimer = nil
		end

		HelperL.ScreenTime(function(isCheck)
		end, GetGameText(luaID2, 49) .. GetGameText(luaID2, 53),true)
	end
end

EventManager:Subscribe(EventID.OnHeroCreate, EntityModule.InitData)

Premier.Instance:RegisterLuaModule(MSG_MODULEID.MSG_MODULEID_ENTITY, 'EntityModule.Handle')

EntityModule:Init()
