-- 物品篮子模块
require "SkepMessage_pb"

local luaID = ('SkepModule')
---@class SkepModule
SkepModule = {}
---@type SkepBase[]
SkepModule.skeps = {}

local lateSyncQueue = {}
function SkepModule.Handle(action, data)
	local m
	if action == SkepMessage_pb.MSG_SKEP_CREATE then -- 创建物品篮
		m = SkepMessage_pb.SC_Skep_CreateSkep()
		m:ParseFromString(data)
		SkepModule.skeps[m.SkepID] = nil
		SkepModule.skeps[m.SkepID] = SkepModule.SC_Skep_CreateSkep(m)
		if lateSyncQueue[m.SkepUID] and lateSyncQueue[m.SkepUID]:Count() > 0 then
			while true do
				local t = lateSyncQueue[m.SkepUID]:popFirst()
				if not t then break end
				t.LateSync(t.d)
			end
		end
	elseif action == SkepMessage_pb.MSG_SKEP_DESTORY then -- 销毁物品篮
		m = SkepMessage_pb.SC_Skep_DestorySkep()
		m:ParseFromString(data)
		SkepModule.SC_Skep_DestorySkep(m)
	elseif action == SkepMessage_pb.MSG_SKEP_SYNCPLACE then -- 同步物品篮
		m = SkepMessage_pb.SC_Skep_SyncPlace()
		m:ParseFromString(data)
		SkepModule.SC_Skep_SyncPlace(m)
	elseif action == SkepMessage_pb.MSG_SKEP_SYNCSIZE then -- 同步物品篮大小
		m = SkepMessage_pb.SC_Skep_SyncSize()
		m:ParseFromString(data)
		SkepModule.SC_Skep_SyncSize(m)
	elseif action == SkepMessage_pb.MSG_SKEP_USEGOODS then -- 使用物品
		m = SkepMessage_pb.SC_Skep_UseGoods()
		m:ParseFromString(data)
		-- ResultCode:DefaultShowResultCode(m.Result)
		EventManager:Fire(EventID.MSG_SKEP_USEGOODS, m.Result)
	elseif action == SkepMessage_pb.MSG_SKEP_SELLGOODS then -- 出售物品
		m = SkepMessage_pb.SC_Skep_SellGoods()
		m:ParseFromString(data)
	end
end

local GetSkepByUID = function(uid)
	for k, v in pairs(SkepModule.skeps) do
		if v.skepUID == uid then
			return v
		end
	end
end

--------------------------------------------------------------------
-- 同步篮子大小
--------------------------------------------------------------------
function SkepModule.SC_Skep_SyncSize(m)
	local curSkep = GetSkepByUID(m.SkepUID)
	if curSkep then
		curSkep.skepMaxsize = m.MaxSize
		curSkep.indexMaxsize = m.MaxSize - 1
	else
		local t = {}
		t.d = m
		t.LateSync = SkepModule.SC_Skep_SyncSize
		lateSyncQueue[m.SkepUID] = lateSyncQueue[m.SkepUID] or HelperL.NewQueue():New()
		lateSyncQueue[m.SkepUID]:pushLast(t)
	end
end

--------------------------------------------------------------------
-- 同步篮子
--------------------------------------------------------------------
function SkepModule.SC_Skep_SyncPlace(m)
	---@type SkepBase
	local curSkep = GetSkepByUID(m.SkepUID)
	if curSkep then
		curSkep:SyncSkepGoods(m)
	else
		local t = {}
		t.d = m
		t.LateSync = SkepModule.SC_Skep_SyncPlace
		lateSyncQueue[m.SkepUID] = lateSyncQueue[m.SkepUID] or HelperL.NewQueue():New()
		lateSyncQueue[m.SkepUID]:pushLast(t)
	end

	EventManager:Fire(EventID.SyncPlace)
end

--------------------------------------------------------------------
-- 销毁篮子
--------------------------------------------------------------------
function SkepModule.SC_Skep_DestorySkep(m)
	SkepModule.skeps[GetSkepByUID(m.SkepUID).skepID] = nil
end

--------------------------------------------------------------------
-- 创建篮子
--------------------------------------------------------------------
function SkepModule.SC_Skep_CreateSkep(m)
	return CreateGoodsSkep(m)
end

--------------------------------------------------------------------
-- 获取篮子
--------------------------------------------------------------------
function SkepModule:GetSkepByID(skepID)
	return self.skeps[skepID]
end

--------------------------------------------------------------------
-- 使用物品
--------------------------------------------------------------------
function SkepModule:UseGood(entity, target)
	local useNum = 0
	if not entity then return end
	local itemID = entity:GetProperty(ENTITY_FIELD.ENTITY_FIELD_ID)
	local cfg = Schemes.Medicament:Get(itemID)
	if not cfg then return end
	local goodNum = entity:GetProperty(GOODS_FIELD.GOODS_FIELD_QTY)
	if cfg.UseMenu == 0 then --0不能使用
		HelperL.ShowMessage(TipType.FlowText, GetGameText(luaID, 1))
		return
	elseif cfg.UseMenu == 1 then --1用1个
		useNum = 1
	elseif cfg.UseMenu == 2 then --2全部使用
		useNum = goodNum
	elseif cfg.UseMenu == 3 then --3弹出选择使用数量UI
		if goodNum > 1 then
			HelperL.ShowSelectUseNum(goodNum, entity, function(useNum)
				if useNum < 0 then
					useNum = 0
				elseif useNum > goodNum then
					useNum = goodNum
				end
				self:ExecuteUseGood(entity, useNum, target)
			end)
			return
		else
			useNum = goodNum
		end
	else
		return
	end
	self:ExecuteUseGood(entity, useNum, target)
end

--------------------------------------------------------------------
-- 自动使用物品
--------------------------------------------------------------------
function SkepModule:AutoUseGoods(entity, target)
	if not entity then return end
	local itemID = entity:GetProperty(ENTITY_FIELD.ENTITY_FIELD_ID)
	local cfg = Schemes.Medicament:Get(itemID)
	if not cfg then return end
	if cfg.UseMenu ~= 4 then return end --自动使用物品
	local useNum = entity:GetProperty(GOODS_FIELD.GOODS_FIELD_QTY)
	self:ExecuteUseGood(entity, useNum, target)
end

--------------------------------------------------------------------
-- 请求使用物品
--------------------------------------------------------------------
function SkepModule:ExecuteUseGood(entity, useNum, target)
	if not entity then return end

	local itemID = entity:GetProperty(ENTITY_FIELD.ENTITY_FIELD_ID)
	local cfg = Schemes.Medicament:Get(itemID)
	if not cfg then return end

	local skepID = entity:GetProperty(GOODS_FIELD.GOODS_FIELD_SKEPID)
	local goodSkep = self:GetSkepByID(skepID)
	if not goodSkep then return end

	local skepUID = goodSkep.skepUID
	local m = SkepMessage_pb.CS_Skep_UseGoods()
	m.SkepUID = skepUID
	m.GoodsUID = entity.uid
	m.Target = target or 0
	m.UseNum = useNum or 0
	Premier.Instance:GetNetwork():SendFromLua(
		ENDPOINT.ENDPOINT_GAMECLIENT,
		ENDPOINT.ENDPOINT_GAMESERVER,
		MSG_MODULEID.MSG_MODULEID_SKEP,
		SkepMessage_pb.MSG_SKEP_USEGOODS,
		m:SerializeToString()
	)
end

--------------------------------------------------------------------
-- 物品属性变动处理
--------------------------------------------------------------------
function SkepModule.OnGoodsPropChange(uid, propID, oldValue, newValue)
	local self = SkepModule
	if propID == GOODS_FIELD.GOODS_FIELD_QTY then
		local entity = EntityModule:GetEntity(uid)
		if entity then
			local skepID = entity:GetProperty(GOODS_FIELD.GOODS_FIELD_SKEPID)
			if self.skeps[skepID] then
				self.skeps[skepID]:OnGoodsPropChange(entity, propID, newValue - oldValue)
			end
		end
	end
end

-- 装备篮子
--(只有火之装备分篮子 除火之装备外其他装备都在流派1篮子里
--所以固定使用流派1篮子,需要使用对应流派火之装备数据用下面的GetEquipByGenreAndEquipType)
function SkepModule.GetEquipSkep()
	local genre = 1
	if EntityModule.hero then
		genre = EntityModule.hero:GetProperty(PLAYER_FIELD.PLAYER_FIELD_GENRE)
	end
	--- 遍历篮子
	return SkepModule.skeps[genre]
end

--------------------------------------------------------------------
-- 请求穿上装备
--------------------------------------------------------------------
function SkepModule.PutOnEquipment(uid)
	local entity = EntityModule:GetEntity(uid)
	if not entity then
		warn("请求穿上装备失败 没有找到实体", uid)
		return
	end

	local equipID = entity:GetProperty(ENTITY_FIELD.ENTITY_FIELD_ID)
	local goodSkep = SkepModule:GetGoodsIdBySkep(equipID)
	if not goodSkep then return end
	local m = SkepMessage_pb.CS_Skep_UseGoods()
	m.SkepUID = goodSkep.skepUID
	m.GoodsUID = entity.uid
	m.UseNum = 1
	Premier.Instance:GetNetwork():SendFromLua(
		ENDPOINT.ENDPOINT_GAMECLIENT,
		ENDPOINT.ENDPOINT_GAMESERVER,
		MSG_MODULEID.MSG_MODULEID_SKEP,
		SkepMessage_pb.MSG_SKEP_USEGOODS,
		m:SerializeToString()
	)
end

--------------------------------------------------------------------
-- 出售商品
--------------------------------------------------------------------
function SkepModule:SellGoods(entity)
	local skepID = entity:GetProperty(GOODS_FIELD.GOODS_FIELD_SKEPID)
	local goodSkep = self:GetSkepByID(skepID)
	if not goodSkep then return end

	local skepUID = goodSkep.skepUID
	local m = SkepMessage_pb.CS_Skep_SellGoods()
	m.SkepUID = skepUID
	m.GoodsUID = entity.uid

	Premier.Instance:GetNetwork():SendFromLua(
		ENDPOINT.ENDPOINT_GAMECLIENT,
		ENDPOINT.ENDPOINT_GAMESERVER,
		MSG_MODULEID.MSG_MODULEID_SKEP,
		SkepMessage_pb.MSG_SKEP_SELLGOODS,
		m:SerializeToString()
	)
end

--------------------------------------------------------------------
-- 获取菌落装备篮子
--------------------------------------------------------------------
function SkepModule.GetFateEquipmentSkep()
	local genre = EntityModule.hero:GetProperty(PLAYER_FIELD.PLAYER_FIELD_GENRE)
	if genre == 1 then
		return SkepModule.skeps[SKEPID.SKEPID_WINGEQUIP]
	elseif genre == 2 then
		return SkepModule.skeps[SKEPID.SKEPID_WINGEQUIP2]
	elseif genre == 3 then
		return SkepModule.skeps[SKEPID.SKEPID_WINGEQUIP3]
	elseif genre == 4 then
		return SkepModule.skeps[SKEPID.SKEPID_WINGEQUIP4]
	end
	--默认职业1
	return SkepModule.skeps[SKEPID.SKEPID_WINGEQUIP]
end

--------------------------------------------------------------------
-- 通过物品ID获取篮子
--------------------------------------------------------------------
function SkepModule:GetGoodsIdBySkep(ID)
	ID = tonumber(ID) or 0
	if ID <= 0 then
		return nil
	end
	--获取物品表格数据
	local item = Schemes.Medicament:Get(ID)
	if ID >= DEFINE.MIN_EQUIPMENT_ID and ID <= DEFINE.MAX_EQUIPMENT_ID then
		item = Schemes.Equipment:Get(ID)
	end
	if item then
		return SkepModule.skeps[item.PacketID]
	end
	return nil
end

--------------------------------------------------------------------
-- 获取背包物品数量
--------------------------------------------------------------------
function SkepModule:GetGoodsCount(ID)
	ID = tonumber(ID) or 0
	if ID <= 0 then
		return 0
	end
	--判断是虚拟物品
	if ID <= VIRTUAL_GOODS_MAX then
		if EntityModule.hero then
			local count = EntityModule.hero:GetProperty(VIRTUAL_GOODS[ID]) or 0
			--修为（银币）有额外加成
			if ID == 4 then
				local cardSkep = SkepModule:GetSkepByID(SKEPID.SKEPID_MONSTERCARD)
				if cardSkep then
					local entity = cardSkep:GetEntityByGoodsID(600109)
					if entity then
						local exp = entity:GetProperty(EQUIPMENT_FIELD.EQUIPMENT_FIELD_SMELTEXP) or 0
						count = exp * 300000000 + count
					end
				end
			end
			return count
		end
	else
		--获取背包篮子
		local skeps = self:GetGoodsIdBySkep(ID)
		if skeps then
			--获取数量
			return skeps:FastCount(ID)
		end
	end
	return 0
end

--------------------------------------------------------------------
---获取坐骑装备实体、穿戴状态
--------------------------------------------------------------------
function SkepModule:GetHorseEntity(equipID)
	local isEquip = false
	local entity = self:GetSkepByID(SKEPID.SKEPID_MOUNTEQUIPPACKET):GetEntityByGoodsID(equipID)
	--获取背包装备，获取不到说明以佩戴
	if not entity then
		isEquip = true
		entity = self:GetSkepByID(SKEPID.SKEPID_MOUNTEQUIP):GetEntityByGoodsID(equipID)
	end
	return entity, isEquip
end

Premier.Instance:RegisterLuaModule(MSG_MODULEID.MSG_MODULEID_SKEP, 'SkepModule.Handle')
EventManager:Subscribe(EventID.OnGoodsPropChange, SkepModule.OnGoodsPropChange)
