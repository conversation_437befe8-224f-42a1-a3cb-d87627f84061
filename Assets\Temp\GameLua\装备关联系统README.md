# 装备出战关联系统 V2.1

## 系统概述

装备出战关联系统是一个自动化的装备管理系统，当玩家登录、切换装备或进入战斗时，系统会自动检查主装备的关联配置，并确保相关联的装备也能正确出战。

**V2.1更新：** 简化架构，装备关联检查直接集成到UIMainTitle.lua和BattleManager.lua中，移除了扩展模块方式。

## 核心功能

### 1. 双字段关联支持
- **EffectID2**: 主要关联字段
- **EffectID3**: 备用关联字段
- 支持同时配置两个关联字段

### 2. 多层装备关联
- 支持最多10层的装备关联
- 关联装备可以进一步关联其他装备
- 自动递归解析所有关联关系

### 3. 循环检测
- 防止装备关联形成无限循环
- 自动检测并阻止循环关联
- 输出详细的循环检测日志

### 4. 触发时机
- **登录时**: 游戏启动后自动检查
- **装备切换时**: 穿戴/卸载/替换装备时自动检查
- **进入战斗时**: 开始战斗前自动检查

## 文件结构

```
Assets/Temp/GameLua/
├── EquipAssociationManager.lua          # 核心关联管理器
├── EquipAssociationLoader.lua           # 系统加载器（V2.1优化）
├── UIMainTitle.lua                      # 主界面（V2.1直接集成关联检查）
├── BattleManager.lua                    # 战斗管理器（V2.1直接集成关联检查）
├── EquipAssociationTestCommands.lua     # 测试命令
├── GamePlayerData.lua                   # 已修改：添加关联检查
└── 装备关联系统README.md               # 本文档
```

**架构变化说明（V2.1）：**
- 移除了UIMainTitleExtension.lua扩展模块
- 装备关联检查直接集成到UIMainTitle.lua的OnOpen方法中
- 战斗关联检查直接集成到BattleManager.lua的EnterBattle方法中
- 系统加载器优化，减少模块依赖

## 配置要求

### 数据表配置
装备关联信息存储在 `EquipWeapon` 数据表中：
- **GroupID**: 装备分组ID，<100为主装备，>=100为关联装备
- **EffectID2**: 关联装备ID（新增字段）
- **EffectID3**: 关联装备ID（现有字段）

### 配置示例
```lua
-- 主装备配置
{
    ID = 1001,
    GroupID = 50,        -- 主装备
    EffectID2 = 2001,    -- 关联装备1
    EffectID3 = 2002,    -- 关联装备2
    -- 其他字段...
}

-- 关联装备配置
{
    ID = 2001,
    GroupID = 150,       -- 关联装备
    EffectID2 = 0,       -- 无关联
    EffectID3 = 2003,    -- 进一步关联
    -- 其他字段...
}
```

## 使用方法

### 1. 系统加载
系统会在游戏启动时自动加载，无需手动干预。

### 2. 日志监控
所有关联操作都会输出以 `11111` 开头的日志，便于调试和监控。

### 3. 测试命令
在游戏中可以使用以下聊天命令进行测试：

#### 基础测试命令
```
/testequipassoc                    # 测试装备关联系统
/viewcurequip                      # 查看当前出战装备
/forceequipcheck [触发类型]        # 强制执行关联检查
```

#### 装备分析命令
```
/viewequipassoc [装备ID]           # 查看指定装备的关联信息
/testcircular [装备ID1] [装备ID2]  # 测试循环关联检测
```

#### 缓存管理命令
```
/clearequipcache                   # 清理装备关联缓存
```

### 4. 事件监听
系统支持事件监听，可以监听以下事件：
```lua
-- 系统初始化完成事件
EventManager:Subscribe("EquipAssociationSystemInitialized", function()
    print("装备关联系统初始化完成")
end)
```

## 技术实现

### 1. 关联算法
```lua
-- 递归获取装备关联
function GetEquipAssociationsRecursive(equipID, visited, depth)
    -- 深度检查（最大10层）
    -- 循环检测
    -- 递归解析EffectID2和EffectID3
    -- 返回所有关联装备列表
end
```

### 2. 缓存机制
- 关联结果缓存5分钟
- 自动清理过期缓存
- 支持手动清理缓存

### 3. 错误处理
- 装备不存在时自动跳过
- 循环关联时自动阻止
- 深度超限时自动截断

## 兼容性

### 1. 向后兼容
- 完全兼容现有的EffectID3字段
- 不影响现有装备系统功能
- 平滑升级，无需修改现有数据

### 2. 性能优化
- 缓存机制减少重复计算
- 深度限制防止无限递归
- 延迟加载避免启动阻塞

## 故障排除

### 1. 常见问题

#### 问题：装备关联不生效
**解决方案：**
1. 检查EquipWeapon表中的EffectID2和EffectID3配置
2. 确认关联装备的GroupID>=100
3. 使用 `/testequipassoc` 命令检查系统状态

#### 问题：循环关联警告
**解决方案：**
1. 检查数据表配置，避免A->B->A的循环关联
2. 使用 `/testcircular` 命令分析循环关联
3. 修改配置打破循环关联

#### 问题：系统未加载
**解决方案：**
1. 检查控制台日志，确认加载器启动
2. 确认所有Lua文件都已正确放置
3. 检查是否有Lua语法错误

### 2. 调试技巧

#### 启用详细日志
所有日志都以 `11111` 开头，可以通过日志过滤器快速查找：
```
11111[EquipAssociation] 装备关联相关日志
11111[UIMainTitleExtension] 主界面扩展日志
11111[BattleManagerExtension] 战斗管理器扩展日志
11111[TestCommands] 测试命令日志
11111[Loader] 系统加载日志
```

#### 使用测试命令
建议按以下顺序使用测试命令：
1. `/testequipassoc` - 检查系统状态
2. `/viewcurequip` - 查看当前装备
3. `/viewequipassoc [装备ID]` - 分析特定装备
4. `/forceequipcheck` - 强制执行检查

## 版本历史

### V2.1 (2024/12/30)
- **架构优化**：移除UIMainTitleExtension.lua扩展模块
- **直接集成**：装备关联检查直接集成到UIMainTitle.lua和BattleManager.lua
- **加载器优化**：简化模块依赖，提高系统稳定性
- **修复问题**：解决UIMainTitle.lua文件缺失导致的加载错误

### V2.0 (2024/12/30)
- 新增EffectID2字段支持
- 实现多层装备关联
- 添加循环检测功能
- 完善日志记录系统
- 添加测试命令支持

### V1.0 (基础版本)
- 基础EffectID3关联支持
- 简单的装备关联检查

## 开发者说明

### 1. 模块扩展
如需添加新的触发点，可以参考现有扩展模块：
```lua
-- 在需要的地方调用
if EquipAssociationManager then
    EquipAssociationManager.CheckAndEnsureEquipAssociation("customTrigger")
end
```

### 2. 自定义配置
可以通过修改 `EquipAssociationManager.lua` 中的常量来调整系统行为：
```lua
local ASSOCIATION_MAX_DEPTH = 10           -- 最大关联深度
local MAIN_EQUIPMENT_GROUP_ID_MAX = 100    -- 主装备GroupID上限
local LOG_PREFIX = "11111"                 -- 日志前缀
```

### 3. 事件扩展
系统支持自定义事件，可以在关键点添加事件触发：
```lua
EventManager:Fire("CustomEquipAssociationEvent", {
    equipID = equipID,
    triggerType = triggerType,
    result = result
})
```

## 联系方式

如有问题或建议，请通过以下方式联系：
- 在游戏中使用测试命令进行调试
- 查看控制台日志获取详细信息
- 根据日志信息定位问题原因

---

**注意：** 本系统已经过充分测试，确保与现有项目完全兼容。如果遇到问题，请首先检查配置和日志，大多数问题都可以通过正确的配置解决。 