﻿using System.Collections.Generic;
using System.Linq;

using Apq.ChangeBubbling;

using CsvTables;

using UnityEngine;

using ViewModel;

using X.PB;

namespace View
{
    /// <summary>
    /// 关卡管理器
    /// </summary>
    public class StageMgr : MonoBehaviour
    {
        /// <summary>
        /// 当前关卡的配置
        /// </summary>
        public BubblingList<CatMainStage.Item> CsvRow_Stage { get; } = new(nameof(CsvRow_Stage));
        
        /// <summary>
        /// 当前关卡的所有回合 的配置
        /// </summary>
        public MissionInfo.Item CsvRow_Mission { get; protected set; }

        /// <summary>
        /// 当前关卡的所有回合 的奖励配置
        /// </summary>
        public Dictionary<int, MissionWaveInfoItem> Dic_Stage { get; protected set; }
        
        /// <summary>
        /// 获取当前关卡类型
        /// </summary>
        public int StageType => CsvRow_Stage.Value.FrontType;

        /// <summary>
        /// 获取当前关卡等级
        /// </summary>
        public int StageLvl => CsvRow_Stage.Value.Id;
        
        /// <summary>
        /// 当前关卡中 刷新随机属性的 剩余次数
        /// </summary>
        public int SkillRefreshTimes { get; set; }

        /// <summary>
        /// 当前关卡中 随机属性全都要的 剩余次数
        /// </summary>
        public int SkillAllGetNum { get; set; }

        public void OnDestroy()
        {
            CsvRow_Stage.Changed -= CsvRow_Stage_OnChanged;
        }

        public void Start()
        {
            CsvRow_Stage.Changed += CsvRow_Stage_OnChanged;
        }

        private void CsvRow_Stage_OnChanged(ChangeEventArgs e)
        {
            {
                var ary = CsvRow_Stage.Value.MapPos.Split(';');
                SkillRefreshTimes = int.Parse(ary[3]);
                SkillAllGetNum = int.Parse(ary[2]);
            }
            
            CsvRow_Mission = SingletonMgr.Instance.CsvLoaderMgr.GetOrAddLoader<MissionInfoCsv>()
                .Dic[int.Parse(CsvRow_Stage.Value.Map)];
            Dic_Stage = CsvRow_Mission.WaveInfo.Split('|').Select(x =>
            {
                var ary = x.Split(';').Select(s => int.TryParse(s, out var d) ? d : 0).ToList();
                return new MissionWaveInfoItem
                {
                    RoundNo = ary[0], Coins = ary[1], BattleBrushEnemyId = ary[2], MapId = ary[3]
                };
            }).ToDictionary(x => x.RoundNo, x => x);
        }

        /// <summary>
        /// 设置当前关卡
        /// </summary>
        public void SetStage(int stageLvl, int stageType)
        {
            CsvRow_Stage.Value = SingletonMgr.Instance.CsvLoaderMgr.GetOrAddLoader<CatMainStageCsv>().Dic[stageLvl];
        }
    }
}