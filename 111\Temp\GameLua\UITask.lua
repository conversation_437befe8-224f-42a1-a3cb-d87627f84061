--[[
   created:	2018/2/26
   author :	松
   purpose:	挑战boss界面
--]]
local luaID = ('UITask')

local UITask = { }

function UITask.Awake( )
    local trans = UITask.gameObject.transform
    UITask.spTitle = trans:Find("CommonBgUI/CommonBG/TopBG/TitleName").gameObject
    UITask.spriteTitle = trans:Find("CommonBgUI/CommonBG/TopBG/TitleSprit").gameObject
    UITask.closeBtn = trans:Find("CommonBgUI/CommonBG/TopBG/Close").gameObject
    UITask.typeOptionTab = trans:Find('Original/Tab/ScrollPanel').gameObject
    UITask.calendarBtn = trans:Find("Original/Tab/CalendarBtn").gameObject
    UITask.acticityBtn = trans:Find("Original/Activity").gameObject
    UITask.VIPBtn = trans:Find("Original/VIP").gameObject
    UITask.UnderGroundBtn = trans:Find("Original/UnderGround").gameObject
    UITask.actInactiveSprite = trans:Find("Original/Activity/inactiveSprite").gameObject
    UITask.actInactiveLable = trans:Find("Original/Activity/inactiveSprite/Label")
    UITask.actActiveSprite = trans:Find("Original/Activity/activeSprite").gameObject
    UITask.actActiveLable = trans:Find("Original/Activity/activeSprite/Label")
    --世界
    UITask.worldBossBtn = trans:Find("Original/worldboss").gameObject
    UITask.actInWorldSprite = trans:Find("Original/worldboss/inactiveSprite").gameObject
    UITask.actInWorldLable = trans:Find("Original/worldboss/inactiveSprite/Label")
    UITask.actWorldSprite = trans:Find("Original/worldboss/activeSprite").gameObject
    UITask.actWorldLable = trans:Find("Original/worldboss/activeSprite/Label")
    UITask.actWorldRedSpot = trans:Find("Original/worldboss/redspot").gameObject
   --专属
    UITask.VIPInactiveSprite = trans:Find("Original/VIP/inactiveSprite").gameObject
    UITask.VIPInactiveLable = trans:Find("Original/VIP/inactiveSprite/Label")
    UITask.VIPActiveSprite = trans:Find("Original/VIP/activeSprit").gameObject
    UITask.VIPActiveLable = trans:Find("Original/VIP/activeSprit/Label")
    UITask.VIPActiveRedSpot = trans:Find("Original/VIP/redspot").gameObject
    --枭雄
    UITask.UGInactiveSprite = trans:Find("Original/UnderGround/inactiveSprite").gameObject
    UITask.UGInactiveLable = trans:Find("Original/UnderGround/inactiveSprite/Label")
    UITask.UGActiveSprite = trans:Find("Original/UnderGround/activeSprit").gameObject
    UITask.UGActiveLable = trans:Find("Original/UnderGround/activeSprit/Label")
    UITask.UGActiveRedSpot = trans:Find("Original/UnderGround/redspot").gameObject
    --个人
    UITask.MyBossBtn = trans:Find("Original/MyBoss").gameObject
    UITask.MyBossInactiveSprite = trans:Find("Original/MyBoss/inactiveSprite").gameObject
    UITask.MyBossInactiveLable = trans:Find("Original/MyBoss/inactiveSprite/Label")
    UITask.MyBossActiveSprite = trans:Find("Original/MyBoss/activeSprit").gameObject
    UITask.MyBossActiveLable = trans:Find("Original/MyBoss/activeSprit/Label")
    UITask.MyBossActiveRedSpot = trans:Find("Original/MyBoss/redspot").gameObject
    --超级
    UITask.SuperBossBtn = trans:Find("Original/SuperBoss").gameObject
    UITask.SuperBossInactiveSprite = trans:Find("Original/SuperBoss/inactiveSprite").gameObject
    UITask.SupreBossInactiveLable = trans:Find("Original/SuperBoss/inactiveSprite/Label")
    UITask.SuperBossActiveSprite = trans:Find("Original/SuperBoss/activeSprit").gameObject
    UITask.SuperBossActiveLable = trans:Find("Original/SuperBoss/activeSprit/Label")

   -- UITask.pendant = trans:Find("Original/Sprite").gameObject
    --预制体字
    UITask.actInactiveLable:GetComponent('Text').text = GetGameText(luaID,12)
    UITask.actActiveLable:GetComponent('Text').text = GetGameText(luaID,13)
    UITask.VIPInactiveLable:GetComponent('Text').text = GetGameText(luaID,7)
    UITask.VIPActiveLable:GetComponent('Text').text = GetGameText(luaID,8)
    UITask.UGInactiveLable:GetComponent('Text').text = GetGameText(luaID,9)
    UITask.UGActiveLable:GetComponent('Text').text = GetGameText(luaID,10)
    UITask.actInWorldLable:GetComponent('Text').text = GetGameText(luaID,14)
    UITask.actWorldLable:GetComponent('Text').text = GetGameText(luaID,15)
    UITask.MyBossInactiveLable:GetComponent('Text').text = GetGameText(luaID, 16)
    UITask.MyBossActiveLable:GetComponent('Text').text = GetGameText(luaID, 17)
    UITask.SupreBossInactiveLable:GetComponent('Text').text = GetGameText(luaID, 19)
    UITask.SuperBossActiveLable:GetComponent('Text').text = GetGameText(luaID, 20)

    HelperL.SetChildLabelText(UITask.calendarBtn.transform,'Label',GetGameText(luaID,4))
    UITask.closeBtn.gameObject:GetComponent("Button").onClick:AddListenerEx(function()
        UITask.Close()
    end)
    UITask.calendarBtn.gameObject:GetComponent("Button").onClick:AddListenerEx(function()
        UITask.OpenCalendarUI()
    end)
    UITask.acticityBtn.gameObject:GetComponent("Button").onClick:AddListenerEx(function()
        UITask.OpenActivityList()
    end)
    UITask.VIPBtn.gameObject:GetComponent("Button").onClick:AddListenerEx(function()
        UITask.OpenVIPList()
    end)
    UITask.UnderGroundBtn.gameObject:GetComponent("Button").onClick:AddListenerEx(function()
        UITask.OpenUnderGroundList()
    end)
    UITask.worldBossBtn.gameObject:GetComponent("Button").onClick:AddListenerEx(function()
        UITask.OpenworldBossBtnList()
    end)
    UITask.MyBossBtn.gameObject:GetComponent("Button").onClick:AddListenerEx(function()
        UITask.OpenMyBossList()
    end)
    UITask.SuperBossBtn.gameObject:GetComponent("Button").onClick:AddListenerEx(function()
        UITask.OpenSuperBossList()
    end)

    UITask.typeOptionList = {}
	UITask.pageList = { }
    UITask.beforeName = ''   
    UITask.OpenActivityList()
    UITask.vipLv = EntityModule.hero.propertyLC:GetProperty(PLAYER_FIELD.PLAYER_FIELD_VIPLEVEL)
    UITask.heroLv = EntityModule.hero.propertyLC:GetProperty(CREATURE_FIELD.CREATURE_FIELD_LEVEL)
    
    UITask.LaternObj = {UITask.acticityBtn,UITask.worldBossBtn,UITask.VIPBtn,UITask.UnderGroundBtn,UITask.MyBossBtn,UITask.SuperBossBtn}
    UITask.levelLimit = {UIManager.guideLvLimitOpen.BossPK,UIManager.guideLvLimitOpen.WorldBoss,UIManager.guideLvLimitOpen.VIPBoss,UIManager.guideLvLimitOpen.UnderGround,UIManager.guideLvLimitOpen.MyBoss,UIManager.guideLvLimitOpen.SuperBoss}
    UITask.SetLatern()
    UITask.OnEnable( )
    MixUICenter.ShowWealthUI()
end
--设置灯笼
function UITask.SetLatern()
    local level = EntityModule.hero.propertyLC:GetProperty(CREATURE_FIELD.CREATURE_FIELD_LEVEL) 
    local index = 0
    for k, v in ipairs(UITask.LaternObj) do
        if level < UITask.levelLimit[k] then
            v:SetActive(false)
        else
            index = index + 1
            v:SetActive(true)
            v.transform.localPosition = Vector3(v.transform.localPosition.x,298 - 97 * index, 0)
        end
    end
    --UITask.pendant.transform.localPosition = Vector3(-522, 308 - 97 * index, 0)
end
--销毁
function UITask.OnDestroy()
    if UITask.LuaFieldBossTask then
        UITask.LuaFieldBossTask.Close()
    end
    if UITask.LuaVIPBossTask then
        UITask.LuaVIPBossTask.Close()
    end
end
--关闭
function UITask.Close( )
    MixUICenter.CloseWealthUI()
	UIManager.Close()
end
--隐藏
function UITask.OnDisable( )
    EventManager:UnSubscribe(EventID.UpdateVIPBtnRedSpot,UITask.UpdateVIPBtnRedSpot)
end
--激活
function UITask.OnEnable( )
    UITask.InitVIPSchemesData()
    UITask.RequestBossData()
    UITask.CheckRedSpot()
    EventManager:Subscribe(EventID.UpdateVIPBtnRedSpot,UITask.UpdateVIPBtnRedSpot)
end
--打开活动分页
function UITask.OpenActivityList()
    UITask.calendarBtn:SetActive(false)
    if  UITask.BeforeActiveSprite == UITask.actActiveSprite then return end
    if not UITask.BeforeActiveSprite then
        UITask.actActiveSprite:SetActive(true)
        UITask.actInactiveSprite:SetActive(false)
        UITask.BeforeActiveSprite = UITask.actActiveSprite
        UITask.BeforeInactiveSprite = UITask.actInactiveSprite
    else

        UITask.BeforeActiveSprite:SetActive(false)
        UITask.BeforeInactiveSprite:SetActive(true)
        UITask.actActiveSprite:SetActive(true)
        UITask.actInactiveSprite:SetActive(false)
        UITask.BeforeActiveSprite = UITask.actActiveSprite
        UITask.BeforeInactiveSprite = UITask.actInactiveSprite
    end
    UITask.CreateTypeOption()
end
--打开世界boss分页
function UITask.OpenworldBossBtnList()
    UITask.calendarBtn:SetActive(false)
    if UITask.BeforeActiveSprite == UITask.actWorldSprite then return end
    UITask.BeforeActiveSprite:SetActive(false)
    UITask.BeforeInactiveSprite:SetActive(true)
    UITask.actWorldSprite:SetActive(true)
    UITask.actInWorldSprite:SetActive(false)
    UITask.BeforeActiveSprite = UITask.actWorldSprite
    UITask.BeforeInactiveSprite = UITask.actInWorldSprite
    UITask.OnWorldBossBtnPageClick() 
    AtlasManager:AsyncGetSprite("gx_00_24", UITask.spriteTitle)
end
--打开vip专属分页
function UITask.OpenVIPList()
    UITask.calendarBtn:SetActive(false)
    if UITask.BeforeActiveSprite == UITask.VIPActiveSprite then return end
    UITask.BeforeActiveSprite:SetActive(false)
    UITask.BeforeInactiveSprite:SetActive(true)
    UITask.VIPActiveSprite:SetActive(true)
    UITask.VIPInactiveSprite:SetActive(false)
    UITask.BeforeActiveSprite = UITask.VIPActiveSprite
    UITask.BeforeInactiveSprite = UITask.VIPInactiveSprite
    UITask.currentVIPSeclect = 0
    UITask.CreateVIPOpion()
    UITask.VIPOpionRedSpot()
    UITask.spTitle:GetComponent('Text').text=GetGameText(luaID,7)
    AtlasManager:AsyncGetSprite("gx_00_25", UITask.spriteTitle)
end
--打开地宫分页
function UITask.OpenUnderGroundList()
    UITask.ClearUnderGroundOpion()
    UITask.calendarBtn:SetActive(false)
    if UITask.BeforeActiveSprite == UITask.UGActiveSprite then return end
    UITask.BeforeActiveSprite:SetActive(false)
    UITask.BeforeInactiveSprite:SetActive(true)
    UITask.UGActiveSprite:SetActive(true)
    UITask.UGInactiveSprite:SetActive(false)
    UITask.BeforeActiveSprite = UITask.UGActiveSprite
    UITask.BeforeInactiveSprite = UITask.UGInactiveSprite
    if UITask.beforeName == "underGroundTask" then return end
    UITask.ChangePage(UITask.beforeName)
    if not UITask.LuaUnderGroundTask then
        local p = HotResManager.ReadUI('ui/Task3D/prefab/UIUnderGrounePanel')
        UITask.LuaUnderGroundTask = GameObject.Instantiate(p, UITask.gameObject.transform)
        UITask.LuaUnderGroundTask.transform.localPosition = Vector3(-130,0,0) 
        UITask.LuaUnderGroundTask:AddComponent(System.Type.GetType('LuaMonoBehavior'))
        UITask.LuaUnderGroundTask:GetComponent('LuaMonoBehavior'):RunLuaFile("UIUnderGroundPanel")
        UITask.pageList["underGroundTask"] = UITask.LuaUnderGroundTask  
    end
    UITask.LuaUnderGroundTask:SetActive(true)
    UITask.beforeName = "underGroundTask"  
    UITask.spTitle:GetComponent('Text').text=GetGameText(luaID,9)
    AtlasManager:AsyncGetSprite("tz_00_5", UITask.spriteTitle)
end
--打开超级boss分页
function UITask.OpenSuperBossList()
    UITask.ClearUnderGroundOpion()
    UITask.calendarBtn:SetActive(false)
    if UITask.BeforeActiveSprite == UITask.SuperBossActiveSprite then return end
    UITask.BeforeActiveSprite:SetActive(false)
    UITask.BeforeInactiveSprite:SetActive(true)
    UITask.SuperBossActiveSprite:SetActive(true)
    UITask.SuperBossInactiveSprite:SetActive(false)
    UITask.BeforeActiveSprite = UITask.SuperBossActiveSprite
    UITask.BeforeInactiveSprite = UITask.SuperBossInactiveSprite
    if UITask.beforeName == "superBossTask" then return end
    UITask.ChangePage(UITask.beforeName)
    if not UITask.LuaSuperBossTask then
        local p = HotResManager.ReadUI('ui/Task3D/prefab/UISuperBoss')
        UITask.LuaSuperBossTask = GameObject.Instantiate(p, UITask.gameObject.transform)
        UITask.LuaSuperBossTask.transform.localPosition = Vector3(-130,0,0) 
        UITask.LuaSuperBossTask:AddComponent(System.Type.GetType('LuaMonoBehavior'))
        UITask.LuaSuperBossTask:GetComponent('LuaMonoBehavior'):RunLuaFile("UISuperBoss")
        UITask.pageList["superBossTask"] = UITask.LuaSuperBossTask  
    end
    UITask.LuaSuperBossTask:SetActive(true)
    UITask.beforeName = "superBossTask"  
    UITask.spTitle:GetComponent('Text').text=GetGameText(luaID,19)
    AtlasManager:AsyncGetSprite("tz_00_7", UITask.spriteTitle) 
end

--打开个人Boss分页
function UITask.OpenMyBossList()
    UITask.ClearUnderGroundOpion()
    UITask.calendarBtn:SetActive(false)
    if UITask.BeforeActiveSprite == UITask.MyBossActiveSprite then return end
    UITask.BeforeActiveSprite:SetActive(false)
    UITask.BeforeInactiveSprite:SetActive(true)
    UITask.MyBossActiveSprite:SetActive(true)
    UITask.MyBossInactiveSprite:SetActive(false)
    UITask.BeforeActiveSprite = UITask.MyBossActiveSprite
    UITask.BeforeInactiveSprite = UITask.MyBossInactiveSprite
    if UITask.beforeName == "myBossTask" then return end
    UITask.ChangePage(UITask.beforeName)
    if not UITask.LuaMyBossTask then   
        local p = HotResManager.ReadUI('ui/Task3D/prefab/UIMyBossPanel')
        UITask.LuaMyBossTask =GameObject.Instantiate(p, UITask.gameObject.transform)
        UITask.LuaMyBossTask.transform.localPosition = Vector3(-130,0,0) 
        UITask.LuaMyBossTask:AddComponent(System.Type.GetType('LuaMonoBehavior'))
        UITask.LuaMyBossTask:GetComponent('LuaMonoBehavior'):RunLuaFile("UIMyBossPanel")
        UITask.pageList["myBossTask"] = UITask.LuaMyBossTask
    end
    UITask.LuaMyBossTask:SetActive(true)
    UITask.beforeName = "myBossTask"  
    UITask.spTitle:GetComponent('Text').text=GetGameText(luaID,16)
    AtlasManager:AsyncGetSprite("tz_00_6", UITask.spriteTitle)
end

--打开活动日历
function UITask.OpenCalendarUI( )
    	--调用活动日历
	local loadCalendar = UITask.gameObject.transform:Find('CaLendar(Clone)')
	if loadCalendar then
		loadCalendar.gameObject:SetActive(true)
	else
		local calendar = require 'Calendar'
		calendar.Init(UITask.gameObject)
	end
end

--清除VIP分页按钮
function UITask.ClearUnderGroundOpion()
    --UITask.typeOptionTab.transform:GetComponent("UIScrollView"):ResetPosition()
    local childCount = UITask.typeOptionTab.transform.childCount
    if childCount ~= 0 then
    	for i = childCount - 1, 0 , -1 do
            if UITask.typeOptionTab.transform:GetChild(i).gameObject.name ~= CalendarBtn then
                GameObject.Destroy(UITask.typeOptionTab.transform:GetChild(i).gameObject)
            end	
    	end
    end
end
--创建vip分页按钮
function UITask.CreateVIPOpion()
    UITask.currentVIPSeclect = nil
    --UITask.typeOptionTab.transform:GetComponent("UIScrollView"):ResetPosition()
    local childCount = UITask.typeOptionTab.transform.childCount
    if childCount ~= 0 then
    	for i = childCount - 1, 0 , -1 do
            if UITask.typeOptionTab.transform:GetChild(i).gameObject.name ~= CalendarBtn then
                GameObject.Destroy(UITask.typeOptionTab.transform:GetChild(i).gameObject)
            end	
    	end
    end
    local vipLevel = EntityModule.hero.propertyLC:GetProperty(PLAYER_FIELD.PLAYER_FIELD_VIPLEVEL)
    if vipLevel < 8 then
        UITask.VIPList = {4,6,8,10}
    else
        UITask.VIPList = {4,6,8,10,11,12,13,14}
    end
    UITask.VIPLevelLimit = {0,200,300,450,550,750,0,0}
    UITask.tabsVIP = {}
    for k, v in ipairs(UITask.VIPList) do
        UITask.tabsVIP[k] = string.format(GetGameText(luaID, 18), UITask.VIPList[k])
    end 
    local typePrefab = HotResManager.ReadUI("ui/UIActivities/prefab/UIVIPBOSSBtn")
    local pos = 0 
    UITask.typeOptionVIPList = {}
    UITask.curTypeOpetionVIPBtn = nil
    for k,v in ipairs( UITask.VIPList) do
        pos = (k-1)*130
        local o = {}
        o.gameObject = GameObject.Instantiate(typePrefab, UITask.typeOptionTab.transform)
        o.gameObject.transform.localPosition = Vector3(-40+pos,-85 ,0)
        o.selected = o.gameObject.transform:Find('Selected').gameObject
        o.normal = o.gameObject.transform:Find('Normal').gameObject
        o.label = o.gameObject.transform:Find('Label'):GetComponent('Text')
        o.red = o.gameObject.transform:Find('Tips').gameObject    
        o.label.text = UITask.tabsVIP[k]
        o.selected:SetActive(false)
        o.red:SetActive(false)
        o.vipLevel = v
        local tabs = UITask.tabsVIP[k]
        UITask.typeOptionVIPList[tabs] = o
        o.gameObject:GetComponent('Button').onClick:AddListenerEx(function()
            UITask.ClickVIPOpionBtn(tabs,UITask.VIPList[k])
        end)
    end
    UITask.curVIPTab = 1
    local count = #UITask.VIPList
    local heroLevel = EntityModule.hero.propertyLC:GetProperty(CREATURE_FIELD.CREATURE_FIELD_LEVEL)
    for i = count,1,-1 do
        if vipLevel >= UITask.VIPList[i] and heroLevel > 400 then
            UITask.curVIPTab = i
            break
        end
    end
    UITask.ClickVIPOpionBtn(UITask.tabsVIP[UITask.curVIPTab], UITask.VIPList[UITask.curVIPTab])
end
--创建分页按钮
function UITask.CreateTypeOption()
    --UITask.typeOptionTab.transform:GetComponent("UIScrollView"):ResetPosition()
    local childCount = UITask.typeOptionTab.transform.childCount
    if childCount ~= 0 then
        for i=childCount - 1, 0, -1 do
            if UITask.typeOptionTab.transform:GetChild(i).gameObject.name ~= CalendarBtn then
                GameObject.Destroy(UITask.typeOptionTab.transform:GetChild(i).gameObject)
            end	
    	end
    end
    UITask.optionName = {GetGameText(luaID, 3),GetGameText(luaID, 2),GetGameText(luaID, 1)}
    UITask.tabs = {"fieldBoss_Tabs","worldBoss_Tabs","activit_Tabs"} --用于外部调用的，分页标记(与UITask.optionName相对应)
    local pos = 0
    UITask.typeOptionList = {}
    local heroLevel = EntityModule.hero.propertyLC:GetProperty(CREATURE_FIELD.CREATURE_FIELD_LEVEL)
    --打开界面定位BOSS分页
	if heroLevel >= UIManager.guideLvLimitOpen.BossPK then
        UITask.OnFieldBossBtnPageClick()
    elseif heroLevel >= UIManager.guideLvLimitOpen.WorldBoss then
        UITask.OnWorldBossBtnPageClick()
    elseif heroLevel >= UIManager.guideLvLimitOpen.VIPBoss then
        UITask.OnVIPBossBtnPageClick()
    elseif heroLevel >= UIManager.guideLvLimitOpen.UnderGround then
        UITask.OpenUnderGroundList()
    elseif heroLevel >= UIManager.guideLvLimitOpen.MyBoss then
		UITask.OpenMyBossList()
	elseif heroLevel >= UIManager.guideLvLimitOpen.SuperBoss then
		UITask.OpenSuperBossList()
    end
end
--点击分页按钮
function UITask.ClickTypeOptionBtn(tabs)
    UITask.ResetPage(tabs)--创建分页内容
end
--支持分页
function UITask.ResetPage(tabs)
    if tabs ==  UITask.tabs[3] then
        UITask.OnActivityPageClick()
    elseif tabs == UITask.tabs[2] then
        UITask.OnWorldBossBtnPageClick()  --世界Boss  
    elseif tabs == UITask.tabs[1] then
        UITask.OnFieldBossBtnPageClick()  --野外BOss
    elseif tabs == UITask.tabs[4] then
        UITask.OnVIPBossBtnPageClick() --vip领地   
    end
end
--点击vip分页按钮
function UITask.ClickVIPOpionBtn(tabs,VIPLevel)
    --UITask.VIPActiveRedSpot:SetActive(false)
    local actID = EntityModule.hero.propertyLC:GetProperty(ENTITY_FIELD.ENTITY_FIELD_ID)
    local server_time = os.date("*t",HelperL.GetServerTime())
    local today = server_time.day
    PlayerPrefs.SetInt("VIPBoss"..actID,today)
    if VIPLevel ==  UITask.currentVIPSeclect then return end
    --UITask.curVIPTabs = tabs
    UITask.curVIPLevel = VIPLevel
    if UITask.curTypeOpetionVIPBtn then
        UITask.curTypeOpetionVIPBtn.label.color = Color(181/255,181/255,181/255,1)
        UITask.curTypeOpetionVIPBtn.label.effectStyle = UILabel.Effect.Outline
        UITask.curTypeOpetionVIPBtn.selected:SetActive(false)
        UITask.curTypeOpetionVIPBtn.normal:SetActive(true)
    end
    local o = UITask.typeOptionVIPList[tabs]
    if not o then return end
    o.label.color = Color(255,255,255,1)
    o.label.effectStyle = UILabel.Effect.Outline
    o.selected:SetActive(true)
    o.normal:SetActive(false)
    UITask.currentVIPSeclect = VIPLevel
    UITask.curTypeOpetionVIPBtn = o
    UITask.RestVIPPage(VIPLevel)
end
function UITask.RestVIPPage(VIPLevel)
    UITask.OnVIPBossBtnPageClick(VIPLevel)
end
--切换界面隐藏之前分页
function UITask.ChangePage(name)
    if not UITask.pageList[name] then return end
    UITask.pageList[name]:SetActive(false)
end
--活动  
function UITask.OnActivityPageClick()
    if UITask.beforeName == "Activity" then return end
    UITask.ChangePage(UITask.beforeName)
    if not UITask.Activity then
        local prefab = HotResManager.ReadUI("ui/TimingActivity/prefab/UIDailyActivities")
        UITask.Activity = GameObject.Instantiate(prefab, UITask.gameObject.transform)
        UITask.Activity:AddComponent(System.Type.GetType('LuaMonoBehavior'))
        UITask.Activity:GetComponent('LuaMonoBehavior'):RunLuaFile("UIDailyActivities")
        UITask.pageList["Activity"] = UITask.Activity
    end
    UITask.Activity:SetActive(true)
    UITask.beforeName = "Activity"
end
--首领boss
function UITask.OnFieldBossBtnPageClick()
    if UITask.beforeName == "fieldBossTask" then 
        return end
    UITask.ChangePage(UITask.beforeName)

    if not UITask.LuaFieldBossTask then
        UITask.LuaFieldBossTask = require "UIBossPanel"
        UITask.pageList["fieldBossTask"] = UITask.LuaFieldBossTask
        UITask.LuaFieldBossTask.Init(UITask.gameObject)
    end
    UITask.LuaFieldBossTask.UpdateView( )
    UITask.beforeName = "fieldBossTask"
    UITask.spTitle:GetComponent('Text').text=GetGameText(luaID,12)
    AtlasManager:AsyncGetSprite("btz_00_11", UITask.spriteTitle)
end
--世界Boss
function UITask.OnWorldBossBtnPageClick()
    local childCount = UITask.typeOptionTab.transform.childCount
    if childCount ~= 0 then
    	for i = childCount - 1, 0 , -1 do
            if UITask.typeOptionTab.transform:GetChild(i).gameObject.name ~= CalendarBtn then
                GameObject.Destroy(UITask.typeOptionTab.transform:GetChild(i).gameObject)
            end	
    	end
    end
	if UITask.beforeName == "worldBossTask" then return end
    UITask.ChangePage(UITask.beforeName)
    if not UITask.LuaWorldBossTask then
        local p = HotResManager.ReadUI('ui/worldBoss/WorldBossWindow')
        UITask.LuaWorldBossTask = GameObject.Instantiate(p, UITask.gameObject.transform)
        UITask.LuaWorldBossTask.transform.localPosition = Vector3(-130,0,0) 
        UITask.pageList["worldBossTask"] = UITask.LuaWorldBossTask
        UITask.LuaWorldBossTask:AddComponent(System.Type.GetType('LuaMonoBehavior'))
        UITask.LuaWorldBossTask:GetComponent('LuaMonoBehavior'):RunLuaFile("UIWorldBoss")   
    end
    UITask.LuaWorldBossTask:SetActive(true)
    UITask.beforeName = "worldBossTask"
    UITask.spTitle:GetComponent('Text').text=GetGameText(luaID,14)
    AtlasManager:AsyncGetSprite("tz_00_11", UITask.spriteTitle)
end

--VIP领地  专属
function UITask.OnVIPBossBtnPageClick(VIPLevel)
    UITask.ChangePage(UITask.beforeName)
    if not UITask.LuaVIPBossTask then
        UITask.LuaVIPBossTask = require "UIVIPBossPanel"
        UITask.pageList["vipBossTask"] = UITask.LuaVIPBossTask
        UITask.LuaVIPBossTask.Init(UITask.gameObject)
    end
    UITask.LuaVIPBossTask.UpdateView(VIPLevel)
    UITask.beforeName = "vipBossTask"
    UITask.spTitle:GetComponent('Text').text=GetGameText(luaID,7)
    AtlasManager:AsyncGetSprite("tz_00_11", UITask.spriteTitle)
end
-- --楚汉战场
-- function UITask.onBattleBtnPageClick()
--     if UITask.beforeName == "ChuhanBattle" then return end
--     UITask.ChangePage(UITask.beforeName)
--     if not UITask.LuaChuhanBattle then
--         UITask.LuaChuhanBattle = require "ChuhanBattlefield"
--         UITask.pageList["ChuhanBattle"] = UITask.LuaChuhanBattle
--         UITask.LuaChuhanBattle.Init(UITask.gameObject)
--     end                      
--     UITask.LuaChuhanBattle:SetActive(true)
--     UITask.LuaChuhanBattle.UpdateView( )
--     UITask.beforeName = "ChuhanBattle"
-- end
-- 窗口消息
function UITask.OnWindowMsg(self, msg, param)
    if msg == UIWndMsg.UITask.jumpPagMsg then
        if type(param) ~= 'table' then
            assert(false, "参数错误！请使用table类型") 
            return
        end
        if param.tag == 'vipBoss_Tabs' then
            UITask.OpenVIPList()
            local bossItems = Schemes.WorldBossWild.items
            local bossItem = nil
            for k, v in ipairs(bossItems) do 
                if v.ID == param.id then
                    bossItem = v
                    break
                end
            end
            if not bossItem then return end
            local sceneItem = Schemes.GameScene:Get(bossItem.BtnMapID)
			if not sceneItem then return end
            for k, v in ipairs(UITask.VIPList) do
                if v == sceneItem.VipLevel then
                    UITask.ClickVIPOpionBtn(UITask.tabsVIP[k], UITask.VIPList[k])
                    UITask.LuaVIPBossTask.OpenAppointBossPag(param.id)
                    break
                end
            end
        elseif param.tag == 'underGroundBoss_Tabs' then
            UITask.OpenUnderGroundList()
            local bossItems = Schemes.WorldBossWild.items
            local bossItem = nil
            for k, v in ipairs(bossItems) do 
                if v.ID == param.id then
                    bossItem = v
                    break
                end
            end
            if not UITask.pageList["underGroundTask"] or not param.id then return end
            local luaMono = UITask.pageList["underGroundTask"]:GetComponent('LuaMonoBehavior')
            if luaMono then
                luaMono:CallLuaFunction('OpenAppointBossPag', param.id)
            end
        elseif param.tag == 'worldBoss_Tabs' then
            UITask.OpenActivityList()
            --UITask.ClickTypeOptionBtn(param.tag)
            UITask.OpenworldBossBtnList()
        elseif param.tag == 'myBoss_Tabs' then
            UITask.OpenMyBossList()
        elseif param.tag == 'superBoss_Tabs' then
            UITask.OpenSuperBossList()
        else
            UITask.OpenActivityList()
            UITask.ClickTypeOptionBtn(param.tag)
            if param.tag == 'fieldBoss_Tabs' then --需跳入指定分页的指定选择按钮
                if not UITask.pageList[UITask.beforeName] or not param.id then return end
                UITask.pageList[UITask.beforeName].OpenAppointBossPag(param.id)
            end
        end
	end
end
function UITask.CheckRedSpot()
    --UITask.VIPActiveRedSpot:SetActive(RedSpotManager.CheckVIPBoss())
    UITask.MyBossActiveRedSpot:SetActive(RedSpotManager.CheckMyBoss())
    UITask.UGActiveRedSpot:SetActive(RedSpotManager.CheckUnderGroundBoss())
    UITask.actWorldRedSpot:SetActive(RedSpotManager.CheckWorldBoss())
end


function UITask.RequestBossData()
    local function CallBack( result ,data)
        if tolua.isnull(UITask.gameObject) then return end
        if result ~= 0 then return end 
        UITask.CheckVIPBossList = {}
        local tempBossItem = {}
        local dataList = dkjsonHelper.decode(data)
        if #dataList > 0 then 
            for j,k in pairs(UITask.curVIPBossList) do 
                for l,m in pairs(dataList) do
                    if k and m then
                        if k.data.ID == m.bossID then 
                            k.reviveTime = m.reviveTime
                            k.isAttenuation = m.isAttenuation
                        end
                    end
                end     
            end
        end
        local result = false
        for k,v in pairs(UITask.curVIPBossList) do
            result =  RedSpotManager.CheckVIPBossGoBtnRedSpot(v) 
            if result then break end
        end
        UITask.VIPActiveRedSpot:SetActive(result)
    end
    local request = string.format("LuaRequestWorldBossWildInfo")
    LuaModule.RunLuaRequest(request,CallBack)
end

--VIP分页红点检测
function UITask.VIPOpionRedSpot()
    local vipLv = EntityModule.hero.propertyLC:GetProperty(PLAYER_FIELD.PLAYER_FIELD_VIPLEVEL)
    local vipResult = false
    for k,v in pairs(UITask.typeOptionVIPList) do
        vipResult = false   
        if vipLv < v.vipLevel then
            v.red:SetActive(false)
        else
            for _,j in pairs(UITask.curVIPBossList) do
                if v.vipLevel == j.vipLevel then
                    vipResult = RedSpotManager.CheckVIPBossGoBtnRedSpot(j)
                    if vipResult then
                        v.red:SetActive(true)
                        break 
                    end
                end
                v.red:SetActive(false)
            end
        end
    end
end

function UITask.UpdateVIPBtnRedSpot(state)
    local result = false
    if UITask.typeOptionVIPList and UITask.currentVIPSeclect then
        for k,v in pairs(UITask.typeOptionVIPList) do
            if tolua.isnull(v.red) then return end
            if v.vipLevel == UITask.currentVIPSeclect then
                v.red:SetActive(state)
            end
            result = result or v.red.activeSelf
        end
        UITask.VIPActiveRedSpot:SetActive(result)
    end
end

function UITask.InitVIPSchemesData()
    UITask.curVIPBossList = {}
    local bossItems = Schemes.WorldBossWild.items
    local tempBossItem = {}
    if #bossItems == 0 then return end
    local sceneItem = nil
    for k,v in ipairs(bossItems) do
        if v.UIType == 2 then
            tempBossItem = {}
            tempBossItem.isAttenuation = false
            tempBossItem.reviveTime = 0
            sceneItem = Schemes.GameScene:Get(v.BtnMapID)
            if sceneItem then
                tempBossItem.vipLevel = sceneItem.VipLevel
            end
            tempBossItem.data = {}
            tempBossItem.data.ID = v.ID
            tempBossItem.data.BtnMapID = v.BtnMapID
            tempBossItem.data.MapID = v.MapID
            table.insert(UITask.curVIPBossList, tempBossItem)
        end
    end
end
return UITask
