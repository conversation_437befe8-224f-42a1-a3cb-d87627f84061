-- 全局场景管理
local luaID = ('HeroDataManager')

local ActorDataMgr = require 'ActorDataMgr'
HeroDataManager = {}

-- 清除数据
function HeroDataManager:ClearData()
	self.logicValue = {}
	self.cooltimeValue = {}
end

-- 逻辑数据相关
HeroDataManager.logicValue = {}
-- 设置逻辑值
function HeroDataManager:SetLogicData(k, v)
	self.logicValue[k] = v
	if SmeltIDSaveLogic[k] and EntityModule.hero then
		for key, value in pairs(SmeltIDSaveLogic) do
			if value == k then
				local equipSmeltStar = Schemes.EquipSmeltStar:GetEquipSmeltStarCfg(key, 0)
				if equipSmeltStar then            
					local value1 = HeroDataManager:GetLogicWord(k,0)
					ActorDataMgr.SetIntByIndex(equipSmeltStar.ActorDataCatalog, 2, value1)
					

					local value2 = HeroDataManager:GetLogicWord(k,1)
					ActorDataMgr.SetIntByIndex(equipSmeltStar.ActorDataCatalog, 1, value2)
				end
				break
			end
		end		
	end
end

function HeroDataManager:UpdateAllLogic()
	for k, v in pairs(self.logicValue) do
		if EntityModule.hero  then
			for key, value in pairs(SmeltIDSaveLogic) do
				if value == k then
					local equipSmeltStar = Schemes.EquipSmeltStar:GetEquipSmeltStarCfg(key, 0)
					if equipSmeltStar then            
						local value1 = HeroDataManager:GetLogicWord(value,0)
						ActorDataMgr.SetIntByIndex(equipSmeltStar.ActorDataCatalog, 2, value1)
						
			
						local value2 = HeroDataManager:GetLogicWord(value,1)
						ActorDataMgr.SetIntByIndex(equipSmeltStar.ActorDataCatalog, 1, value2)						
					end
					break
				end
			end
			
		end
	end
end

-- 获取逻辑值
function HeroDataManager:GetLogicData(k)
	return self.logicValue[k] or 0
end

-- 按位获取逻辑值 32位
function HeroDataManager:GetLogicBit(logicID, index)
	if index < 0 or index > 31 then
		warn("HeroDataManager:GetLogicBit index=", index, logicID)
		return 0
	end

	local value = self:GetLogicData(logicID)
	return Helper.GetBit(value, index)
end

-- 按字节获取逻辑值 4位
function HeroDataManager:GetLogicByte(logicID, index)
	if index < 0 or index > 3 then
		warn("HeroDataManager:GetLogicByte index=", index, logicID)
		return 0
	end

	local value = self:GetLogicData(logicID)
	return Helper.GetDivideInt(value, index)
end

-- 按字节获取逻辑值 6位
function HeroDataManager:GetLogicByteTwo(logicID, index)
	if index < 0 or index > 6 then
		warn("HeroDataManager:GetLogicByte index=", index, logicID)
		return 0
	end

	local value = self:GetLogicData(logicID)
	return Helper.GetDivideInt(value, index)
end

-- 按字节获取逻辑值
function HeroDataManager:GetLogicByteByDiscount(logicID, index)
	--if index < 0 or index > 3 then
	--warn("HeroDataManager:GetLogicByte index=", index, logicID)
	--return 0
	--end

	local value = self:GetLogicData(logicID)
	return Helper.GetDivideInt(value, index)
end

-- 按双字节获取逻辑值
function HeroDataManager:GetLogicWord(logicID, index)
	if index < 0 or index > 1 then
		warn("HeroDataManager:GetLogicWord index=", index, logicID)
		return 0
	end

	local value = self:GetLogicData(logicID)
	return Helper.GetDivideWord(value, index)
end

-- 冷却数据相关
HeroDataManager.cooltimeValue = {}
-- 设置冷却数据
function HeroDataManager:SetCoolTime(k, v)
	local cooldownScheme = Schemes.Cooldown:Get(k)
	if cooldownScheme == nil then
		print('SetCoolTime scheme=nil, id=' .. k)
		return
	end

	self.cooltimeValue[k] = { leftTime = v, beginTime = UnityEngine.Time.time, totalTime = cooldownScheme.Time }
	if cooldownScheme.PublicID > 0 then
		local publicCDScheme = Schemes.Cooldown:Get(cooldownScheme.PublicID)
		if publicCDScheme then
			self:SetCoolTime(cooldownScheme.PublicID, publicCDScheme.Time)
		end
	end

	EventManager:Fire(EventID.CoolDataChange, k)
end

-- 是否冷却中
function HeroDataManager:IsCooling(k, ignorePublic)
	local cooldownScheme = Schemes.Cooldown:Get(k)
	if not cooldownScheme then
		return false
	end

	local info = self.cooltimeValue[k]
	if info == nil then
		if cooldownScheme.PublicID > 0 then
			return self:IsCooling(cooldownScheme.PublicID)
		end
		return false
	end

	local curTime = UnityEngine.Time.time
	if info.leftTime <= (curTime - info.beginTime) * 1000 then
		self.cooltimeValue[k] = nil
		if not ignorePublic then
			return self:IsCooling(cooldownScheme.PublicID, true)
		else
			return false
		end
	end

	return true
end

-- 获取剩余冷却时间
function HeroDataManager:GetLeftCoolTime(k, ignorePublic)
	local cooldownScheme = Schemes.Cooldown:Get(k)
	if not cooldownScheme then
		return 0, 0
	end

	local info = self.cooltimeValue[k]
	if info == nil then
		if cooldownScheme.PublicID > 0 then
			return self:GetLeftCoolTime(cooldownScheme.PublicID)
		end
		return 0, 0
	end

	local curTime = UnityEngine.Time.time
	if info.leftTime < (curTime - info.beginTime) * 1000 then
		self.cooltimeValue[k] = nil
		return 0, 0
	end

	local leftTime = info.leftTime - (curTime - info.beginTime) * 1000
	local rate = 0
	if info.totalTime > 0 then
		rate = leftTime / info.totalTime
	end
	if not ignorePublic then
		if cooldownScheme.PublicID > 0 then
			local publicLeftTime, publicRate = self:GetLeftCoolTime(cooldownScheme.PublicID, true)
			if publicLeftTime > leftTime then
				leftTime = publicLeftTime
				rate = publicRate
			end
		end
	end
	return leftTime, rate
end

-- 购买充值卡数据相关
HeroDataManager.buyRechargeCardRecord = {}
-- 设置购买充值卡记录数据
function HeroDataManager:SetBuyRechardCardRecord(cardList)
	self.buyRechargeCardRecord = {}
	if not cardList then
		return
	end

	for i, v in ipairs(cardList) do
		self.buyRechargeCardRecord[v] = true
	end
end

-- 是否有购买充值卡记录
function HeroDataManager:HaveBuyRechardCardRecord(cardID)
	if self.buyRechargeCardRecord[cardID] then
		return true
	end
	return false
end
