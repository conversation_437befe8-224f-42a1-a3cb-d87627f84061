﻿// ReSharper disable Unity.IncorrectMethodSignature
// ReSharper disable InconsistentNaming

using System.Collections.Generic;
using System.Linq;

using Apq.Unity3D.Extension;

using CsvTables;

using Cysharp.Threading.Tasks;

using Thing;

using UnityEngine;

using ViewModel;

using X.PB;

namespace View
{
    /// <summary>
    /// Npc管理器
    /// </summary>
    [DisallowMultipleComponent]
    public class NpcMgr : MonoBehaviour
    {
        /// <summary>
        /// Npc配置列表
        /// </summary>
        public List<NpcVM> Cfg_NpcList { get; set; }

        /// <summary>
        /// 已创建的Npc列表
        /// </summary>
        public List<NpcBase> NpcList { get; } = new();

        /// <summary>
        /// 重置为指定地图的Npc管理器
        /// </summary>
        /// <param name="csvRow_CatMainMap">地图配置</param>
        public void Reset(CatMainMapCfg.Types.CSVRow csvRow_CatMainMap)
        {
            var cfg_NpcList = csvRow_CatMainMap.NpcIdList
                .Select(x => SingletonMgr.Instance.CsvLoaderMgr.GetOrAddLoader<CatMainNpcCsv>().Dic[x]).ToList();
            // var cfg_NpcPosList = new List<Vector2>();
            // for (var i = 0; i + 1 < csvRow_CatMainMap.NpcPosList.Count; i += 2)
            // {
            //     cfg_NpcPosList.Add(new(csvRow_CatMainMap.NpcPosList[i], csvRow_CatMainMap.NpcPosList[i + 1]));
            // }

            Cfg_NpcList = cfg_NpcList.Select(x => new NpcVM { Npc = x, })
                .ToList();
        }

        /// <summary>
        /// 在场景中创建指定时机的Npc
        /// </summary>
        public async UniTask<List<NpcBase>> CreateNpc(Timing timing, bool onlyNotBorn = true)
        {
            var rtn = new List<NpcBase>();
            var createList = Cfg_NpcList.Where(x => x.Npc.AppearTiming == timing && (!onlyNotBorn || !x.HadBorn))
                .ToList();

            foreach (var npcVm in createList)
            {
                var prefab = await ResMgrAsync.LoadResAsync<GameObject>(npcVm.Npc.Model);
                var npcObj = Instantiate(prefab, transform);
                npcObj.transform.position = new Vector2(npcVm.Npc.Center[0], npcVm.Npc.Center[1]);

                // 加上碰撞盒
                var npcCollider = npcObj.gameObject.GetOrAddComponent<CircleCollider2D>();
                npcCollider.isTrigger = true;
                npcCollider.radius = npcVm.Npc.Radius;

                // 去掉刚体
                var npcRigidbody = npcObj.gameObject.GetComponent<Rigidbody2D>();
                if (npcRigidbody)
                {
                    Destroy(npcRigidbody);
                }

                var npc = npcObj.GetOrAddComponent<NpcBase>();
                npc.Thing = new NpcThing { CsvRow_Npc = { Value = npcVm.Npc } };
                npcVm.HadBorn = true;
                rtn.Add(npc);
            }
            
            NpcList.AddRange(rtn);
            return rtn;
        }
    }
}