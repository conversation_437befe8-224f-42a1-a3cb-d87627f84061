-- 登录UI
local luaID = ('UIShowBoxInfo')

local UIShowBoxInfo = {}

-- 初始化
function UIShowBoxInfo:OnCreate()
	self.objList.Btn_Close.onClick:AddListenerEx(self.OnClickClose)
	self.objList.Btn_Ok.onClick:AddListenerEx(self.OnClickOk)
	self.btnList = { self.objList.Btn_Close, self.objList.Btn_Ok }
	self.goodContent = self.objList.GoodContent.transform
	self.prizeContent = self.objList.PrizeContent.transform
	self.item = self.objList.Item
	self.goodItemList = {}
	self.itemList = {}
	return true
end

-- 窗口开启
function UIShowBoxInfo:OnOpen(giftShowID, prizeID, iconName, giftName, btnName, callback, param1, param2)
	AtlasManager:AsyncGetSprite(iconName, self.objList.Img_Icon)
	self.objList.Txt_Title.text = giftName
	self.giftShowID = giftShowID
	self.prizeID = prizeID
	self.callback = callback
	self.param1 = param1
	self.param2 = param2
	self.objList.Txt_Ok.text = btnName
	self.giftShowConfig = Schemes.GiftShow:Get(giftShowID)
	self:UpdateView()
end

-- 更新视图 
function UIShowBoxInfo:UpdateView()		
	if self.giftShowConfig.Type == 0 then

	else
		if self.giftShowConfig.Type == 1 or self.giftShowConfig.Type == 2 then	
			for k, v in ipairs(self.goodItemList) do
				v.gameObject:SetActive(false)
			end		
			local prize = Schemes.PrizeTable:GetPrize(self.prizeID)
			if prize == nil then return end
			local prizeList = Schemes.PrizeTable:GetPrizeGoods(prize)
			if prizeList == nil then return end
			local len = #prizeList
			if self.giftShowConfig.Type == 2 then
				len = self.giftShowConfig.Parameter
			end
			for k, v in ipairs(self.goodItemList) do
				v.gameObject:SetActive(false)
			end
			for i=1 , len do
				local item = self.goodItemList[i]
				if not item then
					item = CreateSingleGoods(self.prizeContent)
					table.insert(self.goodItemList, item)
				end
				item:SetItemData(prizeList[i].ID, prizeList[i].Num)
				item:SetSize(110,110)
				item:SetVisible(true)
				item:SetShowNum(true)
			end	
			self.objList.GoodContent.gameObject:SetActive(false)	
			self.objList.PrizeContent.gameObject:SetActive(true)		
		elseif self.giftShowConfig.Type == 3 then
			for k, v in ipairs(self.itemList) do
				v.gameObject:SetActive(false)
			end
			for i = 1 , #self.giftShowConfig.TipBG do
				if self.giftShowConfig.TipBG[i] ~= '' and self.giftShowConfig.TipBG[i] ~= '0' then
					local item = self.itemList[i]
					if not item then
						item = self:CreateSubItem(self.goodContent, self.item)
						table.insert(self.itemList, item)
					end
					AtlasManager:AsyncGetSprite(self.giftShowConfig.TipBG[i], item.Img_ItemIcon)
					item.Txt_ItemName.text = self.giftShowConfig.TipText[i]
					item.gameObject:SetActive(true)
				end
			end
			self.objList.GoodContent.gameObject:SetActive(true)	
			self.objList.PrizeContent.gameObject:SetActive(false)		
		end
	end
	if self.giftShowConfig.Description == '0' then
		self.objList.Txt_Desc.text = ''
	else
		self.objList.Txt_Desc.text = self.giftShowConfig.Description
	end	
end

function UIShowBoxInfo.OnClickClose()
	local self = UIShowBoxInfo
	self:CloseSelf()	
end

function UIShowBoxInfo.OnClickOk()
	local self = UIShowBoxInfo
	if not self.giftShowConfig then return end	
	if self.callback then
		self.callback(self.param1, self.param2)
	end
	self:CloseSelf()
end

-- 窗口关闭
function UIShowBoxInfo:OnClose()
end

-- 窗口销毁
function UIShowBoxInfo:OnDestroy()
	-- 释放内存
	for i, btn in ipairs(self.btnList) do
		btn.onClick:RemoveAllListeners()
		btn.onClick:Invoke()
	end
end

return UIShowBoxInfo