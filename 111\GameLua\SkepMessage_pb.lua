-- Generated By protoc-gen-lua Do not Edit
local protobuf = require "protobuf/protobuf"
module('SkepMessage_pb')
local pb = {}


pb.MSG_SKEP_ACTIONID = protobuf.EnumDescriptor();
pb.MSG_SKEP_ACTIONID_MSG_SKEP_NONE_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_SKEP_ACTIONID_MSG_SKEP_CREATE_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_SKEP_ACTIONID_MSG_SKEP_DESTORY_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_SKEP_ACTIONID_MSG_SKEP_SYNCPLACE_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_SKEP_ACTIONID_MSG_SKEP_SYNCSIZE_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_SKEP_ACTIONID_MSG_SKEP_USEGOODS_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_SKEP_ACTIONID_MSG_SKEP_SELLGOODS_ENUM = protobuf.EnumValueDescriptor();
pb.SC_SKEP_CREATESKEP = protobuf.Descriptor();
pb.SC_SKEP_CREATESKEP_SKEPUID_FIELD = protobuf.FieldDescriptor();
pb.SC_SKEP_CREATESKEP_MASTERUID_FIELD = protobuf.FieldDescriptor();
pb.SC_SKEP_CREATESKEP_ACCESS_FIELD = protobuf.FieldDescriptor();
pb.SC_SKEP_CREATESKEP_SKEPID_FIELD = protobuf.FieldDescriptor();
pb.SC_SKEP_CREATESKEP_MAXSIZE_FIELD = protobuf.FieldDescriptor();
pb.SC_SKEP_DESTORYSKEP = protobuf.Descriptor();
pb.SC_SKEP_DESTORYSKEP_SKEPUID_FIELD = protobuf.FieldDescriptor();
pb.SC_SKEP_SYNCPLACE = protobuf.Descriptor();
pb.SC_SKEP_SYNCPLACE_SKEPDATA = protobuf.Descriptor();
pb.SC_SKEP_SYNCPLACE_SKEPDATA_PLACE_FIELD = protobuf.FieldDescriptor();
pb.SC_SKEP_SYNCPLACE_SKEPDATA_GOODSUID_FIELD = protobuf.FieldDescriptor();
pb.SC_SKEP_SYNCPLACE_SKEPUID_FIELD = protobuf.FieldDescriptor();
pb.SC_SKEP_SYNCPLACE_PLACELIST_FIELD = protobuf.FieldDescriptor();
pb.SC_SKEP_SYNCSIZE = protobuf.Descriptor();
pb.SC_SKEP_SYNCSIZE_SKEPUID_FIELD = protobuf.FieldDescriptor();
pb.SC_SKEP_SYNCSIZE_MAXSIZE_FIELD = protobuf.FieldDescriptor();
pb.CS_SKEP_USEGOODS = protobuf.Descriptor();
pb.CS_SKEP_USEGOODS_SKEPUID_FIELD = protobuf.FieldDescriptor();
pb.CS_SKEP_USEGOODS_GOODSUID_FIELD = protobuf.FieldDescriptor();
pb.CS_SKEP_USEGOODS_USENUM_FIELD = protobuf.FieldDescriptor();
pb.CS_SKEP_USEGOODS_TARGET_FIELD = protobuf.FieldDescriptor();
pb.SC_SKEP_USEGOODS = protobuf.Descriptor();
pb.SC_SKEP_USEGOODS_RESULT_FIELD = protobuf.FieldDescriptor();
pb.CS_SKEP_SELLGOODS = protobuf.Descriptor();
pb.CS_SKEP_SELLGOODS_SKEPUID_FIELD = protobuf.FieldDescriptor();
pb.CS_SKEP_SELLGOODS_GOODSUID_FIELD = protobuf.FieldDescriptor();
pb.SC_SKEP_SELLGOODS = protobuf.Descriptor();
pb.SC_SKEP_SELLGOODS_SKEPUID_FIELD = protobuf.FieldDescriptor();
pb.SC_SKEP_SELLGOODS_RESULT_FIELD = protobuf.FieldDescriptor();

pb.MSG_SKEP_ACTIONID_MSG_SKEP_NONE_ENUM.name = "MSG_SKEP_NONE"
pb.MSG_SKEP_ACTIONID_MSG_SKEP_NONE_ENUM.index = 0
pb.MSG_SKEP_ACTIONID_MSG_SKEP_NONE_ENUM.number = 0
pb.MSG_SKEP_ACTIONID_MSG_SKEP_CREATE_ENUM.name = "MSG_SKEP_CREATE"
pb.MSG_SKEP_ACTIONID_MSG_SKEP_CREATE_ENUM.index = 1
pb.MSG_SKEP_ACTIONID_MSG_SKEP_CREATE_ENUM.number = 1
pb.MSG_SKEP_ACTIONID_MSG_SKEP_DESTORY_ENUM.name = "MSG_SKEP_DESTORY"
pb.MSG_SKEP_ACTIONID_MSG_SKEP_DESTORY_ENUM.index = 2
pb.MSG_SKEP_ACTIONID_MSG_SKEP_DESTORY_ENUM.number = 2
pb.MSG_SKEP_ACTIONID_MSG_SKEP_SYNCPLACE_ENUM.name = "MSG_SKEP_SYNCPLACE"
pb.MSG_SKEP_ACTIONID_MSG_SKEP_SYNCPLACE_ENUM.index = 3
pb.MSG_SKEP_ACTIONID_MSG_SKEP_SYNCPLACE_ENUM.number = 3
pb.MSG_SKEP_ACTIONID_MSG_SKEP_SYNCSIZE_ENUM.name = "MSG_SKEP_SYNCSIZE"
pb.MSG_SKEP_ACTIONID_MSG_SKEP_SYNCSIZE_ENUM.index = 4
pb.MSG_SKEP_ACTIONID_MSG_SKEP_SYNCSIZE_ENUM.number = 4
pb.MSG_SKEP_ACTIONID_MSG_SKEP_USEGOODS_ENUM.name = "MSG_SKEP_USEGOODS"
pb.MSG_SKEP_ACTIONID_MSG_SKEP_USEGOODS_ENUM.index = 5
pb.MSG_SKEP_ACTIONID_MSG_SKEP_USEGOODS_ENUM.number = 5
pb.MSG_SKEP_ACTIONID_MSG_SKEP_SELLGOODS_ENUM.name = "MSG_SKEP_SELLGOODS"
pb.MSG_SKEP_ACTIONID_MSG_SKEP_SELLGOODS_ENUM.index = 6
pb.MSG_SKEP_ACTIONID_MSG_SKEP_SELLGOODS_ENUM.number = 6
pb.MSG_SKEP_ACTIONID.name = "MSG_SKEP_ACTIONID"
pb.MSG_SKEP_ACTIONID.full_name = ".MSG_SKEP_ACTIONID"
pb.MSG_SKEP_ACTIONID.values = {pb.MSG_SKEP_ACTIONID_MSG_SKEP_NONE_ENUM,pb.MSG_SKEP_ACTIONID_MSG_SKEP_CREATE_ENUM,pb.MSG_SKEP_ACTIONID_MSG_SKEP_DESTORY_ENUM,pb.MSG_SKEP_ACTIONID_MSG_SKEP_SYNCPLACE_ENUM,pb.MSG_SKEP_ACTIONID_MSG_SKEP_SYNCSIZE_ENUM,pb.MSG_SKEP_ACTIONID_MSG_SKEP_USEGOODS_ENUM,pb.MSG_SKEP_ACTIONID_MSG_SKEP_SELLGOODS_ENUM}
pb.SC_SKEP_CREATESKEP_SKEPUID_FIELD.name = "SkepUID"
pb.SC_SKEP_CREATESKEP_SKEPUID_FIELD.full_name = ".SC_Skep_CreateSkep.SkepUID"
pb.SC_SKEP_CREATESKEP_SKEPUID_FIELD.number = 1
pb.SC_SKEP_CREATESKEP_SKEPUID_FIELD.index = 0
pb.SC_SKEP_CREATESKEP_SKEPUID_FIELD.label = 2
pb.SC_SKEP_CREATESKEP_SKEPUID_FIELD.has_default_value = false
pb.SC_SKEP_CREATESKEP_SKEPUID_FIELD.default_value = 0
pb.SC_SKEP_CREATESKEP_SKEPUID_FIELD.type = 4
pb.SC_SKEP_CREATESKEP_SKEPUID_FIELD.cpp_type = 4

pb.SC_SKEP_CREATESKEP_MASTERUID_FIELD.name = "MasterUID"
pb.SC_SKEP_CREATESKEP_MASTERUID_FIELD.full_name = ".SC_Skep_CreateSkep.MasterUID"
pb.SC_SKEP_CREATESKEP_MASTERUID_FIELD.number = 2
pb.SC_SKEP_CREATESKEP_MASTERUID_FIELD.index = 1
pb.SC_SKEP_CREATESKEP_MASTERUID_FIELD.label = 2
pb.SC_SKEP_CREATESKEP_MASTERUID_FIELD.has_default_value = false
pb.SC_SKEP_CREATESKEP_MASTERUID_FIELD.default_value = 0
pb.SC_SKEP_CREATESKEP_MASTERUID_FIELD.type = 4
pb.SC_SKEP_CREATESKEP_MASTERUID_FIELD.cpp_type = 4

pb.SC_SKEP_CREATESKEP_ACCESS_FIELD.name = "Access"
pb.SC_SKEP_CREATESKEP_ACCESS_FIELD.full_name = ".SC_Skep_CreateSkep.Access"
pb.SC_SKEP_CREATESKEP_ACCESS_FIELD.number = 3
pb.SC_SKEP_CREATESKEP_ACCESS_FIELD.index = 2
pb.SC_SKEP_CREATESKEP_ACCESS_FIELD.label = 2
pb.SC_SKEP_CREATESKEP_ACCESS_FIELD.has_default_value = false
pb.SC_SKEP_CREATESKEP_ACCESS_FIELD.default_value = 0
pb.SC_SKEP_CREATESKEP_ACCESS_FIELD.type = 5
pb.SC_SKEP_CREATESKEP_ACCESS_FIELD.cpp_type = 1

pb.SC_SKEP_CREATESKEP_SKEPID_FIELD.name = "SkepID"
pb.SC_SKEP_CREATESKEP_SKEPID_FIELD.full_name = ".SC_Skep_CreateSkep.SkepID"
pb.SC_SKEP_CREATESKEP_SKEPID_FIELD.number = 4
pb.SC_SKEP_CREATESKEP_SKEPID_FIELD.index = 3
pb.SC_SKEP_CREATESKEP_SKEPID_FIELD.label = 2
pb.SC_SKEP_CREATESKEP_SKEPID_FIELD.has_default_value = false
pb.SC_SKEP_CREATESKEP_SKEPID_FIELD.default_value = 0
pb.SC_SKEP_CREATESKEP_SKEPID_FIELD.type = 5
pb.SC_SKEP_CREATESKEP_SKEPID_FIELD.cpp_type = 1

pb.SC_SKEP_CREATESKEP_MAXSIZE_FIELD.name = "MaxSize"
pb.SC_SKEP_CREATESKEP_MAXSIZE_FIELD.full_name = ".SC_Skep_CreateSkep.MaxSize"
pb.SC_SKEP_CREATESKEP_MAXSIZE_FIELD.number = 5
pb.SC_SKEP_CREATESKEP_MAXSIZE_FIELD.index = 4
pb.SC_SKEP_CREATESKEP_MAXSIZE_FIELD.label = 2
pb.SC_SKEP_CREATESKEP_MAXSIZE_FIELD.has_default_value = false
pb.SC_SKEP_CREATESKEP_MAXSIZE_FIELD.default_value = 0
pb.SC_SKEP_CREATESKEP_MAXSIZE_FIELD.type = 5
pb.SC_SKEP_CREATESKEP_MAXSIZE_FIELD.cpp_type = 1

pb.SC_SKEP_CREATESKEP.name = "SC_Skep_CreateSkep"
pb.SC_SKEP_CREATESKEP.full_name = ".SC_Skep_CreateSkep"
pb.SC_SKEP_CREATESKEP.nested_types = {}
pb.SC_SKEP_CREATESKEP.enum_types = {}
pb.SC_SKEP_CREATESKEP.fields = {pb.SC_SKEP_CREATESKEP_SKEPUID_FIELD, pb.SC_SKEP_CREATESKEP_MASTERUID_FIELD, pb.SC_SKEP_CREATESKEP_ACCESS_FIELD, pb.SC_SKEP_CREATESKEP_SKEPID_FIELD, pb.SC_SKEP_CREATESKEP_MAXSIZE_FIELD}
pb.SC_SKEP_CREATESKEP.is_extendable = false
pb.SC_SKEP_CREATESKEP.extensions = {}
pb.SC_SKEP_DESTORYSKEP_SKEPUID_FIELD.name = "SkepUID"
pb.SC_SKEP_DESTORYSKEP_SKEPUID_FIELD.full_name = ".SC_Skep_DestorySkep.SkepUID"
pb.SC_SKEP_DESTORYSKEP_SKEPUID_FIELD.number = 1
pb.SC_SKEP_DESTORYSKEP_SKEPUID_FIELD.index = 0
pb.SC_SKEP_DESTORYSKEP_SKEPUID_FIELD.label = 2
pb.SC_SKEP_DESTORYSKEP_SKEPUID_FIELD.has_default_value = false
pb.SC_SKEP_DESTORYSKEP_SKEPUID_FIELD.default_value = 0
pb.SC_SKEP_DESTORYSKEP_SKEPUID_FIELD.type = 4
pb.SC_SKEP_DESTORYSKEP_SKEPUID_FIELD.cpp_type = 4

pb.SC_SKEP_DESTORYSKEP.name = "SC_Skep_DestorySkep"
pb.SC_SKEP_DESTORYSKEP.full_name = ".SC_Skep_DestorySkep"
pb.SC_SKEP_DESTORYSKEP.nested_types = {}
pb.SC_SKEP_DESTORYSKEP.enum_types = {}
pb.SC_SKEP_DESTORYSKEP.fields = {pb.SC_SKEP_DESTORYSKEP_SKEPUID_FIELD}
pb.SC_SKEP_DESTORYSKEP.is_extendable = false
pb.SC_SKEP_DESTORYSKEP.extensions = {}
pb.SC_SKEP_SYNCPLACE_SKEPDATA_PLACE_FIELD.name = "Place"
pb.SC_SKEP_SYNCPLACE_SKEPDATA_PLACE_FIELD.full_name = ".SC_Skep_SyncPlace.SkepData.Place"
pb.SC_SKEP_SYNCPLACE_SKEPDATA_PLACE_FIELD.number = 1
pb.SC_SKEP_SYNCPLACE_SKEPDATA_PLACE_FIELD.index = 0
pb.SC_SKEP_SYNCPLACE_SKEPDATA_PLACE_FIELD.label = 2
pb.SC_SKEP_SYNCPLACE_SKEPDATA_PLACE_FIELD.has_default_value = false
pb.SC_SKEP_SYNCPLACE_SKEPDATA_PLACE_FIELD.default_value = 0
pb.SC_SKEP_SYNCPLACE_SKEPDATA_PLACE_FIELD.type = 5
pb.SC_SKEP_SYNCPLACE_SKEPDATA_PLACE_FIELD.cpp_type = 1

pb.SC_SKEP_SYNCPLACE_SKEPDATA_GOODSUID_FIELD.name = "GoodsUID"
pb.SC_SKEP_SYNCPLACE_SKEPDATA_GOODSUID_FIELD.full_name = ".SC_Skep_SyncPlace.SkepData.GoodsUID"
pb.SC_SKEP_SYNCPLACE_SKEPDATA_GOODSUID_FIELD.number = 2
pb.SC_SKEP_SYNCPLACE_SKEPDATA_GOODSUID_FIELD.index = 1
pb.SC_SKEP_SYNCPLACE_SKEPDATA_GOODSUID_FIELD.label = 2
pb.SC_SKEP_SYNCPLACE_SKEPDATA_GOODSUID_FIELD.has_default_value = false
pb.SC_SKEP_SYNCPLACE_SKEPDATA_GOODSUID_FIELD.default_value = 0
pb.SC_SKEP_SYNCPLACE_SKEPDATA_GOODSUID_FIELD.type = 4
pb.SC_SKEP_SYNCPLACE_SKEPDATA_GOODSUID_FIELD.cpp_type = 4

pb.SC_SKEP_SYNCPLACE_SKEPDATA.name = "SkepData"
pb.SC_SKEP_SYNCPLACE_SKEPDATA.full_name = ".SC_Skep_SyncPlace.SkepData"
pb.SC_SKEP_SYNCPLACE_SKEPDATA.nested_types = {}
pb.SC_SKEP_SYNCPLACE_SKEPDATA.enum_types = {}
pb.SC_SKEP_SYNCPLACE_SKEPDATA.fields = {pb.SC_SKEP_SYNCPLACE_SKEPDATA_PLACE_FIELD, pb.SC_SKEP_SYNCPLACE_SKEPDATA_GOODSUID_FIELD}
pb.SC_SKEP_SYNCPLACE_SKEPDATA.is_extendable = false
pb.SC_SKEP_SYNCPLACE_SKEPDATA.extensions = {}
pb.SC_SKEP_SYNCPLACE_SKEPDATA.containing_type = pb.SC_SKEP_SYNCPLACE
pb.SC_SKEP_SYNCPLACE_SKEPUID_FIELD.name = "SkepUID"
pb.SC_SKEP_SYNCPLACE_SKEPUID_FIELD.full_name = ".SC_Skep_SyncPlace.SkepUID"
pb.SC_SKEP_SYNCPLACE_SKEPUID_FIELD.number = 1
pb.SC_SKEP_SYNCPLACE_SKEPUID_FIELD.index = 0
pb.SC_SKEP_SYNCPLACE_SKEPUID_FIELD.label = 2
pb.SC_SKEP_SYNCPLACE_SKEPUID_FIELD.has_default_value = false
pb.SC_SKEP_SYNCPLACE_SKEPUID_FIELD.default_value = 0
pb.SC_SKEP_SYNCPLACE_SKEPUID_FIELD.type = 4
pb.SC_SKEP_SYNCPLACE_SKEPUID_FIELD.cpp_type = 4

pb.SC_SKEP_SYNCPLACE_PLACELIST_FIELD.name = "PlaceList"
pb.SC_SKEP_SYNCPLACE_PLACELIST_FIELD.full_name = ".SC_Skep_SyncPlace.PlaceList"
pb.SC_SKEP_SYNCPLACE_PLACELIST_FIELD.number = 2
pb.SC_SKEP_SYNCPLACE_PLACELIST_FIELD.index = 1
pb.SC_SKEP_SYNCPLACE_PLACELIST_FIELD.label = 3
pb.SC_SKEP_SYNCPLACE_PLACELIST_FIELD.has_default_value = false
pb.SC_SKEP_SYNCPLACE_PLACELIST_FIELD.default_value = {}
pb.SC_SKEP_SYNCPLACE_PLACELIST_FIELD.message_type = pb.SC_SKEP_SYNCPLACE_SKEPDATA
pb.SC_SKEP_SYNCPLACE_PLACELIST_FIELD.type = 11
pb.SC_SKEP_SYNCPLACE_PLACELIST_FIELD.cpp_type = 10

pb.SC_SKEP_SYNCPLACE.name = "SC_Skep_SyncPlace"
pb.SC_SKEP_SYNCPLACE.full_name = ".SC_Skep_SyncPlace"
pb.SC_SKEP_SYNCPLACE.nested_types = {pb.SC_SKEP_SYNCPLACE_SKEPDATA}
pb.SC_SKEP_SYNCPLACE.enum_types = {}
pb.SC_SKEP_SYNCPLACE.fields = {pb.SC_SKEP_SYNCPLACE_SKEPUID_FIELD, pb.SC_SKEP_SYNCPLACE_PLACELIST_FIELD}
pb.SC_SKEP_SYNCPLACE.is_extendable = false
pb.SC_SKEP_SYNCPLACE.extensions = {}
pb.SC_SKEP_SYNCSIZE_SKEPUID_FIELD.name = "SkepUID"
pb.SC_SKEP_SYNCSIZE_SKEPUID_FIELD.full_name = ".SC_Skep_SyncSize.SkepUID"
pb.SC_SKEP_SYNCSIZE_SKEPUID_FIELD.number = 1
pb.SC_SKEP_SYNCSIZE_SKEPUID_FIELD.index = 0
pb.SC_SKEP_SYNCSIZE_SKEPUID_FIELD.label = 2
pb.SC_SKEP_SYNCSIZE_SKEPUID_FIELD.has_default_value = false
pb.SC_SKEP_SYNCSIZE_SKEPUID_FIELD.default_value = 0
pb.SC_SKEP_SYNCSIZE_SKEPUID_FIELD.type = 4
pb.SC_SKEP_SYNCSIZE_SKEPUID_FIELD.cpp_type = 4

pb.SC_SKEP_SYNCSIZE_MAXSIZE_FIELD.name = "MaxSize"
pb.SC_SKEP_SYNCSIZE_MAXSIZE_FIELD.full_name = ".SC_Skep_SyncSize.MaxSize"
pb.SC_SKEP_SYNCSIZE_MAXSIZE_FIELD.number = 2
pb.SC_SKEP_SYNCSIZE_MAXSIZE_FIELD.index = 1
pb.SC_SKEP_SYNCSIZE_MAXSIZE_FIELD.label = 2
pb.SC_SKEP_SYNCSIZE_MAXSIZE_FIELD.has_default_value = false
pb.SC_SKEP_SYNCSIZE_MAXSIZE_FIELD.default_value = 0
pb.SC_SKEP_SYNCSIZE_MAXSIZE_FIELD.type = 13
pb.SC_SKEP_SYNCSIZE_MAXSIZE_FIELD.cpp_type = 3

pb.SC_SKEP_SYNCSIZE.name = "SC_Skep_SyncSize"
pb.SC_SKEP_SYNCSIZE.full_name = ".SC_Skep_SyncSize"
pb.SC_SKEP_SYNCSIZE.nested_types = {}
pb.SC_SKEP_SYNCSIZE.enum_types = {}
pb.SC_SKEP_SYNCSIZE.fields = {pb.SC_SKEP_SYNCSIZE_SKEPUID_FIELD, pb.SC_SKEP_SYNCSIZE_MAXSIZE_FIELD}
pb.SC_SKEP_SYNCSIZE.is_extendable = false
pb.SC_SKEP_SYNCSIZE.extensions = {}
pb.CS_SKEP_USEGOODS_SKEPUID_FIELD.name = "SkepUID"
pb.CS_SKEP_USEGOODS_SKEPUID_FIELD.full_name = ".CS_Skep_UseGoods.SkepUID"
pb.CS_SKEP_USEGOODS_SKEPUID_FIELD.number = 1
pb.CS_SKEP_USEGOODS_SKEPUID_FIELD.index = 0
pb.CS_SKEP_USEGOODS_SKEPUID_FIELD.label = 2
pb.CS_SKEP_USEGOODS_SKEPUID_FIELD.has_default_value = false
pb.CS_SKEP_USEGOODS_SKEPUID_FIELD.default_value = 0
pb.CS_SKEP_USEGOODS_SKEPUID_FIELD.type = 4
pb.CS_SKEP_USEGOODS_SKEPUID_FIELD.cpp_type = 4

pb.CS_SKEP_USEGOODS_GOODSUID_FIELD.name = "GoodsUID"
pb.CS_SKEP_USEGOODS_GOODSUID_FIELD.full_name = ".CS_Skep_UseGoods.GoodsUID"
pb.CS_SKEP_USEGOODS_GOODSUID_FIELD.number = 2
pb.CS_SKEP_USEGOODS_GOODSUID_FIELD.index = 1
pb.CS_SKEP_USEGOODS_GOODSUID_FIELD.label = 2
pb.CS_SKEP_USEGOODS_GOODSUID_FIELD.has_default_value = false
pb.CS_SKEP_USEGOODS_GOODSUID_FIELD.default_value = 0
pb.CS_SKEP_USEGOODS_GOODSUID_FIELD.type = 4
pb.CS_SKEP_USEGOODS_GOODSUID_FIELD.cpp_type = 4

pb.CS_SKEP_USEGOODS_USENUM_FIELD.name = "UseNum"
pb.CS_SKEP_USEGOODS_USENUM_FIELD.full_name = ".CS_Skep_UseGoods.UseNum"
pb.CS_SKEP_USEGOODS_USENUM_FIELD.number = 3
pb.CS_SKEP_USEGOODS_USENUM_FIELD.index = 2
pb.CS_SKEP_USEGOODS_USENUM_FIELD.label = 2
pb.CS_SKEP_USEGOODS_USENUM_FIELD.has_default_value = false
pb.CS_SKEP_USEGOODS_USENUM_FIELD.default_value = 0
pb.CS_SKEP_USEGOODS_USENUM_FIELD.type = 13
pb.CS_SKEP_USEGOODS_USENUM_FIELD.cpp_type = 3

pb.CS_SKEP_USEGOODS_TARGET_FIELD.name = "Target"
pb.CS_SKEP_USEGOODS_TARGET_FIELD.full_name = ".CS_Skep_UseGoods.Target"
pb.CS_SKEP_USEGOODS_TARGET_FIELD.number = 4
pb.CS_SKEP_USEGOODS_TARGET_FIELD.index = 3
pb.CS_SKEP_USEGOODS_TARGET_FIELD.label = 1
pb.CS_SKEP_USEGOODS_TARGET_FIELD.has_default_value = true
pb.CS_SKEP_USEGOODS_TARGET_FIELD.default_value = 0
pb.CS_SKEP_USEGOODS_TARGET_FIELD.type = 4
pb.CS_SKEP_USEGOODS_TARGET_FIELD.cpp_type = 4

pb.CS_SKEP_USEGOODS.name = "CS_Skep_UseGoods"
pb.CS_SKEP_USEGOODS.full_name = ".CS_Skep_UseGoods"
pb.CS_SKEP_USEGOODS.nested_types = {}
pb.CS_SKEP_USEGOODS.enum_types = {}
pb.CS_SKEP_USEGOODS.fields = {pb.CS_SKEP_USEGOODS_SKEPUID_FIELD, pb.CS_SKEP_USEGOODS_GOODSUID_FIELD, pb.CS_SKEP_USEGOODS_USENUM_FIELD, pb.CS_SKEP_USEGOODS_TARGET_FIELD}
pb.CS_SKEP_USEGOODS.is_extendable = false
pb.CS_SKEP_USEGOODS.extensions = {}
pb.SC_SKEP_USEGOODS_RESULT_FIELD.name = "Result"
pb.SC_SKEP_USEGOODS_RESULT_FIELD.full_name = ".SC_Skep_UseGoods.Result"
pb.SC_SKEP_USEGOODS_RESULT_FIELD.number = 1
pb.SC_SKEP_USEGOODS_RESULT_FIELD.index = 0
pb.SC_SKEP_USEGOODS_RESULT_FIELD.label = 2
pb.SC_SKEP_USEGOODS_RESULT_FIELD.has_default_value = false
pb.SC_SKEP_USEGOODS_RESULT_FIELD.default_value = 0
pb.SC_SKEP_USEGOODS_RESULT_FIELD.type = 13
pb.SC_SKEP_USEGOODS_RESULT_FIELD.cpp_type = 3

pb.SC_SKEP_USEGOODS.name = "SC_Skep_UseGoods"
pb.SC_SKEP_USEGOODS.full_name = ".SC_Skep_UseGoods"
pb.SC_SKEP_USEGOODS.nested_types = {}
pb.SC_SKEP_USEGOODS.enum_types = {}
pb.SC_SKEP_USEGOODS.fields = {pb.SC_SKEP_USEGOODS_RESULT_FIELD}
pb.SC_SKEP_USEGOODS.is_extendable = false
pb.SC_SKEP_USEGOODS.extensions = {}
pb.CS_SKEP_SELLGOODS_SKEPUID_FIELD.name = "SkepUID"
pb.CS_SKEP_SELLGOODS_SKEPUID_FIELD.full_name = ".CS_Skep_SellGoods.SkepUID"
pb.CS_SKEP_SELLGOODS_SKEPUID_FIELD.number = 1
pb.CS_SKEP_SELLGOODS_SKEPUID_FIELD.index = 0
pb.CS_SKEP_SELLGOODS_SKEPUID_FIELD.label = 2
pb.CS_SKEP_SELLGOODS_SKEPUID_FIELD.has_default_value = false
pb.CS_SKEP_SELLGOODS_SKEPUID_FIELD.default_value = 0
pb.CS_SKEP_SELLGOODS_SKEPUID_FIELD.type = 4
pb.CS_SKEP_SELLGOODS_SKEPUID_FIELD.cpp_type = 4

pb.CS_SKEP_SELLGOODS_GOODSUID_FIELD.name = "GoodsUID"
pb.CS_SKEP_SELLGOODS_GOODSUID_FIELD.full_name = ".CS_Skep_SellGoods.GoodsUID"
pb.CS_SKEP_SELLGOODS_GOODSUID_FIELD.number = 2
pb.CS_SKEP_SELLGOODS_GOODSUID_FIELD.index = 1
pb.CS_SKEP_SELLGOODS_GOODSUID_FIELD.label = 3
pb.CS_SKEP_SELLGOODS_GOODSUID_FIELD.has_default_value = false
pb.CS_SKEP_SELLGOODS_GOODSUID_FIELD.default_value = {}
pb.CS_SKEP_SELLGOODS_GOODSUID_FIELD.type = 4
pb.CS_SKEP_SELLGOODS_GOODSUID_FIELD.cpp_type = 4

pb.CS_SKEP_SELLGOODS.name = "CS_Skep_SellGoods"
pb.CS_SKEP_SELLGOODS.full_name = ".CS_Skep_SellGoods"
pb.CS_SKEP_SELLGOODS.nested_types = {}
pb.CS_SKEP_SELLGOODS.enum_types = {}
pb.CS_SKEP_SELLGOODS.fields = {pb.CS_SKEP_SELLGOODS_SKEPUID_FIELD, pb.CS_SKEP_SELLGOODS_GOODSUID_FIELD}
pb.CS_SKEP_SELLGOODS.is_extendable = false
pb.CS_SKEP_SELLGOODS.extensions = {}
pb.SC_SKEP_SELLGOODS_SKEPUID_FIELD.name = "SkepUID"
pb.SC_SKEP_SELLGOODS_SKEPUID_FIELD.full_name = ".SC_Skep_SellGoods.SkepUID"
pb.SC_SKEP_SELLGOODS_SKEPUID_FIELD.number = 1
pb.SC_SKEP_SELLGOODS_SKEPUID_FIELD.index = 0
pb.SC_SKEP_SELLGOODS_SKEPUID_FIELD.label = 2
pb.SC_SKEP_SELLGOODS_SKEPUID_FIELD.has_default_value = false
pb.SC_SKEP_SELLGOODS_SKEPUID_FIELD.default_value = 0
pb.SC_SKEP_SELLGOODS_SKEPUID_FIELD.type = 4
pb.SC_SKEP_SELLGOODS_SKEPUID_FIELD.cpp_type = 4

pb.SC_SKEP_SELLGOODS_RESULT_FIELD.name = "Result"
pb.SC_SKEP_SELLGOODS_RESULT_FIELD.full_name = ".SC_Skep_SellGoods.Result"
pb.SC_SKEP_SELLGOODS_RESULT_FIELD.number = 2
pb.SC_SKEP_SELLGOODS_RESULT_FIELD.index = 1
pb.SC_SKEP_SELLGOODS_RESULT_FIELD.label = 2
pb.SC_SKEP_SELLGOODS_RESULT_FIELD.has_default_value = false
pb.SC_SKEP_SELLGOODS_RESULT_FIELD.default_value = 0
pb.SC_SKEP_SELLGOODS_RESULT_FIELD.type = 13
pb.SC_SKEP_SELLGOODS_RESULT_FIELD.cpp_type = 3

pb.SC_SKEP_SELLGOODS.name = "SC_Skep_SellGoods"
pb.SC_SKEP_SELLGOODS.full_name = ".SC_Skep_SellGoods"
pb.SC_SKEP_SELLGOODS.nested_types = {}
pb.SC_SKEP_SELLGOODS.enum_types = {}
pb.SC_SKEP_SELLGOODS.fields = {pb.SC_SKEP_SELLGOODS_SKEPUID_FIELD, pb.SC_SKEP_SELLGOODS_RESULT_FIELD}
pb.SC_SKEP_SELLGOODS.is_extendable = false
pb.SC_SKEP_SELLGOODS.extensions = {}

CS_Skep_SellGoods = protobuf.Message(pb.CS_SKEP_SELLGOODS)
CS_Skep_UseGoods = protobuf.Message(pb.CS_SKEP_USEGOODS)
MSG_SKEP_CREATE = 1
MSG_SKEP_DESTORY = 2
MSG_SKEP_NONE = 0
MSG_SKEP_SELLGOODS = 6
MSG_SKEP_SYNCPLACE = 3
MSG_SKEP_SYNCSIZE = 4
MSG_SKEP_USEGOODS = 5
SC_Skep_CreateSkep = protobuf.Message(pb.SC_SKEP_CREATESKEP)
SC_Skep_DestorySkep = protobuf.Message(pb.SC_SKEP_DESTORYSKEP)
SC_Skep_SellGoods = protobuf.Message(pb.SC_SKEP_SELLGOODS)
SC_Skep_SyncPlace = protobuf.Message(pb.SC_SKEP_SYNCPLACE)
SC_Skep_SyncPlace.SkepData = protobuf.Message(pb.SC_SKEP_SYNCPLACE_SKEPDATA)
SC_Skep_SyncSize = protobuf.Message(pb.SC_SKEP_SYNCSIZE)
SC_Skep_UseGoods = protobuf.Message(pb.SC_SKEP_USEGOODS)

