﻿// ReSharper disable InconsistentNaming
// ReSharper disable UnusedMember.Global

using System.Threading;

using Cysharp.Threading.Tasks;

using Thing;

namespace ThingCdExecutors
{
    /// <summary>
    /// 追踪导弹
    /// </summary>
    public class ThingCdExecutor_2 : ActorGunCdExecutor
	{
        /// <summary>
        /// 追踪目标
        /// </summary>
        public ThingBase Enemy { get; set; }
    }
}
