-- 实体相关定义
ENTITY_CLASS      =
{
	ENTITY_CLASS_NULL = 0,    -- 空类型
	ENTITY_CLASS_MEDICAMENT = 1, -- 药品类型
	ENTITY_CLASS_EQUIPMENT = 2, -- 装备类型
	ENTITY_CLASS_PLAYER = 3,  -- 玩家类型
	ENTITY_CLASS_SCENENPC = 4, -- NPC类型
	ENTITY_CLASS_MONSTER = 5, -- 怪物类型
	ENTITY_CLASS_COLLECT = 6, -- 采集物类型
	ENTITY_CLASS_TRAP = 7,    -- 陷阱类型
	ENTITY_CLASS_MAXID = 8,   -- 最大ID
	-- /************************************************************************/
	ENTITY_CLASS_GOODS = 10001, -- 物品类型(仅Get时使用)
	ENTITY_CLASS_CREATURE,    -- 生物类型(仅Get时使用)
	ENTITY_CLASS_HERO,        -- 英雄类型(仅客户端使用)
};

ENTITY_FIELD      =
{
	ENTITY_FIELD_BEGIN = 0,
	ENTITY_FIELD_ID    = 0, -- 模版ID

	ENTITY_FIELD_END   = 1,
};

CREATURE_FIELD    =
{
	CREATURE_FIELD_BEGIN = ENTITY_FIELD.ENTITY_FIELD_END,
	CREATURE_FIELD_LEVEL = ENTITY_FIELD.ENTITY_FIELD_END, -- 等级
	CREATURE_FIELD_CAMPTYPE = 2,                       -- 阵营类型
	CREATURE_FIELD_CAMPID = 3,                         -- 阵营ID
	CREATURE_FIELD_END = 4,
};

PLAYER_FIELD      =
{
	PLAYER_FIELD_BEGIN = CREATURE_FIELD.CREATURE_FIELD_END,
	PLAYER_FIELD_VOCATION = CREATURE_FIELD.CREATURE_FIELD_END, -- 职业
	PLAYER_FIELD_EMBLEM = 5,                                -- 成就等级
	PLAYER_FIELD_COUNTRY = 6,                               -- 国家
	PLAYER_FIELD_VIPLEVEL = 7,                              -- VIP等级
	PLAYER_FIELD_POWER = 8,                                 -- 战斗力
	PLAYER_FIELD_HP = 9,                                    -- 生命
	PLAYER_FIELD_MAXHP = 10,                                -- 最大生命

	PLAYER_FIELD_MOVE_SPEED = 11,                           -- 移动速度
	PLAYER_FIELD_DIE = 12,                                  -- 是否死亡
	PLAYER_FIELD_CUREXP = 13,                               -- 当前经验值
	PLAYER_FIELD_MAXEXP = 14,                               -- 经验值上限
	PLAYER_FIELD_SOCIETY = 15,                              -- 帮会
	PLAYER_FIELD_ATTACKMODE = 16,                           -- 攻击模式
	PLAYER_FIELD_PKVALUE = 17,                              -- PK值，杀气
	PLAYER_FIELD_LASTPKTIME = 18,                           -- 上次攻击玩家时间
	PLAYER_FIELD_EQUIPMODE = 19,                            -- 人物装备模型
	PLAYER_FIELD_MILITARY = 20,                             -- 军衔积分
	PLAYER_FIELD_WARBAND = 21,                              -- 战队
	PLAYER_FIELD_GENRE = 22,                                -- 流派
	PLAYER_FIELD_BLUEVIPLEVEL = 23,                         -- 蓝钻等级
	PLAYER_FIELD_BLUEVIPTYPE = 24,                          -- 蓝钻类型
	PLAYER_FIELD_BROADCAST = 25,                            -- !>以上为所有玩家可见属性

	PLAYER_FIELD_PHYSICS_ATTACK = 26,                       -- 物理攻击
	PLAYER_FIELD_MAGIC_ATTACK = 27,                         -- 法术攻击
	PLAYER_FIELD_PHYSICS_DEFENSE = 28,                      -- 物理防御
	PLAYER_FIELD_MAGIC_DEFENSE = 29,                        -- 法术防御
	PLAYER_FIELD_CRITICAL_STRIKE = 30,                      -- 暴击
	PLAYER_FIELD_ANTI_CRITICAL_STRIKE = 31,                 -- 防暴
	PLAYER_FIELD_PARRY = 32,                                -- 格挡
	PLAYER_FIELD_ANTI_PARRY = 33,                           -- 穿透
	PLAYER_FIELD_DODGE = 34,                                -- 闪避
	PLAYER_FIELD_HIT = 35,                                  -- 命中
	PLAYER_FIELD_ARMOR = 36,                                -- 护甲
	PLAYER_FIELD_ANTI_ARMOR = 37,                           -- 破甲
	PLAYER_FIELD_DAMAGE_REDUCTION = 38,                     -- 减伤
	PLAYER_FIELD_DAMAGE_ADD = 39,                           -- 伤害加深
	PLAYER_FIELD_PLAYERDAMAGE_ADD = 40,                     -- 对细胞伤害加深
	PLAYER_FIELD_MONSTERDAMAGE_ADD = 41,                    -- 对怪物伤害加深
	PLAYER_FIELD_DRAINRATE = 42,                            -- 吸血比例
	PLAYER_FIELD_REFLECTRATE = 43,                          -- 反伤比例

	PLAYER_FIELD_CURVIPSCORE = 44,                          -- 当前VIP积分
	PLAYER_FIELD_CURENERGY = 45,                            -- 当前体力值
	PLAYER_FIELD_DIAMOND = 46,                              -- 钻石数
	PLAYER_FIELD_MONEY = 47,                                -- 游戏币
	PLAYER_FIELD_MORAL = 48,                                -- 修为值
	PLAYER_FIELD_TALENT = 49,                               -- 天赋值(用做段位赛段位)
	PLAYER_FIELD_EXPLOIT = 50,                              -- 功勋值(用做段位赛星星数)
	PLAYER_FIELD_BINDDIAMOND = 51,                          -- 绑定元宝
	PLAYER_FIELD_COUPONS = 52,                              -- 点券
	PLAYER_FIELD_EXCHANGETICKET = 53,                       -- 兑换券
	PLAYER_FIELD_GOLDCOIN = 54,                             -- 金币(韩版用)
	PLAYER_FIELD_TRAIN = 55,                                -- 剩余帮贡值
	PLAYER_FIELD_PRESTIGE = 56,                             -- 声望值
	PLAYER_FIELD_CHARM = 57,                                -- 魅力值
	PLAYER_FIELD_RECHARGE = 58,                             -- 累计充值
	PLAYER_FIELD_FORBIDTIME = 59,                           -- 禁言时间
	PLAYER_FIELD_COST = 60,                                 -- 累计消费
	PLAYER_FIELD_SYNTHESISVALUE = 61,                       -- 熔炼值
	PLAYER_FIELD_EXPERIENCE = 62,                           -- 历练值
	PLAYER_FIELD_CURFINISHEDLEVELTASK = 63,                 -- 当前完成的升级任务等级(需完成对应等级才能转生继续升级)
	PLAYER_FIELD_FURY = 64,                                 -- 怒气值
	PLAYER_FIELD_ACHIEVE = 65,                              -- 成就值
	PLAYER_FIELD_ADFORBIDTIME = 66,                         -- 广告禁言
	PLAYER_FIELD_AURA = 67,                                 -- 灵气值
	PLAYER_FIELD_EXPADDRATE = 68,                           -- 经验加成比例
	PLAYER_FIELD_MONEYADDRATE = 69,                         -- 金钱加成比例
	PLAYER_FIELD_ACTVCOUNTSOCIETY = 70,                     -- 帮会累计活跃度
	PLAYER_FIELD_ACTVCOUNTWARBAND = 71,                     -- 战队累计活跃度
	PLAYER_FIELD_WARBANDSTACKVALUE = 72,                    -- 战队荣誉值
	PLAYER_FIELD_AGE = 73,                                  -- 年龄
	PLAYER_FIELD_VIP_EXTRAEXP = 74,                         -- VIP存储池经验值(额外)
	PLAYER_FIELD_VIP_EXTRAMONEY = 75,                       -- VIP存储池金钱值(额外)
	PLAYER_FIELD_VIP_EVERY_GIFTVALUE = 76,                  -- VIP每日礼包价值(VIP存储池)
	PLAYER_FIELD_REALRECHARGE = 77,                         -- 真实累计充值
	PLAYER_FIELD_NEWBIEPROPLEVEL = 78,                      -- 武学属性等级
	PLAYER_FIELD_NEWBIEPROPEXP = 79,                        -- 武学属性经验
	PLAYER_FIELD_SYNC_END = 80,                             -- !>以上为玩家自己可见属性
	PLAYER_FIELD_LOGINTIME = 81,                            -- 登录时间
	PLAYER_FIELD_LOGOUTTIME = 82,                           -- 登出时间
	PLAYER_FIELD_UPGRADETIME = 83,                          -- 最后升级时间
	PLAYER_FIELD_SAVEDBTIME = 84,                           -- 上次存盘时间
	PLAYER_FIELD_END = 85,                                  --
};

NPC_FIELD         =
{
	NPC_FIELD_BEGIN = CREATURE_FIELD.CREATURE_FIELD_END,
	NPC_FIELD_COUNTRY = CREATURE_FIELD.CREATURE_FIELD_END, -- 国家
	NPC_FIELD_EQUIPMODE = 5,                            -- 装备模型
	NPC_FIELD_WEAPONID = 6,                             -- 火之装备ID
	NPC_FIELD_WINGID = 7,                               -- 挂件ID
	NPC_FIELD_NEWWINGID = 8,                            -- 翅膀ID
	NPC_FIELD_TITLE1 = 9,                               -- 称号1
	NPC_FIELD_GENRE = 10,                               -- 流派
	NPC_FIELD_ZONE = 11,                                -- 占领区
	NPC_FIELD_EFFECTID = 12,                            -- 特效ID
	NPC_FIELD_END = 13,
};

TRAP_FIELD        =
{
	TRAP_FIELD_BEGIN = PLAYER_FIELD.PLAYER_FIELD_BROADCAST,

	-- 和PLAYER_FIELD_XXX同步
	TRAP_FIELD_PHYSICS_ATTACK = PLAYER_FIELD.PLAYER_FIELD_BROADCAST + 1,    -- 物理攻击
	TRAP_FIELD_MAGIC_ATTACK = PLAYER_FIELD.PLAYER_FIELD_BROADCAST + 2,      -- 法术攻击
	TRAP_FIELD_PHYSICS_DEFENSE = PLAYER_FIELD.PLAYER_FIELD_BROADCAST + 3,   -- 物理防御
	TRAP_FIELD_MAGIC_DEFENSE = PLAYER_FIELD.PLAYER_FIELD_BROADCAST + 4,     -- 法术防御
	TRAP_FIELD_CRITICAL_STRIKE = PLAYER_FIELD.PLAYER_FIELD_BROADCAST + 5,   -- 暴击
	TRAP_FIELD_ANTI_CRITICAL_STRIKE = PLAYER_FIELD.PLAYER_FIELD_BROADCAST + 6, -- 防暴
	TRAP_FIELD_PARRY = PLAYER_FIELD.PLAYER_FIELD_BROADCAST + 7,             -- 格挡
	TRAP_FIELD_ANTI_PARRY = PLAYER_FIELD.PLAYER_FIELD_BROADCAST + 8,        -- 穿透
	TRAP_FIELD_DODGE = PLAYER_FIELD.PLAYER_FIELD_BROADCAST + 9,             -- 闪避
	TRAP_FIELD_HIT = PLAYER_FIELD.PLAYER_FIELD_BROADCAST + 10,              -- 命中
	TRAP_FIELD_ARMOR = PLAYER_FIELD.PLAYER_FIELD_BROADCAST + 11,            -- 护甲
	TRAP_FIELD_ANTI_ARMOR = PLAYER_FIELD.PLAYER_FIELD_BROADCAST + 12,       -- 破甲
	TRAP_FIELD_DAMAGE_REDUCTION = PLAYER_FIELD.PLAYER_FIELD_BROADCAST + 13, -- 减伤
	TRAP_FIELD_DAMAGE_ADD = PLAYER_FIELD.PLAYER_FIELD_BROADCAST + 14,       -- 伤害加深
	TRAP_FIELD_PLAYERDAMAGE_ADD = PLAYER_FIELD.PLAYER_FIELD_BROADCAST + 15, -- 对细胞伤害加深
	TRAP_FIELD_MONSTERDAMAGE_ADD = PLAYER_FIELD.PLAYER_FIELD_BROADCAST + 16, -- 对怪物伤害加深
	TRAP_FIELD_DRAINRATE = PLAYER_FIELD.PLAYER_FIELD_BROADCAST + 17,        -- 吸血比例
	TRAP_FIELD_REFLECTRATE = PLAYER_FIELD.PLAYER_FIELD_BROADCAST + 18,      -- 反伤比例

	TRAP_FIELD_BROADCAST_BEGIN = PLAYER_FIELD.PLAYER_FIELD_BROADCAST + 19,
	TRAP_FIELD_C = PLAYER_FIELD.PLAYER_FIELD_BROADCAST + 19,
	TRAP_FIELD_D = PLAYER_FIELD.PLAYER_FIELD_BROADCAST + 20,
	TRAP_FIELD_E = PLAYER_FIELD.PLAYER_FIELD_BROADCAST + 21,
	TRAP_FIELD_F = PLAYER_FIELD.PLAYER_FIELD_BROADCAST + 22,
	TRAP_FIELD_BIRTH_FACE = PLAYER_FIELD.PLAYER_FIELD_BROADCAST + 23,
	TRAP_FIELD_END = PLAYER_FIELD.PLAYER_FIELD_BROADCAST + 24,
};

MONSTER_FIELD     =
{
	MONSTER_FIELD_BEGIN = PLAYER_FIELD.PLAYER_FIELD_BROADCAST,

	-- 和PLAYER_FIELD_XXX同步
	MONSTER_FIELD_PHYSICS_ATTACK = PLAYER_FIELD.PLAYER_FIELD_BROADCAST + 1,    -- 物理攻击
	MONSTER_FIELD_MAGIC_ATTACK = PLAYER_FIELD.PLAYER_FIELD_BROADCAST + 2,      -- 法术攻击
	MONSTER_FIELD_PHYSICS_DEFENSE = PLAYER_FIELD.PLAYER_FIELD_BROADCAST + 3,   -- 物理防御
	MONSTER_FIELD_MAGIC_DEFENSE = PLAYER_FIELD.PLAYER_FIELD_BROADCAST + 4,     -- 法术防御
	MONSTER_FIELD_CRITICAL_STRIKE = PLAYER_FIELD.PLAYER_FIELD_BROADCAST + 5,   -- 暴击
	MONSTER_FIELD_ANTI_CRITICAL_STRIKE = PLAYER_FIELD.PLAYER_FIELD_BROADCAST + 6, -- 防暴
	MONSTER_FIELD_PARRY = PLAYER_FIELD.PLAYER_FIELD_BROADCAST + 7,             -- 格挡
	MONSTER_FIELD_ANTI_PARRY = PLAYER_FIELD.PLAYER_FIELD_BROADCAST + 8,        -- 穿透
	MONSTER_FIELD_DODGE = PLAYER_FIELD.PLAYER_FIELD_BROADCAST + 9,             -- 闪避
	MONSTER_FIELD_HIT = PLAYER_FIELD.PLAYER_FIELD_BROADCAST + 10,              -- 命中
	MONSTER_FIELD_ARMOR = PLAYER_FIELD.PLAYER_FIELD_BROADCAST + 11,            -- 护甲
	MONSTER_FIELD_ANTI_ARMOR = PLAYER_FIELD.PLAYER_FIELD_BROADCAST + 12,       -- 破甲
	MONSTER_FIELD_DAMAGE_REDUCTION = PLAYER_FIELD.PLAYER_FIELD_BROADCAST + 13, -- 减伤
	MONSTER_FIELD_DAMAGE_ADD = PLAYER_FIELD.PLAYER_FIELD_BROADCAST + 14,       -- 伤害加深
	MONSTER_FIELD_PLAYERDAMAGE_ADD = PLAYER_FIELD.PLAYER_FIELD_BROADCAST + 15, -- 对细胞伤害加深
	MONSTER_FIELD_MONSTERDAMAGE_ADD = PLAYER_FIELD.PLAYER_FIELD_BROADCAST + 16, -- 对怪物伤害加深
	MONSTER_FIELD_DRAINRATE = PLAYER_FIELD.PLAYER_FIELD_BROADCAST + 17,        -- 吸血比例
	MONSTER_FIELD_REFLECTRATE = PLAYER_FIELD.PLAYER_FIELD_BROADCAST + 18,      -- 反伤比例

	MONSTER_FIELD_BROADCAST_BEGIN = PLAYER_FIELD.PLAYER_FIELD_BROADCAST + 19,
	MONSTER_FIELD_C = PLAYER_FIELD.PLAYER_FIELD_BROADCAST + 19,
	MONSTER_FIELD_D = PLAYER_FIELD.PLAYER_FIELD_BROADCAST + 20,
	MONSTER_FIELD_E = PLAYER_FIELD.PLAYER_FIELD_BROADCAST + 21,
	MONSTER_FIELD_F = PLAYER_FIELD.PLAYER_FIELD_BROADCAST + 22,
	MONSTER_FIELD_TYPE = PLAYER_FIELD.PLAYER_FIELD_BROADCAST + 23,
	MONSTER_FIELD_BIRTH_FACE = PLAYER_FIELD.PLAYER_FIELD_BROADCAST + 24,
	MONSTER_FIELD_SHOWMODEL = PLAYER_FIELD.PLAYER_FIELD_BROADCAST + 25,
	MONSTER_FIELD_END = PLAYER_FIELD.PLAYER_FIELD_BROADCAST + 26,
};

COLLECT_FIELD     =
{
	COLLECT_FIELD_BEGIN = CREATURE_FIELD.CREATURE_FIELD_END,
	COLLECT_FIELD_END = CREATURE_FIELD.CREATURE_FIELD_END,
};

GOODS_FIELD       =
{
	GOODS_FIELD_BEGIN = ENTITY_FIELD.ENTITY_FIELD_END,
	GOODS_FIELD_QTY = ENTITY_FIELD.ENTITY_FIELD_END, -- 物品数量
	GOODS_FIELD_SKEPID = 2,                       -- 物品所在篮子ID
	GOODS_FIELD_PLACE = 3,                        -- 物品所在篮子位置
	GOODS_FIELD_VALIDTIME = 4,                    -- 物品有效期
	GOODS_FIELD_END = 5,
};

MEDICAMENT_FIELD  =
{
	MEDICAMENT_FIELD_BEGIN = GOODS_FIELD.GOODS_FIELD_END,
	MEDICAMENT_FIELD_END = GOODS_FIELD.GOODS_FIELD_END,
};

EQUIPMENT_FIELD   =
{
	EQUIPMENT_FIELD_BEGIN = GOODS_FIELD.GOODS_FIELD_END,
	EQUIPMENT_FIELD_POWER = GOODS_FIELD.GOODS_FIELD_END, -- 战斗力（实战）
	EQUIPMENT_FIELD_QUALITY = 6,                      -- 装备品质
	EQUIPMENT_FIELD_STARNUM = 7,                      -- 星星数量
	EQUIPMENT_FIELD_SMELTEXP = 8,                     -- 强化经验(升星经验)
	EQUIPMENT_FIELD_ENCHLEVEL = 9,                    -- 附魔等级(强化的附魔值)
	EQUIPMENT_FIELD_SOULLEVEL = 10,                   -- 体魄等级
	EQUIPMENT_FIELD_SOULEXP = 11,                     -- 体魄经验
	EQUIPMENT_FIELD_ENDURE = 12,                      -- 体魄耐久度
	EQUIPMENT_FIELD_CUSTOM = 13,                      -- 消耗耐久度(升级的附魔值)
	EQUIPMENT_FIELD_SPIRITPOS = 14,                   -- 灵件加在体魄位置 孔位*100 + 新体魄序号
	EQUIPMENT_FIELD_SMELTLEVEL = 15,                  -- 强化等级

	EQUIPMENT_FIELD_EFFECTID1 = 16,                   -- 随机基础属性1
	EQUIPMENT_FIELD_EFFECTID2 = 17,                   -- 随机基础属性2
	EQUIPMENT_FIELD_EFFECTID3 = 18,                   -- 随机基础属性3
	EQUIPMENT_FIELD_EFFECTID4 = 19,                   -- 随机基础属性4
	EQUIPMENT_FIELD_EFFECTID5 = 20,                   -- 随机基础属性5

	EQUIPMENT_FIELD_ADDEFFECT_GROUP1_ID1 = 21,        --附加属性1-1
	EQUIPMENT_FIELD_ADDEFFECT_GROUP1_ID2 = 22,        --附加属性1-2
	EQUIPMENT_FIELD_ADDEFFECT_GROUP1_ID3 = 23,        --附加属性1-3
	EQUIPMENT_FIELD_ADDEFFECT_GROUP1_ID4 = 24,        --附加属性1-4
	EQUIPMENT_FIELD_ADDEFFECT_GROUP1_ID5 = 25,        --附加属性1-5

	EQUIPMENT_FIELD_ADDEFFECT_GROUP2_ID1 = 26,        --附加属性2-1
	EQUIPMENT_FIELD_ADDEFFECT_GROUP2_ID2 = 27,        --附加属性2-2
	EQUIPMENT_FIELD_ADDEFFECT_GROUP2_ID3 = 28,        --附加属性2-3
	EQUIPMENT_FIELD_ADDEFFECT_GROUP2_ID4 = 29,        --附加属性2-4
	EQUIPMENT_FIELD_ADDEFFECT_GROUP2_ID5 = 30,        --附加属性2-5

	EQUIPMENT_FIELD_ADDEFFECT_GROUP3_ID1 = 31,        --附加属性3-1
	EQUIPMENT_FIELD_ADDEFFECT_GROUP3_ID2 = 32,        --附加属性3-2
	EQUIPMENT_FIELD_ADDEFFECT_GROUP3_ID3 = 33,        --附加属性3-3
	EQUIPMENT_FIELD_ADDEFFECT_GROUP3_ID4 = 34,        --附加属性3-4
	EQUIPMENT_FIELD_ADDEFFECT_GROUP3_ID5 = 35,        --附加属性3-5

	EQUIPMENT_FIELD_ADDEFFECT_GROUP4_ID1 = 36,        --附加属性4-1
	EQUIPMENT_FIELD_ADDEFFECT_GROUP4_ID2 = 37,        --附加属性4-2
	EQUIPMENT_FIELD_ADDEFFECT_GROUP4_ID3 = 38,        --附加属性4-3
	EQUIPMENT_FIELD_ADDEFFECT_GROUP4_ID4 = 39,        --附加属性4-4
	EQUIPMENT_FIELD_ADDEFFECT_GROUP4_ID5 = 40,        --附加属性4-5

	EQUIPMENT_FIELD_ADDEFFECT_GROUP5_ID1 = 41,        --附加属性5-1
	EQUIPMENT_FIELD_ADDEFFECT_GROUP5_ID2 = 42,        --附加属性5-2
	EQUIPMENT_FIELD_ADDEFFECT_GROUP5_ID3 = 43,        --附加属性5-3
	EQUIPMENT_FIELD_ADDEFFECT_GROUP5_ID4 = 44,        --附加属性5-4
	EQUIPMENT_FIELD_ADDEFFECT_GROUP5_ID5 = 45,        --附加属性5-5

	EQUIPMENT_FIELD_ADDEFFECT_GROUP6_ID1 = 46,        --附加属性6-1
	EQUIPMENT_FIELD_ADDEFFECT_GROUP6_ID2 = 47,        --附加属性6-2
	EQUIPMENT_FIELD_ADDEFFECT_GROUP6_ID3 = 48,        --附加属性6-3
	EQUIPMENT_FIELD_ADDEFFECT_GROUP6_ID4 = 49,        --附加属性6-4
	EQUIPMENT_FIELD_ADDEFFECT_GROUP6_ID5 = 50,        --附加属性6-5

	EQUIPMENT_FIELD_ADDEFFECT_GROUP7_ID1 = 51,        --附加属性7-1
	EQUIPMENT_FIELD_ADDEFFECT_GROUP7_ID2 = 52,        --附加属性7-2
	EQUIPMENT_FIELD_ADDEFFECT_GROUP7_ID3 = 53,        --附加属性7-3
	EQUIPMENT_FIELD_ADDEFFECT_GROUP7_ID4 = 54,        --附加属性7-4
	EQUIPMENT_FIELD_ADDEFFECT_GROUP7_ID5 = 55,        --附加属性7-5

	EQUIPMENT_FIELD_ADDEFFECT_GROUP8_ID1 = 56,        --附加属性8-1
	EQUIPMENT_FIELD_ADDEFFECT_GROUP8_ID2 = 57,        --附加属性8-2
	EQUIPMENT_FIELD_ADDEFFECT_GROUP8_ID3 = 58,        --附加属性8-3
	EQUIPMENT_FIELD_ADDEFFECT_GROUP8_ID4 = 59,        --附加属性8-4
	EQUIPMENT_FIELD_ADDEFFECT_GROUP8_ID5 = 60,        --附加属性8-5

	EQUIPMENT_FIELD_ADDEFFECT_GROUP9_ID1 = 61,        --附加属性9-1
	EQUIPMENT_FIELD_ADDEFFECT_GROUP9_ID2 = 62,        --附加属性9-2
	EQUIPMENT_FIELD_ADDEFFECT_GROUP9_ID3 = 63,        --附加属性9-3
	EQUIPMENT_FIELD_ADDEFFECT_GROUP9_ID4 = 64,        --附加属性9-4
	EQUIPMENT_FIELD_ADDEFFECT_GROUP9_ID5 = 65,        --附加属性9-5

	EQUIPMENT_FIELD_ADDEFFECT_GROUP10_ID1 = 66,       --附加属性10-1
	EQUIPMENT_FIELD_ADDEFFECT_GROUP10_ID2 = 67,       --附加属性10-2
	EQUIPMENT_FIELD_ADDEFFECT_GROUP10_ID3 = 68,       --附加属性10-3
	EQUIPMENT_FIELD_ADDEFFECT_GROUP10_ID4 = 69,       --附加属性10-4
	EQUIPMENT_FIELD_ADDEFFECT_GROUP10_ID5 = 70,       --附加属性10-5

	EQUIPMENT_FIELD_ADDEFFECT_ADDITIONRATE1 = 71,     -- 附加属性组加成系数1
	EQUIPMENT_FIELD_ADDEFFECT_ADDITIONRATE2 = 72,     -- 附加属性组加成系数2
	EQUIPMENT_FIELD_ADDEFFECT_ADDITIONRATE3 = 73,     -- 附加属性组加成系数3
	EQUIPMENT_FIELD_ADDEFFECT_ADDITIONRATE4 = 74,     -- 附加属性组加成系数4
	EQUIPMENT_FIELD_ADDEFFECT_ADDITIONRATE5 = 75,     -- 附加属性组加成系数5
	EQUIPMENT_FIELD_ADDEFFECT_ADDITIONRATE6 = 76,     -- 附加属性组加成系数6
	EQUIPMENT_FIELD_ADDEFFECT_ADDITIONRATE7 = 77,     -- 附加属性组加成系数7
	EQUIPMENT_FIELD_ADDEFFECT_ADDITIONRATE8 = 78,     -- 附加属性组加成系数8
	EQUIPMENT_FIELD_ADDEFFECT_ADDITIONRATE9 = 79,     -- 附加属性组加成系数9
	EQUIPMENT_FIELD_ADDEFFECT_ADDITIONRATE10 = 80,    -- 附加属性组加成系数10

	EQUIPMENT_FIELD_END = 81,
};

-- 最大虚拟物品ID
VIRTUAL_GOODS_MAX = 1000
-- 虚拟物品
VIRTUAL_GOODS     = {
	[1]  = PLAYER_FIELD.PLAYER_FIELD_CUREXP,
	[2]  = PLAYER_FIELD.PLAYER_FIELD_CURENERGY,
	[3]  = PLAYER_FIELD.PLAYER_FIELD_DIAMOND,  --钻石
	[4]  = PLAYER_FIELD.PLAYER_FIELD_MONEY,    --游戏币
	[5]  = PLAYER_FIELD.PLAYER_FIELD_MORAL,    --修为
	[6]  = PLAYER_FIELD.PLAYER_FIELD_TRAIN,    --帮贡(历练)
	[7]  = PLAYER_FIELD.PLAYER_FIELD_PRESTIGE, --声望
	[8]  = PLAYER_FIELD.PLAYER_FIELD_TALENT,
	[9]  = PLAYER_FIELD.PLAYER_FIELD_EXPLOIT,  --功勋
	[10] = PLAYER_FIELD.PLAYER_FIELD_BINDDIAMOND, --绑定元宝
	[11] = PLAYER_FIELD.PLAYER_FIELD_COUPONS,  --点券
	[12] = PLAYER_FIELD.PLAYER_FIELD_EXPERIENCE,
	[13] = PLAYER_FIELD.PLAYER_FIELD_ACHIEVE,
	[14] = -1,
	[15] = -1,
	[16] = PLAYER_FIELD.PLAYER_FIELD_EXCHANGETICKET, --夺宝券
	[17] = PLAYER_FIELD.PLAYER_FIELD_CURVIPSCORE,
	[18] = PLAYER_FIELD.PLAYER_FIELD_GOLDCOIN,    --虚拟金币
	[19] = PLAYER_FIELD.PLAYER_FIELD_WARBANDSTACKVALUE,
	[20] = -1,
	[21] = -1,
	[22] = PLAYER_FIELD.PLAYER_FIELD_NEWBIEPROPEXP,
}