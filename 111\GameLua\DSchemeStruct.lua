--[[
********************************************************************
    created:
    author :
    purpose:    装备相关：全局常量、枚举、中文字符
*********************************************************************
--]]

--local luaID = ('DSchemeStruct')
local luaIDL = ('HelperL')

--#region 展示屏蔽

-- PROP_TYPE = {
--     PROP_EFFECT_HP = 0,                 -- 生命上限
--     PROP_EFFECT_ATK = 1,                -- 攻击
--     PROP_EFFECT_DEF = 2,                -- 防御
--     PROP_EFFECT_PATK = 3,               -- 物攻
--     PROP_EFFECT_MATK = 4,               -- 法攻
--     PROP_EFFECT_PDEF = 5,               -- 物防
--     PROP_EFFECT_MDEF = 6,               -- 法防
--     PROP_EFFECT_CS = 7,                 -- 暴击
--     PROP_EFFECT_ACS = 8,                -- 防暴
--     PROP_EFFECT_PARRY = 9,              -- 格挡
--     PROP_EFFECT_AP = 10,                -- 穿透
--     PROP_EFFECT_DODGE = 11,             -- 闪避
--     PROP_EFFECT_HIT = 12,               -- 命中
--     PROP_EFFECT_ARMOR = 13,             -- 护甲
--     PROP_EFFECT_AARMOR = 14,            -- 破甲
--     PROP_EFFECT_DAMR = 15,              -- 减伤
--     PROP_EFFECT_DAMA = 16,              -- 加伤
--     PROP_EFFECT_MS = 17,                -- 移动速度
--     PROP_EFFECT_ATKDEF = 18,            -- 攻防
--     PROP_EFFECT_PDAMA = 19,             -- 对细胞伤害加深
--     PROP_EFFECT_MDAMA = 20,             -- 对怪物伤害加深

--     PROP_EFFECT_UPBEGIN = 20,           -- 觉醒属性开始
--     PROP_EFFECT_HPUP = 21,              -- 生命觉醒
--     PROP_EFFECT_ATKUP = 22,             -- 攻击觉醒
--     PROP_EFFECT_DEFUP = 23,             -- 防御觉醒
--     PROP_EFFECT_PATKUP = 24,            -- 物攻觉醒
--     PROP_EFFECT_MATKUP = 25,            -- 法攻觉醒
--     PROP_EFFECT_PDEFUP = 26,            -- 物防觉醒
--     PROP_EFFECT_MDEFUP = 27,            -- 法防觉醒
--     PROP_EFFECT_ALLUP = 28,             -- 全属性觉醒

--     PROP_EFFECT_GODWEAPONUPBEGIN = 30,  -- 体魄总属性开始
--     PROP_EFFECT_GODWEAPONALLUP = 31,    -- 体魄总属性(不含灵件)
--     PROP_EFFECT_GODWEAPONHPUP = 32,     -- 体魄总生命属性(不含灵件)
--     PROP_EFFECT_GODWEAPONPATKUP = 33,   -- 体魄总物攻属性(不含灵件)
--     PROP_EFFECT_GODWEAPONMATKUP = 34,   -- 体魄总法攻属性(不含灵件)
--     PROP_EFFECT_GODWEAPONPDEFUP = 35,   -- 体魄总物防属性(不含灵件)
--     PROP_EFFECT_GODWEAPONMDEFUP = 36,   -- 体魄总法防属性(不含灵件)
--     PROP_EFFECT_GODWEAPONUPEND = 37,    -- 体魄总属性结束

--     PROP_EFFECT_SPIRITUPBEGIN = 40,     -- 体魄灵件总属性开始
--     PROP_EFFECT_SPIRITALLUP = 41,       -- 体魄灵件总属性
--     PROP_EFFECT_SPIRITHPUP = 42,        -- 体魄灵件总生命属性
--     PROP_EFFECT_SPIRITPATKUP = 43,      -- 体魄灵件总物攻属性
--     PROP_EFFECT_SPIRITMATKUP = 44,      -- 体魄灵件总法攻属性
--     PROP_EFFECT_SPIRITPDEFUP = 45,      -- 体魄灵件总物防属性
--     PROP_EFFECT_SPIRITMDEFUP = 46,      -- 体魄灵件总法防属性
--     PROP_EFFECT_SPIRITUPEND = 47,       -- 体魄灵件总属性结束

--     PROP_EFFECT_TOTALEQUIPUPBEGIN = 50, -- 装备总属性开始
--     PROP_EFFECT_TOTALEQUIPALLUP = 51,   -- 装备总属性(不含宝石)
--     PROP_EFFECT_TOTALEQUIPHPUP = 52,    -- 装备总生命属性(不含宝石)
--     PROP_EFFECT_TOTALEQUIPPATKUP = 53,  -- 装备总物攻属性(不含宝石)
--     PROP_EFFECT_TOTALEQUIPMATKUP = 54,  -- 装备总法攻属性(不含宝石)
--     PROP_EFFECT_TOTALEQUIPPDEFUP = 55,  -- 装备总物防属性(不含宝石)
--     PROP_EFFECT_TOTALEQUIPMDEFUP = 56,  -- 装备总法防属性(不含宝石)
--     PROP_EFFECT_TOTALEQUIPUPEND = 57,   -- 装备总属性结束

--     PROP_EFFECT_MOUNTUPBEGIN = 60,      -- 坐骑总属性开始
--     PROP_EFFECT_MOUNTALLUP = 61,        -- 坐骑总属性(包含坐骑装备)
--     PROP_EFFECT_MOUNTHPUP = 62,         -- 坐骑总生命属性(包含坐骑装备)
--     PROP_EFFECT_MOUNTPATKUP = 63,       -- 坐骑总物攻属性(包含坐骑装备)
--     PROP_EFFECT_MOUNTMATKUP = 64,       -- 坐骑总法攻属性(包含坐骑装备)
--     PROP_EFFECT_MOUNTPDEFUP = 65,       -- 坐骑总物防属性(包含坐骑装备)
--     PROP_EFFECT_MOUNTMDEFUP = 66,       -- 坐骑总法防属性(包含坐骑装备)
--     PROP_EFFECT_MOUNTUPEND = 67,        -- 坐骑总属性结束

--     PROP_EFFECT_WINGUPBEGIN = 70,       -- 仙器总属性开始
--     PROP_EFFECT_WINGALLUP = 71,         -- 仙器总属性(包含仙器装备)
--     PROP_EFFECT_WINGHPUP = 72,          -- 仙器总生命属性(包含仙器装备)
--     PROP_EFFECT_WINGPATKUP = 73,        -- 仙器总物攻属性(包含仙器装备)
--     PROP_EFFECT_WINGMATKUP = 74,        -- 仙器总法攻属性(包含仙器装备)
--     PROP_EFFECT_WINGPDEFUP = 75,        -- 仙器总物防属性(包含仙器装备)
--     PROP_EFFECT_WINGMDEFUP = 76,        -- 仙器总法防属性(包含仙器装备)
--     PROP_EFFECT_WINGUPEND = 77,         -- 仙器总属性结束

--     PROP_EFFECT_NEWWINGUPBEGIN = 80,    -- 羽翼总属性开始
--     PROP_EFFECT_NEWWINGALLUP = 81,      -- 羽翼总属性(包含羽翼装备)
--     PROP_EFFECT_NEWWINGHPUP = 82,       -- 羽翼总生命属性(包含羽翼装备)
--     PROP_EFFECT_NEWWINGPATKUP = 83,     -- 羽翼总物攻属性(包含羽翼装备)
--     PROP_EFFECT_NEWWINGMATKUP = 84,     -- 羽翼总法攻属性(包含羽翼装备)
--     PROP_EFFECT_NEWWINGPDEFUP = 85,     -- 羽翼总物防属性(包含羽翼装备)
--     PROP_EFFECT_NEWWINGMDEFUP = 86,     -- 羽翼总法防属性(包含羽翼装备)
--     PROP_EFFECT_NEWWINGUPEND = 87,      -- 羽翼总属性结束

--     PROP_EFFECT_EXPADDRATE = 88,        -- 经验加成比例
--     PROP_EFFECT_MONEYADDRATE = 89,      -- 金钱加成比例
--     PROP_EFFECT_DRAINRATE = 90,         -- 吸血比例
--     PROP_EFFECT_REFLECTRATE = 91,       -- 反伤比例

--     PROP_EFFECT_SPECIALSKILL = 92,      -- 特技

--     PROP_EFFECT_MAX = 93,
-- }

-- PROP_EFFECT_NAME = {
--     [PROP_TYPE.PROP_EFFECT_HP] = GetGameText(luaID, 1),
--     [PROP_TYPE.PROP_EFFECT_ATK] = GetGameText(luaID, 2),
--     [PROP_TYPE.PROP_EFFECT_DEF] = GetGameText(luaID, 3),
--     [PROP_TYPE.PROP_EFFECT_PATK] = GetGameText(luaID, 4),
--     [PROP_TYPE.PROP_EFFECT_MATK] = GetGameText(luaID, 5),
--     [PROP_TYPE.PROP_EFFECT_PDEF] = GetGameText(luaID, 6),
--     [PROP_TYPE.PROP_EFFECT_MDEF] = GetGameText(luaID, 7),
--     [PROP_TYPE.PROP_EFFECT_CS] = GetGameText(luaID, 8),
--     [PROP_TYPE.PROP_EFFECT_ACS] = GetGameText(luaID, 9),
--     [PROP_TYPE.PROP_EFFECT_PARRY] = GetGameText(luaID, 10),
--     [PROP_TYPE.PROP_EFFECT_AP] = GetGameText(luaID, 11),
--     [PROP_TYPE.PROP_EFFECT_DODGE] = GetGameText(luaID, 12),
--     [PROP_TYPE.PROP_EFFECT_HIT] = GetGameText(luaID, 13),
--     [PROP_TYPE.PROP_EFFECT_ARMOR] = GetGameText(luaID, 14),
--     [PROP_TYPE.PROP_EFFECT_AARMOR] = GetGameText(luaID, 15),
--     [PROP_TYPE.PROP_EFFECT_DAMR] = GetGameText(luaID, 16),
--     [PROP_TYPE.PROP_EFFECT_DAMA] = GetGameText(luaID, 17),
--     [PROP_TYPE.PROP_EFFECT_MS] = GetGameText(luaID, 18),
--     [PROP_TYPE.PROP_EFFECT_ATKDEF] = GetGameText(luaID, 19),
--     [PROP_TYPE.PROP_EFFECT_PDAMA] = GetGameText(luaID, 59),
--     [PROP_TYPE.PROP_EFFECT_MDAMA] = GetGameText(luaID, 60),

--     [PROP_TYPE.PROP_EFFECT_HPUP] = GetGameText(luaID, 20),
--     [PROP_TYPE.PROP_EFFECT_ATKUP] = GetGameText(luaID, 21),
--     [PROP_TYPE.PROP_EFFECT_DEFUP] = GetGameText(luaID, 22),
--     [PROP_TYPE.PROP_EFFECT_PATKUP] = GetGameText(luaID, 23),
--     [PROP_TYPE.PROP_EFFECT_MATKUP] = GetGameText(luaID, 24),
--     [PROP_TYPE.PROP_EFFECT_PDEFUP] = GetGameText(luaID, 25),
--     [PROP_TYPE.PROP_EFFECT_MDEFUP] = GetGameText(luaID, 26),
--     [PROP_TYPE.PROP_EFFECT_ALLUP] = GetGameText(luaID, 27),

--     [PROP_TYPE.PROP_EFFECT_GODWEAPONALLUP] = GetGameText(luaID, 28),
--     [PROP_TYPE.PROP_EFFECT_GODWEAPONHPUP] = GetGameText(luaID, 29),
--     [PROP_TYPE.PROP_EFFECT_GODWEAPONPATKUP] = GetGameText(luaID, 30),
--     [PROP_TYPE.PROP_EFFECT_GODWEAPONMATKUP] = GetGameText(luaID, 31),
--     [PROP_TYPE.PROP_EFFECT_GODWEAPONPDEFUP] = GetGameText(luaID, 32),
--     [PROP_TYPE.PROP_EFFECT_GODWEAPONMDEFUP] = GetGameText(luaID, 33),

--     [PROP_TYPE.PROP_EFFECT_SPIRITALLUP] = GetGameText(luaID, 34),
--     [PROP_TYPE.PROP_EFFECT_SPIRITHPUP] = GetGameText(luaID, 35),
--     [PROP_TYPE.PROP_EFFECT_SPIRITPATKUP] = GetGameText(luaID, 36),
--     [PROP_TYPE.PROP_EFFECT_SPIRITMATKUP] = GetGameText(luaID, 37),
--     [PROP_TYPE.PROP_EFFECT_SPIRITPDEFUP] = GetGameText(luaID, 38),
--     [PROP_TYPE.PROP_EFFECT_SPIRITMDEFUP] = GetGameText(luaID, 39),

--     [PROP_TYPE.PROP_EFFECT_TOTALEQUIPALLUP] = GetGameText(luaID, 40),
--     [PROP_TYPE.PROP_EFFECT_TOTALEQUIPHPUP] = GetGameText(luaID, 41),
--     [PROP_TYPE.PROP_EFFECT_TOTALEQUIPPATKUP] = GetGameText(luaID, 42),
--     [PROP_TYPE.PROP_EFFECT_TOTALEQUIPMATKUP] = GetGameText(luaID, 43),
--     [PROP_TYPE.PROP_EFFECT_TOTALEQUIPPDEFUP] = GetGameText(luaID, 44),
--     [PROP_TYPE.PROP_EFFECT_TOTALEQUIPMDEFUP] = GetGameText(luaID, 45),

--     [PROP_TYPE.PROP_EFFECT_MOUNTALLUP] = GetGameText(luaID, 46),
--     [PROP_TYPE.PROP_EFFECT_MOUNTHPUP] = GetGameText(luaID, 47),
--     [PROP_TYPE.PROP_EFFECT_MOUNTPATKUP] = GetGameText(luaID, 48),
--     [PROP_TYPE.PROP_EFFECT_MOUNTMATKUP] = GetGameText(luaID, 49),
--     [PROP_TYPE.PROP_EFFECT_MOUNTPDEFUP] = GetGameText(luaID, 50),
--     [PROP_TYPE.PROP_EFFECT_MOUNTMDEFUP] = GetGameText(luaID, 51),

--     [PROP_TYPE.PROP_EFFECT_WINGALLUP] = GetGameText(luaID, 63),
--     [PROP_TYPE.PROP_EFFECT_WINGHPUP] = GetGameText(luaID, 64),
--     [PROP_TYPE.PROP_EFFECT_WINGPATKUP] = GetGameText(luaID, 65),
--     [PROP_TYPE.PROP_EFFECT_WINGMATKUP] = GetGameText(luaID, 66),
--     [PROP_TYPE.PROP_EFFECT_WINGMDEFUP] = GetGameText(luaID, 67),
--     [PROP_TYPE.PROP_EFFECT_WINGUPEND] = GetGameText(luaID, 68),

--     [PROP_TYPE.PROP_EFFECT_NEWWINGALLUP] = GetGameText(luaID, 69),
--     [PROP_TYPE.PROP_EFFECT_NEWWINGHPUP] = GetGameText(luaID, 70),
--     [PROP_TYPE.PROP_EFFECT_NEWWINGPATKUP] = GetGameText(luaID, 71),
--     [PROP_TYPE.PROP_EFFECT_NEWWINGMATKUP] = GetGameText(luaID, 72),
--     [PROP_TYPE.PROP_EFFECT_NEWWINGPDEFUP] = GetGameText(luaID, 73),
--     [PROP_TYPE.PROP_EFFECT_NEWWINGMDEFUP] = GetGameText(luaID, 74),

--     [PROP_TYPE.PROP_EFFECT_EXPADDRATE] = GetGameText(luaID, 61),
--     [PROP_TYPE.PROP_EFFECT_MONEYADDRATE] = GetGameText(luaID, 62),
--     [PROP_TYPE.PROP_EFFECT_DRAINRATE] = GetGameText(luaID, 75),
--     [PROP_TYPE.PROP_EFFECT_REFLECTRATE] = GetGameText(luaID, 76),
--     [PROP_TYPE.PROP_EFFECT_SPECIALSKILL] = GetGameText(luaID, 77),
-- }

-- ---@type integer[] 属性显示类型：0数值、1百分比
-- PROP_EFFECT_SHOWTYPE = {
--     [PROP_TYPE.PROP_EFFECT_HP] = 0,
--     [PROP_TYPE.PROP_EFFECT_ATK] = 1,
--     [PROP_TYPE.PROP_EFFECT_DEF] = 1,
--     [PROP_TYPE.PROP_EFFECT_PATK] = 0,
--     [PROP_TYPE.PROP_EFFECT_MATK] = 1,
--     [PROP_TYPE.PROP_EFFECT_PDEF] = 1,
--     [PROP_TYPE.PROP_EFFECT_MDEF] = 1,
--     [PROP_TYPE.PROP_EFFECT_CS] = 1,
--     [PROP_TYPE.PROP_EFFECT_ACS] = 0,
--     [PROP_TYPE.PROP_EFFECT_PARRY] = 0,
--     [PROP_TYPE.PROP_EFFECT_AP] = 1,
--     [PROP_TYPE.PROP_EFFECT_DODGE] = 1,
--     [PROP_TYPE.PROP_EFFECT_HIT] = 1,
--     [PROP_TYPE.PROP_EFFECT_ARMOR] = 1,
--     [PROP_TYPE.PROP_EFFECT_AARMOR] = 1,
--     [PROP_TYPE.PROP_EFFECT_DAMR] = 1,
--     [PROP_TYPE.PROP_EFFECT_DAMA] = 1,
--     [PROP_TYPE.PROP_EFFECT_MS] = 1,
--     [PROP_TYPE.PROP_EFFECT_ATKDEF] = 1,
--     [PROP_TYPE.PROP_EFFECT_PDAMA] = 1,
--     [PROP_TYPE.PROP_EFFECT_MDAMA] = 1,

--     [PROP_TYPE.PROP_EFFECT_HPUP] = 1,
--     [PROP_TYPE.PROP_EFFECT_ATKUP] = 1,
--     [PROP_TYPE.PROP_EFFECT_DEFUP] = 1,
--     [PROP_TYPE.PROP_EFFECT_PATKUP] = 1,
--     [PROP_TYPE.PROP_EFFECT_MATKUP] = 1,
--     [PROP_TYPE.PROP_EFFECT_PDEFUP] = 1,
--     [PROP_TYPE.PROP_EFFECT_MDEFUP] = 1,
--     [PROP_TYPE.PROP_EFFECT_ALLUP] = 1,

--     [PROP_TYPE.PROP_EFFECT_GODWEAPONALLUP] = 1,
--     [PROP_TYPE.PROP_EFFECT_GODWEAPONHPUP] = 1,
--     [PROP_TYPE.PROP_EFFECT_GODWEAPONPATKUP] = 1,
--     [PROP_TYPE.PROP_EFFECT_GODWEAPONMATKUP] = 1,
--     [PROP_TYPE.PROP_EFFECT_GODWEAPONPDEFUP] = 1,
--     [PROP_TYPE.PROP_EFFECT_GODWEAPONMDEFUP] = 1,

--     [PROP_TYPE.PROP_EFFECT_SPIRITALLUP] = 1,
--     [PROP_TYPE.PROP_EFFECT_SPIRITHPUP] = 1,
--     [PROP_TYPE.PROP_EFFECT_SPIRITPATKUP] = 1,
--     [PROP_TYPE.PROP_EFFECT_SPIRITMATKUP] = 1,
--     [PROP_TYPE.PROP_EFFECT_SPIRITPDEFUP] = 1,
--     [PROP_TYPE.PROP_EFFECT_SPIRITMDEFUP] = 1,

--     [PROP_TYPE.PROP_EFFECT_TOTALEQUIPALLUP] = 1,
--     [PROP_TYPE.PROP_EFFECT_TOTALEQUIPHPUP] = 1,
--     [PROP_TYPE.PROP_EFFECT_TOTALEQUIPPATKUP] = 1,
--     [PROP_TYPE.PROP_EFFECT_TOTALEQUIPMATKUP] = 1,
--     [PROP_TYPE.PROP_EFFECT_TOTALEQUIPPDEFUP] = 1,
--     [PROP_TYPE.PROP_EFFECT_TOTALEQUIPMDEFUP] = 1,

--     [PROP_TYPE.PROP_EFFECT_MOUNTALLUP] = 1,
--     [PROP_TYPE.PROP_EFFECT_MOUNTHPUP] = 1,
--     [PROP_TYPE.PROP_EFFECT_MOUNTPATKUP] = 1,
--     [PROP_TYPE.PROP_EFFECT_MOUNTMATKUP] = 1,
--     [PROP_TYPE.PROP_EFFECT_MOUNTPDEFUP] = 1,
--     [PROP_TYPE.PROP_EFFECT_MOUNTMDEFUP] = 1,
--     [PROP_TYPE.PROP_EFFECT_EXPADDRATE] = 1,
--     [PROP_TYPE.PROP_EFFECT_MONEYADDRATE] = 1,
--     [PROP_TYPE.PROP_EFFECT_DRAINRATE] = 1,
--     [PROP_TYPE.PROP_EFFECT_REFLECTRATE] = 1,
-- }

-- PROP_EFFECT_POWER = {
--     [PROP_TYPE.PROP_EFFECT_HP] = POWER_COEF.POWER_COEF_HP,
--     [PROP_TYPE.PROP_EFFECT_ATK] = POWER_COEF.POWER_COEF_ATK,
--     [PROP_TYPE.PROP_EFFECT_DEF] = POWER_COEF.POWER_COEF_DEF,
--     [PROP_TYPE.PROP_EFFECT_PATK] = POWER_COEF.POWER_COEF_PATK,
--     [PROP_TYPE.PROP_EFFECT_MATK] = POWER_COEF.POWER_COEF_MATK,
--     [PROP_TYPE.PROP_EFFECT_PDEF] = POWER_COEF.POWER_COEF_PDEF,
--     [PROP_TYPE.PROP_EFFECT_MDEF] = POWER_COEF.POWER_COEF_MDEF,
--     [PROP_TYPE.PROP_EFFECT_CS] = POWER_COEF.POWER_COEF_CRITICAL_STRIKE,
--     [PROP_TYPE.PROP_EFFECT_ACS] = POWER_COEF.POWER_COEF_ANTI_CRITICAL_STRIKE,
--     [PROP_TYPE.PROP_EFFECT_PARRY] = POWER_COEF.POWER_COEF_PARRY,
--     [PROP_TYPE.PROP_EFFECT_AP] = POWER_COEF.POWER_COEF_ANTI_PARRY,
--     [PROP_TYPE.PROP_EFFECT_DODGE] = POWER_COEF.POWER_COEF_DODGE,
--     [PROP_TYPE.PROP_EFFECT_HIT] = POWER_COEF.POWER_COEF_HIT,
--     [PROP_TYPE.PROP_EFFECT_ARMOR] = POWER_COEF.POWER_COEF_ARMOR,
--     [PROP_TYPE.PROP_EFFECT_AARMOR] = POWER_COEF.POWER_COEF_ANTI_ARMOR,
--     [PROP_TYPE.PROP_EFFECT_DAMR] = POWER_COEF.POWER_COEF_DAMAGE_REDUCTION,
--     [PROP_TYPE.PROP_EFFECT_DAMA] = POWER_COEF.POWER_COEF_DAMAGE_ADD,
--     [PROP_TYPE.PROP_EFFECT_MS] = POWER_COEF.POWER_COEF_SPD,
--     [PROP_TYPE.PROP_EFFECT_ATKDEF] = POWER_COEF.POWER_COEF_ATK_DEF,
-- }

-- --星期中文字符
-- NumTransformWeeks = {
--     GetGameText(luaID, 52),
--     GetGameText(luaID, 53),
--     GetGameText(luaID, 54),
--     GetGameText(luaID, 55),
--     GetGameText(luaID, 56),
--     GetGameText(luaID, 57),
--     GetGameText(luaID, 58),
-- }

--#endregion

IOS_DeviceGeneration = {
    Unknown = 0,
    iPhone = 1,
    iPhone3G = 2,
    iPhone3GS = 3,
    iPodTouch1Gen = 4,
    iPodTouch2Gen = 5,
    iPodTouch3Gen = 6,
    iPad1Gen = 7,
    iPhone4 = 8,
    iPodTouch4Gen = 9,
    iPad2Gen = 10,
    iPhone4S = 11,
    iPad3Gen = 12,
    iPhone5 = 13,
    iPodTouch5Gen = 14,
    iPadMini1Gen = 15,
    iPad4Gen = 16,
    iPhone5C = 17,
    iPhone5S = 18,
    iPadAir1 = 19,
    iPadMini2Gen = 20,
    iPhone6 = 21,
    iPhone6Plus = 22,
    iPadMini3Gen = 23,
    iPadAir2 = 24,
    iPhone6S = 25,
    iPhone6SPlus = 26,
    iPadPro1Gen = 27,
    iPadMini4Gen = 28,
    iPhoneSE1Gen = 29,
    iPadPro10Inch1Gen = 30,
    iPhone7 = 31,
    iPhone7Plus = 32,
    iPodTouch6Gen = 33,
    iPad5Gen = 34,
    iPadPro2Gen = 35,
    iPadPro10Inch2Gen = 36,
    iPhone8 = 37,
    iPhone8Plus = 38,
    iPhoneX = 39,
    iPhoneUnknown = 10001,
    iPadUnknown = 10002,
    iPodTouchUnknown = 10003,
}

---@type integer[] 属性显示类型：1数值、2百分比
AttributeShowType = {
	[0] = 1,
	[1] = 2,
	[2] = 1,
	[3] = 1,
	[4] = 2,
	[5] = 2,
	[6] = 2,
	[7] = 2,
	[8] = 1,
	[9] = 1,
	[10] = 2,
	[11] = 2,
	[12] = 2,
	[13] = 2,
	[14] = 2,
	[15] = 2,
	[16] = 2,
	[17] = 2,
	[18] = 2,
	[19] = 0,
	[20] = 2,
	[21] = 2,
	[23] = 2,
	[25] = 2,
	[27] = 2,
	[45] = 2,
	[46] = 2,
	[88] = 2,
	[89] = 2,
	[90] = 2,
	[91] = 2,
}

---@class EquipSmeltAttributeEnum 升级表属性枚举
EquipSmeltAttributeEnum = {
    HP = 1,
    Attack = 2,
    Defense = 3,
    PhysicsAttack = 4,
    MagicAttack = 5,
    PhysicsDefense = 6,
    MagicDefense = 7,
    CriticalStrike = 8,
    AntiCriticalStrike = 9,
    Parry = 10,
    AntiParry = 11,
    Dodge = 12,
    Hit = 13,
    Armor = 14,
    AntiArmor = 15,
    DamageReductin = 16,
    DamageAdd = 17,
    MoveSpeed = 18,
    AttackDefense = 19,
    EquipEffectID = 20,
}

---@type string[] 升级表属性名称
EquipSmeltAttributeName = {
    [EquipSmeltAttributeEnum.HP] = GetGameText(luaIDL, 17),
    [EquipSmeltAttributeEnum.Attack] = GetGameText(luaIDL, 18),
    [EquipSmeltAttributeEnum.Defense] = GetGameText(luaIDL, 19),
    [EquipSmeltAttributeEnum.PhysicsAttack] = GetGameText(luaIDL, 20),
    [EquipSmeltAttributeEnum.MagicAttack] = GetGameText(luaIDL, 21),
    [EquipSmeltAttributeEnum.PhysicsDefense] = GetGameText(luaIDL, 22),
    [EquipSmeltAttributeEnum.MagicDefense] = GetGameText(luaIDL, 23),
    [EquipSmeltAttributeEnum.CriticalStrike] = GetGameText(luaIDL, 24),
    [EquipSmeltAttributeEnum.AntiCriticalStrike] = GetGameText(luaIDL, 25),
    [EquipSmeltAttributeEnum.Parry] = GetGameText(luaIDL, 26),
    [EquipSmeltAttributeEnum.AntiParry] = GetGameText(luaIDL, 27),
    [EquipSmeltAttributeEnum.Dodge] = GetGameText(luaIDL, 24),
    [EquipSmeltAttributeEnum.Hit] = GetGameText(luaIDL, 29),
    [EquipSmeltAttributeEnum.Armor] = GetGameText(luaIDL, 30),
    [EquipSmeltAttributeEnum.AntiArmor] = GetGameText(luaIDL, 31),
    [EquipSmeltAttributeEnum.DamageReductin] = GetGameText(luaIDL, 32),
    [EquipSmeltAttributeEnum.DamageAdd] = GetGameText(luaIDL, 33),
    [EquipSmeltAttributeEnum.MoveSpeed] = GetGameText(luaIDL, 34),
    [EquipSmeltAttributeEnum.AttackDefense] = GetGameText(luaIDL, 35),
    [EquipSmeltAttributeEnum.EquipEffectID] = GetGameText(luaIDL, 16),
}

---@type integer[] 升级表属性显示类型：1数值、2百分比
EquipSmeltAttributeShowType = {
    [EquipSmeltAttributeEnum.HP] = 1,
    [EquipSmeltAttributeEnum.Attack] = 2,
    [EquipSmeltAttributeEnum.Defense] = 1,
    [EquipSmeltAttributeEnum.PhysicsAttack] = 1,
    [EquipSmeltAttributeEnum.MagicAttack] = 2,
    [EquipSmeltAttributeEnum.PhysicsDefense] = 2,
    [EquipSmeltAttributeEnum.MagicDefense] = 2,
    [EquipSmeltAttributeEnum.CriticalStrike] = 2,
    [EquipSmeltAttributeEnum.AntiCriticalStrike] = 1,
    [EquipSmeltAttributeEnum.Parry] = 1,
    [EquipSmeltAttributeEnum.AntiParry] = 2,
    [EquipSmeltAttributeEnum.Dodge] = 2,
    [EquipSmeltAttributeEnum.Hit] = 2,
    [EquipSmeltAttributeEnum.Armor] = 2,
    [EquipSmeltAttributeEnum.AntiArmor] = 2,
    [EquipSmeltAttributeEnum.DamageReductin] = 1,
    [EquipSmeltAttributeEnum.DamageAdd] = 2,
    [EquipSmeltAttributeEnum.MoveSpeed] = 2,
    [EquipSmeltAttributeEnum.AttackDefense] = 2,
    [EquipSmeltAttributeEnum.EquipEffectID] = 1,
}

---@type integer[][] 属性组索引值
EquipAttributeIndex = {
    --属性组一
    [1] = {
        EQUIPMENT_FIELD.EQUIPMENT_FIELD_ADDEFFECT_GROUP1_ID1,
        EQUIPMENT_FIELD.EQUIPMENT_FIELD_ADDEFFECT_GROUP1_ID2,
        EQUIPMENT_FIELD.EQUIPMENT_FIELD_ADDEFFECT_GROUP1_ID3,
        EQUIPMENT_FIELD.EQUIPMENT_FIELD_ADDEFFECT_GROUP1_ID4,
        EQUIPMENT_FIELD.EQUIPMENT_FIELD_ADDEFFECT_GROUP1_ID5,
    },
    --属性组二
    [2] = {
        EQUIPMENT_FIELD.EQUIPMENT_FIELD_ADDEFFECT_GROUP2_ID1,
        EQUIPMENT_FIELD.EQUIPMENT_FIELD_ADDEFFECT_GROUP2_ID2,
        EQUIPMENT_FIELD.EQUIPMENT_FIELD_ADDEFFECT_GROUP2_ID3,
        EQUIPMENT_FIELD.EQUIPMENT_FIELD_ADDEFFECT_GROUP2_ID4,
        EQUIPMENT_FIELD.EQUIPMENT_FIELD_ADDEFFECT_GROUP2_ID5,
    },
    --属性组三
    [3] = {
        EQUIPMENT_FIELD.EQUIPMENT_FIELD_ADDEFFECT_GROUP3_ID1,
        EQUIPMENT_FIELD.EQUIPMENT_FIELD_ADDEFFECT_GROUP3_ID2,
        EQUIPMENT_FIELD.EQUIPMENT_FIELD_ADDEFFECT_GROUP3_ID3,
        EQUIPMENT_FIELD.EQUIPMENT_FIELD_ADDEFFECT_GROUP3_ID4,
        EQUIPMENT_FIELD.EQUIPMENT_FIELD_ADDEFFECT_GROUP3_ID5,
    },
    --属性组四
    [4] = {
        EQUIPMENT_FIELD.EQUIPMENT_FIELD_ADDEFFECT_GROUP4_ID1,
        EQUIPMENT_FIELD.EQUIPMENT_FIELD_ADDEFFECT_GROUP4_ID2,
        EQUIPMENT_FIELD.EQUIPMENT_FIELD_ADDEFFECT_GROUP4_ID3,
        EQUIPMENT_FIELD.EQUIPMENT_FIELD_ADDEFFECT_GROUP4_ID4,
        EQUIPMENT_FIELD.EQUIPMENT_FIELD_ADDEFFECT_GROUP4_ID5,
    },
    --属性组五
    [5] = {
        EQUIPMENT_FIELD.EQUIPMENT_FIELD_ADDEFFECT_GROUP5_ID1,
        EQUIPMENT_FIELD.EQUIPMENT_FIELD_ADDEFFECT_GROUP5_ID2,
        EQUIPMENT_FIELD.EQUIPMENT_FIELD_ADDEFFECT_GROUP5_ID3,
        EQUIPMENT_FIELD.EQUIPMENT_FIELD_ADDEFFECT_GROUP5_ID4,
        EQUIPMENT_FIELD.EQUIPMENT_FIELD_ADDEFFECT_GROUP5_ID5,
    },
    --属性组六
    [6] = {
        EQUIPMENT_FIELD.EQUIPMENT_FIELD_ADDEFFECT_GROUP6_ID1,
        EQUIPMENT_FIELD.EQUIPMENT_FIELD_ADDEFFECT_GROUP6_ID2,
        EQUIPMENT_FIELD.EQUIPMENT_FIELD_ADDEFFECT_GROUP6_ID3,
        EQUIPMENT_FIELD.EQUIPMENT_FIELD_ADDEFFECT_GROUP6_ID4,
        EQUIPMENT_FIELD.EQUIPMENT_FIELD_ADDEFFECT_GROUP6_ID5,
    },
    --属性组七
    [7] = {
        EQUIPMENT_FIELD.EQUIPMENT_FIELD_ADDEFFECT_GROUP7_ID1,
        EQUIPMENT_FIELD.EQUIPMENT_FIELD_ADDEFFECT_GROUP7_ID2,
        EQUIPMENT_FIELD.EQUIPMENT_FIELD_ADDEFFECT_GROUP7_ID3,
        EQUIPMENT_FIELD.EQUIPMENT_FIELD_ADDEFFECT_GROUP7_ID4,
        EQUIPMENT_FIELD.EQUIPMENT_FIELD_ADDEFFECT_GROUP7_ID5,
    },
    --属性组八
    [8] = {
        EQUIPMENT_FIELD.EQUIPMENT_FIELD_ADDEFFECT_GROUP8_ID1,
        EQUIPMENT_FIELD.EQUIPMENT_FIELD_ADDEFFECT_GROUP8_ID2,
        EQUIPMENT_FIELD.EQUIPMENT_FIELD_ADDEFFECT_GROUP8_ID3,
        EQUIPMENT_FIELD.EQUIPMENT_FIELD_ADDEFFECT_GROUP8_ID4,
        EQUIPMENT_FIELD.EQUIPMENT_FIELD_ADDEFFECT_GROUP8_ID5,
    },
    --属性组九
    [9] = {
        EQUIPMENT_FIELD.EQUIPMENT_FIELD_ADDEFFECT_GROUP9_ID1,
        EQUIPMENT_FIELD.EQUIPMENT_FIELD_ADDEFFECT_GROUP9_ID2,
        EQUIPMENT_FIELD.EQUIPMENT_FIELD_ADDEFFECT_GROUP9_ID3,
        EQUIPMENT_FIELD.EQUIPMENT_FIELD_ADDEFFECT_GROUP9_ID4,
        EQUIPMENT_FIELD.EQUIPMENT_FIELD_ADDEFFECT_GROUP9_ID5,
    },
    --属性组十
    [10] = {
        EQUIPMENT_FIELD.EQUIPMENT_FIELD_ADDEFFECT_GROUP10_ID1,
        EQUIPMENT_FIELD.EQUIPMENT_FIELD_ADDEFFECT_GROUP10_ID2,
        EQUIPMENT_FIELD.EQUIPMENT_FIELD_ADDEFFECT_GROUP10_ID3,
        EQUIPMENT_FIELD.EQUIPMENT_FIELD_ADDEFFECT_GROUP10_ID4,
        EQUIPMENT_FIELD.EQUIPMENT_FIELD_ADDEFFECT_GROUP10_ID5,
    },
}
