--[[
********************************************************************
    created:    2023/10/24
    author :    李锦剑
    purpose:    奖励弹窗
*********************************************************************
--]]

---奖励数据
---@class RewardsData
---@field type number 奖励类型
---@field prizeContent string 奖励内容
---@field tipDesc string 提示描述
---@field callback fun() 回调函数

local luaID = ('UIDisplayReward')

---@class UIDisplayReward:UIWndBase
local m = {}

-------------------------------------------
-- 初始化
-------------------------------------------
function m:OnCreate()
	m.objList.root.gameObject:SetActive(false)
	---@type SlotItem[]
	m.goodItemList = {}
	---@type RewardsData[]
	m.rewardsDataList = {}
	m.objList.Btn_Mask.onClick:AddListenerEx(m.OnCloseUI)
	m.objList.Btn_Close.onClick:AddListenerEx(m.OnCloseUI)
	m.objList.Btn_AD.onClick:AddListenerEx(function()
		m.OnCloseUI()
		AdvertisementManager.ShowRewardAd(m.prizeData.tipDesc, function(bool)
			if bool then
				local goodsList = m.AnalyzingRewardsContent(m.prizeData.prizeContent)
				local beishu = Schemes.CommonText:Get(tonumber(m.prizeData.tipDesc)).PrizeType
				HelperL.GetDirectGoods(goodsList, beishu)
			end
		end, true)
	end)

	m.NewTimer = HelperL.NewTimer(m.SetRewardsContent, 5, 1)
	m.NewTimer2 = HelperL.NewTimer(function()
		m.objList.Scr_Goods.verticalNormalizedPosition = 0
	end, 1.2, 1)
	return true
end

-------------------------------------------
-- 窗口开启
-------------------------------------------
function m:OnOpen(type, prizeContent, tipDesc, callback)
	m.objList.root.transform.localScale = Vector3.one
	local data = { type = type, prizeContent = prizeContent, tipDesc = tipDesc, callback = callback }
	if type ~= PrizeContentType.STRING2 and m.isShowRewards then
		table.insert(m.rewardsDataList, data)
		return
	end
	m.isShowRewards = true
	m.NewTimer2.Stop()
	m.NewTimer2.Start()
	m.ShowRewards(data)
end

-------------------------------------------
---展示奖励
---@param data RewardsData
-------------------------------------------
function m.ShowRewards(data)
	m.prizeData = data
	m.type = data.type
	m.callback = data.callback
	m.objList.Btn_AD.gameObject:SetActive(false)
	if data.tipDesc ~= nil and data.tipDesc ~= "" then
		if tonumber(data.tipDesc) > 0 then
			m.objList.Btn_AD.gameObject:SetActive(HelperL.GetAdverState(data.tipDesc) == 1)
			local PrizeType = Schemes.CommonText:Get(m.prizeData.tipDesc).PrizeType
			m.objList.Txt_BtnAD.text = string.format(GetGameText(luaID, 7), PrizeType)
			m.objList.Txt_Hint.text = string.format(GetGameText(luaID, 8), PrizeType)
		end
	end

	--开宝箱十连抽
	if data.type == PrizeContentType.STRING2 then --解析奖励字符串2
		--m.objList.Btn_Mask.enabled = false
		EventManager:Subscribe(EventID.UIDisplayReward_SetRewardsContent, m.SetRewardsContent)
		m.NewTimer.Start()
		return
	end

	--设置奖励内容
	m.SetRewardsContent(data.prizeContent)
end

-------------------------------------------
--解析奖励内容
-------------------------------------------
function m.AnalyzingRewardsContent(prizeContent)
	local tempPrizes = {}
	if prizeContent and prizeContent ~= '' then
		if m.type == PrizeContentType.ID then --解析奖励ID
			local goodsList = Schemes.PrizeTable:GetGoodsList(tonumber(prizeContent))
			if goodsList then
				local cfg
				for _, v in ipairs(goodsList) do
					cfg = Schemes:GetGoodsConfig(v.id)
					if cfg then
						v.num = math.floor(v.num * HelperL.GetGoodsAdd(v.id))
						if HelperL.IsEuipType(v.id) or cfg.BoxSurface == '0' then
							table.insert(tempPrizes, { ID = v.id, Num = v.num })
						end
						HelperL.PlayCollectAnimation(v.id, v.num)
					end
				end
			end
		elseif m.type == PrizeContentType.STRING then --解析奖励字符串
			local prizeInfo = HelperL.Split(prizeContent, '|')
			local goodsInfo, id, goodsNum, cfg
			for i, v in ipairs(prizeInfo) do
				goodsInfo = HelperL.Split(v, '+')
				if goodsInfo[1] and goodsInfo[2] then
					id = tonumber(goodsInfo[1]) or 0
					goodsNum = math.floor((tonumber(goodsInfo[2]) or 0) * HelperL.GetGoodsAdd(id))
					cfg = Schemes:GetGoodsConfig(id)
					if cfg and goodsNum > 0 then
						table.insert(tempPrizes, { ID = id, Num = goodsNum })
						HelperL.PlayCollectAnimation(id, goodsNum)
					end
				end
			end
		elseif m.type == PrizeContentType.STRING2 then --解析奖励字符串2
			local prizeInfo = HelperL.Split(prizeContent, '+')
			local goodsInfo, id, goodsNum, cfg
			for i, v in ipairs(prizeInfo) do
				goodsInfo = HelperL.Split(v, 'x')
				if goodsInfo[1] and goodsInfo[2] then
					id = tonumber(goodsInfo[1]) or 0
					goodsNum = math.floor((tonumber(goodsInfo[2]) or 0) * HelperL.GetGoodsAdd(id))
					cfg = Schemes:GetGoodsConfig(id)
					if cfg and goodsNum > 0 then
						table.insert(tempPrizes, { ID = id, Num = goodsNum })
						HelperL.PlayCollectAnimation(id, goodsNum)
					end
				end
			end
		elseif m.type == PrizeContentType.STRING3 then --解析奖励字符串2
			local prizeInfo = HelperL.Split(prizeContent, '|')
			local goodsInfo, id, goodsNum, cfg
			for i, v in ipairs(prizeInfo) do
				goodsInfo = HelperL.Split(v, ';')
				if goodsInfo[1] and goodsInfo[2] then
					id = tonumber(goodsInfo[1]) or 0
					goodsNum = math.floor((tonumber(goodsInfo[2]) or 0) * HelperL.GetGoodsAdd(id))
					cfg = Schemes:GetGoodsConfig(id)
					if cfg and goodsNum > 0 then
						table.insert(tempPrizes, { ID = id, Num = goodsNum })
						HelperL.PlayCollectAnimation(id, goodsNum)
					end
				end
			end
		end
	end
	return tempPrizes
end

-------------------------------------------
--设置奖励内容
-------------------------------------------
function m.SetRewardsContent(prizeContent)
	m.NewTimer.Stop()
	EventManager:UnSubscribe(EventID.UIDisplayReward_SetRewardsContent, m.SetRewardsContent)
	--解析奖励内容
	m.tempPrizes = m.AnalyzingRewardsContent(prizeContent)
	if #m.tempPrizes == 0 then
		m.OnCloseUI()
		return
	end

	--创建/更新奖励物品
	local num = math.max(#m.goodItemList, #m.tempPrizes)
	for i = 1, num, 1 do
		if not m.goodItemList[i] then
			m.goodItemList[i] = _GAddSlotItem(m.objList.Grid_Goods)
		end
		local item = m.goodItemList[i]
		if m.tempPrizes[i] then
			item:SetItemID(m.tempPrizes[i].ID)
			item:SetCount(m.tempPrizes[i].Num)
			item:SetActive(true)
		else
			item:SetActive(false)
		end
	end

	--播放动画
	m.PlayAnimation()
end

--播放动画
function m.PlayAnimation()
	SoundManager:PlaySound(SoundID.GetAewarded)
	m.objList.root.gameObject:SetActive(true)
end

-------------------------------------------
--关闭界面
-------------------------------------------
function m.OnCloseUI()
	m.objList.root.gameObject:SetActive(false)
	for k, v in pairs(m.goodItemList) do
		v:SetActive(false)
	end
	if m.callback then
		m.callback()
		m.callback = nil
	end
	m.isShowRewards = #m.rewardsDataList > 0
	if m.isShowRewards then
		local data = table.remove(m.rewardsDataList, 1)
		m.ShowRewards(data)
	else
		m.ShowBoxEquipLevel()
	end
	m:CloseSelf()
end

-------------------------------------------
--显示宝箱等级
-------------------------------------------
function m.ShowBoxEquipLevel()
	local level = PropertyCompute.GetBoxEquipLevel(BOX_EQUIP_ID[1])
	if EntityModule.BoxEquipLevelEXP ~= level then
		EntityModule.BoxEquipLevelEXP = level
		UIManager:OpenWnd(WndID.BoxLevel, level)
	end
end

return m
