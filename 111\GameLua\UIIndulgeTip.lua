-- 登录UI
local luaID = ('UIIndulgeTip')

local UIIndulgeTip = {}

-- 初始化
function UIIndulgeTip:OnCreate()
	self.objList.Btn_Close.onClick:AddListenerEx(self.OnClickClose)
	self.btnList = { self.objList.Btn_Close}
	self.objList.Txt_Desc.text = GetGameText(luaID, 1)
	return true
end

-- 窗口开启
function UIIndulgeTip:OnOpen()
end

function UIIndulgeTip.OnClickClose()
	local self = UIIndulgeTip
	self:CloseSelf()
end

-- 窗口关闭
function UIIndulgeTip:OnClose()
end

-- 窗口销毁
function UIIndulgeTip:OnDestroy()
	-- 释放内存
	for i, btn in ipairs(self.btnList) do
		btn.onClick:RemoveAllListeners()
		btn.onClick:Invoke()
	end
end

return UIIndulgeTip