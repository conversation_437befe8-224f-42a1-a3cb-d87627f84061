--[[
********************************************************************
    created:	2023/12/14
    author :	李锦剑
    purpose:    新排行榜模块
*********************************************************************
--]]

local luaID = ('UIRankingList')

---新排行榜模块
---@class RankingModule
local m = {}
RankingModule = m

--排行刷新时间
m.rankingRefreshTimeList = {}

--排行榜缓存数据
---@type SC_Rank_GetRankList[]
m.RankData = {}

--自动更新排名类型
m.RankingType = {
    ------------排行榜不开放，注释即可------------
    -- { type = RANK_TYPE.RANK_TYPE_POWER,                    name = GetGameText(luaID, 1),  title = GetGameText(luaID, 31), }, -- 战斗力
    -- { type = RANK_TYPE.RANK_TYPE_MONEY,                    name = GetGameText(luaID, 2),  title = GetGameText(luaID, 31), }, -- 金钱
    -- { type = RANK_TYPE.RANK_TYPE_MAXHP,                    name = GetGameText(luaID, 3),  title = GetGameText(luaID, 31), }, -- 最大生命
    -- { type = RANK_TYPE.RANK_TYPE_PHYSICS_ATTACK,           name = GetGameText(luaID, 4),  title = GetGameText(luaID, 31), }, -- 物理攻击
    -- { type = RANK_TYPE.RANK_TYPE_MAGIC_ATTACK,             name = GetGameText(luaID, 5),  title = GetGameText(luaID, 31), }, -- 法术攻击
    -- { type = RANK_TYPE.RANK_TYPE_PHYSICS_DEFENSE,          name = GetGameText(luaID, 6),  title = GetGameText(luaID, 31), }, -- 物理防御
    -- { type = RANK_TYPE.RANK_TYPE_MAGIC_DEFENSE,            name = GetGameText(luaID, 7),  title = GetGameText(luaID, 31), }, -- 法术防御

    { type = RANK_TYPE.RANK_TYPE_LEVEL,           name = GetGameText(luaID, 8),  title = GetGameText(luaID, 31), }, -- 等级排行
    -- { type = RANK_TYPE.RANK_TYPE_CHARM,                    name = GetGameText(luaID, 9),  title = GetGameText(luaID, 31), }, -- 魅力
    -- { type = RANK_TYPE.RANK_TYPE_SOCIETYVIT,               name = GetGameText(luaID, 10), title = GetGameText(luaID, 31), }, -- 帮会活跃
    -- { type = RANK_TYPE.RANK_TYPE_ECTYPETOWER,              name = GetGameText(luaID, 11), title = GetGameText(luaID, 31), }, -- 副本塔
    -- { type = RANK_TYPE.RANK_TYPE_WILDBATTLEKILLNUM,        name = GetGameText(luaID, 12), title = GetGameText(luaID, 31), }, -- 楚汉战场击杀数
    -- { type = RANK_TYPE.RANK_TYPE_WILDBATTLECONTKILL,       name = GetGameText(luaID, 13), title = GetGameText(luaID, 31), }, -- 楚汉战场最高连斩数
    -- { type = RANK_TYPE.RANK_TYPE_WILDBATTLEKILLNUM_PD,     name = GetGameText(luaID, 14), title = GetGameText(luaID, 31), }, -- 楚汉战场每日击杀数
    -- { type = RANK_TYPE.RANK_TYPE_WILDBATTLECONTKILL_PD,    name = GetGameText(luaID, 15), title = GetGameText(luaID, 31), }, -- 楚汉战场每日最高连斩数
    -- { type = RANK_TYPE.RANK_TYPE_MOUNTPOWER,               name = GetGameText(luaID, 16), title = GetGameText(luaID, 31), }, -- 坐骑战力
    -- { type = RANK_TYPE.RANK_TYPE_WINGPOWER,                name = GetGameText(luaID, 17), title = GetGameText(luaID, 31), }, -- 仙器战力
    -- { type = RANK_TYPE.RANK_TYPE_NEWWINGPOWER,             name = GetGameText(luaID, 18), title = GetGameText(luaID, 31), }, -- 翅膀战力
    -- { type = RANK_TYPE.RANK_TYPE_SOCIETYPOWER,             name = GetGameText(luaID, 19), title = GetGameText(luaID, 31), }, -- 帮会战力
    -- { type = RANK_TYPE.RANK_TYPE_REAL_LEVEL,               name = GetGameText(luaID, 20), title = GetGameText(luaID, 31), }, -- 真实玩家等级榜
    -- { type = RANK_TYPE.RANK_TYPE_REAL_POWER,               name = GetGameText(luaID, 21), title = GetGameText(luaID, 31), }, -- 真实玩家战力榜
    -- { type = RANK_TYPE.RANK_TYPE_REAL_RECHARGE,            name = GetGameText(luaID, 22), title = GetGameText(luaID, 31), }, -- 真实玩家充值榜
    -- { type = RANK_TYPE.RANK_TYPE_REILIWHEELPOWER,          name = GetGameText(luaID, 23), title = GetGameText(luaID, 31), }, -- 星阵战力
    -- { type = RANK_TYPE.RANK_TYPE_HOLYLIGHTPOWER,           name = GetGameText(luaID, 24), title = GetGameText(luaID, 31), }, -- 圣光战力
    -- { type = RANK_TYPE.RANK_TYPE_NEWWILDBATTLEKILLNUM_PD,  name = GetGameText(luaID, 25), title = GetGameText(luaID, 31), }, -- 新楚汉战场每日击杀数
    -- { type = RANK_TYPE.RANK_TYPE_NEWWILDBATTLECONTKILL_PD, name = GetGameText(luaID, 26), title = GetGameText(luaID, 31), }, -- 新楚汉战场每日最高连斩数
    -- { type = RANK_TYPE.RANK_TYPE_STAGECHALLENGERECORD,     name = GetGameText(luaID, 27), title = GetGameText(luaID, 31), }, -- 挑战关卡记录
    -- { type = RANK_TYPE.RANK_TYPE_MATCHRANKRECORD,          name = GetGameText(luaID, 28), title = GetGameText(luaID, 31), }, -- 匹配段位记录
    -- { type = RANK_TYPE.RANK_TYPE_MATCHSCORERANKRECORD,     name = GetGameText(luaID, 29), title = GetGameText(luaID, 31), }, -- 匹配积分记录

    { type = RANK_TYPE.RANK_TYPE_CATDAYKILLENIMY, name = GetGameText(luaID, 30), title = GetGameText(luaID, 32), }, -- 生存排行
}

--------------------------------------------------------------------
--更新排行榜
--------------------------------------------------------------------
function m.UpdateRankList()
    if not EntityModule.hero then return end
    local time = HelperL.GetServerTime()
    local refreshTime
    for _, v in ipairs(m.RankingType) do
        refreshTime = m.rankingRefreshTimeList[v.type] or 0
        if time > refreshTime then
            --重新获取排行榜数据
            m.RequestRankList(v.type)
            --重新获取排行榜刷新时间--上面也能更新时间
            -- m.UpdateRankingRefreshTime(v.type)
        end
    end
end

--------------------------------------------------------------------
---请求排行榜列表
---@param rankType integer 排行榜类型
--------------------------------------------------------------------
function m.RequestRankList(rankType)
    -------------开始关卡统计------------
    local form = {}
    form["ApiVersion"] = 'v1'
    form["ZoneID"] = LoginModule:GetZoneID()
    form["RankType"] = rankType
    HttpReques.SendRequest(ERequestID.GetRank, form, function(o)
        if o then
            local data = m.ParseFromString(o)
            if data then
                m.SynchronizationRankList(data)
                local time = HelperL.GetCppDateTime2(o.RankTimeTicks)
                m.rankingRefreshTimeList[rankType] = time + 3600000
            end
        end
    end)
end

--------------------------------------------------------------------
---从字符串解析排行榜数据
---@param data integer
---@return SC_Rank_GetRankList|nil
--------------------------------------------------------------------
function m.ParseFromString(data)
    -- print('------从字符串解析排行榜数据-------', tonumber(data.total))
    local list = {}
    list.RankType = data.RankType
    list.RankList = {}
    list.SelfRank = 0
    list.SelfRankValue = 0
    if data and data.total > 0 then
        list.RankList = data.rows
        local actorID = LoginModule:GetSelectActorData().ActorID
        for i = 1, data.total, 1 do
            local v = data.rows[i]
            if v.ActorID == actorID then
                list.SelfRank = i
                list.SelfRankValue = v.Value1
                break
            end
        end
    end
    return list
end

--------------------------------------------------------------------
---请求自己排行
---@param rankType integer 排行榜类型
---@param callback fun(data:{SelfRank:integer, SelfRankValue:integer}) 回调函数
--------------------------------------------------------------------
function m.RequestSelfRank(rankType, callback)
    if callback then
        if rankType == RANK_TYPE.RANK_TYPE_LEVEL then
            local rankData = m.GetRankData(rankType)
            local data = {}
            data.SelfRank = rankData.SelfRank
            data.SelfRankValue = EntityModule.hero:GetProperty(CREATURE_FIELD.CREATURE_FIELD_LEVEL)
            callback(data)
        elseif rankType == RANK_TYPE.RANK_TYPE_CATDAYKILLENIMY then
            local form = {}
            form["ApiVersion"] = 'v1'
            form["StageID"] = 7
            HttpReques.SendRequest(ERequestID.GetStageScore, form, function(o)
                local data = {}
                if o and o.Value then
                    data.SelfRank = o.Value.RankNo_Stage7
                    data.SelfRankValue = o.Value.MaxScore
                else
                    data.SelfRank = 0
                    data.SelfRankValue = 0
                end
                callback(data)
            end)
        end
    end
end

--------------------------------------------------------------------
---根据类型获取自己排行数据
---@param rankType integer 排行榜类型
--------------------------------------------------------------------
function m.GetRankData(rankType)
    local data = m.RankData[rankType]
    if not data then
        data = {
            RankType = rankType,
            SelfRank = 0,
            SelfRankValue = 0,
            RankList = {},
        }
    end
    return data
end

--------------------------------------------------------------------
---同步排行榜数据
---@param data SC_Rank_GetRankList
--------------------------------------------------------------------
function m.SynchronizationRankList(data)
    m.RankData[data.RankType] = data
    EventManager:Fire(EventID.OnRankingListUpdate, data)
end

--------------------------------------------------------------------
---更新排行刷新时间
---@param rankType integer 排行榜类型
--------------------------------------------------------------------
function m.UpdateRankingRefreshTime(rankType)
    local form = {}
    form["ApiVersion"] = 'v1'
    form["ZoneID"] = LoginModule:GetZoneID()
    form["RankType"] = rankType
    HttpReques.SendRequest(ERequestID.GetLastTime_Rank, form, function(o)
        if o then
            local time = HelperL.GetCppDateTime2(o.Value)
            m.rankingRefreshTimeList[rankType] = time + 3600000
        end
    end)
end

--------------------------------------------------------------------
--初始化，请求所有排行榜数据
--------------------------------------------------------------------
function m.Init()
    if m.isInit then return end
    m.isInit = true
    m.UpdateRankList()
    --更新排行榜定时器
    m.updateTimer = Timer.New(m.UpdateRankList, 10, -1)
    m.updateTimer:Start()
end
