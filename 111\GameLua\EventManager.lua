-- 事件管理器
EventManager = {}
-- 订阅事件
function EventManager:Subscribe(eventID, handler)
    if self[eventID] == nil then
        self[eventID] = {}
    end
    table.insert(self[eventID], handler)
end

-- 取消订阅事件
function EventManager:UnSubscribe(eventID, handler)
    if self[eventID] == nil then
        return
    end
    for i = #self[eventID], 1, -1 do
        if self[eventID][i] == handler then
            table.remove(self[eventID], i)
        end
    end
    if #self[eventID] == 0 then
        self[eventID] = nil
    end
end

function EventManager:Fire(eventID, ...)
    if not eventID then
        -- warn('EventManager:Fire 无消息码', debug.traceback())
        if ... ~= nil then
            warn('EventManager:Fire 无消息码 msg=' .. (...), debug.traceback())
        end
        return
    end
    if self[eventID] == nil then
        return
    end
    for _, v in ipairs(self[eventID]) do
        v(...)
    end
end

function EventManager.Fire2(eventID, ...)
    EventManager:Fire(eventID, ...)
end

EventID = {
    GameInit = 1,                        -- 游戏初始化
    OnSceneLoaded = 2,                   -- 场景载入完毕
    OnHeroCreate = 3,                    -- 自身细胞创建成功
    OnDummyCreate = 4,                   -- 他人细胞创建成功
    OnMonsterCreate = 5,                 -- 怪物创建成功
    OnTrapCreate = 6,                    -- 陷阱创建成功
    OnNpcCreate = 7,                     -- npc创建成功
    OnCollectCreate = 8,                 -- 采集物创建成功
    OnDestroyEntity = 9,                 -- 实体销毁
    OnGoodsCreate = 10,                  -- 物品创建成功
    LogicDataChange = 11,                -- 逻辑数据变动
    CoolDataChange = 12,                 -- 冷却数据变动
    OnGetEmailList = 13,                 -- 更新邮件列表
    OnGetEmailInfo = 14,                 -- 更新邮件内容
    OnDeleteMail = 15,                   -- 删除邮件成功
    OnGetEmailAdjunct = 16,              -- 领取附件成功
    OnNewEmail = 17,                     -- 收到新邮件
    OnSkepGoodsChange = 18,              -- 篮子物品更新
    OnRankingListUpdate = 19,            -- 排行榜数据更新
    OnRankChange = 20,                   -- 排行改变(弹提示)

    ActivityStateChange = 21,            -- 活动数据更新
    EntitySyncBuff = 22,                 -- buff数据更新
    TowerBattleFieldUpdate = 23,         -- 塔防数据更新
    EntityPropertyUpdate_Hp = 24,        -- 生物血量更新
    UpdateMatchStatus = 25,              -- 匹配状态更新
    TowerBattleResUpdate = 26,           -- 资源新增提示
    TowerBattleStageStarUpdate = 27,     -- 塔防关卡星级更新
    TowerBattleDeckGroupUpdate = 28,     -- 塔防卡组更新
    WindowOpen = 29,                     -- 窗口打开
    WindowClose = 30,                    -- 窗口关闭
    OnGridLight = 31,                    -- 新手引导指定格子亮起
    OnRequestUseActiveSucc = 32,         -- 放兵结束
    OnFunctionOpen = 33,                 -- 功能开启
    TowerBattlePauseUpdate = 34,         -- 塔防暂停状态改变
    UpdateMatchList = 35,                -- 匹配列表更新
    TowerBattleShowTipText = 36,         -- 塔防提示文字通知
    TowerBattleGridMonsterUpdate = 37,   -- 塔防格子怪物更新
    OnHeroPropChange = 38,               -- 自身属性更新
    TowerBattlePropUpdate = 39,          -- 塔防属性数据更新
    GuideHandle = 40,                    -- 引导事件
    AdvertisePlayComplete = 41,          -- 视频观看完成
    OnMatchRecordDataUpdate = 42,        -- 自身比赛排行数据更新
    TowerBattlecDayMainCount = 43,       -- 塔防当日大关战斗次数更新
    OnGoodsPropChange = 44,              -- 物品属性更新
    StoreList = 45,                      -- 商店事件
    StoreBuyItem = 46,                   -- 商店购买事件
    StoreUpdateFree = 47,                -- 商店更新免费次数
    OnAdvertiseCoolTimeEnd = 48,         -- 免费广告冷却时间结束
    AdvertisePlayOpen = 49,              -- 打开广告
    BuffGridFlowText = 50,               -- buff指定格子飘字
    TowerBattleUpdateMonsterTarget = 51, -- 塔防更新怪物目标
    FreshenWorld = 52,                   -- 刷新主场景关卡旗子
    OnRechargeSDKCallback = 53,          -- sdk充值成功回调
    OnCloseStageWin = 54,                -- 关闭章节礼包的界面
    ChatModule_AddChat = 55,             -- 聊天频道
    UpgradeSkill = 56,                   -- 获得或升级一个技能
    UpdateYinBi = 57,                    -- 获取银币
    GemPartChange = 58,                  -- 探测刷新
    HeroEntered = 59,                    -- 英雄上线
    -- 改变任务显示
    ChangeTaskShow = 60,
    --- 任务事件
    -- (branchID,taskID, change)
    TaskPartUpdate = 61,
    TaskItemRemove = 62,
    TaskPageUpdate = 63,   -- 任务页面更新
    TaskAcceptNotice = 64, -- 任务可接提示
    AutoSeekMainTask = 65, -- 自动任务寻路,
    SocietyLeave = 66,
    -- 引导开启事件
    GuideMaskOpen = 67,
    ChangeMap = 68,
    -- 离开副本后通知
    HeroLeaveEctype = 69,
    -- 神装部位激活状态更新
    UpdateHeroPropActiveRecord = 70,
    -- 神装激活数据更新
    UpdateHeroPropActiveData = 71,
    -- 主角空闲事件
    OnHeroStandIdle = 72,
    --- 主界面隐藏子页面时
    MAIN_TITLE_HIDE_SUB_VIEW = 73,
    EntityPropertyUpdate_Level = 74,
    CheckGuideEvent = 75,
    UpdateInviteDataList = 76, -- 更新邀请列表
    OpenMainButtonUI = 77,     -- 打开主界面下面几个按钮的界面
    TaskShowHandGuide = 78,    -- 任务手指印
    TaskShowUIEquipIndex = 79, -- 任务打开突变的索引界面
    TaskFinish = 80,           -- 任务完成
    StopAutoBoxContinue = 81,  -- 暂停--自动开宝箱
    AutoBoxContinue = 82,      -- 继续--自动开宝箱
    SyncPlace = 83,
    AdvertiseUseBuff = 84,     -- 广告使用Buff回调
    UseMainBuff = 85,          -- 主界面buff
    UpdatePlayerHealth = 86,   -- 更新血量
    -- PlayGetAnimation = 87, --播放获得动画
    MSG_SKEP_USEGOODS = 88,    -- 使用物品
    Battle_End = 89,           -- 战斗结束
    --- 数值资源
    HeroPropertyUpdate_Diamond = 90,
    HeroPropertyUpdate_Silver = 91,
    HeroPropertyUpdate_Xiuwei = 92,
    CheckUseEquip = 93,
    EntityModuleDataUpdate = 94,             -- 实体模块数据更新：节省性能延迟1秒更新
    ShowFateHint = 95,                       -- 命魂属性查看
    UIAttributeUp_SetActiveSkill = 96,       -- 激活技能
    UpdateGameProgress = 97,                 -- 更新游戏进度
    BeforeAddSkill = 98,                     -- (skillType) 添加技能前
    AfterAddSkill = 99,                      -- (skill) 添加技能后
    BeforeAddSkillEffect = 100,              -- (skillType, sillEffectID) 添加技能效果前
    AfterAddSkillEffect = 101,               -- (skill, sillEffectID) 添加技能效果后
    UIDisplayReward_SetRewardsContent = 102, --设置奖励内容
    EquipmentReplace = 103,                  -- 装备替换成功
    UpdateKillNum = 104,                     -- 更新击杀数量
    UpdateGoldCoinNum = 105,                 -- 更新金币数量

    UpdateFightHero = 107,                   -- 更新角色出战列表
    WindowCreated = 108,                     -- 创建了一个窗口
    ActorDataLoaded = 109,                   -- (ServerLoaded, LocalLoaded) 角色数据已加载
    ActorDataChanged = 110,                  -- (dataCatalog) 角色数据已更改
    LoginCallback = 111,                     -- 登录回调
    ActorDataMgrInited = 112,                -- 玩家新逻辑数据以初始化--ActorDataMgr
    LoginComplete = 113,                     -- 登录完成

    
    ChangeNameEvent = 114,                     -- 修改名字成功


    ---------------------------更新HTTP请求数据---------------------------
    UpdateHttpRequestCallBackData = 1000,  --更新HTTP请求数据
    UpdateGameEctypeData = 1001,           -- 更新游戏副本
    UpdateActorTaskData = 1002,            -- 更新任务数据
    UpdateGameEctypeBoxData = 1003,        -- 更新宝箱数据
    UpdateListPets_OnStage = 1005,         -- 更新宠物出战列表
    UpdateGameEctypeStageScoreData = 1006, -- 更新副本积分数据
    UpdateGameEctypeDayScore = 1007,       -- 更新副本每日得分数据
}
