--[[
********************************************************************
    created:    2023/08/30
    author :    李锦剑
    purpose:    充值界面
*********************************************************************
--]]

local luaID = ('UIWelfare')

local m = {}

--------------------------------------------------------------------
-- 订阅事件列表
--------------------------------------------------------------------
function m.<PERSON><PERSON><PERSON>List()
    return {
        [EventID.LogicDataChange] = m.UpdateView,
        [EventID.EntitySyncBuff] = m.UpdateView,
    }
end

--------------------------------------------------------------------
--预制体自适应
--------------------------------------------------------------------
function m.AdaptScale()
    HelperL.AdaptScale(m.objList.Img_Bg1, 6)
end

--------------------------------------------------------------------
-- 初始化
--------------------------------------------------------------------
function m.OnCreate()
    --按钮数据集合
    m.btnDataList = {
        { ActvID = ACTVID.ACTVID_TIMESHOPPING, Text = GetGameText(luaID, 4), LenovoID = RECHARGE_TYPE.RECHARGE_CARD, },
    }
    --按钮Item集合
    m.btnItemList = {}
    --充值Item集合
    m.rechargeItemList = {}

    for i, v in ipairs(m.btnDataList) do
        table.insert(m.btnItemList, m.CreationButton(v, i))
    end

    m.OnClickButton(1)
    m.RegisterClickEvent()
    return true
end

--------------------------------------------------------------------
-- 每秒更新
--------------------------------------------------------------------
function m.OnSecondUpdate()
    for _, v in ipairs(m.rechargeItemList) do
        v.SecondUpdate()
    end
end

function m.OnOpen()
    -- local hero = EntityModule.hero
    -- if not hero then return end
    -- local heroLevel = hero:GetProperty(CREATURE_FIELD.CREATURE_FIELD_LEVEL)
    -- if heroLevel == MainButtonOpenLevel[5] then
    --     local curStep = GuideManager:GetStep(GuideManager.EventType.GiftBoxGuide)
    --     if not GuideManager.isRunning
    --         and curStep == 1 then
    --         local delayGuide = Timer.New(function()
    --             GuideManager.StartGuide(GuideManager.EventType.GiftBoxGuide, 2)
    --         end, 0.5, 1)
    --         delayGuide:Start()
    --     end
    -- end

    -- local curStep = GuideManager:GetStep(GuideManager.EventType.GiftBagGuide)
    -- if not GuideManager.isRunning
    --     and curStep == 1 then
    --     local delayGuide = Timer.New(function()
    --         GuideManager.StartGuide(GuideManager.EventType.GiftBagGuide, 2)
    --     end, 0.5, 1)
    --     delayGuide:Start()
    -- end
end

--注册点击事件
function m.RegisterClickEvent()
    m.objList.Btn_Close.onClick:AddListenerEx(function()
        m:CloseSelf()
    end)
end

-- 创建按钮
function m.CreationButton(data, index)
    local item = {}
    item.data = data
    item.index = index
    item.objList = m:CreateSubItem(m.objList.Grid_Button, m.objList.Item_Button)
    item.objList.Txt_Name.text = data.Text
    item.objList.gameObject:GetComponent('Button').onClick:AddListenerEx(function()
        m.OnClickButton(item.index)
    end)
    return item
end

-- 按钮点击事件
function m.OnClickButton(index)
    if m.selectIndex == index then return end
    if m.selectIndex then
        m.btnItemList[m.selectIndex].objList.Img_Bg.gameObject:SetActive(false)
        m.btnItemList[m.selectIndex].objList.Txt_Name.color = Color(33 / 255, 85 / 255, 148 / 255, 1)
    end
    m.selectIndex = index
    m.btnItemList[index].objList.Img_Bg.gameObject:SetActive(true)
    m.btnItemList[index].objList.Txt_Name.color = Color(1, 1, 1, 1)

    m.UpdateView()
end

--更新界面
function m.UpdateView()
    local btnData = m.btnDataList[m.selectIndex]
    local dataList = {}
    for k, v in ipairs(Schemes.RechargeCard.items) do
        if v.LenovoID == btnData.LenovoID then
            table.insert(dataList, v)
        end
    end
    local num = math.max(#dataList, #m.rechargeItemList)
    local item
    for i = 1, num, 1 do
        if not m.rechargeItemList[i] then
            m.rechargeItemList[i] = m.CreationRecharge(i)
        end
        item = m.rechargeItemList[i]
        item.UpdateData(dataList[i])
    end
end

-- 创建充值按钮
function m.CreationRecharge(index)
    local item = {}
    item.index = index
    item.com = m:CreateSubItem(m.objList.Grid_Recharge, m.objList.Item_Recharge)
    item.com.Btn_Buy.onClick:AddListenerEx(function()
        HelperL.Recharge(item.data.ID)
    end)
    item.com.Btn_GrowRMB2.onClick:AddListenerEx(function()
        HelperL.Recharge(item.data.ID)
    end)
    item.com.Btn_GrowRMB22.onClick:AddListenerEx(function()
        HelperL.Recharge(item.data.ID)
    end)
    item.com.Btn_Ad.onClick:AddListenerEx(function()
        AdvertisementManager.ShowRewardAd(item.data.Description)
    end)
    --每秒更新
    item.SecondUpdate = function()
        item.com.Btn_Ad.gameObject:SetActive(false)
        item.com.Btn_Ad2.gameObject:SetActive(false)

        if item.data and item.data.Description ~= '0' then
            local commonText = Schemes.CommonText:Get(item.data.Description)
            local state = HelperL.GetAdverState(item.data.Description)
            if state ~= 4 then
                if state == 2 then
                    local time = EntityModule.hero.buffLC:GetBuffLeftTime(commonText.Param1)
                    item.com.Txt_Ad2.text = HelperL.GetTimeString(TimeStringType.FullAuto2, time)
                    item.com.Btn_Ad2.gameObject:SetActive(true)
                else
                    item.com.Btn_Ad.gameObject:SetActive(true)
                end
            end
        end
    end
    --更新数据/页面
    item.UpdateData = function(data)
        item.data = data
        if data then
            local price = '￥' .. (data.FirstRMB / 100)
            item.com.Txt_Buy.text = price
            item.com.Txt_Buy2.text = price
            item.com.Txt_GrowRMB2.text = price
            item.com.Txt_GrowRMB22.text = price
            item.com.Txt_Title.text = data.CardName
            item.com.Txt_Name.text = data.FirstCharacter1

            item.com.Obj_AD.gameObject:SetActive(false)
            item.com.Obj_Buy.gameObject:SetActive(false)
            item.com.Btn_Buy.gameObject:SetActive(false)
            item.com.Btn_Buy2.gameObject:SetActive(false)
            item.com.Btn_GrowRMB2.gameObject:SetActive(false)
            item.com.Btn_GrowRMB22.gameObject:SetActive(false)

            if data.Description ~= '0' then
                item.com.Obj_AD.gameObject:SetActive(true)
                local commonText = Schemes.CommonText:Get(data.Description)
                local num = HeroDataManager:GetLogicByte(commonText.Param2, commonText.Param3)
                item.com.Txt_Hint.text = string.format(GetGameText(luaID, 7), UI_COLOR.Red, num, commonText.ToTime)
                if SWITCH.RECHARGE then
                    item.com.Btn_GrowRMB2.gameObject:SetActive(true)
                else
                    -- item.com.Btn_GrowRMB22.gameObject:SetActive(true)
                end
            else
                item.com.Obj_Buy.gameObject:SetActive(true)
                if SWITCH.RECHARGE then
                    item.com.Btn_Buy.gameObject:SetActive(true)
                else
                    -- item.com.Btn_Buy2.gameObject:SetActive(true)
                end
            end
            AtlasManager:AsyncGetSprite(data.FirstPic1, item.com.Img_Icon)
        end
        item.com.gameObject:SetActive(data ~= nil)
        item.SecondUpdate()
    end
    return item
end

return m
