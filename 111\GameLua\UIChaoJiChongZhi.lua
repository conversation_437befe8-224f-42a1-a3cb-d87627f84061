--[[
********************************************************************
    created:    2024/09/24
    author :    李锦剑
    purpose:    超级充值界面
*********************************************************************
--]]

local luaID = ('UIChaoJiChongZhi')

local m = {}
local cardID = 26
--------------------------------------------------------------------
-- 订阅事件列表
--------------------------------------------------------------------
function m.GetOpenEventList()
	return {
		[EventID.LogicDataChange] = m.UpdateView,
		[EventID.EntitySyncBuff] = m.UpdateView,
		--[EventID.OnHeroPropChange] = m.UpdateView,
	}
end

--资源预加载
function m.GetResourceList()
	return {
	}
end

m.buttonDataList = {
	{ ID = 1, Name = GetGameText(luaID, 20), winId = 0 },
	{ ID = 2, Name = GetGameText(luaID, 21), winId = 77 },
	{ ID = 3, Name = GetGameText(luaID, 22), winId = 128 },
	-- { ID = 4, Name = GetGameText(luaID, 23), winId = 0 }, -- 屏蔽ID=4的按钮，避免数据量大导致卡顿
	--{ ID = 5, Name = GetGameText(luaID, 24), winId = 0 },
}

-- 优化：缓存变量，避免重复计算
m.cachedStageList = nil
m.isFirstOpen = true

--------------------------------------------------------------------
-- 初始化
--------------------------------------------------------------------
function m.OnCreate()
	m.goodsItemList = {}
	--m.wndID
	--48   限量礼包
	--125  专属特惠
	m.rechargeCard = Schemes.RechargeCard:Get(cardID)
	local goodsList = Schemes.PrizeTable:GetGoodsList(m.rechargeCard.PrizeID)
	if goodsList then
		local pos = { Vector2(-287, 90), Vector2(0, 90), Vector2(287, 90), }
		for i = 1, 3, 1 do
			if not m.goodsItemList[i] then
				m.goodsItemList[i] = _GAddSlotItem(m.objList.Grid_Goods)
			end
			if goodsList[i] then
				m.goodsItemList[i]:SetItemID(goodsList[i].id)
				m.goodsItemList[i]:SetCount(goodsList[i].num)
				m.goodsItemList[i].gameObject:SetActive(true)
				m.goodsItemList[i].gameObject:GetRectTransform().anchoredPosition = pos[i]
			else
				m.goodsItemList[i].gameObject:SetActive(false)
			end
		end
	end

	m.objList.Txt_Buy.text = '￥' .. math.floor(m.rechargeCard.FirstRMB / 100)
	m.objList.Txt_Buy22.text = '￥' .. math.floor(m.rechargeCard.FirstRMB / 100)
	m.objList.Txt_Grey2.text = '￥' .. math.floor(m.rechargeCard.FirstRMB / 100)
	m.objList.Txt_Describe.text = string.gsub(m.rechargeCard.FirstCharacter1, '<P><P>', '')
	m.objList.Btn_Grey2.gameObject:SetActive(false)
	m.objList.Btn_Buy22.gameObject:SetActive(false)
	if SWITCH.RECHARGE then
		m.objList.Btn_Buy22.gameObject:SetActive(true)
	else
		-- m.objList.Btn_Grey2.gameObject:SetActive(true)
	end
	m.RegisterClickEvent()
	m.Item_Tab_List = {}
	m.CreateTab()
	m.gameEctypeBox_ItemList = {}
	return true
end

function m.CreateTab()
	for i = 1, #m.buttonDataList, 1 do
		if not m.Item_Tab_List[i] then
			m.Item_Tab_List[i] = m.Creation_Item_Tab(i)
		end
		m.Item_Tab_List[i].UpdateData(m.buttonDataList[i])
	end
	m.ShowInfo(1)
end

--------------------------------------------------------------------
---创建按钮
---@param index integer
---@return Item_Tab
--------------------------------------------------------------------
function m.Creation_Item_Tab(index)
	---@class Item_Tab
	local item = {}
	item.index = index
	item.com = m:CreateSubItem(m.objList.Grid_Tab, m.objList.Item_Tab)
	m:AddClick(item.com.Btn_Click, function()
		m.ShowInfo(item.index)
	end)

	item.com.Img_Bg.gameObject:SetActive(true)
	item.com.Img_Select.gameObject:SetActive(false)

	--- 更新数据
	item.UpdateData = function(SmeltID)
		item.com.Txt_Name1.text = SmeltID.Name
		item.com.Txt_Name2.text = SmeltID.Name
	end
	return item
end

--------------------------------------------------------------------
---@param index integer
--------------------------------------------------------------------
function m.ShowInfo(index)
	if m.selectIndex == index then
		return
	end
	local item
	if m.selectIndex then
		item = m.Item_Tab_List[m.selectIndex]
		item.com.Img_Bg.gameObject:SetActive(true)
		item.com.Img_Select.gameObject:SetActive(false)
		if m.buttonDataList[m.selectIndex].winId == 0 then
			-- 屏蔽ID=4的逻辑，避免加载大数据量导致卡顿
			if m.buttonDataList[m.selectIndex].ID ~= 4 then
				m.objList.AllParent.transform:Find("ContentAni_" .. tostring(m.buttonDataList[m.selectIndex].ID)).gameObject
					:SetActive(false)
			end
		else
			UIManager:CloseWndByID(m.buttonDataList[m.selectIndex].winId)
		end
	end
	item = m.Item_Tab_List[index]
	item.com.Img_Bg.gameObject:SetActive(false)
	item.com.Img_Select.gameObject:SetActive(true)

	m.selectIndex = index
	if m.buttonDataList[index].winId == 0 then
		-- 屏蔽ID=4的逻辑，避免加载大数据量导致卡顿
		if m.buttonDataList[m.selectIndex].ID ~= 4 then
			m.objList.AllParent.transform:Find("ContentAni_" .. tostring(m.buttonDataList[m.selectIndex].ID)).gameObject
				:SetActive(true)
		end
	else
		UIManager:OpenWnd(m.buttonDataList[m.selectIndex].winId)
	end
end

--------------------------------------------------------------------
-- 窗口开启
--------------------------------------------------------------------
function m.OnOpen()
	m.createCount = 0
	m.ShowInfo(1)
	m.AddRedDot()
	m.UpdateView()
	
	-- 优化：延迟加载UpdateView4，减少首次打开卡顿
	-- 如果是第一次打开或者需要更新数据时才执行
	if m.isFirstOpen then
		-- 延迟0.1秒再执行UpdateView4，让界面先显示出来
		Timer.New(function()
			m.UpdateView4()
			m.isFirstOpen = false
		end, 0.1, 1):Start()
	else
		m.UpdateView4()
	end
end

function m.OnClose()
	if m.selectIndex then
		if m.buttonDataList[m.selectIndex].winId ~= 0 then
			UIManager:CloseWndByID(m.buttonDataList[m.selectIndex].winId)
		end
	end
end

--------------------------------------------------------------------
--注册点击事件
--------------------------------------------------------------------
function m.RegisterClickEvent()
	m.objList.Btn_Close.onClick:AddListenerEx(function()
		m:CloseSelf()
	end)

	m.objList.Btn_Buy.onClick:AddListenerEx(function()
		HelperL.RechargeAndJudge(cardID)
	end)
	m.objList.Btn_Grey.onClick:AddListenerEx(function()
		HelperL.RechargeAndJudge(cardID)
	end)
	m.objList.Btn_Grey2.onClick:AddListenerEx(function()
		HelperL.RechargeAndJudge(cardID)
	end)
	m.objList.Btn_Buy22.onClick:AddListenerEx(function()
		HelperL.RechargeAndJudge(cardID)
	end)

	m.objList.Btn_AD2.onClick:AddListenerEx(function()
		AdvertisementManager.ShowRewardAdByCardID(cardID)
	end)

	m.objList.Btn_AD22.onClick:AddListenerEx(function()
		HelperL.GetAdverHint(m.rechargeCard.Description, true)
	end)
end

--------------------------------------------------------------------
-- 更新界面
--------------------------------------------------------------------
function m.UpdateView()
	local isBuyCard = HelperL.HadBoughtCardID(cardID)
	m.objList.buy2.gameObject:SetActive(false)
	m.objList.Btn_Buy.gameObject:SetActive(false)
	m.objList.Btn_Grey.gameObject:SetActive(false)
	if isBuyCard then
		m.objList.Btn_Grey.gameObject:SetActive(true)
		m.objList.Txt_Grey.text = CommonTextID.IS_PURCHASE
	else
		m.objList.Btn_Buy.gameObject:SetActive(true)
		-- if m.rechargeCard.Description ~= '0' then
		-- 	m.objList.buy2.gameObject:SetActive(true)
		-- else
		-- 	if SWITCH.RECHARGE then
		-- 		m.objList.Btn_Buy.gameObject:SetActive(true)
		-- 	else
		-- 		-- m.objList.Btn_Grey.gameObject:SetActive(true)
		-- 		-- m.objList.Txt_Grey.text = '￥' .. math.floor(m.rechargeCard.FirstRMB / 100)
		-- 	end
		-- end
	end

	local num, toTime = Schemes.CommonText.GetAdToTimeByCardID(cardID)
	m.objList.Txt_AdNum2.text = string.format(GetGameText(luaID, 19), UI_COLOR.Red, num, toTime)
end

--------------------------------------------------------------------
-- 每秒更新
--------------------------------------------------------------------
function m.OnSecondUpdate()
	local time, state, commonText

	m.objList.Btn_AD2.gameObject:SetActive(false)
	m.objList.Btn_AD22.gameObject:SetActive(false)
	-- m.objList.Img_AD22.gameObject:SetActive(false)
	m.objList.Txt_AD22.gameObject:SetActive(false)
	state = HelperL.GetAdverState(m.rechargeCard.Description)
	if state ~= 4 then
		if state == 2 then
			commonText = Schemes.CommonText:Get(m.rechargeCard.Description)
			time = EntityModule.hero.buffLC:GetBuffLeftTime(commonText.Param1)
			m.objList.Txt_AD22.text = HelperL.GetTimeString(TimeStringType.FullAuto2, time)
			m.objList.Btn_AD22.gameObject:SetActive(true)
			m.objList.Txt_AD22.gameObject:SetActive(true)
		elseif state == 3 then
			-- m.objList.Img_AD22.gameObject:SetActive(true)
			m.objList.Btn_AD22.gameObject:SetActive(true)
		else
			m.objList.Btn_AD2.gameObject:SetActive(true)
		end
	end
end

--------------------------------------------------------------------
-- 添加红点
--------------------------------------------------------------------
function m.AddRedDot()
	m:SetWndRedDot(m.objList.Btn_AD2):AddCheckParam(WndID.ChaoJiChongZhi)
end

function m.UpdateView4()
	-- 优化：使用缓存避免重复获取数据
	if not m.cachedStageList then
		m.cachedStageList = Schemes.CatMainStage:GetByFrontType(1)
	end
	
	local count = 1
	m.createCount = 0
	
	-- 优化：限制最大处理数量，避免一次性处理过多数据导致卡顿
	local maxProcessCount = 5  -- 最多处理5个，可根据实际情况调整
	local processedCount = 0
	
	for _, v in ipairs(m.cachedStageList) do
		if m.createCount > 2 or processedCount >= maxProcessCount then
			return
		end
		processedCount = processedCount + 1
		
		local catMainStage = Schemes.CatMainStage:Get(v.ID)
		--已领取不显示 除了可领取之外的 最多显示3个
		if not GamePlayerData.GameEctype:IsGet(catMainStage.ID, 3) then
			--判断是否可领取
			local strList = HelperL.Split(catMainStage.GeneralDrop, '|')
			local dataList = {}
			local temp, prizeID, waveNumber
			for i, v in ipairs(strList) do
				temp = HelperL.Split(v, ';')
				prizeID = tonumber(temp[2]) or 0
				waveNumber = tonumber(temp[1]) or 0
				if prizeID > 0 and waveNumber > 0 then
					table.insert(dataList, { ID = catMainStage.ID, Type = 1, PrizeID = prizeID, WaveNumber = waveNumber })
				end
			end
			
			-- 优化：确保dataList[3]存在再处理
			if dataList[3] then
				local id = GamePlayerData.GameEctype:GetProgress(1)
				local rankNo = GamePlayerData.GameEctype:GetRankNo(dataList[3].ID)
				--可领取
				if rankNo >= dataList[3].WaveNumber or id >= dataList[3].ID then
	
				else
					m.createCount = m.createCount + 1
				end
				
				if not m.gameEctypeBox_ItemList[count] then
					m.gameEctypeBox_ItemList[count] = m.CreationDayGift(catMainStage)
				end
				m.gameEctypeBox_ItemList[count].UpdateView(catMainStage)
			end
		end
		count = count + 1
	end
end

--------------------------------------------------------------------
---创建副本宝箱框
--------------------------------------------------------------------
function m.CreationDayGift(catMainStage)
	local item = {}
	item.index = catMainStage
	item.objList = m:CreateSubItem(m.objList.Grid_DayGift, m.objList.Item_DayGift)
	--更新界面
	item.UpdateView = function(catMainStage)
		item.data = catMainStage
		if catMainStage then
			item.objList.Txt_Title.text = catMainStage.Name

			local strList = HelperL.Split(catMainStage.GeneralDrop, '|')
			local dataList = {}
			local temp, prizeID, waveNumber
			for i, v in ipairs(strList) do
				temp = HelperL.Split(v, ';')
				prizeID = tonumber(temp[2]) or 0
				waveNumber = tonumber(temp[1]) or 0
				if prizeID > 0 and waveNumber > 0 then
					table.insert(dataList, { ID = catMainStage.ID, Type = 1, PrizeID = prizeID, WaveNumber = waveNumber })
				end
			end
			local data = item.data

			--已领取
			if GamePlayerData.GameEctype:IsGet(data.ID, 3) then
				item.objList.gameObject:SetActive(false)
				item.objList.Btn_.gameObject:SetActive(true)
				item.objList.Btn_Buy.transform.parent.gameObject:SetActive(false)
			else
				-- 优化：确保dataList[3]存在再处理
				if dataList[3] then
					local id = GamePlayerData.GameEctype:GetProgress(1)
					local rankNo = GamePlayerData.GameEctype:GetRankNo(dataList[3].ID)
					--可领取
					if rankNo >= dataList[3].WaveNumber or id >= dataList[3].ID then
						item.objList.Btn_Buy.gameObject:SetActive(true)
						item.objList.Btn_.gameObject:SetActive(false)
						item.objList.Btn_Buy.onClick:AddListenerEx(function()
							m.GetGameEctypeBoxAward(data.ID, 3, item.objList.Btn_Buy.transform.parent.gameObject)
						end)
					else
						item.objList.Btn_Buy.gameObject:SetActive(false)
						item.objList.Btn_.gameObject:SetActive(true)
						item.objList.Txt_Buy.text = CommonTextID.GET
					end
					m.ShowGiftThreeItemIcon(item, dataList[3].PrizeID)
				end
			end
		else
			item.objList.gameObject:SetActive(false)
		end
	end

	return item
end

--------------------------------------------------------------------
---领取副本宝箱奖励
---@param stageID integer
---@param index integer
--------------------------------------------------------------------
function m.GetGameEctypeBoxAward(stageID, index, item)
	local form = {}
	form["stageID"] = stageID
	form["index"] = index
	LuaModuleNew.SendRequest(LuaRequestID.GetEctypeTreasureBoxRewards, form, function(resultCode, content, callbackParam)
		if resultCode == RESULT_CODE_BASE.RESULT_COMMON_SUCCEED[1] then
			item.gameObject:SetActive(false)
		else
			ResultCode.ShowResultCodeCallback(resultCode, content)
		end
	end)
end

--品字方式显示3个奖励物品
function m.ShowGiftThreeItemIcon(item, PrizeID)
	local prizeList = Schemes.PrizeTable:GetGoodsList(PrizeID)
	if not prizeList then
		warn('-----奖励ID为空----prizeId=', PrizeID)
		return
	end
	
	-- 优化：延迟创建GoodsList，只在需要时创建
	if not item.GoodsList then
		---@type SlotItem[]
		item.GoodsList = {}
	end
	
	local posList = { Vector3.New(165, -20, 0), Vector3.New(365, -20, 0), Vector3.New(565, -20, 0) }
	-- 优化：限制显示物品数量，避免创建过多UI元素
	local maxItems = math.min(3, #prizeList)
	for i = 1, maxItems do
		if not item.GoodsList[i] then
			item.GoodsList[i] = _GAddSlotItem(item.objList.Img_Goods)
		end
		item.GoodsList[i]:SetItemID(prizeList[i].id)
		item.GoodsList[i]:SetCount(prizeList[i].num)
		item.GoodsList[i].transform.localPosition = posList[i]
	end
end

return m
