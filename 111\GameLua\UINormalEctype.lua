--[[
********************************************************************
    created:	2024/06/06
    author :	李锦剑
    purpose:    探索历练
*********************************************************************
--]]

local luaID = 'UINormalEctype'
local EctypeType = 1
---探索历练
---@class UINormalEctype:UIWndBase
local m = {}

--------------------------------------------------------------------
-- 订阅事件列表
--------------------------------------------------------------------
function m.GetOpenEventList()
    return {
        [EventID.LogicDataChange] = m.UpdateView,
        [EventID.EntitySyncBuff] = m.UpdateView,
        [EventID.OnGoodsPropChange] = m.UpdateView,
        [EventID.OnHeroPropChange] = m.UpdateView,
        [EventID.UpdateGameEctypeData] = m.UpdateView,
        [EventID.UpdateGameEctypeBoxData] = m.UpdateView,
        [EventID.ActorDataChanged] = m.UpdateView,
    }
end

--------------------------------------------------------------------
-- 初始化
--------------------------------------------------------------------
function m.OnCreate()
    ---@type Item_Box[]
    m.gameEctypeBox_ItemList = {}
    ---@type SlotItem[]
    m.AwardShow_ItemList = {}
    --m.objList.Txt_Goods2.text = GetGameText(luaID, 1)
    m.selectCatMainStageID = GamePlayerData.GameEctype:GetChallengeableID(EctypeType)
    --m.lastCatMainStageID = m.selectCatMainStageID
    m.comResInfo = ComResInfo.New()
    m.comResInfo.BindView(m.objList.Com_Res)

    m.goodsItemList = {}
    m.RegisterClickEvent()
    return true
end

--------------------------------------------------------------------
-- 窗口开启
--------------------------------------------------------------------
function m:OnOpen(stageID)
    m.selectCatMainStageID = stageID
    -- 等待资源加载完成
    coroutine.start(function()
        -- 初始化catMainStage
        m.catMainStage = m.objList.catMainStage
        
        -- 更新视图
        m:UpdateView()
    end)
end

--------------------------------------------------------------------
-- 注册点击事件
--------------------------------------------------------------------
function m.RegisterClickEvent()
    m:AddClick(m.objList.Btn_Close, function()
        m:CloseSelf()
    end)

    m:AddClick(m.objList.Btn_Start, function()
        HelperL.IsShowOkWindow(nil, function(isCheck)
            --关掉手指引导
            EventManager:Fire(EventID.TaskShowHandGuide, 3)
            m.ChuZheng(m.selectCatMainStageID)
        end)
    end)

    m:AddClick(m.objList.Btn_StartGray, function()
        UIManager:OpenWnd(WndID.PurchasePhysicalPower)
    end)
end

--------------------------------------------------------------------
--更新界面
--------------------------------------------------------------------
function m.UpdateView()
    -- local stageID = GamePlayerData.GameEctype:GetChallengeableID(EctypeType)
    -- if m.lastCatMainStageID ~= stageID then
    --     m.selectCatMainStageID = stageID
    --     m.lastCatMainStageID = stageID
    -- end

    --local maxStage = GamePlayerData.GameEctype:GetProgress(EctypeType) 
    local maxChap = math.ceil((m.selectCatMainStageID)/4)
    m.objList.Txt_Chapter1.text = "第\n"..maxChap.."\n章"
    stageID = m.selectCatMainStageID
    local catMainStage = Schemes.CatMainStage:Get(stageID)
    m.objList.Txt_Chapter.text = catMainStage.Name

    local strList = HelperL.Split(catMainStage.GeneralDrop, '|')
    local dataList = {}
    local temp, prizeID, waveNumber
    for i, v in ipairs(strList) do
        temp = HelperL.Split(v, ';')
        prizeID = tonumber(temp[2]) or 0
        waveNumber = tonumber(temp[1]) or 0
        if prizeID > 0 and waveNumber > 0 then
            table.insert(dataList, { ID = stageID, Type = 1, PrizeID = prizeID, WaveNumber = waveNumber })
        end
    end

    local color = '#00FF00'
    local commonText = Schemes.CommonText:Get(121)
    local num2 = HeroDataManager:GetLogicByte(commonText.Param4, commonText.Param5)
    local value2 = EntityModule.hero:GetProperty(PLAYER_FIELD.PLAYER_FIELD_CURENERGY)

    local expendList = HelperL.Split(catMainStage.Need, ';')
    --消耗物品ID
    local expID = tonumber(expendList[1]) or 0
    --消耗物品数量
    local expNum = tonumber(expendList[2]) or 0
    --广告物品ID
    local adID = tonumber(expendList[3])
    --消耗物品ID2
    local expID2 = tonumber(expendList[4]) or 0
    --消耗物品数量2
    local expNum2 = tonumber(expendList[5]) or 0
    local count1 = SkepModule:GetGoodsCount(expID)
    local count2 = SkepModule:GetGoodsCount(expID2)

    local ex = tonumber(HelperL.Split(catMainStage.Need, ";")[2])

    m.objList.Obj_Start.gameObject:SetActive(false)
    m.objList.Btn_Start.gameObject:SetActive(false)
    m.objList.Btn_StartGray.gameObject:SetActive(false)
    HelperL.SetImageGray(m.objList.Img_Chapter, false)
    m.objList.Txt_StartHint.gameObject:SetActive(false)

    local maxStageID = GamePlayerData.GameEctype:GetChallengeableID(EctypeType)
    if stageID <= maxStageID then
        m.objList.Obj_Start.gameObject:SetActive(true)
        if count1 >= expNum and count2 >= expNum2 then
            m.objList.Btn_Start.gameObject:SetActive(true)
            --m.objList.Txt_Goods.text = string.format('<color=%s>%s</color>', color, value2)
        else
            m.objList.Btn_StartGray.gameObject:SetActive(true)
            local cardID = HelperL.GetGoodsIDByCardID(2)
            if cardID ~= 0 then
                -- m.objList.Btn_AD2.gameObject:SetActive(true)
            else
                -- m.objList.Btn_AD.gameObject:SetActive(true)
                local maxEnergy = Schemes.ConstValue:Get(CONST_VALUE.CONST_ENERGY_BUYONE_VALUE)
                if num2 >= commonText.DayTime then
                    color = '#F1A92F'
                end
                -- m.objList.Txt_Goods.text = string.format(
                --     '<color=#00FF00>+%s <color=%s>(%s/%s)</color></color>',
                --     maxEnergy, color, num2, commonText.DayTime
                -- )
            end
        end
        m.objList.Txt_StartExpend.text = "X" .. ex
        m.objList.Txt_StartExpendGray.text = string.format('<color=#F1A92F>X%s</color>', ex)
        m.objList.Txt_StartHint.text = catMainStage.Desc
        m.objList.Txt_StartHint.gameObject:SetActive(true)
    else
        HelperL.SetImageGray(m.objList.Img_Chapter, true)
    end

    --m.objList.Btn_UI1.gameObject:SetActive(UIManager:JudgeOpenLevel(WndID.CatPatrol, false) and stageID <= maxStageID)

    local list = Schemes.CatMainStage:GetByFrontType(EctypeType)
    local minID = list[1].ID
    local maxID = list[#list].ID
    local bool1 = false
    local bool2 = false
    for i = stageID - 1, minID, -1 do
        if RedDotCheckFunc:Check_UIMainEctype(i) then
            bool1 = true
            break
        end
    end
    for i = stageID + 1, maxID, 1 do
        if RedDotCheckFunc:Check_UIMainEctype(i) then
            bool2 = true
            break
        end
    end
    --m.objList.Img_RedDot1.gameObject:SetActive(bool1)
    --m.objList.Img_RedDot2.gameObject:SetActive(bool2)

    for i, v in ipairs(m.goodsItemList) do
        v.gameObject:SetActive(false)
    end

    --创建奖励 stageID
    local prizeGoods = Schemes.CatMainStage:GetSucceedRewards(stageID)
    if prizeGoods then
        for i, v in ipairs(prizeGoods) do
            if not m.goodsItemList[i] then
                m.goodsItemList[i] = _GAddSlotItem(m.objList.Grid_AwardShow)
            end
            if v then
                m.goodsItemList[i]:SetItemID(v.ID)
                m.goodsItemList[i]:SetCount(v.Num)
                m.goodsItemList[i].gameObject:SetActive(true)
            else
                m.goodsItemList[i].gameObject:SetActive(false)
            end
        end
    end


end

--------------------------------------------------------------------
-- 出征
--------------------------------------------------------------------
function m.ChuZheng(stageID)
    local cfg = Schemes.CatMainStage:Get(stageID)
    if not cfg then
        return
    end
    --不能使用原有关闭
    m:CloseSelf()
    --m.lastCatMainStageID = GamePlayerData.GameEctype:GetChallengeableID(EctypeType)
    -- 进入战斗
    BattleManager:EnterBattle(cfg.ID)
end

return m
