-- Generated By protoc-gen-lua Do not Edit
local protobuf = require "protobuf/protobuf"
module('GlobalGameMessage_pb')
local pb = {}


pb.MSG_GLOBALGAME_ACTIONID = protobuf.EnumDescriptor();
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBALGAME_NONE_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBALGAME_SETACTORDATA_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBALGAME_UPDATEACTORDATA_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBALGAME_SYNCACTOREVENT_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBALGAME_UPDATESERVERBASEDATA_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBALGAME_ACTORLUAREQUEST_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBALGAME_FLUSHACTORMAIL_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBALGAME_TRANSMESSAGE_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBALGAME_LEAVEGLOBALSERVER_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_PLAYROOM_CREATE_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_PLAYROOM_GETLIST_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_PLAYROOM_JOIN_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_PLAYROOM_QUIT_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_PLAYROOM_KICK_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_PLAYROOM_PREPARE_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_PLAYROOM_STARTPLAY_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_PLAYROOM_UPDATEROOMLISTINFO_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_PLAYROOM_UPDATEROOMINFO_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_PLAYROOM_INVITEOTHER_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_PLAYROOM_DELETE_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_PLAYROOM_REENTERECTYPE_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_PLAYROOM_ROOMEVENT_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_PLAYROOM_JOINTARGET_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_WILDSCENE_ENTER_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_WILDSCENE_LEAVE_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_ACTVECTYPE_ENTER_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_ACTVECTYPE_LEAVE_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_ACTV_RANK_VALUE_CHANGE_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_ACTV_RANK_LIST_GET_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_ACTV_RANK_VALUE_REPLACE_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_ACTVRECORD_UPDATEVALUE_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_ACTVRECORD_GETLIST_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_SOLOGAME_GETWINCOUNT_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_GAMEECTYPE_ENTER_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_GAMEECTYPE_LEAVE_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_GAMEECTYPE_RESULTDATA_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_STOPACTV_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBALGAME_SETCOUNTRYDATA_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBALGAME_SETSOCIETYDATA_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBALGAME_UPDATESOCIETYMEMBER_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBALGAME_SETWARBANDDATA_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBALGAME_UPDATEWARBANDMEMBER_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_MARKET_GETLIST_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_MARKET_GETSELF_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_MARKET_ADDSELLITEM_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_MARKET_ADDSELLITEMCHECK_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_MARKET_ADDSELLITEMCHECKRESULT_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_MARKET_REMOVESELLITEM_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_MARKET_REMOVESELLITEMRESULT_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_MARKET_BUYSELLITEM_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_MARKET_BUYSELLITEMCHECK_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_MARKET_BUYSELLITEMCHECKRESULT_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_CHAT_TRANS_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_ACTVCOUNT_ADD_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_WILDSCENE_BOSSINFO_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_SURVIVALCHALLENGE_ENTER_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_SURVIVALCHALLENGE_LEAVE_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_SURVIVALCHALLENGE_PLAYERLOCATION_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_SURVIVALCHALLENGE_BATTLEFIELDINFO_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBALGAME_ROBOTSWITCHZONE_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBALGAME_ROBOTSWITCHZONELOGIN_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_TRAININGGROUND_GETLIST_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_TRAININGGROUND_BEGINFIGHT_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_TRAININGGROUND_REFRESHLV_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_TRAININGGROUND_EXPLOITMINE_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_TRAININGGROUND_REFRESHMINLIST_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_TRAININGGROUND_PRIZE_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_TRAININGGROUND_CACHEPLAYERSIM_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_TRAININGGROUND_ENTER_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_TRAININGGROUND_PLUNDERLOG_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_TRAININGGROUND_AWARD_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_LANDCONQUEST_ENTER_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_MARKET_TRADERECORDE_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_LANDCONQUEST_GETBOSSINFO_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_LANDCONQUEST_WINNER_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_BARBARIAN_INVASION_UPDATE_SCORE_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_BARBARIAN_INVASION_RANKINFO_ENUM = protobuf.EnumValueDescriptor();
pb.SS_GLOBALGAME_SETACTORDATA = protobuf.Descriptor();
pb.SS_GLOBALGAME_SETACTORDATA_ACTORID_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBALGAME_SETACTORDATA_AGENTID_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBALGAME_SETACTORDATA_CLIENTID_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBALGAME_SETACTORDATA_ZONEID_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBALGAME_SETACTORDATA_ACTORNAME_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBALGAME_SETACTORDATA_ISONLINE_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBALGAME_SETACTORDATA_LEVEL_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBALGAME_SETACTORDATA_VOCATION_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBALGAME_SETACTORDATA_VIPLEVEL_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBALGAME_SETACTORDATA_POWER_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBALGAME_SETACTORDATA_ROBOTTYPE_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBALGAME_UPDATEACTORDATA = protobuf.Descriptor();
pb.SS_GLOBALGAME_UPDATEACTORDATA_ACTORID_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBALGAME_UPDATEACTORDATA_PROPTYPE_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBALGAME_UPDATEACTORDATA_PROPVALUE_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBALGAME_SYNCACTOREVENT = protobuf.Descriptor();
pb.SS_GLOBALGAME_SYNCACTOREVENT_ACTORID_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBALGAME_SYNCACTOREVENT_EVENTTYPE_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBALGAME_SYNCACTOREVENT_EVENTPARAM1_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBALGAME_SYNCACTOREVENT_EVENTPARAM2_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBALGAME_UPDATESERVERBASEDATA = protobuf.Descriptor();
pb.SS_GLOBALGAME_UPDATESERVERBASEDATA_ZONEID_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBALGAME_UPDATESERVERBASEDATA_OPENTIME_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBALGAME_UPDATESERVERBASEDATA_MERGETIME_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBALGAME_UPDATESERVERBASEDATA_RANKLV_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBALGAME_ACTORLUAREQUEST = protobuf.Descriptor();
pb.SS_GLOBALGAME_ACTORLUAREQUEST_ACTORID_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBALGAME_ACTORLUAREQUEST_REQUEST_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBALGAME_ACTORLUAREQUEST_PARAM1_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBALGAME_ACTORLUAREQUEST_PARAM2_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBALGAME_FLUSHACTORMAIL = protobuf.Descriptor();
pb.SS_GLOBALGAME_FLUSHACTORMAIL_ACTORID_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBALGAME_TRANSMESSAGE = protobuf.Descriptor();
pb.SS_GLOBALGAME_TRANSMESSAGE_ACTORID_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBALGAME_TRANSMESSAGE_ACTIONID_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBALGAME_TRANSMESSAGE_MESSAGEDATA_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBAL_PLAYROOMCREATE = protobuf.Descriptor();
pb.SS_GLOBAL_PLAYROOMCREATE_ACTORID_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBAL_PLAYROOMCREATE_ROOMTYPE_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBAL_PLAYROOMCREATE_ROOMNAME_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBAL_PLAYROOMCREATE_NEEDPOWER_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBAL_PLAYROOMCREATE_AUTOSTART_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBAL_PLAYROOMCREATE_PARAM1_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBAL_PLAYROOMGETLIST = protobuf.Descriptor();
pb.SS_GLOBAL_PLAYROOMGETLIST_ACTORID_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBAL_PLAYROOMGETLIST_ROOMTYPE_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBAL_PLAYROOMGETLIST_BEGINROOMID_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBAL_PLAYROOMJOIN = protobuf.Descriptor();
pb.SS_GLOBAL_PLAYROOMJOIN_ACTORID_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBAL_PLAYROOMJOIN_ROOMTYPE_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBAL_PLAYROOMJOIN_ROOMID_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBAL_PLAYROOMJOIN_ROOMNAME_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBAL_PLAYROOMJOIN_NOTICEPREPARE_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBAL_PLAYROOMQUIT = protobuf.Descriptor();
pb.SS_GLOBAL_PLAYROOMQUIT_ACTORID_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBAL_PLAYROOMQUIT_ROOMID_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBAL_PLAYROOMKICK = protobuf.Descriptor();
pb.SS_GLOBAL_PLAYROOMKICK_ACTORID_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBAL_PLAYROOMKICK_TARACTORID_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBAL_PLAYROOMPREPARE = protobuf.Descriptor();
pb.SS_GLOBAL_PLAYROOMPREPARE_ACTORID_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBAL_PLAYROOMPREPARE_ISPREPARE_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBAL_PLAYROOMSTARTPLAY = protobuf.Descriptor();
pb.SS_GLOBAL_PLAYROOMSTARTPLAY_ACTORID_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBAL_PLAYROOMUPDATEROOMLISTINFO = protobuf.Descriptor();
pb.SS_GLOBAL_PLAYROOMUPDATEROOMLISTINFO_ACTORID_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBAL_PLAYROOMUPDATEROOMLISTINFO_STOPTRACE_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBAL_PLAYROOMUPDATEROOMINFO = protobuf.Descriptor();
pb.SS_GLOBAL_PLAYROOMUPDATEROOMINFO_ACTORID_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBAL_PLAYROOMUPDATEROOMINFO_AUTOSTART_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBAL_PLAYROOMINVITEOTHER = protobuf.Descriptor();
pb.SS_GLOBAL_PLAYROOMINVITEOTHER_ACTORID_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBAL_PLAYROOMINVITEOTHER_TARACTORID_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBAL_PLAYROOMINVITEOTHER_INVITETYPE_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBAL_PLAYROOMDELETE = protobuf.Descriptor();
pb.SS_GLOBAL_PLAYROOMDELETE_ACTORID_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBAL_PLAYROOMREENTERECTYPE = protobuf.Descriptor();
pb.SS_GLOBAL_PLAYROOMREENTERECTYPE_ACTORID_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBAL_PLAYROOMEVENT = protobuf.Descriptor();
pb.SS_GLOBAL_PLAYROOMEVENT_EVENTTYPE_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBAL_PLAYROOMEVENT_ACTORID_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBAL_PLAYROOMEVENT_ROOMTYPE_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBAL_PLAYROOMEVENT_ROOMID_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBAL_PLAYROOMJOINTARGET = protobuf.Descriptor();
pb.SS_GLOBAL_PLAYROOMJOINTARGET_ACTORID_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBAL_PLAYROOMJOINTARGET_TARGETACTORID_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBAL_PLAYROOMJOINTARGET_AUTOPREPARE_FIELD = protobuf.FieldDescriptor();
pb.CS_GLOBAL_WILDSCENEENTER = protobuf.Descriptor();
pb.CS_GLOBAL_WILDSCENEENTER_SCENEID_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBAL_WILDSCENEENTER = protobuf.Descriptor();
pb.SS_GLOBAL_WILDSCENEENTER_ACTORID_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBAL_WILDSCENEENTER_SCENEID_FIELD = protobuf.FieldDescriptor();
pb.CS_GLOBAL_WILDSCENELEAVE = protobuf.Descriptor();
pb.CS_GLOBAL_WILDSCENELEAVE_ACTORID_FIELD = protobuf.FieldDescriptor();
pb.CS_GLOBAL_ACTVECTYPEENTER = protobuf.Descriptor();
pb.CS_GLOBAL_ACTVECTYPEENTER_ACTVID_FIELD = protobuf.FieldDescriptor();
pb.CS_GLOBAL_ACTVECTYPEENTER_PARAM_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBAL_ACTVECTYPEENTER = protobuf.Descriptor();
pb.SS_GLOBAL_ACTVECTYPEENTER_ACTORID_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBAL_ACTVECTYPEENTER_ACTVID_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBAL_ACTVECTYPEENTER_PARAM_FIELD = protobuf.FieldDescriptor();
pb.CS_GLOBAL_ACTVECTYPELEAVE = protobuf.Descriptor();
pb.CS_GLOBAL_ACTVECTYPELEAVE_ACTORID_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBAL_ACTVRANKVALUECHANGE = protobuf.Descriptor();
pb.SS_GLOBAL_ACTVRANKVALUECHANGE_ACTORID_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBAL_ACTVRANKVALUECHANGE_ACTVID_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBAL_ACTVRANKVALUECHANGE_RANKVALUE_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBAL_ACTVRANKVALUECHANGE_SETTYPE_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBAL_ACTVRANKVALUECHANGE_ZONEID_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBAL_ACTVRANKLISTGET = protobuf.Descriptor();
pb.SS_GLOBAL_ACTVRANKLISTGET_ACTORID_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBAL_ACTVRANKLISTGET_SOCIETYID_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBAL_ACTVRANKLISTGET_ACTVID_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBAL_ACTVRANKLISTGET_ZONEID_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBAL_ACTVRANKLISTREPLACE = protobuf.Descriptor();
pb.SS_GLOBAL_ACTVRANKLISTREPLACE_ACTORID_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBAL_ACTVRANKLISTREPLACE_ACTVID_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBAL_ACTVRANKLISTREPLACE_NEWVALUE_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBAL_ACTVRANKLISTREPLACE_REPLACETYPE_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBAL_ACTVRANKLISTREPLACE_ZONEID_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBAL_ACTVRECORDUPDATEVALUE = protobuf.Descriptor();
pb.SS_GLOBAL_ACTVRECORDUPDATEVALUE_ACTORID_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBAL_ACTVRECORDUPDATEVALUE_ACTVID_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBAL_ACTVRECORDUPDATEVALUE_VALUE_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBAL_ACTVRECORDUPDATEVALUE_PARAM_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBAL_ACTVRECORDUPDATEVALUE_ZONEID_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBAL_ACTVRECORDGETLIST = protobuf.Descriptor();
pb.SS_GLOBAL_ACTVRECORDGETLIST_ACTORID_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBAL_ACTVRECORDGETLIST_ACTIVITYID_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBAL_SOLOGAMEGETWINCOUNT = protobuf.Descriptor();
pb.SS_GLOBAL_SOLOGAMEGETWINCOUNT_ACTORID_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBAL_SOLOGAMEGETWINCOUNT_ZONEID_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBAL_SOLOGAMEGETWINCOUNT_PARAM_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBAL_SOLOGAMEGETWINCOUNT_COUNT_FIELD = protobuf.FieldDescriptor();
pb.CS_GLOBAL_GAMEECTYPEENTER = protobuf.Descriptor();
pb.CS_GLOBAL_GAMEECTYPEENTER_CONFIGID_FIELD = protobuf.FieldDescriptor();
pb.CS_GLOBAL_GAMEECTYPEENTER_PARAM_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBAL_GAMEECTYPEENTER = protobuf.Descriptor();
pb.SS_GLOBAL_GAMEECTYPEENTER_ACTORID_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBAL_GAMEECTYPEENTER_CONFIGID_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBAL_GAMEECTYPEENTER_PARAM_FIELD = protobuf.FieldDescriptor();
pb.CS_GLOBAL_GAMEECTYPELEAVE = protobuf.Descriptor();
pb.CS_GLOBAL_GAMEECTYPELEAVE_ACTORID_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBAL_STOPACTV = protobuf.Descriptor();
pb.SS_GLOBAL_STOPACTV_ACTVID_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBALGAME_SETCOUNTRYDATA = protobuf.Descriptor();
pb.SS_GLOBALGAME_SETCOUNTRYDATA_OFFICEDATA = protobuf.Descriptor();
pb.SS_GLOBALGAME_SETCOUNTRYDATA_OFFICEDATA_ACTORID_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBALGAME_SETCOUNTRYDATA_OFFICEDATA_ACTORNAME_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBALGAME_SETCOUNTRYDATA_OFFICEDATA_SOCIETYNAME_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBALGAME_SETCOUNTRYDATA_OFFICEDATA_LEVEL_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBALGAME_SETCOUNTRYDATA_OFFICEDATA_VOCATION_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBALGAME_SETCOUNTRYDATA_OFFICEDATA_POWER_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBALGAME_SETCOUNTRYDATA_OFFICEDATA_OFFICETYPE_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBALGAME_SETCOUNTRYDATA_ZONEID_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBALGAME_SETCOUNTRYDATA_COUNTRYID_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBALGAME_SETCOUNTRYDATA_COUNTRYPOWER_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBALGAME_SETCOUNTRYDATA_COUNTRYMONEY_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBALGAME_SETCOUNTRYDATA_OFFICERLIST_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBALGAME_SETSOCIETYDATA = protobuf.Descriptor();
pb.SS_GLOBALGAME_SETSOCIETYDATA_ZONEID_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBALGAME_SETSOCIETYDATA_SOCIETYID_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBALGAME_SETSOCIETYDATA_SOCIETYNAME_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBALGAME_SETSOCIETYDATA_SOCIETYLEVEL_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBALGAME_UPDATESOCIETYMEMBER = protobuf.Descriptor();
pb.SS_GLOBALGAME_UPDATESOCIETYMEMBER_MEMBERDATA = protobuf.Descriptor();
pb.SS_GLOBALGAME_UPDATESOCIETYMEMBER_MEMBERDATA_ACTORID_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBALGAME_UPDATESOCIETYMEMBER_MEMBERDATA_ACTORNAME_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBALGAME_UPDATESOCIETYMEMBER_MEMBERDATA_LEVEL_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBALGAME_UPDATESOCIETYMEMBER_MEMBERDATA_VOCATION_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBALGAME_UPDATESOCIETYMEMBER_MEMBERDATA_POWER_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBALGAME_UPDATESOCIETYMEMBER_MEMBERDATA_TITLE_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBALGAME_UPDATESOCIETYMEMBER_ZONEID_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBALGAME_UPDATESOCIETYMEMBER_SOCIETYID_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBALGAME_UPDATESOCIETYMEMBER_ISREPLACE_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBALGAME_UPDATESOCIETYMEMBER_MEMBERLIST_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBALGAME_SETWARBANDDATA = protobuf.Descriptor();
pb.SS_GLOBALGAME_SETWARBANDDATA_ZONEID_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBALGAME_SETWARBANDDATA_WARBANDID_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBALGAME_SETWARBANDDATA_WARBANDNAME_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBALGAME_SETWARBANDDATA_COUNTRYID_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBALGAME_UPDATEWARBANDMEMBER = protobuf.Descriptor();
pb.SS_GLOBALGAME_UPDATEWARBANDMEMBER_MEMBERDATA = protobuf.Descriptor();
pb.SS_GLOBALGAME_UPDATEWARBANDMEMBER_MEMBERDATA_ACTORID_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBALGAME_UPDATEWARBANDMEMBER_MEMBERDATA_ACTORNAME_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBALGAME_UPDATEWARBANDMEMBER_MEMBERDATA_LEVEL_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBALGAME_UPDATEWARBANDMEMBER_MEMBERDATA_VOCATION_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBALGAME_UPDATEWARBANDMEMBER_MEMBERDATA_POWER_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBALGAME_UPDATEWARBANDMEMBER_ZONEID_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBALGAME_UPDATEWARBANDMEMBER_WARBANDID_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBALGAME_UPDATEWARBANDMEMBER_ISREPLACE_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBALGAME_UPDATEWARBANDMEMBER_MEMBERLIST_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBALGAME_MARKETADDSELLITEM = protobuf.Descriptor();
pb.SS_GLOBALGAME_MARKETADDSELLITEM_ZONEID_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBALGAME_MARKETADDSELLITEM_ACTORID_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBALGAME_MARKETADDSELLITEM_PRICE_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBALGAME_MARKETADDSELLITEM_GOODSUID_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBALGAME_MARKETADDSELLITEM_GOODSDATA_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBALGAME_MARKETADDSELLITEMCHECK = protobuf.Descriptor();
pb.SS_GLOBALGAME_MARKETADDSELLITEMCHECK_ACTORID_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBALGAME_MARKETADDSELLITEMCHECK_GOODSUID_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBALGAME_MARKETADDSELLITEMCHECK_GOODSNUM_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBALGAME_MARKETADDSELLITEMCHECK_SELLID_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBALGAME_MARKETADDSELLITEMCHECKRESULT = protobuf.Descriptor();
pb.SS_GLOBALGAME_MARKETADDSELLITEMCHECKRESULT_ZONEID_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBALGAME_MARKETADDSELLITEMCHECKRESULT_ACTORID_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBALGAME_MARKETADDSELLITEMCHECKRESULT_SELLID_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBALGAME_MARKETADDSELLITEMCHECKRESULT_RESULT_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBALGAME_MARKETREMOVESELLITEM = protobuf.Descriptor();
pb.SS_GLOBALGAME_MARKETREMOVESELLITEM_ZONEID_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBALGAME_MARKETREMOVESELLITEM_ACTORID_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBALGAME_MARKETREMOVESELLITEM_SELLID_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBALGAME_MARKETREMOVESELLITEM_GOODSNUM_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBALGAME_MARKETREMOVESELLITEMRESULT = protobuf.Descriptor();
pb.SS_GLOBALGAME_MARKETREMOVESELLITEMRESULT_ACTORID_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBALGAME_MARKETREMOVESELLITEMRESULT_SELLID_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBALGAME_MARKETREMOVESELLITEMRESULT_RESULT_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBALGAME_MARKETREMOVESELLITEMRESULT_GOODSDATA_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBALGAME_MARKETBUYSELLITEM = protobuf.Descriptor();
pb.SS_GLOBALGAME_MARKETBUYSELLITEM_ZONEID_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBALGAME_MARKETBUYSELLITEM_ACTORID_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBALGAME_MARKETBUYSELLITEM_SELLID_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBALGAME_MARKETBUYSELLITEM_GOODSNUM_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBALGAME_MARKETBUYSELLITEM_COSTVALUE_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBALGAME_MARKETBUYSELLITEMCHECK = protobuf.Descriptor();
pb.SS_GLOBALGAME_MARKETBUYSELLITEMCHECK_ACTORID_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBALGAME_MARKETBUYSELLITEMCHECK_SELLID_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBALGAME_MARKETBUYSELLITEMCHECK_TOTALPRICE_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBALGAME_MARKETBUYSELLITEMCHECK_BINDRATE_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBALGAME_MARKETBUYSELLITEMCHECK_GOODSDATA_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBALGAME_MARKETBUYSELLITEMCHECK_SELLACTORID_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBALGAME_MARKETBUYSELLITEMCHECKRESULT = protobuf.Descriptor();
pb.SS_GLOBALGAME_MARKETBUYSELLITEMCHECKRESULT_ZONEID_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBALGAME_MARKETBUYSELLITEMCHECKRESULT_ACTORID_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBALGAME_MARKETBUYSELLITEMCHECKRESULT_SELLID_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBALGAME_MARKETBUYSELLITEMCHECKRESULT_RESULT_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBALGAME_MARKETBUYSELLITEMCHECKRESULT_GOODSNUM_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBALGAME_MARKETBUYSELLITEMCHECKRESULT_TOTALPRICE_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBALGAME_ACTVCOUNTADD = protobuf.Descriptor();
pb.SS_GLOBALGAME_ACTVCOUNTADD_TARGETTYPE_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBALGAME_ACTVCOUNTADD_TARGETID_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBALGAME_ACTVCOUNTADD_ADDVALUE_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBALGAME_ACTVCOUNTADD_PARAM1_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBALGAME_ACTVCOUNTADD_PARAM2_FIELD = protobuf.FieldDescriptor();
pb.CS_GLOBAL_SURVIVALCHALLENGEENTER = protobuf.Descriptor();
pb.CS_GLOBAL_SURVIVALCHALLENGEENTER_ACTVID_FIELD = protobuf.FieldDescriptor();
pb.CS_GLOBAL_SURVIVALCHALLENGEENTER_PARAM_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBAL_SURVIVALCHALLENGEENTER = protobuf.Descriptor();
pb.SS_GLOBAL_SURVIVALCHALLENGEENTER_ACTORID_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBAL_SURVIVALCHALLENGEENTER_ACTVID_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBAL_SURVIVALCHALLENGEENTER_PARAM_FIELD = protobuf.FieldDescriptor();
pb.CS_GLOBAL_SURVIVALCHALLENGELEAVE = protobuf.Descriptor();
pb.CS_GLOBAL_SURVIVALCHALLENGELEAVE_ACTORID_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBALGAME_ROBOTSWITCHZONE = protobuf.Descriptor();
pb.SS_GLOBALGAME_ROBOTSWITCHZONE_ACTORID_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBALGAME_ROBOTSWITCHZONE_ZONEID_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBALGAME_ROBOTSWITCHZONE_ROBOTTYPE_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBALGAME_ROBOTSWITCHZONELOGIN = protobuf.Descriptor();
pb.SS_GLOBALGAME_ROBOTSWITCHZONELOGIN_ACTORID_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBALGAME_ROBOTSWITCHZONELOGIN_ROBOTTYPE_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBAL_TRAININGGROUND_PLAYERSIMPLE = protobuf.Descriptor();
pb.SS_GLOBAL_TRAININGGROUND_PLAYERSIMPLE_ACTORID_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBAL_TRAININGGROUND_PLAYERSIMPLE_ZONEID_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBAL_TRAININGGROUND_PLAYERSIMPLE_ACTORNAME_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBAL_TRAININGGROUND_PLAYERSIMPLE_ACTORVOCATION_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBAL_TRAININGGROUND_PLAYERSIMPLE_ACTORCOUNTRY_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBAL_TRAININGGROUND_PLAYERSIMPLE_ACTORLEVEL_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBAL_TRAININGGROUND_PLAYERSIMPLE_POWER_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBAL_TRAININGGROUND_PLAYERSIMPLE_CURHP_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBAL_TRAININGGROUND_PLAYERSIMPLE_MAXHP_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBAL_TRAININGGROUNDENTER = protobuf.Descriptor();
pb.SS_GLOBAL_TRAININGGROUNDENTER_ACTORID_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBAL_TRAININGGROUNDENTER_TARACTORID_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBAL_TRAININGGROUNDENTER_MININGPOS_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBAL_TRAININGGROUNDENTER_MININGID_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBAL_TRAININGGROUNDENTER_BATTLETYPE_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBAL_TRAININGGROUND_AWARD = protobuf.Descriptor();
pb.SS_GLOBAL_TRAININGGROUND_AWARD_RESDATA = protobuf.Descriptor();
pb.SS_GLOBAL_TRAININGGROUND_AWARD_RESDATA_RESNUM_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBAL_TRAININGGROUND_AWARD_RESDATA_RESTYPE_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBAL_TRAININGGROUND_AWARD_RESDATA_BATTLETYPE_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBAL_TRAININGGROUND_AWARD_ZONEID_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBAL_TRAININGGROUND_AWARD_ACTORID_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBAL_TRAININGGROUND_AWARD_RESLIST_FIELD = protobuf.FieldDescriptor();
pb.CS_GLOBAL_LANDCONQUESTENTER = protobuf.Descriptor();
pb.CS_GLOBAL_LANDCONQUESTENTER_PARAM_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBAL_LANDCONQUESTENTER = protobuf.Descriptor();
pb.SS_GLOBAL_LANDCONQUESTENTER_ACTORID_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBAL_LANDCONQUESTENTER_PARAM_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBAL_MARKET_TRADERECORD = protobuf.Descriptor();
pb.SS_GLOBAL_MARKET_TRADERECORD_ACTORID_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBAL_MARKET_TRADERECORD_ZONEID_FIELD = protobuf.FieldDescriptor();
pb.CS_GLOBAL_LANDCONQUESTGETBOSSINFO = protobuf.Descriptor();
pb.CS_GLOBAL_LANDCONQUESTGETBOSSINFO_PARAM_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBAL_LANDCONQUESTGETBOSSINFO = protobuf.Descriptor();
pb.SS_GLOBAL_LANDCONQUESTGETBOSSINFO_ACTORID_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBAL_LANDCONQUESTGETBOSSINFO_PARAM_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBAL_LANDCONQUESTWINNER = protobuf.Descriptor();
pb.SS_GLOBAL_LANDCONQUESTWINNER_TYPE_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBAL_LANDCONQUESTWINNER_ZONEID_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBAL_LANDCONQUESTWINNER_ISWINNER_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBAL_BARBARIANINVASION_UPDATESCORE = protobuf.Descriptor();
pb.SS_GLOBAL_BARBARIANINVASION_UPDATESCORE_ROOMID_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBAL_BARBARIANINVASION_UPDATESCORE_SCORE_FIELD = protobuf.FieldDescriptor();
pb.SS_GLOBAL_BARBARIANINVASION_RANKINFO = protobuf.Descriptor();
pb.SS_GLOBAL_BARBARIANINVASION_RANKINFO_ACTORID_FIELD = protobuf.FieldDescriptor();

pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBALGAME_NONE_ENUM.name = "MSG_GLOBALGAME_NONE"
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBALGAME_NONE_ENUM.index = 0
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBALGAME_NONE_ENUM.number = 0
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBALGAME_SETACTORDATA_ENUM.name = "MSG_GLOBALGAME_SETACTORDATA"
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBALGAME_SETACTORDATA_ENUM.index = 1
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBALGAME_SETACTORDATA_ENUM.number = 1
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBALGAME_UPDATEACTORDATA_ENUM.name = "MSG_GLOBALGAME_UPDATEACTORDATA"
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBALGAME_UPDATEACTORDATA_ENUM.index = 2
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBALGAME_UPDATEACTORDATA_ENUM.number = 2
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBALGAME_SYNCACTOREVENT_ENUM.name = "MSG_GLOBALGAME_SYNCACTOREVENT"
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBALGAME_SYNCACTOREVENT_ENUM.index = 3
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBALGAME_SYNCACTOREVENT_ENUM.number = 3
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBALGAME_UPDATESERVERBASEDATA_ENUM.name = "MSG_GLOBALGAME_UPDATESERVERBASEDATA"
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBALGAME_UPDATESERVERBASEDATA_ENUM.index = 4
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBALGAME_UPDATESERVERBASEDATA_ENUM.number = 4
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBALGAME_ACTORLUAREQUEST_ENUM.name = "MSG_GLOBALGAME_ACTORLUAREQUEST"
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBALGAME_ACTORLUAREQUEST_ENUM.index = 5
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBALGAME_ACTORLUAREQUEST_ENUM.number = 5
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBALGAME_FLUSHACTORMAIL_ENUM.name = "MSG_GLOBALGAME_FLUSHACTORMAIL"
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBALGAME_FLUSHACTORMAIL_ENUM.index = 6
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBALGAME_FLUSHACTORMAIL_ENUM.number = 6
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBALGAME_TRANSMESSAGE_ENUM.name = "MSG_GLOBALGAME_TRANSMESSAGE"
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBALGAME_TRANSMESSAGE_ENUM.index = 7
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBALGAME_TRANSMESSAGE_ENUM.number = 7
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBALGAME_LEAVEGLOBALSERVER_ENUM.name = "MSG_GLOBALGAME_LEAVEGLOBALSERVER"
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBALGAME_LEAVEGLOBALSERVER_ENUM.index = 8
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBALGAME_LEAVEGLOBALSERVER_ENUM.number = 8
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_PLAYROOM_CREATE_ENUM.name = "MSG_GLOBAL_PLAYROOM_CREATE"
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_PLAYROOM_CREATE_ENUM.index = 9
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_PLAYROOM_CREATE_ENUM.number = 10
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_PLAYROOM_GETLIST_ENUM.name = "MSG_GLOBAL_PLAYROOM_GETLIST"
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_PLAYROOM_GETLIST_ENUM.index = 10
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_PLAYROOM_GETLIST_ENUM.number = 11
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_PLAYROOM_JOIN_ENUM.name = "MSG_GLOBAL_PLAYROOM_JOIN"
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_PLAYROOM_JOIN_ENUM.index = 11
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_PLAYROOM_JOIN_ENUM.number = 12
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_PLAYROOM_QUIT_ENUM.name = "MSG_GLOBAL_PLAYROOM_QUIT"
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_PLAYROOM_QUIT_ENUM.index = 12
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_PLAYROOM_QUIT_ENUM.number = 13
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_PLAYROOM_KICK_ENUM.name = "MSG_GLOBAL_PLAYROOM_KICK"
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_PLAYROOM_KICK_ENUM.index = 13
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_PLAYROOM_KICK_ENUM.number = 14
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_PLAYROOM_PREPARE_ENUM.name = "MSG_GLOBAL_PLAYROOM_PREPARE"
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_PLAYROOM_PREPARE_ENUM.index = 14
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_PLAYROOM_PREPARE_ENUM.number = 15
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_PLAYROOM_STARTPLAY_ENUM.name = "MSG_GLOBAL_PLAYROOM_STARTPLAY"
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_PLAYROOM_STARTPLAY_ENUM.index = 15
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_PLAYROOM_STARTPLAY_ENUM.number = 16
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_PLAYROOM_UPDATEROOMLISTINFO_ENUM.name = "MSG_GLOBAL_PLAYROOM_UPDATEROOMLISTINFO"
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_PLAYROOM_UPDATEROOMLISTINFO_ENUM.index = 16
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_PLAYROOM_UPDATEROOMLISTINFO_ENUM.number = 17
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_PLAYROOM_UPDATEROOMINFO_ENUM.name = "MSG_GLOBAL_PLAYROOM_UPDATEROOMINFO"
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_PLAYROOM_UPDATEROOMINFO_ENUM.index = 17
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_PLAYROOM_UPDATEROOMINFO_ENUM.number = 18
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_PLAYROOM_INVITEOTHER_ENUM.name = "MSG_GLOBAL_PLAYROOM_INVITEOTHER"
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_PLAYROOM_INVITEOTHER_ENUM.index = 18
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_PLAYROOM_INVITEOTHER_ENUM.number = 19
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_PLAYROOM_DELETE_ENUM.name = "MSG_GLOBAL_PLAYROOM_DELETE"
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_PLAYROOM_DELETE_ENUM.index = 19
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_PLAYROOM_DELETE_ENUM.number = 20
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_PLAYROOM_REENTERECTYPE_ENUM.name = "MSG_GLOBAL_PLAYROOM_REENTERECTYPE"
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_PLAYROOM_REENTERECTYPE_ENUM.index = 20
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_PLAYROOM_REENTERECTYPE_ENUM.number = 21
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_PLAYROOM_ROOMEVENT_ENUM.name = "MSG_GLOBAL_PLAYROOM_ROOMEVENT"
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_PLAYROOM_ROOMEVENT_ENUM.index = 21
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_PLAYROOM_ROOMEVENT_ENUM.number = 22
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_PLAYROOM_JOINTARGET_ENUM.name = "MSG_GLOBAL_PLAYROOM_JOINTARGET"
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_PLAYROOM_JOINTARGET_ENUM.index = 22
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_PLAYROOM_JOINTARGET_ENUM.number = 23
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_WILDSCENE_ENTER_ENUM.name = "MSG_GLOBAL_WILDSCENE_ENTER"
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_WILDSCENE_ENTER_ENUM.index = 23
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_WILDSCENE_ENTER_ENUM.number = 30
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_WILDSCENE_LEAVE_ENUM.name = "MSG_GLOBAL_WILDSCENE_LEAVE"
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_WILDSCENE_LEAVE_ENUM.index = 24
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_WILDSCENE_LEAVE_ENUM.number = 31
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_ACTVECTYPE_ENTER_ENUM.name = "MSG_GLOBAL_ACTVECTYPE_ENTER"
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_ACTVECTYPE_ENTER_ENUM.index = 25
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_ACTVECTYPE_ENTER_ENUM.number = 40
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_ACTVECTYPE_LEAVE_ENUM.name = "MSG_GLOBAL_ACTVECTYPE_LEAVE"
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_ACTVECTYPE_LEAVE_ENUM.index = 26
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_ACTVECTYPE_LEAVE_ENUM.number = 41
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_ACTV_RANK_VALUE_CHANGE_ENUM.name = "MSG_GLOBAL_ACTV_RANK_VALUE_CHANGE"
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_ACTV_RANK_VALUE_CHANGE_ENUM.index = 27
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_ACTV_RANK_VALUE_CHANGE_ENUM.number = 42
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_ACTV_RANK_LIST_GET_ENUM.name = "MSG_GLOBAL_ACTV_RANK_LIST_GET"
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_ACTV_RANK_LIST_GET_ENUM.index = 28
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_ACTV_RANK_LIST_GET_ENUM.number = 43
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_ACTV_RANK_VALUE_REPLACE_ENUM.name = "MSG_GLOBAL_ACTV_RANK_VALUE_REPLACE"
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_ACTV_RANK_VALUE_REPLACE_ENUM.index = 29
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_ACTV_RANK_VALUE_REPLACE_ENUM.number = 44
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_ACTVRECORD_UPDATEVALUE_ENUM.name = "MSG_GLOBAL_ACTVRECORD_UPDATEVALUE"
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_ACTVRECORD_UPDATEVALUE_ENUM.index = 30
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_ACTVRECORD_UPDATEVALUE_ENUM.number = 45
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_ACTVRECORD_GETLIST_ENUM.name = "MSG_GLOBAL_ACTVRECORD_GETLIST"
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_ACTVRECORD_GETLIST_ENUM.index = 31
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_ACTVRECORD_GETLIST_ENUM.number = 46
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_SOLOGAME_GETWINCOUNT_ENUM.name = "MSG_GLOBAL_SOLOGAME_GETWINCOUNT"
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_SOLOGAME_GETWINCOUNT_ENUM.index = 32
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_SOLOGAME_GETWINCOUNT_ENUM.number = 50
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_GAMEECTYPE_ENTER_ENUM.name = "MSG_GLOBAL_GAMEECTYPE_ENTER"
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_GAMEECTYPE_ENTER_ENUM.index = 33
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_GAMEECTYPE_ENTER_ENUM.number = 51
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_GAMEECTYPE_LEAVE_ENUM.name = "MSG_GLOBAL_GAMEECTYPE_LEAVE"
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_GAMEECTYPE_LEAVE_ENUM.index = 34
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_GAMEECTYPE_LEAVE_ENUM.number = 52
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_GAMEECTYPE_RESULTDATA_ENUM.name = "MSG_GLOBAL_GAMEECTYPE_RESULTDATA"
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_GAMEECTYPE_RESULTDATA_ENUM.index = 35
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_GAMEECTYPE_RESULTDATA_ENUM.number = 53
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_STOPACTV_ENUM.name = "MSG_GLOBAL_STOPACTV"
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_STOPACTV_ENUM.index = 36
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_STOPACTV_ENUM.number = 54
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBALGAME_SETCOUNTRYDATA_ENUM.name = "MSG_GLOBALGAME_SETCOUNTRYDATA"
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBALGAME_SETCOUNTRYDATA_ENUM.index = 37
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBALGAME_SETCOUNTRYDATA_ENUM.number = 60
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBALGAME_SETSOCIETYDATA_ENUM.name = "MSG_GLOBALGAME_SETSOCIETYDATA"
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBALGAME_SETSOCIETYDATA_ENUM.index = 38
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBALGAME_SETSOCIETYDATA_ENUM.number = 61
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBALGAME_UPDATESOCIETYMEMBER_ENUM.name = "MSG_GLOBALGAME_UPDATESOCIETYMEMBER"
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBALGAME_UPDATESOCIETYMEMBER_ENUM.index = 39
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBALGAME_UPDATESOCIETYMEMBER_ENUM.number = 62
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBALGAME_SETWARBANDDATA_ENUM.name = "MSG_GLOBALGAME_SETWARBANDDATA"
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBALGAME_SETWARBANDDATA_ENUM.index = 40
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBALGAME_SETWARBANDDATA_ENUM.number = 63
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBALGAME_UPDATEWARBANDMEMBER_ENUM.name = "MSG_GLOBALGAME_UPDATEWARBANDMEMBER"
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBALGAME_UPDATEWARBANDMEMBER_ENUM.index = 41
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBALGAME_UPDATEWARBANDMEMBER_ENUM.number = 64
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_MARKET_GETLIST_ENUM.name = "MSG_GLOBAL_MARKET_GETLIST"
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_MARKET_GETLIST_ENUM.index = 42
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_MARKET_GETLIST_ENUM.number = 70
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_MARKET_GETSELF_ENUM.name = "MSG_GLOBAL_MARKET_GETSELF"
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_MARKET_GETSELF_ENUM.index = 43
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_MARKET_GETSELF_ENUM.number = 71
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_MARKET_ADDSELLITEM_ENUM.name = "MSG_GLOBAL_MARKET_ADDSELLITEM"
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_MARKET_ADDSELLITEM_ENUM.index = 44
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_MARKET_ADDSELLITEM_ENUM.number = 72
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_MARKET_ADDSELLITEMCHECK_ENUM.name = "MSG_GLOBAL_MARKET_ADDSELLITEMCHECK"
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_MARKET_ADDSELLITEMCHECK_ENUM.index = 45
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_MARKET_ADDSELLITEMCHECK_ENUM.number = 73
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_MARKET_ADDSELLITEMCHECKRESULT_ENUM.name = "MSG_GLOBAL_MARKET_ADDSELLITEMCHECKRESULT"
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_MARKET_ADDSELLITEMCHECKRESULT_ENUM.index = 46
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_MARKET_ADDSELLITEMCHECKRESULT_ENUM.number = 74
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_MARKET_REMOVESELLITEM_ENUM.name = "MSG_GLOBAL_MARKET_REMOVESELLITEM"
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_MARKET_REMOVESELLITEM_ENUM.index = 47
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_MARKET_REMOVESELLITEM_ENUM.number = 75
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_MARKET_REMOVESELLITEMRESULT_ENUM.name = "MSG_GLOBAL_MARKET_REMOVESELLITEMRESULT"
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_MARKET_REMOVESELLITEMRESULT_ENUM.index = 48
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_MARKET_REMOVESELLITEMRESULT_ENUM.number = 76
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_MARKET_BUYSELLITEM_ENUM.name = "MSG_GLOBAL_MARKET_BUYSELLITEM"
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_MARKET_BUYSELLITEM_ENUM.index = 49
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_MARKET_BUYSELLITEM_ENUM.number = 77
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_MARKET_BUYSELLITEMCHECK_ENUM.name = "MSG_GLOBAL_MARKET_BUYSELLITEMCHECK"
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_MARKET_BUYSELLITEMCHECK_ENUM.index = 50
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_MARKET_BUYSELLITEMCHECK_ENUM.number = 78
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_MARKET_BUYSELLITEMCHECKRESULT_ENUM.name = "MSG_GLOBAL_MARKET_BUYSELLITEMCHECKRESULT"
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_MARKET_BUYSELLITEMCHECKRESULT_ENUM.index = 51
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_MARKET_BUYSELLITEMCHECKRESULT_ENUM.number = 79
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_CHAT_TRANS_ENUM.name = "MSG_GLOBAL_CHAT_TRANS"
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_CHAT_TRANS_ENUM.index = 52
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_CHAT_TRANS_ENUM.number = 80
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_ACTVCOUNT_ADD_ENUM.name = "MSG_GLOBAL_ACTVCOUNT_ADD"
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_ACTVCOUNT_ADD_ENUM.index = 53
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_ACTVCOUNT_ADD_ENUM.number = 90
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_WILDSCENE_BOSSINFO_ENUM.name = "MSG_GLOBAL_WILDSCENE_BOSSINFO"
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_WILDSCENE_BOSSINFO_ENUM.index = 54
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_WILDSCENE_BOSSINFO_ENUM.number = 91
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_SURVIVALCHALLENGE_ENTER_ENUM.name = "MSG_GLOBAL_SURVIVALCHALLENGE_ENTER"
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_SURVIVALCHALLENGE_ENTER_ENUM.index = 55
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_SURVIVALCHALLENGE_ENTER_ENUM.number = 92
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_SURVIVALCHALLENGE_LEAVE_ENUM.name = "MSG_GLOBAL_SURVIVALCHALLENGE_LEAVE"
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_SURVIVALCHALLENGE_LEAVE_ENUM.index = 56
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_SURVIVALCHALLENGE_LEAVE_ENUM.number = 93
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_SURVIVALCHALLENGE_PLAYERLOCATION_ENUM.name = "MSG_GLOBAL_SURVIVALCHALLENGE_PLAYERLOCATION"
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_SURVIVALCHALLENGE_PLAYERLOCATION_ENUM.index = 57
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_SURVIVALCHALLENGE_PLAYERLOCATION_ENUM.number = 94
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_SURVIVALCHALLENGE_BATTLEFIELDINFO_ENUM.name = "MSG_GLOBAL_SURVIVALCHALLENGE_BATTLEFIELDINFO"
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_SURVIVALCHALLENGE_BATTLEFIELDINFO_ENUM.index = 58
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_SURVIVALCHALLENGE_BATTLEFIELDINFO_ENUM.number = 95
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBALGAME_ROBOTSWITCHZONE_ENUM.name = "MSG_GLOBALGAME_ROBOTSWITCHZONE"
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBALGAME_ROBOTSWITCHZONE_ENUM.index = 59
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBALGAME_ROBOTSWITCHZONE_ENUM.number = 100
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBALGAME_ROBOTSWITCHZONELOGIN_ENUM.name = "MSG_GLOBALGAME_ROBOTSWITCHZONELOGIN"
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBALGAME_ROBOTSWITCHZONELOGIN_ENUM.index = 60
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBALGAME_ROBOTSWITCHZONELOGIN_ENUM.number = 101
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_TRAININGGROUND_GETLIST_ENUM.name = "MSG_GLOBAL_TRAININGGROUND_GETLIST"
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_TRAININGGROUND_GETLIST_ENUM.index = 61
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_TRAININGGROUND_GETLIST_ENUM.number = 110
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_TRAININGGROUND_BEGINFIGHT_ENUM.name = "MSG_GLOBAL_TRAININGGROUND_BEGINFIGHT"
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_TRAININGGROUND_BEGINFIGHT_ENUM.index = 62
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_TRAININGGROUND_BEGINFIGHT_ENUM.number = 111
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_TRAININGGROUND_REFRESHLV_ENUM.name = "MSG_GLOBAL_TRAININGGROUND_REFRESHLV"
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_TRAININGGROUND_REFRESHLV_ENUM.index = 63
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_TRAININGGROUND_REFRESHLV_ENUM.number = 112
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_TRAININGGROUND_EXPLOITMINE_ENUM.name = "MSG_GLOBAL_TRAININGGROUND_EXPLOITMINE"
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_TRAININGGROUND_EXPLOITMINE_ENUM.index = 64
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_TRAININGGROUND_EXPLOITMINE_ENUM.number = 113
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_TRAININGGROUND_REFRESHMINLIST_ENUM.name = "MSG_GLOBAL_TRAININGGROUND_REFRESHMINLIST"
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_TRAININGGROUND_REFRESHMINLIST_ENUM.index = 65
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_TRAININGGROUND_REFRESHMINLIST_ENUM.number = 114
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_TRAININGGROUND_PRIZE_ENUM.name = "MSG_GLOBAL_TRAININGGROUND_PRIZE"
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_TRAININGGROUND_PRIZE_ENUM.index = 66
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_TRAININGGROUND_PRIZE_ENUM.number = 115
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_TRAININGGROUND_CACHEPLAYERSIM_ENUM.name = "MSG_GLOBAL_TRAININGGROUND_CACHEPLAYERSIM"
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_TRAININGGROUND_CACHEPLAYERSIM_ENUM.index = 67
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_TRAININGGROUND_CACHEPLAYERSIM_ENUM.number = 116
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_TRAININGGROUND_ENTER_ENUM.name = "MSG_GLOBAL_TRAININGGROUND_ENTER"
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_TRAININGGROUND_ENTER_ENUM.index = 68
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_TRAININGGROUND_ENTER_ENUM.number = 117
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_TRAININGGROUND_PLUNDERLOG_ENUM.name = "MSG_GLOBAL_TRAININGGROUND_PLUNDERLOG"
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_TRAININGGROUND_PLUNDERLOG_ENUM.index = 69
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_TRAININGGROUND_PLUNDERLOG_ENUM.number = 118
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_TRAININGGROUND_AWARD_ENUM.name = "MSG_GLOBAL_TRAININGGROUND_AWARD"
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_TRAININGGROUND_AWARD_ENUM.index = 70
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_TRAININGGROUND_AWARD_ENUM.number = 119
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_LANDCONQUEST_ENTER_ENUM.name = "MSG_GLOBAL_LANDCONQUEST_ENTER"
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_LANDCONQUEST_ENTER_ENUM.index = 71
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_LANDCONQUEST_ENTER_ENUM.number = 120
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_MARKET_TRADERECORDE_ENUM.name = "MSG_GLOBAL_MARKET_TRADERECORDE"
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_MARKET_TRADERECORDE_ENUM.index = 72
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_MARKET_TRADERECORDE_ENUM.number = 121
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_LANDCONQUEST_GETBOSSINFO_ENUM.name = "MSG_GLOBAL_LANDCONQUEST_GETBOSSINFO"
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_LANDCONQUEST_GETBOSSINFO_ENUM.index = 73
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_LANDCONQUEST_GETBOSSINFO_ENUM.number = 122
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_LANDCONQUEST_WINNER_ENUM.name = "MSG_GLOBAL_LANDCONQUEST_WINNER"
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_LANDCONQUEST_WINNER_ENUM.index = 74
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_LANDCONQUEST_WINNER_ENUM.number = 123
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_BARBARIAN_INVASION_UPDATE_SCORE_ENUM.name = "MSG_GLOBAL_BARBARIAN_INVASION_UPDATE_SCORE"
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_BARBARIAN_INVASION_UPDATE_SCORE_ENUM.index = 75
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_BARBARIAN_INVASION_UPDATE_SCORE_ENUM.number = 124
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_BARBARIAN_INVASION_RANKINFO_ENUM.name = "MSG_GLOBAL_BARBARIAN_INVASION_RANKINFO"
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_BARBARIAN_INVASION_RANKINFO_ENUM.index = 76
pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_BARBARIAN_INVASION_RANKINFO_ENUM.number = 125
pb.MSG_GLOBALGAME_ACTIONID.name = "MSG_GLOBALGAME_ACTIONID"
pb.MSG_GLOBALGAME_ACTIONID.full_name = ".MSG_GLOBALGAME_ACTIONID"
pb.MSG_GLOBALGAME_ACTIONID.values = {pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBALGAME_NONE_ENUM,pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBALGAME_SETACTORDATA_ENUM,pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBALGAME_UPDATEACTORDATA_ENUM,pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBALGAME_SYNCACTOREVENT_ENUM,pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBALGAME_UPDATESERVERBASEDATA_ENUM,pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBALGAME_ACTORLUAREQUEST_ENUM,pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBALGAME_FLUSHACTORMAIL_ENUM,pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBALGAME_TRANSMESSAGE_ENUM,pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBALGAME_LEAVEGLOBALSERVER_ENUM,pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_PLAYROOM_CREATE_ENUM,pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_PLAYROOM_GETLIST_ENUM,pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_PLAYROOM_JOIN_ENUM,pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_PLAYROOM_QUIT_ENUM,pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_PLAYROOM_KICK_ENUM,pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_PLAYROOM_PREPARE_ENUM,pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_PLAYROOM_STARTPLAY_ENUM,pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_PLAYROOM_UPDATEROOMLISTINFO_ENUM,pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_PLAYROOM_UPDATEROOMINFO_ENUM,pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_PLAYROOM_INVITEOTHER_ENUM,pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_PLAYROOM_DELETE_ENUM,pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_PLAYROOM_REENTERECTYPE_ENUM,pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_PLAYROOM_ROOMEVENT_ENUM,pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_PLAYROOM_JOINTARGET_ENUM,pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_WILDSCENE_ENTER_ENUM,pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_WILDSCENE_LEAVE_ENUM,pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_ACTVECTYPE_ENTER_ENUM,pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_ACTVECTYPE_LEAVE_ENUM,pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_ACTV_RANK_VALUE_CHANGE_ENUM,pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_ACTV_RANK_LIST_GET_ENUM,pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_ACTV_RANK_VALUE_REPLACE_ENUM,pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_ACTVRECORD_UPDATEVALUE_ENUM,pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_ACTVRECORD_GETLIST_ENUM,pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_SOLOGAME_GETWINCOUNT_ENUM,pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_GAMEECTYPE_ENTER_ENUM,pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_GAMEECTYPE_LEAVE_ENUM,pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_GAMEECTYPE_RESULTDATA_ENUM,pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_STOPACTV_ENUM,pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBALGAME_SETCOUNTRYDATA_ENUM,pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBALGAME_SETSOCIETYDATA_ENUM,pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBALGAME_UPDATESOCIETYMEMBER_ENUM,pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBALGAME_SETWARBANDDATA_ENUM,pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBALGAME_UPDATEWARBANDMEMBER_ENUM,pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_MARKET_GETLIST_ENUM,pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_MARKET_GETSELF_ENUM,pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_MARKET_ADDSELLITEM_ENUM,pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_MARKET_ADDSELLITEMCHECK_ENUM,pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_MARKET_ADDSELLITEMCHECKRESULT_ENUM,pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_MARKET_REMOVESELLITEM_ENUM,pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_MARKET_REMOVESELLITEMRESULT_ENUM,pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_MARKET_BUYSELLITEM_ENUM,pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_MARKET_BUYSELLITEMCHECK_ENUM,pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_MARKET_BUYSELLITEMCHECKRESULT_ENUM,pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_CHAT_TRANS_ENUM,pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_ACTVCOUNT_ADD_ENUM,pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_WILDSCENE_BOSSINFO_ENUM,pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_SURVIVALCHALLENGE_ENTER_ENUM,pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_SURVIVALCHALLENGE_LEAVE_ENUM,pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_SURVIVALCHALLENGE_PLAYERLOCATION_ENUM,pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_SURVIVALCHALLENGE_BATTLEFIELDINFO_ENUM,pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBALGAME_ROBOTSWITCHZONE_ENUM,pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBALGAME_ROBOTSWITCHZONELOGIN_ENUM,pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_TRAININGGROUND_GETLIST_ENUM,pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_TRAININGGROUND_BEGINFIGHT_ENUM,pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_TRAININGGROUND_REFRESHLV_ENUM,pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_TRAININGGROUND_EXPLOITMINE_ENUM,pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_TRAININGGROUND_REFRESHMINLIST_ENUM,pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_TRAININGGROUND_PRIZE_ENUM,pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_TRAININGGROUND_CACHEPLAYERSIM_ENUM,pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_TRAININGGROUND_ENTER_ENUM,pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_TRAININGGROUND_PLUNDERLOG_ENUM,pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_TRAININGGROUND_AWARD_ENUM,pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_LANDCONQUEST_ENTER_ENUM,pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_MARKET_TRADERECORDE_ENUM,pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_LANDCONQUEST_GETBOSSINFO_ENUM,pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_LANDCONQUEST_WINNER_ENUM,pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_BARBARIAN_INVASION_UPDATE_SCORE_ENUM,pb.MSG_GLOBALGAME_ACTIONID_MSG_GLOBAL_BARBARIAN_INVASION_RANKINFO_ENUM}
pb.SS_GLOBALGAME_SETACTORDATA_ACTORID_FIELD.name = "ActorID"
pb.SS_GLOBALGAME_SETACTORDATA_ACTORID_FIELD.full_name = ".SS_GlobalGame_SetActorData.ActorID"
pb.SS_GLOBALGAME_SETACTORDATA_ACTORID_FIELD.number = 1
pb.SS_GLOBALGAME_SETACTORDATA_ACTORID_FIELD.index = 0
pb.SS_GLOBALGAME_SETACTORDATA_ACTORID_FIELD.label = 1
pb.SS_GLOBALGAME_SETACTORDATA_ACTORID_FIELD.has_default_value = false
pb.SS_GLOBALGAME_SETACTORDATA_ACTORID_FIELD.default_value = 0
pb.SS_GLOBALGAME_SETACTORDATA_ACTORID_FIELD.type = 5
pb.SS_GLOBALGAME_SETACTORDATA_ACTORID_FIELD.cpp_type = 1

pb.SS_GLOBALGAME_SETACTORDATA_AGENTID_FIELD.name = "AgentID"
pb.SS_GLOBALGAME_SETACTORDATA_AGENTID_FIELD.full_name = ".SS_GlobalGame_SetActorData.AgentID"
pb.SS_GLOBALGAME_SETACTORDATA_AGENTID_FIELD.number = 2
pb.SS_GLOBALGAME_SETACTORDATA_AGENTID_FIELD.index = 1
pb.SS_GLOBALGAME_SETACTORDATA_AGENTID_FIELD.label = 1
pb.SS_GLOBALGAME_SETACTORDATA_AGENTID_FIELD.has_default_value = false
pb.SS_GLOBALGAME_SETACTORDATA_AGENTID_FIELD.default_value = 0
pb.SS_GLOBALGAME_SETACTORDATA_AGENTID_FIELD.type = 5
pb.SS_GLOBALGAME_SETACTORDATA_AGENTID_FIELD.cpp_type = 1

pb.SS_GLOBALGAME_SETACTORDATA_CLIENTID_FIELD.name = "ClientID"
pb.SS_GLOBALGAME_SETACTORDATA_CLIENTID_FIELD.full_name = ".SS_GlobalGame_SetActorData.ClientID"
pb.SS_GLOBALGAME_SETACTORDATA_CLIENTID_FIELD.number = 3
pb.SS_GLOBALGAME_SETACTORDATA_CLIENTID_FIELD.index = 2
pb.SS_GLOBALGAME_SETACTORDATA_CLIENTID_FIELD.label = 1
pb.SS_GLOBALGAME_SETACTORDATA_CLIENTID_FIELD.has_default_value = false
pb.SS_GLOBALGAME_SETACTORDATA_CLIENTID_FIELD.default_value = 0
pb.SS_GLOBALGAME_SETACTORDATA_CLIENTID_FIELD.type = 5
pb.SS_GLOBALGAME_SETACTORDATA_CLIENTID_FIELD.cpp_type = 1

pb.SS_GLOBALGAME_SETACTORDATA_ZONEID_FIELD.name = "ZoneID"
pb.SS_GLOBALGAME_SETACTORDATA_ZONEID_FIELD.full_name = ".SS_GlobalGame_SetActorData.ZoneID"
pb.SS_GLOBALGAME_SETACTORDATA_ZONEID_FIELD.number = 4
pb.SS_GLOBALGAME_SETACTORDATA_ZONEID_FIELD.index = 3
pb.SS_GLOBALGAME_SETACTORDATA_ZONEID_FIELD.label = 1
pb.SS_GLOBALGAME_SETACTORDATA_ZONEID_FIELD.has_default_value = false
pb.SS_GLOBALGAME_SETACTORDATA_ZONEID_FIELD.default_value = 0
pb.SS_GLOBALGAME_SETACTORDATA_ZONEID_FIELD.type = 5
pb.SS_GLOBALGAME_SETACTORDATA_ZONEID_FIELD.cpp_type = 1

pb.SS_GLOBALGAME_SETACTORDATA_ACTORNAME_FIELD.name = "ActorName"
pb.SS_GLOBALGAME_SETACTORDATA_ACTORNAME_FIELD.full_name = ".SS_GlobalGame_SetActorData.ActorName"
pb.SS_GLOBALGAME_SETACTORDATA_ACTORNAME_FIELD.number = 5
pb.SS_GLOBALGAME_SETACTORDATA_ACTORNAME_FIELD.index = 4
pb.SS_GLOBALGAME_SETACTORDATA_ACTORNAME_FIELD.label = 1
pb.SS_GLOBALGAME_SETACTORDATA_ACTORNAME_FIELD.has_default_value = false
pb.SS_GLOBALGAME_SETACTORDATA_ACTORNAME_FIELD.default_value = ""
pb.SS_GLOBALGAME_SETACTORDATA_ACTORNAME_FIELD.type = 9
pb.SS_GLOBALGAME_SETACTORDATA_ACTORNAME_FIELD.cpp_type = 9

pb.SS_GLOBALGAME_SETACTORDATA_ISONLINE_FIELD.name = "IsOnline"
pb.SS_GLOBALGAME_SETACTORDATA_ISONLINE_FIELD.full_name = ".SS_GlobalGame_SetActorData.IsOnline"
pb.SS_GLOBALGAME_SETACTORDATA_ISONLINE_FIELD.number = 6
pb.SS_GLOBALGAME_SETACTORDATA_ISONLINE_FIELD.index = 5
pb.SS_GLOBALGAME_SETACTORDATA_ISONLINE_FIELD.label = 1
pb.SS_GLOBALGAME_SETACTORDATA_ISONLINE_FIELD.has_default_value = false
pb.SS_GLOBALGAME_SETACTORDATA_ISONLINE_FIELD.default_value = 0
pb.SS_GLOBALGAME_SETACTORDATA_ISONLINE_FIELD.type = 5
pb.SS_GLOBALGAME_SETACTORDATA_ISONLINE_FIELD.cpp_type = 1

pb.SS_GLOBALGAME_SETACTORDATA_LEVEL_FIELD.name = "Level"
pb.SS_GLOBALGAME_SETACTORDATA_LEVEL_FIELD.full_name = ".SS_GlobalGame_SetActorData.Level"
pb.SS_GLOBALGAME_SETACTORDATA_LEVEL_FIELD.number = 7
pb.SS_GLOBALGAME_SETACTORDATA_LEVEL_FIELD.index = 6
pb.SS_GLOBALGAME_SETACTORDATA_LEVEL_FIELD.label = 1
pb.SS_GLOBALGAME_SETACTORDATA_LEVEL_FIELD.has_default_value = false
pb.SS_GLOBALGAME_SETACTORDATA_LEVEL_FIELD.default_value = 0
pb.SS_GLOBALGAME_SETACTORDATA_LEVEL_FIELD.type = 5
pb.SS_GLOBALGAME_SETACTORDATA_LEVEL_FIELD.cpp_type = 1

pb.SS_GLOBALGAME_SETACTORDATA_VOCATION_FIELD.name = "Vocation"
pb.SS_GLOBALGAME_SETACTORDATA_VOCATION_FIELD.full_name = ".SS_GlobalGame_SetActorData.Vocation"
pb.SS_GLOBALGAME_SETACTORDATA_VOCATION_FIELD.number = 8
pb.SS_GLOBALGAME_SETACTORDATA_VOCATION_FIELD.index = 7
pb.SS_GLOBALGAME_SETACTORDATA_VOCATION_FIELD.label = 1
pb.SS_GLOBALGAME_SETACTORDATA_VOCATION_FIELD.has_default_value = false
pb.SS_GLOBALGAME_SETACTORDATA_VOCATION_FIELD.default_value = 0
pb.SS_GLOBALGAME_SETACTORDATA_VOCATION_FIELD.type = 5
pb.SS_GLOBALGAME_SETACTORDATA_VOCATION_FIELD.cpp_type = 1

pb.SS_GLOBALGAME_SETACTORDATA_VIPLEVEL_FIELD.name = "VipLevel"
pb.SS_GLOBALGAME_SETACTORDATA_VIPLEVEL_FIELD.full_name = ".SS_GlobalGame_SetActorData.VipLevel"
pb.SS_GLOBALGAME_SETACTORDATA_VIPLEVEL_FIELD.number = 9
pb.SS_GLOBALGAME_SETACTORDATA_VIPLEVEL_FIELD.index = 8
pb.SS_GLOBALGAME_SETACTORDATA_VIPLEVEL_FIELD.label = 1
pb.SS_GLOBALGAME_SETACTORDATA_VIPLEVEL_FIELD.has_default_value = false
pb.SS_GLOBALGAME_SETACTORDATA_VIPLEVEL_FIELD.default_value = 0
pb.SS_GLOBALGAME_SETACTORDATA_VIPLEVEL_FIELD.type = 5
pb.SS_GLOBALGAME_SETACTORDATA_VIPLEVEL_FIELD.cpp_type = 1

pb.SS_GLOBALGAME_SETACTORDATA_POWER_FIELD.name = "Power"
pb.SS_GLOBALGAME_SETACTORDATA_POWER_FIELD.full_name = ".SS_GlobalGame_SetActorData.Power"
pb.SS_GLOBALGAME_SETACTORDATA_POWER_FIELD.number = 10
pb.SS_GLOBALGAME_SETACTORDATA_POWER_FIELD.index = 9
pb.SS_GLOBALGAME_SETACTORDATA_POWER_FIELD.label = 1
pb.SS_GLOBALGAME_SETACTORDATA_POWER_FIELD.has_default_value = false
pb.SS_GLOBALGAME_SETACTORDATA_POWER_FIELD.default_value = 0
pb.SS_GLOBALGAME_SETACTORDATA_POWER_FIELD.type = 5
pb.SS_GLOBALGAME_SETACTORDATA_POWER_FIELD.cpp_type = 1

pb.SS_GLOBALGAME_SETACTORDATA_ROBOTTYPE_FIELD.name = "RobotType"
pb.SS_GLOBALGAME_SETACTORDATA_ROBOTTYPE_FIELD.full_name = ".SS_GlobalGame_SetActorData.RobotType"
pb.SS_GLOBALGAME_SETACTORDATA_ROBOTTYPE_FIELD.number = 11
pb.SS_GLOBALGAME_SETACTORDATA_ROBOTTYPE_FIELD.index = 10
pb.SS_GLOBALGAME_SETACTORDATA_ROBOTTYPE_FIELD.label = 1
pb.SS_GLOBALGAME_SETACTORDATA_ROBOTTYPE_FIELD.has_default_value = false
pb.SS_GLOBALGAME_SETACTORDATA_ROBOTTYPE_FIELD.default_value = 0
pb.SS_GLOBALGAME_SETACTORDATA_ROBOTTYPE_FIELD.type = 5
pb.SS_GLOBALGAME_SETACTORDATA_ROBOTTYPE_FIELD.cpp_type = 1

pb.SS_GLOBALGAME_SETACTORDATA.name = "SS_GlobalGame_SetActorData"
pb.SS_GLOBALGAME_SETACTORDATA.full_name = ".SS_GlobalGame_SetActorData"
pb.SS_GLOBALGAME_SETACTORDATA.nested_types = {}
pb.SS_GLOBALGAME_SETACTORDATA.enum_types = {}
pb.SS_GLOBALGAME_SETACTORDATA.fields = {pb.SS_GLOBALGAME_SETACTORDATA_ACTORID_FIELD, pb.SS_GLOBALGAME_SETACTORDATA_AGENTID_FIELD, pb.SS_GLOBALGAME_SETACTORDATA_CLIENTID_FIELD, pb.SS_GLOBALGAME_SETACTORDATA_ZONEID_FIELD, pb.SS_GLOBALGAME_SETACTORDATA_ACTORNAME_FIELD, pb.SS_GLOBALGAME_SETACTORDATA_ISONLINE_FIELD, pb.SS_GLOBALGAME_SETACTORDATA_LEVEL_FIELD, pb.SS_GLOBALGAME_SETACTORDATA_VOCATION_FIELD, pb.SS_GLOBALGAME_SETACTORDATA_VIPLEVEL_FIELD, pb.SS_GLOBALGAME_SETACTORDATA_POWER_FIELD, pb.SS_GLOBALGAME_SETACTORDATA_ROBOTTYPE_FIELD}
pb.SS_GLOBALGAME_SETACTORDATA.is_extendable = false
pb.SS_GLOBALGAME_SETACTORDATA.extensions = {}
pb.SS_GLOBALGAME_UPDATEACTORDATA_ACTORID_FIELD.name = "ActorID"
pb.SS_GLOBALGAME_UPDATEACTORDATA_ACTORID_FIELD.full_name = ".SS_GlobalGame_UpdateActorData.ActorID"
pb.SS_GLOBALGAME_UPDATEACTORDATA_ACTORID_FIELD.number = 1
pb.SS_GLOBALGAME_UPDATEACTORDATA_ACTORID_FIELD.index = 0
pb.SS_GLOBALGAME_UPDATEACTORDATA_ACTORID_FIELD.label = 1
pb.SS_GLOBALGAME_UPDATEACTORDATA_ACTORID_FIELD.has_default_value = false
pb.SS_GLOBALGAME_UPDATEACTORDATA_ACTORID_FIELD.default_value = 0
pb.SS_GLOBALGAME_UPDATEACTORDATA_ACTORID_FIELD.type = 5
pb.SS_GLOBALGAME_UPDATEACTORDATA_ACTORID_FIELD.cpp_type = 1

pb.SS_GLOBALGAME_UPDATEACTORDATA_PROPTYPE_FIELD.name = "PropType"
pb.SS_GLOBALGAME_UPDATEACTORDATA_PROPTYPE_FIELD.full_name = ".SS_GlobalGame_UpdateActorData.PropType"
pb.SS_GLOBALGAME_UPDATEACTORDATA_PROPTYPE_FIELD.number = 2
pb.SS_GLOBALGAME_UPDATEACTORDATA_PROPTYPE_FIELD.index = 1
pb.SS_GLOBALGAME_UPDATEACTORDATA_PROPTYPE_FIELD.label = 1
pb.SS_GLOBALGAME_UPDATEACTORDATA_PROPTYPE_FIELD.has_default_value = false
pb.SS_GLOBALGAME_UPDATEACTORDATA_PROPTYPE_FIELD.default_value = 0
pb.SS_GLOBALGAME_UPDATEACTORDATA_PROPTYPE_FIELD.type = 5
pb.SS_GLOBALGAME_UPDATEACTORDATA_PROPTYPE_FIELD.cpp_type = 1

pb.SS_GLOBALGAME_UPDATEACTORDATA_PROPVALUE_FIELD.name = "PropValue"
pb.SS_GLOBALGAME_UPDATEACTORDATA_PROPVALUE_FIELD.full_name = ".SS_GlobalGame_UpdateActorData.PropValue"
pb.SS_GLOBALGAME_UPDATEACTORDATA_PROPVALUE_FIELD.number = 3
pb.SS_GLOBALGAME_UPDATEACTORDATA_PROPVALUE_FIELD.index = 2
pb.SS_GLOBALGAME_UPDATEACTORDATA_PROPVALUE_FIELD.label = 1
pb.SS_GLOBALGAME_UPDATEACTORDATA_PROPVALUE_FIELD.has_default_value = false
pb.SS_GLOBALGAME_UPDATEACTORDATA_PROPVALUE_FIELD.default_value = 0
pb.SS_GLOBALGAME_UPDATEACTORDATA_PROPVALUE_FIELD.type = 5
pb.SS_GLOBALGAME_UPDATEACTORDATA_PROPVALUE_FIELD.cpp_type = 1

pb.SS_GLOBALGAME_UPDATEACTORDATA.name = "SS_GlobalGame_UpdateActorData"
pb.SS_GLOBALGAME_UPDATEACTORDATA.full_name = ".SS_GlobalGame_UpdateActorData"
pb.SS_GLOBALGAME_UPDATEACTORDATA.nested_types = {}
pb.SS_GLOBALGAME_UPDATEACTORDATA.enum_types = {}
pb.SS_GLOBALGAME_UPDATEACTORDATA.fields = {pb.SS_GLOBALGAME_UPDATEACTORDATA_ACTORID_FIELD, pb.SS_GLOBALGAME_UPDATEACTORDATA_PROPTYPE_FIELD, pb.SS_GLOBALGAME_UPDATEACTORDATA_PROPVALUE_FIELD}
pb.SS_GLOBALGAME_UPDATEACTORDATA.is_extendable = false
pb.SS_GLOBALGAME_UPDATEACTORDATA.extensions = {}
pb.SS_GLOBALGAME_SYNCACTOREVENT_ACTORID_FIELD.name = "ActorID"
pb.SS_GLOBALGAME_SYNCACTOREVENT_ACTORID_FIELD.full_name = ".SS_GlobalGame_SyncActorEvent.ActorID"
pb.SS_GLOBALGAME_SYNCACTOREVENT_ACTORID_FIELD.number = 1
pb.SS_GLOBALGAME_SYNCACTOREVENT_ACTORID_FIELD.index = 0
pb.SS_GLOBALGAME_SYNCACTOREVENT_ACTORID_FIELD.label = 1
pb.SS_GLOBALGAME_SYNCACTOREVENT_ACTORID_FIELD.has_default_value = false
pb.SS_GLOBALGAME_SYNCACTOREVENT_ACTORID_FIELD.default_value = 0
pb.SS_GLOBALGAME_SYNCACTOREVENT_ACTORID_FIELD.type = 5
pb.SS_GLOBALGAME_SYNCACTOREVENT_ACTORID_FIELD.cpp_type = 1

pb.SS_GLOBALGAME_SYNCACTOREVENT_EVENTTYPE_FIELD.name = "EventType"
pb.SS_GLOBALGAME_SYNCACTOREVENT_EVENTTYPE_FIELD.full_name = ".SS_GlobalGame_SyncActorEvent.EventType"
pb.SS_GLOBALGAME_SYNCACTOREVENT_EVENTTYPE_FIELD.number = 2
pb.SS_GLOBALGAME_SYNCACTOREVENT_EVENTTYPE_FIELD.index = 1
pb.SS_GLOBALGAME_SYNCACTOREVENT_EVENTTYPE_FIELD.label = 1
pb.SS_GLOBALGAME_SYNCACTOREVENT_EVENTTYPE_FIELD.has_default_value = false
pb.SS_GLOBALGAME_SYNCACTOREVENT_EVENTTYPE_FIELD.default_value = 0
pb.SS_GLOBALGAME_SYNCACTOREVENT_EVENTTYPE_FIELD.type = 5
pb.SS_GLOBALGAME_SYNCACTOREVENT_EVENTTYPE_FIELD.cpp_type = 1

pb.SS_GLOBALGAME_SYNCACTOREVENT_EVENTPARAM1_FIELD.name = "EventParam1"
pb.SS_GLOBALGAME_SYNCACTOREVENT_EVENTPARAM1_FIELD.full_name = ".SS_GlobalGame_SyncActorEvent.EventParam1"
pb.SS_GLOBALGAME_SYNCACTOREVENT_EVENTPARAM1_FIELD.number = 3
pb.SS_GLOBALGAME_SYNCACTOREVENT_EVENTPARAM1_FIELD.index = 2
pb.SS_GLOBALGAME_SYNCACTOREVENT_EVENTPARAM1_FIELD.label = 1
pb.SS_GLOBALGAME_SYNCACTOREVENT_EVENTPARAM1_FIELD.has_default_value = false
pb.SS_GLOBALGAME_SYNCACTOREVENT_EVENTPARAM1_FIELD.default_value = 0
pb.SS_GLOBALGAME_SYNCACTOREVENT_EVENTPARAM1_FIELD.type = 5
pb.SS_GLOBALGAME_SYNCACTOREVENT_EVENTPARAM1_FIELD.cpp_type = 1

pb.SS_GLOBALGAME_SYNCACTOREVENT_EVENTPARAM2_FIELD.name = "EventParam2"
pb.SS_GLOBALGAME_SYNCACTOREVENT_EVENTPARAM2_FIELD.full_name = ".SS_GlobalGame_SyncActorEvent.EventParam2"
pb.SS_GLOBALGAME_SYNCACTOREVENT_EVENTPARAM2_FIELD.number = 4
pb.SS_GLOBALGAME_SYNCACTOREVENT_EVENTPARAM2_FIELD.index = 3
pb.SS_GLOBALGAME_SYNCACTOREVENT_EVENTPARAM2_FIELD.label = 1
pb.SS_GLOBALGAME_SYNCACTOREVENT_EVENTPARAM2_FIELD.has_default_value = false
pb.SS_GLOBALGAME_SYNCACTOREVENT_EVENTPARAM2_FIELD.default_value = 0
pb.SS_GLOBALGAME_SYNCACTOREVENT_EVENTPARAM2_FIELD.type = 5
pb.SS_GLOBALGAME_SYNCACTOREVENT_EVENTPARAM2_FIELD.cpp_type = 1

pb.SS_GLOBALGAME_SYNCACTOREVENT.name = "SS_GlobalGame_SyncActorEvent"
pb.SS_GLOBALGAME_SYNCACTOREVENT.full_name = ".SS_GlobalGame_SyncActorEvent"
pb.SS_GLOBALGAME_SYNCACTOREVENT.nested_types = {}
pb.SS_GLOBALGAME_SYNCACTOREVENT.enum_types = {}
pb.SS_GLOBALGAME_SYNCACTOREVENT.fields = {pb.SS_GLOBALGAME_SYNCACTOREVENT_ACTORID_FIELD, pb.SS_GLOBALGAME_SYNCACTOREVENT_EVENTTYPE_FIELD, pb.SS_GLOBALGAME_SYNCACTOREVENT_EVENTPARAM1_FIELD, pb.SS_GLOBALGAME_SYNCACTOREVENT_EVENTPARAM2_FIELD}
pb.SS_GLOBALGAME_SYNCACTOREVENT.is_extendable = false
pb.SS_GLOBALGAME_SYNCACTOREVENT.extensions = {}
pb.SS_GLOBALGAME_UPDATESERVERBASEDATA_ZONEID_FIELD.name = "ZoneID"
pb.SS_GLOBALGAME_UPDATESERVERBASEDATA_ZONEID_FIELD.full_name = ".SS_GlobalGame_UpdateServerBaseData.ZoneID"
pb.SS_GLOBALGAME_UPDATESERVERBASEDATA_ZONEID_FIELD.number = 1
pb.SS_GLOBALGAME_UPDATESERVERBASEDATA_ZONEID_FIELD.index = 0
pb.SS_GLOBALGAME_UPDATESERVERBASEDATA_ZONEID_FIELD.label = 1
pb.SS_GLOBALGAME_UPDATESERVERBASEDATA_ZONEID_FIELD.has_default_value = false
pb.SS_GLOBALGAME_UPDATESERVERBASEDATA_ZONEID_FIELD.default_value = 0
pb.SS_GLOBALGAME_UPDATESERVERBASEDATA_ZONEID_FIELD.type = 5
pb.SS_GLOBALGAME_UPDATESERVERBASEDATA_ZONEID_FIELD.cpp_type = 1

pb.SS_GLOBALGAME_UPDATESERVERBASEDATA_OPENTIME_FIELD.name = "OpenTime"
pb.SS_GLOBALGAME_UPDATESERVERBASEDATA_OPENTIME_FIELD.full_name = ".SS_GlobalGame_UpdateServerBaseData.OpenTime"
pb.SS_GLOBALGAME_UPDATESERVERBASEDATA_OPENTIME_FIELD.number = 2
pb.SS_GLOBALGAME_UPDATESERVERBASEDATA_OPENTIME_FIELD.index = 1
pb.SS_GLOBALGAME_UPDATESERVERBASEDATA_OPENTIME_FIELD.label = 1
pb.SS_GLOBALGAME_UPDATESERVERBASEDATA_OPENTIME_FIELD.has_default_value = false
pb.SS_GLOBALGAME_UPDATESERVERBASEDATA_OPENTIME_FIELD.default_value = 0
pb.SS_GLOBALGAME_UPDATESERVERBASEDATA_OPENTIME_FIELD.type = 5
pb.SS_GLOBALGAME_UPDATESERVERBASEDATA_OPENTIME_FIELD.cpp_type = 1

pb.SS_GLOBALGAME_UPDATESERVERBASEDATA_MERGETIME_FIELD.name = "MergeTime"
pb.SS_GLOBALGAME_UPDATESERVERBASEDATA_MERGETIME_FIELD.full_name = ".SS_GlobalGame_UpdateServerBaseData.MergeTime"
pb.SS_GLOBALGAME_UPDATESERVERBASEDATA_MERGETIME_FIELD.number = 3
pb.SS_GLOBALGAME_UPDATESERVERBASEDATA_MERGETIME_FIELD.index = 2
pb.SS_GLOBALGAME_UPDATESERVERBASEDATA_MERGETIME_FIELD.label = 1
pb.SS_GLOBALGAME_UPDATESERVERBASEDATA_MERGETIME_FIELD.has_default_value = false
pb.SS_GLOBALGAME_UPDATESERVERBASEDATA_MERGETIME_FIELD.default_value = 0
pb.SS_GLOBALGAME_UPDATESERVERBASEDATA_MERGETIME_FIELD.type = 5
pb.SS_GLOBALGAME_UPDATESERVERBASEDATA_MERGETIME_FIELD.cpp_type = 1

pb.SS_GLOBALGAME_UPDATESERVERBASEDATA_RANKLV_FIELD.name = "RankLv"
pb.SS_GLOBALGAME_UPDATESERVERBASEDATA_RANKLV_FIELD.full_name = ".SS_GlobalGame_UpdateServerBaseData.RankLv"
pb.SS_GLOBALGAME_UPDATESERVERBASEDATA_RANKLV_FIELD.number = 4
pb.SS_GLOBALGAME_UPDATESERVERBASEDATA_RANKLV_FIELD.index = 3
pb.SS_GLOBALGAME_UPDATESERVERBASEDATA_RANKLV_FIELD.label = 1
pb.SS_GLOBALGAME_UPDATESERVERBASEDATA_RANKLV_FIELD.has_default_value = false
pb.SS_GLOBALGAME_UPDATESERVERBASEDATA_RANKLV_FIELD.default_value = 0
pb.SS_GLOBALGAME_UPDATESERVERBASEDATA_RANKLV_FIELD.type = 5
pb.SS_GLOBALGAME_UPDATESERVERBASEDATA_RANKLV_FIELD.cpp_type = 1

pb.SS_GLOBALGAME_UPDATESERVERBASEDATA.name = "SS_GlobalGame_UpdateServerBaseData"
pb.SS_GLOBALGAME_UPDATESERVERBASEDATA.full_name = ".SS_GlobalGame_UpdateServerBaseData"
pb.SS_GLOBALGAME_UPDATESERVERBASEDATA.nested_types = {}
pb.SS_GLOBALGAME_UPDATESERVERBASEDATA.enum_types = {}
pb.SS_GLOBALGAME_UPDATESERVERBASEDATA.fields = {pb.SS_GLOBALGAME_UPDATESERVERBASEDATA_ZONEID_FIELD, pb.SS_GLOBALGAME_UPDATESERVERBASEDATA_OPENTIME_FIELD, pb.SS_GLOBALGAME_UPDATESERVERBASEDATA_MERGETIME_FIELD, pb.SS_GLOBALGAME_UPDATESERVERBASEDATA_RANKLV_FIELD}
pb.SS_GLOBALGAME_UPDATESERVERBASEDATA.is_extendable = false
pb.SS_GLOBALGAME_UPDATESERVERBASEDATA.extensions = {}
pb.SS_GLOBALGAME_ACTORLUAREQUEST_ACTORID_FIELD.name = "ActorID"
pb.SS_GLOBALGAME_ACTORLUAREQUEST_ACTORID_FIELD.full_name = ".SS_GlobalGame_ActorLuaRequest.ActorID"
pb.SS_GLOBALGAME_ACTORLUAREQUEST_ACTORID_FIELD.number = 1
pb.SS_GLOBALGAME_ACTORLUAREQUEST_ACTORID_FIELD.index = 0
pb.SS_GLOBALGAME_ACTORLUAREQUEST_ACTORID_FIELD.label = 1
pb.SS_GLOBALGAME_ACTORLUAREQUEST_ACTORID_FIELD.has_default_value = false
pb.SS_GLOBALGAME_ACTORLUAREQUEST_ACTORID_FIELD.default_value = 0
pb.SS_GLOBALGAME_ACTORLUAREQUEST_ACTORID_FIELD.type = 5
pb.SS_GLOBALGAME_ACTORLUAREQUEST_ACTORID_FIELD.cpp_type = 1

pb.SS_GLOBALGAME_ACTORLUAREQUEST_REQUEST_FIELD.name = "Request"
pb.SS_GLOBALGAME_ACTORLUAREQUEST_REQUEST_FIELD.full_name = ".SS_GlobalGame_ActorLuaRequest.Request"
pb.SS_GLOBALGAME_ACTORLUAREQUEST_REQUEST_FIELD.number = 2
pb.SS_GLOBALGAME_ACTORLUAREQUEST_REQUEST_FIELD.index = 1
pb.SS_GLOBALGAME_ACTORLUAREQUEST_REQUEST_FIELD.label = 1
pb.SS_GLOBALGAME_ACTORLUAREQUEST_REQUEST_FIELD.has_default_value = false
pb.SS_GLOBALGAME_ACTORLUAREQUEST_REQUEST_FIELD.default_value = ""
pb.SS_GLOBALGAME_ACTORLUAREQUEST_REQUEST_FIELD.type = 9
pb.SS_GLOBALGAME_ACTORLUAREQUEST_REQUEST_FIELD.cpp_type = 9

pb.SS_GLOBALGAME_ACTORLUAREQUEST_PARAM1_FIELD.name = "Param1"
pb.SS_GLOBALGAME_ACTORLUAREQUEST_PARAM1_FIELD.full_name = ".SS_GlobalGame_ActorLuaRequest.Param1"
pb.SS_GLOBALGAME_ACTORLUAREQUEST_PARAM1_FIELD.number = 3
pb.SS_GLOBALGAME_ACTORLUAREQUEST_PARAM1_FIELD.index = 2
pb.SS_GLOBALGAME_ACTORLUAREQUEST_PARAM1_FIELD.label = 1
pb.SS_GLOBALGAME_ACTORLUAREQUEST_PARAM1_FIELD.has_default_value = false
pb.SS_GLOBALGAME_ACTORLUAREQUEST_PARAM1_FIELD.default_value = 0
pb.SS_GLOBALGAME_ACTORLUAREQUEST_PARAM1_FIELD.type = 5
pb.SS_GLOBALGAME_ACTORLUAREQUEST_PARAM1_FIELD.cpp_type = 1

pb.SS_GLOBALGAME_ACTORLUAREQUEST_PARAM2_FIELD.name = "Param2"
pb.SS_GLOBALGAME_ACTORLUAREQUEST_PARAM2_FIELD.full_name = ".SS_GlobalGame_ActorLuaRequest.Param2"
pb.SS_GLOBALGAME_ACTORLUAREQUEST_PARAM2_FIELD.number = 4
pb.SS_GLOBALGAME_ACTORLUAREQUEST_PARAM2_FIELD.index = 3
pb.SS_GLOBALGAME_ACTORLUAREQUEST_PARAM2_FIELD.label = 1
pb.SS_GLOBALGAME_ACTORLUAREQUEST_PARAM2_FIELD.has_default_value = false
pb.SS_GLOBALGAME_ACTORLUAREQUEST_PARAM2_FIELD.default_value = 0
pb.SS_GLOBALGAME_ACTORLUAREQUEST_PARAM2_FIELD.type = 5
pb.SS_GLOBALGAME_ACTORLUAREQUEST_PARAM2_FIELD.cpp_type = 1

pb.SS_GLOBALGAME_ACTORLUAREQUEST.name = "SS_GlobalGame_ActorLuaRequest"
pb.SS_GLOBALGAME_ACTORLUAREQUEST.full_name = ".SS_GlobalGame_ActorLuaRequest"
pb.SS_GLOBALGAME_ACTORLUAREQUEST.nested_types = {}
pb.SS_GLOBALGAME_ACTORLUAREQUEST.enum_types = {}
pb.SS_GLOBALGAME_ACTORLUAREQUEST.fields = {pb.SS_GLOBALGAME_ACTORLUAREQUEST_ACTORID_FIELD, pb.SS_GLOBALGAME_ACTORLUAREQUEST_REQUEST_FIELD, pb.SS_GLOBALGAME_ACTORLUAREQUEST_PARAM1_FIELD, pb.SS_GLOBALGAME_ACTORLUAREQUEST_PARAM2_FIELD}
pb.SS_GLOBALGAME_ACTORLUAREQUEST.is_extendable = false
pb.SS_GLOBALGAME_ACTORLUAREQUEST.extensions = {}
pb.SS_GLOBALGAME_FLUSHACTORMAIL_ACTORID_FIELD.name = "ActorID"
pb.SS_GLOBALGAME_FLUSHACTORMAIL_ACTORID_FIELD.full_name = ".SS_GlobalGame_FlushActorMail.ActorID"
pb.SS_GLOBALGAME_FLUSHACTORMAIL_ACTORID_FIELD.number = 1
pb.SS_GLOBALGAME_FLUSHACTORMAIL_ACTORID_FIELD.index = 0
pb.SS_GLOBALGAME_FLUSHACTORMAIL_ACTORID_FIELD.label = 1
pb.SS_GLOBALGAME_FLUSHACTORMAIL_ACTORID_FIELD.has_default_value = false
pb.SS_GLOBALGAME_FLUSHACTORMAIL_ACTORID_FIELD.default_value = 0
pb.SS_GLOBALGAME_FLUSHACTORMAIL_ACTORID_FIELD.type = 5
pb.SS_GLOBALGAME_FLUSHACTORMAIL_ACTORID_FIELD.cpp_type = 1

pb.SS_GLOBALGAME_FLUSHACTORMAIL.name = "SS_GlobalGame_FlushActorMail"
pb.SS_GLOBALGAME_FLUSHACTORMAIL.full_name = ".SS_GlobalGame_FlushActorMail"
pb.SS_GLOBALGAME_FLUSHACTORMAIL.nested_types = {}
pb.SS_GLOBALGAME_FLUSHACTORMAIL.enum_types = {}
pb.SS_GLOBALGAME_FLUSHACTORMAIL.fields = {pb.SS_GLOBALGAME_FLUSHACTORMAIL_ACTORID_FIELD}
pb.SS_GLOBALGAME_FLUSHACTORMAIL.is_extendable = false
pb.SS_GLOBALGAME_FLUSHACTORMAIL.extensions = {}
pb.SS_GLOBALGAME_TRANSMESSAGE_ACTORID_FIELD.name = "ActorID"
pb.SS_GLOBALGAME_TRANSMESSAGE_ACTORID_FIELD.full_name = ".SS_GlobalGame_TransMessage.ActorID"
pb.SS_GLOBALGAME_TRANSMESSAGE_ACTORID_FIELD.number = 1
pb.SS_GLOBALGAME_TRANSMESSAGE_ACTORID_FIELD.index = 0
pb.SS_GLOBALGAME_TRANSMESSAGE_ACTORID_FIELD.label = 1
pb.SS_GLOBALGAME_TRANSMESSAGE_ACTORID_FIELD.has_default_value = false
pb.SS_GLOBALGAME_TRANSMESSAGE_ACTORID_FIELD.default_value = 0
pb.SS_GLOBALGAME_TRANSMESSAGE_ACTORID_FIELD.type = 5
pb.SS_GLOBALGAME_TRANSMESSAGE_ACTORID_FIELD.cpp_type = 1

pb.SS_GLOBALGAME_TRANSMESSAGE_ACTIONID_FIELD.name = "ActionID"
pb.SS_GLOBALGAME_TRANSMESSAGE_ACTIONID_FIELD.full_name = ".SS_GlobalGame_TransMessage.ActionID"
pb.SS_GLOBALGAME_TRANSMESSAGE_ACTIONID_FIELD.number = 2
pb.SS_GLOBALGAME_TRANSMESSAGE_ACTIONID_FIELD.index = 1
pb.SS_GLOBALGAME_TRANSMESSAGE_ACTIONID_FIELD.label = 1
pb.SS_GLOBALGAME_TRANSMESSAGE_ACTIONID_FIELD.has_default_value = false
pb.SS_GLOBALGAME_TRANSMESSAGE_ACTIONID_FIELD.default_value = 0
pb.SS_GLOBALGAME_TRANSMESSAGE_ACTIONID_FIELD.type = 5
pb.SS_GLOBALGAME_TRANSMESSAGE_ACTIONID_FIELD.cpp_type = 1

pb.SS_GLOBALGAME_TRANSMESSAGE_MESSAGEDATA_FIELD.name = "MessageData"
pb.SS_GLOBALGAME_TRANSMESSAGE_MESSAGEDATA_FIELD.full_name = ".SS_GlobalGame_TransMessage.MessageData"
pb.SS_GLOBALGAME_TRANSMESSAGE_MESSAGEDATA_FIELD.number = 3
pb.SS_GLOBALGAME_TRANSMESSAGE_MESSAGEDATA_FIELD.index = 2
pb.SS_GLOBALGAME_TRANSMESSAGE_MESSAGEDATA_FIELD.label = 1
pb.SS_GLOBALGAME_TRANSMESSAGE_MESSAGEDATA_FIELD.has_default_value = false
pb.SS_GLOBALGAME_TRANSMESSAGE_MESSAGEDATA_FIELD.default_value = ""
pb.SS_GLOBALGAME_TRANSMESSAGE_MESSAGEDATA_FIELD.type = 12
pb.SS_GLOBALGAME_TRANSMESSAGE_MESSAGEDATA_FIELD.cpp_type = 9

pb.SS_GLOBALGAME_TRANSMESSAGE.name = "SS_GlobalGame_TransMessage"
pb.SS_GLOBALGAME_TRANSMESSAGE.full_name = ".SS_GlobalGame_TransMessage"
pb.SS_GLOBALGAME_TRANSMESSAGE.nested_types = {}
pb.SS_GLOBALGAME_TRANSMESSAGE.enum_types = {}
pb.SS_GLOBALGAME_TRANSMESSAGE.fields = {pb.SS_GLOBALGAME_TRANSMESSAGE_ACTORID_FIELD, pb.SS_GLOBALGAME_TRANSMESSAGE_ACTIONID_FIELD, pb.SS_GLOBALGAME_TRANSMESSAGE_MESSAGEDATA_FIELD}
pb.SS_GLOBALGAME_TRANSMESSAGE.is_extendable = false
pb.SS_GLOBALGAME_TRANSMESSAGE.extensions = {}
pb.SS_GLOBAL_PLAYROOMCREATE_ACTORID_FIELD.name = "ActorID"
pb.SS_GLOBAL_PLAYROOMCREATE_ACTORID_FIELD.full_name = ".SS_Global_PlayRoomCreate.ActorID"
pb.SS_GLOBAL_PLAYROOMCREATE_ACTORID_FIELD.number = 1
pb.SS_GLOBAL_PLAYROOMCREATE_ACTORID_FIELD.index = 0
pb.SS_GLOBAL_PLAYROOMCREATE_ACTORID_FIELD.label = 1
pb.SS_GLOBAL_PLAYROOMCREATE_ACTORID_FIELD.has_default_value = false
pb.SS_GLOBAL_PLAYROOMCREATE_ACTORID_FIELD.default_value = 0
pb.SS_GLOBAL_PLAYROOMCREATE_ACTORID_FIELD.type = 5
pb.SS_GLOBAL_PLAYROOMCREATE_ACTORID_FIELD.cpp_type = 1

pb.SS_GLOBAL_PLAYROOMCREATE_ROOMTYPE_FIELD.name = "RoomType"
pb.SS_GLOBAL_PLAYROOMCREATE_ROOMTYPE_FIELD.full_name = ".SS_Global_PlayRoomCreate.RoomType"
pb.SS_GLOBAL_PLAYROOMCREATE_ROOMTYPE_FIELD.number = 2
pb.SS_GLOBAL_PLAYROOMCREATE_ROOMTYPE_FIELD.index = 1
pb.SS_GLOBAL_PLAYROOMCREATE_ROOMTYPE_FIELD.label = 1
pb.SS_GLOBAL_PLAYROOMCREATE_ROOMTYPE_FIELD.has_default_value = false
pb.SS_GLOBAL_PLAYROOMCREATE_ROOMTYPE_FIELD.default_value = 0
pb.SS_GLOBAL_PLAYROOMCREATE_ROOMTYPE_FIELD.type = 5
pb.SS_GLOBAL_PLAYROOMCREATE_ROOMTYPE_FIELD.cpp_type = 1

pb.SS_GLOBAL_PLAYROOMCREATE_ROOMNAME_FIELD.name = "RoomName"
pb.SS_GLOBAL_PLAYROOMCREATE_ROOMNAME_FIELD.full_name = ".SS_Global_PlayRoomCreate.RoomName"
pb.SS_GLOBAL_PLAYROOMCREATE_ROOMNAME_FIELD.number = 3
pb.SS_GLOBAL_PLAYROOMCREATE_ROOMNAME_FIELD.index = 2
pb.SS_GLOBAL_PLAYROOMCREATE_ROOMNAME_FIELD.label = 1
pb.SS_GLOBAL_PLAYROOMCREATE_ROOMNAME_FIELD.has_default_value = false
pb.SS_GLOBAL_PLAYROOMCREATE_ROOMNAME_FIELD.default_value = ""
pb.SS_GLOBAL_PLAYROOMCREATE_ROOMNAME_FIELD.type = 9
pb.SS_GLOBAL_PLAYROOMCREATE_ROOMNAME_FIELD.cpp_type = 9

pb.SS_GLOBAL_PLAYROOMCREATE_NEEDPOWER_FIELD.name = "NeedPower"
pb.SS_GLOBAL_PLAYROOMCREATE_NEEDPOWER_FIELD.full_name = ".SS_Global_PlayRoomCreate.NeedPower"
pb.SS_GLOBAL_PLAYROOMCREATE_NEEDPOWER_FIELD.number = 4
pb.SS_GLOBAL_PLAYROOMCREATE_NEEDPOWER_FIELD.index = 3
pb.SS_GLOBAL_PLAYROOMCREATE_NEEDPOWER_FIELD.label = 1
pb.SS_GLOBAL_PLAYROOMCREATE_NEEDPOWER_FIELD.has_default_value = false
pb.SS_GLOBAL_PLAYROOMCREATE_NEEDPOWER_FIELD.default_value = 0
pb.SS_GLOBAL_PLAYROOMCREATE_NEEDPOWER_FIELD.type = 5
pb.SS_GLOBAL_PLAYROOMCREATE_NEEDPOWER_FIELD.cpp_type = 1

pb.SS_GLOBAL_PLAYROOMCREATE_AUTOSTART_FIELD.name = "AutoStart"
pb.SS_GLOBAL_PLAYROOMCREATE_AUTOSTART_FIELD.full_name = ".SS_Global_PlayRoomCreate.AutoStart"
pb.SS_GLOBAL_PLAYROOMCREATE_AUTOSTART_FIELD.number = 5
pb.SS_GLOBAL_PLAYROOMCREATE_AUTOSTART_FIELD.index = 4
pb.SS_GLOBAL_PLAYROOMCREATE_AUTOSTART_FIELD.label = 1
pb.SS_GLOBAL_PLAYROOMCREATE_AUTOSTART_FIELD.has_default_value = false
pb.SS_GLOBAL_PLAYROOMCREATE_AUTOSTART_FIELD.default_value = 0
pb.SS_GLOBAL_PLAYROOMCREATE_AUTOSTART_FIELD.type = 5
pb.SS_GLOBAL_PLAYROOMCREATE_AUTOSTART_FIELD.cpp_type = 1

pb.SS_GLOBAL_PLAYROOMCREATE_PARAM1_FIELD.name = "Param1"
pb.SS_GLOBAL_PLAYROOMCREATE_PARAM1_FIELD.full_name = ".SS_Global_PlayRoomCreate.Param1"
pb.SS_GLOBAL_PLAYROOMCREATE_PARAM1_FIELD.number = 6
pb.SS_GLOBAL_PLAYROOMCREATE_PARAM1_FIELD.index = 5
pb.SS_GLOBAL_PLAYROOMCREATE_PARAM1_FIELD.label = 1
pb.SS_GLOBAL_PLAYROOMCREATE_PARAM1_FIELD.has_default_value = false
pb.SS_GLOBAL_PLAYROOMCREATE_PARAM1_FIELD.default_value = 0
pb.SS_GLOBAL_PLAYROOMCREATE_PARAM1_FIELD.type = 5
pb.SS_GLOBAL_PLAYROOMCREATE_PARAM1_FIELD.cpp_type = 1

pb.SS_GLOBAL_PLAYROOMCREATE.name = "SS_Global_PlayRoomCreate"
pb.SS_GLOBAL_PLAYROOMCREATE.full_name = ".SS_Global_PlayRoomCreate"
pb.SS_GLOBAL_PLAYROOMCREATE.nested_types = {}
pb.SS_GLOBAL_PLAYROOMCREATE.enum_types = {}
pb.SS_GLOBAL_PLAYROOMCREATE.fields = {pb.SS_GLOBAL_PLAYROOMCREATE_ACTORID_FIELD, pb.SS_GLOBAL_PLAYROOMCREATE_ROOMTYPE_FIELD, pb.SS_GLOBAL_PLAYROOMCREATE_ROOMNAME_FIELD, pb.SS_GLOBAL_PLAYROOMCREATE_NEEDPOWER_FIELD, pb.SS_GLOBAL_PLAYROOMCREATE_AUTOSTART_FIELD, pb.SS_GLOBAL_PLAYROOMCREATE_PARAM1_FIELD}
pb.SS_GLOBAL_PLAYROOMCREATE.is_extendable = false
pb.SS_GLOBAL_PLAYROOMCREATE.extensions = {}
pb.SS_GLOBAL_PLAYROOMGETLIST_ACTORID_FIELD.name = "ActorID"
pb.SS_GLOBAL_PLAYROOMGETLIST_ACTORID_FIELD.full_name = ".SS_Global_PlayRoomGetList.ActorID"
pb.SS_GLOBAL_PLAYROOMGETLIST_ACTORID_FIELD.number = 1
pb.SS_GLOBAL_PLAYROOMGETLIST_ACTORID_FIELD.index = 0
pb.SS_GLOBAL_PLAYROOMGETLIST_ACTORID_FIELD.label = 1
pb.SS_GLOBAL_PLAYROOMGETLIST_ACTORID_FIELD.has_default_value = false
pb.SS_GLOBAL_PLAYROOMGETLIST_ACTORID_FIELD.default_value = 0
pb.SS_GLOBAL_PLAYROOMGETLIST_ACTORID_FIELD.type = 5
pb.SS_GLOBAL_PLAYROOMGETLIST_ACTORID_FIELD.cpp_type = 1

pb.SS_GLOBAL_PLAYROOMGETLIST_ROOMTYPE_FIELD.name = "RoomType"
pb.SS_GLOBAL_PLAYROOMGETLIST_ROOMTYPE_FIELD.full_name = ".SS_Global_PlayRoomGetList.RoomType"
pb.SS_GLOBAL_PLAYROOMGETLIST_ROOMTYPE_FIELD.number = 2
pb.SS_GLOBAL_PLAYROOMGETLIST_ROOMTYPE_FIELD.index = 1
pb.SS_GLOBAL_PLAYROOMGETLIST_ROOMTYPE_FIELD.label = 1
pb.SS_GLOBAL_PLAYROOMGETLIST_ROOMTYPE_FIELD.has_default_value = false
pb.SS_GLOBAL_PLAYROOMGETLIST_ROOMTYPE_FIELD.default_value = 0
pb.SS_GLOBAL_PLAYROOMGETLIST_ROOMTYPE_FIELD.type = 5
pb.SS_GLOBAL_PLAYROOMGETLIST_ROOMTYPE_FIELD.cpp_type = 1

pb.SS_GLOBAL_PLAYROOMGETLIST_BEGINROOMID_FIELD.name = "BeginRoomID"
pb.SS_GLOBAL_PLAYROOMGETLIST_BEGINROOMID_FIELD.full_name = ".SS_Global_PlayRoomGetList.BeginRoomID"
pb.SS_GLOBAL_PLAYROOMGETLIST_BEGINROOMID_FIELD.number = 3
pb.SS_GLOBAL_PLAYROOMGETLIST_BEGINROOMID_FIELD.index = 2
pb.SS_GLOBAL_PLAYROOMGETLIST_BEGINROOMID_FIELD.label = 1
pb.SS_GLOBAL_PLAYROOMGETLIST_BEGINROOMID_FIELD.has_default_value = false
pb.SS_GLOBAL_PLAYROOMGETLIST_BEGINROOMID_FIELD.default_value = 0
pb.SS_GLOBAL_PLAYROOMGETLIST_BEGINROOMID_FIELD.type = 5
pb.SS_GLOBAL_PLAYROOMGETLIST_BEGINROOMID_FIELD.cpp_type = 1

pb.SS_GLOBAL_PLAYROOMGETLIST.name = "SS_Global_PlayRoomGetList"
pb.SS_GLOBAL_PLAYROOMGETLIST.full_name = ".SS_Global_PlayRoomGetList"
pb.SS_GLOBAL_PLAYROOMGETLIST.nested_types = {}
pb.SS_GLOBAL_PLAYROOMGETLIST.enum_types = {}
pb.SS_GLOBAL_PLAYROOMGETLIST.fields = {pb.SS_GLOBAL_PLAYROOMGETLIST_ACTORID_FIELD, pb.SS_GLOBAL_PLAYROOMGETLIST_ROOMTYPE_FIELD, pb.SS_GLOBAL_PLAYROOMGETLIST_BEGINROOMID_FIELD}
pb.SS_GLOBAL_PLAYROOMGETLIST.is_extendable = false
pb.SS_GLOBAL_PLAYROOMGETLIST.extensions = {}
pb.SS_GLOBAL_PLAYROOMJOIN_ACTORID_FIELD.name = "ActorID"
pb.SS_GLOBAL_PLAYROOMJOIN_ACTORID_FIELD.full_name = ".SS_Global_PlayRoomJoin.ActorID"
pb.SS_GLOBAL_PLAYROOMJOIN_ACTORID_FIELD.number = 1
pb.SS_GLOBAL_PLAYROOMJOIN_ACTORID_FIELD.index = 0
pb.SS_GLOBAL_PLAYROOMJOIN_ACTORID_FIELD.label = 1
pb.SS_GLOBAL_PLAYROOMJOIN_ACTORID_FIELD.has_default_value = false
pb.SS_GLOBAL_PLAYROOMJOIN_ACTORID_FIELD.default_value = 0
pb.SS_GLOBAL_PLAYROOMJOIN_ACTORID_FIELD.type = 5
pb.SS_GLOBAL_PLAYROOMJOIN_ACTORID_FIELD.cpp_type = 1

pb.SS_GLOBAL_PLAYROOMJOIN_ROOMTYPE_FIELD.name = "RoomType"
pb.SS_GLOBAL_PLAYROOMJOIN_ROOMTYPE_FIELD.full_name = ".SS_Global_PlayRoomJoin.RoomType"
pb.SS_GLOBAL_PLAYROOMJOIN_ROOMTYPE_FIELD.number = 2
pb.SS_GLOBAL_PLAYROOMJOIN_ROOMTYPE_FIELD.index = 1
pb.SS_GLOBAL_PLAYROOMJOIN_ROOMTYPE_FIELD.label = 1
pb.SS_GLOBAL_PLAYROOMJOIN_ROOMTYPE_FIELD.has_default_value = false
pb.SS_GLOBAL_PLAYROOMJOIN_ROOMTYPE_FIELD.default_value = 0
pb.SS_GLOBAL_PLAYROOMJOIN_ROOMTYPE_FIELD.type = 5
pb.SS_GLOBAL_PLAYROOMJOIN_ROOMTYPE_FIELD.cpp_type = 1

pb.SS_GLOBAL_PLAYROOMJOIN_ROOMID_FIELD.name = "RoomID"
pb.SS_GLOBAL_PLAYROOMJOIN_ROOMID_FIELD.full_name = ".SS_Global_PlayRoomJoin.RoomID"
pb.SS_GLOBAL_PLAYROOMJOIN_ROOMID_FIELD.number = 3
pb.SS_GLOBAL_PLAYROOMJOIN_ROOMID_FIELD.index = 2
pb.SS_GLOBAL_PLAYROOMJOIN_ROOMID_FIELD.label = 1
pb.SS_GLOBAL_PLAYROOMJOIN_ROOMID_FIELD.has_default_value = false
pb.SS_GLOBAL_PLAYROOMJOIN_ROOMID_FIELD.default_value = 0
pb.SS_GLOBAL_PLAYROOMJOIN_ROOMID_FIELD.type = 5
pb.SS_GLOBAL_PLAYROOMJOIN_ROOMID_FIELD.cpp_type = 1

pb.SS_GLOBAL_PLAYROOMJOIN_ROOMNAME_FIELD.name = "RoomName"
pb.SS_GLOBAL_PLAYROOMJOIN_ROOMNAME_FIELD.full_name = ".SS_Global_PlayRoomJoin.RoomName"
pb.SS_GLOBAL_PLAYROOMJOIN_ROOMNAME_FIELD.number = 4
pb.SS_GLOBAL_PLAYROOMJOIN_ROOMNAME_FIELD.index = 3
pb.SS_GLOBAL_PLAYROOMJOIN_ROOMNAME_FIELD.label = 1
pb.SS_GLOBAL_PLAYROOMJOIN_ROOMNAME_FIELD.has_default_value = false
pb.SS_GLOBAL_PLAYROOMJOIN_ROOMNAME_FIELD.default_value = ""
pb.SS_GLOBAL_PLAYROOMJOIN_ROOMNAME_FIELD.type = 9
pb.SS_GLOBAL_PLAYROOMJOIN_ROOMNAME_FIELD.cpp_type = 9

pb.SS_GLOBAL_PLAYROOMJOIN_NOTICEPREPARE_FIELD.name = "NoticePrepare"
pb.SS_GLOBAL_PLAYROOMJOIN_NOTICEPREPARE_FIELD.full_name = ".SS_Global_PlayRoomJoin.NoticePrepare"
pb.SS_GLOBAL_PLAYROOMJOIN_NOTICEPREPARE_FIELD.number = 5
pb.SS_GLOBAL_PLAYROOMJOIN_NOTICEPREPARE_FIELD.index = 4
pb.SS_GLOBAL_PLAYROOMJOIN_NOTICEPREPARE_FIELD.label = 1
pb.SS_GLOBAL_PLAYROOMJOIN_NOTICEPREPARE_FIELD.has_default_value = false
pb.SS_GLOBAL_PLAYROOMJOIN_NOTICEPREPARE_FIELD.default_value = 0
pb.SS_GLOBAL_PLAYROOMJOIN_NOTICEPREPARE_FIELD.type = 5
pb.SS_GLOBAL_PLAYROOMJOIN_NOTICEPREPARE_FIELD.cpp_type = 1

pb.SS_GLOBAL_PLAYROOMJOIN.name = "SS_Global_PlayRoomJoin"
pb.SS_GLOBAL_PLAYROOMJOIN.full_name = ".SS_Global_PlayRoomJoin"
pb.SS_GLOBAL_PLAYROOMJOIN.nested_types = {}
pb.SS_GLOBAL_PLAYROOMJOIN.enum_types = {}
pb.SS_GLOBAL_PLAYROOMJOIN.fields = {pb.SS_GLOBAL_PLAYROOMJOIN_ACTORID_FIELD, pb.SS_GLOBAL_PLAYROOMJOIN_ROOMTYPE_FIELD, pb.SS_GLOBAL_PLAYROOMJOIN_ROOMID_FIELD, pb.SS_GLOBAL_PLAYROOMJOIN_ROOMNAME_FIELD, pb.SS_GLOBAL_PLAYROOMJOIN_NOTICEPREPARE_FIELD}
pb.SS_GLOBAL_PLAYROOMJOIN.is_extendable = false
pb.SS_GLOBAL_PLAYROOMJOIN.extensions = {}
pb.SS_GLOBAL_PLAYROOMQUIT_ACTORID_FIELD.name = "ActorID"
pb.SS_GLOBAL_PLAYROOMQUIT_ACTORID_FIELD.full_name = ".SS_Global_PlayRoomQuit.ActorID"
pb.SS_GLOBAL_PLAYROOMQUIT_ACTORID_FIELD.number = 1
pb.SS_GLOBAL_PLAYROOMQUIT_ACTORID_FIELD.index = 0
pb.SS_GLOBAL_PLAYROOMQUIT_ACTORID_FIELD.label = 1
pb.SS_GLOBAL_PLAYROOMQUIT_ACTORID_FIELD.has_default_value = false
pb.SS_GLOBAL_PLAYROOMQUIT_ACTORID_FIELD.default_value = 0
pb.SS_GLOBAL_PLAYROOMQUIT_ACTORID_FIELD.type = 5
pb.SS_GLOBAL_PLAYROOMQUIT_ACTORID_FIELD.cpp_type = 1

pb.SS_GLOBAL_PLAYROOMQUIT_ROOMID_FIELD.name = "RoomID"
pb.SS_GLOBAL_PLAYROOMQUIT_ROOMID_FIELD.full_name = ".SS_Global_PlayRoomQuit.RoomID"
pb.SS_GLOBAL_PLAYROOMQUIT_ROOMID_FIELD.number = 2
pb.SS_GLOBAL_PLAYROOMQUIT_ROOMID_FIELD.index = 1
pb.SS_GLOBAL_PLAYROOMQUIT_ROOMID_FIELD.label = 1
pb.SS_GLOBAL_PLAYROOMQUIT_ROOMID_FIELD.has_default_value = false
pb.SS_GLOBAL_PLAYROOMQUIT_ROOMID_FIELD.default_value = 0
pb.SS_GLOBAL_PLAYROOMQUIT_ROOMID_FIELD.type = 5
pb.SS_GLOBAL_PLAYROOMQUIT_ROOMID_FIELD.cpp_type = 1

pb.SS_GLOBAL_PLAYROOMQUIT.name = "SS_Global_PlayRoomQuit"
pb.SS_GLOBAL_PLAYROOMQUIT.full_name = ".SS_Global_PlayRoomQuit"
pb.SS_GLOBAL_PLAYROOMQUIT.nested_types = {}
pb.SS_GLOBAL_PLAYROOMQUIT.enum_types = {}
pb.SS_GLOBAL_PLAYROOMQUIT.fields = {pb.SS_GLOBAL_PLAYROOMQUIT_ACTORID_FIELD, pb.SS_GLOBAL_PLAYROOMQUIT_ROOMID_FIELD}
pb.SS_GLOBAL_PLAYROOMQUIT.is_extendable = false
pb.SS_GLOBAL_PLAYROOMQUIT.extensions = {}
pb.SS_GLOBAL_PLAYROOMKICK_ACTORID_FIELD.name = "ActorID"
pb.SS_GLOBAL_PLAYROOMKICK_ACTORID_FIELD.full_name = ".SS_Global_PlayRoomKick.ActorID"
pb.SS_GLOBAL_PLAYROOMKICK_ACTORID_FIELD.number = 1
pb.SS_GLOBAL_PLAYROOMKICK_ACTORID_FIELD.index = 0
pb.SS_GLOBAL_PLAYROOMKICK_ACTORID_FIELD.label = 1
pb.SS_GLOBAL_PLAYROOMKICK_ACTORID_FIELD.has_default_value = false
pb.SS_GLOBAL_PLAYROOMKICK_ACTORID_FIELD.default_value = 0
pb.SS_GLOBAL_PLAYROOMKICK_ACTORID_FIELD.type = 5
pb.SS_GLOBAL_PLAYROOMKICK_ACTORID_FIELD.cpp_type = 1

pb.SS_GLOBAL_PLAYROOMKICK_TARACTORID_FIELD.name = "TarActorID"
pb.SS_GLOBAL_PLAYROOMKICK_TARACTORID_FIELD.full_name = ".SS_Global_PlayRoomKick.TarActorID"
pb.SS_GLOBAL_PLAYROOMKICK_TARACTORID_FIELD.number = 2
pb.SS_GLOBAL_PLAYROOMKICK_TARACTORID_FIELD.index = 1
pb.SS_GLOBAL_PLAYROOMKICK_TARACTORID_FIELD.label = 1
pb.SS_GLOBAL_PLAYROOMKICK_TARACTORID_FIELD.has_default_value = false
pb.SS_GLOBAL_PLAYROOMKICK_TARACTORID_FIELD.default_value = 0
pb.SS_GLOBAL_PLAYROOMKICK_TARACTORID_FIELD.type = 5
pb.SS_GLOBAL_PLAYROOMKICK_TARACTORID_FIELD.cpp_type = 1

pb.SS_GLOBAL_PLAYROOMKICK.name = "SS_Global_PlayRoomKick"
pb.SS_GLOBAL_PLAYROOMKICK.full_name = ".SS_Global_PlayRoomKick"
pb.SS_GLOBAL_PLAYROOMKICK.nested_types = {}
pb.SS_GLOBAL_PLAYROOMKICK.enum_types = {}
pb.SS_GLOBAL_PLAYROOMKICK.fields = {pb.SS_GLOBAL_PLAYROOMKICK_ACTORID_FIELD, pb.SS_GLOBAL_PLAYROOMKICK_TARACTORID_FIELD}
pb.SS_GLOBAL_PLAYROOMKICK.is_extendable = false
pb.SS_GLOBAL_PLAYROOMKICK.extensions = {}
pb.SS_GLOBAL_PLAYROOMPREPARE_ACTORID_FIELD.name = "ActorID"
pb.SS_GLOBAL_PLAYROOMPREPARE_ACTORID_FIELD.full_name = ".SS_Global_PlayRoomPrepare.ActorID"
pb.SS_GLOBAL_PLAYROOMPREPARE_ACTORID_FIELD.number = 1
pb.SS_GLOBAL_PLAYROOMPREPARE_ACTORID_FIELD.index = 0
pb.SS_GLOBAL_PLAYROOMPREPARE_ACTORID_FIELD.label = 1
pb.SS_GLOBAL_PLAYROOMPREPARE_ACTORID_FIELD.has_default_value = false
pb.SS_GLOBAL_PLAYROOMPREPARE_ACTORID_FIELD.default_value = 0
pb.SS_GLOBAL_PLAYROOMPREPARE_ACTORID_FIELD.type = 5
pb.SS_GLOBAL_PLAYROOMPREPARE_ACTORID_FIELD.cpp_type = 1

pb.SS_GLOBAL_PLAYROOMPREPARE_ISPREPARE_FIELD.name = "IsPrepare"
pb.SS_GLOBAL_PLAYROOMPREPARE_ISPREPARE_FIELD.full_name = ".SS_Global_PlayRoomPrepare.IsPrepare"
pb.SS_GLOBAL_PLAYROOMPREPARE_ISPREPARE_FIELD.number = 2
pb.SS_GLOBAL_PLAYROOMPREPARE_ISPREPARE_FIELD.index = 1
pb.SS_GLOBAL_PLAYROOMPREPARE_ISPREPARE_FIELD.label = 1
pb.SS_GLOBAL_PLAYROOMPREPARE_ISPREPARE_FIELD.has_default_value = false
pb.SS_GLOBAL_PLAYROOMPREPARE_ISPREPARE_FIELD.default_value = 0
pb.SS_GLOBAL_PLAYROOMPREPARE_ISPREPARE_FIELD.type = 5
pb.SS_GLOBAL_PLAYROOMPREPARE_ISPREPARE_FIELD.cpp_type = 1

pb.SS_GLOBAL_PLAYROOMPREPARE.name = "SS_Global_PlayRoomPrepare"
pb.SS_GLOBAL_PLAYROOMPREPARE.full_name = ".SS_Global_PlayRoomPrepare"
pb.SS_GLOBAL_PLAYROOMPREPARE.nested_types = {}
pb.SS_GLOBAL_PLAYROOMPREPARE.enum_types = {}
pb.SS_GLOBAL_PLAYROOMPREPARE.fields = {pb.SS_GLOBAL_PLAYROOMPREPARE_ACTORID_FIELD, pb.SS_GLOBAL_PLAYROOMPREPARE_ISPREPARE_FIELD}
pb.SS_GLOBAL_PLAYROOMPREPARE.is_extendable = false
pb.SS_GLOBAL_PLAYROOMPREPARE.extensions = {}
pb.SS_GLOBAL_PLAYROOMSTARTPLAY_ACTORID_FIELD.name = "ActorID"
pb.SS_GLOBAL_PLAYROOMSTARTPLAY_ACTORID_FIELD.full_name = ".SS_Global_PlayRoomStartPlay.ActorID"
pb.SS_GLOBAL_PLAYROOMSTARTPLAY_ACTORID_FIELD.number = 1
pb.SS_GLOBAL_PLAYROOMSTARTPLAY_ACTORID_FIELD.index = 0
pb.SS_GLOBAL_PLAYROOMSTARTPLAY_ACTORID_FIELD.label = 1
pb.SS_GLOBAL_PLAYROOMSTARTPLAY_ACTORID_FIELD.has_default_value = false
pb.SS_GLOBAL_PLAYROOMSTARTPLAY_ACTORID_FIELD.default_value = 0
pb.SS_GLOBAL_PLAYROOMSTARTPLAY_ACTORID_FIELD.type = 5
pb.SS_GLOBAL_PLAYROOMSTARTPLAY_ACTORID_FIELD.cpp_type = 1

pb.SS_GLOBAL_PLAYROOMSTARTPLAY.name = "SS_Global_PlayRoomStartPlay"
pb.SS_GLOBAL_PLAYROOMSTARTPLAY.full_name = ".SS_Global_PlayRoomStartPlay"
pb.SS_GLOBAL_PLAYROOMSTARTPLAY.nested_types = {}
pb.SS_GLOBAL_PLAYROOMSTARTPLAY.enum_types = {}
pb.SS_GLOBAL_PLAYROOMSTARTPLAY.fields = {pb.SS_GLOBAL_PLAYROOMSTARTPLAY_ACTORID_FIELD}
pb.SS_GLOBAL_PLAYROOMSTARTPLAY.is_extendable = false
pb.SS_GLOBAL_PLAYROOMSTARTPLAY.extensions = {}
pb.SS_GLOBAL_PLAYROOMUPDATEROOMLISTINFO_ACTORID_FIELD.name = "ActorID"
pb.SS_GLOBAL_PLAYROOMUPDATEROOMLISTINFO_ACTORID_FIELD.full_name = ".SS_Global_PlayRoomUpdateRoomListInfo.ActorID"
pb.SS_GLOBAL_PLAYROOMUPDATEROOMLISTINFO_ACTORID_FIELD.number = 1
pb.SS_GLOBAL_PLAYROOMUPDATEROOMLISTINFO_ACTORID_FIELD.index = 0
pb.SS_GLOBAL_PLAYROOMUPDATEROOMLISTINFO_ACTORID_FIELD.label = 1
pb.SS_GLOBAL_PLAYROOMUPDATEROOMLISTINFO_ACTORID_FIELD.has_default_value = false
pb.SS_GLOBAL_PLAYROOMUPDATEROOMLISTINFO_ACTORID_FIELD.default_value = 0
pb.SS_GLOBAL_PLAYROOMUPDATEROOMLISTINFO_ACTORID_FIELD.type = 5
pb.SS_GLOBAL_PLAYROOMUPDATEROOMLISTINFO_ACTORID_FIELD.cpp_type = 1

pb.SS_GLOBAL_PLAYROOMUPDATEROOMLISTINFO_STOPTRACE_FIELD.name = "StopTrace"
pb.SS_GLOBAL_PLAYROOMUPDATEROOMLISTINFO_STOPTRACE_FIELD.full_name = ".SS_Global_PlayRoomUpdateRoomListInfo.StopTrace"
pb.SS_GLOBAL_PLAYROOMUPDATEROOMLISTINFO_STOPTRACE_FIELD.number = 2
pb.SS_GLOBAL_PLAYROOMUPDATEROOMLISTINFO_STOPTRACE_FIELD.index = 1
pb.SS_GLOBAL_PLAYROOMUPDATEROOMLISTINFO_STOPTRACE_FIELD.label = 1
pb.SS_GLOBAL_PLAYROOMUPDATEROOMLISTINFO_STOPTRACE_FIELD.has_default_value = false
pb.SS_GLOBAL_PLAYROOMUPDATEROOMLISTINFO_STOPTRACE_FIELD.default_value = 0
pb.SS_GLOBAL_PLAYROOMUPDATEROOMLISTINFO_STOPTRACE_FIELD.type = 5
pb.SS_GLOBAL_PLAYROOMUPDATEROOMLISTINFO_STOPTRACE_FIELD.cpp_type = 1

pb.SS_GLOBAL_PLAYROOMUPDATEROOMLISTINFO.name = "SS_Global_PlayRoomUpdateRoomListInfo"
pb.SS_GLOBAL_PLAYROOMUPDATEROOMLISTINFO.full_name = ".SS_Global_PlayRoomUpdateRoomListInfo"
pb.SS_GLOBAL_PLAYROOMUPDATEROOMLISTINFO.nested_types = {}
pb.SS_GLOBAL_PLAYROOMUPDATEROOMLISTINFO.enum_types = {}
pb.SS_GLOBAL_PLAYROOMUPDATEROOMLISTINFO.fields = {pb.SS_GLOBAL_PLAYROOMUPDATEROOMLISTINFO_ACTORID_FIELD, pb.SS_GLOBAL_PLAYROOMUPDATEROOMLISTINFO_STOPTRACE_FIELD}
pb.SS_GLOBAL_PLAYROOMUPDATEROOMLISTINFO.is_extendable = false
pb.SS_GLOBAL_PLAYROOMUPDATEROOMLISTINFO.extensions = {}
pb.SS_GLOBAL_PLAYROOMUPDATEROOMINFO_ACTORID_FIELD.name = "ActorID"
pb.SS_GLOBAL_PLAYROOMUPDATEROOMINFO_ACTORID_FIELD.full_name = ".SS_Global_PlayRoomUpdateRoomInfo.ActorID"
pb.SS_GLOBAL_PLAYROOMUPDATEROOMINFO_ACTORID_FIELD.number = 1
pb.SS_GLOBAL_PLAYROOMUPDATEROOMINFO_ACTORID_FIELD.index = 0
pb.SS_GLOBAL_PLAYROOMUPDATEROOMINFO_ACTORID_FIELD.label = 1
pb.SS_GLOBAL_PLAYROOMUPDATEROOMINFO_ACTORID_FIELD.has_default_value = false
pb.SS_GLOBAL_PLAYROOMUPDATEROOMINFO_ACTORID_FIELD.default_value = 0
pb.SS_GLOBAL_PLAYROOMUPDATEROOMINFO_ACTORID_FIELD.type = 5
pb.SS_GLOBAL_PLAYROOMUPDATEROOMINFO_ACTORID_FIELD.cpp_type = 1

pb.SS_GLOBAL_PLAYROOMUPDATEROOMINFO_AUTOSTART_FIELD.name = "AutoStart"
pb.SS_GLOBAL_PLAYROOMUPDATEROOMINFO_AUTOSTART_FIELD.full_name = ".SS_Global_PlayRoomUpdateRoomInfo.AutoStart"
pb.SS_GLOBAL_PLAYROOMUPDATEROOMINFO_AUTOSTART_FIELD.number = 2
pb.SS_GLOBAL_PLAYROOMUPDATEROOMINFO_AUTOSTART_FIELD.index = 1
pb.SS_GLOBAL_PLAYROOMUPDATEROOMINFO_AUTOSTART_FIELD.label = 1
pb.SS_GLOBAL_PLAYROOMUPDATEROOMINFO_AUTOSTART_FIELD.has_default_value = false
pb.SS_GLOBAL_PLAYROOMUPDATEROOMINFO_AUTOSTART_FIELD.default_value = 0
pb.SS_GLOBAL_PLAYROOMUPDATEROOMINFO_AUTOSTART_FIELD.type = 5
pb.SS_GLOBAL_PLAYROOMUPDATEROOMINFO_AUTOSTART_FIELD.cpp_type = 1

pb.SS_GLOBAL_PLAYROOMUPDATEROOMINFO.name = "SS_Global_PlayRoomUpdateRoomInfo"
pb.SS_GLOBAL_PLAYROOMUPDATEROOMINFO.full_name = ".SS_Global_PlayRoomUpdateRoomInfo"
pb.SS_GLOBAL_PLAYROOMUPDATEROOMINFO.nested_types = {}
pb.SS_GLOBAL_PLAYROOMUPDATEROOMINFO.enum_types = {}
pb.SS_GLOBAL_PLAYROOMUPDATEROOMINFO.fields = {pb.SS_GLOBAL_PLAYROOMUPDATEROOMINFO_ACTORID_FIELD, pb.SS_GLOBAL_PLAYROOMUPDATEROOMINFO_AUTOSTART_FIELD}
pb.SS_GLOBAL_PLAYROOMUPDATEROOMINFO.is_extendable = false
pb.SS_GLOBAL_PLAYROOMUPDATEROOMINFO.extensions = {}
pb.SS_GLOBAL_PLAYROOMINVITEOTHER_ACTORID_FIELD.name = "ActorID"
pb.SS_GLOBAL_PLAYROOMINVITEOTHER_ACTORID_FIELD.full_name = ".SS_Global_PlayRoomInviteOther.ActorID"
pb.SS_GLOBAL_PLAYROOMINVITEOTHER_ACTORID_FIELD.number = 1
pb.SS_GLOBAL_PLAYROOMINVITEOTHER_ACTORID_FIELD.index = 0
pb.SS_GLOBAL_PLAYROOMINVITEOTHER_ACTORID_FIELD.label = 1
pb.SS_GLOBAL_PLAYROOMINVITEOTHER_ACTORID_FIELD.has_default_value = false
pb.SS_GLOBAL_PLAYROOMINVITEOTHER_ACTORID_FIELD.default_value = 0
pb.SS_GLOBAL_PLAYROOMINVITEOTHER_ACTORID_FIELD.type = 5
pb.SS_GLOBAL_PLAYROOMINVITEOTHER_ACTORID_FIELD.cpp_type = 1

pb.SS_GLOBAL_PLAYROOMINVITEOTHER_TARACTORID_FIELD.name = "TarActorID"
pb.SS_GLOBAL_PLAYROOMINVITEOTHER_TARACTORID_FIELD.full_name = ".SS_Global_PlayRoomInviteOther.TarActorID"
pb.SS_GLOBAL_PLAYROOMINVITEOTHER_TARACTORID_FIELD.number = 2
pb.SS_GLOBAL_PLAYROOMINVITEOTHER_TARACTORID_FIELD.index = 1
pb.SS_GLOBAL_PLAYROOMINVITEOTHER_TARACTORID_FIELD.label = 1
pb.SS_GLOBAL_PLAYROOMINVITEOTHER_TARACTORID_FIELD.has_default_value = false
pb.SS_GLOBAL_PLAYROOMINVITEOTHER_TARACTORID_FIELD.default_value = 0
pb.SS_GLOBAL_PLAYROOMINVITEOTHER_TARACTORID_FIELD.type = 5
pb.SS_GLOBAL_PLAYROOMINVITEOTHER_TARACTORID_FIELD.cpp_type = 1

pb.SS_GLOBAL_PLAYROOMINVITEOTHER_INVITETYPE_FIELD.name = "InviteType"
pb.SS_GLOBAL_PLAYROOMINVITEOTHER_INVITETYPE_FIELD.full_name = ".SS_Global_PlayRoomInviteOther.InviteType"
pb.SS_GLOBAL_PLAYROOMINVITEOTHER_INVITETYPE_FIELD.number = 3
pb.SS_GLOBAL_PLAYROOMINVITEOTHER_INVITETYPE_FIELD.index = 2
pb.SS_GLOBAL_PLAYROOMINVITEOTHER_INVITETYPE_FIELD.label = 1
pb.SS_GLOBAL_PLAYROOMINVITEOTHER_INVITETYPE_FIELD.has_default_value = false
pb.SS_GLOBAL_PLAYROOMINVITEOTHER_INVITETYPE_FIELD.default_value = 0
pb.SS_GLOBAL_PLAYROOMINVITEOTHER_INVITETYPE_FIELD.type = 5
pb.SS_GLOBAL_PLAYROOMINVITEOTHER_INVITETYPE_FIELD.cpp_type = 1

pb.SS_GLOBAL_PLAYROOMINVITEOTHER.name = "SS_Global_PlayRoomInviteOther"
pb.SS_GLOBAL_PLAYROOMINVITEOTHER.full_name = ".SS_Global_PlayRoomInviteOther"
pb.SS_GLOBAL_PLAYROOMINVITEOTHER.nested_types = {}
pb.SS_GLOBAL_PLAYROOMINVITEOTHER.enum_types = {}
pb.SS_GLOBAL_PLAYROOMINVITEOTHER.fields = {pb.SS_GLOBAL_PLAYROOMINVITEOTHER_ACTORID_FIELD, pb.SS_GLOBAL_PLAYROOMINVITEOTHER_TARACTORID_FIELD, pb.SS_GLOBAL_PLAYROOMINVITEOTHER_INVITETYPE_FIELD}
pb.SS_GLOBAL_PLAYROOMINVITEOTHER.is_extendable = false
pb.SS_GLOBAL_PLAYROOMINVITEOTHER.extensions = {}
pb.SS_GLOBAL_PLAYROOMDELETE_ACTORID_FIELD.name = "ActorID"
pb.SS_GLOBAL_PLAYROOMDELETE_ACTORID_FIELD.full_name = ".SS_Global_PlayRoomDelete.ActorID"
pb.SS_GLOBAL_PLAYROOMDELETE_ACTORID_FIELD.number = 1
pb.SS_GLOBAL_PLAYROOMDELETE_ACTORID_FIELD.index = 0
pb.SS_GLOBAL_PLAYROOMDELETE_ACTORID_FIELD.label = 1
pb.SS_GLOBAL_PLAYROOMDELETE_ACTORID_FIELD.has_default_value = false
pb.SS_GLOBAL_PLAYROOMDELETE_ACTORID_FIELD.default_value = 0
pb.SS_GLOBAL_PLAYROOMDELETE_ACTORID_FIELD.type = 5
pb.SS_GLOBAL_PLAYROOMDELETE_ACTORID_FIELD.cpp_type = 1

pb.SS_GLOBAL_PLAYROOMDELETE.name = "SS_Global_PlayRoomDelete"
pb.SS_GLOBAL_PLAYROOMDELETE.full_name = ".SS_Global_PlayRoomDelete"
pb.SS_GLOBAL_PLAYROOMDELETE.nested_types = {}
pb.SS_GLOBAL_PLAYROOMDELETE.enum_types = {}
pb.SS_GLOBAL_PLAYROOMDELETE.fields = {pb.SS_GLOBAL_PLAYROOMDELETE_ACTORID_FIELD}
pb.SS_GLOBAL_PLAYROOMDELETE.is_extendable = false
pb.SS_GLOBAL_PLAYROOMDELETE.extensions = {}
pb.SS_GLOBAL_PLAYROOMREENTERECTYPE_ACTORID_FIELD.name = "ActorID"
pb.SS_GLOBAL_PLAYROOMREENTERECTYPE_ACTORID_FIELD.full_name = ".SS_Global_PlayRoomReEnterEctype.ActorID"
pb.SS_GLOBAL_PLAYROOMREENTERECTYPE_ACTORID_FIELD.number = 1
pb.SS_GLOBAL_PLAYROOMREENTERECTYPE_ACTORID_FIELD.index = 0
pb.SS_GLOBAL_PLAYROOMREENTERECTYPE_ACTORID_FIELD.label = 1
pb.SS_GLOBAL_PLAYROOMREENTERECTYPE_ACTORID_FIELD.has_default_value = false
pb.SS_GLOBAL_PLAYROOMREENTERECTYPE_ACTORID_FIELD.default_value = 0
pb.SS_GLOBAL_PLAYROOMREENTERECTYPE_ACTORID_FIELD.type = 5
pb.SS_GLOBAL_PLAYROOMREENTERECTYPE_ACTORID_FIELD.cpp_type = 1

pb.SS_GLOBAL_PLAYROOMREENTERECTYPE.name = "SS_Global_PlayRoomReEnterEctype"
pb.SS_GLOBAL_PLAYROOMREENTERECTYPE.full_name = ".SS_Global_PlayRoomReEnterEctype"
pb.SS_GLOBAL_PLAYROOMREENTERECTYPE.nested_types = {}
pb.SS_GLOBAL_PLAYROOMREENTERECTYPE.enum_types = {}
pb.SS_GLOBAL_PLAYROOMREENTERECTYPE.fields = {pb.SS_GLOBAL_PLAYROOMREENTERECTYPE_ACTORID_FIELD}
pb.SS_GLOBAL_PLAYROOMREENTERECTYPE.is_extendable = false
pb.SS_GLOBAL_PLAYROOMREENTERECTYPE.extensions = {}
pb.SS_GLOBAL_PLAYROOMEVENT_EVENTTYPE_FIELD.name = "EventType"
pb.SS_GLOBAL_PLAYROOMEVENT_EVENTTYPE_FIELD.full_name = ".SS_Global_PlayRoomEvent.EventType"
pb.SS_GLOBAL_PLAYROOMEVENT_EVENTTYPE_FIELD.number = 1
pb.SS_GLOBAL_PLAYROOMEVENT_EVENTTYPE_FIELD.index = 0
pb.SS_GLOBAL_PLAYROOMEVENT_EVENTTYPE_FIELD.label = 1
pb.SS_GLOBAL_PLAYROOMEVENT_EVENTTYPE_FIELD.has_default_value = false
pb.SS_GLOBAL_PLAYROOMEVENT_EVENTTYPE_FIELD.default_value = 0
pb.SS_GLOBAL_PLAYROOMEVENT_EVENTTYPE_FIELD.type = 5
pb.SS_GLOBAL_PLAYROOMEVENT_EVENTTYPE_FIELD.cpp_type = 1

pb.SS_GLOBAL_PLAYROOMEVENT_ACTORID_FIELD.name = "ActorID"
pb.SS_GLOBAL_PLAYROOMEVENT_ACTORID_FIELD.full_name = ".SS_Global_PlayRoomEvent.ActorID"
pb.SS_GLOBAL_PLAYROOMEVENT_ACTORID_FIELD.number = 2
pb.SS_GLOBAL_PLAYROOMEVENT_ACTORID_FIELD.index = 1
pb.SS_GLOBAL_PLAYROOMEVENT_ACTORID_FIELD.label = 1
pb.SS_GLOBAL_PLAYROOMEVENT_ACTORID_FIELD.has_default_value = false
pb.SS_GLOBAL_PLAYROOMEVENT_ACTORID_FIELD.default_value = 0
pb.SS_GLOBAL_PLAYROOMEVENT_ACTORID_FIELD.type = 5
pb.SS_GLOBAL_PLAYROOMEVENT_ACTORID_FIELD.cpp_type = 1

pb.SS_GLOBAL_PLAYROOMEVENT_ROOMTYPE_FIELD.name = "RoomType"
pb.SS_GLOBAL_PLAYROOMEVENT_ROOMTYPE_FIELD.full_name = ".SS_Global_PlayRoomEvent.RoomType"
pb.SS_GLOBAL_PLAYROOMEVENT_ROOMTYPE_FIELD.number = 3
pb.SS_GLOBAL_PLAYROOMEVENT_ROOMTYPE_FIELD.index = 2
pb.SS_GLOBAL_PLAYROOMEVENT_ROOMTYPE_FIELD.label = 1
pb.SS_GLOBAL_PLAYROOMEVENT_ROOMTYPE_FIELD.has_default_value = false
pb.SS_GLOBAL_PLAYROOMEVENT_ROOMTYPE_FIELD.default_value = 0
pb.SS_GLOBAL_PLAYROOMEVENT_ROOMTYPE_FIELD.type = 5
pb.SS_GLOBAL_PLAYROOMEVENT_ROOMTYPE_FIELD.cpp_type = 1

pb.SS_GLOBAL_PLAYROOMEVENT_ROOMID_FIELD.name = "RoomID"
pb.SS_GLOBAL_PLAYROOMEVENT_ROOMID_FIELD.full_name = ".SS_Global_PlayRoomEvent.RoomID"
pb.SS_GLOBAL_PLAYROOMEVENT_ROOMID_FIELD.number = 4
pb.SS_GLOBAL_PLAYROOMEVENT_ROOMID_FIELD.index = 3
pb.SS_GLOBAL_PLAYROOMEVENT_ROOMID_FIELD.label = 1
pb.SS_GLOBAL_PLAYROOMEVENT_ROOMID_FIELD.has_default_value = false
pb.SS_GLOBAL_PLAYROOMEVENT_ROOMID_FIELD.default_value = 0
pb.SS_GLOBAL_PLAYROOMEVENT_ROOMID_FIELD.type = 5
pb.SS_GLOBAL_PLAYROOMEVENT_ROOMID_FIELD.cpp_type = 1

pb.SS_GLOBAL_PLAYROOMEVENT.name = "SS_Global_PlayRoomEvent"
pb.SS_GLOBAL_PLAYROOMEVENT.full_name = ".SS_Global_PlayRoomEvent"
pb.SS_GLOBAL_PLAYROOMEVENT.nested_types = {}
pb.SS_GLOBAL_PLAYROOMEVENT.enum_types = {}
pb.SS_GLOBAL_PLAYROOMEVENT.fields = {pb.SS_GLOBAL_PLAYROOMEVENT_EVENTTYPE_FIELD, pb.SS_GLOBAL_PLAYROOMEVENT_ACTORID_FIELD, pb.SS_GLOBAL_PLAYROOMEVENT_ROOMTYPE_FIELD, pb.SS_GLOBAL_PLAYROOMEVENT_ROOMID_FIELD}
pb.SS_GLOBAL_PLAYROOMEVENT.is_extendable = false
pb.SS_GLOBAL_PLAYROOMEVENT.extensions = {}
pb.SS_GLOBAL_PLAYROOMJOINTARGET_ACTORID_FIELD.name = "ActorID"
pb.SS_GLOBAL_PLAYROOMJOINTARGET_ACTORID_FIELD.full_name = ".SS_Global_PlayRoomJoinTarget.ActorID"
pb.SS_GLOBAL_PLAYROOMJOINTARGET_ACTORID_FIELD.number = 1
pb.SS_GLOBAL_PLAYROOMJOINTARGET_ACTORID_FIELD.index = 0
pb.SS_GLOBAL_PLAYROOMJOINTARGET_ACTORID_FIELD.label = 1
pb.SS_GLOBAL_PLAYROOMJOINTARGET_ACTORID_FIELD.has_default_value = false
pb.SS_GLOBAL_PLAYROOMJOINTARGET_ACTORID_FIELD.default_value = 0
pb.SS_GLOBAL_PLAYROOMJOINTARGET_ACTORID_FIELD.type = 5
pb.SS_GLOBAL_PLAYROOMJOINTARGET_ACTORID_FIELD.cpp_type = 1

pb.SS_GLOBAL_PLAYROOMJOINTARGET_TARGETACTORID_FIELD.name = "TargetActorID"
pb.SS_GLOBAL_PLAYROOMJOINTARGET_TARGETACTORID_FIELD.full_name = ".SS_Global_PlayRoomJoinTarget.TargetActorID"
pb.SS_GLOBAL_PLAYROOMJOINTARGET_TARGETACTORID_FIELD.number = 2
pb.SS_GLOBAL_PLAYROOMJOINTARGET_TARGETACTORID_FIELD.index = 1
pb.SS_GLOBAL_PLAYROOMJOINTARGET_TARGETACTORID_FIELD.label = 1
pb.SS_GLOBAL_PLAYROOMJOINTARGET_TARGETACTORID_FIELD.has_default_value = false
pb.SS_GLOBAL_PLAYROOMJOINTARGET_TARGETACTORID_FIELD.default_value = 0
pb.SS_GLOBAL_PLAYROOMJOINTARGET_TARGETACTORID_FIELD.type = 5
pb.SS_GLOBAL_PLAYROOMJOINTARGET_TARGETACTORID_FIELD.cpp_type = 1

pb.SS_GLOBAL_PLAYROOMJOINTARGET_AUTOPREPARE_FIELD.name = "AutoPrepare"
pb.SS_GLOBAL_PLAYROOMJOINTARGET_AUTOPREPARE_FIELD.full_name = ".SS_Global_PlayRoomJoinTarget.AutoPrepare"
pb.SS_GLOBAL_PLAYROOMJOINTARGET_AUTOPREPARE_FIELD.number = 3
pb.SS_GLOBAL_PLAYROOMJOINTARGET_AUTOPREPARE_FIELD.index = 2
pb.SS_GLOBAL_PLAYROOMJOINTARGET_AUTOPREPARE_FIELD.label = 1
pb.SS_GLOBAL_PLAYROOMJOINTARGET_AUTOPREPARE_FIELD.has_default_value = false
pb.SS_GLOBAL_PLAYROOMJOINTARGET_AUTOPREPARE_FIELD.default_value = 0
pb.SS_GLOBAL_PLAYROOMJOINTARGET_AUTOPREPARE_FIELD.type = 5
pb.SS_GLOBAL_PLAYROOMJOINTARGET_AUTOPREPARE_FIELD.cpp_type = 1

pb.SS_GLOBAL_PLAYROOMJOINTARGET.name = "SS_Global_PlayRoomJoinTarget"
pb.SS_GLOBAL_PLAYROOMJOINTARGET.full_name = ".SS_Global_PlayRoomJoinTarget"
pb.SS_GLOBAL_PLAYROOMJOINTARGET.nested_types = {}
pb.SS_GLOBAL_PLAYROOMJOINTARGET.enum_types = {}
pb.SS_GLOBAL_PLAYROOMJOINTARGET.fields = {pb.SS_GLOBAL_PLAYROOMJOINTARGET_ACTORID_FIELD, pb.SS_GLOBAL_PLAYROOMJOINTARGET_TARGETACTORID_FIELD, pb.SS_GLOBAL_PLAYROOMJOINTARGET_AUTOPREPARE_FIELD}
pb.SS_GLOBAL_PLAYROOMJOINTARGET.is_extendable = false
pb.SS_GLOBAL_PLAYROOMJOINTARGET.extensions = {}
pb.CS_GLOBAL_WILDSCENEENTER_SCENEID_FIELD.name = "SceneID"
pb.CS_GLOBAL_WILDSCENEENTER_SCENEID_FIELD.full_name = ".CS_Global_WildSceneEnter.SceneID"
pb.CS_GLOBAL_WILDSCENEENTER_SCENEID_FIELD.number = 1
pb.CS_GLOBAL_WILDSCENEENTER_SCENEID_FIELD.index = 0
pb.CS_GLOBAL_WILDSCENEENTER_SCENEID_FIELD.label = 1
pb.CS_GLOBAL_WILDSCENEENTER_SCENEID_FIELD.has_default_value = false
pb.CS_GLOBAL_WILDSCENEENTER_SCENEID_FIELD.default_value = 0
pb.CS_GLOBAL_WILDSCENEENTER_SCENEID_FIELD.type = 5
pb.CS_GLOBAL_WILDSCENEENTER_SCENEID_FIELD.cpp_type = 1

pb.CS_GLOBAL_WILDSCENEENTER.name = "CS_Global_WildSceneEnter"
pb.CS_GLOBAL_WILDSCENEENTER.full_name = ".CS_Global_WildSceneEnter"
pb.CS_GLOBAL_WILDSCENEENTER.nested_types = {}
pb.CS_GLOBAL_WILDSCENEENTER.enum_types = {}
pb.CS_GLOBAL_WILDSCENEENTER.fields = {pb.CS_GLOBAL_WILDSCENEENTER_SCENEID_FIELD}
pb.CS_GLOBAL_WILDSCENEENTER.is_extendable = false
pb.CS_GLOBAL_WILDSCENEENTER.extensions = {}
pb.SS_GLOBAL_WILDSCENEENTER_ACTORID_FIELD.name = "ActorID"
pb.SS_GLOBAL_WILDSCENEENTER_ACTORID_FIELD.full_name = ".SS_Global_WildSceneEnter.ActorID"
pb.SS_GLOBAL_WILDSCENEENTER_ACTORID_FIELD.number = 1
pb.SS_GLOBAL_WILDSCENEENTER_ACTORID_FIELD.index = 0
pb.SS_GLOBAL_WILDSCENEENTER_ACTORID_FIELD.label = 1
pb.SS_GLOBAL_WILDSCENEENTER_ACTORID_FIELD.has_default_value = false
pb.SS_GLOBAL_WILDSCENEENTER_ACTORID_FIELD.default_value = 0
pb.SS_GLOBAL_WILDSCENEENTER_ACTORID_FIELD.type = 5
pb.SS_GLOBAL_WILDSCENEENTER_ACTORID_FIELD.cpp_type = 1

pb.SS_GLOBAL_WILDSCENEENTER_SCENEID_FIELD.name = "SceneID"
pb.SS_GLOBAL_WILDSCENEENTER_SCENEID_FIELD.full_name = ".SS_Global_WildSceneEnter.SceneID"
pb.SS_GLOBAL_WILDSCENEENTER_SCENEID_FIELD.number = 2
pb.SS_GLOBAL_WILDSCENEENTER_SCENEID_FIELD.index = 1
pb.SS_GLOBAL_WILDSCENEENTER_SCENEID_FIELD.label = 1
pb.SS_GLOBAL_WILDSCENEENTER_SCENEID_FIELD.has_default_value = false
pb.SS_GLOBAL_WILDSCENEENTER_SCENEID_FIELD.default_value = 0
pb.SS_GLOBAL_WILDSCENEENTER_SCENEID_FIELD.type = 5
pb.SS_GLOBAL_WILDSCENEENTER_SCENEID_FIELD.cpp_type = 1

pb.SS_GLOBAL_WILDSCENEENTER.name = "SS_Global_WildSceneEnter"
pb.SS_GLOBAL_WILDSCENEENTER.full_name = ".SS_Global_WildSceneEnter"
pb.SS_GLOBAL_WILDSCENEENTER.nested_types = {}
pb.SS_GLOBAL_WILDSCENEENTER.enum_types = {}
pb.SS_GLOBAL_WILDSCENEENTER.fields = {pb.SS_GLOBAL_WILDSCENEENTER_ACTORID_FIELD, pb.SS_GLOBAL_WILDSCENEENTER_SCENEID_FIELD}
pb.SS_GLOBAL_WILDSCENEENTER.is_extendable = false
pb.SS_GLOBAL_WILDSCENEENTER.extensions = {}
pb.CS_GLOBAL_WILDSCENELEAVE_ACTORID_FIELD.name = "ActorID"
pb.CS_GLOBAL_WILDSCENELEAVE_ACTORID_FIELD.full_name = ".CS_Global_WildSceneLeave.ActorID"
pb.CS_GLOBAL_WILDSCENELEAVE_ACTORID_FIELD.number = 1
pb.CS_GLOBAL_WILDSCENELEAVE_ACTORID_FIELD.index = 0
pb.CS_GLOBAL_WILDSCENELEAVE_ACTORID_FIELD.label = 1
pb.CS_GLOBAL_WILDSCENELEAVE_ACTORID_FIELD.has_default_value = false
pb.CS_GLOBAL_WILDSCENELEAVE_ACTORID_FIELD.default_value = 0
pb.CS_GLOBAL_WILDSCENELEAVE_ACTORID_FIELD.type = 5
pb.CS_GLOBAL_WILDSCENELEAVE_ACTORID_FIELD.cpp_type = 1

pb.CS_GLOBAL_WILDSCENELEAVE.name = "CS_Global_WildSceneLeave"
pb.CS_GLOBAL_WILDSCENELEAVE.full_name = ".CS_Global_WildSceneLeave"
pb.CS_GLOBAL_WILDSCENELEAVE.nested_types = {}
pb.CS_GLOBAL_WILDSCENELEAVE.enum_types = {}
pb.CS_GLOBAL_WILDSCENELEAVE.fields = {pb.CS_GLOBAL_WILDSCENELEAVE_ACTORID_FIELD}
pb.CS_GLOBAL_WILDSCENELEAVE.is_extendable = false
pb.CS_GLOBAL_WILDSCENELEAVE.extensions = {}
pb.CS_GLOBAL_ACTVECTYPEENTER_ACTVID_FIELD.name = "ActvID"
pb.CS_GLOBAL_ACTVECTYPEENTER_ACTVID_FIELD.full_name = ".CS_Global_ActvEctypeEnter.ActvID"
pb.CS_GLOBAL_ACTVECTYPEENTER_ACTVID_FIELD.number = 1
pb.CS_GLOBAL_ACTVECTYPEENTER_ACTVID_FIELD.index = 0
pb.CS_GLOBAL_ACTVECTYPEENTER_ACTVID_FIELD.label = 1
pb.CS_GLOBAL_ACTVECTYPEENTER_ACTVID_FIELD.has_default_value = false
pb.CS_GLOBAL_ACTVECTYPEENTER_ACTVID_FIELD.default_value = 0
pb.CS_GLOBAL_ACTVECTYPEENTER_ACTVID_FIELD.type = 5
pb.CS_GLOBAL_ACTVECTYPEENTER_ACTVID_FIELD.cpp_type = 1

pb.CS_GLOBAL_ACTVECTYPEENTER_PARAM_FIELD.name = "Param"
pb.CS_GLOBAL_ACTVECTYPEENTER_PARAM_FIELD.full_name = ".CS_Global_ActvEctypeEnter.Param"
pb.CS_GLOBAL_ACTVECTYPEENTER_PARAM_FIELD.number = 2
pb.CS_GLOBAL_ACTVECTYPEENTER_PARAM_FIELD.index = 1
pb.CS_GLOBAL_ACTVECTYPEENTER_PARAM_FIELD.label = 1
pb.CS_GLOBAL_ACTVECTYPEENTER_PARAM_FIELD.has_default_value = false
pb.CS_GLOBAL_ACTVECTYPEENTER_PARAM_FIELD.default_value = 0
pb.CS_GLOBAL_ACTVECTYPEENTER_PARAM_FIELD.type = 5
pb.CS_GLOBAL_ACTVECTYPEENTER_PARAM_FIELD.cpp_type = 1

pb.CS_GLOBAL_ACTVECTYPEENTER.name = "CS_Global_ActvEctypeEnter"
pb.CS_GLOBAL_ACTVECTYPEENTER.full_name = ".CS_Global_ActvEctypeEnter"
pb.CS_GLOBAL_ACTVECTYPEENTER.nested_types = {}
pb.CS_GLOBAL_ACTVECTYPEENTER.enum_types = {}
pb.CS_GLOBAL_ACTVECTYPEENTER.fields = {pb.CS_GLOBAL_ACTVECTYPEENTER_ACTVID_FIELD, pb.CS_GLOBAL_ACTVECTYPEENTER_PARAM_FIELD}
pb.CS_GLOBAL_ACTVECTYPEENTER.is_extendable = false
pb.CS_GLOBAL_ACTVECTYPEENTER.extensions = {}
pb.SS_GLOBAL_ACTVECTYPEENTER_ACTORID_FIELD.name = "ActorID"
pb.SS_GLOBAL_ACTVECTYPEENTER_ACTORID_FIELD.full_name = ".SS_Global_ActvEctypeEnter.ActorID"
pb.SS_GLOBAL_ACTVECTYPEENTER_ACTORID_FIELD.number = 1
pb.SS_GLOBAL_ACTVECTYPEENTER_ACTORID_FIELD.index = 0
pb.SS_GLOBAL_ACTVECTYPEENTER_ACTORID_FIELD.label = 1
pb.SS_GLOBAL_ACTVECTYPEENTER_ACTORID_FIELD.has_default_value = false
pb.SS_GLOBAL_ACTVECTYPEENTER_ACTORID_FIELD.default_value = 0
pb.SS_GLOBAL_ACTVECTYPEENTER_ACTORID_FIELD.type = 5
pb.SS_GLOBAL_ACTVECTYPEENTER_ACTORID_FIELD.cpp_type = 1

pb.SS_GLOBAL_ACTVECTYPEENTER_ACTVID_FIELD.name = "ActvID"
pb.SS_GLOBAL_ACTVECTYPEENTER_ACTVID_FIELD.full_name = ".SS_Global_ActvEctypeEnter.ActvID"
pb.SS_GLOBAL_ACTVECTYPEENTER_ACTVID_FIELD.number = 2
pb.SS_GLOBAL_ACTVECTYPEENTER_ACTVID_FIELD.index = 1
pb.SS_GLOBAL_ACTVECTYPEENTER_ACTVID_FIELD.label = 1
pb.SS_GLOBAL_ACTVECTYPEENTER_ACTVID_FIELD.has_default_value = false
pb.SS_GLOBAL_ACTVECTYPEENTER_ACTVID_FIELD.default_value = 0
pb.SS_GLOBAL_ACTVECTYPEENTER_ACTVID_FIELD.type = 5
pb.SS_GLOBAL_ACTVECTYPEENTER_ACTVID_FIELD.cpp_type = 1

pb.SS_GLOBAL_ACTVECTYPEENTER_PARAM_FIELD.name = "Param"
pb.SS_GLOBAL_ACTVECTYPEENTER_PARAM_FIELD.full_name = ".SS_Global_ActvEctypeEnter.Param"
pb.SS_GLOBAL_ACTVECTYPEENTER_PARAM_FIELD.number = 3
pb.SS_GLOBAL_ACTVECTYPEENTER_PARAM_FIELD.index = 2
pb.SS_GLOBAL_ACTVECTYPEENTER_PARAM_FIELD.label = 1
pb.SS_GLOBAL_ACTVECTYPEENTER_PARAM_FIELD.has_default_value = false
pb.SS_GLOBAL_ACTVECTYPEENTER_PARAM_FIELD.default_value = 0
pb.SS_GLOBAL_ACTVECTYPEENTER_PARAM_FIELD.type = 5
pb.SS_GLOBAL_ACTVECTYPEENTER_PARAM_FIELD.cpp_type = 1

pb.SS_GLOBAL_ACTVECTYPEENTER.name = "SS_Global_ActvEctypeEnter"
pb.SS_GLOBAL_ACTVECTYPEENTER.full_name = ".SS_Global_ActvEctypeEnter"
pb.SS_GLOBAL_ACTVECTYPEENTER.nested_types = {}
pb.SS_GLOBAL_ACTVECTYPEENTER.enum_types = {}
pb.SS_GLOBAL_ACTVECTYPEENTER.fields = {pb.SS_GLOBAL_ACTVECTYPEENTER_ACTORID_FIELD, pb.SS_GLOBAL_ACTVECTYPEENTER_ACTVID_FIELD, pb.SS_GLOBAL_ACTVECTYPEENTER_PARAM_FIELD}
pb.SS_GLOBAL_ACTVECTYPEENTER.is_extendable = false
pb.SS_GLOBAL_ACTVECTYPEENTER.extensions = {}
pb.CS_GLOBAL_ACTVECTYPELEAVE_ACTORID_FIELD.name = "ActorID"
pb.CS_GLOBAL_ACTVECTYPELEAVE_ACTORID_FIELD.full_name = ".CS_Global_ActvEctypeLeave.ActorID"
pb.CS_GLOBAL_ACTVECTYPELEAVE_ACTORID_FIELD.number = 1
pb.CS_GLOBAL_ACTVECTYPELEAVE_ACTORID_FIELD.index = 0
pb.CS_GLOBAL_ACTVECTYPELEAVE_ACTORID_FIELD.label = 1
pb.CS_GLOBAL_ACTVECTYPELEAVE_ACTORID_FIELD.has_default_value = false
pb.CS_GLOBAL_ACTVECTYPELEAVE_ACTORID_FIELD.default_value = 0
pb.CS_GLOBAL_ACTVECTYPELEAVE_ACTORID_FIELD.type = 5
pb.CS_GLOBAL_ACTVECTYPELEAVE_ACTORID_FIELD.cpp_type = 1

pb.CS_GLOBAL_ACTVECTYPELEAVE.name = "CS_Global_ActvEctypeLeave"
pb.CS_GLOBAL_ACTVECTYPELEAVE.full_name = ".CS_Global_ActvEctypeLeave"
pb.CS_GLOBAL_ACTVECTYPELEAVE.nested_types = {}
pb.CS_GLOBAL_ACTVECTYPELEAVE.enum_types = {}
pb.CS_GLOBAL_ACTVECTYPELEAVE.fields = {pb.CS_GLOBAL_ACTVECTYPELEAVE_ACTORID_FIELD}
pb.CS_GLOBAL_ACTVECTYPELEAVE.is_extendable = false
pb.CS_GLOBAL_ACTVECTYPELEAVE.extensions = {}
pb.SS_GLOBAL_ACTVRANKVALUECHANGE_ACTORID_FIELD.name = "ActorID"
pb.SS_GLOBAL_ACTVRANKVALUECHANGE_ACTORID_FIELD.full_name = ".SS_Global_ActvRankValueChange.ActorID"
pb.SS_GLOBAL_ACTVRANKVALUECHANGE_ACTORID_FIELD.number = 1
pb.SS_GLOBAL_ACTVRANKVALUECHANGE_ACTORID_FIELD.index = 0
pb.SS_GLOBAL_ACTVRANKVALUECHANGE_ACTORID_FIELD.label = 1
pb.SS_GLOBAL_ACTVRANKVALUECHANGE_ACTORID_FIELD.has_default_value = false
pb.SS_GLOBAL_ACTVRANKVALUECHANGE_ACTORID_FIELD.default_value = 0
pb.SS_GLOBAL_ACTVRANKVALUECHANGE_ACTORID_FIELD.type = 5
pb.SS_GLOBAL_ACTVRANKVALUECHANGE_ACTORID_FIELD.cpp_type = 1

pb.SS_GLOBAL_ACTVRANKVALUECHANGE_ACTVID_FIELD.name = "ActvID"
pb.SS_GLOBAL_ACTVRANKVALUECHANGE_ACTVID_FIELD.full_name = ".SS_Global_ActvRankValueChange.ActvID"
pb.SS_GLOBAL_ACTVRANKVALUECHANGE_ACTVID_FIELD.number = 2
pb.SS_GLOBAL_ACTVRANKVALUECHANGE_ACTVID_FIELD.index = 1
pb.SS_GLOBAL_ACTVRANKVALUECHANGE_ACTVID_FIELD.label = 1
pb.SS_GLOBAL_ACTVRANKVALUECHANGE_ACTVID_FIELD.has_default_value = false
pb.SS_GLOBAL_ACTVRANKVALUECHANGE_ACTVID_FIELD.default_value = 0
pb.SS_GLOBAL_ACTVRANKVALUECHANGE_ACTVID_FIELD.type = 5
pb.SS_GLOBAL_ACTVRANKVALUECHANGE_ACTVID_FIELD.cpp_type = 1

pb.SS_GLOBAL_ACTVRANKVALUECHANGE_RANKVALUE_FIELD.name = "RankValue"
pb.SS_GLOBAL_ACTVRANKVALUECHANGE_RANKVALUE_FIELD.full_name = ".SS_Global_ActvRankValueChange.RankValue"
pb.SS_GLOBAL_ACTVRANKVALUECHANGE_RANKVALUE_FIELD.number = 3
pb.SS_GLOBAL_ACTVRANKVALUECHANGE_RANKVALUE_FIELD.index = 2
pb.SS_GLOBAL_ACTVRANKVALUECHANGE_RANKVALUE_FIELD.label = 1
pb.SS_GLOBAL_ACTVRANKVALUECHANGE_RANKVALUE_FIELD.has_default_value = false
pb.SS_GLOBAL_ACTVRANKVALUECHANGE_RANKVALUE_FIELD.default_value = 0
pb.SS_GLOBAL_ACTVRANKVALUECHANGE_RANKVALUE_FIELD.type = 5
pb.SS_GLOBAL_ACTVRANKVALUECHANGE_RANKVALUE_FIELD.cpp_type = 1

pb.SS_GLOBAL_ACTVRANKVALUECHANGE_SETTYPE_FIELD.name = "SetType"
pb.SS_GLOBAL_ACTVRANKVALUECHANGE_SETTYPE_FIELD.full_name = ".SS_Global_ActvRankValueChange.SetType"
pb.SS_GLOBAL_ACTVRANKVALUECHANGE_SETTYPE_FIELD.number = 4
pb.SS_GLOBAL_ACTVRANKVALUECHANGE_SETTYPE_FIELD.index = 3
pb.SS_GLOBAL_ACTVRANKVALUECHANGE_SETTYPE_FIELD.label = 1
pb.SS_GLOBAL_ACTVRANKVALUECHANGE_SETTYPE_FIELD.has_default_value = false
pb.SS_GLOBAL_ACTVRANKVALUECHANGE_SETTYPE_FIELD.default_value = 0
pb.SS_GLOBAL_ACTVRANKVALUECHANGE_SETTYPE_FIELD.type = 5
pb.SS_GLOBAL_ACTVRANKVALUECHANGE_SETTYPE_FIELD.cpp_type = 1

pb.SS_GLOBAL_ACTVRANKVALUECHANGE_ZONEID_FIELD.name = "ZoneID"
pb.SS_GLOBAL_ACTVRANKVALUECHANGE_ZONEID_FIELD.full_name = ".SS_Global_ActvRankValueChange.ZoneID"
pb.SS_GLOBAL_ACTVRANKVALUECHANGE_ZONEID_FIELD.number = 5
pb.SS_GLOBAL_ACTVRANKVALUECHANGE_ZONEID_FIELD.index = 4
pb.SS_GLOBAL_ACTVRANKVALUECHANGE_ZONEID_FIELD.label = 1
pb.SS_GLOBAL_ACTVRANKVALUECHANGE_ZONEID_FIELD.has_default_value = false
pb.SS_GLOBAL_ACTVRANKVALUECHANGE_ZONEID_FIELD.default_value = 0
pb.SS_GLOBAL_ACTVRANKVALUECHANGE_ZONEID_FIELD.type = 5
pb.SS_GLOBAL_ACTVRANKVALUECHANGE_ZONEID_FIELD.cpp_type = 1

pb.SS_GLOBAL_ACTVRANKVALUECHANGE.name = "SS_Global_ActvRankValueChange"
pb.SS_GLOBAL_ACTVRANKVALUECHANGE.full_name = ".SS_Global_ActvRankValueChange"
pb.SS_GLOBAL_ACTVRANKVALUECHANGE.nested_types = {}
pb.SS_GLOBAL_ACTVRANKVALUECHANGE.enum_types = {}
pb.SS_GLOBAL_ACTVRANKVALUECHANGE.fields = {pb.SS_GLOBAL_ACTVRANKVALUECHANGE_ACTORID_FIELD, pb.SS_GLOBAL_ACTVRANKVALUECHANGE_ACTVID_FIELD, pb.SS_GLOBAL_ACTVRANKVALUECHANGE_RANKVALUE_FIELD, pb.SS_GLOBAL_ACTVRANKVALUECHANGE_SETTYPE_FIELD, pb.SS_GLOBAL_ACTVRANKVALUECHANGE_ZONEID_FIELD}
pb.SS_GLOBAL_ACTVRANKVALUECHANGE.is_extendable = false
pb.SS_GLOBAL_ACTVRANKVALUECHANGE.extensions = {}
pb.SS_GLOBAL_ACTVRANKLISTGET_ACTORID_FIELD.name = "ActorID"
pb.SS_GLOBAL_ACTVRANKLISTGET_ACTORID_FIELD.full_name = ".SS_Global_ActvRankListGet.ActorID"
pb.SS_GLOBAL_ACTVRANKLISTGET_ACTORID_FIELD.number = 1
pb.SS_GLOBAL_ACTVRANKLISTGET_ACTORID_FIELD.index = 0
pb.SS_GLOBAL_ACTVRANKLISTGET_ACTORID_FIELD.label = 1
pb.SS_GLOBAL_ACTVRANKLISTGET_ACTORID_FIELD.has_default_value = false
pb.SS_GLOBAL_ACTVRANKLISTGET_ACTORID_FIELD.default_value = 0
pb.SS_GLOBAL_ACTVRANKLISTGET_ACTORID_FIELD.type = 5
pb.SS_GLOBAL_ACTVRANKLISTGET_ACTORID_FIELD.cpp_type = 1

pb.SS_GLOBAL_ACTVRANKLISTGET_SOCIETYID_FIELD.name = "SocietyID"
pb.SS_GLOBAL_ACTVRANKLISTGET_SOCIETYID_FIELD.full_name = ".SS_Global_ActvRankListGet.SocietyID"
pb.SS_GLOBAL_ACTVRANKLISTGET_SOCIETYID_FIELD.number = 2
pb.SS_GLOBAL_ACTVRANKLISTGET_SOCIETYID_FIELD.index = 1
pb.SS_GLOBAL_ACTVRANKLISTGET_SOCIETYID_FIELD.label = 1
pb.SS_GLOBAL_ACTVRANKLISTGET_SOCIETYID_FIELD.has_default_value = false
pb.SS_GLOBAL_ACTVRANKLISTGET_SOCIETYID_FIELD.default_value = 0
pb.SS_GLOBAL_ACTVRANKLISTGET_SOCIETYID_FIELD.type = 5
pb.SS_GLOBAL_ACTVRANKLISTGET_SOCIETYID_FIELD.cpp_type = 1

pb.SS_GLOBAL_ACTVRANKLISTGET_ACTVID_FIELD.name = "ActvID"
pb.SS_GLOBAL_ACTVRANKLISTGET_ACTVID_FIELD.full_name = ".SS_Global_ActvRankListGet.ActvID"
pb.SS_GLOBAL_ACTVRANKLISTGET_ACTVID_FIELD.number = 3
pb.SS_GLOBAL_ACTVRANKLISTGET_ACTVID_FIELD.index = 2
pb.SS_GLOBAL_ACTVRANKLISTGET_ACTVID_FIELD.label = 1
pb.SS_GLOBAL_ACTVRANKLISTGET_ACTVID_FIELD.has_default_value = false
pb.SS_GLOBAL_ACTVRANKLISTGET_ACTVID_FIELD.default_value = 0
pb.SS_GLOBAL_ACTVRANKLISTGET_ACTVID_FIELD.type = 5
pb.SS_GLOBAL_ACTVRANKLISTGET_ACTVID_FIELD.cpp_type = 1

pb.SS_GLOBAL_ACTVRANKLISTGET_ZONEID_FIELD.name = "ZoneID"
pb.SS_GLOBAL_ACTVRANKLISTGET_ZONEID_FIELD.full_name = ".SS_Global_ActvRankListGet.ZoneID"
pb.SS_GLOBAL_ACTVRANKLISTGET_ZONEID_FIELD.number = 4
pb.SS_GLOBAL_ACTVRANKLISTGET_ZONEID_FIELD.index = 3
pb.SS_GLOBAL_ACTVRANKLISTGET_ZONEID_FIELD.label = 1
pb.SS_GLOBAL_ACTVRANKLISTGET_ZONEID_FIELD.has_default_value = false
pb.SS_GLOBAL_ACTVRANKLISTGET_ZONEID_FIELD.default_value = 0
pb.SS_GLOBAL_ACTVRANKLISTGET_ZONEID_FIELD.type = 5
pb.SS_GLOBAL_ACTVRANKLISTGET_ZONEID_FIELD.cpp_type = 1

pb.SS_GLOBAL_ACTVRANKLISTGET.name = "SS_Global_ActvRankListGet"
pb.SS_GLOBAL_ACTVRANKLISTGET.full_name = ".SS_Global_ActvRankListGet"
pb.SS_GLOBAL_ACTVRANKLISTGET.nested_types = {}
pb.SS_GLOBAL_ACTVRANKLISTGET.enum_types = {}
pb.SS_GLOBAL_ACTVRANKLISTGET.fields = {pb.SS_GLOBAL_ACTVRANKLISTGET_ACTORID_FIELD, pb.SS_GLOBAL_ACTVRANKLISTGET_SOCIETYID_FIELD, pb.SS_GLOBAL_ACTVRANKLISTGET_ACTVID_FIELD, pb.SS_GLOBAL_ACTVRANKLISTGET_ZONEID_FIELD}
pb.SS_GLOBAL_ACTVRANKLISTGET.is_extendable = false
pb.SS_GLOBAL_ACTVRANKLISTGET.extensions = {}
pb.SS_GLOBAL_ACTVRANKLISTREPLACE_ACTORID_FIELD.name = "ActorID"
pb.SS_GLOBAL_ACTVRANKLISTREPLACE_ACTORID_FIELD.full_name = ".SS_Global_ActvRankListReplace.ActorID"
pb.SS_GLOBAL_ACTVRANKLISTREPLACE_ACTORID_FIELD.number = 1
pb.SS_GLOBAL_ACTVRANKLISTREPLACE_ACTORID_FIELD.index = 0
pb.SS_GLOBAL_ACTVRANKLISTREPLACE_ACTORID_FIELD.label = 1
pb.SS_GLOBAL_ACTVRANKLISTREPLACE_ACTORID_FIELD.has_default_value = false
pb.SS_GLOBAL_ACTVRANKLISTREPLACE_ACTORID_FIELD.default_value = 0
pb.SS_GLOBAL_ACTVRANKLISTREPLACE_ACTORID_FIELD.type = 5
pb.SS_GLOBAL_ACTVRANKLISTREPLACE_ACTORID_FIELD.cpp_type = 1

pb.SS_GLOBAL_ACTVRANKLISTREPLACE_ACTVID_FIELD.name = "ActvID"
pb.SS_GLOBAL_ACTVRANKLISTREPLACE_ACTVID_FIELD.full_name = ".SS_Global_ActvRankListReplace.ActvID"
pb.SS_GLOBAL_ACTVRANKLISTREPLACE_ACTVID_FIELD.number = 2
pb.SS_GLOBAL_ACTVRANKLISTREPLACE_ACTVID_FIELD.index = 1
pb.SS_GLOBAL_ACTVRANKLISTREPLACE_ACTVID_FIELD.label = 1
pb.SS_GLOBAL_ACTVRANKLISTREPLACE_ACTVID_FIELD.has_default_value = false
pb.SS_GLOBAL_ACTVRANKLISTREPLACE_ACTVID_FIELD.default_value = 0
pb.SS_GLOBAL_ACTVRANKLISTREPLACE_ACTVID_FIELD.type = 5
pb.SS_GLOBAL_ACTVRANKLISTREPLACE_ACTVID_FIELD.cpp_type = 1

pb.SS_GLOBAL_ACTVRANKLISTREPLACE_NEWVALUE_FIELD.name = "NewValue"
pb.SS_GLOBAL_ACTVRANKLISTREPLACE_NEWVALUE_FIELD.full_name = ".SS_Global_ActvRankListReplace.NewValue"
pb.SS_GLOBAL_ACTVRANKLISTREPLACE_NEWVALUE_FIELD.number = 3
pb.SS_GLOBAL_ACTVRANKLISTREPLACE_NEWVALUE_FIELD.index = 2
pb.SS_GLOBAL_ACTVRANKLISTREPLACE_NEWVALUE_FIELD.label = 1
pb.SS_GLOBAL_ACTVRANKLISTREPLACE_NEWVALUE_FIELD.has_default_value = false
pb.SS_GLOBAL_ACTVRANKLISTREPLACE_NEWVALUE_FIELD.default_value = 0
pb.SS_GLOBAL_ACTVRANKLISTREPLACE_NEWVALUE_FIELD.type = 5
pb.SS_GLOBAL_ACTVRANKLISTREPLACE_NEWVALUE_FIELD.cpp_type = 1

pb.SS_GLOBAL_ACTVRANKLISTREPLACE_REPLACETYPE_FIELD.name = "ReplaceType"
pb.SS_GLOBAL_ACTVRANKLISTREPLACE_REPLACETYPE_FIELD.full_name = ".SS_Global_ActvRankListReplace.ReplaceType"
pb.SS_GLOBAL_ACTVRANKLISTREPLACE_REPLACETYPE_FIELD.number = 4
pb.SS_GLOBAL_ACTVRANKLISTREPLACE_REPLACETYPE_FIELD.index = 3
pb.SS_GLOBAL_ACTVRANKLISTREPLACE_REPLACETYPE_FIELD.label = 1
pb.SS_GLOBAL_ACTVRANKLISTREPLACE_REPLACETYPE_FIELD.has_default_value = false
pb.SS_GLOBAL_ACTVRANKLISTREPLACE_REPLACETYPE_FIELD.default_value = 0
pb.SS_GLOBAL_ACTVRANKLISTREPLACE_REPLACETYPE_FIELD.type = 5
pb.SS_GLOBAL_ACTVRANKLISTREPLACE_REPLACETYPE_FIELD.cpp_type = 1

pb.SS_GLOBAL_ACTVRANKLISTREPLACE_ZONEID_FIELD.name = "ZoneID"
pb.SS_GLOBAL_ACTVRANKLISTREPLACE_ZONEID_FIELD.full_name = ".SS_Global_ActvRankListReplace.ZoneID"
pb.SS_GLOBAL_ACTVRANKLISTREPLACE_ZONEID_FIELD.number = 5
pb.SS_GLOBAL_ACTVRANKLISTREPLACE_ZONEID_FIELD.index = 4
pb.SS_GLOBAL_ACTVRANKLISTREPLACE_ZONEID_FIELD.label = 1
pb.SS_GLOBAL_ACTVRANKLISTREPLACE_ZONEID_FIELD.has_default_value = false
pb.SS_GLOBAL_ACTVRANKLISTREPLACE_ZONEID_FIELD.default_value = 0
pb.SS_GLOBAL_ACTVRANKLISTREPLACE_ZONEID_FIELD.type = 5
pb.SS_GLOBAL_ACTVRANKLISTREPLACE_ZONEID_FIELD.cpp_type = 1

pb.SS_GLOBAL_ACTVRANKLISTREPLACE.name = "SS_Global_ActvRankListReplace"
pb.SS_GLOBAL_ACTVRANKLISTREPLACE.full_name = ".SS_Global_ActvRankListReplace"
pb.SS_GLOBAL_ACTVRANKLISTREPLACE.nested_types = {}
pb.SS_GLOBAL_ACTVRANKLISTREPLACE.enum_types = {}
pb.SS_GLOBAL_ACTVRANKLISTREPLACE.fields = {pb.SS_GLOBAL_ACTVRANKLISTREPLACE_ACTORID_FIELD, pb.SS_GLOBAL_ACTVRANKLISTREPLACE_ACTVID_FIELD, pb.SS_GLOBAL_ACTVRANKLISTREPLACE_NEWVALUE_FIELD, pb.SS_GLOBAL_ACTVRANKLISTREPLACE_REPLACETYPE_FIELD, pb.SS_GLOBAL_ACTVRANKLISTREPLACE_ZONEID_FIELD}
pb.SS_GLOBAL_ACTVRANKLISTREPLACE.is_extendable = false
pb.SS_GLOBAL_ACTVRANKLISTREPLACE.extensions = {}
pb.SS_GLOBAL_ACTVRECORDUPDATEVALUE_ACTORID_FIELD.name = "ActorID"
pb.SS_GLOBAL_ACTVRECORDUPDATEVALUE_ACTORID_FIELD.full_name = ".SS_Global_ActvRecordUpdateValue.ActorID"
pb.SS_GLOBAL_ACTVRECORDUPDATEVALUE_ACTORID_FIELD.number = 1
pb.SS_GLOBAL_ACTVRECORDUPDATEVALUE_ACTORID_FIELD.index = 0
pb.SS_GLOBAL_ACTVRECORDUPDATEVALUE_ACTORID_FIELD.label = 1
pb.SS_GLOBAL_ACTVRECORDUPDATEVALUE_ACTORID_FIELD.has_default_value = false
pb.SS_GLOBAL_ACTVRECORDUPDATEVALUE_ACTORID_FIELD.default_value = 0
pb.SS_GLOBAL_ACTVRECORDUPDATEVALUE_ACTORID_FIELD.type = 5
pb.SS_GLOBAL_ACTVRECORDUPDATEVALUE_ACTORID_FIELD.cpp_type = 1

pb.SS_GLOBAL_ACTVRECORDUPDATEVALUE_ACTVID_FIELD.name = "ActvID"
pb.SS_GLOBAL_ACTVRECORDUPDATEVALUE_ACTVID_FIELD.full_name = ".SS_Global_ActvRecordUpdateValue.ActvID"
pb.SS_GLOBAL_ACTVRECORDUPDATEVALUE_ACTVID_FIELD.number = 2
pb.SS_GLOBAL_ACTVRECORDUPDATEVALUE_ACTVID_FIELD.index = 1
pb.SS_GLOBAL_ACTVRECORDUPDATEVALUE_ACTVID_FIELD.label = 1
pb.SS_GLOBAL_ACTVRECORDUPDATEVALUE_ACTVID_FIELD.has_default_value = false
pb.SS_GLOBAL_ACTVRECORDUPDATEVALUE_ACTVID_FIELD.default_value = 0
pb.SS_GLOBAL_ACTVRECORDUPDATEVALUE_ACTVID_FIELD.type = 5
pb.SS_GLOBAL_ACTVRECORDUPDATEVALUE_ACTVID_FIELD.cpp_type = 1

pb.SS_GLOBAL_ACTVRECORDUPDATEVALUE_VALUE_FIELD.name = "Value"
pb.SS_GLOBAL_ACTVRECORDUPDATEVALUE_VALUE_FIELD.full_name = ".SS_Global_ActvRecordUpdateValue.Value"
pb.SS_GLOBAL_ACTVRECORDUPDATEVALUE_VALUE_FIELD.number = 3
pb.SS_GLOBAL_ACTVRECORDUPDATEVALUE_VALUE_FIELD.index = 2
pb.SS_GLOBAL_ACTVRECORDUPDATEVALUE_VALUE_FIELD.label = 1
pb.SS_GLOBAL_ACTVRECORDUPDATEVALUE_VALUE_FIELD.has_default_value = false
pb.SS_GLOBAL_ACTVRECORDUPDATEVALUE_VALUE_FIELD.default_value = 0
pb.SS_GLOBAL_ACTVRECORDUPDATEVALUE_VALUE_FIELD.type = 5
pb.SS_GLOBAL_ACTVRECORDUPDATEVALUE_VALUE_FIELD.cpp_type = 1

pb.SS_GLOBAL_ACTVRECORDUPDATEVALUE_PARAM_FIELD.name = "Param"
pb.SS_GLOBAL_ACTVRECORDUPDATEVALUE_PARAM_FIELD.full_name = ".SS_Global_ActvRecordUpdateValue.Param"
pb.SS_GLOBAL_ACTVRECORDUPDATEVALUE_PARAM_FIELD.number = 4
pb.SS_GLOBAL_ACTVRECORDUPDATEVALUE_PARAM_FIELD.index = 3
pb.SS_GLOBAL_ACTVRECORDUPDATEVALUE_PARAM_FIELD.label = 1
pb.SS_GLOBAL_ACTVRECORDUPDATEVALUE_PARAM_FIELD.has_default_value = false
pb.SS_GLOBAL_ACTVRECORDUPDATEVALUE_PARAM_FIELD.default_value = 0
pb.SS_GLOBAL_ACTVRECORDUPDATEVALUE_PARAM_FIELD.type = 5
pb.SS_GLOBAL_ACTVRECORDUPDATEVALUE_PARAM_FIELD.cpp_type = 1

pb.SS_GLOBAL_ACTVRECORDUPDATEVALUE_ZONEID_FIELD.name = "ZoneID"
pb.SS_GLOBAL_ACTVRECORDUPDATEVALUE_ZONEID_FIELD.full_name = ".SS_Global_ActvRecordUpdateValue.ZoneID"
pb.SS_GLOBAL_ACTVRECORDUPDATEVALUE_ZONEID_FIELD.number = 5
pb.SS_GLOBAL_ACTVRECORDUPDATEVALUE_ZONEID_FIELD.index = 4
pb.SS_GLOBAL_ACTVRECORDUPDATEVALUE_ZONEID_FIELD.label = 1
pb.SS_GLOBAL_ACTVRECORDUPDATEVALUE_ZONEID_FIELD.has_default_value = false
pb.SS_GLOBAL_ACTVRECORDUPDATEVALUE_ZONEID_FIELD.default_value = 0
pb.SS_GLOBAL_ACTVRECORDUPDATEVALUE_ZONEID_FIELD.type = 5
pb.SS_GLOBAL_ACTVRECORDUPDATEVALUE_ZONEID_FIELD.cpp_type = 1

pb.SS_GLOBAL_ACTVRECORDUPDATEVALUE.name = "SS_Global_ActvRecordUpdateValue"
pb.SS_GLOBAL_ACTVRECORDUPDATEVALUE.full_name = ".SS_Global_ActvRecordUpdateValue"
pb.SS_GLOBAL_ACTVRECORDUPDATEVALUE.nested_types = {}
pb.SS_GLOBAL_ACTVRECORDUPDATEVALUE.enum_types = {}
pb.SS_GLOBAL_ACTVRECORDUPDATEVALUE.fields = {pb.SS_GLOBAL_ACTVRECORDUPDATEVALUE_ACTORID_FIELD, pb.SS_GLOBAL_ACTVRECORDUPDATEVALUE_ACTVID_FIELD, pb.SS_GLOBAL_ACTVRECORDUPDATEVALUE_VALUE_FIELD, pb.SS_GLOBAL_ACTVRECORDUPDATEVALUE_PARAM_FIELD, pb.SS_GLOBAL_ACTVRECORDUPDATEVALUE_ZONEID_FIELD}
pb.SS_GLOBAL_ACTVRECORDUPDATEVALUE.is_extendable = false
pb.SS_GLOBAL_ACTVRECORDUPDATEVALUE.extensions = {}
pb.SS_GLOBAL_ACTVRECORDGETLIST_ACTORID_FIELD.name = "ActorID"
pb.SS_GLOBAL_ACTVRECORDGETLIST_ACTORID_FIELD.full_name = ".SS_Global_ActvRecordGetList.ActorID"
pb.SS_GLOBAL_ACTVRECORDGETLIST_ACTORID_FIELD.number = 1
pb.SS_GLOBAL_ACTVRECORDGETLIST_ACTORID_FIELD.index = 0
pb.SS_GLOBAL_ACTVRECORDGETLIST_ACTORID_FIELD.label = 1
pb.SS_GLOBAL_ACTVRECORDGETLIST_ACTORID_FIELD.has_default_value = false
pb.SS_GLOBAL_ACTVRECORDGETLIST_ACTORID_FIELD.default_value = 0
pb.SS_GLOBAL_ACTVRECORDGETLIST_ACTORID_FIELD.type = 5
pb.SS_GLOBAL_ACTVRECORDGETLIST_ACTORID_FIELD.cpp_type = 1

pb.SS_GLOBAL_ACTVRECORDGETLIST_ACTIVITYID_FIELD.name = "ActivityID"
pb.SS_GLOBAL_ACTVRECORDGETLIST_ACTIVITYID_FIELD.full_name = ".SS_Global_ActvRecordGetList.ActivityID"
pb.SS_GLOBAL_ACTVRECORDGETLIST_ACTIVITYID_FIELD.number = 2
pb.SS_GLOBAL_ACTVRECORDGETLIST_ACTIVITYID_FIELD.index = 1
pb.SS_GLOBAL_ACTVRECORDGETLIST_ACTIVITYID_FIELD.label = 1
pb.SS_GLOBAL_ACTVRECORDGETLIST_ACTIVITYID_FIELD.has_default_value = false
pb.SS_GLOBAL_ACTVRECORDGETLIST_ACTIVITYID_FIELD.default_value = 0
pb.SS_GLOBAL_ACTVRECORDGETLIST_ACTIVITYID_FIELD.type = 5
pb.SS_GLOBAL_ACTVRECORDGETLIST_ACTIVITYID_FIELD.cpp_type = 1

pb.SS_GLOBAL_ACTVRECORDGETLIST.name = "SS_Global_ActvRecordGetList"
pb.SS_GLOBAL_ACTVRECORDGETLIST.full_name = ".SS_Global_ActvRecordGetList"
pb.SS_GLOBAL_ACTVRECORDGETLIST.nested_types = {}
pb.SS_GLOBAL_ACTVRECORDGETLIST.enum_types = {}
pb.SS_GLOBAL_ACTVRECORDGETLIST.fields = {pb.SS_GLOBAL_ACTVRECORDGETLIST_ACTORID_FIELD, pb.SS_GLOBAL_ACTVRECORDGETLIST_ACTIVITYID_FIELD}
pb.SS_GLOBAL_ACTVRECORDGETLIST.is_extendable = false
pb.SS_GLOBAL_ACTVRECORDGETLIST.extensions = {}
pb.SS_GLOBAL_SOLOGAMEGETWINCOUNT_ACTORID_FIELD.name = "ActorID"
pb.SS_GLOBAL_SOLOGAMEGETWINCOUNT_ACTORID_FIELD.full_name = ".SS_Global_SoloGameGetWinCount.ActorID"
pb.SS_GLOBAL_SOLOGAMEGETWINCOUNT_ACTORID_FIELD.number = 1
pb.SS_GLOBAL_SOLOGAMEGETWINCOUNT_ACTORID_FIELD.index = 0
pb.SS_GLOBAL_SOLOGAMEGETWINCOUNT_ACTORID_FIELD.label = 1
pb.SS_GLOBAL_SOLOGAMEGETWINCOUNT_ACTORID_FIELD.has_default_value = false
pb.SS_GLOBAL_SOLOGAMEGETWINCOUNT_ACTORID_FIELD.default_value = 0
pb.SS_GLOBAL_SOLOGAMEGETWINCOUNT_ACTORID_FIELD.type = 5
pb.SS_GLOBAL_SOLOGAMEGETWINCOUNT_ACTORID_FIELD.cpp_type = 1

pb.SS_GLOBAL_SOLOGAMEGETWINCOUNT_ZONEID_FIELD.name = "ZoneID"
pb.SS_GLOBAL_SOLOGAMEGETWINCOUNT_ZONEID_FIELD.full_name = ".SS_Global_SoloGameGetWinCount.ZoneID"
pb.SS_GLOBAL_SOLOGAMEGETWINCOUNT_ZONEID_FIELD.number = 2
pb.SS_GLOBAL_SOLOGAMEGETWINCOUNT_ZONEID_FIELD.index = 1
pb.SS_GLOBAL_SOLOGAMEGETWINCOUNT_ZONEID_FIELD.label = 1
pb.SS_GLOBAL_SOLOGAMEGETWINCOUNT_ZONEID_FIELD.has_default_value = false
pb.SS_GLOBAL_SOLOGAMEGETWINCOUNT_ZONEID_FIELD.default_value = 0
pb.SS_GLOBAL_SOLOGAMEGETWINCOUNT_ZONEID_FIELD.type = 5
pb.SS_GLOBAL_SOLOGAMEGETWINCOUNT_ZONEID_FIELD.cpp_type = 1

pb.SS_GLOBAL_SOLOGAMEGETWINCOUNT_PARAM_FIELD.name = "Param"
pb.SS_GLOBAL_SOLOGAMEGETWINCOUNT_PARAM_FIELD.full_name = ".SS_Global_SoloGameGetWinCount.Param"
pb.SS_GLOBAL_SOLOGAMEGETWINCOUNT_PARAM_FIELD.number = 3
pb.SS_GLOBAL_SOLOGAMEGETWINCOUNT_PARAM_FIELD.index = 2
pb.SS_GLOBAL_SOLOGAMEGETWINCOUNT_PARAM_FIELD.label = 1
pb.SS_GLOBAL_SOLOGAMEGETWINCOUNT_PARAM_FIELD.has_default_value = false
pb.SS_GLOBAL_SOLOGAMEGETWINCOUNT_PARAM_FIELD.default_value = 0
pb.SS_GLOBAL_SOLOGAMEGETWINCOUNT_PARAM_FIELD.type = 5
pb.SS_GLOBAL_SOLOGAMEGETWINCOUNT_PARAM_FIELD.cpp_type = 1

pb.SS_GLOBAL_SOLOGAMEGETWINCOUNT_COUNT_FIELD.name = "Count"
pb.SS_GLOBAL_SOLOGAMEGETWINCOUNT_COUNT_FIELD.full_name = ".SS_Global_SoloGameGetWinCount.Count"
pb.SS_GLOBAL_SOLOGAMEGETWINCOUNT_COUNT_FIELD.number = 4
pb.SS_GLOBAL_SOLOGAMEGETWINCOUNT_COUNT_FIELD.index = 3
pb.SS_GLOBAL_SOLOGAMEGETWINCOUNT_COUNT_FIELD.label = 1
pb.SS_GLOBAL_SOLOGAMEGETWINCOUNT_COUNT_FIELD.has_default_value = false
pb.SS_GLOBAL_SOLOGAMEGETWINCOUNT_COUNT_FIELD.default_value = 0
pb.SS_GLOBAL_SOLOGAMEGETWINCOUNT_COUNT_FIELD.type = 5
pb.SS_GLOBAL_SOLOGAMEGETWINCOUNT_COUNT_FIELD.cpp_type = 1

pb.SS_GLOBAL_SOLOGAMEGETWINCOUNT.name = "SS_Global_SoloGameGetWinCount"
pb.SS_GLOBAL_SOLOGAMEGETWINCOUNT.full_name = ".SS_Global_SoloGameGetWinCount"
pb.SS_GLOBAL_SOLOGAMEGETWINCOUNT.nested_types = {}
pb.SS_GLOBAL_SOLOGAMEGETWINCOUNT.enum_types = {}
pb.SS_GLOBAL_SOLOGAMEGETWINCOUNT.fields = {pb.SS_GLOBAL_SOLOGAMEGETWINCOUNT_ACTORID_FIELD, pb.SS_GLOBAL_SOLOGAMEGETWINCOUNT_ZONEID_FIELD, pb.SS_GLOBAL_SOLOGAMEGETWINCOUNT_PARAM_FIELD, pb.SS_GLOBAL_SOLOGAMEGETWINCOUNT_COUNT_FIELD}
pb.SS_GLOBAL_SOLOGAMEGETWINCOUNT.is_extendable = false
pb.SS_GLOBAL_SOLOGAMEGETWINCOUNT.extensions = {}
pb.CS_GLOBAL_GAMEECTYPEENTER_CONFIGID_FIELD.name = "ConfigID"
pb.CS_GLOBAL_GAMEECTYPEENTER_CONFIGID_FIELD.full_name = ".CS_Global_GameEctypeEnter.ConfigID"
pb.CS_GLOBAL_GAMEECTYPEENTER_CONFIGID_FIELD.number = 1
pb.CS_GLOBAL_GAMEECTYPEENTER_CONFIGID_FIELD.index = 0
pb.CS_GLOBAL_GAMEECTYPEENTER_CONFIGID_FIELD.label = 1
pb.CS_GLOBAL_GAMEECTYPEENTER_CONFIGID_FIELD.has_default_value = false
pb.CS_GLOBAL_GAMEECTYPEENTER_CONFIGID_FIELD.default_value = 0
pb.CS_GLOBAL_GAMEECTYPEENTER_CONFIGID_FIELD.type = 5
pb.CS_GLOBAL_GAMEECTYPEENTER_CONFIGID_FIELD.cpp_type = 1

pb.CS_GLOBAL_GAMEECTYPEENTER_PARAM_FIELD.name = "Param"
pb.CS_GLOBAL_GAMEECTYPEENTER_PARAM_FIELD.full_name = ".CS_Global_GameEctypeEnter.Param"
pb.CS_GLOBAL_GAMEECTYPEENTER_PARAM_FIELD.number = 2
pb.CS_GLOBAL_GAMEECTYPEENTER_PARAM_FIELD.index = 1
pb.CS_GLOBAL_GAMEECTYPEENTER_PARAM_FIELD.label = 1
pb.CS_GLOBAL_GAMEECTYPEENTER_PARAM_FIELD.has_default_value = false
pb.CS_GLOBAL_GAMEECTYPEENTER_PARAM_FIELD.default_value = 0
pb.CS_GLOBAL_GAMEECTYPEENTER_PARAM_FIELD.type = 5
pb.CS_GLOBAL_GAMEECTYPEENTER_PARAM_FIELD.cpp_type = 1

pb.CS_GLOBAL_GAMEECTYPEENTER.name = "CS_Global_GameEctypeEnter"
pb.CS_GLOBAL_GAMEECTYPEENTER.full_name = ".CS_Global_GameEctypeEnter"
pb.CS_GLOBAL_GAMEECTYPEENTER.nested_types = {}
pb.CS_GLOBAL_GAMEECTYPEENTER.enum_types = {}
pb.CS_GLOBAL_GAMEECTYPEENTER.fields = {pb.CS_GLOBAL_GAMEECTYPEENTER_CONFIGID_FIELD, pb.CS_GLOBAL_GAMEECTYPEENTER_PARAM_FIELD}
pb.CS_GLOBAL_GAMEECTYPEENTER.is_extendable = false
pb.CS_GLOBAL_GAMEECTYPEENTER.extensions = {}
pb.SS_GLOBAL_GAMEECTYPEENTER_ACTORID_FIELD.name = "ActorID"
pb.SS_GLOBAL_GAMEECTYPEENTER_ACTORID_FIELD.full_name = ".SS_Global_GameEctypeEnter.ActorID"
pb.SS_GLOBAL_GAMEECTYPEENTER_ACTORID_FIELD.number = 1
pb.SS_GLOBAL_GAMEECTYPEENTER_ACTORID_FIELD.index = 0
pb.SS_GLOBAL_GAMEECTYPEENTER_ACTORID_FIELD.label = 1
pb.SS_GLOBAL_GAMEECTYPEENTER_ACTORID_FIELD.has_default_value = false
pb.SS_GLOBAL_GAMEECTYPEENTER_ACTORID_FIELD.default_value = 0
pb.SS_GLOBAL_GAMEECTYPEENTER_ACTORID_FIELD.type = 5
pb.SS_GLOBAL_GAMEECTYPEENTER_ACTORID_FIELD.cpp_type = 1

pb.SS_GLOBAL_GAMEECTYPEENTER_CONFIGID_FIELD.name = "ConfigID"
pb.SS_GLOBAL_GAMEECTYPEENTER_CONFIGID_FIELD.full_name = ".SS_Global_GameEctypeEnter.ConfigID"
pb.SS_GLOBAL_GAMEECTYPEENTER_CONFIGID_FIELD.number = 2
pb.SS_GLOBAL_GAMEECTYPEENTER_CONFIGID_FIELD.index = 1
pb.SS_GLOBAL_GAMEECTYPEENTER_CONFIGID_FIELD.label = 1
pb.SS_GLOBAL_GAMEECTYPEENTER_CONFIGID_FIELD.has_default_value = false
pb.SS_GLOBAL_GAMEECTYPEENTER_CONFIGID_FIELD.default_value = 0
pb.SS_GLOBAL_GAMEECTYPEENTER_CONFIGID_FIELD.type = 5
pb.SS_GLOBAL_GAMEECTYPEENTER_CONFIGID_FIELD.cpp_type = 1

pb.SS_GLOBAL_GAMEECTYPEENTER_PARAM_FIELD.name = "Param"
pb.SS_GLOBAL_GAMEECTYPEENTER_PARAM_FIELD.full_name = ".SS_Global_GameEctypeEnter.Param"
pb.SS_GLOBAL_GAMEECTYPEENTER_PARAM_FIELD.number = 3
pb.SS_GLOBAL_GAMEECTYPEENTER_PARAM_FIELD.index = 2
pb.SS_GLOBAL_GAMEECTYPEENTER_PARAM_FIELD.label = 1
pb.SS_GLOBAL_GAMEECTYPEENTER_PARAM_FIELD.has_default_value = false
pb.SS_GLOBAL_GAMEECTYPEENTER_PARAM_FIELD.default_value = 0
pb.SS_GLOBAL_GAMEECTYPEENTER_PARAM_FIELD.type = 5
pb.SS_GLOBAL_GAMEECTYPEENTER_PARAM_FIELD.cpp_type = 1

pb.SS_GLOBAL_GAMEECTYPEENTER.name = "SS_Global_GameEctypeEnter"
pb.SS_GLOBAL_GAMEECTYPEENTER.full_name = ".SS_Global_GameEctypeEnter"
pb.SS_GLOBAL_GAMEECTYPEENTER.nested_types = {}
pb.SS_GLOBAL_GAMEECTYPEENTER.enum_types = {}
pb.SS_GLOBAL_GAMEECTYPEENTER.fields = {pb.SS_GLOBAL_GAMEECTYPEENTER_ACTORID_FIELD, pb.SS_GLOBAL_GAMEECTYPEENTER_CONFIGID_FIELD, pb.SS_GLOBAL_GAMEECTYPEENTER_PARAM_FIELD}
pb.SS_GLOBAL_GAMEECTYPEENTER.is_extendable = false
pb.SS_GLOBAL_GAMEECTYPEENTER.extensions = {}
pb.CS_GLOBAL_GAMEECTYPELEAVE_ACTORID_FIELD.name = "ActorID"
pb.CS_GLOBAL_GAMEECTYPELEAVE_ACTORID_FIELD.full_name = ".CS_Global_GameEctypeLeave.ActorID"
pb.CS_GLOBAL_GAMEECTYPELEAVE_ACTORID_FIELD.number = 1
pb.CS_GLOBAL_GAMEECTYPELEAVE_ACTORID_FIELD.index = 0
pb.CS_GLOBAL_GAMEECTYPELEAVE_ACTORID_FIELD.label = 1
pb.CS_GLOBAL_GAMEECTYPELEAVE_ACTORID_FIELD.has_default_value = false
pb.CS_GLOBAL_GAMEECTYPELEAVE_ACTORID_FIELD.default_value = 0
pb.CS_GLOBAL_GAMEECTYPELEAVE_ACTORID_FIELD.type = 5
pb.CS_GLOBAL_GAMEECTYPELEAVE_ACTORID_FIELD.cpp_type = 1

pb.CS_GLOBAL_GAMEECTYPELEAVE.name = "CS_Global_GameEctypeLeave"
pb.CS_GLOBAL_GAMEECTYPELEAVE.full_name = ".CS_Global_GameEctypeLeave"
pb.CS_GLOBAL_GAMEECTYPELEAVE.nested_types = {}
pb.CS_GLOBAL_GAMEECTYPELEAVE.enum_types = {}
pb.CS_GLOBAL_GAMEECTYPELEAVE.fields = {pb.CS_GLOBAL_GAMEECTYPELEAVE_ACTORID_FIELD}
pb.CS_GLOBAL_GAMEECTYPELEAVE.is_extendable = false
pb.CS_GLOBAL_GAMEECTYPELEAVE.extensions = {}
pb.SS_GLOBAL_STOPACTV_ACTVID_FIELD.name = "ActvID"
pb.SS_GLOBAL_STOPACTV_ACTVID_FIELD.full_name = ".SS_Global_StopActv.ActvID"
pb.SS_GLOBAL_STOPACTV_ACTVID_FIELD.number = 1
pb.SS_GLOBAL_STOPACTV_ACTVID_FIELD.index = 0
pb.SS_GLOBAL_STOPACTV_ACTVID_FIELD.label = 1
pb.SS_GLOBAL_STOPACTV_ACTVID_FIELD.has_default_value = false
pb.SS_GLOBAL_STOPACTV_ACTVID_FIELD.default_value = 0
pb.SS_GLOBAL_STOPACTV_ACTVID_FIELD.type = 5
pb.SS_GLOBAL_STOPACTV_ACTVID_FIELD.cpp_type = 1

pb.SS_GLOBAL_STOPACTV.name = "SS_Global_StopActv"
pb.SS_GLOBAL_STOPACTV.full_name = ".SS_Global_StopActv"
pb.SS_GLOBAL_STOPACTV.nested_types = {}
pb.SS_GLOBAL_STOPACTV.enum_types = {}
pb.SS_GLOBAL_STOPACTV.fields = {pb.SS_GLOBAL_STOPACTV_ACTVID_FIELD}
pb.SS_GLOBAL_STOPACTV.is_extendable = false
pb.SS_GLOBAL_STOPACTV.extensions = {}
pb.SS_GLOBALGAME_SETCOUNTRYDATA_OFFICEDATA_ACTORID_FIELD.name = "ActorID"
pb.SS_GLOBALGAME_SETCOUNTRYDATA_OFFICEDATA_ACTORID_FIELD.full_name = ".SS_GlobalGame_SetCountryData.OfficeData.ActorID"
pb.SS_GLOBALGAME_SETCOUNTRYDATA_OFFICEDATA_ACTORID_FIELD.number = 1
pb.SS_GLOBALGAME_SETCOUNTRYDATA_OFFICEDATA_ACTORID_FIELD.index = 0
pb.SS_GLOBALGAME_SETCOUNTRYDATA_OFFICEDATA_ACTORID_FIELD.label = 1
pb.SS_GLOBALGAME_SETCOUNTRYDATA_OFFICEDATA_ACTORID_FIELD.has_default_value = false
pb.SS_GLOBALGAME_SETCOUNTRYDATA_OFFICEDATA_ACTORID_FIELD.default_value = 0
pb.SS_GLOBALGAME_SETCOUNTRYDATA_OFFICEDATA_ACTORID_FIELD.type = 5
pb.SS_GLOBALGAME_SETCOUNTRYDATA_OFFICEDATA_ACTORID_FIELD.cpp_type = 1

pb.SS_GLOBALGAME_SETCOUNTRYDATA_OFFICEDATA_ACTORNAME_FIELD.name = "ActorName"
pb.SS_GLOBALGAME_SETCOUNTRYDATA_OFFICEDATA_ACTORNAME_FIELD.full_name = ".SS_GlobalGame_SetCountryData.OfficeData.ActorName"
pb.SS_GLOBALGAME_SETCOUNTRYDATA_OFFICEDATA_ACTORNAME_FIELD.number = 2
pb.SS_GLOBALGAME_SETCOUNTRYDATA_OFFICEDATA_ACTORNAME_FIELD.index = 1
pb.SS_GLOBALGAME_SETCOUNTRYDATA_OFFICEDATA_ACTORNAME_FIELD.label = 1
pb.SS_GLOBALGAME_SETCOUNTRYDATA_OFFICEDATA_ACTORNAME_FIELD.has_default_value = false
pb.SS_GLOBALGAME_SETCOUNTRYDATA_OFFICEDATA_ACTORNAME_FIELD.default_value = ""
pb.SS_GLOBALGAME_SETCOUNTRYDATA_OFFICEDATA_ACTORNAME_FIELD.type = 9
pb.SS_GLOBALGAME_SETCOUNTRYDATA_OFFICEDATA_ACTORNAME_FIELD.cpp_type = 9

pb.SS_GLOBALGAME_SETCOUNTRYDATA_OFFICEDATA_SOCIETYNAME_FIELD.name = "SocietyName"
pb.SS_GLOBALGAME_SETCOUNTRYDATA_OFFICEDATA_SOCIETYNAME_FIELD.full_name = ".SS_GlobalGame_SetCountryData.OfficeData.SocietyName"
pb.SS_GLOBALGAME_SETCOUNTRYDATA_OFFICEDATA_SOCIETYNAME_FIELD.number = 3
pb.SS_GLOBALGAME_SETCOUNTRYDATA_OFFICEDATA_SOCIETYNAME_FIELD.index = 2
pb.SS_GLOBALGAME_SETCOUNTRYDATA_OFFICEDATA_SOCIETYNAME_FIELD.label = 1
pb.SS_GLOBALGAME_SETCOUNTRYDATA_OFFICEDATA_SOCIETYNAME_FIELD.has_default_value = false
pb.SS_GLOBALGAME_SETCOUNTRYDATA_OFFICEDATA_SOCIETYNAME_FIELD.default_value = ""
pb.SS_GLOBALGAME_SETCOUNTRYDATA_OFFICEDATA_SOCIETYNAME_FIELD.type = 9
pb.SS_GLOBALGAME_SETCOUNTRYDATA_OFFICEDATA_SOCIETYNAME_FIELD.cpp_type = 9

pb.SS_GLOBALGAME_SETCOUNTRYDATA_OFFICEDATA_LEVEL_FIELD.name = "Level"
pb.SS_GLOBALGAME_SETCOUNTRYDATA_OFFICEDATA_LEVEL_FIELD.full_name = ".SS_GlobalGame_SetCountryData.OfficeData.Level"
pb.SS_GLOBALGAME_SETCOUNTRYDATA_OFFICEDATA_LEVEL_FIELD.number = 4
pb.SS_GLOBALGAME_SETCOUNTRYDATA_OFFICEDATA_LEVEL_FIELD.index = 3
pb.SS_GLOBALGAME_SETCOUNTRYDATA_OFFICEDATA_LEVEL_FIELD.label = 1
pb.SS_GLOBALGAME_SETCOUNTRYDATA_OFFICEDATA_LEVEL_FIELD.has_default_value = false
pb.SS_GLOBALGAME_SETCOUNTRYDATA_OFFICEDATA_LEVEL_FIELD.default_value = 0
pb.SS_GLOBALGAME_SETCOUNTRYDATA_OFFICEDATA_LEVEL_FIELD.type = 5
pb.SS_GLOBALGAME_SETCOUNTRYDATA_OFFICEDATA_LEVEL_FIELD.cpp_type = 1

pb.SS_GLOBALGAME_SETCOUNTRYDATA_OFFICEDATA_VOCATION_FIELD.name = "Vocation"
pb.SS_GLOBALGAME_SETCOUNTRYDATA_OFFICEDATA_VOCATION_FIELD.full_name = ".SS_GlobalGame_SetCountryData.OfficeData.Vocation"
pb.SS_GLOBALGAME_SETCOUNTRYDATA_OFFICEDATA_VOCATION_FIELD.number = 5
pb.SS_GLOBALGAME_SETCOUNTRYDATA_OFFICEDATA_VOCATION_FIELD.index = 4
pb.SS_GLOBALGAME_SETCOUNTRYDATA_OFFICEDATA_VOCATION_FIELD.label = 1
pb.SS_GLOBALGAME_SETCOUNTRYDATA_OFFICEDATA_VOCATION_FIELD.has_default_value = false
pb.SS_GLOBALGAME_SETCOUNTRYDATA_OFFICEDATA_VOCATION_FIELD.default_value = 0
pb.SS_GLOBALGAME_SETCOUNTRYDATA_OFFICEDATA_VOCATION_FIELD.type = 5
pb.SS_GLOBALGAME_SETCOUNTRYDATA_OFFICEDATA_VOCATION_FIELD.cpp_type = 1

pb.SS_GLOBALGAME_SETCOUNTRYDATA_OFFICEDATA_POWER_FIELD.name = "Power"
pb.SS_GLOBALGAME_SETCOUNTRYDATA_OFFICEDATA_POWER_FIELD.full_name = ".SS_GlobalGame_SetCountryData.OfficeData.Power"
pb.SS_GLOBALGAME_SETCOUNTRYDATA_OFFICEDATA_POWER_FIELD.number = 6
pb.SS_GLOBALGAME_SETCOUNTRYDATA_OFFICEDATA_POWER_FIELD.index = 5
pb.SS_GLOBALGAME_SETCOUNTRYDATA_OFFICEDATA_POWER_FIELD.label = 1
pb.SS_GLOBALGAME_SETCOUNTRYDATA_OFFICEDATA_POWER_FIELD.has_default_value = false
pb.SS_GLOBALGAME_SETCOUNTRYDATA_OFFICEDATA_POWER_FIELD.default_value = 0
pb.SS_GLOBALGAME_SETCOUNTRYDATA_OFFICEDATA_POWER_FIELD.type = 5
pb.SS_GLOBALGAME_SETCOUNTRYDATA_OFFICEDATA_POWER_FIELD.cpp_type = 1

pb.SS_GLOBALGAME_SETCOUNTRYDATA_OFFICEDATA_OFFICETYPE_FIELD.name = "OfficeType"
pb.SS_GLOBALGAME_SETCOUNTRYDATA_OFFICEDATA_OFFICETYPE_FIELD.full_name = ".SS_GlobalGame_SetCountryData.OfficeData.OfficeType"
pb.SS_GLOBALGAME_SETCOUNTRYDATA_OFFICEDATA_OFFICETYPE_FIELD.number = 7
pb.SS_GLOBALGAME_SETCOUNTRYDATA_OFFICEDATA_OFFICETYPE_FIELD.index = 6
pb.SS_GLOBALGAME_SETCOUNTRYDATA_OFFICEDATA_OFFICETYPE_FIELD.label = 1
pb.SS_GLOBALGAME_SETCOUNTRYDATA_OFFICEDATA_OFFICETYPE_FIELD.has_default_value = false
pb.SS_GLOBALGAME_SETCOUNTRYDATA_OFFICEDATA_OFFICETYPE_FIELD.default_value = 0
pb.SS_GLOBALGAME_SETCOUNTRYDATA_OFFICEDATA_OFFICETYPE_FIELD.type = 5
pb.SS_GLOBALGAME_SETCOUNTRYDATA_OFFICEDATA_OFFICETYPE_FIELD.cpp_type = 1

pb.SS_GLOBALGAME_SETCOUNTRYDATA_OFFICEDATA.name = "OfficeData"
pb.SS_GLOBALGAME_SETCOUNTRYDATA_OFFICEDATA.full_name = ".SS_GlobalGame_SetCountryData.OfficeData"
pb.SS_GLOBALGAME_SETCOUNTRYDATA_OFFICEDATA.nested_types = {}
pb.SS_GLOBALGAME_SETCOUNTRYDATA_OFFICEDATA.enum_types = {}
pb.SS_GLOBALGAME_SETCOUNTRYDATA_OFFICEDATA.fields = {pb.SS_GLOBALGAME_SETCOUNTRYDATA_OFFICEDATA_ACTORID_FIELD, pb.SS_GLOBALGAME_SETCOUNTRYDATA_OFFICEDATA_ACTORNAME_FIELD, pb.SS_GLOBALGAME_SETCOUNTRYDATA_OFFICEDATA_SOCIETYNAME_FIELD, pb.SS_GLOBALGAME_SETCOUNTRYDATA_OFFICEDATA_LEVEL_FIELD, pb.SS_GLOBALGAME_SETCOUNTRYDATA_OFFICEDATA_VOCATION_FIELD, pb.SS_GLOBALGAME_SETCOUNTRYDATA_OFFICEDATA_POWER_FIELD, pb.SS_GLOBALGAME_SETCOUNTRYDATA_OFFICEDATA_OFFICETYPE_FIELD}
pb.SS_GLOBALGAME_SETCOUNTRYDATA_OFFICEDATA.is_extendable = false
pb.SS_GLOBALGAME_SETCOUNTRYDATA_OFFICEDATA.extensions = {}
pb.SS_GLOBALGAME_SETCOUNTRYDATA_OFFICEDATA.containing_type = pb.SS_GLOBALGAME_SETCOUNTRYDATA
pb.SS_GLOBALGAME_SETCOUNTRYDATA_ZONEID_FIELD.name = "ZoneID"
pb.SS_GLOBALGAME_SETCOUNTRYDATA_ZONEID_FIELD.full_name = ".SS_GlobalGame_SetCountryData.ZoneID"
pb.SS_GLOBALGAME_SETCOUNTRYDATA_ZONEID_FIELD.number = 1
pb.SS_GLOBALGAME_SETCOUNTRYDATA_ZONEID_FIELD.index = 0
pb.SS_GLOBALGAME_SETCOUNTRYDATA_ZONEID_FIELD.label = 1
pb.SS_GLOBALGAME_SETCOUNTRYDATA_ZONEID_FIELD.has_default_value = false
pb.SS_GLOBALGAME_SETCOUNTRYDATA_ZONEID_FIELD.default_value = 0
pb.SS_GLOBALGAME_SETCOUNTRYDATA_ZONEID_FIELD.type = 5
pb.SS_GLOBALGAME_SETCOUNTRYDATA_ZONEID_FIELD.cpp_type = 1

pb.SS_GLOBALGAME_SETCOUNTRYDATA_COUNTRYID_FIELD.name = "CountryID"
pb.SS_GLOBALGAME_SETCOUNTRYDATA_COUNTRYID_FIELD.full_name = ".SS_GlobalGame_SetCountryData.CountryID"
pb.SS_GLOBALGAME_SETCOUNTRYDATA_COUNTRYID_FIELD.number = 2
pb.SS_GLOBALGAME_SETCOUNTRYDATA_COUNTRYID_FIELD.index = 1
pb.SS_GLOBALGAME_SETCOUNTRYDATA_COUNTRYID_FIELD.label = 1
pb.SS_GLOBALGAME_SETCOUNTRYDATA_COUNTRYID_FIELD.has_default_value = false
pb.SS_GLOBALGAME_SETCOUNTRYDATA_COUNTRYID_FIELD.default_value = 0
pb.SS_GLOBALGAME_SETCOUNTRYDATA_COUNTRYID_FIELD.type = 5
pb.SS_GLOBALGAME_SETCOUNTRYDATA_COUNTRYID_FIELD.cpp_type = 1

pb.SS_GLOBALGAME_SETCOUNTRYDATA_COUNTRYPOWER_FIELD.name = "CountryPower"
pb.SS_GLOBALGAME_SETCOUNTRYDATA_COUNTRYPOWER_FIELD.full_name = ".SS_GlobalGame_SetCountryData.CountryPower"
pb.SS_GLOBALGAME_SETCOUNTRYDATA_COUNTRYPOWER_FIELD.number = 3
pb.SS_GLOBALGAME_SETCOUNTRYDATA_COUNTRYPOWER_FIELD.index = 2
pb.SS_GLOBALGAME_SETCOUNTRYDATA_COUNTRYPOWER_FIELD.label = 1
pb.SS_GLOBALGAME_SETCOUNTRYDATA_COUNTRYPOWER_FIELD.has_default_value = false
pb.SS_GLOBALGAME_SETCOUNTRYDATA_COUNTRYPOWER_FIELD.default_value = 0
pb.SS_GLOBALGAME_SETCOUNTRYDATA_COUNTRYPOWER_FIELD.type = 5
pb.SS_GLOBALGAME_SETCOUNTRYDATA_COUNTRYPOWER_FIELD.cpp_type = 1

pb.SS_GLOBALGAME_SETCOUNTRYDATA_COUNTRYMONEY_FIELD.name = "CountryMoney"
pb.SS_GLOBALGAME_SETCOUNTRYDATA_COUNTRYMONEY_FIELD.full_name = ".SS_GlobalGame_SetCountryData.CountryMoney"
pb.SS_GLOBALGAME_SETCOUNTRYDATA_COUNTRYMONEY_FIELD.number = 4
pb.SS_GLOBALGAME_SETCOUNTRYDATA_COUNTRYMONEY_FIELD.index = 3
pb.SS_GLOBALGAME_SETCOUNTRYDATA_COUNTRYMONEY_FIELD.label = 1
pb.SS_GLOBALGAME_SETCOUNTRYDATA_COUNTRYMONEY_FIELD.has_default_value = false
pb.SS_GLOBALGAME_SETCOUNTRYDATA_COUNTRYMONEY_FIELD.default_value = 0
pb.SS_GLOBALGAME_SETCOUNTRYDATA_COUNTRYMONEY_FIELD.type = 5
pb.SS_GLOBALGAME_SETCOUNTRYDATA_COUNTRYMONEY_FIELD.cpp_type = 1

pb.SS_GLOBALGAME_SETCOUNTRYDATA_OFFICERLIST_FIELD.name = "OfficerList"
pb.SS_GLOBALGAME_SETCOUNTRYDATA_OFFICERLIST_FIELD.full_name = ".SS_GlobalGame_SetCountryData.OfficerList"
pb.SS_GLOBALGAME_SETCOUNTRYDATA_OFFICERLIST_FIELD.number = 5
pb.SS_GLOBALGAME_SETCOUNTRYDATA_OFFICERLIST_FIELD.index = 4
pb.SS_GLOBALGAME_SETCOUNTRYDATA_OFFICERLIST_FIELD.label = 3
pb.SS_GLOBALGAME_SETCOUNTRYDATA_OFFICERLIST_FIELD.has_default_value = false
pb.SS_GLOBALGAME_SETCOUNTRYDATA_OFFICERLIST_FIELD.default_value = {}
pb.SS_GLOBALGAME_SETCOUNTRYDATA_OFFICERLIST_FIELD.message_type = pb.SS_GLOBALGAME_SETCOUNTRYDATA_OFFICEDATA
pb.SS_GLOBALGAME_SETCOUNTRYDATA_OFFICERLIST_FIELD.type = 11
pb.SS_GLOBALGAME_SETCOUNTRYDATA_OFFICERLIST_FIELD.cpp_type = 10

pb.SS_GLOBALGAME_SETCOUNTRYDATA.name = "SS_GlobalGame_SetCountryData"
pb.SS_GLOBALGAME_SETCOUNTRYDATA.full_name = ".SS_GlobalGame_SetCountryData"
pb.SS_GLOBALGAME_SETCOUNTRYDATA.nested_types = {pb.SS_GLOBALGAME_SETCOUNTRYDATA_OFFICEDATA}
pb.SS_GLOBALGAME_SETCOUNTRYDATA.enum_types = {}
pb.SS_GLOBALGAME_SETCOUNTRYDATA.fields = {pb.SS_GLOBALGAME_SETCOUNTRYDATA_ZONEID_FIELD, pb.SS_GLOBALGAME_SETCOUNTRYDATA_COUNTRYID_FIELD, pb.SS_GLOBALGAME_SETCOUNTRYDATA_COUNTRYPOWER_FIELD, pb.SS_GLOBALGAME_SETCOUNTRYDATA_COUNTRYMONEY_FIELD, pb.SS_GLOBALGAME_SETCOUNTRYDATA_OFFICERLIST_FIELD}
pb.SS_GLOBALGAME_SETCOUNTRYDATA.is_extendable = false
pb.SS_GLOBALGAME_SETCOUNTRYDATA.extensions = {}
pb.SS_GLOBALGAME_SETSOCIETYDATA_ZONEID_FIELD.name = "ZoneID"
pb.SS_GLOBALGAME_SETSOCIETYDATA_ZONEID_FIELD.full_name = ".SS_GlobalGame_SetSocietyData.ZoneID"
pb.SS_GLOBALGAME_SETSOCIETYDATA_ZONEID_FIELD.number = 1
pb.SS_GLOBALGAME_SETSOCIETYDATA_ZONEID_FIELD.index = 0
pb.SS_GLOBALGAME_SETSOCIETYDATA_ZONEID_FIELD.label = 1
pb.SS_GLOBALGAME_SETSOCIETYDATA_ZONEID_FIELD.has_default_value = false
pb.SS_GLOBALGAME_SETSOCIETYDATA_ZONEID_FIELD.default_value = 0
pb.SS_GLOBALGAME_SETSOCIETYDATA_ZONEID_FIELD.type = 5
pb.SS_GLOBALGAME_SETSOCIETYDATA_ZONEID_FIELD.cpp_type = 1

pb.SS_GLOBALGAME_SETSOCIETYDATA_SOCIETYID_FIELD.name = "SocietyID"
pb.SS_GLOBALGAME_SETSOCIETYDATA_SOCIETYID_FIELD.full_name = ".SS_GlobalGame_SetSocietyData.SocietyID"
pb.SS_GLOBALGAME_SETSOCIETYDATA_SOCIETYID_FIELD.number = 2
pb.SS_GLOBALGAME_SETSOCIETYDATA_SOCIETYID_FIELD.index = 1
pb.SS_GLOBALGAME_SETSOCIETYDATA_SOCIETYID_FIELD.label = 1
pb.SS_GLOBALGAME_SETSOCIETYDATA_SOCIETYID_FIELD.has_default_value = false
pb.SS_GLOBALGAME_SETSOCIETYDATA_SOCIETYID_FIELD.default_value = 0
pb.SS_GLOBALGAME_SETSOCIETYDATA_SOCIETYID_FIELD.type = 5
pb.SS_GLOBALGAME_SETSOCIETYDATA_SOCIETYID_FIELD.cpp_type = 1

pb.SS_GLOBALGAME_SETSOCIETYDATA_SOCIETYNAME_FIELD.name = "SocietyName"
pb.SS_GLOBALGAME_SETSOCIETYDATA_SOCIETYNAME_FIELD.full_name = ".SS_GlobalGame_SetSocietyData.SocietyName"
pb.SS_GLOBALGAME_SETSOCIETYDATA_SOCIETYNAME_FIELD.number = 3
pb.SS_GLOBALGAME_SETSOCIETYDATA_SOCIETYNAME_FIELD.index = 2
pb.SS_GLOBALGAME_SETSOCIETYDATA_SOCIETYNAME_FIELD.label = 1
pb.SS_GLOBALGAME_SETSOCIETYDATA_SOCIETYNAME_FIELD.has_default_value = false
pb.SS_GLOBALGAME_SETSOCIETYDATA_SOCIETYNAME_FIELD.default_value = ""
pb.SS_GLOBALGAME_SETSOCIETYDATA_SOCIETYNAME_FIELD.type = 9
pb.SS_GLOBALGAME_SETSOCIETYDATA_SOCIETYNAME_FIELD.cpp_type = 9

pb.SS_GLOBALGAME_SETSOCIETYDATA_SOCIETYLEVEL_FIELD.name = "SocietyLevel"
pb.SS_GLOBALGAME_SETSOCIETYDATA_SOCIETYLEVEL_FIELD.full_name = ".SS_GlobalGame_SetSocietyData.SocietyLevel"
pb.SS_GLOBALGAME_SETSOCIETYDATA_SOCIETYLEVEL_FIELD.number = 4
pb.SS_GLOBALGAME_SETSOCIETYDATA_SOCIETYLEVEL_FIELD.index = 3
pb.SS_GLOBALGAME_SETSOCIETYDATA_SOCIETYLEVEL_FIELD.label = 1
pb.SS_GLOBALGAME_SETSOCIETYDATA_SOCIETYLEVEL_FIELD.has_default_value = false
pb.SS_GLOBALGAME_SETSOCIETYDATA_SOCIETYLEVEL_FIELD.default_value = 0
pb.SS_GLOBALGAME_SETSOCIETYDATA_SOCIETYLEVEL_FIELD.type = 5
pb.SS_GLOBALGAME_SETSOCIETYDATA_SOCIETYLEVEL_FIELD.cpp_type = 1

pb.SS_GLOBALGAME_SETSOCIETYDATA.name = "SS_GlobalGame_SetSocietyData"
pb.SS_GLOBALGAME_SETSOCIETYDATA.full_name = ".SS_GlobalGame_SetSocietyData"
pb.SS_GLOBALGAME_SETSOCIETYDATA.nested_types = {}
pb.SS_GLOBALGAME_SETSOCIETYDATA.enum_types = {}
pb.SS_GLOBALGAME_SETSOCIETYDATA.fields = {pb.SS_GLOBALGAME_SETSOCIETYDATA_ZONEID_FIELD, pb.SS_GLOBALGAME_SETSOCIETYDATA_SOCIETYID_FIELD, pb.SS_GLOBALGAME_SETSOCIETYDATA_SOCIETYNAME_FIELD, pb.SS_GLOBALGAME_SETSOCIETYDATA_SOCIETYLEVEL_FIELD}
pb.SS_GLOBALGAME_SETSOCIETYDATA.is_extendable = false
pb.SS_GLOBALGAME_SETSOCIETYDATA.extensions = {}
pb.SS_GLOBALGAME_UPDATESOCIETYMEMBER_MEMBERDATA_ACTORID_FIELD.name = "ActorID"
pb.SS_GLOBALGAME_UPDATESOCIETYMEMBER_MEMBERDATA_ACTORID_FIELD.full_name = ".SS_GlobalGame_UpdateSocietyMember.MemberData.ActorID"
pb.SS_GLOBALGAME_UPDATESOCIETYMEMBER_MEMBERDATA_ACTORID_FIELD.number = 1
pb.SS_GLOBALGAME_UPDATESOCIETYMEMBER_MEMBERDATA_ACTORID_FIELD.index = 0
pb.SS_GLOBALGAME_UPDATESOCIETYMEMBER_MEMBERDATA_ACTORID_FIELD.label = 1
pb.SS_GLOBALGAME_UPDATESOCIETYMEMBER_MEMBERDATA_ACTORID_FIELD.has_default_value = false
pb.SS_GLOBALGAME_UPDATESOCIETYMEMBER_MEMBERDATA_ACTORID_FIELD.default_value = 0
pb.SS_GLOBALGAME_UPDATESOCIETYMEMBER_MEMBERDATA_ACTORID_FIELD.type = 5
pb.SS_GLOBALGAME_UPDATESOCIETYMEMBER_MEMBERDATA_ACTORID_FIELD.cpp_type = 1

pb.SS_GLOBALGAME_UPDATESOCIETYMEMBER_MEMBERDATA_ACTORNAME_FIELD.name = "ActorName"
pb.SS_GLOBALGAME_UPDATESOCIETYMEMBER_MEMBERDATA_ACTORNAME_FIELD.full_name = ".SS_GlobalGame_UpdateSocietyMember.MemberData.ActorName"
pb.SS_GLOBALGAME_UPDATESOCIETYMEMBER_MEMBERDATA_ACTORNAME_FIELD.number = 2
pb.SS_GLOBALGAME_UPDATESOCIETYMEMBER_MEMBERDATA_ACTORNAME_FIELD.index = 1
pb.SS_GLOBALGAME_UPDATESOCIETYMEMBER_MEMBERDATA_ACTORNAME_FIELD.label = 1
pb.SS_GLOBALGAME_UPDATESOCIETYMEMBER_MEMBERDATA_ACTORNAME_FIELD.has_default_value = false
pb.SS_GLOBALGAME_UPDATESOCIETYMEMBER_MEMBERDATA_ACTORNAME_FIELD.default_value = ""
pb.SS_GLOBALGAME_UPDATESOCIETYMEMBER_MEMBERDATA_ACTORNAME_FIELD.type = 9
pb.SS_GLOBALGAME_UPDATESOCIETYMEMBER_MEMBERDATA_ACTORNAME_FIELD.cpp_type = 9

pb.SS_GLOBALGAME_UPDATESOCIETYMEMBER_MEMBERDATA_LEVEL_FIELD.name = "Level"
pb.SS_GLOBALGAME_UPDATESOCIETYMEMBER_MEMBERDATA_LEVEL_FIELD.full_name = ".SS_GlobalGame_UpdateSocietyMember.MemberData.Level"
pb.SS_GLOBALGAME_UPDATESOCIETYMEMBER_MEMBERDATA_LEVEL_FIELD.number = 3
pb.SS_GLOBALGAME_UPDATESOCIETYMEMBER_MEMBERDATA_LEVEL_FIELD.index = 2
pb.SS_GLOBALGAME_UPDATESOCIETYMEMBER_MEMBERDATA_LEVEL_FIELD.label = 1
pb.SS_GLOBALGAME_UPDATESOCIETYMEMBER_MEMBERDATA_LEVEL_FIELD.has_default_value = false
pb.SS_GLOBALGAME_UPDATESOCIETYMEMBER_MEMBERDATA_LEVEL_FIELD.default_value = 0
pb.SS_GLOBALGAME_UPDATESOCIETYMEMBER_MEMBERDATA_LEVEL_FIELD.type = 5
pb.SS_GLOBALGAME_UPDATESOCIETYMEMBER_MEMBERDATA_LEVEL_FIELD.cpp_type = 1

pb.SS_GLOBALGAME_UPDATESOCIETYMEMBER_MEMBERDATA_VOCATION_FIELD.name = "Vocation"
pb.SS_GLOBALGAME_UPDATESOCIETYMEMBER_MEMBERDATA_VOCATION_FIELD.full_name = ".SS_GlobalGame_UpdateSocietyMember.MemberData.Vocation"
pb.SS_GLOBALGAME_UPDATESOCIETYMEMBER_MEMBERDATA_VOCATION_FIELD.number = 4
pb.SS_GLOBALGAME_UPDATESOCIETYMEMBER_MEMBERDATA_VOCATION_FIELD.index = 3
pb.SS_GLOBALGAME_UPDATESOCIETYMEMBER_MEMBERDATA_VOCATION_FIELD.label = 1
pb.SS_GLOBALGAME_UPDATESOCIETYMEMBER_MEMBERDATA_VOCATION_FIELD.has_default_value = false
pb.SS_GLOBALGAME_UPDATESOCIETYMEMBER_MEMBERDATA_VOCATION_FIELD.default_value = 0
pb.SS_GLOBALGAME_UPDATESOCIETYMEMBER_MEMBERDATA_VOCATION_FIELD.type = 5
pb.SS_GLOBALGAME_UPDATESOCIETYMEMBER_MEMBERDATA_VOCATION_FIELD.cpp_type = 1

pb.SS_GLOBALGAME_UPDATESOCIETYMEMBER_MEMBERDATA_POWER_FIELD.name = "Power"
pb.SS_GLOBALGAME_UPDATESOCIETYMEMBER_MEMBERDATA_POWER_FIELD.full_name = ".SS_GlobalGame_UpdateSocietyMember.MemberData.Power"
pb.SS_GLOBALGAME_UPDATESOCIETYMEMBER_MEMBERDATA_POWER_FIELD.number = 5
pb.SS_GLOBALGAME_UPDATESOCIETYMEMBER_MEMBERDATA_POWER_FIELD.index = 4
pb.SS_GLOBALGAME_UPDATESOCIETYMEMBER_MEMBERDATA_POWER_FIELD.label = 1
pb.SS_GLOBALGAME_UPDATESOCIETYMEMBER_MEMBERDATA_POWER_FIELD.has_default_value = false
pb.SS_GLOBALGAME_UPDATESOCIETYMEMBER_MEMBERDATA_POWER_FIELD.default_value = 0
pb.SS_GLOBALGAME_UPDATESOCIETYMEMBER_MEMBERDATA_POWER_FIELD.type = 5
pb.SS_GLOBALGAME_UPDATESOCIETYMEMBER_MEMBERDATA_POWER_FIELD.cpp_type = 1

pb.SS_GLOBALGAME_UPDATESOCIETYMEMBER_MEMBERDATA_TITLE_FIELD.name = "Title"
pb.SS_GLOBALGAME_UPDATESOCIETYMEMBER_MEMBERDATA_TITLE_FIELD.full_name = ".SS_GlobalGame_UpdateSocietyMember.MemberData.Title"
pb.SS_GLOBALGAME_UPDATESOCIETYMEMBER_MEMBERDATA_TITLE_FIELD.number = 6
pb.SS_GLOBALGAME_UPDATESOCIETYMEMBER_MEMBERDATA_TITLE_FIELD.index = 5
pb.SS_GLOBALGAME_UPDATESOCIETYMEMBER_MEMBERDATA_TITLE_FIELD.label = 1
pb.SS_GLOBALGAME_UPDATESOCIETYMEMBER_MEMBERDATA_TITLE_FIELD.has_default_value = false
pb.SS_GLOBALGAME_UPDATESOCIETYMEMBER_MEMBERDATA_TITLE_FIELD.default_value = 0
pb.SS_GLOBALGAME_UPDATESOCIETYMEMBER_MEMBERDATA_TITLE_FIELD.type = 5
pb.SS_GLOBALGAME_UPDATESOCIETYMEMBER_MEMBERDATA_TITLE_FIELD.cpp_type = 1

pb.SS_GLOBALGAME_UPDATESOCIETYMEMBER_MEMBERDATA.name = "MemberData"
pb.SS_GLOBALGAME_UPDATESOCIETYMEMBER_MEMBERDATA.full_name = ".SS_GlobalGame_UpdateSocietyMember.MemberData"
pb.SS_GLOBALGAME_UPDATESOCIETYMEMBER_MEMBERDATA.nested_types = {}
pb.SS_GLOBALGAME_UPDATESOCIETYMEMBER_MEMBERDATA.enum_types = {}
pb.SS_GLOBALGAME_UPDATESOCIETYMEMBER_MEMBERDATA.fields = {pb.SS_GLOBALGAME_UPDATESOCIETYMEMBER_MEMBERDATA_ACTORID_FIELD, pb.SS_GLOBALGAME_UPDATESOCIETYMEMBER_MEMBERDATA_ACTORNAME_FIELD, pb.SS_GLOBALGAME_UPDATESOCIETYMEMBER_MEMBERDATA_LEVEL_FIELD, pb.SS_GLOBALGAME_UPDATESOCIETYMEMBER_MEMBERDATA_VOCATION_FIELD, pb.SS_GLOBALGAME_UPDATESOCIETYMEMBER_MEMBERDATA_POWER_FIELD, pb.SS_GLOBALGAME_UPDATESOCIETYMEMBER_MEMBERDATA_TITLE_FIELD}
pb.SS_GLOBALGAME_UPDATESOCIETYMEMBER_MEMBERDATA.is_extendable = false
pb.SS_GLOBALGAME_UPDATESOCIETYMEMBER_MEMBERDATA.extensions = {}
pb.SS_GLOBALGAME_UPDATESOCIETYMEMBER_MEMBERDATA.containing_type = pb.SS_GLOBALGAME_UPDATESOCIETYMEMBER
pb.SS_GLOBALGAME_UPDATESOCIETYMEMBER_ZONEID_FIELD.name = "ZoneID"
pb.SS_GLOBALGAME_UPDATESOCIETYMEMBER_ZONEID_FIELD.full_name = ".SS_GlobalGame_UpdateSocietyMember.ZoneID"
pb.SS_GLOBALGAME_UPDATESOCIETYMEMBER_ZONEID_FIELD.number = 1
pb.SS_GLOBALGAME_UPDATESOCIETYMEMBER_ZONEID_FIELD.index = 0
pb.SS_GLOBALGAME_UPDATESOCIETYMEMBER_ZONEID_FIELD.label = 1
pb.SS_GLOBALGAME_UPDATESOCIETYMEMBER_ZONEID_FIELD.has_default_value = false
pb.SS_GLOBALGAME_UPDATESOCIETYMEMBER_ZONEID_FIELD.default_value = 0
pb.SS_GLOBALGAME_UPDATESOCIETYMEMBER_ZONEID_FIELD.type = 5
pb.SS_GLOBALGAME_UPDATESOCIETYMEMBER_ZONEID_FIELD.cpp_type = 1

pb.SS_GLOBALGAME_UPDATESOCIETYMEMBER_SOCIETYID_FIELD.name = "SocietyID"
pb.SS_GLOBALGAME_UPDATESOCIETYMEMBER_SOCIETYID_FIELD.full_name = ".SS_GlobalGame_UpdateSocietyMember.SocietyID"
pb.SS_GLOBALGAME_UPDATESOCIETYMEMBER_SOCIETYID_FIELD.number = 2
pb.SS_GLOBALGAME_UPDATESOCIETYMEMBER_SOCIETYID_FIELD.index = 1
pb.SS_GLOBALGAME_UPDATESOCIETYMEMBER_SOCIETYID_FIELD.label = 1
pb.SS_GLOBALGAME_UPDATESOCIETYMEMBER_SOCIETYID_FIELD.has_default_value = false
pb.SS_GLOBALGAME_UPDATESOCIETYMEMBER_SOCIETYID_FIELD.default_value = 0
pb.SS_GLOBALGAME_UPDATESOCIETYMEMBER_SOCIETYID_FIELD.type = 5
pb.SS_GLOBALGAME_UPDATESOCIETYMEMBER_SOCIETYID_FIELD.cpp_type = 1

pb.SS_GLOBALGAME_UPDATESOCIETYMEMBER_ISREPLACE_FIELD.name = "IsReplace"
pb.SS_GLOBALGAME_UPDATESOCIETYMEMBER_ISREPLACE_FIELD.full_name = ".SS_GlobalGame_UpdateSocietyMember.IsReplace"
pb.SS_GLOBALGAME_UPDATESOCIETYMEMBER_ISREPLACE_FIELD.number = 3
pb.SS_GLOBALGAME_UPDATESOCIETYMEMBER_ISREPLACE_FIELD.index = 2
pb.SS_GLOBALGAME_UPDATESOCIETYMEMBER_ISREPLACE_FIELD.label = 1
pb.SS_GLOBALGAME_UPDATESOCIETYMEMBER_ISREPLACE_FIELD.has_default_value = false
pb.SS_GLOBALGAME_UPDATESOCIETYMEMBER_ISREPLACE_FIELD.default_value = 0
pb.SS_GLOBALGAME_UPDATESOCIETYMEMBER_ISREPLACE_FIELD.type = 5
pb.SS_GLOBALGAME_UPDATESOCIETYMEMBER_ISREPLACE_FIELD.cpp_type = 1

pb.SS_GLOBALGAME_UPDATESOCIETYMEMBER_MEMBERLIST_FIELD.name = "MemberList"
pb.SS_GLOBALGAME_UPDATESOCIETYMEMBER_MEMBERLIST_FIELD.full_name = ".SS_GlobalGame_UpdateSocietyMember.MemberList"
pb.SS_GLOBALGAME_UPDATESOCIETYMEMBER_MEMBERLIST_FIELD.number = 4
pb.SS_GLOBALGAME_UPDATESOCIETYMEMBER_MEMBERLIST_FIELD.index = 3
pb.SS_GLOBALGAME_UPDATESOCIETYMEMBER_MEMBERLIST_FIELD.label = 3
pb.SS_GLOBALGAME_UPDATESOCIETYMEMBER_MEMBERLIST_FIELD.has_default_value = false
pb.SS_GLOBALGAME_UPDATESOCIETYMEMBER_MEMBERLIST_FIELD.default_value = {}
pb.SS_GLOBALGAME_UPDATESOCIETYMEMBER_MEMBERLIST_FIELD.message_type = pb.SS_GLOBALGAME_UPDATESOCIETYMEMBER_MEMBERDATA
pb.SS_GLOBALGAME_UPDATESOCIETYMEMBER_MEMBERLIST_FIELD.type = 11
pb.SS_GLOBALGAME_UPDATESOCIETYMEMBER_MEMBERLIST_FIELD.cpp_type = 10

pb.SS_GLOBALGAME_UPDATESOCIETYMEMBER.name = "SS_GlobalGame_UpdateSocietyMember"
pb.SS_GLOBALGAME_UPDATESOCIETYMEMBER.full_name = ".SS_GlobalGame_UpdateSocietyMember"
pb.SS_GLOBALGAME_UPDATESOCIETYMEMBER.nested_types = {pb.SS_GLOBALGAME_UPDATESOCIETYMEMBER_MEMBERDATA}
pb.SS_GLOBALGAME_UPDATESOCIETYMEMBER.enum_types = {}
pb.SS_GLOBALGAME_UPDATESOCIETYMEMBER.fields = {pb.SS_GLOBALGAME_UPDATESOCIETYMEMBER_ZONEID_FIELD, pb.SS_GLOBALGAME_UPDATESOCIETYMEMBER_SOCIETYID_FIELD, pb.SS_GLOBALGAME_UPDATESOCIETYMEMBER_ISREPLACE_FIELD, pb.SS_GLOBALGAME_UPDATESOCIETYMEMBER_MEMBERLIST_FIELD}
pb.SS_GLOBALGAME_UPDATESOCIETYMEMBER.is_extendable = false
pb.SS_GLOBALGAME_UPDATESOCIETYMEMBER.extensions = {}
pb.SS_GLOBALGAME_SETWARBANDDATA_ZONEID_FIELD.name = "ZoneID"
pb.SS_GLOBALGAME_SETWARBANDDATA_ZONEID_FIELD.full_name = ".SS_GlobalGame_SetWarbandData.ZoneID"
pb.SS_GLOBALGAME_SETWARBANDDATA_ZONEID_FIELD.number = 1
pb.SS_GLOBALGAME_SETWARBANDDATA_ZONEID_FIELD.index = 0
pb.SS_GLOBALGAME_SETWARBANDDATA_ZONEID_FIELD.label = 1
pb.SS_GLOBALGAME_SETWARBANDDATA_ZONEID_FIELD.has_default_value = false
pb.SS_GLOBALGAME_SETWARBANDDATA_ZONEID_FIELD.default_value = 0
pb.SS_GLOBALGAME_SETWARBANDDATA_ZONEID_FIELD.type = 5
pb.SS_GLOBALGAME_SETWARBANDDATA_ZONEID_FIELD.cpp_type = 1

pb.SS_GLOBALGAME_SETWARBANDDATA_WARBANDID_FIELD.name = "WarbandID"
pb.SS_GLOBALGAME_SETWARBANDDATA_WARBANDID_FIELD.full_name = ".SS_GlobalGame_SetWarbandData.WarbandID"
pb.SS_GLOBALGAME_SETWARBANDDATA_WARBANDID_FIELD.number = 2
pb.SS_GLOBALGAME_SETWARBANDDATA_WARBANDID_FIELD.index = 1
pb.SS_GLOBALGAME_SETWARBANDDATA_WARBANDID_FIELD.label = 1
pb.SS_GLOBALGAME_SETWARBANDDATA_WARBANDID_FIELD.has_default_value = false
pb.SS_GLOBALGAME_SETWARBANDDATA_WARBANDID_FIELD.default_value = 0
pb.SS_GLOBALGAME_SETWARBANDDATA_WARBANDID_FIELD.type = 5
pb.SS_GLOBALGAME_SETWARBANDDATA_WARBANDID_FIELD.cpp_type = 1

pb.SS_GLOBALGAME_SETWARBANDDATA_WARBANDNAME_FIELD.name = "WarbandName"
pb.SS_GLOBALGAME_SETWARBANDDATA_WARBANDNAME_FIELD.full_name = ".SS_GlobalGame_SetWarbandData.WarbandName"
pb.SS_GLOBALGAME_SETWARBANDDATA_WARBANDNAME_FIELD.number = 3
pb.SS_GLOBALGAME_SETWARBANDDATA_WARBANDNAME_FIELD.index = 2
pb.SS_GLOBALGAME_SETWARBANDDATA_WARBANDNAME_FIELD.label = 1
pb.SS_GLOBALGAME_SETWARBANDDATA_WARBANDNAME_FIELD.has_default_value = false
pb.SS_GLOBALGAME_SETWARBANDDATA_WARBANDNAME_FIELD.default_value = ""
pb.SS_GLOBALGAME_SETWARBANDDATA_WARBANDNAME_FIELD.type = 9
pb.SS_GLOBALGAME_SETWARBANDDATA_WARBANDNAME_FIELD.cpp_type = 9

pb.SS_GLOBALGAME_SETWARBANDDATA_COUNTRYID_FIELD.name = "CountryID"
pb.SS_GLOBALGAME_SETWARBANDDATA_COUNTRYID_FIELD.full_name = ".SS_GlobalGame_SetWarbandData.CountryID"
pb.SS_GLOBALGAME_SETWARBANDDATA_COUNTRYID_FIELD.number = 4
pb.SS_GLOBALGAME_SETWARBANDDATA_COUNTRYID_FIELD.index = 3
pb.SS_GLOBALGAME_SETWARBANDDATA_COUNTRYID_FIELD.label = 1
pb.SS_GLOBALGAME_SETWARBANDDATA_COUNTRYID_FIELD.has_default_value = false
pb.SS_GLOBALGAME_SETWARBANDDATA_COUNTRYID_FIELD.default_value = 0
pb.SS_GLOBALGAME_SETWARBANDDATA_COUNTRYID_FIELD.type = 5
pb.SS_GLOBALGAME_SETWARBANDDATA_COUNTRYID_FIELD.cpp_type = 1

pb.SS_GLOBALGAME_SETWARBANDDATA.name = "SS_GlobalGame_SetWarbandData"
pb.SS_GLOBALGAME_SETWARBANDDATA.full_name = ".SS_GlobalGame_SetWarbandData"
pb.SS_GLOBALGAME_SETWARBANDDATA.nested_types = {}
pb.SS_GLOBALGAME_SETWARBANDDATA.enum_types = {}
pb.SS_GLOBALGAME_SETWARBANDDATA.fields = {pb.SS_GLOBALGAME_SETWARBANDDATA_ZONEID_FIELD, pb.SS_GLOBALGAME_SETWARBANDDATA_WARBANDID_FIELD, pb.SS_GLOBALGAME_SETWARBANDDATA_WARBANDNAME_FIELD, pb.SS_GLOBALGAME_SETWARBANDDATA_COUNTRYID_FIELD}
pb.SS_GLOBALGAME_SETWARBANDDATA.is_extendable = false
pb.SS_GLOBALGAME_SETWARBANDDATA.extensions = {}
pb.SS_GLOBALGAME_UPDATEWARBANDMEMBER_MEMBERDATA_ACTORID_FIELD.name = "ActorID"
pb.SS_GLOBALGAME_UPDATEWARBANDMEMBER_MEMBERDATA_ACTORID_FIELD.full_name = ".SS_GlobalGame_UpdateWarbandMember.MemberData.ActorID"
pb.SS_GLOBALGAME_UPDATEWARBANDMEMBER_MEMBERDATA_ACTORID_FIELD.number = 1
pb.SS_GLOBALGAME_UPDATEWARBANDMEMBER_MEMBERDATA_ACTORID_FIELD.index = 0
pb.SS_GLOBALGAME_UPDATEWARBANDMEMBER_MEMBERDATA_ACTORID_FIELD.label = 1
pb.SS_GLOBALGAME_UPDATEWARBANDMEMBER_MEMBERDATA_ACTORID_FIELD.has_default_value = false
pb.SS_GLOBALGAME_UPDATEWARBANDMEMBER_MEMBERDATA_ACTORID_FIELD.default_value = 0
pb.SS_GLOBALGAME_UPDATEWARBANDMEMBER_MEMBERDATA_ACTORID_FIELD.type = 5
pb.SS_GLOBALGAME_UPDATEWARBANDMEMBER_MEMBERDATA_ACTORID_FIELD.cpp_type = 1

pb.SS_GLOBALGAME_UPDATEWARBANDMEMBER_MEMBERDATA_ACTORNAME_FIELD.name = "ActorName"
pb.SS_GLOBALGAME_UPDATEWARBANDMEMBER_MEMBERDATA_ACTORNAME_FIELD.full_name = ".SS_GlobalGame_UpdateWarbandMember.MemberData.ActorName"
pb.SS_GLOBALGAME_UPDATEWARBANDMEMBER_MEMBERDATA_ACTORNAME_FIELD.number = 2
pb.SS_GLOBALGAME_UPDATEWARBANDMEMBER_MEMBERDATA_ACTORNAME_FIELD.index = 1
pb.SS_GLOBALGAME_UPDATEWARBANDMEMBER_MEMBERDATA_ACTORNAME_FIELD.label = 1
pb.SS_GLOBALGAME_UPDATEWARBANDMEMBER_MEMBERDATA_ACTORNAME_FIELD.has_default_value = false
pb.SS_GLOBALGAME_UPDATEWARBANDMEMBER_MEMBERDATA_ACTORNAME_FIELD.default_value = ""
pb.SS_GLOBALGAME_UPDATEWARBANDMEMBER_MEMBERDATA_ACTORNAME_FIELD.type = 9
pb.SS_GLOBALGAME_UPDATEWARBANDMEMBER_MEMBERDATA_ACTORNAME_FIELD.cpp_type = 9

pb.SS_GLOBALGAME_UPDATEWARBANDMEMBER_MEMBERDATA_LEVEL_FIELD.name = "Level"
pb.SS_GLOBALGAME_UPDATEWARBANDMEMBER_MEMBERDATA_LEVEL_FIELD.full_name = ".SS_GlobalGame_UpdateWarbandMember.MemberData.Level"
pb.SS_GLOBALGAME_UPDATEWARBANDMEMBER_MEMBERDATA_LEVEL_FIELD.number = 3
pb.SS_GLOBALGAME_UPDATEWARBANDMEMBER_MEMBERDATA_LEVEL_FIELD.index = 2
pb.SS_GLOBALGAME_UPDATEWARBANDMEMBER_MEMBERDATA_LEVEL_FIELD.label = 1
pb.SS_GLOBALGAME_UPDATEWARBANDMEMBER_MEMBERDATA_LEVEL_FIELD.has_default_value = false
pb.SS_GLOBALGAME_UPDATEWARBANDMEMBER_MEMBERDATA_LEVEL_FIELD.default_value = 0
pb.SS_GLOBALGAME_UPDATEWARBANDMEMBER_MEMBERDATA_LEVEL_FIELD.type = 5
pb.SS_GLOBALGAME_UPDATEWARBANDMEMBER_MEMBERDATA_LEVEL_FIELD.cpp_type = 1

pb.SS_GLOBALGAME_UPDATEWARBANDMEMBER_MEMBERDATA_VOCATION_FIELD.name = "Vocation"
pb.SS_GLOBALGAME_UPDATEWARBANDMEMBER_MEMBERDATA_VOCATION_FIELD.full_name = ".SS_GlobalGame_UpdateWarbandMember.MemberData.Vocation"
pb.SS_GLOBALGAME_UPDATEWARBANDMEMBER_MEMBERDATA_VOCATION_FIELD.number = 4
pb.SS_GLOBALGAME_UPDATEWARBANDMEMBER_MEMBERDATA_VOCATION_FIELD.index = 3
pb.SS_GLOBALGAME_UPDATEWARBANDMEMBER_MEMBERDATA_VOCATION_FIELD.label = 1
pb.SS_GLOBALGAME_UPDATEWARBANDMEMBER_MEMBERDATA_VOCATION_FIELD.has_default_value = false
pb.SS_GLOBALGAME_UPDATEWARBANDMEMBER_MEMBERDATA_VOCATION_FIELD.default_value = 0
pb.SS_GLOBALGAME_UPDATEWARBANDMEMBER_MEMBERDATA_VOCATION_FIELD.type = 5
pb.SS_GLOBALGAME_UPDATEWARBANDMEMBER_MEMBERDATA_VOCATION_FIELD.cpp_type = 1

pb.SS_GLOBALGAME_UPDATEWARBANDMEMBER_MEMBERDATA_POWER_FIELD.name = "Power"
pb.SS_GLOBALGAME_UPDATEWARBANDMEMBER_MEMBERDATA_POWER_FIELD.full_name = ".SS_GlobalGame_UpdateWarbandMember.MemberData.Power"
pb.SS_GLOBALGAME_UPDATEWARBANDMEMBER_MEMBERDATA_POWER_FIELD.number = 5
pb.SS_GLOBALGAME_UPDATEWARBANDMEMBER_MEMBERDATA_POWER_FIELD.index = 4
pb.SS_GLOBALGAME_UPDATEWARBANDMEMBER_MEMBERDATA_POWER_FIELD.label = 1
pb.SS_GLOBALGAME_UPDATEWARBANDMEMBER_MEMBERDATA_POWER_FIELD.has_default_value = false
pb.SS_GLOBALGAME_UPDATEWARBANDMEMBER_MEMBERDATA_POWER_FIELD.default_value = 0
pb.SS_GLOBALGAME_UPDATEWARBANDMEMBER_MEMBERDATA_POWER_FIELD.type = 5
pb.SS_GLOBALGAME_UPDATEWARBANDMEMBER_MEMBERDATA_POWER_FIELD.cpp_type = 1

pb.SS_GLOBALGAME_UPDATEWARBANDMEMBER_MEMBERDATA.name = "MemberData"
pb.SS_GLOBALGAME_UPDATEWARBANDMEMBER_MEMBERDATA.full_name = ".SS_GlobalGame_UpdateWarbandMember.MemberData"
pb.SS_GLOBALGAME_UPDATEWARBANDMEMBER_MEMBERDATA.nested_types = {}
pb.SS_GLOBALGAME_UPDATEWARBANDMEMBER_MEMBERDATA.enum_types = {}
pb.SS_GLOBALGAME_UPDATEWARBANDMEMBER_MEMBERDATA.fields = {pb.SS_GLOBALGAME_UPDATEWARBANDMEMBER_MEMBERDATA_ACTORID_FIELD, pb.SS_GLOBALGAME_UPDATEWARBANDMEMBER_MEMBERDATA_ACTORNAME_FIELD, pb.SS_GLOBALGAME_UPDATEWARBANDMEMBER_MEMBERDATA_LEVEL_FIELD, pb.SS_GLOBALGAME_UPDATEWARBANDMEMBER_MEMBERDATA_VOCATION_FIELD, pb.SS_GLOBALGAME_UPDATEWARBANDMEMBER_MEMBERDATA_POWER_FIELD}
pb.SS_GLOBALGAME_UPDATEWARBANDMEMBER_MEMBERDATA.is_extendable = false
pb.SS_GLOBALGAME_UPDATEWARBANDMEMBER_MEMBERDATA.extensions = {}
pb.SS_GLOBALGAME_UPDATEWARBANDMEMBER_MEMBERDATA.containing_type = pb.SS_GLOBALGAME_UPDATEWARBANDMEMBER
pb.SS_GLOBALGAME_UPDATEWARBANDMEMBER_ZONEID_FIELD.name = "ZoneID"
pb.SS_GLOBALGAME_UPDATEWARBANDMEMBER_ZONEID_FIELD.full_name = ".SS_GlobalGame_UpdateWarbandMember.ZoneID"
pb.SS_GLOBALGAME_UPDATEWARBANDMEMBER_ZONEID_FIELD.number = 1
pb.SS_GLOBALGAME_UPDATEWARBANDMEMBER_ZONEID_FIELD.index = 0
pb.SS_GLOBALGAME_UPDATEWARBANDMEMBER_ZONEID_FIELD.label = 1
pb.SS_GLOBALGAME_UPDATEWARBANDMEMBER_ZONEID_FIELD.has_default_value = false
pb.SS_GLOBALGAME_UPDATEWARBANDMEMBER_ZONEID_FIELD.default_value = 0
pb.SS_GLOBALGAME_UPDATEWARBANDMEMBER_ZONEID_FIELD.type = 5
pb.SS_GLOBALGAME_UPDATEWARBANDMEMBER_ZONEID_FIELD.cpp_type = 1

pb.SS_GLOBALGAME_UPDATEWARBANDMEMBER_WARBANDID_FIELD.name = "WarbandID"
pb.SS_GLOBALGAME_UPDATEWARBANDMEMBER_WARBANDID_FIELD.full_name = ".SS_GlobalGame_UpdateWarbandMember.WarbandID"
pb.SS_GLOBALGAME_UPDATEWARBANDMEMBER_WARBANDID_FIELD.number = 2
pb.SS_GLOBALGAME_UPDATEWARBANDMEMBER_WARBANDID_FIELD.index = 1
pb.SS_GLOBALGAME_UPDATEWARBANDMEMBER_WARBANDID_FIELD.label = 1
pb.SS_GLOBALGAME_UPDATEWARBANDMEMBER_WARBANDID_FIELD.has_default_value = false
pb.SS_GLOBALGAME_UPDATEWARBANDMEMBER_WARBANDID_FIELD.default_value = 0
pb.SS_GLOBALGAME_UPDATEWARBANDMEMBER_WARBANDID_FIELD.type = 5
pb.SS_GLOBALGAME_UPDATEWARBANDMEMBER_WARBANDID_FIELD.cpp_type = 1

pb.SS_GLOBALGAME_UPDATEWARBANDMEMBER_ISREPLACE_FIELD.name = "IsReplace"
pb.SS_GLOBALGAME_UPDATEWARBANDMEMBER_ISREPLACE_FIELD.full_name = ".SS_GlobalGame_UpdateWarbandMember.IsReplace"
pb.SS_GLOBALGAME_UPDATEWARBANDMEMBER_ISREPLACE_FIELD.number = 3
pb.SS_GLOBALGAME_UPDATEWARBANDMEMBER_ISREPLACE_FIELD.index = 2
pb.SS_GLOBALGAME_UPDATEWARBANDMEMBER_ISREPLACE_FIELD.label = 1
pb.SS_GLOBALGAME_UPDATEWARBANDMEMBER_ISREPLACE_FIELD.has_default_value = false
pb.SS_GLOBALGAME_UPDATEWARBANDMEMBER_ISREPLACE_FIELD.default_value = 0
pb.SS_GLOBALGAME_UPDATEWARBANDMEMBER_ISREPLACE_FIELD.type = 5
pb.SS_GLOBALGAME_UPDATEWARBANDMEMBER_ISREPLACE_FIELD.cpp_type = 1

pb.SS_GLOBALGAME_UPDATEWARBANDMEMBER_MEMBERLIST_FIELD.name = "MemberList"
pb.SS_GLOBALGAME_UPDATEWARBANDMEMBER_MEMBERLIST_FIELD.full_name = ".SS_GlobalGame_UpdateWarbandMember.MemberList"
pb.SS_GLOBALGAME_UPDATEWARBANDMEMBER_MEMBERLIST_FIELD.number = 4
pb.SS_GLOBALGAME_UPDATEWARBANDMEMBER_MEMBERLIST_FIELD.index = 3
pb.SS_GLOBALGAME_UPDATEWARBANDMEMBER_MEMBERLIST_FIELD.label = 3
pb.SS_GLOBALGAME_UPDATEWARBANDMEMBER_MEMBERLIST_FIELD.has_default_value = false
pb.SS_GLOBALGAME_UPDATEWARBANDMEMBER_MEMBERLIST_FIELD.default_value = {}
pb.SS_GLOBALGAME_UPDATEWARBANDMEMBER_MEMBERLIST_FIELD.message_type = pb.SS_GLOBALGAME_UPDATEWARBANDMEMBER_MEMBERDATA
pb.SS_GLOBALGAME_UPDATEWARBANDMEMBER_MEMBERLIST_FIELD.type = 11
pb.SS_GLOBALGAME_UPDATEWARBANDMEMBER_MEMBERLIST_FIELD.cpp_type = 10

pb.SS_GLOBALGAME_UPDATEWARBANDMEMBER.name = "SS_GlobalGame_UpdateWarbandMember"
pb.SS_GLOBALGAME_UPDATEWARBANDMEMBER.full_name = ".SS_GlobalGame_UpdateWarbandMember"
pb.SS_GLOBALGAME_UPDATEWARBANDMEMBER.nested_types = {pb.SS_GLOBALGAME_UPDATEWARBANDMEMBER_MEMBERDATA}
pb.SS_GLOBALGAME_UPDATEWARBANDMEMBER.enum_types = {}
pb.SS_GLOBALGAME_UPDATEWARBANDMEMBER.fields = {pb.SS_GLOBALGAME_UPDATEWARBANDMEMBER_ZONEID_FIELD, pb.SS_GLOBALGAME_UPDATEWARBANDMEMBER_WARBANDID_FIELD, pb.SS_GLOBALGAME_UPDATEWARBANDMEMBER_ISREPLACE_FIELD, pb.SS_GLOBALGAME_UPDATEWARBANDMEMBER_MEMBERLIST_FIELD}
pb.SS_GLOBALGAME_UPDATEWARBANDMEMBER.is_extendable = false
pb.SS_GLOBALGAME_UPDATEWARBANDMEMBER.extensions = {}
pb.SS_GLOBALGAME_MARKETADDSELLITEM_ZONEID_FIELD.name = "ZoneID"
pb.SS_GLOBALGAME_MARKETADDSELLITEM_ZONEID_FIELD.full_name = ".SS_GlobalGame_MarketAddSellItem.ZoneID"
pb.SS_GLOBALGAME_MARKETADDSELLITEM_ZONEID_FIELD.number = 1
pb.SS_GLOBALGAME_MARKETADDSELLITEM_ZONEID_FIELD.index = 0
pb.SS_GLOBALGAME_MARKETADDSELLITEM_ZONEID_FIELD.label = 1
pb.SS_GLOBALGAME_MARKETADDSELLITEM_ZONEID_FIELD.has_default_value = false
pb.SS_GLOBALGAME_MARKETADDSELLITEM_ZONEID_FIELD.default_value = 0
pb.SS_GLOBALGAME_MARKETADDSELLITEM_ZONEID_FIELD.type = 5
pb.SS_GLOBALGAME_MARKETADDSELLITEM_ZONEID_FIELD.cpp_type = 1

pb.SS_GLOBALGAME_MARKETADDSELLITEM_ACTORID_FIELD.name = "ActorID"
pb.SS_GLOBALGAME_MARKETADDSELLITEM_ACTORID_FIELD.full_name = ".SS_GlobalGame_MarketAddSellItem.ActorID"
pb.SS_GLOBALGAME_MARKETADDSELLITEM_ACTORID_FIELD.number = 2
pb.SS_GLOBALGAME_MARKETADDSELLITEM_ACTORID_FIELD.index = 1
pb.SS_GLOBALGAME_MARKETADDSELLITEM_ACTORID_FIELD.label = 1
pb.SS_GLOBALGAME_MARKETADDSELLITEM_ACTORID_FIELD.has_default_value = false
pb.SS_GLOBALGAME_MARKETADDSELLITEM_ACTORID_FIELD.default_value = 0
pb.SS_GLOBALGAME_MARKETADDSELLITEM_ACTORID_FIELD.type = 5
pb.SS_GLOBALGAME_MARKETADDSELLITEM_ACTORID_FIELD.cpp_type = 1

pb.SS_GLOBALGAME_MARKETADDSELLITEM_PRICE_FIELD.name = "Price"
pb.SS_GLOBALGAME_MARKETADDSELLITEM_PRICE_FIELD.full_name = ".SS_GlobalGame_MarketAddSellItem.Price"
pb.SS_GLOBALGAME_MARKETADDSELLITEM_PRICE_FIELD.number = 3
pb.SS_GLOBALGAME_MARKETADDSELLITEM_PRICE_FIELD.index = 2
pb.SS_GLOBALGAME_MARKETADDSELLITEM_PRICE_FIELD.label = 1
pb.SS_GLOBALGAME_MARKETADDSELLITEM_PRICE_FIELD.has_default_value = false
pb.SS_GLOBALGAME_MARKETADDSELLITEM_PRICE_FIELD.default_value = 0
pb.SS_GLOBALGAME_MARKETADDSELLITEM_PRICE_FIELD.type = 5
pb.SS_GLOBALGAME_MARKETADDSELLITEM_PRICE_FIELD.cpp_type = 1

pb.SS_GLOBALGAME_MARKETADDSELLITEM_GOODSUID_FIELD.name = "GoodsUID"
pb.SS_GLOBALGAME_MARKETADDSELLITEM_GOODSUID_FIELD.full_name = ".SS_GlobalGame_MarketAddSellItem.GoodsUID"
pb.SS_GLOBALGAME_MARKETADDSELLITEM_GOODSUID_FIELD.number = 4
pb.SS_GLOBALGAME_MARKETADDSELLITEM_GOODSUID_FIELD.index = 3
pb.SS_GLOBALGAME_MARKETADDSELLITEM_GOODSUID_FIELD.label = 1
pb.SS_GLOBALGAME_MARKETADDSELLITEM_GOODSUID_FIELD.has_default_value = false
pb.SS_GLOBALGAME_MARKETADDSELLITEM_GOODSUID_FIELD.default_value = 0
pb.SS_GLOBALGAME_MARKETADDSELLITEM_GOODSUID_FIELD.type = 4
pb.SS_GLOBALGAME_MARKETADDSELLITEM_GOODSUID_FIELD.cpp_type = 4

pb.SS_GLOBALGAME_MARKETADDSELLITEM_GOODSDATA_FIELD.name = "GoodsData"
pb.SS_GLOBALGAME_MARKETADDSELLITEM_GOODSDATA_FIELD.full_name = ".SS_GlobalGame_MarketAddSellItem.GoodsData"
pb.SS_GLOBALGAME_MARKETADDSELLITEM_GOODSDATA_FIELD.number = 5
pb.SS_GLOBALGAME_MARKETADDSELLITEM_GOODSDATA_FIELD.index = 4
pb.SS_GLOBALGAME_MARKETADDSELLITEM_GOODSDATA_FIELD.label = 1
pb.SS_GLOBALGAME_MARKETADDSELLITEM_GOODSDATA_FIELD.has_default_value = false
pb.SS_GLOBALGAME_MARKETADDSELLITEM_GOODSDATA_FIELD.default_value = ""
pb.SS_GLOBALGAME_MARKETADDSELLITEM_GOODSDATA_FIELD.type = 12
pb.SS_GLOBALGAME_MARKETADDSELLITEM_GOODSDATA_FIELD.cpp_type = 9

pb.SS_GLOBALGAME_MARKETADDSELLITEM.name = "SS_GlobalGame_MarketAddSellItem"
pb.SS_GLOBALGAME_MARKETADDSELLITEM.full_name = ".SS_GlobalGame_MarketAddSellItem"
pb.SS_GLOBALGAME_MARKETADDSELLITEM.nested_types = {}
pb.SS_GLOBALGAME_MARKETADDSELLITEM.enum_types = {}
pb.SS_GLOBALGAME_MARKETADDSELLITEM.fields = {pb.SS_GLOBALGAME_MARKETADDSELLITEM_ZONEID_FIELD, pb.SS_GLOBALGAME_MARKETADDSELLITEM_ACTORID_FIELD, pb.SS_GLOBALGAME_MARKETADDSELLITEM_PRICE_FIELD, pb.SS_GLOBALGAME_MARKETADDSELLITEM_GOODSUID_FIELD, pb.SS_GLOBALGAME_MARKETADDSELLITEM_GOODSDATA_FIELD}
pb.SS_GLOBALGAME_MARKETADDSELLITEM.is_extendable = false
pb.SS_GLOBALGAME_MARKETADDSELLITEM.extensions = {}
pb.SS_GLOBALGAME_MARKETADDSELLITEMCHECK_ACTORID_FIELD.name = "ActorID"
pb.SS_GLOBALGAME_MARKETADDSELLITEMCHECK_ACTORID_FIELD.full_name = ".SS_GlobalGame_MarketAddSellItemCheck.ActorID"
pb.SS_GLOBALGAME_MARKETADDSELLITEMCHECK_ACTORID_FIELD.number = 1
pb.SS_GLOBALGAME_MARKETADDSELLITEMCHECK_ACTORID_FIELD.index = 0
pb.SS_GLOBALGAME_MARKETADDSELLITEMCHECK_ACTORID_FIELD.label = 1
pb.SS_GLOBALGAME_MARKETADDSELLITEMCHECK_ACTORID_FIELD.has_default_value = false
pb.SS_GLOBALGAME_MARKETADDSELLITEMCHECK_ACTORID_FIELD.default_value = 0
pb.SS_GLOBALGAME_MARKETADDSELLITEMCHECK_ACTORID_FIELD.type = 5
pb.SS_GLOBALGAME_MARKETADDSELLITEMCHECK_ACTORID_FIELD.cpp_type = 1

pb.SS_GLOBALGAME_MARKETADDSELLITEMCHECK_GOODSUID_FIELD.name = "GoodsUID"
pb.SS_GLOBALGAME_MARKETADDSELLITEMCHECK_GOODSUID_FIELD.full_name = ".SS_GlobalGame_MarketAddSellItemCheck.GoodsUID"
pb.SS_GLOBALGAME_MARKETADDSELLITEMCHECK_GOODSUID_FIELD.number = 2
pb.SS_GLOBALGAME_MARKETADDSELLITEMCHECK_GOODSUID_FIELD.index = 1
pb.SS_GLOBALGAME_MARKETADDSELLITEMCHECK_GOODSUID_FIELD.label = 1
pb.SS_GLOBALGAME_MARKETADDSELLITEMCHECK_GOODSUID_FIELD.has_default_value = false
pb.SS_GLOBALGAME_MARKETADDSELLITEMCHECK_GOODSUID_FIELD.default_value = 0
pb.SS_GLOBALGAME_MARKETADDSELLITEMCHECK_GOODSUID_FIELD.type = 4
pb.SS_GLOBALGAME_MARKETADDSELLITEMCHECK_GOODSUID_FIELD.cpp_type = 4

pb.SS_GLOBALGAME_MARKETADDSELLITEMCHECK_GOODSNUM_FIELD.name = "GoodsNum"
pb.SS_GLOBALGAME_MARKETADDSELLITEMCHECK_GOODSNUM_FIELD.full_name = ".SS_GlobalGame_MarketAddSellItemCheck.GoodsNum"
pb.SS_GLOBALGAME_MARKETADDSELLITEMCHECK_GOODSNUM_FIELD.number = 3
pb.SS_GLOBALGAME_MARKETADDSELLITEMCHECK_GOODSNUM_FIELD.index = 2
pb.SS_GLOBALGAME_MARKETADDSELLITEMCHECK_GOODSNUM_FIELD.label = 1
pb.SS_GLOBALGAME_MARKETADDSELLITEMCHECK_GOODSNUM_FIELD.has_default_value = false
pb.SS_GLOBALGAME_MARKETADDSELLITEMCHECK_GOODSNUM_FIELD.default_value = 0
pb.SS_GLOBALGAME_MARKETADDSELLITEMCHECK_GOODSNUM_FIELD.type = 5
pb.SS_GLOBALGAME_MARKETADDSELLITEMCHECK_GOODSNUM_FIELD.cpp_type = 1

pb.SS_GLOBALGAME_MARKETADDSELLITEMCHECK_SELLID_FIELD.name = "SellID"
pb.SS_GLOBALGAME_MARKETADDSELLITEMCHECK_SELLID_FIELD.full_name = ".SS_GlobalGame_MarketAddSellItemCheck.SellID"
pb.SS_GLOBALGAME_MARKETADDSELLITEMCHECK_SELLID_FIELD.number = 4
pb.SS_GLOBALGAME_MARKETADDSELLITEMCHECK_SELLID_FIELD.index = 3
pb.SS_GLOBALGAME_MARKETADDSELLITEMCHECK_SELLID_FIELD.label = 1
pb.SS_GLOBALGAME_MARKETADDSELLITEMCHECK_SELLID_FIELD.has_default_value = false
pb.SS_GLOBALGAME_MARKETADDSELLITEMCHECK_SELLID_FIELD.default_value = 0
pb.SS_GLOBALGAME_MARKETADDSELLITEMCHECK_SELLID_FIELD.type = 5
pb.SS_GLOBALGAME_MARKETADDSELLITEMCHECK_SELLID_FIELD.cpp_type = 1

pb.SS_GLOBALGAME_MARKETADDSELLITEMCHECK.name = "SS_GlobalGame_MarketAddSellItemCheck"
pb.SS_GLOBALGAME_MARKETADDSELLITEMCHECK.full_name = ".SS_GlobalGame_MarketAddSellItemCheck"
pb.SS_GLOBALGAME_MARKETADDSELLITEMCHECK.nested_types = {}
pb.SS_GLOBALGAME_MARKETADDSELLITEMCHECK.enum_types = {}
pb.SS_GLOBALGAME_MARKETADDSELLITEMCHECK.fields = {pb.SS_GLOBALGAME_MARKETADDSELLITEMCHECK_ACTORID_FIELD, pb.SS_GLOBALGAME_MARKETADDSELLITEMCHECK_GOODSUID_FIELD, pb.SS_GLOBALGAME_MARKETADDSELLITEMCHECK_GOODSNUM_FIELD, pb.SS_GLOBALGAME_MARKETADDSELLITEMCHECK_SELLID_FIELD}
pb.SS_GLOBALGAME_MARKETADDSELLITEMCHECK.is_extendable = false
pb.SS_GLOBALGAME_MARKETADDSELLITEMCHECK.extensions = {}
pb.SS_GLOBALGAME_MARKETADDSELLITEMCHECKRESULT_ZONEID_FIELD.name = "ZoneID"
pb.SS_GLOBALGAME_MARKETADDSELLITEMCHECKRESULT_ZONEID_FIELD.full_name = ".SS_GlobalGame_MarketAddSellItemCheckResult.ZoneID"
pb.SS_GLOBALGAME_MARKETADDSELLITEMCHECKRESULT_ZONEID_FIELD.number = 1
pb.SS_GLOBALGAME_MARKETADDSELLITEMCHECKRESULT_ZONEID_FIELD.index = 0
pb.SS_GLOBALGAME_MARKETADDSELLITEMCHECKRESULT_ZONEID_FIELD.label = 1
pb.SS_GLOBALGAME_MARKETADDSELLITEMCHECKRESULT_ZONEID_FIELD.has_default_value = false
pb.SS_GLOBALGAME_MARKETADDSELLITEMCHECKRESULT_ZONEID_FIELD.default_value = 0
pb.SS_GLOBALGAME_MARKETADDSELLITEMCHECKRESULT_ZONEID_FIELD.type = 5
pb.SS_GLOBALGAME_MARKETADDSELLITEMCHECKRESULT_ZONEID_FIELD.cpp_type = 1

pb.SS_GLOBALGAME_MARKETADDSELLITEMCHECKRESULT_ACTORID_FIELD.name = "ActorID"
pb.SS_GLOBALGAME_MARKETADDSELLITEMCHECKRESULT_ACTORID_FIELD.full_name = ".SS_GlobalGame_MarketAddSellItemCheckResult.ActorID"
pb.SS_GLOBALGAME_MARKETADDSELLITEMCHECKRESULT_ACTORID_FIELD.number = 2
pb.SS_GLOBALGAME_MARKETADDSELLITEMCHECKRESULT_ACTORID_FIELD.index = 1
pb.SS_GLOBALGAME_MARKETADDSELLITEMCHECKRESULT_ACTORID_FIELD.label = 1
pb.SS_GLOBALGAME_MARKETADDSELLITEMCHECKRESULT_ACTORID_FIELD.has_default_value = false
pb.SS_GLOBALGAME_MARKETADDSELLITEMCHECKRESULT_ACTORID_FIELD.default_value = 0
pb.SS_GLOBALGAME_MARKETADDSELLITEMCHECKRESULT_ACTORID_FIELD.type = 5
pb.SS_GLOBALGAME_MARKETADDSELLITEMCHECKRESULT_ACTORID_FIELD.cpp_type = 1

pb.SS_GLOBALGAME_MARKETADDSELLITEMCHECKRESULT_SELLID_FIELD.name = "SellID"
pb.SS_GLOBALGAME_MARKETADDSELLITEMCHECKRESULT_SELLID_FIELD.full_name = ".SS_GlobalGame_MarketAddSellItemCheckResult.SellID"
pb.SS_GLOBALGAME_MARKETADDSELLITEMCHECKRESULT_SELLID_FIELD.number = 3
pb.SS_GLOBALGAME_MARKETADDSELLITEMCHECKRESULT_SELLID_FIELD.index = 2
pb.SS_GLOBALGAME_MARKETADDSELLITEMCHECKRESULT_SELLID_FIELD.label = 1
pb.SS_GLOBALGAME_MARKETADDSELLITEMCHECKRESULT_SELLID_FIELD.has_default_value = false
pb.SS_GLOBALGAME_MARKETADDSELLITEMCHECKRESULT_SELLID_FIELD.default_value = 0
pb.SS_GLOBALGAME_MARKETADDSELLITEMCHECKRESULT_SELLID_FIELD.type = 5
pb.SS_GLOBALGAME_MARKETADDSELLITEMCHECKRESULT_SELLID_FIELD.cpp_type = 1

pb.SS_GLOBALGAME_MARKETADDSELLITEMCHECKRESULT_RESULT_FIELD.name = "Result"
pb.SS_GLOBALGAME_MARKETADDSELLITEMCHECKRESULT_RESULT_FIELD.full_name = ".SS_GlobalGame_MarketAddSellItemCheckResult.Result"
pb.SS_GLOBALGAME_MARKETADDSELLITEMCHECKRESULT_RESULT_FIELD.number = 4
pb.SS_GLOBALGAME_MARKETADDSELLITEMCHECKRESULT_RESULT_FIELD.index = 3
pb.SS_GLOBALGAME_MARKETADDSELLITEMCHECKRESULT_RESULT_FIELD.label = 1
pb.SS_GLOBALGAME_MARKETADDSELLITEMCHECKRESULT_RESULT_FIELD.has_default_value = false
pb.SS_GLOBALGAME_MARKETADDSELLITEMCHECKRESULT_RESULT_FIELD.default_value = 0
pb.SS_GLOBALGAME_MARKETADDSELLITEMCHECKRESULT_RESULT_FIELD.type = 5
pb.SS_GLOBALGAME_MARKETADDSELLITEMCHECKRESULT_RESULT_FIELD.cpp_type = 1

pb.SS_GLOBALGAME_MARKETADDSELLITEMCHECKRESULT.name = "SS_GlobalGame_MarketAddSellItemCheckResult"
pb.SS_GLOBALGAME_MARKETADDSELLITEMCHECKRESULT.full_name = ".SS_GlobalGame_MarketAddSellItemCheckResult"
pb.SS_GLOBALGAME_MARKETADDSELLITEMCHECKRESULT.nested_types = {}
pb.SS_GLOBALGAME_MARKETADDSELLITEMCHECKRESULT.enum_types = {}
pb.SS_GLOBALGAME_MARKETADDSELLITEMCHECKRESULT.fields = {pb.SS_GLOBALGAME_MARKETADDSELLITEMCHECKRESULT_ZONEID_FIELD, pb.SS_GLOBALGAME_MARKETADDSELLITEMCHECKRESULT_ACTORID_FIELD, pb.SS_GLOBALGAME_MARKETADDSELLITEMCHECKRESULT_SELLID_FIELD, pb.SS_GLOBALGAME_MARKETADDSELLITEMCHECKRESULT_RESULT_FIELD}
pb.SS_GLOBALGAME_MARKETADDSELLITEMCHECKRESULT.is_extendable = false
pb.SS_GLOBALGAME_MARKETADDSELLITEMCHECKRESULT.extensions = {}
pb.SS_GLOBALGAME_MARKETREMOVESELLITEM_ZONEID_FIELD.name = "ZoneID"
pb.SS_GLOBALGAME_MARKETREMOVESELLITEM_ZONEID_FIELD.full_name = ".SS_GlobalGame_MarketRemoveSellItem.ZoneID"
pb.SS_GLOBALGAME_MARKETREMOVESELLITEM_ZONEID_FIELD.number = 1
pb.SS_GLOBALGAME_MARKETREMOVESELLITEM_ZONEID_FIELD.index = 0
pb.SS_GLOBALGAME_MARKETREMOVESELLITEM_ZONEID_FIELD.label = 1
pb.SS_GLOBALGAME_MARKETREMOVESELLITEM_ZONEID_FIELD.has_default_value = false
pb.SS_GLOBALGAME_MARKETREMOVESELLITEM_ZONEID_FIELD.default_value = 0
pb.SS_GLOBALGAME_MARKETREMOVESELLITEM_ZONEID_FIELD.type = 5
pb.SS_GLOBALGAME_MARKETREMOVESELLITEM_ZONEID_FIELD.cpp_type = 1

pb.SS_GLOBALGAME_MARKETREMOVESELLITEM_ACTORID_FIELD.name = "ActorID"
pb.SS_GLOBALGAME_MARKETREMOVESELLITEM_ACTORID_FIELD.full_name = ".SS_GlobalGame_MarketRemoveSellItem.ActorID"
pb.SS_GLOBALGAME_MARKETREMOVESELLITEM_ACTORID_FIELD.number = 2
pb.SS_GLOBALGAME_MARKETREMOVESELLITEM_ACTORID_FIELD.index = 1
pb.SS_GLOBALGAME_MARKETREMOVESELLITEM_ACTORID_FIELD.label = 1
pb.SS_GLOBALGAME_MARKETREMOVESELLITEM_ACTORID_FIELD.has_default_value = false
pb.SS_GLOBALGAME_MARKETREMOVESELLITEM_ACTORID_FIELD.default_value = 0
pb.SS_GLOBALGAME_MARKETREMOVESELLITEM_ACTORID_FIELD.type = 5
pb.SS_GLOBALGAME_MARKETREMOVESELLITEM_ACTORID_FIELD.cpp_type = 1

pb.SS_GLOBALGAME_MARKETREMOVESELLITEM_SELLID_FIELD.name = "SellID"
pb.SS_GLOBALGAME_MARKETREMOVESELLITEM_SELLID_FIELD.full_name = ".SS_GlobalGame_MarketRemoveSellItem.SellID"
pb.SS_GLOBALGAME_MARKETREMOVESELLITEM_SELLID_FIELD.number = 3
pb.SS_GLOBALGAME_MARKETREMOVESELLITEM_SELLID_FIELD.index = 2
pb.SS_GLOBALGAME_MARKETREMOVESELLITEM_SELLID_FIELD.label = 1
pb.SS_GLOBALGAME_MARKETREMOVESELLITEM_SELLID_FIELD.has_default_value = false
pb.SS_GLOBALGAME_MARKETREMOVESELLITEM_SELLID_FIELD.default_value = 0
pb.SS_GLOBALGAME_MARKETREMOVESELLITEM_SELLID_FIELD.type = 5
pb.SS_GLOBALGAME_MARKETREMOVESELLITEM_SELLID_FIELD.cpp_type = 1

pb.SS_GLOBALGAME_MARKETREMOVESELLITEM_GOODSNUM_FIELD.name = "GoodsNum"
pb.SS_GLOBALGAME_MARKETREMOVESELLITEM_GOODSNUM_FIELD.full_name = ".SS_GlobalGame_MarketRemoveSellItem.GoodsNum"
pb.SS_GLOBALGAME_MARKETREMOVESELLITEM_GOODSNUM_FIELD.number = 4
pb.SS_GLOBALGAME_MARKETREMOVESELLITEM_GOODSNUM_FIELD.index = 3
pb.SS_GLOBALGAME_MARKETREMOVESELLITEM_GOODSNUM_FIELD.label = 1
pb.SS_GLOBALGAME_MARKETREMOVESELLITEM_GOODSNUM_FIELD.has_default_value = false
pb.SS_GLOBALGAME_MARKETREMOVESELLITEM_GOODSNUM_FIELD.default_value = 0
pb.SS_GLOBALGAME_MARKETREMOVESELLITEM_GOODSNUM_FIELD.type = 5
pb.SS_GLOBALGAME_MARKETREMOVESELLITEM_GOODSNUM_FIELD.cpp_type = 1

pb.SS_GLOBALGAME_MARKETREMOVESELLITEM.name = "SS_GlobalGame_MarketRemoveSellItem"
pb.SS_GLOBALGAME_MARKETREMOVESELLITEM.full_name = ".SS_GlobalGame_MarketRemoveSellItem"
pb.SS_GLOBALGAME_MARKETREMOVESELLITEM.nested_types = {}
pb.SS_GLOBALGAME_MARKETREMOVESELLITEM.enum_types = {}
pb.SS_GLOBALGAME_MARKETREMOVESELLITEM.fields = {pb.SS_GLOBALGAME_MARKETREMOVESELLITEM_ZONEID_FIELD, pb.SS_GLOBALGAME_MARKETREMOVESELLITEM_ACTORID_FIELD, pb.SS_GLOBALGAME_MARKETREMOVESELLITEM_SELLID_FIELD, pb.SS_GLOBALGAME_MARKETREMOVESELLITEM_GOODSNUM_FIELD}
pb.SS_GLOBALGAME_MARKETREMOVESELLITEM.is_extendable = false
pb.SS_GLOBALGAME_MARKETREMOVESELLITEM.extensions = {}
pb.SS_GLOBALGAME_MARKETREMOVESELLITEMRESULT_ACTORID_FIELD.name = "ActorID"
pb.SS_GLOBALGAME_MARKETREMOVESELLITEMRESULT_ACTORID_FIELD.full_name = ".SS_GlobalGame_MarketRemoveSellItemResult.ActorID"
pb.SS_GLOBALGAME_MARKETREMOVESELLITEMRESULT_ACTORID_FIELD.number = 1
pb.SS_GLOBALGAME_MARKETREMOVESELLITEMRESULT_ACTORID_FIELD.index = 0
pb.SS_GLOBALGAME_MARKETREMOVESELLITEMRESULT_ACTORID_FIELD.label = 1
pb.SS_GLOBALGAME_MARKETREMOVESELLITEMRESULT_ACTORID_FIELD.has_default_value = false
pb.SS_GLOBALGAME_MARKETREMOVESELLITEMRESULT_ACTORID_FIELD.default_value = 0
pb.SS_GLOBALGAME_MARKETREMOVESELLITEMRESULT_ACTORID_FIELD.type = 5
pb.SS_GLOBALGAME_MARKETREMOVESELLITEMRESULT_ACTORID_FIELD.cpp_type = 1

pb.SS_GLOBALGAME_MARKETREMOVESELLITEMRESULT_SELLID_FIELD.name = "SellID"
pb.SS_GLOBALGAME_MARKETREMOVESELLITEMRESULT_SELLID_FIELD.full_name = ".SS_GlobalGame_MarketRemoveSellItemResult.SellID"
pb.SS_GLOBALGAME_MARKETREMOVESELLITEMRESULT_SELLID_FIELD.number = 2
pb.SS_GLOBALGAME_MARKETREMOVESELLITEMRESULT_SELLID_FIELD.index = 1
pb.SS_GLOBALGAME_MARKETREMOVESELLITEMRESULT_SELLID_FIELD.label = 1
pb.SS_GLOBALGAME_MARKETREMOVESELLITEMRESULT_SELLID_FIELD.has_default_value = false
pb.SS_GLOBALGAME_MARKETREMOVESELLITEMRESULT_SELLID_FIELD.default_value = 0
pb.SS_GLOBALGAME_MARKETREMOVESELLITEMRESULT_SELLID_FIELD.type = 5
pb.SS_GLOBALGAME_MARKETREMOVESELLITEMRESULT_SELLID_FIELD.cpp_type = 1

pb.SS_GLOBALGAME_MARKETREMOVESELLITEMRESULT_RESULT_FIELD.name = "Result"
pb.SS_GLOBALGAME_MARKETREMOVESELLITEMRESULT_RESULT_FIELD.full_name = ".SS_GlobalGame_MarketRemoveSellItemResult.Result"
pb.SS_GLOBALGAME_MARKETREMOVESELLITEMRESULT_RESULT_FIELD.number = 3
pb.SS_GLOBALGAME_MARKETREMOVESELLITEMRESULT_RESULT_FIELD.index = 2
pb.SS_GLOBALGAME_MARKETREMOVESELLITEMRESULT_RESULT_FIELD.label = 1
pb.SS_GLOBALGAME_MARKETREMOVESELLITEMRESULT_RESULT_FIELD.has_default_value = false
pb.SS_GLOBALGAME_MARKETREMOVESELLITEMRESULT_RESULT_FIELD.default_value = 0
pb.SS_GLOBALGAME_MARKETREMOVESELLITEMRESULT_RESULT_FIELD.type = 5
pb.SS_GLOBALGAME_MARKETREMOVESELLITEMRESULT_RESULT_FIELD.cpp_type = 1

pb.SS_GLOBALGAME_MARKETREMOVESELLITEMRESULT_GOODSDATA_FIELD.name = "GoodsData"
pb.SS_GLOBALGAME_MARKETREMOVESELLITEMRESULT_GOODSDATA_FIELD.full_name = ".SS_GlobalGame_MarketRemoveSellItemResult.GoodsData"
pb.SS_GLOBALGAME_MARKETREMOVESELLITEMRESULT_GOODSDATA_FIELD.number = 4
pb.SS_GLOBALGAME_MARKETREMOVESELLITEMRESULT_GOODSDATA_FIELD.index = 3
pb.SS_GLOBALGAME_MARKETREMOVESELLITEMRESULT_GOODSDATA_FIELD.label = 1
pb.SS_GLOBALGAME_MARKETREMOVESELLITEMRESULT_GOODSDATA_FIELD.has_default_value = false
pb.SS_GLOBALGAME_MARKETREMOVESELLITEMRESULT_GOODSDATA_FIELD.default_value = ""
pb.SS_GLOBALGAME_MARKETREMOVESELLITEMRESULT_GOODSDATA_FIELD.type = 12
pb.SS_GLOBALGAME_MARKETREMOVESELLITEMRESULT_GOODSDATA_FIELD.cpp_type = 9

pb.SS_GLOBALGAME_MARKETREMOVESELLITEMRESULT.name = "SS_GlobalGame_MarketRemoveSellItemResult"
pb.SS_GLOBALGAME_MARKETREMOVESELLITEMRESULT.full_name = ".SS_GlobalGame_MarketRemoveSellItemResult"
pb.SS_GLOBALGAME_MARKETREMOVESELLITEMRESULT.nested_types = {}
pb.SS_GLOBALGAME_MARKETREMOVESELLITEMRESULT.enum_types = {}
pb.SS_GLOBALGAME_MARKETREMOVESELLITEMRESULT.fields = {pb.SS_GLOBALGAME_MARKETREMOVESELLITEMRESULT_ACTORID_FIELD, pb.SS_GLOBALGAME_MARKETREMOVESELLITEMRESULT_SELLID_FIELD, pb.SS_GLOBALGAME_MARKETREMOVESELLITEMRESULT_RESULT_FIELD, pb.SS_GLOBALGAME_MARKETREMOVESELLITEMRESULT_GOODSDATA_FIELD}
pb.SS_GLOBALGAME_MARKETREMOVESELLITEMRESULT.is_extendable = false
pb.SS_GLOBALGAME_MARKETREMOVESELLITEMRESULT.extensions = {}
pb.SS_GLOBALGAME_MARKETBUYSELLITEM_ZONEID_FIELD.name = "ZoneID"
pb.SS_GLOBALGAME_MARKETBUYSELLITEM_ZONEID_FIELD.full_name = ".SS_GlobalGame_MarketBuySellItem.ZoneID"
pb.SS_GLOBALGAME_MARKETBUYSELLITEM_ZONEID_FIELD.number = 1
pb.SS_GLOBALGAME_MARKETBUYSELLITEM_ZONEID_FIELD.index = 0
pb.SS_GLOBALGAME_MARKETBUYSELLITEM_ZONEID_FIELD.label = 1
pb.SS_GLOBALGAME_MARKETBUYSELLITEM_ZONEID_FIELD.has_default_value = false
pb.SS_GLOBALGAME_MARKETBUYSELLITEM_ZONEID_FIELD.default_value = 0
pb.SS_GLOBALGAME_MARKETBUYSELLITEM_ZONEID_FIELD.type = 5
pb.SS_GLOBALGAME_MARKETBUYSELLITEM_ZONEID_FIELD.cpp_type = 1

pb.SS_GLOBALGAME_MARKETBUYSELLITEM_ACTORID_FIELD.name = "ActorID"
pb.SS_GLOBALGAME_MARKETBUYSELLITEM_ACTORID_FIELD.full_name = ".SS_GlobalGame_MarketBuySellItem.ActorID"
pb.SS_GLOBALGAME_MARKETBUYSELLITEM_ACTORID_FIELD.number = 2
pb.SS_GLOBALGAME_MARKETBUYSELLITEM_ACTORID_FIELD.index = 1
pb.SS_GLOBALGAME_MARKETBUYSELLITEM_ACTORID_FIELD.label = 1
pb.SS_GLOBALGAME_MARKETBUYSELLITEM_ACTORID_FIELD.has_default_value = false
pb.SS_GLOBALGAME_MARKETBUYSELLITEM_ACTORID_FIELD.default_value = 0
pb.SS_GLOBALGAME_MARKETBUYSELLITEM_ACTORID_FIELD.type = 5
pb.SS_GLOBALGAME_MARKETBUYSELLITEM_ACTORID_FIELD.cpp_type = 1

pb.SS_GLOBALGAME_MARKETBUYSELLITEM_SELLID_FIELD.name = "SellID"
pb.SS_GLOBALGAME_MARKETBUYSELLITEM_SELLID_FIELD.full_name = ".SS_GlobalGame_MarketBuySellItem.SellID"
pb.SS_GLOBALGAME_MARKETBUYSELLITEM_SELLID_FIELD.number = 3
pb.SS_GLOBALGAME_MARKETBUYSELLITEM_SELLID_FIELD.index = 2
pb.SS_GLOBALGAME_MARKETBUYSELLITEM_SELLID_FIELD.label = 1
pb.SS_GLOBALGAME_MARKETBUYSELLITEM_SELLID_FIELD.has_default_value = false
pb.SS_GLOBALGAME_MARKETBUYSELLITEM_SELLID_FIELD.default_value = 0
pb.SS_GLOBALGAME_MARKETBUYSELLITEM_SELLID_FIELD.type = 5
pb.SS_GLOBALGAME_MARKETBUYSELLITEM_SELLID_FIELD.cpp_type = 1

pb.SS_GLOBALGAME_MARKETBUYSELLITEM_GOODSNUM_FIELD.name = "GoodsNum"
pb.SS_GLOBALGAME_MARKETBUYSELLITEM_GOODSNUM_FIELD.full_name = ".SS_GlobalGame_MarketBuySellItem.GoodsNum"
pb.SS_GLOBALGAME_MARKETBUYSELLITEM_GOODSNUM_FIELD.number = 4
pb.SS_GLOBALGAME_MARKETBUYSELLITEM_GOODSNUM_FIELD.index = 3
pb.SS_GLOBALGAME_MARKETBUYSELLITEM_GOODSNUM_FIELD.label = 1
pb.SS_GLOBALGAME_MARKETBUYSELLITEM_GOODSNUM_FIELD.has_default_value = false
pb.SS_GLOBALGAME_MARKETBUYSELLITEM_GOODSNUM_FIELD.default_value = 0
pb.SS_GLOBALGAME_MARKETBUYSELLITEM_GOODSNUM_FIELD.type = 5
pb.SS_GLOBALGAME_MARKETBUYSELLITEM_GOODSNUM_FIELD.cpp_type = 1

pb.SS_GLOBALGAME_MARKETBUYSELLITEM_COSTVALUE_FIELD.name = "CostValue"
pb.SS_GLOBALGAME_MARKETBUYSELLITEM_COSTVALUE_FIELD.full_name = ".SS_GlobalGame_MarketBuySellItem.CostValue"
pb.SS_GLOBALGAME_MARKETBUYSELLITEM_COSTVALUE_FIELD.number = 5
pb.SS_GLOBALGAME_MARKETBUYSELLITEM_COSTVALUE_FIELD.index = 4
pb.SS_GLOBALGAME_MARKETBUYSELLITEM_COSTVALUE_FIELD.label = 1
pb.SS_GLOBALGAME_MARKETBUYSELLITEM_COSTVALUE_FIELD.has_default_value = false
pb.SS_GLOBALGAME_MARKETBUYSELLITEM_COSTVALUE_FIELD.default_value = 0
pb.SS_GLOBALGAME_MARKETBUYSELLITEM_COSTVALUE_FIELD.type = 5
pb.SS_GLOBALGAME_MARKETBUYSELLITEM_COSTVALUE_FIELD.cpp_type = 1

pb.SS_GLOBALGAME_MARKETBUYSELLITEM.name = "SS_GlobalGame_MarketBuySellItem"
pb.SS_GLOBALGAME_MARKETBUYSELLITEM.full_name = ".SS_GlobalGame_MarketBuySellItem"
pb.SS_GLOBALGAME_MARKETBUYSELLITEM.nested_types = {}
pb.SS_GLOBALGAME_MARKETBUYSELLITEM.enum_types = {}
pb.SS_GLOBALGAME_MARKETBUYSELLITEM.fields = {pb.SS_GLOBALGAME_MARKETBUYSELLITEM_ZONEID_FIELD, pb.SS_GLOBALGAME_MARKETBUYSELLITEM_ACTORID_FIELD, pb.SS_GLOBALGAME_MARKETBUYSELLITEM_SELLID_FIELD, pb.SS_GLOBALGAME_MARKETBUYSELLITEM_GOODSNUM_FIELD, pb.SS_GLOBALGAME_MARKETBUYSELLITEM_COSTVALUE_FIELD}
pb.SS_GLOBALGAME_MARKETBUYSELLITEM.is_extendable = false
pb.SS_GLOBALGAME_MARKETBUYSELLITEM.extensions = {}
pb.SS_GLOBALGAME_MARKETBUYSELLITEMCHECK_ACTORID_FIELD.name = "ActorID"
pb.SS_GLOBALGAME_MARKETBUYSELLITEMCHECK_ACTORID_FIELD.full_name = ".SS_GlobalGame_MarketBuySellItemCheck.ActorID"
pb.SS_GLOBALGAME_MARKETBUYSELLITEMCHECK_ACTORID_FIELD.number = 1
pb.SS_GLOBALGAME_MARKETBUYSELLITEMCHECK_ACTORID_FIELD.index = 0
pb.SS_GLOBALGAME_MARKETBUYSELLITEMCHECK_ACTORID_FIELD.label = 1
pb.SS_GLOBALGAME_MARKETBUYSELLITEMCHECK_ACTORID_FIELD.has_default_value = false
pb.SS_GLOBALGAME_MARKETBUYSELLITEMCHECK_ACTORID_FIELD.default_value = 0
pb.SS_GLOBALGAME_MARKETBUYSELLITEMCHECK_ACTORID_FIELD.type = 5
pb.SS_GLOBALGAME_MARKETBUYSELLITEMCHECK_ACTORID_FIELD.cpp_type = 1

pb.SS_GLOBALGAME_MARKETBUYSELLITEMCHECK_SELLID_FIELD.name = "SellID"
pb.SS_GLOBALGAME_MARKETBUYSELLITEMCHECK_SELLID_FIELD.full_name = ".SS_GlobalGame_MarketBuySellItemCheck.SellID"
pb.SS_GLOBALGAME_MARKETBUYSELLITEMCHECK_SELLID_FIELD.number = 2
pb.SS_GLOBALGAME_MARKETBUYSELLITEMCHECK_SELLID_FIELD.index = 1
pb.SS_GLOBALGAME_MARKETBUYSELLITEMCHECK_SELLID_FIELD.label = 1
pb.SS_GLOBALGAME_MARKETBUYSELLITEMCHECK_SELLID_FIELD.has_default_value = false
pb.SS_GLOBALGAME_MARKETBUYSELLITEMCHECK_SELLID_FIELD.default_value = 0
pb.SS_GLOBALGAME_MARKETBUYSELLITEMCHECK_SELLID_FIELD.type = 5
pb.SS_GLOBALGAME_MARKETBUYSELLITEMCHECK_SELLID_FIELD.cpp_type = 1

pb.SS_GLOBALGAME_MARKETBUYSELLITEMCHECK_TOTALPRICE_FIELD.name = "TotalPrice"
pb.SS_GLOBALGAME_MARKETBUYSELLITEMCHECK_TOTALPRICE_FIELD.full_name = ".SS_GlobalGame_MarketBuySellItemCheck.TotalPrice"
pb.SS_GLOBALGAME_MARKETBUYSELLITEMCHECK_TOTALPRICE_FIELD.number = 3
pb.SS_GLOBALGAME_MARKETBUYSELLITEMCHECK_TOTALPRICE_FIELD.index = 2
pb.SS_GLOBALGAME_MARKETBUYSELLITEMCHECK_TOTALPRICE_FIELD.label = 1
pb.SS_GLOBALGAME_MARKETBUYSELLITEMCHECK_TOTALPRICE_FIELD.has_default_value = false
pb.SS_GLOBALGAME_MARKETBUYSELLITEMCHECK_TOTALPRICE_FIELD.default_value = 0
pb.SS_GLOBALGAME_MARKETBUYSELLITEMCHECK_TOTALPRICE_FIELD.type = 5
pb.SS_GLOBALGAME_MARKETBUYSELLITEMCHECK_TOTALPRICE_FIELD.cpp_type = 1

pb.SS_GLOBALGAME_MARKETBUYSELLITEMCHECK_BINDRATE_FIELD.name = "BindRate"
pb.SS_GLOBALGAME_MARKETBUYSELLITEMCHECK_BINDRATE_FIELD.full_name = ".SS_GlobalGame_MarketBuySellItemCheck.BindRate"
pb.SS_GLOBALGAME_MARKETBUYSELLITEMCHECK_BINDRATE_FIELD.number = 4
pb.SS_GLOBALGAME_MARKETBUYSELLITEMCHECK_BINDRATE_FIELD.index = 3
pb.SS_GLOBALGAME_MARKETBUYSELLITEMCHECK_BINDRATE_FIELD.label = 1
pb.SS_GLOBALGAME_MARKETBUYSELLITEMCHECK_BINDRATE_FIELD.has_default_value = false
pb.SS_GLOBALGAME_MARKETBUYSELLITEMCHECK_BINDRATE_FIELD.default_value = 0
pb.SS_GLOBALGAME_MARKETBUYSELLITEMCHECK_BINDRATE_FIELD.type = 5
pb.SS_GLOBALGAME_MARKETBUYSELLITEMCHECK_BINDRATE_FIELD.cpp_type = 1

pb.SS_GLOBALGAME_MARKETBUYSELLITEMCHECK_GOODSDATA_FIELD.name = "GoodsData"
pb.SS_GLOBALGAME_MARKETBUYSELLITEMCHECK_GOODSDATA_FIELD.full_name = ".SS_GlobalGame_MarketBuySellItemCheck.GoodsData"
pb.SS_GLOBALGAME_MARKETBUYSELLITEMCHECK_GOODSDATA_FIELD.number = 5
pb.SS_GLOBALGAME_MARKETBUYSELLITEMCHECK_GOODSDATA_FIELD.index = 4
pb.SS_GLOBALGAME_MARKETBUYSELLITEMCHECK_GOODSDATA_FIELD.label = 1
pb.SS_GLOBALGAME_MARKETBUYSELLITEMCHECK_GOODSDATA_FIELD.has_default_value = false
pb.SS_GLOBALGAME_MARKETBUYSELLITEMCHECK_GOODSDATA_FIELD.default_value = ""
pb.SS_GLOBALGAME_MARKETBUYSELLITEMCHECK_GOODSDATA_FIELD.type = 12
pb.SS_GLOBALGAME_MARKETBUYSELLITEMCHECK_GOODSDATA_FIELD.cpp_type = 9

pb.SS_GLOBALGAME_MARKETBUYSELLITEMCHECK_SELLACTORID_FIELD.name = "SellActorID"
pb.SS_GLOBALGAME_MARKETBUYSELLITEMCHECK_SELLACTORID_FIELD.full_name = ".SS_GlobalGame_MarketBuySellItemCheck.SellActorID"
pb.SS_GLOBALGAME_MARKETBUYSELLITEMCHECK_SELLACTORID_FIELD.number = 6
pb.SS_GLOBALGAME_MARKETBUYSELLITEMCHECK_SELLACTORID_FIELD.index = 5
pb.SS_GLOBALGAME_MARKETBUYSELLITEMCHECK_SELLACTORID_FIELD.label = 1
pb.SS_GLOBALGAME_MARKETBUYSELLITEMCHECK_SELLACTORID_FIELD.has_default_value = false
pb.SS_GLOBALGAME_MARKETBUYSELLITEMCHECK_SELLACTORID_FIELD.default_value = 0
pb.SS_GLOBALGAME_MARKETBUYSELLITEMCHECK_SELLACTORID_FIELD.type = 5
pb.SS_GLOBALGAME_MARKETBUYSELLITEMCHECK_SELLACTORID_FIELD.cpp_type = 1

pb.SS_GLOBALGAME_MARKETBUYSELLITEMCHECK.name = "SS_GlobalGame_MarketBuySellItemCheck"
pb.SS_GLOBALGAME_MARKETBUYSELLITEMCHECK.full_name = ".SS_GlobalGame_MarketBuySellItemCheck"
pb.SS_GLOBALGAME_MARKETBUYSELLITEMCHECK.nested_types = {}
pb.SS_GLOBALGAME_MARKETBUYSELLITEMCHECK.enum_types = {}
pb.SS_GLOBALGAME_MARKETBUYSELLITEMCHECK.fields = {pb.SS_GLOBALGAME_MARKETBUYSELLITEMCHECK_ACTORID_FIELD, pb.SS_GLOBALGAME_MARKETBUYSELLITEMCHECK_SELLID_FIELD, pb.SS_GLOBALGAME_MARKETBUYSELLITEMCHECK_TOTALPRICE_FIELD, pb.SS_GLOBALGAME_MARKETBUYSELLITEMCHECK_BINDRATE_FIELD, pb.SS_GLOBALGAME_MARKETBUYSELLITEMCHECK_GOODSDATA_FIELD, pb.SS_GLOBALGAME_MARKETBUYSELLITEMCHECK_SELLACTORID_FIELD}
pb.SS_GLOBALGAME_MARKETBUYSELLITEMCHECK.is_extendable = false
pb.SS_GLOBALGAME_MARKETBUYSELLITEMCHECK.extensions = {}
pb.SS_GLOBALGAME_MARKETBUYSELLITEMCHECKRESULT_ZONEID_FIELD.name = "ZoneID"
pb.SS_GLOBALGAME_MARKETBUYSELLITEMCHECKRESULT_ZONEID_FIELD.full_name = ".SS_GlobalGame_MarketBuySellItemCheckResult.ZoneID"
pb.SS_GLOBALGAME_MARKETBUYSELLITEMCHECKRESULT_ZONEID_FIELD.number = 1
pb.SS_GLOBALGAME_MARKETBUYSELLITEMCHECKRESULT_ZONEID_FIELD.index = 0
pb.SS_GLOBALGAME_MARKETBUYSELLITEMCHECKRESULT_ZONEID_FIELD.label = 1
pb.SS_GLOBALGAME_MARKETBUYSELLITEMCHECKRESULT_ZONEID_FIELD.has_default_value = false
pb.SS_GLOBALGAME_MARKETBUYSELLITEMCHECKRESULT_ZONEID_FIELD.default_value = 0
pb.SS_GLOBALGAME_MARKETBUYSELLITEMCHECKRESULT_ZONEID_FIELD.type = 5
pb.SS_GLOBALGAME_MARKETBUYSELLITEMCHECKRESULT_ZONEID_FIELD.cpp_type = 1

pb.SS_GLOBALGAME_MARKETBUYSELLITEMCHECKRESULT_ACTORID_FIELD.name = "ActorID"
pb.SS_GLOBALGAME_MARKETBUYSELLITEMCHECKRESULT_ACTORID_FIELD.full_name = ".SS_GlobalGame_MarketBuySellItemCheckResult.ActorID"
pb.SS_GLOBALGAME_MARKETBUYSELLITEMCHECKRESULT_ACTORID_FIELD.number = 2
pb.SS_GLOBALGAME_MARKETBUYSELLITEMCHECKRESULT_ACTORID_FIELD.index = 1
pb.SS_GLOBALGAME_MARKETBUYSELLITEMCHECKRESULT_ACTORID_FIELD.label = 1
pb.SS_GLOBALGAME_MARKETBUYSELLITEMCHECKRESULT_ACTORID_FIELD.has_default_value = false
pb.SS_GLOBALGAME_MARKETBUYSELLITEMCHECKRESULT_ACTORID_FIELD.default_value = 0
pb.SS_GLOBALGAME_MARKETBUYSELLITEMCHECKRESULT_ACTORID_FIELD.type = 5
pb.SS_GLOBALGAME_MARKETBUYSELLITEMCHECKRESULT_ACTORID_FIELD.cpp_type = 1

pb.SS_GLOBALGAME_MARKETBUYSELLITEMCHECKRESULT_SELLID_FIELD.name = "SellID"
pb.SS_GLOBALGAME_MARKETBUYSELLITEMCHECKRESULT_SELLID_FIELD.full_name = ".SS_GlobalGame_MarketBuySellItemCheckResult.SellID"
pb.SS_GLOBALGAME_MARKETBUYSELLITEMCHECKRESULT_SELLID_FIELD.number = 3
pb.SS_GLOBALGAME_MARKETBUYSELLITEMCHECKRESULT_SELLID_FIELD.index = 2
pb.SS_GLOBALGAME_MARKETBUYSELLITEMCHECKRESULT_SELLID_FIELD.label = 1
pb.SS_GLOBALGAME_MARKETBUYSELLITEMCHECKRESULT_SELLID_FIELD.has_default_value = false
pb.SS_GLOBALGAME_MARKETBUYSELLITEMCHECKRESULT_SELLID_FIELD.default_value = 0
pb.SS_GLOBALGAME_MARKETBUYSELLITEMCHECKRESULT_SELLID_FIELD.type = 5
pb.SS_GLOBALGAME_MARKETBUYSELLITEMCHECKRESULT_SELLID_FIELD.cpp_type = 1

pb.SS_GLOBALGAME_MARKETBUYSELLITEMCHECKRESULT_RESULT_FIELD.name = "Result"
pb.SS_GLOBALGAME_MARKETBUYSELLITEMCHECKRESULT_RESULT_FIELD.full_name = ".SS_GlobalGame_MarketBuySellItemCheckResult.Result"
pb.SS_GLOBALGAME_MARKETBUYSELLITEMCHECKRESULT_RESULT_FIELD.number = 4
pb.SS_GLOBALGAME_MARKETBUYSELLITEMCHECKRESULT_RESULT_FIELD.index = 3
pb.SS_GLOBALGAME_MARKETBUYSELLITEMCHECKRESULT_RESULT_FIELD.label = 1
pb.SS_GLOBALGAME_MARKETBUYSELLITEMCHECKRESULT_RESULT_FIELD.has_default_value = false
pb.SS_GLOBALGAME_MARKETBUYSELLITEMCHECKRESULT_RESULT_FIELD.default_value = 0
pb.SS_GLOBALGAME_MARKETBUYSELLITEMCHECKRESULT_RESULT_FIELD.type = 5
pb.SS_GLOBALGAME_MARKETBUYSELLITEMCHECKRESULT_RESULT_FIELD.cpp_type = 1

pb.SS_GLOBALGAME_MARKETBUYSELLITEMCHECKRESULT_GOODSNUM_FIELD.name = "GoodsNum"
pb.SS_GLOBALGAME_MARKETBUYSELLITEMCHECKRESULT_GOODSNUM_FIELD.full_name = ".SS_GlobalGame_MarketBuySellItemCheckResult.GoodsNum"
pb.SS_GLOBALGAME_MARKETBUYSELLITEMCHECKRESULT_GOODSNUM_FIELD.number = 5
pb.SS_GLOBALGAME_MARKETBUYSELLITEMCHECKRESULT_GOODSNUM_FIELD.index = 4
pb.SS_GLOBALGAME_MARKETBUYSELLITEMCHECKRESULT_GOODSNUM_FIELD.label = 1
pb.SS_GLOBALGAME_MARKETBUYSELLITEMCHECKRESULT_GOODSNUM_FIELD.has_default_value = false
pb.SS_GLOBALGAME_MARKETBUYSELLITEMCHECKRESULT_GOODSNUM_FIELD.default_value = 0
pb.SS_GLOBALGAME_MARKETBUYSELLITEMCHECKRESULT_GOODSNUM_FIELD.type = 5
pb.SS_GLOBALGAME_MARKETBUYSELLITEMCHECKRESULT_GOODSNUM_FIELD.cpp_type = 1

pb.SS_GLOBALGAME_MARKETBUYSELLITEMCHECKRESULT_TOTALPRICE_FIELD.name = "TotalPrice"
pb.SS_GLOBALGAME_MARKETBUYSELLITEMCHECKRESULT_TOTALPRICE_FIELD.full_name = ".SS_GlobalGame_MarketBuySellItemCheckResult.TotalPrice"
pb.SS_GLOBALGAME_MARKETBUYSELLITEMCHECKRESULT_TOTALPRICE_FIELD.number = 6
pb.SS_GLOBALGAME_MARKETBUYSELLITEMCHECKRESULT_TOTALPRICE_FIELD.index = 5
pb.SS_GLOBALGAME_MARKETBUYSELLITEMCHECKRESULT_TOTALPRICE_FIELD.label = 1
pb.SS_GLOBALGAME_MARKETBUYSELLITEMCHECKRESULT_TOTALPRICE_FIELD.has_default_value = false
pb.SS_GLOBALGAME_MARKETBUYSELLITEMCHECKRESULT_TOTALPRICE_FIELD.default_value = 0
pb.SS_GLOBALGAME_MARKETBUYSELLITEMCHECKRESULT_TOTALPRICE_FIELD.type = 5
pb.SS_GLOBALGAME_MARKETBUYSELLITEMCHECKRESULT_TOTALPRICE_FIELD.cpp_type = 1

pb.SS_GLOBALGAME_MARKETBUYSELLITEMCHECKRESULT.name = "SS_GlobalGame_MarketBuySellItemCheckResult"
pb.SS_GLOBALGAME_MARKETBUYSELLITEMCHECKRESULT.full_name = ".SS_GlobalGame_MarketBuySellItemCheckResult"
pb.SS_GLOBALGAME_MARKETBUYSELLITEMCHECKRESULT.nested_types = {}
pb.SS_GLOBALGAME_MARKETBUYSELLITEMCHECKRESULT.enum_types = {}
pb.SS_GLOBALGAME_MARKETBUYSELLITEMCHECKRESULT.fields = {pb.SS_GLOBALGAME_MARKETBUYSELLITEMCHECKRESULT_ZONEID_FIELD, pb.SS_GLOBALGAME_MARKETBUYSELLITEMCHECKRESULT_ACTORID_FIELD, pb.SS_GLOBALGAME_MARKETBUYSELLITEMCHECKRESULT_SELLID_FIELD, pb.SS_GLOBALGAME_MARKETBUYSELLITEMCHECKRESULT_RESULT_FIELD, pb.SS_GLOBALGAME_MARKETBUYSELLITEMCHECKRESULT_GOODSNUM_FIELD, pb.SS_GLOBALGAME_MARKETBUYSELLITEMCHECKRESULT_TOTALPRICE_FIELD}
pb.SS_GLOBALGAME_MARKETBUYSELLITEMCHECKRESULT.is_extendable = false
pb.SS_GLOBALGAME_MARKETBUYSELLITEMCHECKRESULT.extensions = {}
pb.SS_GLOBALGAME_ACTVCOUNTADD_TARGETTYPE_FIELD.name = "TargetType"
pb.SS_GLOBALGAME_ACTVCOUNTADD_TARGETTYPE_FIELD.full_name = ".SS_GlobalGame_ActvCountAdd.TargetType"
pb.SS_GLOBALGAME_ACTVCOUNTADD_TARGETTYPE_FIELD.number = 1
pb.SS_GLOBALGAME_ACTVCOUNTADD_TARGETTYPE_FIELD.index = 0
pb.SS_GLOBALGAME_ACTVCOUNTADD_TARGETTYPE_FIELD.label = 1
pb.SS_GLOBALGAME_ACTVCOUNTADD_TARGETTYPE_FIELD.has_default_value = false
pb.SS_GLOBALGAME_ACTVCOUNTADD_TARGETTYPE_FIELD.default_value = 0
pb.SS_GLOBALGAME_ACTVCOUNTADD_TARGETTYPE_FIELD.type = 5
pb.SS_GLOBALGAME_ACTVCOUNTADD_TARGETTYPE_FIELD.cpp_type = 1

pb.SS_GLOBALGAME_ACTVCOUNTADD_TARGETID_FIELD.name = "TargetID"
pb.SS_GLOBALGAME_ACTVCOUNTADD_TARGETID_FIELD.full_name = ".SS_GlobalGame_ActvCountAdd.TargetID"
pb.SS_GLOBALGAME_ACTVCOUNTADD_TARGETID_FIELD.number = 2
pb.SS_GLOBALGAME_ACTVCOUNTADD_TARGETID_FIELD.index = 1
pb.SS_GLOBALGAME_ACTVCOUNTADD_TARGETID_FIELD.label = 1
pb.SS_GLOBALGAME_ACTVCOUNTADD_TARGETID_FIELD.has_default_value = false
pb.SS_GLOBALGAME_ACTVCOUNTADD_TARGETID_FIELD.default_value = 0
pb.SS_GLOBALGAME_ACTVCOUNTADD_TARGETID_FIELD.type = 5
pb.SS_GLOBALGAME_ACTVCOUNTADD_TARGETID_FIELD.cpp_type = 1

pb.SS_GLOBALGAME_ACTVCOUNTADD_ADDVALUE_FIELD.name = "AddValue"
pb.SS_GLOBALGAME_ACTVCOUNTADD_ADDVALUE_FIELD.full_name = ".SS_GlobalGame_ActvCountAdd.AddValue"
pb.SS_GLOBALGAME_ACTVCOUNTADD_ADDVALUE_FIELD.number = 3
pb.SS_GLOBALGAME_ACTVCOUNTADD_ADDVALUE_FIELD.index = 2
pb.SS_GLOBALGAME_ACTVCOUNTADD_ADDVALUE_FIELD.label = 1
pb.SS_GLOBALGAME_ACTVCOUNTADD_ADDVALUE_FIELD.has_default_value = false
pb.SS_GLOBALGAME_ACTVCOUNTADD_ADDVALUE_FIELD.default_value = 0
pb.SS_GLOBALGAME_ACTVCOUNTADD_ADDVALUE_FIELD.type = 5
pb.SS_GLOBALGAME_ACTVCOUNTADD_ADDVALUE_FIELD.cpp_type = 1

pb.SS_GLOBALGAME_ACTVCOUNTADD_PARAM1_FIELD.name = "Param1"
pb.SS_GLOBALGAME_ACTVCOUNTADD_PARAM1_FIELD.full_name = ".SS_GlobalGame_ActvCountAdd.Param1"
pb.SS_GLOBALGAME_ACTVCOUNTADD_PARAM1_FIELD.number = 4
pb.SS_GLOBALGAME_ACTVCOUNTADD_PARAM1_FIELD.index = 3
pb.SS_GLOBALGAME_ACTVCOUNTADD_PARAM1_FIELD.label = 1
pb.SS_GLOBALGAME_ACTVCOUNTADD_PARAM1_FIELD.has_default_value = false
pb.SS_GLOBALGAME_ACTVCOUNTADD_PARAM1_FIELD.default_value = 0
pb.SS_GLOBALGAME_ACTVCOUNTADD_PARAM1_FIELD.type = 5
pb.SS_GLOBALGAME_ACTVCOUNTADD_PARAM1_FIELD.cpp_type = 1

pb.SS_GLOBALGAME_ACTVCOUNTADD_PARAM2_FIELD.name = "Param2"
pb.SS_GLOBALGAME_ACTVCOUNTADD_PARAM2_FIELD.full_name = ".SS_GlobalGame_ActvCountAdd.Param2"
pb.SS_GLOBALGAME_ACTVCOUNTADD_PARAM2_FIELD.number = 5
pb.SS_GLOBALGAME_ACTVCOUNTADD_PARAM2_FIELD.index = 4
pb.SS_GLOBALGAME_ACTVCOUNTADD_PARAM2_FIELD.label = 1
pb.SS_GLOBALGAME_ACTVCOUNTADD_PARAM2_FIELD.has_default_value = false
pb.SS_GLOBALGAME_ACTVCOUNTADD_PARAM2_FIELD.default_value = 0
pb.SS_GLOBALGAME_ACTVCOUNTADD_PARAM2_FIELD.type = 5
pb.SS_GLOBALGAME_ACTVCOUNTADD_PARAM2_FIELD.cpp_type = 1

pb.SS_GLOBALGAME_ACTVCOUNTADD.name = "SS_GlobalGame_ActvCountAdd"
pb.SS_GLOBALGAME_ACTVCOUNTADD.full_name = ".SS_GlobalGame_ActvCountAdd"
pb.SS_GLOBALGAME_ACTVCOUNTADD.nested_types = {}
pb.SS_GLOBALGAME_ACTVCOUNTADD.enum_types = {}
pb.SS_GLOBALGAME_ACTVCOUNTADD.fields = {pb.SS_GLOBALGAME_ACTVCOUNTADD_TARGETTYPE_FIELD, pb.SS_GLOBALGAME_ACTVCOUNTADD_TARGETID_FIELD, pb.SS_GLOBALGAME_ACTVCOUNTADD_ADDVALUE_FIELD, pb.SS_GLOBALGAME_ACTVCOUNTADD_PARAM1_FIELD, pb.SS_GLOBALGAME_ACTVCOUNTADD_PARAM2_FIELD}
pb.SS_GLOBALGAME_ACTVCOUNTADD.is_extendable = false
pb.SS_GLOBALGAME_ACTVCOUNTADD.extensions = {}
pb.CS_GLOBAL_SURVIVALCHALLENGEENTER_ACTVID_FIELD.name = "ActvID"
pb.CS_GLOBAL_SURVIVALCHALLENGEENTER_ACTVID_FIELD.full_name = ".CS_Global_SurvivalChallengeEnter.ActvID"
pb.CS_GLOBAL_SURVIVALCHALLENGEENTER_ACTVID_FIELD.number = 1
pb.CS_GLOBAL_SURVIVALCHALLENGEENTER_ACTVID_FIELD.index = 0
pb.CS_GLOBAL_SURVIVALCHALLENGEENTER_ACTVID_FIELD.label = 1
pb.CS_GLOBAL_SURVIVALCHALLENGEENTER_ACTVID_FIELD.has_default_value = false
pb.CS_GLOBAL_SURVIVALCHALLENGEENTER_ACTVID_FIELD.default_value = 0
pb.CS_GLOBAL_SURVIVALCHALLENGEENTER_ACTVID_FIELD.type = 5
pb.CS_GLOBAL_SURVIVALCHALLENGEENTER_ACTVID_FIELD.cpp_type = 1

pb.CS_GLOBAL_SURVIVALCHALLENGEENTER_PARAM_FIELD.name = "Param"
pb.CS_GLOBAL_SURVIVALCHALLENGEENTER_PARAM_FIELD.full_name = ".CS_Global_SurvivalChallengeEnter.Param"
pb.CS_GLOBAL_SURVIVALCHALLENGEENTER_PARAM_FIELD.number = 2
pb.CS_GLOBAL_SURVIVALCHALLENGEENTER_PARAM_FIELD.index = 1
pb.CS_GLOBAL_SURVIVALCHALLENGEENTER_PARAM_FIELD.label = 1
pb.CS_GLOBAL_SURVIVALCHALLENGEENTER_PARAM_FIELD.has_default_value = false
pb.CS_GLOBAL_SURVIVALCHALLENGEENTER_PARAM_FIELD.default_value = 0
pb.CS_GLOBAL_SURVIVALCHALLENGEENTER_PARAM_FIELD.type = 5
pb.CS_GLOBAL_SURVIVALCHALLENGEENTER_PARAM_FIELD.cpp_type = 1

pb.CS_GLOBAL_SURVIVALCHALLENGEENTER.name = "CS_Global_SurvivalChallengeEnter"
pb.CS_GLOBAL_SURVIVALCHALLENGEENTER.full_name = ".CS_Global_SurvivalChallengeEnter"
pb.CS_GLOBAL_SURVIVALCHALLENGEENTER.nested_types = {}
pb.CS_GLOBAL_SURVIVALCHALLENGEENTER.enum_types = {}
pb.CS_GLOBAL_SURVIVALCHALLENGEENTER.fields = {pb.CS_GLOBAL_SURVIVALCHALLENGEENTER_ACTVID_FIELD, pb.CS_GLOBAL_SURVIVALCHALLENGEENTER_PARAM_FIELD}
pb.CS_GLOBAL_SURVIVALCHALLENGEENTER.is_extendable = false
pb.CS_GLOBAL_SURVIVALCHALLENGEENTER.extensions = {}
pb.SS_GLOBAL_SURVIVALCHALLENGEENTER_ACTORID_FIELD.name = "ActorID"
pb.SS_GLOBAL_SURVIVALCHALLENGEENTER_ACTORID_FIELD.full_name = ".SS_Global_SurvivalChallengeEnter.ActorID"
pb.SS_GLOBAL_SURVIVALCHALLENGEENTER_ACTORID_FIELD.number = 1
pb.SS_GLOBAL_SURVIVALCHALLENGEENTER_ACTORID_FIELD.index = 0
pb.SS_GLOBAL_SURVIVALCHALLENGEENTER_ACTORID_FIELD.label = 1
pb.SS_GLOBAL_SURVIVALCHALLENGEENTER_ACTORID_FIELD.has_default_value = false
pb.SS_GLOBAL_SURVIVALCHALLENGEENTER_ACTORID_FIELD.default_value = 0
pb.SS_GLOBAL_SURVIVALCHALLENGEENTER_ACTORID_FIELD.type = 5
pb.SS_GLOBAL_SURVIVALCHALLENGEENTER_ACTORID_FIELD.cpp_type = 1

pb.SS_GLOBAL_SURVIVALCHALLENGEENTER_ACTVID_FIELD.name = "ActvID"
pb.SS_GLOBAL_SURVIVALCHALLENGEENTER_ACTVID_FIELD.full_name = ".SS_Global_SurvivalChallengeEnter.ActvID"
pb.SS_GLOBAL_SURVIVALCHALLENGEENTER_ACTVID_FIELD.number = 2
pb.SS_GLOBAL_SURVIVALCHALLENGEENTER_ACTVID_FIELD.index = 1
pb.SS_GLOBAL_SURVIVALCHALLENGEENTER_ACTVID_FIELD.label = 1
pb.SS_GLOBAL_SURVIVALCHALLENGEENTER_ACTVID_FIELD.has_default_value = false
pb.SS_GLOBAL_SURVIVALCHALLENGEENTER_ACTVID_FIELD.default_value = 0
pb.SS_GLOBAL_SURVIVALCHALLENGEENTER_ACTVID_FIELD.type = 5
pb.SS_GLOBAL_SURVIVALCHALLENGEENTER_ACTVID_FIELD.cpp_type = 1

pb.SS_GLOBAL_SURVIVALCHALLENGEENTER_PARAM_FIELD.name = "Param"
pb.SS_GLOBAL_SURVIVALCHALLENGEENTER_PARAM_FIELD.full_name = ".SS_Global_SurvivalChallengeEnter.Param"
pb.SS_GLOBAL_SURVIVALCHALLENGEENTER_PARAM_FIELD.number = 3
pb.SS_GLOBAL_SURVIVALCHALLENGEENTER_PARAM_FIELD.index = 2
pb.SS_GLOBAL_SURVIVALCHALLENGEENTER_PARAM_FIELD.label = 1
pb.SS_GLOBAL_SURVIVALCHALLENGEENTER_PARAM_FIELD.has_default_value = false
pb.SS_GLOBAL_SURVIVALCHALLENGEENTER_PARAM_FIELD.default_value = 0
pb.SS_GLOBAL_SURVIVALCHALLENGEENTER_PARAM_FIELD.type = 5
pb.SS_GLOBAL_SURVIVALCHALLENGEENTER_PARAM_FIELD.cpp_type = 1

pb.SS_GLOBAL_SURVIVALCHALLENGEENTER.name = "SS_Global_SurvivalChallengeEnter"
pb.SS_GLOBAL_SURVIVALCHALLENGEENTER.full_name = ".SS_Global_SurvivalChallengeEnter"
pb.SS_GLOBAL_SURVIVALCHALLENGEENTER.nested_types = {}
pb.SS_GLOBAL_SURVIVALCHALLENGEENTER.enum_types = {}
pb.SS_GLOBAL_SURVIVALCHALLENGEENTER.fields = {pb.SS_GLOBAL_SURVIVALCHALLENGEENTER_ACTORID_FIELD, pb.SS_GLOBAL_SURVIVALCHALLENGEENTER_ACTVID_FIELD, pb.SS_GLOBAL_SURVIVALCHALLENGEENTER_PARAM_FIELD}
pb.SS_GLOBAL_SURVIVALCHALLENGEENTER.is_extendable = false
pb.SS_GLOBAL_SURVIVALCHALLENGEENTER.extensions = {}
pb.CS_GLOBAL_SURVIVALCHALLENGELEAVE_ACTORID_FIELD.name = "ActorID"
pb.CS_GLOBAL_SURVIVALCHALLENGELEAVE_ACTORID_FIELD.full_name = ".CS_Global_SurvivalChallengeLeave.ActorID"
pb.CS_GLOBAL_SURVIVALCHALLENGELEAVE_ACTORID_FIELD.number = 1
pb.CS_GLOBAL_SURVIVALCHALLENGELEAVE_ACTORID_FIELD.index = 0
pb.CS_GLOBAL_SURVIVALCHALLENGELEAVE_ACTORID_FIELD.label = 1
pb.CS_GLOBAL_SURVIVALCHALLENGELEAVE_ACTORID_FIELD.has_default_value = false
pb.CS_GLOBAL_SURVIVALCHALLENGELEAVE_ACTORID_FIELD.default_value = 0
pb.CS_GLOBAL_SURVIVALCHALLENGELEAVE_ACTORID_FIELD.type = 5
pb.CS_GLOBAL_SURVIVALCHALLENGELEAVE_ACTORID_FIELD.cpp_type = 1

pb.CS_GLOBAL_SURVIVALCHALLENGELEAVE.name = "CS_Global_SurvivalChallengeLeave"
pb.CS_GLOBAL_SURVIVALCHALLENGELEAVE.full_name = ".CS_Global_SurvivalChallengeLeave"
pb.CS_GLOBAL_SURVIVALCHALLENGELEAVE.nested_types = {}
pb.CS_GLOBAL_SURVIVALCHALLENGELEAVE.enum_types = {}
pb.CS_GLOBAL_SURVIVALCHALLENGELEAVE.fields = {pb.CS_GLOBAL_SURVIVALCHALLENGELEAVE_ACTORID_FIELD}
pb.CS_GLOBAL_SURVIVALCHALLENGELEAVE.is_extendable = false
pb.CS_GLOBAL_SURVIVALCHALLENGELEAVE.extensions = {}
pb.SS_GLOBALGAME_ROBOTSWITCHZONE_ACTORID_FIELD.name = "ActorID"
pb.SS_GLOBALGAME_ROBOTSWITCHZONE_ACTORID_FIELD.full_name = ".SS_GlobalGame_RobotSwitchZone.ActorID"
pb.SS_GLOBALGAME_ROBOTSWITCHZONE_ACTORID_FIELD.number = 1
pb.SS_GLOBALGAME_ROBOTSWITCHZONE_ACTORID_FIELD.index = 0
pb.SS_GLOBALGAME_ROBOTSWITCHZONE_ACTORID_FIELD.label = 1
pb.SS_GLOBALGAME_ROBOTSWITCHZONE_ACTORID_FIELD.has_default_value = false
pb.SS_GLOBALGAME_ROBOTSWITCHZONE_ACTORID_FIELD.default_value = 0
pb.SS_GLOBALGAME_ROBOTSWITCHZONE_ACTORID_FIELD.type = 5
pb.SS_GLOBALGAME_ROBOTSWITCHZONE_ACTORID_FIELD.cpp_type = 1

pb.SS_GLOBALGAME_ROBOTSWITCHZONE_ZONEID_FIELD.name = "ZoneID"
pb.SS_GLOBALGAME_ROBOTSWITCHZONE_ZONEID_FIELD.full_name = ".SS_GlobalGame_RobotSwitchZone.ZoneID"
pb.SS_GLOBALGAME_ROBOTSWITCHZONE_ZONEID_FIELD.number = 2
pb.SS_GLOBALGAME_ROBOTSWITCHZONE_ZONEID_FIELD.index = 1
pb.SS_GLOBALGAME_ROBOTSWITCHZONE_ZONEID_FIELD.label = 1
pb.SS_GLOBALGAME_ROBOTSWITCHZONE_ZONEID_FIELD.has_default_value = false
pb.SS_GLOBALGAME_ROBOTSWITCHZONE_ZONEID_FIELD.default_value = 0
pb.SS_GLOBALGAME_ROBOTSWITCHZONE_ZONEID_FIELD.type = 5
pb.SS_GLOBALGAME_ROBOTSWITCHZONE_ZONEID_FIELD.cpp_type = 1

pb.SS_GLOBALGAME_ROBOTSWITCHZONE_ROBOTTYPE_FIELD.name = "RobotType"
pb.SS_GLOBALGAME_ROBOTSWITCHZONE_ROBOTTYPE_FIELD.full_name = ".SS_GlobalGame_RobotSwitchZone.RobotType"
pb.SS_GLOBALGAME_ROBOTSWITCHZONE_ROBOTTYPE_FIELD.number = 3
pb.SS_GLOBALGAME_ROBOTSWITCHZONE_ROBOTTYPE_FIELD.index = 2
pb.SS_GLOBALGAME_ROBOTSWITCHZONE_ROBOTTYPE_FIELD.label = 1
pb.SS_GLOBALGAME_ROBOTSWITCHZONE_ROBOTTYPE_FIELD.has_default_value = false
pb.SS_GLOBALGAME_ROBOTSWITCHZONE_ROBOTTYPE_FIELD.default_value = 0
pb.SS_GLOBALGAME_ROBOTSWITCHZONE_ROBOTTYPE_FIELD.type = 5
pb.SS_GLOBALGAME_ROBOTSWITCHZONE_ROBOTTYPE_FIELD.cpp_type = 1

pb.SS_GLOBALGAME_ROBOTSWITCHZONE.name = "SS_GlobalGame_RobotSwitchZone"
pb.SS_GLOBALGAME_ROBOTSWITCHZONE.full_name = ".SS_GlobalGame_RobotSwitchZone"
pb.SS_GLOBALGAME_ROBOTSWITCHZONE.nested_types = {}
pb.SS_GLOBALGAME_ROBOTSWITCHZONE.enum_types = {}
pb.SS_GLOBALGAME_ROBOTSWITCHZONE.fields = {pb.SS_GLOBALGAME_ROBOTSWITCHZONE_ACTORID_FIELD, pb.SS_GLOBALGAME_ROBOTSWITCHZONE_ZONEID_FIELD, pb.SS_GLOBALGAME_ROBOTSWITCHZONE_ROBOTTYPE_FIELD}
pb.SS_GLOBALGAME_ROBOTSWITCHZONE.is_extendable = false
pb.SS_GLOBALGAME_ROBOTSWITCHZONE.extensions = {}
pb.SS_GLOBALGAME_ROBOTSWITCHZONELOGIN_ACTORID_FIELD.name = "ActorID"
pb.SS_GLOBALGAME_ROBOTSWITCHZONELOGIN_ACTORID_FIELD.full_name = ".SS_GlobalGame_RobotSwitchZoneLogin.ActorID"
pb.SS_GLOBALGAME_ROBOTSWITCHZONELOGIN_ACTORID_FIELD.number = 1
pb.SS_GLOBALGAME_ROBOTSWITCHZONELOGIN_ACTORID_FIELD.index = 0
pb.SS_GLOBALGAME_ROBOTSWITCHZONELOGIN_ACTORID_FIELD.label = 1
pb.SS_GLOBALGAME_ROBOTSWITCHZONELOGIN_ACTORID_FIELD.has_default_value = false
pb.SS_GLOBALGAME_ROBOTSWITCHZONELOGIN_ACTORID_FIELD.default_value = 0
pb.SS_GLOBALGAME_ROBOTSWITCHZONELOGIN_ACTORID_FIELD.type = 5
pb.SS_GLOBALGAME_ROBOTSWITCHZONELOGIN_ACTORID_FIELD.cpp_type = 1

pb.SS_GLOBALGAME_ROBOTSWITCHZONELOGIN_ROBOTTYPE_FIELD.name = "RobotType"
pb.SS_GLOBALGAME_ROBOTSWITCHZONELOGIN_ROBOTTYPE_FIELD.full_name = ".SS_GlobalGame_RobotSwitchZoneLogin.RobotType"
pb.SS_GLOBALGAME_ROBOTSWITCHZONELOGIN_ROBOTTYPE_FIELD.number = 2
pb.SS_GLOBALGAME_ROBOTSWITCHZONELOGIN_ROBOTTYPE_FIELD.index = 1
pb.SS_GLOBALGAME_ROBOTSWITCHZONELOGIN_ROBOTTYPE_FIELD.label = 1
pb.SS_GLOBALGAME_ROBOTSWITCHZONELOGIN_ROBOTTYPE_FIELD.has_default_value = false
pb.SS_GLOBALGAME_ROBOTSWITCHZONELOGIN_ROBOTTYPE_FIELD.default_value = 0
pb.SS_GLOBALGAME_ROBOTSWITCHZONELOGIN_ROBOTTYPE_FIELD.type = 5
pb.SS_GLOBALGAME_ROBOTSWITCHZONELOGIN_ROBOTTYPE_FIELD.cpp_type = 1

pb.SS_GLOBALGAME_ROBOTSWITCHZONELOGIN.name = "SS_GlobalGame_RobotSwitchZoneLogin"
pb.SS_GLOBALGAME_ROBOTSWITCHZONELOGIN.full_name = ".SS_GlobalGame_RobotSwitchZoneLogin"
pb.SS_GLOBALGAME_ROBOTSWITCHZONELOGIN.nested_types = {}
pb.SS_GLOBALGAME_ROBOTSWITCHZONELOGIN.enum_types = {}
pb.SS_GLOBALGAME_ROBOTSWITCHZONELOGIN.fields = {pb.SS_GLOBALGAME_ROBOTSWITCHZONELOGIN_ACTORID_FIELD, pb.SS_GLOBALGAME_ROBOTSWITCHZONELOGIN_ROBOTTYPE_FIELD}
pb.SS_GLOBALGAME_ROBOTSWITCHZONELOGIN.is_extendable = false
pb.SS_GLOBALGAME_ROBOTSWITCHZONELOGIN.extensions = {}
pb.SS_GLOBAL_TRAININGGROUND_PLAYERSIMPLE_ACTORID_FIELD.name = "ActorID"
pb.SS_GLOBAL_TRAININGGROUND_PLAYERSIMPLE_ACTORID_FIELD.full_name = ".SS_Global_TrainingGround_PlayerSimple.ActorID"
pb.SS_GLOBAL_TRAININGGROUND_PLAYERSIMPLE_ACTORID_FIELD.number = 1
pb.SS_GLOBAL_TRAININGGROUND_PLAYERSIMPLE_ACTORID_FIELD.index = 0
pb.SS_GLOBAL_TRAININGGROUND_PLAYERSIMPLE_ACTORID_FIELD.label = 1
pb.SS_GLOBAL_TRAININGGROUND_PLAYERSIMPLE_ACTORID_FIELD.has_default_value = false
pb.SS_GLOBAL_TRAININGGROUND_PLAYERSIMPLE_ACTORID_FIELD.default_value = 0
pb.SS_GLOBAL_TRAININGGROUND_PLAYERSIMPLE_ACTORID_FIELD.type = 5
pb.SS_GLOBAL_TRAININGGROUND_PLAYERSIMPLE_ACTORID_FIELD.cpp_type = 1

pb.SS_GLOBAL_TRAININGGROUND_PLAYERSIMPLE_ZONEID_FIELD.name = "ZoneID"
pb.SS_GLOBAL_TRAININGGROUND_PLAYERSIMPLE_ZONEID_FIELD.full_name = ".SS_Global_TrainingGround_PlayerSimple.ZoneID"
pb.SS_GLOBAL_TRAININGGROUND_PLAYERSIMPLE_ZONEID_FIELD.number = 2
pb.SS_GLOBAL_TRAININGGROUND_PLAYERSIMPLE_ZONEID_FIELD.index = 1
pb.SS_GLOBAL_TRAININGGROUND_PLAYERSIMPLE_ZONEID_FIELD.label = 1
pb.SS_GLOBAL_TRAININGGROUND_PLAYERSIMPLE_ZONEID_FIELD.has_default_value = false
pb.SS_GLOBAL_TRAININGGROUND_PLAYERSIMPLE_ZONEID_FIELD.default_value = 0
pb.SS_GLOBAL_TRAININGGROUND_PLAYERSIMPLE_ZONEID_FIELD.type = 5
pb.SS_GLOBAL_TRAININGGROUND_PLAYERSIMPLE_ZONEID_FIELD.cpp_type = 1

pb.SS_GLOBAL_TRAININGGROUND_PLAYERSIMPLE_ACTORNAME_FIELD.name = "ActorName"
pb.SS_GLOBAL_TRAININGGROUND_PLAYERSIMPLE_ACTORNAME_FIELD.full_name = ".SS_Global_TrainingGround_PlayerSimple.ActorName"
pb.SS_GLOBAL_TRAININGGROUND_PLAYERSIMPLE_ACTORNAME_FIELD.number = 3
pb.SS_GLOBAL_TRAININGGROUND_PLAYERSIMPLE_ACTORNAME_FIELD.index = 2
pb.SS_GLOBAL_TRAININGGROUND_PLAYERSIMPLE_ACTORNAME_FIELD.label = 1
pb.SS_GLOBAL_TRAININGGROUND_PLAYERSIMPLE_ACTORNAME_FIELD.has_default_value = false
pb.SS_GLOBAL_TRAININGGROUND_PLAYERSIMPLE_ACTORNAME_FIELD.default_value = ""
pb.SS_GLOBAL_TRAININGGROUND_PLAYERSIMPLE_ACTORNAME_FIELD.type = 9
pb.SS_GLOBAL_TRAININGGROUND_PLAYERSIMPLE_ACTORNAME_FIELD.cpp_type = 9

pb.SS_GLOBAL_TRAININGGROUND_PLAYERSIMPLE_ACTORVOCATION_FIELD.name = "ActorVocation"
pb.SS_GLOBAL_TRAININGGROUND_PLAYERSIMPLE_ACTORVOCATION_FIELD.full_name = ".SS_Global_TrainingGround_PlayerSimple.ActorVocation"
pb.SS_GLOBAL_TRAININGGROUND_PLAYERSIMPLE_ACTORVOCATION_FIELD.number = 4
pb.SS_GLOBAL_TRAININGGROUND_PLAYERSIMPLE_ACTORVOCATION_FIELD.index = 3
pb.SS_GLOBAL_TRAININGGROUND_PLAYERSIMPLE_ACTORVOCATION_FIELD.label = 1
pb.SS_GLOBAL_TRAININGGROUND_PLAYERSIMPLE_ACTORVOCATION_FIELD.has_default_value = false
pb.SS_GLOBAL_TRAININGGROUND_PLAYERSIMPLE_ACTORVOCATION_FIELD.default_value = 0
pb.SS_GLOBAL_TRAININGGROUND_PLAYERSIMPLE_ACTORVOCATION_FIELD.type = 5
pb.SS_GLOBAL_TRAININGGROUND_PLAYERSIMPLE_ACTORVOCATION_FIELD.cpp_type = 1

pb.SS_GLOBAL_TRAININGGROUND_PLAYERSIMPLE_ACTORCOUNTRY_FIELD.name = "ActorCountry"
pb.SS_GLOBAL_TRAININGGROUND_PLAYERSIMPLE_ACTORCOUNTRY_FIELD.full_name = ".SS_Global_TrainingGround_PlayerSimple.ActorCountry"
pb.SS_GLOBAL_TRAININGGROUND_PLAYERSIMPLE_ACTORCOUNTRY_FIELD.number = 5
pb.SS_GLOBAL_TRAININGGROUND_PLAYERSIMPLE_ACTORCOUNTRY_FIELD.index = 4
pb.SS_GLOBAL_TRAININGGROUND_PLAYERSIMPLE_ACTORCOUNTRY_FIELD.label = 1
pb.SS_GLOBAL_TRAININGGROUND_PLAYERSIMPLE_ACTORCOUNTRY_FIELD.has_default_value = false
pb.SS_GLOBAL_TRAININGGROUND_PLAYERSIMPLE_ACTORCOUNTRY_FIELD.default_value = 0
pb.SS_GLOBAL_TRAININGGROUND_PLAYERSIMPLE_ACTORCOUNTRY_FIELD.type = 5
pb.SS_GLOBAL_TRAININGGROUND_PLAYERSIMPLE_ACTORCOUNTRY_FIELD.cpp_type = 1

pb.SS_GLOBAL_TRAININGGROUND_PLAYERSIMPLE_ACTORLEVEL_FIELD.name = "ActorLevel"
pb.SS_GLOBAL_TRAININGGROUND_PLAYERSIMPLE_ACTORLEVEL_FIELD.full_name = ".SS_Global_TrainingGround_PlayerSimple.ActorLevel"
pb.SS_GLOBAL_TRAININGGROUND_PLAYERSIMPLE_ACTORLEVEL_FIELD.number = 6
pb.SS_GLOBAL_TRAININGGROUND_PLAYERSIMPLE_ACTORLEVEL_FIELD.index = 5
pb.SS_GLOBAL_TRAININGGROUND_PLAYERSIMPLE_ACTORLEVEL_FIELD.label = 1
pb.SS_GLOBAL_TRAININGGROUND_PLAYERSIMPLE_ACTORLEVEL_FIELD.has_default_value = false
pb.SS_GLOBAL_TRAININGGROUND_PLAYERSIMPLE_ACTORLEVEL_FIELD.default_value = 0
pb.SS_GLOBAL_TRAININGGROUND_PLAYERSIMPLE_ACTORLEVEL_FIELD.type = 5
pb.SS_GLOBAL_TRAININGGROUND_PLAYERSIMPLE_ACTORLEVEL_FIELD.cpp_type = 1

pb.SS_GLOBAL_TRAININGGROUND_PLAYERSIMPLE_POWER_FIELD.name = "Power"
pb.SS_GLOBAL_TRAININGGROUND_PLAYERSIMPLE_POWER_FIELD.full_name = ".SS_Global_TrainingGround_PlayerSimple.Power"
pb.SS_GLOBAL_TRAININGGROUND_PLAYERSIMPLE_POWER_FIELD.number = 7
pb.SS_GLOBAL_TRAININGGROUND_PLAYERSIMPLE_POWER_FIELD.index = 6
pb.SS_GLOBAL_TRAININGGROUND_PLAYERSIMPLE_POWER_FIELD.label = 1
pb.SS_GLOBAL_TRAININGGROUND_PLAYERSIMPLE_POWER_FIELD.has_default_value = false
pb.SS_GLOBAL_TRAININGGROUND_PLAYERSIMPLE_POWER_FIELD.default_value = 0
pb.SS_GLOBAL_TRAININGGROUND_PLAYERSIMPLE_POWER_FIELD.type = 5
pb.SS_GLOBAL_TRAININGGROUND_PLAYERSIMPLE_POWER_FIELD.cpp_type = 1

pb.SS_GLOBAL_TRAININGGROUND_PLAYERSIMPLE_CURHP_FIELD.name = "CurHp"
pb.SS_GLOBAL_TRAININGGROUND_PLAYERSIMPLE_CURHP_FIELD.full_name = ".SS_Global_TrainingGround_PlayerSimple.CurHp"
pb.SS_GLOBAL_TRAININGGROUND_PLAYERSIMPLE_CURHP_FIELD.number = 8
pb.SS_GLOBAL_TRAININGGROUND_PLAYERSIMPLE_CURHP_FIELD.index = 7
pb.SS_GLOBAL_TRAININGGROUND_PLAYERSIMPLE_CURHP_FIELD.label = 1
pb.SS_GLOBAL_TRAININGGROUND_PLAYERSIMPLE_CURHP_FIELD.has_default_value = false
pb.SS_GLOBAL_TRAININGGROUND_PLAYERSIMPLE_CURHP_FIELD.default_value = 0
pb.SS_GLOBAL_TRAININGGROUND_PLAYERSIMPLE_CURHP_FIELD.type = 5
pb.SS_GLOBAL_TRAININGGROUND_PLAYERSIMPLE_CURHP_FIELD.cpp_type = 1

pb.SS_GLOBAL_TRAININGGROUND_PLAYERSIMPLE_MAXHP_FIELD.name = "MaxHp"
pb.SS_GLOBAL_TRAININGGROUND_PLAYERSIMPLE_MAXHP_FIELD.full_name = ".SS_Global_TrainingGround_PlayerSimple.MaxHp"
pb.SS_GLOBAL_TRAININGGROUND_PLAYERSIMPLE_MAXHP_FIELD.number = 9
pb.SS_GLOBAL_TRAININGGROUND_PLAYERSIMPLE_MAXHP_FIELD.index = 8
pb.SS_GLOBAL_TRAININGGROUND_PLAYERSIMPLE_MAXHP_FIELD.label = 1
pb.SS_GLOBAL_TRAININGGROUND_PLAYERSIMPLE_MAXHP_FIELD.has_default_value = false
pb.SS_GLOBAL_TRAININGGROUND_PLAYERSIMPLE_MAXHP_FIELD.default_value = 0
pb.SS_GLOBAL_TRAININGGROUND_PLAYERSIMPLE_MAXHP_FIELD.type = 5
pb.SS_GLOBAL_TRAININGGROUND_PLAYERSIMPLE_MAXHP_FIELD.cpp_type = 1

pb.SS_GLOBAL_TRAININGGROUND_PLAYERSIMPLE.name = "SS_Global_TrainingGround_PlayerSimple"
pb.SS_GLOBAL_TRAININGGROUND_PLAYERSIMPLE.full_name = ".SS_Global_TrainingGround_PlayerSimple"
pb.SS_GLOBAL_TRAININGGROUND_PLAYERSIMPLE.nested_types = {}
pb.SS_GLOBAL_TRAININGGROUND_PLAYERSIMPLE.enum_types = {}
pb.SS_GLOBAL_TRAININGGROUND_PLAYERSIMPLE.fields = {pb.SS_GLOBAL_TRAININGGROUND_PLAYERSIMPLE_ACTORID_FIELD, pb.SS_GLOBAL_TRAININGGROUND_PLAYERSIMPLE_ZONEID_FIELD, pb.SS_GLOBAL_TRAININGGROUND_PLAYERSIMPLE_ACTORNAME_FIELD, pb.SS_GLOBAL_TRAININGGROUND_PLAYERSIMPLE_ACTORVOCATION_FIELD, pb.SS_GLOBAL_TRAININGGROUND_PLAYERSIMPLE_ACTORCOUNTRY_FIELD, pb.SS_GLOBAL_TRAININGGROUND_PLAYERSIMPLE_ACTORLEVEL_FIELD, pb.SS_GLOBAL_TRAININGGROUND_PLAYERSIMPLE_POWER_FIELD, pb.SS_GLOBAL_TRAININGGROUND_PLAYERSIMPLE_CURHP_FIELD, pb.SS_GLOBAL_TRAININGGROUND_PLAYERSIMPLE_MAXHP_FIELD}
pb.SS_GLOBAL_TRAININGGROUND_PLAYERSIMPLE.is_extendable = false
pb.SS_GLOBAL_TRAININGGROUND_PLAYERSIMPLE.extensions = {}
pb.SS_GLOBAL_TRAININGGROUNDENTER_ACTORID_FIELD.name = "ActorID"
pb.SS_GLOBAL_TRAININGGROUNDENTER_ACTORID_FIELD.full_name = ".SS_Global_TrainingGroundEnter.ActorID"
pb.SS_GLOBAL_TRAININGGROUNDENTER_ACTORID_FIELD.number = 1
pb.SS_GLOBAL_TRAININGGROUNDENTER_ACTORID_FIELD.index = 0
pb.SS_GLOBAL_TRAININGGROUNDENTER_ACTORID_FIELD.label = 1
pb.SS_GLOBAL_TRAININGGROUNDENTER_ACTORID_FIELD.has_default_value = false
pb.SS_GLOBAL_TRAININGGROUNDENTER_ACTORID_FIELD.default_value = 0
pb.SS_GLOBAL_TRAININGGROUNDENTER_ACTORID_FIELD.type = 5
pb.SS_GLOBAL_TRAININGGROUNDENTER_ACTORID_FIELD.cpp_type = 1

pb.SS_GLOBAL_TRAININGGROUNDENTER_TARACTORID_FIELD.name = "TarActorID"
pb.SS_GLOBAL_TRAININGGROUNDENTER_TARACTORID_FIELD.full_name = ".SS_Global_TrainingGroundEnter.TarActorID"
pb.SS_GLOBAL_TRAININGGROUNDENTER_TARACTORID_FIELD.number = 2
pb.SS_GLOBAL_TRAININGGROUNDENTER_TARACTORID_FIELD.index = 1
pb.SS_GLOBAL_TRAININGGROUNDENTER_TARACTORID_FIELD.label = 1
pb.SS_GLOBAL_TRAININGGROUNDENTER_TARACTORID_FIELD.has_default_value = false
pb.SS_GLOBAL_TRAININGGROUNDENTER_TARACTORID_FIELD.default_value = 0
pb.SS_GLOBAL_TRAININGGROUNDENTER_TARACTORID_FIELD.type = 13
pb.SS_GLOBAL_TRAININGGROUNDENTER_TARACTORID_FIELD.cpp_type = 3

pb.SS_GLOBAL_TRAININGGROUNDENTER_MININGPOS_FIELD.name = "MiningPos"
pb.SS_GLOBAL_TRAININGGROUNDENTER_MININGPOS_FIELD.full_name = ".SS_Global_TrainingGroundEnter.MiningPos"
pb.SS_GLOBAL_TRAININGGROUNDENTER_MININGPOS_FIELD.number = 3
pb.SS_GLOBAL_TRAININGGROUNDENTER_MININGPOS_FIELD.index = 2
pb.SS_GLOBAL_TRAININGGROUNDENTER_MININGPOS_FIELD.label = 1
pb.SS_GLOBAL_TRAININGGROUNDENTER_MININGPOS_FIELD.has_default_value = false
pb.SS_GLOBAL_TRAININGGROUNDENTER_MININGPOS_FIELD.default_value = 0
pb.SS_GLOBAL_TRAININGGROUNDENTER_MININGPOS_FIELD.type = 5
pb.SS_GLOBAL_TRAININGGROUNDENTER_MININGPOS_FIELD.cpp_type = 1

pb.SS_GLOBAL_TRAININGGROUNDENTER_MININGID_FIELD.name = "MiningID"
pb.SS_GLOBAL_TRAININGGROUNDENTER_MININGID_FIELD.full_name = ".SS_Global_TrainingGroundEnter.MiningID"
pb.SS_GLOBAL_TRAININGGROUNDENTER_MININGID_FIELD.number = 4
pb.SS_GLOBAL_TRAININGGROUNDENTER_MININGID_FIELD.index = 3
pb.SS_GLOBAL_TRAININGGROUNDENTER_MININGID_FIELD.label = 1
pb.SS_GLOBAL_TRAININGGROUNDENTER_MININGID_FIELD.has_default_value = false
pb.SS_GLOBAL_TRAININGGROUNDENTER_MININGID_FIELD.default_value = 0
pb.SS_GLOBAL_TRAININGGROUNDENTER_MININGID_FIELD.type = 5
pb.SS_GLOBAL_TRAININGGROUNDENTER_MININGID_FIELD.cpp_type = 1

pb.SS_GLOBAL_TRAININGGROUNDENTER_BATTLETYPE_FIELD.name = "BattleType"
pb.SS_GLOBAL_TRAININGGROUNDENTER_BATTLETYPE_FIELD.full_name = ".SS_Global_TrainingGroundEnter.BattleType"
pb.SS_GLOBAL_TRAININGGROUNDENTER_BATTLETYPE_FIELD.number = 5
pb.SS_GLOBAL_TRAININGGROUNDENTER_BATTLETYPE_FIELD.index = 4
pb.SS_GLOBAL_TRAININGGROUNDENTER_BATTLETYPE_FIELD.label = 1
pb.SS_GLOBAL_TRAININGGROUNDENTER_BATTLETYPE_FIELD.has_default_value = false
pb.SS_GLOBAL_TRAININGGROUNDENTER_BATTLETYPE_FIELD.default_value = 0
pb.SS_GLOBAL_TRAININGGROUNDENTER_BATTLETYPE_FIELD.type = 5
pb.SS_GLOBAL_TRAININGGROUNDENTER_BATTLETYPE_FIELD.cpp_type = 1

pb.SS_GLOBAL_TRAININGGROUNDENTER.name = "SS_Global_TrainingGroundEnter"
pb.SS_GLOBAL_TRAININGGROUNDENTER.full_name = ".SS_Global_TrainingGroundEnter"
pb.SS_GLOBAL_TRAININGGROUNDENTER.nested_types = {}
pb.SS_GLOBAL_TRAININGGROUNDENTER.enum_types = {}
pb.SS_GLOBAL_TRAININGGROUNDENTER.fields = {pb.SS_GLOBAL_TRAININGGROUNDENTER_ACTORID_FIELD, pb.SS_GLOBAL_TRAININGGROUNDENTER_TARACTORID_FIELD, pb.SS_GLOBAL_TRAININGGROUNDENTER_MININGPOS_FIELD, pb.SS_GLOBAL_TRAININGGROUNDENTER_MININGID_FIELD, pb.SS_GLOBAL_TRAININGGROUNDENTER_BATTLETYPE_FIELD}
pb.SS_GLOBAL_TRAININGGROUNDENTER.is_extendable = false
pb.SS_GLOBAL_TRAININGGROUNDENTER.extensions = {}
pb.SS_GLOBAL_TRAININGGROUND_AWARD_RESDATA_RESNUM_FIELD.name = "ResNum"
pb.SS_GLOBAL_TRAININGGROUND_AWARD_RESDATA_RESNUM_FIELD.full_name = ".SS_Global_TrainingGround_Award.ResData.ResNum"
pb.SS_GLOBAL_TRAININGGROUND_AWARD_RESDATA_RESNUM_FIELD.number = 1
pb.SS_GLOBAL_TRAININGGROUND_AWARD_RESDATA_RESNUM_FIELD.index = 0
pb.SS_GLOBAL_TRAININGGROUND_AWARD_RESDATA_RESNUM_FIELD.label = 1
pb.SS_GLOBAL_TRAININGGROUND_AWARD_RESDATA_RESNUM_FIELD.has_default_value = false
pb.SS_GLOBAL_TRAININGGROUND_AWARD_RESDATA_RESNUM_FIELD.default_value = 0
pb.SS_GLOBAL_TRAININGGROUND_AWARD_RESDATA_RESNUM_FIELD.type = 5
pb.SS_GLOBAL_TRAININGGROUND_AWARD_RESDATA_RESNUM_FIELD.cpp_type = 1

pb.SS_GLOBAL_TRAININGGROUND_AWARD_RESDATA_RESTYPE_FIELD.name = "ResType"
pb.SS_GLOBAL_TRAININGGROUND_AWARD_RESDATA_RESTYPE_FIELD.full_name = ".SS_Global_TrainingGround_Award.ResData.ResType"
pb.SS_GLOBAL_TRAININGGROUND_AWARD_RESDATA_RESTYPE_FIELD.number = 2
pb.SS_GLOBAL_TRAININGGROUND_AWARD_RESDATA_RESTYPE_FIELD.index = 1
pb.SS_GLOBAL_TRAININGGROUND_AWARD_RESDATA_RESTYPE_FIELD.label = 1
pb.SS_GLOBAL_TRAININGGROUND_AWARD_RESDATA_RESTYPE_FIELD.has_default_value = false
pb.SS_GLOBAL_TRAININGGROUND_AWARD_RESDATA_RESTYPE_FIELD.default_value = 0
pb.SS_GLOBAL_TRAININGGROUND_AWARD_RESDATA_RESTYPE_FIELD.type = 5
pb.SS_GLOBAL_TRAININGGROUND_AWARD_RESDATA_RESTYPE_FIELD.cpp_type = 1

pb.SS_GLOBAL_TRAININGGROUND_AWARD_RESDATA_BATTLETYPE_FIELD.name = "BattleType"
pb.SS_GLOBAL_TRAININGGROUND_AWARD_RESDATA_BATTLETYPE_FIELD.full_name = ".SS_Global_TrainingGround_Award.ResData.BattleType"
pb.SS_GLOBAL_TRAININGGROUND_AWARD_RESDATA_BATTLETYPE_FIELD.number = 3
pb.SS_GLOBAL_TRAININGGROUND_AWARD_RESDATA_BATTLETYPE_FIELD.index = 2
pb.SS_GLOBAL_TRAININGGROUND_AWARD_RESDATA_BATTLETYPE_FIELD.label = 1
pb.SS_GLOBAL_TRAININGGROUND_AWARD_RESDATA_BATTLETYPE_FIELD.has_default_value = false
pb.SS_GLOBAL_TRAININGGROUND_AWARD_RESDATA_BATTLETYPE_FIELD.default_value = 0
pb.SS_GLOBAL_TRAININGGROUND_AWARD_RESDATA_BATTLETYPE_FIELD.type = 5
pb.SS_GLOBAL_TRAININGGROUND_AWARD_RESDATA_BATTLETYPE_FIELD.cpp_type = 1

pb.SS_GLOBAL_TRAININGGROUND_AWARD_RESDATA.name = "ResData"
pb.SS_GLOBAL_TRAININGGROUND_AWARD_RESDATA.full_name = ".SS_Global_TrainingGround_Award.ResData"
pb.SS_GLOBAL_TRAININGGROUND_AWARD_RESDATA.nested_types = {}
pb.SS_GLOBAL_TRAININGGROUND_AWARD_RESDATA.enum_types = {}
pb.SS_GLOBAL_TRAININGGROUND_AWARD_RESDATA.fields = {pb.SS_GLOBAL_TRAININGGROUND_AWARD_RESDATA_RESNUM_FIELD, pb.SS_GLOBAL_TRAININGGROUND_AWARD_RESDATA_RESTYPE_FIELD, pb.SS_GLOBAL_TRAININGGROUND_AWARD_RESDATA_BATTLETYPE_FIELD}
pb.SS_GLOBAL_TRAININGGROUND_AWARD_RESDATA.is_extendable = false
pb.SS_GLOBAL_TRAININGGROUND_AWARD_RESDATA.extensions = {}
pb.SS_GLOBAL_TRAININGGROUND_AWARD_RESDATA.containing_type = pb.SS_GLOBAL_TRAININGGROUND_AWARD
pb.SS_GLOBAL_TRAININGGROUND_AWARD_ZONEID_FIELD.name = "ZoneID"
pb.SS_GLOBAL_TRAININGGROUND_AWARD_ZONEID_FIELD.full_name = ".SS_Global_TrainingGround_Award.ZoneID"
pb.SS_GLOBAL_TRAININGGROUND_AWARD_ZONEID_FIELD.number = 1
pb.SS_GLOBAL_TRAININGGROUND_AWARD_ZONEID_FIELD.index = 0
pb.SS_GLOBAL_TRAININGGROUND_AWARD_ZONEID_FIELD.label = 1
pb.SS_GLOBAL_TRAININGGROUND_AWARD_ZONEID_FIELD.has_default_value = false
pb.SS_GLOBAL_TRAININGGROUND_AWARD_ZONEID_FIELD.default_value = 0
pb.SS_GLOBAL_TRAININGGROUND_AWARD_ZONEID_FIELD.type = 5
pb.SS_GLOBAL_TRAININGGROUND_AWARD_ZONEID_FIELD.cpp_type = 1

pb.SS_GLOBAL_TRAININGGROUND_AWARD_ACTORID_FIELD.name = "ActorID"
pb.SS_GLOBAL_TRAININGGROUND_AWARD_ACTORID_FIELD.full_name = ".SS_Global_TrainingGround_Award.ActorID"
pb.SS_GLOBAL_TRAININGGROUND_AWARD_ACTORID_FIELD.number = 2
pb.SS_GLOBAL_TRAININGGROUND_AWARD_ACTORID_FIELD.index = 1
pb.SS_GLOBAL_TRAININGGROUND_AWARD_ACTORID_FIELD.label = 1
pb.SS_GLOBAL_TRAININGGROUND_AWARD_ACTORID_FIELD.has_default_value = false
pb.SS_GLOBAL_TRAININGGROUND_AWARD_ACTORID_FIELD.default_value = 0
pb.SS_GLOBAL_TRAININGGROUND_AWARD_ACTORID_FIELD.type = 5
pb.SS_GLOBAL_TRAININGGROUND_AWARD_ACTORID_FIELD.cpp_type = 1

pb.SS_GLOBAL_TRAININGGROUND_AWARD_RESLIST_FIELD.name = "ResList"
pb.SS_GLOBAL_TRAININGGROUND_AWARD_RESLIST_FIELD.full_name = ".SS_Global_TrainingGround_Award.ResList"
pb.SS_GLOBAL_TRAININGGROUND_AWARD_RESLIST_FIELD.number = 3
pb.SS_GLOBAL_TRAININGGROUND_AWARD_RESLIST_FIELD.index = 2
pb.SS_GLOBAL_TRAININGGROUND_AWARD_RESLIST_FIELD.label = 3
pb.SS_GLOBAL_TRAININGGROUND_AWARD_RESLIST_FIELD.has_default_value = false
pb.SS_GLOBAL_TRAININGGROUND_AWARD_RESLIST_FIELD.default_value = {}
pb.SS_GLOBAL_TRAININGGROUND_AWARD_RESLIST_FIELD.message_type = pb.SS_GLOBAL_TRAININGGROUND_AWARD_RESDATA
pb.SS_GLOBAL_TRAININGGROUND_AWARD_RESLIST_FIELD.type = 11
pb.SS_GLOBAL_TRAININGGROUND_AWARD_RESLIST_FIELD.cpp_type = 10

pb.SS_GLOBAL_TRAININGGROUND_AWARD.name = "SS_Global_TrainingGround_Award"
pb.SS_GLOBAL_TRAININGGROUND_AWARD.full_name = ".SS_Global_TrainingGround_Award"
pb.SS_GLOBAL_TRAININGGROUND_AWARD.nested_types = {pb.SS_GLOBAL_TRAININGGROUND_AWARD_RESDATA}
pb.SS_GLOBAL_TRAININGGROUND_AWARD.enum_types = {}
pb.SS_GLOBAL_TRAININGGROUND_AWARD.fields = {pb.SS_GLOBAL_TRAININGGROUND_AWARD_ZONEID_FIELD, pb.SS_GLOBAL_TRAININGGROUND_AWARD_ACTORID_FIELD, pb.SS_GLOBAL_TRAININGGROUND_AWARD_RESLIST_FIELD}
pb.SS_GLOBAL_TRAININGGROUND_AWARD.is_extendable = false
pb.SS_GLOBAL_TRAININGGROUND_AWARD.extensions = {}
pb.CS_GLOBAL_LANDCONQUESTENTER_PARAM_FIELD.name = "Param"
pb.CS_GLOBAL_LANDCONQUESTENTER_PARAM_FIELD.full_name = ".CS_Global_LandConquestEnter.Param"
pb.CS_GLOBAL_LANDCONQUESTENTER_PARAM_FIELD.number = 1
pb.CS_GLOBAL_LANDCONQUESTENTER_PARAM_FIELD.index = 0
pb.CS_GLOBAL_LANDCONQUESTENTER_PARAM_FIELD.label = 1
pb.CS_GLOBAL_LANDCONQUESTENTER_PARAM_FIELD.has_default_value = false
pb.CS_GLOBAL_LANDCONQUESTENTER_PARAM_FIELD.default_value = 0
pb.CS_GLOBAL_LANDCONQUESTENTER_PARAM_FIELD.type = 5
pb.CS_GLOBAL_LANDCONQUESTENTER_PARAM_FIELD.cpp_type = 1

pb.CS_GLOBAL_LANDCONQUESTENTER.name = "CS_Global_LandConquestEnter"
pb.CS_GLOBAL_LANDCONQUESTENTER.full_name = ".CS_Global_LandConquestEnter"
pb.CS_GLOBAL_LANDCONQUESTENTER.nested_types = {}
pb.CS_GLOBAL_LANDCONQUESTENTER.enum_types = {}
pb.CS_GLOBAL_LANDCONQUESTENTER.fields = {pb.CS_GLOBAL_LANDCONQUESTENTER_PARAM_FIELD}
pb.CS_GLOBAL_LANDCONQUESTENTER.is_extendable = false
pb.CS_GLOBAL_LANDCONQUESTENTER.extensions = {}
pb.SS_GLOBAL_LANDCONQUESTENTER_ACTORID_FIELD.name = "ActorID"
pb.SS_GLOBAL_LANDCONQUESTENTER_ACTORID_FIELD.full_name = ".SS_Global_LandConquestEnter.ActorID"
pb.SS_GLOBAL_LANDCONQUESTENTER_ACTORID_FIELD.number = 1
pb.SS_GLOBAL_LANDCONQUESTENTER_ACTORID_FIELD.index = 0
pb.SS_GLOBAL_LANDCONQUESTENTER_ACTORID_FIELD.label = 1
pb.SS_GLOBAL_LANDCONQUESTENTER_ACTORID_FIELD.has_default_value = false
pb.SS_GLOBAL_LANDCONQUESTENTER_ACTORID_FIELD.default_value = 0
pb.SS_GLOBAL_LANDCONQUESTENTER_ACTORID_FIELD.type = 5
pb.SS_GLOBAL_LANDCONQUESTENTER_ACTORID_FIELD.cpp_type = 1

pb.SS_GLOBAL_LANDCONQUESTENTER_PARAM_FIELD.name = "Param"
pb.SS_GLOBAL_LANDCONQUESTENTER_PARAM_FIELD.full_name = ".SS_Global_LandConquestEnter.Param"
pb.SS_GLOBAL_LANDCONQUESTENTER_PARAM_FIELD.number = 2
pb.SS_GLOBAL_LANDCONQUESTENTER_PARAM_FIELD.index = 1
pb.SS_GLOBAL_LANDCONQUESTENTER_PARAM_FIELD.label = 1
pb.SS_GLOBAL_LANDCONQUESTENTER_PARAM_FIELD.has_default_value = false
pb.SS_GLOBAL_LANDCONQUESTENTER_PARAM_FIELD.default_value = 0
pb.SS_GLOBAL_LANDCONQUESTENTER_PARAM_FIELD.type = 5
pb.SS_GLOBAL_LANDCONQUESTENTER_PARAM_FIELD.cpp_type = 1

pb.SS_GLOBAL_LANDCONQUESTENTER.name = "SS_Global_LandConquestEnter"
pb.SS_GLOBAL_LANDCONQUESTENTER.full_name = ".SS_Global_LandConquestEnter"
pb.SS_GLOBAL_LANDCONQUESTENTER.nested_types = {}
pb.SS_GLOBAL_LANDCONQUESTENTER.enum_types = {}
pb.SS_GLOBAL_LANDCONQUESTENTER.fields = {pb.SS_GLOBAL_LANDCONQUESTENTER_ACTORID_FIELD, pb.SS_GLOBAL_LANDCONQUESTENTER_PARAM_FIELD}
pb.SS_GLOBAL_LANDCONQUESTENTER.is_extendable = false
pb.SS_GLOBAL_LANDCONQUESTENTER.extensions = {}
pb.SS_GLOBAL_MARKET_TRADERECORD_ACTORID_FIELD.name = "ActorID"
pb.SS_GLOBAL_MARKET_TRADERECORD_ACTORID_FIELD.full_name = ".SS_Global_Market_TradeRecord.ActorID"
pb.SS_GLOBAL_MARKET_TRADERECORD_ACTORID_FIELD.number = 1
pb.SS_GLOBAL_MARKET_TRADERECORD_ACTORID_FIELD.index = 0
pb.SS_GLOBAL_MARKET_TRADERECORD_ACTORID_FIELD.label = 1
pb.SS_GLOBAL_MARKET_TRADERECORD_ACTORID_FIELD.has_default_value = false
pb.SS_GLOBAL_MARKET_TRADERECORD_ACTORID_FIELD.default_value = 0
pb.SS_GLOBAL_MARKET_TRADERECORD_ACTORID_FIELD.type = 5
pb.SS_GLOBAL_MARKET_TRADERECORD_ACTORID_FIELD.cpp_type = 1

pb.SS_GLOBAL_MARKET_TRADERECORD_ZONEID_FIELD.name = "ZoneID"
pb.SS_GLOBAL_MARKET_TRADERECORD_ZONEID_FIELD.full_name = ".SS_Global_Market_TradeRecord.ZoneID"
pb.SS_GLOBAL_MARKET_TRADERECORD_ZONEID_FIELD.number = 2
pb.SS_GLOBAL_MARKET_TRADERECORD_ZONEID_FIELD.index = 1
pb.SS_GLOBAL_MARKET_TRADERECORD_ZONEID_FIELD.label = 1
pb.SS_GLOBAL_MARKET_TRADERECORD_ZONEID_FIELD.has_default_value = false
pb.SS_GLOBAL_MARKET_TRADERECORD_ZONEID_FIELD.default_value = 0
pb.SS_GLOBAL_MARKET_TRADERECORD_ZONEID_FIELD.type = 5
pb.SS_GLOBAL_MARKET_TRADERECORD_ZONEID_FIELD.cpp_type = 1

pb.SS_GLOBAL_MARKET_TRADERECORD.name = "SS_Global_Market_TradeRecord"
pb.SS_GLOBAL_MARKET_TRADERECORD.full_name = ".SS_Global_Market_TradeRecord"
pb.SS_GLOBAL_MARKET_TRADERECORD.nested_types = {}
pb.SS_GLOBAL_MARKET_TRADERECORD.enum_types = {}
pb.SS_GLOBAL_MARKET_TRADERECORD.fields = {pb.SS_GLOBAL_MARKET_TRADERECORD_ACTORID_FIELD, pb.SS_GLOBAL_MARKET_TRADERECORD_ZONEID_FIELD}
pb.SS_GLOBAL_MARKET_TRADERECORD.is_extendable = false
pb.SS_GLOBAL_MARKET_TRADERECORD.extensions = {}
pb.CS_GLOBAL_LANDCONQUESTGETBOSSINFO_PARAM_FIELD.name = "Param"
pb.CS_GLOBAL_LANDCONQUESTGETBOSSINFO_PARAM_FIELD.full_name = ".CS_Global_LandConquestGetBossInfo.Param"
pb.CS_GLOBAL_LANDCONQUESTGETBOSSINFO_PARAM_FIELD.number = 1
pb.CS_GLOBAL_LANDCONQUESTGETBOSSINFO_PARAM_FIELD.index = 0
pb.CS_GLOBAL_LANDCONQUESTGETBOSSINFO_PARAM_FIELD.label = 1
pb.CS_GLOBAL_LANDCONQUESTGETBOSSINFO_PARAM_FIELD.has_default_value = false
pb.CS_GLOBAL_LANDCONQUESTGETBOSSINFO_PARAM_FIELD.default_value = 0
pb.CS_GLOBAL_LANDCONQUESTGETBOSSINFO_PARAM_FIELD.type = 5
pb.CS_GLOBAL_LANDCONQUESTGETBOSSINFO_PARAM_FIELD.cpp_type = 1

pb.CS_GLOBAL_LANDCONQUESTGETBOSSINFO.name = "CS_Global_LandConquestGetBossInfo"
pb.CS_GLOBAL_LANDCONQUESTGETBOSSINFO.full_name = ".CS_Global_LandConquestGetBossInfo"
pb.CS_GLOBAL_LANDCONQUESTGETBOSSINFO.nested_types = {}
pb.CS_GLOBAL_LANDCONQUESTGETBOSSINFO.enum_types = {}
pb.CS_GLOBAL_LANDCONQUESTGETBOSSINFO.fields = {pb.CS_GLOBAL_LANDCONQUESTGETBOSSINFO_PARAM_FIELD}
pb.CS_GLOBAL_LANDCONQUESTGETBOSSINFO.is_extendable = false
pb.CS_GLOBAL_LANDCONQUESTGETBOSSINFO.extensions = {}
pb.SS_GLOBAL_LANDCONQUESTGETBOSSINFO_ACTORID_FIELD.name = "ActorID"
pb.SS_GLOBAL_LANDCONQUESTGETBOSSINFO_ACTORID_FIELD.full_name = ".SS_Global_LandConquestGetBossInfo.ActorID"
pb.SS_GLOBAL_LANDCONQUESTGETBOSSINFO_ACTORID_FIELD.number = 1
pb.SS_GLOBAL_LANDCONQUESTGETBOSSINFO_ACTORID_FIELD.index = 0
pb.SS_GLOBAL_LANDCONQUESTGETBOSSINFO_ACTORID_FIELD.label = 1
pb.SS_GLOBAL_LANDCONQUESTGETBOSSINFO_ACTORID_FIELD.has_default_value = false
pb.SS_GLOBAL_LANDCONQUESTGETBOSSINFO_ACTORID_FIELD.default_value = 0
pb.SS_GLOBAL_LANDCONQUESTGETBOSSINFO_ACTORID_FIELD.type = 5
pb.SS_GLOBAL_LANDCONQUESTGETBOSSINFO_ACTORID_FIELD.cpp_type = 1

pb.SS_GLOBAL_LANDCONQUESTGETBOSSINFO_PARAM_FIELD.name = "Param"
pb.SS_GLOBAL_LANDCONQUESTGETBOSSINFO_PARAM_FIELD.full_name = ".SS_Global_LandConquestGetBossInfo.Param"
pb.SS_GLOBAL_LANDCONQUESTGETBOSSINFO_PARAM_FIELD.number = 2
pb.SS_GLOBAL_LANDCONQUESTGETBOSSINFO_PARAM_FIELD.index = 1
pb.SS_GLOBAL_LANDCONQUESTGETBOSSINFO_PARAM_FIELD.label = 1
pb.SS_GLOBAL_LANDCONQUESTGETBOSSINFO_PARAM_FIELD.has_default_value = false
pb.SS_GLOBAL_LANDCONQUESTGETBOSSINFO_PARAM_FIELD.default_value = 0
pb.SS_GLOBAL_LANDCONQUESTGETBOSSINFO_PARAM_FIELD.type = 5
pb.SS_GLOBAL_LANDCONQUESTGETBOSSINFO_PARAM_FIELD.cpp_type = 1

pb.SS_GLOBAL_LANDCONQUESTGETBOSSINFO.name = "SS_Global_LandConquestGetBossInfo"
pb.SS_GLOBAL_LANDCONQUESTGETBOSSINFO.full_name = ".SS_Global_LandConquestGetBossInfo"
pb.SS_GLOBAL_LANDCONQUESTGETBOSSINFO.nested_types = {}
pb.SS_GLOBAL_LANDCONQUESTGETBOSSINFO.enum_types = {}
pb.SS_GLOBAL_LANDCONQUESTGETBOSSINFO.fields = {pb.SS_GLOBAL_LANDCONQUESTGETBOSSINFO_ACTORID_FIELD, pb.SS_GLOBAL_LANDCONQUESTGETBOSSINFO_PARAM_FIELD}
pb.SS_GLOBAL_LANDCONQUESTGETBOSSINFO.is_extendable = false
pb.SS_GLOBAL_LANDCONQUESTGETBOSSINFO.extensions = {}
pb.SS_GLOBAL_LANDCONQUESTWINNER_TYPE_FIELD.name = "Type"
pb.SS_GLOBAL_LANDCONQUESTWINNER_TYPE_FIELD.full_name = ".SS_Global_LandConquestWinner.Type"
pb.SS_GLOBAL_LANDCONQUESTWINNER_TYPE_FIELD.number = 1
pb.SS_GLOBAL_LANDCONQUESTWINNER_TYPE_FIELD.index = 0
pb.SS_GLOBAL_LANDCONQUESTWINNER_TYPE_FIELD.label = 1
pb.SS_GLOBAL_LANDCONQUESTWINNER_TYPE_FIELD.has_default_value = false
pb.SS_GLOBAL_LANDCONQUESTWINNER_TYPE_FIELD.default_value = 0
pb.SS_GLOBAL_LANDCONQUESTWINNER_TYPE_FIELD.type = 5
pb.SS_GLOBAL_LANDCONQUESTWINNER_TYPE_FIELD.cpp_type = 1

pb.SS_GLOBAL_LANDCONQUESTWINNER_ZONEID_FIELD.name = "ZoneID"
pb.SS_GLOBAL_LANDCONQUESTWINNER_ZONEID_FIELD.full_name = ".SS_Global_LandConquestWinner.ZoneID"
pb.SS_GLOBAL_LANDCONQUESTWINNER_ZONEID_FIELD.number = 2
pb.SS_GLOBAL_LANDCONQUESTWINNER_ZONEID_FIELD.index = 1
pb.SS_GLOBAL_LANDCONQUESTWINNER_ZONEID_FIELD.label = 1
pb.SS_GLOBAL_LANDCONQUESTWINNER_ZONEID_FIELD.has_default_value = false
pb.SS_GLOBAL_LANDCONQUESTWINNER_ZONEID_FIELD.default_value = 0
pb.SS_GLOBAL_LANDCONQUESTWINNER_ZONEID_FIELD.type = 5
pb.SS_GLOBAL_LANDCONQUESTWINNER_ZONEID_FIELD.cpp_type = 1

pb.SS_GLOBAL_LANDCONQUESTWINNER_ISWINNER_FIELD.name = "IsWinner"
pb.SS_GLOBAL_LANDCONQUESTWINNER_ISWINNER_FIELD.full_name = ".SS_Global_LandConquestWinner.IsWinner"
pb.SS_GLOBAL_LANDCONQUESTWINNER_ISWINNER_FIELD.number = 3
pb.SS_GLOBAL_LANDCONQUESTWINNER_ISWINNER_FIELD.index = 2
pb.SS_GLOBAL_LANDCONQUESTWINNER_ISWINNER_FIELD.label = 1
pb.SS_GLOBAL_LANDCONQUESTWINNER_ISWINNER_FIELD.has_default_value = false
pb.SS_GLOBAL_LANDCONQUESTWINNER_ISWINNER_FIELD.default_value = 0
pb.SS_GLOBAL_LANDCONQUESTWINNER_ISWINNER_FIELD.type = 5
pb.SS_GLOBAL_LANDCONQUESTWINNER_ISWINNER_FIELD.cpp_type = 1

pb.SS_GLOBAL_LANDCONQUESTWINNER.name = "SS_Global_LandConquestWinner"
pb.SS_GLOBAL_LANDCONQUESTWINNER.full_name = ".SS_Global_LandConquestWinner"
pb.SS_GLOBAL_LANDCONQUESTWINNER.nested_types = {}
pb.SS_GLOBAL_LANDCONQUESTWINNER.enum_types = {}
pb.SS_GLOBAL_LANDCONQUESTWINNER.fields = {pb.SS_GLOBAL_LANDCONQUESTWINNER_TYPE_FIELD, pb.SS_GLOBAL_LANDCONQUESTWINNER_ZONEID_FIELD, pb.SS_GLOBAL_LANDCONQUESTWINNER_ISWINNER_FIELD}
pb.SS_GLOBAL_LANDCONQUESTWINNER.is_extendable = false
pb.SS_GLOBAL_LANDCONQUESTWINNER.extensions = {}
pb.SS_GLOBAL_BARBARIANINVASION_UPDATESCORE_ROOMID_FIELD.name = "RoomID"
pb.SS_GLOBAL_BARBARIANINVASION_UPDATESCORE_ROOMID_FIELD.full_name = ".SS_Global_BarbarianInvasion_UpdateScore.RoomID"
pb.SS_GLOBAL_BARBARIANINVASION_UPDATESCORE_ROOMID_FIELD.number = 1
pb.SS_GLOBAL_BARBARIANINVASION_UPDATESCORE_ROOMID_FIELD.index = 0
pb.SS_GLOBAL_BARBARIANINVASION_UPDATESCORE_ROOMID_FIELD.label = 1
pb.SS_GLOBAL_BARBARIANINVASION_UPDATESCORE_ROOMID_FIELD.has_default_value = false
pb.SS_GLOBAL_BARBARIANINVASION_UPDATESCORE_ROOMID_FIELD.default_value = 0
pb.SS_GLOBAL_BARBARIANINVASION_UPDATESCORE_ROOMID_FIELD.type = 5
pb.SS_GLOBAL_BARBARIANINVASION_UPDATESCORE_ROOMID_FIELD.cpp_type = 1

pb.SS_GLOBAL_BARBARIANINVASION_UPDATESCORE_SCORE_FIELD.name = "Score"
pb.SS_GLOBAL_BARBARIANINVASION_UPDATESCORE_SCORE_FIELD.full_name = ".SS_Global_BarbarianInvasion_UpdateScore.Score"
pb.SS_GLOBAL_BARBARIANINVASION_UPDATESCORE_SCORE_FIELD.number = 2
pb.SS_GLOBAL_BARBARIANINVASION_UPDATESCORE_SCORE_FIELD.index = 1
pb.SS_GLOBAL_BARBARIANINVASION_UPDATESCORE_SCORE_FIELD.label = 1
pb.SS_GLOBAL_BARBARIANINVASION_UPDATESCORE_SCORE_FIELD.has_default_value = false
pb.SS_GLOBAL_BARBARIANINVASION_UPDATESCORE_SCORE_FIELD.default_value = 0
pb.SS_GLOBAL_BARBARIANINVASION_UPDATESCORE_SCORE_FIELD.type = 5
pb.SS_GLOBAL_BARBARIANINVASION_UPDATESCORE_SCORE_FIELD.cpp_type = 1

pb.SS_GLOBAL_BARBARIANINVASION_UPDATESCORE.name = "SS_Global_BarbarianInvasion_UpdateScore"
pb.SS_GLOBAL_BARBARIANINVASION_UPDATESCORE.full_name = ".SS_Global_BarbarianInvasion_UpdateScore"
pb.SS_GLOBAL_BARBARIANINVASION_UPDATESCORE.nested_types = {}
pb.SS_GLOBAL_BARBARIANINVASION_UPDATESCORE.enum_types = {}
pb.SS_GLOBAL_BARBARIANINVASION_UPDATESCORE.fields = {pb.SS_GLOBAL_BARBARIANINVASION_UPDATESCORE_ROOMID_FIELD, pb.SS_GLOBAL_BARBARIANINVASION_UPDATESCORE_SCORE_FIELD}
pb.SS_GLOBAL_BARBARIANINVASION_UPDATESCORE.is_extendable = false
pb.SS_GLOBAL_BARBARIANINVASION_UPDATESCORE.extensions = {}
pb.SS_GLOBAL_BARBARIANINVASION_RANKINFO_ACTORID_FIELD.name = "ActorID"
pb.SS_GLOBAL_BARBARIANINVASION_RANKINFO_ACTORID_FIELD.full_name = ".SS_Global_BarbarianInvasion_RankInfo.ActorID"
pb.SS_GLOBAL_BARBARIANINVASION_RANKINFO_ACTORID_FIELD.number = 1
pb.SS_GLOBAL_BARBARIANINVASION_RANKINFO_ACTORID_FIELD.index = 0
pb.SS_GLOBAL_BARBARIANINVASION_RANKINFO_ACTORID_FIELD.label = 1
pb.SS_GLOBAL_BARBARIANINVASION_RANKINFO_ACTORID_FIELD.has_default_value = false
pb.SS_GLOBAL_BARBARIANINVASION_RANKINFO_ACTORID_FIELD.default_value = 0
pb.SS_GLOBAL_BARBARIANINVASION_RANKINFO_ACTORID_FIELD.type = 5
pb.SS_GLOBAL_BARBARIANINVASION_RANKINFO_ACTORID_FIELD.cpp_type = 1

pb.SS_GLOBAL_BARBARIANINVASION_RANKINFO.name = "SS_Global_BarbarianInvasion_RankInfo"
pb.SS_GLOBAL_BARBARIANINVASION_RANKINFO.full_name = ".SS_Global_BarbarianInvasion_RankInfo"
pb.SS_GLOBAL_BARBARIANINVASION_RANKINFO.nested_types = {}
pb.SS_GLOBAL_BARBARIANINVASION_RANKINFO.enum_types = {}
pb.SS_GLOBAL_BARBARIANINVASION_RANKINFO.fields = {pb.SS_GLOBAL_BARBARIANINVASION_RANKINFO_ACTORID_FIELD}
pb.SS_GLOBAL_BARBARIANINVASION_RANKINFO.is_extendable = false
pb.SS_GLOBAL_BARBARIANINVASION_RANKINFO.extensions = {}

CS_Global_ActvEctypeEnter = protobuf.Message(pb.CS_GLOBAL_ACTVECTYPEENTER)
CS_Global_ActvEctypeLeave = protobuf.Message(pb.CS_GLOBAL_ACTVECTYPELEAVE)
CS_Global_GameEctypeEnter = protobuf.Message(pb.CS_GLOBAL_GAMEECTYPEENTER)
CS_Global_GameEctypeLeave = protobuf.Message(pb.CS_GLOBAL_GAMEECTYPELEAVE)
CS_Global_LandConquestEnter = protobuf.Message(pb.CS_GLOBAL_LANDCONQUESTENTER)
CS_Global_LandConquestGetBossInfo = protobuf.Message(pb.CS_GLOBAL_LANDCONQUESTGETBOSSINFO)
CS_Global_SurvivalChallengeEnter = protobuf.Message(pb.CS_GLOBAL_SURVIVALCHALLENGEENTER)
CS_Global_SurvivalChallengeLeave = protobuf.Message(pb.CS_GLOBAL_SURVIVALCHALLENGELEAVE)
CS_Global_WildSceneEnter = protobuf.Message(pb.CS_GLOBAL_WILDSCENEENTER)
CS_Global_WildSceneLeave = protobuf.Message(pb.CS_GLOBAL_WILDSCENELEAVE)
MSG_GLOBALGAME_ACTORLUAREQUEST = 5
MSG_GLOBALGAME_FLUSHACTORMAIL = 6
MSG_GLOBALGAME_LEAVEGLOBALSERVER = 8
MSG_GLOBALGAME_NONE = 0
MSG_GLOBALGAME_ROBOTSWITCHZONE = 100
MSG_GLOBALGAME_ROBOTSWITCHZONELOGIN = 101
MSG_GLOBALGAME_SETACTORDATA = 1
MSG_GLOBALGAME_SETCOUNTRYDATA = 60
MSG_GLOBALGAME_SETSOCIETYDATA = 61
MSG_GLOBALGAME_SETWARBANDDATA = 63
MSG_GLOBALGAME_SYNCACTOREVENT = 3
MSG_GLOBALGAME_TRANSMESSAGE = 7
MSG_GLOBALGAME_UPDATEACTORDATA = 2
MSG_GLOBALGAME_UPDATESERVERBASEDATA = 4
MSG_GLOBALGAME_UPDATESOCIETYMEMBER = 62
MSG_GLOBALGAME_UPDATEWARBANDMEMBER = 64
MSG_GLOBAL_ACTVCOUNT_ADD = 90
MSG_GLOBAL_ACTVECTYPE_ENTER = 40
MSG_GLOBAL_ACTVECTYPE_LEAVE = 41
MSG_GLOBAL_ACTVRECORD_GETLIST = 46
MSG_GLOBAL_ACTVRECORD_UPDATEVALUE = 45
MSG_GLOBAL_ACTV_RANK_LIST_GET = 43
MSG_GLOBAL_ACTV_RANK_VALUE_CHANGE = 42
MSG_GLOBAL_ACTV_RANK_VALUE_REPLACE = 44
MSG_GLOBAL_BARBARIAN_INVASION_RANKINFO = 125
MSG_GLOBAL_BARBARIAN_INVASION_UPDATE_SCORE = 124
MSG_GLOBAL_CHAT_TRANS = 80
MSG_GLOBAL_GAMEECTYPE_ENTER = 51
MSG_GLOBAL_GAMEECTYPE_LEAVE = 52
MSG_GLOBAL_GAMEECTYPE_RESULTDATA = 53
MSG_GLOBAL_LANDCONQUEST_ENTER = 120
MSG_GLOBAL_LANDCONQUEST_GETBOSSINFO = 122
MSG_GLOBAL_LANDCONQUEST_WINNER = 123
MSG_GLOBAL_MARKET_ADDSELLITEM = 72
MSG_GLOBAL_MARKET_ADDSELLITEMCHECK = 73
MSG_GLOBAL_MARKET_ADDSELLITEMCHECKRESULT = 74
MSG_GLOBAL_MARKET_BUYSELLITEM = 77
MSG_GLOBAL_MARKET_BUYSELLITEMCHECK = 78
MSG_GLOBAL_MARKET_BUYSELLITEMCHECKRESULT = 79
MSG_GLOBAL_MARKET_GETLIST = 70
MSG_GLOBAL_MARKET_GETSELF = 71
MSG_GLOBAL_MARKET_REMOVESELLITEM = 75
MSG_GLOBAL_MARKET_REMOVESELLITEMRESULT = 76
MSG_GLOBAL_MARKET_TRADERECORDE = 121
MSG_GLOBAL_PLAYROOM_CREATE = 10
MSG_GLOBAL_PLAYROOM_DELETE = 20
MSG_GLOBAL_PLAYROOM_GETLIST = 11
MSG_GLOBAL_PLAYROOM_INVITEOTHER = 19
MSG_GLOBAL_PLAYROOM_JOIN = 12
MSG_GLOBAL_PLAYROOM_JOINTARGET = 23
MSG_GLOBAL_PLAYROOM_KICK = 14
MSG_GLOBAL_PLAYROOM_PREPARE = 15
MSG_GLOBAL_PLAYROOM_QUIT = 13
MSG_GLOBAL_PLAYROOM_REENTERECTYPE = 21
MSG_GLOBAL_PLAYROOM_ROOMEVENT = 22
MSG_GLOBAL_PLAYROOM_STARTPLAY = 16
MSG_GLOBAL_PLAYROOM_UPDATEROOMINFO = 18
MSG_GLOBAL_PLAYROOM_UPDATEROOMLISTINFO = 17
MSG_GLOBAL_SOLOGAME_GETWINCOUNT = 50
MSG_GLOBAL_STOPACTV = 54
MSG_GLOBAL_SURVIVALCHALLENGE_BATTLEFIELDINFO = 95
MSG_GLOBAL_SURVIVALCHALLENGE_ENTER = 92
MSG_GLOBAL_SURVIVALCHALLENGE_LEAVE = 93
MSG_GLOBAL_SURVIVALCHALLENGE_PLAYERLOCATION = 94
MSG_GLOBAL_TRAININGGROUND_AWARD = 119
MSG_GLOBAL_TRAININGGROUND_BEGINFIGHT = 111
MSG_GLOBAL_TRAININGGROUND_CACHEPLAYERSIM = 116
MSG_GLOBAL_TRAININGGROUND_ENTER = 117
MSG_GLOBAL_TRAININGGROUND_EXPLOITMINE = 113
MSG_GLOBAL_TRAININGGROUND_GETLIST = 110
MSG_GLOBAL_TRAININGGROUND_PLUNDERLOG = 118
MSG_GLOBAL_TRAININGGROUND_PRIZE = 115
MSG_GLOBAL_TRAININGGROUND_REFRESHLV = 112
MSG_GLOBAL_TRAININGGROUND_REFRESHMINLIST = 114
MSG_GLOBAL_WILDSCENE_BOSSINFO = 91
MSG_GLOBAL_WILDSCENE_ENTER = 30
MSG_GLOBAL_WILDSCENE_LEAVE = 31
SS_GlobalGame_ActorLuaRequest = protobuf.Message(pb.SS_GLOBALGAME_ACTORLUAREQUEST)
SS_GlobalGame_ActvCountAdd = protobuf.Message(pb.SS_GLOBALGAME_ACTVCOUNTADD)
SS_GlobalGame_FlushActorMail = protobuf.Message(pb.SS_GLOBALGAME_FLUSHACTORMAIL)
SS_GlobalGame_MarketAddSellItem = protobuf.Message(pb.SS_GLOBALGAME_MARKETADDSELLITEM)
SS_GlobalGame_MarketAddSellItemCheck = protobuf.Message(pb.SS_GLOBALGAME_MARKETADDSELLITEMCHECK)
SS_GlobalGame_MarketAddSellItemCheckResult = protobuf.Message(pb.SS_GLOBALGAME_MARKETADDSELLITEMCHECKRESULT)
SS_GlobalGame_MarketBuySellItem = protobuf.Message(pb.SS_GLOBALGAME_MARKETBUYSELLITEM)
SS_GlobalGame_MarketBuySellItemCheck = protobuf.Message(pb.SS_GLOBALGAME_MARKETBUYSELLITEMCHECK)
SS_GlobalGame_MarketBuySellItemCheckResult = protobuf.Message(pb.SS_GLOBALGAME_MARKETBUYSELLITEMCHECKRESULT)
SS_GlobalGame_MarketRemoveSellItem = protobuf.Message(pb.SS_GLOBALGAME_MARKETREMOVESELLITEM)
SS_GlobalGame_MarketRemoveSellItemResult = protobuf.Message(pb.SS_GLOBALGAME_MARKETREMOVESELLITEMRESULT)
SS_GlobalGame_RobotSwitchZone = protobuf.Message(pb.SS_GLOBALGAME_ROBOTSWITCHZONE)
SS_GlobalGame_RobotSwitchZoneLogin = protobuf.Message(pb.SS_GLOBALGAME_ROBOTSWITCHZONELOGIN)
SS_GlobalGame_SetActorData = protobuf.Message(pb.SS_GLOBALGAME_SETACTORDATA)
SS_GlobalGame_SetCountryData = protobuf.Message(pb.SS_GLOBALGAME_SETCOUNTRYDATA)
SS_GlobalGame_SetCountryData.OfficeData = protobuf.Message(pb.SS_GLOBALGAME_SETCOUNTRYDATA_OFFICEDATA)
SS_GlobalGame_SetSocietyData = protobuf.Message(pb.SS_GLOBALGAME_SETSOCIETYDATA)
SS_GlobalGame_SetWarbandData = protobuf.Message(pb.SS_GLOBALGAME_SETWARBANDDATA)
SS_GlobalGame_SyncActorEvent = protobuf.Message(pb.SS_GLOBALGAME_SYNCACTOREVENT)
SS_GlobalGame_TransMessage = protobuf.Message(pb.SS_GLOBALGAME_TRANSMESSAGE)
SS_GlobalGame_UpdateActorData = protobuf.Message(pb.SS_GLOBALGAME_UPDATEACTORDATA)
SS_GlobalGame_UpdateServerBaseData = protobuf.Message(pb.SS_GLOBALGAME_UPDATESERVERBASEDATA)
SS_GlobalGame_UpdateSocietyMember = protobuf.Message(pb.SS_GLOBALGAME_UPDATESOCIETYMEMBER)
SS_GlobalGame_UpdateSocietyMember.MemberData = protobuf.Message(pb.SS_GLOBALGAME_UPDATESOCIETYMEMBER_MEMBERDATA)
SS_GlobalGame_UpdateWarbandMember = protobuf.Message(pb.SS_GLOBALGAME_UPDATEWARBANDMEMBER)
SS_GlobalGame_UpdateWarbandMember.MemberData = protobuf.Message(pb.SS_GLOBALGAME_UPDATEWARBANDMEMBER_MEMBERDATA)
SS_Global_ActvEctypeEnter = protobuf.Message(pb.SS_GLOBAL_ACTVECTYPEENTER)
SS_Global_ActvRankListGet = protobuf.Message(pb.SS_GLOBAL_ACTVRANKLISTGET)
SS_Global_ActvRankListReplace = protobuf.Message(pb.SS_GLOBAL_ACTVRANKLISTREPLACE)
SS_Global_ActvRankValueChange = protobuf.Message(pb.SS_GLOBAL_ACTVRANKVALUECHANGE)
SS_Global_ActvRecordGetList = protobuf.Message(pb.SS_GLOBAL_ACTVRECORDGETLIST)
SS_Global_ActvRecordUpdateValue = protobuf.Message(pb.SS_GLOBAL_ACTVRECORDUPDATEVALUE)
SS_Global_BarbarianInvasion_RankInfo = protobuf.Message(pb.SS_GLOBAL_BARBARIANINVASION_RANKINFO)
SS_Global_BarbarianInvasion_UpdateScore = protobuf.Message(pb.SS_GLOBAL_BARBARIANINVASION_UPDATESCORE)
SS_Global_GameEctypeEnter = protobuf.Message(pb.SS_GLOBAL_GAMEECTYPEENTER)
SS_Global_LandConquestEnter = protobuf.Message(pb.SS_GLOBAL_LANDCONQUESTENTER)
SS_Global_LandConquestGetBossInfo = protobuf.Message(pb.SS_GLOBAL_LANDCONQUESTGETBOSSINFO)
SS_Global_LandConquestWinner = protobuf.Message(pb.SS_GLOBAL_LANDCONQUESTWINNER)
SS_Global_Market_TradeRecord = protobuf.Message(pb.SS_GLOBAL_MARKET_TRADERECORD)
SS_Global_PlayRoomCreate = protobuf.Message(pb.SS_GLOBAL_PLAYROOMCREATE)
SS_Global_PlayRoomDelete = protobuf.Message(pb.SS_GLOBAL_PLAYROOMDELETE)
SS_Global_PlayRoomEvent = protobuf.Message(pb.SS_GLOBAL_PLAYROOMEVENT)
SS_Global_PlayRoomGetList = protobuf.Message(pb.SS_GLOBAL_PLAYROOMGETLIST)
SS_Global_PlayRoomInviteOther = protobuf.Message(pb.SS_GLOBAL_PLAYROOMINVITEOTHER)
SS_Global_PlayRoomJoin = protobuf.Message(pb.SS_GLOBAL_PLAYROOMJOIN)
SS_Global_PlayRoomJoinTarget = protobuf.Message(pb.SS_GLOBAL_PLAYROOMJOINTARGET)
SS_Global_PlayRoomKick = protobuf.Message(pb.SS_GLOBAL_PLAYROOMKICK)
SS_Global_PlayRoomPrepare = protobuf.Message(pb.SS_GLOBAL_PLAYROOMPREPARE)
SS_Global_PlayRoomQuit = protobuf.Message(pb.SS_GLOBAL_PLAYROOMQUIT)
SS_Global_PlayRoomReEnterEctype = protobuf.Message(pb.SS_GLOBAL_PLAYROOMREENTERECTYPE)
SS_Global_PlayRoomStartPlay = protobuf.Message(pb.SS_GLOBAL_PLAYROOMSTARTPLAY)
SS_Global_PlayRoomUpdateRoomInfo = protobuf.Message(pb.SS_GLOBAL_PLAYROOMUPDATEROOMINFO)
SS_Global_PlayRoomUpdateRoomListInfo = protobuf.Message(pb.SS_GLOBAL_PLAYROOMUPDATEROOMLISTINFO)
SS_Global_SoloGameGetWinCount = protobuf.Message(pb.SS_GLOBAL_SOLOGAMEGETWINCOUNT)
SS_Global_StopActv = protobuf.Message(pb.SS_GLOBAL_STOPACTV)
SS_Global_SurvivalChallengeEnter = protobuf.Message(pb.SS_GLOBAL_SURVIVALCHALLENGEENTER)
SS_Global_TrainingGroundEnter = protobuf.Message(pb.SS_GLOBAL_TRAININGGROUNDENTER)
SS_Global_TrainingGround_Award = protobuf.Message(pb.SS_GLOBAL_TRAININGGROUND_AWARD)
SS_Global_TrainingGround_Award.ResData = protobuf.Message(pb.SS_GLOBAL_TRAININGGROUND_AWARD_RESDATA)
SS_Global_TrainingGround_PlayerSimple = protobuf.Message(pb.SS_GLOBAL_TRAININGGROUND_PLAYERSIMPLE)
SS_Global_WildSceneEnter = protobuf.Message(pb.SS_GLOBAL_WILDSCENEENTER)

