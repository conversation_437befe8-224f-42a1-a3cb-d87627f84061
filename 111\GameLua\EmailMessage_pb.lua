-- Generated By protoc-gen-lua Do not Edit
local protobuf = require "protobuf/protobuf"
module('EmailMessage_pb')
local pb = {}


pb.MSG_EMAIL_ACTIONID = protobuf.EnumDescriptor();
pb.MSG_EMAIL_ACTIONID_MSG_EMAIL_NONE_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_EMAIL_ACTIONID_MSG_EMAIL_GETLIST_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_EMAIL_ACTIONID_MSG_EMAIL_READEMAIL_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_EMAIL_ACTIONID_MSG_EMAIL_ADJUNCT_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_EMAIL_ACTIONID_MSG_EMAIL_DELETE_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_EMAIL_ACTIONID_MSG_EMAIL_NEWEMAIL_ENUM = protobuf.EnumValueDescriptor();
pb.CS_EMAIL_GETEMAILLIST = protobuf.Descriptor();
pb.CS_EMAIL_GETEMAILLIST_GETTIME_FIELD = protobuf.FieldDescriptor();
pb.CS_EMAIL_GETEMAILLIST_REFRESH_FIELD = protobuf.FieldDescriptor();
pb.SC_EMAIL_GETEMAILLIST = protobuf.Descriptor();
pb.SC_EMAIL_GETEMAILLIST_EMAILDATA = protobuf.Descriptor();
pb.SC_EMAIL_GETEMAILLIST_EMAILDATA_EMAILID_FIELD = protobuf.FieldDescriptor();
pb.SC_EMAIL_GETEMAILLIST_EMAILDATA_TITLE_FIELD = protobuf.FieldDescriptor();
pb.SC_EMAIL_GETEMAILLIST_EMAILDATA_SENDTIME_FIELD = protobuf.FieldDescriptor();
pb.SC_EMAIL_GETEMAILLIST_EMAILDATA_VALIDTIME_FIELD = protobuf.FieldDescriptor();
pb.SC_EMAIL_GETEMAILLIST_EMAILDATA_MARKREAD_FIELD = protobuf.FieldDescriptor();
pb.SC_EMAIL_GETEMAILLIST_EMAILDATA_HASADJUNCT_FIELD = protobuf.FieldDescriptor();
pb.SC_EMAIL_GETEMAILLIST_EMAILNUM_FIELD = protobuf.FieldDescriptor();
pb.SC_EMAIL_GETEMAILLIST_EMAILLIST_FIELD = protobuf.FieldDescriptor();
pb.CS_EMAIL_READMAIL = protobuf.Descriptor();
pb.CS_EMAIL_READMAIL_EMAILID_FIELD = protobuf.FieldDescriptor();
pb.SC_EMAIL_READMAIL = protobuf.Descriptor();
pb.SC_EMAIL_READMAIL_ADJUNCTDATA = protobuf.Descriptor();
pb.SC_EMAIL_READMAIL_ADJUNCTDATA_NUMPROP_FIELD = protobuf.FieldDescriptor();
pb.SC_EMAIL_READMAIL_ADJUNCTDATA_BINDFLAG_FIELD = protobuf.FieldDescriptor();
pb.SC_EMAIL_READMAIL_EMAILID_FIELD = protobuf.FieldDescriptor();
pb.SC_EMAIL_READMAIL_TITLE_FIELD = protobuf.FieldDescriptor();
pb.SC_EMAIL_READMAIL_CONTENT_FIELD = protobuf.FieldDescriptor();
pb.SC_EMAIL_READMAIL_ADJUNCTLIST_FIELD = protobuf.FieldDescriptor();
pb.CS_EMAIL_GETADJUNCT = protobuf.Descriptor();
pb.CS_EMAIL_GETADJUNCT_EMAILID_FIELD = protobuf.FieldDescriptor();
pb.SC_EMAIL_GETADJUNCT = protobuf.Descriptor();
pb.SC_EMAIL_GETADJUNCT_EMAILID_FIELD = protobuf.FieldDescriptor();
pb.SC_EMAIL_GETADJUNCT_RESULT_FIELD = protobuf.FieldDescriptor();
pb.CS_EMAIL_DELETEEMAIL = protobuf.Descriptor();
pb.CS_EMAIL_DELETEEMAIL_EMAILID_FIELD = protobuf.FieldDescriptor();
pb.SC_EMAIL_DELETEEMAIL = protobuf.Descriptor();
pb.SC_EMAIL_DELETEEMAIL_EMAILID_FIELD = protobuf.FieldDescriptor();
pb.SC_EMAIL_DELETEEMAIL_RESULT_FIELD = protobuf.FieldDescriptor();

pb.MSG_EMAIL_ACTIONID_MSG_EMAIL_NONE_ENUM.name = "MSG_EMAIL_NONE"
pb.MSG_EMAIL_ACTIONID_MSG_EMAIL_NONE_ENUM.index = 0
pb.MSG_EMAIL_ACTIONID_MSG_EMAIL_NONE_ENUM.number = 0
pb.MSG_EMAIL_ACTIONID_MSG_EMAIL_GETLIST_ENUM.name = "MSG_EMAIL_GETLIST"
pb.MSG_EMAIL_ACTIONID_MSG_EMAIL_GETLIST_ENUM.index = 1
pb.MSG_EMAIL_ACTIONID_MSG_EMAIL_GETLIST_ENUM.number = 1
pb.MSG_EMAIL_ACTIONID_MSG_EMAIL_READEMAIL_ENUM.name = "MSG_EMAIL_READEMAIL"
pb.MSG_EMAIL_ACTIONID_MSG_EMAIL_READEMAIL_ENUM.index = 2
pb.MSG_EMAIL_ACTIONID_MSG_EMAIL_READEMAIL_ENUM.number = 2
pb.MSG_EMAIL_ACTIONID_MSG_EMAIL_ADJUNCT_ENUM.name = "MSG_EMAIL_ADJUNCT"
pb.MSG_EMAIL_ACTIONID_MSG_EMAIL_ADJUNCT_ENUM.index = 3
pb.MSG_EMAIL_ACTIONID_MSG_EMAIL_ADJUNCT_ENUM.number = 3
pb.MSG_EMAIL_ACTIONID_MSG_EMAIL_DELETE_ENUM.name = "MSG_EMAIL_DELETE"
pb.MSG_EMAIL_ACTIONID_MSG_EMAIL_DELETE_ENUM.index = 4
pb.MSG_EMAIL_ACTIONID_MSG_EMAIL_DELETE_ENUM.number = 4
pb.MSG_EMAIL_ACTIONID_MSG_EMAIL_NEWEMAIL_ENUM.name = "MSG_EMAIL_NEWEMAIL"
pb.MSG_EMAIL_ACTIONID_MSG_EMAIL_NEWEMAIL_ENUM.index = 5
pb.MSG_EMAIL_ACTIONID_MSG_EMAIL_NEWEMAIL_ENUM.number = 5
pb.MSG_EMAIL_ACTIONID.name = "MSG_EMAIL_ACTIONID"
pb.MSG_EMAIL_ACTIONID.full_name = ".MSG_EMAIL_ACTIONID"
pb.MSG_EMAIL_ACTIONID.values = {pb.MSG_EMAIL_ACTIONID_MSG_EMAIL_NONE_ENUM,pb.MSG_EMAIL_ACTIONID_MSG_EMAIL_GETLIST_ENUM,pb.MSG_EMAIL_ACTIONID_MSG_EMAIL_READEMAIL_ENUM,pb.MSG_EMAIL_ACTIONID_MSG_EMAIL_ADJUNCT_ENUM,pb.MSG_EMAIL_ACTIONID_MSG_EMAIL_DELETE_ENUM,pb.MSG_EMAIL_ACTIONID_MSG_EMAIL_NEWEMAIL_ENUM}
pb.CS_EMAIL_GETEMAILLIST_GETTIME_FIELD.name = "GetTime"
pb.CS_EMAIL_GETEMAILLIST_GETTIME_FIELD.full_name = ".CS_Email_GetEmailList.GetTime"
pb.CS_EMAIL_GETEMAILLIST_GETTIME_FIELD.number = 1
pb.CS_EMAIL_GETEMAILLIST_GETTIME_FIELD.index = 0
pb.CS_EMAIL_GETEMAILLIST_GETTIME_FIELD.label = 2
pb.CS_EMAIL_GETEMAILLIST_GETTIME_FIELD.has_default_value = false
pb.CS_EMAIL_GETEMAILLIST_GETTIME_FIELD.default_value = 0
pb.CS_EMAIL_GETEMAILLIST_GETTIME_FIELD.type = 13
pb.CS_EMAIL_GETEMAILLIST_GETTIME_FIELD.cpp_type = 3

pb.CS_EMAIL_GETEMAILLIST_REFRESH_FIELD.name = "Refresh"
pb.CS_EMAIL_GETEMAILLIST_REFRESH_FIELD.full_name = ".CS_Email_GetEmailList.Refresh"
pb.CS_EMAIL_GETEMAILLIST_REFRESH_FIELD.number = 2
pb.CS_EMAIL_GETEMAILLIST_REFRESH_FIELD.index = 1
pb.CS_EMAIL_GETEMAILLIST_REFRESH_FIELD.label = 2
pb.CS_EMAIL_GETEMAILLIST_REFRESH_FIELD.has_default_value = false
pb.CS_EMAIL_GETEMAILLIST_REFRESH_FIELD.default_value = 0
pb.CS_EMAIL_GETEMAILLIST_REFRESH_FIELD.type = 13
pb.CS_EMAIL_GETEMAILLIST_REFRESH_FIELD.cpp_type = 3

pb.CS_EMAIL_GETEMAILLIST.name = "CS_Email_GetEmailList"
pb.CS_EMAIL_GETEMAILLIST.full_name = ".CS_Email_GetEmailList"
pb.CS_EMAIL_GETEMAILLIST.nested_types = {}
pb.CS_EMAIL_GETEMAILLIST.enum_types = {}
pb.CS_EMAIL_GETEMAILLIST.fields = {pb.CS_EMAIL_GETEMAILLIST_GETTIME_FIELD, pb.CS_EMAIL_GETEMAILLIST_REFRESH_FIELD}
pb.CS_EMAIL_GETEMAILLIST.is_extendable = false
pb.CS_EMAIL_GETEMAILLIST.extensions = {}
pb.SC_EMAIL_GETEMAILLIST_EMAILDATA_EMAILID_FIELD.name = "EmailID"
pb.SC_EMAIL_GETEMAILLIST_EMAILDATA_EMAILID_FIELD.full_name = ".SC_Email_GetEmailList.EmailData.EmailID"
pb.SC_EMAIL_GETEMAILLIST_EMAILDATA_EMAILID_FIELD.number = 1
pb.SC_EMAIL_GETEMAILLIST_EMAILDATA_EMAILID_FIELD.index = 0
pb.SC_EMAIL_GETEMAILLIST_EMAILDATA_EMAILID_FIELD.label = 2
pb.SC_EMAIL_GETEMAILLIST_EMAILDATA_EMAILID_FIELD.has_default_value = false
pb.SC_EMAIL_GETEMAILLIST_EMAILDATA_EMAILID_FIELD.default_value = 0
pb.SC_EMAIL_GETEMAILLIST_EMAILDATA_EMAILID_FIELD.type = 13
pb.SC_EMAIL_GETEMAILLIST_EMAILDATA_EMAILID_FIELD.cpp_type = 3

pb.SC_EMAIL_GETEMAILLIST_EMAILDATA_TITLE_FIELD.name = "Title"
pb.SC_EMAIL_GETEMAILLIST_EMAILDATA_TITLE_FIELD.full_name = ".SC_Email_GetEmailList.EmailData.Title"
pb.SC_EMAIL_GETEMAILLIST_EMAILDATA_TITLE_FIELD.number = 2
pb.SC_EMAIL_GETEMAILLIST_EMAILDATA_TITLE_FIELD.index = 1
pb.SC_EMAIL_GETEMAILLIST_EMAILDATA_TITLE_FIELD.label = 2
pb.SC_EMAIL_GETEMAILLIST_EMAILDATA_TITLE_FIELD.has_default_value = false
pb.SC_EMAIL_GETEMAILLIST_EMAILDATA_TITLE_FIELD.default_value = ""
pb.SC_EMAIL_GETEMAILLIST_EMAILDATA_TITLE_FIELD.type = 9
pb.SC_EMAIL_GETEMAILLIST_EMAILDATA_TITLE_FIELD.cpp_type = 9

pb.SC_EMAIL_GETEMAILLIST_EMAILDATA_SENDTIME_FIELD.name = "SendTime"
pb.SC_EMAIL_GETEMAILLIST_EMAILDATA_SENDTIME_FIELD.full_name = ".SC_Email_GetEmailList.EmailData.SendTime"
pb.SC_EMAIL_GETEMAILLIST_EMAILDATA_SENDTIME_FIELD.number = 3
pb.SC_EMAIL_GETEMAILLIST_EMAILDATA_SENDTIME_FIELD.index = 2
pb.SC_EMAIL_GETEMAILLIST_EMAILDATA_SENDTIME_FIELD.label = 2
pb.SC_EMAIL_GETEMAILLIST_EMAILDATA_SENDTIME_FIELD.has_default_value = false
pb.SC_EMAIL_GETEMAILLIST_EMAILDATA_SENDTIME_FIELD.default_value = 0
pb.SC_EMAIL_GETEMAILLIST_EMAILDATA_SENDTIME_FIELD.type = 13
pb.SC_EMAIL_GETEMAILLIST_EMAILDATA_SENDTIME_FIELD.cpp_type = 3

pb.SC_EMAIL_GETEMAILLIST_EMAILDATA_VALIDTIME_FIELD.name = "ValidTime"
pb.SC_EMAIL_GETEMAILLIST_EMAILDATA_VALIDTIME_FIELD.full_name = ".SC_Email_GetEmailList.EmailData.ValidTime"
pb.SC_EMAIL_GETEMAILLIST_EMAILDATA_VALIDTIME_FIELD.number = 4
pb.SC_EMAIL_GETEMAILLIST_EMAILDATA_VALIDTIME_FIELD.index = 3
pb.SC_EMAIL_GETEMAILLIST_EMAILDATA_VALIDTIME_FIELD.label = 2
pb.SC_EMAIL_GETEMAILLIST_EMAILDATA_VALIDTIME_FIELD.has_default_value = false
pb.SC_EMAIL_GETEMAILLIST_EMAILDATA_VALIDTIME_FIELD.default_value = 0
pb.SC_EMAIL_GETEMAILLIST_EMAILDATA_VALIDTIME_FIELD.type = 13
pb.SC_EMAIL_GETEMAILLIST_EMAILDATA_VALIDTIME_FIELD.cpp_type = 3

pb.SC_EMAIL_GETEMAILLIST_EMAILDATA_MARKREAD_FIELD.name = "MarkRead"
pb.SC_EMAIL_GETEMAILLIST_EMAILDATA_MARKREAD_FIELD.full_name = ".SC_Email_GetEmailList.EmailData.MarkRead"
pb.SC_EMAIL_GETEMAILLIST_EMAILDATA_MARKREAD_FIELD.number = 5
pb.SC_EMAIL_GETEMAILLIST_EMAILDATA_MARKREAD_FIELD.index = 4
pb.SC_EMAIL_GETEMAILLIST_EMAILDATA_MARKREAD_FIELD.label = 2
pb.SC_EMAIL_GETEMAILLIST_EMAILDATA_MARKREAD_FIELD.has_default_value = false
pb.SC_EMAIL_GETEMAILLIST_EMAILDATA_MARKREAD_FIELD.default_value = 0
pb.SC_EMAIL_GETEMAILLIST_EMAILDATA_MARKREAD_FIELD.type = 13
pb.SC_EMAIL_GETEMAILLIST_EMAILDATA_MARKREAD_FIELD.cpp_type = 3

pb.SC_EMAIL_GETEMAILLIST_EMAILDATA_HASADJUNCT_FIELD.name = "HasAdjunct"
pb.SC_EMAIL_GETEMAILLIST_EMAILDATA_HASADJUNCT_FIELD.full_name = ".SC_Email_GetEmailList.EmailData.HasAdjunct"
pb.SC_EMAIL_GETEMAILLIST_EMAILDATA_HASADJUNCT_FIELD.number = 6
pb.SC_EMAIL_GETEMAILLIST_EMAILDATA_HASADJUNCT_FIELD.index = 5
pb.SC_EMAIL_GETEMAILLIST_EMAILDATA_HASADJUNCT_FIELD.label = 2
pb.SC_EMAIL_GETEMAILLIST_EMAILDATA_HASADJUNCT_FIELD.has_default_value = false
pb.SC_EMAIL_GETEMAILLIST_EMAILDATA_HASADJUNCT_FIELD.default_value = false
pb.SC_EMAIL_GETEMAILLIST_EMAILDATA_HASADJUNCT_FIELD.type = 8
pb.SC_EMAIL_GETEMAILLIST_EMAILDATA_HASADJUNCT_FIELD.cpp_type = 7

pb.SC_EMAIL_GETEMAILLIST_EMAILDATA.name = "EmailData"
pb.SC_EMAIL_GETEMAILLIST_EMAILDATA.full_name = ".SC_Email_GetEmailList.EmailData"
pb.SC_EMAIL_GETEMAILLIST_EMAILDATA.nested_types = {}
pb.SC_EMAIL_GETEMAILLIST_EMAILDATA.enum_types = {}
pb.SC_EMAIL_GETEMAILLIST_EMAILDATA.fields = {pb.SC_EMAIL_GETEMAILLIST_EMAILDATA_EMAILID_FIELD, pb.SC_EMAIL_GETEMAILLIST_EMAILDATA_TITLE_FIELD, pb.SC_EMAIL_GETEMAILLIST_EMAILDATA_SENDTIME_FIELD, pb.SC_EMAIL_GETEMAILLIST_EMAILDATA_VALIDTIME_FIELD, pb.SC_EMAIL_GETEMAILLIST_EMAILDATA_MARKREAD_FIELD, pb.SC_EMAIL_GETEMAILLIST_EMAILDATA_HASADJUNCT_FIELD}
pb.SC_EMAIL_GETEMAILLIST_EMAILDATA.is_extendable = false
pb.SC_EMAIL_GETEMAILLIST_EMAILDATA.extensions = {}
pb.SC_EMAIL_GETEMAILLIST_EMAILDATA.containing_type = pb.SC_EMAIL_GETEMAILLIST
pb.SC_EMAIL_GETEMAILLIST_EMAILNUM_FIELD.name = "EmailNum"
pb.SC_EMAIL_GETEMAILLIST_EMAILNUM_FIELD.full_name = ".SC_Email_GetEmailList.EmailNum"
pb.SC_EMAIL_GETEMAILLIST_EMAILNUM_FIELD.number = 1
pb.SC_EMAIL_GETEMAILLIST_EMAILNUM_FIELD.index = 0
pb.SC_EMAIL_GETEMAILLIST_EMAILNUM_FIELD.label = 2
pb.SC_EMAIL_GETEMAILLIST_EMAILNUM_FIELD.has_default_value = false
pb.SC_EMAIL_GETEMAILLIST_EMAILNUM_FIELD.default_value = 0
pb.SC_EMAIL_GETEMAILLIST_EMAILNUM_FIELD.type = 13
pb.SC_EMAIL_GETEMAILLIST_EMAILNUM_FIELD.cpp_type = 3

pb.SC_EMAIL_GETEMAILLIST_EMAILLIST_FIELD.name = "EmailList"
pb.SC_EMAIL_GETEMAILLIST_EMAILLIST_FIELD.full_name = ".SC_Email_GetEmailList.EmailList"
pb.SC_EMAIL_GETEMAILLIST_EMAILLIST_FIELD.number = 2
pb.SC_EMAIL_GETEMAILLIST_EMAILLIST_FIELD.index = 1
pb.SC_EMAIL_GETEMAILLIST_EMAILLIST_FIELD.label = 3
pb.SC_EMAIL_GETEMAILLIST_EMAILLIST_FIELD.has_default_value = false
pb.SC_EMAIL_GETEMAILLIST_EMAILLIST_FIELD.default_value = {}
pb.SC_EMAIL_GETEMAILLIST_EMAILLIST_FIELD.message_type = pb.SC_EMAIL_GETEMAILLIST_EMAILDATA
pb.SC_EMAIL_GETEMAILLIST_EMAILLIST_FIELD.type = 11
pb.SC_EMAIL_GETEMAILLIST_EMAILLIST_FIELD.cpp_type = 10

pb.SC_EMAIL_GETEMAILLIST.name = "SC_Email_GetEmailList"
pb.SC_EMAIL_GETEMAILLIST.full_name = ".SC_Email_GetEmailList"
pb.SC_EMAIL_GETEMAILLIST.nested_types = {pb.SC_EMAIL_GETEMAILLIST_EMAILDATA}
pb.SC_EMAIL_GETEMAILLIST.enum_types = {}
pb.SC_EMAIL_GETEMAILLIST.fields = {pb.SC_EMAIL_GETEMAILLIST_EMAILNUM_FIELD, pb.SC_EMAIL_GETEMAILLIST_EMAILLIST_FIELD}
pb.SC_EMAIL_GETEMAILLIST.is_extendable = false
pb.SC_EMAIL_GETEMAILLIST.extensions = {}
pb.CS_EMAIL_READMAIL_EMAILID_FIELD.name = "EmailID"
pb.CS_EMAIL_READMAIL_EMAILID_FIELD.full_name = ".CS_Email_ReadMail.EmailID"
pb.CS_EMAIL_READMAIL_EMAILID_FIELD.number = 1
pb.CS_EMAIL_READMAIL_EMAILID_FIELD.index = 0
pb.CS_EMAIL_READMAIL_EMAILID_FIELD.label = 2
pb.CS_EMAIL_READMAIL_EMAILID_FIELD.has_default_value = false
pb.CS_EMAIL_READMAIL_EMAILID_FIELD.default_value = 0
pb.CS_EMAIL_READMAIL_EMAILID_FIELD.type = 13
pb.CS_EMAIL_READMAIL_EMAILID_FIELD.cpp_type = 3

pb.CS_EMAIL_READMAIL.name = "CS_Email_ReadMail"
pb.CS_EMAIL_READMAIL.full_name = ".CS_Email_ReadMail"
pb.CS_EMAIL_READMAIL.nested_types = {}
pb.CS_EMAIL_READMAIL.enum_types = {}
pb.CS_EMAIL_READMAIL.fields = {pb.CS_EMAIL_READMAIL_EMAILID_FIELD}
pb.CS_EMAIL_READMAIL.is_extendable = false
pb.CS_EMAIL_READMAIL.extensions = {}
pb.SC_EMAIL_READMAIL_ADJUNCTDATA_NUMPROP_FIELD.name = "NumProp"
pb.SC_EMAIL_READMAIL_ADJUNCTDATA_NUMPROP_FIELD.full_name = ".SC_Email_ReadMail.AdjunctData.NumProp"
pb.SC_EMAIL_READMAIL_ADJUNCTDATA_NUMPROP_FIELD.number = 1
pb.SC_EMAIL_READMAIL_ADJUNCTDATA_NUMPROP_FIELD.index = 0
pb.SC_EMAIL_READMAIL_ADJUNCTDATA_NUMPROP_FIELD.label = 3
pb.SC_EMAIL_READMAIL_ADJUNCTDATA_NUMPROP_FIELD.has_default_value = false
pb.SC_EMAIL_READMAIL_ADJUNCTDATA_NUMPROP_FIELD.default_value = {}
pb.SC_EMAIL_READMAIL_ADJUNCTDATA_NUMPROP_FIELD.type = 5
pb.SC_EMAIL_READMAIL_ADJUNCTDATA_NUMPROP_FIELD.cpp_type = 1

pb.SC_EMAIL_READMAIL_ADJUNCTDATA_BINDFLAG_FIELD.name = "BindFlag"
pb.SC_EMAIL_READMAIL_ADJUNCTDATA_BINDFLAG_FIELD.full_name = ".SC_Email_ReadMail.AdjunctData.BindFlag"
pb.SC_EMAIL_READMAIL_ADJUNCTDATA_BINDFLAG_FIELD.number = 2
pb.SC_EMAIL_READMAIL_ADJUNCTDATA_BINDFLAG_FIELD.index = 1
pb.SC_EMAIL_READMAIL_ADJUNCTDATA_BINDFLAG_FIELD.label = 2
pb.SC_EMAIL_READMAIL_ADJUNCTDATA_BINDFLAG_FIELD.has_default_value = false
pb.SC_EMAIL_READMAIL_ADJUNCTDATA_BINDFLAG_FIELD.default_value = 0
pb.SC_EMAIL_READMAIL_ADJUNCTDATA_BINDFLAG_FIELD.type = 5
pb.SC_EMAIL_READMAIL_ADJUNCTDATA_BINDFLAG_FIELD.cpp_type = 1

pb.SC_EMAIL_READMAIL_ADJUNCTDATA.name = "AdjunctData"
pb.SC_EMAIL_READMAIL_ADJUNCTDATA.full_name = ".SC_Email_ReadMail.AdjunctData"
pb.SC_EMAIL_READMAIL_ADJUNCTDATA.nested_types = {}
pb.SC_EMAIL_READMAIL_ADJUNCTDATA.enum_types = {}
pb.SC_EMAIL_READMAIL_ADJUNCTDATA.fields = {pb.SC_EMAIL_READMAIL_ADJUNCTDATA_NUMPROP_FIELD, pb.SC_EMAIL_READMAIL_ADJUNCTDATA_BINDFLAG_FIELD}
pb.SC_EMAIL_READMAIL_ADJUNCTDATA.is_extendable = false
pb.SC_EMAIL_READMAIL_ADJUNCTDATA.extensions = {}
pb.SC_EMAIL_READMAIL_ADJUNCTDATA.containing_type = pb.SC_EMAIL_READMAIL
pb.SC_EMAIL_READMAIL_EMAILID_FIELD.name = "EmailID"
pb.SC_EMAIL_READMAIL_EMAILID_FIELD.full_name = ".SC_Email_ReadMail.EmailID"
pb.SC_EMAIL_READMAIL_EMAILID_FIELD.number = 1
pb.SC_EMAIL_READMAIL_EMAILID_FIELD.index = 0
pb.SC_EMAIL_READMAIL_EMAILID_FIELD.label = 2
pb.SC_EMAIL_READMAIL_EMAILID_FIELD.has_default_value = false
pb.SC_EMAIL_READMAIL_EMAILID_FIELD.default_value = 0
pb.SC_EMAIL_READMAIL_EMAILID_FIELD.type = 13
pb.SC_EMAIL_READMAIL_EMAILID_FIELD.cpp_type = 3

pb.SC_EMAIL_READMAIL_TITLE_FIELD.name = "Title"
pb.SC_EMAIL_READMAIL_TITLE_FIELD.full_name = ".SC_Email_ReadMail.Title"
pb.SC_EMAIL_READMAIL_TITLE_FIELD.number = 2
pb.SC_EMAIL_READMAIL_TITLE_FIELD.index = 1
pb.SC_EMAIL_READMAIL_TITLE_FIELD.label = 2
pb.SC_EMAIL_READMAIL_TITLE_FIELD.has_default_value = false
pb.SC_EMAIL_READMAIL_TITLE_FIELD.default_value = ""
pb.SC_EMAIL_READMAIL_TITLE_FIELD.type = 9
pb.SC_EMAIL_READMAIL_TITLE_FIELD.cpp_type = 9

pb.SC_EMAIL_READMAIL_CONTENT_FIELD.name = "Content"
pb.SC_EMAIL_READMAIL_CONTENT_FIELD.full_name = ".SC_Email_ReadMail.Content"
pb.SC_EMAIL_READMAIL_CONTENT_FIELD.number = 3
pb.SC_EMAIL_READMAIL_CONTENT_FIELD.index = 2
pb.SC_EMAIL_READMAIL_CONTENT_FIELD.label = 2
pb.SC_EMAIL_READMAIL_CONTENT_FIELD.has_default_value = false
pb.SC_EMAIL_READMAIL_CONTENT_FIELD.default_value = ""
pb.SC_EMAIL_READMAIL_CONTENT_FIELD.type = 9
pb.SC_EMAIL_READMAIL_CONTENT_FIELD.cpp_type = 9

pb.SC_EMAIL_READMAIL_ADJUNCTLIST_FIELD.name = "AdjunctList"
pb.SC_EMAIL_READMAIL_ADJUNCTLIST_FIELD.full_name = ".SC_Email_ReadMail.AdjunctList"
pb.SC_EMAIL_READMAIL_ADJUNCTLIST_FIELD.number = 4
pb.SC_EMAIL_READMAIL_ADJUNCTLIST_FIELD.index = 3
pb.SC_EMAIL_READMAIL_ADJUNCTLIST_FIELD.label = 3
pb.SC_EMAIL_READMAIL_ADJUNCTLIST_FIELD.has_default_value = false
pb.SC_EMAIL_READMAIL_ADJUNCTLIST_FIELD.default_value = {}
pb.SC_EMAIL_READMAIL_ADJUNCTLIST_FIELD.message_type = pb.SC_EMAIL_READMAIL_ADJUNCTDATA
pb.SC_EMAIL_READMAIL_ADJUNCTLIST_FIELD.type = 11
pb.SC_EMAIL_READMAIL_ADJUNCTLIST_FIELD.cpp_type = 10

pb.SC_EMAIL_READMAIL.name = "SC_Email_ReadMail"
pb.SC_EMAIL_READMAIL.full_name = ".SC_Email_ReadMail"
pb.SC_EMAIL_READMAIL.nested_types = {pb.SC_EMAIL_READMAIL_ADJUNCTDATA}
pb.SC_EMAIL_READMAIL.enum_types = {}
pb.SC_EMAIL_READMAIL.fields = {pb.SC_EMAIL_READMAIL_EMAILID_FIELD, pb.SC_EMAIL_READMAIL_TITLE_FIELD, pb.SC_EMAIL_READMAIL_CONTENT_FIELD, pb.SC_EMAIL_READMAIL_ADJUNCTLIST_FIELD}
pb.SC_EMAIL_READMAIL.is_extendable = false
pb.SC_EMAIL_READMAIL.extensions = {}
pb.CS_EMAIL_GETADJUNCT_EMAILID_FIELD.name = "EmailID"
pb.CS_EMAIL_GETADJUNCT_EMAILID_FIELD.full_name = ".CS_Email_GetAdjunct.EmailID"
pb.CS_EMAIL_GETADJUNCT_EMAILID_FIELD.number = 1
pb.CS_EMAIL_GETADJUNCT_EMAILID_FIELD.index = 0
pb.CS_EMAIL_GETADJUNCT_EMAILID_FIELD.label = 2
pb.CS_EMAIL_GETADJUNCT_EMAILID_FIELD.has_default_value = false
pb.CS_EMAIL_GETADJUNCT_EMAILID_FIELD.default_value = 0
pb.CS_EMAIL_GETADJUNCT_EMAILID_FIELD.type = 13
pb.CS_EMAIL_GETADJUNCT_EMAILID_FIELD.cpp_type = 3

pb.CS_EMAIL_GETADJUNCT.name = "CS_Email_GetAdjunct"
pb.CS_EMAIL_GETADJUNCT.full_name = ".CS_Email_GetAdjunct"
pb.CS_EMAIL_GETADJUNCT.nested_types = {}
pb.CS_EMAIL_GETADJUNCT.enum_types = {}
pb.CS_EMAIL_GETADJUNCT.fields = {pb.CS_EMAIL_GETADJUNCT_EMAILID_FIELD}
pb.CS_EMAIL_GETADJUNCT.is_extendable = false
pb.CS_EMAIL_GETADJUNCT.extensions = {}
pb.SC_EMAIL_GETADJUNCT_EMAILID_FIELD.name = "EmailID"
pb.SC_EMAIL_GETADJUNCT_EMAILID_FIELD.full_name = ".SC_Email_GetAdjunct.EmailID"
pb.SC_EMAIL_GETADJUNCT_EMAILID_FIELD.number = 1
pb.SC_EMAIL_GETADJUNCT_EMAILID_FIELD.index = 0
pb.SC_EMAIL_GETADJUNCT_EMAILID_FIELD.label = 2
pb.SC_EMAIL_GETADJUNCT_EMAILID_FIELD.has_default_value = false
pb.SC_EMAIL_GETADJUNCT_EMAILID_FIELD.default_value = 0
pb.SC_EMAIL_GETADJUNCT_EMAILID_FIELD.type = 13
pb.SC_EMAIL_GETADJUNCT_EMAILID_FIELD.cpp_type = 3

pb.SC_EMAIL_GETADJUNCT_RESULT_FIELD.name = "Result"
pb.SC_EMAIL_GETADJUNCT_RESULT_FIELD.full_name = ".SC_Email_GetAdjunct.Result"
pb.SC_EMAIL_GETADJUNCT_RESULT_FIELD.number = 2
pb.SC_EMAIL_GETADJUNCT_RESULT_FIELD.index = 1
pb.SC_EMAIL_GETADJUNCT_RESULT_FIELD.label = 2
pb.SC_EMAIL_GETADJUNCT_RESULT_FIELD.has_default_value = false
pb.SC_EMAIL_GETADJUNCT_RESULT_FIELD.default_value = 0
pb.SC_EMAIL_GETADJUNCT_RESULT_FIELD.type = 13
pb.SC_EMAIL_GETADJUNCT_RESULT_FIELD.cpp_type = 3

pb.SC_EMAIL_GETADJUNCT.name = "SC_Email_GetAdjunct"
pb.SC_EMAIL_GETADJUNCT.full_name = ".SC_Email_GetAdjunct"
pb.SC_EMAIL_GETADJUNCT.nested_types = {}
pb.SC_EMAIL_GETADJUNCT.enum_types = {}
pb.SC_EMAIL_GETADJUNCT.fields = {pb.SC_EMAIL_GETADJUNCT_EMAILID_FIELD, pb.SC_EMAIL_GETADJUNCT_RESULT_FIELD}
pb.SC_EMAIL_GETADJUNCT.is_extendable = false
pb.SC_EMAIL_GETADJUNCT.extensions = {}
pb.CS_EMAIL_DELETEEMAIL_EMAILID_FIELD.name = "EmailID"
pb.CS_EMAIL_DELETEEMAIL_EMAILID_FIELD.full_name = ".CS_Email_DeleteEmail.EmailID"
pb.CS_EMAIL_DELETEEMAIL_EMAILID_FIELD.number = 1
pb.CS_EMAIL_DELETEEMAIL_EMAILID_FIELD.index = 0
pb.CS_EMAIL_DELETEEMAIL_EMAILID_FIELD.label = 2
pb.CS_EMAIL_DELETEEMAIL_EMAILID_FIELD.has_default_value = false
pb.CS_EMAIL_DELETEEMAIL_EMAILID_FIELD.default_value = 0
pb.CS_EMAIL_DELETEEMAIL_EMAILID_FIELD.type = 13
pb.CS_EMAIL_DELETEEMAIL_EMAILID_FIELD.cpp_type = 3

pb.CS_EMAIL_DELETEEMAIL.name = "CS_Email_DeleteEmail"
pb.CS_EMAIL_DELETEEMAIL.full_name = ".CS_Email_DeleteEmail"
pb.CS_EMAIL_DELETEEMAIL.nested_types = {}
pb.CS_EMAIL_DELETEEMAIL.enum_types = {}
pb.CS_EMAIL_DELETEEMAIL.fields = {pb.CS_EMAIL_DELETEEMAIL_EMAILID_FIELD}
pb.CS_EMAIL_DELETEEMAIL.is_extendable = false
pb.CS_EMAIL_DELETEEMAIL.extensions = {}
pb.SC_EMAIL_DELETEEMAIL_EMAILID_FIELD.name = "EmailID"
pb.SC_EMAIL_DELETEEMAIL_EMAILID_FIELD.full_name = ".SC_Email_DeleteEmail.EmailID"
pb.SC_EMAIL_DELETEEMAIL_EMAILID_FIELD.number = 1
pb.SC_EMAIL_DELETEEMAIL_EMAILID_FIELD.index = 0
pb.SC_EMAIL_DELETEEMAIL_EMAILID_FIELD.label = 2
pb.SC_EMAIL_DELETEEMAIL_EMAILID_FIELD.has_default_value = false
pb.SC_EMAIL_DELETEEMAIL_EMAILID_FIELD.default_value = 0
pb.SC_EMAIL_DELETEEMAIL_EMAILID_FIELD.type = 13
pb.SC_EMAIL_DELETEEMAIL_EMAILID_FIELD.cpp_type = 3

pb.SC_EMAIL_DELETEEMAIL_RESULT_FIELD.name = "Result"
pb.SC_EMAIL_DELETEEMAIL_RESULT_FIELD.full_name = ".SC_Email_DeleteEmail.Result"
pb.SC_EMAIL_DELETEEMAIL_RESULT_FIELD.number = 2
pb.SC_EMAIL_DELETEEMAIL_RESULT_FIELD.index = 1
pb.SC_EMAIL_DELETEEMAIL_RESULT_FIELD.label = 2
pb.SC_EMAIL_DELETEEMAIL_RESULT_FIELD.has_default_value = false
pb.SC_EMAIL_DELETEEMAIL_RESULT_FIELD.default_value = 0
pb.SC_EMAIL_DELETEEMAIL_RESULT_FIELD.type = 13
pb.SC_EMAIL_DELETEEMAIL_RESULT_FIELD.cpp_type = 3

pb.SC_EMAIL_DELETEEMAIL.name = "SC_Email_DeleteEmail"
pb.SC_EMAIL_DELETEEMAIL.full_name = ".SC_Email_DeleteEmail"
pb.SC_EMAIL_DELETEEMAIL.nested_types = {}
pb.SC_EMAIL_DELETEEMAIL.enum_types = {}
pb.SC_EMAIL_DELETEEMAIL.fields = {pb.SC_EMAIL_DELETEEMAIL_EMAILID_FIELD, pb.SC_EMAIL_DELETEEMAIL_RESULT_FIELD}
pb.SC_EMAIL_DELETEEMAIL.is_extendable = false
pb.SC_EMAIL_DELETEEMAIL.extensions = {}

CS_Email_DeleteEmail = protobuf.Message(pb.CS_EMAIL_DELETEEMAIL)
CS_Email_GetAdjunct = protobuf.Message(pb.CS_EMAIL_GETADJUNCT)
CS_Email_GetEmailList = protobuf.Message(pb.CS_EMAIL_GETEMAILLIST)
CS_Email_ReadMail = protobuf.Message(pb.CS_EMAIL_READMAIL)
MSG_EMAIL_ADJUNCT = 3
MSG_EMAIL_DELETE = 4
MSG_EMAIL_GETLIST = 1
MSG_EMAIL_NEWEMAIL = 5
MSG_EMAIL_NONE = 0
MSG_EMAIL_READEMAIL = 2
SC_Email_DeleteEmail = protobuf.Message(pb.SC_EMAIL_DELETEEMAIL)
SC_Email_GetAdjunct = protobuf.Message(pb.SC_EMAIL_GETADJUNCT)
SC_Email_GetEmailList = protobuf.Message(pb.SC_EMAIL_GETEMAILLIST)
SC_Email_GetEmailList.EmailData = protobuf.Message(pb.SC_EMAIL_GETEMAILLIST_EMAILDATA)
SC_Email_ReadMail = protobuf.Message(pb.SC_EMAIL_READMAIL)
SC_Email_ReadMail.AdjunctData = protobuf.Message(pb.SC_EMAIL_READMAIL_ADJUNCTDATA)

