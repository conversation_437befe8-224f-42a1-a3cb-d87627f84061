-- 血条部件
HpCompCache = HpCompCache or {}
local hpCompCache = HpCompCache

local enemyBgColor = Color(1, 1, 0, 1)
local enemyBarColor = Color(1, 0.1, 0.1, 1)

local hpPrefab = nil
local function HPComp_Init(self)
	if not self.hpObj or tolua.isnull(self.hpObj) then
		if not hpPrefab then
			hpPrefab = HotResManager.ReadUI('ui/Main/HpBar')
			if not hpPrefab then
				error('找不到血条预制体')
				return
			end
		end
		
		local hpObj = GameObject.Instantiate(hpPrefab, EntityModule.modelUIContainerTrans)
		local hpObjTrans = hpObj.transform
		local objList = {}
		Helper.FillLuaComps(hpObjTrans, objList)
		self.hpObj = hpObj
		self.hpObjTrans = hpObjTrans
		self.objList = objList

		self.attachComp = hpObj:AddComponent(typeof(AttachComp))
		self.attachComp.isBillboard = true
	else
		self.hpObj:SetActive(true)
	end
	self.forceShowHpBar = false
	self.attachComp.attachTarget = self.host.modelTrans
	self:UpdateTarget()
	self:UpdateColor()
	self:UpdateSelfHp(nil, self.host:GetProperty(PLAYER_FIELD.PLAYER_FIELD_HP))
end

local function HPComp_UpdateHp(uid, old, new)
	local entity = EntityModule:GetEntity(tostring(uid))
	if not entity then
		return
	end

	local self = entity.hpComp
	if not self then
		return
	end
	
	self:UpdateSelfHp(old, new)
end

local function HPComp_UpdateSelfHp(self, old, new)
	if tolua.isnull(self.hpObj) then
		return
	end
	
	local curHP = new
	local maxHP = self.host:GetProperty(PLAYER_FIELD.PLAYER_FIELD_MAXHP)
	if not curHP or not maxHP or maxHP == 0 then
		return
	end
	if curHP > maxHP then
		curHP = maxHP
	end

	self.objList.Img_Bar.fillAmount = curHP / maxHP
	
	if not self.hideHp then
		if curHP == maxHP and not self.forceShowHpBar then
			self.objList.Img_Bg.enabled = false
			self.objList.Img_Bar.enabled = false
		else
			self.objList.Img_Bg.enabled = true
			self.objList.Img_Bar.enabled = true
			self.forceShowHpBar = true
		end
	end
end

local function HPComp_UpdateColor(self)
	self.objList.Img_Bg.color = enemyBgColor
	self.objList.Img_Bar.color = enemyBarColor
end

local function HPComp_UpdateTarget(self)
	if self.host.nameHeight and self.host.modelTrans then
		local dir = (self.host.modelTrans.forward * -1) * self.host.nameHeight
		dir.y = dir.y + 0.3
		self.attachComp:SetPosOffset(true, dir, nil)
	end
	
	local hideHp = false
	if self.host.buffLC and self.host.buffLC:CheckExStatus(ExStatus.NoDamage) then
		hideHp = true
	end
	self.hideHp = hideHp
	if not hideHp then
		self:UpdateSelfHp(nil, self.host:GetProperty(PLAYER_FIELD.PLAYER_FIELD_HP))
	else
		self.objList.Img_Bg.enabled = false
		self.objList.Img_Bar.enabled = false
	end
	
	local typeName = ''
	if self.host.monsterID then
		local objConfig = Schemes.TowerBattleObject:GetByType(TowerBattleObjType.RegionObjType_Tower, self.host.monsterID)
		if objConfig and objConfig.ShowTypeRange == 2 then
			typeName = objConfig.ShowTypeName[2]
		end
	end
	self.objList.Txt_TypeName.text = typeName
	if typeName == '' then
		self.objList.Img_TypeBG.enabled = false
	else
		self.objList.Img_TypeBG.enabled = true
	end
end

local function HPComp_Destroy(self)
	self.hpObj:SetActive(false)
	table.insert(hpCompCache, self)
end

function CreateHpComp( entity )
	local cacheSize = #hpCompCache
	if cacheSize > 0 then
		local result = hpCompCache[cacheSize]
		table.remove(hpCompCache)
		
		result.host = entity
		result:Init()
		return result
	end
	local hpComp = {
		host = entity,
		
		Init = HPComp_Init,
		UpdateHp = HPComp_UpdateHp,
		UpdateSelfHp = HPComp_UpdateSelfHp,
		UpdateColor = HPComp_UpdateColor,
		UpdateTarget = HPComp_UpdateTarget,
		Destroy = HPComp_Destroy,
	}
	hpComp:Init()
	return hpComp
end

EventManager:Subscribe(EventID.EntityPropertyUpdate_Hp, HPComp_UpdateHp)