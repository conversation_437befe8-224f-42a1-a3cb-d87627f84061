--[[
********************************************************************
    created:	2024/07/19
    author :	李锦剑
    purpose:    用于定义类型，不写代码
*********************************************************************
--]]

--#region
----------------------------表格类型定义----------------------------[[

---新升星表
---@class EquipSmeltStarCfg
---@field Id integer ID
---@field Name string 名称
---@field Remark string 说明
---@field Icon string 图标
---@field ActorDataCatalog integer 角色数据类别
---@field SmeltID integer 升星Id
---@field StarLvl integer 升星等级
---@field StarExp integer 升星经验(等级所需经验值)
---@field StarExpInc integer 升星增加经验值(消耗物品增加经验值)
---@field CostGoodsID1 integer 升星消耗物品1
---@field CostGoodsID1Num integer 升星消耗物品1数量
---@field CostGoodsID2 integer 升星消耗物品2
---@field CostGoodsID2Num integer 升星消耗物品2数量
---@field PropId integer 激活的通用属性Id，CommonProp表


---新升星表数据
---@class EquipSmeltStarDataCfg
---@field Id integer ID
---@field Name string 名称
---@field Remark string 说明
---@field EquipSmeltStarId integer EquipSmeltStar表ID
---@field ActorDataCatalog integer 逻辑值ID
---@field ActivePremissType	integer 解锁条件类型
---@field ActivePremiss1 integer 条件参数1
---@field ActivePremiss2 integer 条件参数2
---@field OprateType integer 类型1=天赋2=基因


---通用属性表
---@class CommonPropCfg
---@field Id integer ID
---@field Name string 名称
---@field Remark string 说明
---@field Icon string 图标
---@field TotalRemark string 总属性说明，客户端显示用
---@field RemarkByStartExp string 附着在火之装备上的属性，根据升星经验来显示说明
---@field Quality integer 本属性的品质
---@field OfferWeight integer 供选权重
---@field MaxGotTimes integer 供选前提:最大获得次数
---@field OfferPremisePropId integer 供选前提:装备的最低升星经验
---@field OfferPremiseGoodsID integer 供选前提:拥有此物品Id的装备(且升星经验达到下一列指定的值)（不一定是火之装备）
---@field OfferPremiseMinGunLvl integer 供选前提:最低枪械等级
---@field StrValues string[] 字符串(数组)
---@field PropType any 枚举，属性类型

---关卡表
---@class CatMainStageCfg
---@field ID integer 关ID
---@field Name string 小关名称
---@field Desc string 小关描述
---@field FrontBarrier integer 前置小关ID	
---@field FrontType integer 前置小关类型1主线2挑战3金币副本4每日挑战（生存模式）5-14消耗道具爬塔模式	
---@field Need string 挑战消耗（物品1ID；物品1数量；广告物品1ID;物品2ID；物品2数量；广告物品2ID）	
---@field EctypeID integer 对应副本ID-存数据用	
---@field ChapSkillID integer 随机技能ID	
---@field EndCondition integer 副本宝箱领取数据ID【ActorDataCatalog.csv】	
---@field EndPram1 integer 副本完成情况数据ID【ActorDataCatalog.csv】
---@field EndPram2 integer 结束条件参数2
---@field EndPram3 integer 是否支持广告复活	
---@field RefreshTime integer[] 初始默认格子数横向X;纵向Y，最大5*7	
---@field ChapMonster string 每日随机属buff性数据ID【ActorDataCatalog.csv】	
---@field FightSkill string 进关卡的BUFF：CommonProp.csvID	
---@field DiffLevel	number[] 关卡难度	
---@field Map string MissionInfo.csv表的ID
---@field First_Prize integer 首通给角色升级经验
---@field PrizeID integer[]	没用
---@field FailPrizeID integer 失败奖励	
---@field MaxGold integer 背景音乐ID=sound.csv
---@field Rebirth_times	integer[] 死亡复活次数	
---@field Rebirth_item1	string[] 胜利通关奖励
---@field Rebirth_item2	string[] 关卡最大波数
---@field Max_time integer 通关最大时长
---@field Icon string 旗帜图标主线1普通2BOSS挑战是creatureID		
---@field PrizeLoopIds integer[] 宝箱的奖励Id序号
---@field MapPos string	胜利结算双倍广告ID；全都要单局；单局刷新次数；失败三倍广告ID：CommonText.csv
---@field Desc2	string 小关描述2
---@field GeneralDrop string 关卡章节宝箱	
---@field MapTexture string	关卡地图
---@field MaxSkillLvl integer 技能等级上限	
---@field Interval_Bestow number 3选1刷新时间间隔
---@field MaxSkillCount integer 最少出战X件装备


---签到奖励表
---@class PrizesevenSigninCfg
---@field Id integer 签到ID
---@field Name string 名字
---@field Remark string 说明
---@field PrizeType integer 分类，1七日签到，2累计签到
---@field Sort integer 分类序号
---@field ReceivePremiss integer 领取条件：1(七日)签到天数，2累计签到天数
---@field ReceivePremissParam integer 领取参数
---@field PrizeId integer 奖励id
---@field ActorDataCatalog integer 奖励领取逻辑值
---@field ResetType integer 重置类型：0不重置，1领完第X个重置
---@field ResetTypeParam integer 重置条件

---充值表表
---@class RechargeCardCfg
---@field ID integer 充值卡ID	
---@field CardName string 充值卡名字	
---@field CardType integer 充值卡类型1三选一;2限购;3其他;4国家基金5等级限购礼包;6通用;7顺达邮政;8VIP推送;9投资	
---@field FirstPic1 string 首次标签1	
---@field FirstCharacter1 string 首次充值文字1，礼包描述	
---@field FirstPicBg string 首次商品图	
---@field FirstPic2 string 首次商标2	
---@field FirstCharacter2 string 首次文字1	
---@field FirstRMB integer 首次需要人民币/分	
---@field FirstDiamond integer 首次购买钻石	
---@field FirstBindDiamond integer 送绑定钻石	
---@field SaveParam1 integer 首次存储参数1	
---@field SaveParam2 integer 首次存储参数2	
---@field FirstDescription string 首次商品描述	
---@field Pic1 string 标签1-返利比例	
---@field Character1 string 文字1	
---@field Bg string 商品图	
---@field Pic2 string 商标2	
---@field Character2 string 文字1	
---@field Diamond integer 购买钻石	
---@field BindDiamond integer 送绑定钻石	
---@field Description string 广告ID（CommonText.csv）	
---@field LenovoID integer 用于前端判断刷新用	
---@field IOSID string IOS内购ID	
---@field coolpayID integer 好友邀请	
---@field PrizeID integer 购买奖励ID	
---@field NeedActvID integer 购买需要活动ID	
---@field CanBuyCount integer 活动期间可购买次数	
---@field ShowHighPrice integer 客户端显示原价-客户端直接显示rmb	
---@field ActvLogicID integer 活动时间逻辑数据ID	
---@field CountLogicID integer 购买次数逻辑数据ID	
---@field CountLogicIndex integer 购买次数逻辑数据索引	
---@field OnestoreID string OnestoreID 	
---@field GoogleID string GoogleID	
---@field IgaworksID string IgaworksID	
---@field VipScore integer 充值VIP积分	
---@field ActvScore integer 万宝阁活动充值积分	
---@field GameID integer 游戏ID	
---@field AddRrecharge integer 是否参与多倍充值（0是1否）	
---@field RechargePoint integer 充值卡对应积分	
---@field ShowGiftID integer 展示礼包ID：表格GiftShow.csv的ID

--------------------------------------------------------------------]]
--#endregion


---@class EquipSmeltStar_Class
---@field items EquipSmeltStarCfg[] 升星表配置列表
---@field Get fun(self:EquipSmeltStar_Class, id:integer|string):EquipSmeltStarCfg 获取升星表配置


---@class EquipSmeltStarData_Class
---@field items EquipSmeltStarDataCfg[] 升星表配置数据列表
---@field Get fun(self:EquipSmeltStarData_Class, id:integer|string):EquipSmeltStarDataCfg 获取升星表配置数据


---@class CommonProp_Class
---@field items CommonPropCfg[] 通用属性表配置列表
---@field Get fun(self:CommonProp_Class, id:integer|string):CommonPropCfg 获取通用属性表配置

---@class CatMainStage_Class
---@field items CatMainStageCfg[] 关卡表配置列表
---@field Get fun(self:CatMainStage_Class, id:integer|string):CatMainStageCfg 获取关卡表配置

---@class PrizesevenSignin_Class
---@field items PrizesevenSigninCfg[] 签到奖励表配置列表
---@field Get fun(self:PrizesevenSignin_Class, id:integer|string):PrizesevenSigninCfg 获取签到奖励表配置

---充值表--类
---@class RechargeCard_Class
---@field items RechargeCardCfg[] 充值表表配置列表
---@field Get fun(self:RechargeCard_Class, id:integer|string):RechargeCardCfg 获取充值表表配置
