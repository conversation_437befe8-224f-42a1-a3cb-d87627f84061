-- 战场指引UI
local luaID = ('UIBattleGuide')

local UIBattleGuide = {}

-- 初始化
function UIBattleGuide:OnCreate()
	self.objList.Btn_Close.onClick:AddListenerEx(self.OnClickClose)
	self.objList.Txt_Title.text = GetGameText(luaID, 1)
	self.objList.Txt_Close.text = GetGameText(luaID, 13)

	return true
end

-- 窗口开启
function UIBattleGuide:OnOpen(mainLevelID)
	self.mainLevelID = mainLevelID

	self:UpdateView()
end

function UIBattleGuide:UpdateView()
	if self.mainLevelID == 1 then
		self.objList.StarContainer1.gameObject:SetActive(false)
		self.objList.StarContainer2.gameObject:SetActive(false)
		self.objList.TowerContainer.gameObject:SetActive(true)
		self.objList.Txt_RangeDesc2Name.gameObject:SetActive(true)
		self.objList.Txt_RangeDesc2.gameObject:SetActive(true)
		self.objList.Txt_RangeDesc3Name:GetRectTransform().anchoredPosition = CachedVector3:Set(-193, -87, 0)
		self.objList.Txt_RangeDesc3:GetRectTransform().anchoredPosition = CachedVector3:Set(-81.3, -189, 0)
	else
		if self.mainLevelID == 4 then
			self.objList.StarContainer1.gameObject:SetActive(false)
			self.objList.StarContainer2.gameObject:SetActive(false)
		else
			self.objList.StarContainer1.gameObject:SetActive(true)
			self.objList.StarContainer2.gameObject:SetActive(true)
			if self.mainLevelID == 3 then
				self.objList.Img_Star3.gameObject:SetActive(false)
				self.objList.Img_Star6.gameObject:SetActive(false)
			else
				self.objList.Img_Star3.gameObject:SetActive(true)
				self.objList.Img_Star6.gameObject:SetActive(true)
			end
		end		
		self.objList.TowerContainer.gameObject:SetActive(false)
		self.objList.Txt_RangeDesc2Name.gameObject:SetActive(false)
		self.objList.Txt_RangeDesc2.gameObject:SetActive(false)
		self.objList.Txt_RangeDesc3Name:GetRectTransform().anchoredPosition = CachedVector3:Set(-193, -2, 0)
		self.objList.Txt_RangeDesc3:GetRectTransform().anchoredPosition = CachedVector3:Set(-81.3, -100, 0)
		
	end
	if self.mainLevelID == 1 then
		self.objList.Txt_RangeDesc1Name.text = GetGameText(luaID, 2)
		self.objList.Txt_RangeDesc2Name.text = GetGameText(luaID, 3)
		self.objList.Txt_RangeDesc3Name.text = GetGameText(luaID, 4)
		self.objList.Txt_RangeDesc1.text = GetGameText(luaID, 5)
		self.objList.Txt_RangeDesc2.text = GetGameText(luaID, 6)
		self.objList.Txt_RangeDesc3.text = GetGameText(luaID, 7)
	elseif self.mainLevelID == 2 or self.mainLevelID == 3 or self.mainLevelID == 6 then
		self.objList.Txt_RangeDesc1Name.text = GetGameText(luaID, 3)
		self.objList.Txt_RangeDesc3Name.text = GetGameText(luaID, 4)
		if self.mainLevelID == 3 then
			self.objList.Txt_RangeDesc1.text = string.format(GetGameText(luaID, 8), 2, 2)
		else
			self.objList.Txt_RangeDesc1.text = string.format(GetGameText(luaID, 8), 3, 3)
		end
		self.objList.Txt_RangeDesc3.text = GetGameText(luaID, 9)
	elseif self.mainLevelID == 5 then
		self.objList.Txt_RangeDesc1Name.text = GetGameText(luaID, 3)
		self.objList.Txt_RangeDesc3Name.text = GetGameText(luaID, 4)
		self.objList.Txt_RangeDesc1.text = string.format(GetGameText(luaID, 8), 3, 3)
		self.objList.Txt_RangeDesc3.text = GetGameText(luaID, 10)
	elseif self.mainLevelID == 4 then
		self.objList.Txt_RangeDesc1Name.text = GetGameText(luaID, 3)
		self.objList.Txt_RangeDesc3Name.text = GetGameText(luaID, 4)
		self.objList.Txt_RangeDesc1.text = GetGameText(luaID, 12)
		self.objList.Txt_RangeDesc3.text = GetGameText(luaID, 11)
	end
end

-- 点击关闭按钮
function UIBattleGuide.OnClickClose()
	local self = UIBattleGuide
	self:CloseSelf()
end

return UIBattleGuide