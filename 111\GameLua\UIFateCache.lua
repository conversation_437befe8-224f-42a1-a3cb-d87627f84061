---
--- Generated by <PERSON><PERSON><PERSON>(https://github.com/<PERSON><PERSON>ua)
--- Created by Administrator.
--- DateTime: 2023/7/23 22:39
--- 【菌落界面】缓存信息
---

local ClassBase = require("Base_Class")
local ECostType = require("UIFateEnumDefine").CostType

---@class UIFateCache:ClassBase
local this = class(ClassBase)

function this:_init()
    self.fields = {}
end

---@param name string @字段名（参考ECacheField）
---@param value integer @值
function this:SetField(name, value)
    self.fields[name] = value
end

---@param name string @字段名（参考ECacheField）
---@return integer @值
function this:GetField(name)
    return self.fields[name]
end

return this