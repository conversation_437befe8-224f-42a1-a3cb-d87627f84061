-- 好友模块
require "FriendMessage_pb"

local luaID = ('FriendModule')

FriendModule = {}

function FriendModule.Handle(action, data)
	--if action == FriendMessage_pb.MSG_FRIEND_GETLIST then
	--	--获取好友列表
	--	local m = FriendMessage_pb.SC_Friend_GetFriendList()
	--	m:ParseFromString(data)
	--	FriendModule.SC_Entity_Friend_GetFriendList(m)
	--end
end

Premier.Instance:RegisterLuaModule(MSG_MODULEID.MSG_MODULEID_FRIEND, 'FriendModule.Handle')
