// ReSharper disable InconsistentNaming
// ReSharper disable IdentifierTy<PERSON>

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;

using Apq.Extension;
using Apq.Unity3D.UnityHelpers;

using Cysharp.Threading.Tasks;

using DataStructure;

using Props;

using RxEventsM2V;

using Thing;

using ThingCdExecutors;

using UnityEngine;

using UniRx;

using View;

using X.PB;

namespace ThingCdExecutors
{
    /// <summary>
    // ///     V43.3 追击导弹执行器2 - 所有子弹攻击同一目标
    ///     保持原有飞行曲线，只修复转圈和目标死亡处理
    /// </summary>
    public class ThingCdExecutor_8 : ActorGunCdExecutor
    {
        /// <inheritdoc />
        public override async UniTaskVoid DoShoot(CancellationToken token)
        {
            // 找一个敌人作为攻击方向
            DistanceThing distanceEnemy = Thing.FindEnemy();

            // 没有攻击目标，不发子弹
            if (distanceEnemy is not { Thing2: not null })
            {
                // Debug.Log($"V43.3 追击导弹2 枪ID:{GunThing.CsvRow_Gun.Value.Id} - 未找到目标敌人，跳过发射");
                return;
            }

            // 按一轮攻击的持续时长预设结束时间
            base.DoShoot(token).Forget();

            await UniTask.SwitchToMainThread();

            // 确保此方法用于角色的枪
            if (Actor == null)
            {
                return;
            }

            CancellationTokenSource cts_Skill = CancellationTokenSource.CreateLinkedTokenSource(
                CTS_Shooter.Token
                , Actor.ThingBehaviour.GetCancellationTokenOnDestroy()
            );

            try
            {
                // 射击的基准方向
                Vector3 shootDir = (distanceEnemy.Thing2.Position - Actor.Position).normalized;

                int shootTimes = (int)Thing.GetTotalLong(PropType.BurstShootTimes).FirstOrDefault();
                List<double> burstDelayList = Thing.GetTotalDouble(PropType.BurstDelayList);
                List<long> burstBulletCountList = Thing.GetTotalLong(PropType.BurstBulletCountList);
                // 各轮射击角度的ID列表
                List<int> burstburstAnglesIds =
                    Thing.GetTotalLong(PropType.BurstAnglesIdList).Select(x => (int)x).ToList();

                // Debug.Log($"V43.3 追击导弹2配置 - 枪ID:{GunThing.CsvRow_Gun.Value.Id} 连射次数:{shootTimes} 延时列表:[{string.Join(",", burstDelayList)}] 子弹数量:[{string.Join(",", burstBulletCountList)}] 角度ID:[{string.Join(",", burstburstAnglesIds)}]");

                // 根据配置延时后射击
                _ = burstDelayList.Take(shootTimes).Select((x, i) =>
                {
                    if (burstBulletCountList.Count <= i)
                    {
                        return i;
                    }

                    long bulletQty = burstBulletCountList[i];

                    BurstOne(cts_Skill.Token, (float)x, distanceEnemy.Thing2, null, shootDir, bulletQty,
                            burstburstAnglesIds.IndexOf_ByCycle(i))
                        .Forget();
                    return i;
                }).ToList();
            }
            catch
            {
                // ignore
            }
        }

        /// <summary>
        ///     发射一轮
        /// </summary>
        /// <param name="token"></param>
        /// <param name="delay">延时:秒</param>
        /// <param name="attackBaseDirFollowThing">攻击基准方向跟随哪个物件(没值则取移动方向)</param>
        /// <param name="trackPos">定点位置</param>
        /// <param name="shootBaseDir">射击的基准方向</param>
        /// <param name="bulletQty">发射的子弹数量</param>
        /// <param name="anglesPropId">往哪个角度发射</param>
        private async UniTaskVoid BurstOne(CancellationToken token, float delay, ThingBase attackBaseDirFollowThing,
            Vector3? trackPos, Vector3 shootBaseDir, float bulletQty, int anglesPropId)
        {
            try
            {
                await UniTask.Delay(TimeSpan.FromSeconds(delay), cancellationToken: token);

                if (token.IsCancellationRequested)
                {
                    return;
                }

                // 没有发射角度就不射击
                if (!SingletonMgr.Instance.GlobalMgr.CommonPropCfg.TryGetValue(anglesPropId, out CommonProp anglesProp))
                {
                    // Debug.LogWarning($"V43.3 追击导弹2 - 角度配置ID {anglesPropId} 不存在");
                    return;
                }

                // 开火声音
                MessageBroker.Default.Publish(new PlayShootSound { Shooter = this });

                List<float> angles = anglesProp.DoubleValues
                    .Select(x => (float)x).ToList();

                // // V43.6 差异：获取单一目标，所有子弹都攻击同一个目标（每轮重新检查），支持死亡坐标
                var sameTarget = GetSameTargetForAllBullets();
                Vector3? targetDeathPosition = null;
                
                if (sameTarget == null)
                {
                    // // V43.6 没有活着的目标，检查是否有死亡坐标记录
                    targetDeathPosition = GetLastTargetDeathPosition();
                }

                string targetInfo = sameTarget != null 
                    ? $"ID:{sameTarget.CsvRow_BattleBrushEnemy.Id}({sameTarget.CsvRow_BattleBrushEnemy.EnemyName}) 血量:{sameTarget.Hp.Value:F0}"
                    : targetDeathPosition.HasValue 
                        ? $"死亡坐标:({targetDeathPosition.Value.x:F1},{targetDeathPosition.Value.y:F1})"
                        : "无目标，直线飞行";
                        
                // Debug.Log($"=== V43.6 追击导弹2一轮发射开始 === 子弹数:{bulletQty} 统一目标:{targetInfo} 角度配置:[{string.Join(",", angles)}]");

                for (int z = 0; z < bulletQty; z++)
                {
                    float angle = angles.IndexOf_ByCycle(z);
                    int maxPenetrateTimes = (int)Thing.GetTotalLong(PropType.MaxPenetrateTimes).FirstOrDefault();
                    int maxBounceTimes = (int)Thing.GetTotalLong(PropType.MaxBounceTimes).FirstOrDefault();
                    int maxSeparateTimes = (int)Thing.GetTotalLong(PropType.MaxSeparateTimes).FirstOrDefault();

                    // 发射方向依次按配置的角度旋转
                    BulletThing bullet = Thing.CreateBullet(this, attackBaseDirFollowThing, trackPos, angle,
                        maxPenetrateTimes, maxBounceTimes, maxSeparateTimes);

                    Vector3 dir_1 = shootBaseDir.RotateAround(Vector3.forward, angle);
                    
                    // 根据角色朝向计算前方偏移（完全复制ThingCdExecutor_1的逻辑）
                    float forwardOffsetX = 2f;
                    if (Actor.ThingBehaviour != null)
                    {
                        var skeAni = Actor.ThingBehaviour.GetComponentInChildren<Spine.Unity.SkeletonAnimation>();
                        if (skeAni != null && skeAni.skeleton != null)
                        {
                            // ScaleX = -1 表示朝右，ScaleX = 1 表示朝左
                            forwardOffsetX = skeAni.skeleton.ScaleX == -1 ? 2f : -2f;
                        }
                    }
                    
                    bullet.Position = Actor.Position + new Vector3(forwardOffsetX, 6f, 0); // 根据角色朝向从前方+2、往上Y+6的位置发射
                    bullet.MoveDirection_Straight.Value = dir_1;

                    // // V43.6 差异：所有子弹都分配同一个目标或死亡坐标
                    if (sameTarget != null)
                    {
                        bullet.TrackEnemy = sameTarget;
                    }
                    else if (targetDeathPosition.HasValue)
                    {
                        // // V43.6 设置目标死亡坐标
                        bullet.TargetDeathPosition = targetDeathPosition.Value;
                        // Debug.Log($"=== V43.6 子弹设置死亡坐标 === 子弹ID:{bullet.CsvRow_Bullet.Value.Id} 死亡坐标:({targetDeathPosition.Value.x:F1},{targetDeathPosition.Value.y:F1})");
                    }

                    // // V43.3 完整一行日志：枪ID、子弹ID、目标信息、坐标、伤害计算
                    var gunRow = GunThing.CsvRow_Gun.Value;
                    var bulletRow = bullet.CsvRow_Bullet.Value;
                    var damage = bullet.GetTotalDouble(PropType.Attack).FirstOrDefault();
                    
                    string bulletTargetInfo = sameTarget != null 
                        ? $"追击目标:{sameTarget.CsvRow_BattleBrushEnemy.Id}({sameTarget.CsvRow_BattleBrushEnemy.EnemyName}) 坐标:({sameTarget.Position.x:F1},{sameTarget.Position.y:F1}) 血量:{sameTarget.Hp.Value:F0}"
                        : "直线飞行(无目标)";
                    
                    // Debug.Log($"=== V43.3 追击导弹2发射 === 枪ID:{gunRow.Id} 枪名:{gunRow.Name} → 子弹ID:{bulletRow.Id} 子弹名:{bulletRow.Name} BulletType:{bulletRow.BulletType} 发射位置:({bullet.Position.x:F1},{bullet.Position.y:F1}) → {bulletTargetInfo} 子弹伤害:{damage:F1}");

                    MessageBroker.Default.Publish(new BornBullet
                    {
                        Bullet = bullet, PositionPre = bullet.Position - dir_1
                    });
                }
            }
            catch (OperationCanceledException) { throw; }
            catch (MissingReferenceException) { }
            catch (Exception ex)
            {
                Debug.LogException(ex);
            }
        }

        // // V43.6 记录最后目标的死亡坐标
        private Vector3? _lastTargetDeathPosition;
        private float _lastTargetDeathTime;
        
        /// <summary>
        // /// V43.6 获取最后目标的死亡坐标
        /// </summary>
        private Vector3? GetLastTargetDeathPosition()
        {
            // 死亡坐标有效期为10秒
            if (_lastTargetDeathPosition.HasValue && Time.time - _lastTargetDeathTime < 10f)
            {
                // Debug.Log($"=== V43.6 获取死亡坐标 === 死亡坐标:({_lastTargetDeathPosition.Value.x:F1},{_lastTargetDeathPosition.Value.y:F1}) 记录时间:{_lastTargetDeathTime:F1} 当前时间:{Time.time:F1}");
                return _lastTargetDeathPosition;
            }
            return null;
        }
        
        /// <summary>
        // /// V43.6 记录目标死亡坐标
        /// </summary>
        private void RecordTargetDeathPosition(Vector3 deathPosition)
        {
            _lastTargetDeathPosition = deathPosition;
            _lastTargetDeathTime = Time.time;
            // Debug.Log($"=== V43.6 记录目标死亡坐标 === 死亡坐标:({deathPosition.x:F1},{deathPosition.y:F1}) 记录时间:{Time.time:F1}");
        }
        
        // // V43.6 记录上一次的目标，用于检测目标死亡
        private MonsterThing _lastTarget;
        
        /// <summary>
        // /// V43.6 核心差异：获取单一目标，所有子弹都攻击同一个目标
        /// 增强目标有效性检查，确保每轮发射时重新验证目标状态，并记录死亡坐标
        /// </summary>
        private MonsterThing GetSameTargetForAllBullets()
        {
            // // V43.6 检查上一次目标是否死亡，如果死亡则记录死亡坐标
            if (_lastTarget != null && _lastTarget.Hp.Value <= 0)
            {
                RecordTargetDeathPosition(_lastTarget.Position);
                // Debug.Log($"=== V43.6 检测到目标死亡 === 目标:{_lastTarget.CsvRow_BattleBrushEnemy.Id}({_lastTarget.CsvRow_BattleBrushEnemy.EnemyName}) 死亡坐标:({_lastTarget.Position.x:F1},{_lastTarget.Position.y:F1})");
                _lastTarget = null;
            }
            
            // 获取角色位置和射程
            Vector3 actorPos = Actor.Position;
            float gunRange = Thing.TotalProp_GunRange;
            int totalMonstersInWorld = SingletonMgr.Instance.BattleMgr.Monsters.Count;
            
            // Debug.Log($"=== V43.6 同一目标搜索开始 === 角色位置:({actorPos.x:F1},{actorPos.y:F1}) 射程:{gunRange:F1} 当前世界怪物总数:{totalMonstersInWorld}");
            
            // // V43.3 增强的存活检查：检查对象、组件、激活状态和血量
            var aliveMonsters = SingletonMgr.Instance.BattleMgr.Monsters
                .Where(m => m != null && 
                           m.ThingBehaviour != null && 
                           m.ThingBehaviour.gameObject.activeInHierarchy && 
                           m.Hp.Value > 0)
                .ToList();
            
            // Debug.Log($"=== V43.3 怪物状态检查 === 存活怪物数:{aliveMonsters.Count}/{totalMonstersInWorld}");
            
            // 计算每个存活怪物的距离
            var monstersWithDistance = new List<(MonsterThing monster, float distance)>();
            foreach (var monster in aliveMonsters)
            {
                float distance = Vector3.Distance(actorPos, monster.Position);
                bool inRange = distance <= gunRange;
                monstersWithDistance.Add((monster, distance));
                
                // Debug.Log($"=== V43.3 怪物距离 === ID:{monster.CsvRow_BattleBrushEnemy.Id}({monster.CsvRow_BattleBrushEnemy.EnemyName}) 位置:({monster.Position.x:F1},{monster.Position.y:F1}) 距离:{distance:F1} 血量:{monster.Hp.Value:F0} 激活:{monster.ThingBehaviour.gameObject.activeInHierarchy} 射程内:{inRange}");
            }
            
            // 按距离排序，选取射程内最近的目标
            var closestTarget = monstersWithDistance
                .Where(t => t.distance <= gunRange)
                .OrderBy(t => t.distance)
                .FirstOrDefault().monster;
            
            if (closestTarget != null)
            {
                float distance = Vector3.Distance(actorPos, closestTarget.Position);
                // Debug.Log($"=== V43.6 同一目标选定 === ID:{closestTarget.CsvRow_BattleBrushEnemy.Id}({closestTarget.CsvRow_BattleBrushEnemy.EnemyName}) 距离:{distance:F1} 血量:{closestTarget.Hp.Value:F0} 对象状态:正常");
                
                // // V43.6 更新当前目标记录
                _lastTarget = closestTarget;
            }
            else
            {
                // Debug.Log($"=== V43.6 同一目标搜索 === 射程内无可用目标，所有子弹将直线飞行");
            }
            
            return closestTarget;
        }
    }
}