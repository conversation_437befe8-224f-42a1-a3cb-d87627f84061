-- Generated By protoc-gen-lua Do not Edit
local protobuf = require "protobuf/protobuf"
module('AIMessage_pb')
local pb = {}


pb.MSG_AI_ACTIONID = protobuf.EnumDescriptor();
pb.MSG_AI_ACTIONID_MSG_AI_NONE_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_AI_ACTIONID_MSG_AI_RUN_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_AI_ACTIONID_MSG_AI_ATTACK_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_AI_ACTIONID_MSG_AI_TURNFACE_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_AI_ACTIONID_MSG_AI_DAMAGE_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_AI_ACTIONID_MSG_AI_POSITION_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_AI_ACTIONID_MSG_AI_GETNEARENEMY_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_AI_ACTIONID_MSG_AI_ATTACK_POS_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_AI_ACTIONID_MSG_AI_DASH_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_AI_ACTIONID_MSG_AI_TALK_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_AI_ACTIONID_MSG_AI_EXSTATUS_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_AI_ACTIONID_MSG_AI_TASKCAMERA_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_AI_ACTIONID_MSG_AI_TRAPMOVE_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_AI_ACTIONID_MSG_AI_SHOWFX_ENUM = protobuf.EnumValueDescriptor();
pb.CS_AI_RUN = protobuf.Descriptor();
pb.CS_AI_RUN_DESTX_FIELD = protobuf.FieldDescriptor();
pb.CS_AI_RUN_DESTY_FIELD = protobuf.FieldDescriptor();
pb.CS_AI_RUN_DESTZ_FIELD = protobuf.FieldDescriptor();
pb.SC_AI_RUN = protobuf.Descriptor();
pb.SC_AI_RUN_ENTITYUID_FIELD = protobuf.FieldDescriptor();
pb.SC_AI_RUN_TARGETUID_FIELD = protobuf.FieldDescriptor();
pb.SC_AI_RUN_DESTX_FIELD = protobuf.FieldDescriptor();
pb.SC_AI_RUN_DESTY_FIELD = protobuf.FieldDescriptor();
pb.SC_AI_RUN_DESTZ_FIELD = protobuf.FieldDescriptor();
pb.SC_AI_RUN_MOVETYPE_FIELD = protobuf.FieldDescriptor();
pb.CS_AI_ATTACK = protobuf.Descriptor();
pb.CS_AI_ATTACK_ID_FIELD = protobuf.FieldDescriptor();
pb.CS_AI_ATTACK_TARGETUID_FIELD = protobuf.FieldDescriptor();
pb.CS_AI_ATTACK_ORIENTATION_FIELD = protobuf.FieldDescriptor();
pb.SC_AI_ATTACK = protobuf.Descriptor();
pb.SC_AI_ATTACK_ID_FIELD = protobuf.FieldDescriptor();
pb.SC_AI_ATTACK_LEVEL_FIELD = protobuf.FieldDescriptor();
pb.SC_AI_ATTACK_ENTITYUID_FIELD = protobuf.FieldDescriptor();
pb.SC_AI_ATTACK_ORIENTATION_FIELD = protobuf.FieldDescriptor();
pb.SC_AI_ATTACK_SRCX_FIELD = protobuf.FieldDescriptor();
pb.SC_AI_ATTACK_SRCZ_FIELD = protobuf.FieldDescriptor();
pb.SC_AI_ATTACK_TARGET_FIELD = protobuf.FieldDescriptor();
pb.SC_AI_ATTACK_ISPREPARE_FIELD = protobuf.FieldDescriptor();
pb.SC_AI_ATTACK_SERIALNUMBER_FIELD = protobuf.FieldDescriptor();
pb.SC_AI_ATTACK_SUBGRID1_FIELD = protobuf.FieldDescriptor();
pb.SC_AI_ATTACK_SUBGRID2_FIELD = protobuf.FieldDescriptor();
pb.SC_AI_ATTACK_SUBGRID3_FIELD = protobuf.FieldDescriptor();
pb.SC_AI_ATTACK_SUBGRID4_FIELD = protobuf.FieldDescriptor();
pb.SC_AI_ATTACK_SUBTARGETTYPE1_FIELD = protobuf.FieldDescriptor();
pb.SC_AI_ATTACK_SUBTARGETTYPE2_FIELD = protobuf.FieldDescriptor();
pb.SC_AI_ATTACK_SUBTARGETTYPE3_FIELD = protobuf.FieldDescriptor();
pb.SC_AI_ATTACK_SUBTARGETTYPE4_FIELD = protobuf.FieldDescriptor();
pb.CS_AI_TURNFACE = protobuf.Descriptor();
pb.CS_AI_TURNFACE_ORIENTATION_FIELD = protobuf.FieldDescriptor();
pb.SC_AI_TURNFACE = protobuf.Descriptor();
pb.SC_AI_TURNFACE_ENTITYUID_FIELD = protobuf.FieldDescriptor();
pb.SC_AI_TURNFACE_ORIENTATION_FIELD = protobuf.FieldDescriptor();
pb.CS_AI_DAMAGE = protobuf.Descriptor();
pb.CS_AI_DAMAGE_SKILLID_FIELD = protobuf.FieldDescriptor();
pb.CS_AI_DAMAGE_SKILLLEVEL_FIELD = protobuf.FieldDescriptor();
pb.CS_AI_DAMAGE_TARGETUID_FIELD = protobuf.FieldDescriptor();
pb.SC_AI_DAMAGE = protobuf.Descriptor();
pb.SC_AI_DAMAGE_VICTIM = protobuf.Descriptor();
pb.SC_AI_DAMAGE_VICTIM_UID_FIELD = protobuf.FieldDescriptor();
pb.SC_AI_DAMAGE_VICTIM_BLOOD_FIELD = protobuf.FieldDescriptor();
pb.SC_AI_DAMAGE_VICTIM_FLAG_FIELD = protobuf.FieldDescriptor();
pb.SC_AI_DAMAGE_ENTITYUID_FIELD = protobuf.FieldDescriptor();
pb.SC_AI_DAMAGE_SKILLID_FIELD = protobuf.FieldDescriptor();
pb.SC_AI_DAMAGE_SERIALNUMBER_FIELD = protobuf.FieldDescriptor();
pb.SC_AI_DAMAGE_SKILLINDEX_FIELD = protobuf.FieldDescriptor();
pb.SC_AI_DAMAGE_SKILLTIME_FIELD = protobuf.FieldDescriptor();
pb.SC_AI_DAMAGE_VICTIMS_FIELD = protobuf.FieldDescriptor();
pb.CS_AI_POSITION = protobuf.Descriptor();
pb.CS_AI_POSITION_DESTX_FIELD = protobuf.FieldDescriptor();
pb.CS_AI_POSITION_DESTY_FIELD = protobuf.FieldDescriptor();
pb.CS_AI_POSITION_DESTZ_FIELD = protobuf.FieldDescriptor();
pb.CS_AI_POSITION_SCENEID_FIELD = protobuf.FieldDescriptor();
pb.SC_AI_POSITION = protobuf.Descriptor();
pb.SC_AI_POSITION_ENTITYUID_FIELD = protobuf.FieldDescriptor();
pb.SC_AI_POSITION_DESTX_FIELD = protobuf.FieldDescriptor();
pb.SC_AI_POSITION_DESTY_FIELD = protobuf.FieldDescriptor();
pb.SC_AI_POSITION_DESTZ_FIELD = protobuf.FieldDescriptor();
pb.SC_AI_POSITION_ISSTAND_FIELD = protobuf.FieldDescriptor();
pb.SC_AI_GETNEARENEMY = protobuf.Descriptor();
pb.SC_AI_GETNEARENEMY_DESTX_FIELD = protobuf.FieldDescriptor();
pb.SC_AI_GETNEARENEMY_DESTY_FIELD = protobuf.FieldDescriptor();
pb.SC_AI_GETNEARENEMY_DESTZ_FIELD = protobuf.FieldDescriptor();
pb.CS_AI_ATTACK_POS = protobuf.Descriptor();
pb.CS_AI_ATTACK_POS_ID_FIELD = protobuf.FieldDescriptor();
pb.CS_AI_ATTACK_POS_DESTX_FIELD = protobuf.FieldDescriptor();
pb.CS_AI_ATTACK_POS_DESTY_FIELD = protobuf.FieldDescriptor();
pb.CS_AI_ATTACK_POS_DESTZ_FIELD = protobuf.FieldDescriptor();
pb.CS_AI_ATTACK_POS_ORIENTATION_FIELD = protobuf.FieldDescriptor();
pb.SC_AI_ATTACK_POS = protobuf.Descriptor();
pb.SC_AI_ATTACK_POS_ID_FIELD = protobuf.FieldDescriptor();
pb.SC_AI_ATTACK_POS_LEVEL_FIELD = protobuf.FieldDescriptor();
pb.SC_AI_ATTACK_POS_ENTITYUID_FIELD = protobuf.FieldDescriptor();
pb.SC_AI_ATTACK_POS_ORIENTATION_FIELD = protobuf.FieldDescriptor();
pb.SC_AI_ATTACK_POS_SRCX_FIELD = protobuf.FieldDescriptor();
pb.SC_AI_ATTACK_POS_SRCZ_FIELD = protobuf.FieldDescriptor();
pb.SC_AI_ATTACK_POS_DESTX_FIELD = protobuf.FieldDescriptor();
pb.SC_AI_ATTACK_POS_DESTY_FIELD = protobuf.FieldDescriptor();
pb.SC_AI_ATTACK_POS_DESTZ_FIELD = protobuf.FieldDescriptor();
pb.SC_AI_ATTACK_POS_ISPREPARE_FIELD = protobuf.FieldDescriptor();
pb.SC_AI_ATTACK_POS_SERIALNUMBER_FIELD = protobuf.FieldDescriptor();
pb.CS_AI_DASH = protobuf.Descriptor();
pb.SC_AI_DASH = protobuf.Descriptor();
pb.SC_AI_DASH_ENTITYUID_FIELD = protobuf.FieldDescriptor();
pb.SC_AI_DASH_DESTX_FIELD = protobuf.FieldDescriptor();
pb.SC_AI_DASH_DESTY_FIELD = protobuf.FieldDescriptor();
pb.SC_AI_DASH_DESTZ_FIELD = protobuf.FieldDescriptor();
pb.SC_AI_DASH_SPEED_FIELD = protobuf.FieldDescriptor();
pb.SC_AI_DASH_SRCUID_FIELD = protobuf.FieldDescriptor();
pb.SC_AI_DASH_SKILLID_FIELD = protobuf.FieldDescriptor();
pb.SC_AI_DASH_SERIALNUMBER_FIELD = protobuf.FieldDescriptor();
pb.SC_AI_DASH_SKILLINDEX_FIELD = protobuf.FieldDescriptor();
pb.SC_AI_DASH_SKILLTIME_FIELD = protobuf.FieldDescriptor();
pb.CS_AI_TALK = protobuf.Descriptor();
pb.SC_AI_TALK = protobuf.Descriptor();
pb.SC_AI_TALK_ENTITYUID_FIELD = protobuf.FieldDescriptor();
pb.SC_AI_TALK_TALKID_FIELD = protobuf.FieldDescriptor();
pb.SC_AI_EXSTATUS = protobuf.Descriptor();
pb.SC_AI_EXSTATUS_ENTITYUID_FIELD = protobuf.FieldDescriptor();
pb.SC_AI_EXSTATUS_STATUSTYPE_FIELD = protobuf.FieldDescriptor();
pb.SC_AI_EXSTATUS_HASSTATUS_FIELD = protobuf.FieldDescriptor();
pb.SC_AI_TASKCAMERA = protobuf.Descriptor();
pb.SC_AI_TASKCAMERA_ENTITYUID_FIELD = protobuf.FieldDescriptor();
pb.SC_AI_TASKCAMERA_TASKCAMERAID_FIELD = protobuf.FieldDescriptor();
pb.SC_AI_TRAPMOVE = protobuf.Descriptor();
pb.SC_AI_TRAPMOVE_ENTITYUID_FIELD = protobuf.FieldDescriptor();
pb.SC_AI_TRAPMOVE_MOVETYPE_FIELD = protobuf.FieldDescriptor();
pb.SC_AI_TRAPMOVE_FACE_FIELD = protobuf.FieldDescriptor();
pb.SC_AI_TRAPMOVE_MOVESPEED_FIELD = protobuf.FieldDescriptor();
pb.SC_AI_TRAPMOVE_CURX_FIELD = protobuf.FieldDescriptor();
pb.SC_AI_TRAPMOVE_CURY_FIELD = protobuf.FieldDescriptor();
pb.SC_AI_TRAPMOVE_CURZ_FIELD = protobuf.FieldDescriptor();
pb.SC_AI_TRAPMOVE_BORNX_FIELD = protobuf.FieldDescriptor();
pb.SC_AI_TRAPMOVE_BORNY_FIELD = protobuf.FieldDescriptor();
pb.SC_AI_TRAPMOVE_BORNZ_FIELD = protobuf.FieldDescriptor();
pb.SC_AI_TRAPMOVE_RADIUS_FIELD = protobuf.FieldDescriptor();
pb.SC_AI_TRAPMOVE_FLOATPARAM1_FIELD = protobuf.FieldDescriptor();
pb.SC_AI_SHOWFX = protobuf.Descriptor();
pb.SC_AI_SHOWFX_FXID_FIELD = protobuf.FieldDescriptor();
pb.SC_AI_SHOWFX_ORIENTATION_FIELD = protobuf.FieldDescriptor();
pb.SC_AI_SHOWFX_DESTX_FIELD = protobuf.FieldDescriptor();
pb.SC_AI_SHOWFX_DESTY_FIELD = protobuf.FieldDescriptor();
pb.SC_AI_SHOWFX_DESTZ_FIELD = protobuf.FieldDescriptor();

pb.MSG_AI_ACTIONID_MSG_AI_NONE_ENUM.name = "MSG_AI_NONE"
pb.MSG_AI_ACTIONID_MSG_AI_NONE_ENUM.index = 0
pb.MSG_AI_ACTIONID_MSG_AI_NONE_ENUM.number = 0
pb.MSG_AI_ACTIONID_MSG_AI_RUN_ENUM.name = "MSG_AI_RUN"
pb.MSG_AI_ACTIONID_MSG_AI_RUN_ENUM.index = 1
pb.MSG_AI_ACTIONID_MSG_AI_RUN_ENUM.number = 1
pb.MSG_AI_ACTIONID_MSG_AI_ATTACK_ENUM.name = "MSG_AI_ATTACK"
pb.MSG_AI_ACTIONID_MSG_AI_ATTACK_ENUM.index = 2
pb.MSG_AI_ACTIONID_MSG_AI_ATTACK_ENUM.number = 2
pb.MSG_AI_ACTIONID_MSG_AI_TURNFACE_ENUM.name = "MSG_AI_TURNFACE"
pb.MSG_AI_ACTIONID_MSG_AI_TURNFACE_ENUM.index = 3
pb.MSG_AI_ACTIONID_MSG_AI_TURNFACE_ENUM.number = 3
pb.MSG_AI_ACTIONID_MSG_AI_DAMAGE_ENUM.name = "MSG_AI_DAMAGE"
pb.MSG_AI_ACTIONID_MSG_AI_DAMAGE_ENUM.index = 4
pb.MSG_AI_ACTIONID_MSG_AI_DAMAGE_ENUM.number = 6
pb.MSG_AI_ACTIONID_MSG_AI_POSITION_ENUM.name = "MSG_AI_POSITION"
pb.MSG_AI_ACTIONID_MSG_AI_POSITION_ENUM.index = 5
pb.MSG_AI_ACTIONID_MSG_AI_POSITION_ENUM.number = 7
pb.MSG_AI_ACTIONID_MSG_AI_GETNEARENEMY_ENUM.name = "MSG_AI_GETNEARENEMY"
pb.MSG_AI_ACTIONID_MSG_AI_GETNEARENEMY_ENUM.index = 6
pb.MSG_AI_ACTIONID_MSG_AI_GETNEARENEMY_ENUM.number = 10
pb.MSG_AI_ACTIONID_MSG_AI_ATTACK_POS_ENUM.name = "MSG_AI_ATTACK_POS"
pb.MSG_AI_ACTIONID_MSG_AI_ATTACK_POS_ENUM.index = 7
pb.MSG_AI_ACTIONID_MSG_AI_ATTACK_POS_ENUM.number = 11
pb.MSG_AI_ACTIONID_MSG_AI_DASH_ENUM.name = "MSG_AI_DASH"
pb.MSG_AI_ACTIONID_MSG_AI_DASH_ENUM.index = 8
pb.MSG_AI_ACTIONID_MSG_AI_DASH_ENUM.number = 12
pb.MSG_AI_ACTIONID_MSG_AI_TALK_ENUM.name = "MSG_AI_TALK"
pb.MSG_AI_ACTIONID_MSG_AI_TALK_ENUM.index = 9
pb.MSG_AI_ACTIONID_MSG_AI_TALK_ENUM.number = 13
pb.MSG_AI_ACTIONID_MSG_AI_EXSTATUS_ENUM.name = "MSG_AI_EXSTATUS"
pb.MSG_AI_ACTIONID_MSG_AI_EXSTATUS_ENUM.index = 10
pb.MSG_AI_ACTIONID_MSG_AI_EXSTATUS_ENUM.number = 14
pb.MSG_AI_ACTIONID_MSG_AI_TASKCAMERA_ENUM.name = "MSG_AI_TASKCAMERA"
pb.MSG_AI_ACTIONID_MSG_AI_TASKCAMERA_ENUM.index = 11
pb.MSG_AI_ACTIONID_MSG_AI_TASKCAMERA_ENUM.number = 15
pb.MSG_AI_ACTIONID_MSG_AI_TRAPMOVE_ENUM.name = "MSG_AI_TRAPMOVE"
pb.MSG_AI_ACTIONID_MSG_AI_TRAPMOVE_ENUM.index = 12
pb.MSG_AI_ACTIONID_MSG_AI_TRAPMOVE_ENUM.number = 16
pb.MSG_AI_ACTIONID_MSG_AI_SHOWFX_ENUM.name = "MSG_AI_SHOWFX"
pb.MSG_AI_ACTIONID_MSG_AI_SHOWFX_ENUM.index = 13
pb.MSG_AI_ACTIONID_MSG_AI_SHOWFX_ENUM.number = 17
pb.MSG_AI_ACTIONID.name = "MSG_AI_ACTIONID"
pb.MSG_AI_ACTIONID.full_name = ".MSG_AI_ACTIONID"
pb.MSG_AI_ACTIONID.values = {pb.MSG_AI_ACTIONID_MSG_AI_NONE_ENUM,pb.MSG_AI_ACTIONID_MSG_AI_RUN_ENUM,pb.MSG_AI_ACTIONID_MSG_AI_ATTACK_ENUM,pb.MSG_AI_ACTIONID_MSG_AI_TURNFACE_ENUM,pb.MSG_AI_ACTIONID_MSG_AI_DAMAGE_ENUM,pb.MSG_AI_ACTIONID_MSG_AI_POSITION_ENUM,pb.MSG_AI_ACTIONID_MSG_AI_GETNEARENEMY_ENUM,pb.MSG_AI_ACTIONID_MSG_AI_ATTACK_POS_ENUM,pb.MSG_AI_ACTIONID_MSG_AI_DASH_ENUM,pb.MSG_AI_ACTIONID_MSG_AI_TALK_ENUM,pb.MSG_AI_ACTIONID_MSG_AI_EXSTATUS_ENUM,pb.MSG_AI_ACTIONID_MSG_AI_TASKCAMERA_ENUM,pb.MSG_AI_ACTIONID_MSG_AI_TRAPMOVE_ENUM,pb.MSG_AI_ACTIONID_MSG_AI_SHOWFX_ENUM}
pb.CS_AI_RUN_DESTX_FIELD.name = "DestX"
pb.CS_AI_RUN_DESTX_FIELD.full_name = ".CS_AI_RUN.DestX"
pb.CS_AI_RUN_DESTX_FIELD.number = 1
pb.CS_AI_RUN_DESTX_FIELD.index = 0
pb.CS_AI_RUN_DESTX_FIELD.label = 1
pb.CS_AI_RUN_DESTX_FIELD.has_default_value = false
pb.CS_AI_RUN_DESTX_FIELD.default_value = 0.0
pb.CS_AI_RUN_DESTX_FIELD.type = 2
pb.CS_AI_RUN_DESTX_FIELD.cpp_type = 6

pb.CS_AI_RUN_DESTY_FIELD.name = "DestY"
pb.CS_AI_RUN_DESTY_FIELD.full_name = ".CS_AI_RUN.DestY"
pb.CS_AI_RUN_DESTY_FIELD.number = 2
pb.CS_AI_RUN_DESTY_FIELD.index = 1
pb.CS_AI_RUN_DESTY_FIELD.label = 1
pb.CS_AI_RUN_DESTY_FIELD.has_default_value = false
pb.CS_AI_RUN_DESTY_FIELD.default_value = 0.0
pb.CS_AI_RUN_DESTY_FIELD.type = 2
pb.CS_AI_RUN_DESTY_FIELD.cpp_type = 6

pb.CS_AI_RUN_DESTZ_FIELD.name = "DestZ"
pb.CS_AI_RUN_DESTZ_FIELD.full_name = ".CS_AI_RUN.DestZ"
pb.CS_AI_RUN_DESTZ_FIELD.number = 3
pb.CS_AI_RUN_DESTZ_FIELD.index = 2
pb.CS_AI_RUN_DESTZ_FIELD.label = 1
pb.CS_AI_RUN_DESTZ_FIELD.has_default_value = false
pb.CS_AI_RUN_DESTZ_FIELD.default_value = 0.0
pb.CS_AI_RUN_DESTZ_FIELD.type = 2
pb.CS_AI_RUN_DESTZ_FIELD.cpp_type = 6

pb.CS_AI_RUN.name = "CS_AI_RUN"
pb.CS_AI_RUN.full_name = ".CS_AI_RUN"
pb.CS_AI_RUN.nested_types = {}
pb.CS_AI_RUN.enum_types = {}
pb.CS_AI_RUN.fields = {pb.CS_AI_RUN_DESTX_FIELD, pb.CS_AI_RUN_DESTY_FIELD, pb.CS_AI_RUN_DESTZ_FIELD}
pb.CS_AI_RUN.is_extendable = false
pb.CS_AI_RUN.extensions = {}
pb.SC_AI_RUN_ENTITYUID_FIELD.name = "EntityUID"
pb.SC_AI_RUN_ENTITYUID_FIELD.full_name = ".SC_AI_RUN.EntityUID"
pb.SC_AI_RUN_ENTITYUID_FIELD.number = 1
pb.SC_AI_RUN_ENTITYUID_FIELD.index = 0
pb.SC_AI_RUN_ENTITYUID_FIELD.label = 1
pb.SC_AI_RUN_ENTITYUID_FIELD.has_default_value = false
pb.SC_AI_RUN_ENTITYUID_FIELD.default_value = 0
pb.SC_AI_RUN_ENTITYUID_FIELD.type = 4
pb.SC_AI_RUN_ENTITYUID_FIELD.cpp_type = 4

pb.SC_AI_RUN_TARGETUID_FIELD.name = "TargetUID"
pb.SC_AI_RUN_TARGETUID_FIELD.full_name = ".SC_AI_RUN.TargetUID"
pb.SC_AI_RUN_TARGETUID_FIELD.number = 2
pb.SC_AI_RUN_TARGETUID_FIELD.index = 1
pb.SC_AI_RUN_TARGETUID_FIELD.label = 1
pb.SC_AI_RUN_TARGETUID_FIELD.has_default_value = false
pb.SC_AI_RUN_TARGETUID_FIELD.default_value = 0
pb.SC_AI_RUN_TARGETUID_FIELD.type = 4
pb.SC_AI_RUN_TARGETUID_FIELD.cpp_type = 4

pb.SC_AI_RUN_DESTX_FIELD.name = "DestX"
pb.SC_AI_RUN_DESTX_FIELD.full_name = ".SC_AI_RUN.DestX"
pb.SC_AI_RUN_DESTX_FIELD.number = 3
pb.SC_AI_RUN_DESTX_FIELD.index = 2
pb.SC_AI_RUN_DESTX_FIELD.label = 1
pb.SC_AI_RUN_DESTX_FIELD.has_default_value = false
pb.SC_AI_RUN_DESTX_FIELD.default_value = 0.0
pb.SC_AI_RUN_DESTX_FIELD.type = 2
pb.SC_AI_RUN_DESTX_FIELD.cpp_type = 6

pb.SC_AI_RUN_DESTY_FIELD.name = "DestY"
pb.SC_AI_RUN_DESTY_FIELD.full_name = ".SC_AI_RUN.DestY"
pb.SC_AI_RUN_DESTY_FIELD.number = 4
pb.SC_AI_RUN_DESTY_FIELD.index = 3
pb.SC_AI_RUN_DESTY_FIELD.label = 1
pb.SC_AI_RUN_DESTY_FIELD.has_default_value = false
pb.SC_AI_RUN_DESTY_FIELD.default_value = 0.0
pb.SC_AI_RUN_DESTY_FIELD.type = 2
pb.SC_AI_RUN_DESTY_FIELD.cpp_type = 6

pb.SC_AI_RUN_DESTZ_FIELD.name = "DestZ"
pb.SC_AI_RUN_DESTZ_FIELD.full_name = ".SC_AI_RUN.DestZ"
pb.SC_AI_RUN_DESTZ_FIELD.number = 5
pb.SC_AI_RUN_DESTZ_FIELD.index = 4
pb.SC_AI_RUN_DESTZ_FIELD.label = 1
pb.SC_AI_RUN_DESTZ_FIELD.has_default_value = false
pb.SC_AI_RUN_DESTZ_FIELD.default_value = 0.0
pb.SC_AI_RUN_DESTZ_FIELD.type = 2
pb.SC_AI_RUN_DESTZ_FIELD.cpp_type = 6

pb.SC_AI_RUN_MOVETYPE_FIELD.name = "MoveType"
pb.SC_AI_RUN_MOVETYPE_FIELD.full_name = ".SC_AI_RUN.MoveType"
pb.SC_AI_RUN_MOVETYPE_FIELD.number = 6
pb.SC_AI_RUN_MOVETYPE_FIELD.index = 5
pb.SC_AI_RUN_MOVETYPE_FIELD.label = 1
pb.SC_AI_RUN_MOVETYPE_FIELD.has_default_value = false
pb.SC_AI_RUN_MOVETYPE_FIELD.default_value = 0
pb.SC_AI_RUN_MOVETYPE_FIELD.type = 5
pb.SC_AI_RUN_MOVETYPE_FIELD.cpp_type = 1

pb.SC_AI_RUN.name = "SC_AI_RUN"
pb.SC_AI_RUN.full_name = ".SC_AI_RUN"
pb.SC_AI_RUN.nested_types = {}
pb.SC_AI_RUN.enum_types = {}
pb.SC_AI_RUN.fields = {pb.SC_AI_RUN_ENTITYUID_FIELD, pb.SC_AI_RUN_TARGETUID_FIELD, pb.SC_AI_RUN_DESTX_FIELD, pb.SC_AI_RUN_DESTY_FIELD, pb.SC_AI_RUN_DESTZ_FIELD, pb.SC_AI_RUN_MOVETYPE_FIELD}
pb.SC_AI_RUN.is_extendable = false
pb.SC_AI_RUN.extensions = {}
pb.CS_AI_ATTACK_ID_FIELD.name = "Id"
pb.CS_AI_ATTACK_ID_FIELD.full_name = ".CS_AI_ATTACK.Id"
pb.CS_AI_ATTACK_ID_FIELD.number = 1
pb.CS_AI_ATTACK_ID_FIELD.index = 0
pb.CS_AI_ATTACK_ID_FIELD.label = 1
pb.CS_AI_ATTACK_ID_FIELD.has_default_value = false
pb.CS_AI_ATTACK_ID_FIELD.default_value = 0
pb.CS_AI_ATTACK_ID_FIELD.type = 5
pb.CS_AI_ATTACK_ID_FIELD.cpp_type = 1

pb.CS_AI_ATTACK_TARGETUID_FIELD.name = "targetUID"
pb.CS_AI_ATTACK_TARGETUID_FIELD.full_name = ".CS_AI_ATTACK.targetUID"
pb.CS_AI_ATTACK_TARGETUID_FIELD.number = 2
pb.CS_AI_ATTACK_TARGETUID_FIELD.index = 1
pb.CS_AI_ATTACK_TARGETUID_FIELD.label = 1
pb.CS_AI_ATTACK_TARGETUID_FIELD.has_default_value = false
pb.CS_AI_ATTACK_TARGETUID_FIELD.default_value = 0
pb.CS_AI_ATTACK_TARGETUID_FIELD.type = 4
pb.CS_AI_ATTACK_TARGETUID_FIELD.cpp_type = 4

pb.CS_AI_ATTACK_ORIENTATION_FIELD.name = "Orientation"
pb.CS_AI_ATTACK_ORIENTATION_FIELD.full_name = ".CS_AI_ATTACK.Orientation"
pb.CS_AI_ATTACK_ORIENTATION_FIELD.number = 3
pb.CS_AI_ATTACK_ORIENTATION_FIELD.index = 2
pb.CS_AI_ATTACK_ORIENTATION_FIELD.label = 1
pb.CS_AI_ATTACK_ORIENTATION_FIELD.has_default_value = false
pb.CS_AI_ATTACK_ORIENTATION_FIELD.default_value = 0.0
pb.CS_AI_ATTACK_ORIENTATION_FIELD.type = 2
pb.CS_AI_ATTACK_ORIENTATION_FIELD.cpp_type = 6

pb.CS_AI_ATTACK.name = "CS_AI_ATTACK"
pb.CS_AI_ATTACK.full_name = ".CS_AI_ATTACK"
pb.CS_AI_ATTACK.nested_types = {}
pb.CS_AI_ATTACK.enum_types = {}
pb.CS_AI_ATTACK.fields = {pb.CS_AI_ATTACK_ID_FIELD, pb.CS_AI_ATTACK_TARGETUID_FIELD, pb.CS_AI_ATTACK_ORIENTATION_FIELD}
pb.CS_AI_ATTACK.is_extendable = false
pb.CS_AI_ATTACK.extensions = {}
pb.SC_AI_ATTACK_ID_FIELD.name = "Id"
pb.SC_AI_ATTACK_ID_FIELD.full_name = ".SC_AI_ATTACK.Id"
pb.SC_AI_ATTACK_ID_FIELD.number = 1
pb.SC_AI_ATTACK_ID_FIELD.index = 0
pb.SC_AI_ATTACK_ID_FIELD.label = 1
pb.SC_AI_ATTACK_ID_FIELD.has_default_value = false
pb.SC_AI_ATTACK_ID_FIELD.default_value = 0
pb.SC_AI_ATTACK_ID_FIELD.type = 5
pb.SC_AI_ATTACK_ID_FIELD.cpp_type = 1

pb.SC_AI_ATTACK_LEVEL_FIELD.name = "Level"
pb.SC_AI_ATTACK_LEVEL_FIELD.full_name = ".SC_AI_ATTACK.Level"
pb.SC_AI_ATTACK_LEVEL_FIELD.number = 2
pb.SC_AI_ATTACK_LEVEL_FIELD.index = 1
pb.SC_AI_ATTACK_LEVEL_FIELD.label = 1
pb.SC_AI_ATTACK_LEVEL_FIELD.has_default_value = false
pb.SC_AI_ATTACK_LEVEL_FIELD.default_value = 0
pb.SC_AI_ATTACK_LEVEL_FIELD.type = 5
pb.SC_AI_ATTACK_LEVEL_FIELD.cpp_type = 1

pb.SC_AI_ATTACK_ENTITYUID_FIELD.name = "EntityUID"
pb.SC_AI_ATTACK_ENTITYUID_FIELD.full_name = ".SC_AI_ATTACK.EntityUID"
pb.SC_AI_ATTACK_ENTITYUID_FIELD.number = 3
pb.SC_AI_ATTACK_ENTITYUID_FIELD.index = 2
pb.SC_AI_ATTACK_ENTITYUID_FIELD.label = 1
pb.SC_AI_ATTACK_ENTITYUID_FIELD.has_default_value = false
pb.SC_AI_ATTACK_ENTITYUID_FIELD.default_value = 0
pb.SC_AI_ATTACK_ENTITYUID_FIELD.type = 4
pb.SC_AI_ATTACK_ENTITYUID_FIELD.cpp_type = 4

pb.SC_AI_ATTACK_ORIENTATION_FIELD.name = "Orientation"
pb.SC_AI_ATTACK_ORIENTATION_FIELD.full_name = ".SC_AI_ATTACK.Orientation"
pb.SC_AI_ATTACK_ORIENTATION_FIELD.number = 4
pb.SC_AI_ATTACK_ORIENTATION_FIELD.index = 3
pb.SC_AI_ATTACK_ORIENTATION_FIELD.label = 1
pb.SC_AI_ATTACK_ORIENTATION_FIELD.has_default_value = false
pb.SC_AI_ATTACK_ORIENTATION_FIELD.default_value = 0.0
pb.SC_AI_ATTACK_ORIENTATION_FIELD.type = 2
pb.SC_AI_ATTACK_ORIENTATION_FIELD.cpp_type = 6

pb.SC_AI_ATTACK_SRCX_FIELD.name = "SrcX"
pb.SC_AI_ATTACK_SRCX_FIELD.full_name = ".SC_AI_ATTACK.SrcX"
pb.SC_AI_ATTACK_SRCX_FIELD.number = 5
pb.SC_AI_ATTACK_SRCX_FIELD.index = 4
pb.SC_AI_ATTACK_SRCX_FIELD.label = 1
pb.SC_AI_ATTACK_SRCX_FIELD.has_default_value = false
pb.SC_AI_ATTACK_SRCX_FIELD.default_value = 0.0
pb.SC_AI_ATTACK_SRCX_FIELD.type = 2
pb.SC_AI_ATTACK_SRCX_FIELD.cpp_type = 6

pb.SC_AI_ATTACK_SRCZ_FIELD.name = "SrcZ"
pb.SC_AI_ATTACK_SRCZ_FIELD.full_name = ".SC_AI_ATTACK.SrcZ"
pb.SC_AI_ATTACK_SRCZ_FIELD.number = 6
pb.SC_AI_ATTACK_SRCZ_FIELD.index = 5
pb.SC_AI_ATTACK_SRCZ_FIELD.label = 1
pb.SC_AI_ATTACK_SRCZ_FIELD.has_default_value = false
pb.SC_AI_ATTACK_SRCZ_FIELD.default_value = 0.0
pb.SC_AI_ATTACK_SRCZ_FIELD.type = 2
pb.SC_AI_ATTACK_SRCZ_FIELD.cpp_type = 6

pb.SC_AI_ATTACK_TARGET_FIELD.name = "Target"
pb.SC_AI_ATTACK_TARGET_FIELD.full_name = ".SC_AI_ATTACK.Target"
pb.SC_AI_ATTACK_TARGET_FIELD.number = 7
pb.SC_AI_ATTACK_TARGET_FIELD.index = 6
pb.SC_AI_ATTACK_TARGET_FIELD.label = 1
pb.SC_AI_ATTACK_TARGET_FIELD.has_default_value = false
pb.SC_AI_ATTACK_TARGET_FIELD.default_value = 0
pb.SC_AI_ATTACK_TARGET_FIELD.type = 4
pb.SC_AI_ATTACK_TARGET_FIELD.cpp_type = 4

pb.SC_AI_ATTACK_ISPREPARE_FIELD.name = "IsPrepare"
pb.SC_AI_ATTACK_ISPREPARE_FIELD.full_name = ".SC_AI_ATTACK.IsPrepare"
pb.SC_AI_ATTACK_ISPREPARE_FIELD.number = 8
pb.SC_AI_ATTACK_ISPREPARE_FIELD.index = 7
pb.SC_AI_ATTACK_ISPREPARE_FIELD.label = 1
pb.SC_AI_ATTACK_ISPREPARE_FIELD.has_default_value = false
pb.SC_AI_ATTACK_ISPREPARE_FIELD.default_value = 0
pb.SC_AI_ATTACK_ISPREPARE_FIELD.type = 5
pb.SC_AI_ATTACK_ISPREPARE_FIELD.cpp_type = 1

pb.SC_AI_ATTACK_SERIALNUMBER_FIELD.name = "SerialNumber"
pb.SC_AI_ATTACK_SERIALNUMBER_FIELD.full_name = ".SC_AI_ATTACK.SerialNumber"
pb.SC_AI_ATTACK_SERIALNUMBER_FIELD.number = 9
pb.SC_AI_ATTACK_SERIALNUMBER_FIELD.index = 8
pb.SC_AI_ATTACK_SERIALNUMBER_FIELD.label = 1
pb.SC_AI_ATTACK_SERIALNUMBER_FIELD.has_default_value = false
pb.SC_AI_ATTACK_SERIALNUMBER_FIELD.default_value = 0
pb.SC_AI_ATTACK_SERIALNUMBER_FIELD.type = 5
pb.SC_AI_ATTACK_SERIALNUMBER_FIELD.cpp_type = 1

pb.SC_AI_ATTACK_SUBGRID1_FIELD.name = "SubGrid1"
pb.SC_AI_ATTACK_SUBGRID1_FIELD.full_name = ".SC_AI_ATTACK.SubGrid1"
pb.SC_AI_ATTACK_SUBGRID1_FIELD.number = 10
pb.SC_AI_ATTACK_SUBGRID1_FIELD.index = 9
pb.SC_AI_ATTACK_SUBGRID1_FIELD.label = 1
pb.SC_AI_ATTACK_SUBGRID1_FIELD.has_default_value = false
pb.SC_AI_ATTACK_SUBGRID1_FIELD.default_value = 0
pb.SC_AI_ATTACK_SUBGRID1_FIELD.type = 5
pb.SC_AI_ATTACK_SUBGRID1_FIELD.cpp_type = 1

pb.SC_AI_ATTACK_SUBGRID2_FIELD.name = "SubGrid2"
pb.SC_AI_ATTACK_SUBGRID2_FIELD.full_name = ".SC_AI_ATTACK.SubGrid2"
pb.SC_AI_ATTACK_SUBGRID2_FIELD.number = 11
pb.SC_AI_ATTACK_SUBGRID2_FIELD.index = 10
pb.SC_AI_ATTACK_SUBGRID2_FIELD.label = 1
pb.SC_AI_ATTACK_SUBGRID2_FIELD.has_default_value = false
pb.SC_AI_ATTACK_SUBGRID2_FIELD.default_value = 0
pb.SC_AI_ATTACK_SUBGRID2_FIELD.type = 5
pb.SC_AI_ATTACK_SUBGRID2_FIELD.cpp_type = 1

pb.SC_AI_ATTACK_SUBGRID3_FIELD.name = "SubGrid3"
pb.SC_AI_ATTACK_SUBGRID3_FIELD.full_name = ".SC_AI_ATTACK.SubGrid3"
pb.SC_AI_ATTACK_SUBGRID3_FIELD.number = 12
pb.SC_AI_ATTACK_SUBGRID3_FIELD.index = 11
pb.SC_AI_ATTACK_SUBGRID3_FIELD.label = 1
pb.SC_AI_ATTACK_SUBGRID3_FIELD.has_default_value = false
pb.SC_AI_ATTACK_SUBGRID3_FIELD.default_value = 0
pb.SC_AI_ATTACK_SUBGRID3_FIELD.type = 5
pb.SC_AI_ATTACK_SUBGRID3_FIELD.cpp_type = 1

pb.SC_AI_ATTACK_SUBGRID4_FIELD.name = "SubGrid4"
pb.SC_AI_ATTACK_SUBGRID4_FIELD.full_name = ".SC_AI_ATTACK.SubGrid4"
pb.SC_AI_ATTACK_SUBGRID4_FIELD.number = 13
pb.SC_AI_ATTACK_SUBGRID4_FIELD.index = 12
pb.SC_AI_ATTACK_SUBGRID4_FIELD.label = 1
pb.SC_AI_ATTACK_SUBGRID4_FIELD.has_default_value = false
pb.SC_AI_ATTACK_SUBGRID4_FIELD.default_value = 0
pb.SC_AI_ATTACK_SUBGRID4_FIELD.type = 5
pb.SC_AI_ATTACK_SUBGRID4_FIELD.cpp_type = 1

pb.SC_AI_ATTACK_SUBTARGETTYPE1_FIELD.name = "SubTargetType1"
pb.SC_AI_ATTACK_SUBTARGETTYPE1_FIELD.full_name = ".SC_AI_ATTACK.SubTargetType1"
pb.SC_AI_ATTACK_SUBTARGETTYPE1_FIELD.number = 14
pb.SC_AI_ATTACK_SUBTARGETTYPE1_FIELD.index = 13
pb.SC_AI_ATTACK_SUBTARGETTYPE1_FIELD.label = 1
pb.SC_AI_ATTACK_SUBTARGETTYPE1_FIELD.has_default_value = false
pb.SC_AI_ATTACK_SUBTARGETTYPE1_FIELD.default_value = 0
pb.SC_AI_ATTACK_SUBTARGETTYPE1_FIELD.type = 5
pb.SC_AI_ATTACK_SUBTARGETTYPE1_FIELD.cpp_type = 1

pb.SC_AI_ATTACK_SUBTARGETTYPE2_FIELD.name = "SubTargetType2"
pb.SC_AI_ATTACK_SUBTARGETTYPE2_FIELD.full_name = ".SC_AI_ATTACK.SubTargetType2"
pb.SC_AI_ATTACK_SUBTARGETTYPE2_FIELD.number = 15
pb.SC_AI_ATTACK_SUBTARGETTYPE2_FIELD.index = 14
pb.SC_AI_ATTACK_SUBTARGETTYPE2_FIELD.label = 1
pb.SC_AI_ATTACK_SUBTARGETTYPE2_FIELD.has_default_value = false
pb.SC_AI_ATTACK_SUBTARGETTYPE2_FIELD.default_value = 0
pb.SC_AI_ATTACK_SUBTARGETTYPE2_FIELD.type = 5
pb.SC_AI_ATTACK_SUBTARGETTYPE2_FIELD.cpp_type = 1

pb.SC_AI_ATTACK_SUBTARGETTYPE3_FIELD.name = "SubTargetType3"
pb.SC_AI_ATTACK_SUBTARGETTYPE3_FIELD.full_name = ".SC_AI_ATTACK.SubTargetType3"
pb.SC_AI_ATTACK_SUBTARGETTYPE3_FIELD.number = 16
pb.SC_AI_ATTACK_SUBTARGETTYPE3_FIELD.index = 15
pb.SC_AI_ATTACK_SUBTARGETTYPE3_FIELD.label = 1
pb.SC_AI_ATTACK_SUBTARGETTYPE3_FIELD.has_default_value = false
pb.SC_AI_ATTACK_SUBTARGETTYPE3_FIELD.default_value = 0
pb.SC_AI_ATTACK_SUBTARGETTYPE3_FIELD.type = 5
pb.SC_AI_ATTACK_SUBTARGETTYPE3_FIELD.cpp_type = 1

pb.SC_AI_ATTACK_SUBTARGETTYPE4_FIELD.name = "SubTargetType4"
pb.SC_AI_ATTACK_SUBTARGETTYPE4_FIELD.full_name = ".SC_AI_ATTACK.SubTargetType4"
pb.SC_AI_ATTACK_SUBTARGETTYPE4_FIELD.number = 17
pb.SC_AI_ATTACK_SUBTARGETTYPE4_FIELD.index = 16
pb.SC_AI_ATTACK_SUBTARGETTYPE4_FIELD.label = 1
pb.SC_AI_ATTACK_SUBTARGETTYPE4_FIELD.has_default_value = false
pb.SC_AI_ATTACK_SUBTARGETTYPE4_FIELD.default_value = 0
pb.SC_AI_ATTACK_SUBTARGETTYPE4_FIELD.type = 5
pb.SC_AI_ATTACK_SUBTARGETTYPE4_FIELD.cpp_type = 1

pb.SC_AI_ATTACK.name = "SC_AI_ATTACK"
pb.SC_AI_ATTACK.full_name = ".SC_AI_ATTACK"
pb.SC_AI_ATTACK.nested_types = {}
pb.SC_AI_ATTACK.enum_types = {}
pb.SC_AI_ATTACK.fields = {pb.SC_AI_ATTACK_ID_FIELD, pb.SC_AI_ATTACK_LEVEL_FIELD, pb.SC_AI_ATTACK_ENTITYUID_FIELD, pb.SC_AI_ATTACK_ORIENTATION_FIELD, pb.SC_AI_ATTACK_SRCX_FIELD, pb.SC_AI_ATTACK_SRCZ_FIELD, pb.SC_AI_ATTACK_TARGET_FIELD, pb.SC_AI_ATTACK_ISPREPARE_FIELD, pb.SC_AI_ATTACK_SERIALNUMBER_FIELD, pb.SC_AI_ATTACK_SUBGRID1_FIELD, pb.SC_AI_ATTACK_SUBGRID2_FIELD, pb.SC_AI_ATTACK_SUBGRID3_FIELD, pb.SC_AI_ATTACK_SUBGRID4_FIELD, pb.SC_AI_ATTACK_SUBTARGETTYPE1_FIELD, pb.SC_AI_ATTACK_SUBTARGETTYPE2_FIELD, pb.SC_AI_ATTACK_SUBTARGETTYPE3_FIELD, pb.SC_AI_ATTACK_SUBTARGETTYPE4_FIELD}
pb.SC_AI_ATTACK.is_extendable = false
pb.SC_AI_ATTACK.extensions = {}
pb.CS_AI_TURNFACE_ORIENTATION_FIELD.name = "Orientation"
pb.CS_AI_TURNFACE_ORIENTATION_FIELD.full_name = ".CS_AI_TURNFACE.Orientation"
pb.CS_AI_TURNFACE_ORIENTATION_FIELD.number = 1
pb.CS_AI_TURNFACE_ORIENTATION_FIELD.index = 0
pb.CS_AI_TURNFACE_ORIENTATION_FIELD.label = 1
pb.CS_AI_TURNFACE_ORIENTATION_FIELD.has_default_value = false
pb.CS_AI_TURNFACE_ORIENTATION_FIELD.default_value = 0.0
pb.CS_AI_TURNFACE_ORIENTATION_FIELD.type = 2
pb.CS_AI_TURNFACE_ORIENTATION_FIELD.cpp_type = 6

pb.CS_AI_TURNFACE.name = "CS_AI_TURNFACE"
pb.CS_AI_TURNFACE.full_name = ".CS_AI_TURNFACE"
pb.CS_AI_TURNFACE.nested_types = {}
pb.CS_AI_TURNFACE.enum_types = {}
pb.CS_AI_TURNFACE.fields = {pb.CS_AI_TURNFACE_ORIENTATION_FIELD}
pb.CS_AI_TURNFACE.is_extendable = false
pb.CS_AI_TURNFACE.extensions = {}
pb.SC_AI_TURNFACE_ENTITYUID_FIELD.name = "EntityUID"
pb.SC_AI_TURNFACE_ENTITYUID_FIELD.full_name = ".SC_AI_TURNFACE.EntityUID"
pb.SC_AI_TURNFACE_ENTITYUID_FIELD.number = 1
pb.SC_AI_TURNFACE_ENTITYUID_FIELD.index = 0
pb.SC_AI_TURNFACE_ENTITYUID_FIELD.label = 1
pb.SC_AI_TURNFACE_ENTITYUID_FIELD.has_default_value = false
pb.SC_AI_TURNFACE_ENTITYUID_FIELD.default_value = 0
pb.SC_AI_TURNFACE_ENTITYUID_FIELD.type = 4
pb.SC_AI_TURNFACE_ENTITYUID_FIELD.cpp_type = 4

pb.SC_AI_TURNFACE_ORIENTATION_FIELD.name = "Orientation"
pb.SC_AI_TURNFACE_ORIENTATION_FIELD.full_name = ".SC_AI_TURNFACE.Orientation"
pb.SC_AI_TURNFACE_ORIENTATION_FIELD.number = 2
pb.SC_AI_TURNFACE_ORIENTATION_FIELD.index = 1
pb.SC_AI_TURNFACE_ORIENTATION_FIELD.label = 1
pb.SC_AI_TURNFACE_ORIENTATION_FIELD.has_default_value = false
pb.SC_AI_TURNFACE_ORIENTATION_FIELD.default_value = 0.0
pb.SC_AI_TURNFACE_ORIENTATION_FIELD.type = 2
pb.SC_AI_TURNFACE_ORIENTATION_FIELD.cpp_type = 6

pb.SC_AI_TURNFACE.name = "SC_AI_TURNFACE"
pb.SC_AI_TURNFACE.full_name = ".SC_AI_TURNFACE"
pb.SC_AI_TURNFACE.nested_types = {}
pb.SC_AI_TURNFACE.enum_types = {}
pb.SC_AI_TURNFACE.fields = {pb.SC_AI_TURNFACE_ENTITYUID_FIELD, pb.SC_AI_TURNFACE_ORIENTATION_FIELD}
pb.SC_AI_TURNFACE.is_extendable = false
pb.SC_AI_TURNFACE.extensions = {}
pb.CS_AI_DAMAGE_SKILLID_FIELD.name = "SkillId"
pb.CS_AI_DAMAGE_SKILLID_FIELD.full_name = ".CS_AI_DAMAGE.SkillId"
pb.CS_AI_DAMAGE_SKILLID_FIELD.number = 1
pb.CS_AI_DAMAGE_SKILLID_FIELD.index = 0
pb.CS_AI_DAMAGE_SKILLID_FIELD.label = 1
pb.CS_AI_DAMAGE_SKILLID_FIELD.has_default_value = false
pb.CS_AI_DAMAGE_SKILLID_FIELD.default_value = 0
pb.CS_AI_DAMAGE_SKILLID_FIELD.type = 5
pb.CS_AI_DAMAGE_SKILLID_FIELD.cpp_type = 1

pb.CS_AI_DAMAGE_SKILLLEVEL_FIELD.name = "SkillLevel"
pb.CS_AI_DAMAGE_SKILLLEVEL_FIELD.full_name = ".CS_AI_DAMAGE.SkillLevel"
pb.CS_AI_DAMAGE_SKILLLEVEL_FIELD.number = 2
pb.CS_AI_DAMAGE_SKILLLEVEL_FIELD.index = 1
pb.CS_AI_DAMAGE_SKILLLEVEL_FIELD.label = 1
pb.CS_AI_DAMAGE_SKILLLEVEL_FIELD.has_default_value = false
pb.CS_AI_DAMAGE_SKILLLEVEL_FIELD.default_value = 0
pb.CS_AI_DAMAGE_SKILLLEVEL_FIELD.type = 5
pb.CS_AI_DAMAGE_SKILLLEVEL_FIELD.cpp_type = 1

pb.CS_AI_DAMAGE_TARGETUID_FIELD.name = "TargetUID"
pb.CS_AI_DAMAGE_TARGETUID_FIELD.full_name = ".CS_AI_DAMAGE.TargetUID"
pb.CS_AI_DAMAGE_TARGETUID_FIELD.number = 4
pb.CS_AI_DAMAGE_TARGETUID_FIELD.index = 2
pb.CS_AI_DAMAGE_TARGETUID_FIELD.label = 1
pb.CS_AI_DAMAGE_TARGETUID_FIELD.has_default_value = false
pb.CS_AI_DAMAGE_TARGETUID_FIELD.default_value = 0
pb.CS_AI_DAMAGE_TARGETUID_FIELD.type = 4
pb.CS_AI_DAMAGE_TARGETUID_FIELD.cpp_type = 4

pb.CS_AI_DAMAGE.name = "CS_AI_DAMAGE"
pb.CS_AI_DAMAGE.full_name = ".CS_AI_DAMAGE"
pb.CS_AI_DAMAGE.nested_types = {}
pb.CS_AI_DAMAGE.enum_types = {}
pb.CS_AI_DAMAGE.fields = {pb.CS_AI_DAMAGE_SKILLID_FIELD, pb.CS_AI_DAMAGE_SKILLLEVEL_FIELD, pb.CS_AI_DAMAGE_TARGETUID_FIELD}
pb.CS_AI_DAMAGE.is_extendable = false
pb.CS_AI_DAMAGE.extensions = {}
pb.SC_AI_DAMAGE_VICTIM_UID_FIELD.name = "UID"
pb.SC_AI_DAMAGE_VICTIM_UID_FIELD.full_name = ".SC_AI_DAMAGE.Victim.UID"
pb.SC_AI_DAMAGE_VICTIM_UID_FIELD.number = 2
pb.SC_AI_DAMAGE_VICTIM_UID_FIELD.index = 0
pb.SC_AI_DAMAGE_VICTIM_UID_FIELD.label = 1
pb.SC_AI_DAMAGE_VICTIM_UID_FIELD.has_default_value = false
pb.SC_AI_DAMAGE_VICTIM_UID_FIELD.default_value = 0
pb.SC_AI_DAMAGE_VICTIM_UID_FIELD.type = 4
pb.SC_AI_DAMAGE_VICTIM_UID_FIELD.cpp_type = 4

pb.SC_AI_DAMAGE_VICTIM_BLOOD_FIELD.name = "Blood"
pb.SC_AI_DAMAGE_VICTIM_BLOOD_FIELD.full_name = ".SC_AI_DAMAGE.Victim.Blood"
pb.SC_AI_DAMAGE_VICTIM_BLOOD_FIELD.number = 3
pb.SC_AI_DAMAGE_VICTIM_BLOOD_FIELD.index = 1
pb.SC_AI_DAMAGE_VICTIM_BLOOD_FIELD.label = 1
pb.SC_AI_DAMAGE_VICTIM_BLOOD_FIELD.has_default_value = false
pb.SC_AI_DAMAGE_VICTIM_BLOOD_FIELD.default_value = 0
pb.SC_AI_DAMAGE_VICTIM_BLOOD_FIELD.type = 17
pb.SC_AI_DAMAGE_VICTIM_BLOOD_FIELD.cpp_type = 1

pb.SC_AI_DAMAGE_VICTIM_FLAG_FIELD.name = "Flag"
pb.SC_AI_DAMAGE_VICTIM_FLAG_FIELD.full_name = ".SC_AI_DAMAGE.Victim.Flag"
pb.SC_AI_DAMAGE_VICTIM_FLAG_FIELD.number = 6
pb.SC_AI_DAMAGE_VICTIM_FLAG_FIELD.index = 2
pb.SC_AI_DAMAGE_VICTIM_FLAG_FIELD.label = 1
pb.SC_AI_DAMAGE_VICTIM_FLAG_FIELD.has_default_value = false
pb.SC_AI_DAMAGE_VICTIM_FLAG_FIELD.default_value = 0
pb.SC_AI_DAMAGE_VICTIM_FLAG_FIELD.type = 5
pb.SC_AI_DAMAGE_VICTIM_FLAG_FIELD.cpp_type = 1

pb.SC_AI_DAMAGE_VICTIM.name = "Victim"
pb.SC_AI_DAMAGE_VICTIM.full_name = ".SC_AI_DAMAGE.Victim"
pb.SC_AI_DAMAGE_VICTIM.nested_types = {}
pb.SC_AI_DAMAGE_VICTIM.enum_types = {}
pb.SC_AI_DAMAGE_VICTIM.fields = {pb.SC_AI_DAMAGE_VICTIM_UID_FIELD, pb.SC_AI_DAMAGE_VICTIM_BLOOD_FIELD, pb.SC_AI_DAMAGE_VICTIM_FLAG_FIELD}
pb.SC_AI_DAMAGE_VICTIM.is_extendable = false
pb.SC_AI_DAMAGE_VICTIM.extensions = {}
pb.SC_AI_DAMAGE_VICTIM.containing_type = pb.SC_AI_DAMAGE
pb.SC_AI_DAMAGE_ENTITYUID_FIELD.name = "EntityUID"
pb.SC_AI_DAMAGE_ENTITYUID_FIELD.full_name = ".SC_AI_DAMAGE.EntityUID"
pb.SC_AI_DAMAGE_ENTITYUID_FIELD.number = 1
pb.SC_AI_DAMAGE_ENTITYUID_FIELD.index = 0
pb.SC_AI_DAMAGE_ENTITYUID_FIELD.label = 1
pb.SC_AI_DAMAGE_ENTITYUID_FIELD.has_default_value = false
pb.SC_AI_DAMAGE_ENTITYUID_FIELD.default_value = 0
pb.SC_AI_DAMAGE_ENTITYUID_FIELD.type = 4
pb.SC_AI_DAMAGE_ENTITYUID_FIELD.cpp_type = 4

pb.SC_AI_DAMAGE_SKILLID_FIELD.name = "SkillId"
pb.SC_AI_DAMAGE_SKILLID_FIELD.full_name = ".SC_AI_DAMAGE.SkillId"
pb.SC_AI_DAMAGE_SKILLID_FIELD.number = 2
pb.SC_AI_DAMAGE_SKILLID_FIELD.index = 1
pb.SC_AI_DAMAGE_SKILLID_FIELD.label = 1
pb.SC_AI_DAMAGE_SKILLID_FIELD.has_default_value = false
pb.SC_AI_DAMAGE_SKILLID_FIELD.default_value = 0
pb.SC_AI_DAMAGE_SKILLID_FIELD.type = 5
pb.SC_AI_DAMAGE_SKILLID_FIELD.cpp_type = 1

pb.SC_AI_DAMAGE_SERIALNUMBER_FIELD.name = "SerialNumber"
pb.SC_AI_DAMAGE_SERIALNUMBER_FIELD.full_name = ".SC_AI_DAMAGE.SerialNumber"
pb.SC_AI_DAMAGE_SERIALNUMBER_FIELD.number = 3
pb.SC_AI_DAMAGE_SERIALNUMBER_FIELD.index = 2
pb.SC_AI_DAMAGE_SERIALNUMBER_FIELD.label = 1
pb.SC_AI_DAMAGE_SERIALNUMBER_FIELD.has_default_value = false
pb.SC_AI_DAMAGE_SERIALNUMBER_FIELD.default_value = 0
pb.SC_AI_DAMAGE_SERIALNUMBER_FIELD.type = 5
pb.SC_AI_DAMAGE_SERIALNUMBER_FIELD.cpp_type = 1

pb.SC_AI_DAMAGE_SKILLINDEX_FIELD.name = "SkillIndex"
pb.SC_AI_DAMAGE_SKILLINDEX_FIELD.full_name = ".SC_AI_DAMAGE.SkillIndex"
pb.SC_AI_DAMAGE_SKILLINDEX_FIELD.number = 4
pb.SC_AI_DAMAGE_SKILLINDEX_FIELD.index = 3
pb.SC_AI_DAMAGE_SKILLINDEX_FIELD.label = 1
pb.SC_AI_DAMAGE_SKILLINDEX_FIELD.has_default_value = false
pb.SC_AI_DAMAGE_SKILLINDEX_FIELD.default_value = 0
pb.SC_AI_DAMAGE_SKILLINDEX_FIELD.type = 5
pb.SC_AI_DAMAGE_SKILLINDEX_FIELD.cpp_type = 1

pb.SC_AI_DAMAGE_SKILLTIME_FIELD.name = "SkillTime"
pb.SC_AI_DAMAGE_SKILLTIME_FIELD.full_name = ".SC_AI_DAMAGE.SkillTime"
pb.SC_AI_DAMAGE_SKILLTIME_FIELD.number = 5
pb.SC_AI_DAMAGE_SKILLTIME_FIELD.index = 4
pb.SC_AI_DAMAGE_SKILLTIME_FIELD.label = 1
pb.SC_AI_DAMAGE_SKILLTIME_FIELD.has_default_value = false
pb.SC_AI_DAMAGE_SKILLTIME_FIELD.default_value = 0.0
pb.SC_AI_DAMAGE_SKILLTIME_FIELD.type = 2
pb.SC_AI_DAMAGE_SKILLTIME_FIELD.cpp_type = 6

pb.SC_AI_DAMAGE_VICTIMS_FIELD.name = "Victims"
pb.SC_AI_DAMAGE_VICTIMS_FIELD.full_name = ".SC_AI_DAMAGE.Victims"
pb.SC_AI_DAMAGE_VICTIMS_FIELD.number = 6
pb.SC_AI_DAMAGE_VICTIMS_FIELD.index = 5
pb.SC_AI_DAMAGE_VICTIMS_FIELD.label = 3
pb.SC_AI_DAMAGE_VICTIMS_FIELD.has_default_value = false
pb.SC_AI_DAMAGE_VICTIMS_FIELD.default_value = {}
pb.SC_AI_DAMAGE_VICTIMS_FIELD.message_type = pb.SC_AI_DAMAGE_VICTIM
pb.SC_AI_DAMAGE_VICTIMS_FIELD.type = 11
pb.SC_AI_DAMAGE_VICTIMS_FIELD.cpp_type = 10

pb.SC_AI_DAMAGE.name = "SC_AI_DAMAGE"
pb.SC_AI_DAMAGE.full_name = ".SC_AI_DAMAGE"
pb.SC_AI_DAMAGE.nested_types = {pb.SC_AI_DAMAGE_VICTIM}
pb.SC_AI_DAMAGE.enum_types = {}
pb.SC_AI_DAMAGE.fields = {pb.SC_AI_DAMAGE_ENTITYUID_FIELD, pb.SC_AI_DAMAGE_SKILLID_FIELD, pb.SC_AI_DAMAGE_SERIALNUMBER_FIELD, pb.SC_AI_DAMAGE_SKILLINDEX_FIELD, pb.SC_AI_DAMAGE_SKILLTIME_FIELD, pb.SC_AI_DAMAGE_VICTIMS_FIELD}
pb.SC_AI_DAMAGE.is_extendable = false
pb.SC_AI_DAMAGE.extensions = {}
pb.CS_AI_POSITION_DESTX_FIELD.name = "DestX"
pb.CS_AI_POSITION_DESTX_FIELD.full_name = ".CS_AI_POSITION.DestX"
pb.CS_AI_POSITION_DESTX_FIELD.number = 1
pb.CS_AI_POSITION_DESTX_FIELD.index = 0
pb.CS_AI_POSITION_DESTX_FIELD.label = 1
pb.CS_AI_POSITION_DESTX_FIELD.has_default_value = false
pb.CS_AI_POSITION_DESTX_FIELD.default_value = 0.0
pb.CS_AI_POSITION_DESTX_FIELD.type = 2
pb.CS_AI_POSITION_DESTX_FIELD.cpp_type = 6

pb.CS_AI_POSITION_DESTY_FIELD.name = "DestY"
pb.CS_AI_POSITION_DESTY_FIELD.full_name = ".CS_AI_POSITION.DestY"
pb.CS_AI_POSITION_DESTY_FIELD.number = 2
pb.CS_AI_POSITION_DESTY_FIELD.index = 1
pb.CS_AI_POSITION_DESTY_FIELD.label = 1
pb.CS_AI_POSITION_DESTY_FIELD.has_default_value = false
pb.CS_AI_POSITION_DESTY_FIELD.default_value = 0.0
pb.CS_AI_POSITION_DESTY_FIELD.type = 2
pb.CS_AI_POSITION_DESTY_FIELD.cpp_type = 6

pb.CS_AI_POSITION_DESTZ_FIELD.name = "DestZ"
pb.CS_AI_POSITION_DESTZ_FIELD.full_name = ".CS_AI_POSITION.DestZ"
pb.CS_AI_POSITION_DESTZ_FIELD.number = 3
pb.CS_AI_POSITION_DESTZ_FIELD.index = 2
pb.CS_AI_POSITION_DESTZ_FIELD.label = 1
pb.CS_AI_POSITION_DESTZ_FIELD.has_default_value = false
pb.CS_AI_POSITION_DESTZ_FIELD.default_value = 0.0
pb.CS_AI_POSITION_DESTZ_FIELD.type = 2
pb.CS_AI_POSITION_DESTZ_FIELD.cpp_type = 6

pb.CS_AI_POSITION_SCENEID_FIELD.name = "SceneID"
pb.CS_AI_POSITION_SCENEID_FIELD.full_name = ".CS_AI_POSITION.SceneID"
pb.CS_AI_POSITION_SCENEID_FIELD.number = 4
pb.CS_AI_POSITION_SCENEID_FIELD.index = 3
pb.CS_AI_POSITION_SCENEID_FIELD.label = 1
pb.CS_AI_POSITION_SCENEID_FIELD.has_default_value = false
pb.CS_AI_POSITION_SCENEID_FIELD.default_value = 0
pb.CS_AI_POSITION_SCENEID_FIELD.type = 5
pb.CS_AI_POSITION_SCENEID_FIELD.cpp_type = 1

pb.CS_AI_POSITION.name = "CS_AI_POSITION"
pb.CS_AI_POSITION.full_name = ".CS_AI_POSITION"
pb.CS_AI_POSITION.nested_types = {}
pb.CS_AI_POSITION.enum_types = {}
pb.CS_AI_POSITION.fields = {pb.CS_AI_POSITION_DESTX_FIELD, pb.CS_AI_POSITION_DESTY_FIELD, pb.CS_AI_POSITION_DESTZ_FIELD, pb.CS_AI_POSITION_SCENEID_FIELD}
pb.CS_AI_POSITION.is_extendable = false
pb.CS_AI_POSITION.extensions = {}
pb.SC_AI_POSITION_ENTITYUID_FIELD.name = "EntityUID"
pb.SC_AI_POSITION_ENTITYUID_FIELD.full_name = ".SC_AI_POSITION.EntityUID"
pb.SC_AI_POSITION_ENTITYUID_FIELD.number = 1
pb.SC_AI_POSITION_ENTITYUID_FIELD.index = 0
pb.SC_AI_POSITION_ENTITYUID_FIELD.label = 1
pb.SC_AI_POSITION_ENTITYUID_FIELD.has_default_value = false
pb.SC_AI_POSITION_ENTITYUID_FIELD.default_value = 0
pb.SC_AI_POSITION_ENTITYUID_FIELD.type = 4
pb.SC_AI_POSITION_ENTITYUID_FIELD.cpp_type = 4

pb.SC_AI_POSITION_DESTX_FIELD.name = "DestX"
pb.SC_AI_POSITION_DESTX_FIELD.full_name = ".SC_AI_POSITION.DestX"
pb.SC_AI_POSITION_DESTX_FIELD.number = 2
pb.SC_AI_POSITION_DESTX_FIELD.index = 1
pb.SC_AI_POSITION_DESTX_FIELD.label = 1
pb.SC_AI_POSITION_DESTX_FIELD.has_default_value = false
pb.SC_AI_POSITION_DESTX_FIELD.default_value = 0.0
pb.SC_AI_POSITION_DESTX_FIELD.type = 2
pb.SC_AI_POSITION_DESTX_FIELD.cpp_type = 6

pb.SC_AI_POSITION_DESTY_FIELD.name = "DestY"
pb.SC_AI_POSITION_DESTY_FIELD.full_name = ".SC_AI_POSITION.DestY"
pb.SC_AI_POSITION_DESTY_FIELD.number = 3
pb.SC_AI_POSITION_DESTY_FIELD.index = 2
pb.SC_AI_POSITION_DESTY_FIELD.label = 1
pb.SC_AI_POSITION_DESTY_FIELD.has_default_value = false
pb.SC_AI_POSITION_DESTY_FIELD.default_value = 0.0
pb.SC_AI_POSITION_DESTY_FIELD.type = 2
pb.SC_AI_POSITION_DESTY_FIELD.cpp_type = 6

pb.SC_AI_POSITION_DESTZ_FIELD.name = "DestZ"
pb.SC_AI_POSITION_DESTZ_FIELD.full_name = ".SC_AI_POSITION.DestZ"
pb.SC_AI_POSITION_DESTZ_FIELD.number = 4
pb.SC_AI_POSITION_DESTZ_FIELD.index = 3
pb.SC_AI_POSITION_DESTZ_FIELD.label = 1
pb.SC_AI_POSITION_DESTZ_FIELD.has_default_value = false
pb.SC_AI_POSITION_DESTZ_FIELD.default_value = 0.0
pb.SC_AI_POSITION_DESTZ_FIELD.type = 2
pb.SC_AI_POSITION_DESTZ_FIELD.cpp_type = 6

pb.SC_AI_POSITION_ISSTAND_FIELD.name = "IsStand"
pb.SC_AI_POSITION_ISSTAND_FIELD.full_name = ".SC_AI_POSITION.IsStand"
pb.SC_AI_POSITION_ISSTAND_FIELD.number = 5
pb.SC_AI_POSITION_ISSTAND_FIELD.index = 4
pb.SC_AI_POSITION_ISSTAND_FIELD.label = 1
pb.SC_AI_POSITION_ISSTAND_FIELD.has_default_value = false
pb.SC_AI_POSITION_ISSTAND_FIELD.default_value = 0
pb.SC_AI_POSITION_ISSTAND_FIELD.type = 5
pb.SC_AI_POSITION_ISSTAND_FIELD.cpp_type = 1

pb.SC_AI_POSITION.name = "SC_AI_POSITION"
pb.SC_AI_POSITION.full_name = ".SC_AI_POSITION"
pb.SC_AI_POSITION.nested_types = {}
pb.SC_AI_POSITION.enum_types = {}
pb.SC_AI_POSITION.fields = {pb.SC_AI_POSITION_ENTITYUID_FIELD, pb.SC_AI_POSITION_DESTX_FIELD, pb.SC_AI_POSITION_DESTY_FIELD, pb.SC_AI_POSITION_DESTZ_FIELD, pb.SC_AI_POSITION_ISSTAND_FIELD}
pb.SC_AI_POSITION.is_extendable = false
pb.SC_AI_POSITION.extensions = {}
pb.SC_AI_GETNEARENEMY_DESTX_FIELD.name = "DestX"
pb.SC_AI_GETNEARENEMY_DESTX_FIELD.full_name = ".SC_AI_GETNEARENEMY.DestX"
pb.SC_AI_GETNEARENEMY_DESTX_FIELD.number = 1
pb.SC_AI_GETNEARENEMY_DESTX_FIELD.index = 0
pb.SC_AI_GETNEARENEMY_DESTX_FIELD.label = 1
pb.SC_AI_GETNEARENEMY_DESTX_FIELD.has_default_value = false
pb.SC_AI_GETNEARENEMY_DESTX_FIELD.default_value = 0.0
pb.SC_AI_GETNEARENEMY_DESTX_FIELD.type = 2
pb.SC_AI_GETNEARENEMY_DESTX_FIELD.cpp_type = 6

pb.SC_AI_GETNEARENEMY_DESTY_FIELD.name = "DestY"
pb.SC_AI_GETNEARENEMY_DESTY_FIELD.full_name = ".SC_AI_GETNEARENEMY.DestY"
pb.SC_AI_GETNEARENEMY_DESTY_FIELD.number = 2
pb.SC_AI_GETNEARENEMY_DESTY_FIELD.index = 1
pb.SC_AI_GETNEARENEMY_DESTY_FIELD.label = 1
pb.SC_AI_GETNEARENEMY_DESTY_FIELD.has_default_value = false
pb.SC_AI_GETNEARENEMY_DESTY_FIELD.default_value = 0.0
pb.SC_AI_GETNEARENEMY_DESTY_FIELD.type = 2
pb.SC_AI_GETNEARENEMY_DESTY_FIELD.cpp_type = 6

pb.SC_AI_GETNEARENEMY_DESTZ_FIELD.name = "DestZ"
pb.SC_AI_GETNEARENEMY_DESTZ_FIELD.full_name = ".SC_AI_GETNEARENEMY.DestZ"
pb.SC_AI_GETNEARENEMY_DESTZ_FIELD.number = 3
pb.SC_AI_GETNEARENEMY_DESTZ_FIELD.index = 2
pb.SC_AI_GETNEARENEMY_DESTZ_FIELD.label = 1
pb.SC_AI_GETNEARENEMY_DESTZ_FIELD.has_default_value = false
pb.SC_AI_GETNEARENEMY_DESTZ_FIELD.default_value = 0.0
pb.SC_AI_GETNEARENEMY_DESTZ_FIELD.type = 2
pb.SC_AI_GETNEARENEMY_DESTZ_FIELD.cpp_type = 6

pb.SC_AI_GETNEARENEMY.name = "SC_AI_GETNEARENEMY"
pb.SC_AI_GETNEARENEMY.full_name = ".SC_AI_GETNEARENEMY"
pb.SC_AI_GETNEARENEMY.nested_types = {}
pb.SC_AI_GETNEARENEMY.enum_types = {}
pb.SC_AI_GETNEARENEMY.fields = {pb.SC_AI_GETNEARENEMY_DESTX_FIELD, pb.SC_AI_GETNEARENEMY_DESTY_FIELD, pb.SC_AI_GETNEARENEMY_DESTZ_FIELD}
pb.SC_AI_GETNEARENEMY.is_extendable = false
pb.SC_AI_GETNEARENEMY.extensions = {}
pb.CS_AI_ATTACK_POS_ID_FIELD.name = "Id"
pb.CS_AI_ATTACK_POS_ID_FIELD.full_name = ".CS_AI_ATTACK_POS.Id"
pb.CS_AI_ATTACK_POS_ID_FIELD.number = 1
pb.CS_AI_ATTACK_POS_ID_FIELD.index = 0
pb.CS_AI_ATTACK_POS_ID_FIELD.label = 1
pb.CS_AI_ATTACK_POS_ID_FIELD.has_default_value = false
pb.CS_AI_ATTACK_POS_ID_FIELD.default_value = 0
pb.CS_AI_ATTACK_POS_ID_FIELD.type = 5
pb.CS_AI_ATTACK_POS_ID_FIELD.cpp_type = 1

pb.CS_AI_ATTACK_POS_DESTX_FIELD.name = "DestX"
pb.CS_AI_ATTACK_POS_DESTX_FIELD.full_name = ".CS_AI_ATTACK_POS.DestX"
pb.CS_AI_ATTACK_POS_DESTX_FIELD.number = 2
pb.CS_AI_ATTACK_POS_DESTX_FIELD.index = 1
pb.CS_AI_ATTACK_POS_DESTX_FIELD.label = 1
pb.CS_AI_ATTACK_POS_DESTX_FIELD.has_default_value = false
pb.CS_AI_ATTACK_POS_DESTX_FIELD.default_value = 0.0
pb.CS_AI_ATTACK_POS_DESTX_FIELD.type = 2
pb.CS_AI_ATTACK_POS_DESTX_FIELD.cpp_type = 6

pb.CS_AI_ATTACK_POS_DESTY_FIELD.name = "DestY"
pb.CS_AI_ATTACK_POS_DESTY_FIELD.full_name = ".CS_AI_ATTACK_POS.DestY"
pb.CS_AI_ATTACK_POS_DESTY_FIELD.number = 3
pb.CS_AI_ATTACK_POS_DESTY_FIELD.index = 2
pb.CS_AI_ATTACK_POS_DESTY_FIELD.label = 1
pb.CS_AI_ATTACK_POS_DESTY_FIELD.has_default_value = false
pb.CS_AI_ATTACK_POS_DESTY_FIELD.default_value = 0.0
pb.CS_AI_ATTACK_POS_DESTY_FIELD.type = 2
pb.CS_AI_ATTACK_POS_DESTY_FIELD.cpp_type = 6

pb.CS_AI_ATTACK_POS_DESTZ_FIELD.name = "DestZ"
pb.CS_AI_ATTACK_POS_DESTZ_FIELD.full_name = ".CS_AI_ATTACK_POS.DestZ"
pb.CS_AI_ATTACK_POS_DESTZ_FIELD.number = 4
pb.CS_AI_ATTACK_POS_DESTZ_FIELD.index = 3
pb.CS_AI_ATTACK_POS_DESTZ_FIELD.label = 1
pb.CS_AI_ATTACK_POS_DESTZ_FIELD.has_default_value = false
pb.CS_AI_ATTACK_POS_DESTZ_FIELD.default_value = 0.0
pb.CS_AI_ATTACK_POS_DESTZ_FIELD.type = 2
pb.CS_AI_ATTACK_POS_DESTZ_FIELD.cpp_type = 6

pb.CS_AI_ATTACK_POS_ORIENTATION_FIELD.name = "Orientation"
pb.CS_AI_ATTACK_POS_ORIENTATION_FIELD.full_name = ".CS_AI_ATTACK_POS.Orientation"
pb.CS_AI_ATTACK_POS_ORIENTATION_FIELD.number = 5
pb.CS_AI_ATTACK_POS_ORIENTATION_FIELD.index = 4
pb.CS_AI_ATTACK_POS_ORIENTATION_FIELD.label = 1
pb.CS_AI_ATTACK_POS_ORIENTATION_FIELD.has_default_value = false
pb.CS_AI_ATTACK_POS_ORIENTATION_FIELD.default_value = 0.0
pb.CS_AI_ATTACK_POS_ORIENTATION_FIELD.type = 2
pb.CS_AI_ATTACK_POS_ORIENTATION_FIELD.cpp_type = 6

pb.CS_AI_ATTACK_POS.name = "CS_AI_ATTACK_POS"
pb.CS_AI_ATTACK_POS.full_name = ".CS_AI_ATTACK_POS"
pb.CS_AI_ATTACK_POS.nested_types = {}
pb.CS_AI_ATTACK_POS.enum_types = {}
pb.CS_AI_ATTACK_POS.fields = {pb.CS_AI_ATTACK_POS_ID_FIELD, pb.CS_AI_ATTACK_POS_DESTX_FIELD, pb.CS_AI_ATTACK_POS_DESTY_FIELD, pb.CS_AI_ATTACK_POS_DESTZ_FIELD, pb.CS_AI_ATTACK_POS_ORIENTATION_FIELD}
pb.CS_AI_ATTACK_POS.is_extendable = false
pb.CS_AI_ATTACK_POS.extensions = {}
pb.SC_AI_ATTACK_POS_ID_FIELD.name = "Id"
pb.SC_AI_ATTACK_POS_ID_FIELD.full_name = ".SC_AI_ATTACK_POS.Id"
pb.SC_AI_ATTACK_POS_ID_FIELD.number = 1
pb.SC_AI_ATTACK_POS_ID_FIELD.index = 0
pb.SC_AI_ATTACK_POS_ID_FIELD.label = 1
pb.SC_AI_ATTACK_POS_ID_FIELD.has_default_value = false
pb.SC_AI_ATTACK_POS_ID_FIELD.default_value = 0
pb.SC_AI_ATTACK_POS_ID_FIELD.type = 5
pb.SC_AI_ATTACK_POS_ID_FIELD.cpp_type = 1

pb.SC_AI_ATTACK_POS_LEVEL_FIELD.name = "Level"
pb.SC_AI_ATTACK_POS_LEVEL_FIELD.full_name = ".SC_AI_ATTACK_POS.Level"
pb.SC_AI_ATTACK_POS_LEVEL_FIELD.number = 2
pb.SC_AI_ATTACK_POS_LEVEL_FIELD.index = 1
pb.SC_AI_ATTACK_POS_LEVEL_FIELD.label = 1
pb.SC_AI_ATTACK_POS_LEVEL_FIELD.has_default_value = false
pb.SC_AI_ATTACK_POS_LEVEL_FIELD.default_value = 0
pb.SC_AI_ATTACK_POS_LEVEL_FIELD.type = 5
pb.SC_AI_ATTACK_POS_LEVEL_FIELD.cpp_type = 1

pb.SC_AI_ATTACK_POS_ENTITYUID_FIELD.name = "EntityUID"
pb.SC_AI_ATTACK_POS_ENTITYUID_FIELD.full_name = ".SC_AI_ATTACK_POS.EntityUID"
pb.SC_AI_ATTACK_POS_ENTITYUID_FIELD.number = 3
pb.SC_AI_ATTACK_POS_ENTITYUID_FIELD.index = 2
pb.SC_AI_ATTACK_POS_ENTITYUID_FIELD.label = 1
pb.SC_AI_ATTACK_POS_ENTITYUID_FIELD.has_default_value = false
pb.SC_AI_ATTACK_POS_ENTITYUID_FIELD.default_value = 0
pb.SC_AI_ATTACK_POS_ENTITYUID_FIELD.type = 4
pb.SC_AI_ATTACK_POS_ENTITYUID_FIELD.cpp_type = 4

pb.SC_AI_ATTACK_POS_ORIENTATION_FIELD.name = "Orientation"
pb.SC_AI_ATTACK_POS_ORIENTATION_FIELD.full_name = ".SC_AI_ATTACK_POS.Orientation"
pb.SC_AI_ATTACK_POS_ORIENTATION_FIELD.number = 4
pb.SC_AI_ATTACK_POS_ORIENTATION_FIELD.index = 3
pb.SC_AI_ATTACK_POS_ORIENTATION_FIELD.label = 1
pb.SC_AI_ATTACK_POS_ORIENTATION_FIELD.has_default_value = false
pb.SC_AI_ATTACK_POS_ORIENTATION_FIELD.default_value = 0.0
pb.SC_AI_ATTACK_POS_ORIENTATION_FIELD.type = 2
pb.SC_AI_ATTACK_POS_ORIENTATION_FIELD.cpp_type = 6

pb.SC_AI_ATTACK_POS_SRCX_FIELD.name = "SrcX"
pb.SC_AI_ATTACK_POS_SRCX_FIELD.full_name = ".SC_AI_ATTACK_POS.SrcX"
pb.SC_AI_ATTACK_POS_SRCX_FIELD.number = 5
pb.SC_AI_ATTACK_POS_SRCX_FIELD.index = 4
pb.SC_AI_ATTACK_POS_SRCX_FIELD.label = 1
pb.SC_AI_ATTACK_POS_SRCX_FIELD.has_default_value = false
pb.SC_AI_ATTACK_POS_SRCX_FIELD.default_value = 0.0
pb.SC_AI_ATTACK_POS_SRCX_FIELD.type = 2
pb.SC_AI_ATTACK_POS_SRCX_FIELD.cpp_type = 6

pb.SC_AI_ATTACK_POS_SRCZ_FIELD.name = "SrcZ"
pb.SC_AI_ATTACK_POS_SRCZ_FIELD.full_name = ".SC_AI_ATTACK_POS.SrcZ"
pb.SC_AI_ATTACK_POS_SRCZ_FIELD.number = 6
pb.SC_AI_ATTACK_POS_SRCZ_FIELD.index = 5
pb.SC_AI_ATTACK_POS_SRCZ_FIELD.label = 1
pb.SC_AI_ATTACK_POS_SRCZ_FIELD.has_default_value = false
pb.SC_AI_ATTACK_POS_SRCZ_FIELD.default_value = 0.0
pb.SC_AI_ATTACK_POS_SRCZ_FIELD.type = 2
pb.SC_AI_ATTACK_POS_SRCZ_FIELD.cpp_type = 6

pb.SC_AI_ATTACK_POS_DESTX_FIELD.name = "DestX"
pb.SC_AI_ATTACK_POS_DESTX_FIELD.full_name = ".SC_AI_ATTACK_POS.DestX"
pb.SC_AI_ATTACK_POS_DESTX_FIELD.number = 7
pb.SC_AI_ATTACK_POS_DESTX_FIELD.index = 6
pb.SC_AI_ATTACK_POS_DESTX_FIELD.label = 1
pb.SC_AI_ATTACK_POS_DESTX_FIELD.has_default_value = false
pb.SC_AI_ATTACK_POS_DESTX_FIELD.default_value = 0.0
pb.SC_AI_ATTACK_POS_DESTX_FIELD.type = 2
pb.SC_AI_ATTACK_POS_DESTX_FIELD.cpp_type = 6

pb.SC_AI_ATTACK_POS_DESTY_FIELD.name = "DestY"
pb.SC_AI_ATTACK_POS_DESTY_FIELD.full_name = ".SC_AI_ATTACK_POS.DestY"
pb.SC_AI_ATTACK_POS_DESTY_FIELD.number = 8
pb.SC_AI_ATTACK_POS_DESTY_FIELD.index = 7
pb.SC_AI_ATTACK_POS_DESTY_FIELD.label = 1
pb.SC_AI_ATTACK_POS_DESTY_FIELD.has_default_value = false
pb.SC_AI_ATTACK_POS_DESTY_FIELD.default_value = 0.0
pb.SC_AI_ATTACK_POS_DESTY_FIELD.type = 2
pb.SC_AI_ATTACK_POS_DESTY_FIELD.cpp_type = 6

pb.SC_AI_ATTACK_POS_DESTZ_FIELD.name = "DestZ"
pb.SC_AI_ATTACK_POS_DESTZ_FIELD.full_name = ".SC_AI_ATTACK_POS.DestZ"
pb.SC_AI_ATTACK_POS_DESTZ_FIELD.number = 9
pb.SC_AI_ATTACK_POS_DESTZ_FIELD.index = 8
pb.SC_AI_ATTACK_POS_DESTZ_FIELD.label = 1
pb.SC_AI_ATTACK_POS_DESTZ_FIELD.has_default_value = false
pb.SC_AI_ATTACK_POS_DESTZ_FIELD.default_value = 0.0
pb.SC_AI_ATTACK_POS_DESTZ_FIELD.type = 2
pb.SC_AI_ATTACK_POS_DESTZ_FIELD.cpp_type = 6

pb.SC_AI_ATTACK_POS_ISPREPARE_FIELD.name = "IsPrepare"
pb.SC_AI_ATTACK_POS_ISPREPARE_FIELD.full_name = ".SC_AI_ATTACK_POS.IsPrepare"
pb.SC_AI_ATTACK_POS_ISPREPARE_FIELD.number = 10
pb.SC_AI_ATTACK_POS_ISPREPARE_FIELD.index = 9
pb.SC_AI_ATTACK_POS_ISPREPARE_FIELD.label = 1
pb.SC_AI_ATTACK_POS_ISPREPARE_FIELD.has_default_value = false
pb.SC_AI_ATTACK_POS_ISPREPARE_FIELD.default_value = 0
pb.SC_AI_ATTACK_POS_ISPREPARE_FIELD.type = 5
pb.SC_AI_ATTACK_POS_ISPREPARE_FIELD.cpp_type = 1

pb.SC_AI_ATTACK_POS_SERIALNUMBER_FIELD.name = "SerialNumber"
pb.SC_AI_ATTACK_POS_SERIALNUMBER_FIELD.full_name = ".SC_AI_ATTACK_POS.SerialNumber"
pb.SC_AI_ATTACK_POS_SERIALNUMBER_FIELD.number = 11
pb.SC_AI_ATTACK_POS_SERIALNUMBER_FIELD.index = 10
pb.SC_AI_ATTACK_POS_SERIALNUMBER_FIELD.label = 1
pb.SC_AI_ATTACK_POS_SERIALNUMBER_FIELD.has_default_value = false
pb.SC_AI_ATTACK_POS_SERIALNUMBER_FIELD.default_value = 0
pb.SC_AI_ATTACK_POS_SERIALNUMBER_FIELD.type = 5
pb.SC_AI_ATTACK_POS_SERIALNUMBER_FIELD.cpp_type = 1

pb.SC_AI_ATTACK_POS.name = "SC_AI_ATTACK_POS"
pb.SC_AI_ATTACK_POS.full_name = ".SC_AI_ATTACK_POS"
pb.SC_AI_ATTACK_POS.nested_types = {}
pb.SC_AI_ATTACK_POS.enum_types = {}
pb.SC_AI_ATTACK_POS.fields = {pb.SC_AI_ATTACK_POS_ID_FIELD, pb.SC_AI_ATTACK_POS_LEVEL_FIELD, pb.SC_AI_ATTACK_POS_ENTITYUID_FIELD, pb.SC_AI_ATTACK_POS_ORIENTATION_FIELD, pb.SC_AI_ATTACK_POS_SRCX_FIELD, pb.SC_AI_ATTACK_POS_SRCZ_FIELD, pb.SC_AI_ATTACK_POS_DESTX_FIELD, pb.SC_AI_ATTACK_POS_DESTY_FIELD, pb.SC_AI_ATTACK_POS_DESTZ_FIELD, pb.SC_AI_ATTACK_POS_ISPREPARE_FIELD, pb.SC_AI_ATTACK_POS_SERIALNUMBER_FIELD}
pb.SC_AI_ATTACK_POS.is_extendable = false
pb.SC_AI_ATTACK_POS.extensions = {}
pb.CS_AI_DASH.name = "CS_AI_DASH"
pb.CS_AI_DASH.full_name = ".CS_AI_DASH"
pb.CS_AI_DASH.nested_types = {}
pb.CS_AI_DASH.enum_types = {}
pb.CS_AI_DASH.fields = {}
pb.CS_AI_DASH.is_extendable = false
pb.CS_AI_DASH.extensions = {}
pb.SC_AI_DASH_ENTITYUID_FIELD.name = "EntityUID"
pb.SC_AI_DASH_ENTITYUID_FIELD.full_name = ".SC_AI_DASH.EntityUID"
pb.SC_AI_DASH_ENTITYUID_FIELD.number = 1
pb.SC_AI_DASH_ENTITYUID_FIELD.index = 0
pb.SC_AI_DASH_ENTITYUID_FIELD.label = 1
pb.SC_AI_DASH_ENTITYUID_FIELD.has_default_value = false
pb.SC_AI_DASH_ENTITYUID_FIELD.default_value = 0
pb.SC_AI_DASH_ENTITYUID_FIELD.type = 4
pb.SC_AI_DASH_ENTITYUID_FIELD.cpp_type = 4

pb.SC_AI_DASH_DESTX_FIELD.name = "DestX"
pb.SC_AI_DASH_DESTX_FIELD.full_name = ".SC_AI_DASH.DestX"
pb.SC_AI_DASH_DESTX_FIELD.number = 2
pb.SC_AI_DASH_DESTX_FIELD.index = 1
pb.SC_AI_DASH_DESTX_FIELD.label = 1
pb.SC_AI_DASH_DESTX_FIELD.has_default_value = false
pb.SC_AI_DASH_DESTX_FIELD.default_value = 0.0
pb.SC_AI_DASH_DESTX_FIELD.type = 2
pb.SC_AI_DASH_DESTX_FIELD.cpp_type = 6

pb.SC_AI_DASH_DESTY_FIELD.name = "DestY"
pb.SC_AI_DASH_DESTY_FIELD.full_name = ".SC_AI_DASH.DestY"
pb.SC_AI_DASH_DESTY_FIELD.number = 3
pb.SC_AI_DASH_DESTY_FIELD.index = 2
pb.SC_AI_DASH_DESTY_FIELD.label = 1
pb.SC_AI_DASH_DESTY_FIELD.has_default_value = false
pb.SC_AI_DASH_DESTY_FIELD.default_value = 0.0
pb.SC_AI_DASH_DESTY_FIELD.type = 2
pb.SC_AI_DASH_DESTY_FIELD.cpp_type = 6

pb.SC_AI_DASH_DESTZ_FIELD.name = "DestZ"
pb.SC_AI_DASH_DESTZ_FIELD.full_name = ".SC_AI_DASH.DestZ"
pb.SC_AI_DASH_DESTZ_FIELD.number = 4
pb.SC_AI_DASH_DESTZ_FIELD.index = 3
pb.SC_AI_DASH_DESTZ_FIELD.label = 1
pb.SC_AI_DASH_DESTZ_FIELD.has_default_value = false
pb.SC_AI_DASH_DESTZ_FIELD.default_value = 0.0
pb.SC_AI_DASH_DESTZ_FIELD.type = 2
pb.SC_AI_DASH_DESTZ_FIELD.cpp_type = 6

pb.SC_AI_DASH_SPEED_FIELD.name = "Speed"
pb.SC_AI_DASH_SPEED_FIELD.full_name = ".SC_AI_DASH.Speed"
pb.SC_AI_DASH_SPEED_FIELD.number = 5
pb.SC_AI_DASH_SPEED_FIELD.index = 4
pb.SC_AI_DASH_SPEED_FIELD.label = 1
pb.SC_AI_DASH_SPEED_FIELD.has_default_value = false
pb.SC_AI_DASH_SPEED_FIELD.default_value = 0.0
pb.SC_AI_DASH_SPEED_FIELD.type = 2
pb.SC_AI_DASH_SPEED_FIELD.cpp_type = 6

pb.SC_AI_DASH_SRCUID_FIELD.name = "SrcUID"
pb.SC_AI_DASH_SRCUID_FIELD.full_name = ".SC_AI_DASH.SrcUID"
pb.SC_AI_DASH_SRCUID_FIELD.number = 6
pb.SC_AI_DASH_SRCUID_FIELD.index = 5
pb.SC_AI_DASH_SRCUID_FIELD.label = 1
pb.SC_AI_DASH_SRCUID_FIELD.has_default_value = false
pb.SC_AI_DASH_SRCUID_FIELD.default_value = 0
pb.SC_AI_DASH_SRCUID_FIELD.type = 4
pb.SC_AI_DASH_SRCUID_FIELD.cpp_type = 4

pb.SC_AI_DASH_SKILLID_FIELD.name = "SkillID"
pb.SC_AI_DASH_SKILLID_FIELD.full_name = ".SC_AI_DASH.SkillID"
pb.SC_AI_DASH_SKILLID_FIELD.number = 7
pb.SC_AI_DASH_SKILLID_FIELD.index = 6
pb.SC_AI_DASH_SKILLID_FIELD.label = 1
pb.SC_AI_DASH_SKILLID_FIELD.has_default_value = false
pb.SC_AI_DASH_SKILLID_FIELD.default_value = 0
pb.SC_AI_DASH_SKILLID_FIELD.type = 5
pb.SC_AI_DASH_SKILLID_FIELD.cpp_type = 1

pb.SC_AI_DASH_SERIALNUMBER_FIELD.name = "SerialNumber"
pb.SC_AI_DASH_SERIALNUMBER_FIELD.full_name = ".SC_AI_DASH.SerialNumber"
pb.SC_AI_DASH_SERIALNUMBER_FIELD.number = 8
pb.SC_AI_DASH_SERIALNUMBER_FIELD.index = 7
pb.SC_AI_DASH_SERIALNUMBER_FIELD.label = 1
pb.SC_AI_DASH_SERIALNUMBER_FIELD.has_default_value = false
pb.SC_AI_DASH_SERIALNUMBER_FIELD.default_value = 0
pb.SC_AI_DASH_SERIALNUMBER_FIELD.type = 5
pb.SC_AI_DASH_SERIALNUMBER_FIELD.cpp_type = 1

pb.SC_AI_DASH_SKILLINDEX_FIELD.name = "SkillIndex"
pb.SC_AI_DASH_SKILLINDEX_FIELD.full_name = ".SC_AI_DASH.SkillIndex"
pb.SC_AI_DASH_SKILLINDEX_FIELD.number = 9
pb.SC_AI_DASH_SKILLINDEX_FIELD.index = 8
pb.SC_AI_DASH_SKILLINDEX_FIELD.label = 1
pb.SC_AI_DASH_SKILLINDEX_FIELD.has_default_value = false
pb.SC_AI_DASH_SKILLINDEX_FIELD.default_value = 0
pb.SC_AI_DASH_SKILLINDEX_FIELD.type = 5
pb.SC_AI_DASH_SKILLINDEX_FIELD.cpp_type = 1

pb.SC_AI_DASH_SKILLTIME_FIELD.name = "SkillTime"
pb.SC_AI_DASH_SKILLTIME_FIELD.full_name = ".SC_AI_DASH.SkillTime"
pb.SC_AI_DASH_SKILLTIME_FIELD.number = 10
pb.SC_AI_DASH_SKILLTIME_FIELD.index = 9
pb.SC_AI_DASH_SKILLTIME_FIELD.label = 1
pb.SC_AI_DASH_SKILLTIME_FIELD.has_default_value = false
pb.SC_AI_DASH_SKILLTIME_FIELD.default_value = 0.0
pb.SC_AI_DASH_SKILLTIME_FIELD.type = 2
pb.SC_AI_DASH_SKILLTIME_FIELD.cpp_type = 6

pb.SC_AI_DASH.name = "SC_AI_DASH"
pb.SC_AI_DASH.full_name = ".SC_AI_DASH"
pb.SC_AI_DASH.nested_types = {}
pb.SC_AI_DASH.enum_types = {}
pb.SC_AI_DASH.fields = {pb.SC_AI_DASH_ENTITYUID_FIELD, pb.SC_AI_DASH_DESTX_FIELD, pb.SC_AI_DASH_DESTY_FIELD, pb.SC_AI_DASH_DESTZ_FIELD, pb.SC_AI_DASH_SPEED_FIELD, pb.SC_AI_DASH_SRCUID_FIELD, pb.SC_AI_DASH_SKILLID_FIELD, pb.SC_AI_DASH_SERIALNUMBER_FIELD, pb.SC_AI_DASH_SKILLINDEX_FIELD, pb.SC_AI_DASH_SKILLTIME_FIELD}
pb.SC_AI_DASH.is_extendable = false
pb.SC_AI_DASH.extensions = {}
pb.CS_AI_TALK.name = "CS_AI_TALK"
pb.CS_AI_TALK.full_name = ".CS_AI_TALK"
pb.CS_AI_TALK.nested_types = {}
pb.CS_AI_TALK.enum_types = {}
pb.CS_AI_TALK.fields = {}
pb.CS_AI_TALK.is_extendable = false
pb.CS_AI_TALK.extensions = {}
pb.SC_AI_TALK_ENTITYUID_FIELD.name = "EntityUID"
pb.SC_AI_TALK_ENTITYUID_FIELD.full_name = ".SC_AI_TALK.EntityUID"
pb.SC_AI_TALK_ENTITYUID_FIELD.number = 1
pb.SC_AI_TALK_ENTITYUID_FIELD.index = 0
pb.SC_AI_TALK_ENTITYUID_FIELD.label = 1
pb.SC_AI_TALK_ENTITYUID_FIELD.has_default_value = false
pb.SC_AI_TALK_ENTITYUID_FIELD.default_value = 0
pb.SC_AI_TALK_ENTITYUID_FIELD.type = 4
pb.SC_AI_TALK_ENTITYUID_FIELD.cpp_type = 4

pb.SC_AI_TALK_TALKID_FIELD.name = "TalkID"
pb.SC_AI_TALK_TALKID_FIELD.full_name = ".SC_AI_TALK.TalkID"
pb.SC_AI_TALK_TALKID_FIELD.number = 2
pb.SC_AI_TALK_TALKID_FIELD.index = 1
pb.SC_AI_TALK_TALKID_FIELD.label = 1
pb.SC_AI_TALK_TALKID_FIELD.has_default_value = false
pb.SC_AI_TALK_TALKID_FIELD.default_value = 0
pb.SC_AI_TALK_TALKID_FIELD.type = 5
pb.SC_AI_TALK_TALKID_FIELD.cpp_type = 1

pb.SC_AI_TALK.name = "SC_AI_TALK"
pb.SC_AI_TALK.full_name = ".SC_AI_TALK"
pb.SC_AI_TALK.nested_types = {}
pb.SC_AI_TALK.enum_types = {}
pb.SC_AI_TALK.fields = {pb.SC_AI_TALK_ENTITYUID_FIELD, pb.SC_AI_TALK_TALKID_FIELD}
pb.SC_AI_TALK.is_extendable = false
pb.SC_AI_TALK.extensions = {}
pb.SC_AI_EXSTATUS_ENTITYUID_FIELD.name = "EntityUID"
pb.SC_AI_EXSTATUS_ENTITYUID_FIELD.full_name = ".SC_AI_EXSTATUS.EntityUID"
pb.SC_AI_EXSTATUS_ENTITYUID_FIELD.number = 1
pb.SC_AI_EXSTATUS_ENTITYUID_FIELD.index = 0
pb.SC_AI_EXSTATUS_ENTITYUID_FIELD.label = 1
pb.SC_AI_EXSTATUS_ENTITYUID_FIELD.has_default_value = false
pb.SC_AI_EXSTATUS_ENTITYUID_FIELD.default_value = 0
pb.SC_AI_EXSTATUS_ENTITYUID_FIELD.type = 4
pb.SC_AI_EXSTATUS_ENTITYUID_FIELD.cpp_type = 4

pb.SC_AI_EXSTATUS_STATUSTYPE_FIELD.name = "StatusType"
pb.SC_AI_EXSTATUS_STATUSTYPE_FIELD.full_name = ".SC_AI_EXSTATUS.StatusType"
pb.SC_AI_EXSTATUS_STATUSTYPE_FIELD.number = 2
pb.SC_AI_EXSTATUS_STATUSTYPE_FIELD.index = 1
pb.SC_AI_EXSTATUS_STATUSTYPE_FIELD.label = 1
pb.SC_AI_EXSTATUS_STATUSTYPE_FIELD.has_default_value = false
pb.SC_AI_EXSTATUS_STATUSTYPE_FIELD.default_value = 0
pb.SC_AI_EXSTATUS_STATUSTYPE_FIELD.type = 5
pb.SC_AI_EXSTATUS_STATUSTYPE_FIELD.cpp_type = 1

pb.SC_AI_EXSTATUS_HASSTATUS_FIELD.name = "HasStatus"
pb.SC_AI_EXSTATUS_HASSTATUS_FIELD.full_name = ".SC_AI_EXSTATUS.HasStatus"
pb.SC_AI_EXSTATUS_HASSTATUS_FIELD.number = 3
pb.SC_AI_EXSTATUS_HASSTATUS_FIELD.index = 2
pb.SC_AI_EXSTATUS_HASSTATUS_FIELD.label = 1
pb.SC_AI_EXSTATUS_HASSTATUS_FIELD.has_default_value = false
pb.SC_AI_EXSTATUS_HASSTATUS_FIELD.default_value = 0
pb.SC_AI_EXSTATUS_HASSTATUS_FIELD.type = 5
pb.SC_AI_EXSTATUS_HASSTATUS_FIELD.cpp_type = 1

pb.SC_AI_EXSTATUS.name = "SC_AI_EXSTATUS"
pb.SC_AI_EXSTATUS.full_name = ".SC_AI_EXSTATUS"
pb.SC_AI_EXSTATUS.nested_types = {}
pb.SC_AI_EXSTATUS.enum_types = {}
pb.SC_AI_EXSTATUS.fields = {pb.SC_AI_EXSTATUS_ENTITYUID_FIELD, pb.SC_AI_EXSTATUS_STATUSTYPE_FIELD, pb.SC_AI_EXSTATUS_HASSTATUS_FIELD}
pb.SC_AI_EXSTATUS.is_extendable = false
pb.SC_AI_EXSTATUS.extensions = {}
pb.SC_AI_TASKCAMERA_ENTITYUID_FIELD.name = "EntityUID"
pb.SC_AI_TASKCAMERA_ENTITYUID_FIELD.full_name = ".SC_AI_TASKCAMERA.EntityUID"
pb.SC_AI_TASKCAMERA_ENTITYUID_FIELD.number = 1
pb.SC_AI_TASKCAMERA_ENTITYUID_FIELD.index = 0
pb.SC_AI_TASKCAMERA_ENTITYUID_FIELD.label = 1
pb.SC_AI_TASKCAMERA_ENTITYUID_FIELD.has_default_value = false
pb.SC_AI_TASKCAMERA_ENTITYUID_FIELD.default_value = 0
pb.SC_AI_TASKCAMERA_ENTITYUID_FIELD.type = 4
pb.SC_AI_TASKCAMERA_ENTITYUID_FIELD.cpp_type = 4

pb.SC_AI_TASKCAMERA_TASKCAMERAID_FIELD.name = "TaskCameraID"
pb.SC_AI_TASKCAMERA_TASKCAMERAID_FIELD.full_name = ".SC_AI_TASKCAMERA.TaskCameraID"
pb.SC_AI_TASKCAMERA_TASKCAMERAID_FIELD.number = 2
pb.SC_AI_TASKCAMERA_TASKCAMERAID_FIELD.index = 1
pb.SC_AI_TASKCAMERA_TASKCAMERAID_FIELD.label = 1
pb.SC_AI_TASKCAMERA_TASKCAMERAID_FIELD.has_default_value = false
pb.SC_AI_TASKCAMERA_TASKCAMERAID_FIELD.default_value = 0
pb.SC_AI_TASKCAMERA_TASKCAMERAID_FIELD.type = 5
pb.SC_AI_TASKCAMERA_TASKCAMERAID_FIELD.cpp_type = 1

pb.SC_AI_TASKCAMERA.name = "SC_AI_TASKCAMERA"
pb.SC_AI_TASKCAMERA.full_name = ".SC_AI_TASKCAMERA"
pb.SC_AI_TASKCAMERA.nested_types = {}
pb.SC_AI_TASKCAMERA.enum_types = {}
pb.SC_AI_TASKCAMERA.fields = {pb.SC_AI_TASKCAMERA_ENTITYUID_FIELD, pb.SC_AI_TASKCAMERA_TASKCAMERAID_FIELD}
pb.SC_AI_TASKCAMERA.is_extendable = false
pb.SC_AI_TASKCAMERA.extensions = {}
pb.SC_AI_TRAPMOVE_ENTITYUID_FIELD.name = "EntityUID"
pb.SC_AI_TRAPMOVE_ENTITYUID_FIELD.full_name = ".SC_AI_TRAPMOVE.EntityUID"
pb.SC_AI_TRAPMOVE_ENTITYUID_FIELD.number = 1
pb.SC_AI_TRAPMOVE_ENTITYUID_FIELD.index = 0
pb.SC_AI_TRAPMOVE_ENTITYUID_FIELD.label = 1
pb.SC_AI_TRAPMOVE_ENTITYUID_FIELD.has_default_value = false
pb.SC_AI_TRAPMOVE_ENTITYUID_FIELD.default_value = 0
pb.SC_AI_TRAPMOVE_ENTITYUID_FIELD.type = 4
pb.SC_AI_TRAPMOVE_ENTITYUID_FIELD.cpp_type = 4

pb.SC_AI_TRAPMOVE_MOVETYPE_FIELD.name = "MoveType"
pb.SC_AI_TRAPMOVE_MOVETYPE_FIELD.full_name = ".SC_AI_TRAPMOVE.MoveType"
pb.SC_AI_TRAPMOVE_MOVETYPE_FIELD.number = 2
pb.SC_AI_TRAPMOVE_MOVETYPE_FIELD.index = 1
pb.SC_AI_TRAPMOVE_MOVETYPE_FIELD.label = 1
pb.SC_AI_TRAPMOVE_MOVETYPE_FIELD.has_default_value = false
pb.SC_AI_TRAPMOVE_MOVETYPE_FIELD.default_value = 0
pb.SC_AI_TRAPMOVE_MOVETYPE_FIELD.type = 5
pb.SC_AI_TRAPMOVE_MOVETYPE_FIELD.cpp_type = 1

pb.SC_AI_TRAPMOVE_FACE_FIELD.name = "Face"
pb.SC_AI_TRAPMOVE_FACE_FIELD.full_name = ".SC_AI_TRAPMOVE.Face"
pb.SC_AI_TRAPMOVE_FACE_FIELD.number = 3
pb.SC_AI_TRAPMOVE_FACE_FIELD.index = 2
pb.SC_AI_TRAPMOVE_FACE_FIELD.label = 1
pb.SC_AI_TRAPMOVE_FACE_FIELD.has_default_value = false
pb.SC_AI_TRAPMOVE_FACE_FIELD.default_value = 0
pb.SC_AI_TRAPMOVE_FACE_FIELD.type = 5
pb.SC_AI_TRAPMOVE_FACE_FIELD.cpp_type = 1

pb.SC_AI_TRAPMOVE_MOVESPEED_FIELD.name = "MoveSpeed"
pb.SC_AI_TRAPMOVE_MOVESPEED_FIELD.full_name = ".SC_AI_TRAPMOVE.MoveSpeed"
pb.SC_AI_TRAPMOVE_MOVESPEED_FIELD.number = 4
pb.SC_AI_TRAPMOVE_MOVESPEED_FIELD.index = 3
pb.SC_AI_TRAPMOVE_MOVESPEED_FIELD.label = 1
pb.SC_AI_TRAPMOVE_MOVESPEED_FIELD.has_default_value = false
pb.SC_AI_TRAPMOVE_MOVESPEED_FIELD.default_value = 0.0
pb.SC_AI_TRAPMOVE_MOVESPEED_FIELD.type = 2
pb.SC_AI_TRAPMOVE_MOVESPEED_FIELD.cpp_type = 6

pb.SC_AI_TRAPMOVE_CURX_FIELD.name = "CurX"
pb.SC_AI_TRAPMOVE_CURX_FIELD.full_name = ".SC_AI_TRAPMOVE.CurX"
pb.SC_AI_TRAPMOVE_CURX_FIELD.number = 5
pb.SC_AI_TRAPMOVE_CURX_FIELD.index = 4
pb.SC_AI_TRAPMOVE_CURX_FIELD.label = 1
pb.SC_AI_TRAPMOVE_CURX_FIELD.has_default_value = false
pb.SC_AI_TRAPMOVE_CURX_FIELD.default_value = 0.0
pb.SC_AI_TRAPMOVE_CURX_FIELD.type = 2
pb.SC_AI_TRAPMOVE_CURX_FIELD.cpp_type = 6

pb.SC_AI_TRAPMOVE_CURY_FIELD.name = "CurY"
pb.SC_AI_TRAPMOVE_CURY_FIELD.full_name = ".SC_AI_TRAPMOVE.CurY"
pb.SC_AI_TRAPMOVE_CURY_FIELD.number = 6
pb.SC_AI_TRAPMOVE_CURY_FIELD.index = 5
pb.SC_AI_TRAPMOVE_CURY_FIELD.label = 1
pb.SC_AI_TRAPMOVE_CURY_FIELD.has_default_value = false
pb.SC_AI_TRAPMOVE_CURY_FIELD.default_value = 0.0
pb.SC_AI_TRAPMOVE_CURY_FIELD.type = 2
pb.SC_AI_TRAPMOVE_CURY_FIELD.cpp_type = 6

pb.SC_AI_TRAPMOVE_CURZ_FIELD.name = "CurZ"
pb.SC_AI_TRAPMOVE_CURZ_FIELD.full_name = ".SC_AI_TRAPMOVE.CurZ"
pb.SC_AI_TRAPMOVE_CURZ_FIELD.number = 7
pb.SC_AI_TRAPMOVE_CURZ_FIELD.index = 6
pb.SC_AI_TRAPMOVE_CURZ_FIELD.label = 1
pb.SC_AI_TRAPMOVE_CURZ_FIELD.has_default_value = false
pb.SC_AI_TRAPMOVE_CURZ_FIELD.default_value = 0.0
pb.SC_AI_TRAPMOVE_CURZ_FIELD.type = 2
pb.SC_AI_TRAPMOVE_CURZ_FIELD.cpp_type = 6

pb.SC_AI_TRAPMOVE_BORNX_FIELD.name = "BornX"
pb.SC_AI_TRAPMOVE_BORNX_FIELD.full_name = ".SC_AI_TRAPMOVE.BornX"
pb.SC_AI_TRAPMOVE_BORNX_FIELD.number = 8
pb.SC_AI_TRAPMOVE_BORNX_FIELD.index = 7
pb.SC_AI_TRAPMOVE_BORNX_FIELD.label = 1
pb.SC_AI_TRAPMOVE_BORNX_FIELD.has_default_value = false
pb.SC_AI_TRAPMOVE_BORNX_FIELD.default_value = 0.0
pb.SC_AI_TRAPMOVE_BORNX_FIELD.type = 2
pb.SC_AI_TRAPMOVE_BORNX_FIELD.cpp_type = 6

pb.SC_AI_TRAPMOVE_BORNY_FIELD.name = "BornY"
pb.SC_AI_TRAPMOVE_BORNY_FIELD.full_name = ".SC_AI_TRAPMOVE.BornY"
pb.SC_AI_TRAPMOVE_BORNY_FIELD.number = 9
pb.SC_AI_TRAPMOVE_BORNY_FIELD.index = 8
pb.SC_AI_TRAPMOVE_BORNY_FIELD.label = 1
pb.SC_AI_TRAPMOVE_BORNY_FIELD.has_default_value = false
pb.SC_AI_TRAPMOVE_BORNY_FIELD.default_value = 0.0
pb.SC_AI_TRAPMOVE_BORNY_FIELD.type = 2
pb.SC_AI_TRAPMOVE_BORNY_FIELD.cpp_type = 6

pb.SC_AI_TRAPMOVE_BORNZ_FIELD.name = "BornZ"
pb.SC_AI_TRAPMOVE_BORNZ_FIELD.full_name = ".SC_AI_TRAPMOVE.BornZ"
pb.SC_AI_TRAPMOVE_BORNZ_FIELD.number = 10
pb.SC_AI_TRAPMOVE_BORNZ_FIELD.index = 9
pb.SC_AI_TRAPMOVE_BORNZ_FIELD.label = 1
pb.SC_AI_TRAPMOVE_BORNZ_FIELD.has_default_value = false
pb.SC_AI_TRAPMOVE_BORNZ_FIELD.default_value = 0.0
pb.SC_AI_TRAPMOVE_BORNZ_FIELD.type = 2
pb.SC_AI_TRAPMOVE_BORNZ_FIELD.cpp_type = 6

pb.SC_AI_TRAPMOVE_RADIUS_FIELD.name = "Radius"
pb.SC_AI_TRAPMOVE_RADIUS_FIELD.full_name = ".SC_AI_TRAPMOVE.Radius"
pb.SC_AI_TRAPMOVE_RADIUS_FIELD.number = 11
pb.SC_AI_TRAPMOVE_RADIUS_FIELD.index = 10
pb.SC_AI_TRAPMOVE_RADIUS_FIELD.label = 1
pb.SC_AI_TRAPMOVE_RADIUS_FIELD.has_default_value = false
pb.SC_AI_TRAPMOVE_RADIUS_FIELD.default_value = 0.0
pb.SC_AI_TRAPMOVE_RADIUS_FIELD.type = 2
pb.SC_AI_TRAPMOVE_RADIUS_FIELD.cpp_type = 6

pb.SC_AI_TRAPMOVE_FLOATPARAM1_FIELD.name = "FloatParam1"
pb.SC_AI_TRAPMOVE_FLOATPARAM1_FIELD.full_name = ".SC_AI_TRAPMOVE.FloatParam1"
pb.SC_AI_TRAPMOVE_FLOATPARAM1_FIELD.number = 12
pb.SC_AI_TRAPMOVE_FLOATPARAM1_FIELD.index = 11
pb.SC_AI_TRAPMOVE_FLOATPARAM1_FIELD.label = 1
pb.SC_AI_TRAPMOVE_FLOATPARAM1_FIELD.has_default_value = false
pb.SC_AI_TRAPMOVE_FLOATPARAM1_FIELD.default_value = 0.0
pb.SC_AI_TRAPMOVE_FLOATPARAM1_FIELD.type = 2
pb.SC_AI_TRAPMOVE_FLOATPARAM1_FIELD.cpp_type = 6

pb.SC_AI_TRAPMOVE.name = "SC_AI_TRAPMOVE"
pb.SC_AI_TRAPMOVE.full_name = ".SC_AI_TRAPMOVE"
pb.SC_AI_TRAPMOVE.nested_types = {}
pb.SC_AI_TRAPMOVE.enum_types = {}
pb.SC_AI_TRAPMOVE.fields = {pb.SC_AI_TRAPMOVE_ENTITYUID_FIELD, pb.SC_AI_TRAPMOVE_MOVETYPE_FIELD, pb.SC_AI_TRAPMOVE_FACE_FIELD, pb.SC_AI_TRAPMOVE_MOVESPEED_FIELD, pb.SC_AI_TRAPMOVE_CURX_FIELD, pb.SC_AI_TRAPMOVE_CURY_FIELD, pb.SC_AI_TRAPMOVE_CURZ_FIELD, pb.SC_AI_TRAPMOVE_BORNX_FIELD, pb.SC_AI_TRAPMOVE_BORNY_FIELD, pb.SC_AI_TRAPMOVE_BORNZ_FIELD, pb.SC_AI_TRAPMOVE_RADIUS_FIELD, pb.SC_AI_TRAPMOVE_FLOATPARAM1_FIELD}
pb.SC_AI_TRAPMOVE.is_extendable = false
pb.SC_AI_TRAPMOVE.extensions = {}
pb.SC_AI_SHOWFX_FXID_FIELD.name = "FxID"
pb.SC_AI_SHOWFX_FXID_FIELD.full_name = ".SC_AI_SHOWFX.FxID"
pb.SC_AI_SHOWFX_FXID_FIELD.number = 1
pb.SC_AI_SHOWFX_FXID_FIELD.index = 0
pb.SC_AI_SHOWFX_FXID_FIELD.label = 1
pb.SC_AI_SHOWFX_FXID_FIELD.has_default_value = false
pb.SC_AI_SHOWFX_FXID_FIELD.default_value = 0
pb.SC_AI_SHOWFX_FXID_FIELD.type = 5
pb.SC_AI_SHOWFX_FXID_FIELD.cpp_type = 1

pb.SC_AI_SHOWFX_ORIENTATION_FIELD.name = "Orientation"
pb.SC_AI_SHOWFX_ORIENTATION_FIELD.full_name = ".SC_AI_SHOWFX.Orientation"
pb.SC_AI_SHOWFX_ORIENTATION_FIELD.number = 2
pb.SC_AI_SHOWFX_ORIENTATION_FIELD.index = 1
pb.SC_AI_SHOWFX_ORIENTATION_FIELD.label = 1
pb.SC_AI_SHOWFX_ORIENTATION_FIELD.has_default_value = false
pb.SC_AI_SHOWFX_ORIENTATION_FIELD.default_value = 0.0
pb.SC_AI_SHOWFX_ORIENTATION_FIELD.type = 2
pb.SC_AI_SHOWFX_ORIENTATION_FIELD.cpp_type = 6

pb.SC_AI_SHOWFX_DESTX_FIELD.name = "DestX"
pb.SC_AI_SHOWFX_DESTX_FIELD.full_name = ".SC_AI_SHOWFX.DestX"
pb.SC_AI_SHOWFX_DESTX_FIELD.number = 3
pb.SC_AI_SHOWFX_DESTX_FIELD.index = 2
pb.SC_AI_SHOWFX_DESTX_FIELD.label = 1
pb.SC_AI_SHOWFX_DESTX_FIELD.has_default_value = false
pb.SC_AI_SHOWFX_DESTX_FIELD.default_value = 0.0
pb.SC_AI_SHOWFX_DESTX_FIELD.type = 2
pb.SC_AI_SHOWFX_DESTX_FIELD.cpp_type = 6

pb.SC_AI_SHOWFX_DESTY_FIELD.name = "DestY"
pb.SC_AI_SHOWFX_DESTY_FIELD.full_name = ".SC_AI_SHOWFX.DestY"
pb.SC_AI_SHOWFX_DESTY_FIELD.number = 4
pb.SC_AI_SHOWFX_DESTY_FIELD.index = 3
pb.SC_AI_SHOWFX_DESTY_FIELD.label = 1
pb.SC_AI_SHOWFX_DESTY_FIELD.has_default_value = false
pb.SC_AI_SHOWFX_DESTY_FIELD.default_value = 0.0
pb.SC_AI_SHOWFX_DESTY_FIELD.type = 2
pb.SC_AI_SHOWFX_DESTY_FIELD.cpp_type = 6

pb.SC_AI_SHOWFX_DESTZ_FIELD.name = "DestZ"
pb.SC_AI_SHOWFX_DESTZ_FIELD.full_name = ".SC_AI_SHOWFX.DestZ"
pb.SC_AI_SHOWFX_DESTZ_FIELD.number = 5
pb.SC_AI_SHOWFX_DESTZ_FIELD.index = 4
pb.SC_AI_SHOWFX_DESTZ_FIELD.label = 1
pb.SC_AI_SHOWFX_DESTZ_FIELD.has_default_value = false
pb.SC_AI_SHOWFX_DESTZ_FIELD.default_value = 0.0
pb.SC_AI_SHOWFX_DESTZ_FIELD.type = 2
pb.SC_AI_SHOWFX_DESTZ_FIELD.cpp_type = 6

pb.SC_AI_SHOWFX.name = "SC_AI_SHOWFX"
pb.SC_AI_SHOWFX.full_name = ".SC_AI_SHOWFX"
pb.SC_AI_SHOWFX.nested_types = {}
pb.SC_AI_SHOWFX.enum_types = {}
pb.SC_AI_SHOWFX.fields = {pb.SC_AI_SHOWFX_FXID_FIELD, pb.SC_AI_SHOWFX_ORIENTATION_FIELD, pb.SC_AI_SHOWFX_DESTX_FIELD, pb.SC_AI_SHOWFX_DESTY_FIELD, pb.SC_AI_SHOWFX_DESTZ_FIELD}
pb.SC_AI_SHOWFX.is_extendable = false
pb.SC_AI_SHOWFX.extensions = {}

CS_AI_ATTACK = protobuf.Message(pb.CS_AI_ATTACK)
CS_AI_ATTACK_POS = protobuf.Message(pb.CS_AI_ATTACK_POS)
CS_AI_DAMAGE = protobuf.Message(pb.CS_AI_DAMAGE)
CS_AI_DASH = protobuf.Message(pb.CS_AI_DASH)
CS_AI_POSITION = protobuf.Message(pb.CS_AI_POSITION)
CS_AI_RUN = protobuf.Message(pb.CS_AI_RUN)
CS_AI_TALK = protobuf.Message(pb.CS_AI_TALK)
CS_AI_TURNFACE = protobuf.Message(pb.CS_AI_TURNFACE)
MSG_AI_ATTACK = 2
MSG_AI_ATTACK_POS = 11
MSG_AI_DAMAGE = 6
MSG_AI_DASH = 12
MSG_AI_EXSTATUS = 14
MSG_AI_GETNEARENEMY = 10
MSG_AI_NONE = 0
MSG_AI_POSITION = 7
MSG_AI_RUN = 1
MSG_AI_SHOWFX = 17
MSG_AI_TALK = 13
MSG_AI_TASKCAMERA = 15
MSG_AI_TRAPMOVE = 16
MSG_AI_TURNFACE = 3
SC_AI_ATTACK = protobuf.Message(pb.SC_AI_ATTACK)
SC_AI_ATTACK_POS = protobuf.Message(pb.SC_AI_ATTACK_POS)
SC_AI_DAMAGE = protobuf.Message(pb.SC_AI_DAMAGE)
SC_AI_DAMAGE.Victim = protobuf.Message(pb.SC_AI_DAMAGE_VICTIM)
SC_AI_DASH = protobuf.Message(pb.SC_AI_DASH)
SC_AI_EXSTATUS = protobuf.Message(pb.SC_AI_EXSTATUS)
SC_AI_GETNEARENEMY = protobuf.Message(pb.SC_AI_GETNEARENEMY)
SC_AI_POSITION = protobuf.Message(pb.SC_AI_POSITION)
SC_AI_RUN = protobuf.Message(pb.SC_AI_RUN)
SC_AI_SHOWFX = protobuf.Message(pb.SC_AI_SHOWFX)
SC_AI_TALK = protobuf.Message(pb.SC_AI_TALK)
SC_AI_TASKCAMERA = protobuf.Message(pb.SC_AI_TASKCAMERA)
SC_AI_TRAPMOVE = protobuf.Message(pb.SC_AI_TRAPMOVE)
SC_AI_TURNFACE = protobuf.Message(pb.SC_AI_TURNFACE)

