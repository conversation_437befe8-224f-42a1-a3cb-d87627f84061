﻿using UnityEngine;

namespace View
{
	// /// <summary>
	// /// 一条激光
	// /// </summary>
	// public class BulletLaser : BulletBase
	// {
	// 	/// <summary>
	// 	/// 激光起点
	// 	/// </summary>
	// 	public GameObject LaserBegin { get; set; }
	// 	/// <summary>
	// 	/// 激光光路
	// 	/// </summary>
	// 	public GameObject LaserMiddle { get; set; }
	// 	/// <summary>
	// 	/// 激光终点
	// 	/// </summary>
	// 	public GameObject LaserEnd { get; set; }
	// }
}
