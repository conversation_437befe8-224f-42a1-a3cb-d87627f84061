--[[
********************************************************************
    created:    2023/07/16
    author :
    purpose:    装备宝箱界面
*********************************************************************
--]]

local luaID = ('UIEquipBox')

local SET = {
    BagSlotMax = 20,  -- 背包单页槽最大值
    SlotWidth = 130,  -- 装备物品槽宽
    SlotHeight = 130, -- 装备物品槽高
}

---@class UIEquipBox:UIWndBase 装备宝箱界面
local m = {}

-- 订阅事件列表
function m.GetOpenEventList()
    return {
        [EventID.LogicDataChange] = m.UpdateView,
        [EventID.OnHeroPropChange] = m.UpdateView,
        [EventID.OnSkepGoodsChange] = m.UpdateSlotItem,
        [EventID.AutoBoxContinue] = m.AutoBoxContinue,
        [EventID.StoreBuyItem] = m.StoreBuyItem,
        [EventID.StopAutoBoxContinue] = m.StopAutoBoxContinue,
    }
end

--预制体自适应
function m.AdaptScale()
    HelperL.AdaptScale_Width(m.objList.Item_AttributeBox)
    HelperL.AdaptScale(m.objList.Img_Bg1, 6)
    HelperL.AdaptScale(m.objList.Img_Bg2, 6)
end

--创建时事件
function m.OnCreate()
    m.adverID            = 111
    --自动开宝箱时间
    m.openBoxTimeNum     = 0.65
    m.autoOpenBoxTimeNum = 0.65
    ---@type SlotItem[]
    m.equipItem          = {}
    m.isAutoOpenBox      = false
    m.can_open_box       = true


    AtlasManager:AsyncGetGoodsSprite(18, m.objList.Img_icon2)
    AtlasManager:AsyncGetGoodsSprite(3, m.objList.Img_Expend1)
    AtlasManager:AsyncGetGoodsSprite(3, m.objList.Img_Expend10)
    AtlasManager:AsyncGetGoodsSprite(3, m.objList.Img_ExpendAuto)

    --开一次宝箱消耗
    m.openBoxExpend = Schemes.ConstValue:Get(CONST_VALUE.CONST_LOTTERY_COSTDIAMOND) or 0

    ---------------创建宝箱模型------------------
    local fileName = "model/prefab/diaoluo/Spine_BaoXiang"
    HotResManager.ReadModelAsync(m.objList.Btn_OpenBox.gameObject, fileName,
        function(objModel)
            local obj = objModel.transform:Find("Spine_WXR05")
            m.curSpineBox = obj:GetComponent(typeof(UnityEngine.Animator))
            objModel.transform.localScale = Vector3(110, 110, 110)
            objModel.transform.localPosition = Vector3(0, 0, 1)
        end, 0, true)

    --自动抽
    m.autoOpenBoxTimer = Timer.New(m.RequestOnecDiamondDraw, m.autoOpenBoxTimeNum, 1)

    if SWITCH.EQUIP_BOX_AUTO then
        --开启自动--关闭10连抽
        m.objList.Obj_OpenBox10.gameObject:SetActive(false)
        m.objList.Obj_OpenBoxAuto.gameObject:SetActive(true)
    else
        --开启10连抽--关闭自动
        m.objList.Obj_OpenBox10.gameObject:SetActive(true)
        m.objList.Obj_OpenBoxAuto.gameObject:SetActive(false)
    end
    m.RegisterClickEvent()
    
    m.Item_Fight = FightAttribute.New()
    m.Item_Fight.Init(m.objList.Item_Fight)
    return true
end

--打开时
function m.OnOpen()
    m.objList.Obj_AdWindow.gameObject:SetActive(false)
    if m.curSpineBox then
        m.curSpineBox:Play("Spine_BaoXiang04")
    end
    m.UpdateSlotItem()
    m.isAutoOpenBox = false
    local bool = m.CheckBestEquipment()
    m.objList.Btn_Gift1.enabled = bool
    m.objList.Btn_Gift10.enabled = bool
    m.UpdateView()
end

--更新装备框
function m.UpdateSlotItem()
    local skepEuip = SkepModule.GetEquipSkep()
    ---@type SlotItem
    local item
    local entity, parent
    for i, v in ipairs(EquipOrder) do
        if not m.equipItem[v] then
            if i < 5 then
                parent = m.objList.LSlots.transform
            else
                parent = m.objList.RSlots.transform
            end
            m.equipItem[v] = _GAddSlotItem(parent)
        end

        item = m.equipItem[v]
        entity = EntityModule:GetEntity(skepEuip[v])
        item:SetEntity(entity)
        if entity then
            local ex = entity:GetProperty(EQUIPMENT_FIELD.EQUIPMENT_FIELD_CUSTOM)
            local quality = entity:GetProperty(EQUIPMENT_FIELD.EQUIPMENT_FIELD_QUALITY)
            local starNum = entity:GetProperty(EQUIPMENT_FIELD.EQUIPMENT_FIELD_STARNUM)
            local num = starNum * 100 + quality * 1000 + ex
            if num > 0 then
                item:SetDes3('+' .. num)
            end
        else
            item:SetDes(GetEquipTypeDescribe(v))
        end
    end
end

--注册点击事件
function m.RegisterClickEvent()
    m.objList.Btn_Gift1.onClick:AddListenerEx(function()
        if m.isAutoOpenBox and m.GiftType == -1 then
            m.CancelAutoOpenBox()
        end
        m.OpenBox(1)
    end)
    m.objList.Btn_Gift10.onClick:AddListenerEx(function()
        if m.isAutoOpenBox and m.GiftType == -1 then
            m.CancelAutoOpenBox()
        end
        m.OpenBox(10)
    end)
    m.objList.Btn_Grey1.onClick:AddListenerEx(function()
        if m.isAutoOpenBox and m.GiftType == -1 then
            m.CancelAutoOpenBox()
        end
        m.OpenBox(1)
    end)
    m.objList.Btn_Grey10.onClick:AddListenerEx(function()
        if m.isAutoOpenBox and m.GiftType == -1 then
            m.CancelAutoOpenBox()
        end
        m.OpenBox(10)
    end)
    m.objList.Btn_Close.onClick:AddListenerEx(function()
        m:CloseSelf()
    end)

    m.objList.Btn_Box_Award.onClick:AddListenerEx(function()
        m.CancelAutoOpenBox()
        UIManager:OpenWnd(WndID.Shop, 1)
    end)
    m.objList.Btn_Auto.onClick:AddListenerEx(function()
        local autoNum = HeroDataManager:GetLogicData(LOGIC_DATA.DATA_AUTO_LOTTEYCOUNT)
        if autoNum > 0 or m.IsInfiniteCard() then
            m.objList.Obj_AdWindow.gameObject:SetActive(false)
            m.objList.Img_Model.gameObject:SetActive(true)
            m.objList.Btn_OpenBox.gameObject:SetActive(true)
            if m.openBoxDelay then
                m.openBoxDelay:Stop()
            end
            m.objList.Btn_GreyAuto.gameObject:SetActive(true)
            m.objList.Btn_OpenBoxAuto.gameObject:SetActive(false)
            m.OpenBox(-1)
        else
            HelperL.ShowMessage(TipType.FlowText, GetGameText(luaID, 60))
        end
    end)
    m.objList.Btn_Close3.onClick:AddListenerEx(function()
        m.objList.Obj_AdWindow.gameObject:SetActive(false)
        m.objList.Img_Model.gameObject:SetActive(true)
        m.objList.Btn_OpenBox.gameObject:SetActive(true)
    end)
    m.objList.Btn_Add_Num.onClick:AddListenerEx(function()
        AdvertisementManager.ShowRewardAd(m.adverID, m.ShowAdWindow)
    end)
    m.objList.Btn_Add_Num2.onClick:AddListenerEx(function()
        AdvertisementManager.GetAdAward(m.adverID, m.ShowAdWindow)
    end)
    m.objList.Btn_Recharge.onClick:AddListenerEx(function()
        m.CancelAutoOpenBox()
        m.objList.Obj_AdWindow.gameObject:SetActive(false)
        m.objList.Img_Model.gameObject:SetActive(true)
        m.objList.Btn_OpenBox.gameObject:SetActive(true)
        local cfg = Schemes.LotteryLevel:Get(1101)
        local cardID = tonumber(HelperL.Split(cfg.Desc, ';')[3]) or 0
        UIManager:OpenWnd(WndID.RecommendCommodities, cardID)
    end)
    m.objList.Btn_OpenBoxAuto.onClick:AddListenerEx(m.ShowAdWindow)
    m.objList.Btn_GreyAuto.onClick:AddListenerEx(m.CancelAutoOpenBox)
    m.objList.Btn_Explain.onClick:AddListenerEx(function()
        HelperL.OpenTipsWindow(9)
    end)
end

--按钮音效
function m.PlayBtnSound()
    SoundManager:PlaySound(1005)
end

--更新界面
function m.UpdateView()
    ---------------更新角色模型---------------------
    m.UpdateUIModel(HelperL.GetWeaponEquipID())

    ---------------更新消耗资源---------------------
    local haveDiamond = EntityModule.hero:GetProperty(PLAYER_FIELD.PLAYER_FIELD_DIAMOND)
    m.objList.Txt_Value2.text = HelperL.GetChangeNum(SkepModule:GetGoodsCount(18))
    local price = m.openBoxExpend
    local bool = price <= haveDiamond
    local color = bool and UI_COLOR.White or UI_COLOR.Red
    m.objList.Txt_Expend1.text = string.format(GetGameText(luaID, 1), color, price,
        haveDiamond)
    m.objList.Txt_ExpendAuto.text = string.format(GetGameText(luaID, 1), color, price,
        haveDiamond)

    m.objList.Btn_Gift1.gameObject:SetActive(bool)
    m.objList.Btn_Grey1.gameObject:SetActive(not bool)

    price = m.openBoxExpend * 9
    bool = price <= haveDiamond
    color = bool and UI_COLOR.White or UI_COLOR.Red
    m.objList.Txt_Expend10.text = string.format(GetGameText(luaID, 1), color, price,
        haveDiamond)
    m.objList.Btn_Gift10.gameObject:SetActive(bool)
    m.objList.Btn_Grey10.gameObject:SetActive(not bool)

    -----------------更新兑换商店入口-------------------
    local level = EntityModule.hero:GetProperty(CREATURE_FIELD.CREATURE_FIELD_LEVEL)
    m.objList.Btn_Box_Award.gameObject:SetActive(level >= BUTTON_OPEN_LEVEL.EquipShop)

    local autoNum = HeroDataManager:GetLogicData(LOGIC_DATA.DATA_AUTO_LOTTEYCOUNT)
    color = autoNum > 0 and UI_COLOR.White or UI_COLOR.Red
    m.objList.Txt_OpenBoxAuto.text = string.format(GetGameText(luaID, 3), color, autoNum)
    m.objList.Txt_GreyAuto.text = string.format(GetGameText(luaID, 4), color, autoNum)
end

--开宝箱
function m.OpenBox(type)
    m.PlayBtnSound()
    -- m.CheckBestEquipment()
    local num = 1
    if type == -1 then
        num = 1
        m.isAutoOpenBox = true
    elseif type == 1 then
        num = 1
        m.isAutoOpenBox = false
    elseif type == 10 then
        num = 9
        m.isAutoOpenBox = true
    end
    --判断消耗资源是否足够
    if HelperL.IsLackGoods(3, m.openBoxExpend * num) then
        m.CancelAutoOpenBox()
        return
    end
    m.objList.Btn_Gift1.enabled = false
    m.objList.Btn_Gift10.enabled = false

    m.curSpineBox:Play("Spine_BaoXiang03")
    m.GiftType = type
    if not m.openBoxDelay then
        m.openBoxDelay = Timer.New(m.RequestOpenBox, m.openBoxTimeNum, 1)
    else
        m.openBoxDelay:Stop()
        m.openBoxDelay:Reset(m.RequestOpenBox, m.openBoxTimeNum, 1)
    end
    m.openBoxDelay:Start()
end

--请求开宝箱
function m.RequestOpenBox()
    if m.GiftType == -1 then
        local auto = 0
        if m.isAutoOpenBox then
            auto = m.IsInfiniteCard() and 0 or 1
        end
        LuaModule.RunLuaRequest(string.format("LuaRequestOnecDiamondDraw?auto=%s", auto), m.RequestCallback)
    elseif m.GiftType == 1 then
        LuaModule.RunLuaRequest("LuaRequestOnecDiamondDraw", m.RequestCallback)
    elseif m.GiftType == 10 then
        HelperL.DisplayReward(PrizeContentType.STRING2, nil, GetGameText(luaID, 2), m.AutoBoxContinue)
        LuaModule.RunLuaRequest("LuaRequestTenDiamondDraw", m.RequestCallback)
    end
end

--请求回调
function m.RequestCallback(result, content)
    m.curSpineBox:Play("Spine_BaoXiang04")
    if result == RESULT_CODE.RESULT_COMMON_SUCCEED then
        local bool = m.CheckBestEquipment()
        if m.GiftType == -1 then

        elseif m.GiftType == 1 then
            m.objList.Btn_Gift1.enabled = bool
            m.objList.Btn_Gift10.enabled = bool
        elseif m.GiftType == 10 then
            EventManager:Fire(EventID.UIDisplayReward_SetRewardsContent, content)
        end
    else
        HelperL.ShowMessage(TipType.FlowText, ResultCode:GetResultCodeStr(result))
    end
end

--检查背包是否还有装备,true:没有装备,false:有装备
function m.CheckBestEquipment()
    local skepBag = SkepModule:GetSkepByID(SKEPID.SKEPID_PACKET)
    local entity
    --背包空格数量
    local nil_amount = 0
    if skepBag then
        for i = 0, skepBag.indexMaxsize do
            entity = EntityModule:GetEntity(skepBag[i])
            if entity then
                if HelperL.IsEuipType(entity:GetProperty(ENTITY_FIELD.ENTITY_FIELD_ID)) then
                    UIManager:OpenWnd(WndID.TipBestEquipment, { uid = skepBag[i], auto = m.isAutoOpenBox })
                    return false
                end
            else
                nil_amount = nil_amount + 1
            end
        end
        if nil_amount > 200 then
            --取出仓库装备
            LuaModule.RunLuaRequest("LuaRequestTakeoutAllGoods")
        end
    end
    return true
end

--继续--自动开宝箱
function m.AutoBoxContinue()
    local bool = m.CheckBestEquipment()
    m.objList.Btn_Gift1.enabled = bool
    m.objList.Btn_Gift10.enabled = bool
    if m.isAutoOpenBox and m.GiftType == -1 then
        m.OpenBox(-1)
    end
end

--更新角色模型
function m.UpdateUIModel(equipID)
    if not m.ui2DModel then
        m.ui2DModel = HelperL.CreateRenderTextureAndModel(m.objList.Img_Model)
    end
    m.ui2DModel:CreateUIModel3(equipID)
end

--商店购买事件
function m.StoreBuyItem()
    m.isAutoOpenBox = false
    m.CheckBestEquipment()
end

--有无限开宝箱卡
function m.IsInfiniteCard()
    return false
end

--显示广告界面
function m.ShowAdWindow()
    m.objList.Obj_AdWindow.gameObject:SetActive(true)
    m.objList.Btn_OpenBox.gameObject:SetActive(false)
    m.objList.Img_Model.gameObject:SetActive(false)
    m.objList.Txt_AdHint1.text = GetGameText(luaID, 32)

    local lotteryLevel = HeroDataManager:GetLogicByte(LOGIC_DATA.DATA_SOCIETY_ECTYPETOWER_HIGHSTAGE, 1)
    lotteryLevel = lotteryLevel <= 0 and 1 or lotteryLevel
    local lotteryItem = Schemes.LotteryLevel:Get(lotteryLevel + 1100)
    local descs = HelperL.Split(lotteryItem.Desc, ';')
    local autoNum = 0
    if lotteryItem then
        autoNum = tonumber(descs[1]) or 0
    end
    local isBuy = m.IsInfiniteCard()
    m.objList.Txt_AdHint2.text = "X" .. lotteryLevel
    local add = 1 - (tonumber(descs[2]) or 0)
    local add2 = (tonumber(descs[4]) or 0) * 100
    if isBuy then
        add = add + add2
    end
    local exp = HeroDataManager:GetLogicData(LOGIC_DATA.DATA_WARBAND_ACTVCOUNTVALUESAVE1)
    m.objList.Txt_AdHint3.text = string.format(GetGameText(luaID, 15), lotteryLevel, autoNum, (add * 100) .. '%', exp,
        lotteryItem.LevelEXP)
    m.objList.Txt_Hint_Recharge.text = string.format(GetGameText(luaID, 52), add2)

    local commonText = Schemes.CommonText:Get(m.adverID)
    local num = HeroDataManager:GetLogicByte(commonText.Param4, commonText.Param5)

    m.objList.Btn_Ad2.gameObject:SetActive(false)
    m.objList.Btn_Auto.gameObject:SetActive(false)
    m.objList.Btn_Add_Num.gameObject:SetActive(false)

    local autoNum2 = HeroDataManager:GetLogicData(LOGIC_DATA.DATA_AUTO_LOTTEYCOUNT)
    if autoNum2 > 0 or isBuy then
        m.objList.Btn_Auto.gameObject:SetActive(true)
    else
        m.objList.Btn_Ad2.gameObject:SetActive(num >= commonText.DayTime)
        m.objList.Btn_Add_Num.gameObject:SetActive(num < commonText.DayTime)
    end
end

--请求一次开宝箱
function m.RequestOnecDiamondDraw()
    local auto = 0
    if m.isAutoOpenBox then
        auto = m.IsInfiniteCard() and 0 or 1
    end
    LuaModule.RunLuaRequest(string.format("LuaRequestOnecDiamondDraw?auto=%s", auto), m.RequestCallback)
end

--暂停--自动开宝箱
function m.StopAutoBoxContinue()
    m.curSpineBox:Play("Spine_BaoXiang04")
end

--取消自动开宝箱
function m.CancelAutoOpenBox()
    m.objList.Btn_GreyAuto.gameObject:SetActive(false)
    m.objList.Btn_OpenBoxAuto.gameObject:SetActive(true)
    m.autoOpenBoxTimer:Stop()
    m.autoOpenBoxTimer:Reset(m.RequestOnecDiamondDraw, m.autoOpenBoxTimeNum, 1)
    m.curSpineBox:Play("Spine_BaoXiang04")
    m.isAutoOpenBox = false
end

--关闭时
function m.OnClose()
    m.CancelAutoOpenBox()
end

return m
