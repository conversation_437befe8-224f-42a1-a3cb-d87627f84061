---@class UISLTSDialog:UIWndBase
local m = {}
--------------------------------------------------------------------
-- 订阅事件列表
--------------------------------------------------------------------
function m.GetOpenEventList()
    return {
       
    }
end

--------------------------------------------------------------------
-- 初始化
--------------------------------------------------------------------
function m.OnCreate()
    m.RegisterClick<PERSON>vent()
    return true
end

--------------------------------------------------------------------
-- 窗口开启
--------------------------------------------------------------------
function m.OnOpen()
    m.objList.Txt_Title.text = GetGameText('UILogin', 24)
    m.objList.Txt_Content.text = GetGameText('UILogin', 4)
end

--------------------------------------------------------------------
--注册点击事件
--------------------------------------------------------------------
function m.RegisterClickEvent()
    m.objList.Btn_Close.onClick:AddListenerEx(m.OnClickClose)
end

--------------------------------------------------------------------
--关闭界面
--------------------------------------------------------------------
function m.OnClickClose()
    m:CloseSelf()
end

return m
