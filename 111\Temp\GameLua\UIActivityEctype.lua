--[[
********************************************************************
    created:	2024/05/28
    author :	李锦剑
    purpose:    活动副本
*********************************************************************
--]]


local luaID = 'UIActivityEctype'

---每日副本
---@class UIActivityEctype:UIWndBase
local m = {}

--------------------------------------------------------------------
-- 订阅事件列表
--------------------------------------------------------------------
function m.GetOpenEventList()
    return {
        [EventID.LogicDataChange] = m.UpdateView,
        [EventID.EntitySyncBuff] = m.UpdateView,
        [EventID.OnGoodsPropChange] = m.UpdateView,
        [EventID.OnHeroPropChange] = m.UpdateView,
        [EventID.ActorDataChanged] = m.UpdateView,
        -- [EventID.ActorDataLoaded] = m.UpdateView,
    }
end

--------------------------------------------------------------------
-- 初始化
--------------------------------------------------------------------
function m.OnCreate()
    m.objList.Txt_TitleSurvival.text = GetGameText(luaID, 4)
    m.objList.Txt_SurvivalContent.text = GetGameText(luaID, 5)
    m.objList.Txt_Survival.text = GetGameText(luaID, 6)

    ---@type Item_Ectype[]
    m.Item_Ectype_List = {}
    for i, v in ipairs(ActivityEctypeTypeList) do
        if not m.Item_Ectype_List[i] then
            m.Item_Ectype_List[i] = m.Creation_Item_Ectype(m.objList.Grid_Ectype, i)
        end
        m.Item_Ectype_List[i].SetData(v)
    end

    m.RegisterClickEvent()
    return true
end

--------------------------------------------------------------------
-- 窗口开启
--------------------------------------------------------------------
function m:OnOpen()
    m.UpdateView()
end

--------------------------------------------------------------------
-- 注册点击事件
--------------------------------------------------------------------
function m.RegisterClickEvent()
    m:AddClick(m.objList.Btn_Close, function()
        m:CloseSelf()
    end)
    m:AddClick(m.objList.Btn_Survival, function()
        UIManager:OpenWnd(WndID.SurvivalEctype)
    end)
end

--------------------------------------------------------------------
--更新界面
--------------------------------------------------------------------
function m.UpdateView()
    for i, v in ipairs(m.Item_Ectype_List) do
        v.UpdateView()
    end
end

--------------------------------------------------------------------
-- 创建副本栏
--------------------------------------------------------------------
function m.Creation_Item_Ectype(parent, index)
    ---@class Item_Ectype
    local item = {}
    item.index = index
    ---@type SlotItem[]
    item.slotItem_List = {}
    item.com = m:CreateSubItem(parent, m.objList.Item_Ectype)
    m:AddClick(item.com.Btn_Start, function()
        -- if not item.ectypeType then
        --     return
        -- end
        -- UIManager:OpenWnd(WndID.EverydayEctype, item.ectypeType)

        m.ChuZheng(item.ectypeType)
    end)
    item.SetData = function(ectypeType)
        item.ectypeType = ectypeType
        if ectypeType then
            item.Stage_List = Schemes.CatMainStage:GetByFrontType(item.ectypeType)
            local maxStage = GamePlayerData.GameEctype:GetProgress(item.ectypeType)
            if maxStage ~= 0 then
                maxStage = (maxStage + 1) % 100
                if maxStage >= #item.Stage_List then
                    maxStage = #item.Stage_List
                end
            else
                maxStage = 1
            end
            item.StageCfg = item.Stage_List[maxStage]
            item.com.gameObject:SetActive(true)
        else
            item.com.gameObject:SetActive(false)
        end
    end
    item.UpdateView = function()
        if not item.StageCfg then return end
        item.com.Txt_Title.text = item.StageCfg.Desc
        item.com.Txt_Desc2.text = item.StageCfg.Desc2
        local iconID = item.StageCfg.Icon
        if iconID == "" or iconID == "0" then
            iconID = "100001"
        end
        AtlasManager:AsyncGetSprite(iconID, item.com.Img_Icon)
        local expendList = HelperL.Split(item.StageCfg.Need, ';')
        --消耗物品ID
        local expID = tonumber(expendList[1]) or 0
        --消耗物品数量
        local expNum = tonumber(expendList[2]) or 0
        --广告物品ID
        -- local adID = tonumber(expendList[3]) or 0
        --消耗物品ID2
        local expID2 = tonumber(expendList[4]) or 0
        --消耗物品数量2
        local expNum2 = tonumber(expendList[5]) or 0

        local count1 = SkepModule:GetGoodsCount(expID) or 0
        local count2 = SkepModule:GetGoodsCount(expID2) or 0

        --挑战次数
        local challengeNum = math.floor(count1 / expNum)
        item.com.Txt_Num.text = string.format(GetGameText(luaID, 1), challengeNum)
        AtlasManager:AsyncGetGoodsSprite(expID2, item.com.Img_EX)
        local color = count2 >= expNum2 and UI_COLOR.White or UI_COLOR.Red
        item.com.Txt_EX.text = string.format("<color=%s>%s/%s</color>", color, count2, expNum2)

        local level1 = EntityModule.hero:GetProperty(CREATURE_FIELD.CREATURE_FIELD_LEVEL)
        local level2 = EctypeUnlockLevel[item.ectypeType] or 0
        if level1 < level2 then
            item.com.Txt_Hint.text = string.format(GetGameText(luaID, 2), level2)
            item.com.Btn_Lock.gameObject:SetActive(true)
            item.com.Btn_Start.gameObject:SetActive(false)
        else
            item.com.Btn_Lock.gameObject:SetActive(false)
            item.com.Btn_Start.gameObject:SetActive(true)
        end
        item.com.Img_RedDot.gameObject:SetActive(RedDotCheckFunc.Check_UIActivityEctype(item.ectypeType))

        if item.StageCfg.ID ~= item.lastStageID then
            item.lastStageID = item.StageCfg.ID
            local prizeGoods = Schemes.CatMainStage:GetSucceedRewards(item.StageCfg.ID)
            local num = math.max(#prizeGoods, #item.slotItem_List)
            for i = 1, num, 1 do
                if not item.slotItem_List[i] then
                    item.slotItem_List[i] = _GAddSlotItem(item.com.Grid_Goods)
                end
                if prizeGoods[i] then
                    item.slotItem_List[i]:SetItemID(prizeGoods[i].ID)
                    item.slotItem_List[i]:SetCount(prizeGoods[i].Num)
                    item.slotItem_List[i]:SetActive(true)
                else
                    item.slotItem_List[i]:SetActive(false)
                end
            end
        end
    end
    return item
end

--------------------------------------------------------------------
-- 出征
--------------------------------------------------------------------
function m.ChuZheng(ectypeType)
    local stageID = GamePlayerData.GameEctype:GetChallengeableID(ectypeType)
    local cfg = Schemes.CatMainStage:Get(stageID)
    if not cfg then
        error('读取 CatMainStage 表失败 stageID=' .. stageID)
        return
    end

    local expendList = HelperL.Split(cfg.Need, ';')
    --消耗物品ID
    local expID = tonumber(expendList[1]) or 0
    --消耗物品数量
    local expNum = tonumber(expendList[2]) or 0
    --广告物品ID
    -- local adID = tonumber(expendList[3])
    --消耗物品ID2
    local expID2 = tonumber(expendList[4]) or 0
    --消耗物品数量2
    local expNum2 = tonumber(expendList[5]) or 0

    --判断挑战次数
    if HelperL.IsLackGoods(expID, expNum, false, false) then
        HelperL.ShowMessage(TipType.FlowText, string.format(GetGameText(luaID, 1), 0))
        return
    end
    --消耗判断
    if HelperL.IsLackGoods(expID2, expNum2, false) then
        return
    end

    m:CloseSelf()
    -- -- 进入战斗
    BattleManager:EnterBattle(cfg.ID)
end

return m
