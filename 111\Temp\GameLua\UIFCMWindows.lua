--[[
********************************************************************
    created:    2024/04/05
    author :    李锦剑
    purpose:    确认弹窗
*********************************************************************
--]]


---@class NotarizeWindowsDatq
---@field type NotarizeWindowsType 窗口类型
---@field content string 提示内容
---@field titleContent ?string 标题内容
---@field checkContent ?string 勾选框内容
---@field okCallback ?fun(okParam:any) 确定按钮回调
---@field cancelCallback ?fun(cancelParam:any) 取消按钮回调
---@field checkCallback ?fun(isOn:boolean, checkParam:any) 勾选按钮回调
---@field okParam ?any 确定按钮参数
---@field cancelParam ?any 取消按钮参数
---@field checkParam ?any 勾选按钮参数


local luaID = 'UIFCMWindows'

---确认弹窗
---@class UIFCMWindows:UIWndBase
local m = {}

--------------------------------------------------------------------
-- 初始化
--------------------------------------------------------------------
function m.OnCreate()
    m.width = m.objList.Rct_Content.sizeDelta.x
    m.height = m.objList.Rct_Content.sizeDelta.y
    m.Reset()
    m.RegisterClickEvent()
    return true
end

--------------------------------------------------------------------
-- 窗口开启
---@param data NotarizeWindowsDatq 窗口数据
--------------------------------------------------------------------
function m:OnOpen(data)
    if not data then
        m.OnClickClose()
        return
    end
    m.Reset()
    m.data = data
    m.UpdateView()
end

--------------------------------------------------------------------
-- 注册点击事件
--------------------------------------------------------------------
function m.RegisterClickEvent()
    m:AddClick(m.objList.Btn_OK, function()
        if type(m.data.okCallback) == 'function' then
            m.data.okCallback(m.data.okParam)
        end
        m.OnClickClose()
    end)
    m:AddClick(m.objList.Btn_Cancel, function()
        if type(m.data.cancelCallback) == 'function' then
            m.data.cancelCallback(m.data.cancelParam)
        end
        m.OnClickClose()
    end)
    m:AddClick(m.objList.Btn_Close, function()
        if type(m.data.cancelCallback) == 'function' then
            m.data.cancelCallback(m.data.cancelParam)
        end
        m.OnClickClose()
    end)
    m.objList.Tog_Check.onValueChanged:AddListener(function(ison)
        if type(m.data.checkCallback) == 'function' then
            m.data.checkCallback(ison, m.data.checkParam)
        end
    end)
end

--------------------------------------------------------------------
-- 更新界面
--------------------------------------------------------------------
function m.UpdateView()
    m.objList.Txt_Tip.gameObject:SetActive(false)
    m.objList.Img_FCM.gameObject:SetActive(false)
    if m.data.type == NotarizeWindowsType.Windows1 then
        m.objList.Btn_OK.gameObject:SetActive(true)
        m.objList.Btn_Cancel.gameObject:SetActive(true)
    elseif m.data.type == NotarizeWindowsType.Windows2 then
        m.objList.Btn_OK.gameObject:SetActive(true)
        m.objList.Btn_Cancel.gameObject:SetActive(true)
        m.objList.Tog_Check.gameObject:SetActive(true)
    elseif m.data.type == NotarizeWindowsType.Windows3 then
        m.objList.Btn_OK.gameObject:SetActive(true)
        if m.data.isFCM ~= nil and m.data.isFCM == true then
            m.objList.Btn_OK.gameObject:SetActive(false)
            m.objList.Txt_Tip.gameObject:SetActive(true)
            m.objList.Img_FCM.gameObject:SetActive(true)
            local curTime = HelperL.GetServerTime()
            m.objList.Txt_Time.text = os.date("%Y年-%m月-%d日 %H时:%M分:%S秒", curTime)
        end
    elseif m.data.type == NotarizeWindowsType.Windows4 then
        m.objList.Btn_OK.gameObject:SetActive(true)
        m.objList.Tog_Check.gameObject:SetActive(true)
    elseif m.data.type == NotarizeWindowsType.Windows5 then
        m.objList.Btn_OK.gameObject:SetActive(true)
        m.objList.Btn_Close.gameObject:SetActive(true)
    else
        m.objList.Btn_Close.gameObject:SetActive(true)
    end

    m.objList.Txt_Title.text = m.data.titleContent or GetGameText(luaID, 1)
    m.objList.Txt_Content.text = m.data.content or CommonTextID.NIL
    m.objList.Txt_Check.text = m.data.checkContent or GetGameText(luaID, 2)

    local height = 0
    if m.objList.Tog_Check.gameObject.activeSelf == false then
        height = height + 80
    end
    if m.objList.Btn_OK.gameObject.activeSelf == false and m.objList.Btn_Cancel.gameObject.activeSelf == false then
        height = height + 150
    end
    m.objList.Rct_Content.sizeDelta = Vector2(m.width, m.height + height)
end

--------------------------------------------------------------------
-- 重置界面
--------------------------------------------------------------------
function m.Reset()
    m.objList.Btn_OK.gameObject:SetActive(false)
    m.objList.Btn_Cancel.gameObject:SetActive(false)
    m.objList.Tog_Check.gameObject:SetActive(false)
    m.objList.Btn_Close.gameObject:SetActive(false)
end

--------------------------------------------------------------------
-- 关闭事件
--------------------------------------------------------------------
function m.OnClickClose()
    if m.data.isFCM ~= nil and m.data.isFCM == true then
        m:CloseSelf()
        LoginModule.Logout()
        return
    end
    m:CloseSelf()
end

return m
