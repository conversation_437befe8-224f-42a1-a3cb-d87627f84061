--[[
   created:	2016/10/11
   author :	吴德燊
   purpose:	任务listview的item
--]]
-- 已废弃
error('载入废弃文件 UITaskListItem')
--[[
	[ gameLuaID['UITaskListItem'] ] = 
	{
		[1] = '自动任务',
		[2] = '接受任务',
		[3] = '[83bbf4]进度 : [-][8cfe68]%d[-][83bbf4]/%d[-]',
		[4] = '立即前往',
		[5] = '组队副本',
		[6] = '单人副本',
		[7] = '组队',
		[8] = '多人副本',
		[9] = '完成任务',
		[10] = '已是最高星级！',
	},
	
local luaID = ('UITaskListItem')

function CreateUITaskListItemScript( obj, luaObj )
	local  o = {}
	o.branchID = 0
	o.taskID = 0
	o.step = 0
	o.gameObject = obj
      
	o.New = function (  )
		o.nameLabel = o.gameObject.transform:Find('Name'):GetComponent('Text')
		o.statusSprite = o.gameObject.transform:Find('StatusSpr'):GetComponent('Image')
		o.selectedObj = o.gameObject.transform:Find('Selected').gameObject
		o.award = o.gameObject.transform:Find('Award').gameObject
		o.describe = o.gameObject.transform:Find('Describe'):GetComponent('Text')
		o.button1 = o.gameObject.transform:Find('Button1').gameObject
		o.button1Sprite = o.button1:GetComponent('Image')
		o.button1Lab1 = o.gameObject.transform:Find('Button1/Label1'):GetComponent('Text')
		o.button1Lab2 = o.gameObject.transform:Find('Button1/Label2'):GetComponent('Text')
		o.autoDailyTaskToggle = o.gameObject.transform:Find('CheckBox1'):GetComponent('UIToggle')
		o.taskImg = o.gameObject.transform:Find('TaskImg/Sprite'):GetComponent('Image')
		o.star = o.gameObject.transform:Find('Star').gameObject
		local label = o.gameObject.transform:Find('CheckBox1/Label'):GetComponent('Text')
		label.text = GetGameText(luaID, 1)
		o.autoDailyTaskToggle.gameObject:SetActive(false)
		UIEventListener.Get(o.autoDailyTaskToggle.gameObject).onClick = o.AutoTaskToggleClick
	end -- new end
	o.SetData = function ( branchID, taskID )

		local iconList = {'rc-04','rc-05','rc-06','rc-07',}
		o.star:SetActive(false)
		o.award:SetActive(false)
		o.button1:SetActive(false)
		local hero = EntityModule.hero
		o.branchID = branchID
		o.taskID = taskID
		o.step = 1
		local taskPart = hero.heroTaskLC
		local taskItem = taskPart.GetItem(branchID, taskID )
		o.taskItem = taskItem
		o.status = taskItem:GetClientStatus()
		if taskItem then o.step = taskItem.step end
		local listTaskScheme = taskItem.taskScheme
		if not listTaskScheme then print('找不到任务ID为'..taskID..'step为'..o.step..'的配置') end
		if iconList[listTaskScheme.TaskImg] then
			o.taskImg.spriteName = iconList[listTaskScheme.TaskImg]
		else
			o.taskImg.spriteName = 'null'
		end
		o.taskImg:MakePixelPerfect()
		if listTaskScheme.ShowStar ~= 0 then
			o.star:SetActive(true)
			local count = listTaskScheme.Quality
			for i=1,5 do
				local go = o.gameObject.transform:Find('Star/UP/Star'..i).gameObject
				if i <= count then
					go:SetActive(true)
				else
					go:SetActive(false)
				end
			end
		end
		if o.branchID == 1 then
			o.autoDailyTaskToggle.gameObject:SetActive(taskItem.taskScheme.AutoNextStep == 1 and true or false)
			local heroTaskLC = EntityModule.hero.heroTaskLC 
			if heroTaskLC.autoDoLoopTask and EntityModule.hero.heroTaskLC.autoDoDailyTask == 4 then
				o.autoDailyTaskToggle.value = true
			else
				o.autoDailyTaskToggle.value = false
			end
		else
			o.autoDailyTaskToggle.gameObject:SetActive(false)
		end
		o.describe.text = listTaskScheme.Describe
		local prize = Schemes.PrizeTable.GetPrize(listTaskScheme.PrizeID, EntityModule.hero.propertyLC:GetProperty(CREATURE_FIELD.CREATURE_FIELD_LEVEL))
		local varietyScheme = Schemes.PrizeTable.GetPrizeVariety(prize)
		for i=1,3 do
			local prize = o.gameObject.transform:Find('Award/Prize'..i).gameObject
			local prizeVariety = o.gameObject.transform:Find('Award/Prize'..i..'/Variety').gameObject
			if varietyScheme[i] ~= nil then
				o.award:SetActive(true)
				local varietySpr = prizeVariety.transform:Find('Sprite'):GetComponent('Image')
				local varietyLab = prizeVariety.transform:Find('Label'):GetComponent('Text')
				prize:SetActive(true)
				if varietyScheme[i].ID == nil then
					prizeVariety:SetActive(true)
					varietySpr.spriteName = varietyScheme[i].icon
					varietyLab.text = varietyScheme[i].num
				else
					local schemes = nil
					if varietyScheme[i].ID >= DEFINE.MIN_EQUIPMENT_ID then
						schemes = Schemes.Equipment.Get(varietyScheme[i].ID)
					else
						schemes = Schemes.Medicament.Get(varietyScheme[i].ID)
					end
					prize:SetActive(false)
				end
			else
				prize:SetActive(false)
			end
		end
		local timeStr = ''
		o.statusSprite.gameObject:SetActive(false)
		o.selectedObj:SetActive(false)
		local taskScheme = Schemes.Task:Get( o.taskID,  o.step)
		--print(branchID,taskID,listTaskScheme.Name,o.status,taskScheme.ClickPointTo,	taskScheme.Type,"   ==================")
		if o.status == ECTaskStatus.CanAccept then
			o.nameLabel.text = string.format('%s',listTaskScheme.Name)
			if o.branchID == 0 or listTaskScheme.Style == 2 then
				o.button1:SetActive(true)
				o.button1Lab1.text = GetGameText(luaID, 2)
				o.button1Sprite.spriteName = 'CMbt01_1'
				UIEventListener.Get(o.button1).onClick = o.AcceptMission
				return
			end
			local branchScheme = GetThisTaskBranchScheme(taskItem)
			local count = branchScheme.GroupCount > 1 and string.format(GetGameText(luaID, 3),taskItem.round,branchScheme.GroupCount) or ''
			o.button1:SetActive(true)
			o.button1Lab1.text = GetGameText(luaID, 2)
			o.button1Sprite.spriteName = 'CMbt01_1'
			o.button1Lab2.text = count
			UIEventListener.Get(o.button1).onClick = o.AcceptMission		
		elseif o.status == ECTaskStatus.Doing then
			o.statusSprite.gameObject:SetActive(true)
			o.statusSprite.spriteName = 'rc-01'
			local branchScheme = GetThisTaskBranchScheme(taskItem)
			o.button1Lab2.text = string.format(GetGameText(luaID, 3),taskItem.round+1,branchScheme.GroupCount)
			o.nameLabel.text = string.format('%s',listTaskScheme.Name)
			
			if taskScheme.ClickPointTo == 0 or taskScheme.ClickPointTo == 4 then
                 
				if taskScheme.Type == TaskType.TaskType_TimePass then

					if not o.timer then
						o.timer = Timer.New(o.CountDown, 1, -1)
					end
					o.button1:SetActive(true)
					o.button1Lab1.text = ''
					o.button1Sprite.spriteName = 'CMbt03_0'	
					o.CountDown()
					if not o.timer.running then
						o.timer:Start()
					end
				else
					o.button1:SetActive(true)
					o.button1Lab1.text = GetGameText(luaID, 4)
					o.button1Sprite.spriteName = 'CMbt02_1'
					UIEventListener.Get(o.button1).onClick = o.GoToDestination
				end
			elseif taskScheme.ClickPointTo == 1 then
				o.button1:SetActive(true)
				o.button1Lab1.text = GetGameText(luaID, 5)
				o.button1Sprite.spriteName = 'CMbt02_1'
				UIEventListener.Get(o.button1).onClick = function ()
					UIManager.OpenTaskEctype(2)
					EventManager:Fire(EventID.EctypeTabItemCallback , 2)
				end
			elseif taskScheme.ClickPointTo == 2 then
				o.button1:SetActive(true)
				o.button1Lab1.text = GetGameText(luaID, 6)
				o.button1Sprite.spriteName = 'CMbt02_1'
				UIEventListener.Get(o.button1).onClick = function ()
					UIManager.OpenTaskEctype(1)
					EventManager:Fire(EventID.EctypeTabItemCallback , 1)
				end
			elseif taskScheme.ClickPointTo == 3 then
				o.button1:SetActive(true)
				o.button1Lab1.text = GetGameText(luaID, 7)
				o.button1Sprite.spriteName = 'CMbt02_1'
				UIEventListener.Get(o.button1).onClick = function ()
					UIManager.LoadUI('UITeam')
				end		
			elseif taskScheme.ClickPointTo == 5 then
				UIManager.LoadUI('UISocietyToPH')
			elseif taskScheme.ClickPointTo == 6 then
				UIManager.LoadUI('UISocietyToCJ')
			elseif taskScheme.ClickPointTo == 7 then
				UIManager.LoadUI('UISocietyToJS')
			elseif taskScheme.ClickPointTo == 26 then
				o.button1:SetActive(true)
				o.button1Lab1.text = GetGameText(luaID, 8)
				o.button1Sprite.spriteName = 'CMbt02_1'
				UIEventListener.Get(o.button1).onClick = function ()
					UIManager.OpenTaskEctype(2)
					UIManager.LoadUI('UIEctype')
				end
			elseif taskScheme.ClickPointTo == 42 then
				o.button1:SetActive(true)
				o.button1Lab1.text = GetGameText(luaID, 4)
				o.button1Sprite.spriteName = 'CMbt02_1'
				UIEventListener.Get(o.button1).onClick =function()
					UIManager.LoadUI('UIEquipmentForge')
					UIManager.SendWndMsg('UIEquipmentForge', UIWndMsg.UIEquipmentForge.jumpPagMsg, 'upStar')
				end
			elseif taskScheme.ClickPointTo == 43 then
				o.button1:SetActive(true)
				o.button1Lab1.text = GetGameText(luaID, 4)
				o.button1Sprite.spriteName = 'CMbt02_1'
				UIEventListener.Get(o.button1).onClick =function()
					UIManager.LoadUI('UIEquipmentForge')
					UIManager.SendWndMsg('UIEquipmentForge', UIWndMsg.UIEquipmentForge.jumpPagMsg, 'strength')
				end
			end

		elseif o.status == ECTaskStatus.CanComplete then
			o.button1Lab1.text = GetGameText(luaID, 9)
			o.button1Sprite.spriteName = 'CMbt02_1'
			o.statusSprite.gameObject:SetActive(true)
			o.statusSprite.spriteName = 'rc-02'
			UIEventListener.Get(o.button1).onClick = function ()
				if taskScheme.GiveNPC == "0" then --直接交任务
					taskPart.SendLuaTurnInTask( o.branchID, o.taskID, o.step )
				else
					o.taskItem:WantToTurnInTask() --去NPC处交任务
				end			
			end 
			o.button1:SetActive(true)
			local branchScheme = GetThisTaskBranchScheme(taskItem)
			o.button1Lab2.text = string.format(GetGameText(luaID, 3),taskItem.round+1,branchScheme.GroupCount)
			o.nameLabel.text = string.format('%s',listTaskScheme.Name)
		else

		end
	end 

	function o.GoToDestination(  )
		local taskPart = EntityModule.hero.heroTaskLC
		local taskScheme = Schemes.Task:Get( o.taskID,  o.step)
		if not taskScheme then error(string.format('任务配置ID%d错误',o.taskID)) end

		luaObj.OnClickBtnClose()--关闭界面
		if taskScheme.Type == TaskType.TaskType_ActorLevel then
			--UIManager.LoadUI('UIEctypeFrame')
		elseif taskScheme.Type == TaskType.TaskType_Escort and taskPart.onEscort == 0 then
			return
		else
			local taskScheme = Schemes.Task:Get(taskScheme.ID,taskScheme.Step)
			if taskScheme.ChapID ~= 0 then
				local chapScheme = Schemes.Chapter.Get(taskScheme.ChapID)
				if chapScheme == nil then return end
				if chapScheme.EntryCondition == 1 then
					EntityModule.hero.ectypeLC.taskSelectEctype = taskScheme.ChapID
					UIManager.OpenTaskEctype(1)
				elseif chapScheme.EntryCondition == 5 then
					EntityModule.hero.ectypeLC.selectedChallengeEctypeID = taskScheme.ChapID
					UIManager.OpenTaskEctype(2)
				end
				return
			end
			if taskScheme.ClickPointTo == 0 or taskScheme.ClickPointTo == 4 then
				o.taskItem:AfterAccept()
			elseif taskScheme.ClickPointTo == 1 then
				UIManager.OpenTaskEctype(2)
			elseif taskScheme.ClickPointTo == 2 then
				EntityModule.hero.ectypeLC.taskSelectEctype = taskScheme.ChapID
				EntityModule.hero.ectypeLC.curSelectTabIndex = 1
				UIManager.OpenTaskEctype(1)

			elseif taskScheme.ClickPointTo == 3 then
				UIManager.LoadUI('UITeam')
			end
		end

	end
    --任务倒计时
	function o.CountDown()
		local curScore = o.taskItem:GetTaskScore()
		local tmpScore = o.taskItem:GetTaskGoal() - curScore
		local hours = tmpScore / 3600
		local seconds = tmpScore % 3600
		local minuates = math.floor((seconds+1) / 60)
		if tmpScore <= 0 then
			EventManager:Fire(EventID.TaskPartUpdate)
			o.timer:Stop()
			return
		end
		seconds = seconds % 60
		local timeText =  string.format('%02d:%02d:%02d',hours,minuates, seconds)
		o.button1Lab1.text = string.format("[00ff00]%s",timeText)
	end

    --接受任务
	function o.AcceptMission(  )
		local taskPart = EntityModule.hero.heroTaskLC
		local taskID = o.taskID
		local step = o.step
		local taskScheme = Schemes.Task:Get(taskID, step)
		local taskItem = taskPart.GetItem(o.branchID,taskID)
		local canAccept, result = taskItem:CanAccept()
		if not canAccept then
			HelperL.AddAMessageTip(HelperL.GetResultCodeStr(result))
		end
		if taskScheme.GroupID==13 then
			--界面不关闭
		else
			luaObj.OnClickBtnClose()
		end
		taskItem:WantToAccept()		
	end

	--自动任务
	function o.AutoTaskToggleClick()
		if o.autoDailyTaskToggle.value == true then
			EntityModule.hero.heroTaskLC.SetAutoTask(o.taskItem)
			local status = o.taskItem:GetClientStatus()
			if status == ECTaskStatus.CanAccept then
				EntityModule.hero.heroTaskLC.SendLuaAcceptTask(o.taskItem.branchID, o.taskItem.taskID, o.taskItem.step)
			elseif status == ECTaskStatus.CanComplete then
				EntityModule.hero.heroTaskLC.SendLuaTurnInTask( o.taskItem.branchID, o.taskItem.taskID, o.taskItem.step)
			elseif status == ECTaskStatus.Accepted or status == ECTaskStatus.Doing then
				o.taskItem:AfterAccept()
			end
		else
			EntityModule.hero.heroTaskLC.SetFocusTask(nil) 
		end
	    luaObj.OnClickBtnClose()
	end
	function o.RefreshTaskStar()
		local taskID = o.taskID
		local step = o.step
		local taskScheme = Schemes.Task:Get(taskID, step)
		if taskScheme.Quality >= 5 then
			HelperL.AddAMessageTip(GetGameText(luaID, 10))
			return
		end
		local taskItem = EntityModule.hero.heroTaskLC.GetItem(o.branchID, o.taskID)
		local branchScheme = GetThisTaskBranchScheme(taskItem)
		EntityModule.hero.heroTaskLC.SendLuaRefreshTask(o.branchID, taskID, step, branchScheme)
		luaObj.isRefresh = true
	end
	luaObj.OnToggleChage = function ()
	
		if o.autoDailyTaskToggle.value == true then
		EntityModule.hero.heroTaskLC.autoDoDailyTask = UITask.curItem.taskItem.taskScheme.GroupID
		else
			EntityModule.hero.heroTaskLC.autoDoDailyTask = 0
		end
	end
	local luaMono = luaObj.gameObject:GetComponent('LuaMonoBehavior')
	luaMono:AddToggleChange(o.autoDailyTaskToggle, 'OnToggleChage')
	o.New()
	return o
end
]]