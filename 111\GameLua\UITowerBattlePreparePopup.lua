-- 塔防战备弹出UI
local luaID = ('UITowerBattlePreparePopup')

local UITowerBattlePreparePopup = dofile('UITowerBattlePrepare') 

-- 初始化
local baseCreateFunc = UITowerBattlePreparePopup.OnCreate
function UITowerBattlePreparePopup:OnCreate()
	if not baseCreateFunc(self) then
		return false
	end
	
	return true
end

-- 窗口开启
local baseOpenFunc = UITowerBattlePreparePopup.OnOpen
function UITowerBattlePreparePopup:OnOpen(stageID)
	baseOpenFunc(self, stageID)
end

-- 窗口关闭
local baseCloseFunc = UITowerBattlePreparePopup.OnClose
function UITowerBattlePreparePopup:OnClose()
	baseCloseFunc(self)
end

return UITowerBattlePreparePopup