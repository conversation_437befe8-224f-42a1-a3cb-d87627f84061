-- 登录UI
local luaID = ('UICardShow')

local UICardShow = {}

-- 初始化
function UICardShow:OnCreate()
	self.objList.Txt_Btn1.text = GetGameText(luaID, 1)
	self.objList.Txt_Btn2.text = GetGameText(luaID, 2)
	self.objList.Btn_Mask.onClick:AddListenerEx(self.OnClickMask)
	
	self.objList.Btn_Btn1.onClick:AddListenerEx(function () self:OnClickActionBtn() end)
	self.objList.Btn_Btn2.onClick:AddListenerEx(self.OnConfirmClearAD)
	self.goodsContent = self.objList.PrizeContent.transform
	self.prizeItem = self.objList.PrizeItem
	self.btnList = { self.objList.Btn_Mask, self.objList.Btn_Btn1}
	self.goodItemList = {}
	return true
end

-- 点击蒙板
function UICardShow.OnClickMask()
	local self = UICardShow
	self:CloseSelf()	
end

-- 点击行为按钮
function UICardShow:OnClickActionBtn()
	self:CloseSelf()
end

-- 窗口开启
function UICardShow:OnOpen(prizeContent)
	self.prizeContent = prizeContent
	self:CreateItems()	 
	self.objList.Btn_Btn2.gameObject:SetActive(false)--(not HelperL.IsNoAD())
end

function UICardShow.OnConfirmClearAD()
	local self = UICardShow
	self:CloseSelf()	
	UIManager:OpenWnd(WndID.ClearAD)
end


function UICardShow:CreateItems()
	for i, v in ipairs(self.goodItemList) do
		v.gameObject:SetActive(false)
	end
	if self.prizeContent then
		for k, v in ipairs(self.prizeContent) do
			local goodsID = v[1]
			local goodsNum = v[2]
			local goodsConfig = Schemes:GetGoodsConfig(goodsID)
			if goodsConfig then
				local goodItem = self.goodItemList[k]
				if not goodItem then
					goodItem = {}
					local obj = GameObject.Instantiate(self.prizeItem, self.goodsContent)
					local objTrans = obj:GetRectTransform()
					Helper.FillLuaComps(objTrans, goodItem)
					goodItem.gameObject = obj
					goodItem.gameObject.name = 'goodItem'
					goodItem.objTrans = objTrans
					table.insert(self.goodItemList, goodItem)
					goodItem.gameObject:SetActive(true)
				end
				AtlasManager:AsyncGetSprite(goodsConfig.IconID, goodItem.Img_ItemIcon)
				local colorStr = HelperL.GetQualityColor(goodsConfig.Quality)
				goodItem.Txt_ItemName.text = string.format('<color=#%s>%s X%s</color>', colorStr, goodsConfig.GoodsName, tostring(goodsNum))
			end
		end
	end
end

-- 窗口关闭
function UICardShow:OnClose()
	
end

-- 窗口销毁
function UICardShow:OnDestroy()
	-- 释放内存
	for i, btn in ipairs(self.btnList) do
		btn.onClick:RemoveAllListeners()
		btn.onClick:Invoke()
	end
end
return UICardShow