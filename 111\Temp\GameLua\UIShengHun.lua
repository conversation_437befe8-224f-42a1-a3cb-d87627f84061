--[[
********************************************************************
    created:	2024/06/14
    author :	李锦剑
    purpose:    龙之石
*********************************************************************
--]]

local luaID = "UIShengHun"
--显示天赋数量
local ShowTalentNum = 50
-----法宝其他配置
local EquipCfg = {
    [1] = { icon = "zjm_icon_7", pos = Vector2(0, 200), content = GetGameText(luaID, 2) },
    [2] = { icon = "zjm_icon_6", pos = Vector2(-480, -200), content = GetGameText(luaID, 3) },
    [3] = { icon = "zjm_icon_8", pos = Vector2(480, -200), content = GetGameText(luaID, 4) },
}

local poss = { Vector2(-771.7, 59), Vector2(-644.4, -138.6), Vector2(-662.5, -392.2), Vector2(-399.2, -228.3), Vector2(
-425, 80.5), Vector2(-156.3, 179.2), Vector2(-158.6, -121.5),
    Vector2(-40, -377), Vector2(190, -230), Vector2(62, -10), }

---角色法宝
---@class UIShengHun:UIWndBase
local m = {}

-- 订阅事件列表
function m.GetOpenEventList()
    return {
        [EventID.StoreList] = m.UpdateView,
        [EventID.LogicDataChange] = m.UpdateView,
        [EventID.OnGoodsPropChange] = m.UpdateView,
        [EventID.ActorDataChanged] = m.UpdateView,
    }
end

--------------------------------------------------------------------
-- 初始化
--------------------------------------------------------------------
function m.OnCreate()
    m.lastTime = 0
    ---@type Item_Treasured[]
    m.Item_Treasured_List = {}
    ---@type Item_Attribute[]
    m.Item_Attribute_List = {}

    local list = Schemes.EquipSmeltStarData:GetByOprateType(TreasuredTricks_OprateType) or {}
    -- for i, v in ipairs(EquipCfg) do
    --     print("list[i] ==== ",list[i])
    m.Item_Treasured_List[1] = m.Create_Item_Treasured(1, list[1])
    -- end
    m.RegisterClickEvent()
    return true
end

--------------------------------------------------------------------
-- 窗口开启
--------------------------------------------------------------------
function m:OnOpen()
    m.CloseInfo()
    m.SelectEquip(1)
    m.UpdateView()
    --m.ShowTalentInof(m.Item_Treasured_List[1].Obj_Type.Obj_Icon_List[1])
    -- for i = 1, #m.Item_Treasured_List[1].Obj_Type.Obj_Icon_List do
    --     m.ShowYiJieSuoTalentInof(m.Item_Treasured_List[1].Obj_Type.Obj_Icon_List[i],i)
    -- end
end

--------------------------------------------------------------------
-- 注册点击事件
--------------------------------------------------------------------
function m.RegisterClickEvent()
    m:AddClick(m.objList.Btn_Close, m.CloseUI)
    --m:AddClick(m.objList.Btn_Close2, m.CloseInfo)
    -- m:AddClick(m.objList.Btn_Upgrade, function()
    --     m.Upgrade(m.SmeltID)
    --     --m.objList.Obj_TalentInof.gameObject:SetActive(false)
    -- end)
    -- m:AddClick(m.objList.Btn_TalentInof, function()
    --     --m.objList.Obj_TalentInof.gameObject:SetActive(false)
    -- end)
    m:AddClick(m.objList.Btn_Help, function()
        SoundManager:PlaySound(7111)
        if m.PropId then
            UIManager:OpenWnd(WndID.RuleTip, Schemes.CommonProp:Get(m.PropId).Remark,"总属性")
        else
            UIManager:OpenWnd(WndID.RuleTip, "未激活属性")
        end
    end)
end

--------------------------------------------------------------------
-- 更新界面
--------------------------------------------------------------------
function m.UpdateView()
    --for i, v in ipairs(m.Item_Treasured_List) do
    m.Item_Treasured_List[1].UpdateView()
    --end

    -- local TotalRemark = ''
    -- if m.selectIndex then
    --     local smeltID = m.Item_Treasured_List[m.selectIndex].cfg.EquipSmeltStarId
    --     local starLvl = GamePlayerData.ActorEquipNew:GetEquipUpgradeLevel(smeltID)
    --     local equipSmeltStar = Schemes.EquipSmeltStar:GetEquipSmeltStarCfg(smeltID, starLvl)
    --     if equipSmeltStar then
    --         TotalRemark = Schemes.CommonProp:Get(equipSmeltStar.PropId).Remark
    --     end
    --     --m.objList.Txt_Hint.text = TotalRemark
    -- end
    -- m.objList.Txt_Attribute.text = TotalRemark
end

--------------------------------------------------------------------
--选择法宝
--------------------------------------------------------------------
function m.SelectEquip(index)
    local item
    if m.selectIndex then
        item = m.Item_Treasured_List[m.selectIndex]
        item.Obj_Type.gameObject:SetActive(false)
    end
    m.selectIndex = index
    item = m.Item_Treasured_List[index]
    item.Obj_Type.gameObject:SetActive(true)

    -- local level = EntityModule.hero:GetProperty(CREATURE_FIELD.CREATURE_FIELD_LEVEL)
    -- if level < item.cfg.ActivePremiss1 then
    --     HelperL.ShowMessage(TipType.FlowText, string.format(GetGameText(luaID, 5), item.cfg.ActivePremiss1))
    --     return
    -- end

    --m.objList.Txt_Title2.text = HelperL.Split(item.cfg.Name, ';')[1]

    m.UpdateView()
    --m.objList.Obj_Info.gameObject:SetActive(true)
    --m.objList.Grid_Treasured.gameObject:SetActive(false)
end

--------------------------------------------------------------------
--创角分页按钮
---@param index integer
---@param cfg EquipSmeltStarDataCfg
---@return Item_Treasured
--------------------------------------------------------------------
function m.Create_Item_Treasured(index, cfg)
    ---@class Item_Treasured
    local item = {}
    item.index = index
    item.cfg = cfg
    item.com = m:CreateSubItem(m.objList.Grid_Treasured, m.objList.Item_Treasured)
    -- m:AddClick(item.com.Btn_Click, function()
    --     m.SelectEquip(item.index)
    -- end)
    item.Obj_Type = m.Get_Obj_Type(item.cfg.Id)
    --item.Obj_Type.gameObject:SetActive(false)

    ---更新界面
    item.UpdateView = function()
        if item.cfg then
            -- local equipCfg = EquipCfg[item.index]
            -- item.com.transform:GetRectTransform().anchoredPosition = equipCfg.pos
            item.com.Txt_Content.text = item.cfg.Remark
            local strList = HelperL.Split(item.cfg.Name, ';')
            item.com.Txt_Content_1.text = strList[1]
            AtlasManager:AsyncGetSprite(strList[2], item.com.Img_Icon, true)
            item.com.Img_Red.gameObject:SetActive(false)
            item.com.Img_Lock.gameObject:SetActive(false)
            HelperL.SetImageGray(item.com.Img_Icon, false)
            HelperL.SetImageGray(item.com.Img_Bg2, false)
            local level = EntityModule.hero:GetProperty(CREATURE_FIELD.CREATURE_FIELD_LEVEL)
            if level >= item.cfg.ActivePremiss1 then
                item.com.Img_Red.gameObject:SetActive(RedDotCheckFunc:Check_UIShengHun(item.cfg.Id))
            else
                item.com.Txt_Unlock.text = string.format(GetGameText(luaID, 5), item.cfg.ActivePremiss1)
                item.com.Img_Lock.gameObject:SetActive(false)
                HelperL.SetImageGray(item.com.Img_Icon, true)
                HelperL.SetImageGray(item.com.Img_Bg2, true)
            end
            item.Obj_Type.UpdateData(item.cfg.EquipSmeltStarId)
            item.com.gameObject:SetActive(true)
        else
            item.com.gameObject:SetActive(false)
        end
    end
    return item
end

--------------------------------------------------------------------
--请求回调
--------------------------------------------------------------------
function m.RequestCallback(resultCode, content)
    if resultCode == RESULT_CODE_BASE.RESULT_COMMON_SUCCEED[1] then
        m.UpdateView()
        HelperL.PlayVFX()
        --  for i = 1, #m.Item_Treasured_List[1].Obj_Type.Obj_Icon_List do
        --     m.ShowYiJieSuoTalentInof(m.Item_Treasured_List[1].Obj_Type.Obj_Icon_List[i], i)
        -- end
        if  m.btnindex == 1 then
            HelperL.ShowMessage(TipType.FlowText, GetGameText(luaID, 11))
        else
            HelperL.ShowMessage(TipType.FlowText, GetGameText(luaID, 12))
        end
    else
        ResultCode:DefaultShowResultCode(resultCode, content)
    end
end

--------------------------------------------------------------------
--天赋升级
---@param smeltID integer 升星ID
--------------------------------------------------------------------
function m.Upgrade(smeltID)
    local curTime = os.clock()
    if curTime - m.lastTime < 0.3 then
        print('天赋激活点太快了1秒后尝试！')
        return
    end
    m.lastTime = curTime

    local starLvl = GamePlayerData.ActorEquipNew:GetEquipUpgradeLevel(smeltID)
    local equipSmeltStar = Schemes.EquipSmeltStar:GetEquipSmeltStarCfg(smeltID, starLvl)
    if equipSmeltStar == nil then
        warn("天赋激活:未找到升星配置 smeltID=", smeltID, starLvl)
        return
    end

    if HelperL.IsLackGoods(equipSmeltStar.CostGoodsID1, equipSmeltStar.CostGoodsID1Num, false) then return end
    if HelperL.IsLackGoods(equipSmeltStar.CostGoodsID2, equipSmeltStar.CostGoodsID2Num, false) then return end

    local form = {}
    form["smeltID"] = smeltID
    form["loopTimes"] = 1
    LuaModuleNew.SendRequest(LuaRequestID.NewCardAddExp2, form, m.RequestCallback)
end

--------------------------------------------------------------------
--获取装备图标
--------------------------------------------------------------------
function m.Create_Item_Icon(parent, index, parentIndex)
    ---@class Item_Obj_Icon
    local item = {}
    item.index = index
    item.parentIndex = parentIndex
    item.com = m:CreateSubItem(parent, m.objList.Item_Icon)
    local name = index .. parentIndex
    item.com.gameObject.name = index;
    --item.Img_JD = m.objList["Img_JD" .. name]
    --item.com.transform:GetRectTransform().anchoredPosition = poss[index]
    m:AddClick(item.com.Btn_Upgrade, function()
        m.btnindex = index
        m.Upgrade(item.SmeltID)
    end)
    -- if (index)%2 == 0 then
    --     item.com.Content.transform:GetRectTransform().anchoredPosition = Vector2.New(0, 40)
    -- end

    ---更新数据
    ---@param cfg  EquipSmeltStarCfg
    item.UpdateData = function(cfg)
        item.equipSme = cfg
        if cfg then
            -- 从CommonProp配置中读取Icon
            local commonProp = Schemes.CommonProp:Get(cfg.PropId)
            if commonProp and commonProp.Icon then
                AtlasManager:AsyncGetSprite(commonProp.Icon, item.com.Img_Icon)
            else
                -- 如果CommonProp中没有Icon配置，则使用原来的cfg.Icon作为备用
                AtlasManager:AsyncGetSprite(cfg.Icon, item.com.Img_Icon)
            end
            --设置默认状态
            HelperL.SetImageGray(item.com.Img_Icon, true)
            item.com.Img_RedDot1.gameObject:SetActive(false)
            item.com.Img_1.gameObject:SetActive(false)
            item.com.Img_2.gameObject:SetActive(false)
            item.com.Img_3.gameObject:SetActive(false)
            item.com.Img_Arrow1.gameObject:SetActive(false)
            item.com.Img_Arrow2.gameObject:SetActive(false)
            item.com.Btn_Upgrade.gameObject:SetActive(false)
            item.com.Txt_CanUpgrade.gameObject:SetActive(false)
            item.com.Txt_Unlock.gameObject:SetActive(false)

            local bool2 = HelperL.IsLackGoods(item.equipSme.CostGoodsID2, item.equipSme.CostGoodsID2Num, false, false)
            local num = SkepModule:GetGoodsCount(item.equipSme.CostGoodsID1)
            local color = (not bool1) and "#fff300" or UI_COLOR.Red
            item.com.Txt_Expend.text = string.format("<color=%s>%s/%s</color>", color,
                HelperL.GetChangeNum(num), HelperL.GetChangeNum(item.equipSme.CostGoodsID1Num))
            AtlasManager:AsyncGetGoodsSprite(item.equipSme.CostGoodsID1, item.com.Img_Expend)
            item.SmeltID = item.equipSme.SmeltID
            item.com.Txt_TalentName.text = item.equipSme.Name
            item.com.Txt_TalentContent.text =  Schemes.CommonProp:Get(item.equipSme.PropId).Name --Schemes.CommonProp:Get(item.equipSme.PropId).Remark

            local state = m.GetActiveState(item.equipSme)
            --已激活/可激活
            if state ~= 3 then
                --已激活
                if state == 1 then
                    item.com.Img_1.gameObject:SetActive(true)
                    HelperL.SetImageGray(item.com.Img_Icon, false)
                    item.com.Img_Arrow1.gameObject:SetActive(true)
                    local starLvl = GamePlayerData.ActorEquipNew:GetEquipUpgradeLevel(item.SmeltID)
                    if starLvl == 49 and index == 50 then
                        item.com.Txt_CanUpgrade.gameObject:SetActive(true)
                        item.com.Img_Arrow1.gameObject:SetActive(false)
                    end
                    m.PropId = item.equipSme.PropId
                else
                    --可激活
                    local bool1 = HelperL.IsLackGoods(
                        item.equipSme.CostGoodsID1, item.equipSme.CostGoodsID1Num, false, false)
                    local bool2 = HelperL.IsLackGoods(
                        item.equipSme.CostGoodsID2, item.equipSme.CostGoodsID2Num, false, false)
                    item.com.Img_RedDot1.gameObject:SetActive(not bool1 and not bool2)
                    --item.com.Txt_CanUpgrade.gameObject:SetActive(not bool1 and not bool2)
                    item.com.Img_3.gameObject:SetActive(true)
                    item.com.Btn_Upgrade.gameObject:SetActive(true)
                    item.com.Img_Arrow2.gameObject:SetActive(true)
                end
            else
                item.com.Img_2.gameObject:SetActive(true)
                item.com.Txt_Unlock.gameObject:SetActive(false)
                item.com.Img_Arrow2.gameObject:SetActive(true)
            end
            item.com.gameObject:SetActive(true)
        else
            item.com.gameObject:SetActive(false)
        end
    end
    return item
end

--------------------------------------------------------------------
--获取装备类型图
--------------------------------------------------------------------
function m.Get_Obj_Type(index)
    ---@class Item_Obj_Type
    local item = {}
    item.index = index
    item.gameObject = m.objList.ScrollView1.gameObject
    item.Grid_Type = m.objList.Grid_Content1
    ---@type Item_Obj_Icon[]
    item.Obj_Icon_List = {}
    for i = 1, ShowTalentNum, 1 do
        item.Obj_Icon_List[i] = m.Create_Item_Icon(item.Grid_Type, i, index)
    end

    ---更新数据
    ---@param smeltID integer 升星ID
    item.UpdateData = function(smeltID)
        local starLvl = GamePlayerData.ActorEquipNew:GetEquipUpgradeLevel(smeltID)
        local equipSmeltList = Schemes.EquipSmeltStar:GetBySmeltID(smeltID) or {}
        --local num = math.floor(starLvl / ShowTalentNum) * ShowTalentNum
        print("starLvl ===== ", starLvl)
        print("#equipSmeltList ===== ", #equipSmeltList)
        for i = 1, ShowTalentNum, 1 do
            item.Obj_Icon_List[i].UpdateData(equipSmeltList[i])
        end
    end
    return item
end

--------------------------------------------------------------------
--获取激活状态，1：已激活，2：可激活，3：条件未达成(先激活上一星)
---@param equipSme EquipSmeltStarCfg
--------------------------------------------------------------------
function m.GetActiveState(equipSme)
    if equipSme then
        -- local maxLevel = PropertyCompute.GetEquipSmeltStarMaxLevel(equipSme.SmeltID)
        local level = GamePlayerData.ActorEquipNew:GetEquipUpgradeLevel(equipSme.SmeltID)
        --已激活
        if equipSme.StarLvl < level then
            return 1
        end
        --可激活
        if equipSme.StarLvl == (level) then
            return 2
        end
    end
    --条件未达成(先激活上一星)
    return 3
end

--------------------------------------------------------------------
--显示天赋信息
---@param item Item_Obj_Icon
--------------------------------------------------------------------
function m.ShowTalentInof(item)
    --m.objList.Btn_Upgrade.gameObject:SetActive(false)
    --m.objList.Txt_Unlock.text = ''
    -- local state = m.GetActiveState(item.equipSme)
    -- local bool1 = HelperL.IsLackGoods(item.equipSme.CostGoodsID1, item.equipSme.CostGoodsID1Num, false, false)
    -- if state == 1 then
    --     m.objList.Txt_Unlock.text = GetGameText(luaID, 9)
    -- elseif state == 2 then

    --     m.objList.Btn_Upgrade.gameObject:SetActive(true)

    --     HelperL.SetImageGray(m.objList.Btn_Upgrade, bool1)
    -- else
    --     m.objList.Txt_Unlock.text = GetGameText(luaID, 10)
    -- end
    -- -- local bool2 = HelperL.IsLackGoods(item.equipSme.CostGoodsID2, item.equipSme.CostGoodsID2Num, false, false)
    -- local num = SkepModule:GetGoodsCount(item.equipSme.CostGoodsID1)
    -- local color = (not bool1) and "#fff300" or UI_COLOR.Red
    -- m.objList.Txt_Expend.text = string.format("<color=%s>%s/%s</color>", color,
    --     HelperL.GetChangeNum(num), HelperL.GetChangeNum(item.equipSme.CostGoodsID1Num))
    -- AtlasManager:AsyncGetGoodsSprite(item.equipSme.CostGoodsID1, m.objList.Img_Expend)
    -- m.SmeltID = item.equipSme.SmeltID
    -- m.objList.Txt_TalentName.text = item.equipSme.Name
    -- m.objList.Txt_TalentContent.text = Schemes.CommonProp:Get(item.equipSme.PropId).Remark
    --m.objList.Obj_TalentInof.gameObject:SetActive(true)
end

--显示已解锁的信息
function m.ShowYiJieSuoTalentInof(item, i)
    local state = m.GetActiveState(item.equipSme)
    if state == 1 or state == 2 then
        print("item.equipSme.SmeltID ===== ", item.equipSme.SmeltID)
        local smeltID = item.equipSme.SmeltID
        local starLvl = GamePlayerData.ActorEquipNew:GetEquipUpgradeLevel(smeltID)
        local equipSmeltStar = Schemes.EquipSmeltStar:GetEquipSmeltStarCfg(smeltID, starLvl)
        if equipSmeltStar then
            --m.objList.Txt_Hint.text = Schemes.CommonProp:Get(equipSmeltStar.PropId).Remark
        end
        --m.ShowTalentInof(m.Item_Treasured_List[1].Obj_Type.Obj_Icon_List[i])
    end
end

--------------------------------------------------------------------
-- 关闭信息界面
--------------------------------------------------------------------
function m.CloseInfo()
    --m.objList.Obj_Info.gameObject:SetActive(false)
    m.objList.Grid_Treasured.gameObject:SetActive(false)
    --m.objList.Obj_TalentInof.gameObject:SetActive(false)
end

--------------------------------------------------------------------
-- 关闭界面
--------------------------------------------------------------------
function m.CloseUI()
    m:CloseSelf()
end

return m
