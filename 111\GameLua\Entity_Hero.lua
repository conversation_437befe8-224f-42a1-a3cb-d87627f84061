---
--- Generated by <PERSON><PERSON><PERSON>(https://github.com/EmmyLua)
--- Created by Administrator.
--- DateTime: 2023/7/22 22:12
---

require("PropertyLC")
require("LogicLC")
require ("HeroTaskLC")
local EntityBase = require("EntityBase")

---@class HeroEntity:EntityBase
---@field _base EntityBase
local this = class(EntityBase)

function this:_init()
    this._base._init(self)
    self:Init()
end

---父
--- 设置数据
---@param d @英雄相关数据
function this:SetData(d)
    --- uid
    self.uid = d.EntityUID
    --- 名字
    self.name = d.ActorName
    --- 细胞创建时间
    self.ActorCreateTime = d.ActorCreateTime
    ---
    self.CountryOffice = d.CountryOffice
    --- 开服时间
    self.ServerOpenTime = d.ServerOpenTime
    --- 合服时间
    self.ServerMergeTime = d.ServerMergeTime
    --- 实体类型
    self.entity_class = ENTITY_CLASS.ENTITY_CLASS_PLAYER
    --- 所属区
    self.zoneID = d.ZoneID
    for k, v in ipairs(d.NumProp) do
        self:SetProperty(k - 1, v)
    end
    --- 添加属性组件
    self.propertyLC = PropertyLC.New()
    for k, v in ipairs(d.NumProp) do
        self.propertyLC:SetProperty(k - 1, v)
    end
    --- 玩家战力
    self.power = self:GetProperty(CREATURE_FIELD.CREATURE_FIELD_POWER)
    --- 宝石数据
    self.equipGemPartDataLC = EquipGemPartDataLC.New()
    for k,v in ipairs(d.EquipGemData.EquipGemList) do
        self.equipGemPartDataLC:Set(k,v)
    end
    self.equipGemPartDataLC:SetFlag(d.GemData.CreateFlag)
    --- 体魄数据
    self.divineWeaponDataLC = DivineWeaponDataLC.New()
    self.divineWeaponDataLC:Set(d.GodWeaponData.GodWeaponUID)
    ---
    self.equipGemPartDataLC:SetFlag(d.GemData.CreateFlag)
    --- 添加Logic组件
    self.logicLC = LogicLC.New()
    for _, v in ipairs(d.LogicData.DataList) do
        self.logicLC:Set(v.DataKey, v.Value)
    end
    -- 添加英雄任务
    self.heroTaskLC = HeroTaskLC.New(self)
    self.heroTaskLC:Build(d.TaskData)
end

return this