--[[
********************************************************************
    created:    2024/07/18
    author :    李锦剑
    purpose:    奖励提示
*********************************************************************
--]]

local luaID = ('UIRewardHint')

---奖励提示
---@class UIRewardHint:UIWndBase
local m = {}

--------------------------------------------------------------------
--创建时
--------------------------------------------------------------------
function m:OnCreate()
    ---提示框对象池
    ---@type Item_RewardHint[]
    m.rewardHintPool = {}
    m.objList.Power.gameObject:SetActive(false)
    m.timerPower = nil
    m.timerDefense2 = nil
    m.isPlay = false
    m.canvasGroup = m.objList.Power.gameObject:GetComponent('CanvasGroup')
    return true
end

--------------------------------------------------------------------
-- 窗口开启
--------------------------------------------------------------------
function m:OnOpen(hintType, ...)
    if hintType == UIRewardHint_Type.Message then
        m.ShowMessage(...)
    elseif hintType == UIRewardHint_Type.Power then
        m.PowerMessage(...)
    elseif hintType == UIRewardHint_Type.Defense then
        m.DefenseMessage(...)
    end
end

--------------------------------------------------------------------
---创建提示框
---@return Item_RewardHint
--------------------------------------------------------------------
function m.CreateRewardHint()
    if #m.rewardHintPool > 0 then
        local item = table.remove(m.rewardHintPool, #m.rewardHintPool)
        item.com.gameObject.transform:SetParent(m.objList.Grid_RewardHint.transform)
        return item
    end
    ---@class Item_RewardHint
    local item = {}
    item.com = m:CreateSubItem(m.objList.Grid_RewardHint, m.objList.Item_RewardHint)
    item.com.gameObject:SetActive(false)
    item.SetContent = function(content, duration)
        item.com.Txt_Content.text = content
        --获取大小
        local width = item.com.Txt_Content.preferredWidth
        local height = item.com.Txt_Content.preferredHeight
        item.com.transform:GetRectTransform().sizeDelta = Vector2(width + 40, height + 40)
        --渐变动画
        item.com.gameObject:SetActive(true)
        Timer.New(function()
            item.com.gameObject:SetActive(false)
            item.com.transform:SetParent(m.objList.Pool_RewardHint.transform)
            table.insert(m.rewardHintPool, item)
        end, 2.4, 1):Start()
    end
    return item
end

--------------------------------------------------------------------
---消息提示
---@param content string 消息内容
---@param duration? number 时间
--------------------------------------------------------------------
function m.ShowMessage(content, duration)
    if not content or content == '' then return end
    --创建提示框
    m.CreateRewardHint().SetContent(content, duration)
end

--------------------------------------------------------------------
---战力变化消息提示
---@param oldPower integer 旧战力
---@param newPower integer 新战力
--------------------------------------------------------------------
function m.PowerMessage(oldPower, newPower)
    --暂时屏蔽
    -- if m.timerPower2 then
    -- 	m.timerPower2:Stop()
    -- 	m.timerPower2 = nil
    -- end

    -- m.countDown = 20
    -- m.Init()
    -- m.oldPower = oldPower
    -- m.newPower = newPower
    -- local value = newPower - oldPower
    -- m.deltaPower = math.floor(value / 20)
    -- m.objList.Txt_Power1.text = '<color=#FFD029>' .. oldPower .. '</color>'
    -- m.objList.Txt_Power2.text = string.format('<color=#%s>%s</color>', value > 0 and '00FF00' or 'FF0000', value)
    -- AtlasManager:AsyncGetSprite(value > 0 and 'jiantou_1' or 'jiantou_2', m.objList.Img_Arrows)
    -- m.canvasGroup.alpha = 1
    -- m.objList.Power.gameObject:SetActive(true)

    -- if not m.timerPower then
    -- 	--播放战力数值变化动画
    -- 	m.timerPower = Timer.New(function()
    -- 		m.oldPower = m.oldPower + m.deltaPower
    -- 		m.objList.Txt_Power1.text = '<color=#FFD029>' .. m.oldPower .. '</color>'
    -- 		m.countDown = m.countDown - 1
    -- 		if m.countDown <= 0 then
    -- 			m.timerPower:Stop()
    -- 			m.timerPower = nil
    -- 			m.objList.Txt_Power1.text = '<color=#FFD029>' .. m.newPower .. '</color>'
    -- 			if not m.timerPower2 then
    -- 				m.timerPower2 = Timer.New(function()
    -- 					m.objList.Power.gameObject:SetActive(false)
    -- 				end, 1, 1)
    -- 				m.timerPower2:Start()
    -- 			end
    -- 		end
    -- 	end, 0.05, -1)
    -- 	m.timerPower:Start()
    -- end
end

--------------------------------------------------------------------
---防御变化消息提示
---@param oldDefense integer 旧防御
---@param newDefense integer 新防御
--------------------------------------------------------------------
function m.DefenseMessage(oldDefense, newDefense)
    --暂时屏蔽
    -- if m.timerDefense2 then
    -- 	m.timerDefense2:Stop()
    -- 	m.timerDefense2 = nil
    -- end
    -- m.countDown2 = 20

    -- m.Init()
    -- m.oldDefense = oldDefense
    -- m.newDefense = newDefense
    -- local value = newDefense - oldDefense
    -- m.deltaDefense = math.floor(value / 20)
    -- m.objList.Txt_Defense1.text = '<color=#FFD029>' .. oldDefense .. '</color>'
    -- m.objList.Txt_Defense2.text = string.format('<color=#%s>%s</color>', value > 0 and '00FF00' or 'FF0000', value)
    -- AtlasManager:AsyncGetSprite(value > 0 and 'jiantou_1' or 'jiantou_2', m.objList.Img_Arrows2)
    -- m.canvasGroup.alpha = 1
    -- m.objList.Defense.gameObject:SetActive(true)
    -- if not m.timerDefense then
    -- 	--播放防御数值变化动画
    -- 	m.timerDefense = Timer.New(function()
    -- 		m.oldDefense = m.oldDefense + m.deltaDefense
    -- 		m.objList.Txt_Defense1.text = '<color=#FFD029>' .. m.oldDefense .. '</color>'
    -- 		m.countDown2 = m.countDown2 - 1
    -- 		if m.countDown2 <= 0 then
    -- 			m.timerDefense:Stop()
    -- 			m.timerDefense = nil
    -- 			m.objList.Txt_Defense1.text = '<color=#FFD029>' .. m.newDefense .. '</color>'
    -- 			if not m.timerDefense2 then
    -- 				m.timerDefense2 = Timer.New(function()
    -- 					m.objList.Defense.gameObject:SetActive(false)
    -- 				end, 1, 1)
    -- 				m.timerDefense2:Start()
    -- 			end
    -- 		end
    -- 	end, 0.05, -1)
    -- 	m.timerDefense:Start()
    -- end
end

return m
