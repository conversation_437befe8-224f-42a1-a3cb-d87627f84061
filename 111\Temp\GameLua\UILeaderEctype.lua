--[[
********************************************************************
    created:	2024/06/09
    author :	gao
*********************************************************************
--]]
--祖玛洞窟
local luaID = "UILeaderEctype"
local FrontType = Leader_EctypeType
local maxNum = 5
---@class UILeaderEctype:UIWndBase
local m = {}

-- 订阅事件列表
function m.GetOpenEventList()
    return {
        [EventID.StoreList] = m.UpdateView,
        [EventID.LogicDataChange] = m.UpdateView,
        [EventID.OnGoodsPropChange] = m.UpdateView,
        [EventID.ActorDataChanged] = m.UpdateView,
    }
end

--------------------------------------------------------------------
-- 初始化
--------------------------------------------------------------------
function m.OnCreate()
    ---@type Item_Ectype[]
    m.Item_Ectype_List = {}
    m.goodsItemList = {}
    m.catMainStageIDList = {}
    m.list = Schemes.CatMainStage:GetByFrontType(FrontType)
    local index
    for i, v in ipairs(m.list) do
        index = math.ceil(i / maxNum)
        if not m.catMainStageIDList[index] then
            m.catMainStageIDList[index] = {}
        end
        table.insert(m.catMainStageIDList[index], v.ID)
    end

    for i = 1, 5 do
        m.CreationItem(i, m.objList["ItemEctype"..1])
    end

    --m.comResInfo = ComResInfo.New()
    --m.comResInfo.BindView(m.objList.Img_Res)

    m.RegisterClickEvent()
    return true
end

--------------------------------------------------------------------
-- 窗口开启
--------------------------------------------------------------------
function m:OnOpen()
    m.UpdateView()
end

--------------------------------------------------------------------
-- 注册点击事件
--------------------------------------------------------------------
function m.RegisterClickEvent()
    m.objList.Btn_Close.onClick:AddListenerEx(function()
        m.CloseUI()
    end)

    m.objList.Btn_Challenge.onClick:AddListenerEx(function()        
        local cfg = Schemes.CatMainStage:Get(m.StageID)
        UIManager:OpenWnd(WndID.EverydayEctype, FrontType)
    end)
end

--------------------------------------------------------------------
-- 更新界面
--------------------------------------------------------------------
function m.UpdateView()
    -- local maxStage = GamePlayerData.GameEctype:GetChallengeableID(FrontType)
    -- local index = math.ceil((maxStage % 10000) / 8)
    -- local list = m.catMainStageIDList[index]
    -- for i, v in ipairs(list) do
    --     m.Item_Ectype_List[i].UpdateData(v)        
    -- end

    local maxStage = GamePlayerData.GameEctype:GetProgress(FrontType)--GamePlayerData.GameEctype:GetChallengeableID(FrontType)
    if maxStage ~= 0 then
        maxStage = (maxStage) % 10000
        if maxStage >= #m.list then
            maxStage = #m.list
        end
    else
        maxStage = 0
    end
    print("maxStage ===== ",maxStage)
    local gapNum = math.floor(maxStage/5)
    local startPos = gapNum * 5 + 1
    local endPos = gapNum * 5 + 5
    
    if endPos > #m.list then
        endPos = #m.list
        startPos = endPos - 4
    end
    for i = startPos, endPos do
        local item = m.Item_Ectype_List[i - startPos + 1]
        item.UpdateData(m.list[i])
        -- if maxStage + 1 == i or maxStage == #m.list then
        --     m.RefreshView()
        -- end
    end

    -- for i = 1, 8 do
    --     m.Item_Ectype_List[i].UpdateData(list[i])        
    -- end

    local stageID = GamePlayerData.GameEctype:GetChallengeableID(FrontType)
    m.selectCatMainStageID = stageID
    local catMainStage = Schemes.CatMainStage:Get(stageID)

    local expendList = HelperL.Split(catMainStage.Need, ';')
    --消耗物品ID
    local expID = tonumber(expendList[1]) or 0
    --消耗物品数量
    local expNum = tonumber(expendList[2]) or 0
    --广告物品ID
    -- local adID = tonumber(expendList[3]) or 0
    --消耗物品ID2
    local expID2 = tonumber(expendList[4]) or 0
    --消耗物品数量2
    local expNum2 = tonumber(expendList[5]) or 0

    local count1 = SkepModule:GetGoodsCount(expID)
    local count2 = SkepModule:GetGoodsCount(expID2)
    m.objList.Img_ChallengeGray.gameObject:SetActive(count1 < expNum)
    local color2 = count2 < expNum2 and "#FFA500" or "#ffffff"
    m.objList.Txt_Expend.text = string.format("<color=%s>%s/%s</color>", color2, count2, expNum2)
    AtlasManager:AsyncGetGoodsSprite(expID2, m.objList.Img_Expend)

    m.objList.Txt_Hint.text = catMainStage.Desc2

    m.objList.Txt_Num.text = string.format(GetGameText(luaID, 4), math.floor(count1 / expNum))

    local mStage = GamePlayerData.GameEctype:GetProgress(FrontType)
    if mStage%100 == #m.list then
        m.objList.Txt_TG.gameObject:SetActive(true)
        m.objList.Btn_Challenge.gameObject:SetActive(false) 
        m.objList.Txt_Num.gameObject:SetActive(false) 
    else
        m.objList.Txt_TG.gameObject:SetActive(false) 
        m.objList.Btn_Challenge.gameObject:SetActive(true) 
        m.objList.Txt_Num.gameObject:SetActive(true) 
    end

    --奖励
    local prizeGoods = Schemes.CatMainStage:GetSucceedRewards(stageID)
    if prizeGoods then
        for i, v in ipairs(prizeGoods) do
            if not m.goodsItemList[i] then
                m.goodsItemList[i] = _GAddSlotItem(m.objList.Grid_Goods)
            end
            if v then
                m.goodsItemList[i]:SetItemID(v.ID)
                m.goodsItemList[i]:SetCount(v.Num)
                m.goodsItemList[i].gameObject:SetActive(true)
            else
                m.goodsItemList[i].gameObject:SetActive(false)
            end
        end
    end
end

--------------------------------------------------------------------
-- 创建副本界面
--------------------------------------------------------------------
function m.CreationItem(index, view)
    ---@class Item_Ectype
    local item = {}
    item.stageID = nil
    item.index = index
    --item.com = {}
    item.com = m:CreateSubItem(m.objList.Content, view)
    --Helper.FillLuaComps(view.gameObject.transform, item.com)
    -- item.com.Btn_Challenge.onClick:AddListenerEx(function()
    --     local cfg = Schemes.CatMainStage:Get(item.stageID)
    --     UIManager:OpenWnd(WndID.EverydayEctype, cfg.FrontType)
    -- end)
    -- if item.com.Btn_Challenge1 ~= nil then
    --     item.com.Btn_Challenge1.onClick:AddListenerEx(function()
    --         local cfg = Schemes.CatMainStage:Get(item.stageID)
    --         UIManager:OpenWnd(WndID.EverydayEctype, cfg.FrontType)
    --     end)
    -- end
    -- item.com.Btn_Lock.onClick:AddListenerEx(function()
    --     HelperL.ShowMessage(TipType.FlowText, string.format(GetGameText(luaID, 2), (item.stageID % 1000) - 1))
    -- end)
    
    item.UpdateData = function(cfg)
        if cfg then
            item.com.Img_Star1.gameObject:SetActive(false)
            item.com.Img_Star2.gameObject:SetActive(false)
            item.com.Img_Star3.gameObject:SetActive(false)
            item.com.Img_1.gameObject:SetActive(false)
            item.com.Img_2.gameObject:SetActive(false)
            item.com.Img_3.gameObject:SetActive(false)
            item.com.Txt_YGT.gameObject:SetActive(false)
            item.com.Txt_Ranking.gameObject:SetActive(true)

            local level = EntityModule.hero:GetProperty(CREATURE_FIELD.CREATURE_FIELD_LEVEL)
            local maxStage = GamePlayerData.GameEctype:GetProgress(FrontType)
            local color = "#CCCCCC"
            --挑战条件未达成
            if level < cfg.EndPram2 then
                -- item.com.Btn_Lock.gameObject:SetActive(true)
                -- item.com.Txt_Condition.text = string.format(GetGameText(luaID, 1), cfg.EndPram2)
                item.com.Img_3.gameObject:SetActive(true)
                item.com.Txt_Normal.gameObject:SetActive(false)
            else
                --已通关
                if maxStage >= cfg.ID then 
                    item.com.Txt_YGT.gameObject:SetActive(true)
                    item.com.Img_2.gameObject:SetActive(true)
                    item.com.Txt_Normal.gameObject:SetActive(false)
                    item.com.Txt_Ranking.gameObject:SetActive(false)
                    -- item.com.Img_OK.gameObject:SetActive(true)
                    -- item.com.Img_JD.gameObject:SetActive(true)
                elseif (maxStage == 0 and item.index == 1) or (maxStage + 1) == cfg.ID then
                    --item.com.Btn_Challenge.gameObject:SetActive(true)
                    item.com.Img_1.gameObject:SetActive(true)
                    m.objList.Txt_Title.text = cfg.Name..cfg.Desc
                    -- item.com.Img_RedDot.gameObject:SetActive(count1 >= expNum)
                    color = "#FFFFFF"
                    item.com.Txt_Normal.gameObject:SetActive(true)
                    m.StageID = cfg.ID
                else
                    -- item.com.Btn_Lock.gameObject:SetActive(true)
                    item.com.Img_3.gameObject:SetActive(true)
                    item.com.Txt_Normal.gameObject:SetActive(false)
                    -- item.com.Txt_Condition.text = string.format(GetGameText(luaID, 2), (stageID % 1000) - 1)
                end
            end
            item.com.Txt_Ranking.text = cfg.Name
        else
            item.com.gameObject:SetActive(false)
        end
    end
    m.Item_Ectype_List[index] = item
end

--------------------------------------------------------------------
-- 关闭界面
--------------------------------------------------------------------
function m.CloseUI()
    m:CloseSelf()
end

return m


