﻿local luaID = ('UISevenDayInvestment')

---@class UISevenDayInvestment: UIWndBase
local m = {}

-- 订阅事件列表
function m.GetOpenEventList()
    return {
        [EventID.LogicDataChange] = m.UpdateView,
        [EventID.EntitySyncBuff] = m.UpdateView,
        --[EventID.OnHeroPropChange] = m.UpdateView,
    }
end

--------------------------------------------------------------------
--预制体自适应
--------------------------------------------------------------------
function m.AdaptScale()
    -- HelperL.AdaptScale(m.objList.Img_Icon1, 6)
    HelperL.AdaptScale_Width(m.objList.Item_Invest)
end

--------------------------------------------------------------------
-- 初始化
--------------------------------------------------------------------
function m.OnCreate()
    m.objList.Txt_Get1.text = GetGameText(luaID, 16)

    m.invsetConfig1 = Schemes.SevenDaysInvest:Get(1)
    m.invsetConfig2 = Schemes.SevenDaysInvest:Get(2)
    m.invsetConfig3 = Schemes.SevenDaysInvest:Get(3)

    m.adID2 = Schemes.RechargeCard:Get(m.invsetConfig2.CardID).Description
    m.adID3 = Schemes.RechargeCard:Get(m.invsetConfig3.CardID).Description


    m.NeedLevelList = m.invsetConfig1.NeedLevel
    m.InvestItemList = {}

    for i = 1, 7, 1 do
        m.InvestItemList[i] = m.CreateInvest(i)
    end

    m.RegisterClickEvent()
    return true
end

-- 窗口开启
function m.OnOpen()
    m.UpdateView()
    m.AddWndRedDots()
end

-- 更新界面
function m.UpdateView()
    for i, v in ipairs(m.InvestItemList) do
        v.UpdateView()
    end

    m.objList.Btn_Get1.gameObject:SetActive(false)
    m.objList.Btn_Get2.gameObject:SetActive(false)
    m.objList.Btn_Get3.gameObject:SetActive(false)
    m.objList.Btn_Buy1.gameObject:SetActive(false)
    m.objList.Btn_Buy2.gameObject:SetActive(false)
    m.objList.Btn_Buy3.gameObject:SetActive(false)
    m.objList.Btn_Grey1.gameObject:SetActive(false)
    m.objList.Btn_Grey2.gameObject:SetActive(false)
    m.objList.Btn_Grey3.gameObject:SetActive(false)

    local lv = m.GetState(m.invsetConfig1, m.GetCurDay(m.invsetConfig1))
    local investTimes = HeroDataManager:GetLogicData(m.invsetConfig1.TimeSaveID)

    if investTimes == 0 then
        m.objList.Btn_Buy1.gameObject:SetActive(true)
    else
        if lv == 3 then
            m.objList.Btn_Grey1.gameObject:SetActive(true)
        else
            m.objList.Btn_Get1.gameObject:SetActive(true)
        end
    end

    investTimes = HeroDataManager:GetLogicData(m.invsetConfig2.TimeSaveID)

    lv = m.GetState(m.invsetConfig2, m.GetCurDay(m.invsetConfig2))
    if lv == 3 then
        m.objList.Btn_Grey2.gameObject:SetActive(true)
        m.objList.Txt_Grey2.text = CommonTextID.IS_GET
    else
        if investTimes ~= 0 then
            m.objList.Txt_Get2.text = CommonTextID.GET
            m.objList.Btn_Get2.gameObject:SetActive(true)
        else
            local card = Schemes.RechargeCard:Get(m.invsetConfig2.CardID)
            local price = string.format(GetGameText(luaID, 15), card.FirstRMB / 100)
            m.objList.Btn_Buy2.gameObject:SetActive(true)
            m.objList.Txt_Buy2.text = price
        end
    end

    investTimes = HeroDataManager:GetLogicData(m.invsetConfig3.TimeSaveID)

    lv = m.GetState(m.invsetConfig3, m.GetCurDay(m.invsetConfig3))
    if lv == 3 then
        m.objList.Btn_Grey3.gameObject:SetActive(true)
        m.objList.Txt_Grey3.text = CommonTextID.IS_GET
    else
        if investTimes ~= 0 then
            m.objList.Btn_Get3.gameObject:SetActive(true)
            m.objList.Txt_Get3.text = CommonTextID.GET
        else
            local card = Schemes.RechargeCard:Get(m.invsetConfig3.CardID)
            local price = string.format(GetGameText(luaID, 15), card.FirstRMB / 100)
            m.objList.Btn_Buy3.gameObject:SetActive(true)
            m.objList.Txt_Buy3.text = price
        end
    end
end

-- 请求扣除元宝(opType:1.投资; 2.领取奖励)
function m.InvestOperation(id, opType)
    if not id or id == 0 then return end
    LuaModule.RunLuaRequest(string.format('LuaRequestSevenDaysInvest?id=%d&opType=%d', id, opType),
        function(resultCode, content)
            if resultCode ~= RESULT_CODE.RESULT_COMMON_SUCCEED then
                HelperL.ShowMessage(TipType.FlowText, ResultCode:GetResultCodeStr(resultCode, content))
            else
                m.UpdateView()
            end
        end
    )
end

-- 注册点击事件
function m.RegisterClickEvent()
    m.objList.Btn_Close.onClick:AddListenerEx(function()
        m:CloseSelf()
    end)

    -------------领取奖励-------------
    m.objList.Btn_Get1.onClick:AddListenerEx(function()
        m.InvestOperation(m.invsetConfig1.ID, 2)
    end)
    m.objList.Btn_Get2.onClick:AddListenerEx(function()
        m.InvestOperation(m.invsetConfig2.ID, 2)
    end)
    m.objList.Btn_Get3.onClick:AddListenerEx(function()
        m.InvestOperation(m.invsetConfig3.ID, 2)
    end)

    -------------购买-------------
    m.objList.Btn_Buy1.onClick:AddListenerEx(function()
        m.InvestOperation(m.invsetConfig1.ID, 1)
    end)
    m.objList.Btn_Buy2.onClick:AddListenerEx(function()
        HelperL.Recharge(m.invsetConfig2.CardID)
    end)
    m.objList.Btn_Buy3.onClick:AddListenerEx(function()
        HelperL.Recharge(m.invsetConfig3.CardID)
    end)
end

-- 创建奖励列表
function m.CreateInvest(index)
    local item = {}
    local prizeID1 = m.invsetConfig1.PrizeID[index]
    local prizeID2 = m.invsetConfig2.PrizeID[index]
    local prizeID3 = m.invsetConfig3.PrizeID[index]
    item.index = index
    item.objList = m:CreateSubItem(m.objList.Grid_Invest, m.objList.Item_Invest)
    item.objList.Txt_Level.text = m.NeedLevelList[index]
    m.CreateSingleGoods(item.objList.Award1, prizeID1)
    m.CreateSingleGoods(item.objList.Award2, prizeID2)
    m.CreateSingleGoods(item.objList.Award3, prizeID3)
    item.UpdateView = function()
        local state1 = m.GetState(m.invsetConfig1, item.index)
        local state2 = m.GetState(m.invsetConfig2, item.index)
        local state3 = m.GetState(m.invsetConfig3, item.index)

        item.objList.Img_Title1.gameObject:SetActive(state1 ~= 2)
        item.objList.Img_Title2.gameObject:SetActive(state2 ~= 2)
        item.objList.Img_Title3.gameObject:SetActive(state3 ~= 2)

        item.objList.Img_Lock1.gameObject:SetActive(state1 == 2)
        --item.objList.Img_Lock2.gameObject:SetActive(state2 == 2)
        item.objList.Img_Lock3.gameObject:SetActive(state3 == 2)

        item.objList.Txt_Title1.text = GetGameText(luaID, state1 == 1 and 5 or 6)
        item.objList.Txt_Title2.text = GetGameText(luaID, state2 == 1 and 5 or 6)
        item.objList.Txt_Title3.text = GetGameText(luaID, state3 == 1 and 5 or 6)
    end
    return item
end

-- 获取领取状态，1可领取，2不可领取，3已领取
function m.GetState(cfg, index)
    if HeroDataManager:GetLogicBit(cfg.GetSaveID, cfg.StartIndex + index - 1) ~= 0 then
        return 3
    end
    local curDay = m.GetCurDay(cfg)
    if curDay < index then
        return 2
    end
    return 1
end

-- 获取天数
function m.GetCurDay(config)
    local curTime = HelperL.GetServerTime()
    if not config then return 0 end
    local investTimes = HeroDataManager:GetLogicData(config.TimeSaveID)
    if investTimes == 0 then return 0 end
    local curDay = HelperL.CalculationIntervalDays(investTimes, curTime)
    if curDay > 7 then
        curDay = 7
    end
    if curDay <= 0 then
        curDay = 1
    end
    return curDay
end

-- 创建奖励商品
function m.CreateSingleGoods(parent, prizeID)
    local prizeList = Schemes.PrizeTable:GetGoodsList(prizeID)
    if not prizeList then return end
    local item = _GAddSlotItem(parent)
    item:SetItemID(prizeList[1].id)
    item:SetCount(prizeList[1].num)
    item:SetBGSize(130,130)
    item:SetIconSize(80,80)
end

-- 添加界面红点
function m.AddWndRedDots()
    local redDot = m:SetWndRedDot(m.objList.Btn_Get1)
    if redDot then
        redDot:AddCheckParam(WndID.SevenDayInvestment, 1)
    end

    redDot = m:SetWndRedDot(m.objList.Btn_Get2)
    if redDot then
        redDot:AddCheckParam(WndID.SevenDayInvestment, 2)
    end

    redDot = m:SetWndRedDot(m.objList.Btn_Get3)
    if redDot then
        redDot:AddCheckParam(WndID.SevenDayInvestment, 3)
    end

    redDot = m:SetWndRedDot(m.objList.Btn_Buy1)
    if redDot then
        redDot:AddCheckParam(WndID.SevenDayInvestment, 1)
    end
end

return m
