--[[
********************************************************************
    created:	2024/06/09
    author :	李锦剑
    purpose:   爬塔-副本
*********************************************************************
--]]

local luaID = "UIPaTaFuBen"
local FrontType = PaTaFuBen_EctypeType
local maxNum = 2
---@class UIPaTaFuBen:UIWndBase
local m = {}

--------------------------------------------------------------------
-- 订阅事件列表
--------------------------------------------------------------------
function m.Get<PERSON>penEventList()
    return {
        [EventID.LogicDataChange] = m.UpdateView,
        [EventID.EntitySyncBuff] = m.UpdateView,
        [EventID.OnGoodsPropChange] = m.UpdateView,
        [EventID.OnHeroPropChange] = m.UpdateView,
        [EventID.ActorDataChanged] = m.UpdateView,
    }
end

--------------------------------------------------------------------
-- 初始化
--------------------------------------------------------------------
function m.OnCreate()
    m.goodsItemList = {}
    m.gameEctypeBox_ItemList = {}
    m.RegisterClickEvent()
    m.StageList = Schemes.CatMainStage:GetByFrontType(FrontType)
    -- m.objList.Img_Select.transform:DOLocalRotate(
    --     m.objList.Img_Select.transform.eulerAngles + Vector3(0, 0, -360), 2,
    --     DG.Tweening.RotateMode.FastBeyond360
    -- ):SetEase(TweeningEase.Linear, 1):SetLoops(-1, TweeningLoopType.Restart)
    m.objList.Img_bjtb_01.gameObject.transform.localScale = Vector3.one;

    for i = 1, 3 do
        if not m.gameEctypeBox_ItemList[i] then
            m.gameEctypeBox_ItemList[i] = m.Creation(i)
        end
        m.gameEctypeBox_ItemList[i].UpdateViewItem(i)
    end
    return true
end

--------------------------------------------------------------------
-- 窗口开启
--------------------------------------------------------------------
function m:OnOpen()
    m.UpdateView()
end

--------------------------------------------------------------------
-- 注册点击事件
--------------------------------------------------------------------
function m.RegisterClickEvent()
    m.objList.Btn_Close.onClick:AddListenerEx(function()
        m.CloseUI()
    end)
end

--------------------------------------------------------------------
-- 更新界面
--------------------------------------------------------------------
function m.UpdateView()
    m.stageID = GamePlayerData.GameEctype:GetChallengeableID(FrontType)
    m.stageID = m.stageID % 1000
    m.objList.Txt_Level.text = string.format(GetGameText(luaID, 1), ToChineseNumbers(m.stageID))
    -- if m.stageID + 2 > #m.StageList then
    --     m.stageID = #m.StageList - 2
    --     --     if m.stageID % 1000 == #m.StageList - 1 then
    --     --         m.objList.Obj_Select.gameObject.transform.localPosition = Vector3(278,-372,0);
    --     --     elseif m.stageID % 1000 == #m.StageList then
    --     --         m.objList.Obj_Select.gameObject.transform.localPosition = Vector3(278,-110,0);
    --     --     end
    --     -- else
    --     --     m.objList.Obj_Select.gameObject.transform.localPosition = Vector3(271,-611,0);
    -- end


    -- local stageID = m.stageID % 1000
    -- m.objList.Txt_Level.text = string.format(GetGameText(luaID, 1), ToChineseNumbers(stageID))
    -- m.objList.Txt_Ranking.text = string.format(GetGameText(luaID, 1), ToChineseNumbers(stageID))
    -- for i = 1, maxNum, 1 do
    --     m.objList["Txt_Ranking" .. i].text = string.format(GetGameText(luaID, 1), ToChineseNumbers(stageID + i))
    -- end

    -- m.objList.Txt_Desc.text = cfg.Desc2
    for i = 1, #m.gameEctypeBox_ItemList do
        m.gameEctypeBox_ItemList[i].UpdateViewItem(i)
    end
    local mStage = GamePlayerData.GameEctype:GetProgress(FrontType)
    if mStage % 1000 == #m.StageList then
        m.objList.Txt_TG.gameObject:SetActive(true)
    else
        m.objList.Txt_TG.gameObject:SetActive(false)
    end
end

function m.Creation(index)
    local item = {}
    item.index = index
    item.objList = m:CreateSubItem(m.objList.Grid_, m.objList.Obj_Ranking)
    --更新界面
    item.UpdateViewItem = function(index)
        -- 当前通过的最大关卡
        local maxStage = GamePlayerData.GameEctype:GetProgress(FrontType)
        if maxStage ~= 0 then
            maxStage = (maxStage) % 10000
            if maxStage >= #m.StageList then
                maxStage = #m.StageList
            end
        else
            maxStage = 0
        end
        local pagesize = 3
        local gapNum = math.floor(maxStage/pagesize)
        local startPos = gapNum * pagesize + 1
        local endPos = gapNum * pagesize + pagesize
        
        if endPos > #m.StageList then
            endPos = #m.StageList
            startPos = endPos - pagesize - 1
        end
        item.stageId = startPos + index - 1 + 140000
        --创建奖励
        local prizeGoods = Schemes.CatMainStage:GetSucceedRewards(item.stageId)
        if prizeGoods then
            m.goodsItemList[index] = {}
            for c = item.objList.Grid_Goods.transform.childCount - 1, 0, -1 do
                if item.objList.Grid_Goods.transform:GetChild(c) then
                    GameObject.Destroy(item.objList.Grid_Goods.transform:GetChild(c).gameObject)
                end
            end
            for i, v in ipairs(prizeGoods) do
                if not m.goodsItemList[index][i] then
                    m.goodsItemList[index][i] = _GAddSlotItem(item.objList.Grid_Goods)
                end
                if v then
                    m.goodsItemList[index][i]:SetItemID(v.ID)
                    m.goodsItemList[index][i]:SetCount(v.Num)
                    m.goodsItemList[index][i].gameObject:SetActive(true)
                else
                    m.goodsItemList[index][i].gameObject:SetActive(false)
                end
            end
        end

        item.objList.Txt_Ranking.text = string.format(GetGameText(luaID, 1), (item.stageId - 140000))
        local cfg = Schemes.CatMainStage:Get(item.stageId)
        local expendList = HelperL.Split(cfg.Need, ';')
        --消耗物品ID
        local expID = tonumber(expendList[1]) or 0
        --消耗物品数量
        local expNum = tonumber(expendList[2]) or 0
        --广告物品ID
        -- local adID = tonumber(expendList[3]) or 0
        local count1 = SkepModule:GetGoodsCount(expID) or 0
        -- local count2 = SkepModule:GetGoodsCount(adID) or 0
        m.objList.Txt_Content.text = cfg.Desc2
        if expID > 0 and expNum > 0 then
            item.objList.Txt_Expend.text = tostring(count1)
            item.objList.Txt_Expend.gameObject:SetActive(true)
            AtlasManager:AsyncGetGoodsSprite(expID, item.objList.Img_Expend)
        else
            item.objList.Txt_Expend.gameObject:SetActive(false)
        end

        HelperL.SetImageGray(item.objList.Btn_Challenge, count1 < expNum)
        item.objList.Img_RedDot.gameObject:SetActive(count1 >= expNum)

        item.objList.Img_Expend.gameObject:SetActive(false)
        item.objList.Btn_Challenge.onClick:RemoveAllListeners()
        local mStage = GamePlayerData.GameEctype:GetChallengeableID(FrontType)
        if item.stageId == mStage then
            item.objList.Btn_Challenge.gameObject:SetActive(true)
            item.objList.Txt_Challenge.text = GetGameText(luaID, 3)
            item.objList.Img_Expend.gameObject:SetActive(true)
            item.objList.Btn_Challenge.onClick:AddListenerEx(function()
                m.ChuZheng(item.stageId)
            end)
        else
            item.objList.Txt_Challenge.text = GetGameText(luaID, 3)
            item.objList.Img_RedDot.gameObject:SetActive(false)
            if item.stageId < mStage then
                item.objList.Txt_Challenge.text = GetGameText(luaID, 2)
            elseif item.stageId > mStage then
                HelperL.SetImageGray(item.objList.Btn_Challenge, true)
            end
        end
    end
    return item
end

--------------------------------------------------------------------
---出征
---@param stageID integer 副本ID
--------------------------------------------------------------------
function m.ChuZheng(stageID)
    local cfg = Schemes.CatMainStage:Get(stageID)
    if not cfg then return end
    local expendList = HelperL.Split(cfg.Need, ';')
    local id1 = tonumber(expendList[1]) or 0
    local num1 = tonumber(expendList[2]) or 0
    if HelperL.IsLackGoods(id1, num1) then return end
    --m.CloseUI()
    BattleManager:EnterBattle(stageID)
end

--------------------------------------------------------------------
-- 关闭界面
--------------------------------------------------------------------
function m.CloseUI()
    m:CloseSelf()
end

return m
