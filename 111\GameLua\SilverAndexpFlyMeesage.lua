--[[
********************************************************************
	created:	2017/3/29
	author :    胡杰灵
	purpose:	银两、经验飘字 - V13.0版本震撼动画升级
*********************************************************************
--]]
local luaID = ('SilverAndexpFlyMeesage')
SilverExpMessageParamCache = SilverExpMessageParamCache or {}


local strCache = {}
local index = 0


local function RunSilverExpMessage(name, value)
    local str = strCache
    --判断是增加数值还是减少数值
    if value > 0 then
        str = string.format(GetGameText(luaID, 13), "+" .. name .. " " .. value)
    else
        str = string.format(GetGameText(luaID, 14), "-" .. name .. " " .. -value)
    end
    local digitText = str

    index = index + 1
    local o = GameObject.Instantiate(HotResManager.ReadUI('ui/Tips/Empty'), UIManager:GetUIContainer(112).transform)
    o.name = 'SilverExpMessage' .. index
    local label = o:GetComponent('Text')
    label.raycastTarget = false
    label.text = digitText
    
    -- V13.0: 设置颜色 - 充值用金黄色，消费用橙色
    if value > 0 then
        label.color = Color(1, 0.8, 0, 1)  -- 金黄色
    else
        label.color = Color(1, 0.5, 0, 1)  -- 橙色
    end
    
    local canvasGroup = o.transform.gameObject:GetComponent('CanvasGroup')
    if not canvasGroup then
        canvasGroup = o.transform.gameObject:AddComponent(typeof(UnityEngine.CanvasGroup))
    end
    
    -- V13.0: 震撼动画升级
    local rectTransform = o:GetComponent('RectTransform')
    
    -- 初始位置设置（屏幕中心偏上）
    local initialPos = Vector3(math.random(-150, 150), math.random(20, 80), 0)
    rectTransform.anchoredPosition = initialPos
    
    -- V13.0: 震撼缩放效果
    rectTransform.localScale = Vector3(0.5, 0.5, 1)  -- 从0.5倍开始
    local scaleSequence = DOTween.Sequence()
    scaleSequence:Append(rectTransform:DOScale(Vector3(1.5, 1.5, 1), 0.15))  -- 快速放大到1.5倍
    scaleSequence:Append(rectTransform:DOScale(Vector3(1, 1, 1), 0.15))      -- 缓回1倍
    scaleSequence:SetUpdate(true)
    
    -- V13.0: 螺旋上升动画参数
    local totalTime = 1.5
    local spiralRadius = 60  -- 螺旋半径
    local spiralSpeed = 720  -- 角速度(度/秒)
    local upSpeed = 80       -- 上升速度
    
    local startTime = Time.time
    local startPos = initialPos
    local currentAngle = 0
    
    -- V13.0: 颤抖效果参数
    local shakeCount = 0
    local maxShakeCount = 10
    local shakeIntensity = 5
    
    local updateTimer
    updateTimer = Timer.New(function()
        local elapsed = Time.time - startTime
        local progress = elapsed / totalTime
        
        if progress >= 1 then
            updateTimer:Stop()
            GameObject.Destroy(canvasGroup.gameObject)
            return
        end
        
                 -- V13.0: 螺旋上升计算
         currentAngle = currentAngle + spiralSpeed * 0.02  -- 每帧增加角度
         local currentRadius = spiralRadius * (1 - progress * 0.5)  -- 半径随高度递减
         local heightProgress = math.pow(progress, 0.8)  -- 非线性高度变化
         
         local spiralX = startPos.x + math.cos(math.rad(currentAngle)) * currentRadius
        local spiralY = startPos.y + upSpeed * heightProgress
        
                 -- V13.0: 文字颤抖效果（前半段时间）
         if progress < 0.5 and shakeCount < maxShakeCount then
             if math.floor(elapsed / 0.05) > shakeCount then
                 shakeCount = shakeCount + 1
                 spiralX = spiralX + math.random(-shakeIntensity, shakeIntensity)
                 spiralY = spiralY + math.random(-shakeIntensity, shakeIntensity)
             end
         end
        
        rectTransform.anchoredPosition = Vector3(spiralX, spiralY, 0)
        
        -- V13.0: 透明度渐变（最后40%时间内渐变消失）
        if progress > 0.6 then
            local fadeProgress = (progress - 0.6) / 0.4
            canvasGroup.alpha = 1 - fadeProgress
        else
            canvasGroup.alpha = 1
        end
        
    end, 0.02, -1, true)  -- 50FPS更新频率确保动画流畅
    
    updateTimer:Start()
end

--添加队列
function AddSilverExpMessageQueue(name, value)
    RunSilverExpMessage(name, value)
end
