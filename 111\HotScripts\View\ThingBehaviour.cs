// ReSharper disable InconsistentNaming

using System.Linq;
using System.Threading;

using Apq.Unity3D.UnityHelpers;
using Apq.Utils;

using Cysharp.Threading.Tasks;

using DataStructure;

using DG.Tweening;

using HotScripts;

using RxEventsM2V;

using Spine.Unity;

using Thing;

using UniRx;

using UnityEngine;

using X.PB;

namespace View
{
    /// <summary>
    ///     物件基类(界面)
    /// </summary>
    public abstract class ThingBehaviour : MonoBehaviour
    {
        /// <summary>
        ///     物件(数据)
        /// </summary>
        public ThingBase Thing { get; set; }

        /// <summary>
        ///     每一帧是否将界面的状态(如：位置、朝向等)同步到数据中。回收到对象池时，应将其设为false。
        /// </summary>
        public bool SyncToThing { get; set; }

        /// <summary>
        ///     已移动时长
        /// </summary>
        public FloatReactiveProperty MoveDuration { get; } = new();

        /// <summary>
        ///     是否应该停留
        /// </summary>
        public bool ShouldStay { get; set; }

        /// <summary>
        ///     物件的血条
        /// </summary>
        public HealthBar HpBar { get; set; }

        private Transform armorSprite { get; set; }
        /// <summary>
		/// 生物的骨骼动画
		/// </summary>
		private SkeletonAnimation SkeAni { get; set; }
        
        private bool isAttacking = false;
        private bool attackPending = false;
        private int currentAttackCount = 0;
        private const int REQUIRED_ATTACK_COUNT = 1;

        // // V44.0 新增配置攻击动作相关字段
        private string currentAttackActionName = "attack01";
        private int currentAttackPlayCount = 1;
        private float currentAttackPlaySpeed = 1.0f;
        private int currentAttackPriority = 1;
        private int currentRunningPriority = 0;

        // V56.1 新增HitSound参数，每次攻击动作播放时同步播放音频
        private string currentAttackHitSound = "";

        public virtual void Awake()
        {
            SkeAni = GetComponentInChildren<SkeletonAnimation>();
            HpBar = GetComponentInChildren<HealthBar>();
            if (HpBar)
            {
                HpBar.bar.gameObject.SetActive(false);
            }

            armorSprite = transform.Find("wuqi/Armor");
            if (!armorSprite)
            {
                armorSprite = transform.Find("wuqi(1)/Armor");
            }
        }

        public virtual void Start()
        {
            try
            {
                if (HpBar)
                {
                    // 血量改变时显示血条
                    MessageBroker.Default.Receive<ThingHp>().Where(e => Util.IsEquals(e.Thing, Thing)).Subscribe(e =>
                    {
                        try
                        {
                            CTS_ShowHpBar.Cancel();
                            CTS_ShowHpBar = new CancellationTokenSource();
                            Task_ShowHpBar(e.NewValue, Thing.GetTotalDouble(PropType.MaxHp).FirstOrDefault(), 3,
                                CTS_ShowHpBar.Token).Forget();
                        }
                        catch (System.NullReferenceException)
                        {
                            // 屏蔽NullReferenceException错误
                        }
                    }).AddTo(this.GetCancellationTokenOnDestroy());

                    // 护甲改变时显示护罩
                    MessageBroker.Default.Receive<ThingArmor>().Where(e => Util.IsEquals(e.Thing, Thing)).Subscribe(e =>
                    {
                        try
                        {
                            Debug.Log($"护甲变化:{e.NewValue}/{Thing.GetTotalDouble(PropType.MaxHp).FirstOrDefault()}");
                            if (e.Inc < 0)
                            {
                                MessageBroker.Default.Publish(new ThingHp
                                {
                                    Thing = e.Thing, OriginalValue = e.Thing.Hp.Value, NewValue = e.Thing.Hp.Value
                                });
                                HpBar.SetDisplayArmorBar((float)(e.NewValue /
                                                                 Thing.GetTotalDouble(PropType.MaxHp).FirstOrDefault()));
                            }

                            ShowArmorSprite(e.NewValue, Thing.GetTotalDouble(PropType.MaxHp).FirstOrDefault());
                        }
                        catch (System.NullReferenceException)
                        {
                            // 屏蔽NullReferenceException错误
                        }
                    }).AddTo(this);

                    // 初始护罩
                    try
                    {
                        ShowArmorSprite(Thing.Armor.Value, Thing.GetTotalDouble(PropType.MaxHp).FirstOrDefault());
                    }
                    catch (System.NullReferenceException)
                    {
                        // 屏蔽NullReferenceException错误
                    }
                }
            }
            catch (System.NullReferenceException)
            {
                // 屏蔽NullReferenceException错误，不影响游戏逻辑
                // Debug.LogWarning("ThingBehaviour Start NullReferenceException 已屏蔽");
            }
            catch (System.Exception)
            {
                // 屏蔽其他异常，确保不崩溃
                // Debug.LogWarning("ThingBehaviour Start Exception 已屏蔽");
            }
        }

        public virtual void Update()
        {
            try
            {
                // 将位置和自转朝向同步到数据中
                if (SyncToThing && Thing != null)
                {
                    DoSyncToThing();
                }
            }
            catch (System.NullReferenceException)
            {
                // 屏蔽NullReferenceException错误
                // Debug.LogWarning("ThingBehaviour Update NullReferenceException 已屏蔽");
            }
            catch (System.Exception)
            {
                // 屏蔽其他异常
                // Debug.LogWarning("ThingBehaviour Update Exception 已屏蔽");
            }
        }

        protected virtual void OnDestroy()
        {
            StopRotate();
            StopMove();

            // Thing?.Dispose();
        }

        /// <summary>
        ///     对界面进行初始化
        /// </summary>
        public async UniTask Init()
        {
            // 目的:等到Awake执行后
            await UniTask.NextFrame();

            await OnBeforeInitView();

            await OnAfterInitView();
        }

        /// <summary>
        ///     初始化界面前
        /// </summary>
        public virtual async UniTask OnBeforeInitView()
        {
            if (Thing is { Provider_BeforeInitView: not null })
            {
                UniTask task = UniTask.Defer(() =>
                {
                    Thing.Provider_BeforeInitView?.Invoke(this);
                    return UniTask.CompletedTask;
                });
                await task;
                await UniTask.NextFrame();
            }
        }

        /// <summary>
        ///     初始化界面后
        /// </summary>
        public virtual async UniTask OnAfterInitView()
        {
            if (Thing is { Provider_AfterInitView: not null })
            {
                UniTask task = UniTask.Defer(() =>
                {
                    Thing.Provider_AfterInitView?.Invoke(this);
                    return UniTask.CompletedTask;
                });
                await task;
            }
        }

        /// <summary>
        ///     将一些界面的值同步至数据中
        /// </summary>
        public virtual void DoSyncToThing()
        {
            if (Thing != null)
            {
                // 位置
                Thing.Position = transform.position;
                // 自转朝向
                Thing.RotationTowards.Value = transform.rotation.eulerAngles;
            }
        }

        /// <summary>
        ///     移除物件数据,并将界面回收到界面池(隐藏就是回收到池了)
        /// </summary>
        public virtual async UniTaskVoid TurnToPool()
        {
            try
            {
                await UniTask.WaitForEndOfFrame(SingletonMgr.Instance.GlobalMgr);

                StopRotate();
                StopMove();

                gameObject.SetActive(false);
                SyncToThing = false;
                // 回收到池中后，就不应停留了
                ShouldStay = false;

                // 延时1分钟后置为null
                //await UniTask.Delay(System.TimeSpan.FromMinutes(1));
                Thing = null;
            }
            catch (System.OperationCanceledException) { throw; }
            catch (MissingReferenceException) { }
            catch (System.Exception ex)
            {
                Debug.LogException(ex);
            }
            //catch { }
        }

        #region 移动

        /// <summary>
        ///     计算当前帧的移动方向(按HowMoveDir指定的方法计算)
        /// </summary>
        public virtual Vector3 CalcMoveDir()
        {
            Vector3 rtn = Thing.GetTotalLong(PropType.HowMoveDir).FirstOrDefault() switch
            {
                (int)DirMethod.Up => Vector3.up,
                (int)DirMethod.Down => Vector3.down,
                (int)DirMethod.Left => Vector3.left,
                (int)DirMethod.Right => Vector3.right,
                (int)DirMethod.Straight => CalcDir_Straight(),
                _ => Vector3.zero
            };
            return rtn;
        }

        /// <summary>
        ///     计算当前帧的直线移动方向
        /// </summary>
        public virtual Vector3 CalcDir_Straight()
        {
            // Thing.MoveDirection_Straight.Value = Vector3.zero;
            return Thing.MoveDirection_Straight.Value;
        }

        /// <summary>
        ///     任务的取消令牌:移动
        /// </summary>
        public CancellationTokenSource CTS_Move { get; set; } = new();

        /// <summary>
        ///     开始移动(按数据中设定的移动方法)
        /// </summary>
        /// <returns>是否已开始移动</returns>
        public virtual void StartMove()
        {
            StopMove();
            CTS_Move = new CancellationTokenSource();

            MoveDuration.Value = 0;
            DoTask_Move(CTS_Move.Token).Forget();
        }

        /// <summary>
        ///     停止移动
        /// </summary>
        public virtual void StopMove()
        {
            CTS_Move.Cancel();
        }

        /// <summary>
        ///     任务实现:移动
        /// </summary>
        public virtual async UniTaskVoid DoTask_Move(CancellationToken token)
        {
            try
            {
                for (;; await UniTask.NextFrame())
                {
                    if (token.IsCancellationRequested)
                    {
                        return;
                    }

                    if (Time.deltaTime <= 0)
                    {
                        continue;
                    }

                    MoveDuration.Value += Time.deltaTime;

                    //// 移动已被Buff接管
                    //if (Thing.IsTakeMove()) continue;

                    if (OnBeforeMoveOne())
                    {
                        return;
                    }

                    try
                    {
                        switch (Thing.GetTotalLong(PropType.MoveMethod).FirstOrDefault())
                        {
                            case (int)MoveMethod.PositionMove:
                                {
                                    LineSegment line = MoveOne_PositionMove(Time.deltaTime);
                                    if (OnAfterMoveOne(line))
                                    {
                                        return;
                                    }
                                }
                                break;
                            case (int)MoveMethod.RigidBodyMove:
                                {
                                    MoveOne_RigidBodyMove(Time.deltaTime);
                                }
                                break;
                        }
                    }
                    catch (System.OperationCanceledException)
                    {
                        throw;
                    }
                    catch (System.Exception ex)
                    {
                        Debug.LogException(ex);
                    }
                }
            }
            catch (System.OperationCanceledException)
            {
                throw;
            }
            catch (MissingReferenceException)
            {
            }
            catch (System.Exception ex)
            {
                Debug.LogException(ex);
            }
            finally
            {
                OnMoveEnd();
            }
        }

        /// <summary>
        ///     移动前的判断
        /// </summary>
        /// <returns>是否不再移动</returns>
        protected virtual bool OnBeforeMoveOne()
        {
            Vector3 speed = CalcSpeed_PositionMove();
            // 有速度就是要移动，则计时
            if (speed != Vector3.zero)
            {
                MoveDuration.Value += Time.deltaTime;
            }

            return false;
        }

        /// <summary>
        ///     移动后的处理
        /// </summary>
        /// <returns>是否不再移动</returns>
        protected virtual bool OnAfterMoveOne(LineSegment line)
        {
            return false;
        }

        /// <summary>
        ///     移动结束时干什么
        /// </summary>
        protected virtual void OnMoveEnd()
        {
        }

        #region PositionMove

        /// <summary>
        ///     计算物件的当前速度(方向和大小)
        /// </summary>
        protected virtual Vector3 CalcSpeed_PositionMove()
        {
            float speed = ShouldStay ? 0
                : Thing.IsHitBack ? Thing.HitBackSpeed
                : (float)Thing.GetTotalDouble(PropType.Speed).FirstOrDefault();
            // 速度不能是负值
            if (speed <= float.Epsilon)
            {
                speed = 0;
            }

            // 停留状态的方向取0
            Vector3 dir_1 = speed == 0 ? Vector3.zero : CalcMoveDir();

            // 击退状态时速度取反
            if (Thing.IsHitBack)
            {
                dir_1 = -1 * dir_1;
            }

            // 冰冻状态速度取0
            if (Thing.IsFreeze)
            {
                speed = 0;
            }

            return dir_1 * speed;
        }

        /// <summary>
        ///     移动一次。计算这次直线移动的线段,修改位置,并设置下一帧的移动方向
        /// </summary>
        /// <param name="duration">这次移动所用的时长(秒)</param>
        /// <returns>这次直线移动经过的线段</returns>
        public virtual LineSegment MoveOne_PositionMove(float duration)
        {
            // 起点为当前位置
            LineSegment rtn = new LineSegment { PosStart = transform.position };

            // 当前速度(方向和大小)
            Vector3 dir = CalcSpeed_PositionMove();

            // 这次移动的长度
            float distance = dir.magnitude * duration;

            // 无障碍物时的直线移动线段
            rtn.Dir = distance * dir.normalized;

            // 当前移动终点的信息
            StraightEndPoint_PositionMove rtnEndPoint = new StraightEndPoint_PositionMove { EndPoint = rtn.PosEnd };

            // 检测障碍物
            {
                // 碰到的边及其位置
                StraightEndPoint_PositionMove collideEdgePoint = DoCollideRectObstacles_PositionMove(rtn);
                if (collideEdgePoint != null)
                {
                    rtnEndPoint = collideEdgePoint;
                    rtn.Dir = collideEdgePoint.EndPoint - rtn.PosStart;
                }
            }

            // // 检测边界
            // {
            //     // 碰到的边及其位置
            //     var collideEdgePoint = DoCollideBounds_PositionMove(rtn);
            //     if (collideEdgePoint != null)
            //     {
            //         rtnEndPoint = collideEdgePoint;
            //         rtn.Dir = collideEdgePoint.EndPoint - rtn.PosStart;
            //     }
            // }

            // 传送至终点
            TranslateToEndPoint_PositionMove(rtnEndPoint);

            return rtn;
        }

        /// <summary>
        ///     执行碰撞检测:矩形障碍物。碰到不能通过的矩形障碍物时，返回新的终点信息
        /// </summary>
        public virtual StraightEndPoint_PositionMove DoCollideRectObstacles_PositionMove(LineSegment move,
            System.Func<RectObstacleThing, bool> predicate = null)
        {
            // 默认条件：有类型的矩形障碍物
            predicate ??= x => x.TerrainType > TerrainType.None;

            StraightEndPoint_PositionMove rtn = null;
            // 射线检测 找出碰到的地形
            // ReSharper disable once Unity.PreferNonAllocApi
            RaycastHit2D[] hits = Physics2D.CircleCastAll(move.PosStart, Thing.TotalProp_Radius,
                move.DirNormal, move.Dir.magnitude, LayerMask.GetMask("Terrain")
            );
            if (hits is { Length: > 0 })
            {
                // 碰到的第一个满足条件的矩形障碍物
                // ReSharper disable once IdentifierTypo
                RaycastHit2D raycastHit2D = hits.FirstOrDefault(x =>
                    x && x.collider.gameObject &&
                    x.collider.gameObject.GetComponent<RectObstacle>() is
                        { RectObstacleThing: not null } rectObstacle &&
                    (predicate == null || predicate.Invoke(rectObstacle.RectObstacleThing)));
                if (raycastHit2D)
                {
                    RectObstacleThing rectObstacleThing =
                        raycastHit2D.collider.gameObject.GetComponent<RectObstacle>().RectObstacleThing;
                    RectArea2D rectArea2D = rectObstacleThing.RectAreas2D.FirstOrDefault();
                    // 碰到矩形障碍物的哪条边
                    LineSegment edge = rectArea2D?.GetEdgeContainsPoint(raycastHit2D.point).FirstOrDefault();
                    if (edge != null)
                    {
                        rtn = new StraightEndPoint_PositionMove
                        {
                            CollidePoint = raycastHit2D.point,
                            MirrorInNormal = edge.Dir.RotateAround(Vector3.forward, 90).normalized
                        };
                    }
                }
            }

            return rtn;
        }

        // /// <summary>
        // /// 执行碰撞检测:边界。碰到不能通过的边界时，返回新的终点信息
        // /// </summary>
        // public virtual StraightEndPoint_PositionMove DoCollideBounds_PositionMove(LineSegment move)
        // {
        //     StraightEndPoint_PositionMove rtn = null;
        //     // 获取战斗区域的边
        //     var edges = GlobalMgr.GetBattleEdges();
        //     foreach (var edge in edges)
        //     {
        //         var (intersect, point) = move.TryGetIntersectPoint(edge);
        //         if (intersect)
        //         {
        //             rtn = new()
        //             {
        //                 CollidePoint = point,
        //                 MirrorInNormal = edge.Dir.RotateAround(Vector3.forward, -90).normalized,
        //             };
        //             break;
        //         }
        //     }
        //
        //     return rtn;
        // }

        /// <summary>
        ///     物件传送至终点
        /// </summary>
        public virtual void TranslateToEndPoint_PositionMove(StraightEndPoint_PositionMove endPoint)
        {
            transform.position = endPoint.EndPoint;
        }

        #endregion

        #region RigidBodyMove

        /// <summary>
        ///     刚体运动(当速度或方向改变时，设置一次刚体速度)
        /// </summary>
        /// <param name="duration">这次移动前经过的时长(秒)</param>
        public void MoveOne_RigidBodyMove(float duration)
        {
            if (!TryGetComponent(out Rigidbody2D rgBody))
            {
                return;
            }

            Vector2 oldVelocity = rgBody.velocity;

            float speed = Thing.IsHitBack
                ? Thing.HitBackSpeed
                : (float)Thing.GetTotalDouble(PropType.Speed).FirstOrDefault();
            // 速度不能是负值
            if (speed <= float.Epsilon)
            {
                speed = 0;
            }

            Vector3 dir_1 = CalcMoveDir();

            // 击退状态时速度取反
            if (Thing.IsHitBack)
            {
                dir_1 = -1 * dir_1;
            }

            // 冰冻状态速度取0
            if (Thing.IsFreeze)
            {
                speed = 0;
            }

            Vector3 newVelocity = speed * dir_1;

            if ((Vector2)newVelocity != oldVelocity)
            {
                rgBody.velocity = newVelocity;
            }

            //DOTween.To(() => rgBody.velocity, v => rgBody.velocity = v,
            //	speed * (Vector2)CalcMoveDir(), duration);
        }

        #endregion

        #endregion

        #region 自转

        /// <summary>
        ///     任务的取消令牌:自转
        /// </summary>
        public CancellationTokenSource CTS_Rotate { get; set; } = new();

        /// <summary>
        ///     开始自转
        /// </summary>
        public virtual void StartRotate()
        {
            StopRotate();
            CTS_Rotate = new CancellationTokenSource();
            DoTask_Rotate(CTS_Rotate.Token).Forget();
        }

        /// <summary>
        ///     停止自转
        /// </summary>
        public virtual void StopRotate()
        {
            CTS_Rotate.Cancel();
        }

        /// <summary>
        ///     任务实现:自转
        /// </summary>
        public virtual async UniTaskVoid DoTask_Rotate(CancellationToken token)
        {
            try
            {
                for (;; await UniTask.NextFrame())
                {
                    if (token.IsCancellationRequested)
                    {
                        return;
                    }

                    if (Time.deltaTime <= 0)
                    {
                        continue;
                    }

                    try
                    {
                        RotateOne(token, Time.deltaTime);
                    }
                    catch (System.OperationCanceledException)
                    {
                        throw;
                    }
                    catch (System.Exception ex)
                    {
                        Debug.LogException(ex);
                    }
                }
            }
            catch (System.OperationCanceledException)
            {
                throw;
            }
            catch (MissingReferenceException)
            {
            }
            catch (System.Exception ex)
            {
                Debug.LogException(ex);
            }
        }

        /// <summary>
        ///     执行一次自转
        /// </summary>
        /// <param name="token"></param>
        /// <param name="duration">这次自转前经过的时长(秒)</param>
        public virtual void RotateOne(CancellationToken token, float duration)
        {
            // 自转角速度
            float rotateSpeed = (float)Thing.GetTotalDouble(PropType.RotateSpeed).FirstOrDefault();
            if (rotateSpeed == 0)
            {
                return;
            }

            float rotateZ = transform.localRotation.eulerAngles.z + (rotateSpeed * duration);
            transform.DOLocalRotate(new Vector3(0, 0, rotateZ), duration, RotateMode.FastBeyond360);

            //transform.Rotate(Vector3.forward, rotateSpeed * duration,
            //	Space.Self);
        }

        #endregion

        #region 血条

        /// <summary>
        ///     任务的取消令牌:
        /// </summary>
        protected CancellationTokenSource CTS_ShowHpBar { get; set; } = new();

/// <summary>
/// 显示血条
/// </summary>
public virtual async UniTaskVoid Task_ShowHpBar(double value, double maxValue, float delay = 30000,
    CancellationToken token = default)
{
    try
    {
        // 设置血条长度
        HpBar.SetDisplayHealth((float)(value / maxValue));
        HpBar.bar.gameObject.SetActive(true);
                                if (value < maxValue * 0.001f  )
                                {
                                    HpBar.barOutline.gameObject.SetActive(true);
                                        Debug.Log("怪物血条长度11");
                                 }
        // 判断是否应该隐藏 barOutline
        bool shouldHideBarOutline =  (value > 0 && value < maxValue * 0.99f);
        bool isBarOutlineHidden = !HpBar.barOutline.gameObject.activeSelf; // 使用 activeSelf 检查当前是否已隐藏

        // 如果需要隐藏且当前未隐藏，则隐藏 barOutline
        if (shouldHideBarOutline && !isBarOutlineHidden)
        {
            HpBar.barOutline.gameObject.SetActive(false);
            // Debug.Log("怪物血条长度：隐藏 barOutline");
            
        }
        
                bool shouldMoveBarOutline = value > maxValue * 0.1f && value < maxValue * 0.99f;
        // 等待指定的延迟时间之前先检查并可能修改 barOutline 的位置
         if (shouldMoveBarOutline)
        {
            // 假设 HpBar.barOutline 是一个具有 Transform 组件的游戏对象
            Transform barOutlineTransform = HpBar.barOutline.GetComponent<Transform>();
            if (barOutlineTransform != null)
            {
                // 修改 barOutline 的位置，假设您想要修改的是 X 坐标
                barOutlineTransform.position = new Vector3(1000, barOutlineTransform.position.y, barOutlineTransform.position.z);
                // Debug.Log("怪物血条长度：移动 barOutline 到 X=1000");
            }
            else
            {
                Debug.LogError("未能获取到 HpBar.barOutline 的 Transform 组件");
            }
        }
        // 等待指定的延迟时间
        await UniTask.Delay(System.TimeSpan.FromSeconds(delay), cancellationToken: token);

        // 检查是否请求取消
        if (token.IsCancellationRequested)
        {
            return;
        }

        // 理论上，如果 value 和 maxValue 在等待期间没有变化，这里不需要重新设置血条长度
        // 但为了安全起见，或者如果外部因素可能改变了它，可以重新设置
        HpBar.SetDisplayHealth((float)(value / maxValue));
        // 注意：这里不需要再次设置 HpBar.bar.gameObject.SetActive(true)，因为它在开始时已经设置为 true
        // 并且没有代码在期间将其设置为 false（除非在别的地方有代码这样做）

        // 同样，不需要再次改变 HpBar.barOutline.gameObject.SetActive 的状态，因为它已经在需要时被隐藏
    }
    catch (System.OperationCanceledException) { throw; }
    catch (MissingReferenceException) { }
    catch (System.Exception ex)
    {
        Debug.LogException(ex);
    }
}
        /// <summary>
        ///     显示护甲图片
        /// </summary>
        /// <param name="curArmorValue"></param>
        /// <param name="maxHp"></param>
        public void ShowArmorSprite(double curArmorValue, double maxHp)
        {
            try
            {
                if (armorSprite == null)
                {
                    return;
                }

                if (curArmorValue < 5 + (0.1f * maxHp))
                {
                    armorSprite.gameObject.SetActive(false);
                    armorSprite.GetComponent<Animation>().enabled = false;
                }
                else if (curArmorValue >= 5 + (0.1f * maxHp) && curArmorValue < 5 + (0.2f * maxHp))
                {
                    armorSprite.gameObject.SetActive(true);
                    armorSprite.GetComponent<Animation>().enabled = true;
                }
                else if (curArmorValue >= 5 + (0.2f * maxHp))
                {
                    armorSprite.gameObject.SetActive(true);
                    armorSprite.GetComponent<Animation>().enabled = false;
                }
            }
            catch (System.NullReferenceException)
            {
                // 屏蔽NullReferenceException错误
                // Debug.LogWarning("ShowArmorSprite NullReferenceException 已屏蔽");
            }
            catch (System.Exception)
            {
                // 屏蔽其他异常
                // Debug.LogWarning("ShowArmorSprite Exception 已屏蔽");
            }
        }

        #endregion
        /// <summary>
		/// 播放动画
		/// </summary>
		/// <param name="aniName">动画名字</param>
		/// <param name="loop">是否循环</param>
		/// <param name="isBackToIdle">当前动画结束是否要回到idle状态</param>
		public void PlayAnimation(string aniName, bool loop = false, bool isBackToIdle = true)
        {
            if (SkeAni == null || SkeAni.state == null) return;
            
            var animation = SkeAni.skeletonDataAsset.GetSkeletonData(false).FindAnimation(aniName);
            if (animation == null)
            {
                //Debug.LogWarning($"动画 {aniName} 不存在于 {gameObject.name}");
                return;
            }

            if (aniName == "attack01")
            {
                if (isAttacking)
                {
                    attackPending = true; // 如果正在攻击，标记为有待处理的攻击
                    return;
                }

                isAttacking = true;
                attackPending = false;
                currentAttackCount = 1; // 开始第一次攻击
                var trackEntry = SkeAni.state.SetAnimation(0, "attack01", false);
                trackEntry.Complete += OnAttackAnimationComplete;
            }
            else
            {
                if (isAttacking) return; // 如果正在攻击，则不允许播放其他动画（移动、待机等）

                // 允许中断循环动画（如移动到待机）
                var currentTrack = SkeAni.state.GetCurrent(0);
                if (currentTrack != null && currentTrack.Animation.Name == aniName && currentTrack.Loop)
                {
                    return; // 如果是同一个循环动画，则不重新播放
                }

                var track = SkeAni.state.SetAnimation(0, aniName, loop);
                if (!loop && isBackToIdle)
                {
                    track.Complete += entry =>
                    {
                        // 确保动画状态没有改变
                        if(SkeAni.state.GetCurrent(0) == entry)
                            SkeAni.state.SetAnimation(0, "idle01", true);
                    };
                }
            }
        }

        private void OnAttackAnimationComplete(Spine.TrackEntry trackEntry)
        {
            trackEntry.Complete -= OnAttackAnimationComplete; // 移除监听器

            if (currentAttackCount < REQUIRED_ATTACK_COUNT)
            {
                // 还没有播放够3次，继续播放下一次攻击
                currentAttackCount++;
                var newTrackEntry = SkeAni.state.SetAnimation(0, "attack01", false);
                newTrackEntry.Complete += OnAttackAnimationComplete;
            }
            else if (attackPending)
            {
                // 3次攻击播放完毕，但有新的攻击请求待处理
                attackPending = false;
                currentAttackCount = 1; // 重新开始新的3次攻击循环
                var newTrackEntry = SkeAni.state.SetAnimation(0, "attack01", false);
                newTrackEntry.Complete += OnAttackAnimationComplete;
            }
            else
            {
                // 3次攻击全部播放完毕，且没有待处理的攻击
                isAttacking = false;
                currentAttackCount = 0;
                
                // 攻击结束后，根据实际情况决定下一个动画
                bool isMoving = (this as CreatureBase)?.IsMoving ?? false;

                if (isMoving)
                {
                    PlayAnimation("move01", true);
                }
                else
                {
                    PlayAnimation("idle01", true);
                }
            }
        }

        public void SetSpineModelDirection(int value)
        {
            if (SkeAni == null) SkeAni = GetComponentInChildren<SkeletonAnimation>();
            if (SkeAni == null || SkeAni.state == null) return;
            SkeAni.skeleton.ScaleX = value;
        }

        public void SetSpineSortingOrderBySelfPosition()
        {
            if (SkeAni == null) return;
            MeshRenderer mr = SkeAni.GetComponent<MeshRenderer>();
            mr.sortingOrder = -(int)(transform.position.y * 1) + 100;
        }

        /// <summary>
        /// V56.1 新增HitSound参数，每次攻击动作播放时同步播放音频
        /// </summary>
        /// <param name="actionName">攻击动作名称</param>
        /// <param name="playCount">播放次数</param>
        /// <param name="playSpeed">播放速度</param>
        /// <param name="priority">优先级</param>
        /// <param name="hitSound">攻击音效文件名</param>
        public void PlayAttackActionWithConfig(string actionName, int playCount, float playSpeed, int priority, string hitSound = "")
        {
            try
            {
                // Debug.Log($"V56.1 播放配置攻击动作: {actionName}, 次数:{playCount}, 速度:{playSpeed}, 优先级:{priority}, 音效:{hitSound}");

                if (SkeAni == null || SkeAni.state == null)
                {
                    // Debug.LogWarning("V44.0 SkeAni或state为空，无法播放攻击动作");
                    return;
                }

                // 检查动画是否存在
                var animation = SkeAni.skeletonDataAsset.GetSkeletonData(false).FindAnimation(actionName);
                if (animation == null)
                {
                    // Debug.LogWarning($"V44.0 动画 {actionName} 不存在于 {gameObject.name}");
                    return;
                }

                // 优先级检查：只有更高优先级才能中断当前攻击动作
                if (isAttacking && priority <= currentRunningPriority)
                {
                    // Debug.Log($"V44.0 攻击动作优先级不足，当前优先级:{currentRunningPriority}, 请求优先级:{priority}");
                    return;
                }

                // 如果有更高优先级，停止当前攻击
                if (isAttacking && priority > currentRunningPriority)
                {
                    // Debug.Log($"V44.0 高优先级攻击动作中断当前攻击，当前优先级:{currentRunningPriority}, 新优先级:{priority}");
                    StopCurrentAttackAction();
                }

                // 设置新的攻击配置
                currentAttackActionName = actionName;
                currentAttackPlayCount = playCount;
                currentAttackPlaySpeed = playSpeed;
                currentAttackPriority = priority;
                currentRunningPriority = priority;
                
                // V56.1 保存攻击音效
                currentAttackHitSound = hitSound;

                // 开始播放攻击动作
                StartNewAttackAction();
            }
            catch (System.Exception ex)
            {
                // Debug.LogError($"V44.0 播放配置攻击动作时发生异常: {ex.Message}");
            }
        }

        /// <summary>
        /// V44.0 开始新的攻击动作
        /// V56.1 增加HitSound音效播放功能
        /// </summary>
        private void StartNewAttackAction()
        {
            try
            {
                isAttacking = true;
                attackPending = false;
                currentAttackCount = 1;

                // Debug.Log($"V44.0 开始播放攻击动作: {currentAttackActionName}, 第{currentAttackCount}/{currentAttackPlayCount}次, 速度:{currentAttackPlaySpeed}");

                var trackEntry = SkeAni.state.SetAnimation(0, currentAttackActionName, false);
                
                // 设置播放速度
                trackEntry.TimeScale = currentAttackPlaySpeed;
                
                trackEntry.Complete += OnConfiguredAttackAnimationComplete;
                
                // V56.1 播放攻击音效（每次攻击动作开始时播放）
                PlayAttackHitSound();
            }
            catch (System.Exception ex)
            {
                // Debug.LogError($"V44.0 开始新攻击动作时发生异常: {ex.Message}");
            }
        }

        /// <summary>
        /// V44.0 停止当前攻击动作
        /// </summary>
        private void StopCurrentAttackAction()
        {
            try
            {
                if (isAttacking)
                {
                    var currentTrack = SkeAni.state.GetCurrent(0);
                    if (currentTrack != null)
                    {
                        currentTrack.Complete -= OnConfiguredAttackAnimationComplete;
                        currentTrack.Complete -= OnAttackAnimationComplete;
                    }

                    isAttacking = false;
                    attackPending = false;
                    currentAttackCount = 0;
                    currentRunningPriority = 0;
                    
                    // Debug.Log("V44.0 停止当前攻击动作");
                }
            }
            catch (System.Exception ex)
            {
                // Debug.LogError($"V44.0 停止攻击动作时发生异常: {ex.Message}");
            }
        }

        /// <summary>
        /// V44.0 配置攻击动画完成回调
        /// V56.1 增加每次攻击动作播放时的HitSound音效
        /// </summary>
        private void OnConfiguredAttackAnimationComplete(Spine.TrackEntry trackEntry)
        {
            try
            {
                trackEntry.Complete -= OnConfiguredAttackAnimationComplete;

                // Debug.Log($"V44.0 攻击动作完成: {currentAttackActionName}, 第{currentAttackCount}/{currentAttackPlayCount}次");

                if (currentAttackCount < currentAttackPlayCount)
                {
                    // 还需要继续播放
                    currentAttackCount++;
                    // Debug.Log($"V44.0 继续播放攻击动作: {currentAttackActionName}, 第{currentAttackCount}/{currentAttackPlayCount}次");
                    
                    var newTrackEntry = SkeAni.state.SetAnimation(0, currentAttackActionName, false);
                    newTrackEntry.TimeScale = currentAttackPlaySpeed;
                    newTrackEntry.Complete += OnConfiguredAttackAnimationComplete;
                    
                    // V56.1 每次攻击动作播放时都播放HitSound音效
                    PlayAttackHitSound();
                }
                else
                {
                    // 攻击动作全部播放完毕
                    // Debug.Log($"V44.0 攻击动作全部完成: {currentAttackActionName}, 共{currentAttackPlayCount}次");
                    
                    isAttacking = false;
                    currentAttackCount = 0;
                    currentRunningPriority = 0;

                    // 攻击结束后，根据实际情况决定下一个动画
                    bool isMoving = (this as CreatureBase)?.IsMoving ?? false;

                    if (isMoving)
                    {
                        PlayAnimation("move01", true);
                    }
                    else
                    {
                        PlayAnimation("idle01", true);
                    }
                }
            }
            catch (System.Exception ex)
            {
                // Debug.LogError($"V44.0 配置攻击动画完成回调时发生异常: {ex.Message}");
            }
        }

        /// <summary>
        /// V56.1 播放攻击音效（每次攻击动作开始时播放）
        /// </summary>
        private void PlayAttackHitSound()
        {
            try
            {
                if (!string.IsNullOrEmpty(currentAttackHitSound))
                {
                    Debug.Log($"V56.1 播放攻击音效: {currentAttackHitSound}");
                    // V56.1 使用AudioPlayer播放攻击音效
                    AudioPlayer.Instance.PlaySound(currentAttackHitSound).Forget();
                }
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"V56.1 播放攻击音效时发生异常: {ex.Message}");
            }
        }
    }
}