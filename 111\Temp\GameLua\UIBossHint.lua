--[[
********************************************************************
    created:    2024/04/11
    author :    李锦剑
    purpose:    BOSS提示界面
*********************************************************************
--]]

local luaID = ('BossHint')

---BOSS提示界面
---@class BossHint:UIWndBase
local m = {}

-------------------------------------------
--创建时事件
-------------------------------------------
function m.OnCreate()
    m.objList.root.gameObject:SetActive(false)
    local anim = m.objList.root:GetComponent("Animation")
    local time = 3.5
    if anim then
        time = anim.clip.length + 1
    end
    m.newTimer = HelperL.NewTimer(m.OnCloseSelf, time, 1)
    m.RegisterClickEvent()
    return true
end

-------------------------------------------
--打开时
-------------------------------------------
function m.OnOpen()
    m.objList.root.gameObject:SetActive(true)
    m.newTimer.Start()
end

-------------------------------------------
--注册点击事件
-------------------------------------------
function m.RegisterClickEvent()
    -- m.objList.Btn_Close.onClick:AddListenerEx(m.OnCloseSelf)
end

-------------------------------------------
--关闭时
-------------------------------------------
function m.OnCloseSelf()
    m.objList.root.gameObject:SetActive(false)
    m:CloseSelf()
end

return m
