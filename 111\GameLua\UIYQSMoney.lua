local luaID = ('UIYQSMoney')

---@class UIYQSMoney:UIWndBase
local m = {}
--------------------------------------------------------------------
-- 订阅事件列表
--------------------------------------------------------------------
function m.GetOpenEventList()
    return {
        [EventID.OnHeroPropChange] = m.UpdateView,
        [EventID.LogicDataChange] = m.UpdateView,
        [EventID.EntitySyncBuff] = m.UpdateView,
    }
end

--------------------------------------------------------------------
-- 初始化
--------------------------------------------------------------------
function m.OnCreate()
    m.goodsItemList = {}
    m.RegisterClickEvent()
    return true
end

--------------------------------------------------------------------
-- 窗口开启
--------------------------------------------------------------------
function m.OnOpen()
    m.UpdateView()
end

--------------------------------------------------------------------
--注册点击事件
--------------------------------------------------------------------
function m.RegisterClickEvent()
    m.objList.Btn_Power.onClick:AddListenerEx(m.OnClickPower)
    m.objList.Btn_Close.onClick:AddListenerEx(m.OnClickClose)
end

--------------------------------------------------------------------
--发放奖励
--------------------------------------------------------------------
function m.OnClickPower()
    local adID = 119
    local commonText = Schemes.CommonText:Get(adID)
    if not commonText then return end
    local num = commonText.DayTime - HeroDataManager:GetLogicByte(commonText.Param4, commonText.Param5)
    if num == 0 then
        ResultCode.ShowResultCodeCallback(RESULT_CODE_BASE.RESULT_COMMON_LACK_DAYTIMES[1])
        return
    end
    local time = EntityModule.hero.buffLC:GetBuffLeftTime(commonText.Param1)
    if time > 0 then
        return
    end
    -- if commonText.DeductGoods == "0" then
    --     AdvertisementManager.ShowRewardAd(adID, function(bool)
    --         if bool then
    --             HelperL.RequestDirectGiveGoodsy(commonText.AddGoods, commonText.DeductGoods)
    --         end
    --     end)
    -- else
    --     local goodsList = m.GetGoodsList(commonText.DeductGoods)
    --     if HelperL.IsLackGoods(goodsList[1].id, goodsList[1].num, false) then
    --         return
    --     end
    --     AdvertisementManager.GetAdAward(adID, function(bool, adID)
    --         if bool then
    --             HelperL.RequestDirectGiveGoodsy(commonText.AddGoods, commonText.DeductGoods)
    --         end
    --     end)
    -- end

        AdvertisementManager.GetAdAward(adID, function(bool, adID)
            if bool then
                -- local goodsList = Schemes.PrizeTable:GetGoodsList(commonText.DeductGoods)
                -- local goodInfo = ''
                -- for i, v in ipairs(goodsList) do
                --     if goodInfo == '' then
                --         goodInfo = v.id .. ';' .. v.num
                --     else
                --         goodInfo = goodInfo .. '|' .. v.id .. ';' .. v.num
                --     end
                -- end
                local costInfo = ""
                --HelperL.RequestDirectGiveGoodsy(goodInfo, costInfo)
                HelperL.GetDirectPrize(tonumber(commonText.DeductGoods), costInfo, nil, true)
            end
        end)
end

--------------------------------------------------------------------
-- 每秒更新
--------------------------------------------------------------------
function m.OnSecondUpdate()
    local adID = 119
    local commonText = Schemes.CommonText:Get(adID)
	local time = EntityModule.hero.buffLC:GetBuffLeftTime(commonText.Param1)
    if time <= 0 then
        return
    end
    
    m.objList.Txt_Time.text = "正在冷却中"..HelperL.GetTimeString(TimeStringType.FullAuto1, time).."。"
end

--------------------------------------------------------------------
-- 获取物品列表
--------------------------------------------------------------------
function m.GetGoodsList(content)
    ---@type {id:integer, num:integer}[]
    local goodsList = {}
    local strList = HelperL.Split(content, "|")
    local tempList
    for i, v in ipairs(strList) do
        tempList = HelperL.Split(v, ";")
        table.insert(goodsList, {
            id = tonumber(tempList[1]),
            num = tonumber(tempList[2]),
        })
    end
    return goodsList
end

--------------------------------------------------------------------
--更新界面
--------------------------------------------------------------------
function m.UpdateView()            
        local commonText = Schemes.CommonText:Get(119)
        local goodsList = m.GetGoodsList(commonText.AddGoods)
        m.objList.Txt_Num.text = goodsList[1].num
        local num = HeroDataManager:GetLogicByte(commonText.Param4, commonText.Param5)
        local color = num > 0 and UI_COLOR.Red or UI_COLOR.Red
        m.objList.Txt_Num.text = string.format(GetGameText(luaID, 1), num, commonText.DayTime)

        local time = EntityModule.hero.buffLC:GetBuffLeftTime(commonText.Param1)
            print("commonText.DeductGoods =====time====== "..time)
        if time > 0 then
            m.objList.Txt_Time.gameObject:SetActive(true)
            m.objList.Btn_Power.gameObject:SetActive(false)
            m.objList.Txt_Num.gameObject:SetActive(false)
        else
            m.objList.Txt_Time.gameObject:SetActive(false)
            m.objList.Btn_Power.gameObject:SetActive(true)
            m.objList.Txt_Num.gameObject:SetActive(true)
        end

        if commonText.DeductGoods ~= "0" then
            local goodsList = Schemes.PrizeTable:GetGoodsList(commonText.DeductGoods)
            if goodsList then
                for i = 1, #goodsList, 1 do
                    if not m.goodsItemList[i] then
                        m.goodsItemList[i] = _GAddSlotItem(m.objList.Grid_Goods)
                    end
                    if goodsList[i] then
                        m.goodsItemList[i]:SetItemID(goodsList[i].id)
                        m.goodsItemList[i]:SetCount(goodsList[i].num)
                        m.goodsItemList[i]:SetSize(80, 80)
                        m.goodsItemList[i].gameObject:SetActive(true)
                    else
                        m.goodsItemList[i].gameObject:SetActive(false)
                    end
                end
            end
        else
            m.objList.Txt_Energy.gameObject:SetActive(false)
        end
end

--------------------------------------------------------------------
--关闭界面
--------------------------------------------------------------------
function m.OnClickClose()
    m:CloseSelf()
end

return m
