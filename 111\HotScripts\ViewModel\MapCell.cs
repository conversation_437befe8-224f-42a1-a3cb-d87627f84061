﻿using UnityEngine;

namespace ViewModel
{
    /// <summary>
    /// 表示地图上一格的范围
    /// </summary>
    public class MapCell
    {
        /// <summary>
        /// 行的范围
        /// </summary>
        public MapRow Row { get; set; }

        /// <summary>
        /// 列的范围
        /// </summary>
        public MapCol Col { get; set; }

        /// <summary>
        /// 获取这一格的中心点
        /// </summary>
        public Vector2 Center => new((Col.MaxX + Col.MinX) / 2, (Row.MaxY + Row.MinY) / 2);
    }
}
