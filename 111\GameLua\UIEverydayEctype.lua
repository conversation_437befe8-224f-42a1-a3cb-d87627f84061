--[[
********************************************************************
    created:	2024/05/28
    author :	李锦剑
    purpose:    每日副本
*********************************************************************
--]]


local luaID = 'UIEverydayEctype'

---每日副本
---@class UIEverydayEctype:UIWndBase
local m = {}

--------------------------------------------------------------------
-- 订阅事件列表
--------------------------------------------------------------------
function m.GetOpenEventList()
    return {
        [EventID.LogicDataChange] = m.UpdateView,
        [EventID.EntitySyncBuff] = m.UpdateView,
        [EventID.OnGoodsPropChange] = m.UpdateView,
        [EventID.ActorDataChanged] = m.UpdateView,
        [EventID.OnHeroPropChange] = m.UpdateView,
        [EventID.ActorDataChanged] = m.UpdateView,
    }
end

--------------------------------------------------------------------
-- 初始化
--------------------------------------------------------------------
function m.OnCreate()
    m.slotItem_List = {}
    m.objList.Txt_Challenge.text = GetGameText(luaID, 3)
    m.objList.Txt_SaoDang.text = GetGameText(luaID, 4)

    m.RegisterClickEvent()
    return true
end

--------------------------------------------------------------------
-- 窗口开启
--------------------------------------------------------------------
function m:OnOpen(ectypeType)
    m.ectypeType = ectypeType

    m.UpdateView()
end

--------------------------------------------------------------------
-- 注册点击事件
--------------------------------------------------------------------
function m.RegisterClickEvent()
    m:AddClick(m.objList.Btn_Close, function()
        m:CloseSelf()
    end)
    m:AddClick(m.objList.Btn_Challenge, function()
        m.ChuZheng(m.selectCatMainStageID)
    end)
end

--------------------------------------------------------------------
--更新界面
--------------------------------------------------------------------
function m.UpdateView()
    local stageID = GamePlayerData.GameEctype:GetChallengeableID(m.ectypeType)
    m.selectCatMainStageID = stageID
    local catMainStage = Schemes.CatMainStage:Get(stageID)
    m.objList.Txt_Title1.text = catMainStage.Name

    local expendList = HelperL.Split(catMainStage.Need, ';')
    --消耗物品ID
    local expID = tonumber(expendList[1]) or 0
    --消耗物品数量
    local expNum = tonumber(expendList[2]) or 0
    --广告物品ID
    -- local adID = tonumber(expendList[3]) or 0
    --消耗物品ID2
    local expID2 = tonumber(expendList[4]) or 0
    --消耗物品数量2
    local expNum2 = tonumber(expendList[5]) or 0

    local count1 = SkepModule:GetGoodsCount(expID)
    local count2 = SkepModule:GetGoodsCount(expID2)
    m.objList.Img_ChallengeGray.gameObject:SetActive(count1 < expNum)
    local color2 = count2 < expNum2 and "#FFA500" or "#ffffff"
    m.objList.Txt_Expend.text = string.format("<color=%s>%s/%s</color>", color2, count2, expNum2)
    m.objList.Txt_Expend.gameObject:SetActive(expNum > 0)
    AtlasManager:AsyncGetGoodsSprite(expID2, m.objList.Img_Expend)

    m.objList.Txt_Num.text = string.format(GetGameText(luaID, 1), math.floor(count1 / expNum))
    m.objList.Txt_Num.gameObject:SetActive(expNum2 > 0)
    m.objList.Txt_Headline2.text = catMainStage.Desc2
    if stageID ~= m.lastStageID then
        m.lastStageID = stageID
        local prizeGoods = Schemes.CatMainStage:GetSucceedRewards(stageID)
        local num = math.max(#prizeGoods, #m.slotItem_List)
        for i = 1, num, 1 do
            if not m.slotItem_List[i] then
                m.slotItem_List[i] = _GAddSlotItem(m.objList.Grid_Goods)
            end
            if prizeGoods[i] then
                m.slotItem_List[i]:SetItemID(prizeGoods[i].ID)
                m.slotItem_List[i]:SetCount(prizeGoods[i].Num)
                m.slotItem_List[i]:SetActive(true)
            else
                m.slotItem_List[i]:SetActive(false)
            end
        end
    end
end

--------------------------------------------------------------------
-- 出征
--------------------------------------------------------------------
function m.ChuZheng(stageID)
    local cfg = Schemes.CatMainStage:Get(stageID)
    if not cfg then
        return
    end

    local expendList = HelperL.Split(cfg.Need, ';')
    --消耗物品ID
    local expID = tonumber(expendList[1]) or 0
    --消耗物品数量
    local expNum = tonumber(expendList[2]) or 0
    --广告物品ID
    -- local adID = tonumber(expendList[3])
    --消耗物品ID2
    local expID2 = tonumber(expendList[4]) or 0
    --消耗物品数量2
    local expNum2 = tonumber(expendList[5]) or 0

    --判断挑战次数
    if HelperL.IsLackGoods(expID, expNum, false, false) then
        HelperL.ShowMessage(TipType.FlowText, GetGameText(luaID, 2))
        return
    end
    --消耗判断
    if HelperL.IsLackGoods(expID2, expNum2, false) then
        return
    end

    m:CloseSelf()
    -- -- 进入战斗
    BattleManager:EnterBattle(cfg.ID)
end

return m
