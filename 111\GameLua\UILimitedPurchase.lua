local luaID = ('UILimitedPurchase')

local UILimitedPurchase = {}

-- 初始化
function UILimitedPurchase:OnCreate(objList)
	self.objList = objList
	self.limitItem = self.objList.LimitItem
	self.limitItem.gameObject:SetActive(false)
	self.itemsContent = self.objList.LimitItemContainer:GetRectTransform()
	self.objList.Txt_LimitedTitle.text = GetGameText(luaID, 1)
	self.btnList = {}
	self.limitItemList = {}
	self.limitConfigList = {}
	self.lastClickTime = 0
	return true
end

function UILimitedPurchase:UpdateView()
	if not ActivityManager:IsActivityOpen(ACTVID.ACTVID_TIMESHOPPING) then
		return
	end
	local beginTime = ActivityManager:GetActivityBeginTime(ACTVID.ACTVID_TIMESHOPPING)
	if beginTime == 0 then return end
	local curTime = HelperL.GetServerTime()
	local stageID = ActivityManager:GetStageIDByActvID(ACTVID.ACTVID_TIMESHOPPING)
	local openDays = math.ceil((curTime - beginTime)/60/60/24)
	for i ,v in ipairs(Schemes.ActivityTimeShopping.items) do
		if v.ID == openDays and stageID == v.StageID then
			self:UpdateItemList(v)
			break
		end
	end
end

function UILimitedPurchase:UpdateItemList(config)
	self.limitConfigList = {}
	local rechargeConfig = Schemes.RechargeCard:Get(config.CardID1)
	table.insert(self.limitConfigList,{name = config.BagName1, oldPrice = config.InitGold1, newPrice = config.NowGold1, prizeID = rechargeConfig.PrizeID, cardID = config.CardID1, bg = 'XS-huangdd', titleBg = 'XS-SX-bth', effectName = 'XS-huangdd-gx'})
	rechargeConfig = Schemes.RechargeCard:Get(config.CardID2)
	table.insert(self.limitConfigList,{name = config.BagName2, oldPrice = config.InitGold2, newPrice = config.NowGold2, prizeID = rechargeConfig.PrizeID, cardID = config.CardID2, bg = 'XS-landd', titleBg = 'XS-SX-btl', effectName = 'XS-landd-gx'})
	if config.CardID3 > 0 then
		rechargeConfig = Schemes.RechargeCard:Get(config.CardID3)
		table.insert(self.limitConfigList,{name = config.BagName3, oldPrice = config.InitGold3, newPrice = config.NowGold3, prizeID = rechargeConfig.PrizeID, cardID = config.CardID3, bg = 'XS-hongdd', titleBg = 'XS-SX-btho', effectName = 'XS-hongdd-gx'})
	end
	self.tweenList = {}
	for i = 1, #self.limitConfigList do
		local limitItem = self.limitItemList[i]
		if not limitItem then
			limitItem = {}
			local obj = GameObject.Instantiate(self.limitItem, self.itemsContent)
			local objTrans = obj:GetRectTransform()
			Helper.FillLuaComps(objTrans, limitItem)
			limitItem.gameObject = obj
			limitItem.objTrans = objTrans
			limitItem.ruleId = 7+i
			limitItem.Btn_Limit.onClick:AddListenerEx(function () 
				self:OnClickBuy(limitItem)
			end)			
			limitItem.Btn_Rule.onClick:AddListenerEx(function () 
				self:OnClickRule(limitItem)
			end)
			limitItem.goodContent = limitItem.LimitPrizeContent:GetRectTransform()
			limitItem.itemList = {}
			table.insert(self.btnList, limitItem.Btn_Limit)
			table.insert(self.limitItemList, limitItem)
		end
		limitItem.data = self.limitConfigList[i]
		limitItem.Txt_BuyEd.text = GetGameText(luaID, 4)
		AtlasManager:AsyncGetSprite(limitItem.data.bg, limitItem.Btn_Limit)
		AtlasManager:AsyncGetSprite(limitItem.data.titleBg, limitItem.Img_LimitItemColor2)
		AtlasManager:AsyncGetSprite(limitItem.data.effectName, limitItem.Img_LimitEffect)
		local prize = Schemes.PrizeTable:GetPrize(limitItem.data.prizeID)
		if prize then  
			local prizeList = Schemes.PrizeTable:GetPrizeGoods(prize)
			if prizeList then
				for k, v in ipairs(limitItem.itemList) do
					v.gameObject:SetActive(false)
				end
				for i=1 , #prizeList do
					local item = limitItem.itemList[i]
					if not item then
						item = CreateSingleGoods(limitItem.goodContent)
						table.insert(limitItem.itemList, item)
					end
					item.objList.Txt_Name.gameObject:SetActive(false)
					item:SetItemData(prizeList[i].ID, prizeList[i].Num)
					item:SetSize(172, 172)
					item:SetVisible(true)
					item:SetActiveImg_BG(false)
					HelperL.AddFloatTween(item.objList.Img_Icon.gameObject.transform, 1, 5, 0.5)
					table.insert(self.tweenList, item.objList.Img_Icon.gameObject.transform)
				end
			end
		end
		
		limitItem.index = i
		limitItem.gameObject.name = "limitItem"..i
		limitItem.gameObject:SetActive(true)
		limitItem.Txt_LimitName.text = self.limitConfigList[i].name
		limitItem.Txt_LimitOnce.text = GetGameText(luaID, 7)
		limitItem.Txt_LimitPriceOld.text = self.limitConfigList[i].oldPrice
		local rechargeConfig = Schemes.RechargeCard:Get(limitItem.data.cardID)
		if rechargeConfig then
			limitItem.Txt_LimitPriceNew.text = rechargeConfig.FirstRMB/100
		end

		local bgTrans = limitItem.Img_LimitEffect.transform
		bgTrans:DOKill()
		bgTrans:DOLocalRotate(bgTrans.eulerAngles + Vector3(0, 0, 360), 2, DG.Tweening.RotateMode.FastBeyond360):SetEase(TweeningEase.Linear, 1):SetLoops(-1, TweeningLoopType.Restart)	

		
	end
	self:OnSecondUpdate()
	self:UpdateBtn()
end

function UILimitedPurchase:UpdateBtn()
	for i,v in ipairs(self.limitItemList) do
		local flag = HeroDataManager:GetLogicBit(LOGIC_DATA.DATA_TIMESHOPPING, v.index-1)
	    if flag ~= 0 then
			v.Img_LimitPriceNew.gameObject:SetActive(false)
			v.Txt_LimitPriceNew.gameObject:SetActive(false)
			v.Img_LimitPriceOld.gameObject:SetActive(false)
			v.Txt_LimitPriceOld.gameObject:SetActive(false)
			v.Img_LimitPriceLine.gameObject:SetActive(false)
			v.Img_LimitEffect.gameObject:SetActive(false)
			v.Txt_BuyEd.gameObject:SetActive(true)
	    else
			v.Img_LimitPriceNew.gameObject:SetActive(true)
			v.Txt_LimitPriceNew.gameObject:SetActive(true)
			v.Img_LimitPriceOld.gameObject:SetActive(true)
			v.Txt_LimitPriceOld.gameObject:SetActive(true)
			v.Img_LimitPriceLine.gameObject:SetActive(true)
			v.Img_LimitEffect.gameObject:SetActive(true)
			v.Txt_BuyEd.gameObject:SetActive(false)
	    end
   end
end 

function UILimitedPurchase:OnClickRule(item)
	if not item then return end	
	local cancelParam = {btnName = GetGameText(luaID, 11), icon = nil, value = '', desc = '', callback = nil, paramArgs = nil}	
	UIManager:OpenWnd(WndID.ShowUnlockBox, GetGameText(luaID, 12), GetGameText(luaID, item.ruleId), cancelParam, nil, nil)
end
-- 点击领取奖励
function UILimitedPurchase:OnClickBuy(item)
	if not item then return end	
    if self.lastClickTime > os.time() then
		HelperL.ShowMessage(TipType.FlowText, string.format(GetGameText(luaID, 14), math.floor(self.lastClickTime - os.time())))
		return
	end 
	local flag = HeroDataManager:GetLogicBit(LOGIC_DATA.DATA_TIMESHOPPING, item.index-1)
	if flag ~= 0 then 
		HelperL.ShowMessage(TipType.FlowText, GetGameText(luaID, 13))
		return 
	end

	local isOpen = ActivityManager:IsActivityOpen(ACTVID.ACTVID_TIMESHOPPING)
	if not isOpen then return end
	--充值
	self.lastClickTime = os.time()+10
	HelperL.Recharge(item.data.cardID)
end
 
-- 每秒更新
function UILimitedPurchase:OnSecondUpdate()	
	local isOpen = ActivityManager:IsActivityOpen(ACTVID.ACTVID_TIMESHOPPING)
	for i ,v in ipairs(self.limitItemList) do
		if isOpen then
			local curDate = Premier.Instance:GetDateTime(HelperL.GetServerTime())
			local endDate = Premier.Instance:GetDateTime(ActivityManager:GetActivityEndTime(ACTVID.ACTVID_TIMESHOPPING))
			local diff = endDate - curDate
			v.Txt_LimitRestTime.text = string.format('<color=#00ff00>%d:%02d:%02d</color>', diff.Hours, diff.Minutes, diff.Seconds)	
		else  
			v.Txt_LimitRestTime.text = GetGameText(luaID, 6)
		end
	end	
end

function UILimitedPurchase:OnClose()
	if self.tweenList then
		for i, tran in ipairs(self.tweenList) do
			HelperL.RemoveFloatTween(tran)
		end
	end
	self.tweenList = nil
end

-- 窗口销毁
function UILimitedPurchase:OnDestroy()
	-- 释放内存
	for i, btn in ipairs(self.btnList) do
		btn.onClick:RemoveAllListeners()
		btn.onClick:Invoke()
	end
end

return UILimitedPurchase