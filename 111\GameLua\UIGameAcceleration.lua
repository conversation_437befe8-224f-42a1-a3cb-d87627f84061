--[[
********************************************************************
    created:	2024/06/28
    author :	李锦剑
    purpose:    看广告加速游戏
*********************************************************************
--]]

local luaID = 'UIGameAcceleration'

--广告ID
local adID = 100

---看广告加速游戏
---@class UIGameAcceleration:UIWndBase
local m = {}

--------------------------------------------------------------------
-- 初始化
--------------------------------------------------------------------
function m.OnCreate()
    m.objList.Txt_Title.text = GetGameText(luaID, 2)
    m.RegisterClickEvent()
    return true
end

--------------------------------------------------------------------
-- 窗口开启
--------------------------------------------------------------------
function m:OnOpen()
    LuaToCshapeManager.Instance:PauseOrResumeBattle(0)
    m.UpdateView()
end

--------------------------------------------------------------------
-- 注册点击事件
--------------------------------------------------------------------
function m.RegisterClickEvent()
    m.objList.Btn_Close.onClick:AddListenerEx(m.OnClickClose)
    m.objList.Btn_AD.onClick:AddListener(m.LookAD)
end

--------------------------------------------------------------------
-- 更新界面
--------------------------------------------------------------------
function m.UpdateView()
    local commonText = Schemes.CommonText:Get(adID)
    local lv = HeroDataManager:GetLogicByte(commonText.Param2, commonText.Param3)
    local num = commonText.ToTime - lv
    m.objList.Txt_Content.text = string.format(GetGameText(luaID, 1), num)
end

--------------------------------------------------------------------
-- 看广告
--------------------------------------------------------------------
function m.LookAD()
    AdvertisementManager.ShowRewardAd(adID, function(bool)
        LuaEventToCsharp.Instance:Notify("看广告加速adID=100", { tostring(bool) })
        m.OnClickClose()
    end, true)
end

--------------------------------------------------------------------
-- 关闭事件
--------------------------------------------------------------------
function m.OnClickClose()
    LuaToCshapeManager.Instance:PauseOrResumeBattle(1)
    m:CloseSelf()
end

return m
