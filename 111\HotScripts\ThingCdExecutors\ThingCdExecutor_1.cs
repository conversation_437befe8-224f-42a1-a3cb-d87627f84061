// ReSharper disable InconsistentNaming

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;

using Apq.Extension;
using Apq.Unity3D.UnityHelpers;

using Cysharp.Threading.Tasks;

using DataStructure;

using Props;

using RxEventsM2V;

using Thing;

using UniRx;

using UnityEngine;

using View;

using X.PB;

using CsvTables;

namespace ThingCdExecutors
{
    /// <summary>
    ///     玩家阵营的连射
    /// </summary>
    public class ThingCdExecutor_1 : ActorGunCdExecutor
    {
        /// <inheritdoc />
        public override async UniTaskVoid DoShoot(CancellationToken token)
        {
            // // V52.1 获取当前技能信息
            string skillName = GunThing.CsvRow_Gun.Value?.Name ?? "未知连射技能";
            int currentPriority = GetCurrentAttackPriority();
            float globalCooldown = GetCurrentGlobalCooldown();

            Debug.Log($"V52.1 连射技能尝试发射: {skillName}, 优先级={currentPriority}, 公共CD={globalCooldown}秒");

            // // V52.1 使用新的技能发射协调机制
            if (Actor != null && !Actor.RequestSkillFire(skillName, currentPriority, GunThing, globalCooldown))
            {
                Debug.Log($"V52.1 连射技能发射请求被拒绝: {skillName}");
                return;
            }

            // 找一个敌人作为攻击方向
            DistanceThing distanceEnemy = Thing.FindEnemy();

            // 没有攻击目标，不发子弹
            if (distanceEnemy is not { Thing2: not null })
            {
                return;
            }

            Debug.Log($"V52.1 连射技能通过所有检查，开始发射: {skillName}, 优先级={currentPriority}");

            // 按一轮攻击的持续时长预设结束时间
            base.DoShoot(token).Forget();

            await UniTask.SwitchToMainThread();

            // 确保此方法用于角色的枪
            if (Actor == null)
            {
                return;
            }

            CancellationTokenSource cts_Skill = CancellationTokenSource.CreateLinkedTokenSource(
                CTS_Shooter.Token
                , Actor.ThingBehaviour.GetCancellationTokenOnDestroy()
            );

            try
            {
                // 射击的基准方向
                Vector3 shootDir = (distanceEnemy.Thing2.Position - Actor.Position).normalized;

                int shootTimes = (int)Thing.GetTotalLong(PropType.BurstShootTimes).FirstOrDefault();
                List<double> burstDelayList = Thing.GetTotalDouble(PropType.BurstDelayList);
                List<long> burstBulletCountList = Thing.GetTotalLong(PropType.BurstBulletCountList);
                // 各轮射击角度的ID列表
                List<int> burstburstAnglesIds =
                    Thing.GetTotalLong(PropType.BurstAnglesIdList).Select(x => (int)x).ToList();

                // 根据配置延时后射击
                _ = burstDelayList.Take(shootTimes).Select((x, i) =>
                {
                    if (burstBulletCountList.Count <= i)
                    {
                        return i;
                    }

                    long bulletQty = burstBulletCountList[i];

                    BurstOne(cts_Skill.Token, (float)x, distanceEnemy.Thing2, null, shootDir, bulletQty,
                            burstburstAnglesIds.IndexOf_ByCycle(i))
                        .Forget();
                    return i;
                }).ToList();
            }
            catch
            {
                // ignore
            }
        }

        /// <summary>
        ///     发射一轮
        /// </summary>
        /// <param name="token"></param>
        /// <param name="delay">延时:秒</param>
        /// <param name="attackBaseDirFollowThing">攻击基准方向跟随哪个物件(没值则取移动方向)</param>
        /// <param name="trackPos">定点位置</param>
        /// <param name="shootBaseDir">射击的基准方向</param>
        /// <param name="bulletQty">发射的子弹数量</param>
        /// <param name="anglesPropId">往哪个角度发射</param>
        private async UniTaskVoid BurstOne(CancellationToken token, float delay, ThingBase attackBaseDirFollowThing,
            Vector3? trackPos, Vector3 shootBaseDir, float bulletQty, int anglesPropId)
        {
            try
            {
                await UniTask.Delay(TimeSpan.FromSeconds(delay), cancellationToken: token);

                if (token.IsCancellationRequested)
                {
                    return;
                }

                // 没有发射角度就不射击
                if (!SingletonMgr.Instance.GlobalMgr.CommonPropCfg.TryGetValue(anglesPropId, out CommonProp anglesProp))
                {
                    return;
                }

                // 开火声音
                MessageBroker.Default.Publish(new PlayShootSound { Shooter = this });
                //Debug.Log($"一轮发射子弹数:{bulletQty}");

                List<float> angles = anglesProp.DoubleValues
                    .Select(x => (float)x).ToList();

                for (int z = 0; z < bulletQty; z++)
                {
                    float angle = angles.IndexOf_ByCycle(z);
                    int maxPenetrateTimes = (int)Thing.GetTotalLong(PropType.MaxPenetrateTimes).FirstOrDefault();
                    int maxBounceTimes = (int)Thing.GetTotalLong(PropType.MaxBounceTimes).FirstOrDefault();
                    int maxSeparateTimes = (int)Thing.GetTotalLong(PropType.MaxSeparateTimes).FirstOrDefault();

                    // 发射方向依次按配置的角度旋转
                    BulletThing bullet = Thing.CreateBullet(this, attackBaseDirFollowThing, trackPos, angle,
                        maxPenetrateTimes, maxBounceTimes, maxSeparateTimes);

                    Vector3 dir_1 = shootBaseDir.RotateAround(Vector3.forward, angle);
                    
                    // 根据角色朝向计算前方偏移
                    float forwardOffsetX = 2f;
                    if (Actor.ThingBehaviour != null)
                    {
                        var skeAni = Actor.ThingBehaviour.GetComponentInChildren<Spine.Unity.SkeletonAnimation>();
                        if (skeAni != null && skeAni.skeleton != null)
                        {
                            // ScaleX = -1 表示朝右，ScaleX = 1 表示朝左
                            forwardOffsetX = skeAni.skeleton.ScaleX == -1 ? 2f : -2f;
                        }
                    }
                    
                    bullet.Position = Actor.Position + new Vector3(forwardOffsetX, 6f, 0); // 根据角色朝向从前方+2、往上Y+6的位置发射
                    bullet.MoveDirection_Straight.Value = dir_1;

                    MessageBroker.Default.Publish(new BornBullet
                    {
                        Bullet = bullet, PositionPre = bullet.Position - dir_1
                    });
                }
            }
            catch (OperationCanceledException) { throw; }
            catch (MissingReferenceException) { }
            catch (Exception ex)
            {
                Debug.LogException(ex);
            }
            //catch { }
        }

        /// <summary>
        // /// V52.0 获取当前技能的攻击动作优先级
        /// 通过解析子弹配置中的Icon字段获取
        /// </summary>
        /// <returns>攻击动作优先级，默认返回0</returns>
        private int GetCurrentAttackPriority()
        {
            try
            {
                // 获取子弹ID
                int bulletId = (int)GunThing.GetTotalLong(X.PB.PropType.BulletId).FirstOrDefault();
                if (bulletId <= 0) return 0;

                // 获取子弹配置
                if (!SingletonMgr.Instance.CsvLoaderMgr.GetOrAddLoader<BulletCsv>().Dic.TryGetValue(bulletId, out var bulletCfg))
                    return 0;

                // 解析Icon字段获取优先级
                string iconConfig = bulletCfg.Icon;
                if (string.IsNullOrWhiteSpace(iconConfig) || iconConfig == "0") return 0;

                string[] parameters = iconConfig.Split(';');
                if (parameters.Length < 4) return 0;

                if (int.TryParse(parameters[3], out int priority))
                {
                    return priority;
                }
            }
            catch (System.Exception ex)
            {
                Debug.LogWarning($"V52.0 获取连射攻击动作优先级失败: {ex.Message}");
            }

            return 0; // 默认优先级
        }

        /// <summary>
        // /// V56.0 获取当前技能的公共CD时长
        /// 根据连射次数和时间间隔计算：公共CD = 最后一次连射需要的时间 + 0.2秒
        /// </summary>
        /// <returns>公共CD时长（秒），默认返回0</returns>
        private float GetCurrentGlobalCooldown()
        {
            try
            {
                // 获取连射配置
                int shootTimes = (int)Thing.GetTotalLong(PropType.BurstShootTimes).FirstOrDefault();
                List<double> burstDelayList = Thing.GetTotalDouble(PropType.BurstDelayList);

                if (shootTimes <= 0 || burstDelayList.Count == 0)
                {
                    return 0.2f; // 没有连射配置时，最小公共CD为0.2秒
                }

                // 计算最后一次连射的时间
                float lastShootTime = 0f;
                for (int i = 0; i < shootTimes && i < burstDelayList.Count; i++)
                {
                    lastShootTime = Math.Max(lastShootTime, (float)burstDelayList[i]);
                }

                // // V56.0 公共CD = 最后一次连射时间 + 0.2秒
                float globalCooldown = lastShootTime + 0.2f;

                Debug.Log($"V56.0 连射技能公共CD计算: 连射次数={shootTimes}, 最后连射时间={lastShootTime}秒, 公共CD={globalCooldown}秒");
                
                return globalCooldown;
            }
            catch (System.Exception ex)
            {
                Debug.LogWarning($"V56.0 获取连射公共CD失败: {ex.Message}，使用默认0.2秒");
            }

            return 0.2f; // 默认公共CD
        }
    }
}