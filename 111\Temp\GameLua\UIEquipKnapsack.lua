--[[
********************************************************************
    created:    2024/02/08
    author :    李锦剑
    purpose:    装备背包
*********************************************************************
--]]
local luaID = ('UIEquipKnapsack')

---@class UIEquipKnapsack:UIWndBase 装备宝箱界面
local m = {}

--------------------------------------------------------------------
-- 订阅事件列表
--------------------------------------------------------------------
function m.GetOpenEventList()
    return {
        [EventID.OnSkepGoodsChange] = m.UpdateView,
        [EventID.StoreBuyItem] = m.UpdateView,
        [EventID.LogicDataChange] = m.UpdateView,
        [EventID.OnHeroPropChange] = m.UpdateView,
        [EventID.EquipmentReplace] = m.UpdateView,
        [EventID.ActorDataChanged] = m.UpdateView,
    }
end

--------------------------------------------------------------------
--预制体自适应
--------------------------------------------------------------------
function m.AdaptScale()
    HelperL.AdaptScale_Width(m.objList.Item_AttributeBox)
    HelperL.AdaptScale(m.objList.Img_Bg1, 6)
    HelperL.AdaptScale(m.objList.Img_Bg2, 6)
end

--------------------------------------------------------------------
--创建时事件
--------------------------------------------------------------------
function m.OnCreate()
    m.lastTime = 0
    --已穿戴装备框
    ---@type SlotItem[]
    m.equipItem = {}
    --背包装备框
    ---@type SlotItem[]
    m.knapsackEquipItem = {}
    --角色框
    ---@type Item_Role[]
    m.Item_Role_List = {}

    --圣物ID
    ---@type integer[]
    m.EquipWeaponIDList = {}
    for i, v in ipairs(Schemes.EquipWeapon.items) do
        if v.GroupID < 1000 then
            table.insert(m.EquipWeaponIDList, v.ID)
        end
    end

    m.RegisterClickEvent()
    m.ToggleView(2)
    return true
end

--------------------------------------------------------------------
--打开时
--------------------------------------------------------------------
function m.OnOpen()
    -- m.UpdateView()
    m.UpdateView2()
end

--------------------------------------------------------------------
--注册点击事件
--------------------------------------------------------------------
function m.RegisterClickEvent()
    m:AddClick(m.objList.Btn_Close, function()
        m:CloseSelf()
    end)
    m:AddClick(m.objList.Btn_Close, function()
        m:CloseSelf()
    end)
    m:AddClick(m.objList.Btn_Synthesis, function()
        UIManager:OpenWnd(WndID.EquipSynthesis)
    end)
    m:AddClick(m.objList.Btn_Role, function()
        m.ToggleView(1)
    end)
    m:AddClick(m.objList.Btn_Knapsack, function()
        m.ToggleView(2)
    end)

    m:AddClick(m.objList.Btn_Equip1, function()
        -- local weaponsKnapsack = GamePlayerData.ActorEquip:GetWeaponsKnapsack(1) or {}
        -- local equipID = weaponsKnapsack[1] or 0
        -- if not equipID then return end
        -- GamePlayerData.ActorEquip:ReplaceEquipIndex(equipID, 1, 1)
    end)
    m:AddClick(m.objList.Btn_Upgrade2, function()
        local weaponsKnapsack = GamePlayerData.ActorEquip:GetWeaponsKnapsack(1) or {}
        local equipID = weaponsKnapsack[1] or 0
        m.objList.Btn_UpBox.gameObject:SetActive(true)
        m.ShowBulletInfo(equipID)
    end)

    m:AddClick(m.objList.Btn_UpBox, function()
        m.objList.Btn_UpBox.gameObject:SetActive(false)
    end)

    m:AddClick(m.objList.Btn_Upgrade1, function()
        m.Upgrade()
    end)

    -- 添加帮助按钮点击事件
    if m.objList.Btn_Help then
        m:AddClick(m.objList.Btn_Help, function()
            SoundManager:PlaySound(7111)
            m.ShowEquipTip()
        end)
    end


end

--------------------------------------------------------------------
--更新装备框
---@param id integer 1:角色界面 2:返回背包界面
--------------------------------------------------------------------
function m.ToggleView(id)
    m.objList.Btn_Role.gameObject:SetActive(id ~= 1)
    m.objList.Obj_Role.gameObject:SetActive(id == 1)

    m.objList.Btn_Knapsack.gameObject:SetActive(id == 1)
    m.objList.Obj_Knapsack.gameObject:SetActive(id ~= 1)

    if id == 1 then
        m.UpdateRole()
    elseif id == 2 then
        if m.ItemRole ~= nil then
            m.ItemRole.Img_Select.gameObject:SetActive(false)
        end
        m.ItemRole = nil
        m.UpdateUIModel()
    end
end

--------------------------------------------------------------------
--更新装备框
--------------------------------------------------------------------
function m.UpdateEquip()
    local skepEuip = SkepModule.GetEquipSkep()
    local entity, item
    local attack = 0
    local hp = 0
    for i, v in ipairs(EquipOrder) do
        if not m.equipItem[i] then
            if i < 5 then
                m.equipItem[i] = _GAddSlotItem(m.objList.Grid_Equip)
                m:CollectChild(m.objList.Grid_Equip, m.equipItem[i])
            else
                m.equipItem[i] = _GAddSlotItem(m.objList.Grid_Equip2)
                m:CollectChild(m.objList.Grid_Equip2, m.equipItem[i])
            end
        end

        entity = EntityModule:GetEntity(skepEuip[v])
        item = m.equipItem[i]
        item:SetEntity(entity)
        m.equipItem[i]:SetClick(m.OnClickEquip, entity)
        if entity then
            item:SetRedDot(RedDotCheckFunc:Check_EquipJinJie(entity))
            item:SetDes(nil)
            local equipID = entity:GetProperty(ENTITY_FIELD.ENTITY_FIELD_ID)
            local cfg = Schemes.Equipment:Get(equipID)
            local level = GamePlayerData.ActorEquipNew:GetEquipUpgradeLevel(cfg.SmeltID)
            if level > 0 then
                item:SetDes3('+' .. level)
            end
            local equipSmeltStar = Schemes.EquipSmeltStar:GetEquipSmeltStarCfg(cfg.SmeltID, level)
            if equipSmeltStar then
                if cfg.QualityLevel < equipSmeltStar.AttackList.Length then
                    attack = attack + equipSmeltStar.AttackList[cfg.QualityLevel]
                end
                if cfg.QualityLevel < equipSmeltStar.MaxHpList.Length then
                    hp = hp + equipSmeltStar.MaxHpList[cfg.QualityLevel]
                end
            end
        else
            item:SetRedDot(false)
            item:SetDes(GetEquipTypeDescribe(v))
        end
    end
    m.objList.Txt_Fight1.text = GetGameText(luaID, 12) .. HelperL.GetChangeNum(attack)
    m.objList.Txt_Fight2.text = GetGameText(luaID, 13) .. HelperL.GetChangeNum(hp)
end

--------------------------------------------------------------------
--更新背包
--------------------------------------------------------------------
function m.UpdateKnapsack()
    m.objList.Img_Role_RedDot.gameObject:SetActive(RedDotCheckFunc:Check_EquipKnapsack_ShengJiJueSe())
    m.objList.Img_Knapsack_RedDot.gameObject:SetActive(
        RedDotCheckFunc:Check_EquipKnapsack_TuiJian() or
        RedDotCheckFunc:Check_EquipKnapsack_ShengJi()
    )
    m.objList.Img_Synthesis_RedDot.gameObject:SetActive(RedDotCheckFunc:Check_UIEquipSynthesis())

    local skepBag = SkepModule:GetSkepByID(SKEPID.SKEPID_PACKET)
    if not skepBag then
        warn('UIRole:CreateSlots，背包篮子都找不到')
        return
    end

    local equips = {}
    local goodsID, entity
    -- 背包里的数据
    for i = 0, skepBag.indexMaxsize do
        entity = EntityModule:GetEntity(skepBag[i])
        if entity then
            goodsID = entity:GetProperty(ENTITY_FIELD.ENTITY_FIELD_ID)
            if HelperL.IsEuipType(goodsID) then
                table.insert(equips, entity)
            end
        end
    end
    table.sort(equips, function(a, b)
        --装备推荐--排序
        local int_A = HelperL.IsRecommendedEquipment(a) and 1 or 0
        local int_B = HelperL.IsRecommendedEquipment(b) and 1 or 0
        if int_A ~= int_B then
            return int_A > int_B
        end

        local goodsID_A = a:GetProperty(ENTITY_FIELD.ENTITY_FIELD_ID)
        local goodsID_B = b:GetProperty(ENTITY_FIELD.ENTITY_FIELD_ID)
        local cfg_A = Schemes.Equipment:Get(goodsID_A)
        local cfg_B = Schemes.Equipment:Get(goodsID_B)

        --装备品质--排序
        if cfg_A.QualityLevel ~= cfg_B.QualityLevel then
            return cfg_A.QualityLevel > cfg_B.QualityLevel
        end

        --装备部位--排序
        if cfg_A.SubType ~= cfg_B.SubType then
            return cfg_A.SubType > cfg_B.SubType
        end

        --装备等级--排序
        if cfg_A.UseLevel ~= cfg_B.UseLevel then
            return cfg_A.UseLevel > cfg_B.UseLevel
        end

        --装备ID--排序
        return goodsID_A > goodsID_B
    end)

    local item
    --最少要显示100个格子
    local num = math.max(100, #equips, #m.knapsackEquipItem)
    for i = 1, num, 1 do
        if not m.knapsackEquipItem[i] then
            m.knapsackEquipItem[i] = _GAddSlotItem(m.objList.Grid_Knapsack)
            m:CollectChild(m.objList.Grid_Knapsack, m.knapsackEquipItem[i])
        end
        item = m.knapsackEquipItem[i]
        item:SetClick(m.OnClickEquip2, equips[i])
        item:SetEntity(equips[i])
        item:SetActive(equips[i] ~= nil or i <= 100)
        if equips[i] then
            --item:SetRedDot(HelperL.IsRecommendedEquipment(equips[i]))
            --else
            item:SetRedDot(false)
        end
    end
end

--------------------------------------------------------------------
--装备点击事件
--------------------------------------------------------------------
function m.OnClickEquip(itemID, entity)
    if not entity then return end
    UIManager:OpenWnd(WndID.EquipUpgrade, entity.uid)
end

--------------------------------------------------------------------
--装备点击事件
--------------------------------------------------------------------
function m.OnClickEquip2(itemID, entity)
    if not entity then return end
    UIManager:OpenWnd(WndID.TipBestEquipment, entity.uid)
end

--------------------------------------------------------------------
--更新角色模型
--------------------------------------------------------------------
function m.UpdateUIModel()
    local weaponsKnapsack = GamePlayerData.ActorEquip:GetWeaponsKnapsack(1) or {}
    local equipID = weaponsKnapsack[1] or 0
    local cfg = Schemes.Equipment:Get(equipID)
    local level = GamePlayerData.ActorEquipNew:GetEquipUpgradeLevel(cfg.SmeltID)
    m.objList.Txt_Level.text = GetGameText(luaID, 6) .. level .. CommonTextID.LEVEL
    m.objList.Txt_EquipName.text = cfg.GoodsName
    --AtlasManager:AsyncGetSprite(HelperL.GetGunIcon(equipID), m.objList.Img_EquipIcon, true)

    --暂时屏蔽

    local gun = Schemes.Gun:Get(cfg.ConsignmentStyle)
    local bullet_List = Schemes.Bullet:GetByBulletId(gun.BulletId)
    local trans = m.objList.Obj_Model.transform
    for c = trans.childCount - 1, 0, -1 do
        if trans:GetChild(c) then
            GameObject.Destroy(trans:GetChild(c).gameObject)
        end
    end
    local parameter
    ResMgr.LoadGameObjectAsync("model/uiSpine/" .. bullet_List[1].Model, function(obj, parameter)
        local model = GameObject.Instantiate(obj, m.objList.Obj_Model.transform)
    end, parameter)
end

--------------------------------------------------------------------
--更新角色模型
--------------------------------------------------------------------
function m.ChangeUIModel(equipID)
    local cfg = Schemes.Equipment:Get(equipID)
    local level = GamePlayerData.ActorEquipNew:GetEquipUpgradeLevel(cfg.SmeltID)
    m.objList.Txt_Level.text = GetGameText(luaID, 6) .. level .. CommonTextID.LEVEL
    m.objList.Txt_EquipName.text = cfg.GoodsName
    --暂时屏蔽

    local gun = Schemes.Gun:Get(cfg.ConsignmentStyle)
    local bullet_List = Schemes.Bullet:GetByBulletId(gun.BulletId)
    local trans = m.objList.Obj_Model.transform
    for c = trans.childCount - 1, 0, -1 do
        if trans:GetChild(c) then
            GameObject.Destroy(trans:GetChild(c).gameObject)
        end
    end
    local parameter
    ResMgr.LoadGameObjectAsync("model/uiSpine/" .. bullet_List[1].Model, function(obj, parameter)
        local model = GameObject.Instantiate(obj, m.objList.Obj_Model.transform)
    end, parameter)
end

--------------------------------------------------------------------
--更新角色
--------------------------------------------------------------------
function m.UpdateRole()
    if not m.objList.Obj_Role.gameObject.activeSelf then
        return
    end
    local dataList = m.EquipWeaponIDList
    for i, v in ipairs(dataList) do
        if not m.Item_Role_List[i] then
            m.Item_Role_List[i] = m.Creation_Item_Role(i)
        end
        m.Item_Role_List[i].UpdateData(v)
    end
end

--------------------------------------------------------------------
--更新界面(延迟更新)
--------------------------------------------------------------------
function m.UpdateView()
    if not m.timer then
        m.timer = Timer.New(function()
            m.UpdateView2()
            m.timer = nil
        end, 0.5, 1)
        m.timer:Start()
    end
end

--------------------------------------------------------------------
--更新界面(立即更新)
--------------------------------------------------------------------
function m.UpdateView2()
    m.UpdateUIModel()
    m.UpdateEquip()
    m.UpdateKnapsack()
    m.UpdateRole()
    local weaponsKnapsack = GamePlayerData.ActorEquip:GetWeaponsKnapsack(1) or {}
    local equipID = weaponsKnapsack[1] or 0
    m.ShowBulletInfo(equipID)
end

m.ItemRole = nil
--------------------------------------------------------------------
--创建属性框
--------------------------------------------------------------------
function m.Creation_Item_Role(index)
    ---@class Item_Role
    local item = {}
    item.index = index
    item.com = m:CreateSubItem(m.objList.Grid_Role, m.objList.Item_Role)
    m:AddClick(item.com.Btn_Equip, function()
        if not item.equipID then return end
        -- if GamePlayerData.ActorEquip:IsWear(item.equipID, 1) then
        --     return
        -- end
        if m.ItemRole ~= nil then
            m.ItemRole.Img_Select.gameObject:SetActive(false)
        end
        GamePlayerData.ActorEquip:ReplaceEquipIndex(item.equipID, 1, 1)
    end)
    m:AddClick(item.com.Btn_Upgrade, function()
        if not item.equipID then return end
        m.Upgrade(item.equipID)
    end)
    m:AddClick(item.com.Btn_Select, function()
        if m.ItemRole == item.com then return end
        if m.ItemRole ~= nil then
            m.ItemRole.Img_Select.gameObject:SetActive(false)
        end
        m.ItemRole = item.com
        m.ItemRole.Img_Select.gameObject:SetActive(true)
        m.ChangeUIModel(item.equipID)
    end)
    ---更新数据
    ---@param equipID integer
    item.UpdateData = function(equipID)
        item.equipID = equipID
        if equipID then
            local cfg = Schemes.Equipment:Get(equipID)
            local level = GamePlayerData.ActorEquipNew:GetEquipUpgradeLevel(cfg.SmeltID)
            local equipSmeltStar = Schemes.EquipSmeltStar:GetEquipSmeltStarCfg(cfg.SmeltID, level)
            local attack = 0
            local hp = 0
            if equipSmeltStar then
                if cfg.QualityLevel < equipSmeltStar.AttackList.Length then
                    attack = attack + equipSmeltStar.AttackList[cfg.QualityLevel]
                end
                if cfg.QualityLevel < equipSmeltStar.MaxHpList.Length then
                    hp = hp + equipSmeltStar.MaxHpList[cfg.QualityLevel]
                end

                local num1 = 0
                if equipSmeltStar.CostGoodsID1 > 0 then
                    num1 = SkepModule:GetGoodsCount(equipSmeltStar.CostGoodsID1)
                    local color1 = num1 < equipSmeltStar.CostGoodsID1Num and UI_COLOR.Red or UI_COLOR.White
                    item.com.Txt_Expend1.text = string.format("<color=%s>%s/%s</color>", color1,
                        HelperL.GetChangeNum(num1),
                        equipSmeltStar.CostGoodsID1Num)
                    AtlasManager:AsyncGetGoodsSprite(equipSmeltStar.CostGoodsID1, item.com.Img_Expend1)
                    item.com.Txt_Expend1.gameObject:SetActive(true)
                else
                    item.com.Txt_Expend1.gameObject:SetActive(false)
                end

                local num2 = 0
                if equipSmeltStar.CostGoodsID2 > 0 then
                    num2 = SkepModule:GetGoodsCount(equipSmeltStar.CostGoodsID2)
                    local color2 = num2 < equipSmeltStar.CostGoodsID2Num and UI_COLOR.Red or UI_COLOR.White
                    item.com.Txt_Expend2.text = string.format("<color=%s>%s/%s</color>", color2,
                        HelperL.GetChangeNum(num2),
                        equipSmeltStar.CostGoodsID2Num)
                    AtlasManager:AsyncGetGoodsSprite(equipSmeltStar.CostGoodsID2, item.com.Img_Expend2)
                    item.com.Txt_Expend2.gameObject:SetActive(true)
                else
                    item.com.Txt_Expend2.gameObject:SetActive(false)
                end

                if num1 >= equipSmeltStar.CostGoodsID1Num and num2 >= equipSmeltStar.CostGoodsID2Num then
                    HelperL.SetImageGray(item.com.Btn_Upgrade, false)
                else
                    HelperL.SetImageGray(item.com.Btn_Upgrade, true)
                end

                item.com.Btn_Upgrade.gameObject:SetActive(true)
            else
                item.com.Btn_Upgrade.gameObject:SetActive(false)
            end

            AtlasManager:AsyncGetSprite(HelperL.GetGunIcon(equipID), item.com.Img_Icon, true)
            item.com.Txt_Name.text = cfg.GoodsName
            item.com.Txt_Level.text = GetGameText(luaID, 6) .. level .. CommonTextID.LEVEL
            item.com.Txt_Attack.text = GetGameText(luaID, 4) .. HelperL.GetChangeNum(attack)
            item.com.Txt_HP.text = GetGameText(luaID, 5) .. HelperL.GetChangeNum(hp)

            --获取最大等级
            local maxLevel = PropertyCompute.GetEquipSmeltStarMaxLevel(cfg.SmeltID)
            if level == 0 then
                item.com.Txt_Upgrade.text = GetGameText(luaID, 7)
                item.com.Btn_Equip.gameObject:SetActive(false)
                item.com.Obj_Unlocked.gameObject:SetActive(true)
            else
                --是否是满级
                if level >= maxLevel then
                    item.com.Txt_Upgrade.text = CommonTextID.IS_FULL_LEVEL
                else
                    item.com.Txt_Upgrade.text = GetGameText(luaID, 8)
                end

                if GamePlayerData.ActorEquip:IsWear(equipID, 1) then
                    item.com.Txt_Equip.text = GetGameText(luaID, 10)
                else
                    item.com.Txt_Equip.text = GetGameText(luaID, 9)
                end

                item.com.Btn_Equip.gameObject:SetActive(true)
                item.com.Obj_Unlocked.gameObject:SetActive(false)
            end

            item.com.gameObject:SetActive(true)
            local weaponsKnapsack = GamePlayerData.ActorEquip:GetWeaponsKnapsack(1) or {}
            local curEquipID = weaponsKnapsack[1] or 0
            if curEquipID == equipID then
                m.ItemRole = item.com
                m.ItemRole.Img_Select.gameObject:SetActive(true)
            end
        else
            item.com.gameObject:SetActive(false)
        end
    end
    return item
end

--------------------------------------------------------------------
--请求回调
--------------------------------------------------------------------
function m.RequestCallback(result, content)
    if result == RESULT_CODE.RESULT_COMMON_SUCCEED then
        -- m.UpdateView()
        local starLvl = GamePlayerData.ActorEquipNew:GetEquipUpgradeLevel(m.SmeltID)
        if starLvl == 0 then
            HelperL.ShowMessage(TipType.FlowText, GetGameText(luaID, 14))
        else
            HelperL.ShowMessage(TipType.FlowText, GetGameText(luaID, 11))
        end
    else
        HelperL.ShowMessage(TipType.FlowText, ResultCode:GetResultCodeStr(result))
    end
end

--------------------------------------------------------------------
--升级
---@param equipID integer 装备ID
--------------------------------------------------------------------
function m.Upgrade(equipID)
    local curTime = os.clock()
    if curTime - m.lastTime < 0.3 then
        print('天赋升级点太快了1秒后尝试！')
        return
    end
    m.lastTime = curTime

    local cfg = Schemes.Equipment:Get(equipID)
    local smeltID = cfg.SmeltID
    m.SmeltID = cfg.SmeltID
    local starLvl = GamePlayerData.ActorEquipNew:GetEquipUpgradeLevel(smeltID)
    local equipSmeltStar = Schemes.EquipSmeltStar:GetEquipSmeltStarCfg(smeltID, starLvl)
    if equipSmeltStar == nil then
        warn("天赋升级:未找到升星配置 smeltID=", smeltID, starLvl)
        return
    end

    if HelperL.IsLackGoods(equipSmeltStar.CostGoodsID1, equipSmeltStar.CostGoodsID1Num, false) then return end
    if HelperL.IsLackGoods(equipSmeltStar.CostGoodsID2, equipSmeltStar.CostGoodsID2Num, false) then return end

    local form = {}
    form["smeltID"] = smeltID
    form["loopTimes"] = 1
    LuaModuleNew.SendRequest(LuaRequestID.NewCardAddExp2, form, m.RequestCallback)
end

--------------------------------------------------------------------
--显示子弹信息
---@param index integer
--------------------------------------------------------------------
function m.ShowBulletInfo(equipID)
    -- local item
    -- if m.selectIndex then
    --     item = m.Item_Tab_List[m.selectIndex]
    --     item.com.Img_Bg.gameObject:SetActive(true)
    --     item.com.Img_Select.gameObject:SetActive(false)
    -- end
    -- item = m.Item_Tab_List[index]
    -- item.com.Img_Bg.gameObject:SetActive(false)
    -- item.com.Img_Select.gameObject:SetActive(true)

    -- m.selectIndex = index

    local equipment = Schemes.Equipment:Get(equipID)
    local cfg = Schemes.Equipment:Get(equipID)
    local level = GamePlayerData.ActorEquipNew:GetEquipUpgradeLevel(cfg.SmeltID)
    local maxLevel = PropertyCompute.GetEquipSmeltStarMaxLevel(cfg.SmeltID)
    local equipSme = Schemes.EquipSmeltStar:GetEquipSmeltStarCfg(cfg.SmeltID, level)
    if not equipSme then
        error('EquipSmeltStar is nil, SmeltID=' .. SmeltID .. ' level=' .. level)
        return
    end
    local attack = 0
    local hp = 0
    if cfg.QualityLevel < equipSme.AttackList.Length then
        attack = attack + equipSme.AttackList[cfg.QualityLevel]
    end
    if cfg.QualityLevel < equipSme.MaxHpList.Length then
        hp = hp + equipSme.MaxHpList[cfg.QualityLevel]
    end
    --设置默认状态
    m.objList.Txt_Hint.gameObject:SetActive(false)
    m.objList.Btn_Upgrade1.gameObject:SetActive(false)
    m.objList.Img_Gray.gameObject:SetActive(false)
    m.objList.Img_UpgradeRedDot.gameObject:SetActive(false)
    m.objList.Txt_Lv.text = level .. "级"
    if level < maxLevel then
        m.objList.Btn_Upgrade1.gameObject:SetActive(true)
        AtlasManager:AsyncGetGoodsSprite(equipSme.CostGoodsID1, m.objList.Img_UpgradeExpend)
        local bool1 = not HelperL.IsLackGoods(equipSme.CostGoodsID1, equipSme.CostGoodsID1Num, false, false)
        m.objList.Txt_UpgradeExpend.text = string.format("<color=%s>%s</color>/%s",
            bool1 and UI_COLOR.Red or UI_COLOR.Red,
            HelperL.GetChangeNum(SkepModule:GetGoodsCount(equipSme.CostGoodsID1)),
            HelperL.GetChangeNum(equipSme.CostGoodsID1Num))

        AtlasManager:AsyncGetGoodsSprite(equipSme.CostGoodsID2, m.objList.Img_UpgradeExpend2)
        local bool2 = not HelperL.IsLackGoods(equipSme.CostGoodsID2, equipSme.CostGoodsID1Num, false, false)
        m.objList.Txt_UpgradeExpend2.text = string.format("%s <color=%s>%s</color>/%s",
            HelperL.GetGoodsName(equipSme.CostGoodsID2),
            bool2 and UI_COLOR.Red or UI_COLOR.Red,
            HelperL.GetChangeNum(SkepModule:GetGoodsCount(equipSme.CostGoodsID2)),
            HelperL.GetChangeNum(equipSme.CostGoodsID2Num))

        if bool1 and bool2 then
            m.objList.Img_UpgradeRedDot.gameObject:SetActive(true)
        else
            m.objList.Img_Gray.gameObject:SetActive(true)
        end
        m.objList.Img_11.gameObject:SetActive(true)
        --m.objList.Txt_Lv.text = string.format(GetGameText(luaID, 19), equipSme.StarLvl)
        --m.objList.Txt_Fill.text = level .. "/" .. maxLevel
        --m.objList.Img_Fill.fillAmount = level / maxLevel
    else
        m.objList.Img_11.gameObject:SetActive(false)
        m.objList.Txt_Hint.text = CommonTextID.IS_FULL_LEVEL
        m.objList.Txt_Hint.gameObject:SetActive(true)
    end

    --获取最大等级
    -- if level == 0 then
    --     m.objList.Txt_Upgrade.text = GetGameText(luaID, 20)
    --     m.objList.Img_Battle.gameObject:SetActive(true)
    -- else
    --     --是否是满级
    --     if level >= maxLevel then
    --         m.objList.Txt_Upgrade.text = GetGameText(luaID, 22)
    --     else
    --         m.objList.Txt_Upgrade.text = GetGameText(luaID, 21)
    --     end
    --     m.objList.Img_Battle.gameObject:SetActive(false)
    -- end
    if level < maxLevel then
        local nextattack = 0
        local nexthp = 0
        local nextequipSme = Schemes.EquipSmeltStar:GetEquipSmeltStarCfg(cfg.SmeltID, level + 1)
        if cfg.QualityLevel + 1 < nextequipSme.AttackList.Length then
            nextattack = nextattack + nextequipSme.AttackList[cfg.QualityLevel]
        end
        if cfg.QualityLevel + 1 < nextequipSme.MaxHpList.Length then
            nexthp = nexthp + nextequipSme.MaxHpList[cfg.QualityLevel]
        end
        -- 通过路径直接访问主界面的 Txt_Desc 组件（不是 EquipTip 中的）
        local mainTxtDesc = m.gameObject.transform:Find("Txt_Desc")
        if mainTxtDesc then
            local textComponent = mainTxtDesc:GetComponent(typeof(UnityEngine.UI.Text))
            if textComponent then
                textComponent.text = GetGameText(luaID, 16) .. "         " ..
                    HelperL.GetChangeNum(attack) ..
                    "                 " ..
                    string.format(GetGameText(luaID, 17), HelperL.GetChangeNum(nextattack)) .. "\n\n\n" ..
                    GetGameText(luaID, 15) .. "         " .. HelperL.GetChangeNum(hp) .. "                 " ..
                    string.format(GetGameText(luaID, 17), HelperL.GetChangeNum(nexthp))
            end
        end
    else
        -- 通过路径直接访问主界面的 Txt_Desc 组件（不是 EquipTip 中的）
        local mainTxtDesc = m.gameObject.transform:Find("Txt_Desc")
        if mainTxtDesc then
            local textComponent = mainTxtDesc:GetComponent(typeof(UnityEngine.UI.Text))
            if textComponent then
                textComponent.text = GetGameText(luaID, 16) .. "         " ..
                    HelperL.GetChangeNum(attack) .. "\n\n\n" .. GetGameText(luaID, 15) .. "         " .. HelperL.GetChangeNum(hp)
            end
        end
        m.objList.Img_1.gameObject:SetActive(false)
        m.objList.Img_2.gameObject:SetActive(false)
    end

    local equipSmeltList = Schemes.EquipSmeltStar:GetBySmeltID(SmeltID) or {}
    for i = 2, #equipSmeltList, 1 do
        if not m.Item_Attribute_List2[i] then
            m.Item_Attribute_List2[i] = m.Creation_Item_Attribute2(i)
        end
        m.Item_Attribute_List2[i].UpdateData(equipSmeltList[i])
    end
end

--------------------------------------------------------------------
---创建属性框
---@param index integer
---@return Item_Attribute4
--------------------------------------------------------------------
function m.Creation_Item_Attribute2(index)
    ---@class Item_Attribute4
    local item = {}
    item.index = index
    item.com = m:CreateSubItem(m.objList.Grid_Attribute2, m.objList.Item_Attribute2)
    ---属性数据--图片、名字、描述
    ---@param equipSme EquipSmeltStarCfg
    item.UpdateData = function(equipSme)
        item.equipSme = equipSme
        if equipSme then
            item.com.Txt_Level.text = string.format(GetGameText(luaID, 19), equipSme.StarLvl)
            item.com.Txt_Value.text = equipSme.Remark
            local level = GamePlayerData.ActorEquipNew:GetEquipUpgradeLevel(equipSme.SmeltID)
            item.com.Img_Lock.gameObject:SetActive(level <= equipSme.StarLvl)
            item.com.gameObject:SetActive(true)
        else
            item.com.gameObject:SetActive(false)
        end
    end
    return item
end

--------------------------------------------------------------------
--升级
---@param equipID integer 装备ID
--------------------------------------------------------------------
function m.Upgrade()
    local curTime = os.clock()
    if curTime - m.lastTime < 0.3 then
        print('天赋升级点太快了1秒后尝试！')
        return
    end
    m.lastTime = curTime
    local weaponsKnapsack = GamePlayerData.ActorEquip:GetWeaponsKnapsack(1) or {}
    local equipID = weaponsKnapsack[1] or 0
    local cfg = Schemes.Equipment:Get(equipID)
    local smeltID = cfg.SmeltID

    local starLvl = GamePlayerData.ActorEquipNew:GetEquipUpgradeLevel(smeltID)
    local equipSmeltStar = Schemes.EquipSmeltStar:GetEquipSmeltStarCfg(smeltID, starLvl)
    if equipSmeltStar == nil then
        warn("天赋升级:未找到升星配置 smeltID=", smeltID, starLvl)
        return
    end

    if HelperL.IsLackGoods(equipSmeltStar.CostGoodsID1, equipSmeltStar.CostGoodsID1Num, false) then return end
    if HelperL.IsLackGoods(equipSmeltStar.CostGoodsID2, equipSmeltStar.CostGoodsID2Num, false) then return end

    local form = {}
    form["smeltID"] = smeltID
    form["loopTimes"] = 1
    LuaModuleNew.SendRequest(LuaRequestID.NewCardAddExp2, form, m.RequestCallback1)
end

--------------------------------------------------------------------
--请求回调
--------------------------------------------------------------------
function m.RequestCallback1(resultCode, content)
    if resultCode == RESULT_CODE_BASE.RESULT_COMMON_SUCCEED[1] then
        --播放升级特效
        HelperL.PlayVFX()
        SoundManager:PlaySound(SoundID.Upgrade)
    else
        ResultCode:DefaultShowResultCode(resultCode, content)
    end
end

--------------------------------------------------------------------
--显示装备提示对话框
--------------------------------------------------------------------
function m.ShowEquipTip()
    if m.objList.EquipTip then
        -- 注册关闭按钮事件（每次显示时重新注册，确保正确绑定）
        local contentAni = m.objList.EquipTip.transform:Find("ContentAni")
        if contentAni then
            local imgBG = contentAni:Find("Img_BG")
            if imgBG then
                local btnClose = imgBG:Find("Btn_Close")
                if btnClose then
                    local buttonComponent = btnClose:GetComponent(typeof(UnityEngine.UI.Button))
                    if buttonComponent then
                        buttonComponent.onClick:RemoveAllListeners()
                        buttonComponent.onClick:AddListener(function()
                            m.HideEquipTip()
                        end)
                    end
                end
            end
        end

        -- 注册遮罩点击事件
        local imgMask = m.objList.EquipTip.transform:Find("Img_Mask")
        if imgMask then
            local buttonComponent = imgMask:GetComponent(typeof(UnityEngine.UI.Button))
            if buttonComponent then
                buttonComponent.onClick:RemoveAllListeners()
                buttonComponent.onClick:AddListener(function()
                    m.HideEquipTip()
                end)
            end
        end

        -- 显示对话框
        m.objList.EquipTip.gameObject:SetActive(true)
    end
end

--------------------------------------------------------------------
--隐藏装备提示对话框
--------------------------------------------------------------------
function m.HideEquipTip()
    if m.objList.EquipTip then
        m.objList.EquipTip.gameObject:SetActive(false)
    end
end

return m
