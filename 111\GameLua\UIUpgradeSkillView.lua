local luaID = ('UIUpgradeSkillView')


local UIUpgradeSkillView = {}

-- 订阅事件列表
function UIUpgradeSkillView:GetOpenEventList()
	return {

	}
end

--技能：1星 蓝色，2-3星橙色，4星大招红色
--buff：1星 绿色，2-3星紫色
local ImgSelectBg = {
	'jn_d_01',
	'jn_d_02',
	'jn_d_03',
	'jn_d_04',
	'jn_d_05',
}

UIUpgradeSkillView.skillRefreshNum = 0

-- 初始化
function UIUpgradeSkillView:OnCreate()
	self.selectSkillItem = nil
	self.objList.Btn_RefreshSkill.onClick:AddListenerEx(self.RefreshSkill)
	self.objList.Btn_FiveSkill.onClick:AddListenerEx(self.SelectFiveSkill)
	self.objList.Txt_Desc.text = GetGameText(luaID, 3)
	self.objList.Txt_ChallengTip.text = GetGameText(luaID, 9)
	self.btnList = {}
	self.itemContainer = self.objList.ItemsContainer.transform
	self.skillItemList = {}
	self.smallSkillList = {}
	self.smallBuffList = {}
	for i = 1, 3 do
		local skillItem = self:CreateSubItem(self.itemContainer, self.objList.Item_Skill)
		skillItem.index = i
		skillItem.gameObject.name = 'skillItem' .. skillItem.index
		table.insert(self.skillItemList, skillItem)
		skillItem.gameObject:SetActive(false)
		skillItem.Btn_Item.onClick:AddListenerEx(function()
			self:OnClickSelectSkill(i)
		end)
		table.insert(self.btnList, skillItem.Btn_Item)
	end

	self.objList.Item_Skill.gameObject:SetActive(false)

	self.seqList = {}
	self.seqList2 = {}

	return true
end

-- 窗口开启
function UIUpgradeSkillView:OnOpen(randomInfoList)
	if not randomInfoList then return end

	EventManager:Fire(EventID.UIAttributeUp_SetActiveSkill, false)
	self.skillRefreshNum = 0
	self.randomInfoList = randomInfoList

	self.isChallengStage = BattleManager.levelConfig.FrontType == 2
	-- 特殊处理，当时挑战模式的时候，把主火之装备的技能提示到3级
	self.objList.Txt_ChallengTip.gameObject:SetActive(self.isChallengStage)
	--self.objList.Btn_FiveSkill.gameObject:SetActive(not BattleManager.hasUseFiveSkill)
	self.objList.Btn_RefreshSkill.gameObject:SetActive(0 < BattleManager.skillNewRefreshNum)
	--self.objList.Txt_FiveSkill1.text = string.format("连选%s次", HelperL.Split(BattleManager.levelConfig.MapPos, ';')[2])
	--self.objList.Txt_FiveSkill2.text = string.format("本次可连续选%s次技能",HelperL.Split(BattleManager.levelConfig.MapPos, ';')[2])
	if BattleManager.levelConfig.ID < 4 then
		self.objList.Txt_ADRefreshSkill.gameObject:SetActive(false)
		self.objList.Txt_RefreshSkill.gameObject:SetActive(true)
	else
		self.objList.Txt_ADRefreshSkill.gameObject:SetActive(true)
		self.objList.Txt_RefreshSkill.gameObject:SetActive(false)
	end
	self:UpdateSkillView()
end

function UIUpgradeSkillView:UpdateSkillView()
	local seq = DOTween.Sequence()
	self.objList.ItemsContainer.transform.localScale = CachedVector3(0, 0, 0)
	seq:Append(self.objList.ItemsContainer.transform:DOScale(CachedVector3(1.1, 1.1, 1.1), 0.15))
	seq:Append(self.objList.ItemsContainer.transform:DOScale(CachedVector3(1, 1, 1), 0.1))
	seq:SetUpdate(true)

	for key, seq in pairs(self.seqList) do
		seq:Kill()
	end
	self.seqList = {}
	for key, seq in pairs(self.seqList2) do
		seq:Kill()
	end
	self.seqList2 = {}

	for i = 1, 3 do
		local item = self.skillItemList[i]
		local dataInfo = self.randomInfoList[i]
		if dataInfo then
			item.data = dataInfo
			local bgStr, config
			local stageSkillMaxLevel = 20;
			-- 当前拥有的技能或Buff的等级,0表示尚未拥有
			local level = 0
			local isSkill = false
			if dataInfo.ID < 1000000 then
				-- 显示候选技能
				isSkill = true
				config = Schemes.CatSkill:Get(dataInfo.ID)
				stageSkillMaxLevel = BattleManager.levelConfig.MaxSkillLvl

				if (dataInfo.Skill) then
					level = dataInfo.Skill.SkillLvl;
				end

				-- 技能中间级别的背景图片
				if (level >= 2 and level < stageSkillMaxLevel - 1) then
					bgStr = ImgSelectBg[3];
					-- 升终极时的背景图片
				elseif (level == stageSkillMaxLevel - 1) then
					bgStr = ImgSelectBg[4]
					-- 技能默认背景图片
				else
					bgStr = ImgSelectBg[2]
				end
				item.Txt_Desc.text = self:GetCatSkillEffectDes(dataInfo)
				item.Img_StarMax.gameObject:SetActive(level == stageSkillMaxLevel - 1)
				item.StarContent.gameObject:SetActive(false)
				item.Txt_Level.gameObject:SetActive(level < stageSkillMaxLevel - 1)
				item.Txt_Level.text = "+"..level
			else
				-- 显示候选Buff
				if (dataInfo.Buff) then
					config = dataInfo.Buff.CsvRow_Buff;
					level = config.Level;
				else
					config = Schemes.Buff:Get(dataInfo.ID)
				end
				if level == 1 then
					bgStr = ImgSelectBg[1]
				elseif level >= 2 and level < BattleManager.levelConfig.MaxSkillLvl then
					bgStr = ImgSelectBg[5]
				else
					bgStr = ImgSelectBg[4]
				end
				item.Txt_Desc.text = config.Tips
				item.Img_StarMax.gameObject:SetActive(false)
				item.StarContent.gameObject:SetActive(true)
				item.Txt_Level.text = ""

			end
			item.Txt_SkillName.text = config.Name
			AtlasManager:AsyncGetSprite(config.Icon, item.Img_Icon)


			if stageSkillMaxLevel - 1 == level and isSkill then
				local canvasGroup = item.Img_StarMax.gameObject:GetComponent('CanvasGroup')
				canvasGroup.alpha = 1
				local seq = DOTween.Sequence()
				seq:Append(canvasGroup:DOFade(0, 0.5))
				seq:Append(canvasGroup:DOFade(1, 0.5))
				seq:SetUpdate(true)
				seq:SetLoops(-1)
				table.insert(self.seqList, seq)
			else
				for ii = 1, item.StarContent.transform.childCount do
					local canvasGroup = item["Img_Star" .. ii].gameObject:GetComponent('CanvasGroup')
					canvasGroup.alpha = 1
					if ii < level + 1 then
						AtlasManager:AsyncGetSprite('xingxing2', item["Img_Star" .. ii])
					elseif ii == level + 1 then
						AtlasManager:AsyncGetSprite('xingxing2', item["Img_Star" .. ii])
						local seq = DOTween.Sequence()
						seq:Append(canvasGroup:DOFade(0, 0.5))
						seq:Append(canvasGroup:DOFade(1, 0.5))
						seq:SetLoops(-1)
						seq:SetUpdate(true)
						table.insert(self.seqList, seq)
					else
						AtlasManager:AsyncGetSprite('xingxing1', item["Img_Star" .. ii])
					end
				end
			end
			item.gameObject:SetActive(true)

			if BattleManager.skillAndBuffFull then
				item.Img_StarMax.gameObject:SetActive(false)
				item.StarContent.gameObject:SetActive(false)
			end

			self:SkillCompound(dataInfo, item)
		else
			item.gameObject:SetActive(false)
		end
	end
end

function UIUpgradeSkillView:GetCatSkillEffectDes(dataInfo)
	local txt_des
	local catSkill = Schemes.CatSkill:Get(dataInfo.ID)
	if dataInfo.level >= BattleManager.levelConfig.MaxSkillLvl - 1 then
		txt_des = Schemes.CatSkill:Get(catSkill.FinalSkillIDs[1]).Remark
	elseif (dataInfo.level > 0) then
		local catSkillEffectItem = Schemes.CatSkillEffect:Get(dataInfo.EffectID)
		if catSkillEffectItem then
			txt_des = catSkillEffectItem.Remark
		end
	else
		if catSkill then
			txt_des = catSkill.Remark
		end
	end

	return txt_des
end

function UIUpgradeSkillView:SkillCompound(dataInfo, skillItem)
	skillItem.Img_Skill01.gameObject:SetActive(false)
	skillItem.Img_Skill02.gameObject:SetActive(false)
	local baseID = dataInfo.ID
	if baseID < 1000000 then --skill
		local skillConfig = Schemes.CatSkill:Get(dataInfo.ID)
		if skillConfig then
			local hadSkill = self:HadSkillByID(baseID)
			local needBuff = skillConfig.CatSkillComb
			if needBuff ~= 0 then
				local buffConfig = Schemes.Buff:Get(needBuff)
				AtlasManager:AsyncGetSprite(buffConfig.Icon, skillItem.Img_icon02)
				skillItem.Img_Skill02.gameObject:SetActive(true)
				local canvasGroup = skillItem.Img_Skill02.gameObject:GetComponent('CanvasGroup')
				canvasGroup.alpha = 1
				local hadBuff = self:HadBuffByID(needBuff)
				if not hadSkill then
					if hadBuff then --大图标未获取，小图标已获取，闪烁
						local seq = DOTween.Sequence()
						seq:Append(canvasGroup:DOFade(0, 0.5))
						seq:Append(canvasGroup:DOFade(1, 0.5))
						seq:SetUpdate(true)
						seq:SetLoops(-1)
						table.insert(self.seqList2, seq)
					end
				end
			end
		end
	else --buff
		local buffConfig = Schemes.Buff:Get(baseID)
		if buffConfig then
			local skillID = self:HadSkillByBuffID(baseID)
			local compoundSkillConfig = Schemes.CatSkill:Get(skillID)
			if compoundSkillConfig then
				AtlasManager:AsyncGetSprite(compoundSkillConfig.Icon, skillItem.Img_icon01)
				skillItem.Img_Skill01.gameObject:SetActive(true)
				local canvasGroup = skillItem.Img_Skill01.gameObject:GetComponent('CanvasGroup')
				canvasGroup.alpha = 1

				local hadBuff = self:HadBuffByID(baseID)
				local hadSkill = self:HadSkillByID(skillID)
				if not hadBuff then
					if hadSkill then --大图标未获取，小图标已获取，闪烁
						local seq = DOTween.Sequence()
						seq:Append(canvasGroup:DOFade(0, 0.5))
						seq:Append(canvasGroup:DOFade(1, 0.5))
						seq:SetUpdate(true)
						seq:SetLoops(-1)
						table.insert(self.seqList2, seq)
					end
				end
			end
		end
	end
end

--是否拥有技能
function UIUpgradeSkillView:HadSkillByID(skillID)
	if not skillID then return false end
	local skillList = EntityModule.luaToCshape:GetSkills()
	skillList = dkjsonHelper.decode(skillList)
	for i = 1, #skillList do
		local skillInfo = skillList[i]
		if skillInfo.SkillID == skillID then
			return true
		end
	end
	return false
end

--是否拥有buff
function UIUpgradeSkillView:HadBuffByID(buffID)
	if not buffID then return false end
	local buffList = EntityModule.luaToCshape:GetBuffs()
	buffList = dkjsonHelper.decode(buffList)
	local buffItem = Schemes.Buff:Get(buffID)
	for i = 1, #buffList do
		local skillInfo = buffList[i]
		if skillInfo.CsvRow_Buff.Type == buffItem.Type then
			return true
		end
	end
	return false
end

--是否拥有buff对应的技能
function UIUpgradeSkillView:HadSkillByBuffID(buffID)
	local needBuffID = 0
	if not buffID then return needBuffID end

	local skillList = EntityModule.luaToCshape:GetSkills()
	skillList = dkjsonHelper.decode(skillList)
	local buffItem = Schemes.Buff:Get(buffID)
	for i = 1, #skillList do
		local skillInfo = skillList[i]
		local skillConfig = Schemes.CatSkill:Get(skillInfo.SkillID)
		if skillConfig then
			local needBuff = skillConfig.CatSkillComb
			if needBuff ~= 0 then
				local needBuffItem = Schemes.Buff:Get(needBuff)
				if needBuffItem.Type == buffItem.Type then
					needBuffID = skillInfo.SkillID
					return needBuffID
				end
			end
		end
	end
	return needBuffID
end

function UIUpgradeSkillView:OnClickSelectSkill(index)
	local selectSkillItem = self.skillItemList[index]
	if not selectSkillItem then
		return
	end
	local dataInfo = selectSkillItem.data
	SoundManager:PlaySound(1009)
	if dataInfo.ID < 1000000 then
		EntityModule.luaToCshape:SkillUp(dataInfo.ID, dataInfo.EffectID)
	else
		local nextId = dataInfo.NextId
		if (dataInfo.IsUplimitBuffID and nextId <= 0) then
			nextId = dataInfo.ID;
		end
		local buffup = GameManager.instance:BuffUp(nextId)
		local info = dkjsonHelper.decode(buffup)
		local buffInfo = BattleManager:ChangeBuffInfo(info.CsvRow_Buff)
		buffInfo.ID = dataInfo.ID
		EventManager:Fire(EventID.UpgradeSkill, 2, buffInfo);
	end
	self.OnClickClose()
	BattleManager.OnSelectSkillComplete()
end

-- 点击返回按钮
function UIUpgradeSkillView.OnClickClose()
	local self = UIUpgradeSkillView
	BattleManager.curUpgradeSkillDialogQueue = BattleManager.curUpgradeSkillDialogQueue - 1
	if BattleManager.curUpgradeSkillDialogQueue > 0 then
		BattleManager.randomInfoList = {}
		local result = BattleManager:prepareRandomData()
		if #result > 0 then
			BattleManager.skillAndBuffFull = false
			for i = 1, #result do
				BattleManager:SaveRandomIdList(result[i])
			end
		else
			BattleManager.skillAndBuffFull = true
			BattleManager:SaveRandomIdList({ ID = BattleManager.uplimitBuffID, IsUplimitBuffID = true, buffLevel = 0, NextId = 0 })
		end
		self.randomInfoList = BattleManager.randomInfoList
		self:UpdateSkillView()
	else
		self:CloseSelf()
		EventManager:Fire(EventID.UIAttributeUp_SetActiveSkill, true)
		BattleManager:ResumeBattle()
	end
end

function UIUpgradeSkillView.OnClickRefresh()

end

function UIUpgradeSkillView:refreshFunc()
	BattleManager.refreshSkillNum = BattleManager.refreshSkillNum + 1
	BattleManager.randomInfoList = {}
	local result = BattleManager:prepareRandomData()
	if #result > 0 then
		BattleManager.skillAndBuffFull = false
		for i = 1, #result do
			BattleManager:SaveRandomIdList(result[i])
		end
	else
		BattleManager.skillAndBuffFull = true
		BattleManager:SaveRandomIdList(BattleManager.uplimitBuffID)
	end
	self.randomInfoList = BattleManager.randomInfoList
	self:UpdateSkillView()
	self.objList.Btn_RefreshSkill.gameObject:SetActive(0 < BattleManager.skillNewRefreshNum)
end

-- 刷新3选1技能
function UIUpgradeSkillView.RefreshSkill()
	if 0 < BattleManager.skillNewRefreshNum then
		if BattleManager.levelConfig.ID < 3 then
			BattleManager.skillNewRefreshNum = BattleManager.skillNewRefreshNum - 1
			--UIUpgradeSkillView:refreshFunc()
			for i = 1, 3, 1 do
				UIUpgradeSkillView:OnClickSelectSkill(i)
			end
		else
			AdvertisementManager.ShowRewardAd(GameAdvertisementID.Skill, function(bool)
				if bool then
					BattleManager.skillNewRefreshNum = BattleManager.skillNewRefreshNum - 1
					--UIUpgradeSkillView:refreshFunc()
					for i = 1, 3, 1 do
						UIUpgradeSkillView:OnClickSelectSkill(i)
					end
				end
			end, true)
		end
	end
end

-- 连选5个技能
function UIUpgradeSkillView.SelectFiveSkill()
	if not BattleManager.hasUseFiveSkill then
		if BattleManager.levelConfig.ID < 4 then
			UIUpgradeSkillView.objList.Btn_FiveSkill.gameObject:SetActive(false)
			BattleManager.hasUseFiveSkill = true
			BattleManager.fiveSkillSelectCount = tonumber(HelperL.Split(BattleManager.levelConfig.MapPos, ';')[2])
		else
			AdvertisementManager.ShowRewardAd(GameAdvertisementID.FiveSelectSkill, function(bool)
				if bool then
					UIUpgradeSkillView.objList.Btn_FiveSkill.gameObject:SetActive(false)
					BattleManager.hasUseFiveSkill = true
					BattleManager.fiveSkillSelectCount = tonumber(HelperL.Split(BattleManager.levelConfig.MapPos, ';')
					[2])
				end
			end, true)
		end
	end
end

return UIUpgradeSkillView
