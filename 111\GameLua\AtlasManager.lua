-- 全局图集管理
AtlasManager = {}
AtlasManager.atlasCache = {}

-- 初始化
function AtlasManager:Init()
end

-- 获取图片
function AtlasManager:GetSprite(spriteName)
	if spriteName == nil then
		return nil
	end
	spriteName = tostring(spriteName)
	local atlasName = Schemes.UIAtlasConfig:GetSpriteAtlas(spriteName)
	if not atlasName then
		warn('AtlasManager:GetSprite 找不到图片对应图集名', spriteName)
		return nil
	end

	local atlas = AtlasManager.atlasCache[atlasName]
	if not atlas then
		atlas = HotResManager.ReadAtlas(atlasName)
		if not atlas then
			warn('AtlasManager:GetSprite not atlas', atlasName, spriteName)
			return nil
		end
		AtlasManager.atlasCache[atlasName] = atlas
	end

	local sp = atlas:GetSprite(spriteName)
	if not sp then
		warn('AtlasManager:GetSprite 找不到目标图片', atlasName, spriteName)
	end
	return sp
end

---获取物品图片
---@param goodsID integer
---@return unknown|nil
function AtlasManager:GetGoodsSprite(goodsID)
	local cfg = Schemes:GetGoodsConfig(goodsID)
	if cfg then
		return AtlasManager:GetSprite(cfg.IconID)
	end
end

-- 获取图片
---@param spriteName string 图片名
---@param image any 图片组件
---@param isSize ?boolean 是否设置大小
---@param callback ?fun(sprite:integer, image:integer, param:integer) 回调
---@param param ?integer 回调参数
function AtlasManager:AsyncGetSprite(spriteName, image, isSize, callback, param)
	if spriteName == nil or spriteName == '' then
		warn('获取图片名称为空 spriteName=', spriteName)
		if type(callback) == 'function' then
			callback(nil, image, param)
		end
		return
	end

	if image ~= nil and image.transform ~= nil then
		image = image.transform:GetComponent('Image')
	end
	if tolua.isnull(image) then
		warn('图片挂载组件为空 image=', image)
		if type(callback) == 'function' then
			callback(nil, image, param)
		end
		return
	end

	spriteName = tostring(spriteName)
	local atlasName = Schemes.UIAtlasConfig:GetSpriteAtlas(spriteName)
	if not atlasName then
		warn('找不到图片对应图集名', spriteName)
		if type(callback) == 'function' then
			callback(nil, image, param)
		end
		return
	end

	local atlas = AtlasManager.atlasCache[atlasName]
	if not atlas then
		local param2 = {}
		param2.image = image
		param2.callback = callback
		param2.param = param
		param2.atlasName = "Assets/Temp/uiatlas/" .. atlasName .. ".spriteatlas"
		param2.spriteName = spriteName
		param2.isSize = isSize == true
		ResMgr.LoadSpriteAtlasAsync(param2.atlasName, function(_atlas, _param)
			local sp = nil
			if not _atlas then
				warn('找不到图集', _param.atlasName, _param.spriteName)
			else
				AtlasManager.atlasCache[_param.atlasName] = _atlas
				sp = _atlas:GetSprite(_param.spriteName)
				if sp == nil then
					warn('找不到目标图片', _param.atlasName, _param.spriteName)
				else
					_param.image.sprite = sp
					if _param.isSize == true then
						_param.image:SetNativeSize()
					end
				end
			end
			if type(_param.callback) == 'function' then
				_param.callback(sp, _param.image, _param.param)
			end
		end, param2)
		return
	end

	local sp = atlas:GetSprite(spriteName)
	if sp == nil then
		warn('找不到目标图片', atlasName, spriteName)
	else
		image.sprite = sp
		if isSize == true then
			image:SetNativeSize()
		end
	end

	if type(callback) == 'function' then
		callback(sp, image, param)
	end
end

--- 获取物品图片
---@param goodsID integer 物品ID
---@param image integer 图片组件
---@param isSize ?boolean 是否设置大小
---@param callback ?fun(sprite:integer, image:integer, param:integer) 回调
---@param param ?integer 回调参数
function AtlasManager:AsyncGetGoodsSprite(goodsID, image, isSize, callback, param)
	if goodsID == nil then return end
	local cfg = Schemes:GetGoodsConfig(goodsID)
	if cfg then
		AtlasManager:AsyncGetSprite(cfg.IconID, image, isSize, callback, param)
	else
		warn('获取物品图片，读表失败 goodsID=', goodsID)
	end
end

function AtlasManager:AsyncGetSprite2(atlasName, spriteName, image, callback)
	local atlasPath = "Assets/Temp/uiatlas/" .. atlasName .. ".spriteatlas"
	ResMgr.LoadSpriteAtlasAsync(atlasPath, function(atlas)
		if not atlas then
			warn('找不到图集', atlasName)
			return
		end
		local sp = atlas:GetSprite(spriteName)
		if not sp then
			warn('找不到目标图片', atlasName, spriteName)
		end
		if image then
			image.sprite = sp
		end
		if type(callback) == 'function' then
			callback()
		end
	end)
end
