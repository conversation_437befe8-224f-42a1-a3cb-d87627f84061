﻿namespace ViewModel
{
    /// <summary>
    /// 用于接收Lua中HelperL.GetPlayerFieldsByActor的返回值
    /// </summary>
    public class PlayerFieldsByActor
    {
        /// <summary>
        /// 生命
        /// </summary>
        public float HP { get; set; }
        /// <summary>
        /// 基础攻击
        /// </summary>
        public float Attack { get; set; }
        /// <summary>
        /// 生命恢复
        /// </summary>
        public float Defense { get; set; }
        /// <summary>
        /// 攻击
        /// </summary>
        public float PhysicsAttack { get; set; }
        /// <summary>
        /// 伤害范围
        /// </summary>
        public float MagicAttack { get; set; }
        /// <summary>
        /// 狂暴时长
        /// </summary>
        public float PhysicsDefense { get; set; }
        /// <summary>
        /// 能量上限
        /// </summary>
        public float MagicDefense { get; set; }
        /// <summary>
        /// 子弹伤害
        /// </summary>
        public float CriticalStrike { get; set; }
        /// <summary>
        /// 直接伤害
        /// </summary>
        public float AntiCriticalStrike { get; set; }
        /// <summary>
        /// 伤害减免
        /// </summary>
        public float Parry { get; set; }
        /// <summary>
        /// 狂暴伤害
        /// </summary>
        public float AntiParry { get; set; }
        /// <summary>
        /// 暴击
        /// </summary>
        public float Dodge { get; set; }
        /// <summary>
        /// 子弹速度
        /// </summary>
        public float Hit { get; set; }
        /// <summary>
        /// XP值恢复
        /// </summary>
        public float Armor { get; set; }
        /// <summary>
        /// 能量上限
        /// </summary>
        public float AntiArmor { get; set; }
        /// <summary>
        /// 减伤
        /// </summary>
        public float DamageReduction { get; set; }
        /// <summary>
        /// 加伤
        /// </summary>
        public float DamageAdd { get; set; }
        /// <summary>
        /// 移动速度
        /// </summary>
        public float MoveSpeed { get; set; }
        /// <summary>
        /// 基础生命
        /// </summary>
        public float AttackDefense { get; set; }
        /// <summary>
        /// XP技能伤害
        /// </summary>
        public float XpDamagePct { get; set; }
        /// <summary>
        /// 暴击率
        /// </summary>
        public float CriticalHitRate { get; set; }
        /// <summary>
        /// 攻击速度
        /// </summary>
        public float AttackSpeed { get; set; }
        /// <summary>
        /// 总生命
        /// </summary>
        public float HP_Final { get; set; }
        /// <summary>
        /// 总攻击
        /// </summary>
        public float Attack_Final { get; set; }
        /// <summary>
        /// 爆伤伤害
        /// </summary>
        public float DamageCritical { get; set; }
        /// <summary>
        /// 对BOSS伤害
        /// </summary>
        public float DamageBossAddPct { get; set; }
        /// <summary>
        /// 获得经验
        /// </summary>
        public float AcquireExp { get; set; }
        /// <summary>
        /// 获得金币
        /// </summary>
        public float AcquireGold { get; set; }
        /// <summary>
        /// 吸血
        /// </summary>
        public float AbsorbHP { get; set; }
        /// <summary>
        /// 反伤
        /// </summary>
        public float DamageBounce { get; set; }

        /// <summary>
        /// 等级
        /// </summary>
        public float level { get; set; }
        /// <summary>
        /// 当前经验值
        /// </summary>
        public float curEXP { get; set; }
        /// <summary>
        /// 最大经验值
        /// </summary>
        public float maxEXP { get; set; }
    }
}
