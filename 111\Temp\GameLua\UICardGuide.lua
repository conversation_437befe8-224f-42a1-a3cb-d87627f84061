-- 塔防卡牌指引UI
local luaID = ('UICardGuide')

local UICardGuide = {}

-- 初始化
function UICardGuide:OnCreate()
	self.objList.Btn_Close.onClick:AddListenerEx(self.OnClickClose)
	self.objList.Txt_Title.text = GetGameText(luaID, 1)
	self.objList.Txt_RangeDescName.text = GetGameText(luaID, 2)
	self.objList.Txt_RangeDesc2Name.text = GetGameText(luaID, 3)
	self.objList.Txt_Close.text = GetGameText(luaID, 4)

	self:InitTowerItem()

	return true
end

-- 窗口是否可开启
function UICardGuide:CanOpen(itemID)
	local config = Schemes.TowerBattleCollect:Get(itemID)
	if not config then
		return false
	end
	
	return true
end

-- 窗口开启
function UICardGuide:OnOpen(itemID, title, num)
	local config = Schemes.TowerBattleCollect:Get(itemID)
	if not config then
		return
	end

	if not self.towerItem then
		self.towerItem = CreateTowerBattleSingleTower(self.objList.TowerItemContainer.transform)
	end
	if num and num > 0 then
		self.objList.Img_CardName.gameObject:SetActive(false)
		self.objList.Sld_Progress.gameObject:SetActive(true)
		if config.HaveActive1 > 0 then
			local goodsID = config.ActiveCostID1[1]
			local goodsConfig = Schemes:GetGoodsConfig(goodsID)
			if config.ActiveCostValue1[1] > 0 then
				self.objList.Sld_Progress.value = num/config.ActiveCostValue1[1]
			end
			if goodsConfig then
				if num >= config.ActiveCostValue1[1] then
					self.objList.Txt_Cost.text = string.format('<color=#30ef30>%s(%s/%s)</color>', goodsConfig.GoodsName, num, config.ActiveCostValue1[1])
				else
					self.objList.Txt_Cost.text = string.format('<color=#ef3030>%s(%s/%s)</color>', goodsConfig.GoodsName, num, config.ActiveCostValue1[1])
				end
			end
		end 
	else
		self.objList.Img_CardName.gameObject:SetActive(true)
		self.objList.Sld_Progress.gameObject:SetActive(false)
		self.objList.Txt_CardName.text = config.ShowName
		self.objList.Img_CardName.gameObject:SetActive(false)
	end
	self.towerItem:SetItemData(itemID)
	self.towerItem.gameObject:SetActive(true)	
	self.towerItem:SetShowCost(false)
	self.objList.Txt_CardDesc.text = config.ShowDesc
	self.objList.Txt_RangeDesc.text = string.gsub(config.RangeDesc,'\\n','\n')
	self.objList.Txt_RangeDesc2.text = string.gsub(config.RangeDesc2,'\\n','\n')
	if title then
		self.objList.Txt_Title.text = title
	end

	for i, v in ipairs(self.towerItemList) do
		v.TowerItemContent:SetActive(false)
	end

	for i, v in ipairs(config.TipGridIndex) do
		local item = self.towerItemList[v]
		if item then
			local bgSprite = config.TipTowerBG[i]
			if bgSprite then
				AtlasManager:AsyncGetSprite(bgSprite, item.Img_TowerBG)
				item.TowerItemContent:SetActive(true)

				local text = config.TipTowerText[i]
				if text and text ~= '' and text ~= '0' then
					AtlasManager:AsyncGetSprite('KPJS-'..text, item.Img_NumberBG)
					item.NumberObj:SetActive(true)
				else
					item.NumberObj:SetActive(false)
				end

				local extraImage = config.TipExtraImage[i]
				if extraImage and extraImage ~= '' and extraImage ~= '0' then
					AtlasManager:AsyncGetSprite(extraImage, item.Img_ExtraImage)
					item.Img_ExtraImage:SetNativeSize()
					local offsetX = config.TipExtraOffsetX[i]
					local offsetY = config.TipExtraOffsetY[i]
					if offsetX and offsetY then
						item.Img_ExtraImage:GetRectTransform().anchoredPosition = CachedVector3:Set(offsetX, offsetY, 0)
					end
					item.Img_ExtraImage.gameObject:SetActive(true)
				else
					item.Img_ExtraImage.gameObject:SetActive(false)
				end
			end
		end
	end
end

-- 初始化塔物件
function UICardGuide:InitTowerItem()
	self.towerItemList = {}
	local prefab = self.objList.SingleTowerItem
	prefab:SetActive(true)
	for i = 1, 18 do
		local container = nil
		if i <= 9 then
			container = self.objList.TowerContainer1.transform
		else
			container = self.objList.TowerContainer2.transform
		end

		local newItem = self:CreateSubItem(container, prefab)
		newItem.index = i
		newItem.gameObject.name = 'Item'..i
		table.insert(self.towerItemList, newItem)
	end
	prefab:SetActive(false)
end

-- 点击关闭按钮
function UICardGuide.OnClickClose()
	local self = UICardGuide
	self:CloseSelf()
end

return UICardGuide