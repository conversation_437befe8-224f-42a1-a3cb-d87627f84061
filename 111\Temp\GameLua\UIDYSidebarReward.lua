--[[
********************************************************************
    created:    2023/08/29
    author :    刘树齐
    purpose:    抖音侧边栏界面
*********************************************************************
--]]

local luaID = ('UIDYSidebarReward')

local m = {}
local adverId = 16--广告id
--------------------------------------------------------------------
-- 订阅事件列表
--------------------------------------------------------------------
function m.Get<PERSON>List()
	return {
		--[EventID.LogicDataChange] = m.UpdateView,
	}
end

--资源预加载
function m.GetResourceList()
	return {
	}
end

--------------------------------------------------------------------
-- 初始化
--------------------------------------------------------------------
function m.OnCreate()
	m.goodsItemList = {}

    local addGoods = Schemes.CommonText:Get(adverId).AddGoods
    local goodsInfo = HelperL.Split(addGoods,"|")
    for i, v in ipairs(goodsInfo) do
        local info = HelperL.Split(v,";")
        local id = info[1]
        local num = info[2]
        local item = CreateSingleGoods(m.objList.Grid_Prize)
        if item then
            item:SetItemData(id, num)
            item:SetSize(150, 150)
            item:SetShowName(false)
            item:SetShowNum(true)
            item:SetVisible(true)
        end
    end

	m.RegisterClickEvent()
	return true
end

--------------------------------------------------------------------
-- 窗口开启
--------------------------------------------------------------------
function m.OnOpen()
    m.UpdateView()
	--m.AddRedDot()
end

--------------------------------------------------------------------
--注册点击事件
--------------------------------------------------------------------
function m.RegisterClickEvent()
	m.objList.Btn_Close.onClick:AddListenerEx(function()
		m:CloseSelf()
	end)

    m.objList.Btn_GotoSidebar.onClick:AddListenerEx(function()
		if not GameLuaAPI.IsLaunchFromSidebar then
			GameLuaAPI.GotoDYSidebar()
            m:CloseSelf()
			return
		end
	end)

	m.objList.Btn_GetPrize.onClick:AddListenerEx(function()
		AdvertisementManager.GetAdAward(adverId, function(bool, adID)
            if bool then
                local goodInfo = Schemes.CommonText:Get(adverId).AddGoods
                HelperL.RequestDirectGiveGoodsy(goodInfo, "", function ()
                    m:CloseSelf()
                end)
            end
        end, true)
	end)
end

--------------------------------------------------------------------
-- 更新界面
--------------------------------------------------------------------
function m.UpdateView()
	if GameLuaAPI.IsLaunchFromSidebar then
        m.objList.Btn_GetPrize.gameObject:SetActive(true)
        m.objList.Btn_GotoSidebar.gameObject:SetActive(false)
    end
end

--------------------------------------------------------------------
-- 添加红点
--------------------------------------------------------------------
function m.AddRedDot()
	--m:SetWndRedDot(m.objList.Btn_GetPrize):AddCheckParam(WndID.DYSidebar)
end

return m
