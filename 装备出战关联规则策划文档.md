# 装备出战关联规则策划文档

## 文档版本
- **版本号**: V2.1
- **更新日期**: 2024年12月
- **维护人员**: 开发团队
- **文档状态**: 已实施

---

## 1. 系统概述

### 1.1 功能描述
装备出战关联系统确保玩家在任何时候都只有一件主装备（GroupID<100）出战，并自动管理与该主装备关联的装备链。系统在游戏启动、装备切换、战斗前等关键节点进行状态检查和自动修正。

### 1.2 设计目标
- **唯一性保证**: 确保同一时间只有1件GroupID<100的主装备出战
- **关联完整性**: 自动管理多层装备关联链（基于EffectID2和EffectID3）
- **状态一致性**: 游戏重启后装备状态与逻辑值保持一致
- **自动修复**: 检测并自动修复异常的装备出战状态

---

## 2. 核心规则定义

### 2.1 装备分类规则
```
主装备: GroupID < 100
关联装备: GroupID >= 100 且 GroupID < 1000
特殊装备: GroupID >= 1000
```

### 2.2 出战数量限制
- **主装备**: 同时只能有1件出战
- **关联装备**: 根据主装备的EffectID2和EffectID3链自动确定
- **总数限制**: 受解锁格子数量限制

### 2.3 关联规则（V2.1版本）
- 主装备通过`EquipWeapon.EffectID2`和`EquipWeapon.EffectID3`字段定义关联装备
- **双字段支持**: EffectID2和EffectID3都可以配置关联装备
- **多重关联**: 单个主装备可以同时关联多个装备
- 支持多层关联：A(EffectID2→B, EffectID3→C) → B(EffectID2→D) → C(EffectID3→E)
- 防循环检测：避免A→B→A的死循环
- 关联装备必须与主装备同时出战/下战

---

## 3. 触发时机与检查点

### 3.1 游戏启动检查（主要修复点）
**触发时机**: 玩家登录游戏后1秒
**检查内容**:
- 统计当前出战的主装备数量
- 验证逻辑值200对应的装备状态
- 检查关联装备完整性
- 自动修正异常状态

**处理逻辑**:
```lua
-- 优先级顺序
1. 使用逻辑值200作为目标主装备（需验证有效性）
2. 如果逻辑值200无效，使用默认装备ID 300020
3. 获取目标装备的完整关联链
4. 比较当前状态与目标状态
5. 仅在不一致时执行状态更新
```

### 3.2 装备切换检查
**触发时机**: 玩家主动切换装备时
**处理逻辑**: UIRoleEquip.Wear()函数处理完整的装备替换

### 3.3 战斗前检查
**触发时机**: 
- BattleManager.EnterBattle()
- BattleManager.OnConfirmBattleProgress()
- BattleManager.RequestEnterBattleScene()

**检查条件**: 当前只有1件主装备出战时
**处理逻辑**: 补充缺失的关联装备

---

## 4. 技术实现架构

### 4.1 核心函数

#### 4.1.1 多层关联装备获取
```lua
-- UIMainTitle.lua
function GetAllAssociatedEquipIDsForLogin(equipId)
    -- 支持多层关联：A→B→C→D
    -- 防循环检测：visitedEquipIDs表
    -- 返回完整关联装备ID列表
end
```

#### 4.1.2 装备状态规范化
```lua
-- UIMainTitle.lua (V1.2版本)
-- 登录后1秒执行完整的装备状态检查和修正
-- 使用"33333333"日志前缀便于调试
```

#### 4.1.3 装备出战管理
```lua
-- UIRoleEquip.lua
function Wear(equipId)
    -- 处理主装备切换
    -- 自动管理关联装备
    -- 一次性状态更新
end
```

### 4.2 数据存储
- **主存储**: `LogicValue.SetWeaponsKnapsack(equipList)`
- **逻辑值200**: 记录玩家选择的主装备ID
- **配置表**: `Schemes.EquipWeapon` 定义装备关联关系

### 4.3 日志系统
```
"11111" - 登录后装备状态监控
"2222" - 战斗前装备检查
"33333333" - 登录装备状态规范化（V1.2新增）
```

---

## 5. 异常处理机制

### 5.1 常见异常情况
1. **多件主装备同时出战**: 游戏重启后最常见问题
2. **关联装备缺失**: 主装备出战但关联装备未出战
3. **逻辑值200无效**: 指向不存在或非主装备的ID
4. **循环关联**: EffectID3形成死循环

### 5.2 自动修复策略
```
异常类型 → 修复方案
多件主装备 → 保留逻辑值200对应装备，卸下其他主装备
关联装备缺失 → 自动装备完整关联链
逻辑值200无效 → 使用默认装备300020
循环关联 → 检测到循环时停止关联链获取
```

### 5.3 容错机制
- **最小化更新**: 只在状态异常时才执行更新操作
- **验证机制**: 更新后0.5秒验证结果
- **降级策略**: 关键步骤失败时使用默认装备
- **详细日志**: 完整记录异常检测和修复过程

---

## 6. 性能优化策略

### 6.1 检查频率控制
- 游戏启动: 仅执行1次完整检查
- 装备切换: 实时检查
- 战斗前: 轻量级检查（仅在必要时）

### 6.2 计算优化
- 防循环检测: 使用visitedEquipIDs表避免重复计算
- 状态比较: 先比较数量再比较内容
- 批量更新: 使用LogicValue.SetWeaponsKnapsack一次性设置

---

## 7. 测试用例

### 7.1 正常流程测试
```
测试场景: 正常装备切换
前置条件: 装备A出战，逻辑值200=A
操作步骤: 切换到装备B
预期结果: B出战，B的关联装备自动出战，逻辑值200更新为B
```

### 7.2 异常修复测试
```
测试场景: 游戏重启后多件主装备出战
前置条件: 人为设置多件主装备出战，逻辑值200=A
操作步骤: 重启游戏
预期结果: 只有装备A出战，其他主装备自动卸下，A的关联装备自动出战
```

### 7.3 边界条件测试
```
测试场景: 逻辑值200指向无效装备
前置条件: 逻辑值200=99999（不存在的装备ID）
操作步骤: 重启游戏
预期结果: 使用默认装备300020，自动设置其关联装备
```

---

## 8. 运维监控

### 8.1 关键指标
- 装备状态规范化触发次数
- 异常状态检测频率
- 自动修复成功率
- 玩家装备切换频率

### 8.2 日志监控
```
关键日志模式:
"33333333 登录装备状态规范化------开始执行"
"33333333 登录装备状态规范化------装备状态规范化成功！"
"33333333 登录装备状态规范化------装备状态规范化可能存在问题"
```

### 8.3 告警机制
- 装备状态规范化失败率 > 5%
- 循环关联检测频率异常
- 默认装备使用率过高

---

## 9. 版本历史

### V1.0 (初始版本)
- 基础装备出战功能
- 简单的关联装备支持
- 战斗前检查机制

### V1.1 (优化版本)
- 多层关联装备支持
- 防循环检测机制
- 改进的日志系统

### V1.2 (当前版本)
- **新增**: 游戏启动时完整装备状态规范化
- **新增**: 基于逻辑值200的智能装备选择
- **新增**: "33333333"日志系统便于调试
- **优化**: 最小化更新策略
- **优化**: 异步验证机制

### V2.1 (当前版本)
- **新增**: EffectID2字段关联装备支持
- **新增**: 双字段同时配置关联装备功能
- **新增**: 多重关联装备支持（单个主装备可关联多个装备）
- **新增**: 智能优先级机制（EffectID2优先作为下一层搜索起点）
- **优化**: 关联装备获取函数支持双字段检测
- **优化**: 日志系统显示详细的字段信息
- **保持**: 完全向后兼容现有EffectID3配置

---

## 10. 未来规划

### 10.1 短期优化
- 增加装备组合预设功能
- 优化关联装备链的UI展示
- 增加玩家手动修复装备状态的入口

### 10.2 长期规划
- 支持条件关联（基于玩家等级、副本进度等）
- 装备出战推荐系统
- 装备组合效果预览

---

## 11. 相关文档
- 《装备系统技术文档》
- 《EquipWeapon配置表说明》
- 《LogicValue系统使用指南》
- 《游戏启动流程文档》

---

**文档维护说明**: 
- 本文档应与代码实现保持同步
- 重大功能变更需更新对应章节
- 新增测试用例需补充到第7章
- 版本发布时需更新第9章版本历史 