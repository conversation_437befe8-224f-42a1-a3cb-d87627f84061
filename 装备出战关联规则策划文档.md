# 装备出战关联规则策划文档

## 文档版本
- **版本号**: V2.1
- **更新日期**: 2024年12月
- **维护人员**: 开发团队
- **文档状态**: 已实施

---

## 1. 系统概述

### 1.1 功能描述
装备出战关联系统确保玩家在任何时候都只有一件主装备（GroupID<100）出战，并自动管理与该主装备关联的装备链。系统在游戏启动、装备切换、战斗前等关键节点进行状态检查和自动修正。

### 1.2 设计目标
- **唯一性保证**: 确保同一时间只有1件GroupID<100的主装备出战
- **关联完整性**: 自动管理多层装备关联链（基于EffectID2和EffectID3）
- **状态一致性**: 游戏重启后装备状态与逻辑值保持一致
- **自动修复**: 检测并自动修复异常的装备出战状态

---

## 2. 核心规则定义

### 2.1 装备分类规则
```
主装备: GroupID < 100
关联装备: GroupID >= 100 且 GroupID < 1000
特殊装备: GroupID >= 1000
```

### 2.2 出战数量限制
- **主装备**: 同时只能有1件出战
- **关联装备**: 根据主装备的EffectID2和EffectID3链自动确定
- **总数限制**: 受解锁格子数量限制

### 2.3 关联规则（V2.1版本）
- 主装备通过`EquipWeapon.EffectID2`和`EquipWeapon.EffectID3`字段定义关联装备
- **双字段支持**: EffectID2和EffectID3都可以配置关联装备
- **多重关联**: 单个主装备可以同时关联多个装备
- 支持多层关联：A(EffectID2→B, EffectID3→C) → B(EffectID2→D) → C(EffectID3→E)
- 防循环检测：避免A→B→A的死循环
- 关联装备必须与主装备同时出战/下战

---

## 3. 触发时机与检查点

### 3.1 游戏启动检查（主要修复点）
**触发时机**: 玩家登录游戏后1秒
**检查内容**:
- 统计当前出战的主装备数量
- 验证逻辑值200对应的装备状态
- 检查关联装备完整性
- 自动修正异常状态

**处理逻辑**:
```lua
-- 优先级顺序
1. 使用逻辑值200作为目标主装备（需验证有效性）
2. 如果逻辑值200无效，使用默认装备ID 300020
3. 获取目标装备的完整关联链
4. 比较当前状态与目标状态
5. 仅在不一致时执行状态更新
```

### 3.2 装备切换检查
**触发时机**: 玩家主动切换装备时
**处理逻辑**: UIRoleEquip.Wear()函数处理完整的装备替换

### 3.3 战斗前检查
**触发时机**: 
- BattleManager.EnterBattle()
- BattleManager.OnConfirmBattleProgress()
- BattleManager.RequestEnterBattleScene()

**检查条件**: 当前只有1件主装备出战时
**处理逻辑**: 补充缺失的关联装备

---

## 4. 技术实现架构

### 4.1 核心函数

#### 4.1.1 多层关联装备获取
```lua
-- UIMainTitle.lua
function GetAllAssociatedEquipIDsForLogin(equipId)
    -- 支持多层关联：A→B→C→D
    -- 防循环检测：visitedEquipIDs表
    -- 返回完整关联装备ID列表
end
```

#### 4.1.2 装备状态规范化
```lua
-- UIMainTitle.lua (V1.2版本)
-- 登录后1秒执行完整的装备状态检查和修正
-- 使用"33333333"日志前缀便于调试
```

#### 4.1.3 装备出战管理
```lua
-- UIRoleEquip.lua
function Wear(equipId)
    -- 处理主装备切换
    -- 自动管理关联装备
    -- 一次性状态更新
end
```

### 4.2 数据存储
- **主存储**: `LogicValue.SetWeaponsKnapsack(equipList)`
- **逻辑值200**: 记录玩家选择的主装备ID
- **配置表**: `Schemes.EquipWeapon` 定义装备关联关系

### 4.3 日志系统
```
"11111" - 登录后装备状态监控
"2222" - 战斗前装备检查
"33333333" - 登录装备状态规范化（V1.2新增）
```

---

## 5. 异常处理机制

### 5.1 常见异常情况
1. **多件主装备同时出战**: 游戏重启后最常见问题
2. **关联装备缺失**: 主装备出战但关联装备未出战
3. **逻辑值200无效**: 指向不存在或非主装备的ID
4. **循环关联**: EffectID3形成死循环

### 5.2 自动修复策略
```
异常类型 → 修复方案
多件主装备 → 保留逻辑值200对应装备，卸下其他主装备
关联装备缺失 → 自动装备完整关联链
逻辑值200无效 → 使用默认装备300020
循环关联 → 检测到循环时停止关联链获取
```

### 5.3 容错机制
- **最小化更新**: 只在状态异常时才执行更新操作
- **验证机制**: 更新后0.5秒验证结果
- **降级策略**: 关键步骤失败时使用默认装备
- **详细日志**: 完整记录异常检测和修复过程

---

## 6. 性能优化策略

### 6.1 检查频率控制
- 游戏启动: 仅执行1次完整检查
- 装备切换: 实时检查
- 战斗前: 轻量级检查（仅在必要时）

### 6.2 计算优化
- 防循环检测: 使用visitedEquipIDs表避免重复计算
- 状态比较: 先比较数量再比较内容
- 批量更新: 使用LogicValue.SetWeaponsKnapsack一次性设置

---

## 7. 测试用例

### 7.1 正常流程测试
```
测试场景: 正常装备切换
前置条件: 装备A出战，逻辑值200=A
操作步骤: 切换到装备B
预期结果: B出战，B的关联装备自动出战，逻辑值200更新为B
```

### 7.2 异常修复测试
```
测试场景: 游戏重启后多件主装备出战
前置条件: 人为设置多件主装备出战，逻辑值200=A
操作步骤: 重启游戏
预期结果: 只有装备A出战，其他主装备自动卸下，A的关联装备自动出战
```

### 7.3 边界条件测试
```
测试场景: 逻辑值200指向无效装备
前置条件: 逻辑值200=99999（不存在的装备ID）
操作步骤: 重启游戏
预期结果: 使用默认装备300020，自动设置其关联装备
```

---

## 8. 运维监控

### 8.1 关键指标
- 装备状态规范化触发次数
- 异常状态检测频率
- 自动修复成功率
- 玩家装备切换频率

### 8.2 日志监控
```
关键日志模式:
"33333333 登录装备状态规范化------开始执行"
"33333333 登录装备状态规范化------装备状态规范化成功！"
"33333333 登录装备状态规范化------装备状态规范化可能存在问题"
```

### 8.3 告警机制
- 装备状态规范化失败率 > 5%
- 循环关联检测频率异常
- 默认装备使用率过高

---

## 9. 版本历史

### V1.0 (初始版本)
- 基础装备出战功能
- 简单的关联装备支持
- 战斗前检查机制

### V1.1 (优化版本)
- 多层关联装备支持
- 防循环检测机制
- 改进的日志系统

### V1.2 (当前版本)
- **新增**: 游戏启动时完整装备状态规范化
- **新增**: 基于逻辑值200的智能装备选择
- **新增**: "33333333"日志系统便于调试
- **优化**: 最小化更新策略
- **优化**: 异步验证机制

### V2.1 (当前版本)
- **新增**: EffectID2字段关联装备支持
- **新增**: 双字段同时配置关联装备功能
- **新增**: 多重关联装备支持（单个主装备可关联多个装备）
- **新增**: 智能优先级机制（EffectID2优先作为下一层搜索起点）
- **优化**: 关联装备获取函数支持双字段检测
- **优化**: 日志系统显示详细的字段信息
- **保持**: 完全向后兼容现有EffectID3配置

---

## 10. 未来规划

### 10.1 短期优化
- 增加装备组合预设功能
- 优化关联装备链的UI展示
- 增加玩家手动修复装备状态的入口

### 10.2 长期规划
- 支持条件关联（基于玩家等级、副本进度等）
- 装备出战推荐系统
- 装备组合效果预览

---

## 11. 相关文档
- 《装备系统技术文档》
- 《EquipWeapon配置表说明》
- 《LogicValue系统使用指南》
- 《游戏启动流程文档》

---

**文档维护说明**: 
- 本文档应与代码实现保持同步
- 重大功能变更需更新对应章节
- 新增测试用例需补充到第7章
- 版本发布时需更新第9章版本历史 

## 9. 装备数据传递给战斗系统完整流程

### 9.1 数据流概述
装备关联出战系统确保关联装备的技能能在战斗中正常使用，通过以下完整的数据传递链路实现：

```
Lua侧装备数据获取 → Lua到C#数据传递 → 战斗系统装备初始化 → 装备属性应用 → 技能数据传递
```

### 9.2 Lua侧装备数据获取

#### 9.2.1 出战装备列表获取
**文件**: `ActorProp.lua`
```lua
-- 获取角色的火之装备列表
function ActorProp.GetWeapons(onlyWear)
    local lst = {}
    local data

    -- 主线进度
    local id = GamePlayerData.GameEctype:GetProgress(1)

    for i, v in ipairs(Schemes.EquipWeapon.items) do
        -- 钱袋 和 背包空格子 总是出战
        if v.GroupID >= 1000 and v.EffectID5 == 0 then
            local weaponProp = ActorProp.GetWeaponProp(v.ID)
            if weaponProp then
                table.insert(lst, weaponProp)
            end
        else
            -- 首战出战的火之装备,取 EquipWeapon表 中 EffectID5=0 的行
            if (id == 0) then
                if (v.EffectID5 == 0) then
                    local weaponProp = ActorProp.GetWeaponProp(v.ID)
                    if weaponProp then
                        table.insert(lst, weaponProp)
                    end
                end
            else
                -- 否则按参数取火之装备
                local weaponProp = ActorProp.GetWeaponProp(v.ID, onlyWear)
                if weaponProp then
                    table.insert(lst, weaponProp)
                end
            end
        end
    end
    return lst
end
```

#### 9.2.2 单个装备属性获取
**文件**: `ActorProp.lua`
```lua
function ActorProp.GetWeaponProp(goodsID, needWear)
    if HelperL.IsEuipType(goodsID) then
        local equipment = Schemes.Equipment:Get(goodsID)
        if equipment then
            if not needWear or GamePlayerData.ActorEquip:IsWear(equipment.ID, 1) then
                local entity = SkepModule:GetSkepByID(equipment.PacketID):GetEntityByGoodsID(equipment.ID)
                return ActorProp.ReadEquipProp(entity)
            end
        end
    end
    return nil
end
```

#### 9.2.3 出战装备ID列表获取
**文件**: `ActorProp.lua`
```lua
-- 获取出战装备背包
GamePlayerData.ActorEquip:GetWeaponsKnapsack(1)
```

#### 9.2.4 装备属性计算
**文件**: `ActorProp.lua`
```lua
-- 读取单件装备的属性
function ActorProp.ReadEquipProp(equip)
    local rtn = {
        -- 计算结果
        EquipProp = ActorProp.NewPropOjb(),
        -- 万分比加成的基础属性(SelfProp+StarProp+StarExpProp)
        BaseProp = ActorProp.NewPropOjb(),
        -- 装备自带的属性
        SelfProp = ActorProp.NewPropOjb(),
        -- 当前星级的升星属性(不含经验提升)
        StarProp = ActorProp.NewPropOjb(),
        -- 由升星经验带来的属性提升
        StarExpProp = ActorProp.NewPropOjb(),
        -- 物品ID
        GoodsID = 0,
        -- 物品名称
        GoodsName = "未找到",
        -- 升星星量
        SmeltStarCount = 0,
        -- 升星经验
        SmeltExp = 0,
    };

    if equip then
        rtn.uid = equip.uid;
        rtn.GoodsID = equip:GetProperty(ENTITY_FIELD.ENTITY_FIELD_ID)
        rtn.SmeltQuality = equip:GetProperty(EQUIPMENT_FIELD.EQUIPMENT_FIELD_QUALITY)
        rtn.SmeltStarNum = equip:GetProperty(EQUIPMENT_FIELD.EQUIPMENT_FIELD_STARNUM)
        rtn.SmeltExp = equip:GetProperty(EQUIPMENT_FIELD.EQUIPMENT_FIELD_SMELTEXP);
        -- 计算装备属性...
    end
    
    return rtn
end
```

### 9.3 Lua到C#数据传递

#### 9.3.1 数据服务接口
**文件**: `DataService.lua`
```lua
-- 获取角色的火之装备列表
function m.GetWeapons(onlyWear)
    return dkjsonHelper.encode(ActorProp.GetWeapons(onlyWear))
end

-- 按物品ID读取角色的火之装备属性
function m.GetWeaponProp(goodsID, needWear)
    return dkjsonHelper.encode(ActorProp.GetWeaponProp(goodsID, needWear))
end

-- 读取角色的装备属性
function m.ReadEquipProp(goodsID)
    local equip = nil
    local equipment = Schemes.Equipment:Get(goodsID)
    if equipment then
        local packsack = SkepModule:GetSkepByID(equipment.PacketID)
        if packsack then
            equip = packsack:GetEntityByGoodsID(equipment.ID)
        end
    end
    return dkjsonHelper.encode(ActorProp.ReadEquipProp(equip))
end
```

#### 9.3.2 C#侧调用接口
**文件**: `LuaDataSrvClient.cs`
```csharp
// 获取角色的武器列表
public List<EquipProps> GetWeapons(bool onlyWear)
{
    var json = LuaManager.Instance.LuaState_.Invoke<bool, string>(
        "DataService.GetWeapons", onlyWear, true);
    return JsonConvert.DeserializeObject<List<EquipProps>>(json);
}

// 按物品ID读取角色的武器属性
public EquipProps GetWeaponProp(int goodsID, bool needWear = false)
{
    var json = LuaManager.Instance.LuaState_.Invoke<int, bool, string>(
        "DataService.GetWeaponProp", goodsID, needWear, true);
    return JsonConvert.DeserializeObject<EquipProps>(json);
}

// 读取角色的装备属性
public EquipProps ReadEquipProp(int goodsId)
{
    var json = LuaManager.Instance.LuaState_.Invoke<int, string>(
        "DataService.ReadEquipProp", goodsId, true);
    return JsonConvert.DeserializeObject<EquipProps>(json);
}
```

#### 9.3.3 装备属性数据结构
**文件**: `EquipProps.cs`
```csharp
// 装备属性(Lua的计算结果)
public class EquipProps
{
    // 计算结果
    public CreaturePropBase EquipProp { get; set; }
    // 万分比加成的基础属性(SelfProp+StarProp+StarExpProp)
    public CreaturePropBase BaseProp { get; set; }
    // 装备自带的属性
    public CreaturePropBase SelfProp { get; set; }
    // 当前星级的升星属性(不含经验提升)
    public CreaturePropBase StarProp { get; set; }
    // 由升星经验带来的属性提升
    public CreaturePropBase StarExpProp { get; set; }
    // 物品ID
    public int GoodsID { get; set; }
    // 物品名称
    public string GoodsName { get; set; }
    // 升星星量
    public int SmeltStarCount { get; set; }
    // 升星经验
    public int SmeltExp { get; set; }
}
```

### 9.4 战斗系统装备初始化

#### 9.4.1 战斗管理器初始化
**文件**: `GameManager.cs`
```csharp
public async UniTaskVoid InitPlayerActor()
{
    // 初始化角色数据
    Actor = new ActorThing
    {
        ActorId = LuaDataSrvClient.Instance.GetActorID(), 
        ActorProp = LuaDataSrvClient.Instance.GetPlayerProp()
    };
    
    // 角色出战的武器 - 调用Lua获取装备数据
    Actor.InitWeapons(LuaDataSrvClient.Instance.GetWeapons(true));

    // 等到通用属性表加载完成
    await UniTask.WaitUntil(() => SingletonMgr.Instance.GlobalMgr.CommonPropCfgLoaded.Value);

    Actor.ReadProgress();
}
```

#### 9.4.2 Actor装备初始化
**文件**: `ActorThing_Props.cs`
```csharp
// 初始化武器列表
public ActorThing InitWeapons(List<EquipProps> weapons)
{
    Weapons = weapons;

    GunIds_Store = SingletonMgr.Instance.CsvLoaderMgr.GetOrAddLoader<GunCsv>().Pb.CSVTable
        .Where(x => !x.IsHidden && CanOfferGun(x))
        .Select(x =>
        {
            WeightingItem<GunCfg.Types.CSVRow> rtn = new WeightingItem<GunCfg.Types.CSVRow> { Value = x };
            rtn.Weighting = rtn.Value.OfferWeight;
            return rtn;
        }).ToList();

    return this;
}
```

#### 9.4.3 枪械创建和初始化
**文件**: `ActorThing.cs`
```csharp
public override void ReCreateGuns(List<GunItem> gunsInBag, bool apply = false)
{
    Guns.Where(p => p.IsHidden).ToList().ForEach(g => g.StopCdExecutor());
    Guns.Clear();

    // 每件出战的武器,创建一个隐藏的枪
    Weapons.ToList().ForEach(w =>
    {
        var csvRow_Equip = SingletonMgr.Instance.CsvLoaderMgr.GetOrAddLoader<EquipmentCsv>().Dic[w.GoodsID];
        var gunItem = new GunItem
        {
            GoodsId = w.GoodsID,
            GunId = csvRow_Equip.ConsignmentStyle,  // Equipment.ConsignmentStyle → Gun.Id
            GunLvl = 1,
            CsvRow_Gun = SingletonMgr.Instance.CsvLoaderMgr.GetOrAddLoader<GunCsv>().Dic[csvRow_Equip.ConsignmentStyle],
            StarCount = LuaDataSrvClient.Instance.GetWeaponProp(w.GoodsID).SmeltStarCount,
        };
        if (gunItem.CsvRow_Gun.GunType is ActionType.Attack or ActionType.Mattack or ActionType.Hp or ActionType.Armor)
        {
            var (gun, _) = AddGun(gunItem, apply);
            gun.Camp = Camp;
            gun.IsHidden = true;
        }
    });

    gunsInBag.Where(g => g.GunId > 0).ToList().ForEach(g =>
    {
        var (gun, _) = AddGun(g, apply);
        gun.Camp = Camp;
    });
}
```

#### 9.4.4 枪械数据结构
**文件**: `GunItem.cs`
```csharp
// 一支枪(角色战场背包中/商店中)
public class GunItem
{
    // 枪的唯一ID
    public Guid GunGuid { get; set; } = Guid.NewGuid();
    // 枪ID
    public int GunId { get; set; }
    // 枪的等级
    public int GunLvl { get; set; }
    // 物品Id
    public int GoodsId { get; set; }
    // 武器的星量
    public int StarCount { get; set; }
    // 枪的背包坐标
    public List<BagPosition> PosInBag { get; set; }
    // 枪的配置行
    public GunCfg.Types.CSVRow CsvRow_Gun { get; set; }
}
```

### 9.5 装备属性应用

#### 9.5.1 枪械初始化
**文件**: `GunThing.cs`
```csharp
// 初始化(读取csv表格中的一行)
public GunThing InitFromCsv(int gunId, int gunLvl, int weaponLvl)
{
    if (gunId <= 0 || gunLvl <= 0)
    {
        return this;
    }
    
    // 设置枪械等级和武器等级
    ThingLvl.Value = gunLvl;
    WeaponLvl.Value = weaponLvl;
    
    // 加载枪械配置
    CsvRow_Gun.Value = SingletonMgr.Instance.CsvLoaderMgr.GetOrAddLoader<GunCsv>().Dic[gunId];
    
    return this;
}
```

#### 9.5.2 装备属性读取
**文件**: `GunThing.cs`
```csharp
public override List<CommonProp> FindInherentPropRows()
{
    List<CommonProp> rtn = new();

    // 角色的枪
    if (Actor != null)
    {
        // 将装备属性转为通用属性
        CreaturePropBase weaponProp =
            LuaDataSrvClient.Instance.GetWeaponProp(CsvRow_Gun.Value.GoodsID)?.EquipProp ??
            LuaDataSrvClient.Instance.ReadStarProp(CsvRow_Gun.Value.GoodsID, -1);
        
        // 应用装备属性到枪械
        if (weaponProp != null)
        {
            // 属性转换和应用逻辑...
        }
    }
    
    return rtn;
}
```

### 9.6 关联装备处理机制

#### 9.6.1 多层关联获取
**文件**: `UIRoleEquip.lua`
```lua
function GetAllAssociatedEquipIDs(equipId)
    local associatedEquipIDs = {}
    local currentEquipId = equipId
    local visitedEquipIDs = {} -- 防止循环引用
    
    while true do
        -- 循环引用检测
        if visitedEquipIDs[currentEquipId] then
            break
        end
        visitedEquipIDs[currentEquipId] = true
        
        -- 获取装备配置
        local equipWeaponConfig = Schemes.EquipWeapon:Get(currentEquipId)
        if not equipWeaponConfig then
            break
        end
        
        local nextEquipId = nil
        local hasAssociated = false

        -- 优先检查EffectID2
        if equipWeaponConfig.EffectID2 and equipWeaponConfig.EffectID2 ~= 0 then
            local associatedId = equipWeaponConfig.EffectID2
            table.insert(associatedEquipIDs, associatedId)
            if not nextEquipId then
                nextEquipId = associatedId
            end
            hasAssociated = true
        end

        -- 检查EffectID3
        if equipWeaponConfig.EffectID3 and equipWeaponConfig.EffectID3 ~= 0 then
            local associatedId = equipWeaponConfig.EffectID3
            table.insert(associatedEquipIDs, associatedId)
            if not nextEquipId then
                nextEquipId = associatedId
            end
            hasAssociated = true
        end

        if hasAssociated and nextEquipId then
            currentEquipId = nextEquipId -- 继续查找下一层关联
        else
            break
        end
    end
    
    return associatedEquipIDs
end
```

#### 9.6.2 战斗前检查
**文件**: `BattleManager.lua`
```lua
-- 如果只有一件装备出战，检查是否需要设置关联装备
if actualEquipCount == 1 then
    local mainEquipID = currentEquipList[1]
    local equipWeaponConfig = Schemes.EquipWeapon:Get(mainEquipID)
    
    -- 使用多层关联装备获取
    local associatedEquipIDs = GetAllAssociatedEquipIDs(mainEquipID)
    if #associatedEquipIDs > 0 then
        for _, associatedEquipID in ipairs(associatedEquipIDs) do
            -- 检查关联装备是否已经出战
            local isAssociatedAlreadyWorn = false
            for _, wornEquipID in ipairs(currentEquipIDs) do
                if wornEquipID == associatedEquipID then
                    isAssociatedAlreadyWorn = true
                    break
                end
            end
            
            if not isAssociatedAlreadyWorn then
                -- 将关联装备设置为出战
                GamePlayerData.ActorEquip:ReplaceEquipIndex(associatedEquipID, 2, 1)
            end
        end
    end
end
```

#### 9.6.3 逻辑值管理
**文件**: `LogicValue.lua`
```lua
function m.SetWeaponsKnapsack(value)
    local str_req2 = string.format('LuaRequestSetLogicData?logicID=%d&logicValue=%d', LOGIC_DATA.DATA_TRAIN_TOTALNUM, value)
    LuaModule.RunLuaRequest(str_req2, function(resultCode2, content2)
        if resultCode2 == RESULT_CODE_BASE.RESULT_COMMON_SUCCEED[1] then
            m.AssociatedEquips()
            ActorDataMgr.SetIntArray(ActorDataCatalog_Const.WeaponsInStage, m.weaponList)
            EventManager:Fire(EventID.LogicDataChange)
        end
    end)
end

function m.AssociatedEquips()
    m.weaponList = {}
    local value = HeroDataManager:GetLogicData(LOGIC_DATA.DATA_TRAIN_TOTALNUM)
    if value > 0 then
        table.insert(m.weaponList, value)
        local equipWeaponConfig = Schemes.EquipWeapon:Get(value)
        if equipWeaponConfig then
            if equipWeaponConfig.EffectID2 > 0 then
                table.insert(m.weaponList, equipWeaponConfig.EffectID2)
            end
            if equipWeaponConfig.EffectID3 > 0 then
                table.insert(m.weaponList, equipWeaponConfig.EffectID3)
            end
        end
    end
end
```

### 9.7 技能数据传递

#### 9.7.1 装备到枪械映射
1. **Equipment.ConsignmentStyle** → **Gun.Id**: 装备通过ConsignmentStyle字段关联到具体的枪械配置
2. **Gun.BulletId** → **Bullet配置**: 枪械通过BulletId关联到子弹模型配置

#### 9.7.2 战斗中使用
**文件**: `UIFightBag.cs`
```csharp
// 战斗背包中的装备管理
public List<GunThing> GunsInBag => SingletonMgr.Instance.BattleMgr.Actor.GunsInBag.ToList()

// 计算装备HP值
private int GetHpValueByActorEquipment()
{
    int hp = 0;
    foreach (var gGun in SingletonMgr.Instance.BattleMgr.Actor.GunsInBag.ToList()
                 .Where(gun => gun.CsvRow_Gun.Value.Id > 0))
    {
        if (gGun.CsvRow_Gun.Value.GunType == ActionType.Hp || gGun.CsvRow_Gun.Value.GunType == ActionType.Armor)
        {
            hp += Convert.ToInt32(LuaDataSrvClient.Instance.ReadEquipProp(gGun.CsvRow_Gun.Value.GoodsID).EquipProp.HP * Math.Pow(1.5f, gGun.ThingLvl.Value - 1));
        }
    }
    return hp + 100;
}
```

### 9.8 数据存储机制

#### 9.8.1 出战装备数据存储
**文件**: `ActorDataCatalog.cs`
```csharp
public enum ActorDataCatalog
{
    // 圣物/武器出战
    WeaponsInStage = 8512,
}
```

**访问方式**:
```csharp
// 读取出战装备数据
ActorDataMgr.SetIntArray(ActorDataCatalog_Const.WeaponsInStage, m.weaponList)
```

### 9.9 关键数据传递流程总结

1. **装备状态获取**: `GamePlayerData.ActorEquip:GetWeaponsKnapsack(1)` 获取当前出战装备列表
2. **属性计算**: `ActorProp.GetWeapons(true)` 计算所有出战装备的属性
3. **数据传递**: `LuaDataSrvClient.Instance.GetWeapons(true)` 传递到C#侧
4. **战斗初始化**: `Actor.InitWeapons()` 初始化角色装备
5. **枪械创建**: `ReCreateGuns()` 根据装备创建对应枪械
6. **属性应用**: `GunThing.InitFromCsv()` 应用装备属性到枪械
7. **关联检查**: 多个检查点确保关联装备正确出战
8. **技能使用**: 战斗中通过枪械配置访问技能数据

这个完整的数据传递链路确保了关联装备的技能能在战斗中正常使用，所有装备属性都被正确传递和应用。 