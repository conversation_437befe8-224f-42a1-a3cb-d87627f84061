-- 王波
local prizeAchieveDataCenter = {}
local bit = require 'bit'
prizeAchieveDataCenter.TypeList = {
    None = 0, -- 无类型
    LoginDay = 1, -- 登录天数
    Level = 2, -- 等级成就
    Power = 3, -- 战力成就
    StrengthLV = 4, -- 强化等级
    Society = 5, -- 帮会
    DivineEQStar = 6, -- 体魄星星
    HorseStar = 7, -- 特殊坐骑星星
    WingStar = 8, -- 特殊翅膀成就
    EquipStar = 9, -- 装备星星
    AdvanceHorseStar = 10, -- 进阶坐骑星星
    AdvanceWingStar = 11, -- 进阶翅膀星星
    BossFirstKill = 12, -- 首领首杀挑战
    AdvanceWingShow = 13, -- 翅膀进阶
    CultivateWingShow = 14, -- 翅膀培养
    AdvanceReikiWheel = 15, -- 星阵进阶
    CultivateReikiWheel = 16, -- 星阵培养
    AdvanceBodyEffect = 17, -- 圣光进阶
    CultivateBodyEffect = 18, -- 圣光培养
    Max = 10 -- 最大

}
-- 获取总类型数量
function prizeAchieveDataCenter.GetPrizeTypeSum()
    local t = {}
    local check = {}
    for k, v in ipairs(Schemes.PrizeAchieve.items) do
        if v.Condition ~= 10 and v.Condition ~= 11 and v.Condition ~= 14 and v.Condition ~= 16 and v.Condition ~= 18 then -- 7和10；8和11 13和14 15和16 17和18 为归为同一类
            if not check[v.Condition] then
                check[v.Condition] = true
                table.insert(t, v)
            end
        end
    end
    return t
end

-- 查询成就ID状态 1已达成未领取 2未达成未领取 3已达成已领取
function prizeAchieveDataCenter.GetPrizeState(prizeID)
    local key = 0
    local schemes = prizeAchieveDataCenter.GetSchemes(prizeID)
    -- 保存逻辑IDx
    local canGetBit = HeroDataManager:GetLogicBit(schemes.SaveID2, schemes.SaveIndex)
    -- 判断是否达成
    local x, y = prizeAchieveDataCenter.GetPrizeValue(prizeID)
    if x / y >= 1 then
        if canGetBit == 0 then
            key = 1
        else
            key = 3
        end
    else
        key = 2
    end
    return key
end
-- 获取某类型成就的所有item
function prizeAchieveDataCenter.GetPrizeTypeList(prizeType, prizeType2)
    if not prizeType then
        return
    end
    local t = {}
    for k, v in ipairs(Schemes.PrizeAchieve.items) do
        if v.Condition == prizeType or (v.Condition == prizeType2) then
            table.insert(t, v)
        end
    end
    return t
end
-- 获取某个成就的进度值
function prizeAchieveDataCenter.GetPrizeValue(prizeID)
    local schemes = prizeAchieveDataCenter.GetSchemes(prizeID)
    if schemes.Condition == prizeAchieveDataCenter.TypeList.LoginDay then
        return prizeAchieveDataCenter.CheckLoginDay(prizeID)
    elseif schemes.Condition == prizeAchieveDataCenter.TypeList.Level then
        return prizeAchieveDataCenter.CheckLevel(prizeID)
    elseif schemes.Condition == prizeAchieveDataCenter.TypeList.Power then
        return prizeAchieveDataCenter.CheckPower(prizeID)
    elseif schemes.Condition == prizeAchieveDataCenter.TypeList.StrengthLV then
        return prizeAchieveDataCenter.CheckStrengthLV(prizeID)
    elseif schemes.Condition == prizeAchieveDataCenter.TypeList.Society then
        return prizeAchieveDataCenter.Checksociety(prizeID)
    elseif schemes.Condition == prizeAchieveDataCenter.TypeList.DivineEQStar then
        return prizeAchieveDataCenter.CheckDivineEquipment(prizeID)
    elseif schemes.Condition == prizeAchieveDataCenter.TypeList.HorseStar then -- 坐骑培养
        -- 现在没有坐骑
        return 0, 0
        -- local id = Schemes.EquipMountAdd.GetEquipID()
        -- local allEntity = RedDotManager:GetFastHorseSkepAllEntity()
        -- return prizeAchieveDataCenter.CultivateQiwuStarNum(prizeID, id, allEntity)
    elseif schemes.Condition == prizeAchieveDataCenter.TypeList.WingStar then -- 披风培养
        -- 现在没有披风
        return 0, 0
        -- local id = Schemes.EquipWingAdd.GetEquipID()
        -- local allEntity = RedDotManager:GetFastWingSkepAllEntity()
        -- return prizeAchieveDataCenter.CultivateQiwuStarNum(prizeID, id, allEntity)
    elseif schemes.Condition == prizeAchieveDataCenter.TypeList.EquipStar then
        return prizeAchieveDataCenter.CheckEquipment(prizeID)
    elseif schemes.Condition == prizeAchieveDataCenter.TypeList.AdvanceHorseStar then -- 坐骑进阶
        local id = Schemes.EquipMountAdd.GetEquipID()
        local entity = EntityModule.entities[SkepModule:GetSkepByID(SKEPID.SKEPID_MOUNT).GetUID(id)]
        return prizeAchieveDataCenter.GetQiwuAdvanceStarNum(prizeID, entity)
    elseif schemes.Condition == prizeAchieveDataCenter.TypeList.AdvanceWingStar then -- 披风进阶
        local id = Schemes.EquipWingAdd.GetEquipID()
        local entity = EntityModule.entities[SkepModule:GetSkepByID(SKEPID.SKEPID_WING).GetUID(id)]
        return prizeAchieveDataCenter.GetQiwuAdvanceStarNum(prizeID, entity)
    elseif schemes.Condition == prizeAchieveDataCenter.TypeList.BossFirstKill then
        return prizeAchieveDataCenter.GetBossFirstKill(prizeID)
    elseif schemes.Condition == prizeAchieveDataCenter.TypeList.AdvanceWingShow then -- 翅膀进阶
        -- 现在没有翅膀
        return 0, 0
        -- local id = Schemes.EquipNewWing.GetEquipID()
        -- local entity = EntityModule.entities[SkepModule:GetSkepByID(SKEPID.SKEPID_NEWWING).GetUID(id)]
        -- return prizeAchieveDataCenter.GetQiwuAdvanceStarNum(prizeID, entity)
    elseif schemes.Condition == prizeAchieveDataCenter.TypeList.CultivateWingShow then -- 翅膀培养
        local id = Schemes.EquipNewWing.GetEquipID()
        local allEntity = RedDotManager:GetFastNewWingSkepAllEntity()
        return prizeAchieveDataCenter.CultivateQiwuStarNum(prizeID, id, allEntity)
    elseif schemes.Condition == prizeAchieveDataCenter.TypeList.AdvanceReikiWheel then -- 星阵进阶
        -- 现在没有星阵   

        return 0, 0
        -- local id = Schemes.EquipAvatar.GetEquipIDByType(AVATAR_TYPE.REIKI_WHEEL)
        -- local entity = EntityModule.entities[SkepModule:GetSkepByID(SKEPID.SKEPID_AVATAR).GetUID(id)]
        -- return prizeAchieveDataCenter.GetQiwuAdvanceStarNum(prizeID, entity)
    elseif schemes.Condition == prizeAchieveDataCenter.TypeList.CultivateReikiWheel then -- 星阵培养
        local id = Schemes.EquipAvatar.GetEquipIDByType(AVATAR_TYPE.REIKI_WHEEL)
        local allEntity = SkepModule.GetAvatarSkep().GetAllEntity()
        return prizeAchieveDataCenter.CultivateQiwuStarNum(prizeID, id, allEntity, 1)
    elseif schemes.Condition == prizeAchieveDataCenter.TypeList.AdvanceBodyEffect then -- 圣光进阶
        -- 现在没有圣光
        return 0, 0
        -- local id = Schemes.EquipAvatar.GetEquipIDByType(AVATAR_TYPE.BODY_EFFECT)
        -- local entity = EntityModule.entities[SkepModule:GetSkepByID(SKEPID.SKEPID_AVATAR).GetUID(id)]
        -- return prizeAchieveDataCenter.GetQiwuAdvanceStarNum(prizeID, entity)
    elseif schemes.Condition == prizeAchieveDataCenter.TypeList.CultivateBodyEffect then -- 圣光培养
        local id = Schemes.EquipAvatar.GetEquipIDByType(AVATAR_TYPE.BODY_EFFECT)
        local allEntity = SkepModule.GetAvatarSkep().GetAllEntity()
        return prizeAchieveDataCenter.CultivateQiwuStarNum(prizeID, id, allEntity, 2)
    end
end

-- 首领首杀挑战检测
function prizeAchieveDataCenter.GetBossFirstKill(prizeID)
    local schemes = prizeAchieveDataCenter.GetSchemes(prizeID)
    local bossFirstKillData = HeroDataManager:GetLogicData(LOGIC_DATA.DATA_CHIEF_FIRST_KILL_FLAG)
    local killFlag = Helper.GetBit(bossFirstKillData, schemes.ConditionY)
    return killFlag, 1
end

-- 查询登录天数类成就进度
function prizeAchieveDataCenter.CheckLoginDay(prizeID)
    local schemes = prizeAchieveDataCenter.GetSchemes(prizeID)
    local targetLoginDay = schemes.ConditionX
    local LoginDay = HeroDataManager:GetLogicData(LOGIC_DATA.DATA_LOGIN_TOTALDAYS)
    return LoginDay, targetLoginDay
end
-- 查询等级类成就进度
function prizeAchieveDataCenter.CheckLevel(prizeID)
    local schemes = prizeAchieveDataCenter.GetSchemes(prizeID)
    local targetLevel = schemes.ConditionX
    local heroLevel = EntityModule.hero:GetProperty(CREATURE_FIELD.CREATURE_FIELD_LEVEL)
    if heroLevel >= targetLevel then
        return 1, 1
    else
        return 0, 1
    end
    -- return heroLevel,targetLevel
end
-- 查询战力类成就进度
function prizeAchieveDataCenter.CheckPower(prizeID)
    local schemes = prizeAchieveDataCenter.GetSchemes(prizeID)
    local targetValue = schemes.ConditionX
    local power = EntityModule.hero:GetProperty(PLAYER_FIELD.PLAYER_FIELD_POWER)
    return power, targetValue
end
-- 查询强化等级类成就进度
function prizeAchieveDataCenter.CheckStrengthLV(prizeID)
    local schemes = prizeAchieveDataCenter.GetSchemes(prizeID)
    -- 目标强化等级
    local tarStrengthLV = schemes.ConditionX
    -- 需求数量目标
    local tarNmu = schemes.ConditionY
    local num = prizeAchieveDataCenter.GetEquipmentStrengthLevelPrize(tarStrengthLV)
    return num, tarNmu
end
-- 查询装备类成就
function prizeAchieveDataCenter.CheckEquipment(prizeID)
    local schemes = prizeAchieveDataCenter.GetSchemes(prizeID)
    local tarStar = schemes.ConditionX
    local tarNmus = schemes.ConditionY
    local num = prizeAchieveDataCenter.CheckEquipmetnStarNmu(tarStar)
    return num, tarNmus
end
-- 查询装备星级成就
function prizeAchieveDataCenter.CheckEquipmetnStarNmu(tarStar)
    local Skep = SkepModule.GetEquipSkep()
    local all = Skep.GetAllEntity()
    local index = 0
    local weaponEntity = SkepModule.GetEquipByGenreAndEquipType(EQUIP_TYPE.EQUIP_TYPE_NORMALWEAPON)
    if weaponEntity then
        local starNum = weaponEntity.propertyLC:GetProperty(EQUIPMENT_FIELD.EQUIPMENT_FIELD_STARNUM)
        local quality = weaponEntity.propertyLC:GetProperty(EQUIPMENT_FIELD.EQUIPMENT_FIELD_QUALITY)
        local s = quality * 10 + starNum
        if s >= tarStar then
            index = index + 1
        end
    end
    for k, v in pairs(all) do
        local place = v.propertyLC:GetProperty(GOODS_FIELD.GOODS_FIELD_PLACE)
        if place ~= EQUIP_TYPE.EQUIP_TYPE_NORMALWEAPON then
            local starNum = v.propertyLC:GetProperty(EQUIPMENT_FIELD.EQUIPMENT_FIELD_STARNUM)
            local quality = v.propertyLC:GetProperty(EQUIPMENT_FIELD.EQUIPMENT_FIELD_QUALITY)
            local s = quality * 10 + starNum
            if s >= tarStar then
                index = index + 1
            end
        end
    end
    return index
end
-- 查询帮会类成就进度
function prizeAchieveDataCenter.Checksociety(prizeID)
    local schemes = prizeAchieveDataCenter.GetSchemes(prizeID)
    -- 如果第一个参数不为0就判断帮会等级 反之判断帮会贡献
    if schemes.ConditionX ~= 0 then
        local societyLevel = 0 -- 现在没有帮会 EntityModule.hero.societyLC.SocietyData.SocietyLevel or 0
        local tarSocietyLevel = schemes.ConditionX
        return societyLevel, tarSocietyLevel
    else
        local societyContribute = 0 -- 现在没有帮会 HelperL.GetSelfTotalContribution() or 0
        local tarSocietyContribute = schemes.ConditionY
        return societyContribute, tarSocietyContribute
    end
end
-- 查询体魄类成就进度
function prizeAchieveDataCenter.CheckDivineEquipment(prizeID)
    local schemes = prizeAchieveDataCenter.GetSchemes(prizeID)
    local tarDivineEquipmenStar = schemes.ConditionX
    local num = prizeAchieveDataCenter.GetDivineEquipmentStarPrize(tarDivineEquipmenStar)
    -- print('体魄类成就进度为',num/tarDivineEquipmenStar)
    return num, tarDivineEquipmenStar
end

-- 装备强化等级成就 
-- tarstrengthLV  目标强化等级
function prizeAchieveDataCenter.GetEquipmentStrengthLevelPrize(tarstrengthLV)
    local equipSkep = SkepModule.GetEquipSkep()
    local allEntity = equipSkep.GetAllEntity()
    local index = 0
    local weaponEntity = SkepModule.GetEquipByGenreAndEquipType(EQUIP_TYPE.EQUIP_TYPE_NORMALWEAPON)
    if weaponEntity then
        local strengthLV = weaponEntity.propertyLC:GetProperty(EQUIPMENT_FIELD.EQUIPMENT_FIELD_SMELTLEVEL)
        if strengthLV >= tarstrengthLV then
            index = index + 1
        end
    end
    for k, v in pairs(allEntity) do
        local place = v.propertyLC:GetProperty(GOODS_FIELD.GOODS_FIELD_PLACE)
        if place ~= EQUIP_TYPE.EQUIP_TYPE_NORMALWEAPON then
            local strengthLV = v.propertyLC:GetProperty(EQUIPMENT_FIELD.EQUIPMENT_FIELD_SMELTLEVEL)
            if strengthLV >= tarstrengthLV then
                index = index + 1
            end
        end
    end
    return index
end
-- tarStarNmu 目标星星数
function prizeAchieveDataCenter.GetDivineEquipmentStarPrize(tarStarNmu)
    local skep = SkepModule.GetDivineWeaponSkep()
    local all = skep.GetAllEntity()
    local t = {}
    for k, v in pairs(all) do
        local starNum = v.propertyLC:GetProperty(EQUIPMENT_FIELD.EQUIPMENT_FIELD_STARNUM)
        local quality = v.propertyLC:GetProperty(EQUIPMENT_FIELD.EQUIPMENT_FIELD_QUALITY)
        -- if quality*10+starNum >= tarStarNmu then
        table.insert(t, quality * 10 + starNum)
        -- end
    end
    table.sort(t, function(a, b)
        return a > b
    end)
    return t[1] or 0
end

-- 奇物系列进阶参数判断
function prizeAchieveDataCenter.GetQiwuAdvanceStarNum(prizeID, entity)
    local schemes = prizeAchieveDataCenter.GetSchemes(prizeID)
    local starNum = -1
    local quality = 0
    local starIndex = -1
    if entity then
        quality = entity.propertyLC:GetProperty(EQUIPMENT_FIELD.EQUIPMENT_FIELD_QUALITY)
        starNum = entity.propertyLC:GetProperty(EQUIPMENT_FIELD.EQUIPMENT_FIELD_STARNUM)
        starIndex = quality * 10 + starNum
    else
        return 0, schemes.ConditionX
    end
    if schemes.ConditionX == 0 and starIndex > -1 then
        return 1, 1
    end
    return starIndex, schemes.ConditionX
end
-- 奇物系列培养参数判断
function prizeAchieveDataCenter.CultivateQiwuStarNum(prizeID, id, allEntity, checkType)
    local schemes = prizeAchieveDataCenter.GetSchemes(prizeID)
    local tarStar = schemes.ConditionX
    local tarNmu = schemes.ConditionY
    local num = 0
    local itemID = nil
    local typeResult = true
    for k, v in pairs(allEntity) do
        itemID = v.propertyLC:GetProperty(ENTITY_FIELD.ENTITY_FIELD_ID)
        if checkType then
            local item = Schemes.EquipAvatar.Get(itemID)
            if item then
                typeResult = item.Type == checkType
            end
        end
        if itemID ~= id and typeResult then
            local allStarNum = math.floor(v.propertyLC:GetProperty(EQUIPMENT_FIELD.EQUIPMENT_FIELD_SMELTEXP) / 100)
            if allStarNum >= tarStar then
                num = num + 1
            end
        end
    end
    return num, tarNmu
end

function prizeAchieveDataCenter.GetSchemes(prizeID)
    if not EntityModule.hero then
        error('未发现hero实体')
        return
    end
    if not prizeID then
        error('错误的成就ID')
        return
    end
    local schemes
    for k, v in ipairs(Schemes.PrizeAchieve.items) do
        if v.ID == prizeID then
            schemes = v
        end
    end
    if not schemes then
        error('PrizeAchieve.csv未发现成就ID' .. prizeID)
    end
    return schemes
end
return prizeAchieveDataCenter
