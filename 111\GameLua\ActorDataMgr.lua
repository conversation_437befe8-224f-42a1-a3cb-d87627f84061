--[[
********************************************************************
    created:	2024/05/16
    author :	李锦剑
    purpose:    玩家新逻辑数据(封装C# ActorDataMgr 类方便使用)
    note   :    提示：只在 LogicValue.lua 中使用，其它地方不要直接使用！！！
*********************************************************************
--]]

---新逻辑数据
---@class ActorDataMgr
local m = {}
-- ActorDataMgr = m

--单例
m.Instance = nil

--------------------------------------------------------------------
---获取逻辑值
---@param dataCatalog integer 逻辑值ID
---@param index integer 索引(索引从1开始)
---@param onlyValid ?boolean 是否仅取效数据，默认：true
---@return integer
--------------------------------------------------------------------
function m.GetIntByIndex(dataCatalog, index, onlyValid)
    if not m.Instance then
        return 0
    end
    dataCatalog = tonumber(dataCatalog) or -1
    if dataCatalog < 0 then
        return 0
    end
    index = tonumber(index) or -1
    if index < 1 then
        return 0
    end
    return m.Instance:GetIntByIndex(dataCatalog, index - 1, onlyValid or true)
end

--------------------------------------------------------------------
---保存逻辑值
---@param dataCatalog integer 逻辑值ID
---@param index integer 索引(索引从1开始)
---@param value integer 值
---@return boolean
--------------------------------------------------------------------
function m.SetIntByIndex(dataCatalog, index, value)
    if m.Instance == nil then
        m.Instance = Helper.GetOrAddActorDataMgr()
    end
    dataCatalog = tonumber(dataCatalog) or -1
    if dataCatalog < 0 then
        return false
    end
    index = tonumber(index) or -1
    if index < 1 then
        return false
    end
    return m.Instance:SetIntByIndex(dataCatalog, index - 1, tonumber(value) or 0)
end

--------------------------------------------------------------------
---角色数据已加载
---@param dataCatalog integer 逻辑值ID
---@param onlyValid ?boolean 是否仅取效数据，默认：true
---@return integer[]|nil
--------------------------------------------------------------------
function m.GetIntArray(dataCatalog, onlyValid)
    if m.Instance == nil then
        return nil
    end
    dataCatalog = tonumber(dataCatalog) or -1
    if dataCatalog < 0 then
        return nil
    end
    ---@type integer[]
    local list = {}
    local array = m.Instance:GetIntArray(dataCatalog, onlyValid or true)
    for i = 0, array.Length - 1 do
        table.insert(list, array[i])
    end
    return list
end

--------------------------------------------------------------------
---保存逻辑值
---@param dataCatalog integer 逻辑值ID
---@param value integer[] 值
---@return boolean
--------------------------------------------------------------------
function m.SetIntArray(dataCatalog, value)
    dataCatalog = tonumber(dataCatalog) or -1
    if dataCatalog < 0 then
        return false
    end
    if type(value) ~= 'table' then
        return false
    end
    local list = Helper.NewIntList()
    for i, v in pairs(value) do
        list:Add(v)
    end
    return m.Instance:SetIntArray(dataCatalog, value)
end

--------------------------------------------------------------------
---角色数据已加载
--------------------------------------------------------------------
function m.UpdateActorData(ServerLoaded, LocalLoaded)
    if (ServerLoaded and LocalLoaded) then
        
        EventManager:Fire(EventID.ActorDataMgrInited)
        EventManager:Fire(EventID.ActorDataChanged)
        EntityModule.BoxEquipLevelEXP = PropertyCompute.GetBoxEquipLevel(BOX_EQUIP_ID[1])
    end
end

EventManager:Subscribe(EventID.ActorDataLoaded, m.UpdateActorData)

return m
