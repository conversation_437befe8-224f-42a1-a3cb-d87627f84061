﻿// ReSharper disable InconsistentNaming
// ReSharper disable IdentifierTypo

using System;
using System.Linq;
using System.Threading;

using Cysharp.Threading.Tasks;

using DataStructure;

using Thing;

using UnityEngine;

using View;

using X.PB;

namespace ThingCdExecutors
{
    /// <summary>
    ///     按Cd时长循环执行的执行器(一轮)(功能完成之后就销毁)
    /// </summary>
    public class ThingCdExecutor : IDisposable
    {
        /// <summary>
        ///     哪个物件的执行器
        /// </summary>
        public ThingBase Thing { get; set; }

        /// <summary>
        ///     本轮预设的结束时间(时间到了就停止并销毁)
        /// </summary>
        public float Preset_FinshTime { get; set; }

        /// <summary>
        ///     本轮射击的取消令牌
        /// </summary>
        public CancellationTokenSource CTS_Shooter { get; } = new();

        /// <summary>
        ///     启动执行器：射击、回血、回护甲
        /// </summary>
        public virtual async UniTaskVoid StartExecutor(CancellationToken token)
        {
            await UniTask.SwitchToMainThread();

            CancellationTokenSource cts = CancellationTokenSource.CreateLinkedTokenSource(token, CTS_Shooter.Token);

            // 有回血
            if (Thing.GetTotalDouble(PropType.HpRally).Count > 0)
            {
                DoHpRally(cts.Token).Forget();
            }

            // 有回血Pct
            if (Thing.GetTotalDouble(PropType.HpRallyPct).Count > 0)
            {
                DoHpRallyPct(cts.Token).Forget();
            }

            // 有回护甲
            if (Thing.GetTotalDouble(PropType.ArmorRally).Count > 0)
            {
                DoArmorRally(cts.Token).Forget();
            }

            // 有回护甲Pct
            if (Thing.GetTotalDouble(PropType.ArmorRallyPct).Count > 0)
            {
                DoArmorRallyPct(cts.Token).Forget();
            }

            // 移动状态不射击
            if (Thing is GunThing gun)
            {
                if (gun.Monster?.Monster != null)
                {
                    // 移动时不能射击
                    if (gun.Monster.Monster.MonsterMoveAI.NavAgent.IsNaving)
                    {
                        return;
                    }
                    // 没有攻击目标，不发子弹
                    if (SingletonMgr.Instance.BattleMgr.PlayerActor != null)
                    {
                        Vector2 shootDir = (gun.Monster.Monster.transform.position - SingletonMgr.Instance.BattleMgr.PlayerActor.transform.position);
                        gun.Monster.Monster.SetSpineModelDirection(shootDir.x > 0 ? 1 : -1);
                    }
                }
                else if (gun.Actor != null)
                {
                    // 玩家武器的攻击动画播放逻辑已移至ActorGunCdExecutor.DoShoot方法中处理
                    // 这里不再处理攻击动画播放，避免重复逻辑
                }
            }

            // 尝试射击(能发射子弹,能打到敌人才会真的发射)
            DoShoot(cts.Token).Forget();
        }

        /// <summary>
        ///     回血
        /// </summary>
        public virtual async UniTaskVoid DoHpRally(CancellationToken token)
        {
            await UniTask.SwitchToMainThread();

            // 目标物件
            ThingBase thing = Thing;
            if (Thing is GunThing gun)
            {
                thing = gun.Owner;
            }

            double hp = thing.Hp.Value;
            double maxHp = thing.TotalProp_MaxHp;
            if (hp < maxHp)
            {
                double hpRally = Thing.GetTotalDouble(PropType.HpRally).FirstOrDefault();
                hp += hpRally;
                hp = Math.Clamp(hp, 0, maxHp);
                thing.Hp.Value = hp;
            }
        }

        /// <summary>
        ///     回血Pct
        /// </summary>
        public virtual async UniTaskVoid DoHpRallyPct(CancellationToken token)
        {
            await UniTask.SwitchToMainThread();

            // 目标物件
            ThingBase thing = Thing;
            if (Thing is GunThing gun)
            {
                thing = gun.Owner;
            }

            double hp = thing.Hp.Value;
            double maxHp = thing.TotalProp_MaxHp;
            if (hp < maxHp)
            {
                double hpRallyPct = Thing.GetTotalDouble(PropType.HpRallyPct).FirstOrDefault();
                hp += maxHp * hpRallyPct;
                hp = Math.Clamp(hp, 0, maxHp);
                thing.Hp.Value = hp;
            }
        }

        /// <summary>
        ///     回护甲
        /// </summary>
        public virtual async UniTaskVoid DoArmorRally(CancellationToken token)
        {
            await UniTask.SwitchToMainThread();

            // 目标物件
            ThingBase thing = Thing;
            if (Thing is GunThing gun)
            {
                thing = gun.Owner;
            }

            double armor = thing.Armor.Value;
            double maxArmorPct = thing.GetTotalDouble(PropType.MaxArmorPct).FirstOrDefault();
            double maxArmor = thing.TotalProp_MaxHp * maxArmorPct;
            if (armor < maxArmor)
            {
                double armorRally = Thing.GetTotalDouble(PropType.ArmorRally).FirstOrDefault();
                armor += armorRally;
                armor = Math.Clamp(armor, 0, maxArmor);
                thing.Armor.Value = armor;
            }
        }

        /// <summary>
        ///     回护甲Pct
        /// </summary>
        public virtual async UniTaskVoid DoArmorRallyPct(CancellationToken token)
        {
            await UniTask.SwitchToMainThread();

            // 目标物件
            ThingBase thing = Thing;
            if (Thing is GunThing gun)
            {
                thing = gun.Owner;
            }

            double armor = thing.Armor.Value;
            double maxArmorPct = thing.GetTotalDouble(PropType.MaxArmorPct).FirstOrDefault();
            double maxArmor = thing.TotalProp_MaxHp * maxArmorPct;
            if (armor < maxArmor)
            {
                double armorRallyPct = Thing.GetTotalDouble(PropType.ArmorRallyPct).FirstOrDefault();
                armor += maxArmor * armorRallyPct;
                armor = Math.Clamp(armor, 0, maxArmor);
                thing.Armor.Value = armor;
            }
        }

        /// <summary>
        ///     执行射击
        /// </summary>
        /// <remarks>基类功能:按一轮攻击的持续时长预设结束时间</remarks>
        public virtual async UniTaskVoid DoShoot(CancellationToken token)
        {
            //Thing.PlayAnimation("attack01");
            double attackDuration = Thing.GetTotalDouble(PropType.AttackDuration).FirstOrDefault();
            double minAttackDuration = Thing.GetTotalDouble(PropType.MinAttackDuration).FirstOrDefault();
            if (attackDuration < minAttackDuration)
            {
                attackDuration = minAttackDuration;
            }

            if (attackDuration < 0.01)
            {
                attackDuration = 0.01; // 至少0.01秒
            }

            Preset_FinshTime = Time.time + (float)attackDuration;

            await UniTask.SwitchToMainThread();
        }

        /// <summary>
        ///     射击后做什么
        /// </summary>
        protected virtual void OnAfterShoot()
        {
        }

        /// <summary>
        ///     立即停止本轮执行器
        /// </summary>
        public virtual void StopExecutor()
        {
            Dispose();
        }

        /// <summary>
        ///     清除本轮射击对场景的所有影响(比如,销毁或还回对象池等)
        /// </summary>
        public virtual void ClearShooter()
        {
        }

        #region IDisposable

        protected bool disposedValue;

        /// <param name="disposing">指定释放类型{true:托管对象,false:未托管对象}</param>
        protected virtual void Dispose(bool disposing)
        {
            if (!disposedValue)
            {
                if (disposing)
                {
                    if (!CTS_Shooter.IsCancellationRequested)
                    {
                        CTS_Shooter.Cancel();
                    }

                    ClearShooter();
                }

                // TODO: 释放未托管的资源(未托管的对象)并重写终结器
                // TODO: 将大型字段设置为 null
                disposedValue = true;
            }
        }

        // // TODO: 仅当"Dispose(bool disposing)"拥有用于释放未托管资源的代码时才替代终结器
        // ~CreatureSkillBase()
        // {
        //     // 不要更改此代码。请将清理代码放入"Dispose(bool disposing)"方法中
        //     Dispose(false);
        // }

        public void Dispose()
        {
            // 不要更改此代码。请将清理代码放入"Dispose(bool disposing)"方法中
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        #endregion
    }
}