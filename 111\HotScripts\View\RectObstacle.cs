﻿using Apq.Unity3D.UnityHelpers;

using Thing;

using UnityEngine;

namespace View
{
    /// <summary>
    /// 矩形障碍物(反弹面取宽的边，即默认镜面为两条横线)
    /// </summary>
    public class RectObstacle : ObstacleBase
    {
        /// <summary>
        /// 矩形障碍物物件(数据)
        /// </summary>
        public RectObstacleThing RectObstacleThing => Thing as RectObstacleThing;
        
        /// <summary>
        /// 碰撞盒
        /// </summary>
        public BoxCollider2D BoxCollider2D { get; protected set; }

        public override void Awake()
        {
            base.Awake();

            BoxCollider2D = GetComponent<BoxCollider2D>();

            // // 加载配置
            // Thing = new RectObstacleThing(nameof(Thing));
            // if (ObstacleId > 0)
            // {
            //     Thing.ReloadAttachedProps();
            //     Thing.ReCalcTotalProps();
            // }

            // 界面的初始值同步到数据中
            DoSyncToThing();
        }

        /// <inheritdoc />
        public override void DoSyncToThing()
        {
            base.DoSyncToThing();

            #region 覆盖区域

            if (ObstacleThing != null)
            {
                var rectArea2D = new RectArea2D { Center = transform.position, Angle = transform.eulerAngles.z, };

                if (BoxCollider2D)
                {
                    rectArea2D.Extents = BoxCollider2D.bounds.extents;
                }

                ObstacleThing.Area2D.Replace(rectArea2D);
            }

            #endregion
        }
    }
}