---
--- Generated by <PERSON><PERSON><PERSON>(https://github.com/<PERSON>Lua)
--- Created by Admin.
--- DateTime: 2023/7/21 17:24
--- 全局工具（存放操作过于频繁的操作）
---

--- 是否为空对象
---@param go GameObject @游戏对象
function IsNullGo(go)
    return tolua.isnull(go)
end

--- 是否为非空对象
---@param go GameObject @游戏对象
function IsNotNullGo(go)
    return not IsNullGo(go)
end

---@param t table @表
function IsEmptyTable(t)
    return (t == nil) or (type(t) ~= "table") or (not next(t))
end

--- 追溯内容（例如调用栈）
local function _TraceBack(errorMsg)
    local str_traceBack = debug.traceback(tostring(errorMsg), 6)
    -- 防止行数过多，在Console上不好观察，故拆成两段
    local red_format = "<color=#FFA2FF>[Error]</color> <color=#FFA2FF>%s</color>"
    print(string.format(red_format, "-> TraceBack ↓ ---------------------------------"))
    print(string.format(red_format, str_traceBack))
    print(string.format(red_format, "--------------------------------- ↑ TraceBack <- "))
    return false
end

--- 尝试调用（抱错时会执行_TraceBack）
---@param func function @目标方法
function TryCall(func, ...)
    return xpcall(func, _TraceBack, ...)
end
