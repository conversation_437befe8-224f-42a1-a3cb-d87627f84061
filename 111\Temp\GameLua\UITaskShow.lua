--[[
********************************************************************
	created:	2016/09/20
	author :	吴德燊
	purpose:	任务，队伍小窗口
*********************************************************************
--]] require "UITaskShowItem"

local luaID = ('UITaskShow')

local UITaskShow = {}
local Count = 1 -- 控制提示显示内容
local isMainlansTips = false -- 是否已创建定时器
--- 初始化UITaskShow
function UITaskShow:New(luaObj, gameObject)
    assert(self.gameObject)
    if gameObject then
        self.gameObject = gameObject
    end
    self.ClickedTaskItem = {}
    UITaskShow.lastTimeRepos = UnityEngine.Time.time
    local parentTrans = self.gameObject.transform:Find('root')
    self.luaObj = luaObj
    self.taskListGird = parentTrans:Find('BG/TaskList/UIGrid').gameObject
    self.handObj = parentTrans:Find('Img_hand').gameObject
    self.handObj:SetActive(false)
    self.taskShowBtn = parentTrans:Find('TaskShowBtn').gameObject
    self.diamondItem = parentTrans:Find('diamondeItem').gameObject
    local prefab = HotResManager.ReadUI('ui/Task3D/prefab/UITaskShowItem')
    self.preLoadTaskShowLen = 10
    self.uiTaskShowItemList = {}
    for i = 1, self.preLoadTaskShowLen do
        local o = GameObject.Instantiate(prefab, self.taskListGird.transform)
        local itemScript = CreateUITaskShowItemScript(o)
        table.insert(self.uiTaskShowItemList, itemScript)
    end
    -- 首领
    local onClickBtnBoss = function()
        if self.lastClick == self.bossBtn then
            return
        end
        self.bossBtnLabel.text = GetGameText(luaID, 8)
        self.temaBtnLabel.text = GetGameText(luaID, 3)
        AtlasManager:AsyncGetSprite("zjm_02_29", self.teamBtn)
        AtlasManager:AsyncGetSprite("zjm_02_28", self.bossBtn)
        self.bossList.gameObject:SetActive(true)
        self.goToTeamBtn:SetActive(false)
        -- self.teamListGO:SetActive(false)
        UITaskShow.taskShowBtn.transform.localPosition = Vector3(42, 0, 0)
        -- 显示刷新Boss列表
        if not self.uiBrowseBossItem then
            self.uiBrowseBossItem = require "UIBrowseBossItem"
            self.uiBrowseBossItem.SetParentObj(UITaskShow.bossList)
        end
        self.uiBrowseBossItem.updateDate()
    end
    --- 数据保存
    self.tasksList = {}
    --- 任务
    local onClickBtnTask = function()
        if self.lastClick ~= nil and self.lastClick == self.taskBtn then
            -- UIManager:OpenWnd(WndID.MainTask)
            return
        end
        AtlasManager:AsyncGetSprite("zjm_02_28", self.taskBtn)
        AtlasManager:AsyncGetSprite("zjm_02_29", self.teamBtn)
        EventManager:Fire(EventID.PlayerWakeUp)
        self.taskBtnLabel.text = GetGameText(luaID, 2)
        self.temaBtnLabel.text = GetGameText(luaID, 3)
        self.lastClick = self.taskBtn
        self.goToTeamBtn:SetActive(false)
        UITaskShow.isDirty = true
    end
    -- 队伍
    local onClickBtnTeam = function()
        EventManager:Fire(EventID.PlayerWakeUp)
    end
    -- 收起与弹出
    local onClickBtnTaskShow = function()
        if self.tweenPositionIsInit then
            AtlasManager:AsyncGetSprite("zjm_02_12", self.taskShowBtn)
            self.taskShowBG:SetActive(true)
            self.tweenPositionIsInit = false
        else
            self.tweenPositionIsInit = true
            AtlasManager:AsyncGetSprite("zjm_02_30", self.taskShowBtn)
            self.taskShowBG:SetActive(false)
        end
    end

    local onClickBossRank = function()
        if self.lastClick == self.bossRankBtn then
            return
        end
        AtlasManager:AsyncGetSprite("zjm_02_29", self.teamBtn)
        AtlasManager:AsyncGetSprite("zjm_02_28", self.bossRankBtn)
        self.lastClick = self.bossRankBtn
        self.goToTeamBtn:SetActive(false)
        self.bossList.gameObject:SetActive(false)
        self.bossRankList.gameObject:SetActive(true)
    end

    local onClickEquipMap = function()
        if not EntityModule.hero then
            return
        end
        UIManager.LoadUI('UIProperty')
        UIManager.SendWndMsg('UIProperty', UIWndMsg.UIProperty.jumpPagMsg, {
            tag = 'UISuit'
        })
    end

    self.taskShowBtn.gameObject:GetComponent('Button').onClick:AddListenerEx(function()
        onClickBtnTaskShow()
    end)
    EventManager:Subscribe(EventID.HeroEntered, UITaskShow.OnInitTask)
    UITaskShow.OnInitTask()
    -- 显示任务
    UITaskShow.onClickBtnTask = onClickBtnTask
    UITaskShow.onClickBtnBoss = onClickBtnBoss
    UITaskShow.onClickBossRank = onClickBossRank
    UITaskShow.onClickBtnTeam = onClickBtnTeam

    -- 队伍界面一直开着，不通过队伍按钮来开关了
    onClickBtnTeam()
    -- 是否在任务列表信息更新后进行追踪
    self.isAutoSeekTaskAfterUpdate = false
end

-- 按钮不同的显示方式（横，竖）
function UITaskShow.SetStyle(style)
    if style == 1 then
        UITaskShow.taskBtn = UITaskShow.taskBtn1
        UITaskShow.taskBtnLabel = UITaskShow.taskBtnLabel1
        UITaskShow.teamBtn = UITaskShow.teamBtn1
        UITaskShow.temaBtnLabel = UITaskShow.temaBtnLabel1
        UITaskShow.taskBtn1:SetActive(true)
        UITaskShow.teamBtn1:SetActive(true)
        UITaskShow.taskBtn2:SetActive(false)
        UITaskShow.teamBtn2:SetActive(false)
        -- UITaskShow.taskListScroll.transform.localPosition = Vector3(-59, -121, 0)
        UITaskShow.taskShowBtn.transform.localPosition = Vector3(42, 0, 0)
    elseif style == 2 then
        UITaskShow.taskBtn = UITaskShow.taskBtn2
        UITaskShow.taskBtnLabel = UITaskShow.taskBtnLabel2
        UITaskShow.teamBtn = UITaskShow.teamBtn2
        UITaskShow.temaBtnLabel = UITaskShow.temaBtnLabel2
        UITaskShow.taskBtn1:SetActive(false)
        UITaskShow.teamBtn1:SetActive(false)
        UITaskShow.taskBtn2:SetActive(true)
        UITaskShow.teamBtn2:SetActive(true)
        -- UITaskShow.taskListScroll.transform.localPosition = Vector3(-10, -134, 0)
        UITaskShow.taskShowBtn.transform.localPosition = Vector3(42, -55, 0)
    end
    UITaskShow.taskListUIPanel.clipOffset = Vector2.zero
    UITaskShow.style = 2
    UITaskShow.OnTaskListGirdReposition()
end

function UITaskShow.OnInitTask()
    UITaskShow.UpdateTask(true)
end

function UITaskShow.OnStoppedMoving(movePos)
    if UITaskShow.WithPosTimer then
        UITaskShow.WithPosTimer:Stop()
    end
end

-- 检测地图是否在Boss地图
function UITaskShow.CheckMapHandle()
    UITaskShow.lastClick = nil
    UITaskShow.showLabel.text = ''
    UITaskShow.taskShowBtn:SetActive(true)
    UITaskShow.vipDescBg:SetActive(false)
    UITaskShow.equipMapObj:SetActive(false)
    -- UIManager.mainLandUI.shrinkGrid1_2.gameObject:SetActive(true)
    local Rect = UITaskShow.showLabel.transform:Find('equipmentFrameAnimation(Clone)')
    if Rect then
        GameObject.Destroy(Rect.gameObject)
    end
    UITaskShow.tipsLabel = {}
    if MixUICenter.isVIPBossMap() then
        UITaskShow.tipsLabel = {GetGameText(luaID, 15), GetGameText(luaID, 17)}
    elseif MixUICenter.isLeanEctypeMap() then
        UITaskShow.tipsLabel = {GetGameText(luaID, 15), GetGameText(luaID, 16)}
    end
    if MixUICenter.isVIPBossMap() then
        UITaskShow.vipDescBg:SetActive(true)
        UITaskShow.taskShowBtn:SetActive(false)
        UITaskShow.bossBtn:SetActive(false)
        UITaskShow.taskBtn:SetActive(false)
        UITaskShow.teamBtn:SetActive(false)
        UITaskShow.teamListGO:SetActive(false)
        UITaskShow.bossRankBtn:SetActive(false)
        -- UITaskShow.taskListScroll.gameObject:SetActive(false)
        -- UITaskShow.focusPanel.gameObject:SetActive(false)
        -- UIManager.mainLandUI.shrinkGrid1_2.gameObject:SetActive(false)
        UITaskShow.onClickBtnBoss()
        EventManager:UnSubscribe(EventID.UnderGroundRank, UITaskShow.BossGameSyncDamage)
        -- UITaskShow.showLabel.text = GetGameText(luaID, 15)
        -- HelperL.AddRectAnimation(UITaskShow.showLabel.gameObject, 386, 24, 6)
        --[[if not UITaskShow.warTeamEctypeTimer and not isMainlansTips then
			UITaskShow.mainlandTipsTimer = Timer.New(UITaskShow.MainlandTips, 10, -1)
			isMainlansTips = true
			UITaskShow.mainlandTipsTimer:Start()
			UITaskShow.MainlandTips()
		end]] --
        UITaskShow.equipMapObj:SetActive(true)
        UITaskShow.UpdateEquipSuitMap()
    elseif MixUICenter.isUnderGroundMap() then
        UITaskShow.bossBtn:SetActive(false)
        UITaskShow.taskBtn:SetActive(false)
        UITaskShow.teamBtn:SetActive(false)
        UITaskShow.teamListGO:SetActive(false)
        UITaskShow.bossRankBtn:SetActive(true)
        -- UITaskShow.taskListScroll.gameObject:SetActive(false)
        -- UITaskShow.focusPanel.gameObject:SetActive(false)
        UITaskShow.bossList.gameObject:SetActive(false)
        UITaskShow.bossRankList.gameObject:SetActive(true)
        UITaskShow.taskShowBtn.transform.localPosition = Vector3(42, 0, 0)
        UITaskShow.onClickBossRank()
        EventManager:Subscribe(EventID.UnderGroundRank, UITaskShow.BossGameSyncDamage)
        -- UITaskShow.showLabel.text = GetGameText(luaID, 15)
        -- HelperL.AddRectAnimation(UITaskShow.showLabel.gameObject, 386, 24, 6)
        UITaskShow.equipMapObj:SetActive(true)
        UITaskShow.UpdateEquipSuitMap()
    elseif MixUICenter.isLeanEctypeMap() then
        if UITaskShow.uiBrowseBossItem then
            UITaskShow.uiBrowseBossItem.OnDisable()
        end
        UITaskShow.bossRankBtn:SetActive(false)
        UITaskShow.bossBtn:SetActive(false)
        UITaskShow.taskBtn:SetActive(true)
        UITaskShow.teamBtn:SetActive(false)
        UITaskShow.teamListGO:SetActive(false)
        UITaskShow.bossList.gameObject:SetActive(false)
        UITaskShow.bossRankList.gameObject:SetActive(false)
        UITaskShow.onClickBtnTask()
        EventManager:UnSubscribe(EventID.UnderGroundRank, UITaskShow.BossGameSyncDamage)
        UITaskShow.showLabel.text = GetGameText(luaID, 15)
        HelperL.AddRectAnimation(UITaskShow.showLabel.gameObject, 386, 24, 6)
        if not UITaskShow.warTeamEctypeTimer and not isMainlansTips then
            UITaskShow.mainlandTipsTimer = Timer.New(UITaskShow.MainlandTips, 10, -1)
            isMainlansTips = true
            UITaskShow.mainlandTipsTimer:Start()
            UITaskShow.MainlandTips()
        end
    elseif MixUICenter.isGlobalWildSceneMap() then
        UITaskShow.bossBtn:SetActive(true)
        UITaskShow.taskBtn:SetActive(false)
        UITaskShow.teamBtn:SetActive(false)
        UITaskShow.teamListGO:SetActive(false)
        UITaskShow.bossRankBtn:SetActive(false)
        -- UITaskShow.taskListScroll.gameObject:SetActive(false)
        -- UITaskShow.focusPanel.gameObject:SetActive(false)
        UITaskShow.onClickBtnBoss()
        EventManager:UnSubscribe(EventID.UnderGroundRank, UITaskShow.BossGameSyncDamage)
    else
        if UITaskShow.uiBrowseBossItem then
            UITaskShow.uiBrowseBossItem.OnDisable()
        end
        UITaskShow.bossRankBtn:SetActive(false)
        UITaskShow.bossBtn:SetActive(false)
        UITaskShow.taskBtn:SetActive(true)
        UITaskShow.teamBtn:SetActive(true)
        -- UITaskShow.teamListGO:SetActive(true)
        UITaskShow.UpdateTeamItemOnInterrupted()
        UITaskShow.bossList.gameObject:SetActive(false)
        UITaskShow.bossRankList.gameObject:SetActive(false)
        UITaskShow.onClickBtnTask()
        UITaskShow.SetStyle(UITaskShow.style)
        if UITaskShow.mainlandTipsTimer then
            UITaskShow.mainlandTipsTimer:Stop()
            UITaskShow.mainlandTipsTimer = nil
            isMainlansTips = false
            Count = 1
        end
        EventManager:UnSubscribe(EventID.UnderGroundRank, UITaskShow.BossGameSyncDamage)
    end
end
-- 枭雄vip地图提示
function UITaskShow.MainlandTips()
    -- local anchorTarget = UIManager.mainLandUI.gameObject
    -- UITaskShow.showLabel:SetAnchor(anchorTarget, -13, -19, 262, 20)
    -- UITaskShow.showLabel.bottomAnchor.relative = 0
    UITaskShow.showLabel.text = UITaskShow.tipsLabel[Count]
    if Count == 1 then
        HelperL.AddRectAnimation(UITaskShow.showLabel.gameObject, 386, 24, 6)
    else
        HelperL.AddRectAnimation(UITaskShow.showLabel.gameObject, 460, 24, 6)
    end
    Count = Count + 1
    if Count > #UITaskShow.tipsLabel then
        Count = 1
    end
end
-- 更新boss伤害排行数据
function UITaskShow.BossGameSyncDamage(message)
    if message == nil then
        return
    end
    local rankListInfo = message.RankList
    -- self.fatigueValue.text = string.format(GetGameText(luaID, 5), message.TiredValue, ' / ', self.maxTiredValue)
    -- self.FatigueSlider.value = message.TiredValue / self.maxTiredValue
    -- if message.TiredValue == self.maxTiredValue then
    -- HelperL.AddAMessageTip(GetGameText(luaID, 8))
    -- end

    if message.SelfRank then
        local damageStr = HelperL.TransNumToStrLetter(message.SelfDamage, 1)
        UITaskShow.selfScore.text = string.format("%s%d%s%s", GetGameText(luaID, 10), message.SelfRank,
            GetGameText(luaID, 12), damageStr)
    else
        UITaskShow.selfScore.text = string.format("%s%s", GetGameText(luaID, 10), GetGameText(luaID, 11))
    end

    if rankListInfo == nil then
        return
    end
    for i = 1, 5 do
        if rankListInfo[i] ~= nil then
            UITaskShow.rankLabelArray[i - 1].gameObject:SetActive(true)
            UITaskShow.ScoreLabelArray[i - 1].gameObject:SetActive(true)
            local k, y = HelperL.CalculatePlayerLoopNum(rankListInfo[i].ActorLevel)
            UITaskShow.rankLabelArray[i - 1].text = string.format("%d. %s%s", i,
                rankListInfo[i].Country ~= 0 and
                    MixUICenter.StrClearColor(HelperL.GetCountryNameColor(rankListInfo[i].Country)) .. '·' or '',
                rankListInfo[i].ActorName)
            local damageStr = HelperL.TransNumToStrLetter(rankListInfo[i].Damage, 1)
            UITaskShow.ScoreLabelArray[i - 1].text = string.format("%s", damageStr)
        else
            UITaskShow.rankLabelArray[i - 1].gameObject:SetActive(false)
            UITaskShow.ScoreLabelArray[i - 1].gameObject:SetActive(false)
        end
    end
end

-- 离开副本时处理
function UITaskShow.OnHeroLeaveEctype(ectypeID)
    if not EntityModule.hero then
        return
    end
    local ectypeScheme = Schemes.ChapEctype.GetItemByEctypeId(ectypeID)
    if not ectypeScheme then
        return
    end
    local chapScheme = Schemes.Chapter.Get(ectypeScheme.ID)
    if not chapScheme then
        return
    end
    UITaskShow.showLabel.gameObject:SetActive(false)
    if chapScheme.SubType == 1 then
        -- 刚出战备任务副本
        for _, v in pairs(EntityModule.hero.heroTaskLC.idToItem) do
            local groupID = v.taskScheme.GroupID
            if groupID == 27 then
                if v:GetClientStatus() == ECTaskStatus.CanComplete then
                    v:WantToTurnInTask()
                    break
                end
            end
        end
    end
end

--- 添加任务条目
function UITaskShow.LateView()
    -- print('Late view')
    EventManager:UnSubscribe(EventID.ChangeTaskShow, UITaskShow.SetStyle)
    EventManager:Subscribe(EventID.ChangeTaskShow, UITaskShow.SetStyle)
    EventManager:UnSubscribe(EventID.TaskPartUpdate, UITaskShow.UpdateTask)
    EventManager:Subscribe(EventID.TaskPartUpdate, UITaskShow.UpdateTask)
    EventManager:UnSubscribe(EventID.SocietyLeave, UITaskShow.UpdateTask)
    EventManager:Subscribe(EventID.SocietyLeave, UITaskShow.UpdateTask)
    EventManager:UnSubscribe(EventID.TaskAcceptNotice, UITaskShow.ShowNoticeArrow)
    EventManager:Subscribe(EventID.TaskAcceptNotice, UITaskShow.ShowNoticeArrow)
    EventManager:UnSubscribe(EventID.GuideMaskOpen, UITaskShow.OnGuideMaskOpen)
    EventManager:Subscribe(EventID.GuideMaskOpen, UITaskShow.OnGuideMaskOpen)
    EventManager:UnSubscribe(EventID.ChangeMap, UITaskShow.CheckMapHandle)
    EventManager:Subscribe(EventID.ChangeMap, UITaskShow.CheckMapHandle)
    EventManager:UnSubscribe(EventID.HeroLeaveEctype, UITaskShow.OnHeroLeaveEctype)
    EventManager:Subscribe(EventID.HeroLeaveEctype, UITaskShow.OnHeroLeaveEctype)
    EventManager:UnSubscribe(EventID.AutoSeekMainTask, UITaskShow.SeekMainTask)
    EventManager:Subscribe(EventID.AutoSeekMainTask, UITaskShow.SeekMainTask)
    EventManager:UnSubscribe(EventID.GuideMaskOpen, UITaskShow.GuideMaskOpen)
    EventManager:Subscribe(EventID.GuideMaskOpen, UITaskShow.GuideMaskOpen)
    EventManager:UnSubscribe(EventID.UpdateHeroPropActiveRecord, UITaskShow.UpdateEquipSuitMap)
    EventManager:Subscribe(EventID.UpdateHeroPropActiveRecord, UITaskShow.UpdateEquipSuitMap)
    EventManager:UnSubscribe(EventID.UpdateHeroPropActiveData, UITaskShow.UpdateEquipSuitMap)
    EventManager:Subscribe(EventID.UpdateHeroPropActiveData, UITaskShow.UpdateEquipSuitMap)
    EventManager:UnSubscribe(EventID.OnHeroStandIdle, UITaskShow.CheckTipArrow)
    EventManager:Subscribe(EventID.OnHeroStandIdle, UITaskShow.CheckTipArrow)
    EventManager:UnSubscribe(EventID.TaskShowHandGuide, UITaskShow.TaskShowHandGuide)
    EventManager:Subscribe(EventID.TaskShowHandGuide, UITaskShow.TaskShowHandGuide)
end

function UITaskShow.SeekMainTask(autoSeekTaskType)
    if not EntityModule.hero then
        return
    end
    -- 在任务列表信息更新后进行追踪
    if autoSeekTaskType == 2 then
        UITaskShow.isAutoSeekTaskAfterUpdate = true
        return
    end
    if autoSeekTaskType == 3 then
        for i, v in ipairs(UITaskShow.uiTaskShowItemList) do
            v.isClickByAuto = true
            v:OnClickContentObj(v)
            v.isClickByAuto = false
            break
        end
        return
    end
end
function UITaskShow:ResetUI()
    for k, v in pairs(UITaskShow.tasksList) do
        UITaskShow:RecycleUnusedItem(v)
        UITaskShow.tasksList[k] = nil
    end
end

local function TaskSortFunc(a, b)
    --[[
	local curFocusTask = EntityModule.hero.heroTaskLC.curFocusTask
	if a == curFocusTask then
		return true
	elseif b == curFocusTask then
		return false
	end
	local status1 = a:GetClientStatus()
	local status2 = b:GetClientStatus()
	local statusPri1 = UITaskShow.TaskStatusPriority[status1] or 0
	local statusPri2 = UITaskShow.TaskStatusPriority[status2] or 0
	if statusPri1 ~= statusPri2 then
		return statusPri1 > statusPri2
	elseif a.taskScheme.SortOrder ~= b.taskScheme.SortOrder then
		return a.taskScheme.SortOrder < b.taskScheme.SortOrder
	end
]]
    local status1 = a:GetClientStatus()
    local status2 = b:GetClientStatus()
    if (a.taskScheme.Type == TaskType.TaskType_ActorLevel or a.taskScheme.Type == TaskType.TaskType_ActorPower) and
        (b.taskScheme.Type == TaskType.TaskType_ActorLevel or b.taskScheme.Type == TaskType.TaskType_ActorPower) then
        if status1 == TASK_STATUS.TASK_STATUS_ACCEPTED and status2 == TASK_STATUS.TASK_STATUS_ACCEPTED then
        elseif status1 == TASK_STATUS.TASK_STATUS_ACCEPTED then
            return false
        elseif status2 == TASK_STATUS.TASK_STATUS_ACCEPTED then
            return true
        end
    elseif (a.taskScheme.Type == TaskType.TaskType_ActorLevel or a.taskScheme.Type == TaskType.TaskType_ActorPower) and
        status1 == TASK_STATUS.TASK_STATUS_ACCEPTED then
        return false
    elseif (b.taskScheme.Type == TaskType.TaskType_ActorLevel or b.taskScheme.Type == TaskType.TaskType_ActorPower) and
        status2 == TASK_STATUS.TASK_STATUS_ACCEPTED then
        return true
    end

    if a.taskScheme.ShowGroup ~= b.taskScheme.ShowGroup then
        return a.taskScheme.ShowGroup < b.taskScheme.ShowGroup
    end
    if a.taskScheme.SortOrder ~= b.taskScheme.SortOrder then
        return a.taskScheme.SortOrder < b.taskScheme.SortOrder
    end
    return a.taskID < b.taskID
end

UITaskShow.TaskStatusPriority = {
    [ECTaskStatus.CanComplete] = 3,
    [ECTaskStatus.UnComplete] = 2,
    [ECTaskStatus.Doing] = 2,
    [ECTaskStatus.CanAccept] = 1
}
UITaskShow.curShowTaskNum = 0
function UITaskShow.UpdateTask(needScroll)
    local taskList = {}
    for k, v in pairs(EntityModule.hero.heroTaskLC.idToItem) do
        if v.taskScheme.GroupID ~= 18 and v.taskScheme.GroupID ~= 38 then
            local clientStatus = v:GetClientStatus()
            if clientStatus ~= ECTaskStatus.Completed and clientStatus ~= ECTaskStatus.Null then
                table.insert(taskList, v)
            end
        end
    end
    local taskPart = EntityModule.hero.heroTaskLC
    table.sort(taskList, TaskSortFunc)

    -- 同组任务未接的最多只显示一个，先删一轮
    local curFocusTask = EntityModule.hero.heroTaskLC.curFocusTask
    local curFocusTaskID = 0
    if curFocusTask then
        curFocusTaskID = curFocusTask.taskScheme.ID
    end
    local hasFocus = false
    local hasRemoved = false
    local taskShowGroupSign = {}
    local taskRemoveList = {}
    local listSize = #taskList
    for i = 1, listSize do
        local taskItem = taskList[i]
        if not taskShowGroupSign[taskItem.taskScheme.ShowGroup] then
            taskShowGroupSign[taskItem.taskScheme.ShowGroup] = true
            if taskItem.taskID == curFocusTaskID then
                hasFocus = true
            end
        else
            local status = taskItem:GetClientStatus()
            if taskItem.taskScheme.ShowGroup > 10000 then
                status = ECTaskStatus.CanAccept
            end
            local statusPri = UITaskShow.TaskStatusPriority[status] or 0
            if taskItem.taskID == curFocusTaskID then
                -- 无条件保留
                hasFocus = true
            elseif statusPri > UITaskShow.TaskStatusPriority[ECTaskStatus.CanAccept] then
                -- 保留
            else
                hasRemoved = true
                table.insert(taskRemoveList, i)
            end
        end
    end
    listSize = #taskRemoveList
    for i = listSize, 1, -1 do
        table.remove(taskList, taskRemoveList[i])
    end

    -- 超出任务时间的不显示
    listSize = #taskList
    local curTimeTable = os.date("*t", HelperL.GetServerTime())
    for i = listSize, 1, -1 do
        if taskList[i].taskScheme.MinHour ~= 0 and taskList[i].taskScheme.MaxHour ~= 0 then
            if curTimeTable.hour < taskList[i].taskScheme.MinHour or curTimeTable.hour > taskList[i].taskScheme.MaxHour then
                table.remove(taskList, i)
            end
        end
    end
    local defulatCount = 4
    if EntityModule.hero then
        local heroLevel = EntityModule.hero.propertyLC:GetProperty(CREATURE_FIELD.CREATURE_FIELD_LEVEL)
        if heroLevel >= 150 then
            defulatCount = 6
        end
    end
    -- 超过7个时只显示7个，再删一轮
    listSize = #taskList
    if listSize >= defulatCount + 1 then
        local curRemove = 0
        local maxRemove = listSize - defulatCount
        for i = listSize, 1, -1 do
            local taskItem = taskList[i]
            local status = taskItem:GetClientStatus()
            if taskItem.taskScheme.ShowGroup > 10000 then
                status = ECTaskStatus.CanAccept
            end
            local statusPri = UITaskShow.TaskStatusPriority[status] or 0
            if taskItem.taskID == curFocusTaskID then
                -- 无条件保留
            elseif statusPri > UITaskShow.TaskStatusPriority[ECTaskStatus.CanAccept] then
                -- 保留
            else
                -- 移除
                hasRemoved = true
                table.remove(taskList, i)

                curRemove = curRemove + 1
                if curRemove >= maxRemove then
                    break
                end
            end
        end
    end
    if hasRemoved then
        -- GetGameText(luaID, 6)按钮占位符
        table.insert(taskList, {
            branchID = -1,
            taskID = -1,
            GetClientStatus = function()
                return 0
            end
        })
    end
    -- 无选中任务时默认第一个
    if not hasFocus and #taskList > 0 then
        curFocusTask = taskList[1]
        curFocusTaskID = curFocusTask.taskID
    end

    local oldTasks = {}
    for i, v in ipairs(UITaskShow.uiTaskShowItemList) do
        if v.taskItem then
            oldTasks[i] = {}
            oldTasks[i].branchID = v.taskItem.branchID
            oldTasks[i].taskID = v.taskItem.taskID
            oldTasks[i].step = v.taskItem.step
        end
    end

    if #taskList > #UITaskShow.uiTaskShowItemList then
        local diff = #taskList - #UITaskShow.uiTaskShowItemList
        for i = 1, diff do
            local prefab = HotResManager.ReadUI('ui/Task3D/prefab/UITaskShowItem')
            local o = GameObject.Instantiate(prefab, UITask.taskListGird.gameObject.transform)
            Helper.SetObjectPosition(o, Vector3(0, -(i - 1 + UITaskShow.preLoadTaskShowLen) * 67, 0), true)
            local itemScript = CreateUITaskShowItemScript(o)
            table.insert(UITaskShow.uiTaskShowItemList, itemScript)
        end
        UITaskShow.preLoadTaskShowLen = UITaskShow.preLoadTaskShowLen + diff
    end
    local focusIndex = 0
    for i, v in ipairs(UITaskShow.uiTaskShowItemList) do
        local taskItem = taskList[i]
        if taskItem then
            local clickedTaskItem = UITaskShow.ClickedTaskItem[taskItem.taskID]
            if not clickedTaskItem then
                local clickedTaskStates = {}
                clickedTaskStates.state1 = 0
                clickedTaskStates.state2 = 0
                clickedTaskStates.state3 = 0
                UITaskShow.ClickedTaskItem[taskItem.taskID] = clickedTaskStates
            end
            if v.clickedState ~= ECTaskStatus.Null then
                UITaskShow.SetTaskState(UITaskShow.ClickedTaskItem[taskItem.taskID], v.clickedState)
                v.clickedState = ECTaskStatus.Null
            end
            local taskState = taskItem:GetClientStatus()
            if UITaskShow.CheckTaskState(UITaskShow.ClickedTaskItem[taskItem.taskID], taskState) then
                v.isFirstClick = true
            else
                v.isFirstClick = false
            end
            v:SetData(taskItem.branchID, taskItem.taskID, taskItem:GetClientStatus())
            if UITaskShow.IsNewAddTask(oldTasks, taskItem) then
                v:AcceptMission(taskItem.branchID, taskItem.taskID)
            end
            v.gameObject.name = taskItem.taskID
            if taskItem.taskID == -1 then
                for i = 0, v.gameObject.transform.childCount - 1 do
                    local o = v.gameObject.transform:GetChild(i).gameObject
                    if o.name == 'UICommonItem(Clone)' then
                        o:SetActive(false)
                    end
                end
            end
            v.gameObject:SetActive(true)
            if taskItem.taskID == curFocusTaskID then
                -- if not v.frameAnimation or tolua.isnull(v.frameAnimation.gameObject) then
                -- 自动接任务
                if not v.rect then
                    -- v.frameAnimation = HelperL.AddRectAnimation(v.gameObject, 274, 72, 1)
                    -- local rectSprite = v.rect:GetComponent('Image')

                else
                    -- v.frameAnimation:Set(v.gameObject, 274, 72, 1)
                    local rectSprite = v.rect:GetComponent('Image')

                    v.rect:SetActive(true)
                end
                focusIndex = i
            else
                -- if not v.frameAnimation or tolua.isnull(v.frameAnimation.gameObject) then
                if not v.rect then
                else
                    v.rect:SetActive(false)
                end
            end
        else
            v:StopFocusAnimation()
            v.gameObject:SetActive(false)
        end
    end
    if UITaskShow.isAutoSeekTaskAfterUpdate then
        UITaskShow.isAutoSeekTaskAfterUpdate = false
        UITaskShow.SeekMainTask(3)
    end
    -- UITaskShow.taskListGird.repositionNow = true

    -- 选中的居中
    local maxIndex = #taskList
    UITaskShow.curShowTaskNum = maxIndex
    if needScroll and maxIndex > 0 then
        local center = (focusIndex - 1) * 72 - 32
        local total = maxIndex * 72 - 170
        if total > 0 then
            -- UITaskShow.taskListScroll:SetDragAmount(0, center / total, false)
        elseif maxIndex <= 3 then
            -- UITaskShow.taskListGird.onReposition = UITaskShow.OnTaskListGirdReposition
        end
    end
    UITaskShow.UpdateNoticeArrow()
    -- UITaskShow.focusPanel.localPosition = UITaskShow.taskListScroll.transform.localPosition
    -- UITaskShow.focusUIPaenl.clipOffset = Vector2(0, UITaskShow.taskListUIPanel.clipOffset.y)
end

-- 判断是否为新增任务
function UITaskShow.IsNewAddTask(oldTasks, taskItem)
    for i, v in ipairs(oldTasks) do
        if v.taskID == taskItem.taskID and v.branchID == taskItem.branchID and v.step == taskItem.step then
            return false
        end
    end
    return true
end

-- 任务列表位置变更回调
function UITaskShow.OnTaskListGirdReposition()
    -- UITaskShow.taskListScroll:ResetPosition()
    -- UITaskShow.taskListGird.onReposition = nil
end

-- 队伍相关操作的回调
function UITaskShow.TeamOperateResultCallBack(operateResult)

end

-- 清空队伍
function UITaskShow.ClearTeam()

end

-- 更新队伍item
function UITaskShow.UpdateTeamItem(sc_teamInfo)

end

function UITaskShow.DestroyGridChild()
    local childArr = UITaskShow.teamGrid:GetComponentsInChildren(System.Type.GetType("LuaMonoBehavior"))
    if childArr ~= nil then
        for i = 0, childArr.Length - 1 do
            GameObject.Destroy(childArr[i].gameObject)
        end
    end
end

function UITaskShow.CreateTeam(m)
    -- if UITaskShow.lastClick == UITaskShow.teamBtn then
    UITaskShow.UpdateTeamItem(m)
    -- end
end

-- 进入游戏，获取队伍信息
function UITaskShow.GameEnterGetTeamInfo(teamInfo)

end

-- 开始拖动回调
function UITaskShow.OnDragStarted()
    local self = UITaskShow
    if self.needTipArrowTask then
        self.needTipArrowTask = nil
        self.UpdateNoticeArrow()
    end
    UITaskShow.WithPosTimer = Timer.New(function()
        -- UITaskShow.focusPanel.localPosition = UITaskShow.taskListScroll.transform.localPosition
        UITaskShow.focusUIPaenl.clipOffset = Vector2(0, UITaskShow.taskListUIPanel.clipOffset.y)
    end, 0.01, -1)
    UITaskShow.WithPosTimer:Start()
end

-- 结束拖动回调
function UITaskShow.OnDragFinished()
    local self = UITaskShow
    if UITaskShow.curShowTaskNum <= 2 then
        -- UITaskShow.taskListScroll:SetDragAmount(0, 0.0001, false)
    end
end

-- 显示提示箭头
function UITaskShow.ShowNoticeArrow(taskItem, isAdd)
    local self = UITaskShow
    if not taskItem or tolua.isnull(self.gameObject) then
        return
    end

    if not isAdd then
        if self.needTipArrowTask == taskItem.taskID then
            self.needTipArrowTask = nil
            self.UpdateNoticeArrow()
        end
        return
    end

    self.needTipArrowTask = taskItem.taskID
end
-- 显示提示箭头
function UITaskShow.UpdateNoticeArrow()
    -- local self = UITaskShow
    -- local taskID = self.needTipArrowTask
    -- self.tipArrow:SetActive(false)
    -- if not taskID or taskID == 0 or UITaskShow.hideTipArrow then
    --     return
    -- end

    -- if not EntityModule.hero then
    --     return
    -- end
    -- local heroLevel = EntityModule.hero.propertyLC:GetProperty(CREATURE_FIELD.CREATURE_FIELD_LEVEL)
    -- if heroLevel >= UIManager.taskTipArrowMaxLevel then
    --     return
    -- end

    -- local targetObj = nil
    -- for i, v in ipairs(UITaskShow.uiTaskShowItemList) do
    --     if v.taskItem and taskID == v.taskItem.taskID then
    --         targetObj = v.gameObject
    --         v.effect:SetActive(false)
    --     end
    -- end
    -- if not targetObj then
    --     return
    -- end
    --[[
	local tarPos = targetObj.transform.position
	tarPos.x = tarPos.x + 0.5
	self.tipArrow.transform.position = tarPos
	self.tipArrow:SetActive(true)
	local tweenPos = self.tipArrow:GetComponent('TweenPosition')
	if not tweenPos then
		tweenPos = self.tipArrow:AddComponent(System.Type.GetType('TweenPosition, Assembly-CSharp-firstpass'))
		tweenPos.duration = 0.4
		tweenPos.style = UITweener.Style.PingPong
	end
	local srcPos = self.tipArrow.transform.localPosition
	tweenPos.from = srcPos
	srcPos.x = srcPos.x + 0.05 * 360
	tweenPos.to = srcPos
	tweenPos:ResetToBeginning()
	--]]
end

-- 引导开始通知
function UITaskShow.OnGuideMaskOpen(isOpen)
    if isOpen then
        UITaskShow.hideTipArrow = true
    else
        UITaskShow.hideTipArrow = false
    end
    UITaskShow.UpdateNoticeArrow()
end

-- 检查是否是已经点击的任务ID
function UITaskShow.CheckTaskState(taskItem, taskState)
    if taskState == ECTaskStatus.CanAccept and taskItem.state1 == 1 then
        return true
    end
    if taskState == ECTaskStatus.Doing and taskItem.state2 == 1 then
        return true
    end
    if taskState == ECTaskStatus.CanComplete and taskItem.state3 == 1 then
        return true
    end
    return false
end

function UITaskShow.SetTaskState(taskItem, taskState)
    if taskState == ECTaskStatus.CanAccept then
        taskItem.state1 = 1
        return
    end
    if taskState == ECTaskStatus.Doing then
        taskItem.state2 = 1
        return
    end
    if taskState == ECTaskStatus.CanComplete then
        taskItem.state3 = 1
        return
    end
end
function UITaskShow.GuideMaskOpen(state)
    if state then
        if not UITaskShow.taskListUIPanel or tolua.isnull(UITaskShow.taskListUIPanel) then
            UITaskShow.taskListUIPanel = UITaskShow.gameObject.transform:Find('TaskList'):GetComponent('UIPanel')
        end
        if UITaskShow.style == 1 then
            SpringPanel.Begin(UITaskShow.taskListUIPanel.cachedGameObject, Vector3.New(-59, -121, 0), 100)
        elseif UITaskShow.style == 2 then
            SpringPanel.Begin(UITaskShow.taskListUIPanel.cachedGameObject, Vector3.New(-10, -134, 0), 100)
        end
        UITaskShow.taskListUIPanel.clipOffset = Vector2.zero
    end
end
function UITaskShow.UpdateTeamItemOnInterrupted()

end
function UITaskShow.UpdateEquipSuitMap()
    if not EntityModule.hero then
        return
    end
    if not UITaskShow.equipMapObj.activeSelf then
        return
    end

    local heroLevel = EntityModule.hero.propertyLC:GetProperty(CREATURE_FIELD.CREATURE_FIELD_LEVEL)
    local suitLevel = HelperL.CalculatePlayerLoopNum(heroLevel)
    if suitLevel > 9 then
        suitLevel = 9
    end
    local suitType = suitLevel + 1
    local activeStep = {}
    for i = 3, 5 do
        -- activeStep[i - 2] = MiscDataCenter.GetHeroPropActiveStep(suitType, i)
    end
    local curQuality = nil
    for i = 1, 3 do
        if i == 1 or activeStep[i - 1] >= 4 then
            if activeStep[i] < 4 then
                if not curQuality then
                    curQuality = i + 2
                end
            end
        end
    end
    if not curQuality then
        curQuality = 5
    end
    local color = nil
    if curQuality == 3 then
        color = Color.New(255 / 255, 0, 227 / 255, 1)
    elseif curQuality == 4 then
        color = Color.New(255 / 255, 246 / 255, 0, 1)
    else
        color = Color.New(255 / 255, 104 / 255, 0, 1)
    end
    UITaskShow.equipMapBG.color = color
    for i = 1, 8 do
        local obj = UITaskShow.equipMapObjList[i]
        -- local activeQuality = MiscDataCenter.GetHeroPropActivePartQuality(suitType, i)
        -- if activeQuality >= curQuality then
        --     obj:SetActive(true)
        -- else
        --     obj:SetActive(false)
        -- end
        -- UITaskShow.equipMapSpriteList[i].color = color
    end
end

function UITaskShow.CheckTipArrow()
    if not EntityModule.hero then
        return
    end
    local heroLevel = EntityModule.hero.propertyLC:GetProperty(CREATURE_FIELD.CREATURE_FIELD_LEVEL)
    -- if heroLevel > HangUp.autoTaskLevel then
    -- return
    -- end
    for i, v in ipairs(UITaskShow.uiTaskShowItemList) do
        if v.style == ECTaskStyle.Trunk then
            v:ShowTipArrow(true)
            break
        end
    end
end

function UITaskShow.TaskShowHandGuide(type)
    if UITaskShow.Timer then
        UITaskShow.Timer:Stop()
        UITaskShow.Timer = nil
    end

    UITaskShow.Timer = Timer.New(function()
        UITaskShow.handObj:SetActive(false)
    end, 3, 1)

    if type == 1 then
        UITaskShow.handObj:SetActive(true)
        UITaskShow.handObj.transform.localPosition = Vector3.New(0, -730, 0)
        --HelperL.AddFloatTween(UITaskShow.handObj.transform, 1, 10, 0.3)
        UITaskShow.Timer:Start()
    elseif type == 2 then
        UITaskShow.handObj:SetActive(true)
        UITaskShow.handObj.transform.localPosition = Vector3.New(410, -793, 0)
        --HelperL.AddFloatTween(UITaskShow.handObj.transform, 1, 10, 0.3)
        UITaskShow.Timer:Start()
    elseif type == 3 then
        UITaskShow.handObj:SetActive(false)
        UITaskShow.handObj.transform:DOKill(true)
    end
end

return UITaskShow
