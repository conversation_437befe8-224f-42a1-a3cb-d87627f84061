﻿using Apq.Unity3D.Extension;

using UnityEngine;

namespace View
{
    /// <summary>
    /// 坐标系池
    /// </summary>
    public class CoordinatePool : MonoBehaviour
    {
        /// <summary>
        /// 获取或创建一个坐标系
        /// </summary>
        public GameObject GetOrCreateCoordinate(string nodeName)
        {
            var rtn = transform.GetOrAddChildGameObject(nodeName, gObj =>!gObj.activeSelf);
            rtn.SetActive(true);
            return rtn;
        }
    }
}