local luaID = ('UISevenInvestment')

local UISevenInvestment = {}

-- 初始化
function UISevenInvestment:OnCreate(objList, parentLua)
	self.objList = objList
	self.parentLua = parentLua
	self.item = self.objList.InvestItemItem
	self.item.gameObject:SetActive(false)
	self.objList.Txt_InvestItemTitle.text = GetGameText(luaID, 2)
	self.objList.Txt_InvestItemDesc1.text = GetGameText(luaID, 3)
	self.objList.Txt_InvestItemDesc2.text = GetGameText(luaID, 9)
	self.objList.Txt_InvestItemDesc3.text = GetGameText(luaID, 10)
	self.objList.Txt_InvestItemValue1.text = '5888'
	self.objList.Txt_InvestItemValue2.text = '680'	
	self.itemsContent = self.objList.InvestItemContainer:GetRectTransform()
	self.btnList = {}
	self.investItemsItemList = {}
	self.sevenDaysInvestItems = {}
    for k,v in ipairs(Schemes.SevenDaysInvest.items) do
        if v and v.Type == 1 then
            table.insert(self.sevenDaysInvestItems, v)
        end
    end

	for i = 1, #self.sevenDaysInvestItems do
		local item = {}
		local obj = GameObject.Instantiate(self.item, self.itemsContent)
		local objTrans = obj:GetRectTransform()
		Helper.FillLuaComps(objTrans, item)
		item.gameObject = obj
		item.objTrans = objTrans
		item.config = self.sevenDaysInvestItems[i]
		item.goodContent = item.InvestItemPrizeContent:GetRectTransform()
		item.itemList = {}
		item.index = i
		item.moreOutline = item.Txt_InvestItemMore:GetComponent('Outline')
		item.titleOutline = item.Txt_InvestItemItemTitle:GetComponent('Outline')
		
		table.insert(self.investItemsItemList, item)
		table.insert(self.btnList, item.Btn_InvestItem)
		table.insert(self.btnList, item.Btn_InvestItemMore)
		table.insert(self.btnList, item.Btn_InvestAdvertise)
		table.insert(self.btnList, item.Btn_Get)
		item.Txt_InvestItemMore.text = GetGameText(luaID, 4)
		item.Txt_InvestAdvertise.text = GetGameText(luaID, 8)
		local sprite1 = ''
		local sprite2 = ''
		if i == 1 then
			sprite1 = 'XS-huangxd'
			sprite2 = 'XS-xh'
			item.moreOutline.effectColor = Color(172/255, 101/255, 0, 1)
			item.titleOutline.effectColor = Color(172/255, 101/255, 0, 1)
		else
			sprite1 = 'XS-lanxd'
			sprite2 = 'XS-lh'
			item.moreOutline.effectColor = Color(4/255, 92/255, 163/255, 1)
			item.titleOutline.effectColor = Color(4/255, 92/255, 163/255, 1)
		end 
		AtlasManager:AsyncGetSprite(sprite1, item.Img_InvestItemItemBg)
		AtlasManager:AsyncGetSprite(sprite2, item.Img_InvestItemItemInnerBg)
		item.Btn_InvestItem.onClick:AddListenerEx(function () 
			self:OnClickInvestItem(item)
		end)
		item.Btn_InvestItemMore.onClick:AddListenerEx(function () 
			self:OnClickInvestItemMore(item)
		end)
		item.Btn_InvestAdvertise.onClick:AddListenerEx(function () 
			self:OnClickInvestAdvertise(item)
		end)		
		item.Btn_Get.onClick:AddListenerEx(function () 
			self:OnClickGet(item)
		end)
		item.gameObject:SetActive(true)		
	end
	return true 
end

function UISevenInvestment:UpdateView()
	local curTime = HelperL.GetServerTime()	
	for k,v in ipairs(self.investItemsItemList) do
		local investTimes = HeroDataManager:GetLogicData(v.config.TimeSaveID) -- 七日投资时间
		local curDay = 1
		if investTimes == 0 then
			curDay = 1
			v.Txt_InvestItemItemTitle.text = string.format(GetGameText(luaID, 1), 1)
			if v.config.CardID == 0 then
				v.Btn_InvestAdvertise.gameObject:SetActive(true)
				v.Btn_InvestItem.gameObject:SetActive(false)		
			else
				local rechargeConfig = Schemes.RechargeCard:Get(v.config.CardID)
				if rechargeConfig then
					v.Txt_InvestItem.text = rechargeConfig.FirstRMB/100
				end
				v.Btn_InvestItem.gameObject:SetActive(true)
				v.Btn_InvestAdvertise.gameObject:SetActive(false)
			end
			v.Btn_Get.gameObject:SetActive(false)
		else
			v.Btn_InvestItem.gameObject:SetActive(false)
			curDay = HelperL.CalculationIntervalDays(investTimes, curTime)
			if curDay > 7 then curDay = 7 end
			v.Txt_InvestItemItemTitle.text = string.format(GetGameText(luaID, 1), curDay)			
			if v.config.ID == 1 then
				if HeroDataManager:GetLogicBit(v.config.GetSaveID, v.config.StartIndex+(curDay-1)) == 0 then --可领取
					v.Btn_Get.gameObject:SetActive(false)
					v.Btn_InvestAdvertise.gameObject:SetActive(true)
				else --已领取 
					v.Btn_Get.gameObject:SetActive(true)
					v.Btn_InvestAdvertise.gameObject:SetActive(false)
					v.Txt_Get.text = GetGameText(luaID, 6)
					HelperL.SetImageGray(v.Btn_Get:GetComponent('Image'), true)
				end
			else
				v.Btn_Get.gameObject:SetActive(true)
				v.Btn_InvestAdvertise.gameObject:SetActive(false)
				if HeroDataManager:GetLogicBit(v.config.GetSaveID, v.config.StartIndex+(curDay-1)) == 0 then --可领取
					v.Txt_Get.text = GetGameText(luaID, 5)
					HelperL.SetImageGray(v.Btn_Get:GetComponent('Image'), false)
				else --已领取 
					v.Txt_Get.text = GetGameText(luaID, 6)
					HelperL.SetImageGray(v.Btn_Get:GetComponent('Image'), true)
				end
			end
		end

		if v.config.ID == 1 then
			local itemRedDot = self.parentLua:SetWndRedDot(v.Btn_InvestAdvertise.transform)
			if itemRedDot then
				itemRedDot:AddCheckParam(RedDotCheckType.SevenDaysInvest, 1, 0)
			end
		else
			local itemRedDot = self.parentLua:SetWndRedDot(v.Btn_Get.transform)
			if itemRedDot then
				itemRedDot:AddCheckParam(RedDotCheckType.SevenDaysInvest, 2, 1)
			end
		end
		
		local prizeID = v.config.PrizeID[curDay]
		local prize = Schemes.PrizeTable:GetPrize(prizeID)
		if prize then
			local prizeList = Schemes.PrizeTable:GetPrizeGoods(prize)
			if prizeList then
				for k, z in ipairs(v.itemList) do
					z.gameObject:SetActive(false)
				end
				for i=1 , #prizeList do
					local item = v.itemList[i]
					if not item then
						item = CreateSingleGoods(v.goodContent)
						table.insert(v.itemList, item)
					end
					item:SetItemData(prizeList[i].ID, prizeList[i].Num)
					item:SetSize(90,90)
					item:SetShowName(false)
					item:SetShowNum(true)
					item:SetVisible(true)
				end
			end
		end
	end	
end

-- 点击领取奖励
function UISevenInvestment:OnClickGet(item)
	if not item then return end
	local investTimes = HeroDataManager:GetLogicData(item.config.TimeSaveID)
	if investTimes == 0 then return end
	self:InvestOperation(item.config.ID, 2)
end

-- 点击领取奖励
function UISevenInvestment:OnClickInvestItem(item)
	if not item then return end
	if item.index ~= 2 then return end
	local investTimes = HeroDataManager:GetLogicData(item.config.TimeSaveID)
	if investTimes ~= 0 then return end
	HelperL.Recharge(item.config.CardID)
end

function UISevenInvestment:InvestOperation(id, opType)
    LuaModule.RunLuaRequest(string.format('LuaRequestSevenDaysInvest?id=%d&opType=%d', id, opType), function(resultCode, content)
        if resultCode ~= RESULT_CODE.RESULT_COMMON_SUCCEED then
            HelperL.ShowMessage(TipType.FlowText, ResultCode:GetResultCodeStr(resultCode, content))	
		else
			self:UpdateView()
        end
    end)
end

--
function UISevenInvestment:OnClickInvestItemMore(item)
	if not item then return end	
	UIManager:OpenWnd(WndID.SevenDayInvestment)
end

function UISevenInvestment:OnClickInvestAdvertise(item)
	if not item then return end	
	if item.index ~= 1 then return end

end
 
-- 窗口关闭
function UISevenInvestment:OnClose()
	
end


-- 窗口销毁
function UISevenInvestment:OnDestroy()
	-- 释放内存
	for i, btn in ipairs(self.btnList) do
		btn.onClick:RemoveAllListeners()
		btn.onClick:Invoke()
	end
end
return UISevenInvestment