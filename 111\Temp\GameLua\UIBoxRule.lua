--[[
********************************************************************
    created:	2024/05/24
    author :	李锦剑
    purpose:    宝箱规则介绍
*********************************************************************
--]]

local luaID = 'UIBoxRule'
--宝箱最大等级
local maxBoxLevel = 10

local boxIconList = {
    [BOX_EQUIP_ID[1]] = {
        bg = 'xsd_bxbjt_1',
        bg2 = 'xsd_bxbjk_1',
        icon = 'xsd_bxtb_1',
        icon2 = 'xsd_bxtb_3',
    },
    [BOX_EQUIP_ID[2]] = {
        bg = 'xsd_bxbjt_2',
        bg2 = 'xsd_bxbjk_2',
        icon = 'xsd_bxtb_2',
        icon2 = 'xsd_bxtb_4',
    },
}

---@class UIBoxRule:UIWndBase
local m = {}

--------------------------------------------------------------------
-- 创建时
--------------------------------------------------------------------
function m.OnCreate()
    m.objList.Txt_Title.text = GetGameText(luaID, 1)
    m.objList.Txt_Title2.text = GetGameText(luaID, 2)
    m.selectLevel = 1
    ---@type Item_Box2[]
    m.Item_Box_List = {}
    local i = 0
    for k, v in pairs(BOX_EQUIP_ID) do
        i = i + 1
        if not m.Item_Box_List[i] then
            m.Item_Box_List[i] = m.Creation_Item_Box(m.objList.Grid_Box, i)
        end
        m.Item_Box_List[i].SetData(v)
    end

    m.RegisterClickEvent()
    return true
end

--------------------------------------------------------------------
-- 打开时
--------------------------------------------------------------------
function m:OnOpen()
    m.selectLevel = 1
    m.UpdateView()
end

--------------------------------------------------------------------
-- 注册点击事件
--------------------------------------------------------------------
function m.RegisterClickEvent()
    m.objList.Btn_Close.onClick:AddListenerEx(function()
        m:CloseSelf()
    end)
    m.objList.Btn_Arrows1.onClick:AddListenerEx(function()
        m.selectLevel = m.selectLevel - 1
        if m.selectLevel < 1 then
            m.selectLevel = 1
        end
        m.UpdateView()
    end)
    m.objList.Btn_Arrows2.onClick:AddListenerEx(function()
        m.selectLevel = m.selectLevel + 1
        if m.selectLevel > maxBoxLevel then
            m.selectLevel = maxBoxLevel
        end
        m.UpdateView()
    end)
end

--------------------------------------------------------------------
--更新界面
--------------------------------------------------------------------
function m.UpdateView()
    m.objList.Txt_Level.text = "LV." .. m.selectLevel
    m.objList.Btn_Arrows1.gameObject:SetActive(m.selectLevel ~= 1)
    m.objList.Btn_Arrows2.gameObject:SetActive(m.selectLevel ~= maxBoxLevel)
    for i, v in ipairs(m.Item_Box_List) do
        v.UpdateView(m.selectLevel)
    end
end

--------------------------------------------------------------------
-- 创建宝箱栏
--------------------------------------------------------------------
function m.Creation_Item_Box(parent, index)
    ---@class Item_Box2
    local item = {}
    item.index = index
    ---@type Item_Goods2[]
    item.Item_Goods_Liat = {}
    item.com = m:CreateSubItem(parent, m.objList.Item_Box)

    ---设置数据
    ---@param equipID integer
    item.SetData = function(equipID)
        item.equipID = equipID
        if item.equipID then
            item.treasureList = {}
            local boxEquipData = PropertyCompute.GetBoxEquipData(equipID)
            if boxEquipData then
                item.treasureList = boxEquipData.treasureList
                local bgData = boxIconList[equipID]
                AtlasManager:AsyncGetSprite(bgData.icon, item.com.Img_Icon)
                AtlasManager:AsyncGetSprite(bgData.bg2, item.com.Img_Bg)
            end
            item.com.gameObject:SetActive(true)
        else
            item.com.gameObject:SetActive(false)
        end
    end

    ---更新界面
    ---@param level integer 宝箱等级
    item.UpdateView = function(level)
        if level < 1 then
            level = 1
        end
        if level > #item.treasureList then
            level = #item.treasureList
        end
        local treasure = item.treasureList[level]
        for i, v in ipairs(treasure) do
            if not item.Item_Goods_Liat[i] then
                item.Item_Goods_Liat[i] = m.Creation_Item_Goods(item.com.Grid_Goods, i)
            end
            item.Item_Goods_Liat[i].UpdateData(v)
        end
    end
    return item
end

--------------------------------------------------------------------
-- 创建宝箱栏
--------------------------------------------------------------------
function m.Creation_Item_Goods(parent, index)
    ---@class Item_Goods2
    local item = {}
    item.index = index
    item.com = m:CreateSubItem(parent, m.objList.Item_Goods)
    item.com.Btn_Click.onClick:AddListenerEx(function()
        if not item.data then return end
        m.ShowRandomRewards(item.data.id)
    end)
    ---更新数据
    ---@param data {id:integer, num:integer}
    item.UpdateData = function(data)
        item.data = data
        if data then
            AtlasManager:AsyncGetGoodsSprite(data.id, item.com.Img_Icon)
            item.com.Txt_Amount.text = "+" .. data.num
            item.com.gameObject:SetActive(true)
        else
            item.com.gameObject:SetActive(false)
        end
    end
    return item
end

--------------------------------------------------------------------
-- 显示随机奖励
--------------------------------------------------------------------
function m.ShowRandomRewards(id)
    local medicament = Schemes.Medicament:Get(id)
    if not medicament or medicament.UseMenu ~= 4 then
        return
    end
    UIManager:OpenWnd(WndID.BoxProbability, id)
end

return m
