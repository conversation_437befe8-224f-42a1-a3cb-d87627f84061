--[[
********************************************************************
    created:    李锦剑
    author :    2024/07/13
    purpose:    邮件
*********************************************************************
--]]

local luaID = ('UIEmail')

---@class UIEmail:UIWndBase
local m = {}

--------------------------------------------------------------------
-- 订阅事件列表
--------------------------------------------------------------------
function m:GetOpenEventList()
    return {
        [EventID.OnNewEmail] = m.UpdateView,
        [EventID.OnDeleteMail] = m.UpdateView,
        [EventID.OnGetEmailInfo] = m.UpdateView,
        [EventID.OnGetEmailList] = m.UpdateView,
        [EventID.OnGetEmailAdjunct] = m.UpdateView,
    }
end

--------------------------------------------------------------------
-- 初始化
--------------------------------------------------------------------
function m:OnCreate()
    m.selectIndex = nil

    m.isdeferredUpdate = false
    m.onceSeleteEmail = true
    ---邮件框列表
    ---@type Item_Email[]
    m.Item_Email_List = {}
    ---邮件物品列表
    ---@type SlotItem[]
    m.emailGoodsList = {}

    m.objList.Txt_AllGet.text = GetGameText(luaID, 12)
    m.objList.Txt_AllDelete.text = GetGameText(luaID, 13)

    m.objList.Txt_Refresh.text = GetGameText(luaID, 15)
    m.objList.Txt_Delete.text = GetGameText(luaID, 11)

    m.objList.Txt_OneGet.text = GetGameText(luaID, 14)
    m.objList.Txt_Tips.text = GetGameText(luaID, 16)
    m.objList.Txt_Email.text = GetGameText(luaID, 20)

    m.objList.InfoContent1:SetActive(true)
    UIManager:SubscribeFrameUpdate(m:GetWndID(), true)
    m.RegisterClickEvent()
    return true
end

--------------------------------------------------------------------
-- 窗口开启
--------------------------------------------------------------------
function m:OnOpen()
    m.objList.InfoContent2.gameObject:SetActive(false)
    local dataList = EmailModule.GetEmailList()
    if dataList and #dataList > 0 then
        m.UpdateView()
    else
        EmailModule.RequestGetEmailList(0, 0)
    end
    local dataList = EmailModule.GetEmailList()
    local num = math.max(#dataList, #m.Item_Email_List)
    if num > 0 then
        m.SelectEmail(1)
    end
end

--------------------------------------------------------------------
-- 注册点击事件
--------------------------------------------------------------------
function m.RegisterClickEvent()
    m.objList.Btn_Close.onClick:AddListenerEx(function()
        m:CloseSelf()
    end)
    m.objList.Btn_Refresh.onClick:AddListenerEx(function()
        EmailModule.RequestGetEmailList(0, 1)
    end)
    m.objList.Btn_AllGet.onClick:AddListenerEx(m.OnGetAllEmailAdjunct)
    m.objList.Btn_OneGet.onClick:AddListenerEx(function()
        m.GetEmailAdjunct(m.selectIndex)
    end)
    m.objList.Btn_AllDelete.onClick:AddListenerEx(m.OnAllDeleteClick)
    m.objList.Btn_Delete.onClick:AddListenerEx(m.OnDeleteClick)
end

--------------------------------------------------------------------
-- 更新界面
--------------------------------------------------------------------
function m.UpdateView()
    if m.isdeferredUpdate then
        m.isdeferredUpdate = false
        if m.timer ~= nil then return end
        m.timer = Timer.New(m.UpdateEmailList, 0.5, 1)
        m.timer:Start()
    else
        m.UpdateEmailList()
    end
    if m.onceSeleteEmail then
        m.onceSeleteEmail = false
        local dataList = EmailModule.GetEmailList()
        local num = math.max(#dataList, #m.Item_Email_List)
        if num > 0 then
            m.SelectEmail(1)
        end
    end
end

--------------------------------------------------------------------
---更新邮件
--------------------------------------------------------------------
function m.UpdateEmailList()
    if m.timer ~= nil then
        m.timer:Stop()
        m.timer = nil
    end

    --未领取附件数量
    local hasAdjunctNum = 0
    local dataList = EmailModule.GetEmailList() or {}

    --MarkRead  0表示未读 1表示已读 2表示已领取
    --顺序是 1已读 0未读 2已领取
    table.sort(dataList, function(a, b)
        if a.MarkRead ~= b.MarkRead then
            if a.MarkRead == 1 then return true end
            if b.MarkRead == 1 then return false end
            return a.MarkRead < b.MarkRead
        end
        return a.SendTime > b.SendTime
    end)

    local num = math.max(#dataList, #m.Item_Email_List)
    for i = 1, num, 1 do
        if not m.Item_Email_List[i] then
            m.Item_Email_List[i] = m.Create_Item_Email(i)
        end
        m.Item_Email_List[i].UpdateData(dataList[i])
        if dataList[i] and dataList[i].HasAdjunct and dataList[i].MarkRead ~= 2 then
            hasAdjunctNum = hasAdjunctNum + 1
        end
    end

    m.objList.Btn_AllGet.gameObject:SetActive(hasAdjunctNum > 0)
    m.objList.Btn_AllDelete.gameObject:SetActive(#dataList > 0)

    m.objList.Txt_Tips.gameObject:SetActive(#dataList == 0)
    m.objList.Txt_MAward.gameObject:SetActive(false)


    if m.selectIndex then
        m.UpdateEmailInfoView()
        m.objList.InfoContent2.gameObject:SetActive(true)
    else
        m.objList.InfoContent2.gameObject:SetActive(false)
    end
end

--------------------------------------------------------------------
---创建邮件框
---@param index integer
---@return Item_Email
--------------------------------------------------------------------
function m.Create_Item_Email(index)
    ---@class Item_Email
    local item = {}
    item.index = index
    item.com = m:CreateSubItem(m.objList.Grid_Email, m.objList.Item_Email)
    item.com.Txt_Get.text = GetGameText(luaID, 2)
    item.com.Txt_Read.text = GetGameText(luaID, 29)

    item.com.Img_Select.gameObject:SetActive(false)
    item.com.Btn_Object.onClick:AddListenerEx(function()
        m.SelectEmail(item.index)
    end)
    item.com.Btn_Get.onClick:AddListenerEx(function()
        --m.GetEmailAdjunct(item.index)
    end)
    item.com.Btn_Read.onClick:AddListenerEx(function()
        m.SelectEmail(item.index)
    end)

    ---更新邮件数据
    ---@param data EmailData
    item.UpdateData = function(data)
        if data then
            item.EmailID = data.EmailID
            local color = data.MarkRead ~= 0 and "#FFFFFF" or "#FFFF00"
            item.com.Txt_Type.text = string.format("<color=%s>%s</color>", color, GetGameText(luaID, 1))
            item.com.Txt_Title.text = string.format("<color=%s>%s</color>", color, data.Title)
            item.com.Txt_DelayTime.text = string.format("<color=%s>%s</color>", color, m.GetDelayTime(data))

            item.com.Btn_Read.gameObject:SetActive(false)
            item.com.Btn_Get.gameObject:SetActive(false)
            item.com.Btn_Had_Read.gameObject:SetActive(false)
            if data.MarkRead == 0 then
                if data.HasAdjunct then
                    item.com.Btn_Get.gameObject:SetActive(true)
                else
                    item.com.Btn_Read.gameObject:SetActive(true)
                end
            else
                if data.HasAdjunct then
                    item.com.Txt_Had_Read.text = CommonTextID.IS_GET
                else
                    item.com.Txt_Had_Read.text = GetGameText(luaID, 30)
                end
                item.com.Btn_Had_Read.gameObject:SetActive(true)
            end
            item.com.Img_Statu.gameObject:SetActive(data.MarkRead == 0)

            item.com.gameObject:SetActive(true)
            if data.MarkRead ~= 2 and data.HasAdjunct then
                item.com.Txt_YiLingQu.gameObject:SetActive(false)
                item.com.Txt_DelayTime.gameObject:SetActive(true)
            else
                item.com.Txt_YiLingQu.gameObject:SetActive(true)
                item.com.Txt_DelayTime.gameObject:SetActive(false)
            end
        else
            item.com.gameObject:SetActive(false)
        end
    end
    return item
end

--------------------------------------------------------------------
---选择邮件框
---@param index integer
--------------------------------------------------------------------
function m.SelectEmail(index)
    local item
    if m.selectIndex then
        item = m.Item_Email_List[m.selectIndex]
        item.com.Img_Select.gameObject:SetActive(false)
    end
    m.selectIndex = index
    item = m.Item_Email_List[index]
    item.com.Img_Select.gameObject:SetActive(true)
    local adjunctData = EmailModule.GetEmailAdjunct(item.EmailID)
    --先拿缓存，没有在请求
    if adjunctData then
        m.UpdateView()
    else
        EmailModule.RequestGetEmailInfo(item.EmailID)
    end
end

--------------------------------------------------------------------
---清除选择
--------------------------------------------------------------------
function m.ClearSelection()
    if m.selectIndex == nil then
        return
    end
    local item = m.Item_Email_List[m.selectIndex]
    item.com.Img_Select.gameObject:SetActive(false)
    item.com.Txt_Title.color = "#805B32"
    item.com.Txt_DelayTime.color = "#805B32"
    m.selectIndex = nil
end

--------------------------------------------------------------------
---获取延迟时间
---@param info any
---@return string
--------------------------------------------------------------------
function m.GetDelayTime(info)
    local time = os.date('%x', info.ValidTime)
    local mon, day, year = string.match(time, "(%d+)/(%d+)/(%d+)")
    if #year < 4 then
        year = "20" .. year
    end
    return year .. "-" .. mon .. "-" .. day
end

--------------------------------------------------------------------
---更新邮件详情
--------------------------------------------------------------------
function m.UpdateEmailInfoView()
    local item = m.Item_Email_List[m.selectIndex]
    local data = EmailModule.GetEmailById(item.EmailID)

    m.objList.Txt_MTitle.text = string.format('%s%s', GetGameText(luaID, 18), data.Title)
    m.objList.Txt_TimeTitle.text = string.format(GetGameText(luaID, 19), m.GetDelayTime(data))

    local content = ''
    local adjunctList = {}
    local adjunctData = EmailModule.GetEmailAdjunct(data.EmailID)
    if adjunctData then
        content = adjunctData.Content
        if adjunctData.AdjunctList and #adjunctData.AdjunctList > 0 then
            adjunctList = adjunctData.AdjunctList
        end
    end

    local num = math.max(#adjunctList, #m.emailGoodsList)
    --创建或更新物品
    for i = 1, num do
        if not m.emailGoodsList[i] then
            m.emailGoodsList[i] = _GAddSlotItem(m.objList.Grid_Goods)
        end
        if adjunctList[i] then
            m.emailGoodsList[i]:SetItemID(adjunctList[i].NumProp[1])
            m.emailGoodsList[i]:SetCount(adjunctList[i].NumProp[2])
            m.emailGoodsList[i]:SetActive(true)
        else
            m.emailGoodsList[i]:SetActive(false)
        end
    end

    m.objList.Txt_MAward.gameObject:SetActive(num > 0)


    m.objList.Txt_ContentValue.text = content
    m.objList.Scr_Goods.gameObject:SetActive(#adjunctList > 0)

    if data.MarkRead ~= 2 and data.HasAdjunct then
        m.objList.Btn_Delete.gameObject:SetActive(false)
        m.objList.Btn_OneGet.gameObject:SetActive(true)
    else
        m.objList.Btn_Delete.gameObject:SetActive(true)
        m.objList.Btn_OneGet.gameObject:SetActive(false)
    end
    m.objList.Txt_OneGet.text = GetGameText(luaID, 2)
end

--------------------------------------------------------------------
--领取所有附件
--------------------------------------------------------------------
function m.OnGetAllEmailAdjunct()
    if #m.Item_Email_List == 0 then
        return
    end
    m.isdeferredUpdate = true
    m.ClearSelection()
    local data
    for i, v in ipairs(m.Item_Email_List) do
        --如果有附件，并且没有被领取
        data = EmailModule.GetEmailById(v.EmailID)
        if data.HasAdjunct and data.MarkRead ~= 2 then
            EmailModule.RequestGetMailAdjunct(data.EmailID)
        end
    end
end

--------------------------------------------------------------------
--获取邮件附件
---@param index integer
--------------------------------------------------------------------
function m.GetEmailAdjunct(index)
    local item = m.Item_Email_List[index]
    if item == nil then
        HelperL.ShowMessage(TipType.FlowText, GetGameText(luaID, 10))
        return
    end

    local emptyCount = SkepModule:GetSkepByID(SKEPID.SKEPID_PACKET):EmptyCount()
    if emptyCount < 1 then
        HelperL.ShowMessage(TipType.FlowText, GetGameText(luaID, 6))
        return
    end

    local data = EmailModule.GetEmailById(item.EmailID)
    if not data.HasAdjunct then
        HelperL.ShowMessage(TipType.FlowText, GetGameText(luaID, 8))
        return
    end
    --m.ClearSelection()
    EmailModule.RequestGetMailAdjunct(item.EmailID)
end

--------------------------------------------------------------------
---删除所有邮件
--------------------------------------------------------------------
function m.OnAllDeleteClick()
    if #m.Item_Email_List == 0 then
        return
    end
    m.isdeferredUpdate = true
    m.ClearSelection()
    local data
    for i, v in ipairs(m.Item_Email_List) do
        data = EmailModule.GetEmailById(v.EmailID)
        if data.MarkRead > 0 then
            EmailModule.RequestDelectEmail(data.EmailID)
        end
    end
end

--------------------------------------------------------------------
---删除邮件
--------------------------------------------------------------------
function m.OnDeleteClick()
    local item = m.Item_Email_List[m.selectIndex]
    if item == nil then
        HelperL.ShowMessage(TipType.FlowText, GetGameText(luaID, 10))
        return
    end
    local data = EmailModule.GetEmailById(item.EmailID)
    if data.MarkRead < 2 and data.HasAdjunct then
        HelperL.ShowMessage(TipType.FlowText, GetGameText(luaID, 9))
        return
    end
    m.ClearSelection()
    EmailModule.RequestDelectEmail(item.EmailID)
end

return m
