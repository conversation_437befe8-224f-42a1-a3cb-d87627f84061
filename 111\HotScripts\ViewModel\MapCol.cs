﻿namespace ViewModel
{
    /// <summary>
    /// 表示一列的x轴范围
    /// </summary>
    public class MapCol
    {
        /// <summary>
        /// 序号(从0开始)
        /// </summary>
        public int ColNo { get; set; }

        /// <summary>
        /// 最小值(包括)
        /// </summary>
        public float MinX { get; set; }

        /// <summary>
        /// 最大值(不包括)
        /// </summary>
        public float MaxX { get; set; }

        public MapCol(int colNo = default, float minX = default, float maxX = default)
        {
            ColNo = colNo;
            MinX = minX;
            MaxX = maxX;
        }
    }
}
