-- Generated By protoc-gen-lua Do not Edit
local protobuf = require "protobuf/protobuf"
module('LuaMessage_pb')
local pb = {}


pb.MSG_LUA_ACTIONID = protobuf.EnumDescriptor();
pb.MSG_LUA_ACTIONID_MSG_LUA_NONE_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_LUA_ACTIONID_MSG_LUA_RUNREQUEST_ENUM = protobuf.EnumValueDescriptor();
pb.CS_LUA_RUNREQUEST = protobuf.Descriptor();
pb.CS_LUA_RUNREQUEST_SERIAL_FIELD = protobuf.FieldDescriptor();
pb.CS_LUA_RUNREQUEST_NEEDREPLY_FIELD = protobuf.FieldDescriptor();
pb.CS_LUA_RUNREQUEST_LUAREQUEST_FIELD = protobuf.FieldDescriptor();
pb.SC_LUA_RUNREQUEST = protobuf.Descriptor();
pb.SC_LUA_RUNREQUEST_SERIAL_FIELD = protobuf.FieldDescriptor();
pb.SC_LUA_RUNREQUEST_RESULT_FIELD = protobuf.FieldDescriptor();
pb.SC_LUA_RUNREQUEST_CONTENT_FIELD = protobuf.FieldDescriptor();

pb.MSG_LUA_ACTIONID_MSG_LUA_NONE_ENUM.name = "MSG_LUA_NONE"
pb.MSG_LUA_ACTIONID_MSG_LUA_NONE_ENUM.index = 0
pb.MSG_LUA_ACTIONID_MSG_LUA_NONE_ENUM.number = 0
pb.MSG_LUA_ACTIONID_MSG_LUA_RUNREQUEST_ENUM.name = "MSG_LUA_RUNREQUEST"
pb.MSG_LUA_ACTIONID_MSG_LUA_RUNREQUEST_ENUM.index = 1
pb.MSG_LUA_ACTIONID_MSG_LUA_RUNREQUEST_ENUM.number = 1
pb.MSG_LUA_ACTIONID.name = "MSG_LUA_ACTIONID"
pb.MSG_LUA_ACTIONID.full_name = ".MSG_LUA_ACTIONID"
pb.MSG_LUA_ACTIONID.values = {pb.MSG_LUA_ACTIONID_MSG_LUA_NONE_ENUM,pb.MSG_LUA_ACTIONID_MSG_LUA_RUNREQUEST_ENUM}
pb.CS_LUA_RUNREQUEST_SERIAL_FIELD.name = "Serial"
pb.CS_LUA_RUNREQUEST_SERIAL_FIELD.full_name = ".CS_Lua_RunRequest.Serial"
pb.CS_LUA_RUNREQUEST_SERIAL_FIELD.number = 1
pb.CS_LUA_RUNREQUEST_SERIAL_FIELD.index = 0
pb.CS_LUA_RUNREQUEST_SERIAL_FIELD.label = 1
pb.CS_LUA_RUNREQUEST_SERIAL_FIELD.has_default_value = false
pb.CS_LUA_RUNREQUEST_SERIAL_FIELD.default_value = 0
pb.CS_LUA_RUNREQUEST_SERIAL_FIELD.type = 13
pb.CS_LUA_RUNREQUEST_SERIAL_FIELD.cpp_type = 3

pb.CS_LUA_RUNREQUEST_NEEDREPLY_FIELD.name = "NeedReply"
pb.CS_LUA_RUNREQUEST_NEEDREPLY_FIELD.full_name = ".CS_Lua_RunRequest.NeedReply"
pb.CS_LUA_RUNREQUEST_NEEDREPLY_FIELD.number = 2
pb.CS_LUA_RUNREQUEST_NEEDREPLY_FIELD.index = 1
pb.CS_LUA_RUNREQUEST_NEEDREPLY_FIELD.label = 1
pb.CS_LUA_RUNREQUEST_NEEDREPLY_FIELD.has_default_value = false
pb.CS_LUA_RUNREQUEST_NEEDREPLY_FIELD.default_value = false
pb.CS_LUA_RUNREQUEST_NEEDREPLY_FIELD.type = 8
pb.CS_LUA_RUNREQUEST_NEEDREPLY_FIELD.cpp_type = 7

pb.CS_LUA_RUNREQUEST_LUAREQUEST_FIELD.name = "LuaRequest"
pb.CS_LUA_RUNREQUEST_LUAREQUEST_FIELD.full_name = ".CS_Lua_RunRequest.LuaRequest"
pb.CS_LUA_RUNREQUEST_LUAREQUEST_FIELD.number = 3
pb.CS_LUA_RUNREQUEST_LUAREQUEST_FIELD.index = 2
pb.CS_LUA_RUNREQUEST_LUAREQUEST_FIELD.label = 1
pb.CS_LUA_RUNREQUEST_LUAREQUEST_FIELD.has_default_value = false
pb.CS_LUA_RUNREQUEST_LUAREQUEST_FIELD.default_value = ""
pb.CS_LUA_RUNREQUEST_LUAREQUEST_FIELD.type = 9
pb.CS_LUA_RUNREQUEST_LUAREQUEST_FIELD.cpp_type = 9

pb.CS_LUA_RUNREQUEST.name = "CS_Lua_RunRequest"
pb.CS_LUA_RUNREQUEST.full_name = ".CS_Lua_RunRequest"
pb.CS_LUA_RUNREQUEST.nested_types = {}
pb.CS_LUA_RUNREQUEST.enum_types = {}
pb.CS_LUA_RUNREQUEST.fields = {pb.CS_LUA_RUNREQUEST_SERIAL_FIELD, pb.CS_LUA_RUNREQUEST_NEEDREPLY_FIELD, pb.CS_LUA_RUNREQUEST_LUAREQUEST_FIELD}
pb.CS_LUA_RUNREQUEST.is_extendable = false
pb.CS_LUA_RUNREQUEST.extensions = {}
pb.SC_LUA_RUNREQUEST_SERIAL_FIELD.name = "Serial"
pb.SC_LUA_RUNREQUEST_SERIAL_FIELD.full_name = ".SC_Lua_RunRequest.Serial"
pb.SC_LUA_RUNREQUEST_SERIAL_FIELD.number = 1
pb.SC_LUA_RUNREQUEST_SERIAL_FIELD.index = 0
pb.SC_LUA_RUNREQUEST_SERIAL_FIELD.label = 1
pb.SC_LUA_RUNREQUEST_SERIAL_FIELD.has_default_value = false
pb.SC_LUA_RUNREQUEST_SERIAL_FIELD.default_value = 0
pb.SC_LUA_RUNREQUEST_SERIAL_FIELD.type = 13
pb.SC_LUA_RUNREQUEST_SERIAL_FIELD.cpp_type = 3

pb.SC_LUA_RUNREQUEST_RESULT_FIELD.name = "Result"
pb.SC_LUA_RUNREQUEST_RESULT_FIELD.full_name = ".SC_Lua_RunRequest.Result"
pb.SC_LUA_RUNREQUEST_RESULT_FIELD.number = 2
pb.SC_LUA_RUNREQUEST_RESULT_FIELD.index = 1
pb.SC_LUA_RUNREQUEST_RESULT_FIELD.label = 1
pb.SC_LUA_RUNREQUEST_RESULT_FIELD.has_default_value = false
pb.SC_LUA_RUNREQUEST_RESULT_FIELD.default_value = 0
pb.SC_LUA_RUNREQUEST_RESULT_FIELD.type = 13
pb.SC_LUA_RUNREQUEST_RESULT_FIELD.cpp_type = 3

pb.SC_LUA_RUNREQUEST_CONTENT_FIELD.name = "Content"
pb.SC_LUA_RUNREQUEST_CONTENT_FIELD.full_name = ".SC_Lua_RunRequest.Content"
pb.SC_LUA_RUNREQUEST_CONTENT_FIELD.number = 3
pb.SC_LUA_RUNREQUEST_CONTENT_FIELD.index = 2
pb.SC_LUA_RUNREQUEST_CONTENT_FIELD.label = 1
pb.SC_LUA_RUNREQUEST_CONTENT_FIELD.has_default_value = false
pb.SC_LUA_RUNREQUEST_CONTENT_FIELD.default_value = ""
pb.SC_LUA_RUNREQUEST_CONTENT_FIELD.type = 9
pb.SC_LUA_RUNREQUEST_CONTENT_FIELD.cpp_type = 9

pb.SC_LUA_RUNREQUEST.name = "SC_Lua_RunRequest"
pb.SC_LUA_RUNREQUEST.full_name = ".SC_Lua_RunRequest"
pb.SC_LUA_RUNREQUEST.nested_types = {}
pb.SC_LUA_RUNREQUEST.enum_types = {}
pb.SC_LUA_RUNREQUEST.fields = {pb.SC_LUA_RUNREQUEST_SERIAL_FIELD, pb.SC_LUA_RUNREQUEST_RESULT_FIELD, pb.SC_LUA_RUNREQUEST_CONTENT_FIELD}
pb.SC_LUA_RUNREQUEST.is_extendable = false
pb.SC_LUA_RUNREQUEST.extensions = {}

CS_Lua_RunRequest = protobuf.Message(pb.CS_LUA_RUNREQUEST)
MSG_LUA_NONE = 0
MSG_LUA_RUNREQUEST = 1
SC_Lua_RunRequest = protobuf.Message(pb.SC_LUA_RUNREQUEST)

