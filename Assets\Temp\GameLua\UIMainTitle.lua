--[[
********************************************************************
    created:	
    author :	
    purpose:    主界面
*********************************************************************
--]]


local luaID = ('UIMainTitle')

--默认主按钮ID
local DefaultMainButton = 3
local EctypeType = 1

---主界面
---@class UIMainTitle:UIWndBase
local m = {}


--------------------------------------------------------------------
-- 订阅事件列表
--------------------------------------------------------------------
function m.GetOpenEventList()
    return {
        [EventID.LogicDataChange] = m.UpdateView,
        [EventID.OnHeroPropChange] = m.UpdateView,
        [EventID.WindowOpen] = m.UpdateView,
        [EventID.StoreBuyItem] = m.UpdateView,
        [EventID.OnSkepGoodsChange] = m.UpdateView,
        [EventID.ActorDataChanged] = m.UpdateView,
        [EventID.EntitySyncBuff] = m.UpdateView,
        [EventID.OnGoodsPropChange] = m.UpdateView,

        [EventID.OpenMainButtonUI] = m.OpenMainButtonUI,
        [EventID.HeroPropertyUpdate_Silver] = m.HeroPropertyUpdate_Silver,
        [EventID.UpdateGameEctypeData] = m.UpdateView,
        [EventID.UpdateGameEctypeBoxData] = m.UpdateView,
		[EventID.WindowClose] = m.OnWindowCloseHandle,
    }
end

--------------------------------------------------------------------
--预制体自适应
--------------------------------------------------------------------
function m.AdaptScale()
    HelperL.AdaptScale_Width(m.objList.Item_Button)
end

--------------------------------------------------------------------
--资源预加载
--------------------------------------------------------------------
function m.GetResourceList()
    return {
        "Assets/Temp/ui/Common/RedDot.prefab",
        "Assets/Temp/ui/Common/MaterialManager.prefab",
        "Assets/Temp/ui/Tips/Empty.prefab",

        "Assets/Temp/ui/Main/WindowController.prefab",
        "Assets/Temp/ui/Common/SlotItem.prefab",
        "Assets/Temp/ui/Common/UIModel.prefab",
        "Assets/Temp/model/prefab/diaoluo/Spine_BaoXiang.prefab",

        "Assets/Temp/model/prefab/diaoluo/jinbi1.prefab",
        "Assets/Temp/model/prefab/diaoluo/Box2.prefab",
    }
end

--------------------------------------------------------------------
--创建时
--------------------------------------------------------------------
function m.OnCreate()
    RankingModule.UpdateRankingRefreshTime(30)

    --神龙
    m.objList.Btn_Test.gameObject:SetActive(GameLuaAPI.GetPlatform() == "unityEditor")
    m.selectCatMainStageID = GamePlayerData.GameEctype:GetChallengeableID(EctypeType)
    m.lastCatMainStageID = m.selectCatMainStageID

    --设置震动默认关闭
    local num = PlayerPrefs.GetInt('SYSTEM_MOBILE_VIBATE_INT', 0)
    if num == 0 then
        PlayerPrefs.SetInt('SYSTEM_MOBILE_VIBATE_INT', 1)
        PlayerPrefs.SetInt("SYSTEM_MOBILE_VIBATE", 0)
    end

    m.EquipWeaponIDList = {}
    for i, v in ipairs(Schemes.EquipWeapon.items) do
        if v.GroupID < 1000 then -- 
            table.insert(m.EquipWeaponIDList, v.ID)
        end
    end

    m.activityGridList = {m.objList.GridRight2,m.objList.GridRight1}
    local activityButtonData = {    
        ------------------------------活动按钮集合2------------------------------
        --{ grid = 1, id = 10000, windowID = 10000,            icon = 'main_30', name = "福利礼包", offset = Vector2(-10, -10), },
       -- { grid = 1, id = 17,   windowID = WndID.Recharge, icon = 'main_18', name = "充值", offset = Vector2(-10, -10), },
        --{ grid = 1, id = 102,   windowID = WndID.ShopNew,            icon = 'main_20', name = "商店", offset = Vector2(-10, -10), },
        { grid = 1, id = 109, windowID = WndID.StoryIntroduce,            icon = 'zhujiemian1043', name = "zhujiemian1002", offset = Vector2(-10, -10), },--背景
        { grid = 1, id = 99, windowID = WndID.RecommendCommodities,  icon = 'zhujiemian1016', name = "zhujiemian1015", offset = Vector2(-10, -10), },--签到
        { grid = 1, id = 19,   windowID = WndID.HeroGift,   icon = 'zhujiemian1010', name = "zhujiemian1011", offset = Vector2(-10, -10), },--礼包
        { grid = 1, id = 131,  windowID = WndID.RoleEquip,    icon = 'zhujiemian1010', name = "角色", offset = Vector2(-10, -10), }
    }

    m.mainButtonList = {}
    local mainButtonData = {
        {
            name = 'zhujiemian1047',
            icon1 = 'zhujiemian1034',
            windowID = WndID.PlayerTalent,
        },
        {
            name = 'zhujiemian1020',
            icon1 = 'zhujiemian1033',
            windowID = WndID.UpgradeStar,
        },
        {
            name = 'zhujiemian1004 ',
            icon1 = 'zhujiemian1025',
            windowID = WndID.UIAchieve,
        },
        {
            name = 'zhujiemian1012',
            icon1 = 'zhujiemian1036',
            windowID = WndID.ShengHun,
        },{
            name = 'zhujiemian1006',
            icon1 = 'zhujiemian1037',
            windowID = WndID.TreasureEvolution,
        },{
            name = 'zhujiemian1045',
            icon1 = 'zhujiemian1027',
            windowID = WndID.EquipSynthesis,
        }
    }
    --创建主按钮
    for i, v in ipairs(mainButtonData) do
        m.mainButtonList[i] = m.CreationMainButton(i, v)
    end

    m.activityButtonList = {}
    --创建活动按钮
    for i, v in ipairs(activityButtonData) do
        table.insert(m.activityButtonList, m.CreationActivityButton(v))
    end
    
    m.comResInfo = ComResInfo.New()
    m.comResInfo.BindView(m.objList.Com_Res)

    m.RegisterClickEvent()
    m.Guide()
    return true
end

function m.OnWindowCloseHandle(windowID)
	--if WndID.RoleEquip ~= windowID then return end
	--m.UpdateUIModel()
end

--------------------------------------------------------------------
-- 打开时
--------------------------------------------------------------------
function m:OnOpen(id, index)
    --BattleManager.CheckBattleProgress()
    --播放背景音乐
    SoundManager:PlayMusic(SoundID.Main)
    --获取商店列表
    StoreModule:RequestGetStoreList(2)

    -- 保存Obj_Model的初始位置
    -- if not m.objModelOriginalPos then
    --     m.objModelOriginalPos = Vector2(m.objList.Obj_Model.transform.localPosition.x, m.objList.Obj_Model.transform.localPosition.y)
    -- end

    --重新添加红点
    for _, v in pairs(m.activityButtonList) do
        v.SetRedDot()
    end
    --m:SetWndRedDot(m.objList.Btn_Ectype1,Vector2(-10, -10)):AddCheckParam(WndID.MiJingFuBen)
    --m:SetWndRedDot(m.objList.Btn_Ectype2,Vector2(-10, -10)):AddCheckParam(WndID.LeaderEctype)
    -- m:SetWndRedDot(m.objList.Btn_Ectype3,Vector2(-10, -10)):AddCheckParam(WndID.EliteEctype)
    -- m:SetWndRedDot(m.objList.Btn_Ectype4,Vector2(-10, -10)):AddCheckParam(WndID.PaTaFuBen)
    -- m:SetWndRedDot(m.objList.Btn_Ectype5,Vector2(-10, -10)):AddCheckParam(WndID.GoldCaveEctypt)
    -- m:SetWndRedDot(m.objList.Btn_Ectype6,Vector2(-10, -10)):AddCheckParam(WndID.ForbiddenAreaEctype)
    --m.OnClickMainButton(index or DefaultMainButton)
    ---主线任务
    UIManager:OpenWnd2(WndID.TaskInterface, m.objList.Obj_Task)
    m.UpdateView()
    m.UpdateUIModel()
end


--------------------------------------------------------------------
-- 打开主界面下面几个按钮的界面
--------------------------------------------------------------------
function m.OpenMainButtonUI(index)
    if index == m.selectMainButtonID then
        return
    end
    m.OnClickMainButton(index)
end

--------------------------------------------------------------------
--更新角色模型
--------------------------------------------------------------------
function m.UpdateUIModel()
    -- local weaponsKnapsack = GamePlayerData.ActorEquip:GetWeaponsKnapsack(1) or {}
    -- local equipID = weaponsKnapsack[1] or 0
    -- local cfg = Schemes.Equipment:Get(equipID)
    
    -- local level = GamePlayerData.ActorEquipNew:GetEquipUpgradeLevel(cfg.SmeltID)
    -- m.objList.Txt_Level.text = GetGameText(luaID, 6) .. level .. CommonTextID.LEVEL
    -- m.objList.Txt_EquipName.text = cfg.GoodsName
    --AtlasManager:AsyncGetSprite(HelperL.GetGunIcon(equipID), m.objList.Img_EquipIcon, true)
    -- if cfg == nil then        
        -- local EquipWeaponIDList = {}
        -- for i, v in ipairs(Schemes.EquipWeapon.items) do
        --     if v.GroupID < 1000 then -- 
        --         table.insert(EquipWeaponIDList, v.ID)
        --     end
        -- end 
        
        --GamePlayerData.ActorEquip:ReplaceEquipIndex(EquipWeaponIDList[1], 1, 1)
        --cfg = Schemes.Equipment:Get(EquipWeaponIDList[1])
    -- end
    --暂时屏蔽
    -- local quality = cfg.Quality
    -- local starNum = cfg.StarNum
    -- local level = 0
    -- local entity = SkepModule:GetSkepByID(cfg.PacketID):GetEntityByGoodsID(cfg.ID)
    --装备已激活
    -- if entity then
    --     quality = entity:GetProperty(EQUIPMENT_FIELD.EQUIPMENT_FIELD_QUALITY)
    --     starNum = entity:GetProperty(EQUIPMENT_FIELD.EQUIPMENT_FIELD_STARNUM)
    --     level = quality * 10 + starNum + 1
    -- end
    -- m.objList.Txt_Lv.text = string.format('<color=#fecd57>等级：%s</color>', level)
    
    local cfg = Schemes.Equipment:Get(m.EquipWeaponIDList[1])
    m.objList.Txt_RoleName1.text = cfg.GoodsName
    if GamePlayerData.ActorEquip:IsWear(cfg.ID, 1) then
        m.objList.Txt_RoleState1.text = "已出战"
    else
        m.objList.Txt_RoleState1.text = "休息中"
    end 
    local gun = Schemes.Gun:Get(cfg.ConsignmentStyle)
    local bullet_List = Schemes.Bullet:GetByBulletId(gun.BulletId)
    local trans = m.objList.Obj_Model1.transform
	for c = trans.childCount - 1, 0, -1 do
		if trans:GetChild(c) then
			GameObject.Destroy(trans:GetChild(c).gameObject)
		end
	end
    print("bullet_List[1].Model ====== "..bullet_List[1].Model)
    local parameter
    ResMgr.LoadGameObjectAsync("model/uiSpine/"..bullet_List[1].Model, function (obj, parameter)
        local model = GameObject.Instantiate(obj, m.objList.Obj_Model1.transform)
        
        -- 停止之前可能存在的动画
        if m.floatAnimation1 then
            m.floatAnimation1:Kill()
            m.floatAnimation1 = nil
        end
        
        -- 创建上下浮动的动画
        -- local objModel = m.objList.Obj_Model1
        -- if not tolua.isnull(objModel) then
            -- 确保先回到初始位置
            -- if not m.objModelOriginalPos1 then
            --     m.objModelOriginalPos1 = Vector3(objModel.transform.localPosition.x, objModel.transform.localPosition.y, objModel.transform.localPosition.z)
            -- end
            -- objModel.transform.localPosition = m.objModelOriginalPos1
            
            -- 创建动画序列
            -- m.floatAnimation1 = DOTween.Sequence()
            
            -- -- 往上浮动到+30
            -- m.floatAnimation1:Append(objModel.transform:DOLocalMoveY(m.objModelOriginalPos1.y + 10, 1.5):SetEase(TweeningEase.InOutSine))
            
            -- -- 往下浮动到-30
            -- m.floatAnimation1:Append(objModel.transform:DOLocalMoveY(m.objModelOriginalPos1.y - 10, 1.5):SetEase(TweeningEase.InOutSine))
            
            -- -- 设置为无限循环
            -- m.floatAnimation1:SetLoops(-1, TweeningLoopType.Yoyo)
        -- end
    end, parameter)

    local cfg1 = Schemes.Equipment:Get(m.EquipWeaponIDList[2])
    m.objList.Txt_RoleName2.text = cfg1.GoodsName
    if GamePlayerData.ActorEquip:IsWear(cfg1.ID, 1) then
        m.objList.Txt_RoleState2.text = "已出战"
    else
        m.objList.Txt_RoleState2.text = "休息中"
    end 
    local gun1 = Schemes.Gun:Get(cfg1.ConsignmentStyle)
    local bullet_List1 = Schemes.Bullet:GetByBulletId(gun1.BulletId)
    local trans1 = m.objList.Obj_Model2.transform
	for c = trans1.childCount - 1, 0, -1 do
		if trans1:GetChild(c) then
			GameObject.Destroy(trans1:GetChild(c).gameObject)
		end
	end
    local parameter1
    ResMgr.LoadGameObjectAsync("model/uiSpine/"..bullet_List1[1].Model, function (obj, parameter)
        local model = GameObject.Instantiate(obj, m.objList.Obj_Model2.transform)        
        -- 停止之前可能存在的动画
        if m.floatAnimation2 then
            m.floatAnimation2:Kill()
            m.floatAnimation2 = nil
        end
        
        -- 创建上下浮动的动画
        -- local objModel = m.objList.Obj_Model2
        -- if not tolua.isnull(objModel) then
            -- 确保先回到初始位置
            -- if not m.objModelOriginalPos2 then
            --     m.objModelOriginalPos2 = Vector3(objModel.transform.localPosition.x, objModel.transform.localPosition.y, objModel.transform.localPosition.z)
            -- end
            -- objModel.transform.localPosition = m.objModelOriginalPos2
            
            -- -- 创建动画序列
            -- m.floatAnimation2 = DOTween.Sequence()
            
            -- -- 往上浮动到+30
            -- m.floatAnimation2:Append(objModel.transform:DOLocalMoveY(m.objModelOriginalPos2.y + 30, 1.5):SetEase(TweeningEase.InOutSine))
            
            -- -- 往下浮动到-30
            -- m.floatAnimation2:Append(objModel.transform:DOLocalMoveY(m.objModelOriginalPos2.y - 30, 1.5):SetEase(TweeningEase.InOutSine))
            
            -- -- 设置为无限循环
            -- m.floatAnimation2:SetLoops(-1, TweeningLoopType.Yoyo)
        -- end
    end, parameter1)

    local cfg2 = Schemes.Equipment:Get(m.EquipWeaponIDList[3])
    m.objList.Txt_RoleName3.text = cfg2.GoodsName
    if GamePlayerData.ActorEquip:IsWear(cfg2.ID, 1) then
        m.objList.Txt_RoleState3.text = "已出战"
    else
        m.objList.Txt_RoleState3.text = "休息中"
    end 
    local gun2 = Schemes.Gun:Get(cfg2.ConsignmentStyle)
    local bullet_List2 = Schemes.Bullet:GetByBulletId(gun2.BulletId)
    local trans2 = m.objList.Obj_Model3.transform
	for c = trans2.childCount - 1, 0, -1 do
		if trans2:GetChild(c) then
			GameObject.Destroy(trans2:GetChild(c).gameObject)
		end
	end
    local parameter2
    ResMgr.LoadGameObjectAsync("model/uiSpine/"..bullet_List2[1].Model, function (obj, parameter)
        local model = GameObject.Instantiate(obj, m.objList.Obj_Model3.transform)        
        -- 停止之前可能存在的动画
        if m.floatAnimation3 then
            m.floatAnimation3:Kill()
            m.floatAnimation3 = nil
        end
        
        -- 创建上下浮动的动画
        -- local objModel = m.objList.Obj_Model3
        -- if not tolua.isnull(objModel) then
        --     -- 确保先回到初始位置
        --     if not m.objModelOriginalPos3 then
        --         m.objModelOriginalPos3 = Vector3(objModel.transform.localPosition.x, objModel.transform.localPosition.y, objModel.transform.localPosition.z)
        --     end
        --     objModel.transform.localPosition = m.objModelOriginalPos3
            
        --     -- 创建动画序列
        --     m.floatAnimation3 = DOTween.Sequence()
            
        --     -- 往上浮动到+30
        --     m.floatAnimation3:Append(objModel.transform:DOLocalMoveY(m.objModelOriginalPos3.y + 30, 1.5):SetEase(TweeningEase.InOutSine))
            
        --     -- 往下浮动到-30
        --     m.floatAnimation3:Append(objModel.transform:DOLocalMoveY(m.objModelOriginalPos3.y - 30, 1.5):SetEase(TweeningEase.InOutSine))
            
        --     -- 设置为无限循环
        --     m.floatAnimation3:SetLoops(-1, TweeningLoopType.Yoyo)
        -- end
    end, parameter2)

    local cfg3 = Schemes.Equipment:Get(m.EquipWeaponIDList[3])
    m.objList.Txt_RoleName4.text = cfg3.GoodsName    
    if GamePlayerData.ActorEquip:IsWear(cfg3.ID, 1) then
        m.objList.Txt_RoleState4.text = "已出战"
    else
        m.objList.Txt_RoleState4.text = "休息中"
    end 
    local gun3 = Schemes.Gun:Get(cfg3.ConsignmentStyle)
    local bullet_List3 = Schemes.Bullet:GetByBulletId(gun3.BulletId)
    local trans3 = m.objList.Obj_Model4.transform
	for c = trans3.childCount - 1, 0, -1 do
		if trans3:GetChild(c) then
			GameObject.Destroy(trans3:GetChild(c).gameObject)
		end
	end
    local parameter3
    ResMgr.LoadGameObjectAsync("model/uiSpine/"..bullet_List3[1].Model, function (obj, parameter)
        local model = GameObject.Instantiate(obj, m.objList.Obj_Model4.transform)        
        -- 停止之前可能存在的动画
        if m.floatAnimation4 then
            m.floatAnimation4:Kill()
            m.floatAnimation4 = nil
        end
        
        -- 创建上下浮动的动画
        -- local objModel = m.objList.Obj_Model4
        -- if not tolua.isnull(objModel) then
        --     -- 确保先回到初始位置
        --     if not m.objModelOriginalPos4 then
        --         m.objModelOriginalPos4 = Vector3(objModel.transform.localPosition.x, objModel.transform.localPosition.y, objModel.transform.localPosition.z)
        --     end
        --     objModel.transform.localPosition = m.objModelOriginalPos4
            
        --     -- 创建动画序列
        --     m.floatAnimation4 = DOTween.Sequence()
            
        --     -- 往上浮动到+30
        --     m.floatAnimation4:Append(objModel.transform:DOLocalMoveY(m.objModelOriginalPos4.y + 30, 1.5):SetEase(TweeningEase.InOutSine))
            
        --     -- 往下浮动到-30
        --     m.floatAnimation4:Append(objModel.transform:DOLocalMoveY(m.objModelOriginalPos4.y - 30, 1.5):SetEase(TweeningEase.InOutSine))
            
        --     -- 设置为无限循环
        --     m.floatAnimation4:SetLoops(-1, TweeningLoopType.Yoyo)
        -- end
    end, parameter3)
end

--------------------------------------------------------------------
-- 注册点击事件
--------------------------------------------------------------------
function m.RegisterClickEvent()  
    m:AddClick(m.objList.Btn_Test, function()
        UIManager:OpenWnd(WndID.TestMode)
    end)
    m:AddClick(m.objList.Btn_Ectype1, function()
        
        UIManager:OpenWnd(WndID.MainEctype)
    end)
    -- m:AddClick(m.objList.Btn_Ectype2, function()
    --     UIManager:OpenWnd(WndID.OpenTreasureBox)
    -- end)

    m:AddClick(m.objList.Btn_Shop, function()
        
        UIManager:OpenWnd(WndID.ShopNew)
    end)

    m:AddClick(m.objList.Btn_Recharge, function()
        
        UIManager:OpenWnd(WndID.Recharge)
    end)

    m:AddClick(m.objList.Btn_Discount, function()
        
        UIManager:OpenWnd(WndID.DiscountGift)
    end)

    m:AddClick(m.objList.Btn_StateBG1, function()
        for i = 1, #m.EquipWeaponIDList, 1 do
            if GamePlayerData.ActorEquip:IsWear(m.EquipWeaponIDList[i], 1) then
                --卸下装备
                GamePlayerData.ActorEquip:UnloadEquip(m.EquipWeaponIDList[i], 1)
                break
            end
        end
        m.smallTimer= Timer.New(function()
            print("m.EquipWeaponIDList[1] ====== ",m.EquipWeaponIDList[1])		
            GamePlayerData.ActorEquip:WearEquip(m.EquipWeaponIDList[1], 1)
        end, 0.5, 1)
        m.smallTimer:Start()
    end)

    m:AddClick(m.objList.Btn_StateBG2, function()
        for i = 1, #m.EquipWeaponIDList, 1 do
            if GamePlayerData.ActorEquip:IsWear(m.EquipWeaponIDList[i], 1) then
                --卸下装备
                GamePlayerData.ActorEquip:UnloadEquip(m.EquipWeaponIDList[i], 1)
                break
            end
        end
        m.smallTimer= Timer.New(function()		
            GamePlayerData.ActorEquip:WearEquip(m.EquipWeaponIDList[2], 1)
        end, 0.5, 1)
        m.smallTimer:Start()
    end)

    m:AddClick(m.objList.Btn_StateBG3, function()
        for i = 1, #m.EquipWeaponIDList, 1 do
            if GamePlayerData.ActorEquip:IsWear(m.EquipWeaponIDList[i], 1) then
                --卸下装备
                GamePlayerData.ActorEquip:UnloadEquip(m.EquipWeaponIDList[i], 1)
                break
            end
        end
        m.smallTimer= Timer.New(function()		
            GamePlayerData.ActorEquip:WearEquip(m.EquipWeaponIDList[3], 1)
        end, 0.5, 1)
        m.smallTimer:Start()
    end)

    m:AddClick(m.objList.Btn_StateBG4, function()
        for i = 1, #m.EquipWeaponIDList, 1 do
            if GamePlayerData.ActorEquip:IsWear(m.EquipWeaponIDList[i], 1) then
                --卸下装备
                GamePlayerData.ActorEquip:UnloadEquip(m.EquipWeaponIDList[i], 1)
                break
            end
        end
        m.smallTimer= Timer.New(function()		
            GamePlayerData.ActorEquip:WearEquip(m.EquipWeaponIDList[3], 1)
        end, 0.5, 1)
        m.smallTimer:Start()
    end)

    
    

    m:AddClick(m.objList.Btn_Start, function()        
        --m.ChuZheng(m.selectCatMainStageID)
        UIManager:OpenWnd(WndID.LeaderEctype)
        
    end)

    m:AddClick(m.objList.Btn_StartGray, function()        
        --HelperL.ShowMessage(TipType.FlowText, "食物不足，不能进入游戏!")
        UIManager:OpenWnd(WndID.LeaderEctype)
    end)

    m:AddClick(m.objList.Btn_PlayerHud1, function()
        UIManager:OpenWnd(WndID.RoleInfo)
        
    end)
    
    m:AddClick(m.objList.Btn_GB, function()
        m.objList.Img_Recharge.gameObject:SetActive(false)   
    end)
end

--------------------------------------------------------------------
--更新资产
--------------------------------------------------------------------
function m.UpdateProperty()
    if not EntityModule.hero then return end
    --头像
    local levelText = EntityModule.hero:GetProperty(CREATURE_FIELD.CREATURE_FIELD_LEVEL)
    local exp = EntityModule.hero:GetProperty(PLAYER_FIELD.PLAYER_FIELD_CUREXP)
    local maxExp = EntityModule.hero:GetProperty(PLAYER_FIELD.PLAYER_FIELD_MAXEXP)
    m.objList.Img_Fill.fillAmount = exp / maxExp
    m.objList.Txt_Ex.text = HelperL.GetChangeNum(exp) .. '/' .. HelperL.GetChangeNum(maxExp)
    m.objList.Txt_RoleName.text = EntityModule.hero.name
    local weaponsKnapsack = GamePlayerData.ActorEquip:GetWeaponsKnapsack(1) or {}
    local curEquipID = weaponsKnapsack[1] or 0
    --AtlasManager:AsyncGetSprite(HelperL.GetGunIcon(curEquipID), m.objList.Img_Head, true)

    local cfg = Schemes.Equipment:Get(m.EquipWeaponIDList[1])
    print("cfg.ID ======1======= ",cfg.ID)
    if GamePlayerData.ActorEquip:IsWear(cfg.ID, 1) then
        m.objList.Txt_RoleState1.text = "已出战"
    else
        m.objList.Txt_RoleState1.text = "休息中"
    end 

    cfg = Schemes.Equipment:Get(m.EquipWeaponIDList[2])
    print("cfg.ID ======2======= ",cfg.ID)
    if GamePlayerData.ActorEquip:IsWear(cfg.ID, 1) then
        m.objList.Txt_RoleState2.text = "已出战"
    else
        m.objList.Txt_RoleState2.text = "休息中"
    end 

    cfg = Schemes.Equipment:Get(m.EquipWeaponIDList[3])
    print("cfg.ID ======3======= ",cfg.ID)
    if GamePlayerData.ActorEquip:IsWear(cfg.ID, 1) then
        m.objList.Txt_RoleState3.text = "已出战"
    else
        m.objList.Txt_RoleState3.text = "休息中"
    end 

    if GamePlayerData.ActorEquip:IsWear(cfg.ID, 1) then
        m.objList.Txt_RoleState4.text = "已出战"
    else
        m.objList.Txt_RoleState4.text = "休息中"
    end 

end

--------------------------------------------------------------------
-- 更新按钮
--------------------------------------------------------------------
function m.UpdateView()
    if not EntityModule.hero then return end
    --更新主界面按钮
    for k, v in pairs(m.mainButtonList) do
        v.UpdateButton()
    end
    
    m.UpdateProperty();
    m.UpdateEctypeView();
end

--------------------------------------------------------------------
---更新副本界面
--------------------------------------------------------------------
function m.UpdateEctypeView()
    local stageID = GamePlayerData.GameEctype:GetChallengeableID(EctypeType)
    if m.lastCatMainStageID ~= stageID then
        m.selectCatMainStageID = stageID
        m.lastCatMainStageID = stageID
    end

    stageID = m.selectCatMainStageID
    print("stageID ====== "..stageID)
    local catMainStage = Schemes.CatMainStage:Get(stageID)
    if catMainStage == nil then return end
    local color = UI_COLOR.White
    local commonText = Schemes.CommonText:Get(121)
    local num2 = HeroDataManager:GetLogicByte(commonText.Param4, commonText.Param5)
    local value2 = EntityModule.hero:GetProperty(PLAYER_FIELD.PLAYER_FIELD_CURENERGY)
    -- local level = EntityModule.hero:GetProperty(CREATURE_FIELD.CREATURE_FIELD_LEVEL)

    local expendList = HelperL.Split(catMainStage.Need, ';')
    --消耗物品ID
    local expID = tonumber(expendList[1]) or 0
    --消耗物品数量
    local expNum = tonumber(expendList[2]) or 0
    --广告物品ID
    local adID = tonumber(expendList[3])
    --消耗物品ID2
    local expID2 = tonumber(expendList[4]) or 0
    --消耗物品数量2
    local expNum2 = tonumber(expendList[5]) or 0
    local count1 = SkepModule:GetGoodsCount(expID)
    local count2 = SkepModule:GetGoodsCount(expID2)

    local ex = tonumber(HelperL.Split(catMainStage.Need, ";")[2])
    --副本图片
    -- local iconList = HelperL.Split(catMainStage.Icon, ';')
    -- AtlasManager:AsyncGetSprite(iconList[2], m.objList.Img_Chapter)



    m.objList.Obj_Start.gameObject:SetActive(false)
    -- m.objList.Img_Lock.gameObject:SetActive(false)
    m.objList.Btn_Start.gameObject:SetActive(false)
    m.objList.Btn_StartGray.gameObject:SetActive(false)
    -- HelperL.SetImageGray(m.objList.Img_Chapter, false)
    --m.objList.Txt_StartHint.gameObject:SetActive(false)

    local maxStageID = GamePlayerData.GameEctype:GetChallengeableID(EctypeType)
    if stageID <= maxStageID then
        m.objList.Obj_Start.gameObject:SetActive(true)
        if count1 >= expNum and count2 >= expNum2 then
            m.objList.Btn_Start.gameObject:SetActive(true)
        else
            m.objList.Btn_StartGray.gameObject:SetActive(true)
        end
        m.objList.Txt_StartExpend.text = "X" .. ex
        m.objList.Txt_StartExpendGray.text = string.format('<color=#FFA2FF>X%s</color>', ex)
    else
        -- m.objList.Img_Lock.gameObject:SetActive(true)
        -- HelperL.SetImageGray(m.objList.Img_Chapter, true)
        m.objList.Txt_StartHint.text = string.format(GetGameText(luaID, 2), stageID - 1)
        m.objList.Txt_StartHint.gameObject:SetActive(true)
    end
end

--------------------------------------------------------------------
--每秒更新
--------------------------------------------------------------------
function m.OnSecondUpdate()
    if m.propertyButtonList and #m.propertyButtonList > 0 then
        for _, v in pairs(m.propertyButtonList) do
            v.SecondUpdate()
        end
    end
end

--------------------------------------------------------------------
-- 添加红点
--------------------------------------------------------------------
function m.AddRedDot(parent, offset)
    if not m.redDotItem then
        m.redDotItem = HotResManager.ReadUI('ui/Common/RedDot')
    end
    local dotObj = GameObject.Instantiate(m.redDotItem.gameObject, parent.transform)
    dotObj:GetRectTransform().anchoredPosition = offset or CachedVector2:Set(-40, -27)
    return dotObj
end

--------------------------------------------------------------------
---创建主按钮
---@param data MainButtonData 数据源
---@return MainButton
--------------------------------------------------------------------
function m.CreationMainButton(index, data)
    ---@class MainButton 主按钮
    local item = {}
    item.index = index
    --按钮数据
    item.data = data
    item.objList = m:CreateSubItem(m.objList.Grid_Main, m.objList.Item_Bottom)

    --item.objList.Txt_Name1.text = data.name
    AtlasManager:AsyncGetSprite(item.data.icon1, item.objList.Img_Icon,true)
    AtlasManager:AsyncGetSprite(item.data.name, item.objList.Img_Name,true)
    AtlasManager:AsyncGetSprite(item.data.icon1, item.objList.Img_Icon1,true)
    AtlasManager:AsyncGetSprite(item.data.name, item.objList.Img_Name1,true)

    --红点
    item.redDot = m.AddRedDot(item.objList.gameObject)
    item.redDot.gameObject:SetActive(false)
    m:AddClick(item.objList.Btn_Click, function()                    
        if UIManager:JudgeOpenLevel(item.data.windowID, false) then
            --m.OnClickMainButton(item.index)
            UIManager:OpenWnd(item.data.windowID)
        else
            local openLevel = UIManager:GetOpenLevel(item.data.windowID)
            HelperL.ShowMessage(TipType.FlowText, string.format(GetGameText(luaID, 80), openLevel))
        end        
    end)

    --更新按钮
    item.UpdateButton = function()
        --判断开放等级
        if UIManager:JudgeOpenLevel(item.data.windowID, false) then
            item.objList.Normal.gameObject:SetActive(true)
            item.objList.Gray.gameObject:SetActive(false)
            item.redDot.gameObject:SetActive(RedDotManager:GetRedDotCheck(item.data.windowID))
        else
            item.objList.Normal.gameObject:SetActive(false)
            item.objList.Gray.gameObject:SetActive(true)
            item.redDot.gameObject:SetActive(false)
        end
    end
    return item
end


--------------------------------------------------------------------
---创建活动按钮
---@param data ActivityButtonData 按钮数据
---@return ActivityButton
--------------------------------------------------------------------
function m.CreationActivityButton(data)
    local parent = m.activityGridList[data.grid]
    ---@class ActivityButton 活动按钮
    local item = {}
    --按钮数据
    item.data = data
    item.objList = m:CreateSubItem(parent, m.objList.Item_Button)
    --item.objList.Txt_Name1.text = data.name
    AtlasManager:AsyncGetSprite(data.icon, item.objList.Img_Icon1,true)
    AtlasManager:AsyncGetSprite(data.name, item.objList.Img_Name1,true)
    m:AddClick(item.objList.Btn_Click, function()
        if item.data.windowID == 10000 then
            m.objList.Img_Recharge.gameObject:SetActive(true)
        else  
            m.objList.Img_Recharge.gameObject:SetActive(false)          
            UIManager:OpenWnd(item.data.windowID)
            -- if item.data.windowID == 73 then
            --     --关掉手指引导
            --     EventManager:Fire(EventID.TaskShowHandGuide, 3)
            -- end
        end
        
    end)
    --设置红点
    item.SetRedDot = function()
        m:SetWndRedDot(item.objList.gameObject, data.offset)
            :AddCheckParam(item.data.windowID, nil)
    end
    return item
end

--------------------------------------------------------------------
-- 主按钮点击事件
--------------------------------------------------------------------
function m.OnClickMainButton(index)
    local item
    if m.selectMainButtonID then
        item = m.mainButtonList[m.selectMainButtonID]
        --item.objList.Txt_Name.gameObject:SetActive(false)
        item.objList.Img_Select.gameObject:SetActive(false)
        --item.rect.sizeDelta = CachedVector2(240, 160)
        --AtlasManager:AsyncGetSprite(item.data.icon, item.com.Img_Icon, true)
        UIManager:CloseWndByID(item.data.windowID)
    end

    if m.selectMainButtonID == index then
        index = DefaultMainButton
    end

    m.selectMainButtonID = index
    item = m.mainButtonList[index]
    --item.objList.Txt_Name.gameObject:SetActive(true)
    item.objList.Img_Select.gameObject:SetActive(true)
    --item.rect.sizeDelta = CachedVector2(280, 180)
    --AtlasManager:AsyncGetSprite(item.data.icon .. "_big", item.com.Img_Icon, true)

    --嵌套到主界面里
    if item.data.windowID then
        UIManager:OpenWnd2(item.data.windowID, m.objList.Depth_1, false)
    end
end

--------------------------------------------------------------------
-- 更新资产
--------------------------------------------------------------------
function m.HeroPropertyUpdate_Silver(newValue, diff, PropID)
    if diff == 0 then return end
    if PropID == PLAYER_FIELD.PLAYER_FIELD_DIAMOND then
        AddSilverExpMessageQueue(GetGameText(luaID, 81), diff)
    end
end


--------------------------------------------------------------------
--新手引导,登录显示
--------------------------------------------------------------------
function m.Guide()
    if HelperL.BeginLogin then
        HelperL.BeginLogin = false
        local Age = EntityModule.VerifyInfo.Age
        if Age < 18 then
            local can, day = HelperL.IsCanGame()
            if can == true then
                local data = {
                    type = NotarizeWindowsType.Windows6,
                    content = GetGameText("HelperL", 49),
                }
                HelperL.NotarizeFCMUI(data)
                HelperL.CreateSmallRoleTime()
            else
                if day ~= 5 and day ~= 6 and day ~= 7 then
                    local data = {
                        type = NotarizeWindowsType.Windows3,
                        content = GetGameText("HelperL", 54),
                        isFCM = true,
                    }
                    HelperL.NotarizeFCMUI(data)
                else
                    local data = {
                        type = NotarizeWindowsType.Windows3,
                        content = GetGameText("HelperL", 49) .. GetGameText("HelperL", 55),
                        isFCM = true,
                    }
                    HelperL.NotarizeFCMUI(data)
                end                
            end
        else
            if  PlayerPrefsManager:GetInt("StoryIntroduce", 0) == 0 then
                PlayerPrefsManager:SetInt("StoryIntroduce", 1)
                UIManager:OpenWnd(WndID.StoryIntroduce)
            end            
        end
        local heroLevel = EntityModule.hero:GetProperty(CREATURE_FIELD.CREATURE_FIELD_LEVEL)
        
        -- 角色等级=1时，执行一次【#逻辑值 200 300020】
        if heroLevel == 1 then
            local chatContent = "#逻辑值 200 300020"
            local msg = ChatMessage_pb.CS_Char_SendChat()
            msg.Channel = 12
            msg.Content = chatContent
            msg.ChatType = 0
            Premier.Instance:GetNetwork():SendFromLua(
                ENDPOINT.ENDPOINT_GAMECLIENT,
                ENDPOINT.ENDPOINT_GAMESERVER,
                MSG_MODULEID.MSG_MODULEID_CHAT,
                ChatMessage_pb.MSG_CHAT_SENDCHAT,
                msg:SerializeToString()
            )
            print("11111 角色等级1-强行修改默认出战装备ID=300020，命令:", chatContent)  -- 这是强行修改默认出战装备ID=300020
        end
        
        -- 角色等级大于等于1时，延迟1秒后根据逻辑值200设置出战装备
        if heroLevel >= 1 then
            Timer.New(function()
                local logicValue200 = HeroDataManager:GetLogicData(LOGIC_DATA.DATA_TRAIN_TOTALNUM)
                print("11111 登录延迟1秒-读取逻辑值200:", logicValue200)

                -- ================================
                -- 登录装备状态规范化逻辑
                -- ================================

                -- 获取多层关联装备
                local function GetAllAssociatedEquipIDsForLogin(equipId)
                    local associatedEquipIDs = {}
                    local currentEquipId = equipId
                    local visitedEquipIDs = {}

                    while true do
                        if visitedEquipIDs[currentEquipId] then
                            print("33333333 登录装备状态规范化------检测到循环引用，停止查找关联装备")
                            break
                        end
                        visitedEquipIDs[currentEquipId] = true

                        local equipWeaponConfig = Schemes.EquipWeapon:Get(currentEquipId)
                        if not equipWeaponConfig then
                            break
                        end

                        if equipWeaponConfig.EffectID3 and equipWeaponConfig.EffectID3 ~= 0 then
                            local associatedId = equipWeaponConfig.EffectID3
                            table.insert(associatedEquipIDs, associatedId)
                            print("33333333 登录装备状态规范化------找到关联装备:", associatedId)
                            currentEquipId = associatedId
                        else
                            break
                        end
                    end

                    return associatedEquipIDs
                end

                print("33333333 登录装备状态规范化------开始执行")

                local logicValue200 = HeroDataManager:GetLogicData(LOGIC_DATA.DATA_TRAIN_TOTALNUM)
                print("33333333 登录装备状态规范化------读取逻辑值200:", logicValue200)

                -- 获取当前所有出战装备
                local currentEquipIDs = GamePlayerData.ActorEquip:GetWeaponsKnapsack(1) or {}
                print("33333333 登录装备状态规范化------当前出战装备列表:", json.encode(currentEquipIDs))

                -- 统计当前主装备(GroupID<100)数量
                local mainEquipList = {}
                local otherEquipList = {}

                for _, equipID in ipairs(currentEquipIDs) do
                    if equipID and equipID ~= 0 then
                        local equipWeaponConfig = Schemes.EquipWeapon:Get(equipID)
                        if equipWeaponConfig then
                            if equipWeaponConfig.GroupID < 100 then
                                table.insert(mainEquipList, equipID)
                            else
                                table.insert(otherEquipList, equipID)
                            end
                        end
                    end
                end

                print("33333333 登录装备状态规范化------当前主装备数量:", #mainEquipList, "列表:", json.encode(mainEquipList))
                print("33333333 登录装备状态规范化------当前其他装备数量:", #otherEquipList, "列表:", json.encode(otherEquipList))

                -- 确定目标主装备ID
                local targetMainEquipID = 300020 -- 默认装备ID

                if logicValue200 and logicValue200 > 0 then
                    local equipConfig = Schemes.Equipment:Get(logicValue200)
                    local equipWeaponConfig = Schemes.EquipWeapon:Get(logicValue200)

                    if equipConfig and equipWeaponConfig and equipWeaponConfig.GroupID < 100 then
                        targetMainEquipID = logicValue200
                        print("33333333 登录装备状态规范化------使用逻辑值200作为目标主装备:", targetMainEquipID)
                    else
                        print("33333333 登录装备状态规范化------逻辑值200无效或不是主装备，使用默认装备")
                    end
                else
                    print("33333333 登录装备状态规范化------逻辑值200无效，使用默认装备")
                end

                local targetAssociatedEquipIDs = GetAllAssociatedEquipIDsForLogin(targetMainEquipID)
                print("33333333 登录装备状态规范化------目标关联装备列表:", json.encode(targetAssociatedEquipIDs))

                -- 构建正确的出战装备列表
                local correctEquipList = {targetMainEquipID}
                for _, associatedId in ipairs(targetAssociatedEquipIDs) do
                    table.insert(correctEquipList, associatedId)
                end

                print("33333333 登录装备状态规范化------正确的出战装备列表:", json.encode(correctEquipList))

                -- 检查当前状态是否已经正确
                local needUpdate = false

                -- 1. 检查主装备数量是否为1且是目标装备
                if #mainEquipList ~= 1 or mainEquipList[1] ~= targetMainEquipID then
                    needUpdate = true
                    print("33333333 登录装备状态规范化------主装备状态不正确，需要更新")
                end

                -- 2. 检查关联装备是否正确
                if not needUpdate then
                    local currentAssociatedInWear = {}
                    for _, equipID in ipairs(otherEquipList) do
                        local equipWeaponConfig = Schemes.EquipWeapon:Get(equipID)
                        if equipWeaponConfig and equipWeaponConfig.GroupID >= 100 and equipWeaponConfig.GroupID < 1000 then
                            table.insert(currentAssociatedInWear, equipID)
                        end
                    end

                    -- 比较关联装备列表
                    if #currentAssociatedInWear ~= #targetAssociatedEquipIDs then
                        needUpdate = true
                        print("33333333 登录装备状态规范化------关联装备数量不匹配，需要更新")
                    else
                        for _, targetId in ipairs(targetAssociatedEquipIDs) do
                            local found = false
                            for _, currentId in ipairs(currentAssociatedInWear) do
                                if currentId == targetId then
                                    found = true
                                    break
                                end
                            end
                            if not found then
                                needUpdate = true
                                print("33333333 登录装备状态规范化------关联装备不匹配，需要更新")
                                break
                            end
                        end
                    end
                end

                if needUpdate then
                    print("33333333 登录装备状态规范化------开始更新装备状态")

                    -- 使用LogicValue.SetWeaponsKnapsack一次性设置正确的装备列表
                    LogicValue.SetWeaponsKnapsack(correctEquipList)

                    print("33333333 登录装备状态规范化------装备状态更新完成")

                    -- 验证更新结果
                    Timer.New(function()
                        local verifyEquipIDs = GamePlayerData.ActorEquip:GetWeaponsKnapsack(1) or {}
                        print("33333333 登录装备状态规范化------更新后验证装备列表:", json.encode(verifyEquipIDs))

                        -- 统计验证结果
                        local verifyMainCount = 0
                        local verifyAssociatedCount = 0
                        for _, equipID in ipairs(verifyEquipIDs) do
                            if equipID and equipID ~= 0 then
                                local equipWeaponConfig = Schemes.EquipWeapon:Get(equipID)
                                if equipWeaponConfig then
                                    if equipWeaponConfig.GroupID < 100 then
                                        verifyMainCount = verifyMainCount + 1
                                    elseif equipWeaponConfig.GroupID >= 100 and equipWeaponConfig.GroupID < 1000 then
                                        verifyAssociatedCount = verifyAssociatedCount + 1
                                    end
                                end
                            end
                        end

                        print("33333333 登录装备状态规范化------验证结果: 主装备数量=" .. verifyMainCount .. " 关联装备数量=" .. verifyAssociatedCount)

                        if verifyMainCount == 1 and verifyAssociatedCount == #targetAssociatedEquipIDs then
                            print("33333333 登录装备状态规范化------装备状态规范化成功！")
                        else
                            print("33333333 登录装备状态规范化------装备状态规范化可能存在问题，请检查")
                        end
                    end, 0.5, 1):Start()
                else
                    print("33333333 登录装备状态规范化------当前装备状态已经正确，无需更新")
                end
            end, 1, 1):Start()
        end
        
        -- 玩家登录游戏后，立即打印一次#逻辑值 200的值，后面每5秒打印1次，执行1分钟
        local logicValue200 = HeroDataManager:GetLogicData(LOGIC_DATA.DATA_TRAIN_TOTALNUM)
        print("11111 登录后-逻辑值200当前值:", logicValue200)
        
        -- 定时器：每5秒打印1次#逻辑值 200的值，执行1分钟（12次）
        local printCount = 0
        local maxPrintCount = 12  -- 1分钟 / 5秒 = 12次
        m.logicValueTimer = Timer.New(function()
            printCount = printCount + 1
            local currentValue = HeroDataManager:GetLogicData(LOGIC_DATA.DATA_TRAIN_TOTALNUM)
            print("11111 定时打印-逻辑值200值:", currentValue, "第", printCount, "次")
            
            if printCount >= maxPrintCount then
                if m.logicValueTimer then
                    m.logicValueTimer:Stop()
                    m.logicValueTimer = nil
                end
            end
        end, 5, maxPrintCount)
        m.logicValueTimer:Start()
        
        if heroLevel < 2 and GuideManager:GetCurGuideType() == 0 then --开始
            UIManager:OpenWnd(WndID.TaskDialogue, 1, function()
                --m.StartGuide(GuideManager.EventType.BeginGameGuide)
                GuideManager:OnGuideStart(1)
            end, true)
            return
        end
    end
end

--------------------------------------------------------------------
-- 窗口关闭时清理资源
--------------------------------------------------------------------
function m:OnClose()
    -- 停止模型浮动动画，防止内存泄漏
    if m.floatAnimation then
        m.floatAnimation:Kill()
        m.floatAnimation = nil
    end
    
    -- 停止逻辑值定时器，防止内存泄漏
    if m.logicValueTimer then
        m.logicValueTimer:Stop()
        m.logicValueTimer = nil
    end

    -- 停止所有模型浮动动画
    if m.floatAnimation1 then
        m.floatAnimation1:Kill()
        m.floatAnimation1 = nil
    end
    if m.floatAnimation2 then
        m.floatAnimation2:Kill()
        m.floatAnimation2 = nil
    end
    if m.floatAnimation3 then
        m.floatAnimation3:Kill()
        m.floatAnimation3 = nil
    end
    if m.floatAnimation4 then
        m.floatAnimation4:Kill()
        m.floatAnimation4 = nil
    end
end





return m
