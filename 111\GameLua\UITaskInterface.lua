--[[
********************************************************************
    created:	2024/04/09
    author :	李锦剑
    purpose:    任务界面
*********************************************************************
--]]

---@class UITaskInterface:UIWndBase
local m = {}

--------------------------------------------------------------------
-- 订阅事件列表
--------------------------------------------------------------------
function m.GetOpenEventList()
    return {
        [EventID.EntityModuleDataUpdate] = m.UpdateView,
        [EventID.UpdateGameEctypeData] = m.UpdateView,
        [EventID.UpdateActorTaskData] = m.UpdateView,
        [EventID.LogicDataChange] = m.UpdateView,
        [EventID.EntitySyncBuff] = m.UpdateView,
        [EventID.OnGoodsPropChange] = m.UpdateView,
        [EventID.OnHeroPropChange] = m.UpdateView,
        [EventID.ActorDataChanged] = m.UpdateView,
    }
end

--------------------------------------------------------------------
-- 初始化
--------------------------------------------------------------------
function m.OnCreate()
    m.objList.Img_Bg.gameObject:SetActive(false)
    m.RegisterClickEvent()
    return true
end

--------------------------------------------------------------------
-- 打开时
--------------------------------------------------------------------
function m.OnOpen()
    m.UpdateView()
    m:SetWndRedDot(m.objList.Img_Bg, Vector3(-20, -20, 0)):AddCheckParam(WndID.TaskInterface)
    m.TaskClick(false)
end

--------------------------------------------------------------------
--注册点击事件
--------------------------------------------------------------------
function m.RegisterClickEvent()
    m.objList.Btn_Get.onClick:AddListenerEx(m.TaskClick)
end

--------------------------------------------------------------------
-- 更新界面
--------------------------------------------------------------------
function m.UpdateView()
    local taskID = GamePlayerData.ActorTask:GetMainTaskID()
    print("taskID =============================",taskID)

    ---完成所有任务
    m.objList.Img_Bg.gameObject:SetActive(taskID ~= -1)

    local taskCfg = Schemes.Task:Get(taskID)
    if taskCfg then
        local jd = GamePlayerData.ActorTask:GetTaskProgress(taskID)
        local color = UI_COLOR.Red
        if jd >= taskCfg.Parameter2 then
            color = UI_COLOR.White
        end
        m.objList.Txt_Desc.text = string.format("剧情\n%s<color=%s>(%s/%s)</color>", taskCfg.Describe, color, jd,
            taskCfg.Parameter2)
    end
end

--------------------------------------------------------------------
-- 获取任务奖励
---@param taskID integer
--------------------------------------------------------------------
function m.GetTaskAward(taskID)
    local taskCfg = Schemes.Task:Get(taskID)
    local jd = GamePlayerData.ActorTask:GetTaskProgress(taskID)
    if jd < taskCfg.Parameter2 then
        HelperL.ShowMessage(TipType.FlowText, '任务未完成！')
        return
    end

    local form = {}
    form["taskID"] = taskID
    LuaModuleNew.SendRequest(LuaRequestID.GetTaskAward, form, function(resultCode, content)
        if resultCode == RESULT_CODE_BASE.RESULT_COMMON_SUCCEED[1] then
            m.UpdateView()
        else
            HelperL.ShowMessage(TipType.FlowText, ResultCode:GetResultCodeStr(resultCode, content))
        end
    end)
end

--------------------------------------------------------------------
-- 任务点击事件
--------------------------------------------------------------------
function m.TaskClick(isHint)
    local taskID = GamePlayerData.ActorTask:GetMainTaskID()
    if taskID == -1 then
        warn("任务已全部完成")
        return
    end

    local taskCfg = Schemes.Task:Get(taskID)
    local jd = GamePlayerData.ActorTask:GetTaskProgress(taskID)
    if jd < taskCfg.Parameter2 then
        if isHint ~= false then
            HelperL.ShowMessage(TipType.FlowText, '任务未完成！')
        end
        return
    end

    local task = Schemes.Task:Get(taskID)
    UIManager:OpenWnd(WndID.TaskDialogue, taskID, function()
        m.GetTaskAward(taskID)
        EventManager:Fire(EventID.TaskFinish)
        if task.Fly > 0 then --完成任务触发引导
            GuideManager:OnGuideStart(task.Fly)
        end
    end)
end

--------------------------------------------------------------------
-- 窗口页面关闭
--------------------------------------------------------------------
function m.OnClickClose()
    m:CloseSelf()
end

return m
