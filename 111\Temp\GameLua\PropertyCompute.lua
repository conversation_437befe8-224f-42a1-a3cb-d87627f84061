--[[
********************************************************************
    created:	2024/05/25
    author :	李锦剑
    purpose:    用于存放属性计算函数，或表格处理函数(HelperL.lua 存放函数过多)
*********************************************************************
--]]

---属性计算
---@class PropertyCompute
local m = {}
PropertyCompute = m


---计算装备升星等级
---@param entity integer
---@return unknown
function m.CalculateLevel(entity)
    if entity then
        local quality = entity:GetProperty(EQUIPMENT_FIELD.EQUIPMENT_FIELD_QUALITY)
        local starNum = entity:GetProperty(EQUIPMENT_FIELD.EQUIPMENT_FIELD_STARNUM)
        local ex = entity:GetProperty(EQUIPMENT_FIELD.EQUIPMENT_FIELD_CUSTOM)
        return (starNum * 100 + quality * 1000 + ex)
    end
    return 0
end

---获取装备宝箱经验
function m.GetBoxEquipExp()
    local equipID = BOX_EQUIP_ID[1]
    return GamePlayerData.ActorEquip:GetEquipUpgradeEXP(equipID)
end

---获取装备宝箱等级
function m.GetBoxEquipLevel(equipID)
    local level = 1
    -- local boxEquipData = m.GetBoxEquipData(equipID)
    -- if boxEquipData then
    --     level = boxEquipData.level
    -- end
    return level
end

---获取装备宝箱属性
---@param equipID integer 装备ID
---@return BoxEquipData|nil
function m.GetBoxEquipData(equipID)
    local boxData = TREASURE_OPENING_BOX_DATA[equipID]
    if not boxData then
        return nil
    end
    --宝箱升星经验
    local exp = m.GetBoxEquipExp()
    local equipment = Schemes.Equipment:Get(equipID)
    local equipSmelt = Schemes.EquipSmelt:Get(equipment.SmeltID, equipment.Quality, equipment.StarNum)
    local starLvlProps = HelperL.Split(equipSmelt.StarLvlProps, '|')
    local treasureLvls = HelperL.Split(equipSmelt.TreasureLvls, '|')

    ---@class BoxEquipData
    local boxEquipData = {}
    --宝箱ID
    boxEquipData.id = boxData.ID
    --宝箱名称
    boxEquipData.name = equipment.GoodsName
    --宝箱图标
    boxEquipData.icon = equipment.IconID
    --宝箱装备ID
    boxEquipData.equipID = boxData.EquipID
    --宝箱看广告ID
    boxEquipData.adID = boxData.AdID
    --宝箱等级
    boxEquipData.level = 0
    --宝箱最大等级
    boxEquipData.maxLevel = #starLvlProps
    --宝箱等级配置
    ---@type BoxEquipLevelData[]
    boxEquipData.boxEquipSmeltList = {}
    --是已满级
    boxEquipData.isFullLevel = false
    --宝箱品质
    boxEquipData.quality = 1
    --当前等级所需经验值
    boxEquipData.levelExpValue = 0
    --下一级等级所需经验值
    boxEquipData.nextLevelExpValue = 0
    --所有等级额外获得奖励物品
    boxEquipData.treasureList = {}
    --多获得物品ID
    local treasureID
    --多获得物品数量
    local treasureNum
    --临时变量
    local list, list2
    --解析宝箱升星配置
    for i, str in ipairs(starLvlProps) do
        list                       = HelperL.Split(str, ';')
        --宝箱等级配置
        ---@class BoxEquipLevelData
        local boxEquipSmelt        = {}
        --需要经验
        boxEquipSmelt.expValue     = tonumber(list[1]) or 0
        --抽奖消耗物品ID
        boxEquipSmelt.expendID     = tonumber(list[2]) or 0
        ---抽奖消耗物品数量
        boxEquipSmelt.expendNum    = tonumber(list[3]) or 0
        --奖励ID
        boxEquipSmelt.prizeID      = tonumber(list[4]) or 0
        --等级额外获得奖励物品
        ---@type {id:integer, num:integer}[]
        boxEquipSmelt.treasureList = {}
        --下一级配置
        ---@type BoxEquipLevelData|nil
        boxEquipSmelt.nextLevel    = nil

        --根据经验设置等级
        if exp >= boxEquipSmelt.expValue then
            boxEquipData.level = i
        end

        --解析等级额外获得奖励物品
        list = HelperL.Split(treasureLvls[i], ';')
        for ii, str2 in ipairs(list) do
            list2 = HelperL.Split(str2, '+')
            treasureID = tonumber(list2[1]) or 0
            treasureNum = tonumber(list2[2]) or 0
            -- if treasureID > 0 and treasureNum > 0 then
            table.insert(boxEquipSmelt.treasureList, {
                id = treasureID,
                num = treasureNum,
            })
            -- end
        end

        --设置下一级
        if i > 1 then
            boxEquipData.boxEquipSmeltList[i - 1].nextLevel = boxEquipSmelt
        end

        table.insert(boxEquipData.boxEquipSmeltList, boxEquipSmelt)
        table.insert(boxEquipData.treasureList, boxEquipSmelt.treasureList)
    end

    boxEquipData.isFullLevel = boxEquipData.level == boxEquipData.maxLevel

    --设置当前等级所需经验值
    local data = boxEquipData.boxEquipSmeltList[boxEquipData.level]
    boxEquipData.levelExpValue = data.expValue
    if boxEquipData.isFullLevel == false and data.nextLevel ~= nil then
        boxEquipData.nextLevelExpValue = data.nextLevel.expValue
    end
    boxEquipData.quality = #data.treasureList
    boxEquipData.boxEquipSmelt = data

    return boxEquipData
end

---获取子弹属性
---@param equipID integer 装备ID
---@return BulletEquipData
function m.GetBulletEquipData(equipID)
    local equipment = Schemes.Equipment:Get(equipID)
    local exp = GamePlayerData.ActorEquip:GetEquipUpgradeEXP(equipID)
    local equipSmelt = Schemes.EquipSmelt:Get(equipment.SmeltID, equipment.Quality, equipment.StarNum)
    local starLvlProps = HelperL.Split(equipSmelt.StarLvlProps, '|')
    ---@class BulletEquipData
    local bulletEquipData = {}
    bulletEquipData.starLvlProps = equipSmelt.StarLvlProps
    --是已满级
    bulletEquipData.isFullLevel = false
    --子弹等级配置
    ---@type BulletEquipLevelData[]
    bulletEquipData.bulletLevelCfg = {}
    --子弹等级
    bulletEquipData.level = 0
    --子弹最大等级
    bulletEquipData.maxLevel = #starLvlProps
    --当前等级所需经验值
    bulletEquipData.levelExpValue = 0
    --下一级等级所需经验值
    bulletEquipData.nextLevelExpValue = 0

    local list, levelCfg
    --解析子弹升星配置
    for i, str in ipairs(starLvlProps) do
        list                       = HelperL.Split(str, ';')
        ---@class BulletEquipLevelData
        local boxEquipSmelt        = {}
        --需要等级
        boxEquipSmelt.exLevel      = tonumber(list[1]) or 0
        --需要经验
        boxEquipSmelt.expValue     = tonumber(list[2]) or 0
        --消耗物品ID
        boxEquipSmelt.expendID     = tonumber(list[3]) or 0
        ---消耗物品数量
        boxEquipSmelt.expendNum    = tonumber(list[4]) or 0
        --表格ID--CommonProp.csv
        boxEquipSmelt.propID       = tonumber(list[5]) or 0
        --下一级表格ID--CommonProp.csv
        boxEquipSmelt.nextPropID   = 0
        --下一级配置
        ---@type BulletEquipLevelData|nil
        boxEquipSmelt.nextLevelCfg = nil

        --根据经验设置等级
        if exp >= boxEquipSmelt.expValue then
            bulletEquipData.level = i
        end
        --设置下一级
        if i > 1 then
            levelCfg = bulletEquipData.bulletLevelCfg[i - 1]
            levelCfg.nextLevelCfg = boxEquipSmelt
            levelCfg.nextPropID = boxEquipSmelt.propID
        end
        table.insert(bulletEquipData.bulletLevelCfg, boxEquipSmelt)
    end
    bulletEquipData.isFullLevel = bulletEquipData.level == bulletEquipData.maxLevel

    --设置当前等级所需经验值
    local data = bulletEquipData.bulletLevelCfg[bulletEquipData.level]
    bulletEquipData.levelExpValue = data.expValue
    if bulletEquipData.isFullLevel == false and data.nextLevelCfg ~= nil then
        bulletEquipData.nextLevelExpValue = data.nextLevelCfg.expValue
    end

    return bulletEquipData
end

---最大升星等级
---@param smeltID integer 升星ID
---@return integer
function m.GetEquipSmeltStarMaxLevel(smeltID)
    local list = Schemes.EquipSmeltStar:GetBySmeltID(smeltID)
    if list and #list > 0 then
        return list[#list].StarLvl
    end
    return 0
end

--- 获取属性类型描述
---@param propId integer 属性ID
---@return integer
function m.GetCommonPropPropType(propId)
    local commonProp2 = Schemes.CommonProp:Get(propId)
    if commonProp2 then
        local propType = Helper.ToInt32(commonProp2.PropType)
        if propType == 1006 or propType == 100 then      --最大Hp
            return 0
        elseif propType == 1007 or propType == 1001 then --攻击
            return 3
        end
    end
    return -1
end

--- 判断是否可以升星
---@param smeltID integer 升星ID
function m.CanUpgrade(equipID,smeltID)
    local starLvl = GamePlayerData.ActorEquipNew:GetEquipUpgradeLevel(smeltID)
    local equipSmeltStar = Schemes.EquipSmeltStar:GetEquipSmeltStarCfg(smeltID, starLvl)
    if equipSmeltStar == nil then
        warn("天赋升级:未找到升星配置 smeltID=", smeltID, starLvl)
        return false
    end


    local cfg = Schemes.Equipment:Get(equipID)
    --是否已获得
    local level = GamePlayerData.ActorEquipNew:GetEquipUpgradeLevel(smeltID)
    local maxLevel = PropertyCompute.GetEquipSmeltStarMaxLevel(smeltID)
    local weaponcfg = Schemes.EquipWeapon:Get(cfg.ID)
    local levelText = EntityModule.hero:GetProperty(CREATURE_FIELD.CREATURE_FIELD_LEVEL)
    local cureffectID5 = weaponcfg.EffectID5
    if levelText >= cureffectID5 then
        --是否是满级
        if level >= maxLevel then
           
        else
            if level ~= 0 then  
                return false
            end
        end
    else
        return false
    end

    local bool1 = not HelperL.IsLackGoods(equipSmeltStar.CostGoodsID1, equipSmeltStar.CostGoodsID1Num, false,false)
    local bool2 = not HelperL.IsLackGoods(equipSmeltStar.CostGoodsID2, equipSmeltStar.CostGoodsID1Num, false,false)
    if bool1 and bool2 then
        return true
    else
        return false
    end

end
