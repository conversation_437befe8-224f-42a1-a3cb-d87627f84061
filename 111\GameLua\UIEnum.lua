﻿--[[
********************************************************************
    created:
    author :
    purpose:    界面相关：全局常量、枚举、开放等级
*********************************************************************
--]]

--屏幕参考尺寸(根据美术参考图修改)
ScreenSize = {
	width = 1920,
	height = 1080,
}

-- 窗口类型定义
WndType = {
	Common = 1,
	Tip = 2,
	Guide = 3,
	Popup = 4,
	Main = 5,
}

-- 窗口ID定义
WndID = {
	Login = 1,                 -- 登录窗口
	TipManager = 2,            -- 提示窗口
	MainTitle = 3,             -- 主界面
	LevelSelect = 4,           -- 选关窗口
	TowerBattleMain = 5,       -- 战场主窗口
	TowerBattleResult = 6,     -- 战场结算窗口
	TowerBattleMatch = 7,      -- 塔防匹配窗口
	GuideView = 8,             -- 引导
	StoryIntroduce = 9,        -- 开篇剧情
	TestMode = 10,             -- 测试模式窗口
	TowerBattlePrepare = 11,   -- 塔防备战窗口
	TowerBattleChooseDeck = 12, -- 塔防选择卡组窗口
	FunctionOpen = 13,         -- 功能开启窗口
	CardShow = 14,             -- 获得卡牌展示窗口
	CardTips = 15,             -- 卡牌tips
	OverlayWaiting = 16,       -- 黑屏等待窗口
	TowerBattleCutScene = 17,  -- 塔防剧情过场窗口
	Recharge = 18,             -- 充值界面（包含投资）
	FirstRecharge = 19,        -- 超值礼包界面（包含体验礼包）
	ShowGoodInfo = 20,         -- 物品展示界面
	RuleTip = 21,              -- 规则提示框
	FormationGuide = 22,       -- 塔防阵型指引
	CardGuide = 23,            -- 塔防卡牌指引
	MatchingTip = 24,          -- 匹配中提示框
	RaceView = 25,             -- 竞技分类
	CardOperation = 26,        -- 卡牌操作
	RankingList = 27,          -- 排行榜
	EctypeView = 28,           -- 副本
	TowerBattlePreparePopup = 29, -- 布阵
	Welfare = 30,              -- 福利(充值)
	SevenDayInvestment = 31,   -- 七日投资
	ShowBoxInfo = 32,          -- 宝箱展示
	RoleAssets = 33,           -- 角色资产
	DirectBuyGoods = 34,       -- 物品直购
	ShowUnlockBox = 35,        -- 宝箱解锁
	SignIn = 36,               -- 签到窗口
	ShowBuyTip = 37,           -- 购买提示窗口
	TenLuckDraw = 38,          -- 十连抽窗口
	BattleGuide = 39,          -- 战场指引
	Email = 40,                -- 邮件
	EveryDayTask = 41,         -- 每日任务
	BrustCatChallenge = 42,    -- 主线关卡
	StoreAll = 43,             -- 商店
	Equipment = 44,            -- 装备
	Airplane = 45,             -- 细胞
	UpgradeSkillView = 46,     -- 升级技能
	Role = 47,                 -- 细胞背包
	DiscountGift2 = 48,        -- 特惠礼包
	GoodTips = 49,             -- 物品装备信息界面
	CatPatrol = 50,            -- 巡逻
	Challege = 51,             -- 挑战
	BattleResultView = 52,     -- 升级技能
	Setting = 53,              -- 设置
	LevelChallenge = 54,       -- 难度挑战
	LuckDraw = 55,             -- 抽奖窗口
	DisplayReward = 56,        -- 显示奖励
	ReviveDialog = 57,         -- 复活界面
	GamePauseView = 58,        -- 暂停界面
	PetShop = 59,              -- 宠物商店界面
	StageReward = 60,          -- 章节礼包界面
	TipBestEquipment = 61,     -- 更高级装备提示界面
	ADFree = 62,               -- 免广告弹窗
	BroatCast = 63,            -- 广播
	Equip = 64,                -- 装备
	Fate = 65,                 -- 菌落
	RoleNew = 66,              -- 新细胞属性界面
	Buff = 67,                 -- 主界面Buff
	AttributeUp = 68,          -- 技能属性升级
	AirPlaneNew = 69,          -- 细胞时装
	PetShopNew = 70,           -- 新宠物商店(伙伴)界面
	OpenTreasureBox = 71,      -- 宝箱商店界面
	Invitation = 72,           -- 邀请礼包
	GameEctype = 73,           -- 每日副本
	MainTask = 74,             -- 主线任务
	MonthCard = 75,            -- 月卡
	TimeLimit = 76,            -- 限时放送
	RecommendCommodities = 77, -- 推荐商品(通用商店界面)
	Shop = 78,                 -- 商店
	ProbabilityInfo = 79,      -- 概率信息
	EquipUpgrade = 80,         -- 装备升级
	EquipBox = 81,             -- 装备宝箱
	SurvivalEctype = 82,       -- 生存副本界面
	RecommendCommodities2 = 83, -- 推荐商品(超值礼包)
	RecommendCommodities3 = 84, -- 推荐商品(关注)
	Compensation = 85,         --维护补偿
	EquipKnapsack = 86,        --装备背包
	GivePhysicalPower = 87,    --赠送体力
	SettleAccounts = 88,       --结算界面
	PurchaseWindows = 89,      --购买界面
	NotarizeWindows = 90,      --确认界面
	TaskInterface = 91,        --任务界面
	RoleInfo = 92,             --角色信息
	BossHint = 93,             --Boss提示
	DYSidebar = 94,            --抖音侧边栏
	SkillsIntroduction = 95,   --技能组合介绍
	PurchasePhysicalPower = 96, --购买体力
	ShopNew = 97,              --新商城
	SettingNew = 98,           --新设置界面
	EquipWeapon = 99,          --体魄界面
	EquipWeaponInfo = 100,     --体魄信息界面
	ADWindow = 101,            --PC广告界面
	BoxRule = 102,             --宝箱规则
	BuyBox = 103,              --购买宝箱
	OpenBox = 104,             --开宝箱界面
	BoxLevel = 105,            --宝箱等级界面
	EverydayEctype = 106,      --每日副本界面
	ActivityEctype = 107,      --日常副本
	MainEctype = 108,          --主线副本界面
	BoxProbability = 109,      --宝箱概率界面
	GameAcceleration = 110,    --看广告加速游戏
	Certification = 111,       --实名认证
	PaTaFuBen = 112,           --爬塔副本
	MiJingFuBen = 113,         --秘境副本
	RoleTalent = 114,          --基因
	TreasuredTricks = 115,     --遗物
	JinJie = 116,              --装备进阶
	RewardHint = 117,          --奖励提示
	EliteEctype = 118,         --精英副本
	LeaderEctype = 119,        --首领副本
	EquipSynthesis = 120,      --装备合成
	TaskDialogue = 121,        --任务对话
	WelfareInvestment = 122,   -- 福利卡投资
	GoldCaveEctypt = 123,      -- 每日副本(黄金洞窟)
	ForbiddenAreaEctype = 124, -- 福利每日副本（大陆禁地）卡投资
	ChaoJiChongZhi = 125,      -- 超级充值
	ShengHun = 126,            -- 龙之石
	XinFa = 127,               --
	DiscountGift = 128,        -- 特惠礼包
	FCMDialogue = 129,        --防沉迷
	RoleEquip = 130,        --角色
	NormalEctype = 131,          --主线副本界面
	YQSMoney = 132,          --摇钱树
	SltsDialog = 133,          --适龄提示
	ChangeName = 134,            -- 改名
}

---------------------窗口开放等级统一放这----------------------------[[

-- 窗口开放等级
WanOpenLevel = {
	-- [WndID.RoleNew] = 0,            -- 新细胞界面
	-- [WndID.AirPlaneNew] = 2,        -- 细胞时装
	-- [WndID.Welfare] = 10,           -- 福利
	-- [WndID.StageReward] = 15,       -- 章节礼包界面
	-- [WndID.DirectBuyGoods] = 0,     -- 物品直购
	-- [WndID.SignIn] = 0,             -- 签到窗口
	-- [WndID.EveryDayTask] = 0,       -- 每日任务
	-- [WndID.Role] = 0,               -- 细胞背包
	-- [WndID.RankingList] = 0,        -- 排行榜
	-- [WndID.Challege] = 0,           -- 挑战
	-- [WndID.LuckDraw] = 0,           -- 抽奖窗口
	-- [WndID.PetShop] = 0,            -- 宠物商店界面
	-- [WndID.Fate] = 30,              -- 菌落
	-- [WndID.Buff] = 5,               -- 主界面Buff
	-- [WndID.PetShopNew] = 15,        -- 新宠物商店(伙伴)界面
	-- [WndID.DiscountGift] = 1,       -- 特惠礼包
	-- [WndID.FirstRecharge] = 2,      -- 超值礼包界面（包含体验礼包）
	-- [WndID.SevenDayInvestment] = 1, -- 七日投资
	-- [WndID.TimeLimit] = 1160,       -- 限时放送
	-- [WndID.RecommendCommodities] = 1, -- 推荐商品
	-- [WndID.MonthCard] = 1,          -- 月卡
	-- [WndID.ADFree] = 5,             -- 免广告弹窗
	-- [WndID.Invitation] = 999,       -- 邀请礼包
	-- [WndID.Email] = 1,              -- 邮件
	-- [WndID.Setting] = 1,            -- 设置
	-- [WndID.CatPatrol] = 5,          -- 巡逻
	-- [WndID.GameEctype] = 7,         -- 每日副本
	-- [WndID.Equip] = 0,              -- 装备突变
	-- [WndID.EquipUpgrade] = 8,       -- 装备升级
	-- [WndID.EquipBox] = 8,           -- 装备宝箱
	-- [WndID.Shop] = 10,              -- 免费商店
	-- [WndID.Welfare] = 999,          -- 福利
	-- [WndID.SurvivalEctype] = 7,     -- 生存副本界面
	-- [WndID.RecommendCommodities2] = 2, -- 推荐商品(超值礼包)
	-- [WndID.RecommendCommodities3] = 1, -- 推荐商品(关注)
	-- [WndID.GivePhysicalPower] = 5,  -- 赠送体力

	[WndID.EquipWeapon] = 1,      -- 体魄界面
	[WndID.OpenTreasureBox] = 3,  -- 宝箱商店
	[WndID.EquipKnapsack] = 4,    -- 装备背包
	[WndID.RoleTalent] = 3,       -- 基因
	[WndID.MiJingFuBen] = 9,      -- 秘境（远征）副本
	[WndID.ChaoJiChongZhi] = 9,   -- 日常副本
	[WndID.TreasuredTricks] = 4,  -- 遗物
	[WndID.ShengHun] = 5,         -- 龙之石
	[WndID.XinFa] = 4,            -- 祈祷
	[WndID.PaTaFuBen] = 5,       -- 爬塔副本
	[WndID.EliteEctype] = 3,     -- 精英副本
	[WndID.LeaderEctype] = 9,    -- 首领副本
	[WndID.GoldCaveEctypt] = 10,   -- 日常副本
	[WndID.ForbiddenAreaEctype] = 8, -- 日常副本
	[WndID.YQSMoney] = 6, -- 摇钱树
	[WndID.SignIn] = 3, -- 签到
}

--排除的窗口ID(不会添加打开窗口列表里，优先级高于--AutoOpenRoleAssets)
ExcludeWndID = {
	--尽量只放“弹窗”ID，现在UI界面都是一个类管理的(UIManager) ，没有“弹窗”的管理类
	--“正常窗口”的一些功能“弹窗”用不到，可在这里加ID进行屏蔽(如：自动打开角色资产界面)

	[WndID.RoleAssets] = true,
	[WndID.TaskInterface] = true,
	[WndID.TipManager] = true,
	[WndID.GuideView] = true,
	[WndID.DisplayReward] = true,
}

--自动打开角色资产界面
AutoOpenRoleAssets = {
	[WndID.MainTitle] = true,
	[WndID.MainEctype] = true,
	[WndID.EverydayEctype] = true,
	[WndID.ShopNew] = true,
	[WndID.MainEctype] = true,
	[WndID.EquipWeapon] = true,
	[WndID.ActivityEctype] = true,
	[WndID.OpenTreasureBox] = true,
	[WndID.RoleTalent] = true,
	[WndID.EquipKnapsack] = true,
	[WndID.TreasuredTricks] = true,
	[WndID.ShengHun] = true,
	[WndID.XinFa] = true,
}

--主界面按钮开放等级
MainButtonOpenLevel = {
	[1] = 4, --装备
	[2] = 999, --菌落
	[3] = 0, --细胞
	[4] = 0, --伙伴
	[5] = 7, --礼包
}

--按钮开放等级
BUTTON_OPEN_LEVEL = {
	EquipShop = 4, --装备商店
}

---------------------窗口开放等级统一放这----------------------------]]

-- 窗口消息码
WndMsg = {
	-- 通用消息码
	CommonMsg_Begin = 0,                     -- 通用消息码起始
	JumpPage = 1,                            -- 跳转页面
	CommonMsg_End = 1000,                    -- 通用消息码结束
	-- 窗口单独消息码，每个窗口保留10个值
	Login_OnLoginSucceed = 10010,            -- 登录窗口-登录成功
	RaceView_TestFuncActive = 10020,         -- 标题窗口-测试模式开启
	TowerBattleChooseDeck_UpdateSelect = 10030, -- 塔防选择卡组窗口-更新选择卡组
	TowerBattlePrepare_UpdateView = 10040,   --塔防战备UI-更新
	Welfare_Diamond = 10050,                 --福利-跳转到钻石
	Welfare_Gold = 10051,                    --福利-跳转到金币商城
	Welfare_Material = 10052,                --福利-跳转到材料商店
	Welfare_Limit = 10053,                   --福利-限时特购
	Welfare_FreeGift = 10054,                --福利-免费礼拜
	Welfare_GrowGift = 10055,                --福利-变强礼包
	Welfare_LegendGift = 10056,              --福利-传说礼包
	Welfare_Investment = 10057,              --福利-七日投资
	Welfare_Privilege = 10058,               --福利-特权卡
	Welfare_ADShop = 10059,                  --福利-广告商城
	TipManager_CloseTip = 10070,             -- 提示UI-关闭提示
}

-- UI材质类型
UIMaterialType = {
	Gray = -1, -- 灰化
	slot_00 = 0, -- 白色
	slot_01 = 1, -- 绿色
	slot_02 = 2, -- 蓝色
	slot_03 = 3, -- 紫色
	slot_04 = 4, -- 金色
	slot_05 = 5, -- 黄色
	slot_06 = 6, -- 红色
}

-- 装备顺序(请勿改顺序)
EquipOrder = {
	EQUIP_TYPE.EQUIP_TYPE_RING,      -- 装备-水之装备--5
	EQUIP_TYPE.EQUIP_TYPE_HEAD,      -- 装备-头饰--1
	EQUIP_TYPE.EQUIP_TYPE_NECKLACE,  -- 装备-暗之装备--6
	EQUIP_TYPE.EQUIP_TYPE_ARMOUR,    -- 装备-衣服--2
	EQUIP_TYPE.EQUIP_TYPE_NORMALWEAPON, -- 装备-火之装备--10
	EQUIP_TYPE.EQUIP_TYPE_SHOES,     -- 装备-鞋子--3
	EQUIP_TYPE.EQUIP_TYPE_BELT,      -- 装备-雷之装备--11
	EQUIP_TYPE.EQUIP_TYPE_PANTS,     -- 装备-护腕--4
}

--副本关卡记录逻辑值ID
TEctypeToLogicValue = {
	[1] = 25,
	[2] = 406,
	[3] = 505,
	[5] = 495,
	[6] = 496,
	[7] = 497,
	[8] = 498,
	[9] = 499,
	[10] = 500,
	[11] = 501,
	[12] = 502,
	[13] = 503,
	[14] = 504,
}

---buff装备和广告ID数据
---@type {equipID:integer, adID:integer}[]
UIMainBuffDataList = {
	{ equipID = 600051, adID = 100 },
	{ equipID = 600052, adID = 101 },
	{ equipID = 600053, adID = 102 },
}

--自定义广告ID
GameAdvertisementID = {
	ReviveGame      = 143, --复活
	Skill           = 142, --3选1技能刷新
	PhysicalPower   = -3, --体力
	FiveSelectSkill = 141, --5连选技能
}

--推荐商品界面--显示类型
UIRecommendCommodities_Type = {
	COMMON = 1, --通用
	ATTENTION = 2, --关注
}

---确认弹窗类型
---@enum NotarizeWindowsType
--确认弹窗类型
NotarizeWindowsType = {
	Windows1 = 1, --窗口1(显示按钮： 确定、取消)
	Windows2 = 2, --窗口2(显示按钮： 确定、取消、勾选)
	Windows3 = 3, --窗口3(显示按钮： 确定)
	Windows4 = 4, --窗口4(显示按钮： 确定、勾选)
	Windows5 = 5, --窗口5(显示按钮： 确定、关闭)
	Windows6 = 6, --窗口6(显示按钮： 关闭)
}

--提示类型
---@enum UIRewardHint_Type
--(UIRewardHint 界面)提示类型
UIRewardHint_Type = {
	Message = 1, --消息、奖励提示
	Power = 2, --战力变化提示
	Defense = 3 --防御变化提示
}

--特效类型
VFX_Type = {
	UPGRADE_FX = 1, -- 升级、培养、强化特效
}

--体魄出战格子解锁条件(格子数量=数组长度)
EQUIP_WEAPON_GRID_UNLOCKED_CONDITION = {
	[1] = 0,
	[2] = 0,
	[3] = 0,
	[4] = 0,
	[5] = 0,
	[6] = 0,
	[7] = 2,
	[8] = 4,
}

--宝箱装备ID
BOX_EQUIP_ID = {
	600106,
	600107,
}

--开宝箱剩余次数广告ID
BOX_EQUIP_AD_ID = {
	[BOX_EQUIP_ID[1]] = 406,
	[BOX_EQUIP_ID[2]] = 407,
}

--开宝箱数据
---@type {ID:integer, EquipID:integer, AdID:integer}[]
TREASURE_OPENING_BOX_DATA = {
	[BOX_EQUIP_ID[1]] = { ID = 1, EquipID = BOX_EQUIP_ID[1], AdID = 202, },
	[BOX_EQUIP_ID[2]] = { ID = 2, EquipID = BOX_EQUIP_ID[2], AdID = 203, },
}

--新商店--商店ID
ShopNew_ShopID = 1

--副本关卡解锁等级
EctypeUnlockLevel = {
	[1] = 1,
	[2] = 1,
	[3] = 1,
	[4] = 1,
	[5] = 1,
	[6] = 6,
	[7] = 7,
	[8] = 1,
	[9] = 1,
	[10] = 1,
	[11] = 1,
	[12] = 1,
	[13] = 1,
	[14] = 1,
}

--副本存储通关逻辑值--这个有效
EctypeSaveLogic = {
	[1] = 25,--LOGIC_DATA.DATA_NEWBIE_TARGETPRIZE_HAVEGET4,
	[5] = 495,--LOGIC_DATA.DATA_FIRSTCHARGE_TIME,
	[6] = 496,--LOGIC_DATA.DATA_FIRSTCHARGE_DATA,
	[7] = 497,--LOGIC_DATA.DATA_RECHARGECARD_VALIDTIME,
	[8] = 498,--LOGIC_DATA.DATA_RECHARGECARD_CARDID,
	[9] = 499,--LOGIC_DATA.DATA_RECHARGECARD_GIFTGOT,
	[10] = 500,--LOGIC_DATA.DATA_RECHARGECARD_LIMITTIME,
	[11] = 501,--LOGIC_DATA.DATA_NEWBIE_TARGETPRIZE_HAVEGET5,
                     ----逻辑值还有502到505能用
}

--副本存储通关逻辑值--这个有效
SmeltIDSaveLogic = {
    [1] = 101,
    [2] = 102,
    [3] = 103,
    [4] = 104,
    [8] = 105,
    [20] = 80,
    [21] = 81,
    [101] = 106,
    [102] = 107,
    [103] = 108,
    [104] = 109,
    [105] = 110,
    [106] = 111,
    [107] = 112,
    [108] = 113,
    [111] = 224,
    [121] = 225,
    [131] = 226,
    [141] = 227,
    [112] = 246,
    [122] = 247,
    [132] = 251,
    [142] = 252,
    [113] = 253,
    [123] = 254,
    [133] = 256,
    [143] = 257,
    [114] = 258,
    [124] = 259,
    [134] = 260,
    [144] = 261,
    [115] = 262,
    [125] = 263,
    [135] = 290,
    [145] = 291,
    [116] = 292,
    [126] = 293,
    [136] = 294,
    [146] = 295,
    [117] = 296,
    [127] = 297,
    [137] = 298,
    [147] = 299,
    [118] = 392,
    [128] = 393,
    [138] = 394,
    [148] = 395,
    [15000] = 114,
    [15001] = 122,
    [15002] = 123,
    [15003] = 124,
    [15004] = 125,
    [15005] = 128,
    [15006] = 129,
    [15007] = 130,
    [15008] = 132,
    [15009] = 141,
    [15010] = 142,
    [15011] = 143,
    [15012] = 144,
    [15049] = 145,
    [20001] = 160,
    [20002] = 161,
    [20003] = 162,
    [20004] = 163,
    [20005] = 164,

}

--日常挑战--副本类型
ActivityEctypeTypeList = { 6, 7 }
--黄金洞窟--副本类型
GoldCave_EctypeType = 6
--生存极限--副本类型
ForbiddenArea_EctypeType = 7
--精英挑战--副本类型
Leader_EctypeType = 8
--首领挑战--副本类型
Elite_EctypeType = 9
--远征--副本类型
MiJingFuBen_EctypeType = 10
--爬塔--副本类型
PaTaFuBen_EctypeType = 11


--法宝--装备ID
TreasuredTricks_EquipID = {
	600112,
	600113,
	600114,
}
--法宝--解锁等级(主线关卡ID)
TreasuredTricks_UnlockLevel = {
	[TreasuredTricks_EquipID[1]] = 0,
	[TreasuredTricks_EquipID[2]] = 10,
	[TreasuredTricks_EquipID[3]] = 20,
}

---经脉装备ID
Meridians_Equip_ID = {
	600115,
	600116,
	600117,
	600118,
}

--任务类型
TaskType = {
	MainEctype = 1, --主线副本
}

--法宝--类型(EquipSmeltStarData.csv)
TreasuredTricks_OprateType = 1
--天赋--类型(EquipSmeltStarData.csv)
RoleTalent_OprateType = 2

--UI通用富文本颜色
UI_COLOR = {
	Red = '#FFA2FF', --红色
	White = '#FFFFFF', --白色
	Gray = '#CCCCCC', --灰色
	Black = '#000000', --黑色
}

--体魄ID
EquipWeaponInfo_EQUIP_ID = 301001