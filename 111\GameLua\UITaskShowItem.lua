--[[
   created:	2016/10/10
   author :	吴德燊
   purpose:	UITaskShowItem
--]] require "QuickUpgrade"
local luaID = ('UITaskShowItem')

local string_format = string.format
function CreateRecycle()
    local o = {}

    function o:push(go)
        table.insert(o, go)
    end

    function o:pop()
        if #o > 0 then
            local top = o[#o]
            table.remove(o, #o)
            return top
        end
        return nil
    end

    return o
end

function CreateUITaskShowItemScript(obj)
    local o = {}
    o.gameObject = obj
    -- 自动完成跳转界面的任务groupID
    o.autoCompleteList = {4, 5, 6, 13, 16, 17, 18, 26}
    -- 达到完成条件强制完成的任务groupID
    o.forceCompleteList = {100}
    o.New = function()
        local parentTrans = o.gameObject.transform
        o.statusObj = parentTrans:Find('Status').gameObject
        o.reddotObj = parentTrans:Find('redspot (1)').gameObject
        o.nameLabel = parentTrans:Find('Txt_Name'):GetComponent('Text')
        o.descLabel = parentTrans:Find('Txt_Desc'):GetComponent('Text')

        o.recommendBtn = parentTrans:Find('pointTo').gameObject
        o.recommendSprite = o.recommendBtn:GetComponent('Image')
        o.recommendBtnComp = o.recommendBtn:GetComponent('Button')
        o.redsopt = parentTrans:Find('pointTo/redspot').gameObject
        o.effect = parentTrans:Find('Effect').gameObject
        o.tipArrow = parentTrans:Find('TipArrow').gameObject
        o.tipArrowTween = o.tipArrow:GetComponent('TweenPosition')
        -- o.recommendBtnComp.onClick:AddListenerEx(o.OnClickRecommendBtn)
        o.gameObject:GetComponent('Button').onClick:AddListenerEx(function()
            o.OpenMainTaskAuto()
            -- o.OnClickContentObj(o)
        end)
        -- 检测快速升级红点
        function o.CheckRedspot()
            if RedDotCheckFunc.CheckQuickUpgrade() then
                o.redsopt:SetActive(true)
            else
                o.redsopt:SetActive(false)
            end
        end
        o.isFirstClick = false
        o.curTaskID = 0
        o.focusItems = {}
        o.curState = ECTaskStatus.Null
        o.clickedState = ECTaskStatus.Null
        o.isClickByAuto = false
        o.startScale = Vector3(2.5, 2.5, 1)
    end

    o.UpdateTimeStr = function(...)
        local curScore = o.taskItem:GetTaskScore()
        local tmpScore = o.taskItem:GetTaskGoal() - curScore
        local hours = tmpScore / 3600
        local seconds = tmpScore % 3600
        local minuates = math.floor(seconds / 60)
        local taskScheme = o.taskItem.taskScheme
        if tmpScore <= 0 then
            o.nameLabel.text = string_format("<color=FFFF00>%s</color>%s", taskScheme.Name, '')
            o.reddotObj:SetActive(true)
            o.descLabel.text = taskScheme.Track3
            o.timer:Stop()
            return
        end
        seconds = seconds % 60
        local timeText = string.format('%02d:%02d', minuates, seconds)
        o.nameLabel.text = string_format("%s  %s", taskScheme.Name, timeText)
    end

    o.SetData = function(self, branchID, taskID, status)
        -- self.CheckRedspot()
        self.effect:SetActive(false)
        if self.timer and self.timer.running then
            self.timer:Stop()
        end
        if branchID < 0 and taskID < 0 then
            self.reddotObj:SetActive(false)
            self.recommendBtn:SetActive(false)
            self.tipArrow:SetActive(false)
            self.descLabel.text = ''
            self.nameLabel.text = GetGameText(luaID, 1)
            o.StopFocusAnimation()
            local nameTrans = self.nameLabel.transform
            nameTrans.localPosition = Vector3.zero
            -- self.nameLabel.alignment = NGUIText.Alignment.Center
            self.moreTask = true
            self.taskItem = nil
            self.taskID = nil
            return
        end
        if self.moreTask then
            local nameTrans = self.nameLabel.transform
            nameTrans.localPosition = Vector3(0, 15, 0)
            -- self.nameLabel.alignment = NGUIText.Alignment.Left

            self.moreTask = nil
        end
        if self.taskID ~= taskID then
            self.tipArrow:SetActive(false)
        end
        local hero = EntityModule.hero
        local taskPart = hero.heroTaskLC
        self.taskItem = taskPart.GetItem(branchID, taskID)
        self.taskID = taskID
        if self.taskItem then
            self.step = self.taskItem.step
            --[[
			if self.flyDestTaskBtn then
				local curTaskScheme = self.taskItem.taskScheme
				if curTaskScheme then
					--任务是否可用小飞鞋
					if curTaskScheme.Fly == 1 and not WorldModule.IsBanFly() then
						self.flyDestTaskBtn:SetActive(true)
					else
						self.flyDestTaskBtn:SetActive(false)
					end
				end
				--点击了小飞鞋按钮
				UIEventListener.Get(self.flyDestTaskBtn).onClick = function()
					if self.taskItem then
						self.OnClickContentObj(self.gameObject)
						UIManager.mainlandUI.TaskFlyBtnClick(self.flyDestTaskBtn)
					end
				end
			end
			--]]
        end

        local taskScheme = self.taskItem.taskScheme
        self.recommendBtn:SetActive(false)
        -- self.recommendSprite.atlas = HelperL.GetAtlas('MainLandIconAtlas')
        -- self.recommendBtnComp.normalSprite = 'zjm_tj'
        -- self.recommendSprite.spriteName = 'zjm_tj'
        self.status = self.taskItem.GetClientStatus and self.taskItem:GetClientStatus() or status
        if not taskScheme then
            return
        end
        if o.isFirstClick then
            o.StopFocusAnimation()
        else
            if o.curTaskID ~= taskID or o.curState ~= self.status then
                o.StopFocusAnimation()
                -- focus黄框
                local noticeType = taskScheme.AcceptNotice
                local isShow = o.CheckCanShowFocusAni(noticeType, self.status)
                if isShow then
                    -- o.FocusRepeat()
                    o.PlayFocusAnimation()
                end
            end
        end
        o.curState = self.status
        o.curTaskID = taskID
        self.style = taskScheme.Style
        if self.style == ECTaskStyle.Trunk then
            if taskScheme.Preview == 1 then
                local prize = Schemes.PrizeTable:GetGoods(taskScheme.PrizeID)
                if prize then
                    local prizeList = Schemes.PrizeTable.GetPrizeGoods(prize)
                    if prizeList and #prizeList > 0 then
                        if not self.mainTaskPrizePre then
                            self.mainTaskPrizePre = UICommonItem.New(prizeList[1].ID)
                            self.mainTaskPrizePre:AddToParent(o.gameObject, 172, 0, 0)
                        end
                        self.mainTaskPrizePre:SetID(prizeList[1].ID)
                        self.mainTaskPrizePre:SetNum(prizeList[1].Num)
                        self.mainTaskPrizePre:SetNumVisible(true)
                        self.mainTaskPrizePre:SetVisible(true)
                        self.mainTaskPrizePre.gameObject.transform.localScale = Vector3(0.8, 0.8, 0.8)
                        EventManager:Fire(EventID.ChangeTaskShow, 2)
                    end
                end
            else
                if self.mainTaskPrizePre then
                    self.mainTaskPrizePre:SetVisible(false)
                end
            end
        else
            if self.mainTaskPrizePre then
                self.mainTaskPrizePre:SetVisible(false)
            end
        end
        local round = self.taskItem.round
        local timeStr = ''
        local score = ''
        if self.style == ECTaskStyle.Trunk or self.style == ECTaskStyle.Branch then
            timeStr = ''
        else
            local goal = Schemes.TaskBranch.GroupCount(taskScheme.GroupID)
            if self.status == ECTaskStatus.Doing or self.status == ECTaskStatus.CanComplete then
                round = round + 1
            end
            timeStr = string_format("[FFFF00](%s/%s)[-]", round, goal)
        end
        if self.status == ECTaskStatus.CanComplete then
            self.nameLabel.text = string_format("<color=#FFFF00>%s</color>", taskScheme.Name)
            self.descLabel.text =string_format(" <color=#81EB70>%s</color>",  taskScheme.Track3 .. timeStr)
            -- o.curFocusTask
            local curFocusTask = EntityModule.hero.heroTaskLC.curFocusTask
            if curFocusTask and curFocusTask == self.taskItem and o.autoCompleteList then
                -- 帮会跑环、采集、建设、提升、历练、组队、多人挑战、自动跑环类任务，完成后自动打开界面
                for _, v in ipairs(o.autoCompleteList) do
                    if v == taskScheme.GroupID then
                        if self.style == ECTaskStyle.Society then
                            if self.taskItem.taskScheme.ClickPointTo == 5 then
                                EntityModule.hero.heroTaskLC.AutoTaskPH = 1
                                UIManager.LoadUI('UISocietyToPH')
                            elseif self.taskItem.taskScheme.ClickPointTo == 6 then
                                EntityModule.hero.heroTaskLC.AutoTaskCJ = 1
                                UIManager.LoadUI('UISocietyToCJ')
                            elseif self.taskItem.taskScheme.ClickPointTo == 7 then
                                EntityModule.hero.heroTaskLC.AutoTaskJS = 1
                                UIManager.LoadUI('UISocietyToJS')
                            end
                        else
                            if self.taskItem.taskScheme.ClickPointTo == 36 then
                                EntityModule.hero.heroTaskLC.AutoTaskDailyPH = 1
                                UIManager.LoadUI('UILoopTask')
                                break
                            end
                            self.taskItem:WantToTurnInTask()
                        end
                        break
                    end
                end
            end
            if o.forceCompleteList then
                -- 达到完成条件即强制打开界面完成的任务
                for _, v in ipairs(o.forceCompleteList) do
                    if v == taskScheme.GroupID then
                        if self.taskItem.taskScheme.ClickPointTo == 81 then
                            UIManager.LoadUI('UIDefendCountry')
                            UIManager.SendWndMsg('UIDefendCountry', UIWndMsg.UIDefendCountry.jumpInfiltrateEnemy)
                            break
                        end
                    end
                end
            end
            if self.style == ECTaskStyle.Daily and taskScheme.GroupID >= 46 and taskScheme.GroupID <= 59 then
                if WorldModule.ectypeInfo and WorldModule.ectypeInfo.EctypeID == 0 and not self.showComplete then
                    PlayerPrefs.SetInt('isCompleteFormClick', self.taskItem.taskScheme.GroupID)
                    self.taskItem:WantToTurnInTask()
                    self.showComplete = true
                end
            end
        elseif self.status == ECTaskStatus.Doing then
            self.descLabel.color = Color.white
            self.showComplete = false
            if taskScheme.Type == TaskType.TaskType_KillLevelMonster or 
            taskScheme.Type ==TaskType.TaskType_KillTargetMonster or 
            taskScheme.Type == TaskType.TaskType_CollectGood or
                taskScheme.Type == TaskType.TaskType_OperateCollectGood or 
                taskScheme.Type == TaskType.TaskType_UpGrade or
                taskScheme.Type == TaskType.TaskType_JoinActivity or 
                taskScheme.Type ==TaskType.TaskType_EveryDayUpgrade or 
                taskScheme.Type == TaskType.TaskType_LieMingAndQiFuCount or
                taskScheme.Type == TaskType.TaskType_ActorLevel then
                score = string_format("<color=#FFA500>(%s/%s)</color>", self.taskItem.score, taskScheme.Parameter2)
            end
            if taskScheme.Type == TaskType.TaskType_FriendShip then
                score = string_format("<color=#FFA500>(%s/%s)</color>", self.taskItem.score, taskScheme.Parameter1)
            end
            if taskScheme.Type == TaskType.TaskType_FuBenId then
                score = string_format("<color=#FFA500>(%s/%s)</color>", 0, 1)
            end
            if taskScheme.Type == TaskType.TaskType_TimePass then
                if self.status == ECTaskStatus.Doing then
                    if not self.timer then
                        self.timer = Timer.New(self.UpdateTimeStr, 1, -1)
                    end
                    self.UpdateTimeStr()
                    self.timer:Start()
                end
            else
                self.nameLabel.text = string_format("%s", taskScheme.Name)
            end
            self.descLabel.text = taskScheme.Track2 .. score
            if taskScheme.Recommend > 0 then
                self.recommendBtn:SetActive(true)
                -- self.flyDestTaskBtn:SetActive(false)
            elseif taskScheme.FastSpeakType == 1 then
                self.recommendBtnComp.normalSprite = 'zjm_zdui'
                AtlasManager:AsyncGetSprite("zjm_zdui", self.recommendSprite)
                self.recommendBtn:SetActive(true)
                -- self.flyDestTaskBtn:SetActive(false)
                self.redsopt:SetActive(false)
            elseif taskScheme.RecommendPointTo == 1 then
                self.recommendBtnComp.normalSprite = 'zjm_bq'
                AtlasManager:AsyncGetSprite("zjm_bq", self.recommendSprite)
                self.recommendBtn:SetActive(true)
                self.redsopt:SetActive(false)
            end
        elseif self.status == ECTaskStatus.CanAccept then
            self.descLabel.color = Color.white
            self.showComplete = false
            self.nameLabel.text = string_format("%s", taskScheme.Name)
            self.descLabel.text = taskScheme.Track1 .. timeStr
            if taskScheme.Recommend > 0 then
                self.recommendBtn:SetActive(true)
            elseif taskScheme.FastSpeakType == 1 then
                self.recommendBtnComp.normalSprite = 'zjm_zdui'
                AtlasManager:AsyncGetSprite("zjm_zdui", self.recommendSprite)
                self.recommendBtn:SetActive(true)
                self.redsopt:SetActive(false)
            elseif taskScheme.RecommendPointTo == 1 then
                self.recommendBtnComp.normalSprite = 'zjm_bq'
                AtlasManager:AsyncGetSprite("zjm_bq", self.recommendSprite)
                self.recommendBtn:SetActive(true)
                self.redsopt:SetActive(false)
            end
        else
            self.nameLabel.text = string_format("%s", taskScheme.Name)
            self.descLabel.text = taskScheme.Track1 .. timeStr
            if taskScheme.Recommend > 0 then
                self.recommendBtn:SetActive(true)
                -- self.flyDestTaskBtn:SetActive(false)
            end
        end
        self.reddotObj:SetActive(status == ECTaskStatus.CanComplete)

        -- local recommendCollider = self.recommendBtn:GetComponent('BoxCollider')
        -- if self.recommendSprite.spriteName == 'zjm_tj' then
        -- 	recommendCollider.center = Vector3(-110,-10,0)
        -- 	recommendCollider.size = Vector3(260,60,0)
        -- else
        -- 	recommendCollider.center = Vector3(0,0,0)
        -- 	recommendCollider.size = Vector3(45,38,0)
        -- end

        -- 对所有系列任务（即需完成多次）的进度次数进行展示
        -- taskScheme为任务表，GroupCount为系列任务的总次数，curCount为系列任务的进度次数(默认加一)	--taskScheme.TaskTimes
        if taskScheme.GroupID ~= 0 then
            local GroupCount = Schemes.TaskBranch.GroupCount(taskScheme.GroupID)
            local curCount = self.taskItem.round + 1
            if GroupCount and GroupCount >= 2 then
                self.nameLabel.text = string_format("%s<color=FFFF00>(%d/%d)</color>", taskScheme.Name, curCount,
                    GroupCount)
            end
        end
    end

    o.AcceptMission = function(self, branchID, taskID)
        local taskPart = EntityModule.hero.heroTaskLC
        local taskItem = taskPart.GetItem(branchID, taskID)
        if not taskItem then
            return
        end
        local step = nil
        if taskItem then
            step = taskItem.step
        end
        local taskScheme = Schemes.Task:Get(taskID, step)
        local canAccept, result = taskItem:CanAccept()
        if not canAccept then
            return false
        end
        -- 新建细胞已经弹过升级送VIP界面就直接接任务 接受第一个任务放到第一次关闭升级送VIP界面里
        if taskScheme.ShowStar == 1 then
            taskItem:WantToAccept()
        end
    end

    o.OnClickContentObj = function(go1, script)
        if not o.isClickByAuto then
            o.clickedState = o.curState
            o.isFirstClick = true
        end
        -- EventManager:Fire(EventID.PlayerWakeUp)
        --		HelperL.SetAutoFight(false) -- 停止自动挂机
        -- MiscDataCenter.StopRunToFollowNpc()
        o.tipArrow:SetActive(false)
        local self = o
        self.effect:SetActive(false)
        EventManager:Fire(EventID.TaskAcceptNotice, self.taskItem, false)
        local hero = EntityModule.hero
        local taskPart = hero.heroTaskLC
        if not self.taskItem then
            if self.moreTask then
                UIManager.LoadUI('UIDailyPlay')
            end
            return
        end
        local style = self.taskItem.taskScheme.Style
        local clientStatus = self.taskItem.GetClientStatus and self.taskItem:GetClientStatus() or ECTaskStatus.Null
        EntityModule.hero.heroTaskLC.SetCurFocusTask(self.taskItem)
        if clientStatus == ECTaskStatus.CanComplete then
            if style == ECTaskStyle.Society then
                if self.taskItem.taskScheme.ClickPointTo == 5 then
                    UIManager.LoadUI('UISocietyToPH')
                elseif self.taskItem.taskScheme.ClickPointTo == 6 then
                    UIManager.LoadUI('UISocietyToCJ')
                elseif self.taskItem.taskScheme.ClickPointTo == 7 then
                    UIManager.LoadUI('UISocietyToJS')
                end
                return
            end
            if style == ECTaskStyle.Daily then
                PlayerPrefs.SetInt('isCompleteFormClick', self.taskItem.taskScheme.GroupID)
            end
            self.taskItem:WantToTurnInTask()
        elseif clientStatus == ECTaskStatus.Doing then
            if self.taskItem.taskScheme.Type == TaskType.TaskType_Escort then
                if taskPart.onEscort ~= 1 then
                    EntityModule.hero.escortLG.WantToOnEscort()
                    return
                end
                self.taskItem:AfterAccept()
            else
                local taskScheme = self.taskItem.taskScheme
                if taskScheme.ChapID ~= 0 then
                    local chapScheme = Schemes.Chapter.Get(taskScheme.ChapID)
                    if chapScheme == nil then
                        return
                    end
                    if chapScheme.EntryCondition == 1 then
                        EntityModule.hero.ectypeLC.taskSelectEctype = taskScheme.ChapID
                        UIManager.OpenTaskEctype(1)
                    elseif chapScheme.EntryCondition == 5 then
                        EntityModule.hero.ectypeLC.selectedChallengeEctypeID = taskScheme.ChapID
                        UIManager.OpenTaskEctype(2)
                    end
                    return
                end
                if taskScheme.ClickPointTo == 0 or taskScheme.ClickPointTo == 4 then
                    self.taskItem:AfterAccept()
                elseif taskScheme.ClickPointTo == 2 then
                    EntityModule.hero.ectypeLC.taskSelectEctype = taskScheme.ChapID
                    EntityModule.hero.ectypeLC.curSelectTabIndex = 1
                    UIManager.OpenTaskEctype(1)
                elseif taskScheme.ClickPointTo == 3 then
                    UIManager.LoadUI('UITeam')
                    -- 帮会跑环、采集、建设
                elseif taskScheme.ClickPointTo >= 5 and taskScheme.ClickPointTo <= 7 then
                    self.taskItem:AfterAccept()
                    -- 体魄界面
                elseif taskScheme.ClickPointTo == 18 then
                    UIManager.weapenPanel = 1
                    UIManager.LoadUI('UIDivineWeaponWindow')
                    -- 单人副本界面
                elseif taskScheme.ClickPointTo == 25 then
                    UIManager.OpenTaskEctype(1)
                    UIManager.SendWndMsg('UIEctype', UIWndMsg.UIEctype.jumpToMaterialChildPage, taskScheme.TaskImg)
                    -- 多人副本
                elseif taskScheme.ClickPointTo == 26 then
                    EntityModule.hero.ectypeLC.selectedChallengeEctypeID = taskScheme.TaskImg
                    UIManager.OpenTaskEctype(2)
                    -- 通天塔
                elseif taskScheme.ClickPointTo == 27 then
                    UIManager.OpenTaskEctype(3)
                    -- 任务跑环界面
                elseif taskScheme.ClickPointTo == 36 then
                    self.taskItem:AfterAccept()
                    -- UIManager.LoadUI('UILoopTask')
                    -- 锻造突变界面
                elseif taskScheme.ClickPointTo == 41 then
                    UIManager.LoadUI('UIEquipmentForge')
                    UIManager.SendWndMsg('UIEquipmentForge', UIWndMsg.UIEquipmentForge.jumpPagMsg, 'clear')
                    -- 锻造升级界面
                elseif taskScheme.ClickPointTo == 42 then
                    UIManager.LoadUI('UIEquipmentForge')
                    UIManager.SendWndMsg('UIEquipmentForge', UIWndMsg.UIEquipmentForge.jumpPagMsg, 'upStar')
                    -- 锻造强化界面
                elseif taskScheme.ClickPointTo == 43 then
                    UIManager.LoadUI('UIEquipmentForge')
                    UIManager.SendWndMsg('UIEquipmentForge', UIWndMsg.UIEquipmentForge.jumpPagMsg, 'strength')
                    -- 锻造提品界面
                elseif taskScheme.ClickPointTo == 44 then
                    UIManager.LoadUI('UIEquipmentForge')
                    UIManager.SendWndMsg('UIEquipmentForge', UIWndMsg.UIEquipmentForge.jumpPagMsg, 'syn')
                    -- 帮会副本界面
                elseif taskScheme.ClickPointTo == 47 then
                    local societyID = EntityModule.hero.societyLC.SocietyData.SocietyID
                    if societyID == 0 then
                        UIManager.LoadUI("UISocietyCommonWin")
                        UIManager.SendWndMsg('UISocietyCommonWin', UIWndMsg.UISocietyMainPanel.jumpPagMsg, 'societyList')
                        HelperL.ShowMessage(TipType.FlowText, GetGameText(luaID, 2))
                    else
                        UIManager.LoadUI('UISocietyMainPanel')
                        UIManager.SendWndMsg('UISocietyMainPanel', UIWndMsg.UISocietyMainPanel.jumpPagMsg, 5)
                    end
                    -- 熔炼界面
                elseif taskScheme.ClickPointTo == 48 then
                    UIManager.LoadUI('UISmelting')
                    -- 技能界面
                elseif taskScheme.ClickPointTo == 50 then
                    UIManager.LoadUI('UISkill')
                    UIManager.SendWndMsg('UISkill', UIWndMsg.UISkill.jumpPagMsg, 1)
                    -- 神印界面
                elseif taskScheme.ClickPointTo == 51 then
                    UIManager.LoadUI('UISkill')
                    UIManager.SendWndMsg('UISkill', UIWndMsg.UISkill.jumpPagMsg, GENRE.GENRE_MAX)
                    -- 坐骑界面
                elseif taskScheme.ClickPointTo == 62 then
                    local param = {
                        index = 2,
                        isSpecial = 0
                    }
                    UIManager.LoadUI('UIWingShow')
                    UIManager.SendWndMsg('UIWingShow', UIWndMsg.UIWingShow.jumpPagMsg, param)
                    -- 仙器界面
                elseif taskScheme.ClickPointTo == 63 then
                    local param = {
                        index = 3,
                        isSpecial = 0
                    }
                    UIManager.LoadUI('UIWingShow')
                    UIManager.SendWndMsg('UIWingShow', UIWndMsg.UIWingShow.jumpPagMsg, param)
                    -- 卡牌界面
                elseif taskScheme.ClickPointTo == 64 then
                    UIManager.LoadUI('UIBeauty')
                    -- 每日必做界面
                elseif taskScheme.ClickPointTo == 65 then
                    UIManager.LoadUI('UIDailyPlay')
                    -- 进入帮会副本
                elseif taskScheme.ClickPointTo == 66 then
                    if not WorldModule.ectypeInfo or WorldModule.ectypeInfo.EctypeID ~= 7950 then
                        local societyID = EntityModule.hero.societyLC.SocietyData.SocietyID
                        if societyID == 0 then
                            UIManager.LoadUI("UISocietyCommonWin")
                            UIManager.SendWndMsg('UISocietyCommonWin', UIWndMsg.UISocietyMainPanel.jumpPagMsg,
                                'societyList')
                            HelperL.ShowMessage(TipType.FlowText, GetGameText(luaID, 2))
                        else
                            LuaModule.RunLuaRequest("LuaRequestEnterSocietyEctype", function(resultCode, content)
                                if resultCode == RESULT_CODE.RESULT_COMMON_SUCCEED then
                                    self.taskItem:AfterAccept()
                                else
                                    HelperL.MessageTipCallBack(resultCode)
                                end
                            end)
                        end
                    else
                        if EntityModule.hero then
                            local autoFight = IsTaskNavigationNeedAutoFight(taskScheme)
                            EntityModule.hero.navigationLG:SetWorldDestination(WorldModule.mapId,
                                HelperL.PBString2Vector3(taskScheme.WayCoordinate), autoFight, _, _, self.taskItem
                                    .taskID * 256 + self.taskItem.branchID)
                        end
                    end
                elseif taskScheme.ClickPointTo == 67 then
                    -- 打开竞技场界面
                    UIManager.LoadUI('UIArena')
                elseif taskScheme.ClickPointTo == 68 then
                    -- 快速升级TIPS
                    local prefab = HotResManager.ReadUI("UI/Task/prefab/QuickUpgrade")
                    local QuickUpgrade = GameObject.Instantiate(prefab, o.gameObject.transform)
                    local itemScript = QuickUpgradeChangeType(QuickUpgrade, taskScheme.Recommend, o)
                    -- local anchor = QuickUpgrade.gameObject.transform:Find('Bg'):GetComponent("Image")
                    -- local anchorTarget = o.gameObject
                    -- if tolua.isnull(anchorTarget) then return end
                    -- if GameLuaAPI.IsIPhoneX() then
                    -- 	anchor:SetAnchor(anchorTarget, 391, 17, 669, 623)
                    -- else		
                    -- 	anchor:SetAnchor(anchorTarget, 266, -50, 544, 623)
                    -- end
                    -- anchor.rightAnchor.relative = 0
                    -- anchor.bottomAnchor.relative = 0 
                    -- anchor.topAnchor.relative = 0 
                    -- 专属boss
                elseif taskScheme.ClickPointTo == 69 then
                    UIManager.LoadUI('UITask')
                    UIManager.SendWndMsg('UITask', UIWndMsg.UITask.jumpPagMsg, {
                        tag = 'vipBoss_Tabs'
                    })
                    -- 枭雄boss
                elseif taskScheme.ClickPointTo == 70 then
                    UIManager.LoadUI('UITask')
                    UIManager.SendWndMsg('UITask', UIWndMsg.UITask.jumpPagMsg, {
                        tag = 'underGroundBoss_Tabs'
                    })
                    -- 野外boss
                elseif taskScheme.ClickPointTo == 71 then
                    UIManager.LoadUI('UITask')
                    -- 战场
                elseif taskScheme.ClickPointTo == 72 then
                    UIManager.LoadUIWithoutPool('UIBattleGround')
                    -- 边境战场
                elseif taskScheme.ClickPointTo == 73 then
                    UIManager.LoadUI('ChuhanBattlefield')
                    -- 世界boss
                elseif taskScheme.ClickPointTo == 74 then
                    UIManager.LoadUI('UITask')
                    UIManager.SendWndMsg('UITask', UIWndMsg.UITask.jumpPagMsg, {
                        tag = 'worldBoss_Tabs'
                    })
                    -- 个人boss
                elseif taskScheme.ClickPointTo == 75 then
                    UIManager.LoadUI("UITask")
                    UIManager.SendWndMsg('UITask', UIWndMsg.UITask.jumpPagMsg, {
                        tag = 'myBoss_Tabs'
                    })
                    -- 翅膀界面
                elseif taskScheme.ClickPointTo == 76 then
                    local param = {
                        index = 1,
                        isSpecial = 0
                    }
                    UIManager.LoadUI('UIWingShow')
                    UIManager.SendWndMsg('UIWingShow', UIWndMsg.UIWingShow.jumpPagMsg, param)
                    -- 剧情副本
                elseif taskScheme.ClickPointTo == 77 then
                    UIManager.OpenTaskEctype(5)
                    UIManager.LoadUI('UIEctype')
                    -- 跨服
                elseif taskScheme.ClickPointTo == 78 then
                    UIManager.LoadUI('UICrossServiceActivities')
                    -- 菌落 -> 探测
                elseif taskScheme.ClickPointTo == 79 then
                    UIManager.PropertyPanel = 1
                    UIManager.LoadUI('UIProperty')
                    UIManager.SendWndMsg('UIProperty', UIWndMsg.UIProperty.jumpLifePagMsg, 4)
                    -- 帮会 -> 活动
                elseif taskScheme.ClickPointTo == 80 then
                    local societyID = EntityModule.hero.societyLC.SocietyData.SocietyID
                    if societyID == 0 then
                        UIManager.LoadUI("UISocietyCommonWin")
                        UIManager.SendWndMsg('UISocietyCommonWin', UIWndMsg.UISocietyMainPanel.jumpPagMsg, 'societyList')
                        HelperL.ShowMessage(TipType.FlowText, GetGameText(luaID, 2))
                    else
                        UIManager.LoadUI('UISocietyMainPanel')
                        UIManager.SendWndMsg('UISocietyMainPanel', UIWndMsg.UISocietyMainPanel.jumpPagMsg, 3)
                    end
                    -- 保家卫国 -> 潜入敌营
                elseif taskScheme.ClickPointTo == 81 then
                    self.taskItem:AfterAccept()
                    -- UIManager.LoadUI('UIDefendCountry')
                    -- UIManager.SendWndMsg('UIDefendCountry', UIWndMsg.UIDefendCountry.jumpInfiltrateEnemy)
                    -- 新版变强界面
                elseif taskScheme.ClickPointTo == 82 then
                    UIManager.LoadUI('UIQuickStrength')
                elseif taskScheme.ClickPointTo == 83 then
                    UIManager.OpenTaskEctype(8)
                else
                    error(' here ')
                end
            end
        elseif clientStatus == ECTaskStatus.CanAccept then
            EntityModule.hero.heroTaskLC.SetFocusTask(self.taskItem)
            if style == ECTaskStyle.Escort then
                local acceptNPC = EntityModule.hero.heroTaskLC.GetAcceptNPC(self.taskItem.taskScheme)
                EntityModule.hero.navigationLG:NavigateToNPC(acceptNPC)
            elseif style == ECTaskStyle.Society then
                local societyID = EntityModule.hero.societyLC.SocietyData.SocietyID
                if societyID == 0 then
                    UIManager.LoadUI("UISocietyCommonWin")
                    UIManager.SendWndMsg('UISocietyCommonWin', UIWndMsg.UISocietyMainPanel.jumpPagMsg, 'societyList')
                    HelperL.ShowMessage(TipType.FlowText, GetGameText(luaID, 2))
                else
                    if self.taskItem.taskScheme.ClickPointTo == 5 then
                        UIManager.LoadUI('UISocietyToPH')
                    elseif self.taskItem.taskScheme.ClickPointTo == 6 then
                        UIManager.LoadUI('UISocietyToCJ')
                    elseif self.taskItem.taskScheme.ClickPointTo == 7 then
                        UIManager.LoadUI('UISocietyToJS')
                    end
                end
            else
                local mapID = taskPart.GetMapID(self.taskItem.taskScheme.AcceptMapID)
                local tagMapID = taskPart.CalcMapID(0, mapID)
                if tagMapID ~= 0 then
                    self.taskItem:WantToAccept()
                else
                    local groupID = self.taskItem.taskScheme.GroupID
                    if style == ECTaskStyle.Branch then

                    elseif style == ECTaskStyle.Daily then
                        -- groupID 大于100000 为导航点击且不需要显示每日必做背景的标识
                        groupID = groupID + 100000
                    elseif style == ECTaskStyle.Loop then
                        UIManager.LoadUI('UILoopTask')
                        return
                    elseif style == ECTaskStyle.Trunk then
                        o.OpenMainTaskAuto()
                        -- UIManager:OpenWnd(WndID.MainTask)
                        return
                    end
                    UIManager.LoadUI('UIDailyPlay')
                    UIManager.SendWndMsg('UIDailyPlay', UIWndMsg.UIDailyPlay.jumpAppointTaskAccept, groupID)
                end
            end
        else
            UIManager.LoadUI('UIDailyPlay')
        end
    end

    o.OpenMainTaskAuto = function()
        local trunkTask = nil
        local hero = EntityModule.hero
        local taskPart = hero.heroTaskLC
        for k, v in pairs(taskPart.idToItem) do
            if v.status ~= TASK_STATUS.TASK_STATUS_COMPLETED and v.taskScheme.Style == ECTaskStyle.Trunk then
                trunkTask = v
            end
        end

        if trunkTask == nil then
            return
        end
        local status = trunkTask:GetClientStatus()
        --print("taskStatus:"..tostring(status))
        local taskItem = taskPart.GetItem(trunkTask.branchID, trunkTask.taskID)
        if status == ECTaskStatus.CanComplete then
            taskPart.SendLuaTurnInTask(trunkTask.branchID, trunkTask.taskID, trunkTask.step, o.FinishTaskCallBack)
            EventManager:Fire(EventID.TaskFinish)
        elseif status == ECTaskStatus.Doing then
            o.OpenMainTaskGuide(trunkTask)
        end
    end

    o.OpenMainTaskGuide = function(trunkTask)
        --print(trunkTask.taskScheme.Type .. "||" .. trunkTask.taskScheme.Parameter1 .. "||")
        if trunkTask.taskScheme.Type == TaskType.TaskType_FuBenId then
            if trunkTask.taskScheme.Parameter1 < 9999 then
                EventManager:Fire(EventID.TaskShowHandGuide, 1)
            elseif trunkTask.taskScheme.Parameter1 > 9999 then
                EventManager:Fire(EventID.TaskShowHandGuide, 2)
            end
        elseif trunkTask.taskScheme.Type == TaskType.TaskType_EveryDayUpgrade then
            if trunkTask.taskScheme.Parameter1 == 9 then
                EventManager:Fire(EventID.OpenMainButtonUI, 3,1)
            elseif trunkTask.taskScheme.Parameter1 == 3 then
                EventManager:Fire(EventID.OpenMainButtonUI, 1,2)
            elseif trunkTask.taskScheme.Parameter1 == 1 then
                EventManager:Fire(EventID.OpenMainButtonUI, 1,3)
            end
        elseif trunkTask.taskScheme.Type == TaskType.TaskType_LieMingAndQiFuCount then
            if trunkTask.taskScheme.Parameter1 == 2 then
                EventManager:Fire(EventID.OpenMainButtonUI, 1,1)
            elseif trunkTask.taskScheme.Parameter1 == 1 then
                EventManager:Fire(EventID.OpenMainButtonUI, 2,1)
            end
        elseif trunkTask.taskScheme.Type == TaskType.TaskType_ActorLevel then
            EventManager:Fire(EventID.OpenMainButtonUI, 1,1)
        end
    end

    o.FinishTaskCallBack = function(result)
        if result == RESULT_CODE.RESULT_COMMON_SUCCEED then

        else
            HelperL.ShowMessage(TipType.FlowText, ResultCode:GetResultCodeStr(result, content))
        end
    end

    o.OnClickRecommendBtn = function(go, script)
        local self = script
        local hero = EntityModule.hero
        if not hero then
            return
        end
        local taskPart = hero.heroTaskLC
        if not o.taskItem then
            return
        end
        local taskScheme = o.taskItem.taskScheme
        if taskScheme.Recommend > 0 then
            local prefab = HotResManager.ReadUI("UI/Task/prefab/QuickUpgrade")
            local QuickUpgrade = GameObject.Instantiate(prefab, o.gameObject.transform)
            local itemScript = QuickUpgradeChangeType(QuickUpgrade, taskScheme.Recommend, o)
            local anchor = QuickUpgrade.gameObject.transform:Find('Bg').gameObject:GetComponent("Image")
            local anchorTarget = o.gameObject
            -- if tolua.isnull(anchorTarget) then return end
            -- if GameLuaAPI.IsIPhoneX() then
            -- 	anchor:SetAnchor(anchorTarget, 391, 17, 669, 623)
            -- else		
            -- 	anchor:SetAnchor(anchorTarget, 266, -50, 544, 623)
            -- end
            -- anchor.rightAnchor.relative = 0
            -- anchor.bottomAnchor.relative = 0 
            -- anchor.topAnchor.relative = 0 
        elseif taskScheme.RecommendPointTo == 1 then
            UIManager.LoadUI('UIQuickStrength')
        elseif taskScheme.FastSpeakType == 1 then
            -- 组队喊话
            local level = hero.propertyLC:GetProperty(CREATURE_FIELD.CREATURE_FIELD_LEVEL)
            local k, v, lvText = HelperL.CalculatePlayerLoopNum(level)
            local chatText = string.gsub(taskScheme.FastSpeakContent, '%[ActorLevel%]', lvText)
            local tarLvText = string.format(GetGameText(luaID, 3), level)
            chatText = string.gsub(chatText, '%[TarLevel%]', tarLvText)
            local teamID = EntityModule.GetTeamID(hero.uid)

            local makeTeamCallback = function(info)
                UIManager.LoadUI('UITeam')
                -- if UIManager.mainlandUI and UIManager.mainlandUI.uiTaskShowScript then
                -- 	UIManager.mainLandUI.uiTaskShowScript.onClickBtnTeam()
                -- end
            end
            local sendCallBack = function(info)
                local hero = EntityModule.hero
                if not hero then
                    return
                end
                local teamID = EntityModule.GetTeamID(hero.uid)
                if teamID > 0 then
                    local ext = string.format('#~%d~#', teamID)
                    local msg = nil
                    if info.inputField then
                        msg = info.inputField.value .. ext
                    else
                        msg = info.param .. ext
                    end
                    if info.channel == CHAT_CHANNEL.CHAT_CHANNEL_SOCIETY then
                        local society = hero.propertyLC:GetProperty(PLAYER_FIELD.PLAYER_FIELD_SOCIETY)
                        -- 没有帮会弹出提示
                        if society == 0 then
                            local btnInfoList = {}
                            table.insert(btnInfoList, {
                                name = GetGameText(luaID, 11)
                            })
                            table.insert(btnInfoList, {
                                name = GetGameText(luaID, 10),
                                callbackFunc = function()
                                    UIManager.LoadUI("UISocietyCommonWin")
                                    UIManager.SendWndMsg('UISocietyCommonWin', UIWndMsg.UISocietyMainPanel.jumpPagMsg,
                                        'societyList')
                                end
                            })
                            UIManager.AddMessageBOXCommon(GetGameText(luaID, 9), btnInfoList, nil, nil, nil, true)
                        else
                            ChatModule.SendChatMsg(info.channel, msg)
                        end
                    else
                        ChatModule.SendChatMsg(info.channel, msg)
                    end
                else
                    HelperL.ShowMessage(TipType.FlowText, GetGameText(luaID, 4))
                    return true
                end
            end

            local btnInfoList = {}
            table.insert(btnInfoList, {
                name = GetGameText(luaID, 5),
                callbackFunc = makeTeamCallback
            })
            -- table.insert(btnInfoList, { name = GetGameText(luaID, 6), callbackFunc = sendCallBack, param = chatText })
            table.insert(btnInfoList, {
                name = GetGameText(luaID, 7),
                callbackFunc = sendCallBack,
                param = chatText,
                channel = CHAT_CHANNEL.CHAT_CHANNEL_HELP
            })
            table.insert(btnInfoList, {
                name = GetGameText(luaID, 8),
                callbackFunc = sendCallBack,
                param = chatText,
                channel = CHAT_CHANNEL.CHAT_CHANNEL_SOCIETY
            })
            UIManager.AddMessageBOXCommon(chatText, btnInfoList, nil, nil, nil, true)
        end
    end

    function o.PlayFocusAnimation()
        --[[if o.PlayFocusAnimationTimer then
			o.PlayFocusAnimationTimer:Stop()
			o.PlayFocusAnimationTimer:Reset(o.FocusRepeat,2,-1)
		else
			o.PlayFocusAnimationTimer = Timer.New(o.FocusRepeat,2,-1)
		end
		o.PlayFocusAnimationTimer:Start()--]]
        -- if not o.focusAnimation then
        -- 	local temp = HotResManager.ReadUI('ui/Pack/taskFocusRect')
        -- 	o.focusAnimation = GameObject.Instantiate(temp, o.gameObject.transform) 
        -- end
        -- o.focusAnimation:SetActive(true)
    end

    --[[o.FocusRepeat = function()
		if o.focusItems and #o.focusItems < 3 then
			o.focusItems = {}
			local temp = HotResManager.ReadUI('ui/Pack/taskFocusRect')
			for i = 1,3 do
				if not o.focusParent then
					o.focusParent = GameObject.New()
					o.focusParent.layer = Layer.ZenUI
					o.focusParent.transform:SetParent(o.gameObject.transform.parent.parent.parent:Find('FocusPanel/FocusParent'))
					o.focusParent.transform.localPosition = o.gameObject.transform.localPosition
					o.focusParent.transform.localScale = Vector3(1,1,1)
				end
				local tempItem = {}
				local temp2 = GameObject.AddChild(o.focusParent,temp)
				temp2.gameObject:SetActive(false)
				temp2 = temp2:GetComponent('Image')
				temp2.depth = 1
				tempItem.focusSprite = temp2
				tempItem.index = i
				TweenScale.Begin(tempItem.focusSprite.gameObject,0.1,Vector3.one)
				tempItem.focusSprite.width = 268
				tempItem.focusSprite.height = 64
				local twScale = tempItem.focusSprite.gameObject:GetComponent('TweenScale')
				tempItem.twScale = twScale
				TweenAlpha.Begin(tempItem.focusSprite.gameObject, 0.1, 0);
				local twAlpha = tempItem.focusSprite.gameObject:GetComponent('TweenAlpha')
				tempItem.twAlpha = twAlpha
				tempItem.HideSprite = function()
					tempItem.focusSprite.gameObject:SetActive(false)
				end
				tempItem.PlayFocusAnimation = function()
					tempItem.focusSprite.gameObject:SetActive(true)
					tempItem.focusSprite.transform.localScale = o.startScale
					tempItem.twAlpha.from = 0
					tempItem.twAlpha.to = 1
					tempItem.twAlpha.duration = 0.1
					tempItem.twAlpha.delay = tempItem.index  * 0.2
					tempItem.twAlpha:ResetToBeginning()
					tempItem.twAlpha:PlayForward()
					tempItem.twAlpha:SetOnFinished(EventDelegate.Callback( function()
						tempItem.twScale.from = o.startScale
						tempItem.twScale.to = Vector3.one
						tempItem.twScale.duration = 0.3
						tempItem.twScale:ResetToBeginning()
						tempItem.twScale:PlayForward()
					end ))
				end
				tempItem.focusSprite.gameObject:SetActive(false)
				table.insert(o.focusItems,tempItem)
			end
		end
		for i, v in pairs(o.focusItems) do
			v:PlayFocusAnimation()
		end
	end--]]

    function o.StopFocusAnimation()
        --[[if o.PlayFocusAnimationTimer then
			o.PlayFocusAnimationTimer:Stop()
		end
		for i, v in pairs(o.focusItems) do
			v:HideSprite()
		end--]]
        if o.focusAnimation then
            o.focusAnimation:SetActive(false)
        end
    end
    -- 是否显示特效
    function o.CheckCanShowFocusAni(noticeValue, statusValue)
        local isShow = false
        if noticeValue and #noticeValue > 0 and noticeValue[1] > 0 then
            local noticeNum = noticeValue[1]
            if noticeNum == 1 then
                if statusValue == ECTaskStatus.CanAccept then
                    isShow = true
                else
                    isShow = false
                end
            elseif noticeNum == 2 then
                if statusValue == ECTaskStatus.Doing then
                    isShow = true
                else
                    isShow = false
                end
            elseif noticeNum == 3 then
                if statusValue == ECTaskStatus.CanComplete then
                    isShow = true
                else
                    isShow = false
                end
            elseif noticeNum == 4 then
                if statusValue == ECTaskStatus.CanAccept or statusValue == ECTaskStatus.Doing then
                    isShow = true
                else
                    isShow = false
                end
            elseif noticeNum == 5 then
                if statusValue == ECTaskStatus.CanAccept or statusValue == ECTaskStatus.CanComplete then
                    isShow = true
                else
                    isShow = false
                end
            elseif noticeNum == 6 then
                if statusValue == ECTaskStatus.Doing or statusValue == ECTaskStatus.CanComplete then
                    isShow = true
                else
                    isShow = false
                end
            elseif noticeNum == 7 then
                if statusValue == ECTaskStatus.Doing or statusValue == ECTaskStatus.CanComplete or
                    ECTaskStatus.CanAccept then
                    isShow = true
                else
                    isShow = false
                end
            else
                isShow = false
            end
        else
            isShow = false
        end
        return isShow
    end

    o.ShowTipArrow = function(self, isShow)
        if not isShow then
            self.tipArrow:SetActive(false)
            return
        end

        local baseX = 166
        if self.mainTaskPrizePre and self.mainTaskPrizePre.gameObject.activeSelf then
            baseX = 246
        end
        local loc = Vector3(baseX - 4, 0, 0)
        self.tipArrowTween.from = loc
        loc.x = baseX + 4
        self.tipArrowTween.to = loc
        self.tipArrowTween.enabled = true
        self.tipArrow:SetActive(true)
    end

    o.New()
    return o
end
