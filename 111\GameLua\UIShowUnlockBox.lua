﻿
local luaID = ('UIShowUnlockBox')

local UIShowUnlockBox = {}

-- 初始化
function UIShowUnlockBox:OnCreate()
	self.objList.Btn_Close.onClick:AddListenerEx(self.OnClickClose)
	self.objList.Btn_Btn1.onClick:AddListenerEx(self.OnClickBtn1)
	self.objList.Btn_Btn2.onClick:AddListenerEx(self.OnClickBtn2)
	self.objList.Btn_Btn3.onClick:AddListenerEx(self.OnClickBtn3)
	self.btnList = { self.objList.Btn_Close, self.objList.Btn_Btn1, self.objList.Btn_Btn2, self.objList.Btn_Btn3}
	self.prizeContent = self.objList.PrizeContent.transform
	self.itemList = {}
	return true
end

-- 窗口开启
function UIShowUnlockBox:On<PERSON><PERSON>(name, desc, cancelParam, okParam, advertiseParam)
	self.objList.Txt_Title.text = name
	self.objList.Txt_Desc.text = desc
	self.cancelParam = cancelParam
	self.okParam = okParam
	self.advertiseParam = advertiseParam
	self:UpdateView()
end

-- 更新视图 
function UIShowUnlockBox:UpdateView()		
	if not self.cancelParam then
		self.objList.Btn_Btn1.gameObject:SetActive(false)
	else
		self.objList.Btn_Btn1.gameObject:SetActive(true)
		self.objList.Txt_Btn1.text = self.cancelParam.btnName
		if self.cancelParam.icon then
			self.objList.Img_icon1.gameObject:SetActive(true)
		else
			self.objList.Img_icon1.gameObject:SetActive(false)
		end
		self.objList.Txt_PreValue1.text = self.cancelParam.value
		self.objList.Txt_Value1.text = self.cancelParam.desc		
	end
	
	if not self.okParam then
		self.objList.Btn_Btn2.gameObject:SetActive(false)
	else
		self.objList.Btn_Btn2.gameObject:SetActive(true)
		self.objList.Txt_Btn2.text = self.okParam.btnName
		if self.okParam.icon then
			self.objList.Img_icon2.gameObject:SetActive(true)
		else
			self.objList.Img_icon2.gameObject:SetActive(false)
		end
		self.objList.Txt_PreValue2.text = self.okParam.value
		self.objList.Txt_Value2.text = self.okParam.desc	
	end

	if not self.advertiseParam then
		self.objList.Btn_Btn3.gameObject:SetActive(false)
	else
		self.objList.Btn_Btn3.gameObject:SetActive(true)
		self.objList.Txt_Btn3.text = self.advertiseParam.btnName
		self.objList.Txt_PreValue3.text = self.advertiseParam.value
		self.objList.Txt_Value3.text = self.advertiseParam.desc	
	end
	self:OnSecondUpdate()
end

function UIShowUnlockBox.OnClickBtn1()
	local self = UIShowUnlockBox
	self:CloseSelf()
	if self.cancelParam.callback then
		self.cancelParam.callback()
	end	
end

function UIShowUnlockBox.OnClickBtn2()
	local self = UIShowUnlockBox
	self:CloseSelf()	
	if self.okParam.callback then
		self.okParam.callback()
	end
end

function UIShowUnlockBox.OnClickBtn3()
	local self = UIShowUnlockBox
	self:CloseSelf()
	if self.advertiseParam.callback then
		self.advertiseParam.callback(self.advertiseParam.paramArgs)
	end	
end

function UIShowUnlockBox.OnClickClose()
	local self = UIShowUnlockBox
	self:CloseSelf()	
end

function UIShowUnlockBox.OnClickOk()
	local self = UIShowUnlockBox
	self:CloseSelf()
end


-- 窗口关闭
function UIShowUnlockBox:OnClose()
end

-- 窗口销毁
function UIShowUnlockBox:OnDestroy()
	-- 释放内存
	for i, btn in ipairs(self.btnList) do
		btn.onClick:RemoveAllListeners()
		btn.onClick:Invoke()
	end
end

return UIShowUnlockBox