using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;

using Cysharp.Threading.Tasks;

using DataStructure;

using Props;

using Thing;

using UnityEngine;

using X.PB;

namespace View
{
    /// <summary>
    /// ShootMethod=9 圆弧追击子弹的特殊处理逻辑
    /// </summary>
    public static class CircularTrackBullet
    {
        /// <summary>
        /// 圆弧追击半径
        /// </summary>
        public const float CIRCULAR_TRACK_RADIUS = 18.11f;
        
        /// <summary>
        /// V54新增：绕圈一周后检查距离的阈值(像素)
        /// </summary>
        private const float CIRCULAR_DISTANCE_CHECK_THRESHOLD = 50f;
        
        /// <summary>
        /// V54新增：绕圈检查角度(度)
        /// </summary>
        private const float CIRCULAR_CHECK_ANGLE = 350f;
        
        /// <summary>
        /// V50修复：绕圈追击概率 (9999%)
        /// </summary>
        private const float CircularTrackProbability = 0.9999f;
        
        /// <summary>
        /// V50新增：寻找新目标的时间间隔(秒)
        /// </summary>
        private const float FIND_NEW_TARGET_INTERVAL = 0.2f;
        
        /// <summary>
        /// V51新增：新目标寻找范围倍数（相对于攻击距离）
        /// </summary>
        private const float NEW_TARGET_RANGE_MULTIPLIER = 2.0f;
        
        /// <summary>
        /// V52新增：目标中心位置调整的最大允许误差（像素）
        /// </summary>
        private const float TARGET_CENTER_MAX_ERROR = 2.0f;
        
        /// <summary>
        /// V52新增：目标中心位置平滑调整系数
        /// </summary>
        private const float TARGET_CENTER_SMOOTH_FACTOR = 0.3f;
        
        /// <summary>
        /// V54版本修改：处理圆弧追击子弹击中敌人后的逻辑 - 击中后开始绕圈飞行
        /// </summary>
        /// <param name="bulletThing">子弹Thing</param>
        /// <param name="target">被击中的目标</param>
        /// <param name="bulletTransform">子弹Transform</param>
        /// <returns>是否继续追踪当前目标</returns>
        public static bool ProcessHitEnemy(BulletThing bulletThing, MonsterThing target, Transform bulletTransform)
        {
            if (bulletThing == null || target == null) return false;
            
            var gunThing = bulletThing.CdExecutor?.Thing as GunThing;
            var bulletRow = bulletThing.CsvRow_Bullet?.Value;
            
            // 检查穿透次数
            if (bulletThing.PenetrateTimes.Value <= 0)
            {
                // 穿透次数用尽，子弹应该销毁
                bulletThing.ShouldDiscard.Value = true;
                Debug.Log($"=== V54 圆弧追击子弹穿透用尽【销毁】 === 子弹销毁，穿透次数=0");
                return false;
            }
            
            // 减少穿透次数
            bulletThing.PenetrateTimes.Value--;
            
            // 目标已死亡，寻找新目标
            if (target.Hp.Value <= 0)
            {
                Debug.Log($"=== V54 圆弧追击子弹目标死亡 === 寻找新目标，剩余穿透次数:{bulletThing.PenetrateTimes.Value}");
                return FindNewTarget(bulletThing, bulletTransform);
            }
            
            // V54核心修改：击中怪物后开始绕圈飞行
            Debug.Log($"=== V54 圆弧追击子弹击中怪物【开始绕圈】 === 枪ID:{gunThing?.CsvRow_Gun?.Value?.Id} 子弹ID:{bulletRow?.Id} → 目标:{target.CsvRow_BattleBrushEnemy.Id}({target.CsvRow_BattleBrushEnemy.EnemyName}) 穿透剩余:{bulletThing.PenetrateTimes.Value}次");
            
            // 设置绕圈飞行模式
            SetupCircularTrack(bulletThing, target, bulletTransform);
            
            return true;
        }
        
        /// <summary>
        /// V54修改：设置子弹绕目标圆周飞行的参数 - 支持绕圈后检查距离重新直线追击
        /// </summary>
        public static void SetupCircularTrack(BulletThing bulletThing, MonsterThing target, Transform bulletTransform)
        {
            // 设置圆周飞行的参数
            bulletThing.TrackEnemy = target; // 设置追踪目标
            
            // 记录圆周飞行的起始角度和时间
            bulletThing.CircularTrackStartTime = Time.time;
            
            // 计算子弹相对于目标的初始角度
            Vector3 relativePos = bulletTransform.position - target.Position;
            float initialAngle = Mathf.Atan2(relativePos.y, relativePos.x) * Mathf.Rad2Deg;
            bulletThing.CircularTrackStartAngle = initialAngle;
            
            // 设置圆周飞行的半径
            bulletThing.CircularTrackRadius = CIRCULAR_TRACK_RADIUS;
            
            // 统一设置为顺时针方向
            bulletThing.CircularTrackClockwise = true;
            
            // 设置圆周飞行的标志
            bulletThing.IsCircularTracking = true;
            
            // V54重要：重置追击新目标状态
            bulletThing.IsChasingNewTarget = false;
            
            // V54新增：记录绕圈开始时的目标位置，用于距离检查
            bulletThing.CircularTrackTargetStartPos = target.Position;
            
            Debug.Log($"=== V54 圆弧追击子弹设置圆周飞行 === 目标:{target.CsvRow_BattleBrushEnemy.Id} 初始角度:{initialAngle:F1}° 半径:{CIRCULAR_TRACK_RADIUS:F2} 方向:{(bulletThing.CircularTrackClockwise ? "顺时针" : "逆时针")} 开始位置:({target.Position.x:F1},{target.Position.y:F1})");
        }
        
        /// <summary>
        /// V54新增：检查绕圈过程中是否应该切换回直线追击（怪物移动距离过远且绕圈角度足够）
        /// </summary>
        /// <param name="bulletThing">子弹Thing</param>
        /// <param name="target">目标怪物</param>
        /// <returns>是否应该切换到直线追击</returns>
        public static bool ShouldSwitchToDirectChase(BulletThing bulletThing, MonsterThing target)
        {
            if (!bulletThing.IsCircularTracking || target == null) return false;
            
            // 计算当前已绕圈的角度
            float currentTime = Time.time;
            float elapsed = currentTime - bulletThing.CircularTrackStartTime;
            float speed = 60f; // 60度/秒的绕圈角速度
            float currentAngle = elapsed * speed;
            
            // 检查是否已经绕了350度以上
            if (currentAngle < CIRCULAR_CHECK_ANGLE) return false;
            
            // 检查怪物是否移动距离过远
            float targetMovedDistance = Vector3.Distance(target.Position, bulletThing.CircularTrackTargetStartPos);
            
            if (targetMovedDistance > CIRCULAR_DISTANCE_CHECK_THRESHOLD)
            {
                Debug.Log($"=== V54 绕圈检查【切换直线追击】 === 目标:{target.CsvRow_BattleBrushEnemy.Id} 已绕圈角度:{currentAngle:F1}°/{CIRCULAR_CHECK_ANGLE}° 怪物移动距离:{targetMovedDistance:F1}px>{CIRCULAR_DISTANCE_CHECK_THRESHOLD}px 开始位置:({bulletThing.CircularTrackTargetStartPos.x:F1},{bulletThing.CircularTrackTargetStartPos.y:F1}) 当前位置:({target.Position.x:F1},{target.Position.y:F1})");
                return true;
            }
            
            Debug.Log($"=== V54 绕圈检查【继续绕圈】 === 目标:{target.CsvRow_BattleBrushEnemy.Id} 已绕圈角度:{currentAngle:F1}°/{CIRCULAR_CHECK_ANGLE}° 怪物移动距离:{targetMovedDistance:F1}px<={CIRCULAR_DISTANCE_CHECK_THRESHOLD}px");
            return false;
        }
        
        /// <summary>
        /// V54新增：从绕圈模式切换到直线追击模式
        /// </summary>
        /// <param name="bulletThing">子弹Thing</param>
        /// <param name="target">目标怪物</param>
        public static void SwitchToDirectChase(BulletThing bulletThing, MonsterThing target)
        {
            if (bulletThing == null || target == null) return;
            
            // 重置绕圈状态
            bulletThing.IsCircularTracking = false;
            bulletThing.IsChasingNewTarget = true; // 开启直线追击模式
            bulletThing.NewTargetChaseStartTime = Time.time;
            
            Debug.Log($"=== V54 切换到直线追击 === 目标:{target.CsvRow_BattleBrushEnemy.Id} 从绕圈模式切换到直线追击模式");
        }
        
        /// <summary>
        /// V51重构：寻找新的追踪目标 - 扩大范围并设置曲线追击
        /// </summary>
        /// <returns>是否找到新目标</returns>
        public static bool FindNewTarget(BulletThing bulletThing, Transform bulletTransform)
        {
            var gunThing = bulletThing.CdExecutor?.Thing as GunThing;
            if (gunThing == null) return false;
            
            // V51重要优化：使用扩大的寻敌范围（攻击距离的2倍）
            float originalGunRange = gunThing.TotalProp_GunRange;
            float expandedRange = originalGunRange * NEW_TARGET_RANGE_MULTIPLIER;
            
            // 在扩大范围内寻找敌人
            var monsters = SingletonMgr.Instance.BattleMgr.FindMonster(
                bulletTransform.position, 
                0f, // 子弹半径设为0，使用目标位置直接计算
                gunThing.CsvRow_Gun.Value.FindActionTarget, 
                expandedRange);
            
            if (monsters != null && monsters.Count > 0)
            {
                // 选择最近的活着的目标
                var closestTarget = monsters
                    .Where(m => m.Thing2 is MonsterThing monster && monster.Hp.Value > 0)
                    .OrderBy(m => m.Distance)
                    .FirstOrDefault();
                
                if (closestTarget?.Thing2 is MonsterThing newMonster)
                {
                    // V54重要修改：设置直线追击而不是曲线追击
                    bulletThing.TrackEnemy = newMonster;
                    bulletThing.IsCircularTracking = false; // 重要：不立即开始绕圈
                    bulletThing.IsChasingNewTarget = true; // V54修改：标记为直线追击新目标状态
                    bulletThing.NewTargetChaseStartTime = Time.time; // 记录开始追击时间
                    
                    Debug.Log($"=== V54 圆弧追击子弹找到新目标【直线追击】 === 新目标:{newMonster.CsvRow_BattleBrushEnemy.Id}({newMonster.CsvRow_BattleBrushEnemy.EnemyName}) 距离:{closestTarget.Distance:F1}px 血量:{newMonster.Hp.Value:F0} 寻敌范围:{expandedRange:F1}px(原始:{originalGunRange:F1}px)");
                    return true;
                }
            }
            
            // V50修复：没找到新目标时不立即清除追踪状态，改为设置无目标寻找状态
            bulletThing.TrackEnemy = null;
            bulletThing.IsCircularTracking = false;
            bulletThing.IsChasingNewTarget = false;
            
            // V50新增：设置寻找新目标的时间戳
            bulletThing.LastFindTargetTime = Time.time;
            
            Debug.Log($"=== V51 圆弧追击子弹寻找新目标失败 === 进入无目标寻找模式，每{FIND_NEW_TARGET_INTERVAL}秒重试，寻敌范围:{expandedRange:F1}px");
            return false;
        }
        
        /// <summary>
        /// V50新增：定期寻找新目标（用于无目标状态）
        /// </summary>
        public static bool TryFindNewTargetPeriodically(BulletThing bulletThing, Transform bulletTransform)
        {
            // 检查是否到了寻找新目标的时间
            if (Time.time - bulletThing.LastFindTargetTime < FIND_NEW_TARGET_INTERVAL)
            {
                return false; // 还没到时间
            }
            
            // 更新寻找时间戳
            bulletThing.LastFindTargetTime = Time.time;
            
            // 使用相同的扩大寻敌范围逻辑
            return FindNewTarget(bulletThing, bulletTransform);
        }
        
        /// <summary>
        /// V54修改：计算直线追击新目标的移动方向 - 直接朝目标直线飞行
        /// </summary>
        public static Vector3 CalculateChaseNewTargetDirection(BulletThing bulletThing, MonsterThing target, Vector3 currentPos)
        {
            if (target == null || !bulletThing.IsChasingNewTarget) return Vector3.right;
            
            // V54核心修改：直接朝目标直线飞行，不做任何曲线转向
            Vector3 targetPos = target.Position;
            Vector3 targetDir = (targetPos - currentPos).normalized;
            
            Debug.Log($"=== V54 直线追击新目标 === 目标:{target.CsvRow_BattleBrushEnemy.Id} 距离:{Vector3.Distance(currentPos, targetPos):F1}px 当前位置:({currentPos.x:F1},{currentPos.y:F1}) 目标位置:({targetPos.x:F1},{targetPos.y:F1})");
            
            return targetDir;
        }
        

        
        /// <summary>
        /// 计算圆周飞行的下一个位置
        /// </summary>
        public static Vector3 CalculateCircularPosition(BulletThing bulletThing, MonsterThing target, float duration)
        {
            if (!bulletThing.IsCircularTracking || target == null) return Vector3.zero;
            
            // 计算已经飞行的时间
            float elapsedTime = Time.time - bulletThing.CircularTrackStartTime;
            
            // 计算角速度 (度/秒)，完成一圈大约需要2秒
            float angularSpeed = 180f; // 每秒180度，2秒一圈
            
            // 计算当前角度
            float currentAngle = bulletThing.CircularTrackStartAngle;
            if (bulletThing.CircularTrackClockwise)
            {
                currentAngle -= angularSpeed * elapsedTime; // 顺时针减少角度
            }
            else
            {
                currentAngle += angularSpeed * elapsedTime; // 逆时针增加角度
            }
            
            // 转换为弧度
            float angleRad = currentAngle * Mathf.Deg2Rad;
            
            // 计算圆周上的位置 (相对于目标)
            float x = Mathf.Cos(angleRad) * bulletThing.CircularTrackRadius;
            float y = Mathf.Sin(angleRad) * bulletThing.CircularTrackRadius;
            
            // 返回世界坐标 (目标位置 + 相对位置)
            return target.Position + new Vector3(x, y, 0);
        }
        
        /// <summary>
        /// 计算圆周飞行的移动方向
        /// </summary>
        public static Vector3 CalculateCircularMoveDirection(BulletThing bulletThing, MonsterThing target, Vector3 currentPos)
        {
            if (!bulletThing.IsCircularTracking || target == null) return Vector3.right;
            
            // 计算已经飞行的时间
            float elapsedTime = Time.time - bulletThing.CircularTrackStartTime;
            
            // 计算角速度 (度/秒)
            float angularSpeed = 180f; // 每秒180度，2秒一圈
            
            // 计算当前角度
            float currentAngle = bulletThing.CircularTrackStartAngle;
            if (bulletThing.CircularTrackClockwise)
            {
                currentAngle -= angularSpeed * elapsedTime; // 顺时针减少角度
            }
            else
            {
                currentAngle += angularSpeed * elapsedTime; // 逆时针增加角度
            }
            
            // 计算切线方向 (垂直于半径方向)
            float tangentAngle = currentAngle + (bulletThing.CircularTrackClockwise ? -90f : 90f);
            float tangentRad = tangentAngle * Mathf.Deg2Rad;
            
            // 计算切线方向向量
            return new Vector3(Mathf.Cos(tangentRad), Mathf.Sin(tangentRad), 0).normalized;
        }
        
        /// <summary>
        /// V50新增：计算无目标时的绕圈飞行方向（固定半径绕圈）
        /// </summary>
        public static Vector3 CalculateNoTargetCircularDirection(BulletThing bulletThing, Vector3 currentPos)
        {
            // V50：无目标时按固定半径绕圈飞行，速度始终是圆的切线方向
            if (!bulletThing.NoTargetCircularCenter.HasValue)
            {
                // 第一次进入无目标状态，以当前位置为圆心
                bulletThing.NoTargetCircularCenter = currentPos;
                bulletThing.NoTargetCircularStartTime = Time.time;
                bulletThing.NoTargetCircularStartAngle = 0f; // 从0度开始
                Debug.Log($"=== V51 无目标绕圈初始化 === 圆心:({currentPos.x:F1},{currentPos.y:F1}) 半径:{CIRCULAR_TRACK_RADIUS:F2}");
            }
            
            // 计算已经飞行的时间
            float elapsedTime = Time.time - bulletThing.NoTargetCircularStartTime;
            
            // 计算角速度 (度/秒)，与有目标时相同
            float angularSpeed = 180f; // 每秒180度，2秒一圈
            
            // 计算当前角度
            float currentAngle = bulletThing.NoTargetCircularStartAngle + angularSpeed * elapsedTime;
            
            // 计算切线方向 (垂直于半径方向)
            float tangentAngle = currentAngle + 90f; // 逆时针方向
            float tangentRad = tangentAngle * Mathf.Deg2Rad;
            
            // 计算切线方向向量
            return new Vector3(Mathf.Cos(tangentRad), Mathf.Sin(tangentRad), 0).normalized;
        }
        
        /// <summary>
        /// V52新增：获取目标的真实中心位置
        /// </summary>
        /// <param name="target">目标怪物</param>
        /// <returns>目标的中心世界坐标</returns>
        public static Vector3 GetTargetCenterPosition(MonsterThing target)
        {
            if (target?.ThingBehaviour?.transform == null)
            {
                return target?.Position ?? Vector3.zero;
            }
            
            // V52重要：使用Transform位置作为最准确的中心位置
            // 因为Transform.position通常是预制体的pivot点，更接近视觉中心
            Vector3 transformCenter = target.ThingBehaviour.transform.position;
            
            // 获取怪物的碰撞体信息来进一步精确中心位置
            var collider = target.ThingBehaviour.GetComponent<Collider2D>();
            if (collider != null)
            {
                // 使用碰撞体的bounds中心，这是最准确的物理中心
                Vector3 colliderCenter = collider.bounds.center;
                Debug.Log($"=== V52 目标中心计算 === 目标:{target.CsvRow_BattleBrushEnemy.Id} Transform:({transformCenter.x:F2},{transformCenter.y:F2}) Collider:({colliderCenter.x:F2},{colliderCenter.y:F2}) Position:({target.Position.x:F2},{target.Position.y:F2})");
                return colliderCenter;
            }
            
            // 备选方案：使用Transform位置
            Debug.Log($"=== V52 目标中心计算(备选) === 目标:{target.CsvRow_BattleBrushEnemy.Id} 使用Transform:({transformCenter.x:F2},{transformCenter.y:F2})");
            return transformCenter;
        }
        
        /// <summary>
        /// V52新增：检查并调整子弹位置，确保绕目标中心飞行
        /// </summary>
        /// <param name="bulletThing">子弹Thing</param>
        /// <param name="target">目标怪物</param>
        /// <param name="currentPos">子弹当前位置</param>
        /// <returns>调整后的子弹位置</returns>
        public static Vector3 AdjustPositionToTargetCenter(BulletThing bulletThing, MonsterThing target, Vector3 currentPos)
        {
            if (!bulletThing.IsCircularTracking || target == null) return currentPos;
            
            // 获取目标的真实中心位置
            Vector3 targetCenter = GetTargetCenterPosition(target);
            
            // 计算期望的圆周位置
            Vector3 expectedPosition = CalculateExpectedCircularPosition(bulletThing, targetCenter);
            
            // 计算当前位置与期望位置的偏差
            float positionError = Vector3.Distance(currentPos, expectedPosition);
            
            // V52：如果偏差超过允许范围，进行平滑调整
            if (positionError > TARGET_CENTER_MAX_ERROR)
            {
                // 平滑调整到正确位置
                Vector3 adjustedPos = Vector3.Lerp(currentPos, expectedPosition, TARGET_CENTER_SMOOTH_FACTOR);
                Debug.Log($"=== V52 位置调整 === 目标:{target.CsvRow_BattleBrushEnemy.Id} 偏差:{positionError:F2}px > {TARGET_CENTER_MAX_ERROR}px 当前:({currentPos.x:F1},{currentPos.y:F1}) 期望:({expectedPosition.x:F1},{expectedPosition.y:F1}) 调整:({adjustedPos.x:F1},{adjustedPos.y:F1})");
                return adjustedPos;
            }
            
            return currentPos;
        }
        
        /// <summary>
        /// V52新增：计算基于目标中心的期望圆周位置
        /// </summary>
        /// <param name="bulletThing">子弹Thing</param>
        /// <param name="targetCenter">目标中心位置</param>
        /// <returns>期望的圆周位置</returns>
        private static Vector3 CalculateExpectedCircularPosition(BulletThing bulletThing, Vector3 targetCenter)
        {
            // 计算已经飞行的时间
            float elapsedTime = Time.time - bulletThing.CircularTrackStartTime;
            
            // 计算角速度 (度/秒)
            float angularSpeed = 180f; // 每秒180度，2秒一圈
            
            // 计算当前角度
            float currentAngle = bulletThing.CircularTrackStartAngle;
            if (bulletThing.CircularTrackClockwise)
            {
                currentAngle -= angularSpeed * elapsedTime; // 顺时针减少角度
            }
            else
            {
                currentAngle += angularSpeed * elapsedTime; // 逆时针增加角度
            }
            
            // 转换为弧度
            float angleRad = currentAngle * Mathf.Deg2Rad;
            
            // 计算圆周上的位置 (相对于目标中心)
            float x = Mathf.Cos(angleRad) * bulletThing.CircularTrackRadius;
            float y = Mathf.Sin(angleRad) * bulletThing.CircularTrackRadius;
            
            // 返回基于目标中心的世界坐标
            return targetCenter + new Vector3(x, y, 0);
        }
    }
}