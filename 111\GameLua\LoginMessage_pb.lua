-- Generated By protoc-gen-lua Do not Edit
local protobuf = require "protobuf/protobuf"
module('LoginMessage_pb')
local pb = {}


pb.MSG_LOGIN_ACTIONID = protobuf.EnumDescriptor();
pb.MSG_LOGIN_ACTIONID_MSG_LOGIN_NONE_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_LOGIN_ACTIONID_MSG_LOGIN_REGISTER_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_LOGIN_ACTIONID_MSG_LOGIN_LOGINUSER_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_LOGIN_ACTIONID_MSG_LOGIN_CREATEACTOR_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_LOGIN_ACTIONID_MSG_LOGIN_DELETEACTOR_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_LOGIN_ACTIONID_MSG_LOGIN_SELETEACTOR_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_LOGIN_ACTIONID_MSG_LOGIN_RECONNECT_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_LOGIN_ACTIONID_MSG_LOGIN_ADD_RECONNECT_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_LOGIN_ACTIONID_MSG_LOGIN_DEL_RECONNECT_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_LOGIN_ACTIONID_MSG_LOGIN_LOGINUSER_UC_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_LOGIN_ACTIONID_MSG_LOGIN_SWITCHACTOR_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_LOGIN_ACTIONID_MSG_LOGIN_RECONNECT_UC_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_LOGIN_ACTIONID_MSG_LOGIN_LOGINUSER_MI_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_LOGIN_ACTIONID_MSG_LOGIN_RECONNECT_MI_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_LOGIN_ACTIONID_MSG_LOGIN_LOGINUSER_ZS_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_LOGIN_ACTIONID_MSG_LOGIN_RECONNECT_ZS_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_LOGIN_ACTIONID_MSG_LOGIN_LOGOUTUSER_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_LOGIN_ACTIONID_MSG_LOGIN_LOGINUSER_SDK_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_LOGIN_ACTIONID_MSG_LOGIN_LOGINUSER_VIVOSDK_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_LOGIN_ACTIONID_MSG_LOGIN_LOGINUSER_AMIGOSDK_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_LOGIN_ACTIONID_MSG_LOGIN_LOGINUSER_LENOVOSDK_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_LOGIN_ACTIONID_MSG_LOGIN_LOGINUSER_HUAWEISDK_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_LOGIN_ACTIONID_MSG_LOGIN_LOGINUSER_YIJIESDK_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_LOGIN_ACTIONID_MSG_LOGIN_LOGINUSER_IOS_13YX_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_LOGIN_ACTIONID_MSG_LOGIN_LOGINUSER_6KW_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_LOGIN_ACTIONID_MSG_LOGIN_LOGINUSER_669_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_LOGIN_ACTIONID_MSG_LOGIN_LOGINUSER_COM_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_LOGIN_PLAYER_STATE = protobuf.EnumDescriptor();
pb.MSG_LOGIN_PLAYER_STATE_MSG_LOGIN_PLAYER_STATE_UNKNOWN_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_LOGIN_PLAYER_STATE_MSG_LOGIN_PLAYER_STATE_CREATE_ACTOR_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_LOGIN_PLAYER_STATE_MSG_LOGIN_PLAYER_STATE_SELECT_ACTOR_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_LOGIN_PLAYER_STATE_MSG_LOGIN_PLAYER_STATE_PLAYING_ENUM = protobuf.EnumValueDescriptor();
pb.CL_LOGIN_REGISTER = protobuf.Descriptor();
pb.CL_LOGIN_REGISTER_USERNAME_FIELD = protobuf.FieldDescriptor();
pb.CL_LOGIN_REGISTER_PASSWORD_FIELD = protobuf.FieldDescriptor();
pb.LC_LOGIN_REGISTER = protobuf.Descriptor();
pb.LC_LOGIN_REGISTER_RESULT_FIELD = protobuf.FieldDescriptor();
pb.CL_LOGIN_LOGINUSER = protobuf.Descriptor();
pb.CL_LOGIN_LOGINUSER_USERNAME_FIELD = protobuf.FieldDescriptor();
pb.CL_LOGIN_LOGINUSER_PASSWORD_FIELD = protobuf.FieldDescriptor();
pb.CL_LOGIN_LOGINUSER_LOGINID_FIELD = protobuf.FieldDescriptor();
pb.SC_LOGIN_LOGINUSER = protobuf.Descriptor();
pb.SC_LOGIN_LOGINUSER_ACTORINFO = protobuf.Descriptor();
pb.SC_LOGIN_LOGINUSER_ACTORINFO_ACTORID_FIELD = protobuf.FieldDescriptor();
pb.SC_LOGIN_LOGINUSER_ACTORINFO_ACTORNAME_FIELD = protobuf.FieldDescriptor();
pb.SC_LOGIN_LOGINUSER_ACTORINFO_LEVEL_FIELD = protobuf.FieldDescriptor();
pb.SC_LOGIN_LOGINUSER_ACTORINFO_VOCATION_FIELD = protobuf.FieldDescriptor();
pb.SC_LOGIN_LOGINUSER_ACTORINFO_COUNTRY_FIELD = protobuf.FieldDescriptor();
pb.SC_LOGIN_LOGINUSER_ACTORINFO_EQUIPMODEID_FIELD = protobuf.FieldDescriptor();
pb.SC_LOGIN_LOGINUSER_ACTORINFO_WEAPONID_FIELD = protobuf.FieldDescriptor();
pb.SC_LOGIN_LOGINUSER_ACTORINFO_WINGID_FIELD = protobuf.FieldDescriptor();
pb.SC_LOGIN_LOGINUSER_ACTORINFO_NEWWINGID_FIELD = protobuf.FieldDescriptor();
pb.SC_LOGIN_LOGINUSER_RESULT_FIELD = protobuf.FieldDescriptor();
pb.SC_LOGIN_LOGINUSER_ACTORLIST_FIELD = protobuf.FieldDescriptor();
pb.SC_LOGIN_LOGINUSER_SERVERTIME_FIELD = protobuf.FieldDescriptor();
pb.SC_LOGIN_LOGINUSER_USERNAME_FIELD = protobuf.FieldDescriptor();
pb.SC_LOGIN_LOGINUSER_CURCACHEACTORID_FIELD = protobuf.FieldDescriptor();
pb.CL_LOGIN_CREATEACTOR = protobuf.Descriptor();
pb.CL_LOGIN_CREATEACTOR_ACTORNAME_FIELD = protobuf.FieldDescriptor();
pb.CL_LOGIN_CREATEACTOR_VOCATION_FIELD = protobuf.FieldDescriptor();
pb.CL_LOGIN_CREATEACTOR_COUNTRY_FIELD = protobuf.FieldDescriptor();
pb.LC_LOGIN_CREATEACTOR = protobuf.Descriptor();
pb.LC_LOGIN_CREATEACTOR_ACTORINFO = protobuf.Descriptor();
pb.LC_LOGIN_CREATEACTOR_ACTORINFO_ACTORID_FIELD = protobuf.FieldDescriptor();
pb.LC_LOGIN_CREATEACTOR_ACTORINFO_ACTORNAME_FIELD = protobuf.FieldDescriptor();
pb.LC_LOGIN_CREATEACTOR_ACTORINFO_LEVEL_FIELD = protobuf.FieldDescriptor();
pb.LC_LOGIN_CREATEACTOR_ACTORINFO_VOCATION_FIELD = protobuf.FieldDescriptor();
pb.LC_LOGIN_CREATEACTOR_ACTORINFO_COUNTRY_FIELD = protobuf.FieldDescriptor();
pb.LC_LOGIN_CREATEACTOR_RESULT_FIELD = protobuf.FieldDescriptor();
pb.LC_LOGIN_CREATEACTOR_NEWACTOR_FIELD = protobuf.FieldDescriptor();
pb.CL_LOGIN_DELETEACTOR = protobuf.Descriptor();
pb.CL_LOGIN_DELETEACTOR_ACTORID_FIELD = protobuf.FieldDescriptor();
pb.CL_LOGIN_DELETEACTOR_ACTORNAME_FIELD = protobuf.FieldDescriptor();
pb.LC_LOGIN_DELETEACTOR = protobuf.Descriptor();
pb.LC_LOGIN_DELETEACTOR_RESULT_FIELD = protobuf.FieldDescriptor();
pb.LC_LOGIN_DELETEACTOR_ACTORID_FIELD = protobuf.FieldDescriptor();
pb.CL_LOGIN_SELETEACTOR = protobuf.Descriptor();
pb.CL_LOGIN_SELETEACTOR_ACTORID_FIELD = protobuf.FieldDescriptor();
pb.CL_LOGIN_SELETEACTOR_ACTORNAME_FIELD = protobuf.FieldDescriptor();
pb.CL_LOGIN_SELETEACTOR_USERID_FIELD = protobuf.FieldDescriptor();
pb.CL_LOGIN_SELETEACTOR_ZONEID_FIELD = protobuf.FieldDescriptor();
pb.CL_LOGIN_SELETEACTOR_TOKEN_FIELD = protobuf.FieldDescriptor();
pb.LC_LOGIN_SELETEACTOR = protobuf.Descriptor();
pb.LC_LOGIN_SELETEACTOR_RESULT_FIELD = protobuf.FieldDescriptor();
pb.LC_LOGIN_SELETEACTOR_MODE_FIELD = protobuf.FieldDescriptor();
pb.LS_LOGIN_SELETEACTOR = protobuf.Descriptor();
pb.LS_LOGIN_SELETEACTOR_CLIENTID_FIELD = protobuf.FieldDescriptor();
pb.LS_LOGIN_SELETEACTOR_USERID_FIELD = protobuf.FieldDescriptor();
pb.LS_LOGIN_SELETEACTOR_ACTORID_FIELD = protobuf.FieldDescriptor();
pb.LS_LOGIN_SELETEACTOR_ACTORNAME_FIELD = protobuf.FieldDescriptor();
pb.CL_LOGIN_RECONNECT = protobuf.Descriptor();
pb.CL_LOGIN_RECONNECT_USERNAME_FIELD = protobuf.FieldDescriptor();
pb.CL_LOGIN_RECONNECT_PASSWORD_FIELD = protobuf.FieldDescriptor();
pb.CL_LOGIN_RECONNECT_LOGINID_FIELD = protobuf.FieldDescriptor();
pb.CL_LOGIN_RECONNECT_STATE_FIELD = protobuf.FieldDescriptor();
pb.CL_LOGIN_RECONNECT_UC = protobuf.Descriptor();
pb.CL_LOGIN_RECONNECT_UC_SID_FIELD = protobuf.FieldDescriptor();
pb.CL_LOGIN_RECONNECT_UC_LOGINID_FIELD = protobuf.FieldDescriptor();
pb.CL_LOGIN_RECONNECT_UC_STATE_FIELD = protobuf.FieldDescriptor();
pb.CL_LOGIN_RECONNECT_UC_SIDMD5_FIELD = protobuf.FieldDescriptor();
pb.CL_LOGIN_RECONNECT_UC_DEBUGMODE_FIELD = protobuf.FieldDescriptor();
pb.LC_LOGIN_RECONNECT = protobuf.Descriptor();
pb.LC_LOGIN_RECONNECT_RESULT_FIELD = protobuf.FieldDescriptor();
pb.SL_LOGIN_ADDRECONNECT = protobuf.Descriptor();
pb.SL_LOGIN_ADDRECONNECT_USERID_FIELD = protobuf.FieldDescriptor();
pb.SL_LOGIN_ADDRECONNECT_ACTORID_FIELD = protobuf.FieldDescriptor();
pb.SL_LOGIN_DELRECONNECT = protobuf.Descriptor();
pb.SL_LOGIN_DELRECONNECT_USERID_FIELD = protobuf.FieldDescriptor();
pb.LS_LOGIN_RECONNECT = protobuf.Descriptor();
pb.LS_LOGIN_RECONNECT_USERID_FIELD = protobuf.FieldDescriptor();
pb.LS_LOGIN_RECONNECT_NEWCLIENTID_FIELD = protobuf.FieldDescriptor();
pb.CL_LOGIN_LOGINUSER_UC = protobuf.Descriptor();
pb.CL_LOGIN_LOGINUSER_UC_ZONEID_FIELD = protobuf.FieldDescriptor();
pb.CL_LOGIN_LOGINUSER_UC_SID_FIELD = protobuf.FieldDescriptor();
pb.CL_LOGIN_LOGINUSER_UC_SIDMD5_FIELD = protobuf.FieldDescriptor();
pb.CL_LOGIN_LOGINUSER_UC_DEBUGMODE_FIELD = protobuf.FieldDescriptor();
pb.CL_LOGIN_LOGINUSER_UC_TS_FIELD = protobuf.FieldDescriptor();
pb.CL_LOGIN_LOGINUSER_UC_UUID_FIELD = protobuf.FieldDescriptor();
pb.CL_LOGIN_SWITCHACTOR = protobuf.Descriptor();
pb.CL_LOGIN_SWITCHACTOR_ACTORID_FIELD = protobuf.FieldDescriptor();
pb.CL_LOGIN_SWITCHACTOR_ACTORNAME_FIELD = protobuf.FieldDescriptor();
pb.LC_LOGIN_SWITCHACTOR = protobuf.Descriptor();
pb.LC_LOGIN_SWITCHACTOR_RESULT_FIELD = protobuf.FieldDescriptor();
pb.CL_LOGIN_LOGINUSER_MI = protobuf.Descriptor();
pb.CL_LOGIN_LOGINUSER_MI_ZONEID_FIELD = protobuf.FieldDescriptor();
pb.CL_LOGIN_LOGINUSER_MI_UID_FIELD = protobuf.FieldDescriptor();
pb.CL_LOGIN_LOGINUSER_MI_SID_FIELD = protobuf.FieldDescriptor();
pb.CL_LOGIN_RECONNECT_MI = protobuf.Descriptor();
pb.CL_LOGIN_RECONNECT_MI_ZONEID_FIELD = protobuf.FieldDescriptor();
pb.CL_LOGIN_RECONNECT_MI_UID_FIELD = protobuf.FieldDescriptor();
pb.CL_LOGIN_RECONNECT_MI_SID_FIELD = protobuf.FieldDescriptor();
pb.CL_LOGIN_RECONNECT_MI_STATE_FIELD = protobuf.FieldDescriptor();
pb.CL_LOGIN_LOGINUSER_ZS = protobuf.Descriptor();
pb.CL_LOGIN_LOGINUSER_ZS_ZONEID_FIELD = protobuf.FieldDescriptor();
pb.CL_LOGIN_LOGINUSER_ZS_UID_FIELD = protobuf.FieldDescriptor();
pb.CL_LOGIN_LOGINUSER_ZS_SID_FIELD = protobuf.FieldDescriptor();
pb.CL_LOGIN_RECONNECT_ZS = protobuf.Descriptor();
pb.CL_LOGIN_RECONNECT_ZS_ZONEID_FIELD = protobuf.FieldDescriptor();
pb.CL_LOGIN_RECONNECT_ZS_UID_FIELD = protobuf.FieldDescriptor();
pb.CL_LOGIN_RECONNECT_ZS_SID_FIELD = protobuf.FieldDescriptor();
pb.CL_LOGIN_RECONNECT_ZS_STATE_FIELD = protobuf.FieldDescriptor();
pb.CL_LOGIN_LOGOUTUSER = protobuf.Descriptor();
pb.CL_LOGIN_LOGOUTUSER_ACTORID_FIELD = protobuf.FieldDescriptor();
pb.CL_LOGIN_LOGOUTUSER_ACTORNAME_FIELD = protobuf.FieldDescriptor();
pb.LC_LOGIN_LOGOUTUSER = protobuf.Descriptor();
pb.LC_LOGIN_LOGOUTUSER_RESULT_FIELD = protobuf.FieldDescriptor();
pb.LS_LOGIN_LOGOUTUSER = protobuf.Descriptor();
pb.LS_LOGIN_LOGOUTUSER_CLIENTID_FIELD = protobuf.FieldDescriptor();
pb.LS_LOGIN_LOGOUTUSER_USERID_FIELD = protobuf.FieldDescriptor();
pb.LS_LOGIN_LOGOUTUSER_ACTORID_FIELD = protobuf.FieldDescriptor();
pb.LS_LOGIN_LOGOUTUSER_USERNAME_FIELD = protobuf.FieldDescriptor();
pb.LS_LOGIN_LOGOUTUSER_ACTORNAME_FIELD = protobuf.FieldDescriptor();
pb.CL_LOGIN_LOGINUSER_SDK = protobuf.Descriptor();
pb.CL_LOGIN_LOGINUSER_SDK_ZONEID_FIELD = protobuf.FieldDescriptor();
pb.CL_LOGIN_LOGINUSER_SDK_CHANNELID_FIELD = protobuf.FieldDescriptor();
pb.CL_LOGIN_LOGINUSER_SDK_UID_FIELD = protobuf.FieldDescriptor();
pb.LD_LOGIN_LOGINUSER_SDK = protobuf.Descriptor();
pb.LD_LOGIN_LOGINUSER_SDK_ZONEID_FIELD = protobuf.FieldDescriptor();
pb.LD_LOGIN_LOGINUSER_SDK_USERID_FIELD = protobuf.FieldDescriptor();
pb.LD_LOGIN_LOGINUSER_SDK_ACTORID_FIELD = protobuf.FieldDescriptor();
pb.LD_LOGIN_LOGINUSER_SDK_ACTORNAME_FIELD = protobuf.FieldDescriptor();
pb.LD_LOGIN_LOGINUSER_SDK_CLIENTUID_FIELD = protobuf.FieldDescriptor();
pb.LD_LOGIN_LOGINUSER_SDK_IP_FIELD = protobuf.FieldDescriptor();
pb.LD_LOGIN_LOGINUSER_SDK_DEVMODEL_FIELD = protobuf.FieldDescriptor();
pb.LD_LOGIN_LOGINUSER_SDK_LOGINID_FIELD = protobuf.FieldDescriptor();
pb.CL_LOGIN_LOGINUSER_VIVO = protobuf.Descriptor();
pb.CL_LOGIN_LOGINUSER_VIVO_UID_FIELD = protobuf.FieldDescriptor();
pb.CL_LOGIN_LOGINUSER_VIVO_AUTHTOKEN_FIELD = protobuf.FieldDescriptor();
pb.CL_LOGIN_LOGINUSER_VIVO_USERNAME_FIELD = protobuf.FieldDescriptor();
pb.CL_LOGIN_LOGINUSER_VIVO_CHANNELID_FIELD = protobuf.FieldDescriptor();
pb.CL_LOGIN_LOGINUSER_VIVO_ZONEID_FIELD = protobuf.FieldDescriptor();
pb.CL_LOGIN_LOGINUSER_VIVO_UUID_FIELD = protobuf.FieldDescriptor();
pb.CL_LOGIN_LOGINUSER_OPPO = protobuf.Descriptor();
pb.CL_LOGIN_LOGINUSER_OPPO_SSOID_FIELD = protobuf.FieldDescriptor();
pb.CL_LOGIN_LOGINUSER_OPPO_TOKEN_FIELD = protobuf.FieldDescriptor();
pb.CL_LOGIN_LOGINUSER_OPPO_UID_FIELD = protobuf.FieldDescriptor();
pb.CL_LOGIN_LOGINUSER_OPPO_ZONEID_FIELD = protobuf.FieldDescriptor();
pb.CL_LOGIN_LOGINUSER_AMIGO = protobuf.Descriptor();
pb.CL_LOGIN_LOGINUSER_AMIGO_UID_FIELD = protobuf.FieldDescriptor();
pb.CL_LOGIN_LOGINUSER_AMIGO_AUTHTOKEN_FIELD = protobuf.FieldDescriptor();
pb.CL_LOGIN_LOGINUSER_AMIGO_USERNAME_FIELD = protobuf.FieldDescriptor();
pb.CL_LOGIN_LOGINUSER_AMIGO_CHANNELID_FIELD = protobuf.FieldDescriptor();
pb.CL_LOGIN_LOGINUSER_AMIGO_ZONEID_FIELD = protobuf.FieldDescriptor();
pb.CL_LOGIN_LOGINUSER_AMIGO_UUID_FIELD = protobuf.FieldDescriptor();
pb.CL_LOGIN_LOGINUSER_LENOVO = protobuf.Descriptor();
pb.CL_LOGIN_LOGINUSER_LENOVO_UID_FIELD = protobuf.FieldDescriptor();
pb.CL_LOGIN_LOGINUSER_LENOVO_AUTHTOKEN_FIELD = protobuf.FieldDescriptor();
pb.CL_LOGIN_LOGINUSER_LENOVO_CHANNELID_FIELD = protobuf.FieldDescriptor();
pb.CL_LOGIN_LOGINUSER_LENOVO_ZONEID_FIELD = protobuf.FieldDescriptor();
pb.CL_LOGIN_LOGINUSER_LENOVO_UUID_FIELD = protobuf.FieldDescriptor();
pb.CL_LOGIN_LOGINUSER_HUAWEI = protobuf.Descriptor();
pb.CL_LOGIN_LOGINUSER_HUAWEI_UID_FIELD = protobuf.FieldDescriptor();
pb.CL_LOGIN_LOGINUSER_HUAWEI_AUTHTOKEN_FIELD = protobuf.FieldDescriptor();
pb.CL_LOGIN_LOGINUSER_HUAWEI_CHANNELID_FIELD = protobuf.FieldDescriptor();
pb.CL_LOGIN_LOGINUSER_HUAWEI_ZONEID_FIELD = protobuf.FieldDescriptor();
pb.CL_LOGIN_LOGINUSER_HUAWEI_TS_FIELD = protobuf.FieldDescriptor();
pb.CL_LOGIN_LOGINUSER_HUAWEI_LEVEL_FIELD = protobuf.FieldDescriptor();
pb.CL_LOGIN_LOGINUSER_HUAWEI_UUID_FIELD = protobuf.FieldDescriptor();
pb.CL_LOGIN_LOGINUSER_SDK_NEW = protobuf.Descriptor();
pb.CL_LOGIN_LOGINUSER_SDK_NEW_UID_FIELD = protobuf.FieldDescriptor();
pb.CL_LOGIN_LOGINUSER_SDK_NEW_AUTHTOKEN_FIELD = protobuf.FieldDescriptor();
pb.CL_LOGIN_LOGINUSER_SDK_NEW_CHANNELID_FIELD = protobuf.FieldDescriptor();
pb.CL_LOGIN_LOGINUSER_SDK_NEW_ZONEID_FIELD = protobuf.FieldDescriptor();
pb.CL_LOGIN_LOGINUSER_SDK_NEW_UUID_FIELD = protobuf.FieldDescriptor();
pb.CL_LOGIN_LOGINUSER_SDK_NEW_SDKID_FIELD = protobuf.FieldDescriptor();
pb.CL_LOGIN_LOGINUSER_6KW = protobuf.Descriptor();
pb.CL_LOGIN_LOGINUSER_6KW_UID_FIELD = protobuf.FieldDescriptor();
pb.CL_LOGIN_LOGINUSER_6KW_AUTHTOKEN_FIELD = protobuf.FieldDescriptor();
pb.CL_LOGIN_LOGINUSER_6KW_CHANNELID_FIELD = protobuf.FieldDescriptor();
pb.CL_LOGIN_LOGINUSER_6KW_ZONEID_FIELD = protobuf.FieldDescriptor();
pb.CL_LOGIN_LOGINUSER_6KW_UUID_FIELD = protobuf.FieldDescriptor();
pb.CL_LOGIN_LOGINUSER_669 = protobuf.Descriptor();
pb.CL_LOGIN_LOGINUSER_669_UID_FIELD = protobuf.FieldDescriptor();
pb.CL_LOGIN_LOGINUSER_669_AUTHTOKEN_FIELD = protobuf.FieldDescriptor();
pb.CL_LOGIN_LOGINUSER_669_CHANNELID_FIELD = protobuf.FieldDescriptor();
pb.CL_LOGIN_LOGINUSER_669_ZONEID_FIELD = protobuf.FieldDescriptor();
pb.CL_LOGIN_LOGINUSER_669_UUID_FIELD = protobuf.FieldDescriptor();
pb.CL_LOGIN_LOGINUSER_669_APPID_FIELD = protobuf.FieldDescriptor();
pb.CL_LOGIN_LOGINUSER_SDK_COM = protobuf.Descriptor();
pb.CL_LOGIN_LOGINUSER_SDK_COM_UID_FIELD = protobuf.FieldDescriptor();
pb.CL_LOGIN_LOGINUSER_SDK_COM_AUTHTOKEN_FIELD = protobuf.FieldDescriptor();
pb.CL_LOGIN_LOGINUSER_SDK_COM_CHANNELID_FIELD = protobuf.FieldDescriptor();
pb.CL_LOGIN_LOGINUSER_SDK_COM_ZONEID_FIELD = protobuf.FieldDescriptor();
pb.CL_LOGIN_LOGINUSER_SDK_COM_UUID_FIELD = protobuf.FieldDescriptor();
pb.CL_LOGIN_LOGINUSER_SDK_COM_PARAM_FIELD = protobuf.FieldDescriptor();

pb.MSG_LOGIN_ACTIONID_MSG_LOGIN_NONE_ENUM.name = "MSG_LOGIN_NONE"
pb.MSG_LOGIN_ACTIONID_MSG_LOGIN_NONE_ENUM.index = 0
pb.MSG_LOGIN_ACTIONID_MSG_LOGIN_NONE_ENUM.number = 0
pb.MSG_LOGIN_ACTIONID_MSG_LOGIN_REGISTER_ENUM.name = "MSG_LOGIN_REGISTER"
pb.MSG_LOGIN_ACTIONID_MSG_LOGIN_REGISTER_ENUM.index = 1
pb.MSG_LOGIN_ACTIONID_MSG_LOGIN_REGISTER_ENUM.number = 1
pb.MSG_LOGIN_ACTIONID_MSG_LOGIN_LOGINUSER_ENUM.name = "MSG_LOGIN_LOGINUSER"
pb.MSG_LOGIN_ACTIONID_MSG_LOGIN_LOGINUSER_ENUM.index = 2
pb.MSG_LOGIN_ACTIONID_MSG_LOGIN_LOGINUSER_ENUM.number = 2
pb.MSG_LOGIN_ACTIONID_MSG_LOGIN_CREATEACTOR_ENUM.name = "MSG_LOGIN_CREATEACTOR"
pb.MSG_LOGIN_ACTIONID_MSG_LOGIN_CREATEACTOR_ENUM.index = 3
pb.MSG_LOGIN_ACTIONID_MSG_LOGIN_CREATEACTOR_ENUM.number = 3
pb.MSG_LOGIN_ACTIONID_MSG_LOGIN_DELETEACTOR_ENUM.name = "MSG_LOGIN_DELETEACTOR"
pb.MSG_LOGIN_ACTIONID_MSG_LOGIN_DELETEACTOR_ENUM.index = 4
pb.MSG_LOGIN_ACTIONID_MSG_LOGIN_DELETEACTOR_ENUM.number = 4
pb.MSG_LOGIN_ACTIONID_MSG_LOGIN_SELETEACTOR_ENUM.name = "MSG_LOGIN_SELETEACTOR"
pb.MSG_LOGIN_ACTIONID_MSG_LOGIN_SELETEACTOR_ENUM.index = 5
pb.MSG_LOGIN_ACTIONID_MSG_LOGIN_SELETEACTOR_ENUM.number = 5
pb.MSG_LOGIN_ACTIONID_MSG_LOGIN_RECONNECT_ENUM.name = "MSG_LOGIN_RECONNECT"
pb.MSG_LOGIN_ACTIONID_MSG_LOGIN_RECONNECT_ENUM.index = 6
pb.MSG_LOGIN_ACTIONID_MSG_LOGIN_RECONNECT_ENUM.number = 6
pb.MSG_LOGIN_ACTIONID_MSG_LOGIN_ADD_RECONNECT_ENUM.name = "MSG_LOGIN_ADD_RECONNECT"
pb.MSG_LOGIN_ACTIONID_MSG_LOGIN_ADD_RECONNECT_ENUM.index = 7
pb.MSG_LOGIN_ACTIONID_MSG_LOGIN_ADD_RECONNECT_ENUM.number = 7
pb.MSG_LOGIN_ACTIONID_MSG_LOGIN_DEL_RECONNECT_ENUM.name = "MSG_LOGIN_DEL_RECONNECT"
pb.MSG_LOGIN_ACTIONID_MSG_LOGIN_DEL_RECONNECT_ENUM.index = 8
pb.MSG_LOGIN_ACTIONID_MSG_LOGIN_DEL_RECONNECT_ENUM.number = 8
pb.MSG_LOGIN_ACTIONID_MSG_LOGIN_LOGINUSER_UC_ENUM.name = "MSG_LOGIN_LOGINUSER_UC"
pb.MSG_LOGIN_ACTIONID_MSG_LOGIN_LOGINUSER_UC_ENUM.index = 9
pb.MSG_LOGIN_ACTIONID_MSG_LOGIN_LOGINUSER_UC_ENUM.number = 9
pb.MSG_LOGIN_ACTIONID_MSG_LOGIN_SWITCHACTOR_ENUM.name = "MSG_LOGIN_SWITCHACTOR"
pb.MSG_LOGIN_ACTIONID_MSG_LOGIN_SWITCHACTOR_ENUM.index = 10
pb.MSG_LOGIN_ACTIONID_MSG_LOGIN_SWITCHACTOR_ENUM.number = 10
pb.MSG_LOGIN_ACTIONID_MSG_LOGIN_RECONNECT_UC_ENUM.name = "MSG_LOGIN_RECONNECT_UC"
pb.MSG_LOGIN_ACTIONID_MSG_LOGIN_RECONNECT_UC_ENUM.index = 11
pb.MSG_LOGIN_ACTIONID_MSG_LOGIN_RECONNECT_UC_ENUM.number = 11
pb.MSG_LOGIN_ACTIONID_MSG_LOGIN_LOGINUSER_MI_ENUM.name = "MSG_LOGIN_LOGINUSER_MI"
pb.MSG_LOGIN_ACTIONID_MSG_LOGIN_LOGINUSER_MI_ENUM.index = 12
pb.MSG_LOGIN_ACTIONID_MSG_LOGIN_LOGINUSER_MI_ENUM.number = 12
pb.MSG_LOGIN_ACTIONID_MSG_LOGIN_RECONNECT_MI_ENUM.name = "MSG_LOGIN_RECONNECT_MI"
pb.MSG_LOGIN_ACTIONID_MSG_LOGIN_RECONNECT_MI_ENUM.index = 13
pb.MSG_LOGIN_ACTIONID_MSG_LOGIN_RECONNECT_MI_ENUM.number = 13
pb.MSG_LOGIN_ACTIONID_MSG_LOGIN_LOGINUSER_ZS_ENUM.name = "MSG_LOGIN_LOGINUSER_ZS"
pb.MSG_LOGIN_ACTIONID_MSG_LOGIN_LOGINUSER_ZS_ENUM.index = 14
pb.MSG_LOGIN_ACTIONID_MSG_LOGIN_LOGINUSER_ZS_ENUM.number = 14
pb.MSG_LOGIN_ACTIONID_MSG_LOGIN_RECONNECT_ZS_ENUM.name = "MSG_LOGIN_RECONNECT_ZS"
pb.MSG_LOGIN_ACTIONID_MSG_LOGIN_RECONNECT_ZS_ENUM.index = 15
pb.MSG_LOGIN_ACTIONID_MSG_LOGIN_RECONNECT_ZS_ENUM.number = 15
pb.MSG_LOGIN_ACTIONID_MSG_LOGIN_LOGOUTUSER_ENUM.name = "MSG_LOGIN_LOGOUTUSER"
pb.MSG_LOGIN_ACTIONID_MSG_LOGIN_LOGOUTUSER_ENUM.index = 16
pb.MSG_LOGIN_ACTIONID_MSG_LOGIN_LOGOUTUSER_ENUM.number = 16
pb.MSG_LOGIN_ACTIONID_MSG_LOGIN_LOGINUSER_SDK_ENUM.name = "MSG_LOGIN_LOGINUSER_SDK"
pb.MSG_LOGIN_ACTIONID_MSG_LOGIN_LOGINUSER_SDK_ENUM.index = 17
pb.MSG_LOGIN_ACTIONID_MSG_LOGIN_LOGINUSER_SDK_ENUM.number = 17
pb.MSG_LOGIN_ACTIONID_MSG_LOGIN_LOGINUSER_VIVOSDK_ENUM.name = "MSG_LOGIN_LOGINUSER_VIVOSDK"
pb.MSG_LOGIN_ACTIONID_MSG_LOGIN_LOGINUSER_VIVOSDK_ENUM.index = 18
pb.MSG_LOGIN_ACTIONID_MSG_LOGIN_LOGINUSER_VIVOSDK_ENUM.number = 18
pb.MSG_LOGIN_ACTIONID_MSG_LOGIN_LOGINUSER_AMIGOSDK_ENUM.name = "MSG_LOGIN_LOGINUSER_AMIGOSDK"
pb.MSG_LOGIN_ACTIONID_MSG_LOGIN_LOGINUSER_AMIGOSDK_ENUM.index = 19
pb.MSG_LOGIN_ACTIONID_MSG_LOGIN_LOGINUSER_AMIGOSDK_ENUM.number = 19
pb.MSG_LOGIN_ACTIONID_MSG_LOGIN_LOGINUSER_LENOVOSDK_ENUM.name = "MSG_LOGIN_LOGINUSER_LENOVOSDK"
pb.MSG_LOGIN_ACTIONID_MSG_LOGIN_LOGINUSER_LENOVOSDK_ENUM.index = 20
pb.MSG_LOGIN_ACTIONID_MSG_LOGIN_LOGINUSER_LENOVOSDK_ENUM.number = 20
pb.MSG_LOGIN_ACTIONID_MSG_LOGIN_LOGINUSER_HUAWEISDK_ENUM.name = "MSG_LOGIN_LOGINUSER_HUAWEISDK"
pb.MSG_LOGIN_ACTIONID_MSG_LOGIN_LOGINUSER_HUAWEISDK_ENUM.index = 21
pb.MSG_LOGIN_ACTIONID_MSG_LOGIN_LOGINUSER_HUAWEISDK_ENUM.number = 21
pb.MSG_LOGIN_ACTIONID_MSG_LOGIN_LOGINUSER_YIJIESDK_ENUM.name = "MSG_LOGIN_LOGINUSER_YIJIESDK"
pb.MSG_LOGIN_ACTIONID_MSG_LOGIN_LOGINUSER_YIJIESDK_ENUM.index = 22
pb.MSG_LOGIN_ACTIONID_MSG_LOGIN_LOGINUSER_YIJIESDK_ENUM.number = 22
pb.MSG_LOGIN_ACTIONID_MSG_LOGIN_LOGINUSER_IOS_13YX_ENUM.name = "MSG_LOGIN_LOGINUSER_IOS_13YX"
pb.MSG_LOGIN_ACTIONID_MSG_LOGIN_LOGINUSER_IOS_13YX_ENUM.index = 23
pb.MSG_LOGIN_ACTIONID_MSG_LOGIN_LOGINUSER_IOS_13YX_ENUM.number = 23
pb.MSG_LOGIN_ACTIONID_MSG_LOGIN_LOGINUSER_6KW_ENUM.name = "MSG_LOGIN_LOGINUSER_6KW"
pb.MSG_LOGIN_ACTIONID_MSG_LOGIN_LOGINUSER_6KW_ENUM.index = 24
pb.MSG_LOGIN_ACTIONID_MSG_LOGIN_LOGINUSER_6KW_ENUM.number = 24
pb.MSG_LOGIN_ACTIONID_MSG_LOGIN_LOGINUSER_669_ENUM.name = "MSG_LOGIN_LOGINUSER_669"
pb.MSG_LOGIN_ACTIONID_MSG_LOGIN_LOGINUSER_669_ENUM.index = 25
pb.MSG_LOGIN_ACTIONID_MSG_LOGIN_LOGINUSER_669_ENUM.number = 25
pb.MSG_LOGIN_ACTIONID_MSG_LOGIN_LOGINUSER_COM_ENUM.name = "MSG_LOGIN_LOGINUSER_COM"
pb.MSG_LOGIN_ACTIONID_MSG_LOGIN_LOGINUSER_COM_ENUM.index = 26
pb.MSG_LOGIN_ACTIONID_MSG_LOGIN_LOGINUSER_COM_ENUM.number = 26
pb.MSG_LOGIN_ACTIONID.name = "MSG_LOGIN_ACTIONID"
pb.MSG_LOGIN_ACTIONID.full_name = ".MSG_LOGIN_ACTIONID"
pb.MSG_LOGIN_ACTIONID.values = {pb.MSG_LOGIN_ACTIONID_MSG_LOGIN_NONE_ENUM,pb.MSG_LOGIN_ACTIONID_MSG_LOGIN_REGISTER_ENUM,pb.MSG_LOGIN_ACTIONID_MSG_LOGIN_LOGINUSER_ENUM,pb.MSG_LOGIN_ACTIONID_MSG_LOGIN_CREATEACTOR_ENUM,pb.MSG_LOGIN_ACTIONID_MSG_LOGIN_DELETEACTOR_ENUM,pb.MSG_LOGIN_ACTIONID_MSG_LOGIN_SELETEACTOR_ENUM,pb.MSG_LOGIN_ACTIONID_MSG_LOGIN_RECONNECT_ENUM,pb.MSG_LOGIN_ACTIONID_MSG_LOGIN_ADD_RECONNECT_ENUM,pb.MSG_LOGIN_ACTIONID_MSG_LOGIN_DEL_RECONNECT_ENUM,pb.MSG_LOGIN_ACTIONID_MSG_LOGIN_LOGINUSER_UC_ENUM,pb.MSG_LOGIN_ACTIONID_MSG_LOGIN_SWITCHACTOR_ENUM,pb.MSG_LOGIN_ACTIONID_MSG_LOGIN_RECONNECT_UC_ENUM,pb.MSG_LOGIN_ACTIONID_MSG_LOGIN_LOGINUSER_MI_ENUM,pb.MSG_LOGIN_ACTIONID_MSG_LOGIN_RECONNECT_MI_ENUM,pb.MSG_LOGIN_ACTIONID_MSG_LOGIN_LOGINUSER_ZS_ENUM,pb.MSG_LOGIN_ACTIONID_MSG_LOGIN_RECONNECT_ZS_ENUM,pb.MSG_LOGIN_ACTIONID_MSG_LOGIN_LOGOUTUSER_ENUM,pb.MSG_LOGIN_ACTIONID_MSG_LOGIN_LOGINUSER_SDK_ENUM,pb.MSG_LOGIN_ACTIONID_MSG_LOGIN_LOGINUSER_VIVOSDK_ENUM,pb.MSG_LOGIN_ACTIONID_MSG_LOGIN_LOGINUSER_AMIGOSDK_ENUM,pb.MSG_LOGIN_ACTIONID_MSG_LOGIN_LOGINUSER_LENOVOSDK_ENUM,pb.MSG_LOGIN_ACTIONID_MSG_LOGIN_LOGINUSER_HUAWEISDK_ENUM,pb.MSG_LOGIN_ACTIONID_MSG_LOGIN_LOGINUSER_YIJIESDK_ENUM,pb.MSG_LOGIN_ACTIONID_MSG_LOGIN_LOGINUSER_IOS_13YX_ENUM,pb.MSG_LOGIN_ACTIONID_MSG_LOGIN_LOGINUSER_6KW_ENUM,pb.MSG_LOGIN_ACTIONID_MSG_LOGIN_LOGINUSER_669_ENUM,pb.MSG_LOGIN_ACTIONID_MSG_LOGIN_LOGINUSER_COM_ENUM}
pb.MSG_LOGIN_PLAYER_STATE_MSG_LOGIN_PLAYER_STATE_UNKNOWN_ENUM.name = "MSG_LOGIN_PLAYER_STATE_UNKNOWN"
pb.MSG_LOGIN_PLAYER_STATE_MSG_LOGIN_PLAYER_STATE_UNKNOWN_ENUM.index = 0
pb.MSG_LOGIN_PLAYER_STATE_MSG_LOGIN_PLAYER_STATE_UNKNOWN_ENUM.number = 0
pb.MSG_LOGIN_PLAYER_STATE_MSG_LOGIN_PLAYER_STATE_CREATE_ACTOR_ENUM.name = "MSG_LOGIN_PLAYER_STATE_CREATE_ACTOR"
pb.MSG_LOGIN_PLAYER_STATE_MSG_LOGIN_PLAYER_STATE_CREATE_ACTOR_ENUM.index = 1
pb.MSG_LOGIN_PLAYER_STATE_MSG_LOGIN_PLAYER_STATE_CREATE_ACTOR_ENUM.number = 1
pb.MSG_LOGIN_PLAYER_STATE_MSG_LOGIN_PLAYER_STATE_SELECT_ACTOR_ENUM.name = "MSG_LOGIN_PLAYER_STATE_SELECT_ACTOR"
pb.MSG_LOGIN_PLAYER_STATE_MSG_LOGIN_PLAYER_STATE_SELECT_ACTOR_ENUM.index = 2
pb.MSG_LOGIN_PLAYER_STATE_MSG_LOGIN_PLAYER_STATE_SELECT_ACTOR_ENUM.number = 2
pb.MSG_LOGIN_PLAYER_STATE_MSG_LOGIN_PLAYER_STATE_PLAYING_ENUM.name = "MSG_LOGIN_PLAYER_STATE_PLAYING"
pb.MSG_LOGIN_PLAYER_STATE_MSG_LOGIN_PLAYER_STATE_PLAYING_ENUM.index = 3
pb.MSG_LOGIN_PLAYER_STATE_MSG_LOGIN_PLAYER_STATE_PLAYING_ENUM.number = 3
pb.MSG_LOGIN_PLAYER_STATE.name = "MSG_LOGIN_PLAYER_STATE"
pb.MSG_LOGIN_PLAYER_STATE.full_name = ".MSG_LOGIN_PLAYER_STATE"
pb.MSG_LOGIN_PLAYER_STATE.values = {pb.MSG_LOGIN_PLAYER_STATE_MSG_LOGIN_PLAYER_STATE_UNKNOWN_ENUM,pb.MSG_LOGIN_PLAYER_STATE_MSG_LOGIN_PLAYER_STATE_CREATE_ACTOR_ENUM,pb.MSG_LOGIN_PLAYER_STATE_MSG_LOGIN_PLAYER_STATE_SELECT_ACTOR_ENUM,pb.MSG_LOGIN_PLAYER_STATE_MSG_LOGIN_PLAYER_STATE_PLAYING_ENUM}
pb.CL_LOGIN_REGISTER_USERNAME_FIELD.name = "UserName"
pb.CL_LOGIN_REGISTER_USERNAME_FIELD.full_name = ".CL_Login_Register.UserName"
pb.CL_LOGIN_REGISTER_USERNAME_FIELD.number = 1
pb.CL_LOGIN_REGISTER_USERNAME_FIELD.index = 0
pb.CL_LOGIN_REGISTER_USERNAME_FIELD.label = 2
pb.CL_LOGIN_REGISTER_USERNAME_FIELD.has_default_value = false
pb.CL_LOGIN_REGISTER_USERNAME_FIELD.default_value = ""
pb.CL_LOGIN_REGISTER_USERNAME_FIELD.type = 9
pb.CL_LOGIN_REGISTER_USERNAME_FIELD.cpp_type = 9

pb.CL_LOGIN_REGISTER_PASSWORD_FIELD.name = "Password"
pb.CL_LOGIN_REGISTER_PASSWORD_FIELD.full_name = ".CL_Login_Register.Password"
pb.CL_LOGIN_REGISTER_PASSWORD_FIELD.number = 2
pb.CL_LOGIN_REGISTER_PASSWORD_FIELD.index = 1
pb.CL_LOGIN_REGISTER_PASSWORD_FIELD.label = 2
pb.CL_LOGIN_REGISTER_PASSWORD_FIELD.has_default_value = false
pb.CL_LOGIN_REGISTER_PASSWORD_FIELD.default_value = ""
pb.CL_LOGIN_REGISTER_PASSWORD_FIELD.type = 9
pb.CL_LOGIN_REGISTER_PASSWORD_FIELD.cpp_type = 9

pb.CL_LOGIN_REGISTER.name = "CL_Login_Register"
pb.CL_LOGIN_REGISTER.full_name = ".CL_Login_Register"
pb.CL_LOGIN_REGISTER.nested_types = {}
pb.CL_LOGIN_REGISTER.enum_types = {}
pb.CL_LOGIN_REGISTER.fields = {pb.CL_LOGIN_REGISTER_USERNAME_FIELD, pb.CL_LOGIN_REGISTER_PASSWORD_FIELD}
pb.CL_LOGIN_REGISTER.is_extendable = false
pb.CL_LOGIN_REGISTER.extensions = {}
pb.LC_LOGIN_REGISTER_RESULT_FIELD.name = "Result"
pb.LC_LOGIN_REGISTER_RESULT_FIELD.full_name = ".LC_Login_Register.Result"
pb.LC_LOGIN_REGISTER_RESULT_FIELD.number = 1
pb.LC_LOGIN_REGISTER_RESULT_FIELD.index = 0
pb.LC_LOGIN_REGISTER_RESULT_FIELD.label = 2
pb.LC_LOGIN_REGISTER_RESULT_FIELD.has_default_value = false
pb.LC_LOGIN_REGISTER_RESULT_FIELD.default_value = 0
pb.LC_LOGIN_REGISTER_RESULT_FIELD.type = 13
pb.LC_LOGIN_REGISTER_RESULT_FIELD.cpp_type = 3

pb.LC_LOGIN_REGISTER.name = "LC_Login_Register"
pb.LC_LOGIN_REGISTER.full_name = ".LC_Login_Register"
pb.LC_LOGIN_REGISTER.nested_types = {}
pb.LC_LOGIN_REGISTER.enum_types = {}
pb.LC_LOGIN_REGISTER.fields = {pb.LC_LOGIN_REGISTER_RESULT_FIELD}
pb.LC_LOGIN_REGISTER.is_extendable = false
pb.LC_LOGIN_REGISTER.extensions = {}
pb.CL_LOGIN_LOGINUSER_USERNAME_FIELD.name = "UserName"
pb.CL_LOGIN_LOGINUSER_USERNAME_FIELD.full_name = ".CL_Login_LoginUser.UserName"
pb.CL_LOGIN_LOGINUSER_USERNAME_FIELD.number = 1
pb.CL_LOGIN_LOGINUSER_USERNAME_FIELD.index = 0
pb.CL_LOGIN_LOGINUSER_USERNAME_FIELD.label = 2
pb.CL_LOGIN_LOGINUSER_USERNAME_FIELD.has_default_value = false
pb.CL_LOGIN_LOGINUSER_USERNAME_FIELD.default_value = ""
pb.CL_LOGIN_LOGINUSER_USERNAME_FIELD.type = 9
pb.CL_LOGIN_LOGINUSER_USERNAME_FIELD.cpp_type = 9

pb.CL_LOGIN_LOGINUSER_PASSWORD_FIELD.name = "Password"
pb.CL_LOGIN_LOGINUSER_PASSWORD_FIELD.full_name = ".CL_Login_LoginUser.Password"
pb.CL_LOGIN_LOGINUSER_PASSWORD_FIELD.number = 2
pb.CL_LOGIN_LOGINUSER_PASSWORD_FIELD.index = 1
pb.CL_LOGIN_LOGINUSER_PASSWORD_FIELD.label = 2
pb.CL_LOGIN_LOGINUSER_PASSWORD_FIELD.has_default_value = false
pb.CL_LOGIN_LOGINUSER_PASSWORD_FIELD.default_value = ""
pb.CL_LOGIN_LOGINUSER_PASSWORD_FIELD.type = 9
pb.CL_LOGIN_LOGINUSER_PASSWORD_FIELD.cpp_type = 9

pb.CL_LOGIN_LOGINUSER_LOGINID_FIELD.name = "LoginID"
pb.CL_LOGIN_LOGINUSER_LOGINID_FIELD.full_name = ".CL_Login_LoginUser.LoginID"
pb.CL_LOGIN_LOGINUSER_LOGINID_FIELD.number = 5
pb.CL_LOGIN_LOGINUSER_LOGINID_FIELD.index = 2
pb.CL_LOGIN_LOGINUSER_LOGINID_FIELD.label = 2
pb.CL_LOGIN_LOGINUSER_LOGINID_FIELD.has_default_value = false
pb.CL_LOGIN_LOGINUSER_LOGINID_FIELD.default_value = 0
pb.CL_LOGIN_LOGINUSER_LOGINID_FIELD.type = 13
pb.CL_LOGIN_LOGINUSER_LOGINID_FIELD.cpp_type = 3

pb.CL_LOGIN_LOGINUSER.name = "CL_Login_LoginUser"
pb.CL_LOGIN_LOGINUSER.full_name = ".CL_Login_LoginUser"
pb.CL_LOGIN_LOGINUSER.nested_types = {}
pb.CL_LOGIN_LOGINUSER.enum_types = {}
pb.CL_LOGIN_LOGINUSER.fields = {pb.CL_LOGIN_LOGINUSER_USERNAME_FIELD, pb.CL_LOGIN_LOGINUSER_PASSWORD_FIELD, pb.CL_LOGIN_LOGINUSER_LOGINID_FIELD}
pb.CL_LOGIN_LOGINUSER.is_extendable = false
pb.CL_LOGIN_LOGINUSER.extensions = {}
pb.SC_LOGIN_LOGINUSER_ACTORINFO_ACTORID_FIELD.name = "ActorID"
pb.SC_LOGIN_LOGINUSER_ACTORINFO_ACTORID_FIELD.full_name = ".SC_Login_LoginUser.ActorInfo.ActorID"
pb.SC_LOGIN_LOGINUSER_ACTORINFO_ACTORID_FIELD.number = 1
pb.SC_LOGIN_LOGINUSER_ACTORINFO_ACTORID_FIELD.index = 0
pb.SC_LOGIN_LOGINUSER_ACTORINFO_ACTORID_FIELD.label = 2
pb.SC_LOGIN_LOGINUSER_ACTORINFO_ACTORID_FIELD.has_default_value = false
pb.SC_LOGIN_LOGINUSER_ACTORINFO_ACTORID_FIELD.default_value = 0
pb.SC_LOGIN_LOGINUSER_ACTORINFO_ACTORID_FIELD.type = 13
pb.SC_LOGIN_LOGINUSER_ACTORINFO_ACTORID_FIELD.cpp_type = 3

pb.SC_LOGIN_LOGINUSER_ACTORINFO_ACTORNAME_FIELD.name = "ActorName"
pb.SC_LOGIN_LOGINUSER_ACTORINFO_ACTORNAME_FIELD.full_name = ".SC_Login_LoginUser.ActorInfo.ActorName"
pb.SC_LOGIN_LOGINUSER_ACTORINFO_ACTORNAME_FIELD.number = 2
pb.SC_LOGIN_LOGINUSER_ACTORINFO_ACTORNAME_FIELD.index = 1
pb.SC_LOGIN_LOGINUSER_ACTORINFO_ACTORNAME_FIELD.label = 2
pb.SC_LOGIN_LOGINUSER_ACTORINFO_ACTORNAME_FIELD.has_default_value = false
pb.SC_LOGIN_LOGINUSER_ACTORINFO_ACTORNAME_FIELD.default_value = ""
pb.SC_LOGIN_LOGINUSER_ACTORINFO_ACTORNAME_FIELD.type = 9
pb.SC_LOGIN_LOGINUSER_ACTORINFO_ACTORNAME_FIELD.cpp_type = 9

pb.SC_LOGIN_LOGINUSER_ACTORINFO_LEVEL_FIELD.name = "Level"
pb.SC_LOGIN_LOGINUSER_ACTORINFO_LEVEL_FIELD.full_name = ".SC_Login_LoginUser.ActorInfo.Level"
pb.SC_LOGIN_LOGINUSER_ACTORINFO_LEVEL_FIELD.number = 3
pb.SC_LOGIN_LOGINUSER_ACTORINFO_LEVEL_FIELD.index = 2
pb.SC_LOGIN_LOGINUSER_ACTORINFO_LEVEL_FIELD.label = 2
pb.SC_LOGIN_LOGINUSER_ACTORINFO_LEVEL_FIELD.has_default_value = false
pb.SC_LOGIN_LOGINUSER_ACTORINFO_LEVEL_FIELD.default_value = 0
pb.SC_LOGIN_LOGINUSER_ACTORINFO_LEVEL_FIELD.type = 13
pb.SC_LOGIN_LOGINUSER_ACTORINFO_LEVEL_FIELD.cpp_type = 3

pb.SC_LOGIN_LOGINUSER_ACTORINFO_VOCATION_FIELD.name = "Vocation"
pb.SC_LOGIN_LOGINUSER_ACTORINFO_VOCATION_FIELD.full_name = ".SC_Login_LoginUser.ActorInfo.Vocation"
pb.SC_LOGIN_LOGINUSER_ACTORINFO_VOCATION_FIELD.number = 4
pb.SC_LOGIN_LOGINUSER_ACTORINFO_VOCATION_FIELD.index = 3
pb.SC_LOGIN_LOGINUSER_ACTORINFO_VOCATION_FIELD.label = 2
pb.SC_LOGIN_LOGINUSER_ACTORINFO_VOCATION_FIELD.has_default_value = false
pb.SC_LOGIN_LOGINUSER_ACTORINFO_VOCATION_FIELD.default_value = 0
pb.SC_LOGIN_LOGINUSER_ACTORINFO_VOCATION_FIELD.type = 13
pb.SC_LOGIN_LOGINUSER_ACTORINFO_VOCATION_FIELD.cpp_type = 3

pb.SC_LOGIN_LOGINUSER_ACTORINFO_COUNTRY_FIELD.name = "Country"
pb.SC_LOGIN_LOGINUSER_ACTORINFO_COUNTRY_FIELD.full_name = ".SC_Login_LoginUser.ActorInfo.Country"
pb.SC_LOGIN_LOGINUSER_ACTORINFO_COUNTRY_FIELD.number = 5
pb.SC_LOGIN_LOGINUSER_ACTORINFO_COUNTRY_FIELD.index = 4
pb.SC_LOGIN_LOGINUSER_ACTORINFO_COUNTRY_FIELD.label = 2
pb.SC_LOGIN_LOGINUSER_ACTORINFO_COUNTRY_FIELD.has_default_value = false
pb.SC_LOGIN_LOGINUSER_ACTORINFO_COUNTRY_FIELD.default_value = 0
pb.SC_LOGIN_LOGINUSER_ACTORINFO_COUNTRY_FIELD.type = 13
pb.SC_LOGIN_LOGINUSER_ACTORINFO_COUNTRY_FIELD.cpp_type = 3

pb.SC_LOGIN_LOGINUSER_ACTORINFO_EQUIPMODEID_FIELD.name = "EquipModeID"
pb.SC_LOGIN_LOGINUSER_ACTORINFO_EQUIPMODEID_FIELD.full_name = ".SC_Login_LoginUser.ActorInfo.EquipModeID"
pb.SC_LOGIN_LOGINUSER_ACTORINFO_EQUIPMODEID_FIELD.number = 6
pb.SC_LOGIN_LOGINUSER_ACTORINFO_EQUIPMODEID_FIELD.index = 5
pb.SC_LOGIN_LOGINUSER_ACTORINFO_EQUIPMODEID_FIELD.label = 2
pb.SC_LOGIN_LOGINUSER_ACTORINFO_EQUIPMODEID_FIELD.has_default_value = false
pb.SC_LOGIN_LOGINUSER_ACTORINFO_EQUIPMODEID_FIELD.default_value = 0
pb.SC_LOGIN_LOGINUSER_ACTORINFO_EQUIPMODEID_FIELD.type = 13
pb.SC_LOGIN_LOGINUSER_ACTORINFO_EQUIPMODEID_FIELD.cpp_type = 3

pb.SC_LOGIN_LOGINUSER_ACTORINFO_WEAPONID_FIELD.name = "WeaponID"
pb.SC_LOGIN_LOGINUSER_ACTORINFO_WEAPONID_FIELD.full_name = ".SC_Login_LoginUser.ActorInfo.WeaponID"
pb.SC_LOGIN_LOGINUSER_ACTORINFO_WEAPONID_FIELD.number = 7
pb.SC_LOGIN_LOGINUSER_ACTORINFO_WEAPONID_FIELD.index = 6
pb.SC_LOGIN_LOGINUSER_ACTORINFO_WEAPONID_FIELD.label = 2
pb.SC_LOGIN_LOGINUSER_ACTORINFO_WEAPONID_FIELD.has_default_value = false
pb.SC_LOGIN_LOGINUSER_ACTORINFO_WEAPONID_FIELD.default_value = 0
pb.SC_LOGIN_LOGINUSER_ACTORINFO_WEAPONID_FIELD.type = 13
pb.SC_LOGIN_LOGINUSER_ACTORINFO_WEAPONID_FIELD.cpp_type = 3

pb.SC_LOGIN_LOGINUSER_ACTORINFO_WINGID_FIELD.name = "WingID"
pb.SC_LOGIN_LOGINUSER_ACTORINFO_WINGID_FIELD.full_name = ".SC_Login_LoginUser.ActorInfo.WingID"
pb.SC_LOGIN_LOGINUSER_ACTORINFO_WINGID_FIELD.number = 8
pb.SC_LOGIN_LOGINUSER_ACTORINFO_WINGID_FIELD.index = 7
pb.SC_LOGIN_LOGINUSER_ACTORINFO_WINGID_FIELD.label = 2
pb.SC_LOGIN_LOGINUSER_ACTORINFO_WINGID_FIELD.has_default_value = false
pb.SC_LOGIN_LOGINUSER_ACTORINFO_WINGID_FIELD.default_value = 0
pb.SC_LOGIN_LOGINUSER_ACTORINFO_WINGID_FIELD.type = 13
pb.SC_LOGIN_LOGINUSER_ACTORINFO_WINGID_FIELD.cpp_type = 3

pb.SC_LOGIN_LOGINUSER_ACTORINFO_NEWWINGID_FIELD.name = "NewWingID"
pb.SC_LOGIN_LOGINUSER_ACTORINFO_NEWWINGID_FIELD.full_name = ".SC_Login_LoginUser.ActorInfo.NewWingID"
pb.SC_LOGIN_LOGINUSER_ACTORINFO_NEWWINGID_FIELD.number = 9
pb.SC_LOGIN_LOGINUSER_ACTORINFO_NEWWINGID_FIELD.index = 8
pb.SC_LOGIN_LOGINUSER_ACTORINFO_NEWWINGID_FIELD.label = 2
pb.SC_LOGIN_LOGINUSER_ACTORINFO_NEWWINGID_FIELD.has_default_value = false
pb.SC_LOGIN_LOGINUSER_ACTORINFO_NEWWINGID_FIELD.default_value = 0
pb.SC_LOGIN_LOGINUSER_ACTORINFO_NEWWINGID_FIELD.type = 13
pb.SC_LOGIN_LOGINUSER_ACTORINFO_NEWWINGID_FIELD.cpp_type = 3

pb.SC_LOGIN_LOGINUSER_ACTORINFO.name = "ActorInfo"
pb.SC_LOGIN_LOGINUSER_ACTORINFO.full_name = ".SC_Login_LoginUser.ActorInfo"
pb.SC_LOGIN_LOGINUSER_ACTORINFO.nested_types = {}
pb.SC_LOGIN_LOGINUSER_ACTORINFO.enum_types = {}
pb.SC_LOGIN_LOGINUSER_ACTORINFO.fields = {pb.SC_LOGIN_LOGINUSER_ACTORINFO_ACTORID_FIELD, pb.SC_LOGIN_LOGINUSER_ACTORINFO_ACTORNAME_FIELD, pb.SC_LOGIN_LOGINUSER_ACTORINFO_LEVEL_FIELD, pb.SC_LOGIN_LOGINUSER_ACTORINFO_VOCATION_FIELD, pb.SC_LOGIN_LOGINUSER_ACTORINFO_COUNTRY_FIELD, pb.SC_LOGIN_LOGINUSER_ACTORINFO_EQUIPMODEID_FIELD, pb.SC_LOGIN_LOGINUSER_ACTORINFO_WEAPONID_FIELD, pb.SC_LOGIN_LOGINUSER_ACTORINFO_WINGID_FIELD, pb.SC_LOGIN_LOGINUSER_ACTORINFO_NEWWINGID_FIELD}
pb.SC_LOGIN_LOGINUSER_ACTORINFO.is_extendable = false
pb.SC_LOGIN_LOGINUSER_ACTORINFO.extensions = {}
pb.SC_LOGIN_LOGINUSER_ACTORINFO.containing_type = pb.SC_LOGIN_LOGINUSER
pb.SC_LOGIN_LOGINUSER_RESULT_FIELD.name = "Result"
pb.SC_LOGIN_LOGINUSER_RESULT_FIELD.full_name = ".SC_Login_LoginUser.Result"
pb.SC_LOGIN_LOGINUSER_RESULT_FIELD.number = 1
pb.SC_LOGIN_LOGINUSER_RESULT_FIELD.index = 0
pb.SC_LOGIN_LOGINUSER_RESULT_FIELD.label = 2
pb.SC_LOGIN_LOGINUSER_RESULT_FIELD.has_default_value = false
pb.SC_LOGIN_LOGINUSER_RESULT_FIELD.default_value = 0
pb.SC_LOGIN_LOGINUSER_RESULT_FIELD.type = 13
pb.SC_LOGIN_LOGINUSER_RESULT_FIELD.cpp_type = 3

pb.SC_LOGIN_LOGINUSER_ACTORLIST_FIELD.name = "ActorList"
pb.SC_LOGIN_LOGINUSER_ACTORLIST_FIELD.full_name = ".SC_Login_LoginUser.ActorList"
pb.SC_LOGIN_LOGINUSER_ACTORLIST_FIELD.number = 2
pb.SC_LOGIN_LOGINUSER_ACTORLIST_FIELD.index = 1
pb.SC_LOGIN_LOGINUSER_ACTORLIST_FIELD.label = 3
pb.SC_LOGIN_LOGINUSER_ACTORLIST_FIELD.has_default_value = false
pb.SC_LOGIN_LOGINUSER_ACTORLIST_FIELD.default_value = {}
pb.SC_LOGIN_LOGINUSER_ACTORLIST_FIELD.message_type = pb.SC_LOGIN_LOGINUSER_ACTORINFO
pb.SC_LOGIN_LOGINUSER_ACTORLIST_FIELD.type = 11
pb.SC_LOGIN_LOGINUSER_ACTORLIST_FIELD.cpp_type = 10

pb.SC_LOGIN_LOGINUSER_SERVERTIME_FIELD.name = "ServerTime"
pb.SC_LOGIN_LOGINUSER_SERVERTIME_FIELD.full_name = ".SC_Login_LoginUser.ServerTime"
pb.SC_LOGIN_LOGINUSER_SERVERTIME_FIELD.number = 3
pb.SC_LOGIN_LOGINUSER_SERVERTIME_FIELD.index = 2
pb.SC_LOGIN_LOGINUSER_SERVERTIME_FIELD.label = 1
pb.SC_LOGIN_LOGINUSER_SERVERTIME_FIELD.has_default_value = false
pb.SC_LOGIN_LOGINUSER_SERVERTIME_FIELD.default_value = 0
pb.SC_LOGIN_LOGINUSER_SERVERTIME_FIELD.type = 13
pb.SC_LOGIN_LOGINUSER_SERVERTIME_FIELD.cpp_type = 3

pb.SC_LOGIN_LOGINUSER_USERNAME_FIELD.name = "UserName"
pb.SC_LOGIN_LOGINUSER_USERNAME_FIELD.full_name = ".SC_Login_LoginUser.UserName"
pb.SC_LOGIN_LOGINUSER_USERNAME_FIELD.number = 4
pb.SC_LOGIN_LOGINUSER_USERNAME_FIELD.index = 3
pb.SC_LOGIN_LOGINUSER_USERNAME_FIELD.label = 1
pb.SC_LOGIN_LOGINUSER_USERNAME_FIELD.has_default_value = false
pb.SC_LOGIN_LOGINUSER_USERNAME_FIELD.default_value = ""
pb.SC_LOGIN_LOGINUSER_USERNAME_FIELD.type = 9
pb.SC_LOGIN_LOGINUSER_USERNAME_FIELD.cpp_type = 9

pb.SC_LOGIN_LOGINUSER_CURCACHEACTORID_FIELD.name = "CurCacheActorID"
pb.SC_LOGIN_LOGINUSER_CURCACHEACTORID_FIELD.full_name = ".SC_Login_LoginUser.CurCacheActorID"
pb.SC_LOGIN_LOGINUSER_CURCACHEACTORID_FIELD.number = 5
pb.SC_LOGIN_LOGINUSER_CURCACHEACTORID_FIELD.index = 4
pb.SC_LOGIN_LOGINUSER_CURCACHEACTORID_FIELD.label = 1
pb.SC_LOGIN_LOGINUSER_CURCACHEACTORID_FIELD.has_default_value = false
pb.SC_LOGIN_LOGINUSER_CURCACHEACTORID_FIELD.default_value = 0
pb.SC_LOGIN_LOGINUSER_CURCACHEACTORID_FIELD.type = 13
pb.SC_LOGIN_LOGINUSER_CURCACHEACTORID_FIELD.cpp_type = 3

pb.SC_LOGIN_LOGINUSER.name = "SC_Login_LoginUser"
pb.SC_LOGIN_LOGINUSER.full_name = ".SC_Login_LoginUser"
pb.SC_LOGIN_LOGINUSER.nested_types = {pb.SC_LOGIN_LOGINUSER_ACTORINFO}
pb.SC_LOGIN_LOGINUSER.enum_types = {}
pb.SC_LOGIN_LOGINUSER.fields = {pb.SC_LOGIN_LOGINUSER_RESULT_FIELD, pb.SC_LOGIN_LOGINUSER_ACTORLIST_FIELD, pb.SC_LOGIN_LOGINUSER_SERVERTIME_FIELD, pb.SC_LOGIN_LOGINUSER_USERNAME_FIELD, pb.SC_LOGIN_LOGINUSER_CURCACHEACTORID_FIELD}
pb.SC_LOGIN_LOGINUSER.is_extendable = false
pb.SC_LOGIN_LOGINUSER.extensions = {}
pb.CL_LOGIN_CREATEACTOR_ACTORNAME_FIELD.name = "ActorName"
pb.CL_LOGIN_CREATEACTOR_ACTORNAME_FIELD.full_name = ".CL_Login_CreateActor.ActorName"
pb.CL_LOGIN_CREATEACTOR_ACTORNAME_FIELD.number = 1
pb.CL_LOGIN_CREATEACTOR_ACTORNAME_FIELD.index = 0
pb.CL_LOGIN_CREATEACTOR_ACTORNAME_FIELD.label = 2
pb.CL_LOGIN_CREATEACTOR_ACTORNAME_FIELD.has_default_value = false
pb.CL_LOGIN_CREATEACTOR_ACTORNAME_FIELD.default_value = ""
pb.CL_LOGIN_CREATEACTOR_ACTORNAME_FIELD.type = 9
pb.CL_LOGIN_CREATEACTOR_ACTORNAME_FIELD.cpp_type = 9

pb.CL_LOGIN_CREATEACTOR_VOCATION_FIELD.name = "Vocation"
pb.CL_LOGIN_CREATEACTOR_VOCATION_FIELD.full_name = ".CL_Login_CreateActor.Vocation"
pb.CL_LOGIN_CREATEACTOR_VOCATION_FIELD.number = 4
pb.CL_LOGIN_CREATEACTOR_VOCATION_FIELD.index = 1
pb.CL_LOGIN_CREATEACTOR_VOCATION_FIELD.label = 2
pb.CL_LOGIN_CREATEACTOR_VOCATION_FIELD.has_default_value = false
pb.CL_LOGIN_CREATEACTOR_VOCATION_FIELD.default_value = 0
pb.CL_LOGIN_CREATEACTOR_VOCATION_FIELD.type = 13
pb.CL_LOGIN_CREATEACTOR_VOCATION_FIELD.cpp_type = 3

pb.CL_LOGIN_CREATEACTOR_COUNTRY_FIELD.name = "Country"
pb.CL_LOGIN_CREATEACTOR_COUNTRY_FIELD.full_name = ".CL_Login_CreateActor.Country"
pb.CL_LOGIN_CREATEACTOR_COUNTRY_FIELD.number = 5
pb.CL_LOGIN_CREATEACTOR_COUNTRY_FIELD.index = 2
pb.CL_LOGIN_CREATEACTOR_COUNTRY_FIELD.label = 2
pb.CL_LOGIN_CREATEACTOR_COUNTRY_FIELD.has_default_value = false
pb.CL_LOGIN_CREATEACTOR_COUNTRY_FIELD.default_value = 0
pb.CL_LOGIN_CREATEACTOR_COUNTRY_FIELD.type = 13
pb.CL_LOGIN_CREATEACTOR_COUNTRY_FIELD.cpp_type = 3

pb.CL_LOGIN_CREATEACTOR.name = "CL_Login_CreateActor"
pb.CL_LOGIN_CREATEACTOR.full_name = ".CL_Login_CreateActor"
pb.CL_LOGIN_CREATEACTOR.nested_types = {}
pb.CL_LOGIN_CREATEACTOR.enum_types = {}
pb.CL_LOGIN_CREATEACTOR.fields = {pb.CL_LOGIN_CREATEACTOR_ACTORNAME_FIELD, pb.CL_LOGIN_CREATEACTOR_VOCATION_FIELD, pb.CL_LOGIN_CREATEACTOR_COUNTRY_FIELD}
pb.CL_LOGIN_CREATEACTOR.is_extendable = false
pb.CL_LOGIN_CREATEACTOR.extensions = {}
pb.LC_LOGIN_CREATEACTOR_ACTORINFO_ACTORID_FIELD.name = "ActorID"
pb.LC_LOGIN_CREATEACTOR_ACTORINFO_ACTORID_FIELD.full_name = ".LC_Login_CreateActor.ActorInfo.ActorID"
pb.LC_LOGIN_CREATEACTOR_ACTORINFO_ACTORID_FIELD.number = 1
pb.LC_LOGIN_CREATEACTOR_ACTORINFO_ACTORID_FIELD.index = 0
pb.LC_LOGIN_CREATEACTOR_ACTORINFO_ACTORID_FIELD.label = 2
pb.LC_LOGIN_CREATEACTOR_ACTORINFO_ACTORID_FIELD.has_default_value = false
pb.LC_LOGIN_CREATEACTOR_ACTORINFO_ACTORID_FIELD.default_value = 0
pb.LC_LOGIN_CREATEACTOR_ACTORINFO_ACTORID_FIELD.type = 13
pb.LC_LOGIN_CREATEACTOR_ACTORINFO_ACTORID_FIELD.cpp_type = 3

pb.LC_LOGIN_CREATEACTOR_ACTORINFO_ACTORNAME_FIELD.name = "ActorName"
pb.LC_LOGIN_CREATEACTOR_ACTORINFO_ACTORNAME_FIELD.full_name = ".LC_Login_CreateActor.ActorInfo.ActorName"
pb.LC_LOGIN_CREATEACTOR_ACTORINFO_ACTORNAME_FIELD.number = 2
pb.LC_LOGIN_CREATEACTOR_ACTORINFO_ACTORNAME_FIELD.index = 1
pb.LC_LOGIN_CREATEACTOR_ACTORINFO_ACTORNAME_FIELD.label = 2
pb.LC_LOGIN_CREATEACTOR_ACTORINFO_ACTORNAME_FIELD.has_default_value = false
pb.LC_LOGIN_CREATEACTOR_ACTORINFO_ACTORNAME_FIELD.default_value = ""
pb.LC_LOGIN_CREATEACTOR_ACTORINFO_ACTORNAME_FIELD.type = 9
pb.LC_LOGIN_CREATEACTOR_ACTORINFO_ACTORNAME_FIELD.cpp_type = 9

pb.LC_LOGIN_CREATEACTOR_ACTORINFO_LEVEL_FIELD.name = "Level"
pb.LC_LOGIN_CREATEACTOR_ACTORINFO_LEVEL_FIELD.full_name = ".LC_Login_CreateActor.ActorInfo.Level"
pb.LC_LOGIN_CREATEACTOR_ACTORINFO_LEVEL_FIELD.number = 3
pb.LC_LOGIN_CREATEACTOR_ACTORINFO_LEVEL_FIELD.index = 2
pb.LC_LOGIN_CREATEACTOR_ACTORINFO_LEVEL_FIELD.label = 2
pb.LC_LOGIN_CREATEACTOR_ACTORINFO_LEVEL_FIELD.has_default_value = false
pb.LC_LOGIN_CREATEACTOR_ACTORINFO_LEVEL_FIELD.default_value = 0
pb.LC_LOGIN_CREATEACTOR_ACTORINFO_LEVEL_FIELD.type = 13
pb.LC_LOGIN_CREATEACTOR_ACTORINFO_LEVEL_FIELD.cpp_type = 3

pb.LC_LOGIN_CREATEACTOR_ACTORINFO_VOCATION_FIELD.name = "Vocation"
pb.LC_LOGIN_CREATEACTOR_ACTORINFO_VOCATION_FIELD.full_name = ".LC_Login_CreateActor.ActorInfo.Vocation"
pb.LC_LOGIN_CREATEACTOR_ACTORINFO_VOCATION_FIELD.number = 4
pb.LC_LOGIN_CREATEACTOR_ACTORINFO_VOCATION_FIELD.index = 3
pb.LC_LOGIN_CREATEACTOR_ACTORINFO_VOCATION_FIELD.label = 2
pb.LC_LOGIN_CREATEACTOR_ACTORINFO_VOCATION_FIELD.has_default_value = false
pb.LC_LOGIN_CREATEACTOR_ACTORINFO_VOCATION_FIELD.default_value = 0
pb.LC_LOGIN_CREATEACTOR_ACTORINFO_VOCATION_FIELD.type = 13
pb.LC_LOGIN_CREATEACTOR_ACTORINFO_VOCATION_FIELD.cpp_type = 3

pb.LC_LOGIN_CREATEACTOR_ACTORINFO_COUNTRY_FIELD.name = "Country"
pb.LC_LOGIN_CREATEACTOR_ACTORINFO_COUNTRY_FIELD.full_name = ".LC_Login_CreateActor.ActorInfo.Country"
pb.LC_LOGIN_CREATEACTOR_ACTORINFO_COUNTRY_FIELD.number = 5
pb.LC_LOGIN_CREATEACTOR_ACTORINFO_COUNTRY_FIELD.index = 4
pb.LC_LOGIN_CREATEACTOR_ACTORINFO_COUNTRY_FIELD.label = 2
pb.LC_LOGIN_CREATEACTOR_ACTORINFO_COUNTRY_FIELD.has_default_value = false
pb.LC_LOGIN_CREATEACTOR_ACTORINFO_COUNTRY_FIELD.default_value = 0
pb.LC_LOGIN_CREATEACTOR_ACTORINFO_COUNTRY_FIELD.type = 13
pb.LC_LOGIN_CREATEACTOR_ACTORINFO_COUNTRY_FIELD.cpp_type = 3

pb.LC_LOGIN_CREATEACTOR_ACTORINFO.name = "ActorInfo"
pb.LC_LOGIN_CREATEACTOR_ACTORINFO.full_name = ".LC_Login_CreateActor.ActorInfo"
pb.LC_LOGIN_CREATEACTOR_ACTORINFO.nested_types = {}
pb.LC_LOGIN_CREATEACTOR_ACTORINFO.enum_types = {}
pb.LC_LOGIN_CREATEACTOR_ACTORINFO.fields = {pb.LC_LOGIN_CREATEACTOR_ACTORINFO_ACTORID_FIELD, pb.LC_LOGIN_CREATEACTOR_ACTORINFO_ACTORNAME_FIELD, pb.LC_LOGIN_CREATEACTOR_ACTORINFO_LEVEL_FIELD, pb.LC_LOGIN_CREATEACTOR_ACTORINFO_VOCATION_FIELD, pb.LC_LOGIN_CREATEACTOR_ACTORINFO_COUNTRY_FIELD}
pb.LC_LOGIN_CREATEACTOR_ACTORINFO.is_extendable = false
pb.LC_LOGIN_CREATEACTOR_ACTORINFO.extensions = {}
pb.LC_LOGIN_CREATEACTOR_ACTORINFO.containing_type = pb.LC_LOGIN_CREATEACTOR
pb.LC_LOGIN_CREATEACTOR_RESULT_FIELD.name = "Result"
pb.LC_LOGIN_CREATEACTOR_RESULT_FIELD.full_name = ".LC_Login_CreateActor.Result"
pb.LC_LOGIN_CREATEACTOR_RESULT_FIELD.number = 1
pb.LC_LOGIN_CREATEACTOR_RESULT_FIELD.index = 0
pb.LC_LOGIN_CREATEACTOR_RESULT_FIELD.label = 2
pb.LC_LOGIN_CREATEACTOR_RESULT_FIELD.has_default_value = false
pb.LC_LOGIN_CREATEACTOR_RESULT_FIELD.default_value = 0
pb.LC_LOGIN_CREATEACTOR_RESULT_FIELD.type = 13
pb.LC_LOGIN_CREATEACTOR_RESULT_FIELD.cpp_type = 3

pb.LC_LOGIN_CREATEACTOR_NEWACTOR_FIELD.name = "NewActor"
pb.LC_LOGIN_CREATEACTOR_NEWACTOR_FIELD.full_name = ".LC_Login_CreateActor.NewActor"
pb.LC_LOGIN_CREATEACTOR_NEWACTOR_FIELD.number = 2
pb.LC_LOGIN_CREATEACTOR_NEWACTOR_FIELD.index = 1
pb.LC_LOGIN_CREATEACTOR_NEWACTOR_FIELD.label = 1
pb.LC_LOGIN_CREATEACTOR_NEWACTOR_FIELD.has_default_value = false
pb.LC_LOGIN_CREATEACTOR_NEWACTOR_FIELD.default_value = nil
pb.LC_LOGIN_CREATEACTOR_NEWACTOR_FIELD.message_type = pb.LC_LOGIN_CREATEACTOR_ACTORINFO
pb.LC_LOGIN_CREATEACTOR_NEWACTOR_FIELD.type = 11
pb.LC_LOGIN_CREATEACTOR_NEWACTOR_FIELD.cpp_type = 10

pb.LC_LOGIN_CREATEACTOR.name = "LC_Login_CreateActor"
pb.LC_LOGIN_CREATEACTOR.full_name = ".LC_Login_CreateActor"
pb.LC_LOGIN_CREATEACTOR.nested_types = {pb.LC_LOGIN_CREATEACTOR_ACTORINFO}
pb.LC_LOGIN_CREATEACTOR.enum_types = {}
pb.LC_LOGIN_CREATEACTOR.fields = {pb.LC_LOGIN_CREATEACTOR_RESULT_FIELD, pb.LC_LOGIN_CREATEACTOR_NEWACTOR_FIELD}
pb.LC_LOGIN_CREATEACTOR.is_extendable = false
pb.LC_LOGIN_CREATEACTOR.extensions = {}
pb.CL_LOGIN_DELETEACTOR_ACTORID_FIELD.name = "ActorID"
pb.CL_LOGIN_DELETEACTOR_ACTORID_FIELD.full_name = ".CL_Login_DeleteActor.ActorID"
pb.CL_LOGIN_DELETEACTOR_ACTORID_FIELD.number = 1
pb.CL_LOGIN_DELETEACTOR_ACTORID_FIELD.index = 0
pb.CL_LOGIN_DELETEACTOR_ACTORID_FIELD.label = 2
pb.CL_LOGIN_DELETEACTOR_ACTORID_FIELD.has_default_value = false
pb.CL_LOGIN_DELETEACTOR_ACTORID_FIELD.default_value = 0
pb.CL_LOGIN_DELETEACTOR_ACTORID_FIELD.type = 13
pb.CL_LOGIN_DELETEACTOR_ACTORID_FIELD.cpp_type = 3

pb.CL_LOGIN_DELETEACTOR_ACTORNAME_FIELD.name = "ActorName"
pb.CL_LOGIN_DELETEACTOR_ACTORNAME_FIELD.full_name = ".CL_Login_DeleteActor.ActorName"
pb.CL_LOGIN_DELETEACTOR_ACTORNAME_FIELD.number = 2
pb.CL_LOGIN_DELETEACTOR_ACTORNAME_FIELD.index = 1
pb.CL_LOGIN_DELETEACTOR_ACTORNAME_FIELD.label = 2
pb.CL_LOGIN_DELETEACTOR_ACTORNAME_FIELD.has_default_value = false
pb.CL_LOGIN_DELETEACTOR_ACTORNAME_FIELD.default_value = ""
pb.CL_LOGIN_DELETEACTOR_ACTORNAME_FIELD.type = 9
pb.CL_LOGIN_DELETEACTOR_ACTORNAME_FIELD.cpp_type = 9

pb.CL_LOGIN_DELETEACTOR.name = "CL_Login_DeleteActor"
pb.CL_LOGIN_DELETEACTOR.full_name = ".CL_Login_DeleteActor"
pb.CL_LOGIN_DELETEACTOR.nested_types = {}
pb.CL_LOGIN_DELETEACTOR.enum_types = {}
pb.CL_LOGIN_DELETEACTOR.fields = {pb.CL_LOGIN_DELETEACTOR_ACTORID_FIELD, pb.CL_LOGIN_DELETEACTOR_ACTORNAME_FIELD}
pb.CL_LOGIN_DELETEACTOR.is_extendable = false
pb.CL_LOGIN_DELETEACTOR.extensions = {}
pb.LC_LOGIN_DELETEACTOR_RESULT_FIELD.name = "Result"
pb.LC_LOGIN_DELETEACTOR_RESULT_FIELD.full_name = ".LC_Login_DeleteActor.Result"
pb.LC_LOGIN_DELETEACTOR_RESULT_FIELD.number = 1
pb.LC_LOGIN_DELETEACTOR_RESULT_FIELD.index = 0
pb.LC_LOGIN_DELETEACTOR_RESULT_FIELD.label = 2
pb.LC_LOGIN_DELETEACTOR_RESULT_FIELD.has_default_value = false
pb.LC_LOGIN_DELETEACTOR_RESULT_FIELD.default_value = 0
pb.LC_LOGIN_DELETEACTOR_RESULT_FIELD.type = 13
pb.LC_LOGIN_DELETEACTOR_RESULT_FIELD.cpp_type = 3

pb.LC_LOGIN_DELETEACTOR_ACTORID_FIELD.name = "ActorID"
pb.LC_LOGIN_DELETEACTOR_ACTORID_FIELD.full_name = ".LC_Login_DeleteActor.ActorID"
pb.LC_LOGIN_DELETEACTOR_ACTORID_FIELD.number = 2
pb.LC_LOGIN_DELETEACTOR_ACTORID_FIELD.index = 1
pb.LC_LOGIN_DELETEACTOR_ACTORID_FIELD.label = 2
pb.LC_LOGIN_DELETEACTOR_ACTORID_FIELD.has_default_value = false
pb.LC_LOGIN_DELETEACTOR_ACTORID_FIELD.default_value = 0
pb.LC_LOGIN_DELETEACTOR_ACTORID_FIELD.type = 13
pb.LC_LOGIN_DELETEACTOR_ACTORID_FIELD.cpp_type = 3

pb.LC_LOGIN_DELETEACTOR.name = "LC_Login_DeleteActor"
pb.LC_LOGIN_DELETEACTOR.full_name = ".LC_Login_DeleteActor"
pb.LC_LOGIN_DELETEACTOR.nested_types = {}
pb.LC_LOGIN_DELETEACTOR.enum_types = {}
pb.LC_LOGIN_DELETEACTOR.fields = {pb.LC_LOGIN_DELETEACTOR_RESULT_FIELD, pb.LC_LOGIN_DELETEACTOR_ACTORID_FIELD}
pb.LC_LOGIN_DELETEACTOR.is_extendable = false
pb.LC_LOGIN_DELETEACTOR.extensions = {}
pb.CL_LOGIN_SELETEACTOR_ACTORID_FIELD.name = "ActorID"
pb.CL_LOGIN_SELETEACTOR_ACTORID_FIELD.full_name = ".CL_Login_SeleteActor.ActorID"
pb.CL_LOGIN_SELETEACTOR_ACTORID_FIELD.number = 1
pb.CL_LOGIN_SELETEACTOR_ACTORID_FIELD.index = 0
pb.CL_LOGIN_SELETEACTOR_ACTORID_FIELD.label = 2
pb.CL_LOGIN_SELETEACTOR_ACTORID_FIELD.has_default_value = false
pb.CL_LOGIN_SELETEACTOR_ACTORID_FIELD.default_value = 0
pb.CL_LOGIN_SELETEACTOR_ACTORID_FIELD.type = 13
pb.CL_LOGIN_SELETEACTOR_ACTORID_FIELD.cpp_type = 3

pb.CL_LOGIN_SELETEACTOR_ACTORNAME_FIELD.name = "ActorName"
pb.CL_LOGIN_SELETEACTOR_ACTORNAME_FIELD.full_name = ".CL_Login_SeleteActor.ActorName"
pb.CL_LOGIN_SELETEACTOR_ACTORNAME_FIELD.number = 2
pb.CL_LOGIN_SELETEACTOR_ACTORNAME_FIELD.index = 1
pb.CL_LOGIN_SELETEACTOR_ACTORNAME_FIELD.label = 2
pb.CL_LOGIN_SELETEACTOR_ACTORNAME_FIELD.has_default_value = false
pb.CL_LOGIN_SELETEACTOR_ACTORNAME_FIELD.default_value = ""
pb.CL_LOGIN_SELETEACTOR_ACTORNAME_FIELD.type = 9
pb.CL_LOGIN_SELETEACTOR_ACTORNAME_FIELD.cpp_type = 9

pb.CL_LOGIN_SELETEACTOR_USERID_FIELD.name = "UserID"
pb.CL_LOGIN_SELETEACTOR_USERID_FIELD.full_name = ".CL_Login_SeleteActor.UserID"
pb.CL_LOGIN_SELETEACTOR_USERID_FIELD.number = 3
pb.CL_LOGIN_SELETEACTOR_USERID_FIELD.index = 2
pb.CL_LOGIN_SELETEACTOR_USERID_FIELD.label = 2
pb.CL_LOGIN_SELETEACTOR_USERID_FIELD.has_default_value = false
pb.CL_LOGIN_SELETEACTOR_USERID_FIELD.default_value = 0
pb.CL_LOGIN_SELETEACTOR_USERID_FIELD.type = 13
pb.CL_LOGIN_SELETEACTOR_USERID_FIELD.cpp_type = 3

pb.CL_LOGIN_SELETEACTOR_ZONEID_FIELD.name = "ZoneID"
pb.CL_LOGIN_SELETEACTOR_ZONEID_FIELD.full_name = ".CL_Login_SeleteActor.ZoneID"
pb.CL_LOGIN_SELETEACTOR_ZONEID_FIELD.number = 4
pb.CL_LOGIN_SELETEACTOR_ZONEID_FIELD.index = 3
pb.CL_LOGIN_SELETEACTOR_ZONEID_FIELD.label = 2
pb.CL_LOGIN_SELETEACTOR_ZONEID_FIELD.has_default_value = false
pb.CL_LOGIN_SELETEACTOR_ZONEID_FIELD.default_value = 0
pb.CL_LOGIN_SELETEACTOR_ZONEID_FIELD.type = 5
pb.CL_LOGIN_SELETEACTOR_ZONEID_FIELD.cpp_type = 1

pb.CL_LOGIN_SELETEACTOR_TOKEN_FIELD.name = "Token"
pb.CL_LOGIN_SELETEACTOR_TOKEN_FIELD.full_name = ".CL_Login_SeleteActor.Token"
pb.CL_LOGIN_SELETEACTOR_TOKEN_FIELD.number = 5
pb.CL_LOGIN_SELETEACTOR_TOKEN_FIELD.index = 4
pb.CL_LOGIN_SELETEACTOR_TOKEN_FIELD.label = 2
pb.CL_LOGIN_SELETEACTOR_TOKEN_FIELD.has_default_value = false
pb.CL_LOGIN_SELETEACTOR_TOKEN_FIELD.default_value = ""
pb.CL_LOGIN_SELETEACTOR_TOKEN_FIELD.type = 9
pb.CL_LOGIN_SELETEACTOR_TOKEN_FIELD.cpp_type = 9

pb.CL_LOGIN_SELETEACTOR.name = "CL_Login_SeleteActor"
pb.CL_LOGIN_SELETEACTOR.full_name = ".CL_Login_SeleteActor"
pb.CL_LOGIN_SELETEACTOR.nested_types = {}
pb.CL_LOGIN_SELETEACTOR.enum_types = {}
pb.CL_LOGIN_SELETEACTOR.fields = {pb.CL_LOGIN_SELETEACTOR_ACTORID_FIELD, pb.CL_LOGIN_SELETEACTOR_ACTORNAME_FIELD, pb.CL_LOGIN_SELETEACTOR_USERID_FIELD, pb.CL_LOGIN_SELETEACTOR_ZONEID_FIELD, pb.CL_LOGIN_SELETEACTOR_TOKEN_FIELD}
pb.CL_LOGIN_SELETEACTOR.is_extendable = false
pb.CL_LOGIN_SELETEACTOR.extensions = {}
pb.LC_LOGIN_SELETEACTOR_RESULT_FIELD.name = "Result"
pb.LC_LOGIN_SELETEACTOR_RESULT_FIELD.full_name = ".LC_Login_SeleteActor.Result"
pb.LC_LOGIN_SELETEACTOR_RESULT_FIELD.number = 1
pb.LC_LOGIN_SELETEACTOR_RESULT_FIELD.index = 0
pb.LC_LOGIN_SELETEACTOR_RESULT_FIELD.label = 2
pb.LC_LOGIN_SELETEACTOR_RESULT_FIELD.has_default_value = false
pb.LC_LOGIN_SELETEACTOR_RESULT_FIELD.default_value = 0
pb.LC_LOGIN_SELETEACTOR_RESULT_FIELD.type = 13
pb.LC_LOGIN_SELETEACTOR_RESULT_FIELD.cpp_type = 3

pb.LC_LOGIN_SELETEACTOR_MODE_FIELD.name = "Mode"
pb.LC_LOGIN_SELETEACTOR_MODE_FIELD.full_name = ".LC_Login_SeleteActor.Mode"
pb.LC_LOGIN_SELETEACTOR_MODE_FIELD.number = 2
pb.LC_LOGIN_SELETEACTOR_MODE_FIELD.index = 1
pb.LC_LOGIN_SELETEACTOR_MODE_FIELD.label = 2
pb.LC_LOGIN_SELETEACTOR_MODE_FIELD.has_default_value = false
pb.LC_LOGIN_SELETEACTOR_MODE_FIELD.default_value = 0
pb.LC_LOGIN_SELETEACTOR_MODE_FIELD.type = 13
pb.LC_LOGIN_SELETEACTOR_MODE_FIELD.cpp_type = 3

pb.LC_LOGIN_SELETEACTOR.name = "LC_Login_SeleteActor"
pb.LC_LOGIN_SELETEACTOR.full_name = ".LC_Login_SeleteActor"
pb.LC_LOGIN_SELETEACTOR.nested_types = {}
pb.LC_LOGIN_SELETEACTOR.enum_types = {}
pb.LC_LOGIN_SELETEACTOR.fields = {pb.LC_LOGIN_SELETEACTOR_RESULT_FIELD, pb.LC_LOGIN_SELETEACTOR_MODE_FIELD}
pb.LC_LOGIN_SELETEACTOR.is_extendable = false
pb.LC_LOGIN_SELETEACTOR.extensions = {}
pb.LS_LOGIN_SELETEACTOR_CLIENTID_FIELD.name = "ClientID"
pb.LS_LOGIN_SELETEACTOR_CLIENTID_FIELD.full_name = ".LS_Login_SeleteActor.ClientID"
pb.LS_LOGIN_SELETEACTOR_CLIENTID_FIELD.number = 1
pb.LS_LOGIN_SELETEACTOR_CLIENTID_FIELD.index = 0
pb.LS_LOGIN_SELETEACTOR_CLIENTID_FIELD.label = 2
pb.LS_LOGIN_SELETEACTOR_CLIENTID_FIELD.has_default_value = false
pb.LS_LOGIN_SELETEACTOR_CLIENTID_FIELD.default_value = 0
pb.LS_LOGIN_SELETEACTOR_CLIENTID_FIELD.type = 13
pb.LS_LOGIN_SELETEACTOR_CLIENTID_FIELD.cpp_type = 3

pb.LS_LOGIN_SELETEACTOR_USERID_FIELD.name = "UserID"
pb.LS_LOGIN_SELETEACTOR_USERID_FIELD.full_name = ".LS_Login_SeleteActor.UserID"
pb.LS_LOGIN_SELETEACTOR_USERID_FIELD.number = 2
pb.LS_LOGIN_SELETEACTOR_USERID_FIELD.index = 1
pb.LS_LOGIN_SELETEACTOR_USERID_FIELD.label = 2
pb.LS_LOGIN_SELETEACTOR_USERID_FIELD.has_default_value = false
pb.LS_LOGIN_SELETEACTOR_USERID_FIELD.default_value = 0
pb.LS_LOGIN_SELETEACTOR_USERID_FIELD.type = 13
pb.LS_LOGIN_SELETEACTOR_USERID_FIELD.cpp_type = 3

pb.LS_LOGIN_SELETEACTOR_ACTORID_FIELD.name = "ActorID"
pb.LS_LOGIN_SELETEACTOR_ACTORID_FIELD.full_name = ".LS_Login_SeleteActor.ActorID"
pb.LS_LOGIN_SELETEACTOR_ACTORID_FIELD.number = 3
pb.LS_LOGIN_SELETEACTOR_ACTORID_FIELD.index = 2
pb.LS_LOGIN_SELETEACTOR_ACTORID_FIELD.label = 2
pb.LS_LOGIN_SELETEACTOR_ACTORID_FIELD.has_default_value = false
pb.LS_LOGIN_SELETEACTOR_ACTORID_FIELD.default_value = 0
pb.LS_LOGIN_SELETEACTOR_ACTORID_FIELD.type = 13
pb.LS_LOGIN_SELETEACTOR_ACTORID_FIELD.cpp_type = 3

pb.LS_LOGIN_SELETEACTOR_ACTORNAME_FIELD.name = "ActorName"
pb.LS_LOGIN_SELETEACTOR_ACTORNAME_FIELD.full_name = ".LS_Login_SeleteActor.ActorName"
pb.LS_LOGIN_SELETEACTOR_ACTORNAME_FIELD.number = 4
pb.LS_LOGIN_SELETEACTOR_ACTORNAME_FIELD.index = 3
pb.LS_LOGIN_SELETEACTOR_ACTORNAME_FIELD.label = 2
pb.LS_LOGIN_SELETEACTOR_ACTORNAME_FIELD.has_default_value = false
pb.LS_LOGIN_SELETEACTOR_ACTORNAME_FIELD.default_value = ""
pb.LS_LOGIN_SELETEACTOR_ACTORNAME_FIELD.type = 9
pb.LS_LOGIN_SELETEACTOR_ACTORNAME_FIELD.cpp_type = 9

pb.LS_LOGIN_SELETEACTOR.name = "LS_Login_SeleteActor"
pb.LS_LOGIN_SELETEACTOR.full_name = ".LS_Login_SeleteActor"
pb.LS_LOGIN_SELETEACTOR.nested_types = {}
pb.LS_LOGIN_SELETEACTOR.enum_types = {}
pb.LS_LOGIN_SELETEACTOR.fields = {pb.LS_LOGIN_SELETEACTOR_CLIENTID_FIELD, pb.LS_LOGIN_SELETEACTOR_USERID_FIELD, pb.LS_LOGIN_SELETEACTOR_ACTORID_FIELD, pb.LS_LOGIN_SELETEACTOR_ACTORNAME_FIELD}
pb.LS_LOGIN_SELETEACTOR.is_extendable = false
pb.LS_LOGIN_SELETEACTOR.extensions = {}
pb.CL_LOGIN_RECONNECT_USERNAME_FIELD.name = "UserName"
pb.CL_LOGIN_RECONNECT_USERNAME_FIELD.full_name = ".CL_Login_Reconnect.UserName"
pb.CL_LOGIN_RECONNECT_USERNAME_FIELD.number = 1
pb.CL_LOGIN_RECONNECT_USERNAME_FIELD.index = 0
pb.CL_LOGIN_RECONNECT_USERNAME_FIELD.label = 2
pb.CL_LOGIN_RECONNECT_USERNAME_FIELD.has_default_value = false
pb.CL_LOGIN_RECONNECT_USERNAME_FIELD.default_value = ""
pb.CL_LOGIN_RECONNECT_USERNAME_FIELD.type = 9
pb.CL_LOGIN_RECONNECT_USERNAME_FIELD.cpp_type = 9

pb.CL_LOGIN_RECONNECT_PASSWORD_FIELD.name = "Password"
pb.CL_LOGIN_RECONNECT_PASSWORD_FIELD.full_name = ".CL_Login_Reconnect.Password"
pb.CL_LOGIN_RECONNECT_PASSWORD_FIELD.number = 2
pb.CL_LOGIN_RECONNECT_PASSWORD_FIELD.index = 1
pb.CL_LOGIN_RECONNECT_PASSWORD_FIELD.label = 2
pb.CL_LOGIN_RECONNECT_PASSWORD_FIELD.has_default_value = false
pb.CL_LOGIN_RECONNECT_PASSWORD_FIELD.default_value = ""
pb.CL_LOGIN_RECONNECT_PASSWORD_FIELD.type = 9
pb.CL_LOGIN_RECONNECT_PASSWORD_FIELD.cpp_type = 9

pb.CL_LOGIN_RECONNECT_LOGINID_FIELD.name = "LoginID"
pb.CL_LOGIN_RECONNECT_LOGINID_FIELD.full_name = ".CL_Login_Reconnect.LoginID"
pb.CL_LOGIN_RECONNECT_LOGINID_FIELD.number = 3
pb.CL_LOGIN_RECONNECT_LOGINID_FIELD.index = 2
pb.CL_LOGIN_RECONNECT_LOGINID_FIELD.label = 2
pb.CL_LOGIN_RECONNECT_LOGINID_FIELD.has_default_value = false
pb.CL_LOGIN_RECONNECT_LOGINID_FIELD.default_value = 0
pb.CL_LOGIN_RECONNECT_LOGINID_FIELD.type = 13
pb.CL_LOGIN_RECONNECT_LOGINID_FIELD.cpp_type = 3

pb.CL_LOGIN_RECONNECT_STATE_FIELD.name = "State"
pb.CL_LOGIN_RECONNECT_STATE_FIELD.full_name = ".CL_Login_Reconnect.State"
pb.CL_LOGIN_RECONNECT_STATE_FIELD.number = 4
pb.CL_LOGIN_RECONNECT_STATE_FIELD.index = 3
pb.CL_LOGIN_RECONNECT_STATE_FIELD.label = 2
pb.CL_LOGIN_RECONNECT_STATE_FIELD.has_default_value = false
pb.CL_LOGIN_RECONNECT_STATE_FIELD.default_value = nil
pb.CL_LOGIN_RECONNECT_STATE_FIELD.enum_type = MSG_LOGIN_PLAYER_STATE
pb.CL_LOGIN_RECONNECT_STATE_FIELD.type = 14
pb.CL_LOGIN_RECONNECT_STATE_FIELD.cpp_type = 8

pb.CL_LOGIN_RECONNECT.name = "CL_Login_Reconnect"
pb.CL_LOGIN_RECONNECT.full_name = ".CL_Login_Reconnect"
pb.CL_LOGIN_RECONNECT.nested_types = {}
pb.CL_LOGIN_RECONNECT.enum_types = {}
pb.CL_LOGIN_RECONNECT.fields = {pb.CL_LOGIN_RECONNECT_USERNAME_FIELD, pb.CL_LOGIN_RECONNECT_PASSWORD_FIELD, pb.CL_LOGIN_RECONNECT_LOGINID_FIELD, pb.CL_LOGIN_RECONNECT_STATE_FIELD}
pb.CL_LOGIN_RECONNECT.is_extendable = false
pb.CL_LOGIN_RECONNECT.extensions = {}
pb.CL_LOGIN_RECONNECT_UC_SID_FIELD.name = "Sid"
pb.CL_LOGIN_RECONNECT_UC_SID_FIELD.full_name = ".CL_Login_Reconnect_UC.Sid"
pb.CL_LOGIN_RECONNECT_UC_SID_FIELD.number = 1
pb.CL_LOGIN_RECONNECT_UC_SID_FIELD.index = 0
pb.CL_LOGIN_RECONNECT_UC_SID_FIELD.label = 2
pb.CL_LOGIN_RECONNECT_UC_SID_FIELD.has_default_value = false
pb.CL_LOGIN_RECONNECT_UC_SID_FIELD.default_value = ""
pb.CL_LOGIN_RECONNECT_UC_SID_FIELD.type = 9
pb.CL_LOGIN_RECONNECT_UC_SID_FIELD.cpp_type = 9

pb.CL_LOGIN_RECONNECT_UC_LOGINID_FIELD.name = "LoginID"
pb.CL_LOGIN_RECONNECT_UC_LOGINID_FIELD.full_name = ".CL_Login_Reconnect_UC.LoginID"
pb.CL_LOGIN_RECONNECT_UC_LOGINID_FIELD.number = 2
pb.CL_LOGIN_RECONNECT_UC_LOGINID_FIELD.index = 1
pb.CL_LOGIN_RECONNECT_UC_LOGINID_FIELD.label = 2
pb.CL_LOGIN_RECONNECT_UC_LOGINID_FIELD.has_default_value = false
pb.CL_LOGIN_RECONNECT_UC_LOGINID_FIELD.default_value = 0
pb.CL_LOGIN_RECONNECT_UC_LOGINID_FIELD.type = 13
pb.CL_LOGIN_RECONNECT_UC_LOGINID_FIELD.cpp_type = 3

pb.CL_LOGIN_RECONNECT_UC_STATE_FIELD.name = "State"
pb.CL_LOGIN_RECONNECT_UC_STATE_FIELD.full_name = ".CL_Login_Reconnect_UC.State"
pb.CL_LOGIN_RECONNECT_UC_STATE_FIELD.number = 3
pb.CL_LOGIN_RECONNECT_UC_STATE_FIELD.index = 2
pb.CL_LOGIN_RECONNECT_UC_STATE_FIELD.label = 2
pb.CL_LOGIN_RECONNECT_UC_STATE_FIELD.has_default_value = false
pb.CL_LOGIN_RECONNECT_UC_STATE_FIELD.default_value = nil
pb.CL_LOGIN_RECONNECT_UC_STATE_FIELD.enum_type = MSG_LOGIN_PLAYER_STATE
pb.CL_LOGIN_RECONNECT_UC_STATE_FIELD.type = 14
pb.CL_LOGIN_RECONNECT_UC_STATE_FIELD.cpp_type = 8

pb.CL_LOGIN_RECONNECT_UC_SIDMD5_FIELD.name = "SidMd5"
pb.CL_LOGIN_RECONNECT_UC_SIDMD5_FIELD.full_name = ".CL_Login_Reconnect_UC.SidMd5"
pb.CL_LOGIN_RECONNECT_UC_SIDMD5_FIELD.number = 4
pb.CL_LOGIN_RECONNECT_UC_SIDMD5_FIELD.index = 3
pb.CL_LOGIN_RECONNECT_UC_SIDMD5_FIELD.label = 2
pb.CL_LOGIN_RECONNECT_UC_SIDMD5_FIELD.has_default_value = false
pb.CL_LOGIN_RECONNECT_UC_SIDMD5_FIELD.default_value = ""
pb.CL_LOGIN_RECONNECT_UC_SIDMD5_FIELD.type = 9
pb.CL_LOGIN_RECONNECT_UC_SIDMD5_FIELD.cpp_type = 9

pb.CL_LOGIN_RECONNECT_UC_DEBUGMODE_FIELD.name = "DebugMode"
pb.CL_LOGIN_RECONNECT_UC_DEBUGMODE_FIELD.full_name = ".CL_Login_Reconnect_UC.DebugMode"
pb.CL_LOGIN_RECONNECT_UC_DEBUGMODE_FIELD.number = 5
pb.CL_LOGIN_RECONNECT_UC_DEBUGMODE_FIELD.index = 4
pb.CL_LOGIN_RECONNECT_UC_DEBUGMODE_FIELD.label = 2
pb.CL_LOGIN_RECONNECT_UC_DEBUGMODE_FIELD.has_default_value = false
pb.CL_LOGIN_RECONNECT_UC_DEBUGMODE_FIELD.default_value = 0
pb.CL_LOGIN_RECONNECT_UC_DEBUGMODE_FIELD.type = 5
pb.CL_LOGIN_RECONNECT_UC_DEBUGMODE_FIELD.cpp_type = 1

pb.CL_LOGIN_RECONNECT_UC.name = "CL_Login_Reconnect_UC"
pb.CL_LOGIN_RECONNECT_UC.full_name = ".CL_Login_Reconnect_UC"
pb.CL_LOGIN_RECONNECT_UC.nested_types = {}
pb.CL_LOGIN_RECONNECT_UC.enum_types = {}
pb.CL_LOGIN_RECONNECT_UC.fields = {pb.CL_LOGIN_RECONNECT_UC_SID_FIELD, pb.CL_LOGIN_RECONNECT_UC_LOGINID_FIELD, pb.CL_LOGIN_RECONNECT_UC_STATE_FIELD, pb.CL_LOGIN_RECONNECT_UC_SIDMD5_FIELD, pb.CL_LOGIN_RECONNECT_UC_DEBUGMODE_FIELD}
pb.CL_LOGIN_RECONNECT_UC.is_extendable = false
pb.CL_LOGIN_RECONNECT_UC.extensions = {}
pb.LC_LOGIN_RECONNECT_RESULT_FIELD.name = "Result"
pb.LC_LOGIN_RECONNECT_RESULT_FIELD.full_name = ".LC_Login_Reconnect.Result"
pb.LC_LOGIN_RECONNECT_RESULT_FIELD.number = 1
pb.LC_LOGIN_RECONNECT_RESULT_FIELD.index = 0
pb.LC_LOGIN_RECONNECT_RESULT_FIELD.label = 2
pb.LC_LOGIN_RECONNECT_RESULT_FIELD.has_default_value = false
pb.LC_LOGIN_RECONNECT_RESULT_FIELD.default_value = 0
pb.LC_LOGIN_RECONNECT_RESULT_FIELD.type = 13
pb.LC_LOGIN_RECONNECT_RESULT_FIELD.cpp_type = 3

pb.LC_LOGIN_RECONNECT.name = "LC_Login_Reconnect"
pb.LC_LOGIN_RECONNECT.full_name = ".LC_Login_Reconnect"
pb.LC_LOGIN_RECONNECT.nested_types = {}
pb.LC_LOGIN_RECONNECT.enum_types = {}
pb.LC_LOGIN_RECONNECT.fields = {pb.LC_LOGIN_RECONNECT_RESULT_FIELD}
pb.LC_LOGIN_RECONNECT.is_extendable = false
pb.LC_LOGIN_RECONNECT.extensions = {}
pb.SL_LOGIN_ADDRECONNECT_USERID_FIELD.name = "UserID"
pb.SL_LOGIN_ADDRECONNECT_USERID_FIELD.full_name = ".SL_Login_AddReconnect.UserID"
pb.SL_LOGIN_ADDRECONNECT_USERID_FIELD.number = 1
pb.SL_LOGIN_ADDRECONNECT_USERID_FIELD.index = 0
pb.SL_LOGIN_ADDRECONNECT_USERID_FIELD.label = 2
pb.SL_LOGIN_ADDRECONNECT_USERID_FIELD.has_default_value = false
pb.SL_LOGIN_ADDRECONNECT_USERID_FIELD.default_value = 0
pb.SL_LOGIN_ADDRECONNECT_USERID_FIELD.type = 13
pb.SL_LOGIN_ADDRECONNECT_USERID_FIELD.cpp_type = 3

pb.SL_LOGIN_ADDRECONNECT_ACTORID_FIELD.name = "ActorID"
pb.SL_LOGIN_ADDRECONNECT_ACTORID_FIELD.full_name = ".SL_Login_AddReconnect.ActorID"
pb.SL_LOGIN_ADDRECONNECT_ACTORID_FIELD.number = 2
pb.SL_LOGIN_ADDRECONNECT_ACTORID_FIELD.index = 1
pb.SL_LOGIN_ADDRECONNECT_ACTORID_FIELD.label = 2
pb.SL_LOGIN_ADDRECONNECT_ACTORID_FIELD.has_default_value = false
pb.SL_LOGIN_ADDRECONNECT_ACTORID_FIELD.default_value = 0
pb.SL_LOGIN_ADDRECONNECT_ACTORID_FIELD.type = 13
pb.SL_LOGIN_ADDRECONNECT_ACTORID_FIELD.cpp_type = 3

pb.SL_LOGIN_ADDRECONNECT.name = "SL_Login_AddReconnect"
pb.SL_LOGIN_ADDRECONNECT.full_name = ".SL_Login_AddReconnect"
pb.SL_LOGIN_ADDRECONNECT.nested_types = {}
pb.SL_LOGIN_ADDRECONNECT.enum_types = {}
pb.SL_LOGIN_ADDRECONNECT.fields = {pb.SL_LOGIN_ADDRECONNECT_USERID_FIELD, pb.SL_LOGIN_ADDRECONNECT_ACTORID_FIELD}
pb.SL_LOGIN_ADDRECONNECT.is_extendable = false
pb.SL_LOGIN_ADDRECONNECT.extensions = {}
pb.SL_LOGIN_DELRECONNECT_USERID_FIELD.name = "UserID"
pb.SL_LOGIN_DELRECONNECT_USERID_FIELD.full_name = ".SL_Login_DelReconnect.UserID"
pb.SL_LOGIN_DELRECONNECT_USERID_FIELD.number = 1
pb.SL_LOGIN_DELRECONNECT_USERID_FIELD.index = 0
pb.SL_LOGIN_DELRECONNECT_USERID_FIELD.label = 2
pb.SL_LOGIN_DELRECONNECT_USERID_FIELD.has_default_value = false
pb.SL_LOGIN_DELRECONNECT_USERID_FIELD.default_value = 0
pb.SL_LOGIN_DELRECONNECT_USERID_FIELD.type = 13
pb.SL_LOGIN_DELRECONNECT_USERID_FIELD.cpp_type = 3

pb.SL_LOGIN_DELRECONNECT.name = "SL_Login_DelReconnect"
pb.SL_LOGIN_DELRECONNECT.full_name = ".SL_Login_DelReconnect"
pb.SL_LOGIN_DELRECONNECT.nested_types = {}
pb.SL_LOGIN_DELRECONNECT.enum_types = {}
pb.SL_LOGIN_DELRECONNECT.fields = {pb.SL_LOGIN_DELRECONNECT_USERID_FIELD}
pb.SL_LOGIN_DELRECONNECT.is_extendable = false
pb.SL_LOGIN_DELRECONNECT.extensions = {}
pb.LS_LOGIN_RECONNECT_USERID_FIELD.name = "UserID"
pb.LS_LOGIN_RECONNECT_USERID_FIELD.full_name = ".LS_Login_Reconnect.UserID"
pb.LS_LOGIN_RECONNECT_USERID_FIELD.number = 1
pb.LS_LOGIN_RECONNECT_USERID_FIELD.index = 0
pb.LS_LOGIN_RECONNECT_USERID_FIELD.label = 2
pb.LS_LOGIN_RECONNECT_USERID_FIELD.has_default_value = false
pb.LS_LOGIN_RECONNECT_USERID_FIELD.default_value = 0
pb.LS_LOGIN_RECONNECT_USERID_FIELD.type = 13
pb.LS_LOGIN_RECONNECT_USERID_FIELD.cpp_type = 3

pb.LS_LOGIN_RECONNECT_NEWCLIENTID_FIELD.name = "NewClientID"
pb.LS_LOGIN_RECONNECT_NEWCLIENTID_FIELD.full_name = ".LS_Login_Reconnect.NewClientID"
pb.LS_LOGIN_RECONNECT_NEWCLIENTID_FIELD.number = 2
pb.LS_LOGIN_RECONNECT_NEWCLIENTID_FIELD.index = 1
pb.LS_LOGIN_RECONNECT_NEWCLIENTID_FIELD.label = 2
pb.LS_LOGIN_RECONNECT_NEWCLIENTID_FIELD.has_default_value = false
pb.LS_LOGIN_RECONNECT_NEWCLIENTID_FIELD.default_value = 0
pb.LS_LOGIN_RECONNECT_NEWCLIENTID_FIELD.type = 13
pb.LS_LOGIN_RECONNECT_NEWCLIENTID_FIELD.cpp_type = 3

pb.LS_LOGIN_RECONNECT.name = "LS_Login_Reconnect"
pb.LS_LOGIN_RECONNECT.full_name = ".LS_Login_Reconnect"
pb.LS_LOGIN_RECONNECT.nested_types = {}
pb.LS_LOGIN_RECONNECT.enum_types = {}
pb.LS_LOGIN_RECONNECT.fields = {pb.LS_LOGIN_RECONNECT_USERID_FIELD, pb.LS_LOGIN_RECONNECT_NEWCLIENTID_FIELD}
pb.LS_LOGIN_RECONNECT.is_extendable = false
pb.LS_LOGIN_RECONNECT.extensions = {}
pb.CL_LOGIN_LOGINUSER_UC_ZONEID_FIELD.name = "ZoneID"
pb.CL_LOGIN_LOGINUSER_UC_ZONEID_FIELD.full_name = ".CL_Login_LoginUser_UC.ZoneID"
pb.CL_LOGIN_LOGINUSER_UC_ZONEID_FIELD.number = 1
pb.CL_LOGIN_LOGINUSER_UC_ZONEID_FIELD.index = 0
pb.CL_LOGIN_LOGINUSER_UC_ZONEID_FIELD.label = 2
pb.CL_LOGIN_LOGINUSER_UC_ZONEID_FIELD.has_default_value = false
pb.CL_LOGIN_LOGINUSER_UC_ZONEID_FIELD.default_value = 0
pb.CL_LOGIN_LOGINUSER_UC_ZONEID_FIELD.type = 13
pb.CL_LOGIN_LOGINUSER_UC_ZONEID_FIELD.cpp_type = 3

pb.CL_LOGIN_LOGINUSER_UC_SID_FIELD.name = "Sid"
pb.CL_LOGIN_LOGINUSER_UC_SID_FIELD.full_name = ".CL_Login_LoginUser_UC.Sid"
pb.CL_LOGIN_LOGINUSER_UC_SID_FIELD.number = 2
pb.CL_LOGIN_LOGINUSER_UC_SID_FIELD.index = 1
pb.CL_LOGIN_LOGINUSER_UC_SID_FIELD.label = 2
pb.CL_LOGIN_LOGINUSER_UC_SID_FIELD.has_default_value = false
pb.CL_LOGIN_LOGINUSER_UC_SID_FIELD.default_value = ""
pb.CL_LOGIN_LOGINUSER_UC_SID_FIELD.type = 9
pb.CL_LOGIN_LOGINUSER_UC_SID_FIELD.cpp_type = 9

pb.CL_LOGIN_LOGINUSER_UC_SIDMD5_FIELD.name = "SidMd5"
pb.CL_LOGIN_LOGINUSER_UC_SIDMD5_FIELD.full_name = ".CL_Login_LoginUser_UC.SidMd5"
pb.CL_LOGIN_LOGINUSER_UC_SIDMD5_FIELD.number = 3
pb.CL_LOGIN_LOGINUSER_UC_SIDMD5_FIELD.index = 2
pb.CL_LOGIN_LOGINUSER_UC_SIDMD5_FIELD.label = 2
pb.CL_LOGIN_LOGINUSER_UC_SIDMD5_FIELD.has_default_value = false
pb.CL_LOGIN_LOGINUSER_UC_SIDMD5_FIELD.default_value = ""
pb.CL_LOGIN_LOGINUSER_UC_SIDMD5_FIELD.type = 9
pb.CL_LOGIN_LOGINUSER_UC_SIDMD5_FIELD.cpp_type = 9

pb.CL_LOGIN_LOGINUSER_UC_DEBUGMODE_FIELD.name = "DebugMode"
pb.CL_LOGIN_LOGINUSER_UC_DEBUGMODE_FIELD.full_name = ".CL_Login_LoginUser_UC.DebugMode"
pb.CL_LOGIN_LOGINUSER_UC_DEBUGMODE_FIELD.number = 4
pb.CL_LOGIN_LOGINUSER_UC_DEBUGMODE_FIELD.index = 3
pb.CL_LOGIN_LOGINUSER_UC_DEBUGMODE_FIELD.label = 2
pb.CL_LOGIN_LOGINUSER_UC_DEBUGMODE_FIELD.has_default_value = false
pb.CL_LOGIN_LOGINUSER_UC_DEBUGMODE_FIELD.default_value = 0
pb.CL_LOGIN_LOGINUSER_UC_DEBUGMODE_FIELD.type = 5
pb.CL_LOGIN_LOGINUSER_UC_DEBUGMODE_FIELD.cpp_type = 1

pb.CL_LOGIN_LOGINUSER_UC_TS_FIELD.name = "Ts"
pb.CL_LOGIN_LOGINUSER_UC_TS_FIELD.full_name = ".CL_Login_LoginUser_UC.Ts"
pb.CL_LOGIN_LOGINUSER_UC_TS_FIELD.number = 5
pb.CL_LOGIN_LOGINUSER_UC_TS_FIELD.index = 4
pb.CL_LOGIN_LOGINUSER_UC_TS_FIELD.label = 2
pb.CL_LOGIN_LOGINUSER_UC_TS_FIELD.has_default_value = false
pb.CL_LOGIN_LOGINUSER_UC_TS_FIELD.default_value = ""
pb.CL_LOGIN_LOGINUSER_UC_TS_FIELD.type = 9
pb.CL_LOGIN_LOGINUSER_UC_TS_FIELD.cpp_type = 9

pb.CL_LOGIN_LOGINUSER_UC_UUID_FIELD.name = "UUID"
pb.CL_LOGIN_LOGINUSER_UC_UUID_FIELD.full_name = ".CL_Login_LoginUser_UC.UUID"
pb.CL_LOGIN_LOGINUSER_UC_UUID_FIELD.number = 6
pb.CL_LOGIN_LOGINUSER_UC_UUID_FIELD.index = 5
pb.CL_LOGIN_LOGINUSER_UC_UUID_FIELD.label = 1
pb.CL_LOGIN_LOGINUSER_UC_UUID_FIELD.has_default_value = false
pb.CL_LOGIN_LOGINUSER_UC_UUID_FIELD.default_value = ""
pb.CL_LOGIN_LOGINUSER_UC_UUID_FIELD.type = 9
pb.CL_LOGIN_LOGINUSER_UC_UUID_FIELD.cpp_type = 9

pb.CL_LOGIN_LOGINUSER_UC.name = "CL_Login_LoginUser_UC"
pb.CL_LOGIN_LOGINUSER_UC.full_name = ".CL_Login_LoginUser_UC"
pb.CL_LOGIN_LOGINUSER_UC.nested_types = {}
pb.CL_LOGIN_LOGINUSER_UC.enum_types = {}
pb.CL_LOGIN_LOGINUSER_UC.fields = {pb.CL_LOGIN_LOGINUSER_UC_ZONEID_FIELD, pb.CL_LOGIN_LOGINUSER_UC_SID_FIELD, pb.CL_LOGIN_LOGINUSER_UC_SIDMD5_FIELD, pb.CL_LOGIN_LOGINUSER_UC_DEBUGMODE_FIELD, pb.CL_LOGIN_LOGINUSER_UC_TS_FIELD, pb.CL_LOGIN_LOGINUSER_UC_UUID_FIELD}
pb.CL_LOGIN_LOGINUSER_UC.is_extendable = false
pb.CL_LOGIN_LOGINUSER_UC.extensions = {}
pb.CL_LOGIN_SWITCHACTOR_ACTORID_FIELD.name = "ActorID"
pb.CL_LOGIN_SWITCHACTOR_ACTORID_FIELD.full_name = ".CL_Login_SwitchActor.ActorID"
pb.CL_LOGIN_SWITCHACTOR_ACTORID_FIELD.number = 1
pb.CL_LOGIN_SWITCHACTOR_ACTORID_FIELD.index = 0
pb.CL_LOGIN_SWITCHACTOR_ACTORID_FIELD.label = 2
pb.CL_LOGIN_SWITCHACTOR_ACTORID_FIELD.has_default_value = false
pb.CL_LOGIN_SWITCHACTOR_ACTORID_FIELD.default_value = 0
pb.CL_LOGIN_SWITCHACTOR_ACTORID_FIELD.type = 13
pb.CL_LOGIN_SWITCHACTOR_ACTORID_FIELD.cpp_type = 3

pb.CL_LOGIN_SWITCHACTOR_ACTORNAME_FIELD.name = "ActorName"
pb.CL_LOGIN_SWITCHACTOR_ACTORNAME_FIELD.full_name = ".CL_Login_SwitchActor.ActorName"
pb.CL_LOGIN_SWITCHACTOR_ACTORNAME_FIELD.number = 2
pb.CL_LOGIN_SWITCHACTOR_ACTORNAME_FIELD.index = 1
pb.CL_LOGIN_SWITCHACTOR_ACTORNAME_FIELD.label = 2
pb.CL_LOGIN_SWITCHACTOR_ACTORNAME_FIELD.has_default_value = false
pb.CL_LOGIN_SWITCHACTOR_ACTORNAME_FIELD.default_value = ""
pb.CL_LOGIN_SWITCHACTOR_ACTORNAME_FIELD.type = 9
pb.CL_LOGIN_SWITCHACTOR_ACTORNAME_FIELD.cpp_type = 9

pb.CL_LOGIN_SWITCHACTOR.name = "CL_Login_SwitchActor"
pb.CL_LOGIN_SWITCHACTOR.full_name = ".CL_Login_SwitchActor"
pb.CL_LOGIN_SWITCHACTOR.nested_types = {}
pb.CL_LOGIN_SWITCHACTOR.enum_types = {}
pb.CL_LOGIN_SWITCHACTOR.fields = {pb.CL_LOGIN_SWITCHACTOR_ACTORID_FIELD, pb.CL_LOGIN_SWITCHACTOR_ACTORNAME_FIELD}
pb.CL_LOGIN_SWITCHACTOR.is_extendable = false
pb.CL_LOGIN_SWITCHACTOR.extensions = {}
pb.LC_LOGIN_SWITCHACTOR_RESULT_FIELD.name = "Result"
pb.LC_LOGIN_SWITCHACTOR_RESULT_FIELD.full_name = ".LC_Login_SwitchActor.Result"
pb.LC_LOGIN_SWITCHACTOR_RESULT_FIELD.number = 1
pb.LC_LOGIN_SWITCHACTOR_RESULT_FIELD.index = 0
pb.LC_LOGIN_SWITCHACTOR_RESULT_FIELD.label = 2
pb.LC_LOGIN_SWITCHACTOR_RESULT_FIELD.has_default_value = false
pb.LC_LOGIN_SWITCHACTOR_RESULT_FIELD.default_value = 0
pb.LC_LOGIN_SWITCHACTOR_RESULT_FIELD.type = 13
pb.LC_LOGIN_SWITCHACTOR_RESULT_FIELD.cpp_type = 3

pb.LC_LOGIN_SWITCHACTOR.name = "LC_Login_SwitchActor"
pb.LC_LOGIN_SWITCHACTOR.full_name = ".LC_Login_SwitchActor"
pb.LC_LOGIN_SWITCHACTOR.nested_types = {}
pb.LC_LOGIN_SWITCHACTOR.enum_types = {}
pb.LC_LOGIN_SWITCHACTOR.fields = {pb.LC_LOGIN_SWITCHACTOR_RESULT_FIELD}
pb.LC_LOGIN_SWITCHACTOR.is_extendable = false
pb.LC_LOGIN_SWITCHACTOR.extensions = {}
pb.CL_LOGIN_LOGINUSER_MI_ZONEID_FIELD.name = "ZoneID"
pb.CL_LOGIN_LOGINUSER_MI_ZONEID_FIELD.full_name = ".CL_Login_LoginUser_MI.ZoneID"
pb.CL_LOGIN_LOGINUSER_MI_ZONEID_FIELD.number = 1
pb.CL_LOGIN_LOGINUSER_MI_ZONEID_FIELD.index = 0
pb.CL_LOGIN_LOGINUSER_MI_ZONEID_FIELD.label = 2
pb.CL_LOGIN_LOGINUSER_MI_ZONEID_FIELD.has_default_value = false
pb.CL_LOGIN_LOGINUSER_MI_ZONEID_FIELD.default_value = 0
pb.CL_LOGIN_LOGINUSER_MI_ZONEID_FIELD.type = 13
pb.CL_LOGIN_LOGINUSER_MI_ZONEID_FIELD.cpp_type = 3

pb.CL_LOGIN_LOGINUSER_MI_UID_FIELD.name = "Uid"
pb.CL_LOGIN_LOGINUSER_MI_UID_FIELD.full_name = ".CL_Login_LoginUser_MI.Uid"
pb.CL_LOGIN_LOGINUSER_MI_UID_FIELD.number = 2
pb.CL_LOGIN_LOGINUSER_MI_UID_FIELD.index = 1
pb.CL_LOGIN_LOGINUSER_MI_UID_FIELD.label = 2
pb.CL_LOGIN_LOGINUSER_MI_UID_FIELD.has_default_value = false
pb.CL_LOGIN_LOGINUSER_MI_UID_FIELD.default_value = 0
pb.CL_LOGIN_LOGINUSER_MI_UID_FIELD.type = 3
pb.CL_LOGIN_LOGINUSER_MI_UID_FIELD.cpp_type = 2

pb.CL_LOGIN_LOGINUSER_MI_SID_FIELD.name = "Sid"
pb.CL_LOGIN_LOGINUSER_MI_SID_FIELD.full_name = ".CL_Login_LoginUser_MI.Sid"
pb.CL_LOGIN_LOGINUSER_MI_SID_FIELD.number = 3
pb.CL_LOGIN_LOGINUSER_MI_SID_FIELD.index = 2
pb.CL_LOGIN_LOGINUSER_MI_SID_FIELD.label = 2
pb.CL_LOGIN_LOGINUSER_MI_SID_FIELD.has_default_value = false
pb.CL_LOGIN_LOGINUSER_MI_SID_FIELD.default_value = ""
pb.CL_LOGIN_LOGINUSER_MI_SID_FIELD.type = 9
pb.CL_LOGIN_LOGINUSER_MI_SID_FIELD.cpp_type = 9

pb.CL_LOGIN_LOGINUSER_MI.name = "CL_Login_LoginUser_MI"
pb.CL_LOGIN_LOGINUSER_MI.full_name = ".CL_Login_LoginUser_MI"
pb.CL_LOGIN_LOGINUSER_MI.nested_types = {}
pb.CL_LOGIN_LOGINUSER_MI.enum_types = {}
pb.CL_LOGIN_LOGINUSER_MI.fields = {pb.CL_LOGIN_LOGINUSER_MI_ZONEID_FIELD, pb.CL_LOGIN_LOGINUSER_MI_UID_FIELD, pb.CL_LOGIN_LOGINUSER_MI_SID_FIELD}
pb.CL_LOGIN_LOGINUSER_MI.is_extendable = false
pb.CL_LOGIN_LOGINUSER_MI.extensions = {}
pb.CL_LOGIN_RECONNECT_MI_ZONEID_FIELD.name = "ZoneID"
pb.CL_LOGIN_RECONNECT_MI_ZONEID_FIELD.full_name = ".CL_Login_Reconnect_MI.ZoneID"
pb.CL_LOGIN_RECONNECT_MI_ZONEID_FIELD.number = 1
pb.CL_LOGIN_RECONNECT_MI_ZONEID_FIELD.index = 0
pb.CL_LOGIN_RECONNECT_MI_ZONEID_FIELD.label = 2
pb.CL_LOGIN_RECONNECT_MI_ZONEID_FIELD.has_default_value = false
pb.CL_LOGIN_RECONNECT_MI_ZONEID_FIELD.default_value = 0
pb.CL_LOGIN_RECONNECT_MI_ZONEID_FIELD.type = 13
pb.CL_LOGIN_RECONNECT_MI_ZONEID_FIELD.cpp_type = 3

pb.CL_LOGIN_RECONNECT_MI_UID_FIELD.name = "Uid"
pb.CL_LOGIN_RECONNECT_MI_UID_FIELD.full_name = ".CL_Login_Reconnect_MI.Uid"
pb.CL_LOGIN_RECONNECT_MI_UID_FIELD.number = 2
pb.CL_LOGIN_RECONNECT_MI_UID_FIELD.index = 1
pb.CL_LOGIN_RECONNECT_MI_UID_FIELD.label = 2
pb.CL_LOGIN_RECONNECT_MI_UID_FIELD.has_default_value = false
pb.CL_LOGIN_RECONNECT_MI_UID_FIELD.default_value = 0
pb.CL_LOGIN_RECONNECT_MI_UID_FIELD.type = 3
pb.CL_LOGIN_RECONNECT_MI_UID_FIELD.cpp_type = 2

pb.CL_LOGIN_RECONNECT_MI_SID_FIELD.name = "Sid"
pb.CL_LOGIN_RECONNECT_MI_SID_FIELD.full_name = ".CL_Login_Reconnect_MI.Sid"
pb.CL_LOGIN_RECONNECT_MI_SID_FIELD.number = 3
pb.CL_LOGIN_RECONNECT_MI_SID_FIELD.index = 2
pb.CL_LOGIN_RECONNECT_MI_SID_FIELD.label = 2
pb.CL_LOGIN_RECONNECT_MI_SID_FIELD.has_default_value = false
pb.CL_LOGIN_RECONNECT_MI_SID_FIELD.default_value = ""
pb.CL_LOGIN_RECONNECT_MI_SID_FIELD.type = 9
pb.CL_LOGIN_RECONNECT_MI_SID_FIELD.cpp_type = 9

pb.CL_LOGIN_RECONNECT_MI_STATE_FIELD.name = "State"
pb.CL_LOGIN_RECONNECT_MI_STATE_FIELD.full_name = ".CL_Login_Reconnect_MI.State"
pb.CL_LOGIN_RECONNECT_MI_STATE_FIELD.number = 4
pb.CL_LOGIN_RECONNECT_MI_STATE_FIELD.index = 3
pb.CL_LOGIN_RECONNECT_MI_STATE_FIELD.label = 2
pb.CL_LOGIN_RECONNECT_MI_STATE_FIELD.has_default_value = false
pb.CL_LOGIN_RECONNECT_MI_STATE_FIELD.default_value = nil
pb.CL_LOGIN_RECONNECT_MI_STATE_FIELD.enum_type = MSG_LOGIN_PLAYER_STATE
pb.CL_LOGIN_RECONNECT_MI_STATE_FIELD.type = 14
pb.CL_LOGIN_RECONNECT_MI_STATE_FIELD.cpp_type = 8

pb.CL_LOGIN_RECONNECT_MI.name = "CL_Login_Reconnect_MI"
pb.CL_LOGIN_RECONNECT_MI.full_name = ".CL_Login_Reconnect_MI"
pb.CL_LOGIN_RECONNECT_MI.nested_types = {}
pb.CL_LOGIN_RECONNECT_MI.enum_types = {}
pb.CL_LOGIN_RECONNECT_MI.fields = {pb.CL_LOGIN_RECONNECT_MI_ZONEID_FIELD, pb.CL_LOGIN_RECONNECT_MI_UID_FIELD, pb.CL_LOGIN_RECONNECT_MI_SID_FIELD, pb.CL_LOGIN_RECONNECT_MI_STATE_FIELD}
pb.CL_LOGIN_RECONNECT_MI.is_extendable = false
pb.CL_LOGIN_RECONNECT_MI.extensions = {}
pb.CL_LOGIN_LOGINUSER_ZS_ZONEID_FIELD.name = "ZoneID"
pb.CL_LOGIN_LOGINUSER_ZS_ZONEID_FIELD.full_name = ".CL_Login_LoginUser_ZS.ZoneID"
pb.CL_LOGIN_LOGINUSER_ZS_ZONEID_FIELD.number = 1
pb.CL_LOGIN_LOGINUSER_ZS_ZONEID_FIELD.index = 0
pb.CL_LOGIN_LOGINUSER_ZS_ZONEID_FIELD.label = 2
pb.CL_LOGIN_LOGINUSER_ZS_ZONEID_FIELD.has_default_value = false
pb.CL_LOGIN_LOGINUSER_ZS_ZONEID_FIELD.default_value = 0
pb.CL_LOGIN_LOGINUSER_ZS_ZONEID_FIELD.type = 13
pb.CL_LOGIN_LOGINUSER_ZS_ZONEID_FIELD.cpp_type = 3

pb.CL_LOGIN_LOGINUSER_ZS_UID_FIELD.name = "Uid"
pb.CL_LOGIN_LOGINUSER_ZS_UID_FIELD.full_name = ".CL_Login_LoginUser_ZS.Uid"
pb.CL_LOGIN_LOGINUSER_ZS_UID_FIELD.number = 2
pb.CL_LOGIN_LOGINUSER_ZS_UID_FIELD.index = 1
pb.CL_LOGIN_LOGINUSER_ZS_UID_FIELD.label = 2
pb.CL_LOGIN_LOGINUSER_ZS_UID_FIELD.has_default_value = false
pb.CL_LOGIN_LOGINUSER_ZS_UID_FIELD.default_value = ""
pb.CL_LOGIN_LOGINUSER_ZS_UID_FIELD.type = 9
pb.CL_LOGIN_LOGINUSER_ZS_UID_FIELD.cpp_type = 9

pb.CL_LOGIN_LOGINUSER_ZS_SID_FIELD.name = "Sid"
pb.CL_LOGIN_LOGINUSER_ZS_SID_FIELD.full_name = ".CL_Login_LoginUser_ZS.Sid"
pb.CL_LOGIN_LOGINUSER_ZS_SID_FIELD.number = 3
pb.CL_LOGIN_LOGINUSER_ZS_SID_FIELD.index = 2
pb.CL_LOGIN_LOGINUSER_ZS_SID_FIELD.label = 2
pb.CL_LOGIN_LOGINUSER_ZS_SID_FIELD.has_default_value = false
pb.CL_LOGIN_LOGINUSER_ZS_SID_FIELD.default_value = ""
pb.CL_LOGIN_LOGINUSER_ZS_SID_FIELD.type = 9
pb.CL_LOGIN_LOGINUSER_ZS_SID_FIELD.cpp_type = 9

pb.CL_LOGIN_LOGINUSER_ZS.name = "CL_Login_LoginUser_ZS"
pb.CL_LOGIN_LOGINUSER_ZS.full_name = ".CL_Login_LoginUser_ZS"
pb.CL_LOGIN_LOGINUSER_ZS.nested_types = {}
pb.CL_LOGIN_LOGINUSER_ZS.enum_types = {}
pb.CL_LOGIN_LOGINUSER_ZS.fields = {pb.CL_LOGIN_LOGINUSER_ZS_ZONEID_FIELD, pb.CL_LOGIN_LOGINUSER_ZS_UID_FIELD, pb.CL_LOGIN_LOGINUSER_ZS_SID_FIELD}
pb.CL_LOGIN_LOGINUSER_ZS.is_extendable = false
pb.CL_LOGIN_LOGINUSER_ZS.extensions = {}
pb.CL_LOGIN_RECONNECT_ZS_ZONEID_FIELD.name = "ZoneID"
pb.CL_LOGIN_RECONNECT_ZS_ZONEID_FIELD.full_name = ".CL_Login_Reconnect_ZS.ZoneID"
pb.CL_LOGIN_RECONNECT_ZS_ZONEID_FIELD.number = 1
pb.CL_LOGIN_RECONNECT_ZS_ZONEID_FIELD.index = 0
pb.CL_LOGIN_RECONNECT_ZS_ZONEID_FIELD.label = 2
pb.CL_LOGIN_RECONNECT_ZS_ZONEID_FIELD.has_default_value = false
pb.CL_LOGIN_RECONNECT_ZS_ZONEID_FIELD.default_value = 0
pb.CL_LOGIN_RECONNECT_ZS_ZONEID_FIELD.type = 13
pb.CL_LOGIN_RECONNECT_ZS_ZONEID_FIELD.cpp_type = 3

pb.CL_LOGIN_RECONNECT_ZS_UID_FIELD.name = "Uid"
pb.CL_LOGIN_RECONNECT_ZS_UID_FIELD.full_name = ".CL_Login_Reconnect_ZS.Uid"
pb.CL_LOGIN_RECONNECT_ZS_UID_FIELD.number = 2
pb.CL_LOGIN_RECONNECT_ZS_UID_FIELD.index = 1
pb.CL_LOGIN_RECONNECT_ZS_UID_FIELD.label = 2
pb.CL_LOGIN_RECONNECT_ZS_UID_FIELD.has_default_value = false
pb.CL_LOGIN_RECONNECT_ZS_UID_FIELD.default_value = ""
pb.CL_LOGIN_RECONNECT_ZS_UID_FIELD.type = 9
pb.CL_LOGIN_RECONNECT_ZS_UID_FIELD.cpp_type = 9

pb.CL_LOGIN_RECONNECT_ZS_SID_FIELD.name = "Sid"
pb.CL_LOGIN_RECONNECT_ZS_SID_FIELD.full_name = ".CL_Login_Reconnect_ZS.Sid"
pb.CL_LOGIN_RECONNECT_ZS_SID_FIELD.number = 3
pb.CL_LOGIN_RECONNECT_ZS_SID_FIELD.index = 2
pb.CL_LOGIN_RECONNECT_ZS_SID_FIELD.label = 2
pb.CL_LOGIN_RECONNECT_ZS_SID_FIELD.has_default_value = false
pb.CL_LOGIN_RECONNECT_ZS_SID_FIELD.default_value = ""
pb.CL_LOGIN_RECONNECT_ZS_SID_FIELD.type = 9
pb.CL_LOGIN_RECONNECT_ZS_SID_FIELD.cpp_type = 9

pb.CL_LOGIN_RECONNECT_ZS_STATE_FIELD.name = "State"
pb.CL_LOGIN_RECONNECT_ZS_STATE_FIELD.full_name = ".CL_Login_Reconnect_ZS.State"
pb.CL_LOGIN_RECONNECT_ZS_STATE_FIELD.number = 4
pb.CL_LOGIN_RECONNECT_ZS_STATE_FIELD.index = 3
pb.CL_LOGIN_RECONNECT_ZS_STATE_FIELD.label = 2
pb.CL_LOGIN_RECONNECT_ZS_STATE_FIELD.has_default_value = false
pb.CL_LOGIN_RECONNECT_ZS_STATE_FIELD.default_value = nil
pb.CL_LOGIN_RECONNECT_ZS_STATE_FIELD.enum_type = MSG_LOGIN_PLAYER_STATE
pb.CL_LOGIN_RECONNECT_ZS_STATE_FIELD.type = 14
pb.CL_LOGIN_RECONNECT_ZS_STATE_FIELD.cpp_type = 8

pb.CL_LOGIN_RECONNECT_ZS.name = "CL_Login_Reconnect_ZS"
pb.CL_LOGIN_RECONNECT_ZS.full_name = ".CL_Login_Reconnect_ZS"
pb.CL_LOGIN_RECONNECT_ZS.nested_types = {}
pb.CL_LOGIN_RECONNECT_ZS.enum_types = {}
pb.CL_LOGIN_RECONNECT_ZS.fields = {pb.CL_LOGIN_RECONNECT_ZS_ZONEID_FIELD, pb.CL_LOGIN_RECONNECT_ZS_UID_FIELD, pb.CL_LOGIN_RECONNECT_ZS_SID_FIELD, pb.CL_LOGIN_RECONNECT_ZS_STATE_FIELD}
pb.CL_LOGIN_RECONNECT_ZS.is_extendable = false
pb.CL_LOGIN_RECONNECT_ZS.extensions = {}
pb.CL_LOGIN_LOGOUTUSER_ACTORID_FIELD.name = "ActorID"
pb.CL_LOGIN_LOGOUTUSER_ACTORID_FIELD.full_name = ".CL_Login_LogoutUser.ActorID"
pb.CL_LOGIN_LOGOUTUSER_ACTORID_FIELD.number = 1
pb.CL_LOGIN_LOGOUTUSER_ACTORID_FIELD.index = 0
pb.CL_LOGIN_LOGOUTUSER_ACTORID_FIELD.label = 2
pb.CL_LOGIN_LOGOUTUSER_ACTORID_FIELD.has_default_value = false
pb.CL_LOGIN_LOGOUTUSER_ACTORID_FIELD.default_value = 0
pb.CL_LOGIN_LOGOUTUSER_ACTORID_FIELD.type = 13
pb.CL_LOGIN_LOGOUTUSER_ACTORID_FIELD.cpp_type = 3

pb.CL_LOGIN_LOGOUTUSER_ACTORNAME_FIELD.name = "ActorName"
pb.CL_LOGIN_LOGOUTUSER_ACTORNAME_FIELD.full_name = ".CL_Login_LogoutUser.ActorName"
pb.CL_LOGIN_LOGOUTUSER_ACTORNAME_FIELD.number = 2
pb.CL_LOGIN_LOGOUTUSER_ACTORNAME_FIELD.index = 1
pb.CL_LOGIN_LOGOUTUSER_ACTORNAME_FIELD.label = 2
pb.CL_LOGIN_LOGOUTUSER_ACTORNAME_FIELD.has_default_value = false
pb.CL_LOGIN_LOGOUTUSER_ACTORNAME_FIELD.default_value = ""
pb.CL_LOGIN_LOGOUTUSER_ACTORNAME_FIELD.type = 9
pb.CL_LOGIN_LOGOUTUSER_ACTORNAME_FIELD.cpp_type = 9

pb.CL_LOGIN_LOGOUTUSER.name = "CL_Login_LogoutUser"
pb.CL_LOGIN_LOGOUTUSER.full_name = ".CL_Login_LogoutUser"
pb.CL_LOGIN_LOGOUTUSER.nested_types = {}
pb.CL_LOGIN_LOGOUTUSER.enum_types = {}
pb.CL_LOGIN_LOGOUTUSER.fields = {pb.CL_LOGIN_LOGOUTUSER_ACTORID_FIELD, pb.CL_LOGIN_LOGOUTUSER_ACTORNAME_FIELD}
pb.CL_LOGIN_LOGOUTUSER.is_extendable = false
pb.CL_LOGIN_LOGOUTUSER.extensions = {}
pb.LC_LOGIN_LOGOUTUSER_RESULT_FIELD.name = "Result"
pb.LC_LOGIN_LOGOUTUSER_RESULT_FIELD.full_name = ".LC_Login_LogoutUser.Result"
pb.LC_LOGIN_LOGOUTUSER_RESULT_FIELD.number = 1
pb.LC_LOGIN_LOGOUTUSER_RESULT_FIELD.index = 0
pb.LC_LOGIN_LOGOUTUSER_RESULT_FIELD.label = 2
pb.LC_LOGIN_LOGOUTUSER_RESULT_FIELD.has_default_value = false
pb.LC_LOGIN_LOGOUTUSER_RESULT_FIELD.default_value = 0
pb.LC_LOGIN_LOGOUTUSER_RESULT_FIELD.type = 13
pb.LC_LOGIN_LOGOUTUSER_RESULT_FIELD.cpp_type = 3

pb.LC_LOGIN_LOGOUTUSER.name = "LC_Login_LogoutUser"
pb.LC_LOGIN_LOGOUTUSER.full_name = ".LC_Login_LogoutUser"
pb.LC_LOGIN_LOGOUTUSER.nested_types = {}
pb.LC_LOGIN_LOGOUTUSER.enum_types = {}
pb.LC_LOGIN_LOGOUTUSER.fields = {pb.LC_LOGIN_LOGOUTUSER_RESULT_FIELD}
pb.LC_LOGIN_LOGOUTUSER.is_extendable = false
pb.LC_LOGIN_LOGOUTUSER.extensions = {}
pb.LS_LOGIN_LOGOUTUSER_CLIENTID_FIELD.name = "ClientID"
pb.LS_LOGIN_LOGOUTUSER_CLIENTID_FIELD.full_name = ".LS_Login_LogoutUser.ClientID"
pb.LS_LOGIN_LOGOUTUSER_CLIENTID_FIELD.number = 1
pb.LS_LOGIN_LOGOUTUSER_CLIENTID_FIELD.index = 0
pb.LS_LOGIN_LOGOUTUSER_CLIENTID_FIELD.label = 2
pb.LS_LOGIN_LOGOUTUSER_CLIENTID_FIELD.has_default_value = false
pb.LS_LOGIN_LOGOUTUSER_CLIENTID_FIELD.default_value = 0
pb.LS_LOGIN_LOGOUTUSER_CLIENTID_FIELD.type = 13
pb.LS_LOGIN_LOGOUTUSER_CLIENTID_FIELD.cpp_type = 3

pb.LS_LOGIN_LOGOUTUSER_USERID_FIELD.name = "UserID"
pb.LS_LOGIN_LOGOUTUSER_USERID_FIELD.full_name = ".LS_Login_LogoutUser.UserID"
pb.LS_LOGIN_LOGOUTUSER_USERID_FIELD.number = 2
pb.LS_LOGIN_LOGOUTUSER_USERID_FIELD.index = 1
pb.LS_LOGIN_LOGOUTUSER_USERID_FIELD.label = 2
pb.LS_LOGIN_LOGOUTUSER_USERID_FIELD.has_default_value = false
pb.LS_LOGIN_LOGOUTUSER_USERID_FIELD.default_value = 0
pb.LS_LOGIN_LOGOUTUSER_USERID_FIELD.type = 13
pb.LS_LOGIN_LOGOUTUSER_USERID_FIELD.cpp_type = 3

pb.LS_LOGIN_LOGOUTUSER_ACTORID_FIELD.name = "ActorID"
pb.LS_LOGIN_LOGOUTUSER_ACTORID_FIELD.full_name = ".LS_Login_LogoutUser.ActorID"
pb.LS_LOGIN_LOGOUTUSER_ACTORID_FIELD.number = 3
pb.LS_LOGIN_LOGOUTUSER_ACTORID_FIELD.index = 2
pb.LS_LOGIN_LOGOUTUSER_ACTORID_FIELD.label = 2
pb.LS_LOGIN_LOGOUTUSER_ACTORID_FIELD.has_default_value = false
pb.LS_LOGIN_LOGOUTUSER_ACTORID_FIELD.default_value = 0
pb.LS_LOGIN_LOGOUTUSER_ACTORID_FIELD.type = 13
pb.LS_LOGIN_LOGOUTUSER_ACTORID_FIELD.cpp_type = 3

pb.LS_LOGIN_LOGOUTUSER_USERNAME_FIELD.name = "UserName"
pb.LS_LOGIN_LOGOUTUSER_USERNAME_FIELD.full_name = ".LS_Login_LogoutUser.UserName"
pb.LS_LOGIN_LOGOUTUSER_USERNAME_FIELD.number = 4
pb.LS_LOGIN_LOGOUTUSER_USERNAME_FIELD.index = 3
pb.LS_LOGIN_LOGOUTUSER_USERNAME_FIELD.label = 2
pb.LS_LOGIN_LOGOUTUSER_USERNAME_FIELD.has_default_value = false
pb.LS_LOGIN_LOGOUTUSER_USERNAME_FIELD.default_value = ""
pb.LS_LOGIN_LOGOUTUSER_USERNAME_FIELD.type = 9
pb.LS_LOGIN_LOGOUTUSER_USERNAME_FIELD.cpp_type = 9

pb.LS_LOGIN_LOGOUTUSER_ACTORNAME_FIELD.name = "ActorName"
pb.LS_LOGIN_LOGOUTUSER_ACTORNAME_FIELD.full_name = ".LS_Login_LogoutUser.ActorName"
pb.LS_LOGIN_LOGOUTUSER_ACTORNAME_FIELD.number = 5
pb.LS_LOGIN_LOGOUTUSER_ACTORNAME_FIELD.index = 4
pb.LS_LOGIN_LOGOUTUSER_ACTORNAME_FIELD.label = 2
pb.LS_LOGIN_LOGOUTUSER_ACTORNAME_FIELD.has_default_value = false
pb.LS_LOGIN_LOGOUTUSER_ACTORNAME_FIELD.default_value = ""
pb.LS_LOGIN_LOGOUTUSER_ACTORNAME_FIELD.type = 9
pb.LS_LOGIN_LOGOUTUSER_ACTORNAME_FIELD.cpp_type = 9

pb.LS_LOGIN_LOGOUTUSER.name = "LS_Login_LogoutUser"
pb.LS_LOGIN_LOGOUTUSER.full_name = ".LS_Login_LogoutUser"
pb.LS_LOGIN_LOGOUTUSER.nested_types = {}
pb.LS_LOGIN_LOGOUTUSER.enum_types = {}
pb.LS_LOGIN_LOGOUTUSER.fields = {pb.LS_LOGIN_LOGOUTUSER_CLIENTID_FIELD, pb.LS_LOGIN_LOGOUTUSER_USERID_FIELD, pb.LS_LOGIN_LOGOUTUSER_ACTORID_FIELD, pb.LS_LOGIN_LOGOUTUSER_USERNAME_FIELD, pb.LS_LOGIN_LOGOUTUSER_ACTORNAME_FIELD}
pb.LS_LOGIN_LOGOUTUSER.is_extendable = false
pb.LS_LOGIN_LOGOUTUSER.extensions = {}
pb.CL_LOGIN_LOGINUSER_SDK_ZONEID_FIELD.name = "ZoneID"
pb.CL_LOGIN_LOGINUSER_SDK_ZONEID_FIELD.full_name = ".CL_Login_LoginUser_SDK.ZoneID"
pb.CL_LOGIN_LOGINUSER_SDK_ZONEID_FIELD.number = 1
pb.CL_LOGIN_LOGINUSER_SDK_ZONEID_FIELD.index = 0
pb.CL_LOGIN_LOGINUSER_SDK_ZONEID_FIELD.label = 2
pb.CL_LOGIN_LOGINUSER_SDK_ZONEID_FIELD.has_default_value = false
pb.CL_LOGIN_LOGINUSER_SDK_ZONEID_FIELD.default_value = 0
pb.CL_LOGIN_LOGINUSER_SDK_ZONEID_FIELD.type = 13
pb.CL_LOGIN_LOGINUSER_SDK_ZONEID_FIELD.cpp_type = 3

pb.CL_LOGIN_LOGINUSER_SDK_CHANNELID_FIELD.name = "ChannelID"
pb.CL_LOGIN_LOGINUSER_SDK_CHANNELID_FIELD.full_name = ".CL_Login_LoginUser_SDK.ChannelID"
pb.CL_LOGIN_LOGINUSER_SDK_CHANNELID_FIELD.number = 2
pb.CL_LOGIN_LOGINUSER_SDK_CHANNELID_FIELD.index = 1
pb.CL_LOGIN_LOGINUSER_SDK_CHANNELID_FIELD.label = 2
pb.CL_LOGIN_LOGINUSER_SDK_CHANNELID_FIELD.has_default_value = false
pb.CL_LOGIN_LOGINUSER_SDK_CHANNELID_FIELD.default_value = 0
pb.CL_LOGIN_LOGINUSER_SDK_CHANNELID_FIELD.type = 13
pb.CL_LOGIN_LOGINUSER_SDK_CHANNELID_FIELD.cpp_type = 3

pb.CL_LOGIN_LOGINUSER_SDK_UID_FIELD.name = "Uid"
pb.CL_LOGIN_LOGINUSER_SDK_UID_FIELD.full_name = ".CL_Login_LoginUser_SDK.Uid"
pb.CL_LOGIN_LOGINUSER_SDK_UID_FIELD.number = 3
pb.CL_LOGIN_LOGINUSER_SDK_UID_FIELD.index = 2
pb.CL_LOGIN_LOGINUSER_SDK_UID_FIELD.label = 2
pb.CL_LOGIN_LOGINUSER_SDK_UID_FIELD.has_default_value = false
pb.CL_LOGIN_LOGINUSER_SDK_UID_FIELD.default_value = ""
pb.CL_LOGIN_LOGINUSER_SDK_UID_FIELD.type = 9
pb.CL_LOGIN_LOGINUSER_SDK_UID_FIELD.cpp_type = 9

pb.CL_LOGIN_LOGINUSER_SDK.name = "CL_Login_LoginUser_SDK"
pb.CL_LOGIN_LOGINUSER_SDK.full_name = ".CL_Login_LoginUser_SDK"
pb.CL_LOGIN_LOGINUSER_SDK.nested_types = {}
pb.CL_LOGIN_LOGINUSER_SDK.enum_types = {}
pb.CL_LOGIN_LOGINUSER_SDK.fields = {pb.CL_LOGIN_LOGINUSER_SDK_ZONEID_FIELD, pb.CL_LOGIN_LOGINUSER_SDK_CHANNELID_FIELD, pb.CL_LOGIN_LOGINUSER_SDK_UID_FIELD}
pb.CL_LOGIN_LOGINUSER_SDK.is_extendable = false
pb.CL_LOGIN_LOGINUSER_SDK.extensions = {}
pb.LD_LOGIN_LOGINUSER_SDK_ZONEID_FIELD.name = "ZoneID"
pb.LD_LOGIN_LOGINUSER_SDK_ZONEID_FIELD.full_name = ".LD_Login_LoginUser_SDK.ZoneID"
pb.LD_LOGIN_LOGINUSER_SDK_ZONEID_FIELD.number = 1
pb.LD_LOGIN_LOGINUSER_SDK_ZONEID_FIELD.index = 0
pb.LD_LOGIN_LOGINUSER_SDK_ZONEID_FIELD.label = 2
pb.LD_LOGIN_LOGINUSER_SDK_ZONEID_FIELD.has_default_value = false
pb.LD_LOGIN_LOGINUSER_SDK_ZONEID_FIELD.default_value = 0
pb.LD_LOGIN_LOGINUSER_SDK_ZONEID_FIELD.type = 13
pb.LD_LOGIN_LOGINUSER_SDK_ZONEID_FIELD.cpp_type = 3

pb.LD_LOGIN_LOGINUSER_SDK_USERID_FIELD.name = "UserID"
pb.LD_LOGIN_LOGINUSER_SDK_USERID_FIELD.full_name = ".LD_Login_LoginUser_SDK.UserID"
pb.LD_LOGIN_LOGINUSER_SDK_USERID_FIELD.number = 2
pb.LD_LOGIN_LOGINUSER_SDK_USERID_FIELD.index = 1
pb.LD_LOGIN_LOGINUSER_SDK_USERID_FIELD.label = 2
pb.LD_LOGIN_LOGINUSER_SDK_USERID_FIELD.has_default_value = false
pb.LD_LOGIN_LOGINUSER_SDK_USERID_FIELD.default_value = 0
pb.LD_LOGIN_LOGINUSER_SDK_USERID_FIELD.type = 4
pb.LD_LOGIN_LOGINUSER_SDK_USERID_FIELD.cpp_type = 4

pb.LD_LOGIN_LOGINUSER_SDK_ACTORID_FIELD.name = "ActorID"
pb.LD_LOGIN_LOGINUSER_SDK_ACTORID_FIELD.full_name = ".LD_Login_LoginUser_SDK.ActorID"
pb.LD_LOGIN_LOGINUSER_SDK_ACTORID_FIELD.number = 3
pb.LD_LOGIN_LOGINUSER_SDK_ACTORID_FIELD.index = 2
pb.LD_LOGIN_LOGINUSER_SDK_ACTORID_FIELD.label = 2
pb.LD_LOGIN_LOGINUSER_SDK_ACTORID_FIELD.has_default_value = false
pb.LD_LOGIN_LOGINUSER_SDK_ACTORID_FIELD.default_value = 0
pb.LD_LOGIN_LOGINUSER_SDK_ACTORID_FIELD.type = 4
pb.LD_LOGIN_LOGINUSER_SDK_ACTORID_FIELD.cpp_type = 4

pb.LD_LOGIN_LOGINUSER_SDK_ACTORNAME_FIELD.name = "ActorName"
pb.LD_LOGIN_LOGINUSER_SDK_ACTORNAME_FIELD.full_name = ".LD_Login_LoginUser_SDK.ActorName"
pb.LD_LOGIN_LOGINUSER_SDK_ACTORNAME_FIELD.number = 4
pb.LD_LOGIN_LOGINUSER_SDK_ACTORNAME_FIELD.index = 3
pb.LD_LOGIN_LOGINUSER_SDK_ACTORNAME_FIELD.label = 2
pb.LD_LOGIN_LOGINUSER_SDK_ACTORNAME_FIELD.has_default_value = false
pb.LD_LOGIN_LOGINUSER_SDK_ACTORNAME_FIELD.default_value = ""
pb.LD_LOGIN_LOGINUSER_SDK_ACTORNAME_FIELD.type = 9
pb.LD_LOGIN_LOGINUSER_SDK_ACTORNAME_FIELD.cpp_type = 9

pb.LD_LOGIN_LOGINUSER_SDK_CLIENTUID_FIELD.name = "ClientUID"
pb.LD_LOGIN_LOGINUSER_SDK_CLIENTUID_FIELD.full_name = ".LD_Login_LoginUser_SDK.ClientUID"
pb.LD_LOGIN_LOGINUSER_SDK_CLIENTUID_FIELD.number = 5
pb.LD_LOGIN_LOGINUSER_SDK_CLIENTUID_FIELD.index = 4
pb.LD_LOGIN_LOGINUSER_SDK_CLIENTUID_FIELD.label = 2
pb.LD_LOGIN_LOGINUSER_SDK_CLIENTUID_FIELD.has_default_value = false
pb.LD_LOGIN_LOGINUSER_SDK_CLIENTUID_FIELD.default_value = ""
pb.LD_LOGIN_LOGINUSER_SDK_CLIENTUID_FIELD.type = 9
pb.LD_LOGIN_LOGINUSER_SDK_CLIENTUID_FIELD.cpp_type = 9

pb.LD_LOGIN_LOGINUSER_SDK_IP_FIELD.name = "IP"
pb.LD_LOGIN_LOGINUSER_SDK_IP_FIELD.full_name = ".LD_Login_LoginUser_SDK.IP"
pb.LD_LOGIN_LOGINUSER_SDK_IP_FIELD.number = 6
pb.LD_LOGIN_LOGINUSER_SDK_IP_FIELD.index = 5
pb.LD_LOGIN_LOGINUSER_SDK_IP_FIELD.label = 2
pb.LD_LOGIN_LOGINUSER_SDK_IP_FIELD.has_default_value = false
pb.LD_LOGIN_LOGINUSER_SDK_IP_FIELD.default_value = ""
pb.LD_LOGIN_LOGINUSER_SDK_IP_FIELD.type = 9
pb.LD_LOGIN_LOGINUSER_SDK_IP_FIELD.cpp_type = 9

pb.LD_LOGIN_LOGINUSER_SDK_DEVMODEL_FIELD.name = "DevModel"
pb.LD_LOGIN_LOGINUSER_SDK_DEVMODEL_FIELD.full_name = ".LD_Login_LoginUser_SDK.DevModel"
pb.LD_LOGIN_LOGINUSER_SDK_DEVMODEL_FIELD.number = 7
pb.LD_LOGIN_LOGINUSER_SDK_DEVMODEL_FIELD.index = 6
pb.LD_LOGIN_LOGINUSER_SDK_DEVMODEL_FIELD.label = 2
pb.LD_LOGIN_LOGINUSER_SDK_DEVMODEL_FIELD.has_default_value = false
pb.LD_LOGIN_LOGINUSER_SDK_DEVMODEL_FIELD.default_value = ""
pb.LD_LOGIN_LOGINUSER_SDK_DEVMODEL_FIELD.type = 9
pb.LD_LOGIN_LOGINUSER_SDK_DEVMODEL_FIELD.cpp_type = 9

pb.LD_LOGIN_LOGINUSER_SDK_LOGINID_FIELD.name = "LoginID"
pb.LD_LOGIN_LOGINUSER_SDK_LOGINID_FIELD.full_name = ".LD_Login_LoginUser_SDK.LoginID"
pb.LD_LOGIN_LOGINUSER_SDK_LOGINID_FIELD.number = 8
pb.LD_LOGIN_LOGINUSER_SDK_LOGINID_FIELD.index = 7
pb.LD_LOGIN_LOGINUSER_SDK_LOGINID_FIELD.label = 2
pb.LD_LOGIN_LOGINUSER_SDK_LOGINID_FIELD.has_default_value = false
pb.LD_LOGIN_LOGINUSER_SDK_LOGINID_FIELD.default_value = 0
pb.LD_LOGIN_LOGINUSER_SDK_LOGINID_FIELD.type = 13
pb.LD_LOGIN_LOGINUSER_SDK_LOGINID_FIELD.cpp_type = 3

pb.LD_LOGIN_LOGINUSER_SDK.name = "LD_Login_LoginUser_SDK"
pb.LD_LOGIN_LOGINUSER_SDK.full_name = ".LD_Login_LoginUser_SDK"
pb.LD_LOGIN_LOGINUSER_SDK.nested_types = {}
pb.LD_LOGIN_LOGINUSER_SDK.enum_types = {}
pb.LD_LOGIN_LOGINUSER_SDK.fields = {pb.LD_LOGIN_LOGINUSER_SDK_ZONEID_FIELD, pb.LD_LOGIN_LOGINUSER_SDK_USERID_FIELD, pb.LD_LOGIN_LOGINUSER_SDK_ACTORID_FIELD, pb.LD_LOGIN_LOGINUSER_SDK_ACTORNAME_FIELD, pb.LD_LOGIN_LOGINUSER_SDK_CLIENTUID_FIELD, pb.LD_LOGIN_LOGINUSER_SDK_IP_FIELD, pb.LD_LOGIN_LOGINUSER_SDK_DEVMODEL_FIELD, pb.LD_LOGIN_LOGINUSER_SDK_LOGINID_FIELD}
pb.LD_LOGIN_LOGINUSER_SDK.is_extendable = false
pb.LD_LOGIN_LOGINUSER_SDK.extensions = {}
pb.CL_LOGIN_LOGINUSER_VIVO_UID_FIELD.name = "Uid"
pb.CL_LOGIN_LOGINUSER_VIVO_UID_FIELD.full_name = ".CL_Login_LoginUser_Vivo.Uid"
pb.CL_LOGIN_LOGINUSER_VIVO_UID_FIELD.number = 1
pb.CL_LOGIN_LOGINUSER_VIVO_UID_FIELD.index = 0
pb.CL_LOGIN_LOGINUSER_VIVO_UID_FIELD.label = 1
pb.CL_LOGIN_LOGINUSER_VIVO_UID_FIELD.has_default_value = false
pb.CL_LOGIN_LOGINUSER_VIVO_UID_FIELD.default_value = ""
pb.CL_LOGIN_LOGINUSER_VIVO_UID_FIELD.type = 9
pb.CL_LOGIN_LOGINUSER_VIVO_UID_FIELD.cpp_type = 9

pb.CL_LOGIN_LOGINUSER_VIVO_AUTHTOKEN_FIELD.name = "AuthToken"
pb.CL_LOGIN_LOGINUSER_VIVO_AUTHTOKEN_FIELD.full_name = ".CL_Login_LoginUser_Vivo.AuthToken"
pb.CL_LOGIN_LOGINUSER_VIVO_AUTHTOKEN_FIELD.number = 2
pb.CL_LOGIN_LOGINUSER_VIVO_AUTHTOKEN_FIELD.index = 1
pb.CL_LOGIN_LOGINUSER_VIVO_AUTHTOKEN_FIELD.label = 1
pb.CL_LOGIN_LOGINUSER_VIVO_AUTHTOKEN_FIELD.has_default_value = false
pb.CL_LOGIN_LOGINUSER_VIVO_AUTHTOKEN_FIELD.default_value = ""
pb.CL_LOGIN_LOGINUSER_VIVO_AUTHTOKEN_FIELD.type = 9
pb.CL_LOGIN_LOGINUSER_VIVO_AUTHTOKEN_FIELD.cpp_type = 9

pb.CL_LOGIN_LOGINUSER_VIVO_USERNAME_FIELD.name = "UserName"
pb.CL_LOGIN_LOGINUSER_VIVO_USERNAME_FIELD.full_name = ".CL_Login_LoginUser_Vivo.UserName"
pb.CL_LOGIN_LOGINUSER_VIVO_USERNAME_FIELD.number = 3
pb.CL_LOGIN_LOGINUSER_VIVO_USERNAME_FIELD.index = 2
pb.CL_LOGIN_LOGINUSER_VIVO_USERNAME_FIELD.label = 1
pb.CL_LOGIN_LOGINUSER_VIVO_USERNAME_FIELD.has_default_value = false
pb.CL_LOGIN_LOGINUSER_VIVO_USERNAME_FIELD.default_value = ""
pb.CL_LOGIN_LOGINUSER_VIVO_USERNAME_FIELD.type = 9
pb.CL_LOGIN_LOGINUSER_VIVO_USERNAME_FIELD.cpp_type = 9

pb.CL_LOGIN_LOGINUSER_VIVO_CHANNELID_FIELD.name = "ChannelID"
pb.CL_LOGIN_LOGINUSER_VIVO_CHANNELID_FIELD.full_name = ".CL_Login_LoginUser_Vivo.ChannelID"
pb.CL_LOGIN_LOGINUSER_VIVO_CHANNELID_FIELD.number = 4
pb.CL_LOGIN_LOGINUSER_VIVO_CHANNELID_FIELD.index = 3
pb.CL_LOGIN_LOGINUSER_VIVO_CHANNELID_FIELD.label = 1
pb.CL_LOGIN_LOGINUSER_VIVO_CHANNELID_FIELD.has_default_value = false
pb.CL_LOGIN_LOGINUSER_VIVO_CHANNELID_FIELD.default_value = 0
pb.CL_LOGIN_LOGINUSER_VIVO_CHANNELID_FIELD.type = 13
pb.CL_LOGIN_LOGINUSER_VIVO_CHANNELID_FIELD.cpp_type = 3

pb.CL_LOGIN_LOGINUSER_VIVO_ZONEID_FIELD.name = "ZoneID"
pb.CL_LOGIN_LOGINUSER_VIVO_ZONEID_FIELD.full_name = ".CL_Login_LoginUser_Vivo.ZoneID"
pb.CL_LOGIN_LOGINUSER_VIVO_ZONEID_FIELD.number = 5
pb.CL_LOGIN_LOGINUSER_VIVO_ZONEID_FIELD.index = 4
pb.CL_LOGIN_LOGINUSER_VIVO_ZONEID_FIELD.label = 1
pb.CL_LOGIN_LOGINUSER_VIVO_ZONEID_FIELD.has_default_value = false
pb.CL_LOGIN_LOGINUSER_VIVO_ZONEID_FIELD.default_value = 0
pb.CL_LOGIN_LOGINUSER_VIVO_ZONEID_FIELD.type = 5
pb.CL_LOGIN_LOGINUSER_VIVO_ZONEID_FIELD.cpp_type = 1

pb.CL_LOGIN_LOGINUSER_VIVO_UUID_FIELD.name = "UUID"
pb.CL_LOGIN_LOGINUSER_VIVO_UUID_FIELD.full_name = ".CL_Login_LoginUser_Vivo.UUID"
pb.CL_LOGIN_LOGINUSER_VIVO_UUID_FIELD.number = 6
pb.CL_LOGIN_LOGINUSER_VIVO_UUID_FIELD.index = 5
pb.CL_LOGIN_LOGINUSER_VIVO_UUID_FIELD.label = 1
pb.CL_LOGIN_LOGINUSER_VIVO_UUID_FIELD.has_default_value = false
pb.CL_LOGIN_LOGINUSER_VIVO_UUID_FIELD.default_value = ""
pb.CL_LOGIN_LOGINUSER_VIVO_UUID_FIELD.type = 9
pb.CL_LOGIN_LOGINUSER_VIVO_UUID_FIELD.cpp_type = 9

pb.CL_LOGIN_LOGINUSER_VIVO.name = "CL_Login_LoginUser_Vivo"
pb.CL_LOGIN_LOGINUSER_VIVO.full_name = ".CL_Login_LoginUser_Vivo"
pb.CL_LOGIN_LOGINUSER_VIVO.nested_types = {}
pb.CL_LOGIN_LOGINUSER_VIVO.enum_types = {}
pb.CL_LOGIN_LOGINUSER_VIVO.fields = {pb.CL_LOGIN_LOGINUSER_VIVO_UID_FIELD, pb.CL_LOGIN_LOGINUSER_VIVO_AUTHTOKEN_FIELD, pb.CL_LOGIN_LOGINUSER_VIVO_USERNAME_FIELD, pb.CL_LOGIN_LOGINUSER_VIVO_CHANNELID_FIELD, pb.CL_LOGIN_LOGINUSER_VIVO_ZONEID_FIELD, pb.CL_LOGIN_LOGINUSER_VIVO_UUID_FIELD}
pb.CL_LOGIN_LOGINUSER_VIVO.is_extendable = false
pb.CL_LOGIN_LOGINUSER_VIVO.extensions = {}
pb.CL_LOGIN_LOGINUSER_OPPO_SSOID_FIELD.name = "Ssoid"
pb.CL_LOGIN_LOGINUSER_OPPO_SSOID_FIELD.full_name = ".CL_Login_LoginUser_Oppo.Ssoid"
pb.CL_LOGIN_LOGINUSER_OPPO_SSOID_FIELD.number = 1
pb.CL_LOGIN_LOGINUSER_OPPO_SSOID_FIELD.index = 0
pb.CL_LOGIN_LOGINUSER_OPPO_SSOID_FIELD.label = 1
pb.CL_LOGIN_LOGINUSER_OPPO_SSOID_FIELD.has_default_value = false
pb.CL_LOGIN_LOGINUSER_OPPO_SSOID_FIELD.default_value = ""
pb.CL_LOGIN_LOGINUSER_OPPO_SSOID_FIELD.type = 9
pb.CL_LOGIN_LOGINUSER_OPPO_SSOID_FIELD.cpp_type = 9

pb.CL_LOGIN_LOGINUSER_OPPO_TOKEN_FIELD.name = "Token"
pb.CL_LOGIN_LOGINUSER_OPPO_TOKEN_FIELD.full_name = ".CL_Login_LoginUser_Oppo.Token"
pb.CL_LOGIN_LOGINUSER_OPPO_TOKEN_FIELD.number = 2
pb.CL_LOGIN_LOGINUSER_OPPO_TOKEN_FIELD.index = 1
pb.CL_LOGIN_LOGINUSER_OPPO_TOKEN_FIELD.label = 1
pb.CL_LOGIN_LOGINUSER_OPPO_TOKEN_FIELD.has_default_value = false
pb.CL_LOGIN_LOGINUSER_OPPO_TOKEN_FIELD.default_value = ""
pb.CL_LOGIN_LOGINUSER_OPPO_TOKEN_FIELD.type = 9
pb.CL_LOGIN_LOGINUSER_OPPO_TOKEN_FIELD.cpp_type = 9

pb.CL_LOGIN_LOGINUSER_OPPO_UID_FIELD.name = "Uid"
pb.CL_LOGIN_LOGINUSER_OPPO_UID_FIELD.full_name = ".CL_Login_LoginUser_Oppo.Uid"
pb.CL_LOGIN_LOGINUSER_OPPO_UID_FIELD.number = 3
pb.CL_LOGIN_LOGINUSER_OPPO_UID_FIELD.index = 2
pb.CL_LOGIN_LOGINUSER_OPPO_UID_FIELD.label = 1
pb.CL_LOGIN_LOGINUSER_OPPO_UID_FIELD.has_default_value = false
pb.CL_LOGIN_LOGINUSER_OPPO_UID_FIELD.default_value = ""
pb.CL_LOGIN_LOGINUSER_OPPO_UID_FIELD.type = 9
pb.CL_LOGIN_LOGINUSER_OPPO_UID_FIELD.cpp_type = 9

pb.CL_LOGIN_LOGINUSER_OPPO_ZONEID_FIELD.name = "ZoneID"
pb.CL_LOGIN_LOGINUSER_OPPO_ZONEID_FIELD.full_name = ".CL_Login_LoginUser_Oppo.ZoneID"
pb.CL_LOGIN_LOGINUSER_OPPO_ZONEID_FIELD.number = 4
pb.CL_LOGIN_LOGINUSER_OPPO_ZONEID_FIELD.index = 3
pb.CL_LOGIN_LOGINUSER_OPPO_ZONEID_FIELD.label = 1
pb.CL_LOGIN_LOGINUSER_OPPO_ZONEID_FIELD.has_default_value = false
pb.CL_LOGIN_LOGINUSER_OPPO_ZONEID_FIELD.default_value = 0
pb.CL_LOGIN_LOGINUSER_OPPO_ZONEID_FIELD.type = 5
pb.CL_LOGIN_LOGINUSER_OPPO_ZONEID_FIELD.cpp_type = 1

pb.CL_LOGIN_LOGINUSER_OPPO.name = "CL_Login_LoginUser_Oppo"
pb.CL_LOGIN_LOGINUSER_OPPO.full_name = ".CL_Login_LoginUser_Oppo"
pb.CL_LOGIN_LOGINUSER_OPPO.nested_types = {}
pb.CL_LOGIN_LOGINUSER_OPPO.enum_types = {}
pb.CL_LOGIN_LOGINUSER_OPPO.fields = {pb.CL_LOGIN_LOGINUSER_OPPO_SSOID_FIELD, pb.CL_LOGIN_LOGINUSER_OPPO_TOKEN_FIELD, pb.CL_LOGIN_LOGINUSER_OPPO_UID_FIELD, pb.CL_LOGIN_LOGINUSER_OPPO_ZONEID_FIELD}
pb.CL_LOGIN_LOGINUSER_OPPO.is_extendable = false
pb.CL_LOGIN_LOGINUSER_OPPO.extensions = {}
pb.CL_LOGIN_LOGINUSER_AMIGO_UID_FIELD.name = "Uid"
pb.CL_LOGIN_LOGINUSER_AMIGO_UID_FIELD.full_name = ".CL_Login_LoginUser_Amigo.Uid"
pb.CL_LOGIN_LOGINUSER_AMIGO_UID_FIELD.number = 1
pb.CL_LOGIN_LOGINUSER_AMIGO_UID_FIELD.index = 0
pb.CL_LOGIN_LOGINUSER_AMIGO_UID_FIELD.label = 1
pb.CL_LOGIN_LOGINUSER_AMIGO_UID_FIELD.has_default_value = false
pb.CL_LOGIN_LOGINUSER_AMIGO_UID_FIELD.default_value = ""
pb.CL_LOGIN_LOGINUSER_AMIGO_UID_FIELD.type = 9
pb.CL_LOGIN_LOGINUSER_AMIGO_UID_FIELD.cpp_type = 9

pb.CL_LOGIN_LOGINUSER_AMIGO_AUTHTOKEN_FIELD.name = "AuthToken"
pb.CL_LOGIN_LOGINUSER_AMIGO_AUTHTOKEN_FIELD.full_name = ".CL_Login_LoginUser_Amigo.AuthToken"
pb.CL_LOGIN_LOGINUSER_AMIGO_AUTHTOKEN_FIELD.number = 2
pb.CL_LOGIN_LOGINUSER_AMIGO_AUTHTOKEN_FIELD.index = 1
pb.CL_LOGIN_LOGINUSER_AMIGO_AUTHTOKEN_FIELD.label = 1
pb.CL_LOGIN_LOGINUSER_AMIGO_AUTHTOKEN_FIELD.has_default_value = false
pb.CL_LOGIN_LOGINUSER_AMIGO_AUTHTOKEN_FIELD.default_value = ""
pb.CL_LOGIN_LOGINUSER_AMIGO_AUTHTOKEN_FIELD.type = 9
pb.CL_LOGIN_LOGINUSER_AMIGO_AUTHTOKEN_FIELD.cpp_type = 9

pb.CL_LOGIN_LOGINUSER_AMIGO_USERNAME_FIELD.name = "UserName"
pb.CL_LOGIN_LOGINUSER_AMIGO_USERNAME_FIELD.full_name = ".CL_Login_LoginUser_Amigo.UserName"
pb.CL_LOGIN_LOGINUSER_AMIGO_USERNAME_FIELD.number = 3
pb.CL_LOGIN_LOGINUSER_AMIGO_USERNAME_FIELD.index = 2
pb.CL_LOGIN_LOGINUSER_AMIGO_USERNAME_FIELD.label = 1
pb.CL_LOGIN_LOGINUSER_AMIGO_USERNAME_FIELD.has_default_value = false
pb.CL_LOGIN_LOGINUSER_AMIGO_USERNAME_FIELD.default_value = ""
pb.CL_LOGIN_LOGINUSER_AMIGO_USERNAME_FIELD.type = 9
pb.CL_LOGIN_LOGINUSER_AMIGO_USERNAME_FIELD.cpp_type = 9

pb.CL_LOGIN_LOGINUSER_AMIGO_CHANNELID_FIELD.name = "ChannelID"
pb.CL_LOGIN_LOGINUSER_AMIGO_CHANNELID_FIELD.full_name = ".CL_Login_LoginUser_Amigo.ChannelID"
pb.CL_LOGIN_LOGINUSER_AMIGO_CHANNELID_FIELD.number = 4
pb.CL_LOGIN_LOGINUSER_AMIGO_CHANNELID_FIELD.index = 3
pb.CL_LOGIN_LOGINUSER_AMIGO_CHANNELID_FIELD.label = 1
pb.CL_LOGIN_LOGINUSER_AMIGO_CHANNELID_FIELD.has_default_value = false
pb.CL_LOGIN_LOGINUSER_AMIGO_CHANNELID_FIELD.default_value = 0
pb.CL_LOGIN_LOGINUSER_AMIGO_CHANNELID_FIELD.type = 13
pb.CL_LOGIN_LOGINUSER_AMIGO_CHANNELID_FIELD.cpp_type = 3

pb.CL_LOGIN_LOGINUSER_AMIGO_ZONEID_FIELD.name = "ZoneID"
pb.CL_LOGIN_LOGINUSER_AMIGO_ZONEID_FIELD.full_name = ".CL_Login_LoginUser_Amigo.ZoneID"
pb.CL_LOGIN_LOGINUSER_AMIGO_ZONEID_FIELD.number = 5
pb.CL_LOGIN_LOGINUSER_AMIGO_ZONEID_FIELD.index = 4
pb.CL_LOGIN_LOGINUSER_AMIGO_ZONEID_FIELD.label = 1
pb.CL_LOGIN_LOGINUSER_AMIGO_ZONEID_FIELD.has_default_value = false
pb.CL_LOGIN_LOGINUSER_AMIGO_ZONEID_FIELD.default_value = 0
pb.CL_LOGIN_LOGINUSER_AMIGO_ZONEID_FIELD.type = 5
pb.CL_LOGIN_LOGINUSER_AMIGO_ZONEID_FIELD.cpp_type = 1

pb.CL_LOGIN_LOGINUSER_AMIGO_UUID_FIELD.name = "UUID"
pb.CL_LOGIN_LOGINUSER_AMIGO_UUID_FIELD.full_name = ".CL_Login_LoginUser_Amigo.UUID"
pb.CL_LOGIN_LOGINUSER_AMIGO_UUID_FIELD.number = 6
pb.CL_LOGIN_LOGINUSER_AMIGO_UUID_FIELD.index = 5
pb.CL_LOGIN_LOGINUSER_AMIGO_UUID_FIELD.label = 1
pb.CL_LOGIN_LOGINUSER_AMIGO_UUID_FIELD.has_default_value = false
pb.CL_LOGIN_LOGINUSER_AMIGO_UUID_FIELD.default_value = ""
pb.CL_LOGIN_LOGINUSER_AMIGO_UUID_FIELD.type = 9
pb.CL_LOGIN_LOGINUSER_AMIGO_UUID_FIELD.cpp_type = 9

pb.CL_LOGIN_LOGINUSER_AMIGO.name = "CL_Login_LoginUser_Amigo"
pb.CL_LOGIN_LOGINUSER_AMIGO.full_name = ".CL_Login_LoginUser_Amigo"
pb.CL_LOGIN_LOGINUSER_AMIGO.nested_types = {}
pb.CL_LOGIN_LOGINUSER_AMIGO.enum_types = {}
pb.CL_LOGIN_LOGINUSER_AMIGO.fields = {pb.CL_LOGIN_LOGINUSER_AMIGO_UID_FIELD, pb.CL_LOGIN_LOGINUSER_AMIGO_AUTHTOKEN_FIELD, pb.CL_LOGIN_LOGINUSER_AMIGO_USERNAME_FIELD, pb.CL_LOGIN_LOGINUSER_AMIGO_CHANNELID_FIELD, pb.CL_LOGIN_LOGINUSER_AMIGO_ZONEID_FIELD, pb.CL_LOGIN_LOGINUSER_AMIGO_UUID_FIELD}
pb.CL_LOGIN_LOGINUSER_AMIGO.is_extendable = false
pb.CL_LOGIN_LOGINUSER_AMIGO.extensions = {}
pb.CL_LOGIN_LOGINUSER_LENOVO_UID_FIELD.name = "Uid"
pb.CL_LOGIN_LOGINUSER_LENOVO_UID_FIELD.full_name = ".CL_Login_LoginUser_Lenovo.Uid"
pb.CL_LOGIN_LOGINUSER_LENOVO_UID_FIELD.number = 1
pb.CL_LOGIN_LOGINUSER_LENOVO_UID_FIELD.index = 0
pb.CL_LOGIN_LOGINUSER_LENOVO_UID_FIELD.label = 1
pb.CL_LOGIN_LOGINUSER_LENOVO_UID_FIELD.has_default_value = false
pb.CL_LOGIN_LOGINUSER_LENOVO_UID_FIELD.default_value = ""
pb.CL_LOGIN_LOGINUSER_LENOVO_UID_FIELD.type = 9
pb.CL_LOGIN_LOGINUSER_LENOVO_UID_FIELD.cpp_type = 9

pb.CL_LOGIN_LOGINUSER_LENOVO_AUTHTOKEN_FIELD.name = "AuthToken"
pb.CL_LOGIN_LOGINUSER_LENOVO_AUTHTOKEN_FIELD.full_name = ".CL_Login_LoginUser_Lenovo.AuthToken"
pb.CL_LOGIN_LOGINUSER_LENOVO_AUTHTOKEN_FIELD.number = 2
pb.CL_LOGIN_LOGINUSER_LENOVO_AUTHTOKEN_FIELD.index = 1
pb.CL_LOGIN_LOGINUSER_LENOVO_AUTHTOKEN_FIELD.label = 1
pb.CL_LOGIN_LOGINUSER_LENOVO_AUTHTOKEN_FIELD.has_default_value = false
pb.CL_LOGIN_LOGINUSER_LENOVO_AUTHTOKEN_FIELD.default_value = ""
pb.CL_LOGIN_LOGINUSER_LENOVO_AUTHTOKEN_FIELD.type = 9
pb.CL_LOGIN_LOGINUSER_LENOVO_AUTHTOKEN_FIELD.cpp_type = 9

pb.CL_LOGIN_LOGINUSER_LENOVO_CHANNELID_FIELD.name = "ChannelID"
pb.CL_LOGIN_LOGINUSER_LENOVO_CHANNELID_FIELD.full_name = ".CL_Login_LoginUser_Lenovo.ChannelID"
pb.CL_LOGIN_LOGINUSER_LENOVO_CHANNELID_FIELD.number = 3
pb.CL_LOGIN_LOGINUSER_LENOVO_CHANNELID_FIELD.index = 2
pb.CL_LOGIN_LOGINUSER_LENOVO_CHANNELID_FIELD.label = 1
pb.CL_LOGIN_LOGINUSER_LENOVO_CHANNELID_FIELD.has_default_value = false
pb.CL_LOGIN_LOGINUSER_LENOVO_CHANNELID_FIELD.default_value = 0
pb.CL_LOGIN_LOGINUSER_LENOVO_CHANNELID_FIELD.type = 13
pb.CL_LOGIN_LOGINUSER_LENOVO_CHANNELID_FIELD.cpp_type = 3

pb.CL_LOGIN_LOGINUSER_LENOVO_ZONEID_FIELD.name = "ZoneID"
pb.CL_LOGIN_LOGINUSER_LENOVO_ZONEID_FIELD.full_name = ".CL_Login_LoginUser_Lenovo.ZoneID"
pb.CL_LOGIN_LOGINUSER_LENOVO_ZONEID_FIELD.number = 4
pb.CL_LOGIN_LOGINUSER_LENOVO_ZONEID_FIELD.index = 3
pb.CL_LOGIN_LOGINUSER_LENOVO_ZONEID_FIELD.label = 1
pb.CL_LOGIN_LOGINUSER_LENOVO_ZONEID_FIELD.has_default_value = false
pb.CL_LOGIN_LOGINUSER_LENOVO_ZONEID_FIELD.default_value = 0
pb.CL_LOGIN_LOGINUSER_LENOVO_ZONEID_FIELD.type = 5
pb.CL_LOGIN_LOGINUSER_LENOVO_ZONEID_FIELD.cpp_type = 1

pb.CL_LOGIN_LOGINUSER_LENOVO_UUID_FIELD.name = "UUID"
pb.CL_LOGIN_LOGINUSER_LENOVO_UUID_FIELD.full_name = ".CL_Login_LoginUser_Lenovo.UUID"
pb.CL_LOGIN_LOGINUSER_LENOVO_UUID_FIELD.number = 5
pb.CL_LOGIN_LOGINUSER_LENOVO_UUID_FIELD.index = 4
pb.CL_LOGIN_LOGINUSER_LENOVO_UUID_FIELD.label = 1
pb.CL_LOGIN_LOGINUSER_LENOVO_UUID_FIELD.has_default_value = false
pb.CL_LOGIN_LOGINUSER_LENOVO_UUID_FIELD.default_value = ""
pb.CL_LOGIN_LOGINUSER_LENOVO_UUID_FIELD.type = 9
pb.CL_LOGIN_LOGINUSER_LENOVO_UUID_FIELD.cpp_type = 9

pb.CL_LOGIN_LOGINUSER_LENOVO.name = "CL_Login_LoginUser_Lenovo"
pb.CL_LOGIN_LOGINUSER_LENOVO.full_name = ".CL_Login_LoginUser_Lenovo"
pb.CL_LOGIN_LOGINUSER_LENOVO.nested_types = {}
pb.CL_LOGIN_LOGINUSER_LENOVO.enum_types = {}
pb.CL_LOGIN_LOGINUSER_LENOVO.fields = {pb.CL_LOGIN_LOGINUSER_LENOVO_UID_FIELD, pb.CL_LOGIN_LOGINUSER_LENOVO_AUTHTOKEN_FIELD, pb.CL_LOGIN_LOGINUSER_LENOVO_CHANNELID_FIELD, pb.CL_LOGIN_LOGINUSER_LENOVO_ZONEID_FIELD, pb.CL_LOGIN_LOGINUSER_LENOVO_UUID_FIELD}
pb.CL_LOGIN_LOGINUSER_LENOVO.is_extendable = false
pb.CL_LOGIN_LOGINUSER_LENOVO.extensions = {}
pb.CL_LOGIN_LOGINUSER_HUAWEI_UID_FIELD.name = "Uid"
pb.CL_LOGIN_LOGINUSER_HUAWEI_UID_FIELD.full_name = ".CL_Login_LoginUser_HuaWei.Uid"
pb.CL_LOGIN_LOGINUSER_HUAWEI_UID_FIELD.number = 1
pb.CL_LOGIN_LOGINUSER_HUAWEI_UID_FIELD.index = 0
pb.CL_LOGIN_LOGINUSER_HUAWEI_UID_FIELD.label = 1
pb.CL_LOGIN_LOGINUSER_HUAWEI_UID_FIELD.has_default_value = false
pb.CL_LOGIN_LOGINUSER_HUAWEI_UID_FIELD.default_value = ""
pb.CL_LOGIN_LOGINUSER_HUAWEI_UID_FIELD.type = 9
pb.CL_LOGIN_LOGINUSER_HUAWEI_UID_FIELD.cpp_type = 9

pb.CL_LOGIN_LOGINUSER_HUAWEI_AUTHTOKEN_FIELD.name = "AuthToken"
pb.CL_LOGIN_LOGINUSER_HUAWEI_AUTHTOKEN_FIELD.full_name = ".CL_Login_LoginUser_HuaWei.AuthToken"
pb.CL_LOGIN_LOGINUSER_HUAWEI_AUTHTOKEN_FIELD.number = 2
pb.CL_LOGIN_LOGINUSER_HUAWEI_AUTHTOKEN_FIELD.index = 1
pb.CL_LOGIN_LOGINUSER_HUAWEI_AUTHTOKEN_FIELD.label = 1
pb.CL_LOGIN_LOGINUSER_HUAWEI_AUTHTOKEN_FIELD.has_default_value = false
pb.CL_LOGIN_LOGINUSER_HUAWEI_AUTHTOKEN_FIELD.default_value = ""
pb.CL_LOGIN_LOGINUSER_HUAWEI_AUTHTOKEN_FIELD.type = 9
pb.CL_LOGIN_LOGINUSER_HUAWEI_AUTHTOKEN_FIELD.cpp_type = 9

pb.CL_LOGIN_LOGINUSER_HUAWEI_CHANNELID_FIELD.name = "ChannelID"
pb.CL_LOGIN_LOGINUSER_HUAWEI_CHANNELID_FIELD.full_name = ".CL_Login_LoginUser_HuaWei.ChannelID"
pb.CL_LOGIN_LOGINUSER_HUAWEI_CHANNELID_FIELD.number = 3
pb.CL_LOGIN_LOGINUSER_HUAWEI_CHANNELID_FIELD.index = 2
pb.CL_LOGIN_LOGINUSER_HUAWEI_CHANNELID_FIELD.label = 1
pb.CL_LOGIN_LOGINUSER_HUAWEI_CHANNELID_FIELD.has_default_value = false
pb.CL_LOGIN_LOGINUSER_HUAWEI_CHANNELID_FIELD.default_value = 0
pb.CL_LOGIN_LOGINUSER_HUAWEI_CHANNELID_FIELD.type = 13
pb.CL_LOGIN_LOGINUSER_HUAWEI_CHANNELID_FIELD.cpp_type = 3

pb.CL_LOGIN_LOGINUSER_HUAWEI_ZONEID_FIELD.name = "ZoneID"
pb.CL_LOGIN_LOGINUSER_HUAWEI_ZONEID_FIELD.full_name = ".CL_Login_LoginUser_HuaWei.ZoneID"
pb.CL_LOGIN_LOGINUSER_HUAWEI_ZONEID_FIELD.number = 4
pb.CL_LOGIN_LOGINUSER_HUAWEI_ZONEID_FIELD.index = 3
pb.CL_LOGIN_LOGINUSER_HUAWEI_ZONEID_FIELD.label = 1
pb.CL_LOGIN_LOGINUSER_HUAWEI_ZONEID_FIELD.has_default_value = false
pb.CL_LOGIN_LOGINUSER_HUAWEI_ZONEID_FIELD.default_value = 0
pb.CL_LOGIN_LOGINUSER_HUAWEI_ZONEID_FIELD.type = 5
pb.CL_LOGIN_LOGINUSER_HUAWEI_ZONEID_FIELD.cpp_type = 1

pb.CL_LOGIN_LOGINUSER_HUAWEI_TS_FIELD.name = "Ts"
pb.CL_LOGIN_LOGINUSER_HUAWEI_TS_FIELD.full_name = ".CL_Login_LoginUser_HuaWei.Ts"
pb.CL_LOGIN_LOGINUSER_HUAWEI_TS_FIELD.number = 5
pb.CL_LOGIN_LOGINUSER_HUAWEI_TS_FIELD.index = 4
pb.CL_LOGIN_LOGINUSER_HUAWEI_TS_FIELD.label = 1
pb.CL_LOGIN_LOGINUSER_HUAWEI_TS_FIELD.has_default_value = false
pb.CL_LOGIN_LOGINUSER_HUAWEI_TS_FIELD.default_value = ""
pb.CL_LOGIN_LOGINUSER_HUAWEI_TS_FIELD.type = 9
pb.CL_LOGIN_LOGINUSER_HUAWEI_TS_FIELD.cpp_type = 9

pb.CL_LOGIN_LOGINUSER_HUAWEI_LEVEL_FIELD.name = "Level"
pb.CL_LOGIN_LOGINUSER_HUAWEI_LEVEL_FIELD.full_name = ".CL_Login_LoginUser_HuaWei.Level"
pb.CL_LOGIN_LOGINUSER_HUAWEI_LEVEL_FIELD.number = 6
pb.CL_LOGIN_LOGINUSER_HUAWEI_LEVEL_FIELD.index = 5
pb.CL_LOGIN_LOGINUSER_HUAWEI_LEVEL_FIELD.label = 1
pb.CL_LOGIN_LOGINUSER_HUAWEI_LEVEL_FIELD.has_default_value = false
pb.CL_LOGIN_LOGINUSER_HUAWEI_LEVEL_FIELD.default_value = ""
pb.CL_LOGIN_LOGINUSER_HUAWEI_LEVEL_FIELD.type = 9
pb.CL_LOGIN_LOGINUSER_HUAWEI_LEVEL_FIELD.cpp_type = 9

pb.CL_LOGIN_LOGINUSER_HUAWEI_UUID_FIELD.name = "UUID"
pb.CL_LOGIN_LOGINUSER_HUAWEI_UUID_FIELD.full_name = ".CL_Login_LoginUser_HuaWei.UUID"
pb.CL_LOGIN_LOGINUSER_HUAWEI_UUID_FIELD.number = 7
pb.CL_LOGIN_LOGINUSER_HUAWEI_UUID_FIELD.index = 6
pb.CL_LOGIN_LOGINUSER_HUAWEI_UUID_FIELD.label = 1
pb.CL_LOGIN_LOGINUSER_HUAWEI_UUID_FIELD.has_default_value = false
pb.CL_LOGIN_LOGINUSER_HUAWEI_UUID_FIELD.default_value = ""
pb.CL_LOGIN_LOGINUSER_HUAWEI_UUID_FIELD.type = 9
pb.CL_LOGIN_LOGINUSER_HUAWEI_UUID_FIELD.cpp_type = 9

pb.CL_LOGIN_LOGINUSER_HUAWEI.name = "CL_Login_LoginUser_HuaWei"
pb.CL_LOGIN_LOGINUSER_HUAWEI.full_name = ".CL_Login_LoginUser_HuaWei"
pb.CL_LOGIN_LOGINUSER_HUAWEI.nested_types = {}
pb.CL_LOGIN_LOGINUSER_HUAWEI.enum_types = {}
pb.CL_LOGIN_LOGINUSER_HUAWEI.fields = {pb.CL_LOGIN_LOGINUSER_HUAWEI_UID_FIELD, pb.CL_LOGIN_LOGINUSER_HUAWEI_AUTHTOKEN_FIELD, pb.CL_LOGIN_LOGINUSER_HUAWEI_CHANNELID_FIELD, pb.CL_LOGIN_LOGINUSER_HUAWEI_ZONEID_FIELD, pb.CL_LOGIN_LOGINUSER_HUAWEI_TS_FIELD, pb.CL_LOGIN_LOGINUSER_HUAWEI_LEVEL_FIELD, pb.CL_LOGIN_LOGINUSER_HUAWEI_UUID_FIELD}
pb.CL_LOGIN_LOGINUSER_HUAWEI.is_extendable = false
pb.CL_LOGIN_LOGINUSER_HUAWEI.extensions = {}
pb.CL_LOGIN_LOGINUSER_SDK_NEW_UID_FIELD.name = "Uid"
pb.CL_LOGIN_LOGINUSER_SDK_NEW_UID_FIELD.full_name = ".CL_Login_LoginUser_SDK_NEW.Uid"
pb.CL_LOGIN_LOGINUSER_SDK_NEW_UID_FIELD.number = 1
pb.CL_LOGIN_LOGINUSER_SDK_NEW_UID_FIELD.index = 0
pb.CL_LOGIN_LOGINUSER_SDK_NEW_UID_FIELD.label = 1
pb.CL_LOGIN_LOGINUSER_SDK_NEW_UID_FIELD.has_default_value = false
pb.CL_LOGIN_LOGINUSER_SDK_NEW_UID_FIELD.default_value = ""
pb.CL_LOGIN_LOGINUSER_SDK_NEW_UID_FIELD.type = 9
pb.CL_LOGIN_LOGINUSER_SDK_NEW_UID_FIELD.cpp_type = 9

pb.CL_LOGIN_LOGINUSER_SDK_NEW_AUTHTOKEN_FIELD.name = "AuthToken"
pb.CL_LOGIN_LOGINUSER_SDK_NEW_AUTHTOKEN_FIELD.full_name = ".CL_Login_LoginUser_SDK_NEW.AuthToken"
pb.CL_LOGIN_LOGINUSER_SDK_NEW_AUTHTOKEN_FIELD.number = 2
pb.CL_LOGIN_LOGINUSER_SDK_NEW_AUTHTOKEN_FIELD.index = 1
pb.CL_LOGIN_LOGINUSER_SDK_NEW_AUTHTOKEN_FIELD.label = 1
pb.CL_LOGIN_LOGINUSER_SDK_NEW_AUTHTOKEN_FIELD.has_default_value = false
pb.CL_LOGIN_LOGINUSER_SDK_NEW_AUTHTOKEN_FIELD.default_value = ""
pb.CL_LOGIN_LOGINUSER_SDK_NEW_AUTHTOKEN_FIELD.type = 9
pb.CL_LOGIN_LOGINUSER_SDK_NEW_AUTHTOKEN_FIELD.cpp_type = 9

pb.CL_LOGIN_LOGINUSER_SDK_NEW_CHANNELID_FIELD.name = "ChannelID"
pb.CL_LOGIN_LOGINUSER_SDK_NEW_CHANNELID_FIELD.full_name = ".CL_Login_LoginUser_SDK_NEW.ChannelID"
pb.CL_LOGIN_LOGINUSER_SDK_NEW_CHANNELID_FIELD.number = 3
pb.CL_LOGIN_LOGINUSER_SDK_NEW_CHANNELID_FIELD.index = 2
pb.CL_LOGIN_LOGINUSER_SDK_NEW_CHANNELID_FIELD.label = 1
pb.CL_LOGIN_LOGINUSER_SDK_NEW_CHANNELID_FIELD.has_default_value = false
pb.CL_LOGIN_LOGINUSER_SDK_NEW_CHANNELID_FIELD.default_value = 0
pb.CL_LOGIN_LOGINUSER_SDK_NEW_CHANNELID_FIELD.type = 13
pb.CL_LOGIN_LOGINUSER_SDK_NEW_CHANNELID_FIELD.cpp_type = 3

pb.CL_LOGIN_LOGINUSER_SDK_NEW_ZONEID_FIELD.name = "ZoneID"
pb.CL_LOGIN_LOGINUSER_SDK_NEW_ZONEID_FIELD.full_name = ".CL_Login_LoginUser_SDK_NEW.ZoneID"
pb.CL_LOGIN_LOGINUSER_SDK_NEW_ZONEID_FIELD.number = 4
pb.CL_LOGIN_LOGINUSER_SDK_NEW_ZONEID_FIELD.index = 3
pb.CL_LOGIN_LOGINUSER_SDK_NEW_ZONEID_FIELD.label = 1
pb.CL_LOGIN_LOGINUSER_SDK_NEW_ZONEID_FIELD.has_default_value = false
pb.CL_LOGIN_LOGINUSER_SDK_NEW_ZONEID_FIELD.default_value = 0
pb.CL_LOGIN_LOGINUSER_SDK_NEW_ZONEID_FIELD.type = 5
pb.CL_LOGIN_LOGINUSER_SDK_NEW_ZONEID_FIELD.cpp_type = 1

pb.CL_LOGIN_LOGINUSER_SDK_NEW_UUID_FIELD.name = "UUID"
pb.CL_LOGIN_LOGINUSER_SDK_NEW_UUID_FIELD.full_name = ".CL_Login_LoginUser_SDK_NEW.UUID"
pb.CL_LOGIN_LOGINUSER_SDK_NEW_UUID_FIELD.number = 5
pb.CL_LOGIN_LOGINUSER_SDK_NEW_UUID_FIELD.index = 4
pb.CL_LOGIN_LOGINUSER_SDK_NEW_UUID_FIELD.label = 1
pb.CL_LOGIN_LOGINUSER_SDK_NEW_UUID_FIELD.has_default_value = false
pb.CL_LOGIN_LOGINUSER_SDK_NEW_UUID_FIELD.default_value = ""
pb.CL_LOGIN_LOGINUSER_SDK_NEW_UUID_FIELD.type = 9
pb.CL_LOGIN_LOGINUSER_SDK_NEW_UUID_FIELD.cpp_type = 9

pb.CL_LOGIN_LOGINUSER_SDK_NEW_SDKID_FIELD.name = "SDKID"
pb.CL_LOGIN_LOGINUSER_SDK_NEW_SDKID_FIELD.full_name = ".CL_Login_LoginUser_SDK_NEW.SDKID"
pb.CL_LOGIN_LOGINUSER_SDK_NEW_SDKID_FIELD.number = 6
pb.CL_LOGIN_LOGINUSER_SDK_NEW_SDKID_FIELD.index = 5
pb.CL_LOGIN_LOGINUSER_SDK_NEW_SDKID_FIELD.label = 1
pb.CL_LOGIN_LOGINUSER_SDK_NEW_SDKID_FIELD.has_default_value = false
pb.CL_LOGIN_LOGINUSER_SDK_NEW_SDKID_FIELD.default_value = ""
pb.CL_LOGIN_LOGINUSER_SDK_NEW_SDKID_FIELD.type = 9
pb.CL_LOGIN_LOGINUSER_SDK_NEW_SDKID_FIELD.cpp_type = 9

pb.CL_LOGIN_LOGINUSER_SDK_NEW.name = "CL_Login_LoginUser_SDK_NEW"
pb.CL_LOGIN_LOGINUSER_SDK_NEW.full_name = ".CL_Login_LoginUser_SDK_NEW"
pb.CL_LOGIN_LOGINUSER_SDK_NEW.nested_types = {}
pb.CL_LOGIN_LOGINUSER_SDK_NEW.enum_types = {}
pb.CL_LOGIN_LOGINUSER_SDK_NEW.fields = {pb.CL_LOGIN_LOGINUSER_SDK_NEW_UID_FIELD, pb.CL_LOGIN_LOGINUSER_SDK_NEW_AUTHTOKEN_FIELD, pb.CL_LOGIN_LOGINUSER_SDK_NEW_CHANNELID_FIELD, pb.CL_LOGIN_LOGINUSER_SDK_NEW_ZONEID_FIELD, pb.CL_LOGIN_LOGINUSER_SDK_NEW_UUID_FIELD, pb.CL_LOGIN_LOGINUSER_SDK_NEW_SDKID_FIELD}
pb.CL_LOGIN_LOGINUSER_SDK_NEW.is_extendable = false
pb.CL_LOGIN_LOGINUSER_SDK_NEW.extensions = {}
pb.CL_LOGIN_LOGINUSER_6KW_UID_FIELD.name = "Uid"
pb.CL_LOGIN_LOGINUSER_6KW_UID_FIELD.full_name = ".CL_Login_LoginUser_6kw.Uid"
pb.CL_LOGIN_LOGINUSER_6KW_UID_FIELD.number = 1
pb.CL_LOGIN_LOGINUSER_6KW_UID_FIELD.index = 0
pb.CL_LOGIN_LOGINUSER_6KW_UID_FIELD.label = 1
pb.CL_LOGIN_LOGINUSER_6KW_UID_FIELD.has_default_value = false
pb.CL_LOGIN_LOGINUSER_6KW_UID_FIELD.default_value = ""
pb.CL_LOGIN_LOGINUSER_6KW_UID_FIELD.type = 9
pb.CL_LOGIN_LOGINUSER_6KW_UID_FIELD.cpp_type = 9

pb.CL_LOGIN_LOGINUSER_6KW_AUTHTOKEN_FIELD.name = "AuthToken"
pb.CL_LOGIN_LOGINUSER_6KW_AUTHTOKEN_FIELD.full_name = ".CL_Login_LoginUser_6kw.AuthToken"
pb.CL_LOGIN_LOGINUSER_6KW_AUTHTOKEN_FIELD.number = 2
pb.CL_LOGIN_LOGINUSER_6KW_AUTHTOKEN_FIELD.index = 1
pb.CL_LOGIN_LOGINUSER_6KW_AUTHTOKEN_FIELD.label = 1
pb.CL_LOGIN_LOGINUSER_6KW_AUTHTOKEN_FIELD.has_default_value = false
pb.CL_LOGIN_LOGINUSER_6KW_AUTHTOKEN_FIELD.default_value = ""
pb.CL_LOGIN_LOGINUSER_6KW_AUTHTOKEN_FIELD.type = 9
pb.CL_LOGIN_LOGINUSER_6KW_AUTHTOKEN_FIELD.cpp_type = 9

pb.CL_LOGIN_LOGINUSER_6KW_CHANNELID_FIELD.name = "ChannelID"
pb.CL_LOGIN_LOGINUSER_6KW_CHANNELID_FIELD.full_name = ".CL_Login_LoginUser_6kw.ChannelID"
pb.CL_LOGIN_LOGINUSER_6KW_CHANNELID_FIELD.number = 3
pb.CL_LOGIN_LOGINUSER_6KW_CHANNELID_FIELD.index = 2
pb.CL_LOGIN_LOGINUSER_6KW_CHANNELID_FIELD.label = 1
pb.CL_LOGIN_LOGINUSER_6KW_CHANNELID_FIELD.has_default_value = false
pb.CL_LOGIN_LOGINUSER_6KW_CHANNELID_FIELD.default_value = 0
pb.CL_LOGIN_LOGINUSER_6KW_CHANNELID_FIELD.type = 13
pb.CL_LOGIN_LOGINUSER_6KW_CHANNELID_FIELD.cpp_type = 3

pb.CL_LOGIN_LOGINUSER_6KW_ZONEID_FIELD.name = "ZoneID"
pb.CL_LOGIN_LOGINUSER_6KW_ZONEID_FIELD.full_name = ".CL_Login_LoginUser_6kw.ZoneID"
pb.CL_LOGIN_LOGINUSER_6KW_ZONEID_FIELD.number = 4
pb.CL_LOGIN_LOGINUSER_6KW_ZONEID_FIELD.index = 3
pb.CL_LOGIN_LOGINUSER_6KW_ZONEID_FIELD.label = 1
pb.CL_LOGIN_LOGINUSER_6KW_ZONEID_FIELD.has_default_value = false
pb.CL_LOGIN_LOGINUSER_6KW_ZONEID_FIELD.default_value = 0
pb.CL_LOGIN_LOGINUSER_6KW_ZONEID_FIELD.type = 5
pb.CL_LOGIN_LOGINUSER_6KW_ZONEID_FIELD.cpp_type = 1

pb.CL_LOGIN_LOGINUSER_6KW_UUID_FIELD.name = "UUID"
pb.CL_LOGIN_LOGINUSER_6KW_UUID_FIELD.full_name = ".CL_Login_LoginUser_6kw.UUID"
pb.CL_LOGIN_LOGINUSER_6KW_UUID_FIELD.number = 5
pb.CL_LOGIN_LOGINUSER_6KW_UUID_FIELD.index = 4
pb.CL_LOGIN_LOGINUSER_6KW_UUID_FIELD.label = 1
pb.CL_LOGIN_LOGINUSER_6KW_UUID_FIELD.has_default_value = false
pb.CL_LOGIN_LOGINUSER_6KW_UUID_FIELD.default_value = ""
pb.CL_LOGIN_LOGINUSER_6KW_UUID_FIELD.type = 9
pb.CL_LOGIN_LOGINUSER_6KW_UUID_FIELD.cpp_type = 9

pb.CL_LOGIN_LOGINUSER_6KW.name = "CL_Login_LoginUser_6kw"
pb.CL_LOGIN_LOGINUSER_6KW.full_name = ".CL_Login_LoginUser_6kw"
pb.CL_LOGIN_LOGINUSER_6KW.nested_types = {}
pb.CL_LOGIN_LOGINUSER_6KW.enum_types = {}
pb.CL_LOGIN_LOGINUSER_6KW.fields = {pb.CL_LOGIN_LOGINUSER_6KW_UID_FIELD, pb.CL_LOGIN_LOGINUSER_6KW_AUTHTOKEN_FIELD, pb.CL_LOGIN_LOGINUSER_6KW_CHANNELID_FIELD, pb.CL_LOGIN_LOGINUSER_6KW_ZONEID_FIELD, pb.CL_LOGIN_LOGINUSER_6KW_UUID_FIELD}
pb.CL_LOGIN_LOGINUSER_6KW.is_extendable = false
pb.CL_LOGIN_LOGINUSER_6KW.extensions = {}
pb.CL_LOGIN_LOGINUSER_669_UID_FIELD.name = "Uid"
pb.CL_LOGIN_LOGINUSER_669_UID_FIELD.full_name = ".CL_Login_LoginUser_669.Uid"
pb.CL_LOGIN_LOGINUSER_669_UID_FIELD.number = 1
pb.CL_LOGIN_LOGINUSER_669_UID_FIELD.index = 0
pb.CL_LOGIN_LOGINUSER_669_UID_FIELD.label = 1
pb.CL_LOGIN_LOGINUSER_669_UID_FIELD.has_default_value = false
pb.CL_LOGIN_LOGINUSER_669_UID_FIELD.default_value = ""
pb.CL_LOGIN_LOGINUSER_669_UID_FIELD.type = 9
pb.CL_LOGIN_LOGINUSER_669_UID_FIELD.cpp_type = 9

pb.CL_LOGIN_LOGINUSER_669_AUTHTOKEN_FIELD.name = "AuthToken"
pb.CL_LOGIN_LOGINUSER_669_AUTHTOKEN_FIELD.full_name = ".CL_Login_LoginUser_669.AuthToken"
pb.CL_LOGIN_LOGINUSER_669_AUTHTOKEN_FIELD.number = 2
pb.CL_LOGIN_LOGINUSER_669_AUTHTOKEN_FIELD.index = 1
pb.CL_LOGIN_LOGINUSER_669_AUTHTOKEN_FIELD.label = 1
pb.CL_LOGIN_LOGINUSER_669_AUTHTOKEN_FIELD.has_default_value = false
pb.CL_LOGIN_LOGINUSER_669_AUTHTOKEN_FIELD.default_value = ""
pb.CL_LOGIN_LOGINUSER_669_AUTHTOKEN_FIELD.type = 9
pb.CL_LOGIN_LOGINUSER_669_AUTHTOKEN_FIELD.cpp_type = 9

pb.CL_LOGIN_LOGINUSER_669_CHANNELID_FIELD.name = "ChannelID"
pb.CL_LOGIN_LOGINUSER_669_CHANNELID_FIELD.full_name = ".CL_Login_LoginUser_669.ChannelID"
pb.CL_LOGIN_LOGINUSER_669_CHANNELID_FIELD.number = 3
pb.CL_LOGIN_LOGINUSER_669_CHANNELID_FIELD.index = 2
pb.CL_LOGIN_LOGINUSER_669_CHANNELID_FIELD.label = 1
pb.CL_LOGIN_LOGINUSER_669_CHANNELID_FIELD.has_default_value = false
pb.CL_LOGIN_LOGINUSER_669_CHANNELID_FIELD.default_value = 0
pb.CL_LOGIN_LOGINUSER_669_CHANNELID_FIELD.type = 13
pb.CL_LOGIN_LOGINUSER_669_CHANNELID_FIELD.cpp_type = 3

pb.CL_LOGIN_LOGINUSER_669_ZONEID_FIELD.name = "ZoneID"
pb.CL_LOGIN_LOGINUSER_669_ZONEID_FIELD.full_name = ".CL_Login_LoginUser_669.ZoneID"
pb.CL_LOGIN_LOGINUSER_669_ZONEID_FIELD.number = 4
pb.CL_LOGIN_LOGINUSER_669_ZONEID_FIELD.index = 3
pb.CL_LOGIN_LOGINUSER_669_ZONEID_FIELD.label = 1
pb.CL_LOGIN_LOGINUSER_669_ZONEID_FIELD.has_default_value = false
pb.CL_LOGIN_LOGINUSER_669_ZONEID_FIELD.default_value = 0
pb.CL_LOGIN_LOGINUSER_669_ZONEID_FIELD.type = 5
pb.CL_LOGIN_LOGINUSER_669_ZONEID_FIELD.cpp_type = 1

pb.CL_LOGIN_LOGINUSER_669_UUID_FIELD.name = "UUID"
pb.CL_LOGIN_LOGINUSER_669_UUID_FIELD.full_name = ".CL_Login_LoginUser_669.UUID"
pb.CL_LOGIN_LOGINUSER_669_UUID_FIELD.number = 5
pb.CL_LOGIN_LOGINUSER_669_UUID_FIELD.index = 4
pb.CL_LOGIN_LOGINUSER_669_UUID_FIELD.label = 1
pb.CL_LOGIN_LOGINUSER_669_UUID_FIELD.has_default_value = false
pb.CL_LOGIN_LOGINUSER_669_UUID_FIELD.default_value = ""
pb.CL_LOGIN_LOGINUSER_669_UUID_FIELD.type = 9
pb.CL_LOGIN_LOGINUSER_669_UUID_FIELD.cpp_type = 9

pb.CL_LOGIN_LOGINUSER_669_APPID_FIELD.name = "AppId"
pb.CL_LOGIN_LOGINUSER_669_APPID_FIELD.full_name = ".CL_Login_LoginUser_669.AppId"
pb.CL_LOGIN_LOGINUSER_669_APPID_FIELD.number = 6
pb.CL_LOGIN_LOGINUSER_669_APPID_FIELD.index = 5
pb.CL_LOGIN_LOGINUSER_669_APPID_FIELD.label = 1
pb.CL_LOGIN_LOGINUSER_669_APPID_FIELD.has_default_value = false
pb.CL_LOGIN_LOGINUSER_669_APPID_FIELD.default_value = ""
pb.CL_LOGIN_LOGINUSER_669_APPID_FIELD.type = 9
pb.CL_LOGIN_LOGINUSER_669_APPID_FIELD.cpp_type = 9

pb.CL_LOGIN_LOGINUSER_669.name = "CL_Login_LoginUser_669"
pb.CL_LOGIN_LOGINUSER_669.full_name = ".CL_Login_LoginUser_669"
pb.CL_LOGIN_LOGINUSER_669.nested_types = {}
pb.CL_LOGIN_LOGINUSER_669.enum_types = {}
pb.CL_LOGIN_LOGINUSER_669.fields = {pb.CL_LOGIN_LOGINUSER_669_UID_FIELD, pb.CL_LOGIN_LOGINUSER_669_AUTHTOKEN_FIELD, pb.CL_LOGIN_LOGINUSER_669_CHANNELID_FIELD, pb.CL_LOGIN_LOGINUSER_669_ZONEID_FIELD, pb.CL_LOGIN_LOGINUSER_669_UUID_FIELD, pb.CL_LOGIN_LOGINUSER_669_APPID_FIELD}
pb.CL_LOGIN_LOGINUSER_669.is_extendable = false
pb.CL_LOGIN_LOGINUSER_669.extensions = {}
pb.CL_LOGIN_LOGINUSER_SDK_COM_UID_FIELD.name = "Uid"
pb.CL_LOGIN_LOGINUSER_SDK_COM_UID_FIELD.full_name = ".CL_Login_LoginUser_SDK_Com.Uid"
pb.CL_LOGIN_LOGINUSER_SDK_COM_UID_FIELD.number = 1
pb.CL_LOGIN_LOGINUSER_SDK_COM_UID_FIELD.index = 0
pb.CL_LOGIN_LOGINUSER_SDK_COM_UID_FIELD.label = 1
pb.CL_LOGIN_LOGINUSER_SDK_COM_UID_FIELD.has_default_value = false
pb.CL_LOGIN_LOGINUSER_SDK_COM_UID_FIELD.default_value = ""
pb.CL_LOGIN_LOGINUSER_SDK_COM_UID_FIELD.type = 9
pb.CL_LOGIN_LOGINUSER_SDK_COM_UID_FIELD.cpp_type = 9

pb.CL_LOGIN_LOGINUSER_SDK_COM_AUTHTOKEN_FIELD.name = "AuthToken"
pb.CL_LOGIN_LOGINUSER_SDK_COM_AUTHTOKEN_FIELD.full_name = ".CL_Login_LoginUser_SDK_Com.AuthToken"
pb.CL_LOGIN_LOGINUSER_SDK_COM_AUTHTOKEN_FIELD.number = 2
pb.CL_LOGIN_LOGINUSER_SDK_COM_AUTHTOKEN_FIELD.index = 1
pb.CL_LOGIN_LOGINUSER_SDK_COM_AUTHTOKEN_FIELD.label = 1
pb.CL_LOGIN_LOGINUSER_SDK_COM_AUTHTOKEN_FIELD.has_default_value = false
pb.CL_LOGIN_LOGINUSER_SDK_COM_AUTHTOKEN_FIELD.default_value = ""
pb.CL_LOGIN_LOGINUSER_SDK_COM_AUTHTOKEN_FIELD.type = 9
pb.CL_LOGIN_LOGINUSER_SDK_COM_AUTHTOKEN_FIELD.cpp_type = 9

pb.CL_LOGIN_LOGINUSER_SDK_COM_CHANNELID_FIELD.name = "ChannelID"
pb.CL_LOGIN_LOGINUSER_SDK_COM_CHANNELID_FIELD.full_name = ".CL_Login_LoginUser_SDK_Com.ChannelID"
pb.CL_LOGIN_LOGINUSER_SDK_COM_CHANNELID_FIELD.number = 3
pb.CL_LOGIN_LOGINUSER_SDK_COM_CHANNELID_FIELD.index = 2
pb.CL_LOGIN_LOGINUSER_SDK_COM_CHANNELID_FIELD.label = 1
pb.CL_LOGIN_LOGINUSER_SDK_COM_CHANNELID_FIELD.has_default_value = false
pb.CL_LOGIN_LOGINUSER_SDK_COM_CHANNELID_FIELD.default_value = 0
pb.CL_LOGIN_LOGINUSER_SDK_COM_CHANNELID_FIELD.type = 13
pb.CL_LOGIN_LOGINUSER_SDK_COM_CHANNELID_FIELD.cpp_type = 3

pb.CL_LOGIN_LOGINUSER_SDK_COM_ZONEID_FIELD.name = "ZoneID"
pb.CL_LOGIN_LOGINUSER_SDK_COM_ZONEID_FIELD.full_name = ".CL_Login_LoginUser_SDK_Com.ZoneID"
pb.CL_LOGIN_LOGINUSER_SDK_COM_ZONEID_FIELD.number = 4
pb.CL_LOGIN_LOGINUSER_SDK_COM_ZONEID_FIELD.index = 3
pb.CL_LOGIN_LOGINUSER_SDK_COM_ZONEID_FIELD.label = 1
pb.CL_LOGIN_LOGINUSER_SDK_COM_ZONEID_FIELD.has_default_value = false
pb.CL_LOGIN_LOGINUSER_SDK_COM_ZONEID_FIELD.default_value = 0
pb.CL_LOGIN_LOGINUSER_SDK_COM_ZONEID_FIELD.type = 5
pb.CL_LOGIN_LOGINUSER_SDK_COM_ZONEID_FIELD.cpp_type = 1

pb.CL_LOGIN_LOGINUSER_SDK_COM_UUID_FIELD.name = "UUID"
pb.CL_LOGIN_LOGINUSER_SDK_COM_UUID_FIELD.full_name = ".CL_Login_LoginUser_SDK_Com.UUID"
pb.CL_LOGIN_LOGINUSER_SDK_COM_UUID_FIELD.number = 5
pb.CL_LOGIN_LOGINUSER_SDK_COM_UUID_FIELD.index = 4
pb.CL_LOGIN_LOGINUSER_SDK_COM_UUID_FIELD.label = 1
pb.CL_LOGIN_LOGINUSER_SDK_COM_UUID_FIELD.has_default_value = false
pb.CL_LOGIN_LOGINUSER_SDK_COM_UUID_FIELD.default_value = ""
pb.CL_LOGIN_LOGINUSER_SDK_COM_UUID_FIELD.type = 9
pb.CL_LOGIN_LOGINUSER_SDK_COM_UUID_FIELD.cpp_type = 9

pb.CL_LOGIN_LOGINUSER_SDK_COM_PARAM_FIELD.name = "Param"
pb.CL_LOGIN_LOGINUSER_SDK_COM_PARAM_FIELD.full_name = ".CL_Login_LoginUser_SDK_Com.Param"
pb.CL_LOGIN_LOGINUSER_SDK_COM_PARAM_FIELD.number = 6
pb.CL_LOGIN_LOGINUSER_SDK_COM_PARAM_FIELD.index = 5
pb.CL_LOGIN_LOGINUSER_SDK_COM_PARAM_FIELD.label = 1
pb.CL_LOGIN_LOGINUSER_SDK_COM_PARAM_FIELD.has_default_value = false
pb.CL_LOGIN_LOGINUSER_SDK_COM_PARAM_FIELD.default_value = ""
pb.CL_LOGIN_LOGINUSER_SDK_COM_PARAM_FIELD.type = 9
pb.CL_LOGIN_LOGINUSER_SDK_COM_PARAM_FIELD.cpp_type = 9

pb.CL_LOGIN_LOGINUSER_SDK_COM.name = "CL_Login_LoginUser_SDK_Com"
pb.CL_LOGIN_LOGINUSER_SDK_COM.full_name = ".CL_Login_LoginUser_SDK_Com"
pb.CL_LOGIN_LOGINUSER_SDK_COM.nested_types = {}
pb.CL_LOGIN_LOGINUSER_SDK_COM.enum_types = {}
pb.CL_LOGIN_LOGINUSER_SDK_COM.fields = {pb.CL_LOGIN_LOGINUSER_SDK_COM_UID_FIELD, pb.CL_LOGIN_LOGINUSER_SDK_COM_AUTHTOKEN_FIELD, pb.CL_LOGIN_LOGINUSER_SDK_COM_CHANNELID_FIELD, pb.CL_LOGIN_LOGINUSER_SDK_COM_ZONEID_FIELD, pb.CL_LOGIN_LOGINUSER_SDK_COM_UUID_FIELD, pb.CL_LOGIN_LOGINUSER_SDK_COM_PARAM_FIELD}
pb.CL_LOGIN_LOGINUSER_SDK_COM.is_extendable = false
pb.CL_LOGIN_LOGINUSER_SDK_COM.extensions = {}

CL_Login_CreateActor = protobuf.Message(pb.CL_LOGIN_CREATEACTOR)
CL_Login_DeleteActor = protobuf.Message(pb.CL_LOGIN_DELETEACTOR)
CL_Login_LoginUser = protobuf.Message(pb.CL_LOGIN_LOGINUSER)
CL_Login_LoginUser_669 = protobuf.Message(pb.CL_LOGIN_LOGINUSER_669)
CL_Login_LoginUser_6kw = protobuf.Message(pb.CL_LOGIN_LOGINUSER_6KW)
CL_Login_LoginUser_Amigo = protobuf.Message(pb.CL_LOGIN_LOGINUSER_AMIGO)
CL_Login_LoginUser_HuaWei = protobuf.Message(pb.CL_LOGIN_LOGINUSER_HUAWEI)
CL_Login_LoginUser_Lenovo = protobuf.Message(pb.CL_LOGIN_LOGINUSER_LENOVO)
CL_Login_LoginUser_MI = protobuf.Message(pb.CL_LOGIN_LOGINUSER_MI)
CL_Login_LoginUser_Oppo = protobuf.Message(pb.CL_LOGIN_LOGINUSER_OPPO)
CL_Login_LoginUser_SDK = protobuf.Message(pb.CL_LOGIN_LOGINUSER_SDK)
CL_Login_LoginUser_SDK_Com = protobuf.Message(pb.CL_LOGIN_LOGINUSER_SDK_COM)
CL_Login_LoginUser_SDK_NEW = protobuf.Message(pb.CL_LOGIN_LOGINUSER_SDK_NEW)
CL_Login_LoginUser_UC = protobuf.Message(pb.CL_LOGIN_LOGINUSER_UC)
CL_Login_LoginUser_Vivo = protobuf.Message(pb.CL_LOGIN_LOGINUSER_VIVO)
CL_Login_LoginUser_ZS = protobuf.Message(pb.CL_LOGIN_LOGINUSER_ZS)
CL_Login_LogoutUser = protobuf.Message(pb.CL_LOGIN_LOGOUTUSER)
CL_Login_Reconnect = protobuf.Message(pb.CL_LOGIN_RECONNECT)
CL_Login_Reconnect_MI = protobuf.Message(pb.CL_LOGIN_RECONNECT_MI)
CL_Login_Reconnect_UC = protobuf.Message(pb.CL_LOGIN_RECONNECT_UC)
CL_Login_Reconnect_ZS = protobuf.Message(pb.CL_LOGIN_RECONNECT_ZS)
CL_Login_Register = protobuf.Message(pb.CL_LOGIN_REGISTER)
CL_Login_SeleteActor = protobuf.Message(pb.CL_LOGIN_SELETEACTOR)
CL_Login_SwitchActor = protobuf.Message(pb.CL_LOGIN_SWITCHACTOR)
LC_Login_CreateActor = protobuf.Message(pb.LC_LOGIN_CREATEACTOR)
LC_Login_CreateActor.ActorInfo = protobuf.Message(pb.LC_LOGIN_CREATEACTOR_ACTORINFO)
LC_Login_DeleteActor = protobuf.Message(pb.LC_LOGIN_DELETEACTOR)
LC_Login_LogoutUser = protobuf.Message(pb.LC_LOGIN_LOGOUTUSER)
LC_Login_Reconnect = protobuf.Message(pb.LC_LOGIN_RECONNECT)
LC_Login_Register = protobuf.Message(pb.LC_LOGIN_REGISTER)
LC_Login_SeleteActor = protobuf.Message(pb.LC_LOGIN_SELETEACTOR)
LC_Login_SwitchActor = protobuf.Message(pb.LC_LOGIN_SWITCHACTOR)
LD_Login_LoginUser_SDK = protobuf.Message(pb.LD_LOGIN_LOGINUSER_SDK)
LS_Login_LogoutUser = protobuf.Message(pb.LS_LOGIN_LOGOUTUSER)
LS_Login_Reconnect = protobuf.Message(pb.LS_LOGIN_RECONNECT)
LS_Login_SeleteActor = protobuf.Message(pb.LS_LOGIN_SELETEACTOR)
MSG_LOGIN_ADD_RECONNECT = 7
MSG_LOGIN_CREATEACTOR = 3
MSG_LOGIN_DELETEACTOR = 4
MSG_LOGIN_DEL_RECONNECT = 8
MSG_LOGIN_LOGINUSER = 2
MSG_LOGIN_LOGINUSER_669 = 25
MSG_LOGIN_LOGINUSER_6KW = 24
MSG_LOGIN_LOGINUSER_AMIGOSDK = 19
MSG_LOGIN_LOGINUSER_COM = 26
MSG_LOGIN_LOGINUSER_HUAWEISDK = 21
MSG_LOGIN_LOGINUSER_IOS_13YX = 23
MSG_LOGIN_LOGINUSER_LENOVOSDK = 20
MSG_LOGIN_LOGINUSER_MI = 12
MSG_LOGIN_LOGINUSER_SDK = 17
MSG_LOGIN_LOGINUSER_UC = 9
MSG_LOGIN_LOGINUSER_VIVOSDK = 18
MSG_LOGIN_LOGINUSER_YIJIESDK = 22
MSG_LOGIN_LOGINUSER_ZS = 14
MSG_LOGIN_LOGOUTUSER = 16
MSG_LOGIN_NONE = 0
MSG_LOGIN_PLAYER_STATE_CREATE_ACTOR = 1
MSG_LOGIN_PLAYER_STATE_PLAYING = 3
MSG_LOGIN_PLAYER_STATE_SELECT_ACTOR = 2
MSG_LOGIN_PLAYER_STATE_UNKNOWN = 0
MSG_LOGIN_RECONNECT = 6
MSG_LOGIN_RECONNECT_MI = 13
MSG_LOGIN_RECONNECT_UC = 11
MSG_LOGIN_RECONNECT_ZS = 15
MSG_LOGIN_REGISTER = 1
MSG_LOGIN_SELETEACTOR = 5
MSG_LOGIN_SWITCHACTOR = 10
SC_Login_LoginUser = protobuf.Message(pb.SC_LOGIN_LOGINUSER)
SC_Login_LoginUser.ActorInfo = protobuf.Message(pb.SC_LOGIN_LOGINUSER_ACTORINFO)
SL_Login_AddReconnect = protobuf.Message(pb.SL_LOGIN_ADDRECONNECT)
SL_Login_DelReconnect = protobuf.Message(pb.SL_LOGIN_DELRECONNECT)

