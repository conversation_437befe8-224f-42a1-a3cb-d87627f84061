--[[
********************************************************************
    created:	2024/05/10
    author :	黄宗银
    purpose:    Http请求助手
*********************************************************************
--]]

HttpHelper = {
    --- 解析响应并执行回调
    ---@param url string 请求地址
    ---@param rst table 响应数据(text、error)
    ---@param cbSuccess? function 成功的回调函数
    ---@param cbFailed? function 失败的回调函数
    ---@param rData? table 与请求关联的额外数据
    ParseRst = function(url, rst, cbSuccess, cbFailed, rData)
        local err = "";
        local rspData = nil;
        if rst == nil then
            err = string.format("响应数据为空:[%s]", url);
        else
            if string.len(rst.error) > 0 then
                err = string.format("Http请求失败[%s]: %s", url, rst.error);
            else
                rspData = dkjsonHelper.decode(rst.text)
                -- 将isError转为Success，以兼容老接口
                if rspData and rspData.isError ~= nil then
                    rspData.Success = not rspData.isError;
                end
            end
        end

        -- 如果有设置事件号,则引发回调前事件(参数与回调一样)
        if (rData and rData.EventID) then
            EventManager:Fire(rData.EventID, 0, rspData, rData)
        end

        -- 执行回调方法
        if (rspData and rspData.Success) then
            if (cbSuccess ~= nil) then
                cbSuccess(rspData, rData);
            end
        else
            error(err);
            if (cbFailed ~= nil) then
                cbFailed(rspData, rData);
            end
        end

        -- 如果有设置事件号,则引发回调后事件(参数与回调一样)
        if (rData and rData.EventID) then
            EventManager:Fire(rData.EventID, 1, rspData, rData)
        end
    end,

    --- 发出Http请求,收到响应后,对其解析并执行回调
    ---@param url string 请求地址
    ---@param ps table 请求参数
    ---@param cbSuccess? function 成功的回调函数
    ---@param cbFailed? function 失败的回调函数
    ---@param method? string 请求方法
    ---@param rData? table 与请求关联的额外数据
    Send = function(url, ps, cbSuccess, cbFailed, method, rData)
        -- 如果角色已上线，则所有请求都加上ActorID这个参数
	    local actorData = LoginModule:GetSelectActorData()
        if(actorData and ps)then
            if (ps.ActorID == nil) then
                ps.ActorID = actorData.ActorID;
            end
        end

        HttpRequestHelper.HttpSend(url, ps, function(rst)
            HttpHelper.ParseRst(url, rst, cbSuccess, cbFailed, rData);
        end, rData, method)
    end,

    --- 发出json请求,收到响应后,对其解析并执行回调
    ---@param url string 请求地址
    ---@param json table json字符串
    ---@param cbSuccess? function 成功的回调函数
    ---@param cbFailed? function 失败的回调函数
    ---@param method? string 请求方法
    ---@param rData? table 与请求关联的额外数据
    HttpPostJson = function(url, json, cbSuccess, cbFailed, rData)
        HttpRequestHelper.HttpPostJson(url, json, function(rst)
            HttpHelper.ParseRst(url, rst, cbSuccess, cbFailed, rData);
        end, rData)
    end
}
