local luaID = ('UIBattleResultView')


local UIBattleResultView = {}

-- 订阅事件列表
function UIBattleResultView:GetOpenEventList()
	return
	{
		
	}
end

-- 初始化
function UIBattleResultView:OnCreate()
	self.selectSkillItem = nil
	self.objList.Btn_Close.onClick:AddListenerEx(self.OnClickClose)
	self.objList.Btn_Close2.onClick:AddListenerEx(self.OnClickClose)
	self.objList.Btn_quit.onClick:AddListenerEx(self.OnClickClose)
	self.objList.Btn_rechallenge.onClick:AddListenerEx(self.OnClickReChallenge)
	self.objList.Txt_Quit.text = GetGameText(luaID, 3)
	self.objList.Txt_NextLevel.text = GetGameText(luaID, 2)
	self.objList.Txt_Rechallenge.text = GetGameText(luaID, 1)
	self.btnList = { self.objList.Btn_Close, self.objList.Btn_rechallenge, self.objList.Btn_Close2, self.objList.Btn_nextLevel, self.objList.Btn_quit}	
	
	return true
end 

-- 窗口开启
function UIBattleResultView:OnOpen(isSucces, resultStr)

	self.objList.success.gameObject:SetActive(isSucces)
	self.objList.fail.gameObject:SetActive(not isSucces)
	self.objList.Btn_nextLevel.gameObject:SetActive(isSucces)
	
	local result = HelperL.Split(resultStr, ',')	
	self.objList.Txt_GoldNum.text = result[1]
	self.objList.Txt_KillNum.text = result[2]
	self.objList.Txt_CurLevel.text = "等级:" .. result[3]

end

function UIBattleResultView:OnClickReChallenge()
	UIBattleResultView:OnClose()
	UIBattleResultView:BattleById(BattleManager.levelConfig.ID)
end

function UIBattleResultView:BattleById(id)
	LuaModule.RunLuaRequest(string.format('LuaRequestBrustCatEnterBattleStart?StageID=%s', tostring(id)), function(resultCode, content)
		if resultCode ~= RESULT_CODE.RESULT_COMMON_SUCCEED then
			HelperL.ShowMessage(TipType.FlowText, ResultCode:GetResultCodeStr(resultCode, content))
		else
			self:CloseSelf()
			EntityModule.luaToCshape:LuaSendData(id, HelperL.GetWeaponEquipID(), HelperL.GetPlayerFields())
			--HelperL.GetPlayerFields()获取玩家属性 字符串 属性ID;属性值|属性ID;属性值
			--HelperL.GetWeaponEquipID() --获取火之装备ID
			print("HelperL.GetPlayerFields() = " .. HelperL.GetPlayerFields())
			print("HelperL.GetWeaponEquipID() = " .. HelperL.GetWeaponEquipID())
			BattleManager.InitBattle(id)
			SceneManager:LoadSceneWithLoading(tostring(id), true, false)
			UIManager:CloseWnd(WndType.Main)
			--进入战斗
		end
	end)
end


-- 每秒更新
function UIBattleResultView:OnSecondUpdate()
	
end

-- 窗口关闭
function UIBattleResultView:OnClose()
	
end

-- 窗口销毁
function UIBattleResultView:OnDestroy()
	-- 释放内存
	for i, btn in ipairs(self.btnList) do
		btn.onClick:RemoveAllListeners()
		btn.onClick:Invoke()
	end
end


-- 点击返回按钮
function UIBattleResultView.OnClickClose()
	local self = UIBattleResultView
	self:CloseSelf()
end
return UIBattleResultView