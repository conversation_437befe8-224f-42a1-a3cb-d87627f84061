﻿using System.Collections.Generic;

using Apq.Unity3D.UnityHelpers;

namespace ViewModel
{
    /// <summary>
    /// 按间隔计算出的直线移动轨迹
    /// </summary>
    public class StraightLocus_Duration
    {
        /// <summary>
        /// 直线移动线段(轨迹)。每条线段表示用Duration时长，从起点移动到终点
        /// </summary>
        public List<LineSegment> Locus { get; } = new();

        /// <summary>
        /// 间隔时长(秒)。默认0.03秒，近似33FPS
        /// </summary>
        public float Duration { get; set; } = 0.03f;
        
        /// <summary>
        /// 移动进度(索引号)
        /// </summary>
        public int Progress { get; set; }
    }
}