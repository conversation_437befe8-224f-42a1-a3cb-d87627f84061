﻿// ReSharper disable InconsistentNaming

using System;
using System.Collections.Generic;

using DataStructure;

using X.PB;

namespace ViewModel
{
	/// <summary>
	/// 一支枪(角色战场背包中/商店中)
	/// </summary>
	public class GunItem
	{
		/// <summary>
		/// 枪的唯一ID
		/// </summary>
		public Guid GunGuid { get; set; } = Guid.NewGuid();

		/// <summary>
		/// 枪ID
		/// </summary>
		public int GunId { get; set; }

		/// <summary>
		/// 枪的等级
		/// </summary>
		public int GunLvl { get; set; }

		/// <summary>
		/// 物品Id
		/// </summary>
		public int GoodsId { get; set; }

		/// <summary>
		/// 武器的星量
		/// </summary>
		public int StarCount { get; set; }

		/// <summary>
		/// 枪的背包坐标
		/// </summary>
		public List<BagPosition> PosInBag { get; set; }

		/// <summary>
		/// 枪的配置行
		/// </summary>
		public GunCfg.Types.CSVRow CsvRow_Gun { get; set; }

        // /// <summary>
        // /// 暂停出战的回合数(>0即为死亡状态)
        // /// </summary>
        // public int SuspendFightTimes { get; set; }
	}
}
