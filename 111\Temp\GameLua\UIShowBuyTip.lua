
local luaID = ('UIShowBuyTip')

local UIShowBuyTip = {}

-- 初始化
function UIShowBuyTip:OnCreate()
--	self.objList.Btn_Close.onClick:AddListenerEx(self.OnClickClose)
	self.objList.Btn_Mask.onClick:AddListenerEx(self.OnClickClose)
	self.objList.Btn_Btn1.onClick:AddListenerEx(self.OnClickBtn1)
	self.btnList = { self.objList.Btn_Close, self.objList.Btn_Btn1}
	self.prizeContent = self.objList.PrizeContent.transform
	self.itemList = {}
	return true
end

-- 窗口开启
function UIShowBuyTip:OnOpen(title, goodsName, icon, okParam,quality)
	self.objList.Txt_Title.text = title
	self.objList.Txt_ItemTitle.text = goodsName
	AtlasManager:AsyncGetSprite(okParam.icon_bg, self.objList.Img_ItemIcon_bg)
	AtlasManager:AsyncGetSprite(icon, self.objList.Img_ItemIcon)

	HelperL.SetImageMaterial(self.objList.Img_vfx,quality)
	self.okParam = okParam
	self:UpdateView()
end

-- 更新视图 
function UIShowBuyTip:UpdateView()	
	if not self.okParam then
		self.objList.Btn_Btn1.gameObject:SetActive(false)
	else
		self.objList.Btn_Btn1.gameObject:SetActive(true)
		self.objList.Txt_Btn1.text = self.okParam.btnName
		if self.okParam.icon then
			self.objList.Img_icon1.gameObject:SetActive(true)
		else
			self.objList.Img_icon1.gameObject:SetActive(false)
		end	
	end
end

function UIShowBuyTip.OnClickBtn1()
	local self = UIShowBuyTip
	self:CloseSelf()
	if self.okParam.callback then
		self.okParam.callback()
	end
end

function UIShowBuyTip.OnClickClose()
	local self = UIShowBuyTip
	self:CloseSelf()	
end

-- 窗口关闭
function UIShowBuyTip:OnClose()
end

-- 窗口销毁
function UIShowBuyTip:OnDestroy()
	-- 释放内存
	for i, btn in ipairs(self.btnList) do
		btn.onClick:RemoveAllListeners()
		btn.onClick:Invoke()
	end
end

return UIShowBuyTip