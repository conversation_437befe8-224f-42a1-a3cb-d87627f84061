--[[
********************************************************************
    created:	2024/09/13
    author :	高
    purpose:    活动副本
*********************************************************************
--]]


local luaID = 'UIForbiddenAreaEctype'

---每日副本（大陆禁地）
---@class UIForbiddenAreaEctype:UIWndBase
local m = {}

local ectypeType = ForbiddenArea_EctypeType

local maxStage = 0
--------------------------------------------------------------------
-- 订阅事件列表
--------------------------------------------------------------------
function m.GetOpenEventList()
    return {
        [EventID.LogicDataChange] = m.UpdateView,
        [EventID.EntitySyncBuff] = m.UpdateView,
        [EventID.OnGoodsPropChange] = m.UpdateView,
        [EventID.OnHeroPropChange] = m.UpdateView,
        [EventID.ActorDataChanged] = m.UpdateView,
    }
end

--------------------------------------------------------------------
-- 初始化
--------------------------------------------------------------------
function m.OnCreate()
    m.slotItemList = {}
    m.ItemList = {} 
    m.StageList = Schemes.CatMainStage:GetByFrontType(ectypeType)
    for i = 1, 5 do
        m.CreationItem(i, m.objList["Ectype"..i])
    end

    m.comResInfo = ComResInfo.New()
    m.comResInfo.BindView(m.objList.Img_Res)

    m.RegisterClickEvent()
    return true
end

--------------------------------------------------------------------
-- 窗口开启
--------------------------------------------------------------------
function m:OnOpen()
    m.UpdateView()
end

--------------------------------------------------------------------
-- 注册点击事件
--------------------------------------------------------------------
function m.RegisterClickEvent()
    m:AddClick(m.objList.Btn_Close, function()
        m:CloseSelf()
    end)

    m:AddClick(m.objList.Btn_Start, function()
        m.ChuZheng()
    end)
end

--------------------------------------------------------------------
-- 创建副本栏
--------------------------------------------------------------------
function m.CreationItem(index, view)
    ---@class Item_Ectype
    local item = {}
    item.index = index
    item.config = nil
    item.com = {}
    Helper.FillLuaComps(view.gameObject.transform, item.com)
    item.UpdateView = function(config,iPos)
        item.config = config
        item.com.Txt_Lv.text = config.Name
        print("config.Name =========== ", config.Name)
        if maxStage + 1 == iPos then 
            item.com.Img_Normal.gameObject:SetActive(true)
            item.com.Img_TG.gameObject:SetActive(false)
            item.com.Img_Gray.gameObject:SetActive(false)
        else
            if iPos <= maxStage or (maxStage == #m.StageList and iPos == #m.StageList) then 
                item.com.Img_Normal.gameObject:SetActive(false)
                item.com.Img_TG.gameObject:SetActive(true)
                item.com.Img_Gray.gameObject:SetActive(false)
                item.com.Txt_Lv.text = '<color=#727171>已通关！</color>'
            else
                item.com.Img_Normal.gameObject:SetActive(false)
                item.com.Img_TG.gameObject:SetActive(false)
                item.com.Img_Gray.gameObject:SetActive(true)
            end
        end
    end
    m.ItemList[index] = item
end

function m.OnClickItem(config)
    m.RefreshEctypeView(config)
end

function m.UpdateView()

    maxStage = GamePlayerData.GameEctype:GetProgress(ectypeType)

    if maxStage ~= 0 then
        maxStage = (maxStage) % 120000
        if maxStage >= #m.StageList then
            maxStage = #m.StageList
        end
    else
        maxStage = 0
    end
    local gapNum = math.floor(maxStage/5)
    local startPos = gapNum * 5 + 1
    local endPos = gapNum * 5 + 5
    
    if endPos > #m.StageList then
        endPos = #m.StageList
        startPos = endPos - 4
    end
    for i = startPos, endPos do
        local item = m.ItemList[i - startPos + 1]
        item.UpdateView(m.StageList[i],i)
        if maxStage + 1 == i or maxStage == #m.StageList then
            m.OnClickItem(item.config)
        end
    end

    if maxStage == #m.StageList then
        m.objList.Txt_TG.gameObject:SetActive(true)
        m.objList.Btn_Start.gameObject:SetActive(false)
        m.objList.Txt_Num.gameObject:SetActive(false)
    else
        m.objList.Txt_TG.gameObject:SetActive(false) 
        m.objList.Btn_Start.gameObject:SetActive(true)
        m.objList.Txt_Num.gameObject:SetActive(true)
    end
end

--------------------------------------------------------------------
--更新界面
--------------------------------------------------------------------
function m.RefreshEctypeView(config)
    m.StageCfg = config
    m.objList.Txt_Title.text = m.StageCfg.Desc
    local list = HelperL.Split( m.StageCfg.Desc2,"\\n")
    str = ""
    for i = 1, #list do
        if i < #list then
            str = str..list[i].."\n"
        else
            str =  str..list[i]
        end
    end
    m.objList.Txt_Desc2.text = str
    AtlasManager:AsyncGetSprite(iconID, m.objList.Img_Icon)
    local expendList = HelperL.Split(m.StageCfg.Need, ';')
    --消耗物品ID
    local expID = tonumber(expendList[1]) or 0
    --消耗物品数量
    local expNum = tonumber(expendList[2]) or 0
    --广告物品ID
    -- local adID = tonumber(expendList[3]) or 0
    --消耗物品ID2
    local expID2 = tonumber(expendList[4]) or 0
    --消耗物品数量2
    local expNum2 = tonumber(expendList[5]) or 0

    local count1 = SkepModule:GetGoodsCount(expID) or 0
    local count2 = SkepModule:GetGoodsCount(expID2) or 0

    --挑战次数
    local challengeNum = math.floor(count1 / expNum)
    m.objList.Txt_Num.text = string.format(GetGameText(luaID, 1), challengeNum)
    AtlasManager:AsyncGetGoodsSprite(expID2, m.objList.Img_EX)
    local color = count2 >= expNum2 and UI_COLOR.White or UI_COLOR.Red
    m.objList.Txt_EX.text = string.format("<color=%s>%s/%s</color>", color, count2, expNum2)

    local level1 = EntityModule.hero:GetProperty(CREATURE_FIELD.CREATURE_FIELD_LEVEL)
    local level2 = EctypeUnlockLevel[ectypeType] or 0
    if level1 < level2 then
       -- m.objList.Txt_Hint.text = string.format(GetGameText(luaID, 2), level2)
        m.objList.Btn_Start.gameObject:SetActive(false)
    else
        m.objList.Btn_Start.gameObject:SetActive(true)
    end
    m.objList.Img_RedDot.gameObject:SetActive(RedDotCheckFunc.Check_UIForbiddenAreaEctype(ectypeType))

    if m.StageCfg.ID ~= m.lastStageID then
        m.lastStageID = m.StageCfg.ID
        local prizeGoods = Schemes.CatMainStage:GetSucceedRewards(m.StageCfg.ID)
        local num = math.max(#prizeGoods, #m.slotItemList)
        for i = 1, num, 1 do
            if not m.slotItemList[i] then
                m.slotItemList[i] = _GAddSlotItem(m.objList.Grid_Goods)
            end
            if prizeGoods[i] then
                m.slotItemList[i]:SetItemID(prizeGoods[i].ID)
                m.slotItemList[i]:SetCount(prizeGoods[i].Num)
                m.slotItemList[i]:SetActive(true)
            else
                m.slotItemList[i]:SetActive(false)
            end
        end
    end
end

--------------------------------------------------------------------
-- 出征
--------------------------------------------------------------------
function m.ChuZheng()
    local stageID = GamePlayerData.GameEctype:GetChallengeableID(ectypeType)
    local cfg = Schemes.CatMainStage:Get(stageID)
    if not cfg then
        error('读取 CatMainStage 表失败 stageID=' .. stageID)
        return
    end

    local expendList = HelperL.Split(cfg.Need, ';')
    --消耗物品ID
    local expID = tonumber(expendList[1]) or 0
    --消耗物品数量
    local expNum = tonumber(expendList[2]) or 0
    --广告物品ID
    -- local adID = tonumber(expendList[3])
    --消耗物品ID2
    local expID2 = tonumber(expendList[4]) or 0
    --消耗物品数量2
    local expNum2 = tonumber(expendList[5]) or 0

    --判断挑战次数
    if HelperL.IsLackGoods(expID, expNum, false, false) then
        HelperL.ShowMessage(TipType.FlowText, GetGameText(luaID, 7))
        return
    end
    --消耗判断
    if HelperL.IsLackGoods(expID2, expNum2, false) then
        return
    end

    --m:CloseSelf()
    -- -- 进入战斗
    BattleManager:EnterBattle(cfg.ID)
end

return m
