GuideManager = {}

local self = GuideManager

GuideManager.EventType =
{
	AirPlaneGuide            = 1, --升级细胞的指引
	UpgradeEquipmentGuide    = 2, --升级装备的指引
	ChangeSideKick           = 3, --更换宠物的指引
	BeginGameGuide           = 4, --首次登陆指引
	PropUpgradeGuide         = 5, --细胞属性强化
	EquipOpenBoxGuide        = 6, --装备开箱子
	GiftBagGuide             = 7, --礼包-章节礼包
	PatrolGuide              = 8, --巡逻
	TaskCompleteGuide        = 9, --任务完成
	EquipUpStarGuide         = 10, --装备-升级
	PartnerGuide             = 11, --伙伴
	EquipSophisticationGuide = 12, --装备-突变
	GiftBoxGuide             = 13, --礼包-宝箱
	DestinyGuide             = 14, --菌落
	TipBestEquipment         = 15, --新增高级装备直营
	EctypeGuide              = 16, --副本
}

--引导是否正在进行
GuideManager.isRunning = false --是否在引导中
GuideManager.isLockScreen = false
GuideManager.currentGuideType = 0
GuideManager.currentGuideStep = 0
GuideManager.curGuideAllStepData = {}

--旧版代码写死步骤引导（废弃）
function GuideManager.StartGuide(type, step)
	--[[
	local self = GuideManager
	self.currentGuideType = type
	self.currentGuideStep = step
	print("引导的类型" .. tostring(type))
	print("引导的步骤" .. tostring(step))
	self.isRunning = true
	if UIManager:IsWndOpen(WndID.GuideView) then
		UIManager:CloseWndByID(WndID.GuideView)
	end
	UIManager:OpenWnd(WndID.GuideView, type, step)
--]]
end

function GuideManager:EndGuide()
	self.isRunning = false
	--PlayerPrefsManager:SetInt("newGuide" .. self.currentGuideType, 1)
end

function GuideManager:FaildGuide()
	self.isRunning = false
	-- local count = PlayerPrefsManager:GetInt("type" .. self.currentGuideType)
	-- if count == 2 then
	-- 	PlayerPrefsManager:SetInt("newGuide" .. self.currentGuideType, 1)
	-- else
	-- 	count = count + 1
	-- 	PlayerPrefsManager:SetInt("type" .. self.currentGuideType, count)
	-- end
end

function GuideManager:ReConnect()
	self.isRunning = false
	if UIManager:IsWndOpen(WndID.GuideView) then
		UIManager:CloseWndByID(WndID.GuideView)
	end
end

----------------------------
--强制引导
----------------------------

--引导步数
function GuideManager:EndStep(currentGuideType, step)
	self.isRunning = false
	PlayerPrefsManager:SetInt("newGuide" .. currentGuideType, step)
end

function GuideManager:GetStep(currentGuideType)
	return PlayerPrefsManager:GetInt("newGuide" .. currentGuideType)
end

--引导结果
function GuideManager:SetComplete(currentGuideType)
	local actorID = EntityModule.hero:GetProperty(ENTITY_FIELD.ENTITY_FIELD_ID)
	self.isRunning = false
	PlayerPrefsManager:SetInt(actorID .. "Result" .. currentGuideType, 1)
end

function GuideManager:SetFaild(currentGuideType)
	local actorID = EntityModule.hero:GetProperty(ENTITY_FIELD.ENTITY_FIELD_ID)
	PlayerPrefsManager:SetInt(actorID .. "Result" .. currentGuideType, 0)
end

function GuideManager:IsComplete(currentGuideType)
	local actorID = EntityModule.hero:GetProperty(ENTITY_FIELD.ENTITY_FIELD_ID)
	if PlayerPrefsManager:GetInt(actorID .. "Result" .. currentGuideType) == 1 then
		return true
	else
		return false
	end
end

--引导次数
function GuideManager:SetCount(currentGuideType, count)
	self.isRunning = false
	PlayerPrefsManager:SetInt("GuideCount" .. currentGuideType, count)
end

function GuideManager:GetCount(currentGuideType)
	return PlayerPrefsManager:GetInt("GuideCount" .. currentGuideType)
end

----------------------------------------------------------------------新版走配置表引导--------------------------------------------

GuideManager.Runing = false;
--引导类型
function GuideManager:SetCurGuideType()
	local actorID = EntityModule.hero:GetProperty(ENTITY_FIELD.ENTITY_FIELD_ID)
	PlayerPrefsManager:SetInt("CurGuideType" .. actorID, self.currentGuideType)
end

function GuideManager:GetCurGuideType()
	local actorID = EntityModule.hero:GetProperty(ENTITY_FIELD.ENTITY_FIELD_ID)
	return PlayerPrefs.GetInt("CurGuideType" .. actorID, 0)
end

function GuideManager:OnGuideStart(guideType)
	print('------guideType--------',guideType, debug.traceback())
	print("引导的类型GGG" .. tostring(guideType) .. "/" .. GamePlayerData.GameEctype:GetChallengeableID(1))
	if (guideType == 13 and GamePlayerData.GameEctype:GetChallengeableID(1) < 2) then return end
	if (guideType == 14 and GamePlayerData.GameEctype:GetChallengeableID(1) ~= 3) then return end
	if (guideType == 15 and GamePlayerData.GameEctype:GetChallengeableID(1) ~= 4) then return end
	if (guideType == 16 and GamePlayerData.GameEctype:GetChallengeableID(1) ~= 7) then return end
	if self.Runing then return end
	self.currentGuideType = guideType
	self.currentGuideStep = 1
	self.Runing = true
	self:SetCurGuideType()
	self:SetFaild(guideType)
	print('------guideType--11111------',guideType)

	self.curGuideAllStepData = {}
	for k, v in ipairs(Schemes.Guide.items) do
		if v.ID == guideType then
			local guideStepData = {}
			guideStepData.wndId = v.WindowID
			guideStepData.FocusOn = v.FocusOn
			guideStepData.ClickFocus = v.ClickFocus
			guideStepData.TipsContent = v.TipsContent
			guideStepData.HaveScript = v.HaveScript
			guideStepData.DelayTime = v.DelayTime
			guideStepData.LeftHand = v.LeftHand
			guideStepData.Offset = v.Offset
			table.insert(self.curGuideAllStepData, guideStepData)
		end
	end
	if #self.curGuideAllStepData == 0 then
		warn("Guide配置表没有引导类型id", guideType)
		return
	end
	local step = 1
	warn("时间加速", UnityEngine.Time.timeScale)
	print("引导的类型" .. tostring(guideType), "步骤数", #self.curGuideAllStepData)
	print("引导的步骤" .. tostring(step), self.curGuideAllStepData[step].FocusOn)
	if UIManager:IsWndOpen(WndID.GuideView) then
		UIManager:CloseWndByID(WndID.GuideView)
	end
	UIManager:OpenWnd2(WndID.GuideView, nil, false, guideType, step)
end

--ui面板打开时检测是否有正在进行的引导需要触发
function GuideManager:OnWndOpenCheckGuide(wndId)
	if EntityModule.hero == nil or self.Runing or wndId == WndID.Login or wndId == WndID.OverlayWaiting or wndId == WndID.TipManager or wndId == WndID.RuleTip then
		return
	end
	local curGuideType = self:GetCurGuideType()
	print(curGuideType .. "当前引导完成GGG", self:IsComplete(curGuideType))
	if not self:IsComplete(curGuideType) then
		for k, v in ipairs(Schemes.Guide.items) do
			if v.ID == curGuideType and v.WindowID == wndId then
				self:OnGuideStart(curGuideType)
				return
			end
		end
	end

	--------------打开UI触发引导id写死--------------------
	-- if not self:IsComplete(13) and GamePlayerData.GameEctype:GetChallengeableID(1) == 2 then
	-- 	self:OnGuideStart(13)
	-- elseif not self:IsComplete(14) and GamePlayerData.GameEctype:GetChallengeableID(1) == 3 then
	-- 	self:OnGuideStart(14)
	-- elseif not self:IsComplete(15) and GamePlayerData.GameEctype:GetChallengeableID(1) == 4 then
	-- 	self:OnGuideStart(15)
	-- elseif not self:IsComplete(16) and GamePlayerData.GameEctype:GetChallengeableID(1) == 7 then
	-- 	self:OnGuideStart(16)
	-- end
end
