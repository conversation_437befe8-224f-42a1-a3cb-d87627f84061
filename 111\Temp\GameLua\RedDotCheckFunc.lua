--[[
********************************************************************
    created:    2023/09/19
    author :    李锦剑
    purpose:    存放红点检测函数--函数放这里代码整洁一些
*********************************************************************
--]]

---@class RedDotCheckFunc
local m = {}

RedDotCheckFunc = m
--储存窗口是否开启过，只记录一次
m.WindowOpen = {}

--检测七日投资红点
function m:CheckSevenDaysInvest(id)
    local curTime = HelperL.GetServerTime()
    local judge = function(cfg)
        if cfg and cfg.Type == 1 then
            local investTimes = HeroDataManager:GetLogicData(cfg.TimeSaveID) -- 七日投资时间
            local curDay = HelperL.CalculationIntervalDays(investTimes, curTime)
            if curDay > 7 then curDay = 7 end
            if investTimes > 0 then
                if HeroDataManager:GetLogicBit(cfg.GetSaveID, cfg.StartIndex + (curDay - 1)) == 0 then --可领取
                    return true
                end
            else
                if id == 1 and investTimes == 0 then
                    return true
                end
            end
        end
        return false
    end

    if id then
        local config = Schemes.SevenDaysInvest:Get(id)
        if judge(config) then
            return true
        end
    else
        for k, v in ipairs(Schemes.SevenDaysInvest.items) do
            if judge(v) then
                return true
            end
        end
    end
    return false
end

--检测七日签到
function m:SignInRedDot(ID)
    ---单个检测
    ---@param cfg PrizesevenSigninCfg
    ---@return boolean
    local judge = function(cfg)
        if not cfg then
            return false
        end
        if cfg.PrizeType == 1 then
            local time = HelperL.GetServerTime()

            --local signInNumber = HeroDataManager:GetLogicData(LOGIC_DATA.DATA_WEEK_SIGNIN_BITS)

            local signInTime = HeroDataManager:GetLogicData(LOGIC_DATA.DATA_HORSE_HAVEGET)
            --签到时间
            --local signInTime = GamePlayerData.ActorActivity:GetSignInData(SignInData_Index.SignInTime)
            --判断是否已签到
            if not HelperL.is_same_day(time, signInTime) then
                return true
            end
        elseif cfg.PrizeType == 2 then
            local signInCumulativeNumber = GamePlayerData.ActorActivity:GetSignInData(
                SignInData_Index.SignInCumulativeNumber)
            if GamePlayerData.LogicValue:GetIntByIndex(cfg.ActorDataCatalog, cfg.Sort) == 0 then
                if signInCumulativeNumber >= cfg.ReceivePremissParam then
                    return true
                end
            end
        end
        return false
    end

    if ID then
        return judge(Schemes.PrizesevenSignin:Get(ID))
    else
        --签到数据
        local signinDataList1 = Schemes.PrizesevenSignin:GetByPrizeType(1)
        if signinDataList1 then
            if judge(signinDataList1[1]) then
                return true
            end
        end

        --累计签到数据
        -- local signinDataList2 = Schemes.PrizesevenSignin:GetByPrizeType(2)
        -- if signinDataList2 then
        --     for i, v in ipairs(signinDataList2) do
        --         if judge(v) then
        --             return true
        --         end
        --     end
        -- end
    end
    return false
end

--检测免广告
function m:ADFreeRedDot()
    local isADFree = HelperL.IsNoAD()
    --判断界面是否开启过
    if not isADFree and m.WindowOpen[WndID.ADFree] ~= true then
        return true
    end

    return false
end

--检测邮件
function m:EmailRedDot()
    local dataList = EmailModule.GetEmailList();
    for _, v in pairs(dataList) do
        if v.MarkRead == 0 or (v.MarkRead ~= 2 and v.HasAdjunct) then
            return true;
        end
    end
    return false;
end

---检测月卡
---@param id integer
---@return boolean
function m:CheckMonthCard(id)
    local judge = function(_id)
        if HelperL.IsBuyPrivilegeCard(_id) then
            local lv = HeroDataManager:GetLogicBit(LOGIC_DATA.DATA_VIP_GET_PERDAY_GIFT, 16 + _id)
            --月卡已激活，有奖励可领取
            if lv == 0 then
                return true
            end
        end
        return false
    end

    if id then
        if judge(id) then
            return true
        end
    else
        for i = 1, 2, 1 do
            if judge(i) then
                return true
            end
        end
    end
    return false
end

--检测邀请界面
function m:CheckInvitation(id)
    local isBuy1 = HelperL.HadBoughtCardID(78)
    local isBuy2 = HelperL.HadBoughtCardID(79)
    if not isBuy1 or not isBuy2 then
        --判断界面是否开启过
        if m.WindowOpen[WndID.Invitation] ~= true then
            return true
        end
    end
    if id and not HelperL.HadBoughtCardID(id) then
        return true
    end
    return false
end

--检测福利红点
function m:CheckWelfare(cardID)
    if not m.welfareDataList then
        m.welfareDataList = {}
        for k, v in ipairs(Schemes.RechargeCard.items) do
            if v.LenovoID == RECHARGE_TYPE.RECHARGE_CARD then
                table.insert(m.welfareDataList, v)
            end
        end
    end
    --单个活动红点检测
    local judge = function(cfg)
        if cfg and cfg.Description ~= '0' then
            local state = HelperL.GetAdverState(cfg.Description)
            if state == 1 then
                return true
            end
        end
        return false
    end

    if cardID then
        local rechargeCard = Schemes.RechargeCard:Get(cardID)
        if judge(rechargeCard) then
            return true
        end
    else
        for i, v in ipairs(m.welfareDataList) do
            if judge(v) then
                return true
            end
        end
    end
    return false
end

--检测超值礼包
function m:CheckFirstCharge()
    local state = HelperL.GetAdverStateByCardID(20)
    local isBuyCard = HelperL.HadBoughtCardID(20)
    if not isBuyCard and state == 1 then
        return false
    end
    return false
end

--检测超级充值
function m:CheckChaoJiChongZhi()
    local state = HelperL.GetAdverStateByCardID(20)
    local isBuyCard = HelperL.HadBoughtCardID(20)
    if not isBuyCard and state == 1 then
        return false
    end
    return false
end

--特惠礼包红点
function m:CheckDiscountGift(actvID, cardID)
    -- --获取活动数据
    -- if not m.discountGiftData then
    --     m.discountGiftData = {}
    --     local actvIDList = {
    --         [ACTVID.ACTVID_RECHARGEBUY_1] = true,
    --         [ACTVID.ACTVID_RECHARGEBUY_2] = true,
    --         [ACTVID.ACTVID_RECHARGEBUY_3] = true,
    --     }
    --     for k, v in ipairs(Schemes.RechargeCard.items) do
    --         if actvIDList[v.NeedActvID] then
    --             if not m.discountGiftData[v.NeedActvID] then
    --                 m.discountGiftData[v.NeedActvID] = {}
    --             end
    --             table.insert(m.discountGiftData[v.NeedActvID], v)
    --         end
    --     end
    -- end

    -- --单个活动红点检测
    -- local judge = function(dataList, cardID2)
    --     if dataList then
    --         local state
    --         for i, v in ipairs(dataList) do
    --             state = HelperL.GetAdverState(v.Description)
    --             if state == 1 then
    --                 return true
    --             end
    --             --单个充值卡红点检测
    --             if cardID2 == v.ID then
    --                 return false
    --             end
    --         end
    --     end
    --     return false
    -- end

    -- --指定活动红点检测
    -- if actvID then
    --     if judge(m.discountGiftData[actvID], cardID) then
    --         return true
    --     end
    -- else
    --     for _, v in pairs(m.discountGiftData) do
    --         if judge(v) then
    --             return true
    --         end
    --     end
    -- end
    return false
end

--章节礼包红点
function m:CheckStageReward(id)
    --判断界面是否开启过
    if m.WindowOpen[WndID.StageReward] ~= true then
        return true
    end
    --获取通关主线副本ID
    local curMainStage = HeroDataManager:GetLogicData(LOGIC_DATA.DATA_SOCIETY_ECTYPETOWER_CURSTAGE)
    if curMainStage > 0 then
        --单个副本红点检测
        local judge = function(cardID)
            local data = Schemes.RechargeCard:Get(cardID)
            if data.Description ~= '0' then
                local state = HelperL.GetAdverState(data.Description)
                if state == 1 then
                    return true
                end
            end
            return false
        end
        local Recharge_ID = 79
        if id then
            return (id <= 40) and judge(Recharge_ID + id)
        else
            for i = 1, curMainStage do
                if curMainStage > 40 then
                    break
                end
                if judge(Recharge_ID + i) then
                    return true
                end
            end
        end
    end
    return false
end

--装备--宝箱红点
function m.CheckEquipBox()
    --[[
    local haveDiamond = EntityModule.hero:GetProperty(PLAYER_FIELD.PLAYER_FIELD_DIAMOND)
    local onePrice = Schemes.ConstValue:Get(CONST_VALUE.CONST_LOTTERY_COSTDIAMOND)
    local lotteryLevel = HeroDataManager:GetLogicByte(LOGIC_DATA.DATA_SOCIETY_ECTYPETOWER_HIGHSTAGE, 1)
    lotteryLevel = lotteryLevel <= 0 and 1 or lotteryLevel
    local lotteryItem = Schemes.LotteryLevel:Get(lotteryLevel + 1100)
    local descs = HelperL.Split(lotteryItem.Desc, ';')
    local add = tonumber(descs[2]) or 1
    if haveDiamond >= math.floor(onePrice * add) then
        return true
    end
    return false
    --]]

    --检查背包是否有装备
    local skepBag = SkepModule:GetSkepByID(SKEPID.SKEPID_PACKET)
    if not skepBag then
        return false
    end
    if skepBag then
        -- 背包里的数据
        for bagIndex = 0, skepBag.skepMaxsize do
            local eUID = skepBag[bagIndex]
            if eUID then
                local entity = EntityModule:GetEntity(eUID)
                if entity then
                    local goodsID = entity:GetProperty(ENTITY_FIELD.ENTITY_FIELD_ID)
                    if goodsID > DEFINE.MAX_MEDICAMENT_ID then
                        return true
                    end
                end
            end
        end
    end
    return false
end

--装备--突变红点
function m.CheckEquipForging()
    --获取装备背包栏子
    local skepEuip = SkepModule.GetEquipSkep()
    if skepEuip then
        local entity, list, cfg, equipPolish
        for _, v in ipairs(EquipOrder) do
            entity = EntityModule:GetEntity(skepEuip[v])
            if entity then
                list = ActorProp.GetEquipAttributeList(entity)
                if list then
                    cfg = Schemes.EquipEffect:Get(list[1][1].EffectID)
                    if cfg then
                        equipPolish = Schemes.EquipPolish:Get(cfg.GgoodsPolish)
                        if equipPolish then
                            if SkepModule:GetGoodsCount(9) >= equipPolish.Money then
                                return true
                            end
                        end
                    end
                end
            end
        end
    end
    return false
end

--装备--升级红点
function m.CheckEquipStarNumber()
    --获取装备背包栏子
    local skepEuip = SkepModule.GetEquipSkep()
    if skepEuip then
        local entity, starNum, cfg, moneyAll
        for _, v in ipairs(EquipOrder) do
            entity = EntityModule:GetEntity(skepEuip[v])
            if entity then
                cfg = Schemes:GetGoodsConfig(entity:GetProperty(ENTITY_FIELD.ENTITY_FIELD_ID))
                if cfg then
                    starNum = entity:GetProperty(EQUIPMENT_FIELD.EQUIPMENT_FIELD_STARNUM)
                    cfg = Schemes.EquipSmelt:Get(cfg.SmeltID, cfg.Quality, starNum)
                    if cfg then
                        moneyAll = EntityModule.hero:GetProperty(PLAYER_FIELD.PLAYER_FIELD_MORAL)
                        if moneyAll >= cfg.Quality1 then
                            return true
                        end
                    end
                end
            end
        end
    end
    return false
end

m.quickUpgradeScript = require("QuickUpgrade")
--检测快速升级红点
function m.CheckQuickUpgrade()
    local haveExp = false
    local Num = 0
    local heroLevel = EntityModule.hero.propertyLC:GetProperty(CREATURE_FIELD.CREATURE_FIELD_LEVEL)
    local levelScheme = Schemes.PlayerBaseProp.Get(heroLevel)
    local maxLimit = 0
    local getExp = HeroDataManager:GetLogicData(LOGIC_DATA.DATA_EXPWELFARE_GET)
    local noGetExp = HeroDataManager:GetLogicData(LOGIC_DATA.DATA_EXPWELFARE_NOGET)
    if levelScheme ~= nil then
        local serverOpenDays = HelperL.GetServerOpenDays()
        local nIndex = 1
        if levelScheme.ServerOpenDay then
            for i = 1, #levelScheme.ServerOpenDay do
                if serverOpenDays >= levelScheme.ServerOpenDay[i] then
                    nIndex = i
                else
                    break
                end
            end
        end
        maxLimit = math.min((levelScheme.PerDayGiveMaxExp[nIndex] + noGetExp), levelScheme.PerDayGiveMaxExp[nIndex] * 5)
        --算出进度条
        Num = maxLimit - getExp
        if Num <= 0 then
            haveExp = false
        else
            haveExp = true
        end
    end
    local redspot = m.quickUpgradeScript:CheckRedSpot()
    if redspot or haveExp then
        return true
    end
    return false
end

--巡逻红点
function m:CatPatrolRed(type)
    --体力物品ID
    local PhysicalPowerGoodsID = 2
    -- 117消耗体力快速巡逻
    local adID1 = 117
    -- 123看广告，同时消耗体力，快速巡逻
    -- local adID2 = 123
    local maxStage = GamePlayerData.GameEctype:GetProgress(1)
    if maxStage <= 0 then
        maxStage = 1
    end
    local patrolConfig
    for _, v in ipairs(Schemes.PrizePatrol.items) do
        if v and v.RecePram2 == maxStage then
            patrolConfig = v
            break
        end
    end
    if not patrolConfig then
        patrolConfig = Schemes.PrizePatrol:Get(1)
    end

    if type ~= 2 then
        local state1 = HelperL.GetAdverState(adID1)
        if state1 == 1 then
            local expendNum = tonumber(patrolConfig.PowerConsum) or 0
            if not HelperL.IsLackGoods(PhysicalPowerGoodsID, expendNum, false, false) then
                return true
            end
        end
    end

    -- if type ~= 1 then
    --     local state2 = HelperL.GetAdverState(adID2)
    --     if state2 == 1 then
    --         local expendNum = tonumber(patrolConfig.PowerConsum) or 0
    --         if not HelperL.IsLackGoods(PhysicalPowerGoodsID, expendNum, false, false) then
    --             return true
    --         end
    --     end
    -- end
    return false
end

--检测--单个副本--红点
function m:FuBenRed(frontType)
    local stageList = Schemes.CatMainStage:GetByFrontType(frontType)
    if stageList then
        local maxStage = HeroDataManager:GetLogicData(TEctypeToLogicValue[frontType])
        if maxStage ~= 0 then
            maxStage = (maxStage + 1) % 100
            if maxStage >= #stageList then
                maxStage = #stageList
            end
        else
            maxStage = 1
        end
        local expendList = HelperL.Split(stageList[maxStage].Need, ';')
        local count1 = SkepModule:GetGoodsCount(tonumber(expendList[1]))
        local count2 = SkepModule:GetGoodsCount(tonumber(expendList[3]))
        local num = tonumber(expendList[2])
        local level1 = EntityModule.hero:GetProperty(CREATURE_FIELD.CREATURE_FIELD_LEVEL)
        local level2 = EctypeUnlockLevel[frontType] or 0
        if level1 >= level2 and count1 >= num and count2 >= num then
            return true
        end
    end
    return false
end

--检测--生存排行--红点
function m:CheckSurvivalRankingList()
    local dataList = Schemes.CommonText:GetByTextType(4)
    --根据奖励类型分类
    local rankingDataList = {}
    for i, v in ipairs(dataList) do
        if not rankingDataList[v.PrizeType] then
            rankingDataList[v.PrizeType] = {}
        end
        table.insert(rankingDataList[v.PrizeType], v)
    end

    local commonText = rankingDataList[1][1]
    --判断总次数
    local num1 = HeroDataManager:GetLogicByte(commonText.Param2, commonText.Param3)
    if commonText.ToTime ~= 0 and num1 >= commonText.ToTime then
        return false
    end
    --判断今日次数
    local num2 = HeroDataManager:GetLogicByte(commonText.Param4, commonText.Param5)
    if num2 >= commonText.DayTime then
        return false
    end
    local killsNum = HeroDataManager:GetLogicData(LOGIC_DATA.DATA_ECTYPESEVEN_MAXKILLNUM)
    local data = RankingModule.GetRankData(RANK_TYPE.RANK_TYPE_CATDAYKILLENIMY)
    if not data then
        RankingModule.RequestRankList(RANK_TYPE.RANK_TYPE_CATDAYKILLENIMY)
        return false
    end
    if killsNum > 10000 and data.SelfRank and data.SelfRank > 0 then
        local list = rankingDataList[1]
        for i, v in ipairs(list) do
            if data.SelfRank >= v.RankMin and data.SelfRank <= v.RankMax then
                return true
            end
        end
    else
        local list = rankingDataList[2]
        for i, v in ipairs(list) do
            if killsNum >= v.RankMin and killsNum <= v.RankMax then
                return true
            end
        end
    end
    return false
end

--检测--生存--红点
function m:CheckSurvivalEctype()
    --生存副本红点
    if m:FuBenRed(7) then
        return true
    end
    --排行榜红点
    if m:CheckSurvivalRankingList() then
        return true
    end
    return false
end

--每日副本红点
function m:CheckGameEctype()
    for frontType = 5, 14, 1 do
        if m:FuBenRed(frontType) then
            return true
        end
    end

    if not SWITCH.GAME_ECTYPE_RANKING_CLOSE then
        --排行榜红点
        if m:CheckSurvivalRankingList() then
            return true
        end
    end

    return false
end

--检测菌落红点
function m:CheckFate()
    local money = EntityModule.hero:GetProperty(PLAYER_FIELD.PLAYER_FIELD_PRESTIGE)
    local cfg = Schemes.GemCreate:Get(1)
    if money >= cfg.CostSilver then
        return true
    end
    return false
end

--检测细胞红点-属性红点
function m:CheckRole()
    local cardSkep = SkepModule:GetSkepByID(SKEPID.SKEPID_MONSTERCARD)
    local entity, bool, smeltScheme
    for i, v in ipairs(EquipConfigIdList) do
        entity = cardSkep:GetEntityByGoodsID(v.equipID)
        if entity then
            if i > 1 then
                --判断解锁条件
                entity = cardSkep:GetEntityByGoodsID(EquipConfigIdList[i - 1].equipID)
                bool = math.floor(entity:GetProperty(EQUIPMENT_FIELD.EQUIPMENT_FIELD_SMELTEXP) / 100) >=
                    v.unlockConditions
            else
                bool = true
            end
            if bool then
                smeltScheme = Schemes.EquipSmelt:Get(
                    Schemes.Equipment:Get(v.equipID).SmeltID,
                    entity:GetProperty(EQUIPMENT_FIELD.EQUIPMENT_FIELD_QUALITY),
                    entity:GetProperty(EQUIPMENT_FIELD.EQUIPMENT_FIELD_STARNUM)
                )
                if smeltScheme then
                    if not (smeltScheme.NextStarNum == 0 and smeltScheme.NextQuality == 0) then -- 未满级
                        if m:CheckRoleShuXingQiangHua(smeltScheme.CostMoney) then
                            return true
                        end
                    end
                end
            end
        end
    end
    return false
end

--检测细胞-细胞-强化红点
function m:CheckRoleShuXingQiangHua(needMoney)
    local hasMoney = SkepModule:GetGoodsCount(4)
    if hasMoney >= needMoney then
        return true
    end
    return false
end

--检测细胞-细胞红点
function m:CheckRoleRole()
    local skepAir = SkepModule:GetSkepByID(SKEPID.SKEPID_GODWEAPON)
    local createBtnList = {}
    local airEntity
    for index, weaponItem in ipairs(Schemes.EquipWeapon.items) do
        -- 默认取第一个未激活细胞装备信息
        if not createBtnList[weaponItem.GroupID] then
            createBtnList[weaponItem.GroupID] = weaponItem
        end
        -- 看看背包篮子里有没有
        airEntity = skepAir:GetEntityByGoodsID(weaponItem.ID)
        if airEntity then
            -- 同一组同时拥有多个，就一直覆盖下去，最后就是最高级的
            createBtnList[weaponItem.GroupID] = weaponItem -- 当前组已找到激活的细胞装备
        end
    end
    for index, v in ipairs(createBtnList) do
        if m:CheckRoleRoleJiHuoOrJinJie(v) then
            return true
        end
    end
    return false
end

--检测细胞--激活、进阶、吞噬、看广告--红点
function m:CheckRoleRoleJiHuoOrJinJie(v)
    if m:CheckRoleRoleJiHuo(v) then
        return true
    end
    if m:CheckRoleRoleJinJie(v) then
        return true
    end
    if m:CheckRoleRoleCytophagy(v) then
        return true
    end
    if m:CheckRoleRoleAdJiHuo(v) then
        return true
    end
    return false
end

--检测细胞-细胞-激活红点
function m:CheckRoleRoleJiHuo(v)
    local skepAir = SkepModule:GetSkepByID(SKEPID.SKEPID_GODWEAPON)
    local airEntity = skepAir:GetEntityByGoodsID(v.ID)
    if airEntity == nil then
        local skepBag = SkepModule:GetSkepByID(SKEPID.SKEPID_PACKET)
        local goodsNum = skepBag:FastCount(v.Active1Param1)
        if goodsNum >= v.Active1Param2 then
            --可激活
            return true
        end
    end
    return false
end

--检测细胞-细胞-进阶-红点
function m:CheckRoleRoleJinJie(v)
    local skepAir = SkepModule:GetSkepByID(SKEPID.SKEPID_GODWEAPON)
    local airEntity = skepAir:GetEntityByGoodsID(v.ID)
    if airEntity then
        -- 已激活
        local equipentItem = Schemes.Equipment:Get(v.ID)
        local quality = airEntity:GetProperty(EQUIPMENT_FIELD.EQUIPMENT_FIELD_QUALITY)
        local starNum = airEntity:GetProperty(EQUIPMENT_FIELD.EQUIPMENT_FIELD_STARNUM)
        local smeltScheme = Schemes.EquipSmelt:Get(equipentItem.SmeltID, quality, starNum)
        if smeltScheme then
            local goods_id = smeltScheme.GoodsID
            local cost_goodsnum = smeltScheme.GoodsNum
            local goodsNum = SkepModule:GetGoodsCount(goods_id)

            local goods_id2 = 4
            local cost_goodsnum2 = smeltScheme.CostMoney
            local goodsNum2 = SkepModule:GetGoodsCount(goods_id2)
            if goodsNum >= cost_goodsnum and goodsNum2 >= cost_goodsnum2 then
                return true
            end
        end
    end
    return false
end

--检测细胞-细胞-吞噬-红点
function m:CheckRoleRoleCytophagy(v)
    local cfg = Schemes.Equipment:Get(v.EffectID1)
    local quality = cfg.Quality
    local starNum = cfg.StarNum
    local entity = SkepModule:GetSkepByID(SKEPID.SKEPID_MONSTERCARD):GetEntityByGoodsID(v.EffectID1)
    if entity then
        quality = entity:GetProperty(EQUIPMENT_FIELD.EQUIPMENT_FIELD_QUALITY)
        starNum = entity:GetProperty(EQUIPMENT_FIELD.EQUIPMENT_FIELD_STARNUM)
    end
    local smeltScheme = Schemes.EquipSmelt:Get(cfg.SmeltID, quality, starNum)
    if smeltScheme and smeltScheme.LevelMaxExp ~= -1 then
        if SkepModule:GetGoodsCount(smeltScheme.GoodsID) >= smeltScheme.GoodsNum then
            return true
        end
    end
    return false
end

--检测细胞-细胞-看广告获取激活材料
function m:CheckRoleRoleAdJiHuo(v)
    local skepAir = SkepModule:GetSkepByID(SKEPID.SKEPID_GODWEAPON)
    local airEntity = skepAir:GetEntityByGoodsID(v.ID)
    if airEntity == nil then
        local skepBag = SkepModule:GetSkepByID(SKEPID.SKEPID_PACKET)
        local goodsNum = skepBag:FastCount(v.Active1Param1)
        if goodsNum < v.Active1Param2 then
            local level = EntityModule.hero:GetProperty(CREATURE_FIELD.CREATURE_FIELD_LEVEL)
            local cfg = Schemes.Medicament:Get(v.Active1Param1)
            if level >= cfg.Grade then
                local cardID = HelperL.GetGoodsIDByCardID(v.Active1Param1, function(id)
                    local state = HelperL.GetAdverStateByCardID(id)
                    return state == 1 or state == 2
                end)
                return HelperL.GetAdverStateByCardID(cardID) == 1
            end
        end
    end
    return false
end

--检测伙伴红点
function m:Check_UIPetShopNew(ID)
    --单个红点检测
    local judge = function(v)
        local entity = SkepModule:GetSkepByID(SKEPID.SKEPID_MOUNT):GetEntityByGoodsID(v.ID)
        if entity then
            local quality = entity:GetProperty(EQUIPMENT_FIELD.EQUIPMENT_FIELD_QUALITY)
            local starNum = entity:GetProperty(EQUIPMENT_FIELD.EQUIPMENT_FIELD_STARNUM)
            local equipCfg = Schemes.Equipment:Get(v.ID)
            local smeltScheme = Schemes.EquipSmelt:Get(equipCfg.SmeltID, quality, starNum)
            if smeltScheme and smeltScheme.LevelMaxExp ~= -1 then
                local goodsNum1 = SkepModule:GetGoodsCount(smeltScheme.GoodsID)
                local goodsNum2 = SkepModule:GetGoodsCount(4)
                if goodsNum1 >= smeltScheme.GoodsNum and goodsNum2 >= smeltScheme.CostMoney then
                    return true
                end
            end
        else
            local goodsNum = SkepModule:GetGoodsCount(v.GoodsID1)
            if goodsNum >= v.GoodsNum1 then
                return true
            end
        end
        return false
    end

    if ID then
        local v = Schemes.EquipMount:Get(ID)
        if judge(v) then
            return true
        end
    else
        for _, v in ipairs(Schemes.EquipMount.items) do
            if judge(v) then
                return true
            end
        end
    end
    return false
end

--检测商店红点
function m:CheckShop(id)
    if not id then return false end
    local cfg = Schemes.Store:Get(id)
    local store, storeID
    for i = 1, 10 do
        storeID = cfg['StoreItemsID' .. i]
        if storeID == 0 then
            return false
        end
        store = StoreModule:GetStoreItemList(storeID)
        if store then
            for _, v in ipairs(store.ItemList) do
                if HelperL.GetAdverState(v.Discount) == 1 then
                    return true
                end
            end
        else
            StoreModule:RequestGetStoreList(storeID)
        end
    end

    return false
end

--检测开宝箱红点
function m:CheckOpenTreasureBox()
    -- local data, boxEquipSmelt, num
    -- for i, v in ipairs(BOX_EQUIP_ID) do
    --     if v ~= BOX_EQUIP_ID[1] then
    --         data = PropertyCompute.GetBoxEquipData(BOX_EQUIP_ID[i])
    --         if data then
    --             boxEquipSmelt = data.boxEquipSmeltList[data.level]
    --             num = SkepModule:GetGoodsCount(boxEquipSmelt.expendID)
    --             if num >= boxEquipSmelt.expendNum then
    --                 return true
    --             end
    --         end
    --     end
    -- end
    return false
end

--检测buff红点
function m:CheckBuff(buffID)
    local adverIDList = {
        [600051] = 100,
        [600052] = 101,
        [600053] = 102
    }
    if buffID then
        if HelperL.GetAdverState(adverIDList[buffID]) == 1 then
            return true
        end
    else
        for _, v in pairs(adverIDList) do
            if HelperL.GetAdverState(v) == 1 then
                return true
            end
        end
    end
    return false
end

--通用商店红点
function m:RecommendCommoditiesRedDot(cardID)
    return false --HelperL.GetAdverStateByCardID(cardID) == 1 or cardID == -1
end

--赠送体力
function m:CheckGivePhysicalPower()
    local commonText = Schemes.CommonText:Get(116)
    --判断今日次数
    local num = HeroDataManager:GetLogicByte(commonText.Param4, commonText.Param5)
    if num < commonText.DayTime then
        local condition = 0
        -- 获取当前时间的小时数（24小时制）
        local current_hour = tonumber(os.date("%H")) or 0
        if current_hour >= 0 and current_hour < 6 then
            condition = 1
        elseif current_hour >= 6 and current_hour < 12 then
            condition = 2
        elseif current_hour >= 12 and current_hour < 18 then
            condition = 3
        elseif current_hour >= 18 and current_hour < 24 then
            condition = 4
        end
        if num < condition then
            return true
        end
    end
    return false
end

--任务红点
function m:Check_UITaskInterface(taskID)
    taskID = taskID or GamePlayerData.ActorTask:GetMainTaskID()
    local taskCfg = Schemes.Task:Get(taskID)
    if taskCfg then
        local jd = GamePlayerData.ActorTask:GetTaskProgress(taskID)
        if jd >= taskCfg.Parameter2 then
            return true
        end
    end
    return false
end

function m:Check_WelfareInvestment()
    return m:CheckSevenDaysInvest(3)
end

--检测--新商城--红点
function m:Check_UIShopNew(storeID)
    --单个红点检测
    local judge = function(_storeID)
        local storeData = StoreModule:GetStoreItemList(_storeID)
        if storeData and #storeData.ItemList > 0 then
            for _, data in ipairs(storeData.ItemList) do
                if data.LimitActorDay > 0 then --看广告免费
                    local cfgAD = Schemes.CommonText:Get(data.LimitActorDay)
                    local num = HeroDataManager:GetLogicByte(cfgAD.Param4, cfgAD.Param5)
                    if num == 0 then
                        return true
                    end
                end
            end
        end
        return false
    end
    if storeID then
        if judge(storeID) then
            return true
        end
    else
        local StoreID_List = { 2, 9 }
        for i, v in ipairs(StoreID_List) do
            if judge(v) then
                return true
            end
        end
    end
    return false
end

--单个副本红点检测
local function SingleEctypeTypeCheck(ectypeType)
    local stageID = GamePlayerData.GameEctype:GetChallengeableID(ectypeType)
    local cfg     = Schemes.CatMainStage:Get(stageID)
    local maxStage = GamePlayerData.GameEctype:GetProgress(ectypeType)
    local stageList = Schemes.CatMainStage:GetByFrontType(ectypeType)

    if maxStage%1000 >= #stageList then
        return false
    end
    if cfg then
        local expendList = HelperL.Split(cfg.Need, ';')
        --消耗物品ID
        local expID = tonumber(expendList[1]) or 0
        --消耗物品数量
        local expNum = tonumber(expendList[2]) or 0
        --广告物品ID
        -- local adID = tonumber(expendList[3]) or 0
        --消耗物品ID2
        local expID2 = tonumber(expendList[4]) or 0
        --消耗物品数量2
        local expNum2 = tonumber(expendList[5]) or 0
        if not HelperL.IsLackGoods(expID, expNum, false, false) and
            not HelperL.IsLackGoods(expID2, expNum2, false, false)
        then
            return true
        end
    end
    return false
end

--检测--大陆禁地--红点
function m:Check_UIForbiddenAreaEctype()
    if SingleEctypeTypeCheck(ForbiddenArea_EctypeType) then
        return true
    end

    return false
end

--检测--黄金洞窟--红点
function m:Check_UIGoldCaveEctype()
    if SingleEctypeTypeCheck(GoldCave_EctypeType) then
        return true
    end

    return false
end

--检测--体魄背包--红点
---@param equipID ?integer 装备ID, nil检测所有体魄
---@param Type ?integer nil全都检测，1只检测体魄升级，2只检测体魄技能升级，3不检测背包剩余格子数量
---@param index ?integer 2只检测体魄技能升级，使用
---@return boolean
function m:Check_UIEquipWeapon(equipID, Type, index)
    --单个红点检测
    local judge = function(equipID2, Type2)
        local equipment = Schemes.Equipment:Get(equipID2)
        local equipWeapon = Schemes.EquipWeapon:Get(equipID2)
        local stageID = GamePlayerData.GameEctype:GetProgress(1)
        if stageID < equipWeapon.EffectID5 then
            return false
        end
        local entity = SkepModule:GetSkepByID(equipment.PacketID):GetEntityByGoodsID(equipID2)
        local quality = equipment.Quality
        local starNum = equipment.StarNum
        local level = 1
        --装备已激活
        if entity then
            quality = entity:GetProperty(EQUIPMENT_FIELD.EQUIPMENT_FIELD_QUALITY)
            starNum = entity:GetProperty(EQUIPMENT_FIELD.EQUIPMENT_FIELD_STARNUM)
            level = quality * 10 + starNum + 1
        end

        if Type2 ~= 2 then
            local smelt = Schemes.EquipSmelt:Get(equipment.SmeltID, quality, starNum)
            if smelt and smelt.LevelMaxExp > 0 then
                local bool1 = not HelperL.IsLackGoods(smelt.GoodsID, smelt.GoodsNum, false, false)
                local bool2 = not HelperL.IsLackGoods(4, smelt.CostMoney, false, false)
                if bool1 and bool2 then
                    return true
                end
            end
        end

        if Type2 ~= 1 then
            local gun = Schemes.Gun:Get(equipment.ConsignmentStyle)
            local bullet_List = Schemes.Bullet:GetByBulletId(gun.BulletId)
            if bullet_List then
                local judge2 = function(bullet)
                    if not bullet then
                        return false
                    end
                    local bulletCfg = PropertyCompute.GetBulletEquipData(bullet.Remark)
                    local bulletLevelCfg = bulletCfg.bulletLevelCfg[bulletCfg.level]
                    if level < bullet.BulletLvl or level < bulletLevelCfg.exLevel or bulletCfg.isFullLevel then
                        return false
                    end
                    return not HelperL.IsLackGoods(bulletLevelCfg.expendID, bulletLevelCfg.expendNum, false, false)
                end
                if index then
                    if judge2(bullet_List[index]) then
                        return true
                    end
                else
                    for i, v in ipairs(bullet_List) do
                        if judge2(v) then
                            return true
                        end
                    end
                end
            end
        end
        return false
    end

    if equipID then
        if judge(equipID, Type) then
            return true
        end
    else
        local stageID = GamePlayerData.GameEctype:GetProgress(1)
        --已解锁，未穿戴装备数量
        local unlockedNum = 0
        for i, v in ipairs(Schemes.EquipWeapon.items) do
            if v.GroupID < 1000 then
                if judge(v.ID, Type) then
                    return true
                end
                if stageID >= v.EffectID5 then
                    if not GamePlayerData.ActorEquip:IsWear(v.ID, 1) then
                        unlockedNum = unlockedNum + 1
                    end
                end
            end
        end

        if Type ~= 3 then
            ---背包剩余格子数量
            local num = GamePlayerData.ActorEquip:GetResidualLatticeAmount(1)
            --有背包格子和可穿戴装备
            if num > 0 and unlockedNum > 0 then
                return true
            end
        end
    end

    return false
end

--检测--主线副本--红点
function m:Check_UIMainEctype(stageID)
    --单个红点检测
    local judge = function(_stageID)
        local catMainStage = Schemes.CatMainStage:Get(_stageID)
        if not catMainStage then
            return false
        end
        --通关最大ID
        local maxStageID = GamePlayerData.GameEctype:GetProgress(catMainStage.FrontType)
        --波数
        local rankNo = GamePlayerData.GameEctype:GetRankNo(_stageID)
        local strList = HelperL.Split(catMainStage.GeneralDrop, '|')
        local waveNumber
        for i, v in ipairs(strList) do
            waveNumber = tonumber(HelperL.Split(v, ';')[1]) or 0
            if waveNumber > 0 then
                if not GamePlayerData.GameEctype:IsGet(_stageID, i) then
                    --已通关或达到指定波数
                    if maxStageID >= _stageID or rankNo >= waveNumber then
                        return true
                    end
                end
            end
        end
        return false
    end

    if stageID then
        if judge(stageID) then
            return true
        end
    else
        --主线副本宝箱红点检测
        local list = Schemes.CatMainStage:GetByFrontType(1)
        for _, v in ipairs(list) do
            if judge(v.ID) then
                return true
            end
        end
    end
    return false
end

--检测--爬塔副本--红点
function m:Check_UIPaTaFuBen()
    if SingleEctypeTypeCheck(PaTaFuBen_EctypeType) then
        return true
    end

    return false
end

--检测--秘境副本--红点
function m:Check_UIMiJingFuBen()
    if SingleEctypeTypeCheck(MiJingFuBen_EctypeType) then
        return true
    end

    return false
end

--检测--角色天赋--红点
function m:Check_UIRoleTalent()
    local smeltID = Schemes.EquipSmeltStarData:GetByOprateType(RoleTalent_OprateType)[1].EquipSmeltStarId
    local maxLevel = PropertyCompute.GetEquipSmeltStarMaxLevel(smeltID)
    local starLvl = GamePlayerData.ActorEquipNew:GetEquipUpgradeLevel(smeltID)
    if starLvl < maxLevel then
        local equipSmeltStar = Schemes.EquipSmeltStar:GetEquipSmeltStarCfg(smeltID, starLvl)
        if equipSmeltStar then
            if not HelperL.IsLackGoods(equipSmeltStar.CostGoodsID1, equipSmeltStar.CostGoodsID1Num, false, false)
                and not HelperL.IsLackGoods(equipSmeltStar.CostGoodsID2, equipSmeltStar.CostGoodsID2Num, false, false)
            then
                return true
            end
        end
    end
    return false
end

--检测--角色法宝--红点
function m:Check_UITreasuredTricks(equipSmeltStarData_ID)
    --单个红点检测
    ---@param cfg EquipSmeltStarDataCfg
    ---@return boolean
    local judge = function(cfg)
        local level = EntityModule.hero:GetProperty(CREATURE_FIELD.CREATURE_FIELD_LEVEL)
        if level >= cfg.ActivePremiss1 then
            local maxLevel = PropertyCompute.GetEquipSmeltStarMaxLevel(cfg.EquipSmeltStarId)
            local starLvl = GamePlayerData.ActorEquipNew:GetEquipUpgradeLevel(cfg.EquipSmeltStarId)
            if starLvl < maxLevel then
                local equipSmeltStar = Schemes.EquipSmeltStar:GetEquipSmeltStarCfg(cfg.EquipSmeltStarId, starLvl)
                if equipSmeltStar then
                    if not HelperL.IsLackGoods(equipSmeltStar.CostGoodsID1, equipSmeltStar.CostGoodsID1Num, false, false)
                        and not HelperL.IsLackGoods(equipSmeltStar.CostGoodsID2, equipSmeltStar.CostGoodsID2Num, false, false)
                    then
                        return true
                    end
                end
            end
        end
        return false
    end
    if equipSmeltStarData_ID then
        local cfg = Schemes.EquipSmeltStarData:Get(equipSmeltStarData_ID)
        if judge(cfg) then
            return true
        end
    else
        local list = Schemes.EquipSmeltStarData:GetByOprateType(TreasuredTricks_OprateType)
        if list then
            for _, v in ipairs(list) do
                if judge(v) then
                    return true
                end
            end
        end
    end
    return false
end

--检测--角色法宝--龙之石
function m:Check_UIShengHun(equipSmeltStarData_ID)
    --单个红点检测
    ---@param cfg EquipSmeltStarDataCfg
    ---@return boolean
    equipSmeltStarData_ID = 1
    local judge = function(cfg)
        local level = EntityModule.hero:GetProperty(CREATURE_FIELD.CREATURE_FIELD_LEVEL)
        if level >= cfg.ActivePremiss1 then
            local maxLevel = PropertyCompute.GetEquipSmeltStarMaxLevel(cfg.EquipSmeltStarId)
            local starLvl = GamePlayerData.ActorEquipNew:GetEquipUpgradeLevel(cfg.EquipSmeltStarId)
            if starLvl < maxLevel then
                local equipSmeltStar = Schemes.EquipSmeltStar:GetEquipSmeltStarCfg(cfg.EquipSmeltStarId, starLvl)
                if equipSmeltStar then
                    if not HelperL.IsLackGoods(equipSmeltStar.CostGoodsID1, equipSmeltStar.CostGoodsID1Num, false, false)
                        and not HelperL.IsLackGoods(equipSmeltStar.CostGoodsID2, equipSmeltStar.CostGoodsID2Num, false, false)
                    then
                        return true
                    end
                end
            end
        end
        return false
    end
    if equipSmeltStarData_ID then
        local cfg = Schemes.EquipSmeltStarData:Get(equipSmeltStarData_ID)
        if judge(cfg) then
            return true
        end
    else
        local list = Schemes.EquipSmeltStarData:GetByOprateType(TreasuredTricks_OprateType)
        if list then
            for _, v in ipairs(list) do
                if judge(v) then
                    return true
                end
            end
        end
    end
    return false
end

--检测--角色祈祷--红点
function m:Check_UIXinFa()
    --单个红点检测x
    ---@param cfg EquipSmeltStarDataCfg
    ---@return boolean
    local judge = function(smeltID)
        local maxLevel = PropertyCompute.GetEquipSmeltStarMaxLevel(smeltID)
        local starLvl = GamePlayerData.ActorEquipNew:GetEquipUpgradeLevel(smeltID)
        --if starLvl == 0 then starLvl = 1 end
        if starLvl < maxLevel then
             print("smeltID -======00============ "..smeltID)
            local equipSmeltStar = Schemes.EquipSmeltStar:GetEquipSmeltStarCfg(smeltID, starLvl+1)
            if equipSmeltStar then
                    print("equipSmeltStar.CostGoodsID1 -======00============ "..equipSmeltStar.CostGoodsID1)
                    print("equipSmeltStar.CostGoodsID1Num -====00============== "..equipSmeltStar.CostGoodsID1Num)
                    print("equipSmeltStar.CostGoodsID2 -=======00=========== "..equipSmeltStar.CostGoodsID2)
                    print("equipSmeltStar.CostGoodsID2Num -=====00============= "..equipSmeltStar.CostGoodsID2Num) 
                if not HelperL.IsLackGoods(equipSmeltStar.CostGoodsID1, equipSmeltStar.CostGoodsID1Num, false, false)
                    and not HelperL.IsLackGoods(equipSmeltStar.CostGoodsID2, equipSmeltStar.CostGoodsID2Num, false, false)
                then
                    return true
                end
            end
        end
        return false
    end
    
    local list = {20001,20002,20003}--Schemes.EquipSmeltStarData:GetByOprateType(TreasuredTricks_OprateType)
    if list then
        for _, v in ipairs(list) do
            if judge(v) then
                return true
            end
        end
    end
    return false
end

--检测装备进阶
function m:Check_EquipJinJie(entity)
    if not entity then return false end
    local equipID = entity:GetProperty(ENTITY_FIELD.ENTITY_FIELD_ID)
    local equipCfg = Schemes.Equipment:Get(equipID)
    if not equipCfg then return false end
    local smeltID = equipCfg.SmeltIDe
    local starLvl = GamePlayerData.ActorEquipNew:GetEquipUpgradeLevel(smeltID)
    local maxLevel = PropertyCompute.GetEquipSmeltStarMaxLevel(smeltID)
    if starLvl >= maxLevel then
        return false
    end
    local equipSmeltStar = Schemes.EquipSmeltStar:GetEquipSmeltStarCfg(smeltID, starLvl)
    if equipSmeltStar == nil then
        warn("天赋升级:未找到升星配置 smeltID=", smeltID, starLvl)
        return false
    end
    if HelperL.IsLackGoods(equipSmeltStar.CostGoodsID1, equipSmeltStar.CostGoodsID1Num, false, false) then
        return false
    end
    if HelperL.IsLackGoods(equipSmeltStar.CostGoodsID2, equipSmeltStar.CostGoodsID2Num, false, false) then
        return false
    end

    return true
end

--检测装备背包
function m:Check_EquipKnapsack()
    --有可推荐装备
    -- if m:Check_EquipKnapsack_TuiJian() then
    --     return true
    -- end

    --有可升级装备
    if m:Check_EquipKnapsack_ShengJi() then
        return true
    end

    --有可合成装备
    -- if m:Check_UIEquipSynthesis() then
    --     return true
    -- end

    --有可升级角色
    -- if m:Check_EquipKnapsack_ShengJiJueSe() then
    --     return true
    -- end

    return false
end

--检测装备背包--有可推荐装备
function m:Check_EquipKnapsack_TuiJian()
    local goodsID
    local skepBag = SkepModule:GetSkepByID(SKEPID.SKEPID_PACKET)
    local entity
    --获取背包所有装备
    for i = 0, skepBag.indexMaxsize do
        entity = EntityModule:GetEntity(skepBag[i])
        if entity then
            goodsID = entity:GetProperty(ENTITY_FIELD.ENTITY_FIELD_ID)
            if goodsID > DEFINE.MAX_MEDICAMENT_ID then
                --遍历背包装备是否有推荐
                if HelperL.IsRecommendedEquipment(entity) then
                    return true
                end
            end
        end
    end
    return false
end

--检测装备背包--有可升级装备
function m:Check_EquipKnapsack_ShengJi()
    --遍历装备背包是否有进阶
    local skepEuip = SkepModule.GetEquipSkep()
    for i = 0, skepEuip.indexMaxsize do
        if m:Check_EquipJinJie(EntityModule:GetEntity(skepEuip[i])) then
            return true
        end
    end
    return false
end

--检测装备背包--有可升级角色
function m:Check_EquipKnapsack_ShengJiJueSe()
    if not m.EquipWeaponIDList then
        m.EquipWeaponIDList = {}
        m.IDList = {}
        for i, v in ipairs(Schemes.EquipWeapon.items) do
            if v.GroupID < 1000 then
                local cfg = Schemes.Equipment:Get(v.ID)
                table.insert(m.IDList, v.ID)
                table.insert(m.EquipWeaponIDList, cfg.SmeltID)
            end
        end
    end
    local index = 1
    for _, smeltID in ipairs(m.EquipWeaponIDList) do
        if PropertyCompute.CanUpgrade(m.IDList[index],smeltID) then
            return true
        end
        index = index + 1
    end

    return false
end



--检测精英副本
function m:Check_UIEliteEctype()
    if SingleEctypeTypeCheck(Elite_EctypeType) then
        return true
    end

    return false
end

--检测首领副本
function m:Check_UILeaderEctype()
    if SingleEctypeTypeCheck(Leader_EctypeType) then
        return true
    end

    return false
end

--检测装备合成红点
function m:Check_UIEquipSynthesis(equipID)
    local skepBag = SkepModule:GetSkepByID(SKEPID.SKEPID_PACKET)

    --单个红点检测
    ---@param _equipID integer 装备ID
    ---@return boolean
    local judge = function(_equipID)
        local cfg = Schemes.Equipment:Get(_equipID)
        local strList = HelperL.Split(cfg.ModelID, "|")
        local uidList = {}
        local uidTable = {}
        local temp, id, num, entity
        for _, v in ipairs(strList) do
            temp = HelperL.Split(v, ";")
            id = tonumber(temp[1]) or 0
            num = tonumber(temp[2]) or 0
            if id > 0 and num > 0 then
                for i = 0, skepBag.indexMaxsize do
                    entity = EntityModule:GetEntity(skepBag[i])
                    if entity then
                        if not uidTable[entity.uid] then
                            if id == entity:GetProperty(ENTITY_FIELD.ENTITY_FIELD_ID) then
                                uidTable[entity.uid] = entity.uid
                                table.insert(uidList, entity.uid)
                                break
                            end
                        end
                    end
                end
            end
        end

        return #strList == #uidList
    end

    if equipID ~= nil then
        return judge(equipID)
    else
        local entity, goodsID, cfg
        for i = 0, skepBag.indexMaxsize do
            entity = EntityModule:GetEntity(skepBag[i])
            if entity then
                goodsID = entity:GetProperty(ENTITY_FIELD.ENTITY_FIELD_ID)
                cfg = Schemes.Equipment:Get(goodsID)
                if cfg and cfg.MeltingGoods > 0 and cfg.QualityLevel <= QUALITY.QUALITY_ORANGE then
                    if judge(goodsID) then
                        return true
                    end
                end
            end
        end
    end
    return false
end

--检测体魄界面
function m:Check_UIEquipWeaponInfo()
    local equipWeapon = Schemes.EquipWeapon:Get(EquipWeaponInfo_EQUIP_ID)
    --升星id
    SmeltID_List = {}

    local SmeltID
    local strs = HelperL.Split(equipWeapon.ActiveDesc1, ";")
    for i, v in ipairs(strs) do
        SmeltID = tonumber(v) or 0
        if SmeltID > 0 then
            table.insert(SmeltID_List, SmeltID)
        end
    end

    --单个红点检测
    ---@param smeltID integer 升星ID
    ---@return boolean
    local judge = function(smeltID)
        local maxLevel = PropertyCompute.GetEquipSmeltStarMaxLevel(smeltID)
        local level = GamePlayerData.ActorEquipNew:GetEquipUpgradeLevel(smeltID)
        -- local exp = GamePlayerData.ActorEquipNew:GetEquipUpgradeEXP(smeltID)
        local equipSme = Schemes.EquipSmeltStar:GetEquipSmeltStarCfg(smeltID, level)
        if equipSme then
            if level < maxLevel then
                local bool1 = not HelperL.IsLackGoods(equipSme.CostGoodsID1, equipSme.CostGoodsID1Num, false, false)
                local bool2 = not HelperL.IsLackGoods(equipSme.CostGoodsID2, equipSme.CostGoodsID1Num, false, false)
                if bool1 and bool2 then
                    return true
                end
            end
        end
        return false
    end

    if #SmeltID_List > 0 then
        for i, v in ipairs(SmeltID_List) do
            if judge(v) then
                return true
            end
        end
    end

    return false
end
