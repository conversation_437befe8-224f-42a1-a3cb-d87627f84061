﻿json = require('dkjson')

dkjsonHelper = {
	-- 是否为数组(table的成员全为number类型,从1开始并且连续)
	isArray = function(t)
		if type(t) ~= "table" then
			return false
		end

		local n = #t
		for i, v in pairs(t) do
			if type(i) ~= "number" then
				return false
			end
			if i > n then
				return false
			end
		end

		return true
	end,
	decode = function(v)
		if type(v) == "string" then
			local f = string.sub(v, 1, 1);
			if f == '{' or f == '[' then
				v = json.decode(v);
				if type(v) == "table" then
					for k, val in pairs(v) do
						v[k] = dkjsonHelper.decode(val)
					end
				end
			end
		end
		return v;
	end,
	encode = function(v, state)
		return json.encode(v, state)
	end,
	---comment 浅克隆(仅复制直接成员)
	---@param v table
	MemberwiseClone = function(v)
		local rtn = {};
		for key, value in pairs(v) do
			rtn[key] = value;
		end
		return rtn;
	end
}

if (false) then
	local rnds = { k1 = 0, k2 = 0, k3 = 0, k4 = 0, k5 = 0, k6 = 0, k7 = 0, k8 = 0, k9 = 0, k10 = 0 };
	-- 生成10万个[1, 10000]之间的随机数
    for i = 1, 100000, 1 do
		-- 测试时每次都一样的分布,所以这是伪随机算法,需要真随机数时不要使用它
        -- local r = math.random(10000);
		-- 测试时每次分布不一样,所以这是真随机算法!
		local r = RandomNum.RandomInt(1, 10001);
		if (r > 1000 and r <= 2000) then
			rnds.k2 = rnds.k2 + 1;
		elseif (r > 2000 and r <= 3000) then
			rnds.k3 = rnds.k3 + 1;
		elseif (r > 3000 and r <= 4000) then
			rnds.k4 = rnds.k4 + 1;
		elseif (r > 4000 and r <= 5000) then
			rnds.k5 = rnds.k5 + 1;
		elseif (r > 5000 and r <= 6000) then
			rnds.k6 = rnds.k6 + 1;
		elseif (r > 6000 and r <= 7000) then
			rnds.k7 = rnds.k7 + 1;
		elseif (r > 7000 and r <= 8000) then
			rnds.k8 = rnds.k8 + 1;
		elseif (r > 8000 and r <= 9000) then
			rnds.k9 = rnds.k9 + 1;
		elseif (r > 9000) then
			rnds.k10 = rnds.k10 + 1;
		else
			rnds.k1 = rnds.k1 + 1;
		end
	end

	print("10万个随机数的分布", dkjsonHelper.encode(rnds));
end
