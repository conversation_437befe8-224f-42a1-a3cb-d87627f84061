-- 登录UI
local luaID = ('UIClearAD')

local m = {}

-- 初始化
function m.OnCreate()
	m.objList.Txt_Desc.text = GetGameText(luaID, 1)
	m.objList.Txt_Recharge.text = GetGameText(luaID, 2)
	m.objList.Btn_Mask.onClick:AddListenerEx(m.OnClickMask)
	m.objList.Btn_Recharge.onClick:AddListenerEx(m.OnClickRecharge)

	return true
end

-- 点击蒙板
function m.OnClickMask()
	m:CloseSelf()
end

-- 点击行为按钮
function m.OnClickRecharge()
	HelperL.Recharge(HelperL.noAdRechargeCardID)
end

-- -- 窗口开启
-- function m:OnOpen()
-- end

-- -- 窗口关闭
-- function m:OnClose()
-- end

-- -- 窗口销毁
-- function m:OnDestroy()
-- end

return m
