--[[
********************************************************************
    created:	2024/06/06
    author :	
    purpose:    引导界面
*********************************************************************
--]]

local luaID = 'UIGuideView'

---@class UIGuideView:UIWndBase
local m = {}
--引导剪切形状
local PointType = { Circle = 1, Rect = 2 }

--------------------------------------------------------------------
-- 初始化
--------------------------------------------------------------------
function m:OnCreate()
	m.objList.Guide.gameObject:SetActive(false)
	m.img_circle_mask_force = m.objList.ClickMask_Force.transform:GetComponent('Image')
	m.img_rect_mask_force = m.objList.RectMask_Force.transform:GetComponent('Image')
	m.btnCloseTran = m.objList.Btn_Close.transform
	m.btnTxtCloseTran = m.objList.Btn_Txt_Close.transform
	m.objList.Img_Jiantou.transform:DOLocalMoveY(m.objList.Img_Jiantou.transform.localPosition.y - 20, 1):SetLoops(-1,
		DG.Tweening.LoopType.Yoyo)

	m.RegisterClickEvent()
	return true
end

--------------------------------------------------------------------
-- 窗口开启
--------------------------------------------------------------------
function m:OnOpen(guideType, step)
	m.cur_guide_type = guideType
	m:OnGuideFocus(guideType, step)
end

--------------------------------------------------------------------
-- 注册点击事件
--------------------------------------------------------------------
function m.RegisterClickEvent()
	--m.objList.Btn_ClickMask.onClick:AddListenerEx(m.CloseUI)
	m.objList.Btn_Close.onClick:AddListenerEx(m.OnClickForceMask)
	m.objList.Btn_Txt_Close.onClick:AddListenerEx(m.OnClickForceMask)
end

--------------------------------------------------------------------
-- 关闭引导界面
--------------------------------------------------------------------
function m.CloseUI()
	m.objList.Guide.gameObject:SetActive(false)
	m.objList.Btn_ClickMask.gameObject:SetActive(false)
	m.img_circle_mask_force.gameObject:SetActive(false)
	m.img_rect_mask_force.gameObject:SetActive(false)
	m.btnCloseTran.gameObject:SetActive(false)
	m.btnTxtCloseTran.gameObject:SetActive(false)
	print("关闭引导界面")
	--SoundManager:SetXmMusic(1.0)
	m:CloseSelf()
end

--------------------------------------------------------------------
-- 强制关闭引导界面
--------------------------------------------------------------------
function m.OnClickForceMask()
	HelperL.ShowMessage(TipType.MsgBoxType1, "是否退出引导？",
		{ text = "否", callback = m.OnExitGuideCallbackNo },
		{ text = "是", callback = m.OnExitGuideCallbackYes }, nil, 7)
end

--------------------------------------------------------------------
--不退出引导
--------------------------------------------------------------------
function m.OnExitGuideCallbackNo()

end

--------------------------------------------------------------------
--退出引导
--------------------------------------------------------------------
function m.OnExitGuideCallbackYes()
	m.CloseUI()
	GuideManager:SetComplete(m.cur_guide_type)
	GuideManager.isRunning = false
	print("当前关完成：" .. m.cur_guide_type)
end

--------------------------------------------------------------------
--引导配置表的焦点锁定
--------------------------------------------------------------------
function m:OnGuideFocus(guideType, step)
	local data = GuideManager.curGuideAllStepData[step]
	local wndId = data.wndId
	local window = UIManager:GetUIWndBase(wndId)
	if not window then
		warn("引导窗口不存在", wndId)
		return
	end
	m.objList.Guide.gameObject:SetActive(false)
	m.objList.Hand.gameObject:SetActive(false)

	local delayFocus = Timer.New(function()
		m.objList.Guide.gameObject:SetActive(true)
		m.objList.Hand.gameObject:SetActive(true)
		m.objList.Guide.transform:Find("Tip").gameObject:SetActive(data.TipsContent ~= "0")
		m.objList.Guide.transform:Find("Tip/Bg/Text"):GetComponent("Text").text = data.TipsContent

		if data.HaveScript == 1 then
			m.objList.Guide.transform:Find("Tip").localPosition = -m.objList.Guide.transform.localPosition
			m.objList.Guide.transform:Find("Tip"):GetComponent("RectTransform").anchoredPosition = CachedVector2(0, 500)
		elseif data.HaveScript == 2 then
			m.objList.Guide.transform:Find("Tip"):GetComponent("RectTransform").anchoredPosition = CachedVector2(0, 0)
		elseif data.HaveScript == 3 then
			m.objList.Guide.transform:Find("Tip"):GetComponent("RectTransform").anchoredPosition = CachedVector2(0, -500)
		else
			m.objList.Guide.transform:Find("Tip"):GetComponent("RectTransform").anchoredPosition = CachedVector2(0, 0)
		end

		--if data.HaveScript == 0 then
		--	m.objList.Guide.transform:Find("Tip").localPosition = CachedVector2(0, 170)
		--elseif data.HaveScript == 1 then
		--	m.objList.Guide.transform:Find("Tip").localPosition = CachedVector2(0, -170)
		--elseif data.HaveScript == 2 then
		--	m.objList.Guide.transform:Find("Tip").localPosition = CachedVector2(-350, 0)
		--elseif data.HaveScript == 3 then
		--	m.objList.Guide.transform:Find("Tip").localPosition = CachedVector2(350, 0)
		--end
		--处理聚焦对象路径相同情况，采用子物体下标序号查找
		print("------------引导------------", guideType, step, wndId, data.HaveScript, data.FocusOn)
		local focusPaths = HelperL.Split(data.FocusOn, "/")
		local focusObj = nil
		local listName = focusPaths[1]
		if listName == "[第一层]" then
			local objName = focusPaths[2]
			focusObj = window.objList[objName].gameObject
		elseif listName == "[第二层]" then
			local parentName = focusPaths[2]
			local index = focusPaths[3]
			--local childName = focusPaths[4] or "gameObject"
			local objListChild = window.objListChild
			focusObj = (window.objListChild[parentName][index]).gameObject
			if #focusPaths >= 4 and wndId == 130 then
				if step == 2 then
					focusObj = focusObj.transform:Find("GameObject/Btn_Active").gameObject	
				elseif step == 3 then
					focusObj = focusObj.transform:Find("GameObject/Btn_Up").gameObject	
				end
			elseif #focusPaths >= 4 and wndId == 123 then
				focusObj = focusObj.transform:Find("Btn_Start").gameObject	
			elseif #focusPaths >= 4 and wndId == 127 then
				focusObj = focusObj.transform:Find("Item1").gameObject 
			elseif #focusPaths >= 4 and wndId == 126 then
				focusObj = focusObj.transform:Find("Content/Btn_Upgrade").gameObject 
			elseif #focusPaths >= 4 then
				focusObj = focusObj.transform:Find("Btn_Challenge").gameObject				
			end
			--focusObj = objListChild.gameObject --objList[childName].gameObject
		end

		if focusObj == nil then
			error("找不到聚焦对象：" .. data.FocusOn)
			return
		end

		-- local rect = focusObj:GetComponent('RectTransform')
		-- if rect == nil then
		-- 	error("找不到聚焦对象 RectTransform 组件：" .. data.FocusOn)
		-- 	return
		-- end
		-- local btn = focusObj:GetComponent('Button')
		-- if btn == nil then
		-- 	error("找不到聚焦对象 Button 组件：" .. data.FocusOn)
		-- 	return
		-- end
		
		local targetObj = GameObject.Instantiate(focusObj, m.objList.CloneContent.gameObject.transform)		
		targetObj.transform.position=focusObj.gameObject.transform.position --设置位置
		targetObj:GetRectTransform().sizeDelta = focusObj.gameObject:GetRectTransform().sizeDelta

		---处理事件
		local ClickTarget = function (pointerEventData,isComplete,gt,st,onClickCallBack)
			--点击了引导的目标
			if not tolua.isnull(focusObj) then
				local targetFocusObj = focusObj
				if data.ClickFocus ~= "None" then
					local focuUiTrans = focusObj.transform
					targetFocusObj = focuUiTrans:Find(data.ClickFocus)
				end
				local eventTrigger = targetFocusObj.gameObject:GetComponent(typeof(EventTrigger))
				if eventTrigger and eventTrigger.OnPointerClick then
					eventTrigger:OnPointerClick(pointerEventData)
				else
					targetFocusObj.gameObject:GetComponent("Button").onClick:Invoke()
				end	
				GameObject.Destroy(targetObj)
				m.objList.Guide.gameObject:SetActive(false)
				m.objList.Hand.gameObject:SetActive(false)
				if isComplete then
					GuideManager:SetComplete(gt)
					m.CloseUI()
				end
				GuideManager:EndStep(gt, st)
				if onClickCallBack then onClickCallBack() end
			end
		end

		local clickTaget = targetObj
		if data.ClickFocus ~= "None" then
			print("------------data.ClickFocus------------", data.ClickFocus)
			clickTaget = targetObj.transform:Find(data.ClickFocus)
			if not clickTaget then
				print("------------data.ClickFocus----111--------", data.ClickFocus)
				clickTaget = targetObj
			end
		end

		local onBtnClickCallback = nil
		local is_complete = (step == #GuideManager.curGuideAllStepData)
		if not is_complete then
			onBtnClickCallback = function()
				m:OnGuideFocus(guideType, step + 1)
			end
		else
			onBtnClickCallback = function()
				GuideManager.Runing = false
			end
		end

		local eventTrigger = clickTaget.gameObject:GetComponent(typeof(EventTrigger))
		if eventTrigger then 
			if data.Animation == 2 then
				clickTaget.gameObject:GetComponent(typeof(EventTrigger)).triggers = focusObj.gameObject:GetComponent(typeof(EventTrigger)).triggers
			end
			eventTrigger:ClearListener(EventTriggerType.PointerClick)
		else
			eventTrigger = clickTaget.gameObject:AddComponent(typeof(EventTrigger))
		end	
		eventTrigger:AddListener(EventTriggerType.PointerClick, function(pointerEventData)
			--if paramList.Animation == 2 then return end
			ClickTarget(pointerEventData, is_complete, guideType, step,onBtnClickCallback)
		end)
		m.uiCanvas.sortingLayerName = "UI"
		m.objList.Btn_ClickMask.gameObject:SetActive(true)
		local pos = targetObj.transform.position
		local pos1 = targetObj.transform.position
		m.objList.Hand.transform.localScale = Vector3.New(1, 1, 1)
		if data.LeftHand == 3 then
			pos1.y = pos1.y + 2
			pos1.x = pos1.x + 4
		elseif data.LeftHand == 4 then
			pos1.y = pos1.y + 2
			pos1.x = pos1.x - 4
			m.objList.Hand.transform.localScale = Vector3.New(-1, -1, 1)
		elseif data.LeftHand == 1 then
			pos1.y = pos1.y - 3
			pos1.x = pos1.x + 4
			pos.x = pos.x + 0.5
			pos.y = pos.y - 0.5
		elseif data.LeftHand == 2 then
			pos1.y = pos1.y - 2
			pos1.x = pos1.x - 3
			m.objList.Hand.transform.localScale = Vector3.New(-1, 1, 1)
		end
		m.objList.Hand.transform.position = pos
		m.objList.Guide.transform.position = pos1
	end, data.DelayTime * 0.1, 1, true)
	delayFocus:Start()
end

--------------------------------------------------------------------
--设置引导点
--------------------------------------------------------------------
function m:SetPointRect(position)
	m.uiCanvas.sortingLayerName = "UI"
	-- local Btn_Force = m.objList.Btn_Rect_Force:GetComponent('RectTransform')
	-- local btn_force_pivot = Vector2.New(0.5, 0.5)
	-- pivot = btn_force_pivot - pivot
	-- Btn_Force.sizeDelta = size
	-- Btn_Force.position = position
	-- local pos = Vector2.New(Btn_Force.localPosition.x, Btn_Force.localPosition.y + size.y * pivot.y)
	-- Btn_Force.localPosition = pos
	
	
	
	-- m.img_rect_mask_force.gameObject:SetActive(true)
	-- local local_pos = Btn_Force.localPosition * rootScale
	-- local value = Vector4.New(local_pos.x, local_pos.y, 0, 0)
	-- local material = m.img_rect_mask_force.material
	-- material:SetVector("_Center", value)

	-- function AddCircleRadiusCallbackX(val)
	-- 	material:SetFloat("_SliderX", val * cur_btn.transform.lossyScale.x / Btn_Force.transform.lossyScale.x)
	-- end

	-- function AddCircleRadiusCallbackY(val)
	-- 	material:SetFloat("_SliderY", val * cur_btn.transform.lossyScale.y / Btn_Force.transform.lossyScale.y)
	-- end

	--Helper.DotweenTo(1000, size.x / 2 * rootScale, 0.3, AddCircleRadiusCallbackX)
	--Helper.DotweenTo(500, size.y / 2 * rootScale, 0.3, AddCircleRadiusCallbackY)

	-- local cur_onClick = function()
	-- 	m.objList.Guide.gameObject:SetActive(false)
	-- 	if is_complete then
	-- 		GuideManager:SetComplete(guideType)
	-- 		m.CloseUI()
	-- 	end
	-- 	GuideManager:EndStep(guideType, step)
	-- 	--m.OnClickMask()
	-- 	if cur_btn then cur_btn.onClick:Invoke() end
	-- 	if onClickCallBack then onClickCallBack() end
	-- end
	-- m.objList.Btn_Rect_Force.onClick:RemoveAllListeners()
	-- m.objList.Btn_Rect_Force.onClick:AddListenerEx(cur_onClick)
end

return m
