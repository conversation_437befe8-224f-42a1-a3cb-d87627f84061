-- 全局场景管理
local luaID = ('SceneManager')

SceneManager = SceneManager or
	{
		isLoading = false,
		asyncItem = nil,
		closeLoadingUI = false,
		hideLoadingUI = false,
		cacheParam = {},
		waitDownload = 0
	}

function SceneManager:ChangeLoadingLabel()
	local rand = math.random(1, #Schemes.LoadingTips.items)
	local tipsScheme = Schemes.LoadingTips:Get(rand)
	if not tipsScheme then return end
	self.Txt_BarTip.text = tipsScheme.Content
end

function SceneManager:GetMainCamera()
	return self.mainCamera
end

function SceneManager:LoadSceneWithLoading(name, closeLoadingUI, changePic)
	--DOTween.CompleteAll()
	if self.isLoading then
		print('LoadSceneWithLoading isLoading==true  name=' .. name)
		self.cacheParam = { name = name, closeLoadingUI = closeLoadingUI }
		return
	end
	print('LoadSceneWithLoading ' .. name)
	self:ChangeLoadingLabel()
	if self.Txt_Bar then
		self.Txt_Bar.text = GetGameText(luaID, 1)
	end

	self.curSceneName = name
	self.curSceneABName = HotResManager.GetSceneABName(self.curSceneName)
	--self.CheckSceneUI(name)

	if not self.hideLoadingUI then
		self:ShowLoadingUI(changePic, true)
		local mainCamera = self.mainCamera
		if not tolua.isnull(mainCamera) then
			mainCamera.transform.position = Vector3(0, 1000, 0)
			mainCamera.transform.rotation = Quaternion.identity
		end
	end
	self.isLoading = true
	self.closeLoadingUI = closeLoadingUI
	self.beginCountdown = 3
	Helper.SetHideDownloadTips(true)
	--self:BeginOnUpdate()
	ResMgr.LoadSceneAsync("Assets/Scenes/".. self.curSceneName .. ".unity")
end

function SceneManager:UpdateLoadingBar(realProcess)
	local self = SceneManager
	if self.Sld_ProgressBar then
		self.Sld_ProgressBar.value = realProcess
		self.Img_Fill.fillAmount = realProcess
		self.Txt_ProgressBar.text = math.floor((realProcess / 1) * 100) .. '%'
		self.Sld_ProgressBar.gameObject:SetActive(true)
	end
end

function SceneManager:BeginOnUpdate()
	if not self.updateHandle then
		self.updateHandle = UpdateBeat:CreateListener(self.OnUpdate, self)
	end
	UpdateBeat:AddListener(self.updateHandle)
end

function SceneManager:EndOnUpdate()
	UpdateBeat:RemoveListener(self.updateHandle)
end

--addressable加载场景进度刷新
function SceneManager.OnLoadingUpdate(progress)
	--print(progress.."进度")
	if progress < 1 then--加载中
		SceneManager:UpdateLoadingBar(progress)
	elseif progress >= 1 then--加载完成
		print("场景完成")
		WorldModule.OnlevelWasLoaded()
		if SceneManager.closeLoadingUI then
			SceneManager.loadingUIObj:SetActive(false)
			Helper.SetHideDownloadTips(false)
			SceneManager.isLoading = false;
		end
		SceneManager:OnLevelWasLoaded()
	end
end
function SceneManager.OnUpdate(self)
	local sceneABName = self.curSceneABName
	if self.beginCountdown ~= nil and self.beginCountdown > 0 then
		self.beginCountdown = self.beginCountdown - 1
		if self.beginCountdown == 0 then
			if Schemes.lateLoadScheme then
				Schemes.LateLoadAllScheme(false)
				Schemes.PreLoadScheme(9999)
			end
			self.loadingProcess = 0
			self.loadingFakeProcess = 0
			self.loadingFakeProcessSpeed = 0.05

			self.asyncItem = UnityEngine.SceneManagement.SceneManager.LoadSceneAsync(self.curSceneName)
			if self.asyncItem then
				self.asyncItem.allowSceneActivation = true
			else
				error('读取场景失败 ' .. tostring(self.curSceneName))
			end

			--if HotResManager.UseHotBundle then
			--if not HotResManager.IsBundleDownloaded(sceneABName) then
			--local packID = HotResManager.GetBundlePackID(sceneABName)
			--if packID > 0 then
			--local scheme = Schemes.DownloadPack:Get(packID)
			--local isStandAlone = 1
			--if scheme then
			--isStandAlone = scheme.IsStandAlone
			--end
			--local size = Helper.GetDownloadingPackSize(-1)
			--if isStandAlone == 0 then
			--size = Helper.GetAllPackSize(packID, 2)
			--else
			--size = Helper.GetDownloadingPackSize(packID)
			--end
			--self.downloadingScene = true
			--Helper.ResetTipDownload(size)
			--Helper.StartDownloadPack(packID, isStandAlone)
			--Helper.SetHideDownloadTips(false)
			--else
			--warn('找不到场景对应包ID ' .. sceneABName)
			--end
			--elseif not HotResManager.IsBundleLoaded(sceneABName) then
			--self.readingScene = true
			--HotResManager.LoadAssetBundleAsync(sceneABName)
			--else
			--self.asyncItem = UnityEngine.SceneManagement.SceneManager.LoadSceneAsync(self.curSceneName)
			--if self.asyncItem then
			--self.asyncItem.allowSceneActivation = true
			--else
			--error('读取场景失败 ' .. tostring(self.curSceneName))
			--end
			--end
			--else

			--self.asyncItem = UnityEngine.SceneManagement.SceneManager.LoadSceneAsync(self.curSceneName)
			--if self.asyncItem then
			--self.asyncItem.allowSceneActivation = true
			--else
			--error('读取场景失败 ' .. tostring(self.curSceneName))
			--end

			--end
		else
			return
		end
	end

	if not self.isLoading then
		self:EndOnUpdate()
		return
	end

	if self.downloadingScene then
		if Helper.GetDownloadQueueStatus() == 0 then
			self.downloadingScene = false
			self.readingScene = true
			HotResManager.LoadAssetBundleAsync(sceneABName)

			self.loadingProcess = 0
			self.loadingFakeProcess = 0
			self.loadingFakeProcessSpeed = 0.05
			--self:UpdateLoadingBar(0)
			self:ChangeLoadingLabel()
		else
			--self:UpdateLoadingBar(0)
		end
	elseif self.readingScene then
		if HotResManager.IsBundleLoaded(sceneABName) then
			self.readingScene = false
			self.asyncItem = UnityEngine.SceneManagement.SceneManager.LoadSceneAsync(self.curSceneName)
			self.asyncItem.allowSceneActivation = true
			self:UpdateLoadingBar(0.3)
		else
			--self:UpdateLoadingBar(0.3)
		end
	elseif self.asyncItem then
		if self.waitDownload > 0 then
			if self.waitDownload > 2 then
				local isDownloading = Helper.GetDownloadQueueStatus()
				if isDownloading == 0 then
					self:SetWaitDownloading(false)
				end
			else
				self.waitDownload = self.waitDownload + 1
			end
		elseif self.asyncItem.isDone then
			collectgarbage("collect")

			self:EndOnUpdate()
			self.isLoading = false
			self.asyncItem = nil
			self.readingScene = false
			self.downloadingScene = false
			self.loadingProcess = 0
			self.loadingFakeProcess = 0
			self.loadingFakeProcessSpeed = 0.05
			if self.cacheParam.name ~= nil then
				local name = self.cacheParam.name
				local closeLoadingUI = self.cacheParam.closeLoadingUI
				self.cacheParam = {}
				self:LoadSceneWithLoading(name, closeLoadingUI, false)
				return
			end
			print("场景跳转成功开始发射")
			EventManager:Fire(EventID.OnSceneLoaded, self.curSceneName)
			WorldModule.OnlevelWasLoaded()

			if self.closeLoadingUI then
				self:CloseLoadingUI()
				Helper.SetHideDownloadTips(false)
				-- 去掉这个延时试试
				--[[
				local closeUICallback = function()
						if not self.isLoading then
							self:CloseLoadingUI()
						end
						self.closeUITimer = nil
						Helper.SetHideDownloadTips(false)
					end
				if self.closeUITimer == nil then
					self.closeUITimer = Timer.New(closeUICallback, 1.0, 1)
					self.closeUITimer:Start()
				else
					self.closeUITimer:Stop()
					self.closeUITimer:Reset(closeUICallback, 1.0, 1)
					self.closeUITimer:Start()
				end
				]]
			end
		else
			self:UpdateLoadingBar(self.asyncItem.progress * 0.7 + 0.3)
		end
	else
		self:EndOnUpdate()
	end
end

local loadingPicName = { '1', '2', '3', '4' }
function SceneManager:ShowLoadingUI(changePic, isInternal)
	if changePic then
		local texture = HotResManager.ReadImage('image/LD-JiaZai' .. loadingPicName[math.random(#loadingPicName)] ..
			'.png')
		if not texture or tolua.isnull(texture) then
			texture = HotResManager.ReadImage('image/LD_QiDong.png') --读不到默认读1
		end
		self.RawImg_CompLogo.texture = texture
	else
		if not isInternal then
			if self.Txt_Bar then
				self.Txt_Bar.text = ''
			end
			Helper.SetHideDownloadTips(true)
		end
	end

	self.loadingUIObj:SetActive(true)
end

function SceneManager:CloseLoadingUI()
	self.loadingUIObj:SetActive(false)
end

function SceneManager:SetWaitDownloading(isWait)
	if isWait then
		self.waitDownload = 1
		Helper.SetFastDownloadPack(1)
	else
		self.waitDownload = 0
		Helper.SetFastDownloadPack(0)
	end
end

function SceneManager:OnLevelWasLoaded()
	print('level was loaded ' .. self.curSceneName)
	--Helper.ClearObjMemory()
	if self.cacheParam.name ~= nil then
		return
	end

	self.mainCamera = Camera.main
	if self.mainCamera then
		--self.mainCameraController = self.mainCamera:GetComponent('CameraController')
		local cullMask = self.mainCamera.cullingMask
		--cullMask = Helper.ResetBit(cullMask, Layer.Loading)
		cullMask = Helper.ResetBit(cullMask, Layer.UI)
		self.mainCamera.cullingMask = cullMask
	else
		warn('SceneManager:OnLevelWasLoaded mainCamera=nil ', self.curSceneName)
	end

	-- 处理UI
	UIManager.OnLevelWasLoaded(self.curSceneName)
	EntityModule:OnLevelWasLoaded(self.curSceneName)
	EventManager:Fire(EventID.OnSceneLoaded, self.curSceneName)
	--if GameLuaAPI.Channel ~= GameLuaAPI.eChannel.eChannel_None and SceneManager.curSceneName == 'GameLogin' and not LoginModule.isLoginOK then
	--GameLuaAPI.SDKLogin()
	--end
end

function SceneManager:Init()
	-- 获取载入界面
	self.loadingUIObj = GameObject.Find('Loading')
	Helper.FillLuaComps(self.loadingUIObj.transform, self)
	UnityEngine.Object.DontDestroyOnLoad(self.loadingUIObj)

	--UI适配 缩放
	-- HelperL.AdaptScale(self.root, 1)
	-- HelperL.AdaptScale(self.CompLogo, 6)

	self.Txt_Versions.text = Helper.GetBigVer()
	self.Sld_ProgressBar.value = 0
	self.Img_Fill.fillAmount = 0
	self.Txt_ProgressBar.text = '0%'
	self.Txt_HealthTip1.text = GetGameText(luaID, 2)
	self.Txt_HealthTip2.text = GetGameText(luaID, 3)
	self.Txt_HealthTip3.text = GetGameText(luaID, 4)

	-- 先在这里设置fxpool，后续再优化
	-- local fxPool = GameObject.Find('FxPool')
	-- UnityEngine.Object.DontDestroyOnLoad(fxPool)

	--self:LoadSceneWithLoading('GameLogin', true, false)
	self.loadingUIObj:SetActive(false)
end

function SceneManager:SetHideLoadingUI(isHide)
	if isHide then
		self.hideLoadingUI = true
		if self.isLoading then
			self:CloseLoadingUI()
		end
	else
		self.hideLoadingUI = false
		if self.isLoading then
			self:ShowLoadingUI(true)
		end
	end
end