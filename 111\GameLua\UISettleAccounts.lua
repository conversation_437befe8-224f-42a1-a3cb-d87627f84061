--[[
********************************************************************
    created:    2024/05/15
    author :    李锦剑
    purpose:    结算界面
*********************************************************************
--]]

local luaID = ('UISettleAccounts')
---@class UISettleAccounts:UIWndBase
local m = {}

--------------------------------------------------------------------
-- 创建窗口
--------------------------------------------------------------------
function m.OnCreate()
    ---@type SlotItem[]
    m.SlotItem_List = {}
    m.ResetUI()
    m.RegisterClickEvent()
    return true
end

--------------------------------------------------------------------
-- V31.5-fix 窗口开启（在OnOpen层面防重复）
--------------------------------------------------------------------
function m:OnOpen(stageId, isSuccess, roundNo, addFactor)
    -- V31.5-fix 防止重复调用（延迟重置标志）
    if m._isOpening then
        --print("=== V31.5-fix OnOpen重复调用，忽略 ===")
        return
    end
    
    m._isOpening = true
    --print("=== V31.5-fix OnOpen开始，stageId=" .. stageId .. "，isSuccess=" .. tostring(isSuccess) .. " ===")
    
    --关卡ID
    m.stageId = stageId
    --是否通关
    m.isSuccess = isSuccess
    --刷怪波数
    m.roundNo = roundNo
    --奖励加成
    m.addFactor = addFactor

    m.UpdateView()
    
    -- V31.2-fix 先设置文字内容，避免动画过程中修改文本
    if isSuccess then
        SoundManager:PlaySound(SoundID.Success)
        m.objList.Txt_Content.text = GetGameText(luaID, 2)
    else
        SoundManager:PlaySound(SoundID.Failure)
        m.objList.Txt_Content.text = GetGameText(luaID, 3)
    end
    
    -- V31.0 初始化动画前状态
    m.InitAnimationState(isSuccess)
    
    -- V31.0 播放顺序动画
    m.PlaySequentialAnimation(isSuccess)
    
    -- V31.5-fix 不在这里重置标志，而是在动画完成后重置
    --print("=== V31.5-fix OnOpen完成，等待动画完成后重置标志 ===")
end

--------------------------------------------------------------------
-- V31.4-fix 初始化动画前状态
--------------------------------------------------------------------
function m.InitAnimationState(isSuccess)
    -- V31.0 立即显示的元素（不需要动画）
    m.objList.root.gameObject:SetActive(true)
    
    -- V31.0 Img_Mask(1)和其他固定元素立即显示
    if m.objList.Img_Mask then
        m.objList.Img_Mask.gameObject:SetActive(true)
    end
    
    -- V31.0 需要动画的元素初始隐藏
    m.objList.Img_Headline1.gameObject:SetActive(false)
    m.objList.Img_Headline2.gameObject:SetActive(false)
    m.objList.Txt_Content.gameObject:SetActive(false)
    m.objList.Grid_Award.gameObject:SetActive(false)
    m.objList.Btn_Close1.gameObject:SetActive(false)
    
    -- V31.0 检查是否有Img_Mask(2)并隐藏
    if m.objList.Img_Mask_2 then
        m.objList.Img_Mask_2.gameObject:SetActive(false)
    end
    
    --print("=== V31.0 SettleAccounts 动画初始化完成 ===")
end

--------------------------------------------------------------------
-- V31.5-fix 播放顺序动画（在动画完成后重置标志）
--------------------------------------------------------------------
function m.PlaySequentialAnimation(isSuccess)
    --print("=== V31.5-fix 开始播放SettleAccounts顺序动画，isSuccess=" .. tostring(isSuccess) .. " ===")
    local totalDelay = 0
    
    -- V31.2-fix 第1步：0.3秒后显示标题动画
    totalDelay = 0.3
    Timer.New(function()
        local headlineObj = isSuccess and m.objList.Img_Headline1 or m.objList.Img_Headline2
        local headlineName = isSuccess and "Img_Headline1" or "Img_Headline2"
        headlineObj.gameObject:SetActive(true)
        
        --print("=== V31.2-fix 第1步：" .. headlineName .. "开始动画，0.3秒 ===")
        -- 缩放动画效果
        local originalScale = headlineObj.transform.localScale
        headlineObj.transform.localScale = Vector3.New(0.3, 0.3, 1)
        local tween = headlineObj.transform:DOScale(originalScale, 0.3):SetEase(DG.Tweening.Ease.OutBack)
        tween:OnComplete(function()
            --print("=== V31.2-fix 第1步：" .. headlineName .. "动画完成 ===")
        end)
    end, totalDelay, 1):Start()
    
    -- V31.2-fix 第2步：再0.5秒后显示Img_Mask(2)和Txt_Content动画
    totalDelay = totalDelay + 0.5
    Timer.New(function()
        --print("=== V31.2-fix 第2步：Img_Mask(2)和Txt_Content开始动画，0.5秒 ===")
        
        -- 显示Img_Mask(2)（如果存在）立即显示
        if m.objList.Img_Mask_2 then
            m.objList.Img_Mask_2.gameObject:SetActive(true)
            --print("=== V31.2-fix Img_Mask(2)已显示 ===")
        end
        
        -- V31.2-fix 显示并动画Txt_Content，添加完成回调
        m.objList.Txt_Content.gameObject:SetActive(true)
        local originalScale = m.objList.Txt_Content.transform.localScale
        m.objList.Txt_Content.transform.localScale = Vector3.New(0.5, 0.5, 1)
        local tween = m.objList.Txt_Content.transform:DOScale(originalScale, 0.5):SetEase(DG.Tweening.Ease.OutQuart)
        tween:OnComplete(function()
            --print("=== V31.2-fix 第2步：Txt_Content缩放动画完成 ===")
        end)
    end, totalDelay, 1):Start()
    
    -- V31.2-fix 第3步：再0.5秒后显示Grid_Award奖励动画
    totalDelay = totalDelay + 0.5
    Timer.New(function()
        --print("=== V31.2-fix 第3步：Grid_Award奖励开始动画，0.5秒 ===")
        
        m.objList.Grid_Award.gameObject:SetActive(true)
        
        -- 奖励网格从小到大出现
        local originalScale = m.objList.Grid_Award.transform.localScale
        m.objList.Grid_Award.transform.localScale = Vector3.New(0.1, 0.1, 1)
        local tween = m.objList.Grid_Award.transform:DOScale(originalScale, 0.5):SetEase(DG.Tweening.Ease.OutBounce)
        tween:OnComplete(function()
            --print("=== V31.2-fix 第3步：Grid_Award缩放动画完成 ===")
        end)
        
        -- 奖励项目依次出现
        m.AnimateRewardItems()
    end, totalDelay, 1):Start()
    
    -- V31.5-fix 第4步：再0.2秒后显示Btn_Close1砸下来震撼效果（最后一步，完成后重置标志）
    totalDelay = totalDelay + 0.2
    Timer.New(function()
        --print("=== V31.2-fix 第4步：Btn_Close1砸下震撼效果，0.2秒 ===")
        
        m.objList.Btn_Close1.gameObject:SetActive(true)
        
        -- 砸下来效果：从大到小的戏剧性效果
        local originalScale = m.objList.Btn_Close1.transform.localScale
        local originalPos = m.objList.Btn_Close1.transform.localPosition
        m.objList.Btn_Close1.transform.localScale = Vector3.New(3, 3, 1)
        
        local scaleTween = m.objList.Btn_Close1.transform:DOScale(originalScale, 0.2):SetEase(DG.Tweening.Ease.OutBack)
        scaleTween:OnComplete(function()
            --print("=== V31.2-fix Btn_Close1缩放动画完成 ===")
            -- 缩放完成后播放震动效果
            local shakeTween = m.objList.Btn_Close1.transform:DOShakePosition(0.3, Vector3.New(5, 5, 0), 10, 90, false, true)
            shakeTween:OnComplete(function()
                --print("=== V31.5-fix SettleAccounts所有动画播放完成！ ===")
                -- V31.5-fix 动画完全结束后才重置标志
                m._isOpening = false
                --print("=== V31.5-fix _isOpening标志已重置，可以接受新的调用 ===")
            end)
        end)
    end, totalDelay, 1):Start()
end

--------------------------------------------------------------------
-- V31.3-fix 奖励物品动画
--------------------------------------------------------------------
function m.AnimateRewardItems()
    --print("=== V31.3-fix 开始奖励物品依次动画 ===")
    -- 让每个奖励物品依次弹出
    local activeItemCount = 0
    local completedCount = 0
    
    -- 先统计活跃物品数量，并调试输出
    --print("=== V31.3-fix m.SlotItem_List长度: " .. #m.SlotItem_List .. " ===")
    for i, slotItem in ipairs(m.SlotItem_List) do
        if slotItem then
            local isActive = slotItem.gameObject.activeSelf
            --print("=== V31.3-fix 奖励物品" .. i .. "存在，激活状态: " .. tostring(isActive) .. " ===")
            if isActive then
                activeItemCount = activeItemCount + 1
            end
        else
            --print("=== V31.3-fix 奖励物品" .. i .. "不存在 ===")
        end
    end
    
    for i, slotItem in ipairs(m.SlotItem_List) do
        if slotItem and slotItem.gameObject.activeSelf then
            Timer.New(function()
                --print("=== V31.3-fix 奖励物品" .. i .. "开始动画 ===")
                local originalScale = slotItem.transform.localScale
                slotItem.transform.localScale = Vector3.New(0.2, 0.2, 1)
                local scaleTween = slotItem.transform:DOScale(originalScale, 0.3):SetEase(DG.Tweening.Ease.OutBack)
                
                -- 添加轻微的旋转效果
                local rotateTween = slotItem.transform:DORotate(Vector3.New(0, 0, 360), 0.5, DG.Tweening.RotateMode.Fast):SetEase(DG.Tweening.Ease.OutQuart)
                rotateTween:OnComplete(function()
                    completedCount = completedCount + 1
                    --print("=== V31.3-fix 奖励物品" .. i .. "动画完成 (" .. completedCount .. "/" .. activeItemCount .. ") ===")
                    if completedCount >= activeItemCount then
                        --print("=== V31.3-fix 所有奖励物品动画完成 ===")
                    end
                end)
            end, i * 0.1, 1):Start()
        end
    end
    
    if activeItemCount == 0 then
        --print("=== V31.3-fix 没有需要动画的奖励物品，可能失败时没有奖励 ===")
    else
        --print("=== V31.3-fix 找到 " .. activeItemCount .. " 个需要动画的奖励物品 ===")
    end
end

--------------------------------------------------------------------
--注册点击事件
--------------------------------------------------------------------
function m.RegisterClickEvent()
    m.objList.Btn_Close.onClick:AddListenerEx(function ()
        m.GetRewards(false)
    end)
    m.objList.Btn_Close1.onClick:AddListenerEx(function ()
    m.GetRewards(false)
end)
    m.objList.Btn_OK.onClick:AddListenerEx(function()
        m.GetRewards(false)
    end)
    m.objList.Btn_Double.onClick:AddListenerEx(function()
        m.GetRewards(true)
    end)
end

--------------------------------------------------------------------
-- 更新界面
--------------------------------------------------------------------
function m.UpdateView()
    local prizeList = m.CalculatingRewards(m.stageId, m.isSuccess, m.roundNo, m.addFactor)
    local num = math.max(#m.SlotItem_List, #prizeList)
    local goodInfo = '0'
    for i = 1, num, 1 do
        if not m.SlotItem_List[i] then
            m.SlotItem_List[i] = _GAddSlotItem(m.objList.Grid_Award)
        end
        if prizeList[i] then
            m.SlotItem_List[i]:SetItemID(prizeList[i].ID)
            m.SlotItem_List[i]:SetCount(prizeList[i].Num)
            m.SlotItem_List[i]:SetActive(true)
            if goodInfo == '0' then
                goodInfo = prizeList[i].ID .. ';' .. prizeList[i].Num
            else
                goodInfo = goodInfo .. '|' .. prizeList[i].ID .. ';' .. prizeList[i].Num
            end
        else
            m.SlotItem_List[i]:SetActive(false)
        end
    end
    m.goodInfo = goodInfo
    m.prizeList = prizeList

    local cfg = Schemes.CatMainStage:Get(m.stageId)
    local adverID = HelperL.Split(cfg.MapPos, ';')[1]
    local commonText = Schemes.CommonText:Get(adverID)
    m.objList.Txt_Hint.text = cfg.Name
    m.objList.Btn_Double.gameObject:SetActive(false)
    if commonText then
        local num2 = HeroDataManager:GetLogicByte(commonText.Param4, commonText.Param5)
        m.objList.Txt_AdNum.text = string.format(GetGameText(luaID, 1), num2, commonText.DayTime)
        m.objList.Btn_Double.gameObject:SetActive(num2 < commonText.DayTime)
    end
end

--------------------------------------------------------------------
---计算奖励
---@param stageId integer 关卡ID
---@param isSuccess boolean 是否成功
---@param roundNo integer 刷怪ID用来取失败奖励
---@param addFactor integer 奖励加成
---@return PrizeData[]
---@return PrizeData[]
--------------------------------------------------------------------
function m.CalculatingRewards(stageId, isSuccess, roundNo, addFactor)
    if not addFactor or addFactor <= 0 then
        addFactor = 1
    end

    ---@type PrizeData[]
    local prizeGoodsNum = {}
    ---@type PrizeData[]
    local prizeGoodsList = {}

    if isSuccess then
        local catMainStage = Schemes.CatMainStage:Get(stageId)
        local strList = catMainStage.Rebirth_item1
        local temp, goodsID, goodsNum, medicament
        for i, str in ipairs(strList) do
            temp = HelperL.Split(str, "|")
            goodsID = tonumber(temp[1]) or 0
            goodsNum = (tonumber(temp[2]) or 0) * addFactor
            if goodsID > 0 and goodsNum > 0 then
                medicament = Schemes:GetGoodsConfig(goodsID)
                if medicament then
                    --判断是随机物品
                    if medicament.UseMenu == 4 then
                        for ii = 1, goodsNum, 1 do
                            Schemes.PrizeTable.__RandomPrize(medicament.UseParam1, 1, true, 1, prizeGoodsNum,
                                prizeGoodsList)
                        end
                    else
                        if prizeGoodsNum[goodsID] then
                            prizeGoodsNum[goodsID].Num = prizeGoodsNum[goodsID].Num + goodsNum
                        else
                            local goods = { ID = goodsID, Num = goodsNum, }
                            prizeGoodsNum[goodsID] = goods
                            table.insert(prizeGoodsList, goods)
                        end
                    end
                end
            end
        end
        
        local catMainStage = Schemes.CatMainStage:Get(stageId)
        local lastStageID = HeroDataManager:GetLogicData(EctypeSaveLogic[catMainStage.FrontType])    
        if stageId > lastStageID then   
            local str_req2 = string.format('LuaRequestSetLogicData?logicID=%d&logicValue=%d', EctypeSaveLogic[catMainStage.FrontType],stageId)
            LuaModule.RunLuaRequest(str_req2, function(resultCode2, content2)
                --print("resultCode2 = ", resultCode2)
                if resultCode2 == RESULT_CODE_BASE.RESULT_COMMON_SUCCEED[1] then
                end
            end)
        end

        
    else
        local catMainStage = Schemes.CatMainStage:Get(stageId)
        local missionInfo = Schemes.MissionInfo:Get(catMainStage.Map)
        local temp, _roundNo, _battleBrushEnemyID, _goldNum
        local battleBrushEnemyID
        local strList = HelperL.Split(missionInfo.WaveInfo, "|")
        for i, str in ipairs(strList) do
            temp = HelperL.Split(str, ";")
            _roundNo = tonumber(temp[1]) or 0
            -- _goldNum = tonumber(temp[2]) or 0
            _battleBrushEnemyID = tonumber(temp[3]) or 0
            if not battleBrushEnemyID then
                battleBrushEnemyID = _battleBrushEnemyID
            else
                if _roundNo == roundNo then
                    battleBrushEnemyID = _battleBrushEnemyID
                    break
                end
            end
        end

        local prizeStr = Schemes.BattleBrushEnemy:Get(battleBrushEnemyID).FailPrize
        Schemes.PrizeTable.__RandomPrize2(prizeStr, addFactor, prizeGoodsNum, prizeGoodsList)
    end

    return prizeGoodsList, prizeGoodsNum
end

--------------------------------------------------------------------
-- V31.5-fix 重置UI
--------------------------------------------------------------------
function m.ResetUI()
    -- V31.5-fix 重置开启标志
    m._isOpening = false
    
    m.goodInfo = '0'
    m.objList.Img_Headline1.gameObject:SetActive(false)
    m.objList.Img_Headline2.gameObject:SetActive(false)
    m.objList.root.gameObject:SetActive(false)
    for i, v in ipairs(m.SlotItem_List) do
        v:SetActive(false)
    end
    
    --print("=== V31.5-fix ResetUI完成，开启标志已重置 ===")
end

--------------------------------------------------------------------
-- 关闭UI
--------------------------------------------------------------------
function m.OnCloseUI()
    m.ResetUI()
    m:CloseSelf()
    LuaToCshapeManager.Instance:FightQuit()
end

--------------------------------------------------------------------
---获取副本奖励
---@param isDouble boolean 是否双倍奖励
--------------------------------------------------------------------
function m.GetRewards(isDouble)
    local cfg = Schemes.CatMainStage:Get(m.stageId)
    if isDouble then
        local adverID = HelperL.Split(cfg.MapPos, ';')[1]
        AdvertisementManager.ShowRewardAd(adverID, function(bool, adID, code)
            --广告拉取失败，不做如何处理
            if code ~= RESULT_CODE_BASE_2.FAILURE_TO_GET_ADS[1] then
                if bool then
                    for i, v in ipairs(m.prizeList) do
                        v.Num = v.Num * 2
                    end
                    local goodInfo = HelperL.TableAttributeConcatenation(m.prizeList) or "0"
                    HelperL.RequestDirectGiveGoodsy(goodInfo, '0', m.RequestCallback, false)
                else
                    HelperL.RequestDirectGiveGoodsy(m.goodInfo, '0', m.RequestCallback, false)
                end
            end
        end)
    else
        HelperL.RequestDirectGiveGoodsy(m.goodInfo, '0', m.RequestCallback, false)
    end
    --主线副本，有额外奖励
    if cfg.FrontType == 1 then
        local id = GamePlayerData.GameEctype:GetProgress(1)
        if m.stageId == id then
            local goodInfo = '1;' .. (cfg.First_Prize or 1)
            HelperL.RequestDirectGiveGoodsy(goodInfo, '0', nil, false)
        end
    end
end

--------------------------------------------------------------------
--请求回调
--------------------------------------------------------------------
function m.RequestCallback(resultCode, content)
    if resultCode == RESULT_CODE_BASE.RESULT_COMMON_SUCCEED[1] then
        m.SavingCopiesOfData()
    else
        ResultCode:DefaultShowResultCode(resultCode, content)
    end
    m.OnCloseUI()
end

--------------------------------------------------------------------
---保存副本数据
--------------------------------------------------------------------
function m.SavingCopiesOfData()
    local form = {}
    form["stageID"] = m.stageId
    form["isSuccess"] = m.isSuccess
    form["roundNo"] = m.roundNo
    LuaModuleNew.SendRequest(LuaRequestID.SaveEctypeProgress, form, function(resultCode, content, callbackParam)
        if resultCode == RESULT_CODE_BASE.RESULT_COMMON_SUCCEED[1] then
            --print("保存副本数据成功")
            -- 清除战斗进度
            LuaToCshapeManager.Instance:ClearProgress()
        else
            ResultCode.ShowResultCodeCallback(resultCode, content)
        end
    end)
end

return m
