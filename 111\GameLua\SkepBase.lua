-- 物品篮子类
---@class SkepBase
local SkepBase = {}
SkepBase.__index = SkepBase

--- 创建
function SkepBase:New(m)
	--背包UID
	self.skepUID      = m.SkepUID
	self.masterUID    = m.MasterUID
	self.access       = m.Access
	--背包ID
	self.skepID       = m.SkepID
	--背包格子数量
	self.skepMaxsize  = m.MaxSize
	--最大索引值，索引从0快速
	self.indexMaxsize = m.MaxSize - 1
end

--- 同步
function SkepBase:SyncSkepGoods(m)
	for _, v in ipairs(m.PlaceList) do
		-- 删除某个物品
		if v.GoodsUID == '0' then
			if self[v.Place] then
				if Time.frameCount == self.fastFrameCount then
					-- 更新fastcount缓存
					local entity = EntityModule:GetEntity(self[v.Place])
					if entity then
						local goodsID = entity:GetProperty(ENTITY_FIELD.ENTITY_FIELD_ID)
						local goodsNum = entity:GetProperty(GOODS_FIELD.GOODS_FIELD_QTY)
						if self.fastCount[goodsID] then
							self.fastCount[goodsID] = self.fastCount[goodsID] - goodsNum
						end
					end
				end
				self[v.Place] = nil
			end
		else
			local beforeUID = self[v.Place]
			self[v.Place] = v.GoodsUID
			if Time.frameCount == self.fastFrameCount then
				if beforeUID ~= v.GoodsUID then
					-- 更新fastcount缓存
					local entity = EntityModule:GetEntity(v.GoodsUID)
					if entity then
						local goodsID = entity:GetProperty(ENTITY_FIELD.ENTITY_FIELD_ID)
						local goodsNum = entity:GetProperty(GOODS_FIELD.GOODS_FIELD_QTY)
						if not self.fastCount[goodsID] then
							self.fastCount[goodsID] = goodsNum
						else
							self.fastCount[goodsID] = self.fastCount[goodsID] + goodsNum
						end
					end
				end
			end
			SkepModule:AutoUseGoods(EntityModule:GetEntity(v.GoodsUID))
		end
	end
	EventManager:Fire(EventID.OnSkepGoodsChange, self.skepID, m)
end

-- 获取物品数量
function SkepBase:Count(goodID)
	local count = 0
	for i = 0, self.indexMaxsize do
		if self[i] then
			local entity = EntityModule:GetEntity(self[i])
			if entity then
				if entity:GetProperty(ENTITY_FIELD.ENTITY_FIELD_ID) == goodID then
					count = count + entity:GetProperty(GOODS_FIELD.GOODS_FIELD_QTY)
				end
			end
		end
	end
	return count
end

--查询空位数量
function SkepBase:EmptyCount()
	local count = 0
	for i = 0, self.indexMaxsize do
		if self[i] == nil then
			count = count + 1
		else
			local entity = EntityModule:GetEntity(self[i])
			if not entity then
				count = count + 1
			end
		end
	end
	return count
end

--快速计数
function SkepBase:MakeFastCount()
	local countList = {}
	local equipCountList = {}
	for i = 0, self.indexMaxsize do
		if self[i] then
			local entity = EntityModule:GetEntity(self[i])
			if entity then
				local goodsID = entity:GetProperty(ENTITY_FIELD.ENTITY_FIELD_ID)
				local count = entity:GetProperty(GOODS_FIELD.GOODS_FIELD_QTY)
				if countList[goodsID] == nil then
					countList[goodsID] = count
				else
					countList[goodsID] = countList[goodsID] + count
				end
				--装备类型的多保存一份绑定与非绑定数量
				if HelperL.IsEuipType(goodsID) then
					local flags = entity.flags
					if equipCountList[goodsID] == nil then
						equipCountList[goodsID] = {}
					end
					if equipCountList[goodsID][flags] == nil then
						equipCountList[goodsID][flags] = count
					else
						equipCountList[goodsID][flags] = equipCountList[goodsID][flags] + count
					end
				end
			end
		end
	end
	self.fastCount = countList
	self.fastEquipCount = equipCountList
	self.fastFrameCount = Time.frameCount
end

--查询空位数量(效率)
function SkepBase:FastCount(goodID)
	if Time.frameCount ~= self.fastFrameCount then
		self:MakeFastCount()
	end
	return self.fastCount[goodID] or 0
end

--道具变更
function SkepBase:OnGoodsPropChange(entity, propID, deltaValue)
	if Time.frameCount == self.fastFrameCount and propID == GOODS_FIELD.GOODS_FIELD_QTY then
		-- 更新fastcount缓存
		local goodsID = entity:GetProperty(ENTITY_FIELD.ENTITY_FIELD_ID)
		if self.fastCount[goodsID] then
			self.fastCount[goodsID] = self.fastCount[goodsID] + deltaValue
		end
	end
end

--获取物品UID
function SkepBase:GetGoodsUID(goodID)
	for i = 0, self.indexMaxsize do
		if self[i] then
			local entity = EntityModule:GetEntity(self[i])
			if entity then
				if entity:GetProperty(ENTITY_FIELD.ENTITY_FIELD_ID) == goodID then
					return self[i]
				end
			end
		end
	end
	return 0
end

-- 是否已满
function SkepBase:IsFull()
	for i = 0, self.indexMaxsize do
		if self[i] == nil or not EntityModule:GetEntity(self[i]) then
			return false
		end
	end
	return true
end

-- 是否为空
function SkepBase:IsEmpty()
	for i = 0, self.indexMaxsize do
		if self[i] then
			local entity = EntityModule:GetEntity(self[i])
			if entity then
				return false
			end
		end
	end
	return true
end

-- 通过位置获取物品实体
function SkepBase:GetEntityByPlace(place)
	return EntityModule:GetEntity(self[place])
end

-- 通过id获取物品实体
function SkepBase:GetEntityByGoodsID(goodsID)
	local entity
	for i = 0, self.indexMaxsize do
		if self[i] then
			entity = EntityModule:GetEntity(self[i])
			if entity then
				if entity:GetProperty(ENTITY_FIELD.ENTITY_FIELD_ID) == goodsID then
					return entity
				end
			end
		end
	end
	return nil
end

---创建物品篮子
---@param msg integer
---@return SkepBase
function CreateGoodsSkep(msg)
	local skep = {}
	setmetatable(skep, SkepBase)
	skep:New(msg)
	return skep
end
