FunctionOpenManager = {}

FunctionOpenManager.FunctionType =
{
	GameWindow = 1,				
	TowerBattle = 2,					
}
FunctionOpenManager.IsOpenFun = false

FunctionOpenManager.StageID = 6 --关卡ID大于6后将不检测功能开启
FunctionOpenManager.waitOpenList = HelperL.NewQueue():New()
FunctionOpenManager.openObjList = {}

--启动功能开启检测
function FunctionOpenManager.StartFunctionCheck(windowID)
	local self = FunctionOpenManager
	if windowID ~= WndID.TowerBattleMain then return end
	self.IsOpenFun = false
	if TowerBattleManager:GetStageStar(self.StageID) > 0 then return end
	if not self.funTimer then
		self.funTimer = Timer.New(self.CheckFunctionOpen, 1, -1)
		self.funTimer:Start()
	end	
end

function FunctionOpenManager.StopFunctionCheck(windowID)
	local self = FunctionOpenManager
	if windowID ~= WndID.TowerBattleMain then return end 
	if self.funTimer then
		self.funTimer:Stop()
		self.funTimer = nil
	end
	self.initCheckTime = nil
	self.openObjList = {}
end

--检测功能开启
function FunctionOpenManager.CheckFunctionOpen()
	local self = FunctionOpenManager
	local config = TowerBattleManager:GetCurStageConfig()
	if not config then return end
	local funList = {}
	for k,v in ipairs(Schemes.FunctionOpen.items) do
		if not self.openObjList[v] then
			if v.Parameter1 == config.ID and v.FunctionType == self.FunctionType.TowerBattle and TowerBattleManager:GetStageStar(v.Parameter1) <= 0 and not UIManager:IsWndOpen(WndID.TowerBattleCutScene) then
				self.waitOpenList:pushLast(v)
				self.openObjList[v] = true
			end
		end
	end	
	if self.IsOpenFun == false and self.waitOpenList:Count() > 0 then
		self.IsOpenFun = true
		local funConfig = self.waitOpenList:popFirst()
		UIManager:OpenWnd(WndID.FunctionOpen, funConfig)
	end
end

function FunctionOpenManager:OpenNextFunction()
	if self.IsOpenFun == false and self.waitOpenList:Count() > 0 then
		self.IsOpenFun = true
		local funConfig = self.waitOpenList:popFirst()
		UIManager:OpenWnd(WndID.FunctionOpen, funConfig)
	end
end

--检测模块是否开启
function FunctionOpenManager:IsOpenFunction(WndID)
	for k,v in ipairs(Schemes.FunctionOpen.items) do
		if v.FunctionType == self.FunctionType.GameWindow and v.WindowID == WndID then
			return TowerBattleManager:GetStageStar(v.Parameter1) > 0 
		end
	end
	return true
end

--是否开启场景物件
function FunctionOpenManager:IsOpenBattleObject(objectType, index)
	if TowerBattleManager:GetStageStar(self.StageID) > 0 then return true end
	if not self.initCheckTime then self.initCheckTime = HelperL.GetServerTime() end
	local funConfig = Schemes.FunctionOpen:GetFunctionOpenByParams(2, objectType, index)
	if not funConfig then return true end
	local levelID = funConfig.Parameter1
	local time = funConfig.Parameter2
	if TowerBattleManager:GetStageStar(levelID) > 0 then 
		return true
	end
	local config = TowerBattleManager:GetCurStageConfig()
	if not config then return false end
	if levelID > config.ID then return false end	
	local serverTime = HelperL.GetServerTime()
	local startTime = serverTime - self.initCheckTime
	if startTime > time then
		return true
	end
	return false
end

EventManager:Subscribe(EventID.WindowOpen, FunctionOpenManager.StartFunctionCheck)
EventManager:Subscribe(EventID.WindowClose, FunctionOpenManager.StopFunctionCheck)