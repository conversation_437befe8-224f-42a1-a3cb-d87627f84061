local luaID = ('UICarnivalRechargeBuy')

-- 初始化
local function OnCreate(self, container, buyItem, needAtvID, parentLua)
	self.needAtvID = needAtvID
	self.buyItem = buyItem
	self.parentLua = parentLua
	self.buyItem.gameObject:SetActive(false)
	self.itemsContent = container:GetRectTransform()
	self.btnList = {}
	self.buyItemList = {}
	self.buyConfigList = {}
	self.discontEmptyItemList = {}
	self.lastClickTime = 0
	for k, v in ipairs(Schemes.RechargeCard.items) do
		if v.NeedActvID == needAtvID then
			table.insert(self.buyConfigList, v)
		end
	end

	for i,v in ipairs(self.buyConfigList) do
		local buyItem = self.parentLua:CreateSubItem(self.itemsContent, self.buyItem)
		buyItem.config = self.buyConfigList[i]
		table.insert(self.buyItemList, buyItem)
		table.insert(self.btnList, buyItem.Btn_BuyGrowGift)
		table.insert(self.btnList, buyItem.Btn_Instro)
		buyItem.Btn_BuyGrowGift.onClick:AddListenerEx(function () 
			self:OnClickBuy(buyItem)
		end)
		buyItem.Btn_Instro.onClick:AddListenerEx(function () 
			self:OnClickInstro(buyItem)
		end)
		buyItem.Txt_GrowRMB.text = '￥' .. v.FirstRMB/100
		buyItem.Txt_GrowGiftItemTitle.text = buyItem.config.CardName
		buyItem.Txt_GrowBuyed.text = GetGameText(luaID, 5)
		AtlasManager:AsyncGetSprite(buyItem.config.FirstPic1, buyItem.Img_GrowGiftIcon)
		buyItem.gameObject:SetActive(true)
		buyItem.Txt_GrowItemDesc.text = string.gsub(v.FirstCharacter1,'\\n','\n')
		if self.parentLua.ShowGiftThreeItemIcon ~= nil then
			self.parentLua.ShowGiftThreeItemIcon(buyItem)
		end
	end
	
	self:AddEmptyDiscountItem()
	return true 
end

local function UpdateView(self)	
	for k,v in ipairs(self.buyItemList) do
		local canReceive = HeroDataManager:GetLogicByteTwo(v.config.CountLogicID, v.config.CountLogicIndex)
		print('v.config = '..v.config.ID.."   canReceive:"..canReceive..'  CountLogicID'..v.config.CountLogicID..'   CountLogicIndex'..v.config.CountLogicIndex)
		if canReceive < v.config.CanBuyCount then
			if v.Txt_GrowRMB.text == "0" then
				v.Img_GrowRMB.gameObject:SetActive(false)
				v.Txt_GrowRMB.gameObject:SetActive(false)
				if v.Img_ad then
					v.Img_ad.gameObject:SetActive(true)
				end
			else
				--v.Img_GrowRMB.gameObject:SetActive(true)
				v.Txt_GrowRMB.gameObject:SetActive(true)
			end
			v.Txt_GrowBuyed.gameObject:SetActive(false)
			v.Btn_Instro.gameObject:SetActive(true)
		else
			v.Img_GrowRMB.gameObject:SetActive(false)
			v.Txt_GrowRMB.gameObject:SetActive(false)
			v.Txt_GrowBuyed.gameObject:SetActive(true)
			v.Btn_Instro.gameObject:SetActive(false)
		end
	end
end

local function AddEmptyDiscountItem(self)
	local numcount = #self.buyItemList
	local discontEmptyItemList = self.discontEmptyItemList
	for i=1,8-numcount do
		local discountItem = discontEmptyItemList[i]
		if not discountItem then
			local discountItem = {}
			local obj = self.parentLua:CreateSubItem(self.itemsContent, self.buyItem)
			discountItem = obj.transform
			--local objTrans = obj.transform:GetRectTransform()
			--Helper.FillLuaComps(objTrans, discountItem)
			--discountItem.gameObject = obj
			--discountItem.objTrans = objTrans
			
			table.insert(discontEmptyItemList, discountItem)
			local AllNode = obj.gameObject:GetRectTransform()
			for i = 1, AllNode.transform.childCount do
				AllNode.transform:GetChild(i - 1).gameObject:SetActive(false)
			end
			discountItem.gameObject:SetActive(true)
			local img_empty = discountItem.transform:Find('Img_empty')
			if img_empty then img_empty.gameObject:SetActive(true) end
		end

	end
	self.discontEmptyItemList = discontEmptyItemList
end

-- 点击领取奖励
local function OnClickBuy(self, buyItem)
	if not buyItem then return end
	if self.lastClickTime > os.time() then
		HelperL.ShowMessage(TipType.FlowText, string.format(GetGameText(luaID, 4), math.floor(self.lastClickTime - os.time())))
		return
	end	
	local canReceive = HeroDataManager:GetLogicByteTwo(buyItem.config.CountLogicID, buyItem.config.CountLogicIndex)
	--print(canReceive .. "   " .. buyItem.config.CanBuyCount)
	if canReceive >= buyItem.config.CanBuyCount then 
		HelperL.ShowMessage(TipType.FlowText, GetGameText(luaID, 3))
		return 
	end
	self.lastClickTime = os.time()+10
	if buyItem.config.FirstRMB == 0 then
		self:RequestBuyZero(buyItem.config.ID)
	else
		HelperL.Recharge(buyItem.config.ID)
	end
end

local function OnClickInstro(self, buyItem)
	if not buyItem then return end	
	UIManager:OpenWnd(WndID.ShowBoxInfo, buyItem.config.ShowGiftID, buyItem.config.PrizeID, buyItem.config.FirstPic1, buyItem.config.CardName, GetGameText(luaID, 2), nil, nil)
end

local function RequestBuyZero(self,cardID)
	local str_req = string.format("LuaRequestRechargeCardByZero?CardID=%s", cardID)
	LuaModule.RunLuaRequest(str_req, function(resultCode, content)
			if resultCode ~= RESULT_CODE.RESULT_COMMON_SUCCEED then
				HelperL.ShowMessage(TipType.FlowText, ResultCode:GetResultCodeStr(resultCode, content))
			else
				HelperL.ShowMessage(TipType.FlowText, ResultCode:GetResultCodeStr(resultCode, content))
				self:UpdateView()
			end
		end)
end

 
-- 窗口关闭
local function OnClose()
	
end

-- 每秒更新
local function OnSecondUpdate(self)
	
end


-- 窗口销毁
local function OnDestroy(self)
	-- 释放内存
	for i, btn in ipairs(self.btnList) do
		btn.onClick:RemoveAllListeners()
		btn.onClick:Invoke()
	end
end

function CreateCarnivalRecharge(container, buyItem, needAtvID, parentLua)
	local item = {}
	item.OnCreate = OnCreate
	item.OnDestroy = OnDestroy
	item.OnSecondUpdate = OnSecondUpdate
	item.UpdateView = UpdateView
	item.OnClickBuy = OnClickBuy
	item.RequestBuyZero = RequestBuyZero
	item.OnClickInstro = OnClickInstro
	item.AddEmptyDiscountItem = AddEmptyDiscountItem
	if not item:OnCreate(container, buyItem, needAtvID, parentLua) then
		warn('CreateCarnivalRecharge 初始化失败')
		return nil
	end
	
	return item
end