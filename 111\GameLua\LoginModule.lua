-- 登录模块
require 'AgentMessage_pb'
require 'CenterMessage_pb'
require 'LoginMessage_pb'

local luaID = ('LoginModule')

GameChannel = {
	ChannelNone = 0,
	Channel_waiwang = 2,
	Channel_TapTap = 3,             --taptap-安卓
	Channel_IOS_TapTap = 4,         --taptap-IOS
	Channel_XiongMaoXiuXian_quick = 5, --熊猫休闲-quick-安卓
	Channel_Youyi = 6,              --游易
	Channel_Yimei = 7,              --易美

	Channel_Max
}

YiMeiGameChannel = {
	yimeiChannel_None = 0, --无
	yimeiChannel_TapTap = 1, --tt
	yimeiChannel_TouTiao = 2, --头条
	yimeiChannel_DouYin = 3, --抖音
}

LoginModule = {}
LoginModule.name = 'LoginModule'
LoginModule.isLoginOK = false

local pwdUrl = "https://zl-wsr.0037wan.com/charge62/Login/";

function LoginModule.Handle(action, data)
	local m
	if action == LoginMessage_pb.MSG_LOGIN_LOGINUSER then --登录账号
		m = LoginMessage_pb.SC_Login_LoginUser()
		m:ParseFromString(data)
		LoginModule:SC_Login_LoginUser(m)
	elseif action == LoginMessage_pb.MSG_LOGIN_LOGOUTUSER then -- 登出账号
		m = LoginMessage_pb.LC_Login_LogoutUser()
		m:ParseFromString(data)
		LoginModule:SC_Login_LogoutUser(m)
	end
end

function LoginModule:SC_Login_LoginUser(m)
	--账号验证完毕
	if m.Result ~= RESULT_CODE.RESULT_COMMON_SUCCEED then
		warn('SC_Login_LoginUser 登录失败', m.Result)
		HelperL.ShowMessage(TipType.FlowText, GetGameText(luaID, 1))
	end
end

function LoginModule:SC_Login_LogoutUser(m)
	if m.Result == RESULT_CODE.RESULT_SELECTACTOR_KICKOUT then
		--GameModule:OnReceiveHeartBeat()
		-- 被踢出
		--local btnInfoList = {}
		--table.insert(btnInfoList, { name = GetGameText(luaID, 3), countDown = 5, light = true, callbackFunc = function() UIManager.Go2SelectZone() end })
		--UIManager.AddMessageBOXCommon(GetGameText(luaID, 7), btnInfoList, nil, nil, true)
	end
end

function LoginModule.OnGetRolesCallback(www)
	local self = LoginModule
	if www == nil or www.text == nil or www.text == "" then
		print('Login failed')
		return
	end
	print('OnGetRolesCallback------www.text=', www.text)
	local selectZone = nil
	local m = dkjsonHelper.decode(www.text)
	--获取有角色的区ID
	if m.data and #m.data ~= 0 then
		for i, v in pairs(m.data) do
			if v.zoneId > 0 then
				selectZone = v.zoneId
				break
			end
		end
	end

	if selectZone then
		local selectZoneExist = false
		for _, v in ipairs(self.fineZoneList) do
			if selectZone == v then
				selectZoneExist = true
				break;
			end
		end
		if not selectZoneExist then
			selectZone = nil
			warn("角色之前的服务器选区不在zone表里，改成使用zone表推荐区")
		end
	end

	--使用推荐区
	if not selectZone then
		local fineZoneList = FineZoneList
		if not fineZoneList then
			fineZoneList = self.fineZoneList
		end
		if #fineZoneList == 0 then
			warn('LoginModule.OnGetRolesCallback #fineZoneList == 0')
			return
		end
		--随机推荐区
		local rand = math.random(1, #fineZoneList)
		selectZone = fineZoneList[rand]
	end

	local loginAddr = self.GetLoginServerAddress()

	local gameChannelID = 0
	if GameLuaAPI.GameChannelID then gameChannelID = GameLuaAPI.GameChannelID end
	--通用参数
	local form = {}
	form['username'] = GameLuaAPI.Uid
	form['usersource'] = gameChannelID
	form['uuid'] = Helper.GetInstallationUUID()
	form['zoneid'] = selectZone
	local extra = { ['sdkid'] = '0' }
	--if GameLuaAPI.SdkLoginParam ~= nil then extra['token'] = GameLuaAPI.SdkLoginParam end
	--quick  获取分包渠道ID
	if GameLuaAPI.Channel == GameLuaAPI.eChannel.eChannel_XiongMaoXiuXian_quick then
		extra["sdkid"] = GameLuaAPI.SdkLoginParam
	end
	--if GameLuaAPI.Channel == GameLuaAPI.eChannel.eChannel_Youyi then
	--extra['sdkid'] = '1214'
	--end
	local equipmentCode = GameLuaAPI.getUUID()
	extra['EquipmentCode'] = '0'
	if equipmentCode ~= nil or equipmentCode ~= '' then
		extra['EquipmentCode'] = GameLuaAPI.Uid .. gameChannelID .. equipmentCode
	end
	form['extra'] = json.encode(extra)
	print('lua_loginAdress:', loginAddr)
	print('username:', GameLuaAPI.Uid)
	print('usersource:', gameChannelID)
	print('uuid:', Helper.GetInstallationUUID())
	print('zoneid:', selectZone)
	print('extra:', json.encode(extra))
	HttpRequestHelper.HttpPostRaw(loginAddr .. 'Login', form, LoginModule.OnLoginCallback)
end

function LoginModule:PostProcessZoneList()
	if not Zones then
		return
	end
	-- if ZonesEx and HelperL.IsSpecialDevice() then
	-- 	for i, v in pairs(ZonesEx) do
	-- 		table.insert(Zones, v)
	-- 	end
	-- end
	local curTime = self.CurTime
	if not curTime then
		curTime = os.time()
	end
	local timeZoneOffset = 0
	local skipZoneList = {}
	local zoneidToIndex = {}
	local zoneidToItem = {}
	local maxZoneItem = nil
	local maxZoneIndex = -1
	local newestServerItem = nil
	local newestServerTime = 2 * 60 * 60
	local fineZoneList = {}
	for i, v in pairs(Zones) do
		local key = tonumber(i)
		if key and key ~= 0 and v[1] ~= nil then
			v.index = v[1]
			v.id = v[2]
			v.url = v[3]
			v.ip = v[4]
			v.port = v[5]
			v.lanIP = v[6]
			v.name = v[7]
			v.state = tonumber(v[8])
			v.flag = 0 --tonumber(v[9])
			v.notice = v[10]
			v.closetime = v[11]

			zoneidToItem[v.id] = v

			-- local skipZone = false
			-- local openTime = v[11]
			-- if openTime and string.len(openTime) > 1 then
			-- 	local splitTime = HelperL.Split(openTime, '-')
			-- 	if splitTime and #splitTime == 5 then
			-- 		local finalOpenTime = os.time({
			-- 			year = splitTime[1],
			-- 			month = splitTime[2],
			-- 			day = splitTime[3],
			-- 			hour =
			-- 				splitTime[4],
			-- 			min = splitTime[5],
			-- 			sec = 0
			-- 		})
			-- 		finalOpenTime = finalOpenTime + timeZoneOffset
			-- 		if curTime < finalOpenTime then
			-- 			table.insert(skipZoneList, i)
			-- 			skipZone = true
			-- 		end
			-- 	end
			-- end

			-- local blockTime = v[12]
			-- if not skipZone and v.state == 4 and blockTime and string.len(blockTime) > 1 then
			-- 	local splitTime = HelperL.Split(blockTime, '-')
			-- 	if splitTime and #splitTime >= 5 then
			-- 		local finalBlockTime = os.time({
			-- 			year = splitTime[1],
			-- 			month = splitTime[2],
			-- 			day = splitTime[3],
			-- 			hour =
			-- 				splitTime[4],
			-- 			min = splitTime[5],
			-- 			sec = 0
			-- 		})
			-- 		finalBlockTime = finalBlockTime + timeZoneOffset
			-- 		if curTime < finalBlockTime then
			-- 			v.state = 3
			-- 			v.blockTime = splitTime
			-- 			skipZone = true

			-- 			local deltaTime = finalBlockTime - curTime
			-- 			if deltaTime < 2 * 60 * 60 and deltaTime < newestServerTime then
			-- 				newestServerTime = deltaTime
			-- 				newestServerItem = v
			-- 			end
			-- 		else
			-- 			v.state = 1
			-- 		end
			-- 	end
			-- end

			-- if not skipZone then
			-- 	if v.index > maxZoneIndex then
			-- 		maxZoneIndex = v.index
			-- 		maxZoneItem = v
			-- 	end
			-- end

			if v.state == 1 then
				table.insert(fineZoneList, v.id)
			end

			zoneidToIndex[v.id] = v.index
		end
	end
	self.fineZoneList = fineZoneList
	ZoneIDToIndex = zoneidToIndex
	ZoneIDToItem = zoneidToItem

	--if maxZoneItem then
	--	maxZoneItem.flag = 2
	--	Zones.default = maxZoneItem.index
	--end

	for i, v in ipairs(skipZoneList) do
		Zones[v] = nil
	end
end

--加载服务器列表
function LoginModule.LoadZoneList(www)
	if not www then
		warn('LoadZoneList not www')
		return
	end

	local self = LoginModule
	local errStr = www.error
	print('LoadZoneList---------www.error=', errStr)
	if errStr and string.len(errStr) > 0 then
		-- 没下载成功时换地址
		-- if self.zoneUrlIndex < 3 then
		-- 	self.zoneUrlIndex = self.zoneUrlIndex + 1
		-- 	if self.zoneUrlIndex == 2 then
		-- 		print('下载区列表1', GameGlobal.url_zone2_)
		-- 		HttpRequestHelper.AddWWWBasic(GameGlobal.url_zone2_, self.LoadZoneList, 1)
		-- 		return
		-- 	else
		-- 		print('下载区列表2', GameGlobal.url_zone3_)
		-- 		HttpRequestHelper.AddWWWBasic(GameGlobal.url_zone3_, self.LoadZoneList, 1)
		-- 		return
		-- 	end
		-- end

		--使用默认地址
		local zoneurl = LoginModule.GetZoneUrl(GameChannel.ChannelNone)
		HttpRequestHelper.HttpGet(zoneurl, self.LoadZoneList, 1)
		return
	end
	print('LoadZoneList---------www.text=', www.text)
	local text = www.text
	if www.isDone and text ~= nil and text ~= '' then
		-- 先解密
		if GameGlobal.needMix then
			text = Helper.DecodeWWWText(www.data, GameGlobal.abFileKey)
		end

		loadstring(text)()
		if ZoneReplaceUrl then
			local url = string.format('%s?t=%d', ZoneReplaceUrl, HelperL.GetServerTime())
			ZoneReplaceUrl = nil
			print('服务器列表下载地址跳转', url)
			HttpRequestHelper.HttpGet(url, self.LoadZoneList, 1)
			return
		end
		--UnityEngine.PlayerPrefs.SetString('LuaZone', text)
		self:PostProcessZoneList()
		--是否使用本地时间
		local isUseLocalTime = GameLuaAPI.GetPlatform() == "unityEditor"
		-- 加载成功后开始同步服务器时间
		Helper.StartSyncServerTime(300, isUseLocalTime);

		local form = {}
		form['username'] = GameLuaAPI.Uid
		HttpRequestHelper.HttpPostRaw(LoginModule.GetLoginServerAddress() .. 'getroles', form, self.OnGetRolesCallback)
		-- else
		-- 	self.UpdateView()
	end
end

-- 执行登录
function LoginModule:DoLogin()
	local loginAddr = self.GetLoginServerAddress()
	print('LoginServerAddress', loginAddr)

	-- 尝试登录
	if SceneManager.isLoading then return end
	if GameLuaAPI.Uid == nil or GameLuaAPI.Uid == "" then
		GameLuaAPI.SDKLogin()
		return
	end
	local curTime = UnityEngine.Time.time
	if self.lastLoginTime and curTime - self.lastLoginTime < 5.0 then
		warn('LoginModule:DoLogin 频繁登录')
		return
	end
	self.lastLoginTime = curTime

	self.zoneUrlIndex = 1
	local zoneurl = LoginModule.GetZoneUrl()
	print('下载区列表', zoneurl)
	HttpRequestHelper.HttpGet(zoneurl, nil, self.LoadZoneList)
	HelperL.ShowBlackWaitingUI(1, 30.0, nil, "登录中……")
end

--	sdk登陆成功
function LoginModule.LoginOK(str)
	local self = LoginModule
	self.isLoginOK = true
end

-- 登录回调
function LoginModule.OnLoginCallback(www)
	print('登录回调:', json.encode(www));
	if www == nil or www.text == nil or www.text == '' then
		print('OnLoginCallback Login failed')
		if www ~= nil and www.error ~= nil then print('OnLoginCallback Login error:' .. www.error) end
		return
	end

	print('lua_loginback_www.text:' .. www.text)

	local self = LoginModule
	local m = dkjsonHelper.decode(www.text) or {}
	if m.Result == -2 then
		local splitTime = HelperL.Split(m.ExpireTime, 'T')
		local timeStr = splitTime[1] .. ' ' .. splitTime[2]
		HelperL.ShowMessage(TipType.FlowText, string.format(GetGameText(luaID, 2), timeStr))
		return
	end

	-- 获取Pwd
	local form = {}
	form['ApiVersion'] = 'V1'
	form['UserID'] = m.UserID
	form['UserPwd'] = LoginModule._p_s_d
	HttpRequestHelper.HttpPost(pwdUrl .. 'GetUserPwd', form, function(Rsp)
		if Rsp == nil or Rsp.text == nil or Rsp.text == '' then
			if Rsp then
				print('GetUserPwd error:' .. Rsp.error)
				HelperL.ShowMessage(TipType.FlowText, Rsp.error)
			else
				HelperL.ShowMessage(TipType.FlowText, '登入失败')
			end
			return
		end

		local pwdRtn = dkjsonHelper.decode(Rsp.text)
		local isPC = GameLuaAPI.ChannelID == GameChannel.ChannelNone
		if not isPC or (pwdRtn.Success and pwdRtn.Value == LoginModule._p_s_d) then
			if self:CheckCanLogin(m) then
				self:SetZoneID(m.ZoneID)
				self:SetGameID(m.GameID)
				self:SetUserID(m.UserID)
				self:SetCPToken(m.CPToken)
				local actorList = nil
				if m.Actors ~= "" then
					actorList = dkjsonHelper.decode(m.Actors)
				else
					actorList = {}
				end
				self:SetActorList(actorList)

				--HelperL.CloseBlackWaitingUI()
				if not SWITCH.CERTIFICATION then
					HelperL.CloseBlackWaitingUI()
					UIManager:OpenWnd(WndID.Certification, function()
						--执行登入回调事件
						EventManager:Fire(EventID.LoginCallback)
					end)
				else
					--执行登入回调事件
					EventManager:Fire(EventID.LoginCallback)
				end
			end
		else
			HelperL.CloseBlackWaitingUI()
			HelperL.ShowMessage(TipType.FlowText, '密码不正确！')
		end
	end)
end

--检测能否登陆
function LoginModule:CheckCanLogin(data)
	--白名单上 不管维护还是关闭都可以进入
	if data.IsWhiteList then

	else
		--[[
		local btnInfoList = {}
		table.insert(btnInfoList, {name = GetGameText(luaID, 75)})
		local noticeText = Zone_DefaultTip3 or GetGameText(luaID, 76)
		if Zones[data.ZoneID].state == 3 then
			--维护中，不能进入
			if Zones[data.ZoneID].notice ~= nil and string.len(Zones[data.ZoneID].notice) > 0 then
				noticeText = Zones[data.ZoneID].notice
			end
			UIManager.AddMessageBOXCommon(noticeText, btnInfoList)
			return false
		else
			if data.IsZoneClose and data.ActorNum <= 0 then
				--关闭中，并且该区没有细胞不能进
				noticeText = GetGameText(luaID, 170)
				UIManager.AddMessageBOXCommon(noticeText, btnInfoList)
				return false
			end
		end
		]]
	end
	return true
end

-- 创建新细胞
function LoginModule:CreateActor(actorName, callback)
	if SceneManager.isLoading then
		return
	end

	--if UIActor.createActor.inputName.value == 'zl9527' then
	--	Helper.OpenTestLog()
	--	HelperL.AddAMessageTip(HelperL.GetResultCodeStr(RESULT_CODE.RESULT_CREATEACTOR_NAMEEXISTS.id))
	--	return
	--end

	--通用参数
	local form = {}
	form['userid'] = self:GetUserID()
	form['zoneid'] = self:GetZoneID()
	form['actorname'] = actorName
	form['vocation'] = 1
	form['bodycolor'] = 0
	HttpRequestHelper.HttpPostRaw(self.GetLoginServerAddress() .. 'AddActor', form, callback)
end

-- 创建新细胞数据处理
function LoginModule:AddActorData(data)
	self.ActorList = self.ActorList or {}
	table.insert(self.ActorList, data)
end

-- 删除细胞
function LoginModule:DeleteActor()
	local actorList = self:GetActorList()
	if not actorList then
		return
	end

	if #actorList == 0 then
		return
	end

	-- 自动选第一个细胞
	local actorData = actorList[1]
	local form = {}
	form["userid"] = self:GetUserID()
	form["actorid"] = actorData.ActorID
	form["actorname"] = actorData.ActorName
	form["zoneid"] = self:GetZoneID()
	HttpRequestHelper.HttpPostRaw(self.GetLoginServerAddress() .. 'DeleteActor', form, self.DeleteActorCallBack)
end

-- 删除细胞回调
function LoginModule.DeleteActorCallBack(www)
	local m = dkjsonHelper.decode(www.text)
	if m.result == 0 then
		HelperL.ShowMessage(TipType.FlowText, GetGameText(luaID, 4))
	end
end

-- 选择细胞
function LoginModule:SelectActor(actorData)
	if SceneManager.isLoading then
		return
	end

	-- 显示等待UI
	--SceneManager:CheckLevelDownloadPack(actorData.Level)
	if SceneManager.isShowingForceDownBox then
		-- 有需要强制下载的就先不进游戏了
		return
	end

	self:SetSelectActorData(actorData)
	LoginModule:ConnectZone()
end

-- 连接服务器
function LoginModule:ConnectZone()
	local self = LoginModule;
	-- 没加载完的部分全读掉
	Schemes.LateLoadAllScheme(true)

	if GameLuaAPI.Uid == nil or GameLuaAPI.Uid == "" then
		GameLuaAPI.SDKLogin()
		return
	end

	--连接网关服
	local zoneID = self:GetZoneID()
	local zoneData = nil
	for i, v in pairs(Zones) do
		if v.id == zoneID then
			zoneData = v
			break
		end
	end

	if not zoneData then
		warn('LoginModule:ConnectZone not zoneData', zoneID)
		return
	end

	Helper.tZoneID_ = zoneID
	local actorData = self:GetSelectActorData()

	local loginParam = GsProxy.JsonParams.LoginParam.New();
	loginParam.Server = zoneData.ip;
	loginParam.LanIp = zoneData.lanIP;
	loginParam.Port = zoneData.port;
	loginParam.ActorHadBorn = EntityModule.hadBorn;
	loginParam.UserID = self:GetUserID();
	loginParam.ZoneID = self:GetZoneID();
	loginParam.ActorID = actorData.ActorID;
	loginParam.ActorName = actorData.ActorName;
	loginParam.Token = self:GetCPToken();

	local objNetwork = Premier.Instance:GetNetwork();
	objNetwork:SetClient(zoneData.url, loginParam);
	objNetwork:Connect()
end

-- 获取心跳检测数据(由C#调用。前提：已登录)
function LoginModule.Provider_HeartBeatData()
	local data = Premier.Instance:GetNetwork():BuildSendData(
		ENDPOINT.ENDPOINT_GAMECLIENT,
		ENDPOINT.ENDPOINT_GAMESERVER,
		MSG_MODULEID.MSG_MODULEID_GAME,
		GameMessage_pb.MSG_GAME_HEARTBEAT
	);
	return data;
end

-- 提供登录数据(由C#调用，用于角色登录。前提：已连接)
function LoginModule.Provider_LoginData()
	local self = LoginModule;
	local actorData = self:GetSelectActorData()
	local m = AgentMessage_pb.CA_Agent_Player_Selete_Zone()
	m.ActorID = actorData.ActorID
	m.ActorName = actorData.ActorName
	m.UserID = self:GetUserID()
	m.ZoneID = self:GetZoneID()
	m.Token = self:GetCPToken()

	local data = Premier.Instance:GetNetwork():BuildSendData(
		ENDPOINT.ENDPOINT_GAMECLIENT,
		ENDPOINT.ENDPOINT_AGENTSERVER,
		MSG_MODULEID.MSG_MODULEID_AGENT,
		AgentMessage_pb.MSG_AGENT_PLAYER_SELECT_ZONE,
		m:SerializeToString()
	);
	return data;
end

-- 连接服务器成功
function LoginModule.OnConnected()
	print('LoginModule.OnConnected -- 连接成功');
	local self = LoginModule;
	local actorData = self:GetSelectActorData()
	local form = {}
	form["userid"] = self:GetUserID()
	form["actorid"] = actorData.ActorID
	form["actorname"] = actorData.ActorName
	form["zoneid"] = self:GetZoneID()
	form["loginid"] = self:GetZoneID()
	form["clientuid"] = Helper.GetInstallationUUID()
	HttpRequestHelper.HttpPostRaw(LoginModule.GetLoginServerAddress() .. 'SelectActor', form, function() end)
end

function LoginModule.OnConnectError(ex)
	-- 非战斗状态,才弹出失败提示
	if (not EntityModule.luaToCshape.IsFighting) then
		HelperL.CloseBlackWaitingUI()
		--HelperL.ShowMessage(TipType.FlowText, ex.Message)
		HelperL.ShowMessage(TipType.FlowText, "连接失败")
	end
end

function LoginModule.OnConnectTimeout()
	HelperL.CloseBlackWaitingUI()
	HelperL.ShowMessage(TipType.FlowText, "连接超时")
end

--切换区
function LoginModule.SwitchZone(zoneid)
	warn('跨区功能待整理')
	--[[
	LoginModule.isReconnecting = true
	GlobalGameDataCenter.isSwitchingGlobalServer = true
	SceneManager:ShowLoadingUI(true)
	--local tempBak = LoginModule
	--HelperL.ClearGameData() --重置数据
	--LoginModule = tempBak
	-- 删除其他实体
	local destroyList = {}
	for i, v in pairs(EntityModule.entityList) do
		if v and v.tag ~= 'Hero' then
			table.insert(destroyList, v.uid)
		end
	end
	for i, v in ipairs(destroyList) do
		EntityModule.DestroyEntity(v)
	end
	if EntityModule.hero then
		if EntityModule.hero.npcFollowPos and EntityModule.hero.npcFollowPos > 0 then
			EntityModule.hero.npcFollowPos = 0
			EntityModule.UpdateFollowNpcPos(EntityModule.hero)
		end
		HeroTaskLC.Clean()
		EventManager:Fire(EventID.EscortControlEvent,false,false)
	end

	GameModule.lastHeartBeatTime = nil
	
	HelperL.ZoneID = zoneid
	]]
end

function LoginModule.Logout()
	--[[
	if SceneManager.curSceneName == 'SelectZone' then
		--SceneManager.LoadScene('SelectZone')
	else
		UIManager.mainlandUI = nil
		Premier.Instance:GetNetwork():OnConnectClose()
		HelperL.ClearGameData()
		SceneManager.isLogin = false
		SceneManager:LoadSceneWithLoading('SelectZone', true, false)
	end
	]]

	-- 保存后移除角色数据(单例)
	EntityModule.ActorDataMgr:SaveAndRemove();

	EntityModule.hadBorn = false
	UIManager.mainlandUI = nil
	SceneManager.islogOut = true
	-- 关闭长连接(会自动停用断线重连)
	Premier.Instance:GetNetwork():Close()
	HelperL.ClearGameData()
	UIManager.loginType = 1
	GameLuaAPI.Uid = ""
	--UIManager.OnLevelWasLoaded('GameLogin')
	SceneManager:LoadSceneWithLoading('GameLogin', true, false)
end

function LoginModule.Exit()
	--[[
	local btnInfoList = {}
	local confirmFunc = function()
		GameLuaAPI.QuitGame()
	end
	table.insert(btnInfoList, { name = GetGameText(luaID, 2)})
	table.insert(btnInfoList, { name = GetGameText(luaID, 3), light = true, callbackFunc = confirmFunc })
	UIManager.AddMessageBOXCommon(GetGameText(luaID, 4), btnInfoList)
	]]
end

--登录网址
local loginUrl = {
	--默认地址
	[GameChannel.ChannelNone] = "http://************/Login62/",
	[GameChannel.Channel_waiwang] = "http://************/Login62/",
	[GameChannel.Channel_TapTap] = "http://************/Login62/",
	[GameChannel.Channel_IOS_TapTap] = "http://************/Login62/",
	[GameChannel.Channel_XiongMaoXiuXian_quick] = "http://************/Login62/",
	[GameChannel.Channel_Youyi] = "http://************/Login62/",
	[GameChannel.Channel_Yimei] = "http://************/Login62/",
}

---获取登录服务器地址
---@param channelID? integer
---@return string 登录地址
function LoginModule.GetLoginServerAddress(channelID)
	-- 内网测试用
	--return "http://************/Login62/"
	-- 外网正式
	--return "http://************/Login62/"
	return loginUrl[channelID or GameLuaAPI.ChannelID] or loginUrl[GameChannel.ChannelNone]
end

--服务器列表下载地址
-- local zoneUrl = {
-- 	--默认地址
-- 	[GameChannel.ChannelNone] = GameGlobal.cdnUrl,
-- 	[GameChannel.Channel_waiwang] = GameGlobal.cdnUrl,
-- 	[GameChannel.Channel_TapTap] = GameGlobal.cdnUrl,
-- 	[GameChannel.Channel_IOS_TapTap] = GameGlobal.cdnUrl,
-- 	[GameChannel.Channel_XiongMaoXiuXian_quick] = GameGlobal.cdnUrl,
-- 	[GameChannel.Channel_Youyi] = GameGlobal.cdnUrl,
-- 	[GameChannel.Channel_Yimei] = GameGlobal.cdnUrl,


-- }

---获取服务器列表下载地址
---@param channelID? integer 渠道ID
---@return string 下载地址
function LoginModule.GetZoneUrl(channelID)
	local serverTime = HelperL.GetServerTime()
	-- local url = zoneUrl[channelID or GameLuaAPI.ChannelID] or zoneUrl[GameChannel.ChannelNone]

	local url = "https://kjygxbzyf.emaygames.cn/deer/XHRWX/Zone.lua"
	if GameLuaAPI.GetPlatform() == "unityEditor" then
		url = 'file:///' .. UnityEngine.Application.dataPath .. '/Temp/GameLua/Zone.lua'
	elseif GameLuaAPI.GetPlatform() == "android" then
		url = "http://43.136.66.30/deer/banhao/banhao10/Zone.lua"    ---安装包服务器列表
	elseif GameLuaAPI.GetPlatform() == "webgl" then
		url = "https://kjygxbzyf.emaygames.cn/deer/gfxg/Wechat/Zone.lua"
	end
	return url .. '?t=' .. serverTime
end

HelperL.ClassAddVar(LoginModule, 'UserID')
HelperL.ClassAddVar(LoginModule, 'ZoneID')
HelperL.ClassAddVar(LoginModule, 'CPToken')
HelperL.ClassAddVar(LoginModule, 'GameID')
HelperL.ClassAddVar(LoginModule, 'ActorList')
HelperL.ClassAddVar(LoginModule, 'SelectActorData')

Premier.Instance:RegisterLuaModule(MSG_MODULEID.MSG_MODULEID_LOGIN, 'LoginModule.Handle')
