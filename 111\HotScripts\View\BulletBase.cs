﻿// ReSharper disable InconsistentNaming
// ReSharper disable IdentifierTypo

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;

using Apq.Unity3D.Extension;
using Apq.Unity3D.UnityHelpers;

using Cysharp.Threading.Tasks;

using DG.Tweening;

using RxEventsM2V;

using Thing;

using UnityEngine;

using ViewModel;

using X.PB;

namespace View
{
    /// <summary>
    ///     单个子弹/一条激光
    /// </summary>
    public abstract class BulletBase : ThingBehaviour
    {
        /// <summary>
        ///     子弹物件(数据)
        /// </summary>
        public BulletThing BulletThing => Thing as BulletThing;

        /// <summary>
        ///     发出子弹的枪
        /// </summary>
        public GunThing GunThing => BulletThing?.CdExecutor?.Thing as GunThing;

        /// <summary>
        ///     存放轨迹的根节点
        /// </summary>
        public GameObject LocusRoot { get; set; }

        /// <summary>
        ///     子弹图片所在节点
        /// </summary>
        public GameObject ImgElement { get; set; }

        /// <summary>
        ///     子弹的图片
        /// </summary>
        public SpriteRenderer SpriteRenderer { get; set; }

        /// <summary>
        ///     子弹飞行特效预制体所在节点
        /// </summary>
        public GameObject PrefabElement { get; set; }

        /// <summary>
        ///     子弹飞行特效预制体实例
        /// </summary>
        public GameObject PrefabInstance { get; set; }

        /// <summary>
        ///     子弹的直线移动轨迹(线段)
        /// </summary>
        public List<LineSegment> StraightLocus { get; } = new();

        /// <summary>
        ///     子弹的直线移动轨迹(间隔)
        /// </summary>
        public StraightLocus_Duration StraightLocus_Duration { get; } = new();

        /// <summary>
        ///     移动间隔时长(秒)
        /// </summary>
        public float MoveInterval { get; set; } = 0.03f;

        /// <summary>
        ///     获取角色
        /// </summary>
        public ActorThing Actor => SingletonMgr.Instance.BattleMgr.Actor;

        /// <summary>
        ///     是否是玩家阵营发出的子弹
        /// </summary>
        public bool IsPlayerBullet => BulletThing.CdExecutor.Thing.Camp is Camp.Player;

        /// <summary>
        ///     是否是怪物阵营发出的子弹
        /// </summary>
        public bool IsMonsterBullet => BulletThing.CdExecutor.Thing.Camp is Camp.Monster;

        public override void Awake()
        {
            // 子弹没有血条和护罩，就不调用基类方法了
            // base.Awake();

            LocusRoot = transform.GetOrAddChildGameObject(nameof(LocusRoot));
            ImgElement = transform.GetOrAddChildGameObject("Img");
            SpriteRenderer = ImgElement.GetOrAddComponent<SpriteRenderer>();
            PrefabElement = transform.GetOrAddChildGameObject("Prefab");
        }

        public override void Start()
        {
            // 子弹没有血条和护罩，就不调用基类方法了
            // base.Start();
        }

        /// <inheritdoc />
        public override async UniTaskVoid TurnToPool()
        {
            try
            {
                await UniTask.WaitForEndOfFrame(SingletonMgr.Instance.GlobalMgr);

                // 隐藏轨迹
                LocusRoot.SetActive(false);

                // 隐藏子弹图片
                ImgElement.SetActive(false);

                // 子弹图片的缩放重置为1
                SpriteRenderer.gameObject.transform.localScale = Vector3.one;

                // 隐藏和清理子弹预制体
                PrefabElement.SetActive(false);
                if (PrefabInstance != null)
                {
                    Destroy(PrefabInstance);
                    PrefabInstance = null;
                }
                
                base.TurnToPool().Forget();
            }
            catch (OperationCanceledException) { throw; }
            catch (MissingReferenceException) { }
            catch (Exception ex)
            {
                Debug.LogException(ex);
            }
            //catch { }
        }

        /// <summary>
        ///     设置子弹在哪层
        /// </summary>
        /// <param name="layerName"></param>
        /// <param name="orderInLayer"></param>
        public void SetSortingLayer(string layerName, int orderInLayer)
        {
            SpriteRenderer.sortingLayerName = layerName;
            SpriteRenderer.sortingOrder = orderInLayer;
        }

        /// <summary>
        ///     设置子弹的不透明度
        /// </summary>
        public void SetOpacity(float opacity)
        {
            SpriteRenderer.SetOpacity(opacity);
        }

        /// <summary>
        ///     执行一次自转
        /// </summary>
        /// <param name="token"></param>
        /// <param name="duration">这次自转前经过的时长(秒)</param>
        public override void RotateOne(CancellationToken token, float duration)
        {
            // 自转角速度
            float rotateSpeed =
                (float)BulletThing.CdExecutor.Thing.GetTotalDouble(PropType.RotateSpeed).FirstOrDefault();
            if (rotateSpeed == 0)
            {
                return;
            }

            float rotateZ = transform.localRotation.eulerAngles.z + (rotateSpeed * duration);
            transform.DOLocalRotate(new Vector3(0, 0, rotateZ), duration, RotateMode.FastBeyond360);

            //transform.Rotate(Vector3.forward, rotateSpeed * duration,
            //	Space.Self);
        }

        /// <summary>
        ///     计算子弹的当前速度(方向和大小)
        /// </summary>
        protected override Vector3 CalcSpeed_PositionMove()
        {
            float speed = ShouldStay
                ? 0
                : (float)BulletThing.CdExecutor.Thing.GetTotalDouble(PropType.BulletSpeed).FirstOrDefault();
            // 速度不能是负值
            if (speed <= float.Epsilon)
            {
                speed = 0;
            }

            // 停留状态的方向取0
            Vector3 dir_1 = speed == 0 ? Vector3.zero : CalcMoveDir();

            return dir_1 * speed;
        }

        /// <summary>
        ///     执行碰撞检测:敌人
        /// </summary>
        /// <returns>碰到的敌人(及其格子)</returns>
        public abstract IList<HitThingCells> DoCollideEnemies(LineSegment line);

        /// <summary>
        ///     击中敌人后怎么办
        /// </summary>
        /// <param name="line">移动的线段</param>
        /// <param name="thing">发出子弹的物件</param>
        /// <param name="enemies">击中的敌人(及其格子)</param>
        protected abstract void OnHitEnemy(LineSegment line, ThingBase thing, IList<HitThingCells> enemies);

        /// <summary>
        ///     给敌人造成伤害(给敌人加Buff、造成伤害)
        /// </summary>
        /// <param name="thing">哪个物件的攻击</param>
        /// <param name="enemies">受击的敌人(及其格子)</param>
        /// <param name="onDied">如果敌人死了做什么</param>
        /// <returns>被伤害敌人的半径最大值</returns>
        public abstract float DamageEnemy(ThingBase thing, IList<HitThingCells> enemies,
            Action<MonsterThing> onDied = null);

        /// <summary>
        ///     延时在指定位置按概率爆炸
        /// </summary>
        /// <param name="token"></param>
        /// <param name="thing">哪个物件 发出的子弹的 爆炸</param>
        /// <param name="pos">爆炸位置</param>
        /// <param name="exploseType">爆炸类型{1:击中的爆炸,2:击杀的爆炸}</param>
        /// <param name="exploseNo">第几次爆炸(0开始)</param>
        protected abstract UniTaskVoid DoExplose(CancellationToken token, ThingBase thing,
            Vector3 pos, byte exploseType = 1, int exploseNo = 0);

        #region 仅用于激光的部分

        ///// <summary>
        ///// 激光伤害方式{0:无、未定义, 1:接触终点, 2:接触矩形区域}
        ///// </summary>
        //public int LaserDamageType { get; set; }

        ///// <summary>
        ///// 这条激光的所有光段(穿透、折射)
        ///// </summary>
        //public List<BulletLaser> LaserLines { get; } = new();

        //protected CreatureThing LaserTarget_m;

        ///// <summary>
        ///// 锁定的攻击目标(不一定有值)
        ///// </summary>
        //public CreatureThing LaserTarget
        //{
        //	get => LaserTarget_m;
        //	set
        //	{
        //		LaserTarget_m = value;
        //		LaserTarget_m.Hp.Changed += LaserTarget_Hp_Changed;
        //	}
        //}

        #endregion
    }
}