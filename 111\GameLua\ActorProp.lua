﻿--[[
********************************************************************
    created:
    author :
    purpose:    计算细胞属性
*********************************************************************
--]]
-- local luaID = ('ActorProp')
ActorProp = {
	PropNames = {
		HP = { Text_cn = "生命", PctEnd = false },
		Attack = { Text_cn = "基础攻击", PctEnd = true },
		Defense = { Text_cn = "生命恢复", PctEnd = false },
		PhysicsAttack = { Text_cn = "攻击", PctEnd = false, FireFactor = 1 },
		MagicAttack = { Text_cn = "生命恢复-点数", PctEnd = false, FireFactor = 0.2 },
		PhysicsDefense = { Text_cn = "生命恢复-百分比", PctEnd = true, FireFactor = 0.2 },
		MagicDefense = { Text_cn = "护甲恢复-点数", PctEnd = false },
		CriticalStrike = { Text_cn = "护甲恢复-百分比", PctEnd = true, FireFactor = 0.5 },
		AntiCriticalStrike = { Text_cn = "直接伤害", PctEnd = false, FireFactor = 1 },
		Parry = { Text_cn = "伤害减免", PctEnd = false },
		AntiParry = { Text_cn = "狂暴伤害", PctEnd = true, FireFactor = 0.5 },
		Dodge = { Text_cn = "射程", PctEnd = true, FireFactor = 0.5 },
		Hit = { Text_cn = "子弹速度", PctEnd = true, FireFactor = 0.3 },
		Armor = { Text_cn = "XP值恢复", PctEnd = true },
		AntiArmor = { Text_cn = "能量上限", PctEnd = false },
		DamageReduction = { Text_cn = "减伤", PctEnd = true },
		DamageAdd = { Text_cn = "加伤", PctEnd = true, FireFactor = 0.8 },
		MoveSpeed = { Text_cn = "移动速度", PctEnd = true },
		AttackDefense = { Text_cn = "基础生命", PctEnd = true },
		XpDamagePct = { Text_cn = "XP技能伤害", PctEnd = true, FireFactor = 0.2 },
		CriticalHitRate = { Text_cn = "暴击率", PctEnd = true, FireFactor = 0.5 },
		AttackSpeed = { Text_cn = "攻击速度", PctEnd = true, FireFactor = 0.6 },
		HP_Final = { Text_cn = "总生命", PctEnd = true },
		Attack_Final = { Text_cn = "总攻击", PctEnd = true },
		DamageCritical = { Text_cn = "暴击伤害", PctEnd = true, FireFactor = 0.2 },
		DamageBossAddPct = { Text_cn = "对BOSS伤害", PctEnd = true, FireFactor = 0.3 },
		AcquireExp = { Text_cn = "获得经验", PctEnd = true },
		AcquireGold = { Text_cn = "获得银币", PctEnd = true },
		AbsorbHP = { Text_cn = "吸血", PctEnd = true },
		DamageBounce = { Text_cn = "反伤", PctEnd = true },
	},

	PctProps = {
		-- { Name = "AttackDefense", PctForProp = "HP" },
		-- { Name = "AttackDefense", PctForProp = "HP",            PctForFinal = true },
		-- { Name = "HP_Final",      PctForProp = "HP",            PctForFinal = true },

		--{ Name = "Attack",        PctForProp = "PhysicsAttack" },
		-- { Name = "Attack",        PctForProp = "PhysicsAttack", PctForFinal = true },
		-- { Name = "Attack_Final",  PctForProp = "PhysicsAttack", PctForFinal = true },
	},

	EffectType_Prop = {
		--生命
		{ EffectType = 0,  ActorProp = "HP",                 EquipSmeltProp = "HP" },
		--基础攻击
		{ EffectType = 1,  ActorProp = "Attack",             EquipSmeltProp = "Attack" },
		--生命恢复
		{ EffectType = 2,  ActorProp = "Defense",            EquipSmeltProp = "Defense" },
		--攻击
		{ EffectType = 3,  ActorProp = "PhysicsAttack",      EquipSmeltProp = "PhysicsAttack" },
		--伤害范围
		{ EffectType = 4,  ActorProp = "MagicAttack",        EquipSmeltProp = "MagicAttack" },
		--狂暴时长
		{ EffectType = 5,  ActorProp = "PhysicsDefense",     EquipSmeltProp = "PhysicsDefense" },
		--能量上限
		{ EffectType = 6,  ActorProp = "MagicDefense",       EquipSmeltProp = "MagicDefense" },
		--子弹伤害
		{ EffectType = 7,  ActorProp = "CriticalStrike",     EquipSmeltProp = "CriticalStrike" },
		--直接伤害
		{ EffectType = 8,  ActorProp = "AntiCriticalStrike", EquipSmeltProp = "AntiCriticalStrike" },
		--伤害减免
		{ EffectType = 9,  ActorProp = "Parry",              EquipSmeltProp = "Parry" },
		--狂暴伤害
		{ EffectType = 10, ActorProp = "AntiParry",          EquipSmeltProp = "AntiParry" },
		--暴击
		{ EffectType = 11, ActorProp = "Dodge" },
		--子弹速度
		{ EffectType = 12, ActorProp = "Hit",                EquipSmeltProp = "Hit" },
		--XP值恢复
		{ EffectType = 13, ActorProp = "Armor",              EquipSmeltProp = "Armor" },
		--能量上限
		{ EffectType = 14, ActorProp = "AntiArmor",          EquipSmeltProp = "AntiArmor" },
		--减伤
		{ EffectType = 15, ActorProp = "DamageReduction",    EquipSmeltProp = "DamageReduction" },
		--加伤
		{ EffectType = 16, ActorProp = "DamageAdd",          EquipSmeltProp = "DamageAdd" },
		--移动速度
		{ EffectType = 17, ActorProp = "MoveSpeed" },
		--基础生命
		{ EffectType = 18, ActorProp = "AttackDefense",      EquipSmeltProp = "AttackDefense" },
		--XP技能伤害
		{ EffectType = 20, ActorProp = "XpDamagePct" },
		--暴击率
		{ EffectType = 21, ActorProp = "CriticalHitRate",    EquipSmeltProp = "Dodge" },
		--攻击速度
		{ EffectType = 23, ActorProp = "AttackSpeed" },
		--总生命
		{ EffectType = 25, ActorProp = "HP_Final" },
		--总攻击
		{ EffectType = 27, ActorProp = "Attack_Final" },
		--暴击伤害
		{ EffectType = 45, ActorProp = "DamageCritical",     EquipSmeltProp = "MoveSpeed" },
		--对BOSS伤害
		{ EffectType = 46, ActorProp = "DamageBossAddPct" },
		--获得经验
		{ EffectType = 88, ActorProp = "AcquireExp" },
		--获得金币
		{ EffectType = 89, ActorProp = "AcquireGold" },
		--吸血
		{ EffectType = 90, ActorProp = "AbsorbHP" },
		--反伤
		{ EffectType = 91, ActorProp = "DamageBounce" },
	},
};

for _, p in ipairs(ActorProp.EffectType_Prop) do
	-- 显示名称
	p.Text_cn = ActorProp.PropNames[p.ActorProp].Text_cn;
	-- 是否以百分比显示数值
	p.PctEnd = ActorProp.PropNames[p.ActorProp].PctEnd;
end

function ActorProp.PctProps_First(name)
	for _, pp in ipairs(ActorProp.PctProps) do
		if pp.Name == name then
			return pp
		end
	end
end

function ActorProp.EffectType_Prop_First(EffectType)
	for _, tp in ipairs(ActorProp.EffectType_Prop) do
		if tp.EffectType == EffectType then
			return tp
		end
	end
end

function ActorProp.GetEffectType(name_ActorProp)
	for _, tp in ipairs(ActorProp.EffectType_Prop) do
		if tp.ActorProp == name_ActorProp then
			return tp.EffectType;
		end
	end
end

function ActorProp.NewPropOjb()
	local obj = {}
	for _, tp in ipairs(ActorProp.EffectType_Prop) do
		obj[tp.ActorProp] = 0;
	end
	return obj;
end

---comment 星量换算为品质星数(<=-1表示没有星)
---@return table
function ActorProp.ConvertStarCount2StarObj(starCount)
	local rtn = { Quality = 0, StarNum = -1 }; -- 默认取最小星数减1
	if (starCount == -1) then
		-- -1是最小星量, 但 -1 % 10 结果是 9,所以要特殊处理。
		rtn = { Quality = 0, StarNum = 0 };
	end
	if (starCount >= 0) then
		rtn.StarNum = starCount % 10 + 1;
		rtn.Quality = math.floor(starCount / 10);
	end
	return rtn;
end

---comment 品质星数换算为星量(<=-1表示没有星)
function ActorProp.ConvertToStarCount(quality, starNum)
	if (quality == nil or starNum == nil) then
		return -1; -- 默认没有星(最小星量)
	end

	if (starNum == 0) then
		-- -1 % 10 结果是 9,所以要特殊处理。这时quality也应该只会是0
		return -1;
	end

	return quality * 10 + (starNum - 1) % 10;
end

---comment 从csv表格中的一行读取出属性
---@param csvRow integer
---@return table
function ActorProp.ReadProp_PlayerBaseProp(csvRow)
	local obj = ActorProp.NewPropOjb();
	for key, _ in pairs(obj) do
		obj[key] = csvRow[key];
	end
	return obj;
end

---comment 获取升星配置中的一行
---@param smeltID integer 升星ID
---@param starCount integer 星量，有值则以此为准
---@param quality integer 品质
---@param starNum integer 星数
function ActorProp.ReadCSVRow_EquipSmelt(smeltID, starCount, quality, starNum)
	local star = {};
	if (starCount ~= nil) then
		star = ActorProp.ConvertStarCount2StarObj(starCount);
	else
		star.StarNum = starNum;
		star.Quality = quality;
	end

	return Schemes.EquipSmelt:Get(smeltID, star.Quality, star.StarNum)
end

--获取上星升本星所需经验
function ActorProp.GetLastLevelExp(csvRow_Smelt)
	local starCount = ActorProp.ConvertToStarCount(csvRow_Smelt.Quality, csvRow_Smelt.StarNum);
	local preLevelExp = 0;
	local preSmeltItem = ActorProp.ReadCSVRow_EquipSmelt(csvRow_Smelt.ID, starCount - 1)
	if (preSmeltItem) then
		preLevelExp = preSmeltItem.LevelExp;
	end
	return preLevelExp;
end

---comment 属性值增加指定数值
---@param refProp table
---@param key string
---@param value number
function ActorProp.PropAddValue(refProp, key, value)
	if key == nil then
		return
	end
	if value == nil then
		return
	end
	if refProp[key] == nil then
		refProp[key] = 0
	end
	refProp[key] = refProp[key] + value
end

---comment prop作为加成，应用到result上。(最终万分比视为数值)
---@param result table
---@param prop table
---@param CalcPct boolean 是否计算单件万分比
---@param pctBase table 单件万分比属性的基础值
---@return table result
function ActorProp.PropSum(result, prop, CalcPct, pctBase)
	--print("PropSum -- 参数 CalcPct:", CalcPct, 'pctBase:', json.encode(pctBase))
	for key, value in pairs(prop) do
		if type(value) == "number" then
			local pp = ActorProp.PctProps_First(key);
			if (not pp or not CalcPct or pp.PctForFinal) then
				-- print("PropSum -- key:", key)
				-- HelperL.Dump(value)
				ActorProp.PropAddValue(result, key, value)
			else
				ActorProp.PropAddValue(result, pp.PctForProp, pctBase[pp.PctForProp] * (value / 10000))
			end
		end
	end
	return result;
end

---comment 读取细胞等级对应的属性
---@return table
function ActorProp.ReadActorLevelProp()
	local baseProp = ActorProp.NewPropOjb();
	local level = EntityModule.hero:GetProperty(CREATURE_FIELD.CREATURE_FIELD_LEVEL)
	local playerBaseProp = Schemes.PlayerBaseProp:Get(level)
	if playerBaseProp == nil then
		playerBaseProp = Schemes.PlayerBaseProp:Get(1)
	end
	if playerBaseProp == nil then
		return baseProp
	end

	local tb_playerBaseProp = ActorProp.ReadProp_PlayerBaseProp(playerBaseProp)
	return ActorProp.PropSum(baseProp, tb_playerBaseProp)
end

---@class CalcEquipProp
---@field EquipProp table 计算结果
---@field BaseProp table SelfProp+StarProp+StarExpProp
---@field SelfProp table 装备自带的属性
---@field StarProp table 当前星级的升星属性(不含经验提升)
---@field StarExpProp table 由升星经验带来的属性提升
---@field uid integer 实体唯一ID
---@field GoodsID integer 物品ID
---@field GoodsName string 物品名称
---@field EntityClass integer 实体类型
---@field SubType integer 实体子类型
---@field SmeltID integer 升星ID
---@field SmeltQuality integer 品质
---@field SmeltStarNum integer 星数
---@field SmeltStarCount integer 星量
---@field SmeltExp integer 升星经验
---@field StarExpInc integer 升星等级

--- 读取单件装备的属性
---@param equip any
---@return CalcEquipProp
function ActorProp.ReadEquipProp(equip)
	---@type CalcEquipProp
	local rtn = {
		-- 计算结果
		EquipProp = ActorProp.NewPropOjb(),
		-- 万分比加成的基础属性(SelfProp+StarProp+StarExpProp)
		BaseProp = ActorProp.NewPropOjb(),
		-- 装备自带的属性
		SelfProp = ActorProp.NewPropOjb(),
		-- 当前星级的升星属性(不含经验提升)
		StarProp = ActorProp.NewPropOjb(),
		-- 由升星经验带来的属性提升
		StarExpProp = ActorProp.NewPropOjb(),
		-- -- 上一星级的属性值
		-- PreStarProp = ActorProp.NewPropOjb(),
		-- -- 下一星级的属性值
		NextStarProp = ActorProp.NewPropOjb(),
		-- 实体唯一ID
		uid = 0,
		-- 物品ID
		GoodsID = 0,
		-- 物品名称
		GoodsName = "未找到",
		-- 实体类型
		EntityClass = 0,
		-- 实体子类型
		SubType = 0,
		-- 升星ID
		SmeltID = 0,
		-- 品质
		SmeltQuality = 0,
		-- 星数
		SmeltStarNum = 0,
		-- 星量
		SmeltStarCount = 0,
		-- 升星经验
		SmeltExp = 0,
		-- 点一下加多少
		StarExpInc = 0,
	};

	if equip then
		rtn.uid = equip.uid;
		rtn.GoodsID = equip:GetProperty(ENTITY_FIELD.ENTITY_FIELD_ID)
		rtn.SmeltQuality = equip:GetProperty(EQUIPMENT_FIELD.EQUIPMENT_FIELD_QUALITY)
		rtn.SmeltStarNum = equip:GetProperty(EQUIPMENT_FIELD.EQUIPMENT_FIELD_STARNUM)
		rtn.SmeltExp = equip:GetProperty(EQUIPMENT_FIELD.EQUIPMENT_FIELD_SMELTEXP);
		local expRestEveryStar = false;
		if rtn.GoodsID then
			local csvRow_Equipment = Schemes.Equipment:Get(rtn.GoodsID)
			-- 装备类先算上装备本身的属性
			if (csvRow_Equipment) then
				rtn.GoodsName = csvRow_Equipment.GoodsName;
				rtn.EntityClass = csvRow_Equipment.EntityClass;
				rtn.SubType = csvRow_Equipment.SubType;
				rtn.SmeltID = csvRow_Equipment.SmeltID;
				expRestEveryStar = rtn.SubType >= 1 and rtn.SubType <= 12;
				if expRestEveryStar then
					rtn.SmeltExp = equip:GetProperty(EQUIPMENT_FIELD.EQUIPMENT_FIELD_CUSTOM)
				end

				-- -- 服务端的命格升级有问题,改成客户端根据经验换算为对应的品质、星数
				-- if (rtn.EntityClass == 2 and rtn.SubType == 26) then
				-- 	-- 从品质0、星数0这一行中取出等级梯度
				-- 	local csvRow_Smelt0 = ActorProp.ReadCSVRow_EquipSmelt(rtn.SmeltID, -1);
				-- 	local smeltLevel0 = 0;
				-- 	if csvRow_Smelt0 then
				-- 		for i, v in ipairs(csvRow_Smelt0.LevelStepRate) do
				-- 			if (rtn.SmeltExp < v) then
				-- 				smeltLevel0 = i;
				-- 				break;
				-- 			end
				-- 		end
				-- 	end
				-- 	if smeltLevel0 > 0 then
				-- 		local tmp = ActorProp.ConvertStarCount2StarObj(smeltLevel0 - 1);
				-- 		rtn.SmeltQuality = tmp.Quality;
				-- 		rtn.SmeltStarNum = tmp.StarNum;
				-- 	end
				-- end

				-- 计算SelfProp
				for i = EQUIPMENT_FIELD.EQUIPMENT_FIELD_EFFECTID1, EQUIPMENT_FIELD.EQUIPMENT_FIELD_ADDEFFECT_GROUP10_ID5, 1 do
					local p = ActorProp.GetAttributeData(equip:GetProperty(i), i, rtn.GoodsID)
					if (p) then
						local tp = ActorProp.EffectType_Prop_First(p.Type);
						if tp then
							ActorProp.PropAddValue(rtn.SelfProp, tp.ActorProp, p.Value);
							--print("ReadEquipProp -- tp:", json.encode(tp), "p:", json.encode(p));
						end
					end
				end
				--print("ReadEquipProp -- rtn.SelfProp:", json.encode(rtn.SelfProp))
			end
			-- 道具类
			local csvRow_Medicament = Schemes.Medicament:Get(rtn.GoodsID)
			if (csvRow_Medicament) then
				rtn.GoodsName = csvRow_Medicament.GoodsName;
				rtn.EntityClass = csvRow_Medicament.EntityClass;
				rtn.SubType = csvRow_Medicament.SubType;
				rtn.SmeltID = csvRow_Medicament.SmeltID;
				rtn.SmeltQuality = csvRow_Medicament.Quality;
				rtn.SmeltStarNum = csvRow_Medicament.StarNum;
			end

			-- 根据品质、星数找到升星表的一行
			if not rtn.SmeltQuality then
				rtn.SmeltQuality = 0;
			end
			if not rtn.SmeltStarNum then
				rtn.SmeltStarNum = 0;
			end

			local csvRow_Smelt = nil;
			local csvRow_NextSmelt = nil;

			if (rtn.EntityClass == 2 and rtn.SubType == 26) then
				local slevel = ActorProp.GetEquipSmeltLevel(equip);
				rtn.SmeltQuality = 0;
				rtn.SmeltStarNum = slevel.level - 1;
				csvRow_Smelt = ActorProp.ReadCSVRow_EquipSmelt(rtn.SmeltID, nil, rtn.SmeltQuality, rtn.SmeltStarNum);
				csvRow_NextSmelt = ActorProp.ReadCSVRow_EquipSmelt(rtn.SmeltID, nil, rtn.SmeltQuality,
					rtn.SmeltStarNum + 1);
				if csvRow_Smelt then
					--print("ReadEquipProp -- 开始计算升星属性:")
					rtn.StarProp = ActorProp.ReadStarProp(rtn.SmeltID, nil, rtn.SmeltQuality, rtn.SmeltStarNum);
				end
				if (csvRow_NextSmelt) then
					rtn.NextStarProp = ActorProp.ReadStarProp(rtn.SmeltID, nil, rtn.SmeltQuality, rtn.SmeltStarNum + 1);
				end
			else
				rtn.SmeltStarCount = ActorProp.ConvertToStarCount(rtn.SmeltQuality, rtn.SmeltStarNum);
				csvRow_Smelt = ActorProp.ReadCSVRow_EquipSmelt(rtn.SmeltID, rtn.SmeltStarCount);
				csvRow_NextSmelt = ActorProp.ReadCSVRow_EquipSmelt(rtn.SmeltID, rtn.SmeltStarCount + 1);
				if csvRow_Smelt then
					--print("ReadEquipProp -- 开始计算升星属性:")
					rtn.StarProp = ActorProp.ReadStarProp(rtn.SmeltID, rtn.SmeltStarCount);
				end
				if csvRow_NextSmelt then
					rtn.NextStarProp = ActorProp.ReadStarProp(rtn.SmeltID, rtn.SmeltStarCount + 1);
				end
			end

			-- 升星经验换算为提升量
			if csvRow_Smelt then
				-- -- 升星经验提升
				-- if not excludeSmeltExp and csvRow_Smelt.NextQuality > 0 and csvRow_Smelt.NextStarNum > 0 and csvRow_Smelt.ExpProRatio > 0 then
				-- 	rtn.SmeltExp = equip:GetProperty(EQUIPMENT_FIELD.EQUIPMENT_FIELD_SMELTEXP);
				-- if (rtn.SmeltStarCount > 0) then
				-- 	rtn.PreStarProp = ActorProp.ReadStarProp(csvRow_Equipment.SmeltID, rtn.SmeltStarCount - 1);
				-- end
				-- 	-- 未升到最后一级时,将升星经验按比例换算为属性提升
				--     -- 上一级的最大升星经验
				--     local preLevelExp = 0;
				-- 	local preSmeltItem = ActorProp.ReadCSVRow_EquipSmelt(csvRow_Equipment.SmeltID, rtn.SmeltStarCount - 1)
				--     if (preSmeltItem) then
				--         preLevelExp = preSmeltItem.LevelExp;
				--     end
				-- 	if (rtn.SmeltExp > preLevelExp and csvRow_Smelt.LevelExp > preLevelExp) then
				-- 		local expRate = (rtn.SmeltExp - preLevelExp) / (csvRow_Smelt.LevelExp - preLevelExp);
				-- 		for key, value in pairs(rtn.NextStarProp) do
				-- 			local incValue = value - rtn.StarProp[key]
				-- 			if incValue > 0 then
				-- 				rtn.StarExpProp[key] = csvRow_Smelt.ExpProRatio * incValue * expRate;
				-- 			end
				-- 		end
				-- 	end
				-- end

				-- 未升到最后一级时,将升星经验换算为属性提升(之前是按进度比例,现在是按配置的倍率)
				if rtn.SmeltExp and csvRow_NextSmelt then
					--print("ReadEquipProp -- 开始计算升星经验属性:")
					for key, value in pairs(rtn.StarProp) do
						if rtn.NextStarProp[key] > value then
							if expRestEveryStar and csvRow_Smelt.MeltingNumber > 0 then
								local starExpInc = csvRow_Smelt.ExpProRatio / csvRow_Smelt.MeltingNumber
								if csvRow_Smelt.LevelMaxExp ~= -1 then
									rtn.StarExpInc = starExpInc
								end
								
								rtn.StarExpProp[key] = starExpInc * rtn.SmeltExp;
							elseif rtn.SmeltExp > csvRow_Smelt.SaleMoney and csvRow_Smelt.LevelExpRate > 0 then
								local starExpInc = csvRow_Smelt.ExpProRatio / csvRow_Smelt.LevelExpRate;
								if csvRow_Smelt.LevelMaxExp ~= -1 then
									rtn.StarExpInc = starExpInc
								end

								rtn.StarExpProp[key] = starExpInc * (rtn.SmeltExp - csvRow_Smelt.SaleMoney);
							end
						end
					end
				end
			end

			ActorProp.PropSum(rtn.BaseProp, rtn.SelfProp);
			ActorProp.PropSum(rtn.BaseProp, rtn.StarProp);
			ActorProp.PropSum(rtn.BaseProp, rtn.StarExpProp);

			ActorProp.PropSum(rtn.EquipProp, rtn.BaseProp, true, rtn.BaseProp);
		end
	end
	return rtn;
end

---comment 获取装备的升星ID
--- 传递的参数: starCount 或 quality、starNum
---@param GoodsId integer 物品ID
---@return integer 升星ID
function ActorProp.GetSmeltID(GoodsId)
	local csvRow_Equipment = Schemes.Equipment:Get(GoodsId)
	if (csvRow_Equipment) then
		return csvRow_Equipment.SmeltID;
	else
		local csvRow_Medicament = Schemes.Medicament:Get(GoodsId)
		if (csvRow_Medicament) then
			return csvRow_Equipment.SmeltID;
		end
	end
	return 0;
end

---comment 读取升星属性
--- 传递的参数: starCount 或 quality、starNum
---@param starCount integer 星量，有值则以此为准
---@param quality integer 品质
---@param starNum integer 星数
function ActorProp.ReadStarProp(smeltID, starCount, quality, starNum)
	local rtn = ActorProp.NewPropOjb();
	local p1 = ActorProp.NewPropOjb();
	local p2 = ActorProp.NewPropOjb();
	local csvRow_Smelt = ActorProp.ReadCSVRow_EquipSmelt(smeltID, starCount, quality, starNum)
	if csvRow_Smelt then
		-- 读取有对应属性的列
		for _, tp in ipairs(ActorProp.EffectType_Prop) do
			if tp.EquipSmeltProp then
				ActorProp.PropAddValue(p1, tp.ActorProp, csvRow_Smelt[tp.EquipSmeltProp])
			end
		end
		-- 读取列:EquipEffectID
		for i = 1, #csvRow_Smelt.EquipEffectID do
			local effectID = tonumber(csvRow_Smelt.EquipEffectID[i]) / 10000
			--print("ReadStarProp -- local effectID:" .. effectID)
			local effectItem = Schemes.EquipEffect:Get(effectID)
			if effectItem then
				local tp = ActorProp.EffectType_Prop_First(effectItem.EffectType);
				if tp then
					ActorProp.PropAddValue(p2, tp.ActorProp, effectItem.EffectParam);
				end
			end
		end

		-- 读取列:StarLvlProps
		if (string.len(csvRow_Smelt.StarLvlProps) > 2) then
			if (starCount == nil) then
				starCount = ActorProp.ConvertToStarCount(quality, starNum);
			end

			for i, v in ipairs(HelperL.Split(csvRow_Smelt.StarLvlProps, ";")) do
				local cdt = HelperL.Split(v, "|");
				local cdt_StarCount = ActorProp.ConvertToStarCount(cdt[1], cdt[2]);
				if (starCount >= cdt_StarCount) then
					local effectID = tonumber(cdt[3])
					--print("ReadStarProp -- local effectID:" .. effectID)
					local effectItem = Schemes.EquipEffect:Get(effectID)
					if effectItem then
						local tp = ActorProp.EffectType_Prop_First(effectItem.EffectType);
						if tp then
							ActorProp.PropAddValue(p2, tp.ActorProp, effectItem.EffectParam);
						end
					end
				end
			end
		end

		ActorProp.PropSum(rtn, p1);
		ActorProp.PropSum(rtn, p2);
	end
	return rtn;
end

-- 统计满足最小星量要求的装备数量(<=-1表示没有星)
function ActorProp.Stat_Star(minStarCount)
	local rtn = 0;
	local equipSkep = SkepModule.GetEquipSkep()
	for i = EQUIP_TYPE.EQUIP_TYPE_HEAD, EQUIP_TYPE.EQUIP_TYPE_MAXID - 1 do
		if i ~= EQUIP_TYPE.EQUIP_TYPE_WEAPON or i ~= EQUIP_TYPE.EQUIP_TYPE_FASHION or i ~= EQUIP_TYPE.EQUIP_TYPE_CLOAK then
			local entity = EntityModule:GetEntity(equipSkep[i])
			if entity then
				local quality = entity:GetProperty(EQUIPMENT_FIELD.EQUIPMENT_FIELD_QUALITY)
				local starNum = entity:GetProperty(EQUIPMENT_FIELD.EQUIPMENT_FIELD_STARNUM)
				local starCount = ActorProp.ConvertToStarCount(quality, starNum);
				if (starCount >= minStarCount) then
					rtn = rtn + 1;
				end
			end
		end
	end
	return rtn;
end

--计算细胞属性（服务器无法算到正确的属性，放到客户端自己算）
function ActorProp.GetPlayerFieldsByClient()
	local rtn = {
		-- 计算结果
		Result = ActorProp.NewPropOjb(),
		-- 攻击能力(火力值)
		FirePower = 0,
		-- 防御能力
		DefensePower = 0,
		-- 细胞属性(按等级取配置表)
		ActorBase = ActorProp.NewPropOjb(),
		-- 细胞列表
		Airs = {},
		-- 装备列表
		Equips = {},
		-- 卡牌列表
		Cards = {},
		-- 菌落列表
		Fates = {},
		-- 宠物列表
		Pets = {},
		-- 坐骑装备
		MountEquips = {},
		-- 升星目标达成的额外属性
		StarReach = ActorProp.NewPropOjb(),
	};

	-- 细胞属性
	local skepAir = SkepModule:GetSkepByID(SKEPID.SKEPID_GODWEAPON)
	if skepAir then
		for _, weaponItem in ipairs(Schemes.EquipWeapon.items) do
			-- 看看背包篮子里有没有
			local airEntity = skepAir:GetEntityByGoodsID(weaponItem.ID)
			if airEntity then
				table.insert(rtn.Airs, ActorProp.ReadEquipProp(airEntity))
			end
		end
	end

	-- 装备属性
	local equipSkep = SkepModule.GetEquipSkep()
	for i = 1, 11 do
		if equipSkep[i] then
			local equipUID = equipSkep[i]
			local equip = EntityModule:GetEntity(equipUID)
			table.insert(rtn.Equips, ActorProp.ReadEquipProp(equip))
		end
	end

	-- 卡牌属性
	local packetIDList = Schemes.Equipment:GetByPacketID()
	local EquipmentData = packetIDList[10]
	for _, v in ipairs(EquipmentData) do
		local cardScheme = Schemes.EquipCollect:Get(v.ID)
		local cardSkep = SkepModule:GetSkepByID(SKEPID.SKEPID_MONSTERCARD)
		if cardScheme then
			local entity = cardSkep:GetEntityByGoodsID(cardScheme.ID)
			if entity then
				table.insert(rtn.Cards, ActorProp.ReadEquipProp(entity))
			end
		end
	end

	-- 坐骑装备
	EquipmentData = packetIDList[18]
	for _, v in pairs(EquipmentData) do
		--if v.ID > 600050 and v.ID < 600054 then
		local cardSkep = SkepModule:GetSkepByID(SKEPID.SKEPID_MOUNTEQUIP)
		local entity
		if cardSkep then
			entity = cardSkep:GetEntityByGoodsID(v.ID)
			if entity then
				local index = 1
				if v.ID == 600051 then
					index = 1
				elseif v.ID == 600052 then
					index = 2
				elseif v.ID == 600053 then
					index = 3
				end
				local commonText = Schemes.CommonText:Get(99 + index)
				--print(v.ID.."||"..tostring(EntityModule.hero.buffLC:HasBuff(commonText.Param1)) .."||"..EntityModule.hero.buffLC:GetBuffLeftTime(commonText.Param1))
				if EntityModule.hero.buffLC:HasBuff(commonText.Param1) and EntityModule.hero.buffLC:GetBuffLeftTime(commonText.Param1) > 1 then
					print("佩戴：" .. v.ID)
					table.insert(rtn.MountEquips, ActorProp.ReadEquipProp(entity))
				else
					--print("取下"..v.ID)
					-- local str_req = string.format("LuaRequestTakeMountEquip?equip=%s", entity.uid)
					-- LuaModule.RunLuaRequest(str_req, function(result, content)
					-- 	if result == RESULT_CODE.RESULT_COMMON_SUCCEED then
					-- 		-- HelperL.ShowMessage(TipType.FlowText, GetGameText(luaID, 12))
					-- 	else
					-- 		HelperL.ShowMessage(TipType.FlowText, ResultCode:GetResultCodeStr(result))
					-- 	end
					-- end)
				end
			end
		end
		--end
	end

	-- 菌落属性
	local skepFate = SkepModule.GetFateEquipmentSkep()
	for i = 0, skepFate.indexMaxsize do
		if skepFate[i] then
			local equipUID = skepFate[i]
			local equip = EntityModule:GetEntity(equipUID)
			table.insert(rtn.Fates, ActorProp.ReadEquipProp(equip))
		end
	end

	-- 宠物属性(原坐骑)
	local skepMount = SkepModule:GetSkepByID(SKEPID.SKEPID_MOUNT)
	if skepMount then
		for i = 0, skepMount.indexMaxsize do
			if skepMount[i] then
				local entity = EntityModule:GetEntity(skepMount[i])
				if entity then
					local equipProp = ActorProp.ReadEquipProp(entity);
					table.insert(rtn.Pets, equipProp)
				end
			end
		end
	end

	-- 升星目标达成的额外属性
	local StarReach_EffectIDs = {};
	for i, v in ipairs(Schemes.EquipForgeAddition.items) do
		if (v.Type == 0) then
			local equipCount = ActorProp.Stat_Star(v.Condition2 - 1);
			if (equipCount >= v.Condition1) then
				table.insert(StarReach_EffectIDs, v.EffectList);
			end
		end
	end
	for _, effectList in ipairs(StarReach_EffectIDs) do
		for _, v in ipairs(effectList) do
			local effectID = tonumber(v) / 10000
			--print("ReadStarProp -- local effectID:" .. effectID)
			local effectItem = Schemes.EquipEffect:Get(effectID)
			if effectItem then
				local tp = ActorProp.EffectType_Prop_First(effectItem.EffectType);
				if tp then
					ActorProp.PropAddValue(rtn.StarReach, tp.ActorProp, effectItem.EffectParam);
				end
			end
		end
	end

	-- 细胞等级属性+细胞属性+装备属性+卡牌属性+菌落属性+宠物属性(原坐骑)
	rtn.ActorBase = ActorProp.ReadActorLevelProp();
	--print("GetPlayerFieldsByClient -- 细胞等级属性:", json.encode(rtn.ActorBase))
	ActorProp.PropSum(rtn.Result, rtn.ActorBase);

	--print("GetPlayerFieldsByClient -- 细胞:", #rtn.Airs)
	for _, equipProp in ipairs(rtn.Airs) do
		-- print("GetPlayerFieldsByClient -- Airs:GoodsID=" .. equipProp.GoodsID
		-- 	.. "升星ID=" .. equipProp.SmeltID
		-- 	.. "品质=" .. equipProp.SmeltQuality
		-- 	.. "星数=" .. equipProp.SmeltStarNum
		-- 	.. "名称=" .. equipProp.GoodsName
		-- 	, json.encode(equipProp))
		ActorProp.PropSum(rtn.Result, equipProp.EquipProp);
	end

	--print("GetPlayerFieldsByClient -- 装备:", #rtn.Equips)
	for _, equipProp in ipairs(rtn.Equips) do
		-- print("GetPlayerFieldsByClient -- 装备:GoodsID=" .. equipProp.GoodsID
		-- 	.. "升星ID=" .. equipProp.SmeltID
		-- 	.. "品质=" .. equipProp.SmeltQuality
		-- 	.. "星数=" .. equipProp.SmeltStarNum
		-- 	.. "名称=" .. equipProp.GoodsName
		-- 	, json.encode(equipProp))
		ActorProp.PropSum(rtn.Result, equipProp.EquipProp);
	end

	--print("GetPlayerFieldsByClient -- 卡牌:", #rtn.Cards)
	for _, equipProp in ipairs(rtn.Cards) do
		-- print("GetPlayerFieldsByClient -- 卡牌:GoodsID=" .. equipProp.GoodsID
		-- 	.. "升星ID=" .. equipProp.SmeltID
		-- 	.. "品质=" .. equipProp.SmeltQuality
		-- 	.. "星数=" .. equipProp.SmeltStarNum
		-- 	.. "名称=" .. equipProp.GoodsName
		-- 	, json.encode(equipProp))
		ActorProp.PropSum(rtn.Result, equipProp.EquipProp);
	end

	--print("GetPlayerFieldsByClient -- 坐骑装备:", #rtn.MountEquips)
	for _, equipProp in ipairs(rtn.MountEquips) do
		-- print("GetPlayerFieldsByClient -- 坐骑装备:GoodsID=" .. equipProp.GoodsID
		-- 	.. "升星ID=" .. equipProp.SmeltID
		-- 	.. "品质=" .. equipProp.SmeltQuality
		-- 	.. "星数=" .. equipProp.SmeltStarNum
		-- 	.. "名称=" .. equipProp.GoodsName
		-- 	, json.encode(equipProp))
		ActorProp.PropSum(rtn.Result, equipProp.EquipProp);
	end

	--print("GetPlayerFieldsByClient -- 菌落:", #rtn.Fates)
	for _, equipProp in ipairs(rtn.Fates) do
		-- print("GetPlayerFieldsByClient -- 菌落:GoodsID=" .. equipProp.GoodsID
		-- 	.. "升星ID=" .. equipProp.SmeltID
		-- 	.. "品质=" .. equipProp.SmeltQuality
		-- 	.. "星数=" .. equipProp.SmeltStarNum
		-- 	.. "名称=" .. equipProp.GoodsName
		-- 	, json.encode(equipProp))
		ActorProp.PropSum(rtn.Result, equipProp.EquipProp);
	end

	--print("GetPlayerFieldsByClient -- 宠物:", #rtn.Pets)
	for _, equipProp in ipairs(rtn.Pets) do
		-- print("GetPlayerFieldsByClient -- 宠物:GoodsID=" .. equipProp.GoodsID
		-- 	.. "升星ID=" .. equipProp.SmeltID
		-- 	.. "品质=" .. equipProp.SmeltQuality
		-- 	.. "星数=" .. equipProp.SmeltStarNum
		-- 	.. "名称=" .. equipProp.GoodsName
		-- 	, json.encode(equipProp))
		ActorProp.PropSum(rtn.Result, equipProp.EquipProp);
	end

	-- 升星目标达成的额外属性
	ActorProp.PropSum(rtn.Result, rtn.StarReach);

	-- 应用最终比例加成
	for _, p in ipairs(ActorProp.PctProps) do
		if p.PctForFinal then
			-- 多个比例到应用到同一属性上时相当于连乘
			ActorProp.PropAddValue(rtn.Result, p.PctForProp, rtn.Result[p.PctForProp] * (rtn.Result[p.Name] / 10000))
		end
	end

	-- 计算攻击能力(火力值)
	rtn.FirePower = math.floor(
		(rtn.Result.PhysicsAttack * ActorProp.PropNames.PhysicsAttack.FireFactor
			+ rtn.Result.AntiCriticalStrike * ActorProp.PropNames.AntiCriticalStrike.FireFactor
		)
		* (1 + rtn.Result.AttackSpeed / 10000 * ActorProp.PropNames.AttackSpeed.FireFactor
			+ rtn.Result.DamageAdd / 10000 * ActorProp.PropNames.DamageAdd.FireFactor)
		* (1 + rtn.Result.CriticalHitRate / 10000 * ActorProp.PropNames.CriticalHitRate.FireFactor
			+ rtn.Result.DamageCritical / 10000 * ActorProp.PropNames.DamageCritical.FireFactor
			+ rtn.Result.DamageBossAddPct / 10000 * ActorProp.PropNames.DamageBossAddPct.FireFactor
			+ rtn.Result.MagicAttack / 10000 * ActorProp.PropNames.MagicAttack.FireFactor
			+ rtn.Result.PhysicsDefense / 10000 * ActorProp.PropNames.PhysicsDefense.FireFactor
			+ rtn.Result.CriticalStrike / 10000 * ActorProp.PropNames.CriticalStrike.FireFactor
			+ rtn.Result.AntiParry / 10000 * ActorProp.PropNames.AntiParry.FireFactor
			+ rtn.Result.Dodge / 10000 * ActorProp.PropNames.Dodge.FireFactor
			+ rtn.Result.Hit / 10000 * ActorProp.PropNames.Hit.FireFactor
			+ rtn.Result.XpDamagePct / 10000 * ActorProp.PropNames.XpDamagePct.FireFactor
		)
	);

	-- 计算防御能力
	rtn.DefensePower = math.floor((rtn.Result.HP + rtn.Result.Parry * 100)
		* (1 + rtn.Result.DamageReduction / 10000) *
		(1 + rtn.Result.AbsorbHP / 10000)
		+ ((1 + rtn.Result.DamageReduction / 10000) * rtn.Result.Defense * 5)
	);

	--print("计算出来的细胞属性:", json.encode(rtn.Result))
	print("攻击能力(火力值):", rtn.FirePower)
	print("防御能力:", rtn.DefensePower)
	return rtn;
end

--- 获取角色的火之装备的属性
---@param goodsID integer 物品ID
---@param needWear ?boolean 是否佩戴(出战)了才读取,否则总是读取
---@return integer 火之装备的属性
function ActorProp.GetWeaponProp(goodsID, needWear)
	if HelperL.IsEuipType(goodsID) then
		local equipment = Schemes.Equipment:Get(goodsID)
		if equipment then
			if not needWear or GamePlayerData.ActorEquip:IsWear(equipment.ID, 1) then
				local entity = SkepModule:GetSkepByID(equipment.PacketID):GetEntityByGoodsID(equipment.ID)
				return ActorProp.ReadEquipProp(entity)
			end
		end
	end
	return nil
end

--- 获取角色的火之装备列表
---@param onlyWear boolean 仅佩戴(出战)的,否则所有拥有的火之装备
---@return table 火之装备的装备属性列表
function ActorProp.GetWeapons(onlyWear)
	local lst = {}
	local data

	-- 主线进度
	local id = GamePlayerData.GameEctype:GetProgress(1)

	for i, v in ipairs(Schemes.EquipWeapon.items) do
		-- 钱袋 和 背包空格子 总是出战
		if v.GroupID >= 1000 and v.EffectID5 == 0 then
			local weaponProp = ActorProp.GetWeaponProp(v.ID)
			if weaponProp then
				table.insert(lst, weaponProp)
			end
		else
			-- 首战出战的火之装备,取 EquipWeapon表 中 EffectID5=0 的行
			if (id == 0) then
				if (v.EffectID5 == 0) then
					local weaponProp = ActorProp.GetWeaponProp(v.ID)
					if weaponProp then
						table.insert(lst, weaponProp)
					end
				end
			else
				-- 否则按参数取火之装备
				local weaponProp = ActorProp.GetWeaponProp(v.ID, onlyWear)
				if weaponProp then
					table.insert(lst, weaponProp)
				end
			end
		end
	end
	return lst
end

--读取装备属性用于显示(每条一行,同类属性也不合并)
function ActorProp.ReadEquipEffect(equip)
	local rtn = {};
	local fvar = {}; -- 函数内变量,不用写太多local
	if equip then
		fvar.uid = equip.uid;
		fvar.GoodsID = equip:GetProperty(ENTITY_FIELD.ENTITY_FIELD_ID)
		fvar.SmeltQuality = equip:GetProperty(EQUIPMENT_FIELD.EQUIPMENT_FIELD_QUALITY)
		fvar.SmeltStarNum = equip:GetProperty(EQUIPMENT_FIELD.EQUIPMENT_FIELD_STARNUM)
		fvar.SmeltExp = equip:GetProperty(EQUIPMENT_FIELD.EQUIPMENT_FIELD_SMELTEXP);
		fvar.expRestEveryStar = false;
		if fvar.GoodsID then
			-- 装备类
			fvar.csvRow_Equipment = Schemes.Equipment:Get(fvar.GoodsID)
			if (fvar.csvRow_Equipment) then
				fvar.EntityClass = fvar.csvRow_Equipment.EntityClass;
				fvar.SubType = fvar.csvRow_Equipment.SubType;
				fvar.SmeltID = fvar.csvRow_Equipment.SmeltID;
				fvar.expRestEveryStar = fvar.SubType >= 1 and fvar.SubType <= 12;
				if fvar.expRestEveryStar then
					fvar.SmeltExp = equip:GetProperty(EQUIPMENT_FIELD.EQUIPMENT_FIELD_CUSTOM)
				end

				-- 读取SelfProp
				for i = EQUIPMENT_FIELD.EQUIPMENT_FIELD_EFFECTID1, EQUIPMENT_FIELD.EQUIPMENT_FIELD_ADDEFFECT_GROUP10_ID5, 1 do
					local p = ActorProp.GetAttributeData(equip:GetProperty(i), i, fvar.GoodsID)
					if (p) then
						local tp = ActorProp.EffectType_Prop_First(p.Type);
						if tp and p.Value > 0 then
							table.insert(rtn,
								{ EffectType = p.Type, Value = p.Value, ValueFrom = "EquipProp", EquipPropID = i });
						end
					end
				end
				--print("ReadEquipProp -- rtn.SelfProp:", json.encode(rtn.SelfProp))
			end
			-- 道具类
			fvar.csvRow_Medicament = Schemes.Medicament:Get(rtn.GoodsID)
			if (fvar.csvRow_Medicament) then
				fvar.EntityClass = fvar.csvRow_Medicament.EntityClass;
				fvar.SubType = fvar.csvRow_Medicament.SubType;
				fvar.SmeltID = fvar.csvRow_Medicament.SmeltID;
				fvar.SmeltQuality = fvar.csvRow_Medicament.Quality;
				fvar.SmeltStarNum = fvar.csvRow_Medicament.StarNum;
			end

			-- 读取升星属性
			if not fvar.SmeltQuality then
				fvar.SmeltQuality = 0;
			end
			if not fvar.SmeltStarNum then
				fvar.SmeltStarNum = 0;
			end
			fvar.SmeltStarCount = ActorProp.ConvertToStarCount(fvar.SmeltQuality, fvar.SmeltStarNum);
			local csvRow_Smelt = ActorProp.ReadCSVRow_EquipSmelt(fvar.SmeltID, fvar.SmeltStarCount);
			local csvRow_NextSmelt = ActorProp.ReadCSVRow_EquipSmelt(fvar.SmeltID, fvar.SmeltStarCount + 1);
			if csvRow_Smelt then
				for _, tp in ipairs(ActorProp.EffectType_Prop) do
					if tp.EquipSmeltProp then
						local val = csvRow_Smelt[tp.EquipSmeltProp];
						if (val and val > 0) then
							table.insert(rtn, { EffectType = tp.Type, Value = val, ValueFrom = "EquipSmelt" });
						end
					end
				end
				for i = 1, #csvRow_Smelt.EquipEffectID do
					local effectID = tonumber(csvRow_Smelt.EquipEffectID[i])
					local tp = ActorProp.EffectType_Prop_First(effectID);
					if tp then
						local effectItem = Schemes.EquipEffect:Get(effectID)
						if effectItem and effectItem.EffectParam > 0 then
							table.insert(rtn,
								{
									EffectType = effectID,
									Value = effectItem.EffectParam,
									ValueFrom = "EquipSmelt.EquipEffectID"
								});
						end
					end
				end

				-- 未升到最后一级时,将升星经验换算为属性提升(之前是按进度比例,现在是按配置的倍率)
				if fvar.SmeltExp and fvar.SmeltExp > 0 and csvRow_NextSmelt and csvRow_Smelt.MeltingNumber > 0 then
					local SmeltExpAscend = 0;
					if fvar.expRestEveryStar and csvRow_Smelt.MeltingNumber > 0 then
						SmeltExpAscend = csvRow_Smelt.ExpProRatio * fvar.SmeltExp /
							csvRow_Smelt.MeltingNumber;
					elseif not fvar.expRestEveryStar and fvar.SmeltExp > csvRow_Smelt.SaleMoney and csvRow_Smelt.LevelExpRate > 0 then
						SmeltExpAscend = csvRow_Smelt.ExpProRatio *
							(fvar.SmeltExp - csvRow_Smelt.SaleMoney) / csvRow_Smelt.LevelExpRate;
					end

					for _, e in ipairs(rtn) do
						e.SmeltExpAscend = SmeltExpAscend;
					end
				end
			end

			--加上中文名等
			for _, e in ipairs(rtn) do
				local tp = ActorProp.EffectType_Prop_First(e.EffectType)
				if tp then
					e.Text_cn = tp.Text_cn;
					e.PctEnd = tp.PctEnd;
				end
			end
		end
	end
	return rtn;
end

--获取突变属性组数
function ActorProp.GetShowGroupNum(entity)
	-- 暂时弃用
	-- local itemID = entity:GetProperty(ENTITY_FIELD.ENTITY_FIELD_ID)
	-- local itemInfo = Schemes.Equipment:Get(itemID)
	-- local num = 0
	-- if itemInfo.UseLevel >= 700 then
	-- 	num = 10
	-- elseif itemInfo.UseLevel >= 600 then
	-- 	num = 9
	-- elseif itemInfo.UseLevel >= 500 then
	-- 	num = 8
	-- elseif itemInfo.UseLevel >= 400 then
	-- 	num = 6
	-- elseif itemInfo.UseLevel >= 300 then
	-- 	num = 4
	-- elseif itemInfo.UseLevel >= 200 then
	-- 	num = 3
	-- elseif itemInfo.UseLevel >= 100 then
	-- 	num = 2
	-- else
	-- 	num = 1
	-- end

	--固定10组属性
	return #EquipAttributeIndex
end

---获取(命格)品质颜色
---@overload fun(quality: integer)
---@param quality integer 品质
---@param isRichText boolean 启用富文本
---@return string
function ActorProp.GetQualityColor(quality, isRichText)
	--默认：白色品质
	local color = UI_COLOR.White
	if quality == 2 then
		-- 绿色品质
		color = UI_COLOR.White
	elseif quality == 3 then
		-- 蓝色品质
		color = '#00C3C3'
	elseif quality == 4 then
		-- 紫色品质
		color = '#F600FF'
	elseif quality >= 5 and quality <= 6 then
		-- 金色品质
		color = '#FFFC00'
	elseif quality >= 7 and quality <= 8 then
		-- 橙色品质
		color = '#FF9100'
	elseif quality >= 9 and quality <= 10 then
		-- 红色品质
		color = UI_COLOR.Red
	end
	if isRichText then
		return '<color=' .. color .. '>%s</color>'
	end
	return color
end

---获取(命格)品质颜色(Unity颜色 'Color')
---@param quality integer 品质
function ActorProp.GetQualityColor2(quality)
	--默认：白色品质
	local color = Color(1, 1, 1, 1)
	if quality == 2 then
		-- 绿色品质
		color = Color(0, 1, 0, 1)
	elseif quality == 3 then
		-- 蓝色品质
		color = Color(0, 1, 1, 1)
	elseif quality == 4 then
		-- 紫色品质
		color = Color(1, 0, 1, 1)
	elseif quality >= 5 and quality <= 6 then
		-- 金色品质
		color = Color(1, 1, 0, 1)
	elseif quality >= 7 and quality <= 8 then
		-- 橙色品质
		color = Color(1, 115 / 255, 0, 1)
	elseif quality >= 9 and quality <= 10 then
		-- 红色品质
		color = Color(1, 0, 0, 1)
	end
	return color
end

---获取(装备突变属性)品质描述
---@param quality integer 品质
---@return string
function ActorProp.GetQualityDesc(quality)
	--默认：白色品质
	local desc = 'E'
	if quality == 2 then
		-- 绿色品质
		desc = 'D'
	elseif quality == 3 then
		-- 蓝色品质
		desc = 'C'
	elseif quality == 4 then
		-- 紫色品质
		desc = 'B'
	elseif quality >= 5 and quality <= 6 then
		-- 金色品质
		desc = 'A'
	elseif quality >= 7 and quality <= 8 then
		-- 橙色品质
		desc = 'S'
	elseif quality >= 9 and quality <= 10 then
		-- 红色品质
		desc = 'SS'
	end
	return desc
end

---获取属性值描述
---@param id integer 实体属性ID
---@return string
function ActorProp.GetAttributeDesc(id)
	local propID = math.floor(id / 10000)
	local level = id % 10000
	local item = Schemes.EquipEffect:Get(propID)
	if not item then return '' end
	--if level == 0 then return '' end

	local value = math.floor(level * item.LevelValueRate + item.EffectParam)
	local color = ActorProp.GetQualityColor(item.EffectQuality, true)
	local typeDesc = GetAttributeTypeDesc(item.EffectType)
	return string.format(color, typeDesc .. value)
end

---属性转化属性类型
---@param effectType integer 属性类型
---@param effectValue number 属性值
---@return string 属性描述
---@return number 属性值
---@return string 显示单位
function ActorProp.GetAttributeShowText(effectType, effectValue)
	local value = effectValue
	local type = AttributeShowType[effectType]
	if type == 1 then
		value = math.floor(effectValue)
		return '+' .. value, value, ''
	elseif type == 2 then
		value = HelperL.Round(effectValue / 100, 2)
		return '+' .. value .. '%', value, '%'
	end
	return '+' .. value, value, ''
end

---@class AttributeData:table 实体属性数据
---@field ID integer 实体属性ID
---@field Level integer 属性等级
---@field EffectID integer 属性ID，对应属性表 EquipEffect.csv
---@field Item table 属性表数据
---@field Icon string 属性图标
---@field Type integer 属性类型
---@field Quality integer 属性品质
---@field Value integer 属性值
---@field Value2 number 计算后的属性值
---@field Color string 属性颜色
---@field TypeDesc string 属性类型描述
---@field Unit string 属性单位
---@field Desc string 属性值描述

---获取属性信息
---@param id integer 实体属性ID
---@return AttributeData|nil
function ActorProp.GetAttributeData(id, index, equipmentID)
	if not id or id == 0 then return nil end
	local propID = math.floor(id / 10000)
	local item = Schemes.EquipEffect:Get(propID)
	if not item then return nil end
	local addition = 1
	if index >= EQUIPMENT_FIELD.EQUIPMENT_FIELD_EFFECTID1 and index <= EQUIPMENT_FIELD.EQUIPMENT_FIELD_EFFECTID5 then
		local cfg = Schemes.Equipment:Get(equipmentID)
		if cfg then
			local list = HelperL.Split(cfg.EffectCoef1White, ";")
			addition = tonumber(list[index - 15]) or 1
		end
	end
	local level = 0 --id % 10000
	local m = {}
	--实体数据
	m.ID = id
	m.Level = level
	m.EffectID = propID
	--继承数据，方便使用
	m.Item = item
	m.Icon = item.Icon
	m.Type = item.EffectType
	m.Quality = item.EffectQuality
	m.PolishParam2 = item.PolishParam2
	--计算数据
	m.Value = (level * item.LevelValueRate + item.EffectParam) * addition
	m.Color = ActorProp.GetQualityColor(item.EffectQuality)
	m.TypeDesc = GetAttributeTypeDesc(item.EffectType)
	local desc, value, unit = ActorProp.GetAttributeShowText(item.EffectType, m.Value)
	m.Desc = string.format('<color=' .. m.Color .. '>%s</color>', m.TypeDesc .. desc)
	m.Unit = unit
	m.Value2 = value
	return m
end

---获取装备属性信息组列表
---@param entity integer 装备实体
---@return AttributeData[][]|nil
function ActorProp.GetEquipAttributeList(entity)
	if not entity then return nil end
	local equipmentID = entity:GetProperty(ENTITY_FIELD.ENTITY_FIELD_ID)
	local num = ActorProp.GetShowGroupNum(entity)
	local attributeArray = {}
	local attribute, id, data
	for i = 1, num, 1 do
		attribute = {}
		for ii = 1, 5, 1 do
			id = entity:GetProperty(EquipAttributeIndex[i][ii])
			data = ActorProp.GetAttributeData(id, ii, equipmentID)
			attribute[ii] = data
		end
		if #attribute > 0 then
			table.insert(attributeArray, attribute)
		end
	end
	return attributeArray
end

---@class ActorProp_FateBuff
---@field name string @属性名（Buff名）
---@field value number @属性值（Buff值）

---@class ActorProp_EquipSmeltAttribute
---@field attrType EquipSmeltAttributeEnum @属性类型
---@field attrName string @属性名称
---@field attrValue number|string @属性值

---获取升星表属性
---@param smeltScheme integer @EquipSmelt其中一栏的配置信息
---@param unitConvertFun? fun(num:number):number|string 单位转换函数
---@return ActorProp_EquipSmeltAttribute[]
function ActorProp.GetTransAttributeBySmelt(smeltScheme, unitConvertFun)
	local attrList = {}
	if smeltScheme then
		if not unitConvertFun then
			unitConvertFun = function(num) return num end
		end
		local attr = {
			[EquipSmeltAttributeEnum.HP] = smeltScheme.HP,
			[EquipSmeltAttributeEnum.Attack] = smeltScheme.Attack,
			[EquipSmeltAttributeEnum.Defense] = smeltScheme.Defense,
			[EquipSmeltAttributeEnum.PhysicsAttack] = smeltScheme.PhysicsAttack,
			[EquipSmeltAttributeEnum.MagicAttack] = smeltScheme.MagicAttack,
			[EquipSmeltAttributeEnum.PhysicsDefense] = smeltScheme.PhysicsDefense,
			[EquipSmeltAttributeEnum.MagicDefense] = smeltScheme.MagicDefense,
			[EquipSmeltAttributeEnum.CriticalStrike] = smeltScheme.CriticalStrike,
			[EquipSmeltAttributeEnum.AntiCriticalStrike] = smeltScheme.AntiCriticalStrike,
			[EquipSmeltAttributeEnum.Parry] = smeltScheme.Parry,
			[EquipSmeltAttributeEnum.AntiParry] = smeltScheme.AntiParry,
			[EquipSmeltAttributeEnum.Dodge] = smeltScheme.Dodge,
			[EquipSmeltAttributeEnum.Hit] = smeltScheme.Hit,
			[EquipSmeltAttributeEnum.Armor] = smeltScheme.Armor,
			[EquipSmeltAttributeEnum.AntiArmor] = smeltScheme.AntiArmor,
			[EquipSmeltAttributeEnum.DamageReductin] = smeltScheme.DamageReduction,
			[EquipSmeltAttributeEnum.DamageAdd] = smeltScheme.DamageAdd,
			[EquipSmeltAttributeEnum.MoveSpeed] = smeltScheme.MoveSpeed,
			[EquipSmeltAttributeEnum.AttackDefense] = smeltScheme.AttackDefense,
			[EquipSmeltAttributeEnum.EquipEffectID] = smeltScheme.EquipEffectID,
		}
		for _attrType, _attrValue in pairs(attr) do
			if _attrType == EquipSmeltAttributeEnum.EquipEffectID then
				local effectCount = #_attrValue
				if effectCount > 0 then
					for _, k in pairs(_attrValue) do
						if type(k) == 'number' and k ~= 0 then
							local propID, level = HelperL.GetPropEffectIDAndLevel(k)
							local equipEffectItem = Schemes.EquipEffect:Get(propID)
							if equipEffectItem ~= nil then
								local propType = AttributeShowType[equipEffectItem.EffectType]
								local tempValue = 0
								if propType == 2 then
									tempValue = HelperL.Round((HelperL.GetPropEffectValue(equipEffectItem, level) / 100),
										2)
								else
									tempValue = HelperL.GetPropEffectValue(equipEffectItem, level)
								end
								if tempValue > 0 then
									--单位转
									tempValue = unitConvertFun(tempValue)
									table.insert(attrList, {
										attrType = _attrType,
										attrName = GetAttributeTypeDesc(equipEffectItem.EffectType),
										attrValue = (propType == 2) and tempValue .. '%' or tempValue,
									})
								end
							end
						end
					end
				end
			elseif _attrValue > 0 then
				local propType = EquipSmeltAttributeShowType[_attrType]
				local tempValue
				if propType == 2 then
					tempValue = HelperL.Round((_attrValue / 100), 2)
				else
					tempValue = _attrValue
				end
				--单位转
				tempValue = unitConvertFun(tempValue)
				table.insert(attrList, {
					attrType = _attrType,
					attrName = EquipSmeltAttributeName[_attrType],
					attrValue = (propType == 2) and tempValue .. '%' or tempValue,
				})
			end
		end
	else
		error('EquipSmelt配置错误')
	end
	return attrList
end

--获取奇物，器魂星当前属性值
function ActorProp.GetEquipStarCurrPro(smelt, curExp, nextValue, curValue, equipItem)
	if smelt.LevelExpRate == 0 then
		return curValue
	end
	curExp = ActorProp.GetEquipStarCurrExp(smelt, curExp, equipItem)
	local levelRate = math.floor((curExp - ActorProp.GetLastLevelExp(smelt)) / smelt.LevelExpRate)
	local lastExpRate = (smelt.LevelExp - ActorProp.GetLastLevelExp(smelt)) / smelt.LevelExpRate
	local targetValue = curValue +
		levelRate / lastExpRate * (nextValue - curValue) *
		smelt.ExpProRatio --+tonumber(smelt.EquipEffectID[1])
	return math.floor(targetValue)
end

--获取奇物，器魂星下一级属性值
function ActorProp.GetEquipStarNextPro(smelt, curExp, nextValue, curValue, equipItem)
	if smelt.LevelExpRate == 0 then
		return curValue
	end
	curExp = ActorProp.GetEquipStarCurrExp(smelt, curExp, equipItem)
	local levelRate = math.floor((curExp - ActorProp.GetLastLevelExp(smelt) + smelt.LevelExpRate) / smelt.LevelExpRate)
	local lastExpRate = (smelt.LevelExp - ActorProp.GetLastLevelExp(smelt)) / smelt.LevelExpRate
	local targetValue = curValue +
		levelRate / lastExpRate * (nextValue - curValue) *
		smelt.ExpProRatio --+tonumber(smelt.EquipEffectID[1])
	return math.floor(targetValue)
end

--获取奇物，器魂星当前经验
function ActorProp.GetEquipStarCurrExp(smelt, curExp, equipItem)
	if ActorProp.IsQiWuEquipment(equipItem, true) and smelt then
		if smelt.LevelStepRate and #smelt.LevelStepRate > 0 then
			local levelStepRate = smelt.LevelStepRate[1]
			if levelStepRate then
				local smeltExp = math.floor(levelStepRate * smelt.LevelExpRate)
				return math.min(curExp, smeltExp), curExp >= smeltExp
			end
		end
	end
	return curExp, false
end

--是否是奇物装备，器魂类型
--isCheck是否检测器魂
function ActorProp.IsQiWuEquipment(equipItem, isCheck)
	if not equipItem then return false end
	local subType = equipItem.SubType
	if subType >= EQUIP_TYPE.EQUIP_TYPE_MOUNTEQUIP1 and subType <= EQUIP_TYPE.EQUIP_TYPE_MOUNTEQUIP4 or --坐骑装备类型
		subType >= EQUIP_TYPE.EQUIP_TYPE_WINGEQUIP1 and subType <= EQUIP_TYPE.EQUIP_TYPE_WINGEQUIP4 or --仙器装备类型
		subType >= EQUIP_TYPE.EQUIP_TYPE_NEWWINGEQUIP1 and subType <= EQUIP_TYPE.EQUIP_TYPE_NEWWINGEQUIP4 or --翅膀装备类型
		subType >= EQUIP_TYPE.EQUIP_TYPE_AVATAREQUIPA1 and subType <= EQUIP_TYPE.EQUIP_TYPE_AVATAREQUIPA4 or --星阵装备类型
		subType >= EQUIP_TYPE.EQUIP_TYPE_AVATAREQUIPB1 and subType <= EQUIP_TYPE.EQUIP_TYPE_AVATAREQUIPB4 or --圣光装备类型
		(isCheck and subType == EQUIP_TYPE.EQUIP_TYPE_WINGEQUIP) then                                  --器魂类型
		return true
	end
	return false
end

--获取当前星属性值和下个星属性值
function ActorProp.GetCurAndNextStarValue(entity, type)
	---@type ActorProp_FateBuff[]
	local buffList = {} -- buff描述
	if entity == nil then
		return buffList
	end
	-- 寻找物品配置
	local goodsId = entity:GetProperty(ENTITY_FIELD.ENTITY_FIELD_ID) -- 物品Id
	local goodsCfg = Schemes:GetGoodsConfig(goodsId)              -- 物品配置
	local quality                                                 -- 品质
	local starNum                                                 -- 星数
	if type == 1 then
		local med = Schemes.Medicament:Get(goodsId)
		-- 寻找锻造配置
		quality = med.Quality -- 品质
		starNum = med.StarNum -- 星数
	elseif type == 2 then
		local med = Schemes.Equipment:Get(goodsId)
		-- 寻找锻造配置
		quality = med.Quality -- 品质
		starNum = med.StarNum -- 星数
	elseif type == 3 then
		local med = Schemes.Equipment:Get(goodsId)
		-- 寻找锻造配置
		quality = entity:GetProperty(EQUIPMENT_FIELD.EQUIPMENT_FIELD_QUALITY) -- 品质
		starNum = entity:GetProperty(EQUIPMENT_FIELD.EQUIPMENT_FIELD_STARNUM) -- 星数
	end

	local smeltCfg = Schemes.EquipSmelt:Get(goodsCfg.SmeltID, quality, starNum) -- 当前锻造配置
	print('------------当前锻造配置----------', goodsCfg.SmeltID, quality, starNum)
	if smeltCfg then
		print('------------smeltCfg----------', smeltCfg.ID)
		local attrList = ActorProp.GetTransAttributeBySmelt(smeltCfg) -- 当前锻造配置
		-- 正式开始判断，根据是否满星，选择哪张作为主表进行遍历
		---@type ActorProp_EquipSmeltAttribute[]
		local mainAttrList
		local nextAttrList                                                  -- 遍历所需用表
		local isMaxAttr = smeltCfg.NextQuality == 0 and smeltCfg.NextStarNum == 0 -- 满星&满品时，为true
		mainAttrList = attrList
		if isMaxAttr then
			nextAttrList = attrList
		else
			-- 下一级锻造配置
			local nextSmeltCfg = Schemes.EquipSmelt:Get(smeltCfg.ID, smeltCfg.NextQuality, smeltCfg.NextStarNum)
			nextAttrList = ActorProp.GetTransAttributeBySmelt(nextSmeltCfg)
		end
		-- 遍历属性表
		local curExp = entity:GetProperty(EQUIPMENT_FIELD.EQUIPMENT_FIELD_SMELTEXP) -- 当前经验
		for _, attr in ipairs(mainAttrList) do
			local value = attr.attrValue                                      -- 属性值
			-- 若不是满星，需要寻找结合【下一锻造配置】和【当前锻造配置】相比较
			local curValue = 0                                                -- 当前属性值
			for _, curAttr in ipairs(mainAttrList) do
				if curAttr and curAttr.attrType == attr.attrType then
					curValue = curAttr.attrValue
				end
			end
			value = curValue
			table.insert(buffList, { name = attr.attrName, value = value })
		end

		for _, attr in ipairs(nextAttrList) do
			local value = attr.attrValue -- 属性值
			local curValue = 0  -- 当前属性值
			for _, curAttr in ipairs(nextAttrList) do
				if curAttr and curAttr.attrType == attr.attrType then
					curValue = curAttr.attrValue
				end
			end
			value = curValue
			table.insert(buffList, { name = attr.attrName, value = value })
		end
	end
	return buffList
end

---获取奇物装备，器魂类型等级
---@param entity integer
---@return  {level:integer,exp:integer,maxExp:integer}
function ActorProp.GetEquipSmeltLevel(entity)
	local level = 1
	local id = entity:GetProperty(ENTITY_FIELD.ENTITY_FIELD_ID) -- 物品ID
	local exp = entity:GetProperty(EQUIPMENT_FIELD.EQUIPMENT_FIELD_SMELTEXP)
	local cfg = Schemes.EquipSmelt:Get(Schemes.Equipment:Get(id).SmeltID, 0, 0)
	for i, v in ipairs(cfg.LevelStepRate) do
		level = i
		if exp < v then
			break
		end
	end
	local maxExp = cfg.LevelStepRate[level]
	if level > 1 then
		exp = exp - cfg.LevelStepRate[level - 1]
		maxExp = maxExp - cfg.LevelStepRate[level - 1]
	end
	return { level = level, exp = exp, maxExp = maxExp }
end

---获取属性描述
---@param entity integer
function ActorProp.GetEquipSmeltProperty(entity)
	local list = {}
	if entity then
		local id = entity:GetProperty(ENTITY_FIELD.ENTITY_FIELD_ID)            -- 物品Id
		local cfg = Schemes.Equipment:Get(id)                                  -- 物品配置
		if cfg then
			local quality = entity:GetProperty(EQUIPMENT_FIELD.EQUIPMENT_FIELD_QUALITY) -- 品质
			local starNum = entity:GetProperty(EQUIPMENT_FIELD.EQUIPMENT_FIELD_STARNUM) -- 星数
			local smeltCfg = Schemes.EquipSmelt:Get(cfg.SmeltID, quality, starNum) -- 当前锻造配置
			if smeltCfg then
				local eEffect, _name, _value
				for i, effectID in ipairs(smeltCfg.EquipEffectID) do
					eEffect = Schemes.EquipEffect:Get(math.floor(effectID / 10000))
					if eEffect then
						_name = GetAttributeTypeDesc(eEffect.EffectType)
						_value = ActorProp.GetAttributeShowText(eEffect.EffectType, eEffect.EffectParam)

						table.insert(list, { desc = _name, value = _value, })
					end
				end
			end
		end
	end
	return list
end

--判断是否是百分比计算
function ActorProp.IsPercentum(key)
	local prop = ActorProp.PropNames[key]
	if prop then
		return prop.PctEnd
	end
	return false
end

--角色属性显示
ActorProp.RoleAttributes = {
	'HP',
	'PhysicsAttack',
	'Defense',
	'AttackSpeed',
	'AbsorbHP',
	'CriticalHitRate',
	'DamageCritical',
	'DamageAdd',
	'DamageReduction',
	'AcquireGold',
	'AcquireExp',
	'DamageBossAddPct',
}

---升星表属性(包含下一级)
---@class EquipSmeltAttribute
---@field Type integer 属性类型
---@field Name string 属性名称
---@field Value integer 当前值(计算后的属性值)
---@field NextValue integer 下一级值(计算后的属性值)
---@field Unit string 单位

---获取升星表属性(包含下一级)
---@param smeltCfg integer @EquipSmelt其中一栏的配置信息
---@return EquipSmeltAttribute[]
function ActorProp.GetEquipSmeltAttribute(smeltCfg)
	if not smeltCfg then
		warn('EquipSmelt配置错误')
		return {}
	end

	-----------------计算基础属性-------------------------
	--当前基础属性
	local attr = {
		[EquipSmeltAttributeEnum.HP] = smeltCfg.HP,
		[EquipSmeltAttributeEnum.Attack] = smeltCfg.Attack,
		[EquipSmeltAttributeEnum.Defense] = smeltCfg.Defense,
		[EquipSmeltAttributeEnum.PhysicsAttack] = smeltCfg.PhysicsAttack,
		[EquipSmeltAttributeEnum.MagicAttack] = smeltCfg.MagicAttack,
		[EquipSmeltAttributeEnum.PhysicsDefense] = smeltCfg.PhysicsDefense,
		[EquipSmeltAttributeEnum.MagicDefense] = smeltCfg.MagicDefense,
		[EquipSmeltAttributeEnum.CriticalStrike] = smeltCfg.CriticalStrike,
		[EquipSmeltAttributeEnum.AntiCriticalStrike] = smeltCfg.AntiCriticalStrike,
		[EquipSmeltAttributeEnum.Parry] = smeltCfg.Parry,
		[EquipSmeltAttributeEnum.AntiParry] = smeltCfg.AntiParry,
		[EquipSmeltAttributeEnum.Dodge] = smeltCfg.Dodge,
		[EquipSmeltAttributeEnum.Hit] = smeltCfg.Hit,
		[EquipSmeltAttributeEnum.Armor] = smeltCfg.Armor,
		[EquipSmeltAttributeEnum.AntiArmor] = smeltCfg.AntiArmor,
		[EquipSmeltAttributeEnum.DamageReductin] = smeltCfg.DamageReduction,
		[EquipSmeltAttributeEnum.DamageAdd] = smeltCfg.DamageAdd,
		[EquipSmeltAttributeEnum.MoveSpeed] = smeltCfg.MoveSpeed,
		[EquipSmeltAttributeEnum.AttackDefense] = smeltCfg.AttackDefense,
	}
	local equipEffectID1 = smeltCfg.EquipEffectID

	--下一级基础属性
	local nextAttr = {}
	local equipEffectID2 = {}
	if smeltCfg.LevelMaxExp ~= -1 then
		local nextSmeltCfg = Schemes.EquipSmelt:Get(smeltCfg.ID, smeltCfg.NextQuality, smeltCfg.NextStarNum)
		if nextSmeltCfg then
			nextAttr = {
				[EquipSmeltAttributeEnum.HP] = nextSmeltCfg.HP,
				[EquipSmeltAttributeEnum.Attack] = nextSmeltCfg.Attack,
				[EquipSmeltAttributeEnum.Defense] = nextSmeltCfg.Defense,
				[EquipSmeltAttributeEnum.PhysicsAttack] = nextSmeltCfg.PhysicsAttack,
				[EquipSmeltAttributeEnum.MagicAttack] = nextSmeltCfg.MagicAttack,
				[EquipSmeltAttributeEnum.PhysicsDefense] = nextSmeltCfg.PhysicsDefense,
				[EquipSmeltAttributeEnum.MagicDefense] = nextSmeltCfg.MagicDefense,
				[EquipSmeltAttributeEnum.CriticalStrike] = nextSmeltCfg.CriticalStrike,
				[EquipSmeltAttributeEnum.AntiCriticalStrike] = nextSmeltCfg.AntiCriticalStrike,
				[EquipSmeltAttributeEnum.Parry] = nextSmeltCfg.Parry,
				[EquipSmeltAttributeEnum.AntiParry] = nextSmeltCfg.AntiParry,
				[EquipSmeltAttributeEnum.Dodge] = nextSmeltCfg.Dodge,
				[EquipSmeltAttributeEnum.Hit] = nextSmeltCfg.Hit,
				[EquipSmeltAttributeEnum.Armor] = nextSmeltCfg.Armor,
				[EquipSmeltAttributeEnum.AntiArmor] = nextSmeltCfg.AntiArmor,
				[EquipSmeltAttributeEnum.DamageReductin] = nextSmeltCfg.DamageReduction,
				[EquipSmeltAttributeEnum.DamageAdd] = nextSmeltCfg.DamageAdd,
				[EquipSmeltAttributeEnum.MoveSpeed] = nextSmeltCfg.MoveSpeed,
				[EquipSmeltAttributeEnum.AttackDefense] = nextSmeltCfg.AttackDefense,
			}
			equipEffectID2 = nextSmeltCfg.EquipEffectID
		end
	end

	local attrList = {}
	local unit, tempValue1, tempValue2
	for _attrType, _attrValue in ipairs(attr) do
		tempValue1 = _attrValue
		tempValue2 = nextAttr[_attrType] or 0
		if tempValue1 > 0 or tempValue2 > 0 then
			unit = ''
			if EquipSmeltAttributeShowType[_attrType] == 2 then
				tempValue1 = HelperL.Round((tempValue1 / 100), 2)
				tempValue2 = HelperL.Round((tempValue2 / 100), 2)
				unit = '%'
			end

			table.insert(attrList, {
				Type = _attrType,
				Name = EquipSmeltAttributeName[_attrType],
				Value = tempValue1,
				NextValue = tempValue2,
				Unit = unit,
			})
		end
	end



	-----------------计算特殊属性-------------------------

	local propID1, level1
	local propID2, level2
	local effectCfg1, effectCfg2
	local effectType
	local num = math.max(#equipEffectID1, #equipEffectID2)
	for i = 1, num, 1 do
		propID1, level1 = HelperL.GetPropEffectIDAndLevel(equipEffectID1[i])
		propID2, level2 = HelperL.GetPropEffectIDAndLevel(equipEffectID2[i])
		effectCfg1 = Schemes.EquipEffect:Get(propID1)
		effectCfg2 = Schemes.EquipEffect:Get(propID2)
		if effectCfg1 or effectCfg1 then
			tempValue1, tempValue2, effectType = 0, 0, 0
			--当前特殊属性
			if effectCfg1 then
				tempValue1 = HelperL.GetPropEffectValue(effectCfg1, level1)
				effectType = effectCfg1.EffectType
			end
			--下一级特殊属性
			if effectCfg2 then
				tempValue2 = HelperL.GetPropEffectValue(effectCfg2, level2)
				effectType = effectCfg2.EffectType
			end

			if tempValue1 > 0 or tempValue2 > 0 then
				if AttributeShowType[effectType] == 2 then
					tempValue1 = HelperL.Round(tempValue1 / 100, 2)
					tempValue2 = HelperL.Round(tempValue2 / 100, 2)
					unit = '%'
				else
					unit = ''
				end

				table.insert(attrList, {
					Type = effectType,
					Name = GetAttributeTypeDesc(effectType),
					Value = tempValue1,
					NextValue = tempValue2,
					Unit = unit,
				})
			end
		end
	end

	return attrList
end
