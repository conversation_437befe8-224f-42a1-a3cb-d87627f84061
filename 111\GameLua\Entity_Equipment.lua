---
--- Generated by <PERSON><PERSON><PERSON>(https://github.com/<PERSON><PERSON>ua)
--- Created by Administrator.
--- DateTime: 2023/7/29 0:05
---

local EntityBase = require("EntityBase")

---@class EquipmentEntity:EntityBase
---@field EntityBase
local this = class(EntityBase)

function this:_init()
    this._base._init(self)
    self.name = "_Equip"
    --- 实体类型
    self.entity_class = ENTITY_CLASS.ENTITY_CLASS_EQUIPMENT
end

--- 设置数据
---@parma d @装备相关数据
function this:SetData(d)
    --- uid
    self.uid = d.EntityUID
    --- 是否绑定 0未绑定 1绑定
    self.flags = d.Flags
    for k, v in ipairs(d.NumProp) do
        self:SetProperty(k - 1, v)
    end
end

return this