﻿--热更数据
STREAMING_DATA = {
	--签到连接
	SIGN_IN_URL = "https://tap.cn/L7QHmxXX?channel=rep-rep_i0zwg8kgtip_h5url882",
	--关注跳转链接
	ATTENTION_URL = "",
	--补偿公告
	COMPENSATION_AFFICHE =
	"为了提升游戏的整体体验，计划于\n2024年2月6日14点进行了版本更新\n特别提醒：由于本次更新伙伴系统还在升级优化，更新后暂时关闭伙伴系统。（伙伴数据都在，战斗不能使用技能。）\n更新内容如下：\n1. 战斗表现和操作策略进行大改\n2. 增加战斗结算可看广告获取双倍奖励\n3. 优化看广告获取奖励的功能\n5.菌落改为50级开放，伙伴改为75级开放（老用户已开放等级没到的数据都在，仅入口关闭）\n6. 10-100关，主线关卡每5关必定可获得细胞奖励。\n7. 生存副本产出改为细胞进化道具\n8. 修复其他一些bug\n9. 装备、菌落数值调整优化，老角色战力会有所下降\n10. 更新后，会根据角色等级给予老用户补偿\n\n本次更新由于伙伴系统的更新，老玩家会出现总战力下降等问题，非常抱歉，还请大家多多理解，后期伙伴系统完善会更新版本打开继续给大家体验。",
}


--开关，true开，false关
SWITCH = {
	RECHARGE = false,                         --充值
	ATTENTION_URL = false,                    --关注入口
	COMBAT_ATTRIBUTES = false,                --战斗属性
	EQUIP_BOX_AUTO = false,                   --装备宝箱：开启自动抽奖，关闭10连抽
	GAME_ECTYPE_RANKING_CLOSE = false,        --关闭副本排行榜
	SIGN_IN = false,                          --签到按钮入口开关
	UPDATE_APK = false,                       --更新apk开关
	KNAPSACK_SHOW_ITEM = false,               --背包显示道具开关
	CERTIFICATION = false,                    --关闭实名认证
	SCREEN_TIME_TEST = false,                 --防沉迷测试
	CLOSE_GUIDE = false,                      --关闭
	CANCEL_RESTRICTION_OF_LOG_IN_MINORS = true ,--取消未成年人登录限制#设等级 44
	FASTER_CUSTOMS_CLEARANCE =false , --快速通关true         --false
}

--防沉迷测试列表
SCREEN_TIME_TEST_LIST = {
	-- ['角色UserID'] = true,
}

-- 服务端应用程序的Url根路径(结尾不要加/)
SrvUrlRoot = {
	Charge = "https://zl-wsr.0037wan.com/charge62",
	WCollect = "http://************/WCollect62",
}

--开启日志列表
ENABLE_LOG_LIST = {
	-- ['角色UserID'] = true,
	['100704'] = true,
}

print("zones ...")
-- 外网tap正式服： 43.136.26.246:2091     区ID=2002
-- 外网内部测试服： 43.136.26.246:2091     区ID=2001
-- 本地127.0.0.1:1491   区ID = 1001
-- 本地127.0.0.1:1491   区ID = 1002
-- --推荐区，新号进哪个服，改推荐区即可！
-- FineZoneList = { 2001 }
Zones =
        ---下面：内网本地2001区
--{ 	[3] = { 3, 2001, "ews://127.0.0.1:45800/GsProxy", '127.0.0.1', 2091, '127.0.0.1', "本机私服1002", "1", "2", "服务器于3月28日15:30进行例行维护，预计维护3小时！", "2035-03-15 18:00" },}    FineZoneList = { 2001 }
          ---下面：连内网本地私服1121区
--{           [1] = { 1, 1101, "ews://127.0.0.1:45800/GsProxy", '127.0.0.1', 2091, '127.0.0.1', "外网内部测试服", "1", "2", "服务器于3月28日15:30进行例行维护，预计维护3小时！", "2035-03-15 18:00" },}    FineZoneList = { 1101 }
         ---下面：连外网1121区
{[1] = { 1, 1181, "wss://zl-wsr.0037wan.com/GsProxy", '43.138.151.246', 1183, '172.16.0.11', "外网内部测试服", "1", "2", "服务器于3月28日15:30进行例行维护，预计维护3小时！", "2035-03-15 18:00" },}  FineZoneList = { 1181 }
return Zones
