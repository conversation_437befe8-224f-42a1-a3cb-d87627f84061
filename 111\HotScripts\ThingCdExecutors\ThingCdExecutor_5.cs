// ReSharper disable InconsistentNaming

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;

using Apq.Extension;
using Apq.Unity3D.UnityHelpers;

using Cysharp.Threading.Tasks;

using DataStructure;

using RxEventsM2V;

using Thing;

using UniRx;

using UnityEngine;

using X.PB;

using CsvTables;

using Props;

using View;

namespace ThingCdExecutors
{
    /// <summary>
    ///     玩家阵营的排射
    /// </summary>
    public class ThingCdExecutor_5 : ActorGunCdExecutor
    {
        /// <inheritdoc />
        public override async UniTaskVoid DoShoot(CancellationToken token)
        {
            // V56.5 获取当前技能信息
            string skillName = GunThing.CsvRow_Gun.Value?.Name ?? "未知排射技能";
            int currentPriority = GetCurrentAttackPriority();
            float globalCooldown = GetCurrentGlobalCooldown();

         //   Debug.Log($"V56.5 排射技能尝试发射: {skillName}, 优先级={currentPriority}, 公共CD={globalCooldown}秒");

            // V56.5 使用新的技能发射协调机制
            if (Actor != null && !Actor.RequestSkillFire(skillName, currentPriority, GunThing, globalCooldown))
            {
             //   Debug.Log($"V56.5 排射技能发射请求被拒绝: {skillName}");
                return;
            }

            // 找一个敌人作为攻击方向
            DistanceThing distanceEnemy = Thing.FindEnemy();

            // 没有攻击目标，不发子弹
            if (distanceEnemy is not { Thing2: not null })
            {
                return;
            }

          //  Debug.Log($"V56.5 排射技能通过所有检查，开始发射: {skillName}, 优先级={currentPriority}");

            // 按一轮攻击的持续时长预设结束时间
            base.DoShoot(token).Forget();

            await UniTask.SwitchToMainThread();

            // 确保此方法用于角色的枪
            if (Actor == null)
            {
                return;
            }

            // // 技能可由令牌或玩家死亡的方式停止
            // var cts_Skill = CancellationTokenSource.CreateLinkedTokenSource(
            //     CTS_Shooter.Token
            //     //,Actor.GetCancellationTokenOnDestroy()
            // );

            try
            {
                // 射击的基准方向
                Vector3 baseDir_1 = (distanceEnemy.Thing2.Position - Actor.Position).normalized;
                // var baseDir_1 = Vector3.right.RotateAround(Vector3.forward, 50);

                #region 启动每个角度的任务

                await UniTask.NextFrame();
                AbreastAngle(token, baseDir_1, PropType.AbreastAngle00Shoot, PropType.AbreastAngle00ShootTimes,
                    PropType.AbreastAngle00MaxShootTimes, PropType.AbreastAngle00DelayList,
                    PropType.AbreastAngle00BulletCount, PropType.AbreastAngle00MaxBulletCount,
                    PropType.AbreastAngle00BulletSpacing).Forget();

                await UniTask.NextFrame();
                AbreastAngle(token, baseDir_1, PropType.AbreastAngle01Shoot, PropType.AbreastAngle01ShootTimes,
                    PropType.AbreastAngle01MaxShootTimes, PropType.AbreastAngle01DelayList,
                    PropType.AbreastAngle01BulletCount, PropType.AbreastAngle01MaxBulletCount,
                    PropType.AbreastAngle01BulletSpacing).Forget();

                await UniTask.NextFrame();
                AbreastAngle(token, baseDir_1, PropType.AbreastAngle02Shoot, PropType.AbreastAngle02ShootTimes,
                    PropType.AbreastAngle02MaxShootTimes, PropType.AbreastAngle02DelayList,
                    PropType.AbreastAngle02BulletCount, PropType.AbreastAngle02MaxBulletCount,
                    PropType.AbreastAngle02BulletSpacing).Forget();

                await UniTask.NextFrame();
                AbreastAngle(token, baseDir_1, PropType.AbreastAngle03Shoot, PropType.AbreastAngle03ShootTimes,
                    PropType.AbreastAngle03MaxShootTimes, PropType.AbreastAngle03DelayList,
                    PropType.AbreastAngle03BulletCount, PropType.AbreastAngle03MaxBulletCount,
                    PropType.AbreastAngle03BulletSpacing).Forget();

                await UniTask.NextFrame();
                AbreastAngle(token, baseDir_1, PropType.AbreastAngle04Shoot, PropType.AbreastAngle04ShootTimes,
                    PropType.AbreastAngle04MaxShootTimes, PropType.AbreastAngle04DelayList,
                    PropType.AbreastAngle04BulletCount, PropType.AbreastAngle04MaxBulletCount,
                    PropType.AbreastAngle04BulletSpacing).Forget();

                await UniTask.NextFrame();
                AbreastAngle(token, baseDir_1, PropType.AbreastAngle05Shoot, PropType.AbreastAngle05ShootTimes,
                    PropType.AbreastAngle05MaxShootTimes, PropType.AbreastAngle05DelayList,
                    PropType.AbreastAngle05BulletCount, PropType.AbreastAngle05MaxBulletCount,
                    PropType.AbreastAngle05BulletSpacing).Forget();

                await UniTask.NextFrame();
                AbreastAngle(token, baseDir_1, PropType.AbreastAngle06Shoot, PropType.AbreastAngle06ShootTimes,
                    PropType.AbreastAngle06MaxShootTimes, PropType.AbreastAngle06DelayList,
                    PropType.AbreastAngle06BulletCount, PropType.AbreastAngle06MaxBulletCount,
                    PropType.AbreastAngle06BulletSpacing).Forget();

                await UniTask.NextFrame();
                AbreastAngle(token, baseDir_1, PropType.AbreastAngle07Shoot, PropType.AbreastAngle07ShootTimes,
                    PropType.AbreastAngle07MaxShootTimes, PropType.AbreastAngle07DelayList,
                    PropType.AbreastAngle07BulletCount, PropType.AbreastAngle07MaxBulletCount,
                    PropType.AbreastAngle07BulletSpacing).Forget();

                await UniTask.NextFrame();
                AbreastAngle(token, baseDir_1, PropType.AbreastAngle08Shoot, PropType.AbreastAngle08ShootTimes,
                    PropType.AbreastAngle08MaxShootTimes, PropType.AbreastAngle08DelayList,
                    PropType.AbreastAngle08BulletCount, PropType.AbreastAngle08MaxBulletCount,
                    PropType.AbreastAngle08BulletSpacing).Forget();

                await UniTask.NextFrame();
                AbreastAngle(token, baseDir_1, PropType.AbreastAngle09Shoot, PropType.AbreastAngle09ShootTimes,
                    PropType.AbreastAngle09MaxShootTimes, PropType.AbreastAngle09DelayList,
                    PropType.AbreastAngle09BulletCount, PropType.AbreastAngle09MaxBulletCount,
                    PropType.AbreastAngle09BulletSpacing).Forget();

                #endregion

                OnAfterShoot();
            }
            catch
            {
                // ignore
            }
        }

        /// <summary>
        ///     指定角度的排射(多轮,正负角度)
        /// </summary>
        /// <param name="token"></param>
        /// <param name="baseDir_1">射击的基准方向</param>
        /// <param name="propType_ShootAngle">排射角度</param>
        /// <param name="propType_ShootTimes">排射次数</param>
        /// <param name="propType_MaxShootTimes">排射次数上限</param>
        /// <param name="propType_DelayList">排射延时列表</param>
        /// <param name="propType_BulletCount">排射子弹数量</param>
        /// <param name="propType_MaxBulletCount">排射子弹数量上限</param>
        /// <param name="propType_BulletSpacing">排射子弹间距</param>
        private async UniTaskVoid AbreastAngle(CancellationToken token, Vector3 baseDir_1,
            PropType propType_ShootAngle, PropType propType_ShootTimes, PropType propType_MaxShootTimes,
            PropType propType_DelayList, PropType propType_BulletCount, PropType propType_MaxBulletCount,
            PropType propType_BulletSpacing)
        {
            try
            {
                await UniTask.SwitchToMainThread();
                // await UniTask.Delay(System.TimeSpan.FromSeconds(delay), cancellationToken: token);

                if (token.IsCancellationRequested)
                {
                    return;
                }

                // 排射次数
                long shootTimes = Thing.GetTotalLong(propType_ShootTimes).FirstOrDefault();
                long maxShootTimes = Thing.GetTotalLong(propType_MaxShootTimes).FirstOrDefault();
                if (shootTimes > maxShootTimes)
                {
                    shootTimes = maxShootTimes;
                }

                // 没有次数，退出
                if (shootTimes <= 0)
                {
                    return;
                }

                float moveDuration = 0;
                PlayerMove playerMove = Actor.ThingBehaviour.GetComponent<PlayerMove>();
                if (playerMove)
                {
                    moveDuration = playerMove.MoveDuration;
                }

                // 排射角度
                double shootAngle = Thing.GetTotalDouble(propType_ShootAngle).FirstOrDefault();

                // 排射延时列表(秒)
                List<double> delayList = Thing.GetTotalDouble(propType_DelayList);

                // 排射子弹数量
                long bulletCount = Thing.GetTotalLong(propType_BulletCount).FirstOrDefault();
                long maxBulletCount = Thing.GetTotalLong(propType_MaxBulletCount).FirstOrDefault();
                if (bulletCount > maxBulletCount)
                {
                    bulletCount = maxBulletCount;
                }

                // 排射子弹间距
                double bulletSpacing = Thing.GetTotalDouble(propType_BulletSpacing).FirstOrDefault();

                // 正角度射击方向
                Vector3 shootDir1_1 = baseDir_1.RotateAround(Vector3.forward, (float)shootAngle);
                // 负角度射击方向
                Vector3 shootDir2_1 = Vector3.zero;
                // 角度不等于0、180时，负角度也发射
                if (shootAngle != 0 && Math.Abs(shootAngle - 180) > float.Epsilon)
                {
                    // 射击方向
                    shootDir2_1 = baseDir_1.RotateAround(Vector3.forward, (float)-shootAngle);
                }

                for (int i = 0; i < shootTimes; i++)
                {
                    float delay = (float)delayList.IndexOfOrLastOrDefault(i);

                    if (shootDir1_1 != Vector3.zero)
                    {
                        AbreastOneAngle(token, delay, shootDir1_1, bulletCount, bulletSpacing, moveDuration).Forget();
                    }

                    if (shootDir2_1 != Vector3.zero)
                    {
                        AbreastOneAngle(token, delay, shootDir2_1, bulletCount, bulletSpacing, moveDuration).Forget();
                    }
                }
            }
            catch (OperationCanceledException) { throw; }
            catch (MissingReferenceException) { }
            catch (Exception ex)
            {
                Debug.LogException(ex);
            }
            //catch { }
        }

        /// <summary>
        ///     指定角度的排射(一轮)
        /// </summary>
        /// <param name="token"></param>
        /// <param name="delay">延时:秒</param>
        /// <param name="shootDir_1">射击的方向</param>
        /// <param name="bulletCount">排射子弹数量</param>
        /// <param name="bulletSpacing">排射子弹间距</param>
        /// <param name="moveDuration">角色移动时长</param>
        private async UniTaskVoid AbreastOneAngle(CancellationToken token, float delay, Vector3 shootDir_1,
            long bulletCount, double bulletSpacing, float moveDuration)
        {
            try
            {
                await UniTask.Delay(TimeSpan.FromSeconds(delay), cancellationToken: token);

                // 开火声音
                MessageBroker.Default.Publish(new PlayShootSound { Shooter = this });
                //Debug.Log($"一轮发射子弹数:{bulletQty}");

                // 子弹半径
                double bulletRadius = Thing.GetTotalDouble(PropType.BulletRadius).FirstOrDefault();
                // 所需切线的长度
                float tangentLength = (float)((bulletRadius * bulletCount) + (bulletSpacing * (bulletCount - 1)));
                // 计算切线
                LineSegment tangent = Actor.CircularArea2D.CalcTangent(shootDir_1, tangentLength);
                // 均匀分割切线，子弹从这些分割点出生
                List<Vector3> pos = tangent.CalcEvenlySplit((int)bulletCount);

                int maxPenetrateTimes = (int)Thing.GetTotalLong(PropType.MaxPenetrateTimes).FirstOrDefault();
                int maxBounceTimes = (int)Thing.GetTotalLong(PropType.MaxBounceTimes).FirstOrDefault();
                int maxSeparateTimes = (int)Thing.GetTotalLong(PropType.MaxSeparateTimes).FirstOrDefault();

                pos.ForEach(p =>
                {
                    // // 如果该位置范围内有子弹不可通过的障碍物，则跳过
                    // var obstacles = SingletonMgr.Instance.BattleMgr.FindObstacles(new()
                    // {
                    //     Center = p, Radius = (float)bulletRadius,
                    // });
                    // if (obstacles is { Count: > 0 } && obstacles.Any(x => x.TerrainType >= TerrainType.Wall))
                    // {
                    //     return;
                    // }

                    BulletThing bullet = Thing.CreateBullet(this, null, null, 0, maxPenetrateTimes, maxBounceTimes,
                        maxSeparateTimes);

                    // 根据角色朝向计算前方偏移
                    float forwardOffsetX = 2f;
                    if (Actor.ThingBehaviour != null)
                    {
                        var skeAni = Actor.ThingBehaviour.GetComponentInChildren<Spine.Unity.SkeletonAnimation>();
                        if (skeAni != null && skeAni.skeleton != null)
                        {
                            // ScaleX = -1 表示朝右，ScaleX = 1 表示朝左
                            forwardOffsetX = skeAni.skeleton.ScaleX == -1 ? 2f : -2f;
                        }
                    }

                    bullet.Position = p + new Vector3(forwardOffsetX, 6f, 0); // 根据角色朝向从前方+2、往上Y+6的位置发射
                    bullet.MoveDirection_Straight.Value = shootDir_1;
                    bullet.MoveDuration = moveDuration;

                    MessageBroker.Default.Publish(new BornBullet
                    {
                        Bullet = bullet,
                        // 上一次的位置:出生点往发射反方向走一个生物半径
                        PositionPre = p - (shootDir_1 * Actor.TotalProp_Radius)
                    });
                });
            }
            catch (OperationCanceledException) { throw; }
            catch (MissingReferenceException) { }
            catch (Exception ex)
            {
                Debug.LogException(ex);
            }
            //catch { }
        }

        /// <summary>
        // /// V56.0 获取当前技能的攻击动作优先级
        /// 通过解析子弹配置中的Icon字段获取
        /// </summary>
        /// <returns>攻击动作优先级，默认返回0</returns>
        private int GetCurrentAttackPriority()
        {
            try
            {
                // 获取子弹ID
                int bulletId = (int)GunThing.GetTotalLong(X.PB.PropType.BulletId).FirstOrDefault();
                if (bulletId <= 0) return 0;

                // 获取子弹配置
                if (!SingletonMgr.Instance.CsvLoaderMgr.GetOrAddLoader<BulletCsv>().Dic.TryGetValue(bulletId, out var bulletCfg))
                    return 0;

                // 解析Icon字段获取优先级
                string iconConfig = bulletCfg.Icon;
                if (string.IsNullOrWhiteSpace(iconConfig) || iconConfig == "0") return 0;

                string[] parameters = iconConfig.Split(';');
                if (parameters.Length < 4) return 0;

                if (int.TryParse(parameters[3], out int priority))
                {
                    return priority;
                }
            }
            catch (System.Exception ex)
            {
                Debug.LogWarning($"V56.0 获取排射攻击动作优先级失败: {ex.Message}");
            }

            return 0; // 默认优先级
        }

        /// <summary>
        // /// V56.0 获取当前技能的公共CD时长
        /// 根据排射次数和延时间隔计算：公共CD = 最后一次排射需要的时间 + 0.2秒
        /// </summary>
        /// <returns>公共CD时长（秒），默认返回0</returns>
        private float GetCurrentGlobalCooldown()
        {
            try
            {
                float maxShootTime = 0f;
                
                // 检查所有10个角度的排射配置
                PropType[] shootTimesProps = {
                    PropType.AbreastAngle00ShootTimes, PropType.AbreastAngle01ShootTimes, PropType.AbreastAngle02ShootTimes,
                    PropType.AbreastAngle03ShootTimes, PropType.AbreastAngle04ShootTimes, PropType.AbreastAngle05ShootTimes,
                    PropType.AbreastAngle06ShootTimes, PropType.AbreastAngle07ShootTimes, PropType.AbreastAngle08ShootTimes,
                    PropType.AbreastAngle09ShootTimes
                };
                
                PropType[] delayListProps = {
                    PropType.AbreastAngle00DelayList, PropType.AbreastAngle01DelayList, PropType.AbreastAngle02DelayList,
                    PropType.AbreastAngle03DelayList, PropType.AbreastAngle04DelayList, PropType.AbreastAngle05DelayList,
                    PropType.AbreastAngle06DelayList, PropType.AbreastAngle07DelayList, PropType.AbreastAngle08DelayList,
                    PropType.AbreastAngle09DelayList
                };

                for (int i = 0; i < shootTimesProps.Length; i++)
                {
                    long shootTimes = Thing.GetTotalLong(shootTimesProps[i]).FirstOrDefault();
                    if (shootTimes <= 0) continue;

                    List<double> delayList = Thing.GetTotalDouble(delayListProps[i]);
                    if (delayList.Count == 0) continue;

                    // 计算这个角度最后一次排射的时间
                    for (int j = 0; j < shootTimes && j < delayList.Count; j++)
                    {
                        maxShootTime = Math.Max(maxShootTime, (float)delayList[j]);
                    }
                }

                // // V56.0 公共CD = 最后一次排射时间 + 0.2秒
                float globalCooldown = maxShootTime + 0.2f;

                Debug.Log($"V56.0 排射技能公共CD计算: 最后排射时间={maxShootTime}秒, 公共CD={globalCooldown}秒");
                
                return globalCooldown;
            }
            catch (System.Exception ex)
            {
                Debug.LogWarning($"V56.0 获取排射公共CD失败: {ex.Message}，使用默认0.2秒");
            }

            return 0.2f; // 默认公共CD
        }
    }
}