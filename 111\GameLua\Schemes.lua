-- 表格读取
require 'schemes_pb'
--local luaID = ('Schemes')

Schemes = {}

--通用模板，如果你有特殊需求，则不适用，需要单独写(Lua解锁表格)
local function Schemes_Load(self, name)
	local ta = HotResManager.ReadPb(name)
	local m = schemes_pb[name]()
	m:ParseFromString(Helper.GetLuaByteBuffer(ta))
	self.name = name
	self.items = m.items
	local fastItems = {}
	for _, v in ipairs(self.items) do
		fastItems[v.ID] = v
	end
	self.fastItems = fastItems
end

--通用模板，如果你有特殊需求，则不适用，需要单独写(C#解锁表格)
local function Schemes_Load2(self, name)
	local m = {}
	m.items = {}

	if CsvReader4Lua then
		local fun = CsvReader4Lua[name]
		local list = fun()
		for i = 0, list.Length - 1, 1 do
			table.insert(m.items, list[i])
		end
	else
		warn('获取C#表格失败失败 name=', name)
	end

	self.name = name
	self.items = m.items
	local fastItems = {}
	for _, v in ipairs(self.items) do
		fastItems[v.Id] = v
	end
	self.fastItems = fastItems
end

local function Schemes_Get(self, id)
	id = tonumber(id)
	if not id then
		warn('取表格时没传id', tostring(self.name), debug.traceback())
		return nil
	end
	return self.fastItems[id]
end

---根据类型获取表格数据
---@param self integer 表格原数据
---@param type integer 类型
---@return unknown
local function Schemes_GetByType(self, type)
	local typeList = {}
	for i, v in ipairs(self.items) do
		if not typeList[v.Type] then
			typeList[v.Type] = {}
		end
		table.insert(typeList[v.Type], v)
	end
	self._typeList = typeList
	--重置函数
	self.GetByType = function(_self, _type)
		--print('------以重置函数-----', self.name)
		return _self._typeList[_type]
	end
	--print('------未重置函数-----', self.name)
	return self._typeList[type]
end

--根据类型筛选表格数据
local function Schemes_GetByTypeList(self)
	self:GetByType()
	return self._typeList
end

--Lua解析表格
local pbSchemeTable = {
	'LoadingTips', 'UIConfig', 'Cooldown', 'ChapEctype', 'TowerBattleStage', 'TowerBattleGrid',
	'TowerBattleObject', 'TowerBattleMainLevel', 'TowerBattleSubLevel',
	'TowerBattleExEffect', 'GameScene', 'Monster', 'Creature', 'FxConfig', 'Skill', 'TrapConfig', 'CreatureMotionConfig',
	'Buff', 'BuffEffect', 'Guide', 'GuidePrompt', 'GuideEvent',
	'FunctionOpen', 'GuideUI', 'TowerBattleStageAction', 'TowerBattleCollect', 'Equipment', 'Medicament',
	'TowerBattleAnnounce', 'UIAtlasConfig',
	'Sound', 'TowerBattleModelCutScene', 'ShowStoryConfig', 'MatchConfig', 'PrizeTreasureBoxUnlock', 'PriceFormula',
	'EquipSmelt', 'EquipCollect', 'EquipSmeltBase', 'EquipEffect', 'EquipExchange',
	'ConstValue', 'VipValue', 'FirstRecharge', 'RechargeCard', 'SegmentUpStar', 'PrizeTable', 'PrizeCoef',
	'PrizeVariableGoods', 'PrizeTime', 'SevenDaysInvest',
	'TowerBattleFormationGuide', 'ActivityTimeShopping', 'Store', 'PrivilegeCard', 'GiftShow', 'PrizeSignin',
	'RandomName', 'ActivityCount', 'ActivityCountPrize', 'LoginReward', 'CatMainStage',
	'ActivityNewbieTargetReward', 'TaskBranch', 'TaskWay', 'TaskFastUpgrade', 'TaskCamera', 'PrizeLoop', 'PrizeAchieve',
	'CatSkill', 'CatSkillEffect', 'CatBrushMonster', 'EquipWeapon', 'CatsKillLevelUp', 'CatsKillSelect', 'CatDrop',
	'PrizePatrol', 'CatMainLevel',
	'EquipDecomposition', 'ShowMessages', 'PlayerBaseProp', 'MonsterNew', 'StoreRefresh', 'GoodsFrom', 'CatExchangeBox',
	'CatPets', 'GemCreate', 'EquipPolish', 'SkillUp', "EquipForgeAddition", "TreasureEastCity", "CommonText",
	'LotteryLevel', 'CatFriend', "Gemstone", "Tips", "MissionInfo", "BattleBrushEnemy", 'EquipMount', 'Task',
}

--C#解析表格
local pbSchemeTable2 = {
	"Gun", "Bullet", "CommonProp", "EquipSmeltStar", "EquipSmeltStarData", "PrizesevenSignin",
}

local pbSchemeNum = #pbSchemeTable

-- ChapEctype
Schemes.CatDrop = {}
function Schemes.CatDrop.Init()
	local dropDic = {}
	for _, v in ipairs(Schemes.CatDrop.items) do
		dropDic[v.ID] = {}
		local idStrList = HelperL.Split(v.Goods, "|")
		for i = 1, #idStrList do
			local infoList = HelperL.Split(idStrList[i], ";")
			local dropInfo = {}
			dropInfo.DropType = tonumber(infoList[1])
			dropInfo.DropID = tonumber(infoList[2])
			dropInfo.DropNum = tonumber(infoList[3])
			dropInfo.DropPro = tonumber(infoList[4])
			dropInfo.DropCoorID = tonumber(infoList[5])
			table.insert(dropDic[v.ID], dropInfo)
		end
	end
	Schemes.CatDrop.dropDic = dropDic
end

function Schemes.CatDrop:GetByDropID(id)
	return self.dropDic[id]
end

-- ChapEctype
Schemes.ChapEctype = {}
function Schemes.ChapEctype.Init()
	local itemByEctypeID = {}
	for _, v in ipairs(Schemes.ChapEctype.items) do
		itemByEctypeID[v.EctypeID] = v
	end
	Schemes.ChapEctype.itemByEctypeID = itemByEctypeID
end

function Schemes.ChapEctype:GetByEctypeID(id)
	return self.itemByEctypeID[id]
end

-- TowerBattleMainLevel
Schemes.TowerBattleMainLevel = {}
function Schemes.TowerBattleMainLevel.Init()
	local itemBySubID = {}
	local itemByRaceType = {}
	for _, v in ipairs(Schemes.TowerBattleMainLevel.items) do
		for m, n in ipairs(v.LevelList) do
			itemBySubID[n] = v
		end
		itemByRaceType[v.RaceType] = v
	end
	Schemes.TowerBattleMainLevel.itemBySubID = itemBySubID
	Schemes.TowerBattleMainLevel.itemByRaceType = itemByRaceType
end

function Schemes.TowerBattleMainLevel:GetBySubID(id)
	return self.itemBySubID[id]
end

function Schemes.TowerBattleMainLevel:GetByStageID(id)
	local subLevelConfig = Schemes.TowerBattleSubLevel:GetByStageID(id)
	if subLevelConfig then
		return self.itemBySubID[subLevelConfig.ID]
	end
	return nil
end

function Schemes.TowerBattleMainLevel:GetByRaceType(type)
	return self.itemByRaceType[type]
end

-- TowerBattleSubLevel
Schemes.TowerBattleSubLevel = {}
function Schemes.TowerBattleSubLevel.Init()
	local itemByStageID = {}
	for _, v in ipairs(Schemes.TowerBattleSubLevel.items) do
		for m, n in ipairs(v.StageList) do
			if not itemByStageID[n] then
				itemByStageID[n] = v
			end
		end
	end
	Schemes.TowerBattleSubLevel.itemByStageID = itemByStageID
end

function Schemes.TowerBattleSubLevel:GetByStageID(id)
	return self.itemByStageID[id]
end

-- TowerBattleObject
Schemes.TowerBattleObject = {}
function Schemes.TowerBattleObject.Init()
	local itemByType = {}
	for _, v in ipairs(Schemes.TowerBattleObject.items) do
		local targetList = itemByType[v.ObjType]
		if not targetList then
			targetList = {}
			itemByType[v.ObjType] = targetList
		end
		targetList[v.ObjParam1] = v
	end
	Schemes.TowerBattleObject.itemByType = itemByType
end

function Schemes.TowerBattleObject:GetByType(objType, targetID)
	local targetList = self.itemByType[objType]
	if not targetList then
		return nil
	end

	return targetList[targetID]
end

-- StoreRefresh
Schemes.StoreRefresh = {}
function Schemes.StoreRefresh.Init()
	local item = {}
	for _, v in ipairs(Schemes.StoreRefresh.items) do
		item[v.ID] = item[v.ID] or {}
		item[v.ID][v.Times] = v
	end
	Schemes.StoreRefresh.item = item
end

function Schemes.StoreRefresh:GetByTimes(storeID, times)
	local targetList = self.item[storeID]
	if not targetList then
		return nil
	end

	return targetList[times]
end

-- TowerBattleCollect
Schemes.TowerBattleCollect = {}
function Schemes.TowerBattleCollect.Init()
	local itemByGoodsID = {}
	local itemByActiveGoodsID = {}
	for _, v in ipairs(Schemes.TowerBattleCollect.items) do
		itemByGoodsID[v.GoodsID] = v
		itemByActiveGoodsID[v.ActiveCostID1[1]] = v
	end
	Schemes.TowerBattleCollect.itemByGoodsID = itemByGoodsID
	Schemes.TowerBattleCollect.itemByActiveGoodsID = itemByActiveGoodsID
end

function Schemes.TowerBattleCollect:GetByGoodsID(id)
	return self.itemByGoodsID[id]
end

function Schemes.TowerBattleCollect:GetByActiveGoodsID(id)
	return self.itemByActiveGoodsID[id]
end

-- FunctionOpen
Schemes.FunctionOpen = {}
function Schemes.FunctionOpen.Init()
	local stepItems = {}
	for _, v in ipairs(Schemes.FunctionOpen.items) do
		stepItems[v.FunctionType * 100 + v.WindowID * 10 + v.WindowNo] = v
	end
	Schemes.FunctionOpen.stepItems = stepItems
end

function Schemes.FunctionOpen:GetFunctionOpenByParams(funType, wID, wNo)
	return Schemes.FunctionOpen.stepItems[funType * 100 + wID * 10 + wNo]
end

function Schemes:GetGoodsConfig(goodsID)
	goodsID = tonumber(goodsID) or 0
	if goodsID == 0 then
		return nil
	end
	if goodsID <= DEFINE.MAX_MEDICAMENT_ID then
		return Schemes.Medicament:Get(goodsID)
	end

	return Schemes.Equipment:Get(goodsID)
end

--Equipment
Schemes.Equipment = {}
function Schemes.Equipment:ByIdList(idList)
	local config = {}
	local cfg
	for k, v in pairs(idList) do
		cfg = Schemes.Equipment:Get(v)
		if cfg then
			table.insert(config, cfg)
		end
	end
	return config
end

function Schemes.Equipment:GetByPacketID()
	self.packetIDList = {}
	for _, v in ipairs(Schemes.Equipment.items) do
		if not self.packetIDList[v.PacketID] then
			self.packetIDList[v.PacketID] = {}
		end
		table.insert(self.packetIDList[v.PacketID], v)
	end

	--重置函数
	Schemes.Equipment.GetByPacketID = function(self)
		return self.packetIDList
	end
	return self.packetIDList
end

-- UIAtlasConfig
Schemes.UIAtlasConfig = {}
function Schemes.UIAtlasConfig.Init()
	local spriteAtlas = {}
	for _, v in ipairs(Schemes.UIAtlasConfig.items) do
		spriteAtlas[v.Name] = v.Pack
	end
	Schemes.UIAtlasConfig.spriteAtlas = spriteAtlas
end

function Schemes.UIAtlasConfig:GetSpriteAtlas(spriteName)
	return Schemes.UIAtlasConfig.spriteAtlas[spriteName]
end

-- EquipSmelt
Schemes.EquipSmelt = {}
function Schemes.EquipSmelt.Init()
	local fastItem = {}
	for _, v in ipairs(Schemes.EquipSmelt.items) do
		fastItem[v.ID] = fastItem[v.ID] or {}
		fastItem[v.ID][v.Quality * 1000 + v.StarNum] = v
	end
	Schemes.EquipSmelt.fastItem = fastItem
end

--根据等级获得幻化升级条件参数
function Schemes.EquipSmelt:GetLevelStepRateByLevel(id, quality, starNum, level)
	if not id or not quality or not starNum then return 0 end
	local config = self:Get(id, quality, starNum)
	local rate = 0
	if config then
		local count = #config.LevelStepRate
		rate = config.LevelStepRate[count]
		local index = math.floor(((level - 1) / 100) + 1)
		if index < count then
			rate = config.LevelStepRate[index]
		end
	end
	return rate
end

--获得星星数对应可培养经验
function Schemes.EquipSmelt:GetMaxExp(id, quality, starNum, level)
	if not id or not quality or not starNum then return 0 end
	if not level then
		if not EntityModule.hero then
			return 0
		end
		level = EntityModule.hero:GetProperty(CREATURE_FIELD.CREATURE_FIELD_LEVEL)
	end
	local rate = self:GetLevelStepRateByLevel(id, quality, starNum, level)
	return level * rate * 100, rate
end

function Schemes.EquipSmelt:Get(id, quality, starNum)
	local itemTable = self.fastItem[id]
	if not itemTable then
		return nil
	end
	if quality == nil then
		quality = 0
	end
	if starNum == nil then
		starNum = 0
	end
	return itemTable[quality * 1000 + starNum]
end

--根据ID分类
function Schemes.EquipSmelt:GetByID(id)
	self.__idList = {}
	for _, v in ipairs(Schemes.EquipSmelt.items) do
		if not self.__idList[v.ID] then
			self.__idList[v.ID] = {}
		end
		table.insert(self.__idList[v.ID], v)
	end

	self.GetByID = function(_self, _id)
		return _self.__idList[_id]
	end
	return self.__idList[id]
end

-- ConstValue
Schemes.ConstValue = {}
function Schemes.ConstValue:Get(id)
	if not id then
		warn('取表格时没传id', tostring(self), debug.traceback())
		return nil
	end
	local item = self.fastItems[id]
	if not item then
		warn('Schemes.ConstValue:Get not item', id, debug.traceback())
		return 0
	end

	return item.Value
end

-- VipValue
Schemes.VipValue = {}
function Schemes.VipValue:GetByVipLevel(id, vipLevel)
	if not id then
		warn('取表格时没传id', tostring(self), debug.traceback())
		return nil
	end

	if not vipLevel then
		local hero = EntityModule.hero
		if not hero then
			return nil
		end

		vipLevel = hero:GetProperty(PLAYER_FIELD.PLAYER_FIELD_VIPLEVEL)
	end

	local vipScheme = self:Get(id)
	if not vipScheme then
		warn('Schemes.VipValue:GetByVipLevel not vipScheme', id)
		return nil
	end

	local result = vipScheme['Vip' .. vipLevel]
	if not result then
		warn('Schemes.VipValue:GetByVipLevel not result', id, vipLevel)
		return nil
	end

	return result
end

-- SegmentUpStar
Schemes.SegmentUpStar = {}
function Schemes.SegmentUpStar.Init()
	local itemBySegStar = {}
	local itemBySeg = {}
	local itemBySegGift = {}
	for _, v in ipairs(Schemes.SegmentUpStar.items) do
		itemBySegStar[v.Segment * 1000 + v.Star] = v
		itemBySeg[v.Segment] = v
		if v.PrizeID > 0 then
			itemBySegGift[v.Segment] = v
		end
	end
	Schemes.SegmentUpStar.itemBySegStar = itemBySegStar
	Schemes.SegmentUpStar.itemBySeg = itemBySeg
	Schemes.SegmentUpStar.itemBySegGift = itemBySegGift
end

function Schemes.SegmentUpStar:GetBySegAndStar(seg, star)
	local key = seg * 1000 + star
	return Schemes.SegmentUpStar.itemBySegStar[key]
end

function Schemes.SegmentUpStar:GetBySegGift(seg)
	return Schemes.SegmentUpStar.itemBySegGift[seg]
end

---奖励物品
---@class PrizeGoods
---@field Type integer
---@field ID  integer
---@field Num  integer
---@field Chance integer
---@field Chance2 integer
---@field TransVoc integer
---@field Bind integer
---@field Time integer

Schemes.PrizeTable = {}

---获取奖励物品
---@param id integer 奖励ID
---@return { ID: integer, Goods: PrizeGoods[] }
function Schemes.PrizeTable:GetGoods(id)
	---@type {ID:integer, Goods:PrizeGoods[]}[]
	self.goodsFastItems = {}
	for k, v in ipairs(self.items) do
		local prizeGoods = {}
		for i = 1, 40 do
			local fieldStr = v['Goods' .. i]
			if string.len(fieldStr) > 1 then
				local subStr = HelperL.Split(fieldStr, '+')
				if #subStr > 1 then
					table.insert(prizeGoods, {
						Type = tonumber(subStr[1]) or 0,
						ID = tonumber(subStr[2]) or 0,
						Num = tonumber(subStr[3]) or 0,
						Chance = tonumber(subStr[4]) or 0,
						Chance2 = tonumber(subStr[5]) or 0,
						TransVoc = tonumber(subStr[6]) or 0,
						Bind = tonumber(subStr[7]) or 0,
						Time = tonumber(subStr[8]) or 0,
					})
				end
			end
		end
		self.goodsFastItems[v.ID] = { ID = v.ID, Goods = prizeGoods, }
	end

	--重置函数
	self.GetGoods = function(_self, _id)
		return _self.goodsFastItems[tonumber(_id) or 0]
	end

	return self.goodsFastItems[tonumber(id) or 0]
end

VIRTUAL_GOODSID_BEGIN                = 1
VIRTUAL_GOODSID_PLAYEREXP            = 1    -- 细胞经验ID
VIRTUAL_GOODSID_ENERGY               = 2    -- 虚拟体力ID
VIRTUAL_GOODSID_DIAMOND              = 3    -- 虚拟钻石ID
VIRTUAL_GOODSID_MONEY                = 4    -- 虚拟金币ID
VIRTUAL_GOODSID_MORAL                = 5    -- 虚拟修为ID
VIRTUAL_GOODSID_TRAIN                = 6    -- 虚拟帮贡ID
VIRTUAL_GOODSID_PRESTIGE             = 7    -- 虚拟声望ID
VIRTUAL_GOODSID_TALENT               = 8    -- 虚拟天赋ID
VIRTUAL_GOODSID_EXPLOIT              = 9    -- 虚拟功勋ID
VIRTUAL_GOODSID_BINDDIAMOND          = 10   -- 虚拟绑定元宝ID
VIRTUAL_GOODSID_COUPONS              = 11   -- 虚拟点券ID
VIRTUAL_GOODSID_EXPERIENCE           = 12   -- 虚拟历练ID
VIRTUAL_GOODSID_ACHIEVE              = 13   -- 虚拟成就ID
VIRTUAL_GOODSID_COUNTRYPOWER         = 14   -- 虚拟国力ID
VIRTUAL_GOODSID_COUNTRYMONEY         = 15   -- 虚拟国库ID
VIRTUAL_GOODSID_EXCHANGETICKET       = 16   -- 虚拟兑换券ID
VIRTUAL_GOODSID_VIPSCORE             = 17   -- 虚拟VIP积分ID
VIRTUAL_GOODSID_GOLDCOIN             = 18   -- 虚拟金币ID
VIRTUAL_GOODSID_WARBANDSTACK         = 19   -- 虚拟战队荣誉ID
VIRTUAL_GOODSID_SOCIETYACTV          = 20   -- 虚拟帮会活跃ID
VIRTUAL_GOODSID_WARBANDACTV          = 21   -- 虚拟战队活跃ID
VIRTUAL_GOODSID_RECHARGEPOINT        = 22   -- 虚拟充值积分ID
VIRTUAL_GOODSID_CUR_MAX              = 22   -- 现在最大虚拟物品ID
VIRTUAL_GOODSID_MAX                  = 1000 -- 最大虚拟物品ID

Schemes.PrizeTable.virtualGoodsField = {
	[VIRTUAL_GOODSID_PLAYEREXP] = 'PlayerExp',
	[VIRTUAL_GOODSID_ENERGY] = 'Energy',
	[VIRTUAL_GOODSID_DIAMOND] = 'Diamond',
	[VIRTUAL_GOODSID_MONEY] = 'Money',
	[VIRTUAL_GOODSID_MORAL] = 'Moral',
	[VIRTUAL_GOODSID_TRAIN] = 'Train',
	[VIRTUAL_GOODSID_PRESTIGE] = 'Prestige',
	[VIRTUAL_GOODSID_TALENT] = 'Talent',
	[VIRTUAL_GOODSID_EXPLOIT] = 'Exploit',
	[VIRTUAL_GOODSID_BINDDIAMOND] = 'DiamondBinding',
	[VIRTUAL_GOODSID_COUPONS] = 'Coupons',
	[VIRTUAL_GOODSID_EXPERIENCE] = 'Experience',
}

---获取虚拟物品系数
---@param level integer 角色等级
---@param id integer 物品ID
---@return integer
function Schemes.PrizeTable:GetVirtualGoodsField(level, id)
	local coef = Schemes.PrizeCoef:Get(level)
	if coef then
		local coefValue = coef[self.virtualGoodsField[id]]
		if coefValue then
			return coefValue / 10000
		end
	end
	return 1
end

function Schemes.PrizeTable:GetTransPrizeVarGoods(id, level, depth)
	local varConfig = Schemes.PrizeVariableGoods:Get(id)
	if not varConfig then
		print('GetTransPrizeVarGoods not varConfig', id)
		return 0, 0
	end

	if not depth then
		depth = 1
	end

	local goodsIndex = 0

	local levelTargetID = varConfig.LevelTargetID
	local serverOpenType = varConfig.ServerOpenType
	local actvList = varConfig.ActvID
	if levelTargetID and #levelTargetID > 0 and levelTargetID[1] > 0 then
		-- 按等级区分
		for i, v in ipairs(levelTargetID) do
			if level <= v then
				goodsIndex = i
				break
			end
		end
	elseif serverOpenType ~= nil and serverOpenType > 0 then
		local serverOpenParam = varConfig.ServerOpenParam
		local serverOpenDay = varConfig.ServerOpenDay
		local curOpenDay = 0
		if serverOpenType == 1 then
			curOpenDay = HelperL.GetServerOpenDays()
		elseif serverOpenType == 2 then
			curOpenDay = HelperL.GetServerOpenWeeks()
		elseif serverOpenType == 3 then
			curOpenDay = HelperL.GetServerOpenMonths()
		elseif serverOpenType == 4 then
			if not ActivityManager:IsActivityOpen(serverOpenParam) then
				return 0, 0
			end
			curOpenDay = ActivityManager:GetStageIDByActvID(serverOpenParam)
		else
			warn('GetTransPrizeVarGoods unknown ServerOpenType  ' .. id)
			return 0, 0
		end
		for i, v in ipairs(serverOpenDay) do
			if curOpenDay <= v then
				goodsIndex = i
				break
			end
		end
	elseif actvList and actvList[1] > 0 then
		-- 按开启活动区分
		local goodsList = nil
		for i, v in ipairs(actvList) do
			if ActivityManager:IsActivityOpen(v) then
				goodsIndex = i
				break
			end
		end
	else
		print('GetTransPrizeVarGoods PrizeVariableGoods未配置任何跳转方式 ' .. tostring(id))
	end

	local goodsID = 0
	local goodsNum = 0
	if goodsIndex == 0 then
		if depth < 10 then
			if varConfig.JumpID > 0 then
				depth = depth + 1
				goodsID, goodsNum = Schemes.PrizeTable:GetTransPrizeVarGoods(varConfig.JumpID, level, depth)
			end
		else
			print('GetTransPrizeVarGoods 跳转10次仍未找到目标物品ID' .. tostring(varConfig.JumpID))
		end
	else
		goodsID = varConfig.GoodsList[goodsIndex] or 0
		goodsNum = varConfig.GoodsCount[goodsIndex] or 0
		if goodsNum < 0 then
			local coefValue = Schemes.PrizeTable:GetVirtualGoodsField(level, id)
			goodsNum = -goodsNum * coefValue
		end
	end
	return goodsID, goodsNum
end

function Schemes.PrizeTable:GetPrizeGoods(prize)
	if prize == nil then return end
	local prizeGoodsList = {}
	local hero = EntityModule.hero
	local level = nil
	if not level and hero then
		level = hero:GetProperty(CREATURE_FIELD.CREATURE_FIELD_LEVEL)
	end

	if prize.Goods ~= nil then
		for k, v in ipairs(prize.Goods) do
			local goodsID = 0
			local goodsNum = 0
			local changed = false
			if v.Type == 0 then
				goodsID = v.ID
			elseif v.Type == 1 then
				goodsID, goodsNum = Schemes.PrizeTable:GetTransPrizeVarGoods(v.ID, level, 1)
				changed = true
			end
			if goodsID > 0 then
				if v.TransVoc > 0 then
					local vocConfig = Schemes.PrizeVocationGoods:Get(goodsID)
					goodsID = 0
					changed = true
					if vocConfig then
						if #vocConfig.VocationList == #vocConfig.GoodsList then
							for m, n in ipairs(vocConfig.VocationList) do
								if vocation == n then
									goodsID = vocConfig.GoodsList[m]
									break
								end
							end
						else
							print('GetPrizeGoods PrizeVocationGoods里职业和物品数不匹配 ', goodsID)
						end
					end
				end

				if goodsID > 0 then
					if not changed then
						table.insert(prizeGoodsList, v)
					else
						local goodsData = {}
						for m, n in pairs(v) do
							goodsData[m] = n
						end
						goodsData.ID = goodsID
						if goodsNum > 0 then
							goodsData.Num = goodsNum
						end
						table.insert(prizeGoodsList, goodsData)
					end
				end
			end
			--虚拟物品需要显示
			if v.Type == nil and v.ID and v.ID < VIRTUAL_GOODSID_MAX then
				table.insert(prizeGoodsList, v)
			end
		end
	end
	return prizeGoodsList
end

function Schemes.PrizeTable:GetPrize(id, level)
	local prize = Schemes.PrizeTable:GetGoods(id)
	if prize == nil then return nil end
	level = level or EntityModule.hero:GetProperty(CREATURE_FIELD.CREATURE_FIELD_LEVEL)
	local realPrize = {}
	realPrize.ID = prize.ID
	if prize.Goods ~= nil then
		realPrize.Goods = {}
		local coefValue
		for i, v in ipairs(prize.Goods) do
			local singleGoods = {
				Type = v.Type,
				ID = v.ID,
				Num = v.Num,
				Chance = v.Chance,
				TransVoc = v.TransVoc,
				Bind = v.Bind,
				Time = v.Time,
			}
			if v.Type == 0 and v.Num < 0 then
				coefValue = Schemes.PrizeTable:GetVirtualGoodsField(level, id)
				singleGoods.Num = -v.Num * coefValue
			end
			table.insert(realPrize.Goods, singleGoods)
		end
	end
	return realPrize
end

-- 获取指定物品信息
function Schemes.PrizeTable:GetGoodInfo(PrizeID, GoodIndex)
	local prizeConfig = Schemes.PrizeTable:GetPrize(PrizeID)
	if prizeConfig then
		local prizeList = Schemes.PrizeTable:GetPrizeGoods(prizeConfig)
		if prizeList then
			if not GoodIndex or GoodIndex < 1 or GoodIndex > #prizeList then
				return nil
			else
				return prizeList[GoodIndex]
			end
		end
	end
	return nil
end

-- 获取指定物品列表信息
function Schemes.PrizeTable:GetGoodInfos(PrizeID)
	local prizeConfig = Schemes.PrizeTable:GetPrize(PrizeID)
	if prizeConfig then
		local prizeList = Schemes.PrizeTable:GetPrizeGoods(prizeConfig)
		if prizeList then
			return prizeList
		end
	end
	return nil
end

--------------------------------------------------------------------
-- 获取奖励列表(ID\数量\名称\图片)
--------------------------------------------------------------------
function Schemes.PrizeTable:GetGoodsList(prizeID)
	local prizeGoods = Schemes.PrizeTable:GetGoodInfos(prizeID)
	if not prizeGoods then return nil end
	---@class PrizeParse
	local goodShuXing
	---@type PrizeParse[]
	local goodsList = {}
	local goods, item
	for i = 1, #prizeGoods do
		goods = prizeGoods[i]
		item = Schemes.Medicament:Get(goods.ID)
		if item then
			goodShuXing = {}
			goodShuXing.id = goods.ID
			goodShuXing.num = goods.Num
			goodShuXing.name = item.GoodsName
			goodShuXing.iconID = item.IconID
			table.insert(goodsList, goodShuXing)
		end
	end
	return goodsList
end

---奖励数据
---@class PrizeData
---@field ID integer 物品ID
---@field Num integer 物品数量

--------------------------------------------------------------------
---计算随机奖励物品
---@param prizeID integer 奖励ID
---@param addFactor ?number 奖励加成系数，默认：1
---@param isRandom ?boolean 是否使用随机值，默认：true
---@param depth ?integer 获取随机奖励，嵌套深度，默认：1
---@param prizeGoodsNum ?PrizeData[] 用于函数内部，获取随机奖励(字典型)
---@param prizeGoodsList ?PrizeData[] 用于函数内部，获取随机奖励(数组型)
---@return PrizeData[]|nil
---@return PrizeData[]|nil
--------------------------------------------------------------------
function Schemes.PrizeTable.__RandomPrize(prizeID, addFactor, isRandom, depth, prizeGoodsNum, prizeGoodsList)
	local prize = Schemes.PrizeTable:GetGoods(prizeID)
	if prize == nil then
		return nil, nil
	end
	if depth == nil then
		depth = 1
	end
	addFactor = tonumber(addFactor) or 1
	--奖励物品计数(用于去重)
	prizeGoodsNum = prizeGoodsNum or {}
	--奖励物品列表
	prizeGoodsList = prizeGoodsList or {}
	local level = 1
	if EntityModule.hero then
		level = EntityModule.hero:GetProperty(CREATURE_FIELD.CREATURE_FIELD_LEVEL)
	end
	--随机值
	local randomValue = math.random(0, 9999)
	local goodsID, goodsNum, medicament
	local isSendDirectly
	for i, v in ipairs(prize.Goods) do
		isSendDirectly = v.Chance == 0 and v.Chance2 == 0
		if (isRandom == false) or isSendDirectly or (randomValue >= v.Chance and randomValue <= v.Chance2) then
			if v.Type == 0 then
				goodsID = v.ID
				--计算虚拟物品系数
				if v.Num < 0 then
					goodsNum = -v.Num * Schemes.PrizeTable:GetVirtualGoodsField(level, goodsID)
				else
					goodsNum = v.Num
				end
			elseif v.Type == 1 then
				goodsID, goodsNum = Schemes.PrizeTable:GetTransPrizeVarGoods(v.ID, level, 1)
			else
				goodsID = 0
				goodsNum = 0
			end

			if goodsID > 0 and goodsNum > 0 then
				goodsNum = goodsNum * addFactor
				medicament = Schemes.Medicament:Get(goodsID)
				--判断是随机物品
				if depth > 0 and medicament and medicament.UseMenu == 4 then
					--(使用菜单,0不能,1用1个,2全部使用,3弹出选择使用数量UI,4自动使用)
					--获取随机物品
					for ii = 1, goodsNum, 1 do
						Schemes.PrizeTable.__RandomPrize(medicament.UseParam1, 1, isRandom, depth - 1,
							prizeGoodsNum, prizeGoodsList)
					end
				else
					if prizeGoodsNum[goodsID] then
						prizeGoodsNum[goodsID].Num = prizeGoodsNum[goodsID].Num + goodsNum
					else
						local goods = { ID = goodsID, Num = goodsNum, }
						prizeGoodsNum[goodsID] = goods
						table.insert(prizeGoodsList, goods)
					end
				end
			end
		end
	end
	return prizeGoodsList, prizeGoodsNum
end

---计算随机奖励物品2
---@param prizeStr string 奖励字符串，格式：物品ID;物品数量|物品ID;物品数量
---@param addFactor integer 奖励加成系数，默认：1
---@param prizeGoodsNum ?PrizeData[] 用于函数内部，获取随机奖励(字典型)
---@param prizeGoodsList ?PrizeData[] 用于函数内部，获取随机奖励(数组型)
---@return PrizeData[]|nil
---@return PrizeData[]|nil
function Schemes.PrizeTable.__RandomPrize2(prizeStr, addFactor, prizeGoodsNum, prizeGoodsList)
	addFactor = tonumber(addFactor) or 1
	--奖励物品计数(用于去重)
	prizeGoodsNum = prizeGoodsNum or {}
	--奖励物品列表
	prizeGoodsList = prizeGoodsList or {}

	local strList = HelperL.Split(prizeStr, "|")
	local temp, goodsID, goodsNum, medicament
	for i, str in ipairs(strList) do
		temp = HelperL.Split(str, ";")
		goodsID = tonumber(temp[1]) or 0
		goodsNum = (tonumber(temp[2]) or 0) * addFactor
		if goodsID > 0 and goodsNum > 0 then
			medicament = Schemes:GetGoodsConfig(goodsID)
			if medicament then
				--判断是随机物品
				if medicament.UseMenu == 4 then
					for ii = 1, goodsNum, 1 do
						Schemes.PrizeTable.__RandomPrize(medicament.UseParam1, 1, true, 1, prizeGoodsNum, prizeGoodsList)
					end
				else
					if prizeGoodsNum[goodsID] then
						prizeGoodsNum[goodsID].Num = prizeGoodsNum[goodsID].Num + goodsNum
					else
						local goods = { ID = goodsID, Num = goodsNum, }
						prizeGoodsNum[goodsID] = goods
						table.insert(prizeGoodsList, goods)
					end
				end
			end
		end
	end
	return prizeGoodsList, prizeGoodsNum
end

--------------------------------------------------------------------
---获取随机奖励物品
---@param prizeID integer 奖励ID
---@param isRandom ?boolean 是否使用随机值，默认：true
---@param depth ?integer 获取随机奖励，嵌套深度，默认：1
---@return PrizeData[]|nil
--------------------------------------------------------------------
function Schemes.PrizeTable:GetRandomPrize(prizeID, isRandom, depth)
	return self.__RandomPrize(prizeID, 1, isRandom, depth, {}, {})
end

-- PrizeTime
Schemes.PrizeTime = {}
function Schemes.PrizeTime.Init()
	local list = {}
	local typeParamListData = {}
	local typeParamList = {}
	for i, v in ipairs(Schemes.PrizeTime.items) do
		if not list[v.Condition] then
			list[v.Condition] = { v }
		else
			table.insert(list[v.Condition], v)
		end

		if v.Condition == 3 then
			if not typeParamList[v.TypeParam] then
				typeParamList[v.TypeParam] = 0
			end
			typeParamList[v.TypeParam] = typeParamList[v.TypeParam] + 1
			if not typeParamListData[v.TypeParam] then
				typeParamListData[v.TypeParam] = {}
			end
			table.insert(typeParamListData[v.TypeParam], v)
		end
	end
	Schemes.PrizeTime.conditionList = list
	Schemes.PrizeTime.typeParamListData = typeParamListData
	Schemes.PrizeTime.typeParamList = typeParamList
end

function Schemes.PrizeTime:GetByCondition(condition)
	return Schemes.PrizeTime.conditionList[condition]
end

function Schemes.PrizeTime:GetTypeParamList()
	return Schemes.PrizeTime.typeParamList
end

function Schemes.PrizeTime:GetTypeParamListData(typeParam)
	return Schemes.PrizeTime.typeParamListData[typeParam]
end

-- EquipSmeltBase
Schemes.EquipSmeltBase = {}
function Schemes.EquipSmeltBase.Init()
	local levelItemList = {}
	for _, v in ipairs(Schemes.EquipSmeltBase.items) do
		levelItemList[v.ID * 1000 + v.SmeltLevel] = v
	end
	Schemes.EquipSmeltBase.levelItemList = levelItemList
end

--根据等级获得幻化升级条件参数
function Schemes.EquipSmeltBase:GetLevelStepRateByLevel(id, smeltLevel, level)
	if not id or not smeltLevel then return 0 end
	local equipSmeltBase = Schemes.EquipSmeltBase:Get(id, smeltLevel)
	local rate = 0
	if equipSmeltBase then
		local count = #equipSmeltBase.LevelStepRate
		rate = equipSmeltBase.LevelStepRate[count]
		local index = math.floor(((level - 1) / 100) + 1)
		if index < count then
			rate = equipSmeltBase.LevelStepRate[index]
		end
	end
	return rate
end

function Schemes.EquipSmeltBase:Get(id, smeltLevel)
	return Schemes.EquipSmeltBase.levelItemList[id * 1000 + smeltLevel]
end

Schemes.EquipDecomposition = {}
function Schemes.EquipDecomposition:Get(id, smeltLevel)
	for _, v in ipairs(Schemes.EquipDecomposition.items) do
		if v.ID == id and v.SmeltLevel == smeltLevel then
			return v
		end
	end
	return nil
end

-- Store
Schemes.Store = {}
function Schemes.Store.Init()
	local itemIDList = {}
	for k, v in ipairs(Schemes.Store.items) do
		for i = 1, 10 do
			local itemID = v['StoreItemsID' .. i]
			if itemID ~= 0 and not itemIDList[itemID] then
				itemIDList[itemID] = v.ID
			end
		end
	end
	Schemes.Store.itemIDList = itemIDList
end

--获得商店ID(传商店分页ID)
function Schemes.Store:GetIDByStoreItemsID(ID)
	if not Schemes.Store.ItemIDList[ID] then
		return 0
	end
	return Schemes.Store.ItemIDList[ID]
end

-- RandomName
Schemes.RandomName = {}
function Schemes.RandomName.Init()
	if not Schemes.RandomName.isInited then
		Schemes.RandomName.isInited = true

		local itemByType = {}
		for i, v in ipairs(Schemes.RandomName.items) do
			if not itemByType[v.Type] then
				itemByType[v.Type] = {}
			end
			table.insert(itemByType[v.Type], v)
		end
		Schemes.RandomName.itemByType = itemByType
	end
end

function Schemes.RandomName.GetItemList(itemType)
	Schemes.RandomName.Init()
	return Schemes.RandomName.itemByType[itemType]
end

-- ShowMessages
Schemes.ShowMessages = {}
function Schemes.ShowMessages.Init()
	if not Schemes.ShowMessages.isInited then
		Schemes.ShowMessages.isInited = true
		local itemByType = {}
		for i, v in ipairs(Schemes.ShowMessages.items) do
			if not itemByType[v.ThingsType] then
				itemByType[v.ThingsType] = {}
			end
			table.insert(itemByType[v.ThingsType], v)
		end
		Schemes.ShowMessages.itemByType = itemByType
	end
end

function Schemes.ShowMessages.GetItemList(itemType)
	Schemes.ShowMessages.Init()
	return Schemes.ShowMessages.itemByType[itemType]
end

--任务
Schemes.TaskBranch = {}
function Schemes.TaskBranch.InitFirstGroupItemList()
	local itemList = {}
	for _, v in ipairs(Schemes.TaskBranch.items) do
		if not itemList[v.GroupID] then
			itemList[v.GroupID] = v
		end
	end
	Schemes.TaskBranch.firstGroupItemList = itemList
end

Schemes.SkillUp = {}
function Schemes.SkillUp.Init()
	local stepItems = {}
	for _, v in ipairs(Schemes.SkillUp.items) do
		stepItems[v.ID + v.Level * 100] = v
	end
	Schemes.SkillUp.stepItems = stepItems
end

function Schemes.SkillUp:Get(id, level)
	return Schemes.SkillUp.stepItems[id + level * 100]
end

-- Task
Schemes.Task = {}
function Schemes.Task:MaxTaskID()
	return Schemes.Task.items[#Schemes.Task.items].ID
end

function Schemes.Task.GetGoal(id)
	local item = Schemes.Task:Get(id)
	if not item then return 0xFFFFFF end
	if item.Type == TaskType.TaskType_KillTargetMonster then
		return item.Parameter2
	elseif item.Type == TaskType.TaskType_DramaEctype then
		return 1
	elseif item.Type == TaskType.TaskType_DailyEctype then
		return 1
	elseif item.Type == TaskType.TaskType_JoinActivity then
		return item.Parameter2
	elseif item.Type == TaskType.TaskType_KillLevelMonster then
		return item.Parameter2
	elseif item.Type == TaskType.TaskType_Talk then
		return 1
	elseif item.Type == TaskType.TaskType_TimeTalk then
		return 1
	elseif item.Type == TaskType.TaskType_TimePass then
		return item.Parameter1
	elseif item.Type == TaskType.TaskType_ActorLevel then
		return item.Parameter1
	elseif item.Type == TaskType.TaskType_FriendShip then
		return item.Parameter1
	elseif item.Type == TaskType.TaskType_KillPlayer then
		return item.Parameter1
	elseif item.Type == TaskType.TaskType_Escort then
		return 1
	elseif item.Type == TaskType.TaskType_TalkEctype then
		return 1
	elseif item.Type == TaskType.TaskType_EnterScene then
		return 1
	elseif item.Type == TaskType.TaskType_LieMingAndQiFuCount then
		return item.Parameter1
	elseif item.Type == TaskType.TaskType_FuBenId then
		return 1
	end
end

Schemes.SocietyTimeMake = {}
function Schemes.SocietyTimeMake.GetByTypeAndLevel(type, level)
	for _, v in ipairs(Schemes.SocietyTimeMake.items) do
		if v.AlchemyType == type and v.AlchemyLevel == level then
			return v
		end
	end
end

function Schemes.TaskBranch.GroupCount(groupID)
	if not Schemes.TaskBranch.firstGroupItemList then
		Schemes.TaskBranch.InitFirstGroupItemList()
	end
	local curItem = Schemes.TaskBranch.firstGroupItemList[groupID]
	if not curItem then
		print('TaskBranch.GroupCount 找不到组ID=' .. groupID)
		return 0
	end

	return curItem.GroupCount
end

function Schemes.TaskBranch.MinLevel(groupID)
	if not Schemes.TaskBranch.firstGroupItemList then
		Schemes.TaskBranch.InitFirstGroupItemList()
	end
	local curItem = Schemes.TaskBranch.firstGroupItemList[groupID]
	if not curItem then
		print('TaskBranch.MinLevel 找不到组ID=' .. groupID)
		return 0
	end

	return curItem.MinLevel
end

function Schemes.TaskBranch.MaxLevel(groupID)
	if not Schemes.TaskBranch.firstGroupItemList then
		Schemes.TaskBranch.InitFirstGroupItemList()
	end
	local curItem = Schemes.TaskBranch.firstGroupItemList[groupID]
	if not curItem then
		print('TaskBranch.MaxLevel 找不到组ID=' .. groupID)
		return 0
	end

	return curItem.MaxLevel
end

function Schemes.TaskBranch.EqualLevel(Level, groupID)
	if Level < 200 then Level = 200 end
	for _, v in ipairs(Schemes.TaskBranch.items) do
		if Level >= v.MinLevel and Level <= v.MaxLevel and groupID == v.GroupID then
			return v
		end
	end
	print('TaskBranch.EqualLevel 找不到组ID=' .. groupID .. '  Level=' .. Level)
	return nil
end

-- TowerBattleSubLevel
Schemes.CommonText = {}

---获取看广告进度
---@param adID integer 广告ID
---@return integer 进度
---@return integer 总数
function Schemes.CommonText.GetAdToTime(adID)
	local num = 0
	local toTime = 0
	local commonText = Schemes.CommonText:Get(adID)
	if commonText then
		num = HeroDataManager:GetLogicByte(commonText.Param2, commonText.Param3)
		toTime = commonText.ToTime
	end

	return num, toTime
end

---获取看广告进度
---@param cardID integer 充值卡ID
---@return integer 进度
---@return integer 总数
function Schemes.CommonText.GetAdToTimeByCardID(cardID)
	local num = 0
	local toTime = 0
	local scheme = Schemes.RechargeCard:Get(cardID)
	if scheme and scheme.Description ~= '0' then
		num, toTime = Schemes.CommonText.GetAdToTime(scheme.Description)
	end

	return num, toTime
end

---根据类型获取表格数据
---@param textType integer 类型
---@return table
function Schemes.CommonText:GetByTextType(textType)
	self._textTypeList = {}
	for i, v in ipairs(self.items) do
		if not self._textTypeList[v.TextType] then
			self._textTypeList[v.TextType] = {}
		end
		table.insert(self._textTypeList[v.TextType], v)
	end
	--重置函数
	self.GetByTextType = function(self, textType)
		return self._textTypeList[textType]
	end
	return self._textTypeList[textType]
end

---@class RechargeCard:RechargeCard_Class
Schemes.RechargeCard = {}
---获取充值卡价格和描述
---@overload fun(cardID:integer):integer, string
---@overload fun(cardID:integer, str:string):integer, string
---@param cardID integer 充值卡ID
---@param str string 拼接内容，默认拼接：'元'
---@param isFormat boolean 是否使用 string.format() 函数，默认：false
---@return integer 价格
---@return string 描述
function Schemes.RechargeCard.GetPrice(cardID, str, isFormat)
	local price = 0
	local card = Schemes.RechargeCard:Get(cardID)
	if card then
		price = card.FirstRMB / 100
	end
	local describe = price .. CommonTextID.ZW_MONEY
	if str then
		if isFormat then
			describe = string.format(str, price)
		else
			describe = price .. str
		end
	end
	return price, describe
end

---获取指定充值卡类型列表
---@param cardType integer 充值卡类型
---@return RechargeCardCfg[]
function Schemes.RechargeCard:GetByCardType(cardType)
	---@type RechargeCardCfg[][]
	self.__CardType_List = {}
	for i, v in ipairs(self.items) do
		if not self.__CardType_List[v.CardType] then
			self.__CardType_List[v.CardType] = {}
		end
		table.insert(self.__CardType_List[v.CardType], v)
	end

	---获取指定充值卡类型列表
	---@param _self RechargeCard
	---@param _cardType integer 充值卡类型
	---@return unknown
	self.GetByCardType = function(_self, _cardType)
		return _self.__CardType_List[_cardType]
	end
	return self.__CardType_List[cardType]
end

Schemes.CatFriend = {}
Schemes.CatFriend.GetByType = Schemes_GetByType
Schemes.CatFriend.GetByTypeList = Schemes_GetByTypeList

Schemes.EquipForgeAddition = {}
Schemes.EquipForgeAddition.GetByType = Schemes_GetByType


Schemes.EquipCollect = {}
Schemes.EquipCollect.GetByType = Schemes_GetByType

---@class CatMainStage:CatMainStage_Class
Schemes.CatMainStage = {}
---comment
---@param frontType integer 副本类型
---@return CatMainStageCfg[]
function Schemes.CatMainStage:GetByFrontType(frontType)
	---@type CatMainStageCfg[][]
	self._frontTypeList = {}
	for i, v in ipairs(Schemes.CatMainStage.items) do
		if not self._frontTypeList[v.FrontType] then
			self._frontTypeList[v.FrontType] = {}
		end
		table.insert(self._frontTypeList[v.FrontType], v)
	end
	---comment
	---@param _self CatMainStage
	---@param _frontType integer
	---@return CatMainStageCfg[]
	Schemes.CatMainStage.GetByFrontType = function(_self, _frontType)
		return _self._frontTypeList[tonumber(_frontType) or 0]
	end
	return self._frontTypeList[tonumber(frontType) or 0]
end

---通关奖励缓存
Schemes.CatMainStage.__SucceedRewardsList = {}

---获取通关奖励
---@param stageId integer 关卡ID
---@return PrizeData[]
function Schemes.CatMainStage:GetSucceedRewards(stageId)
	--先拿缓存
	if self.__SucceedRewardsList[stageId] then
		return self.__SucceedRewardsList[stageId]
	end

	---@type PrizeData[]
	local prizeGoodsNum = {}
	---@type PrizeData[]
	local prizeGoodsList = {}
	local catMainStage = self:Get(stageId)
	local strList = catMainStage.Rebirth_item1
	local temp, goodsID, goodsNum, medicament
	for i, str in ipairs(strList) do
		temp = HelperL.Split(str, "|")
		goodsID = tonumber(temp[1]) or 0
		goodsNum = (tonumber(temp[2]) or 0)
		if goodsID > 0 and goodsNum > 0 then
			medicament = Schemes:GetGoodsConfig(goodsID)
			if medicament then
				--判断是随机物品
				-- if medicament.UseMenu == 4 then
				-- 	for ii = 1, goodsNum, 1 do
				-- 		Schemes.PrizeTable.__RandomPrize(medicament.UseParam1, 1, true, 1, prizeGoodsNum,
				-- 			prizeGoodsList)
				-- 	end
				-- else
				if prizeGoodsNum[goodsID] then
					prizeGoodsNum[goodsID].Num = prizeGoodsNum[goodsID].Num + goodsNum
				else
					local goods = { ID = goodsID, Num = goodsNum, }
					prizeGoodsNum[goodsID] = goods
					table.insert(prizeGoodsList, goods)
				end
				-- end
			end
		end
	end

	self.__SucceedRewardsList[stageId] = prizeGoodsList
	return prizeGoodsList
end

Schemes.EquipWeapon = {}
function Schemes.EquipWeapon:GetByGroupID(groupID)
	self._groupIDList = {}
	for i, v in ipairs(Schemes.EquipWeapon.items) do
		if not self._groupIDList[v.GroupID] then
			self._groupIDList[v.GroupID] = {}
		end
		table.insert(self._groupIDList[v.GroupID], v)
	end
	--重置函数
	Schemes.EquipWeapon.GetByGroupID = function(_self, _frontType)
		return _self._groupIDList[_frontType]
	end
	return self._groupIDList[groupID]
end

Schemes.Bullet = {}
function Schemes.Bullet:GetByBulletId(bulletId)
	local BulletId_List = {}
	for i, v in ipairs(self.items) do
		if not BulletId_List[v.BulletId] then
			BulletId_List[v.BulletId] = {}
		end
		table.insert(BulletId_List[v.BulletId], v)
	end
	self._BulletId_List = BulletId_List
	--重置函数
	self.GetByType = function(_self, _bulletId)
		return _self._BulletId_List[_bulletId]
	end
	return self._BulletId_List[bulletId]
end

--引导
Schemes.Guide = {}
function Schemes.Guide:GetIDList()
	self._idTypeList = {}
	self._idList = {}
	for i, v in ipairs(self.items) do
		if not self._idTypeList[v.ID] then
			self._idTypeList[v.ID] = {}
			table.insert(self._idList, v.ID)
		end
		table.insert(self._idTypeList[v.ID], v)
	end
	--ID排序小到大
	table.sort(self._idList, function(a, b)
		return a < b
	end)

	--重置函数
	self.GetIDList = function(_self)
		return _self._idList
	end
	return self._idList
end

function Schemes.Guide:GetByID(id)
	if self._idTypeList == nil then
		self:GetIDList()
	end
	--重置函数
	self.GetByID = function(_self, _id)
		return _self._idTypeList[_id]
	end
	return self._idTypeList[id]
end

---升星表
---@class EquipSmeltStar:EquipSmeltStar_Class
Schemes.EquipSmeltStar = {}
---获取属性表
---@param smeltID integer 升星Id
---@param starLvl integer 升星等级
---@return EquipSmeltStarCfg|nil
function Schemes.EquipSmeltStar:GetEquipSmeltStarCfg(smeltID, starLvl)
	self._equipSmeltStarCfgList = {}
	for i, v in ipairs(self.items) do
		self._equipSmeltStarCfgList[string.format("%s_%s", v.SmeltID, v.StarLvl)] = v
	end

	Schemes.EquipSmeltStar.GetEquipSmeltStarCfg = function(_self, _smeltID, _starLvl)
		return _self._equipSmeltStarCfgList[string.format("%s_%s", _smeltID, _starLvl)]
	end
	return self._equipSmeltStarCfgList[string.format("%s_%s", smeltID, starLvl)]
end

---升星表
---@param smeltID integer 升星Id
---@return EquipSmeltStarCfg[]|nil
function Schemes.EquipSmeltStar:GetBySmeltID(smeltID)
	self._smeltIDList = {}
	for i, v in ipairs(self.items) do
		if not self._smeltIDList[v.SmeltID] then
			self._smeltIDList[v.SmeltID] = {}
		end
		table.insert(self._smeltIDList[v.SmeltID], v)
	end

	---comment
	---@param _self EquipSmeltStar
	---@param _smeltID integer 升星Id
	---@return EquipSmeltStarCfg[]|nil
	Schemes.EquipSmeltStar.GetBySmeltID = function(_self, _smeltID)
		return _self._smeltIDList[tonumber(_smeltID) or 0]
	end
	return self._smeltIDList[tonumber(smeltID) or 0]
end

---@class EquipSmeltStarData:EquipSmeltStarData_Class
Schemes.EquipSmeltStarData = {}

---
---@param oprateType integer 类型
---@return EquipSmeltStarDataCfg[]|nil
function Schemes.EquipSmeltStarData:GetByOprateType(oprateType)
	self._oprateTypeList = {}
	for i, v in ipairs(self.items) do
		if not self._oprateTypeList[v.OprateType] then
			self._oprateTypeList[v.OprateType] = {}
		end
		table.insert(self._oprateTypeList[v.OprateType], v)
	end

	---
	---@param _self EquipSmeltStarData
	---@param _oprateType integer 类型
	---@return EquipSmeltStarDataCfg[]|nil
	Schemes.EquipSmeltStarData.GetByOprateType = function(_self, _oprateType)
		return _self._oprateTypeList[_oprateType]
	end
	return self._oprateTypeList[tonumber(oprateType) or 0]
end

---属性表
---@class CommonProp:CommonProp_Class
Schemes.CommonProp = {}

---签到奖励表
---@class PrizesevenSignin:PrizesevenSignin_Class
Schemes.PrizesevenSignin = {}

---comment
---@param prizeType integer 分类
---@return PrizesevenSigninCfg[]
function Schemes.PrizesevenSignin:GetByPrizeType(prizeType)
	---@type PrizesevenSigninCfg[][]
	self._prizeTypeList = {}
	for i, v in ipairs(self.items) do
		if not self._prizeTypeList[v.PrizeType] then
			self._prizeTypeList[v.PrizeType] = {}
		end
		table.insert(self._prizeTypeList[v.PrizeType], v)
	end

	---comment
	---@param _self PrizesevenSignin
	---@param _prizeType integer 分类
	---@return PrizesevenSigninCfg[]
	Schemes.PrizesevenSignin.GetByPrizeType = function(_self, _prizeType)
		return _self._prizeTypeList[_prizeType]
	end
	return self._prizeTypeList[prizeType]
end

local exSchemeTable = {
	Schemes.ChapEctype.Init,
	Schemes.TowerBattleMainLevel.Init,
	Schemes.TowerBattleSubLevel.Init,
	Schemes.TowerBattleObject.Init,
	Schemes.FunctionOpen.Init,
	Schemes.UIAtlasConfig.Init,
	Schemes.TowerBattleCollect.Init,
	Schemes.EquipSmelt.Init,
	Schemes.SegmentUpStar.Init,
	Schemes.PrizeTime.Init,
	Schemes.EquipSmeltBase.Init,
	Schemes.Store.Init,

	Schemes.ShowMessages.Init,
	Schemes.CatDrop.Init,
	Schemes.StoreRefresh.Init,
	Schemes.SkillUp.Init,
	Schemes.GroupBranchTask,
}
local exSchemeNum = #exSchemeTable

local preloadIndex = 1
local preloadIndexEx = 1
local preloadIndexLua = 1
local preloadEnd = false
function Schemes.ResetPreLoadLua()
	preloadIndexLua = 1
	preloadEnd = false
end

function Schemes.OnPreloadEnd()
	print('表格初始化完毕')
end

function Schemes.PreLoadScheme(numLoad)
	if preloadEnd then
		return false
	end
	print('exSchemeNum=' .. exSchemeNum)

	--Lua解析表格
	for i = 1, numLoad do
		if preloadIndex > pbSchemeNum then
			if preloadIndexEx > exSchemeNum then
				if preloadIndexLua > _NeedPreloadLuaSize then
					preloadEnd = true
					Schemes.OnPreloadEnd()
					collectgarbage("collect")
					break
				else
					require(_NeedPreloadLua[preloadIndexLua])
					preloadIndexLua = preloadIndexLua + 1
				end
			else
				if not exSchemeTable[preloadIndexEx] then print(preloadIndexEx) end
				exSchemeTable[preloadIndexEx]()
				preloadIndexEx = preloadIndexEx + 1
			end
		else
			local t = pbSchemeTable[preloadIndex]
			local scheme = Schemes[t]
			if not scheme then
				scheme = {}
				Schemes[t] = scheme
			end
			scheme.Load = Schemes_Load
			if not scheme.Get then
				scheme.Get = Schemes_Get
			end
			scheme:Load(t)
			preloadIndex = preloadIndex + 1
		end
	end

	--C#解析表格
	for index, name in ipairs(pbSchemeTable2) do
		if not Schemes[name] then
			Schemes[name] = {}
		end
		local scheme = Schemes[name]
		scheme.Load = Schemes_Load2
		if not scheme.Get then
			scheme.Get = Schemes_Get
		end
		scheme:Load(name)
	end
	return true
end

-- 延迟载入所有表格
function Schemes.LateLoadAllScheme(needLoad)
	Schemes.lateLoadScheme = needLoad
end

function InitTaskAndTaskBranch()
	for i, v in ipairs(Schemes.TaskBranch.items) do
		if v then
			for i = 1, 5 do
				for i1, v1 in ipairs(v['Task' .. i]) do
					if v1 ~= 0 then
						local taskScheme = Schemes.Task:Get(v1)
						if not taskScheme then
							error(' 任务表 ' .. v1)
						end
						TaskIDMapTaskBranchScheme[v1] = v
					end
				end
			end
		end
	end
end

function GetThisTaskBranchScheme(task)
	if not TaskIDMapTaskBranchScheme then
		TaskIDMapTaskBranchScheme = {}
		InitTaskAndTaskBranch()
	end
	if not TaskIDMapTaskBranchScheme[task.taskID] then
		print("找不到日常任务配置 " .. task.taskID)
		return nil
	end
	return TaskIDMapTaskBranchScheme[task.taskID]
end

BranchGroup = {}
function Schemes.GroupBranchTask()
	for _, v in ipairs(Schemes.TaskBranch.items) do
		if v and v.GroupID then
			local groupID = v.GroupID
			BranchGroup[groupID] = BranchGroup[groupID] or {}
			table.insert(BranchGroup[groupID], v)
		end
	end
end

-- 先不启用
Schemes.PreLoadScheme(9999)
