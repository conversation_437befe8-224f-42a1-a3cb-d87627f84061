
-- 错误日志--
function Error(str)
	if not Debugger.useLog then
		return;
	end
	Debugger.LogError(tostring(str) .. "\n" .. debug.traceback());
end

-- 警告日志--
function Warning(str)
	if not Debugger.useLog then
		return;
	end
	Debugger.LogWarning(tostring(str) .. "\n" .. debug.traceback());
end

-- 输出日志--
function log(str)
	if not Debugger.useLog then
		return;
	end
	Debugger.Log(tostring(str) .. "\n" .. debug.traceback());
end

-- 带lua堆栈的输出日志--弃用
function logTrace(str)
	if not Debugger.useLog then
		return;
	end
	Debugger.Log(tostring(str) .. "\n" .. debug.traceback());
end

function table.contains(table, element)
	if table == nil then
		return false
	end
	
	for _, value in pairs(table) do
		if value == element then
			return true
		end
	end
	
	return false
end

function table.getCount(table)
	if table == nil then
		return 0;
	end
	
	local _count = 0;
	for _, k in pairs(table) do
		_count = _count + 1;
	end
	return _count;
end

function AddSecretKey(args)
    local str = args.."&c33ec46e7b7c37420d6ee75f3c89b199";
    local md5 = Util.md5(str);
    return args.."&sign="..md5;
end

function TableEncry(str)
	local str = str.."&444c33ec46e7b7c37420d6ee75f3c89b199444";
	local md5 = Util.md5(str);
	return md5;
end

function StrToTable(strData)
	local str = string.gsub(strData, "\r", "");
	local str = string.gsub(strData, "\n", "");

	local f = loadstring("do local ret=" .. strData .. " return ret end")
	if f then
	   return f()
	end
end

function Split(szFullString, szSeparator)
	if nil == szFullString or nil == szSeparator then
		return nil;
	end
	
	local nFindStartIndex = 1;
	local nSplitIndex = 1;
	local nSplitArray = {};
	
	while true do
	   local nFindLastIndex = string.find(szFullString, szSeparator, nFindStartIndex);
	   if not nFindLastIndex then
			nSplitArray[nSplitIndex] = string.sub(szFullString, nFindStartIndex, string.len(szFullString));
			break
	   end
	   nSplitArray[nSplitIndex] = string.sub(szFullString, nFindStartIndex, nFindLastIndex - 1);
	   nFindStartIndex = nFindLastIndex + string.len(szSeparator);
	   nSplitIndex = nSplitIndex + 1;
	end
	
	return nSplitArray
end

function TableToStr(tableData)  
    local szRet = "{"  
    function doT2S(_i, _v)  
        if "number" == type(_i) then  
            szRet = szRet .. "[" .. _i .. "] = "  
            if "number" == type(_v) then  
                szRet = szRet .. _v .. ","  
            elseif "string" == type(_v) then  
                szRet = szRet .. '"' .. _v .. '"' .. ","  
            elseif "table" == type(_v) then  
                szRet = szRet .. TableToStr(_v) .. ","  
            else  
                szRet = szRet .. "nil,"  
            end  
        elseif "string" == type(_i) then  
            szRet = szRet .. '["' .. _i .. '"] = '  
            if "number" == type(_v) then  
                szRet = szRet .. _v .. ","  
            elseif "string" == type(_v) then  
                szRet = szRet .. '"' .. _v .. '"' .. ","  
            elseif "table" == type(_v) then  
                szRet = szRet .. TableToStr(_v) .. ","  
            else  
                szRet = szRet .. "nil,"  
            end  
        end  
    end  
    table.foreach(tableData, doT2S)  
    szRet = szRet .. "}"  
    return szRet  
end 

function EncodeURL(s)
    s = string.gsub(s, "([^%w%.%- ])", function(c) return string.format("%%%02X", string.byte(c)) end)
    return string.gsub(s, " ", "+")
end

function DecodeURL(s)
	s = string.gsub(s, "+", " ")
    s = string.gsub(s, '%%(%x%x)', function(h) return string.char(tonumber(h, 16)) end)
    return s
end

--去掉字符串前后空格
function sxtrim(str)
	str = string.gsub(str, "^[ \t\n\r]+", "")
	return string.gsub(str, "[ \t\n\r]+$", "")  
end

function trim(s) 
	return (string.gsub(s, "^%s*(.-)%s*$", "%1"))
end

--字符串数量
function utf8len(str)
  local len  = #str
  local left = len
  local cnt  = 0
  local arr  = {0, 0xc0, 0xe0, 0xf0, 0xf8, 0xfc}
  while left ~= 0 do
    local tmp = string.byte(str, -left)
    local i   = #arr
    while arr[i] do
      if tmp >= arr[i] then
        left = left - i
        break
      end
      i = i - 1
    end
    cnt = cnt + 1
  end
  return cnt
end

function GetBytes(char)
	if not char then
		return 0;
	end
	local code = string.byte(char);
	if code < 127 then
		return 1;
	elseif code <= 223 then
		return 2;
	elseif code <= 239 then
		return 3;
	elseif code <= 247 then
		return 4;
	else
		return 0;
	end
end

function StringSub(str, startIndex, endIndex)
	local tempStr = str;
	local byteStart = 1;
	local byteEnd = -1;
	local index = 0;
	local bytes = 0;

	startIndex = math.max(startIndex, 1);
	endIndex = endIndex or -1;
	while string.len(tempStr) > 0 do     
		if index == startIndex - 1 then
			byteStart = bytes + 1;
		elseif index == endIndex then
			byteEnd = bytes;
			break;
		end
		bytes = bytes + GetBytes(tempStr);
		tempStr = string.sub(str, bytes+1);
		index = index + 1
	end
	
   return string.sub(str, byteStart, byteEnd)
end

--获取客户端当前对应的服务器时间戳
function GetTimeZone(time)
	local data =tostring( time)
    if #data < 14 then
		return 0
	end 

	local y = string.sub(data, 1, 4)
	local m = string.sub(data, 5, 6)
	local d = string.sub(data, 7, 8)
	local h = string.sub(data, 9, 10)
	local mi = string.sub(data, 11, 12)
	local s = string.sub(data, 13, 14)
	
	local t_startTime = { year=y, month=m, day=d, hour=h, min=mi, sec=s }
	local startTime = os.time(t_startTime)   
	if not startTime then 
		return 0;
	end
	return startTime;
end

function get_week_day()
	local time = GameDataManager.GetServerTime();
	time = math.floor(time);
	local t = os.date("*t", time);
	return t.wday;
end

function MailTime(time)
	return os.date("%Y-%m-%d", time);
end

function MineEventTime(time)
	return os.date("%Y-%m-%d %H:%M:%S", time);
end

--时分秒倒计时
function LotteryTime(s)
	s = math.floor(s);
	if s < 0 then
		return "";
	end
	
	local h = math.floor(s / 3600);
	if h > 0 then
		s = s % 3600;
	end
	local m = math.floor(s / 60);
	if m > 0 then
		s = s % 60;
	end
	
	local str = "";
	if h >= 10 then
		str = str..h..":";
	else
		str = str.."0"..h..":";
	end

	if m >= 10 then
		str = str..m..":";
	else
		str = str.."0"..m..":";
	end
	
	if s >= 10 then
		str = str..s;
	else
		str = str.."0"..s;
	end
	
	return str;
end

function ActivityTime(s)
	s = math.floor(s);
	if s <= 0 then
		return "";
	end
	
	local d = math.floor(s / 86400);
	if d > 0 then
		s = s % 86400;
	end
	
	local h = math.floor(s / 3600);
	if h > 0 then
		s = s % 3600;
	end
	
	local m = math.floor(s / 60);
	if m > 0 then
		s = s % 60;
	end
	
	return d.._T["cn_100721"]..h.._T["cn_100722"]..m.._T["cn_100723"]..s.._T["cn_100121"];
end

function LegionTime(s)
	s = math.floor(s);
	if s <= 0 then
		return "";
	end
	
	local d = math.floor(s / 86400);
	if d > 0 then
		s = s % 86400;
	end
	
	local h = math.floor(s / 3600);
	if h > 0 then
		s = s % 3600;
	end
	
	local m = math.floor(s / 60);
	if m > 0 then
		s = s % 60;
	end
	
	if d > 0 then
		return d.._T["cn_100721"]..h.._T["cn_100722"]..m.._T["cn_100723"]..s.._T["cn_100121"];
	else
		if h > 0 then
			return h.._T["cn_100722"]..m.._T["cn_100723"]..s.._T["cn_100121"];
		else
			if m > 0 then
				return m.._T["cn_100723"]..s.._T["cn_100121"];
			else
				return s.._T["cn_100121"];
			end
		end
	end
	
	return d.._T["cn_100721"]..h.._T["cn_100722"]..m.._T["cn_100723"]..s.._T["cn_100121"];
end

function NumUnit(num)
	if nil == num then
		return "";
	end
	
	if math.floor(num / 100000000) > 0 then
		return math.floor(num / 100000000).._T["cn_100725"];
	end
	if math.floor(num / 10000) > 0 then
		return math.floor(num / 10000).._T["cn_100726"];
	end
	return tostring(num);
end

function GetURL(type)
	return AppConst.loginUrl..type;
end

--获得物品
function ItemToStr(attach_list, split)
	if nil == attach_list then
		return "";
	end
	
	local _tab = {}; 
	for _,v in ipairs(attach_list) do
		local str = nil;
		if v.object_type == object_type.cortege then
			str = NameList("CORTEGE_NAME_"..v.attach_type).."*"..v.attach_count;
		else
			local cfg = ConfigManager.GetItemBaseById(v.attach_type);
			if cfg then
				str = ItemColor[cfg.quality]..NameList("ITEM_NAME_"..v.attach_type).."*"..v.attach_count.."[-]";
			end
		end
		
		if str then
			table.insert(_tab, str);
		end
	end
	return table.concat(_tab, (split or "，"));
end

function DropStrTab(attach_list)
	local _tab = {}; 
	if nil == attach_list then
		return _tab;
	end
	
	for _,v in ipairs(attach_list) do
		local str = nil;
		if v.object_type == object_type.cortege then
			str = NameList("CORTEGE_NAME_"..v.attach_type).."*"..v.attach_count;
		else
			local cfg = ConfigManager.GetItemBaseById(v.attach_type);
			if cfg then
				str = ItemColor[cfg.quality]..NameList("ITEM_NAME_"..v.attach_type).."*"..v.attach_count.."[-]";
			end
		end
		
		if str then
			table.insert(_tab, str);
		end
	end
	return _tab;
end

function DropToStr(id, split)
	local _tab = {}; 
	local drop_cfg = ConfigManager.GetDropById(id);
	local drop_list = Split(drop_cfg.list, "|");
	for _,v in ipairs(drop_list) do
		local str = nil;
		local tmp = Split(v, ";");
		if tonumber(tmp[1]) == object_type.cortege then
			str = NameList("CORTEGE_NAME_"..tmp[2]).."*"..tmp[5];
		else
			local item_id = tonumber(tmp[2]);
			local cfg = ConfigManager.GetItemBaseById(item_id);
			if item_id == 9001 then
				str = ItemColor[cfg.quality]..NameList("ITEM_NAME_"..tmp[2]).."*"..
					NumUnit(tonumber(tmp[5])).."[-]";
			else
				str = ItemColor[cfg.quality]..NameList("ITEM_NAME_"..tmp[2]).."*"..tmp[5].."[-]";
			end
		end
		table.insert(_tab, str);
	end
	
	split = split or "，";
	return table.concat(_tab, split);
end

function NameList(id)
	local cfg = ConfigManager.GetNameListById(id);
	if cfg then
		return cfg.name_string;
	end
	return ""
end

function IsUnLock(id)
	local info = HomeDataManager.GetFuncDisInfo(id);
	if info then
		return true;
	end
	return false;
end

--显示功能开启界面
function ShowFuncOpen(id)
	local info = HomeDataManager.GetFuncDisInfo(id);
	if info then
		if info.state == 0 then
			local cfg = ConfigManager.GetFunctionDisplay(id);
			if cfg.open_tips ~= "" then
				ModuleManager.SendNotification(HomeNotes.OPEN_FUNC_OPEN_PANEL, info);
			end
		end
	end
end

--显示章节剧情
function ShowChapterPlot(id)
	--引导中不弹出副本对话框
	if not WhetherPopout() then
		return false;
	end
	
	local info = OutsideDataManager.GetTollgateInfo();
	if id < info.newest_chall_id or (info.newest_chall_id == info.newest_pass_id) then
		return false;
	end
	
	local cfg = ConfigManager.GetGuideTipById(info.newest_chall_id);
	if cfg then
		ModuleManager.SendNotification(HomeNotes.OPEN_CHAPTER_PLOT);
		return true;
	end
	return false;
end

--一些弹框在某些条件不弹出
function WhetherPopout()
	if GuideDataManager.IsGuidePause() and
		not OutsideDataManager.GetArenaFightStatus() and 
		not OutsideDataManager.GetWildFightStatus() and 
		not OutsideDataManager.GetLegionBossFightStatus() and
		not ScienceDataManager.GetExperienceFightStatus() and
		not ScienceDataManager.GetMineFightStatus() and 
		not AthleticsDataManager.GetCrossFightStatus() and
		not OutsideDataManager.GetJobFightStatus() and 
		not OutsideDataManager.GetResFightStatus() and
		not OutsideDataManager.GetTowerFightStatus() and
		not OutsideDataManager.GetInfiniteFightStatus() and
		not OutsideDataManager.GetInfiniteEmbattleStatus() and
		not OutsideDataManager.GetBossFightStatus() then
		return true;
	end
	return false;
end

function GetAttrTypeName(type)
	if type == AttrChangeType.DamageEnhanced then
		return _T["cn_100727"];
	elseif type == AttrChangeType.ReboundDamage then
		return _T["cn_100728"];
	elseif type == AttrChangeType.Firing then
		return _T["cn_100729"];
	elseif type == AttrChangeType.Bleed then
		return _T["cn_100730"];
	elseif type == AttrChangeType.Block then
		return _T["cn_100731"];
	elseif type == AttrChangeType.TargetCrit then
		return _T["cn_100732"];
	elseif type == AttrChangeType.SuckBlood then
		return _T["cn_100733"];
	elseif type == AttrChangeType.Silence then
		return _T["cn_100734"];
	elseif type == AttrChangeType.Frozen then
		return _T["cn_100735"];
	elseif type == AttrChangeType.Dizz then
		return _T["cn_100736"];
	elseif type == AttrChangeType.Benumb then
		return _T["cn_100737"];
	elseif type == AttrChangeType.WaitRevive then
		return _T["cn_100738"];
	else 
		return "";
	end
end

--显示属性
function ShowAttr(attr_type, attr_num)
	if attr_type == 1 or attr_type == 2 or attr_type == 3 or attr_type == 6 or attr_type == 9 then
		attr_num = Mathf.Round(attr_num);
		return AttrName[attr_type].."+"..attr_num;
	else
		attr_num = attr_num*0.01;
		return AttrName[attr_type].."+"..attr_num.."%";
	end
end

--装备基础属性
function GetEquipBaseEffect(id)
	local cfg = ConfigManager.GetEquipExBaseById(id);
	local base_effect_list = Split(cfg.base_effect_list, "|");
	return tonumber(base_effect_list[1]), tonumber(base_effect_list[2]);
end

--装备精炼基础和成长属性
function GetEquipRefineBaseAndGrowEffect(id, lv)
	local cfg = ConfigManager.GetEquipExRefineById(id, lv);
	local base_effect_list = Split(cfg.base_effect_list, "|");
	local grow_effect_list = Split(cfg.grow_effect_list, "|");
	return tonumber(base_effect_list[2]), tonumber(grow_effect_list[2]);
end

function ChangeString(str)
	local LIMITED_MSG = ConfigManager.GetConfig(ConfigManager.CONFIGNAME_FORBIDDEN);
	if LIMITED_MSG then
		for k, v in pairs(LIMITED_MSG) do     
			if string.len(str) >= string.len(v) then  
				if string.find(str, v, 1) then
					local star = ShowStar(v);
					str = string.gsub(str, v, star);
				end
			end
		end
	end
	return str;
end

function ShowStar(str)
	local len = widthSingle(str);
	local star = ""
	for i = 1,len do
		star = star.."*"
	end
	return star
end

function widthSingle(inputstr)
   local lenInByte = #inputstr;
   local width = 0;
   local i = 1;
   while (i <= lenInByte) 
    do
        local curByte = string.byte(inputstr, i);
        local byteCount = 1;
        if curByte>0 and curByte<=127 then
            byteCount = 1;                                          
        elseif curByte>=192 and curByte<223 then
            byteCount = 2;                                         
        elseif curByte>=224 and curByte<239 then
            byteCount = 3;                                        
        elseif curByte>=240 and curByte<=247 then
            byteCount = 4;                                      
        end

		if curByte == 46 then
			return 6;
		end
                                      
        i = i + byteCount;                                
        width = width + 1;                               
    end
    return width;
end

function calc_fight_result(f_r)
    local result = 1
    local tmp = 1
    local a = 1
    local b = 1

    for i = 1, 3 do
        local op_i = f_r.rvo[i] - f_r.ao
        if i == 1 then
            a = f_r.rvl[1] - f_r.av
            b = f_r.rvl[2] - f_r.av
        else
            a = tmp
            b = f_r.rvl[i + 1] - f_r.av
        end

        -- 加法
        if op_i == 1 then
            tmp = a + b
        -- 减法
        elseif op_i == 2 then
            tmp = math.abs(a - b)
            if tmp == 0 then
                tmp = a + b
            end
        --  乘法
        elseif op_i == 3 then
            if a > 100 then
                a = a % 100
                if a < 50 then
                    a = 39
                end
            end

            if b > 100 then
                b = b % 100
                if b < 50 then
                    b = 49
                end
            end

            tmp = a * b
        elseif op_i == 4 then
            if a > 100 then
                a = a % 100
                if a < 50 then
                    a = 29
                end
            end

            if b > 100 then
                b = b % 100
                if b < 50 then
                    b = 99
                end
            end

            tmp = a * a + b * b
        elseif op_i == 5 then
            tmp = math.abs(a - 1000) + b
        elseif op_i == 6 then
            tmp = math.abs(b - 1000) + a
        end
    end

    return tmp
end

function calc_fight_result_boss(f_r)
    local result = 1
    local tmp = 1
    local a = 1
    local b = 1

    for i = 1, 3 do
         local op_i = f_r.rvo[i] - f_r.ao
        if i == 1 then
            a = f_r.rvl[1]
            b = f_r.rvl[2]
        else
            a = tmp
            b = f_r.rvl[i + 1]
        end

        -- 加法
        if op_i == 1 then
            tmp = a + b
        -- 减法
        elseif op_i == 2 then
            tmp = math.abs(a - b)
            if tmp == 0 then
                tmp = a + b
            end
        --  乘法
        elseif op_i == 3 then
            if a > 100 then
                a = a % 100
                if a < 50 then
                    a = 39
                end
            end

            if b > 100 then
                b = b % 100
                if b < 50 then
                    b = 49
                end
            end

            tmp = a * b
        --  平方和
        elseif op_i == 4 then
            if a > 100 then
                a = a % 100
                if a < 50 then
                    a = 29
                end
            end

            if b > 100 then
                b = b % 100
                if b < 50 then
                    b = 99
                end
            end

            tmp = a * a + b * b
        -- a-1000
        elseif op_i == 5 then
            tmp = math.abs(a - 1000) + b
        -- b-1000
        elseif op_i == 6 then
            tmp = math.abs(b - 1000) + a
        end
    end

    return tmp
end

-- 检测移动
function check_wild_adventure_move(cur_coordinate, move_coordinate)
    -- 当前坐标
    local cur_x = tonumber(cur_coordinate[1])
    local cur_y = tonumber(cur_coordinate[2])
	
	-- 移动坐标
    local move_x = tonumber(move_coordinate[1])
    local move_y = tonumber(move_coordinate[2])

	if cur_x == move_x and cur_y == move_y then
		return false
	end
	
    local is_legal = false

    if cur_y == move_y then
        if move_x == cur_x - 2 or move_x == cur_x + 2 then
            is_legal = true
        end
    elseif move_x == cur_x - 1 or move_x == cur_x + 1 then
        if move_y == cur_y + 1 or move_y == cur_y - 1 then
            is_legal = true
        end
    end
    
    if not is_legal then
        return false
    end

    return true
end

--复制table数据
function CopyTable(info)
	local tmp = {};
	for k,v in pairs(info) do
		tmp[k] = v;
	end
	return tmp;
end

function WheelCostName(type)
	if type == 1 then
		return _T["cn_100739"];
	elseif type == 2 then
		return _T["cn_100018"];
	elseif type == 3 then
		return _T["cn_100741"];
	elseif type == 4 then
		return _T["cn_100742"];
	elseif type == 5 then
		return _T["cn_100743"];
	end
end

function ClosePopout()
	PanelManager.RemovePopoutPanel();
	ModuleManager.SendNotification(HomeNotes.CLOSE_CHAPTER_PLOT);
end

function ShowDrop(param)
	local info = {};
	info.type = TipType.drop;
	for _,v in pairs(param) do
		info.desc = v;
		ModuleManager.SendNotification(CommonTipNotes.OPEN_COMMONTIP_PANEL, info);
	end
end

function ShowSkillTip(param)
	local info = {};
	info.type = TipType.skilltip;
	info.desc = param;
	ModuleManager.SendNotification(CommonTipNotes.OPEN_COMMONTIP_PANEL, info);
end

function ShowFight(param)
	local info = {};
	info.type = TipType.fight;
	info.fight = param;
	ModuleManager.SendNotification(CommonTipNotes.OPEN_COMMONTIP_PANEL, info);
end

function ShowFirstAttack(param)
	local info = {};
	info.type = TipType.first_attack;
	info.fight = param;
	ModuleManager.SendNotification(CommonTipNotes.OPEN_COMMONTIP_PANEL, info);
end

function ShowWarn(param)
	local info = {};
	info.type = TipType.warn;
	info.desc = FontColor[33]..param;
	ModuleManager.SendNotification(CommonTipNotes.OPEN_COMMONTIP_PANEL, info);
end

--升级、进阶等提示
function ShowUpWarn(param, color)
	local info = {};
	info.type = TipType.upwarn;
	info.desc = param;
	info.color = color;
	ModuleManager.SendNotification(CommonTipNotes.OPEN_COMMONTIP_PANEL, info);
end

function WarnDialog(param)
	ModuleManager.SendNotification(WarnDialogNotes.OPEN_WARNDIALOG_PANEL, param);
end

function ExitDialog(param)
	ModuleManager.SendNotification(WarnDialogNotes.OPEN_EXITDIALOG_PANEL, param);
end

function ShowAwait(status)
	ModuleManager.SendNotification(CommonTipNotes.OPEN_AWAIT_PANEL, status);
end

function CortegeName(id, aptitude)
	local cfg = ConfigManager.GetCortegeInitAptitude(id);
	if aptitude and aptitude > cfg.aptitude then
		return _T["cn_100854"]..NameList("CORTEGE_NAME_"..id);
	end
	return NameList("CORTEGE_NAME_"..id);
end

--加速行为强制退出
function AcceleExit()
	local param = {};
	param.s_title = _T["cn_100062"];
	param.s_content = FontColor[6].._T["cn_100744"];
	param.type = ExitType.network;
	ExitDialog(param);
	--关闭网络连接
	networkMgr:CloseSocket();
end

--数据异常强制退出
function AbnormalExit()
	local param = {};
	param.s_title = _T["cn_100062"];
	param.s_content = FontColor[6].._T["cn_100745"];
	param.type = ExitType.network;
	ExitDialog(param);
	--关闭网络连接
	networkMgr:CloseSocket();
end

--幸存者数据异常
function SurviveAbnormalExit(embattle_list)
	local isExit = false;
	for _,v in pairs(embattle_list) do
		local proInfo = SurviveDataManager.GetCortegeProById(v.uid);
		if proInfo then
			for key,val in pairs(proInfo) do
				if ((v[key]*2 + 444)*2 - 709)*2 ~= val then
					isExit = true;
					break;
				end
			end
		end
		if isExit then
			break;
		end
	end

	if isExit then
		AbnormalExit();
	end
end

function IsOpenServiceNextDay()
	if RoleData and RoleData.open_time then
		local time = RoleData.open_time;
		time = math.floor(time);
		local t = os.date("*t", time); 
		local s = os.time({year = t["year"],month = t["month"],day = t["day"], hour = 5});
		if GameDataManager.GetServerTime() - s > 86400 then
			return true;
		end
	end
	return false;
end

function CreateTime()
	if RoleData and RoleData.create_time then
		local time = RoleData.create_time;
		time = math.floor(time);
		local t = os.date("*t", time); 
		local s = os.time({year = t["year"],month = t["month"],day = t["day"], hour = 5});
		return s;
	end
	return 0;
end

function InCreateServerDay()
	if RoleData and RoleData.create_time then
		local time = RoleData.create_time;
		time = math.floor(time);
		local t = os.date("*t", time); 
		local s = os.time({year = t["year"],month = t["month"],day = t["day"], hour = 5});
		local interval = GameDataManager.GetServerTime() - s;
		if math.floor(interval/86400) + 1 <= 7 then
			return true;
		end
	end
	return false;
end