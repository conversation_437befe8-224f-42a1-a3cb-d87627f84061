--[[
********************************************************************
    created:	2024/05/16
    author :	李锦剑
    purpose:    玩家数据(只读)
*********************************************************************
--]]

---玩家数据类
---@class GamePlayerData
local m = {}
GamePlayerData = m


--Http请求返回数据处理
---@param requestID integer 请求ID
---@param data any 请求返回数据
function m.HttpHandle(requestID, data)
    if data == nil then return end

    if requestID == ERequestID.ListPets_OnStage or requestID == ERequestID.SavePets_OnStage then
        if data.Value ~= nil then
            m.PetsInStage = data.Value
            EventManager:Fire(EventID.UpdateListPets_OnStage, data.Value)
        end
    elseif requestID == ERequestID.EndStage
        or requestID == ERequestID.SaveStageScore
        or requestID == ERequestID.SaveStageScore2
    then
        HttpReques.SendRequest(ERequestID.ListPassedStage_StageType)
        HttpReques.SendRequest(ERequestID.ListStageScores, nil, nil, nil, 'GET')
    elseif requestID == ERequestID.ListActorTask then
        m.ActorTask:Set(data)
    elseif requestID == ERequestID.SaveActorTask then
        HttpReques.SendRequest(ERequestID.ListActorTask)
    elseif requestID == ERequestID.AddActorBattleTreasure then
        HttpReques.SendRequest(ERequestID.ListActorBattleTreasure, nil, nil, nil, 'GET')
    elseif requestID == ERequestID.SaveLog_Charge then
        EntityModule.GetUserIdNo()
    elseif requestID == ERequestID.GetUserIdNo then
        EntityModule.InitVerifyInfo(data.Value)
    elseif requestID == ERequestID.IdNameVerify then
        EntityModule.InitVerifyInfo(data.Value)
    end

    --Http通用更新事件
    EventManager:Fire(EventID.UpdateHttpRequestCallBackData, requestID)
end

--初始化数据
function m.InitData()
    HttpReques.SendRequest(ERequestID.ListPassedStage_StageType)
    HttpReques.SendRequest(ERequestID.ListActorTask)
    HttpReques.SendRequest(ERequestID.ListActorBattleTreasure, nil, nil, nil, 'GET')
    HttpReques.SendRequest(ERequestID.ListActorEquip, { EquipType = 1 }, nil, nil, 'GET')
    HttpReques.SendRequest(ERequestID.ListStageScores, nil, nil, nil, 'GET')
    HttpReques.SendRequest(ERequestID.ListActorBattleStat_Day, nil, nil, nil, 'GET')
end

--注册--自身细胞创建成功--事件
EventManager:Subscribe(EventID.OnHeroCreate, m.InitData)


--宠物出战列表
m.PetsInStage = {}

--#region 逻辑值
-------------------------------------------------------

--逻辑值
m.LogicValue = {}

---获取逻辑值
---@param dataCatalog integer 逻辑值ID
---@param index integer 索引(索引从1开始)
---@param onlyValid ?boolean 是否仅取效数据，默认：true
---@return integer
function m.LogicValue:GetIntByIndex(dataCatalog, index, onlyValid)
    return LogicValue.GetIntByIndex(dataCatalog, index, onlyValid)
end

-------------------------------------------------------
--#endregion

--#region 副本
-------------------------------------------------------

---副本类
m.GameEctype = {}

---获取--已通关最大关卡ID
---@param stageType integer 副本类型 1-14，默认：1
---@return integer
function m.GameEctype:GetProgress(stageType)
    stageType = tonumber(stageType) or 1
    --return LogicValue.GetEctypeStageLV(stageType, EctypeStage_Index.MAX_STAGE_ID)
    return HeroDataManager:GetLogicData(EctypeSaveLogic[stageType])
end

---获取--指定类型副本可挑战ID
---@param stageType integer 副本类型
---@return integer
function m.GameEctype:GetChallengeableID(stageType)
    --已通关记录副本ID
    local stageID = self:GetProgress(stageType)
    if stageID ~= 0 then
        --获取可挑战ID(记录的下一关)
        local catMainStage = Schemes.CatMainStage:Get(stageID + 1)
        if catMainStage then
            stageID = catMainStage.ID
        end
    else
        --获取--指定类型副本--首关ID
        local list = Schemes.CatMainStage:GetByFrontType(stageType)
        if list and #list > 0 then
            stageID = list[1].ID
        end
    end
    return stageID
end

---判断--副本关卡宝箱是否已领取
---@param stageID integer 副本ID
---@param index integer 索引(从1开始)
function m.GameEctype:IsGet(stageID, index)
    local value = LogicValue.GetEctypeBoxLV(stageID, EctypeBox_Index.TREASURE_LOGICAL_VALUE)
    local lv = LogicValue.GetBit(value, index)
    return lv ~= 0
end

---获取--副本关卡波数
---@param stageID integer 副本ID
---@return integer
function m.GameEctype:GetRankNo(stageID)
    return LogicValue.GetEctypeBoxLV(stageID, EctypeBox_Index.MAX_RANK_NO)
end

---获取--副本每日最高得分
---@param stageID integer 副本ID
---@return integer
function m.GameEctype:GetMaxScore(stageID)
    return LogicValue.GetEctypeBoxLV(stageID, EctypeBox_Index.EVERYDAY_ACCUMULATE_SCORE)
end

---请求--副本每日buffs
---@param stageType integer 副本类型
function m.GameEctype:RequestBuffs(stageType)
    local list = Helper.GetStagePropIds(stageType)
    -- print("----请求--副本每日buffs------", stageType, list.Length)
end

---获取--副本每日buffs
---@param stageID integer 副本ID
---@return integer[]
function m.GameEctype:GetBuffs(stageID)
    return LogicValue.GetEctypeBuffs(stageID) or {}
end

---获取--副本战斗进度
---@param index BattleProgress_Index 索引
---@return integer
function m.GameEctype.GetBattleProgress(index)
    return LogicValue.GetBattleProgress(index)
end

-------------------------------------------------------
--#endregion


--#region 任务
-------------------------------------------------------

---任务数据
---@class ActorTaskData
---@field TaskID integer
---@field ActorID integer
---@field TaskLine integer
---@field TaskLvl integer
---@field TaskStatus integer
---@field StartTime string
---@field EndTime string
---@field AbandonTime string
---@field RowStatus integer
---@field CrtTime string
---@field MdyTime string

--任务
m.ActorTask = {}

--任务数据
---@type ActorTaskData[]
m.ActorTask.data = {}
--主线任务ID
m.ActorTask.mainTaskID = 1

--设置--任务数据
---@param data {rows:ActorTaskData[]}
function m.ActorTask:Set(data)
    self.data = {}
    if data.rows and #data.rows > 0 then
        for k, v in pairs(data.rows) do
            self.data[v.TaskLine] = v
            -- if v.TaskStatus == 3 then
            self.mainTaskID = v.TaskLine
            -- end
        end
    end
    EventManager:Fire(EventID.UpdateActorTaskData)
end

--获取--已完成任务ID
---@param taskID ?integer
---@return integer
function m.ActorTask:GetFinishTaskID(taskID)
    return LogicValue.GetTaskDataLV(taskID or 1, TaskData_Index.FinishID)
end

--获取--当前任务状态，0为未完成，1为已完成
---@param taskID ?integer
---@return integer
function m.ActorTask:GetTaskState(taskID)
    return LogicValue.GetTaskDataLV(taskID or 1, TaskData_Index.State)
end

--获取主线任务ID
function m.ActorTask:GetMainTaskID()
    local taskID = self:GetFinishTaskID()
    -- local maxTaskID = Schemes.Task:MaxTaskID()
    local taskCfg = Schemes.Task:Get(taskID + 1)
    if taskCfg ~= nil then
        return taskID + 1
    end
    return -1
end

function m.ActorTask:GetTaskProgress(taskID)
    local taskCfg = Schemes.Task:Get(taskID)
    if taskCfg then
        -- local hero = EntityModule.hero
        -- local level = hero:GetProperty(CREATURE_FIELD.CREATURE_FIELD_LEVEL)
        -- if level < taskCfg.MinLevel or level > taskCfg.MaxLevel then
        --     return false
        -- end

        --主线副本
        if taskCfg.Type == TaskType.MainEctype then
            return m.GameEctype:GetProgress(taskCfg.Parameter1)
        end
    end
    return 0
end

-------------------------------------------------------
--#endregion


--#region 装备
-------------------------------------------------------

---装备类
m.ActorEquip = {}

---获取--装备数据(数组)
---@param equipType ?integer 装备类型，默认：1体魄
---@return integer[]|nil
function m.ActorEquip:GetWeaponsKnapsack(equipType)
    -- equipType = tonumber(equipType) or 1
    return LogicValue.GetWeaponsKnapsack()
end

---查询--装备数据
---@param equipID integer 装备ID
---@param equipType ?integer 装备类型，默认：1体魄
---@return integer
function m.ActorEquip:Find(equipID, equipType)
    local list = self:GetWeaponsKnapsack(equipType)
    if list then
        for i, v in ipairs(list) do
            if v == equipID then
                return equipID
            end
        end
    end
    return 0
end

---查询--是否穿戴
---@param equipID integer 装备ID
---@param equipType ?integer 装备类型，默认：1体魄
---@return boolean
function m.ActorEquip:IsWear(equipID, equipType)
    local id = self:Find(equipID, equipType)
    if id ~= 0 then
        return true
    end
    return false
end

---卸下装备
---@param equipID integer
---@param equipType ?integer 装备类型，默认：1体魄
function m.ActorEquip:UnloadEquip(equipID, equipType)
    if equipID == nil then
        return
    end

    --判断装备是否已经穿戴
    if not self:IsWear(equipID, equipType) then
        warn('UnloadEquip--卸下装备--装备未穿戴', equipID, equipType)
        return
    end

    --查询装备
    local id = self:Find(equipID, equipType)
    if id == 0 then
        warn('UnloadEquip--卸下装备--装备查询失败', equipID, equipType)
        return
    end

    local list = self:GetWeaponsKnapsack(equipType)
    if list == nil then
        warn('UnloadEquip--卸下装备--装备列表为空', equipID, equipType)
        return
    end

    --收集已穿戴装备
    local wearList = {}
    for i, v in ipairs(list) do
        if v ~= equipID then
            table.insert(wearList, v)
        end
    end

    --请求保存装备
    LogicValue.SetWeaponsKnapsack(wearList)
end

---穿戴装备
---@param equipID integer
---@param equipType ?integer 装备类型，默认：1体魄
function m.ActorEquip:WearEquip(equipID, equipType)
    if equipID == nil then
        return
    end

    --判断装备是否已经穿戴
    if self:IsWear(equipID, equipType) then
        warn('WearEquip--穿戴装备--装备已穿戴', equipID, equipType)
        return
    end

    --收集已穿戴装备
    local wearList = self:GetWeaponsKnapsack(equipType) or {}
    table.insert(wearList, equipID)

    --已解锁格子数量
    local unlockedNum = self:GetUnlockedLatticeAmount()

    --出战格子数量不足
    if #wearList > unlockedNum then
        warn('WearEquip--穿戴装备--出战格子数量不足', #wearList, unlockedNum, json.encode(wearList))
        return
    end

    --请求保存装备
    LogicValue.SetWeaponsKnapsack(wearList)
end

---替换装备(根据装备ID替换)
---@param equipID integer 原装备ID
---@param replaceEquipID integer 替换装备ID
---@param equipType ?integer 装备类型，默认：1体魄
function m.ActorEquip:ReplaceEquip(equipID, replaceEquipID, equipType)
    if equipID == nil or replaceEquipID == nil then
        return
    end

    --收集已穿戴装备
    local wearList = {}
    local list = self:GetWeaponsKnapsack(equipType)
    if list then
        for i, v in ipairs(list) do
            if v == equipID then
                table.insert(wearList, replaceEquipID)
            else
                table.insert(wearList, v)
            end
        end
    end

    --请求保存装备
    LogicValue.SetWeaponsKnapsack(wearList)
end

---替换装备(根据索引位置替换)
---@param equipID integer 装备ID
---@param index integer 替换装备ID
---@param equipType ?integer 装备类型，默认：1体魄
function m.ActorEquip:ReplaceEquipIndex(equipID, index, equipType)
    if equipID == nil or index == nil then
        return
    end

    local wearList = self:GetWeaponsKnapsack(equipType) or {}
    wearList[index] = equipID
    --请求保存装备
    LogicValue.SetWeaponsKnapsack(wearList)
end

---获取已解锁格子数量
---@return integer
function m.ActorEquip:GetUnlockedLatticeAmount(equipType)
    --主线副本进度
    local stageID = m.GameEctype:GetProgress(equipType)
    --已解锁格子数量
    local unlockedNum = 0
    for i, id in ipairs(EQUIP_WEAPON_GRID_UNLOCKED_CONDITION) do
        if stageID >= id then
            unlockedNum = unlockedNum + 1
        end
    end
    return unlockedNum
end

---获取背包剩余格子数量
---@param equipType ?integer 装备类型，默认：1体魄
---@return integer
function m.ActorEquip:GetResidualLatticeAmount(equipType)
    --已解锁格子数量
    local unlockedNum = self:GetUnlockedLatticeAmount(equipType)
    local list = self:GetWeaponsKnapsack(equipType) or {}
    local num = unlockedNum - #list
    if num < 0 then
        num = 0
    end
    return num
end

---获取出战体魄数量
---@param equipType integer 装备类型，默认：1体魄
---@return integer
function m.ActorEquip:GetWearEquipAmount(equipType)
    local list = self:GetWeaponsKnapsack(equipType) or {}
    return #list
end

---获取装备升级经验
---@param equipID integer 装备ID
---@return integer
function m.ActorEquip:GetEquipUpgradeEXP(equipID)
    return LogicValue.GetEquipDataLV(equipID, 1)--EquipData_Index.UPGRADE_EXP
end

---装备类-新
m.ActorEquipNew = {}

---获取装备升级等级
---@param equipID integer 装备ID
---@return integer
function m.ActorEquipNew:GetEquipUpgradeLevel(equipID)
    return LogicValue.GetEquipDataLV2(equipID, 0)--EquipData_Index.UPGRADE_LEVEL
end

---获取装备升级经验
---@param equipID integer 装备ID
---@return integer
function m.ActorEquipNew:GetEquipUpgradeEXP(equipID)
    return LogicValue.GetEquipDataLV2(equipID, 1)--EquipData_Index.UPGRADE_EXP
end

---设置装备类型(部位)品质
---@param equipID integer 装备ID
---@param quality integer 装备类型品质
function m.ActorEquipNew:SetEquipTypeQuality(equipID, quality)
    local cfg = Schemes.Equipment:Get(equipID)
    if not cfg then return end
    local dataCatalog = cfg.TheBest
    if dataCatalog == nil then return end
    quality = tonumber(quality) or 0
    if quality < QUALITY.QUALITY_WHITE then
        quality = 0
    end
    if quality > QUALITY.QUALITY_RED then
        quality = QUALITY.QUALITY_RED
    end
    LogicValue.SetIntByIndex(dataCatalog, 1, quality + 1)
end

-------------------------------------------------------
--#endregion


--#region 活动
-------------------------------------------------------

--活动数据
m.ActorActivity = {}

--获取签到数据
---@param index SignInData_Index 索引
function m.ActorActivity:GetSignInData(index)
    return LogicValue.GetSignInData(index)
end

-------------------------------------------------------
--#endregion
