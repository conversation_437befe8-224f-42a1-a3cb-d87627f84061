-- Generated By protoc-gen-lua Do not Edit
local protobuf = require "protobuf/protobuf"
module('WorldMessage_pb')
local pb = {}


pb.MSG_WORLD_ACTIONID = protobuf.EnumDescriptor();
pb.MSG_WORLD_ACTIONID_MSG_WORLD_NONE_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_WORLD_ACTIONID_MSG_WORLD_LOADSCENE_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_WORLD_ACTIONID_MSG_WORLD_DESTORYSCENE_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_WORLD_ACTIONID_MSG_WORLD_STARTECTYPE_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_WORLD_ACTIONID_MSG_WORLD_REQUEST_SYN_9GRID_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_WORLD_ACTIONID_MSG_WORLD_ECTYPEINFO_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_WORLD_ACTIONID_MSG_WORLD_CREATE_HERO_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_WORLD_ACTIONID_MSG_WORLD_SCENE_STATICS_ENUM = protobuf.EnumValueDescriptor();
pb.SC_WORLD_LOADSCENE = protobuf.Descriptor();
pb.SC_WORLD_LOADSCENE_SCENEID_FIELD = protobuf.FieldDescriptor();
pb.SC_WORLD_LOADSCENE_MAPID_FIELD = protobuf.FieldDescriptor();
pb.SC_WORLD_LOADSCENE_POSX_FIELD = protobuf.FieldDescriptor();
pb.SC_WORLD_LOADSCENE_POSY_FIELD = protobuf.FieldDescriptor();
pb.SC_WORLD_LOADSCENE_POSZ_FIELD = protobuf.FieldDescriptor();
pb.SC_WORLD_LOADSCENE_SERVERTIME_FIELD = protobuf.FieldDescriptor();
pb.SC_WORLD_DESTORYSCENE = protobuf.Descriptor();
pb.SC_WORLD_DESTORYSCENE_SCENEID_FIELD = protobuf.FieldDescriptor();
pb.SC_WORLD_STRATECTYPE = protobuf.Descriptor();
pb.SC_WORLD_STRATECTYPE_ECTYPEID_FIELD = protobuf.FieldDescriptor();
pb.CS_WORLD_REQUEST_SYN_9GRID = protobuf.Descriptor();
pb.SC_WORLD_ECTYPEINFO = protobuf.Descriptor();
pb.SC_WORLD_ECTYPEINFO_ECTYPEID_FIELD = protobuf.FieldDescriptor();
pb.SC_WORLD_ECTYPEINFO_SCENEID_FIELD = protobuf.FieldDescriptor();
pb.SC_WORLD_ECTYPEINFO_ENDTIME_FIELD = protobuf.FieldDescriptor();
pb.SC_WORLD_ECTYPEINFO_TIMENEXT_FIELD = protobuf.FieldDescriptor();
pb.SC_WORLD_ECTYPEINFO_MONSTERNUM_FIELD = protobuf.FieldDescriptor();
pb.SC_WORLD_ECTYPEINFO_ZYGOTENUM_FIELD = protobuf.FieldDescriptor();
pb.SC_WORLD_ECTYPEINFO_BATCH_FIELD = protobuf.FieldDescriptor();
pb.SC_WORLD_SCENE_STATICS = protobuf.Descriptor();
pb.SC_WORLD_SCENE_STATICS_COUNTRYDATA = protobuf.Descriptor();
pb.SC_WORLD_SCENE_STATICS_COUNTRYDATA_COUNTRYID_FIELD = protobuf.FieldDescriptor();
pb.SC_WORLD_SCENE_STATICS_COUNTRYDATA_PLAYERCOUNT_FIELD = protobuf.FieldDescriptor();
pb.SC_WORLD_SCENE_STATICS_COUNTRYLIST_FIELD = protobuf.FieldDescriptor();

pb.MSG_WORLD_ACTIONID_MSG_WORLD_NONE_ENUM.name = "MSG_WORLD_NONE"
pb.MSG_WORLD_ACTIONID_MSG_WORLD_NONE_ENUM.index = 0
pb.MSG_WORLD_ACTIONID_MSG_WORLD_NONE_ENUM.number = 0
pb.MSG_WORLD_ACTIONID_MSG_WORLD_LOADSCENE_ENUM.name = "MSG_WORLD_LOADSCENE"
pb.MSG_WORLD_ACTIONID_MSG_WORLD_LOADSCENE_ENUM.index = 1
pb.MSG_WORLD_ACTIONID_MSG_WORLD_LOADSCENE_ENUM.number = 1
pb.MSG_WORLD_ACTIONID_MSG_WORLD_DESTORYSCENE_ENUM.name = "MSG_WORLD_DESTORYSCENE"
pb.MSG_WORLD_ACTIONID_MSG_WORLD_DESTORYSCENE_ENUM.index = 2
pb.MSG_WORLD_ACTIONID_MSG_WORLD_DESTORYSCENE_ENUM.number = 2
pb.MSG_WORLD_ACTIONID_MSG_WORLD_STARTECTYPE_ENUM.name = "MSG_WORLD_STARTECTYPE"
pb.MSG_WORLD_ACTIONID_MSG_WORLD_STARTECTYPE_ENUM.index = 3
pb.MSG_WORLD_ACTIONID_MSG_WORLD_STARTECTYPE_ENUM.number = 3
pb.MSG_WORLD_ACTIONID_MSG_WORLD_REQUEST_SYN_9GRID_ENUM.name = "MSG_WORLD_REQUEST_SYN_9GRID"
pb.MSG_WORLD_ACTIONID_MSG_WORLD_REQUEST_SYN_9GRID_ENUM.index = 4
pb.MSG_WORLD_ACTIONID_MSG_WORLD_REQUEST_SYN_9GRID_ENUM.number = 4
pb.MSG_WORLD_ACTIONID_MSG_WORLD_ECTYPEINFO_ENUM.name = "MSG_WORLD_ECTYPEINFO"
pb.MSG_WORLD_ACTIONID_MSG_WORLD_ECTYPEINFO_ENUM.index = 5
pb.MSG_WORLD_ACTIONID_MSG_WORLD_ECTYPEINFO_ENUM.number = 5
pb.MSG_WORLD_ACTIONID_MSG_WORLD_CREATE_HERO_ENUM.name = "MSG_WORLD_CREATE_HERO"
pb.MSG_WORLD_ACTIONID_MSG_WORLD_CREATE_HERO_ENUM.index = 6
pb.MSG_WORLD_ACTIONID_MSG_WORLD_CREATE_HERO_ENUM.number = 6
pb.MSG_WORLD_ACTIONID_MSG_WORLD_SCENE_STATICS_ENUM.name = "MSG_WORLD_SCENE_STATICS"
pb.MSG_WORLD_ACTIONID_MSG_WORLD_SCENE_STATICS_ENUM.index = 7
pb.MSG_WORLD_ACTIONID_MSG_WORLD_SCENE_STATICS_ENUM.number = 7
pb.MSG_WORLD_ACTIONID.name = "MSG_WORLD_ACTIONID"
pb.MSG_WORLD_ACTIONID.full_name = ".MSG_WORLD_ACTIONID"
pb.MSG_WORLD_ACTIONID.values = {pb.MSG_WORLD_ACTIONID_MSG_WORLD_NONE_ENUM,pb.MSG_WORLD_ACTIONID_MSG_WORLD_LOADSCENE_ENUM,pb.MSG_WORLD_ACTIONID_MSG_WORLD_DESTORYSCENE_ENUM,pb.MSG_WORLD_ACTIONID_MSG_WORLD_STARTECTYPE_ENUM,pb.MSG_WORLD_ACTIONID_MSG_WORLD_REQUEST_SYN_9GRID_ENUM,pb.MSG_WORLD_ACTIONID_MSG_WORLD_ECTYPEINFO_ENUM,pb.MSG_WORLD_ACTIONID_MSG_WORLD_CREATE_HERO_ENUM,pb.MSG_WORLD_ACTIONID_MSG_WORLD_SCENE_STATICS_ENUM}
pb.SC_WORLD_LOADSCENE_SCENEID_FIELD.name = "SceneID"
pb.SC_WORLD_LOADSCENE_SCENEID_FIELD.full_name = ".SC_World_LoadScene.SceneID"
pb.SC_WORLD_LOADSCENE_SCENEID_FIELD.number = 1
pb.SC_WORLD_LOADSCENE_SCENEID_FIELD.index = 0
pb.SC_WORLD_LOADSCENE_SCENEID_FIELD.label = 2
pb.SC_WORLD_LOADSCENE_SCENEID_FIELD.has_default_value = false
pb.SC_WORLD_LOADSCENE_SCENEID_FIELD.default_value = 0
pb.SC_WORLD_LOADSCENE_SCENEID_FIELD.type = 13
pb.SC_WORLD_LOADSCENE_SCENEID_FIELD.cpp_type = 3

pb.SC_WORLD_LOADSCENE_MAPID_FIELD.name = "MapID"
pb.SC_WORLD_LOADSCENE_MAPID_FIELD.full_name = ".SC_World_LoadScene.MapID"
pb.SC_WORLD_LOADSCENE_MAPID_FIELD.number = 2
pb.SC_WORLD_LOADSCENE_MAPID_FIELD.index = 1
pb.SC_WORLD_LOADSCENE_MAPID_FIELD.label = 2
pb.SC_WORLD_LOADSCENE_MAPID_FIELD.has_default_value = false
pb.SC_WORLD_LOADSCENE_MAPID_FIELD.default_value = 0
pb.SC_WORLD_LOADSCENE_MAPID_FIELD.type = 13
pb.SC_WORLD_LOADSCENE_MAPID_FIELD.cpp_type = 3

pb.SC_WORLD_LOADSCENE_POSX_FIELD.name = "PosX"
pb.SC_WORLD_LOADSCENE_POSX_FIELD.full_name = ".SC_World_LoadScene.PosX"
pb.SC_WORLD_LOADSCENE_POSX_FIELD.number = 3
pb.SC_WORLD_LOADSCENE_POSX_FIELD.index = 2
pb.SC_WORLD_LOADSCENE_POSX_FIELD.label = 2
pb.SC_WORLD_LOADSCENE_POSX_FIELD.has_default_value = false
pb.SC_WORLD_LOADSCENE_POSX_FIELD.default_value = 0.0
pb.SC_WORLD_LOADSCENE_POSX_FIELD.type = 2
pb.SC_WORLD_LOADSCENE_POSX_FIELD.cpp_type = 6

pb.SC_WORLD_LOADSCENE_POSY_FIELD.name = "PosY"
pb.SC_WORLD_LOADSCENE_POSY_FIELD.full_name = ".SC_World_LoadScene.PosY"
pb.SC_WORLD_LOADSCENE_POSY_FIELD.number = 4
pb.SC_WORLD_LOADSCENE_POSY_FIELD.index = 3
pb.SC_WORLD_LOADSCENE_POSY_FIELD.label = 2
pb.SC_WORLD_LOADSCENE_POSY_FIELD.has_default_value = false
pb.SC_WORLD_LOADSCENE_POSY_FIELD.default_value = 0.0
pb.SC_WORLD_LOADSCENE_POSY_FIELD.type = 2
pb.SC_WORLD_LOADSCENE_POSY_FIELD.cpp_type = 6

pb.SC_WORLD_LOADSCENE_POSZ_FIELD.name = "PosZ"
pb.SC_WORLD_LOADSCENE_POSZ_FIELD.full_name = ".SC_World_LoadScene.PosZ"
pb.SC_WORLD_LOADSCENE_POSZ_FIELD.number = 5
pb.SC_WORLD_LOADSCENE_POSZ_FIELD.index = 4
pb.SC_WORLD_LOADSCENE_POSZ_FIELD.label = 2
pb.SC_WORLD_LOADSCENE_POSZ_FIELD.has_default_value = false
pb.SC_WORLD_LOADSCENE_POSZ_FIELD.default_value = 0.0
pb.SC_WORLD_LOADSCENE_POSZ_FIELD.type = 2
pb.SC_WORLD_LOADSCENE_POSZ_FIELD.cpp_type = 6

pb.SC_WORLD_LOADSCENE_SERVERTIME_FIELD.name = "ServerTime"
pb.SC_WORLD_LOADSCENE_SERVERTIME_FIELD.full_name = ".SC_World_LoadScene.ServerTime"
pb.SC_WORLD_LOADSCENE_SERVERTIME_FIELD.number = 6
pb.SC_WORLD_LOADSCENE_SERVERTIME_FIELD.index = 5
pb.SC_WORLD_LOADSCENE_SERVERTIME_FIELD.label = 2
pb.SC_WORLD_LOADSCENE_SERVERTIME_FIELD.has_default_value = false
pb.SC_WORLD_LOADSCENE_SERVERTIME_FIELD.default_value = 0
pb.SC_WORLD_LOADSCENE_SERVERTIME_FIELD.type = 13
pb.SC_WORLD_LOADSCENE_SERVERTIME_FIELD.cpp_type = 3

pb.SC_WORLD_LOADSCENE.name = "SC_World_LoadScene"
pb.SC_WORLD_LOADSCENE.full_name = ".SC_World_LoadScene"
pb.SC_WORLD_LOADSCENE.nested_types = {}
pb.SC_WORLD_LOADSCENE.enum_types = {}
pb.SC_WORLD_LOADSCENE.fields = {pb.SC_WORLD_LOADSCENE_SCENEID_FIELD, pb.SC_WORLD_LOADSCENE_MAPID_FIELD, pb.SC_WORLD_LOADSCENE_POSX_FIELD, pb.SC_WORLD_LOADSCENE_POSY_FIELD, pb.SC_WORLD_LOADSCENE_POSZ_FIELD, pb.SC_WORLD_LOADSCENE_SERVERTIME_FIELD}
pb.SC_WORLD_LOADSCENE.is_extendable = false
pb.SC_WORLD_LOADSCENE.extensions = {}
pb.SC_WORLD_DESTORYSCENE_SCENEID_FIELD.name = "SceneID"
pb.SC_WORLD_DESTORYSCENE_SCENEID_FIELD.full_name = ".SC_World_DestoryScene.SceneID"
pb.SC_WORLD_DESTORYSCENE_SCENEID_FIELD.number = 1
pb.SC_WORLD_DESTORYSCENE_SCENEID_FIELD.index = 0
pb.SC_WORLD_DESTORYSCENE_SCENEID_FIELD.label = 2
pb.SC_WORLD_DESTORYSCENE_SCENEID_FIELD.has_default_value = false
pb.SC_WORLD_DESTORYSCENE_SCENEID_FIELD.default_value = 0
pb.SC_WORLD_DESTORYSCENE_SCENEID_FIELD.type = 13
pb.SC_WORLD_DESTORYSCENE_SCENEID_FIELD.cpp_type = 3

pb.SC_WORLD_DESTORYSCENE.name = "SC_World_DestoryScene"
pb.SC_WORLD_DESTORYSCENE.full_name = ".SC_World_DestoryScene"
pb.SC_WORLD_DESTORYSCENE.nested_types = {}
pb.SC_WORLD_DESTORYSCENE.enum_types = {}
pb.SC_WORLD_DESTORYSCENE.fields = {pb.SC_WORLD_DESTORYSCENE_SCENEID_FIELD}
pb.SC_WORLD_DESTORYSCENE.is_extendable = false
pb.SC_WORLD_DESTORYSCENE.extensions = {}
pb.SC_WORLD_STRATECTYPE_ECTYPEID_FIELD.name = "EctypeID"
pb.SC_WORLD_STRATECTYPE_ECTYPEID_FIELD.full_name = ".SC_World_StratEctype.EctypeID"
pb.SC_WORLD_STRATECTYPE_ECTYPEID_FIELD.number = 1
pb.SC_WORLD_STRATECTYPE_ECTYPEID_FIELD.index = 0
pb.SC_WORLD_STRATECTYPE_ECTYPEID_FIELD.label = 2
pb.SC_WORLD_STRATECTYPE_ECTYPEID_FIELD.has_default_value = false
pb.SC_WORLD_STRATECTYPE_ECTYPEID_FIELD.default_value = 0
pb.SC_WORLD_STRATECTYPE_ECTYPEID_FIELD.type = 13
pb.SC_WORLD_STRATECTYPE_ECTYPEID_FIELD.cpp_type = 3

pb.SC_WORLD_STRATECTYPE.name = "SC_World_StratEctype"
pb.SC_WORLD_STRATECTYPE.full_name = ".SC_World_StratEctype"
pb.SC_WORLD_STRATECTYPE.nested_types = {}
pb.SC_WORLD_STRATECTYPE.enum_types = {}
pb.SC_WORLD_STRATECTYPE.fields = {pb.SC_WORLD_STRATECTYPE_ECTYPEID_FIELD}
pb.SC_WORLD_STRATECTYPE.is_extendable = false
pb.SC_WORLD_STRATECTYPE.extensions = {}
pb.CS_WORLD_REQUEST_SYN_9GRID.name = "CS_World_Request_Syn_9Grid"
pb.CS_WORLD_REQUEST_SYN_9GRID.full_name = ".CS_World_Request_Syn_9Grid"
pb.CS_WORLD_REQUEST_SYN_9GRID.nested_types = {}
pb.CS_WORLD_REQUEST_SYN_9GRID.enum_types = {}
pb.CS_WORLD_REQUEST_SYN_9GRID.fields = {}
pb.CS_WORLD_REQUEST_SYN_9GRID.is_extendable = false
pb.CS_WORLD_REQUEST_SYN_9GRID.extensions = {}
pb.SC_WORLD_ECTYPEINFO_ECTYPEID_FIELD.name = "EctypeID"
pb.SC_WORLD_ECTYPEINFO_ECTYPEID_FIELD.full_name = ".SC_World_EctypeInfo.EctypeID"
pb.SC_WORLD_ECTYPEINFO_ECTYPEID_FIELD.number = 1
pb.SC_WORLD_ECTYPEINFO_ECTYPEID_FIELD.index = 0
pb.SC_WORLD_ECTYPEINFO_ECTYPEID_FIELD.label = 2
pb.SC_WORLD_ECTYPEINFO_ECTYPEID_FIELD.has_default_value = false
pb.SC_WORLD_ECTYPEINFO_ECTYPEID_FIELD.default_value = 0
pb.SC_WORLD_ECTYPEINFO_ECTYPEID_FIELD.type = 13
pb.SC_WORLD_ECTYPEINFO_ECTYPEID_FIELD.cpp_type = 3

pb.SC_WORLD_ECTYPEINFO_SCENEID_FIELD.name = "SceneID"
pb.SC_WORLD_ECTYPEINFO_SCENEID_FIELD.full_name = ".SC_World_EctypeInfo.SceneID"
pb.SC_WORLD_ECTYPEINFO_SCENEID_FIELD.number = 2
pb.SC_WORLD_ECTYPEINFO_SCENEID_FIELD.index = 1
pb.SC_WORLD_ECTYPEINFO_SCENEID_FIELD.label = 2
pb.SC_WORLD_ECTYPEINFO_SCENEID_FIELD.has_default_value = false
pb.SC_WORLD_ECTYPEINFO_SCENEID_FIELD.default_value = 0
pb.SC_WORLD_ECTYPEINFO_SCENEID_FIELD.type = 13
pb.SC_WORLD_ECTYPEINFO_SCENEID_FIELD.cpp_type = 3

pb.SC_WORLD_ECTYPEINFO_ENDTIME_FIELD.name = "EndTime"
pb.SC_WORLD_ECTYPEINFO_ENDTIME_FIELD.full_name = ".SC_World_EctypeInfo.EndTime"
pb.SC_WORLD_ECTYPEINFO_ENDTIME_FIELD.number = 3
pb.SC_WORLD_ECTYPEINFO_ENDTIME_FIELD.index = 2
pb.SC_WORLD_ECTYPEINFO_ENDTIME_FIELD.label = 2
pb.SC_WORLD_ECTYPEINFO_ENDTIME_FIELD.has_default_value = false
pb.SC_WORLD_ECTYPEINFO_ENDTIME_FIELD.default_value = 0
pb.SC_WORLD_ECTYPEINFO_ENDTIME_FIELD.type = 13
pb.SC_WORLD_ECTYPEINFO_ENDTIME_FIELD.cpp_type = 3

pb.SC_WORLD_ECTYPEINFO_TIMENEXT_FIELD.name = "TimeNext"
pb.SC_WORLD_ECTYPEINFO_TIMENEXT_FIELD.full_name = ".SC_World_EctypeInfo.TimeNext"
pb.SC_WORLD_ECTYPEINFO_TIMENEXT_FIELD.number = 4
pb.SC_WORLD_ECTYPEINFO_TIMENEXT_FIELD.index = 3
pb.SC_WORLD_ECTYPEINFO_TIMENEXT_FIELD.label = 2
pb.SC_WORLD_ECTYPEINFO_TIMENEXT_FIELD.has_default_value = false
pb.SC_WORLD_ECTYPEINFO_TIMENEXT_FIELD.default_value = 0
pb.SC_WORLD_ECTYPEINFO_TIMENEXT_FIELD.type = 13
pb.SC_WORLD_ECTYPEINFO_TIMENEXT_FIELD.cpp_type = 3

pb.SC_WORLD_ECTYPEINFO_MONSTERNUM_FIELD.name = "MonsterNum"
pb.SC_WORLD_ECTYPEINFO_MONSTERNUM_FIELD.full_name = ".SC_World_EctypeInfo.MonsterNum"
pb.SC_WORLD_ECTYPEINFO_MONSTERNUM_FIELD.number = 5
pb.SC_WORLD_ECTYPEINFO_MONSTERNUM_FIELD.index = 4
pb.SC_WORLD_ECTYPEINFO_MONSTERNUM_FIELD.label = 2
pb.SC_WORLD_ECTYPEINFO_MONSTERNUM_FIELD.has_default_value = false
pb.SC_WORLD_ECTYPEINFO_MONSTERNUM_FIELD.default_value = 0
pb.SC_WORLD_ECTYPEINFO_MONSTERNUM_FIELD.type = 13
pb.SC_WORLD_ECTYPEINFO_MONSTERNUM_FIELD.cpp_type = 3

pb.SC_WORLD_ECTYPEINFO_ZYGOTENUM_FIELD.name = "ZygoteNum"
pb.SC_WORLD_ECTYPEINFO_ZYGOTENUM_FIELD.full_name = ".SC_World_EctypeInfo.ZygoteNum"
pb.SC_WORLD_ECTYPEINFO_ZYGOTENUM_FIELD.number = 6
pb.SC_WORLD_ECTYPEINFO_ZYGOTENUM_FIELD.index = 5
pb.SC_WORLD_ECTYPEINFO_ZYGOTENUM_FIELD.label = 2
pb.SC_WORLD_ECTYPEINFO_ZYGOTENUM_FIELD.has_default_value = false
pb.SC_WORLD_ECTYPEINFO_ZYGOTENUM_FIELD.default_value = 0
pb.SC_WORLD_ECTYPEINFO_ZYGOTENUM_FIELD.type = 13
pb.SC_WORLD_ECTYPEINFO_ZYGOTENUM_FIELD.cpp_type = 3

pb.SC_WORLD_ECTYPEINFO_BATCH_FIELD.name = "Batch"
pb.SC_WORLD_ECTYPEINFO_BATCH_FIELD.full_name = ".SC_World_EctypeInfo.Batch"
pb.SC_WORLD_ECTYPEINFO_BATCH_FIELD.number = 7
pb.SC_WORLD_ECTYPEINFO_BATCH_FIELD.index = 6
pb.SC_WORLD_ECTYPEINFO_BATCH_FIELD.label = 2
pb.SC_WORLD_ECTYPEINFO_BATCH_FIELD.has_default_value = false
pb.SC_WORLD_ECTYPEINFO_BATCH_FIELD.default_value = 0
pb.SC_WORLD_ECTYPEINFO_BATCH_FIELD.type = 13
pb.SC_WORLD_ECTYPEINFO_BATCH_FIELD.cpp_type = 3

pb.SC_WORLD_ECTYPEINFO.name = "SC_World_EctypeInfo"
pb.SC_WORLD_ECTYPEINFO.full_name = ".SC_World_EctypeInfo"
pb.SC_WORLD_ECTYPEINFO.nested_types = {}
pb.SC_WORLD_ECTYPEINFO.enum_types = {}
pb.SC_WORLD_ECTYPEINFO.fields = {pb.SC_WORLD_ECTYPEINFO_ECTYPEID_FIELD, pb.SC_WORLD_ECTYPEINFO_SCENEID_FIELD, pb.SC_WORLD_ECTYPEINFO_ENDTIME_FIELD, pb.SC_WORLD_ECTYPEINFO_TIMENEXT_FIELD, pb.SC_WORLD_ECTYPEINFO_MONSTERNUM_FIELD, pb.SC_WORLD_ECTYPEINFO_ZYGOTENUM_FIELD, pb.SC_WORLD_ECTYPEINFO_BATCH_FIELD}
pb.SC_WORLD_ECTYPEINFO.is_extendable = false
pb.SC_WORLD_ECTYPEINFO.extensions = {}
pb.SC_WORLD_SCENE_STATICS_COUNTRYDATA_COUNTRYID_FIELD.name = "CountryID"
pb.SC_WORLD_SCENE_STATICS_COUNTRYDATA_COUNTRYID_FIELD.full_name = ".SC_World_Scene_Statics.CountryData.CountryID"
pb.SC_WORLD_SCENE_STATICS_COUNTRYDATA_COUNTRYID_FIELD.number = 1
pb.SC_WORLD_SCENE_STATICS_COUNTRYDATA_COUNTRYID_FIELD.index = 0
pb.SC_WORLD_SCENE_STATICS_COUNTRYDATA_COUNTRYID_FIELD.label = 2
pb.SC_WORLD_SCENE_STATICS_COUNTRYDATA_COUNTRYID_FIELD.has_default_value = false
pb.SC_WORLD_SCENE_STATICS_COUNTRYDATA_COUNTRYID_FIELD.default_value = 0
pb.SC_WORLD_SCENE_STATICS_COUNTRYDATA_COUNTRYID_FIELD.type = 13
pb.SC_WORLD_SCENE_STATICS_COUNTRYDATA_COUNTRYID_FIELD.cpp_type = 3

pb.SC_WORLD_SCENE_STATICS_COUNTRYDATA_PLAYERCOUNT_FIELD.name = "PlayerCount"
pb.SC_WORLD_SCENE_STATICS_COUNTRYDATA_PLAYERCOUNT_FIELD.full_name = ".SC_World_Scene_Statics.CountryData.PlayerCount"
pb.SC_WORLD_SCENE_STATICS_COUNTRYDATA_PLAYERCOUNT_FIELD.number = 2
pb.SC_WORLD_SCENE_STATICS_COUNTRYDATA_PLAYERCOUNT_FIELD.index = 1
pb.SC_WORLD_SCENE_STATICS_COUNTRYDATA_PLAYERCOUNT_FIELD.label = 2
pb.SC_WORLD_SCENE_STATICS_COUNTRYDATA_PLAYERCOUNT_FIELD.has_default_value = false
pb.SC_WORLD_SCENE_STATICS_COUNTRYDATA_PLAYERCOUNT_FIELD.default_value = 0
pb.SC_WORLD_SCENE_STATICS_COUNTRYDATA_PLAYERCOUNT_FIELD.type = 13
pb.SC_WORLD_SCENE_STATICS_COUNTRYDATA_PLAYERCOUNT_FIELD.cpp_type = 3

pb.SC_WORLD_SCENE_STATICS_COUNTRYDATA.name = "CountryData"
pb.SC_WORLD_SCENE_STATICS_COUNTRYDATA.full_name = ".SC_World_Scene_Statics.CountryData"
pb.SC_WORLD_SCENE_STATICS_COUNTRYDATA.nested_types = {}
pb.SC_WORLD_SCENE_STATICS_COUNTRYDATA.enum_types = {}
pb.SC_WORLD_SCENE_STATICS_COUNTRYDATA.fields = {pb.SC_WORLD_SCENE_STATICS_COUNTRYDATA_COUNTRYID_FIELD, pb.SC_WORLD_SCENE_STATICS_COUNTRYDATA_PLAYERCOUNT_FIELD}
pb.SC_WORLD_SCENE_STATICS_COUNTRYDATA.is_extendable = false
pb.SC_WORLD_SCENE_STATICS_COUNTRYDATA.extensions = {}
pb.SC_WORLD_SCENE_STATICS_COUNTRYDATA.containing_type = pb.SC_WORLD_SCENE_STATICS
pb.SC_WORLD_SCENE_STATICS_COUNTRYLIST_FIELD.name = "CountryList"
pb.SC_WORLD_SCENE_STATICS_COUNTRYLIST_FIELD.full_name = ".SC_World_Scene_Statics.CountryList"
pb.SC_WORLD_SCENE_STATICS_COUNTRYLIST_FIELD.number = 1
pb.SC_WORLD_SCENE_STATICS_COUNTRYLIST_FIELD.index = 0
pb.SC_WORLD_SCENE_STATICS_COUNTRYLIST_FIELD.label = 3
pb.SC_WORLD_SCENE_STATICS_COUNTRYLIST_FIELD.has_default_value = false
pb.SC_WORLD_SCENE_STATICS_COUNTRYLIST_FIELD.default_value = {}
pb.SC_WORLD_SCENE_STATICS_COUNTRYLIST_FIELD.message_type = pb.SC_WORLD_SCENE_STATICS_COUNTRYDATA
pb.SC_WORLD_SCENE_STATICS_COUNTRYLIST_FIELD.type = 11
pb.SC_WORLD_SCENE_STATICS_COUNTRYLIST_FIELD.cpp_type = 10

pb.SC_WORLD_SCENE_STATICS.name = "SC_World_Scene_Statics"
pb.SC_WORLD_SCENE_STATICS.full_name = ".SC_World_Scene_Statics"
pb.SC_WORLD_SCENE_STATICS.nested_types = {pb.SC_WORLD_SCENE_STATICS_COUNTRYDATA}
pb.SC_WORLD_SCENE_STATICS.enum_types = {}
pb.SC_WORLD_SCENE_STATICS.fields = {pb.SC_WORLD_SCENE_STATICS_COUNTRYLIST_FIELD}
pb.SC_WORLD_SCENE_STATICS.is_extendable = false
pb.SC_WORLD_SCENE_STATICS.extensions = {}

CS_World_Request_Syn_9Grid = protobuf.Message(pb.CS_WORLD_REQUEST_SYN_9GRID)
MSG_WORLD_CREATE_HERO = 6
MSG_WORLD_DESTORYSCENE = 2
MSG_WORLD_ECTYPEINFO = 5
MSG_WORLD_LOADSCENE = 1
MSG_WORLD_NONE = 0
MSG_WORLD_REQUEST_SYN_9GRID = 4
MSG_WORLD_SCENE_STATICS = 7
MSG_WORLD_STARTECTYPE = 3
SC_World_DestoryScene = protobuf.Message(pb.SC_WORLD_DESTORYSCENE)
SC_World_EctypeInfo = protobuf.Message(pb.SC_WORLD_ECTYPEINFO)
SC_World_LoadScene = protobuf.Message(pb.SC_WORLD_LOADSCENE)
SC_World_Scene_Statics = protobuf.Message(pb.SC_WORLD_SCENE_STATICS)
SC_World_Scene_Statics.CountryData = protobuf.Message(pb.SC_WORLD_SCENE_STATICS_COUNTRYDATA)
SC_World_StratEctype = protobuf.Message(pb.SC_WORLD_STRATECTYPE)

