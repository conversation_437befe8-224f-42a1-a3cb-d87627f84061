--[[
********************************************************************
    created:	2024/04/09
    author :	李锦剑
    purpose:    新排行榜模块
*********************************************************************
--]]

---Http请求回调参数
---@class HttpRequestCallBackParam
---@field requestID integer 请求ID
---@field callback ?fun(data:any, param ?:any) 回调函数
---@field callbackParam ?any 回调参数

--Http请求枚举
ERequestID = {
    --广告统计
    BtnClick                    = 1,
    --生存积分统计
    SaveStageScore              = 2,
    --获取宠物出战列表
    ListPets_OnStage            = 3,
    --获取副本令牌
    BeginStage                  = 4,
    --获取排行榜列表
    GetRank                     = 5,
    --获取自己排行榜
    GetStageScore               = 6,
    --获取排行榜刷新时间
    GetLastTime_Rank            = 7,
    --保存关卡分数
    SaveStageScore2             = 8,
    --获取已通关的管卡列表(每种管卡类型取最大等级那一行)
    ListPassedStage_StageType   = 10,
    --保存宠物出战列表
    SavePets_OnStage            = 11,
    --关卡统计
    EndStage                    = 12,
    --获取任务列表
    ListActorTask               = 13,
    --保存任务列表
    SaveActorTask               = 14,
    --获取角色已领取的关卡宝箱
    ListActorBattleTreasure     = 15,
    --保存已领取的关卡宝箱数据
    AddActorBattleTreasure      = 16,
    --获取角色装备，1体魄
    ListActorEquip              = 17,
    --保存角色装备，1体魄
    SaveActorEquip              = 18,
    --获取关卡列表(及其得分等)
    ListStageScores             = 19,
    --获取关卡的战斗统计(当天)
    ListActorBattleStat_Day     = 20,
    --获取角色最后的关卡进度状态
    GetActorBattleProgress_Last = 21,

    ---------------------版号独有-------------------[[

    --保存消费日志
    SaveLog_Charge              = -1,
    --获取用户实名认证信息
    GetUserIdNo                 = -2,
    --用户是否可以登录
    UserCanLogin                = -3,
    --实名认证
    IdNameVerify                = -4,

    ---------------------版号独有-------------------]]
}

--Http请求地址
local httpRequestUrl = {
    [ERequestID.BtnClick] = "http://************/WCollect62/Collect/BtnClick",
    [ERequestID.SaveStageScore] = "http://************/WCollect62/GT/SaveStageScore",
    [ERequestID.ListPets_OnStage] = "https://zl-wsr.0037wan.com/charge62/gs/ListPets_OnStage",
    [ERequestID.BeginStage] = "http://************/WCollect62/Collect/BeginStage",
    [ERequestID.GetRank] = "http://************/WCollect62/GT/GetRank",
    [ERequestID.GetStageScore] = "http://************/WCollect62/GT/GetStageScore",
    [ERequestID.GetLastTime_Rank] = "http://************/WCollect62/GT/GetLastTime_Rank",
    [ERequestID.SaveStageScore2] = "https://zl-wsr.0037wan.com/charge62/Gs/SaveStageScore",
    [ERequestID.ListPassedStage_StageType] = "https://zl-wsr.0037wan.com/charge62/Gs/ListPassedStage_StageType",
    [ERequestID.SavePets_OnStage] = "https://zl-wsr.0037wan.com/charge62/gs/SavePets_OnStage",
    [ERequestID.EndStage] = "http://************/WCollect62/Collect/EndStage",
    [ERequestID.ListActorTask] = "https://zl-wsr.0037wan.com/charge62/Gs/ListActorTask",
    [ERequestID.SaveActorTask] = "https://zl-wsr.0037wan.com/charge62/Gs/SaveActorTask",
    [ERequestID.ListActorBattleTreasure] = "https://zl-wsr.0037wan.com/charge62/Gs/ListActorBattleTreasure",
    [ERequestID.AddActorBattleTreasure] = "https://zl-wsr.0037wan.com/charge62/Gs/AddActorBattleTreasure",
    [ERequestID.ListActorEquip] = "https://zl-wsr.0037wan.com/charge62/Gs/ListActorEquip",
    [ERequestID.SaveActorEquip] = "https://zl-wsr.0037wan.com/charge62/Gs/SaveActorEquip",
    [ERequestID.ListStageScores] = "https://zl-wsr.0037wan.com/charge62/Gs/ListStageScores",
    [ERequestID.ListActorBattleStat_Day] = "https://zl-wsr.0037wan.com/charge62/Gs/ListActorBattleStat_Day",
    [ERequestID.GetActorBattleProgress_Last] = "https://zl-wsr.0037wan.com/charge62/Gs/GetActorBattleProgress_Last",

    ---------------------版号独有-------------------[[

    [ERequestID.SaveLog_Charge] = "https://zl-wsr.0037wan.com/charge62/Charge/SaveLog_Charge",
    [ERequestID.GetUserIdNo] = "https://zl-wsr.0037wan.com/charge62/Gs/GetUserIdNo",
    [ERequestID.UserCanLogin] = "https://zl-wsr.0037wan.com/charge62/Gs/UserCanLogin",
    [ERequestID.IdNameVerify] = "https://zl-wsr.0037wan.com/charge62/Gs/IdNameVerify",

    ---------------------版号独有-------------------]]
}

--Http请求描述(给报错打印用)
local httpRequestDescribe = {
    [ERequestID.BtnClick] = "广告统计",
    [ERequestID.SaveStageScore] = "保存关卡分数",
    [ERequestID.ListPets_OnStage] = "获取宠物出战列表",
    [ERequestID.BeginStage] = "获取副本令牌",
    [ERequestID.GetRank] = "获取排行榜列表",
    [ERequestID.GetStageScore] = "获取自己排行榜",
    [ERequestID.GetLastTime_Rank] = "获取排行榜刷新时间",
    [ERequestID.SaveStageScore2] = "保存关卡分数",
    [ERequestID.ListPassedStage_StageType] = "获取通过副本列表",
    [ERequestID.SavePets_OnStage] = "保存宠物出战列表",
    [ERequestID.EndStage] = "关卡统计",
    [ERequestID.ListActorTask] = "获取任务列表",
    [ERequestID.SaveActorTask] = "保存任务列表",
    [ERequestID.ListActorBattleTreasure] = "获取角色已领取的关卡宝箱",
    [ERequestID.AddActorBattleTreasure] = "保存已领取的关卡宝箱数据",
    [ERequestID.ListActorEquip] = "获取角色装备",
    [ERequestID.SaveActorEquip] = "保存角色装备",
    [ERequestID.ListStageScores] = "获取关卡列表(及其得分等)",
    [ERequestID.ListActorBattleStat_Day] = "获取关卡的战斗统计(当天)",

    ---------------------版号独有-------------------[[

    [ERequestID.SaveLog_Charge] = "保存消费日志",
    [ERequestID.GetUserIdNo] = "获取用户实名认证信息",
    [ERequestID.UserCanLogin] = "用户是否可以登录",
    [ERequestID.IdNameVerify] = "获取用户实名认证信息",

    ---------------------版号独有-------------------]]
}


--Http请求封装
HttpReques = {}

--------------------------------------------------------------------
--获取Http请求描述
--------------------------------------------------------------------
function HttpReques.GetDescribe(requestID)
    return httpRequestDescribe[tonumber(requestID) or 0]
end

--------------------------------------------------------------------
--获取Http请求地址
--------------------------------------------------------------------
function HttpReques.GetUrl(requestID)
    return httpRequestUrl[tonumber(requestID) or 0]
end

--------------------------------------------------------------------
---解析Http请求数据
---@param www any 数据
---@param describe ?string 请求描述(给报错打印用)
---@return any|nil 解析后的数据
--------------------------------------------------------------------
function HttpReques.ParseFromHttpRequestData(www, describe)
    local err = describe or ''
    if string.len(www.error) > 0 then
        err = string.format("%s--请求失败：message=", err, www.error)
    else
        print(string.format("%s--请求回调数据\ntext=%s", err, www.text), debug.traceback())
        local data = dkjsonHelper.decode(www.text)
        if data then
            if data.Success == false or data.isError == true then
                err = string.format("%s--请求回调数据错误：message=%s", err, data.Message)
            end
            return data
        else
            err = string.format("%s--请求回调数据数据解析失败!!!", err)
        end
    end
    error(err .. '\n' .. debug.traceback())
    return nil
end

--------------------------------------------------------------------
---Http请求回调
---@param www any 请求数据
---@param param HttpRequestCallBackParam Http回调参数
--------------------------------------------------------------------
function HttpReques.RequestCallBack(www, param)
    if param == nil then
        warn('Http回调参数 为空')
        return
    end
    --获取请求ID
    local requestID = tonumber(param.requestID) or 0
    --解析数据
    local data = HttpReques.ParseFromHttpRequestData(www,
        string.format('RequestID=%s %s', requestID, HttpReques.GetDescribe(requestID)))

    --更新数据
    GamePlayerData.HttpHandle(requestID, data)

    --执行回调
    if type(param.callback) == 'function' then
        param.callback(data, param.callbackParam)
    end
end

--------------------------------------------------------------------
---发送Http请求
---@param requestID integer 请求ID
---@param form ?table 请求参数
---@param callback ?fun(data:any, param:any) 回调函数
---@param callbackParam ?any 回调参数
---@param requestMode ?string 请求方式 "GET" 或 "POST" ，默认："POST"
--------------------------------------------------------------------
function HttpReques.SendRequest(requestID, form, callback, callbackParam, requestMode)
    local url = HttpReques.GetUrl(requestID)
    if url == nil then
        print('Http请求地址不存在 httpRequestID', requestID)
        return
    end

    requestMode = requestMode or "POST"

    --设置必填参数
    form = form or {}
    local actorData = LoginModule:GetSelectActorData()
    if actorData then
        form.ActorID = actorData.ActorID
    end

    local content = ''
    for k, v in pairs(form) do
        if content == '' then
            content = tostring(k) .. '=' .. tostring(v)
        else
            content = content .. '&' .. tostring(k) .. '=' .. tostring(v)
        end
    end
    local des = HttpReques.GetDescribe(requestID)
    local str = '请求--%s--ID= %s\n请求方式= %s\n请求链接= %s'
    print(string.format(str, des, requestID, requestMode, url .. '?' .. content))

    --添加请求回调参数
    ---@type HttpRequestCallBackParam
    local param = {
        requestID = requestID,
        callback = callback,
        callbackParam = callbackParam,
    }
    HttpRequestHelper.HttpSend(url, form, HttpReques.RequestCallBack, param, requestMode)
end
