-- Generated By protoc-gen-lua Do not Edit
local protobuf = require "protobuf/protobuf"
module('Misc_pb')
local pb = {}


pb.NAVIGATIONMESH = protobuf.Descriptor();
pb.NAVIGATIONMESH_VERTEX = protobuf.Descriptor();
pb.NAVIGATIONMESH_VERTEX_X_FIELD = protobuf.FieldDescriptor();
pb.NAVIGATIONMESH_VERTEX_Y_FIELD = protobuf.FieldDescriptor();
pb.NAVIGATIONMESH_VERTEX_Z_FIELD = protobuf.FieldDescriptor();
pb.NAVIGATIONMESH_TRIANGLE = protobuf.Descriptor();
pb.NAVIGATIONMESH_TRIANGLE_A_FIELD = protobuf.FieldDescriptor();
pb.NAVIGATIONMESH_TRIANGLE_B_FIELD = protobuf.FieldDescriptor();
pb.NAVIGATIONMESH_TRIANGLE_C_FIELD = protobuf.FieldDescriptor();
pb.NAVIGATIONMESH_TRIANGLE_NEIGHBOUR_FIELD = protobuf.FieldDescriptor();
pb.NAVIGATIONMESH_VERTICES_FIELD = protobuf.FieldDescriptor();
pb.NAVIGATIONMESH_TRIANGLES_FIELD = protobuf.FieldDescriptor();
pb.NAVIGATIONMESH_POSITION_FIELD = protobuf.FieldDescriptor();
pb.NAVIGATIONMESH_LENGTH_FIELD = protobuf.FieldDescriptor();
pb.NAVIGATIONMESH_VALID_FIELD = protobuf.FieldDescriptor();
pb.NAVIGATIONMESH_CENTER_FIELD = protobuf.FieldDescriptor();
pb.NAVIGATIONMESH_SIZE_FIELD = protobuf.FieldDescriptor();
pb.NAVIGATIONMESH_ROWS_FIELD = protobuf.FieldDescriptor();
pb.NAVIGATIONMESH_COLS_FIELD = protobuf.FieldDescriptor();
pb.CAMERAPLOT = protobuf.Descriptor();
pb.CAMERAPLOT_PLOTMONSTER = protobuf.Descriptor();
pb.CAMERAPLOT_PLOTMONSTER_MONSTERID_FIELD = protobuf.FieldDescriptor();
pb.CAMERAPLOT_PLOTMONSTER_X_FIELD = protobuf.FieldDescriptor();
pb.CAMERAPLOT_PLOTMONSTER_Y_FIELD = protobuf.FieldDescriptor();
pb.CAMERAPLOT_PLOTMONSTER_Z_FIELD = protobuf.FieldDescriptor();
pb.CAMERAPLOT_PLOTMONSTER_ANGLE_FIELD = protobuf.FieldDescriptor();
pb.CAMERAPLOT_PLOTMONSTER_SKILLID_FIELD = protobuf.FieldDescriptor();
pb.CAMERAPLOT_VERTEX = protobuf.Descriptor();
pb.CAMERAPLOT_VERTEX_FOV_FIELD = protobuf.FieldDescriptor();
pb.CAMERAPLOT_VERTEX_ALPHA_FIELD = protobuf.FieldDescriptor();
pb.CAMERAPLOT_VERTEX_BETA_FIELD = protobuf.FieldDescriptor();
pb.CAMERAPLOT_VERTEX_DISTANCE_FIELD = protobuf.FieldDescriptor();
pb.CAMERAPLOT_VERTEX_MONSTERS_FIELD = protobuf.FieldDescriptor();
pb.CAMERAPLOT_POINT_FIELD = protobuf.FieldDescriptor();
pb.CAMERAPLOT_SPEED_FIELD = protobuf.FieldDescriptor();

pb.NAVIGATIONMESH_VERTEX_X_FIELD.name = "x"
pb.NAVIGATIONMESH_VERTEX_X_FIELD.full_name = ".NavigationMesh.Vertex.x"
pb.NAVIGATIONMESH_VERTEX_X_FIELD.number = 1
pb.NAVIGATIONMESH_VERTEX_X_FIELD.index = 0
pb.NAVIGATIONMESH_VERTEX_X_FIELD.label = 1
pb.NAVIGATIONMESH_VERTEX_X_FIELD.has_default_value = false
pb.NAVIGATIONMESH_VERTEX_X_FIELD.default_value = 0.0
pb.NAVIGATIONMESH_VERTEX_X_FIELD.type = 2
pb.NAVIGATIONMESH_VERTEX_X_FIELD.cpp_type = 6

pb.NAVIGATIONMESH_VERTEX_Y_FIELD.name = "y"
pb.NAVIGATIONMESH_VERTEX_Y_FIELD.full_name = ".NavigationMesh.Vertex.y"
pb.NAVIGATIONMESH_VERTEX_Y_FIELD.number = 2
pb.NAVIGATIONMESH_VERTEX_Y_FIELD.index = 1
pb.NAVIGATIONMESH_VERTEX_Y_FIELD.label = 1
pb.NAVIGATIONMESH_VERTEX_Y_FIELD.has_default_value = false
pb.NAVIGATIONMESH_VERTEX_Y_FIELD.default_value = 0.0
pb.NAVIGATIONMESH_VERTEX_Y_FIELD.type = 2
pb.NAVIGATIONMESH_VERTEX_Y_FIELD.cpp_type = 6

pb.NAVIGATIONMESH_VERTEX_Z_FIELD.name = "z"
pb.NAVIGATIONMESH_VERTEX_Z_FIELD.full_name = ".NavigationMesh.Vertex.z"
pb.NAVIGATIONMESH_VERTEX_Z_FIELD.number = 3
pb.NAVIGATIONMESH_VERTEX_Z_FIELD.index = 2
pb.NAVIGATIONMESH_VERTEX_Z_FIELD.label = 1
pb.NAVIGATIONMESH_VERTEX_Z_FIELD.has_default_value = false
pb.NAVIGATIONMESH_VERTEX_Z_FIELD.default_value = 0.0
pb.NAVIGATIONMESH_VERTEX_Z_FIELD.type = 2
pb.NAVIGATIONMESH_VERTEX_Z_FIELD.cpp_type = 6

pb.NAVIGATIONMESH_VERTEX.name = "Vertex"
pb.NAVIGATIONMESH_VERTEX.full_name = ".NavigationMesh.Vertex"
pb.NAVIGATIONMESH_VERTEX.nested_types = {}
pb.NAVIGATIONMESH_VERTEX.enum_types = {}
pb.NAVIGATIONMESH_VERTEX.fields = {pb.NAVIGATIONMESH_VERTEX_X_FIELD, pb.NAVIGATIONMESH_VERTEX_Y_FIELD, pb.NAVIGATIONMESH_VERTEX_Z_FIELD}
pb.NAVIGATIONMESH_VERTEX.is_extendable = false
pb.NAVIGATIONMESH_VERTEX.extensions = {}
pb.NAVIGATIONMESH_VERTEX.containing_type = pb.NAVIGATIONMESH
pb.NAVIGATIONMESH_TRIANGLE_A_FIELD.name = "a"
pb.NAVIGATIONMESH_TRIANGLE_A_FIELD.full_name = ".NavigationMesh.Triangle.a"
pb.NAVIGATIONMESH_TRIANGLE_A_FIELD.number = 1
pb.NAVIGATIONMESH_TRIANGLE_A_FIELD.index = 0
pb.NAVIGATIONMESH_TRIANGLE_A_FIELD.label = 1
pb.NAVIGATIONMESH_TRIANGLE_A_FIELD.has_default_value = false
pb.NAVIGATIONMESH_TRIANGLE_A_FIELD.default_value = 0
pb.NAVIGATIONMESH_TRIANGLE_A_FIELD.type = 5
pb.NAVIGATIONMESH_TRIANGLE_A_FIELD.cpp_type = 1

pb.NAVIGATIONMESH_TRIANGLE_B_FIELD.name = "b"
pb.NAVIGATIONMESH_TRIANGLE_B_FIELD.full_name = ".NavigationMesh.Triangle.b"
pb.NAVIGATIONMESH_TRIANGLE_B_FIELD.number = 2
pb.NAVIGATIONMESH_TRIANGLE_B_FIELD.index = 1
pb.NAVIGATIONMESH_TRIANGLE_B_FIELD.label = 1
pb.NAVIGATIONMESH_TRIANGLE_B_FIELD.has_default_value = false
pb.NAVIGATIONMESH_TRIANGLE_B_FIELD.default_value = 0
pb.NAVIGATIONMESH_TRIANGLE_B_FIELD.type = 5
pb.NAVIGATIONMESH_TRIANGLE_B_FIELD.cpp_type = 1

pb.NAVIGATIONMESH_TRIANGLE_C_FIELD.name = "c"
pb.NAVIGATIONMESH_TRIANGLE_C_FIELD.full_name = ".NavigationMesh.Triangle.c"
pb.NAVIGATIONMESH_TRIANGLE_C_FIELD.number = 3
pb.NAVIGATIONMESH_TRIANGLE_C_FIELD.index = 2
pb.NAVIGATIONMESH_TRIANGLE_C_FIELD.label = 1
pb.NAVIGATIONMESH_TRIANGLE_C_FIELD.has_default_value = false
pb.NAVIGATIONMESH_TRIANGLE_C_FIELD.default_value = 0
pb.NAVIGATIONMESH_TRIANGLE_C_FIELD.type = 5
pb.NAVIGATIONMESH_TRIANGLE_C_FIELD.cpp_type = 1

pb.NAVIGATIONMESH_TRIANGLE_NEIGHBOUR_FIELD.name = "neighbour"
pb.NAVIGATIONMESH_TRIANGLE_NEIGHBOUR_FIELD.full_name = ".NavigationMesh.Triangle.neighbour"
pb.NAVIGATIONMESH_TRIANGLE_NEIGHBOUR_FIELD.number = 4
pb.NAVIGATIONMESH_TRIANGLE_NEIGHBOUR_FIELD.index = 3
pb.NAVIGATIONMESH_TRIANGLE_NEIGHBOUR_FIELD.label = 3
pb.NAVIGATIONMESH_TRIANGLE_NEIGHBOUR_FIELD.has_default_value = false
pb.NAVIGATIONMESH_TRIANGLE_NEIGHBOUR_FIELD.default_value = {}
pb.NAVIGATIONMESH_TRIANGLE_NEIGHBOUR_FIELD.type = 5
pb.NAVIGATIONMESH_TRIANGLE_NEIGHBOUR_FIELD.cpp_type = 1

pb.NAVIGATIONMESH_TRIANGLE.name = "Triangle"
pb.NAVIGATIONMESH_TRIANGLE.full_name = ".NavigationMesh.Triangle"
pb.NAVIGATIONMESH_TRIANGLE.nested_types = {}
pb.NAVIGATIONMESH_TRIANGLE.enum_types = {}
pb.NAVIGATIONMESH_TRIANGLE.fields = {pb.NAVIGATIONMESH_TRIANGLE_A_FIELD, pb.NAVIGATIONMESH_TRIANGLE_B_FIELD, pb.NAVIGATIONMESH_TRIANGLE_C_FIELD, pb.NAVIGATIONMESH_TRIANGLE_NEIGHBOUR_FIELD}
pb.NAVIGATIONMESH_TRIANGLE.is_extendable = false
pb.NAVIGATIONMESH_TRIANGLE.extensions = {}
pb.NAVIGATIONMESH_TRIANGLE.containing_type = pb.NAVIGATIONMESH
pb.NAVIGATIONMESH_VERTICES_FIELD.name = "vertices"
pb.NAVIGATIONMESH_VERTICES_FIELD.full_name = ".NavigationMesh.vertices"
pb.NAVIGATIONMESH_VERTICES_FIELD.number = 1
pb.NAVIGATIONMESH_VERTICES_FIELD.index = 0
pb.NAVIGATIONMESH_VERTICES_FIELD.label = 3
pb.NAVIGATIONMESH_VERTICES_FIELD.has_default_value = false
pb.NAVIGATIONMESH_VERTICES_FIELD.default_value = {}
pb.NAVIGATIONMESH_VERTICES_FIELD.message_type = pb.NAVIGATIONMESH_VERTEX
pb.NAVIGATIONMESH_VERTICES_FIELD.type = 11
pb.NAVIGATIONMESH_VERTICES_FIELD.cpp_type = 10

pb.NAVIGATIONMESH_TRIANGLES_FIELD.name = "triangles"
pb.NAVIGATIONMESH_TRIANGLES_FIELD.full_name = ".NavigationMesh.triangles"
pb.NAVIGATIONMESH_TRIANGLES_FIELD.number = 2
pb.NAVIGATIONMESH_TRIANGLES_FIELD.index = 1
pb.NAVIGATIONMESH_TRIANGLES_FIELD.label = 3
pb.NAVIGATIONMESH_TRIANGLES_FIELD.has_default_value = false
pb.NAVIGATIONMESH_TRIANGLES_FIELD.default_value = {}
pb.NAVIGATIONMESH_TRIANGLES_FIELD.message_type = pb.NAVIGATIONMESH_TRIANGLE
pb.NAVIGATIONMESH_TRIANGLES_FIELD.type = 11
pb.NAVIGATIONMESH_TRIANGLES_FIELD.cpp_type = 10

pb.NAVIGATIONMESH_POSITION_FIELD.name = "position"
pb.NAVIGATIONMESH_POSITION_FIELD.full_name = ".NavigationMesh.position"
pb.NAVIGATIONMESH_POSITION_FIELD.number = 3
pb.NAVIGATIONMESH_POSITION_FIELD.index = 2
pb.NAVIGATIONMESH_POSITION_FIELD.label = 1
pb.NAVIGATIONMESH_POSITION_FIELD.has_default_value = false
pb.NAVIGATIONMESH_POSITION_FIELD.default_value = nil
pb.NAVIGATIONMESH_POSITION_FIELD.message_type = pb.NAVIGATIONMESH_VERTEX
pb.NAVIGATIONMESH_POSITION_FIELD.type = 11
pb.NAVIGATIONMESH_POSITION_FIELD.cpp_type = 10

pb.NAVIGATIONMESH_LENGTH_FIELD.name = "length"
pb.NAVIGATIONMESH_LENGTH_FIELD.full_name = ".NavigationMesh.length"
pb.NAVIGATIONMESH_LENGTH_FIELD.number = 4
pb.NAVIGATIONMESH_LENGTH_FIELD.index = 3
pb.NAVIGATIONMESH_LENGTH_FIELD.label = 1
pb.NAVIGATIONMESH_LENGTH_FIELD.has_default_value = false
pb.NAVIGATIONMESH_LENGTH_FIELD.default_value = 0.0
pb.NAVIGATIONMESH_LENGTH_FIELD.type = 2
pb.NAVIGATIONMESH_LENGTH_FIELD.cpp_type = 6

pb.NAVIGATIONMESH_VALID_FIELD.name = "valid"
pb.NAVIGATIONMESH_VALID_FIELD.full_name = ".NavigationMesh.valid"
pb.NAVIGATIONMESH_VALID_FIELD.number = 5
pb.NAVIGATIONMESH_VALID_FIELD.index = 4
pb.NAVIGATIONMESH_VALID_FIELD.label = 3
pb.NAVIGATIONMESH_VALID_FIELD.has_default_value = false
pb.NAVIGATIONMESH_VALID_FIELD.default_value = {}
pb.NAVIGATIONMESH_VALID_FIELD.type = 8
pb.NAVIGATIONMESH_VALID_FIELD.cpp_type = 7

pb.NAVIGATIONMESH_CENTER_FIELD.name = "center"
pb.NAVIGATIONMESH_CENTER_FIELD.full_name = ".NavigationMesh.center"
pb.NAVIGATIONMESH_CENTER_FIELD.number = 6
pb.NAVIGATIONMESH_CENTER_FIELD.index = 5
pb.NAVIGATIONMESH_CENTER_FIELD.label = 1
pb.NAVIGATIONMESH_CENTER_FIELD.has_default_value = false
pb.NAVIGATIONMESH_CENTER_FIELD.default_value = nil
pb.NAVIGATIONMESH_CENTER_FIELD.message_type = pb.NAVIGATIONMESH_VERTEX
pb.NAVIGATIONMESH_CENTER_FIELD.type = 11
pb.NAVIGATIONMESH_CENTER_FIELD.cpp_type = 10

pb.NAVIGATIONMESH_SIZE_FIELD.name = "size"
pb.NAVIGATIONMESH_SIZE_FIELD.full_name = ".NavigationMesh.size"
pb.NAVIGATIONMESH_SIZE_FIELD.number = 7
pb.NAVIGATIONMESH_SIZE_FIELD.index = 6
pb.NAVIGATIONMESH_SIZE_FIELD.label = 1
pb.NAVIGATIONMESH_SIZE_FIELD.has_default_value = false
pb.NAVIGATIONMESH_SIZE_FIELD.default_value = nil
pb.NAVIGATIONMESH_SIZE_FIELD.message_type = pb.NAVIGATIONMESH_VERTEX
pb.NAVIGATIONMESH_SIZE_FIELD.type = 11
pb.NAVIGATIONMESH_SIZE_FIELD.cpp_type = 10

pb.NAVIGATIONMESH_ROWS_FIELD.name = "rows"
pb.NAVIGATIONMESH_ROWS_FIELD.full_name = ".NavigationMesh.rows"
pb.NAVIGATIONMESH_ROWS_FIELD.number = 8
pb.NAVIGATIONMESH_ROWS_FIELD.index = 7
pb.NAVIGATIONMESH_ROWS_FIELD.label = 1
pb.NAVIGATIONMESH_ROWS_FIELD.has_default_value = false
pb.NAVIGATIONMESH_ROWS_FIELD.default_value = 0
pb.NAVIGATIONMESH_ROWS_FIELD.type = 5
pb.NAVIGATIONMESH_ROWS_FIELD.cpp_type = 1

pb.NAVIGATIONMESH_COLS_FIELD.name = "cols"
pb.NAVIGATIONMESH_COLS_FIELD.full_name = ".NavigationMesh.cols"
pb.NAVIGATIONMESH_COLS_FIELD.number = 9
pb.NAVIGATIONMESH_COLS_FIELD.index = 8
pb.NAVIGATIONMESH_COLS_FIELD.label = 1
pb.NAVIGATIONMESH_COLS_FIELD.has_default_value = false
pb.NAVIGATIONMESH_COLS_FIELD.default_value = 0
pb.NAVIGATIONMESH_COLS_FIELD.type = 5
pb.NAVIGATIONMESH_COLS_FIELD.cpp_type = 1

pb.NAVIGATIONMESH.name = "NavigationMesh"
pb.NAVIGATIONMESH.full_name = ".NavigationMesh"
pb.NAVIGATIONMESH.nested_types = {pb.NAVIGATIONMESH_VERTEX, pb.NAVIGATIONMESH_TRIANGLE}
pb.NAVIGATIONMESH.enum_types = {}
pb.NAVIGATIONMESH.fields = {pb.NAVIGATIONMESH_VERTICES_FIELD, pb.NAVIGATIONMESH_TRIANGLES_FIELD, pb.NAVIGATIONMESH_POSITION_FIELD, pb.NAVIGATIONMESH_LENGTH_FIELD, pb.NAVIGATIONMESH_VALID_FIELD, pb.NAVIGATIONMESH_CENTER_FIELD, pb.NAVIGATIONMESH_SIZE_FIELD, pb.NAVIGATIONMESH_ROWS_FIELD, pb.NAVIGATIONMESH_COLS_FIELD}
pb.NAVIGATIONMESH.is_extendable = false
pb.NAVIGATIONMESH.extensions = {}
pb.CAMERAPLOT_PLOTMONSTER_MONSTERID_FIELD.name = "monsterId"
pb.CAMERAPLOT_PLOTMONSTER_MONSTERID_FIELD.full_name = ".CameraPlot.PlotMonster.monsterId"
pb.CAMERAPLOT_PLOTMONSTER_MONSTERID_FIELD.number = 1
pb.CAMERAPLOT_PLOTMONSTER_MONSTERID_FIELD.index = 0
pb.CAMERAPLOT_PLOTMONSTER_MONSTERID_FIELD.label = 1
pb.CAMERAPLOT_PLOTMONSTER_MONSTERID_FIELD.has_default_value = false
pb.CAMERAPLOT_PLOTMONSTER_MONSTERID_FIELD.default_value = 0
pb.CAMERAPLOT_PLOTMONSTER_MONSTERID_FIELD.type = 5
pb.CAMERAPLOT_PLOTMONSTER_MONSTERID_FIELD.cpp_type = 1

pb.CAMERAPLOT_PLOTMONSTER_X_FIELD.name = "x"
pb.CAMERAPLOT_PLOTMONSTER_X_FIELD.full_name = ".CameraPlot.PlotMonster.x"
pb.CAMERAPLOT_PLOTMONSTER_X_FIELD.number = 2
pb.CAMERAPLOT_PLOTMONSTER_X_FIELD.index = 1
pb.CAMERAPLOT_PLOTMONSTER_X_FIELD.label = 1
pb.CAMERAPLOT_PLOTMONSTER_X_FIELD.has_default_value = false
pb.CAMERAPLOT_PLOTMONSTER_X_FIELD.default_value = 0.0
pb.CAMERAPLOT_PLOTMONSTER_X_FIELD.type = 2
pb.CAMERAPLOT_PLOTMONSTER_X_FIELD.cpp_type = 6

pb.CAMERAPLOT_PLOTMONSTER_Y_FIELD.name = "y"
pb.CAMERAPLOT_PLOTMONSTER_Y_FIELD.full_name = ".CameraPlot.PlotMonster.y"
pb.CAMERAPLOT_PLOTMONSTER_Y_FIELD.number = 3
pb.CAMERAPLOT_PLOTMONSTER_Y_FIELD.index = 2
pb.CAMERAPLOT_PLOTMONSTER_Y_FIELD.label = 1
pb.CAMERAPLOT_PLOTMONSTER_Y_FIELD.has_default_value = false
pb.CAMERAPLOT_PLOTMONSTER_Y_FIELD.default_value = 0.0
pb.CAMERAPLOT_PLOTMONSTER_Y_FIELD.type = 2
pb.CAMERAPLOT_PLOTMONSTER_Y_FIELD.cpp_type = 6

pb.CAMERAPLOT_PLOTMONSTER_Z_FIELD.name = "z"
pb.CAMERAPLOT_PLOTMONSTER_Z_FIELD.full_name = ".CameraPlot.PlotMonster.z"
pb.CAMERAPLOT_PLOTMONSTER_Z_FIELD.number = 4
pb.CAMERAPLOT_PLOTMONSTER_Z_FIELD.index = 3
pb.CAMERAPLOT_PLOTMONSTER_Z_FIELD.label = 1
pb.CAMERAPLOT_PLOTMONSTER_Z_FIELD.has_default_value = false
pb.CAMERAPLOT_PLOTMONSTER_Z_FIELD.default_value = 0.0
pb.CAMERAPLOT_PLOTMONSTER_Z_FIELD.type = 2
pb.CAMERAPLOT_PLOTMONSTER_Z_FIELD.cpp_type = 6

pb.CAMERAPLOT_PLOTMONSTER_ANGLE_FIELD.name = "angle"
pb.CAMERAPLOT_PLOTMONSTER_ANGLE_FIELD.full_name = ".CameraPlot.PlotMonster.angle"
pb.CAMERAPLOT_PLOTMONSTER_ANGLE_FIELD.number = 5
pb.CAMERAPLOT_PLOTMONSTER_ANGLE_FIELD.index = 4
pb.CAMERAPLOT_PLOTMONSTER_ANGLE_FIELD.label = 1
pb.CAMERAPLOT_PLOTMONSTER_ANGLE_FIELD.has_default_value = false
pb.CAMERAPLOT_PLOTMONSTER_ANGLE_FIELD.default_value = 0.0
pb.CAMERAPLOT_PLOTMONSTER_ANGLE_FIELD.type = 2
pb.CAMERAPLOT_PLOTMONSTER_ANGLE_FIELD.cpp_type = 6

pb.CAMERAPLOT_PLOTMONSTER_SKILLID_FIELD.name = "skillId"
pb.CAMERAPLOT_PLOTMONSTER_SKILLID_FIELD.full_name = ".CameraPlot.PlotMonster.skillId"
pb.CAMERAPLOT_PLOTMONSTER_SKILLID_FIELD.number = 6
pb.CAMERAPLOT_PLOTMONSTER_SKILLID_FIELD.index = 5
pb.CAMERAPLOT_PLOTMONSTER_SKILLID_FIELD.label = 1
pb.CAMERAPLOT_PLOTMONSTER_SKILLID_FIELD.has_default_value = false
pb.CAMERAPLOT_PLOTMONSTER_SKILLID_FIELD.default_value = 0
pb.CAMERAPLOT_PLOTMONSTER_SKILLID_FIELD.type = 5
pb.CAMERAPLOT_PLOTMONSTER_SKILLID_FIELD.cpp_type = 1

pb.CAMERAPLOT_PLOTMONSTER.name = "PlotMonster"
pb.CAMERAPLOT_PLOTMONSTER.full_name = ".CameraPlot.PlotMonster"
pb.CAMERAPLOT_PLOTMONSTER.nested_types = {}
pb.CAMERAPLOT_PLOTMONSTER.enum_types = {}
pb.CAMERAPLOT_PLOTMONSTER.fields = {pb.CAMERAPLOT_PLOTMONSTER_MONSTERID_FIELD, pb.CAMERAPLOT_PLOTMONSTER_X_FIELD, pb.CAMERAPLOT_PLOTMONSTER_Y_FIELD, pb.CAMERAPLOT_PLOTMONSTER_Z_FIELD, pb.CAMERAPLOT_PLOTMONSTER_ANGLE_FIELD, pb.CAMERAPLOT_PLOTMONSTER_SKILLID_FIELD}
pb.CAMERAPLOT_PLOTMONSTER.is_extendable = false
pb.CAMERAPLOT_PLOTMONSTER.extensions = {}
pb.CAMERAPLOT_PLOTMONSTER.containing_type = pb.CAMERAPLOT
pb.CAMERAPLOT_VERTEX_FOV_FIELD.name = "fov"
pb.CAMERAPLOT_VERTEX_FOV_FIELD.full_name = ".CameraPlot.Vertex.fov"
pb.CAMERAPLOT_VERTEX_FOV_FIELD.number = 1
pb.CAMERAPLOT_VERTEX_FOV_FIELD.index = 0
pb.CAMERAPLOT_VERTEX_FOV_FIELD.label = 1
pb.CAMERAPLOT_VERTEX_FOV_FIELD.has_default_value = false
pb.CAMERAPLOT_VERTEX_FOV_FIELD.default_value = 0.0
pb.CAMERAPLOT_VERTEX_FOV_FIELD.type = 2
pb.CAMERAPLOT_VERTEX_FOV_FIELD.cpp_type = 6

pb.CAMERAPLOT_VERTEX_ALPHA_FIELD.name = "alpha"
pb.CAMERAPLOT_VERTEX_ALPHA_FIELD.full_name = ".CameraPlot.Vertex.alpha"
pb.CAMERAPLOT_VERTEX_ALPHA_FIELD.number = 2
pb.CAMERAPLOT_VERTEX_ALPHA_FIELD.index = 1
pb.CAMERAPLOT_VERTEX_ALPHA_FIELD.label = 1
pb.CAMERAPLOT_VERTEX_ALPHA_FIELD.has_default_value = false
pb.CAMERAPLOT_VERTEX_ALPHA_FIELD.default_value = 0.0
pb.CAMERAPLOT_VERTEX_ALPHA_FIELD.type = 2
pb.CAMERAPLOT_VERTEX_ALPHA_FIELD.cpp_type = 6

pb.CAMERAPLOT_VERTEX_BETA_FIELD.name = "beta"
pb.CAMERAPLOT_VERTEX_BETA_FIELD.full_name = ".CameraPlot.Vertex.beta"
pb.CAMERAPLOT_VERTEX_BETA_FIELD.number = 3
pb.CAMERAPLOT_VERTEX_BETA_FIELD.index = 2
pb.CAMERAPLOT_VERTEX_BETA_FIELD.label = 1
pb.CAMERAPLOT_VERTEX_BETA_FIELD.has_default_value = false
pb.CAMERAPLOT_VERTEX_BETA_FIELD.default_value = 0.0
pb.CAMERAPLOT_VERTEX_BETA_FIELD.type = 2
pb.CAMERAPLOT_VERTEX_BETA_FIELD.cpp_type = 6

pb.CAMERAPLOT_VERTEX_DISTANCE_FIELD.name = "distance"
pb.CAMERAPLOT_VERTEX_DISTANCE_FIELD.full_name = ".CameraPlot.Vertex.distance"
pb.CAMERAPLOT_VERTEX_DISTANCE_FIELD.number = 4
pb.CAMERAPLOT_VERTEX_DISTANCE_FIELD.index = 3
pb.CAMERAPLOT_VERTEX_DISTANCE_FIELD.label = 1
pb.CAMERAPLOT_VERTEX_DISTANCE_FIELD.has_default_value = false
pb.CAMERAPLOT_VERTEX_DISTANCE_FIELD.default_value = 0.0
pb.CAMERAPLOT_VERTEX_DISTANCE_FIELD.type = 2
pb.CAMERAPLOT_VERTEX_DISTANCE_FIELD.cpp_type = 6

pb.CAMERAPLOT_VERTEX_MONSTERS_FIELD.name = "monsters"
pb.CAMERAPLOT_VERTEX_MONSTERS_FIELD.full_name = ".CameraPlot.Vertex.monsters"
pb.CAMERAPLOT_VERTEX_MONSTERS_FIELD.number = 5
pb.CAMERAPLOT_VERTEX_MONSTERS_FIELD.index = 4
pb.CAMERAPLOT_VERTEX_MONSTERS_FIELD.label = 3
pb.CAMERAPLOT_VERTEX_MONSTERS_FIELD.has_default_value = false
pb.CAMERAPLOT_VERTEX_MONSTERS_FIELD.default_value = {}
pb.CAMERAPLOT_VERTEX_MONSTERS_FIELD.message_type = pb.CAMERAPLOT_PLOTMONSTER
pb.CAMERAPLOT_VERTEX_MONSTERS_FIELD.type = 11
pb.CAMERAPLOT_VERTEX_MONSTERS_FIELD.cpp_type = 10

pb.CAMERAPLOT_VERTEX.name = "Vertex"
pb.CAMERAPLOT_VERTEX.full_name = ".CameraPlot.Vertex"
pb.CAMERAPLOT_VERTEX.nested_types = {}
pb.CAMERAPLOT_VERTEX.enum_types = {}
pb.CAMERAPLOT_VERTEX.fields = {pb.CAMERAPLOT_VERTEX_FOV_FIELD, pb.CAMERAPLOT_VERTEX_ALPHA_FIELD, pb.CAMERAPLOT_VERTEX_BETA_FIELD, pb.CAMERAPLOT_VERTEX_DISTANCE_FIELD, pb.CAMERAPLOT_VERTEX_MONSTERS_FIELD}
pb.CAMERAPLOT_VERTEX.is_extendable = false
pb.CAMERAPLOT_VERTEX.extensions = {}
pb.CAMERAPLOT_VERTEX.containing_type = pb.CAMERAPLOT
pb.CAMERAPLOT_POINT_FIELD.name = "point"
pb.CAMERAPLOT_POINT_FIELD.full_name = ".CameraPlot.point"
pb.CAMERAPLOT_POINT_FIELD.number = 1
pb.CAMERAPLOT_POINT_FIELD.index = 0
pb.CAMERAPLOT_POINT_FIELD.label = 3
pb.CAMERAPLOT_POINT_FIELD.has_default_value = false
pb.CAMERAPLOT_POINT_FIELD.default_value = {}
pb.CAMERAPLOT_POINT_FIELD.message_type = pb.CAMERAPLOT_VERTEX
pb.CAMERAPLOT_POINT_FIELD.type = 11
pb.CAMERAPLOT_POINT_FIELD.cpp_type = 10

pb.CAMERAPLOT_SPEED_FIELD.name = "speed"
pb.CAMERAPLOT_SPEED_FIELD.full_name = ".CameraPlot.speed"
pb.CAMERAPLOT_SPEED_FIELD.number = 2
pb.CAMERAPLOT_SPEED_FIELD.index = 1
pb.CAMERAPLOT_SPEED_FIELD.label = 1
pb.CAMERAPLOT_SPEED_FIELD.has_default_value = false
pb.CAMERAPLOT_SPEED_FIELD.default_value = 0.0
pb.CAMERAPLOT_SPEED_FIELD.type = 2
pb.CAMERAPLOT_SPEED_FIELD.cpp_type = 6

pb.CAMERAPLOT.name = "CameraPlot"
pb.CAMERAPLOT.full_name = ".CameraPlot"
pb.CAMERAPLOT.nested_types = {pb.CAMERAPLOT_PLOTMONSTER, pb.CAMERAPLOT_VERTEX}
pb.CAMERAPLOT.enum_types = {}
pb.CAMERAPLOT.fields = {pb.CAMERAPLOT_POINT_FIELD, pb.CAMERAPLOT_SPEED_FIELD}
pb.CAMERAPLOT.is_extendable = false
pb.CAMERAPLOT.extensions = {}

CameraPlot = protobuf.Message(pb.CAMERAPLOT)
CameraPlot.PlotMonster = protobuf.Message(pb.CAMERAPLOT_PLOTMONSTER)
CameraPlot.Vertex = protobuf.Message(pb.CAMERAPLOT_VERTEX)
NavigationMesh = protobuf.Message(pb.NAVIGATIONMESH)
NavigationMesh.Triangle = protobuf.Message(pb.NAVIGATIONMESH_TRIANGLE)
NavigationMesh.Vertex = protobuf.Message(pb.NAVIGATIONMESH_VERTEX)

