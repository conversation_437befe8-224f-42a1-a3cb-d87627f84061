using System.Collections.Generic;

using Cysharp.Threading.Tasks;

using UnityEngine;

namespace View
{
	/// <summary>
	/// 对象模板池
	/// </summary>
	[DisallowMultipleComponent]
	public class ObjectTmpPool : MonoBehaviour
	{
		private Dictionary<string, GameObject> Dic { get; } = new();

		/// <summary>
		/// 从模板池中获取或新建一个预制体(隐藏)
		/// </summary>
		/// <param name="prefabPath">资源路径</param>
		public async UniTask<GameObject> GetOrCreatePrefabFromPool(string prefabPath)
		{
			if (Dic.TryGetValue(prefabPath, out var gameObj))
			{
				return gameObj;
			}

			gameObj = await ResMgrAsync.LoadResAsync<GameObject>(prefabPath);
			gameObj = Instantiate(gameObj, transform);
			gameObj.SetActive(false);
			Dic[prefabPath] = gameObj;
			return gameObj;
		}
	}
}
