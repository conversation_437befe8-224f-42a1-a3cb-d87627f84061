--[[
********************************************************************
    created:    2023/08/06
    author :    李锦剑
    purpose:    UI窗口控制器
*********************************************************************
--]]

---@class WindowData 窗口数据
---@field windowID integer 窗口ID
---@field openCloseBut ?boolean 是否开启关闭按钮，默认：false

---@class UIConfig UI配置
---@field ID integer 唯一窗口ID
---@field Desc string 描述
---@field MainType integer 主类型(1普通UI;2提示UI;3引导UI;4弹出UI;主界面UI)
---@field SubType integer 子类型
---@field LuaFile string lua文件名
---@field Prefab string 预制体路径
---@field SortOrder integer 指定排序层
---@field OpenLimitType1 integer 开启限制类型1
---@field OpenLimitParam1 integer 开启限制参数1
---@field OpenLimitType2 integer 开启限制类型2
---@field OpenLimitParam2 integer 开启限制参数2
---@field OpenAniType integer 打开时动画类型
---@field OpenAniParam1 number 打开时动画参数1
---@field OpenAniParam2 number 打开时动画参数2
---@field OpenAniParam3 number 打开时动画参数3
---@field CloseAniType integer 关闭时动画类型
---@field CloseAniParam1 number 关闭时动画参数1
---@field CloseAniParam2 number 关闭时动画参数2
---@field CloseAniParam3 number 关闭时动画参数3


---创建控制器
---@param dataList WindowData[] 数据集合
---@param parent any 父节点
---@param initIndex ?integer 初始索引
---@return Controller
local NewController = function(dataList, parent, initIndex)
    ---@class Controller
    local m = {}
    m.dataList = dataList or {}
    m.initIndex = initIndex or 1
    m.objList = {}
    ---@type ControllerButton[]
    m.buttonList = {}
    m.selectIndex = nil
    if not parent then
        local uiconfig = Schemes.UIConfig:Get(WndID.MainTitle)
        parent = UIManager:GetUIContainer(uiconfig.SortOrder)
    end
    m.gameObject = GameObject.Instantiate(HotResManager.ReadUI('ui/Main/WindowController'), parent.transform)

    m.transform = m.gameObject.transform
    Helper.ResetTransform(m.transform)
    Helper.FillLuaComps(m.transform, m.objList)
    m.objList.Obj_Item.gameObject:SetActive(false)

    --窗口数据 >=2 ，才显示按钮列表
    m.objList.ScrollView_Button.gameObject:SetActive(#m.dataList > 1)

    --选择UI界面
    ---@param index integer 选择界面索引
    ---@param ... unknown 界面打开时，参数
    m.SelectUI = function(index, ...)
        if m.selectIndex == index then return end
        if index > #m.buttonList then
            index = #m.buttonList
        end
        if index < 1 then
            index = 1
        end
        if m.selectIndex then
            m.buttonList[m.selectIndex].OnClose()
        end

        m.selectIndex = index
        m.buttonList[index].OnOpen()
    end

    ---显示/隐藏Controller
    ---@param bool boolean
    ---@param index? integer 选择界面索引
    ---@param ... unknown 界面打开时，参数
    m.SetActive = function(bool, index, ...)
        if bool then
            m.SelectUI(index or 1, ...)
        else
            m.buttonList[m.selectIndex].OnClose()
            m.selectIndex = nil
        end
        m.gameObject:SetActive(bool)
    end

    --更新按钮
    m.UpdateButton = function()
        for i, item in pairs(m.buttonList) do
            local isOpen = UIManager:JudgeOpenLevel(item.data.windowID, false)
            item.objList.Img_Lock.gameObject:SetActive(not isOpen)
            item.objList.Obj_Unlock.gameObject:SetActive(isOpen)
        end
    end

    --初始化
    ---@param isOpenCloseButton ?boolean 是否显示关闭按钮
    m.Init = function(isOpenCloseButton)
        m.objList.Btn_Close2.gameObject:SetActive(isOpenCloseButton == true)
        --创建按钮
        for i, v in ipairs(m.dataList) do
            m.buttonList[i] = {}
            ---@class ControllerButton
            local item = m.buttonList[i]
            item.data = v
            item.index = i
            item.redDot = nil
            item.gameObject = GameObject.Instantiate(m.objList.Item_Button.gameObject, m.objList.Grid_Button.transform)
            item.transform = item.gameObject.transform
            item.objList = {}
            Helper.FillLuaComps(item.transform, item.objList)
            item.objList.Txt_Name.text = GetActivityName(v.windowID)
            item.objList.Txt_Lock.text = GetActivityName(v.windowID)
            item.objList.Txt_Select.text = GetActivityName(v.windowID)
            item.objList.Img_Bg.gameObject:SetActive(true)
            item.objList.Img_Select.gameObject:SetActive(false)
            local isOpen = UIManager:JudgeOpenLevel(v.windowID, false)
            item.objList.Img_Lock.gameObject:SetActive(not isOpen)
            item.objList.Obj_Unlock.gameObject:SetActive(isOpen)

            item.but = item.gameObject:GetComponent('Button')
            item.but.onClick:AddListenerEx(function()
                if UIManager:JudgeOpenLevel(item.data.windowID) then
                    m.SelectUI(i)
                end
            end)
            --开启
            item.OnOpen = function()
                UIManager:OpenWnd2(item.data.windowID, m.objList.UiRoot, item.data.openCloseBut == true)
                item.objList.Img_Bg.gameObject:SetActive(false)
                item.objList.Img_Select.gameObject:SetActive(true)
            end
            --关闭
            item.OnClose = function()
                UIManager:CloseWndByID(item.data.windowID)
                item.objList.Img_Bg.gameObject:SetActive(true)
                item.objList.Img_Select.gameObject:SetActive(false)
            end

            item.redDot = RedDotManager:SetRedDot(item.transform, CachedVector2:Set(-30, -27))
            item.redDot:AddCheckParam(v.windowID)
        end

        m.SelectUI(m.initIndex)
    end

    m.objList.Btn_Close2.onClick:AddListenerEx(function()
        m.SetActive(false)
    end)
    m.objList.Btn_Close2.gameObject:SetActive(false)
    return m
end


WindowController = {
    New = NewController,
}
