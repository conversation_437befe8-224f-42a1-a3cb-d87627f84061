// ReSharper disable ClassWithVirtualMembersNeverInherited.Global

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;

using Apq;
using Apq.Unity3D.UnityHelpers;

using Cysharp.Threading.Tasks;

using DataStructure;

using HotScripts;

using Thing;

using UnityEngine;

using X.PB;

namespace View
{
    /// <summary>
    ///     玩家导弹(定点打击)
    /// </summary>
    public class ActorMissileTrackPos : ActorBulletBase
    {
        /// <summary>
        ///     轨迹生成器(包含发射器)
        /// </summary>
        public MissileLocusGenerator MissileLocusGenerator { get; set; }

        /// <summary>
        ///     导弹轨迹(关键点)
        /// </summary>
        public List<Vector3> Locus { get; set; }

        /// <summary>
        ///     是用上面还是下面的轨迹
        /// </summary>
        public bool LocusDown { get; set; }

        /// <inheritdoc />
        public override async UniTask OnAfterInitView()
        {
            await base.OnAfterInitView();

            float bulletLocusDuration = (float)GunThing.GetTotalDouble(PropType.BulletLocusDuration).FirstOrDefault();
            if (bulletLocusDuration > 0)
            {
                // 落点预示
                EffectMgr.Instance.ShowEffect(EffectPath.BornCircle,
                    MissileLocusGenerator.TargetPosition!.Value, 2, null, bulletLocusDuration).Forget();

                await UniTask.Delay(TimeSpan.FromSeconds(bulletLocusDuration));
            }
        }

        /// <inheritdoc />
        public override async UniTaskVoid TurnToPool()
        {
            try
            {
                await UniTask.WaitForEndOfFrame(SingletonMgr.Instance.GlobalMgr);
                
                // 重置子弹图片缩放
                if (ImgElement != null)
                {
                    ImgElement.transform.localScale = Vector3.one;
                }
                
                gameObject.SetActive(false);

                // 隐藏导弹发射器(坐标系对象)
                MissileLocusGenerator.MissileEjector.gameObject.SetActive(false);

                // 延时1分钟后置为null
                await UniTask.Delay(TimeSpan.FromMinutes(1));
                Thing = null;
            }
            catch (OperationCanceledException) { throw; }
            catch (MissingReferenceException) { }
            catch (Exception ex)
            {
                Debug.LogException(ex);
            }
        }

        #region 爆炸

        /// <summary>
        ///     在目标位置爆炸后归还到子弹池
        /// </summary>
        /// <param name="token"></param>
        public void DoExplose(CancellationToken token)
        {
            // 爆炸声音
            string exploseSound = BulletThing.CdExecutor.Thing.GetTotalString(PropType.ExploseSound).FirstOrDefault();
            AudioPlayer.Instance.PlaySound(exploseSound).Forget();
            // 爆炸特效
            string exploseEffect = BulletThing.CdExecutor.Thing.GetTotalString(PropType.ExploseEffect).FirstOrDefault();
            // 爆炸半径
            float exploseRadius =
                (float)BulletThing.CdExecutor.Thing.GetTotalDouble(PropType.ExploseRadius).FirstOrDefault();

            if (!string.IsNullOrWhiteSpace(exploseEffect))
            {
                EffectMgr.Instance.ShowEffect(EffectMgr.Instance.GetEffectPath(exploseEffect),
                    MissileLocusGenerator.TargetPosition!.Value,
                    exploseRadius).Forget();
            }

            // 玩家导弹对敌人造成伤害
            var monsters = SingletonMgr.Instance.BattleMgr.FindMonster(
                MissileLocusGenerator.TargetPosition!.Value, 0f, FindActionTarget.NearestEnemy, exploseRadius, 999);

            foreach (var distanceMonster in monsters)
            {
                if (distanceMonster.Thing2 is MonsterThing monster && monster.Hp.Value > 0)
                {
                    (double damage, bool isCritical) =
                        Helper.CalcDamage(BulletThing, 0, 1, monster);

                    // 受击方先接受枪携带的Buff
                    monster.ReceiveBuffByBulletHit(BulletThing.CdExecutor.Thing, BuffRecvType.Explose);

                    // 敌人接受伤害
                    monster.TakeHit(BulletThing.CdExecutor.Thing, damage, isCritical);
                }
            }

            // 界面还给子弹池
            TurnToPool().Forget();
        }

        #endregion

        #region 移动

        public override void StartMove()
        {
            base.StartMove();
        }

        /// <summary>
        ///     任务实现:按轨迹移动
        /// </summary>
        public override async UniTaskVoid DoTask_Move(CancellationToken token)
        {
            try
            {
                float duration = 0.03f;
                
                // 显示子弹图片
                ImgElement.SetActive(true);

                for (;; await UniTask.Delay(TimeSpan.FromSeconds(duration), cancellationToken: token))
                {
                    if (token.IsCancellationRequested)
                    {
                        return;
                    }

                    if (Time.deltaTime <= 0)
                    {
                        continue;
                    }

                    if (OnBeforeMoveOne())
                    {
                        return;
                    }

                    try
                    {
                        // 实时更新导弹发射器位置为玩家当前位置  
                        if (MissileLocusGenerator?.MissileEjector != null && BulletThing?.CdExecutor?.Thing is ActorThing actorThing)
                        {
                            MissileLocusGenerator.MissileEjector.transform.position = actorThing.Position;
                        }
                        
                        // 计算起点到终点的直线距离作为飞行距离参考
                        Vector3 startPos = MissileLocusGenerator.MissileEjector.transform.position;
                        Vector3 endPos = MissileLocusGenerator.TargetPosition!.Value;
                        float distance = Vector3.Distance(startPos, endPos);
                        
                        // 速度
                        float speed = (float)BulletThing.CdExecutor.Thing.GetTotalDouble(PropType.BulletSpeed).FirstOrDefault();
                        // 飞行全程所需时长
                        float totalDuration = distance / speed;
                        // 本次移动时长
                        float thisDuration = Mathf.Min(duration, totalDuration);

                        // 根据导弹轨迹移动
                        if (MoveOne_PositionMove(thisDuration, totalDuration, null))
                        {
                            return;
                        }
                    }
                    catch (OperationCanceledException) { throw; }
                    catch (MissingReferenceException) { }
                    catch (Exception ex)
                    {
                        Debug.LogException(ex);
                    }
                }
            }
            catch (OperationCanceledException) { throw; }
            catch (MissingReferenceException) { }
            catch (Exception ex)
            {
                Debug.LogException(ex);
            }
        }

        /// <summary>
        /// 移动前检查
        /// </summary>
        protected override bool OnBeforeMoveOne()
        {
            // 子弹销毁了或隐藏了，结束
            if (!this || !isActiveAndEnabled)
            {
                return true;
            }

            // 达到子弹最大存活时长,结束
            if (BulletThing.LifeBeginTime.Value +
                BulletThing.CdExecutor.Thing.GetTotalDouble(PropType.BulletLife).FirstOrDefault() <= Time.time)
            {
                return true;
            }

            return false;
        }

        /// <summary>
        /// 移动结束处理
        /// </summary>
        protected override void OnMoveEnd()
        {
            // 到达目标位置，爆炸
            DoExplose(this.GetCancellationTokenOnDestroy());
        }

        /// <summary>
        /// 按导弹轨迹移动一步
        /// </summary>
        public virtual bool MoveOne_PositionMove(float duration, float totalDuration, CubicBezierPath cubicBezier)
        {
            if (totalDuration <= 0)
            {
                return true;
            }

            // 移动计时
            BulletThing.MoveDuration += duration;

            // 时间进度
            float progress = Mathf.Clamp01(BulletThing.MoveDuration / totalDuration);

            #region 导弹前进一次(抛物线轨迹)

            // 起点和终点位置
            Vector3 startPos = MissileLocusGenerator.MissileEjector.transform.position;
            Vector3 endPos = MissileLocusGenerator.TargetPosition!.Value;
            
            // 计算抛物线的水平和垂直距离
            float horizontalDistance = Vector3.Distance(new Vector3(startPos.x, 0, startPos.z), new Vector3(endPos.x, 0, endPos.z));
            float verticalDistance = endPos.y - startPos.y;
            
            // 抛物线高度系数，让抛物线更明显
            float arcHeight = horizontalDistance * 0.5f; // 抛物线最高点为水平距离的一半
            
            // 根据时间进度计算当前位置
            Vector3 currentPos = Vector3.Lerp(startPos, endPos, progress);
            
            // 添加抛物线高度：使用二次函数 y = 4h * t * (1-t)，其中h为最大高度，t为进度
            float parabolaY = 4 * arcHeight * progress * (1 - progress);
            currentPos.y += parabolaY;
            
            // 计算导弹朝向（切线方向）
            Vector3 direction = endPos - startPos;
            // 抛物线的导数：dy/dt = 4h * (1-2t)
            float parabolaDerivative = 4 * arcHeight * (1 - 2 * progress);
            Vector3 tangent = direction.normalized;
            tangent.y = parabolaDerivative / horizontalDistance; // 调整y方向的切线
            
            // 设置导弹位置和朝向
            transform.position = currentPos;
            transform.right = tangent.normalized;
            
            // 抛物线视觉效果：缩放变化
            // 在抛物线最高点（progress=0.5）时最大，起点和终点时最小
            float scaleProgress = 1 - Mathf.Abs(progress - 0.5f) * 2; // 0到1的缩放进度
            float minScale = 0.8f; // 最小缩放
            float maxScale = 1.5f; // 最大缩放
            float currentScale = Mathf.Lerp(minScale, maxScale, scaleProgress);
            
            // 应用缩放到子弹图片
            if (ImgElement != null)
            {
                ImgElement.transform.localScale = Vector3.one * currentScale;
            }

            #endregion

            return progress >= 1;
        }

        #endregion
    }
} 