// ReSharper disable InconsistentNaming
// ReSharper disable IdentifierTypo

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;

using Apq.Extension;
using Apq.Unity3D.UnityHelpers;

using Cysharp.Threading.Tasks;

using DataStructure;

using Props;

using RxEventsM2V;

using Thing;

using ThingCdExecutors;

using UnityEngine;

using UniRx;

using View;

using X.PB;

namespace ThingCdExecutors
{
    /// <summary>
    ///     V43.3 追击导弹执行器 - 每个子弹追击不同目标的螺旋曲线
    ///     保持原有飞行曲线，只修复转圈和目标死亡处理
    /// </summary>
    public class ThingCdExecutor_7 : ActorGunCdExecutor
    {
        /// <inheritdoc />
        public override async UniTaskVoid DoShoot(CancellationToken token)
        {
            // V52.1 获取当前技能信息
            string skillName = $"{GunThing.CsvRow_Gun.Value?.Name ?? "未知追击导弹技能"}(ID:{GunThing.CsvRow_Gun.Value?.Id})";
            int currentPriority = GetCurrentAttackPriority();
            float globalCooldown = GetCurrentGlobalCooldown();

            Debug.Log($"V52.1 追击导弹技能尝试发射: {skillName}, 优先级={currentPriority}, 公共CD={globalCooldown}秒");

            // V52.1 使用新的技能发射协调机制
            if (Actor != null && !Actor.RequestSkillFire(skillName, currentPriority, GunThing, globalCooldown))
            {
                Debug.Log($"V52.1 追击导弹技能发射请求被拒绝: {skillName}");
                return;
            }

            // 找一个敌人作为攻击方向
            DistanceThing distanceEnemy = Thing.FindEnemy();

            // 没有攻击目标，不发子弹
            if (distanceEnemy is not { Thing2: not null })
            {
                Debug.Log($"V52.1 追击导弹 {skillName} - 未找到目标敌人，跳过发射");
                return;
            }

            Debug.Log($"V52.1 追击导弹技能通过所有检查，开始发射: {skillName}, 优先级={currentPriority}");

            // 按一轮攻击的持续时长预设结束时间
            base.DoShoot(token).Forget();

            await UniTask.SwitchToMainThread();

            // 确保此方法用于角色的枪
            if (Actor == null)
            {
                return;
            }

            CancellationTokenSource cts_Skill = CancellationTokenSource.CreateLinkedTokenSource(
                CTS_Shooter.Token
                , Actor.ThingBehaviour.GetCancellationTokenOnDestroy()
            );

            try
            {
                // 射击的基准方向
                Vector3 shootDir = (distanceEnemy.Thing2.Position - Actor.Position).normalized;

                int shootTimes = (int)Thing.GetTotalLong(PropType.BurstShootTimes).FirstOrDefault();
                List<double> burstDelayList = Thing.GetTotalDouble(PropType.BurstDelayList);
                List<long> burstBulletCountList = Thing.GetTotalLong(PropType.BurstBulletCountList);
                // 各轮射击角度的ID列表
                List<int> burstburstAnglesIds =
                    Thing.GetTotalLong(PropType.BurstAnglesIdList).Select(x => (int)x).ToList();

                // Debug.Log($"V43.3 追击导弹配置 - 枪ID:{GunThing.CsvRow_Gun.Value.Id} 连射次数:{shootTimes} 延时列表:[{string.Join(",", burstDelayList)}] 子弹数量:[{string.Join(",", burstBulletCountList)}] 角度ID:[{string.Join(",", burstburstAnglesIds)}]");

                // 根据配置延时后射击
                _ = burstDelayList.Take(shootTimes).Select((x, i) =>
                {
                    if (burstBulletCountList.Count <= i)
                    {
                        return i;
                    }

                    long configBulletQty = burstBulletCountList[i];
                    
                    // V52.0 新需求：子弹数量=min(可用目标数量A, BurstBulletCountList的值)
                    // 每次发射前先找当前目标怪物数量
                    var availableTargets = GetCurrentAvailableTargetCount();
                    long actualBulletQty = Math.Min(availableTargets, configBulletQty);
                    
                    Debug.Log($"=== V52.0 子弹数量调整 === 配置子弹数:{configBulletQty} 可用目标数:{availableTargets} 实际发射数:{actualBulletQty}");

                    BurstOne(cts_Skill.Token, (float)x, distanceEnemy.Thing2, null, shootDir, actualBulletQty,
                            burstburstAnglesIds.IndexOf_ByCycle(i))
                        .Forget();
                    return i;
                }).ToList();
            }
            catch
            {
                // ignore
            }
        }

        /// <summary>
        ///     发射一轮
        /// </summary>
        /// <param name="token"></param>
        /// <param name="delay">延时:秒</param>
        /// <param name="attackBaseDirFollowThing">攻击基准方向跟随哪个物件(没值则取移动方向)</param>
        /// <param name="trackPos">定点位置</param>
        /// <param name="shootBaseDir">射击的基准方向</param>
        /// <param name="bulletQty">发射的子弹数量</param>
        /// <param name="anglesPropId">往哪个角度发射</param>
        private async UniTaskVoid BurstOne(CancellationToken token, float delay, ThingBase attackBaseDirFollowThing,
            Vector3? trackPos, Vector3 shootBaseDir, float bulletQty, int anglesPropId)
        {
            try
            {
                await UniTask.Delay(TimeSpan.FromSeconds(delay), cancellationToken: token);

                if (token.IsCancellationRequested)
                {
                    return;
                }

                // 没有发射角度就不射击
                if (!SingletonMgr.Instance.GlobalMgr.CommonPropCfg.TryGetValue(anglesPropId, out CommonProp anglesProp))
                {
                    // Debug.LogWarning($"V43.3 追击导弹 - 角度配置ID {anglesPropId} 不存在");
                    return;
                }

                // 开火声音
                MessageBroker.Default.Publish(new PlayShootSound { Shooter = this });

                List<float> angles = anglesProp.DoubleValues
                    .Select(x => (float)x).ToList();

                // V43.3 差异4：获取可用目标列表，确保同一轮内不重复分配
                var availableTargets = GetAvailableTargetsForBurst((int)bulletQty);

                // Debug.Log($"=== V43.3 追击导弹一轮发射开始 === 子弹数:{bulletQty} 可用目标数:{availableTargets.Count} 角度配置:[{string.Join(",", angles)}] 目标分配成功率:{availableTargets.Count}/{bulletQty}({(float)availableTargets.Count/bulletQty*100:F1}%)");

                for (int z = 0; z < bulletQty; z++)
                {
                    float angle = angles.IndexOf_ByCycle(z);
                    int maxPenetrateTimes = (int)Thing.GetTotalLong(PropType.MaxPenetrateTimes).FirstOrDefault();
                    int maxBounceTimes = (int)Thing.GetTotalLong(PropType.MaxBounceTimes).FirstOrDefault();
                    int maxSeparateTimes = (int)Thing.GetTotalLong(PropType.MaxSeparateTimes).FirstOrDefault();

                    // 发射方向依次按配置的角度旋转
                    BulletThing bullet = Thing.CreateBullet(this, attackBaseDirFollowThing, trackPos, angle,
                        maxPenetrateTimes, maxBounceTimes, maxSeparateTimes);

                    Vector3 dir_1 = shootBaseDir.RotateAround(Vector3.forward, angle);
                    
                    // 根据角色朝向计算前方偏移（完全复制ThingCdExecutor_1的逻辑）
                    float forwardOffsetX = 2f;
                    if (Actor.ThingBehaviour != null)
                    {
                        var skeAni = Actor.ThingBehaviour.GetComponentInChildren<Spine.Unity.SkeletonAnimation>();
                        if (skeAni != null && skeAni.skeleton != null)
                        {
                            // ScaleX = -1 表示朝右，ScaleX = 1 表示朝左
                            forwardOffsetX = skeAni.skeleton.ScaleX == -1 ? 2f : -2f;
                        }
                    }
                    
                    bullet.Position = Actor.Position + new Vector3(forwardOffsetX, 6f, 0); // 根据角色朝向从前方+2、往上Y+6的位置发射
                    bullet.MoveDirection_Straight.Value = dir_1;

                    // V43.3 目标分配：有怪物目标优先，无目标则攻击正前方指定距离点
                    MonsterThing targetForThisBullet = null;
                    Vector3? frontTargetPosition = null;
                    
                    if (z < availableTargets.Count)
                    {
                        // 有怪物目标，进行追击
                        targetForThisBullet = availableTargets[z];
                        bullet.TrackEnemy = targetForThisBullet;
                    }
                    else
                    {
                        // 无怪物目标，攻击角色正前方gun表配置距离的点
                        float gunRange = Thing.TotalProp_GunRange;
                        Vector3 frontDirection = shootBaseDir; // 使用射击基准方向作为正前方
                        frontTargetPosition = Actor.Position + frontDirection * gunRange;
                        bullet.TrackPosition = frontTargetPosition;
                    }

                    // V43.3 完整一行日志：枪ID、子弹ID、目标信息、坐标、伤害计算
                    var gunRow = GunThing.CsvRow_Gun.Value;
                    var bulletRow = bullet.CsvRow_Bullet.Value;
                    var damage = bullet.GetTotalDouble(PropType.Attack).FirstOrDefault();
                    
                    string targetInfo = targetForThisBullet != null 
                        ? $"追击目标:{targetForThisBullet.CsvRow_BattleBrushEnemy.Id}({targetForThisBullet.CsvRow_BattleBrushEnemy.EnemyName}) 坐标:({targetForThisBullet.Position.x:F1},{targetForThisBullet.Position.y:F1})"
                        : $"攻击正前方坐标点:({frontTargetPosition.Value.x:F1},{frontTargetPosition.Value.y:F1}) 距离:{Thing.TotalProp_GunRange:F1}";
                    
                    // Debug.Log($"=== V43.3 追击导弹发射 === 枪ID:{gunRow.Id} 枪名:{gunRow.Name} → 子弹ID:{bulletRow.Id} 子弹名:{bulletRow.Name} BulletType:{bulletRow.BulletType} 发射位置:({bullet.Position.x:F1},{bullet.Position.y:F1}) → {targetInfo} 子弹伤害:{damage:F1}");

                    MessageBroker.Default.Publish(new BornBullet
                    {
                        Bullet = bullet, PositionPre = bullet.Position - dir_1
                    });
                }
            }
            catch (OperationCanceledException) { throw; }
            catch (MissingReferenceException) { }
            catch (Exception ex)
            {
                Debug.LogException(ex);
            }
        }

        /// <summary>
        /// V52.0 获取当前可用目标数量，用于调整发射子弹数量
        /// </summary>
        private int GetCurrentAvailableTargetCount()
        {
            // 获取角色位置和射程
            Vector3 actorPos = Actor.Position;
            float gunRange = Thing.TotalProp_GunRange;
            
            // 获取射程内存活的怪物数量
            var availableTargetCount = SingletonMgr.Instance.BattleMgr.Monsters
                .Where(m => m != null && m.Hp.Value > 0)
                .Count(m => Vector3.Distance(actorPos, m.Position) <= gunRange);
            
            // Debug.Log($"=== V52.0 目标计数 === 角色位置:({actorPos.x:F1},{actorPos.y:F1}) 射程:{gunRange:F1} 射程内存活怪物数:{availableTargetCount}");
            
            return availableTargetCount;
        }

        /// <summary>
        /// V43.3 差异4：获取同一轮内可用的目标列表，避免重复分配 - 增强日志诊断
        /// </summary>
        private List<MonsterThing> GetAvailableTargetsForBurst(int maxCount)
        {
            var allTargets = new List<MonsterThing>();
            
            // 获取角色位置和射程
            Vector3 actorPos = Actor.Position;
            float gunRange = Thing.TotalProp_GunRange;
            int totalMonstersInWorld = SingletonMgr.Instance.BattleMgr.Monsters.Count;
            
            // Debug.Log($"=== V43.3 目标搜索开始 === 角色位置:({actorPos.x:F1},{actorPos.y:F1}) 射程:{gunRange:F1} 需要目标数:{maxCount} 当前世界怪物总数:{totalMonstersInWorld}");
            
            // 检查所有怪物的状态和距离
            var aliveMonsters = SingletonMgr.Instance.BattleMgr.Monsters
                .Where(m => m != null && m.Hp.Value > 0)
                .ToList();
            
            // Debug.Log($"=== V43.3 怪物状态检查 === 存活怪物数:{aliveMonsters.Count}/{totalMonstersInWorld}");
            
            // 计算每个存活怪物的距离
            var monstersWithDistance = new List<(MonsterThing monster, float distance)>();
            foreach (var monster in aliveMonsters)
            {
                float distance = Vector3.Distance(actorPos, monster.Position);
                bool inRange = distance <= gunRange;
                monstersWithDistance.Add((monster, distance));
                
                // Debug.Log($"=== V43.3 怪物距离 === ID:{monster.CsvRow_BattleBrushEnemy.Id}({monster.CsvRow_BattleBrushEnemy.EnemyName}) 位置:({monster.Position.x:F1},{monster.Position.y:F1}) 距离:{distance:F1} 血量:{monster.Hp.Value:F0} 射程内:{inRange}");
            }
            
            // 按距离排序，选取射程内的目标
            var validTargets = monstersWithDistance
                .Where(t => t.distance <= gunRange)
                .OrderBy(t => t.distance)
                .Take(maxCount)
                .Select(t => t.monster)
                .ToList();
            
            // Debug.Log($"=== V43.3 目标分配结果 === 射程内怪物数:{validTargets.Count} 需要:{maxCount} 最终分配:{Math.Min(validTargets.Count, maxCount)}个目标");
            
            // 输出分配的目标详情
            for (int i = 0; i < validTargets.Count; i++)
            {
                var target = validTargets[i];
                float distance = Vector3.Distance(actorPos, target.Position);
                // Debug.Log($"=== V43.3 分配目标{i+1} === ID:{target.CsvRow_BattleBrushEnemy.Id}({target.CsvRow_BattleBrushEnemy.EnemyName}) 距离:{distance:F1} 血量:{target.Hp.Value:F0}");
            }
            
            return validTargets;
        }

        /// <summary>
        /// V52.1 获取当前技能的攻击动作优先级
        /// 通过解析子弹配置中的Icon字段获取
        /// </summary>
        /// <returns>攻击动作优先级，默认返回0</returns>
        private int GetCurrentAttackPriority()
        {
            try
            {
                // 获取子弹ID
                int bulletId = (int)GunThing.GetTotalLong(X.PB.PropType.BulletId).FirstOrDefault();
                if (bulletId <= 0) return 0;

                // 获取子弹配置
                if (!SingletonMgr.Instance.CsvLoaderMgr.GetOrAddLoader<CsvTables.BulletCsv>().Dic.TryGetValue(bulletId, out var bulletCfg))
                    return 0;

                // 解析Icon字段获取优先级
                string iconConfig = bulletCfg.Icon;
                if (string.IsNullOrWhiteSpace(iconConfig) || iconConfig == "0") return 0;

                string[] parameters = iconConfig.Split(';');
                if (parameters.Length < 4) return 0; // 需要第4个参数（攻击动作优先级）

                if (int.TryParse(parameters[3], out int priority))
                {
                    return priority;
                }
            }
            catch (System.Exception ex)
            {
                // Debug.LogWarning($"V52.1 获取追击导弹优先级失败: {ex.Message}");
            }

            return 0; // 默认优先级
        }

        /// <summary>
        /// V52.1 获取当前技能的公共CD时长
        /// 通过解析子弹配置中的Icon字段获取
        /// </summary>
        /// <returns>公共CD时长（秒），默认返回0</returns>
        private float GetCurrentGlobalCooldown()
        {
            try
            {
                // 获取子弹ID
                int bulletId = (int)GunThing.GetTotalLong(X.PB.PropType.BulletId).FirstOrDefault();
                if (bulletId <= 0) return 0;

                // 获取子弹配置
                if (!SingletonMgr.Instance.CsvLoaderMgr.GetOrAddLoader<CsvTables.BulletCsv>().Dic.TryGetValue(bulletId, out var bulletCfg))
                    return 0;

                // 解析Icon字段获取公共CD
                string iconConfig = bulletCfg.Icon;
                if (string.IsNullOrWhiteSpace(iconConfig) || iconConfig == "0") return 0;

                string[] parameters = iconConfig.Split(';');
                if (parameters.Length < 8) return 0; // 需要第8个参数（公共CD）

                if (float.TryParse(parameters[7], out float globalCooldown))
                {
                    return globalCooldown;
                }
            }
            catch (System.Exception ex)
            {
                // Debug.LogWarning($"V52.1 获取追击导弹公共CD失败: {ex.Message}");
            }

            return 0; // 默认无公共CD
        }
    }
}