-- Generated By protoc-gen-lua Do not Edit
local protobuf = require "protobuf/protobuf"
module('SocietyMessage_pb')
local pb = {}


pb.MSG_SOCIETY_ACTIONID = protobuf.EnumDescriptor();
pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_NONE_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_CREATE_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_DELETE_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_MEMBERLIST_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_JOIN_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_LEAVE_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_KICK_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_APPOINT_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_SUCCEED_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_GETLIST_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_FIND_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_INFO_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_APPLYLIST_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_JOINLIST_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_MSGCACHE_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_TASKLIST_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_DISTRIBUTEHISTORY_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_GETEVENTINFOLIST_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_CONTRIBUTIONHISTORY_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_GETNAME_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_ACTIVITY_MSG_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_TOWER_CLEARANCE_NUMBER_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_TOWER_CLEARANCE_TOPLEVEL_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_ADDCONTRIBUTIONHISTORY_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_ALCHEMY_SPEEDUP_HELPLIST_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_ALCHEMY_SPEEDUP_HELPEDLIST_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_INVITEJOIN_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_UPDATE_RED_PACKET_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_RED_PACKET_RANK_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_RED_PACKET_PASSWORD_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_RED_PACKET_LIMIT_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_RED_PACKET_SELF_RANK_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_RED_PACKET_OPEN_HISTORY_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_RED_PACKET_SUPPER_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_RED_PACKET_PRIZE_INFO_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_RED_PACKET_TRANSFER_PASSWORD_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_RED_PACKET_OPENTOTAL_PROCESS_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_RED_PACKET_OTHER_PRIZE_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_SOCIETY_ACTIONID_MSG_WARBAND_BEGIN_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_SOCIETY_ACTIONID_MSG_WARBAND_CREATE_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_SOCIETY_ACTIONID_MSG_WARBAND_DELETE_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_SOCIETY_ACTIONID_MSG_WARBAND_JOIN_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_SOCIETY_ACTIONID_MSG_WARBAND_LEAVE_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_SOCIETY_ACTIONID_MSG_WARBAND_GETLIST_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_SOCIETY_ACTIONID_MSG_WARBAND_UPDATEDATA_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_SOCIETY_ACTIONID_MSG_WARBAND_UPDATEMEMBER_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_SOCIETY_ACTIONID_MSG_WARBAND_SETWARBANDCONFIG_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_SOCIETY_ACTIONID_MSG_WARBAND_GETAPPLYLIST_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_SOCIETY_ACTIONID_MSG_WARBAND_DEALAPPLY_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_SOCIETY_ACTIONID_MSG_WARBAND_KICKMEMBER_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_SOCIETY_ACTIONID_MSG_WARBAND_CHANGELEADER_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_SOCIETY_ACTIONID_MSG_WARBAND_GETACTVRECORD_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_SOCIETY_ACTIONID_MSG_WARBAND_RECHARGEHISTORY_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_SOCIETY_ACTIONID_MSG_WARBAND_GETRECHARGEMESSAGE_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_SOCIETY_ACTIONID_MSG_WARBAND_INVITEJOIN_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_SOCIETY_ACTIONID_MSG_WARBAND_PRIZENOTICE_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_SOCIETY_ACTIONID_MSG_WARBAND_OPENRANDOMECTYPENOTICE_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_SOCIETY_ACTIONID_MSG_WARBAND_OPENRANDOMECTYPE_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_SOCIETY_ACTIONID_MSG_WARBAND_ENTERRANDOMECTYPE_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_SOCIETY_ACTIONID_MSG_WARBAND_RANDOMECTYPERECORD_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_SOCIETY_ACTIONID_MSG_WARBAND_INVITEFOLLOW_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_SOCIETY_ACTIONID_MSG_WARBAND_FIND_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_SOCIETY_ACTIONID_MSG_WARBAND_END_ENUM = protobuf.EnumValueDescriptor();
pb.CS_SOCIETY_CREATE = protobuf.Descriptor();
pb.CS_SOCIETY_CREATE_SOCIETYNAME_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_CREATE = protobuf.Descriptor();
pb.SC_SOCIETY_CREATE_SOCIETYID_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_CREATE_SOCIETYNAME_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_CREATE_CHIEFID_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_CREATE_SOCIETYLEVEL_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_CREATE_SOCIETYEXP_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_CREATE_VITALITY_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_CREATE_MEMBERNUM_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_CREATE_FEASTLEAVE_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_CREATE_FEASTADD_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_CREATE_LASTFEAST_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_CREATE_CHIEFLOGOUTTIME_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_CREATE_SOCIETYINFO_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_CREATE_LEVELLIMIT_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_CREATE_JOINLIMIT_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_CREATE_FEASTACTOR_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_CREATE_FEASTACTORNAME_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_CREATE_RESULT_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_CREATE_BUYREVIVE_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_CREATE_REVIVE_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_CREATE_PRACTICECOUNT_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_CREATE_PRACTICETIME_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_CREATE_COUNTRY_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_CREATE_PRACTICENOTICE_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_CREATE_FEASTNOTICE_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_CREATE_CONTRIBUTIONNOTICE_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_CREATE_PRIZELIMIT_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_CREATE_POWERLIMIT_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_CREATE_FOUND_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_CREATE_CONSTRUCTION_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_CREATE_BUILDTIMEEND_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_CREATE_CLEARANCELEVEL5_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_CREATE_CLEARANCELEVEL10_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_CREATE_CLEARANCELEVEL15_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_CREATE_CLEARANCELEVEL20_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_CREATE_CLEARANCELEVELRANK1ACTORNAME_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_CREATE_CLEARANCELEVELRANK1VOCATIONID_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_CREATE_CLEARANCELEVELRANK1ECTYPEID_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_CREATE_CLEARANCEECTYPEID_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_CREATE_CLEARANCETIME_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_CREATE_ALCHEMYTYPE_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_CREATE_FURNACELEVEL_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_CREATE_CHANGEALCHEMYTYPETIMES_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_CREATE_POWER_FIELD = protobuf.FieldDescriptor();
pb.CS_SOCIETY_DELETE = protobuf.Descriptor();
pb.CS_SOCIETY_DELETE_SOCIETYID_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_DELETE = protobuf.Descriptor();
pb.SC_SOCIETY_DELETE_SOCIETYID_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_DELETE_RESULT_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_DELETE_SOCIETYNAME_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_DELETE_INITIATIVE_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_DELETE_CHIEFID_FIELD = protobuf.FieldDescriptor();
pb.CS_SOCIETY_MEMBERLIST = protobuf.Descriptor();
pb.CS_SOCIETY_MEMBERLIST_SOCIETYID_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_MEMBERLIST = protobuf.Descriptor();
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER = protobuf.Descriptor();
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_ACTORID_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_ACTORNAME_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_LEVEL_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_VOCATION_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_POWER_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_VIPSCORE_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_CHARM_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_ISONLINE_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_TITLE_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_CONTRIBUTION_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_LOGOUTTIME_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_SOCIETYID_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_ALCHEMYTIME_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_ALCHEMYID_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_HEPLEDTIMES_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_DHEPLEDTIMES_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_CURACTVCOUNT_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_TOTALACTVCOUNT_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_BLUEVIPLEVEL_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_BLUEVIPTYPE_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_GENRE_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_MEMBERLIST_SOCIETYID_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_MEMBERLIST_MEMBERLIST_FIELD = protobuf.FieldDescriptor();
pb.CS_SOCIETY_JOIN = protobuf.Descriptor();
pb.CS_SOCIETY_JOIN_SOCIETYID_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_JOIN = protobuf.Descriptor();
pb.SC_SOCIETY_JOIN_SOCIETYID_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_JOIN_SOCIETYNAME_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_JOIN_CHIEFID_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_JOIN_SOCIETYLEVEL_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_JOIN_SOCIETYEXP_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_JOIN_VITALITY_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_JOIN_MEMBERNUM_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_JOIN_FEASTLEAVE_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_JOIN_FEASTADD_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_JOIN_LASTFEAST_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_JOIN_CHIEFLOGOUTTIME_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_JOIN_SOCIETYINFO_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_JOIN_LEVELLIMIT_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_JOIN_JOINLIMIT_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_JOIN_FEASTACTOR_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_JOIN_FEASTACTORNAME_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_JOIN_RESULT_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_JOIN_BUYREVIVE_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_JOIN_REVIVE_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_JOIN_PRACTICECOUNT_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_JOIN_PRACTICETIME_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_JOIN_COUNTRY_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_JOIN_PRACTICENOTICE_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_JOIN_FEASTNOTICE_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_JOIN_CONTRIBUTIONNOTICE_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_JOIN_PRIZELIMIT_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_JOIN_POWERLIMIT_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_JOIN_FOUND_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_JOIN_CONSTRUCTION_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_JOIN_BUILDTIMEEND_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_JOIN_CLEARANCELEVEL5_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_JOIN_CLEARANCELEVEL10_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_JOIN_CLEARANCELEVEL15_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_JOIN_CLEARANCELEVEL20_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_JOIN_CLEARANCELEVELRANK1ACTORNAME_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_JOIN_CLEARANCELEVELRANK1VOCATIONID_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_JOIN_CLEARANCELEVELRANK1ECTYPEID_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_JOIN_CLEARANCEECTYPEID_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_JOIN_CLEARANCETIME_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_JOIN_ALCHEMYTYPE_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_JOIN_FURNACELEVEL_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_JOIN_CHANGEALCHEMYTYPETIMES_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_JOIN_POWER_FIELD = protobuf.FieldDescriptor();
pb.CS_SOCIETY_LEAVE = protobuf.Descriptor();
pb.CS_SOCIETY_LEAVE_SOCIETYID_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_LEAVE = protobuf.Descriptor();
pb.SC_SOCIETY_LEAVE_SOCIETYID_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_LEAVE_RESULT_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_LEAVE_SOCIETYNAME_FIELD = protobuf.FieldDescriptor();
pb.CS_SOCIETY_KICK = protobuf.Descriptor();
pb.CS_SOCIETY_KICK_SOCIETYID_FIELD = protobuf.FieldDescriptor();
pb.CS_SOCIETY_KICK_KICKACTORID_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_KICK = protobuf.Descriptor();
pb.SC_SOCIETY_KICK_ACTORID_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_KICK_SOCIETYID_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_KICK_KICKACTORID_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_KICK_RESULT_FIELD = protobuf.FieldDescriptor();
pb.CS_SOCIETY_APPOINT = protobuf.Descriptor();
pb.CS_SOCIETY_APPOINT_SOCIETYID_FIELD = protobuf.FieldDescriptor();
pb.CS_SOCIETY_APPOINT_APPOINTACTORID_FIELD = protobuf.FieldDescriptor();
pb.CS_SOCIETY_APPOINT_TITLE_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_APPOINT = protobuf.Descriptor();
pb.SC_SOCIETY_APPOINT_ACTORID_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_APPOINT_SOCIETYID_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_APPOINT_APPOINTACTORID_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_APPOINT_TITLE_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_APPOINT_RESULT_FIELD = protobuf.FieldDescriptor();
pb.CS_SOCIETY_SUCCEED = protobuf.Descriptor();
pb.CS_SOCIETY_SUCCEED_SOCIETYID_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_SUCCEED = protobuf.Descriptor();
pb.SC_SOCIETY_SUCCEED_ACTORID_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_SUCCEED_SOCIETYID_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_SUCCEED_PRECHIEFID_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_SUCCEED_RESULT_FIELD = protobuf.FieldDescriptor();
pb.CS_SOCIETY_GETLIST = protobuf.Descriptor();
pb.CS_SOCIETY_GETLIST_BEGINSOCIETYID_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_GETLIST = protobuf.Descriptor();
pb.SC_SOCIETY_GETLIST_SOCIETYDATA = protobuf.Descriptor();
pb.SC_SOCIETY_GETLIST_SOCIETYDATA_SOCIETYID_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_GETLIST_SOCIETYDATA_SOCIETYNAME_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_GETLIST_SOCIETYDATA_MEMBERNUM_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_GETLIST_SOCIETYDATA_SOCIETYINFO_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_GETLIST_SOCIETYDATA_LEVELLIMIT_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_GETLIST_SOCIETYDATA_JOINLIMIT_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_GETLIST_SOCIETYDATA_COUNTRY_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_GETLIST_SOCIETYDATA_LEVEL_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_GETLIST_SOCIETYDATA_POWER_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_GETLIST_SOCIETYLIST_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_GETLIST_BEGINSOCIETYID_FIELD = protobuf.FieldDescriptor();
pb.CS_SOCIETY_FIND = protobuf.Descriptor();
pb.CS_SOCIETY_FIND_SOCIETYNAME_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_FIND = protobuf.Descriptor();
pb.SC_SOCIETY_FIND_SOCIETYID_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_FIND_SOCIETYNAME_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_FIND_MEMBERNUM_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_FIND_SOCIETYINFO_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_FIND_LEVELLIMIT_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_FIND_JOINLIMIT_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_FIND_RESULT_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_FIND_COUNTRY_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_FIND_LEVEL_FIELD = protobuf.FieldDescriptor();
pb.CS_SOCIETY_INFO = protobuf.Descriptor();
pb.CS_SOCIETY_INFO_SOCIETYID_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_INFO = protobuf.Descriptor();
pb.SC_SOCIETY_INFO_SOCIETYID_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_INFO_SOCIETYNAME_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_INFO_CHIEFID_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_INFO_SOCIETYLEVEL_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_INFO_SOCIETYEXP_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_INFO_VITALITY_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_INFO_MEMBERNUM_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_INFO_FEASTLEAVE_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_INFO_FEASTADD_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_INFO_LASTFEAST_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_INFO_CHIEFLOGOUTTIME_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_INFO_SOCIETYINFO_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_INFO_LEVELLIMIT_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_INFO_JOINLIMIT_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_INFO_FEASTACTOR_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_INFO_FEASTACTORNAME_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_INFO_RESULT_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_INFO_BUYREVIVE_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_INFO_REVIVE_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_INFO_PRACTICECOUNT_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_INFO_PRACTICETIME_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_INFO_COUNTRY_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_INFO_PRACTICENOTICE_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_INFO_FEASTNOTICE_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_INFO_CONTRIBUTIONNOTICE_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_INFO_PRIZELIMIT_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_INFO_POWERLIMIT_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_INFO_FOUND_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_INFO_CONSTRUCTION_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_INFO_BUILDTIMEEND_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_INFO_ALCHEMYTYPE_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_INFO_FURNACELEVEL_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_INFO_CHANGEALCHEMYTYPETIMES_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_INFO_CREATETIME_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_INFO_POWER_FIELD = protobuf.FieldDescriptor();
pb.CS_SOCIETY_APPLYLIST = protobuf.Descriptor();
pb.CS_SOCIETY_APPLYLIST_SOCIETYID_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_APPLYLIST = protobuf.Descriptor();
pb.SC_SOCIETY_APPLYLIST_APPLYDATA = protobuf.Descriptor();
pb.SC_SOCIETY_APPLYLIST_APPLYDATA_APPLICANT = protobuf.Descriptor();
pb.SC_SOCIETY_APPLYLIST_APPLYDATA_APPLICANT_ACTORID_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_APPLYLIST_APPLYDATA_APPLICANT_ACTORNAME_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_APPLYLIST_APPLYDATA_APPLICANT_POWER_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_APPLYLIST_APPLYDATA_APPLICANT_LEVEL_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_APPLYLIST_APPLYDATA_APPLICANT_CONTRIBUTION_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_APPLYLIST_APPLYDATA_PRIZEID_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_APPLYLIST_APPLYDATA_PRIZENUM_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_APPLYLIST_APPLYDATA_LASTTIME_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_APPLYLIST_APPLYDATA_APPLICANTLIST_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_APPLYLIST_APPLYLIST_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_JOINLISTBACK = protobuf.Descriptor();
pb.SC_SOCIETY_JOINLISTBACK_USERINFO = protobuf.Descriptor();
pb.SC_SOCIETY_JOINLISTBACK_USERINFO_ACTORID_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_JOINLISTBACK_USERINFO_ACTORNAME_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_JOINLISTBACK_USERINFO_POWER_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_JOINLISTBACK_USERINFO_LEVEL_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_JOINLISTBACK_USERINFO_VOCATION_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_JOINLISTBACK_USERINFO_BLUEVIPLEVEL_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_JOINLISTBACK_USERINFO_BLUEVIPTYPE_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_JOINLISTBACK_JOINLIST_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_JOINLISTBACK_COUNTNUM_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_JOINLISTBACK_ADDFLAG_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_MSGCACHE = protobuf.Descriptor();
pb.SC_SOCIETY_MSGCACHE_MSGINFO = protobuf.Descriptor();
pb.SC_SOCIETY_MSGCACHE_MSGINFO_ACTORID_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_MSGCACHE_MSGINFO_SENDTIME_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_MSGCACHE_MSGINFO_ACTORNAME_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_MSGCACHE_MSGINFO_MSG_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_MSGCACHE_MSGLIST_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_TASKLIST = protobuf.Descriptor();
pb.SC_SOCIETY_TASKLIST_TASKID1_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_TASKLIST_TASKID2_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_TASKLIST_TASKID3_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_TASKLIST_TASKID4_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_DISTRIBUTEHISTORY = protobuf.Descriptor();
pb.SC_SOCIETY_DISTRIBUTEHISTORY_HISTORYINFO = protobuf.Descriptor();
pb.SC_SOCIETY_DISTRIBUTEHISTORY_HISTORYINFO_ACTORID_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_DISTRIBUTEHISTORY_HISTORYINFO_TIME_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_DISTRIBUTEHISTORY_HISTORYINFO_ACTORNAME_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_DISTRIBUTEHISTORY_HISTORYINFO_ITEMID_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_DISTRIBUTEHISTORY_HISTORYINFO_ITEMNUM_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_DISTRIBUTEHISTORY_HISTORYINFO_ACTORLEVEL_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_DISTRIBUTEHISTORY_HISTORYINFO_TITLE_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_DISTRIBUTEHISTORY_HISTORYINFO_GIVEPRIZEACTORID_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_DISTRIBUTEHISTORY_HISTORYINFO_GIVEPRIZEACTORNAME_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_DISTRIBUTEHISTORY_HISTORYLIST_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_DISTRIBUTEHISTORY_COUNTNUM_FIELD = protobuf.FieldDescriptor();
pb.CS_SOCIETY_GETEVENTINFOLIST = protobuf.Descriptor();
pb.CS_SOCIETY_GETEVENTINFOLIST_SOCIETYID_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_GETEVENTINFOLIST = protobuf.Descriptor();
pb.SC_SOCIETY_GETEVENTINFOLIST_EVENTINFO = protobuf.Descriptor();
pb.SC_SOCIETY_GETEVENTINFOLIST_EVENTINFO_FACTORNAME_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_GETEVENTINFOLIST_EVENTINFO_SACTORNAME_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_GETEVENTINFOLIST_EVENTINFO_FAPPOINTID_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_GETEVENTINFOLIST_EVENTINFO_SAPPOINTID_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_GETEVENTINFOLIST_EVENTINFO_MONEY_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_GETEVENTINFOLIST_EVENTINFO_DIAMOND_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_GETEVENTINFOLIST_EVENTINFO_GOODSID_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_GETEVENTINFOLIST_EVENTINFO_TIME_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_GETEVENTINFOLIST_EVENTINFO_EVENTTYPE_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_GETEVENTINFOLIST_EVENTINFOLIST_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_GETEVENTINFOLIST_COUNTNUM_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_GETCONTRIBUTIONHISTORY = protobuf.Descriptor();
pb.SC_SOCIETY_GETCONTRIBUTIONHISTORY_HISTORY = protobuf.Descriptor();
pb.SC_SOCIETY_GETCONTRIBUTIONHISTORY_HISTORY_ACTORID_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_GETCONTRIBUTIONHISTORY_HISTORY_TIME_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_GETCONTRIBUTIONHISTORY_HISTORY_ACTORNAME_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_GETCONTRIBUTIONHISTORY_HISTORY_TYPE_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_GETCONTRIBUTIONHISTORY_HISTORY_NUM_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_GETCONTRIBUTIONHISTORY_HISTORYLIST_FIELD = protobuf.FieldDescriptor();
pb.CS_SOCIETY_GETNAME = protobuf.Descriptor();
pb.CS_SOCIETY_GETNAME_SOCIETYID_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_GETNAME = protobuf.Descriptor();
pb.SC_SOCIETY_GETNAME_SOCIETYID_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_GETNAME_SOCIETYNAME_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_ACTIVITYMSG = protobuf.Descriptor();
pb.SC_SOCIETY_ACTIVITYMSG_SOCIETYID_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_ACTIVITYMSG_ACTIVITYTYPE_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_ACTIVITYMSG_ENDTIME_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_TOWER_CLEARANCE_NUMBER = protobuf.Descriptor();
pb.SC_SOCIETY_TOWER_CLEARANCE_NUMBER_SOCIETYID_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_TOWER_CLEARANCE_NUMBER_LEVEL_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_TOWER_CLEARANCE_NUMBER_NUMBER_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_TOWER_CLEARANCE_TOPLEVEL = protobuf.Descriptor();
pb.SC_SOCIETY_TOWER_CLEARANCE_TOPLEVEL_ACTORNAME_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_TOWER_CLEARANCE_TOPLEVEL_VOCATION_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_TOWER_CLEARANCE_TOPLEVEL_ECTYPEID_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPLIST = protobuf.Descriptor();
pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPLIST_HELPDATA = protobuf.Descriptor();
pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPLIST_HELPDATA_ACTORID_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPLIST_HELPDATA_ACTORNAME_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPLIST_HELPDATA_USEDIAMOND_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPLIST_HELPDATA_REDUCETIME_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPLIST_HELPDATA_SPEEDUPTIME_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPLIST_HELPDATA_ISREADED_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPLIST_HELPLIST_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPEDLIST = protobuf.Descriptor();
pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPEDLIST_HELPEDDATA = protobuf.Descriptor();
pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPEDLIST_HELPEDDATA_ACTORID_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPEDLIST_HELPEDDATA_ACTORNAME_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPEDLIST_HELPEDDATA_USEDIAMOND_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPEDLIST_HELPEDDATA_REDUCETIME_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPEDLIST_HELPEDDATA_SPEEDUPTIME_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPEDLIST_HELPEDDATA_ISREADED_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPEDLIST_HELPEDLIST_FIELD = protobuf.FieldDescriptor();
pb.CS_SOCIETY_INVITEJOIN = protobuf.Descriptor();
pb.CS_SOCIETY_INVITEJOIN_ACTORID_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_INVITEJOIN = protobuf.Descriptor();
pb.SC_SOCIETY_INVITEJOIN_ACTORID_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_INVITEJOIN_ACTORNAME_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_INVITEJOIN_SOCIETYID_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_INVITEJOIN_SOCIETYNAME_FIELD = protobuf.FieldDescriptor();
pb.CS_SOCIETY_UPDATEREDPACKET = protobuf.Descriptor();
pb.CS_SOCIETY_UPDATEREDPACKET_ACTORID_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_UPDATEREDPACKET = protobuf.Descriptor();
pb.SC_SOCIETY_UPDATEREDPACKET_RESULT_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_UPDATEREDPACKET_TOTALOPENCOUNT_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_UPDATEREDPACKET_SOCIETYTOTALOPENCOUNT_FIELD = protobuf.FieldDescriptor();
pb.CS_SOCIETY_RED_PACKET_RANK = protobuf.Descriptor();
pb.CS_SOCIETY_RED_PACKET_RANK_ACTORID_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_RED_PACKET_RANK = protobuf.Descriptor();
pb.SC_SOCIETY_RED_PACKET_RANK_RANKINFO = protobuf.Descriptor();
pb.SC_SOCIETY_RED_PACKET_RANK_RANKINFO_SOCIETYID_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_RED_PACKET_RANK_RANKINFO_COUNTRYID_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_RED_PACKET_RANK_RANKINFO_SOCIETYNAME_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_RED_PACKET_RANK_RANKINFO_OPENCOUNT_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_RED_PACKET_RANK_RESULT_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_RED_PACKET_RANK_RANKLIST_FIELD = protobuf.FieldDescriptor();
pb.CS_SOCIETY_RED_PACKET_PASSWORD = protobuf.Descriptor();
pb.CS_SOCIETY_RED_PACKET_PASSWORD_ACTORID_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_RED_PACKET_PASSWORD = protobuf.Descriptor();
pb.SC_SOCIETY_RED_PACKET_PASSWORD_RESULT_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_RED_PACKET_PASSWORD_PASSWORD_FIELD = protobuf.FieldDescriptor();
pb.CS_SOCIETY_RED_PACKET_LIMIT = protobuf.Descriptor();
pb.CS_SOCIETY_RED_PACKET_LIMIT_ACTORID_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_RED_PACKET_LIMIT = protobuf.Descriptor();
pb.SC_SOCIETY_RED_PACKET_LIMIT_HISTROYINFO = protobuf.Descriptor();
pb.SC_SOCIETY_RED_PACKET_LIMIT_HISTROYINFO_PASSWORD_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_RED_PACKET_LIMIT_RESULT_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_RED_PACKET_LIMIT_HISTORY_FIELD = protobuf.FieldDescriptor();
pb.CS_SOCIETY_RED_PACKET_SELF_RANK = protobuf.Descriptor();
pb.CS_SOCIETY_RED_PACKET_SELF_RANK_ACTORID_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_RED_PACKET_SELF_RANK = protobuf.Descriptor();
pb.SC_SOCIETY_RED_PACKET_SELF_RANK_RANKINFO = protobuf.Descriptor();
pb.SC_SOCIETY_RED_PACKET_SELF_RANK_RANKINFO_ACTORID_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_RED_PACKET_SELF_RANK_RANKINFO_ACTORNAME_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_RED_PACKET_SELF_RANK_RANKINFO_OPENCOUNT_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_RED_PACKET_SELF_RANK_RANKINFO_SUPPEROPENCOUNT_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_RED_PACKET_SELF_RANK_RANKINFO_COUNTRYID_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_RED_PACKET_SELF_RANK_RESULT_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_RED_PACKET_SELF_RANK_RANKLIST_FIELD = protobuf.FieldDescriptor();
pb.CS_SOCIETY_RED_PACKET_OPEN_HISTORY = protobuf.Descriptor();
pb.CS_SOCIETY_RED_PACKET_OPEN_HISTORY_ACTORID_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_RED_PACKET_OPEN_HISTORY = protobuf.Descriptor();
pb.SC_SOCIETY_RED_PACKET_OPEN_HISTORY_HISTROYINFO = protobuf.Descriptor();
pb.SC_SOCIETY_RED_PACKET_OPEN_HISTORY_HISTROYINFO_PRIZES_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_RED_PACKET_OPEN_HISTORY_HISTROYINFO_REDTYPE_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_RED_PACKET_OPEN_HISTORY_RESULT_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_RED_PACKET_OPEN_HISTORY_HISTORYLIST_FIELD = protobuf.FieldDescriptor();
pb.CS_SOCIETY_RED_PACKET_SUPPER = protobuf.Descriptor();
pb.CS_SOCIETY_RED_PACKET_SUPPER_ACTORID_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_RED_PACKET_SUPPER = protobuf.Descriptor();
pb.SC_SOCIETY_RED_PACKET_SUPPER_RESULT_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_RED_PACKET_SUPPER_COUNTRYID_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_RED_PACKET_SUPPER_SOCIETYNAME_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_RED_PACKET_SUPPER_ACTORNAME_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_RED_PACKET_SUPPER_PRIZES_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_RED_PACKET_PRIZE = protobuf.Descriptor();
pb.SC_SOCIETY_RED_PACKET_PRIZE_PRIZEINFO = protobuf.Descriptor();
pb.SC_SOCIETY_RED_PACKET_PRIZE_PRIZEINFO_GOODSID_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_RED_PACKET_PRIZE_PRIZEINFO_COUNT_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_RED_PACKET_PRIZE_TOTALOPENCOUNT_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_RED_PACKET_PRIZE_TOTALSUPPEROPENCOUNT_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_RED_PACKET_PRIZE_INFO_FIELD = protobuf.FieldDescriptor();
pb.CS_SOCIETY_RED_PACKET_TRANSFER = protobuf.Descriptor();
pb.CS_SOCIETY_RED_PACKET_TRANSFER_ACTORID_FIELD = protobuf.FieldDescriptor();
pb.CS_SOCIETY_RED_PACKET_TRANSFER_PASSWORD_FIELD = protobuf.FieldDescriptor();
pb.CS_SOCIETY_RED_PACKET_TRANSFER_TRANSFERTYPE_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_RED_PACKET_TRANSFER = protobuf.Descriptor();
pb.SC_SOCIETY_RED_PACKET_TRANSFER_HISTROYINFO = protobuf.Descriptor();
pb.SC_SOCIETY_RED_PACKET_TRANSFER_HISTROYINFO_ACTORNAME_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_RED_PACKET_TRANSFER_HISTROYINFO_PASSWORD_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_RED_PACKET_TRANSFER_RESULT_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_RED_PACKET_TRANSFER_HISTORY_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_RED_PACKET_OPENTOTAL_PROCESS = protobuf.Descriptor();
pb.SC_SOCIETY_RED_PACKET_OPENTOTAL_PROCESS_OPENCOUNT_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_RED_PACKET_OPENTOTAL_PROCESS_NEEDCOUNT_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_RED_PACKET_OPENTOTAL_PROCESS_LIMITCOUNT_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_RED_PACKET_OPENTOTAL_PROCESS_PRIZESTRING_FIELD = protobuf.FieldDescriptor();
pb.SC_SOCIETY_RED_PACKET_OTHER_PRIZE = protobuf.Descriptor();
pb.SC_SOCIETY_RED_PACKET_OTHER_PRIZE_PRIZESTRING_FIELD = protobuf.FieldDescriptor();
pb.CS_WARBAND_CREATE = protobuf.Descriptor();
pb.CS_WARBAND_CREATE_WARBANDNAME_FIELD = protobuf.FieldDescriptor();
pb.SC_WARBAND_CREATE = protobuf.Descriptor();
pb.SC_WARBAND_CREATE_RESULT_FIELD = protobuf.FieldDescriptor();
pb.SC_WARBAND_CREATE_PARAM_FIELD = protobuf.FieldDescriptor();
pb.SC_WARBAND_DELETE = protobuf.Descriptor();
pb.SC_WARBAND_DELETE_RESULT_FIELD = protobuf.FieldDescriptor();
pb.CS_WARBAND_JOIN = protobuf.Descriptor();
pb.CS_WARBAND_JOIN_WARBANDID_FIELD = protobuf.FieldDescriptor();
pb.SC_WARBAND_JOIN = protobuf.Descriptor();
pb.SC_WARBAND_JOIN_RESULT_FIELD = protobuf.FieldDescriptor();
pb.SC_WARBAND_LEAVE = protobuf.Descriptor();
pb.SC_WARBAND_LEAVE_RESULT_FIELD = protobuf.FieldDescriptor();
pb.CS_WARBAND_GETLIST = protobuf.Descriptor();
pb.CS_WARBAND_GETLIST_COUNTRY_FIELD = protobuf.FieldDescriptor();
pb.CS_WARBAND_GETLIST_PAGEINDEX_FIELD = protobuf.FieldDescriptor();
pb.CS_WARBAND_GETLIST_PAGESIZE_FIELD = protobuf.FieldDescriptor();
pb.CS_WARBAND_GETLIST_SEARCHNAME_FIELD = protobuf.FieldDescriptor();
pb.SC_WARBAND_GETLIST = protobuf.Descriptor();
pb.SC_WARBAND_GETLIST_WARBANDDATA = protobuf.Descriptor();
pb.SC_WARBAND_GETLIST_WARBANDDATA_WARBANDID_FIELD = protobuf.FieldDescriptor();
pb.SC_WARBAND_GETLIST_WARBANDDATA_WARBANDNAME_FIELD = protobuf.FieldDescriptor();
pb.SC_WARBAND_GETLIST_WARBANDDATA_MEMBERNUM_FIELD = protobuf.FieldDescriptor();
pb.SC_WARBAND_GETLIST_WARBANDDATA_LEVELLIMIT_FIELD = protobuf.FieldDescriptor();
pb.SC_WARBAND_GETLIST_WARBANDDATA_JOINLIMIT_FIELD = protobuf.FieldDescriptor();
pb.SC_WARBAND_GETLIST_WARBANDDATA_TOTALPOWER_FIELD = protobuf.FieldDescriptor();
pb.SC_WARBAND_GETLIST_WARBANDLIST_FIELD = protobuf.FieldDescriptor();
pb.SC_WARBAND_GETLIST_COUNTRY_FIELD = protobuf.FieldDescriptor();
pb.SC_WARBAND_GETLIST_PAGEINDEX_FIELD = protobuf.FieldDescriptor();
pb.SC_WARBAND_GETLIST_PAGESIZE_FIELD = protobuf.FieldDescriptor();
pb.SC_WARBAND_GETLIST_WARBANDCOUNT_FIELD = protobuf.FieldDescriptor();
pb.SC_WARBAND_UPDATEDATA = protobuf.Descriptor();
pb.SC_WARBAND_UPDATEDATA_WARBANDID_FIELD = protobuf.FieldDescriptor();
pb.SC_WARBAND_UPDATEDATA_WARBANDNAME_FIELD = protobuf.FieldDescriptor();
pb.SC_WARBAND_UPDATEDATA_COUNTRY_FIELD = protobuf.FieldDescriptor();
pb.SC_WARBAND_UPDATEDATA_LEADERID_FIELD = protobuf.FieldDescriptor();
pb.SC_WARBAND_UPDATEDATA_LEVELLIMIT_FIELD = protobuf.FieldDescriptor();
pb.SC_WARBAND_UPDATEDATA_JOINLIMIT_FIELD = protobuf.FieldDescriptor();
pb.SC_WARBAND_UPDATEDATA_POWERLIMIT_FIELD = protobuf.FieldDescriptor();
pb.SC_WARBAND_UPDATEDATA_RANDOMECTYPECOUNT_FIELD = protobuf.FieldDescriptor();
pb.SC_WARBAND_UPDATEDATA_RANDOMECTYPECONFIGID_FIELD = protobuf.FieldDescriptor();
pb.SC_WARBAND_UPDATEDATA_RANDOMECTYPEOPENTIME_FIELD = protobuf.FieldDescriptor();
pb.SC_WARBAND_UPDATEDATA_RANDOMECTYPECOOLDOWNTIME_FIELD = protobuf.FieldDescriptor();
pb.SC_WARBAND_MEMBERLIST = protobuf.Descriptor();
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA = protobuf.Descriptor();
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_ACTORID_FIELD = protobuf.FieldDescriptor();
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_ACTORNAME_FIELD = protobuf.FieldDescriptor();
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_LEVEL_FIELD = protobuf.FieldDescriptor();
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_VOCATION_FIELD = protobuf.FieldDescriptor();
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_POWER_FIELD = protobuf.FieldDescriptor();
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_VIPLEVEL_FIELD = protobuf.FieldDescriptor();
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_ISONLINE_FIELD = protobuf.FieldDescriptor();
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_CURACTVCOUNT_FIELD = protobuf.FieldDescriptor();
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_TOTALACTVCOUNT_FIELD = protobuf.FieldDescriptor();
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_LOGOUTTIME_FIELD = protobuf.FieldDescriptor();
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_EQUIPMODEID_FIELD = protobuf.FieldDescriptor();
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_WEAPONCREATUREID_FIELD = protobuf.FieldDescriptor();
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_WINGCREATUREID_FIELD = protobuf.FieldDescriptor();
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_NEWWINGCREATUREID_FIELD = protobuf.FieldDescriptor();
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_OPENECTYPECOUNT_FIELD = protobuf.FieldDescriptor();
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_JOINTIME_FIELD = protobuf.FieldDescriptor();
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_BLUEVIPLEVEL_FIELD = protobuf.FieldDescriptor();
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_BLUEVIPTYPE_FIELD = protobuf.FieldDescriptor();
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_GENRE_FIELD = protobuf.FieldDescriptor();
pb.SC_WARBAND_MEMBERLIST_WARBANDID_FIELD = protobuf.FieldDescriptor();
pb.SC_WARBAND_MEMBERLIST_ISINIT_FIELD = protobuf.FieldDescriptor();
pb.SC_WARBAND_MEMBERLIST_MEMBERLIST_FIELD = protobuf.FieldDescriptor();
pb.CS_WARBAND_SETWARBANDCONFIG = protobuf.Descriptor();
pb.CS_WARBAND_SETWARBANDCONFIG_JOINLIMIT_FIELD = protobuf.FieldDescriptor();
pb.CS_WARBAND_SETWARBANDCONFIG_LEVELLIMIT_FIELD = protobuf.FieldDescriptor();
pb.CS_WARBAND_SETWARBANDCONFIG_POWERLIMIT_FIELD = protobuf.FieldDescriptor();
pb.SC_WARBAND_SETWARBANDCONFIG = protobuf.Descriptor();
pb.SC_WARBAND_SETWARBANDCONFIG_RESULT_FIELD = protobuf.FieldDescriptor();
pb.SC_WARBAND_GETAPPLYLIST = protobuf.Descriptor();
pb.SC_WARBAND_GETAPPLYLIST_APPLYACTOR = protobuf.Descriptor();
pb.SC_WARBAND_GETAPPLYLIST_APPLYACTOR_ACTORID_FIELD = protobuf.FieldDescriptor();
pb.SC_WARBAND_GETAPPLYLIST_APPLYACTOR_ACTORNAME_FIELD = protobuf.FieldDescriptor();
pb.SC_WARBAND_GETAPPLYLIST_APPLYACTOR_LEVEL_FIELD = protobuf.FieldDescriptor();
pb.SC_WARBAND_GETAPPLYLIST_APPLYACTOR_VOCATION_FIELD = protobuf.FieldDescriptor();
pb.SC_WARBAND_GETAPPLYLIST_APPLYACTOR_POWER_FIELD = protobuf.FieldDescriptor();
pb.SC_WARBAND_GETAPPLYLIST_APPLYACTOR_VIPLEVEL_FIELD = protobuf.FieldDescriptor();
pb.SC_WARBAND_GETAPPLYLIST_APPLYACTOR_BLUEVIPLEVEL_FIELD = protobuf.FieldDescriptor();
pb.SC_WARBAND_GETAPPLYLIST_APPLYACTOR_BLUEVIPTYPE_FIELD = protobuf.FieldDescriptor();
pb.SC_WARBAND_GETAPPLYLIST_APPLYLIST_FIELD = protobuf.FieldDescriptor();
pb.CS_WARBAND_DEALAPPLY = protobuf.Descriptor();
pb.CS_WARBAND_DEALAPPLY_TARGETID_FIELD = protobuf.FieldDescriptor();
pb.CS_WARBAND_DEALAPPLY_REPLY_FIELD = protobuf.FieldDescriptor();
pb.SC_WARBAND_DEALAPPLY = protobuf.Descriptor();
pb.SC_WARBAND_DEALAPPLY_RESULT_FIELD = protobuf.FieldDescriptor();
pb.SC_WARBAND_DEALAPPLY_WARBANDNAME_FIELD = protobuf.FieldDescriptor();
pb.CS_WARBAND_KICKMEMBER = protobuf.Descriptor();
pb.CS_WARBAND_KICKMEMBER_TARGETID_FIELD = protobuf.FieldDescriptor();
pb.SC_WARBAND_KICKMEMBER = protobuf.Descriptor();
pb.SC_WARBAND_KICKMEMBER_RESULT_FIELD = protobuf.FieldDescriptor();
pb.CS_WARBAND_CHANGELEADER = protobuf.Descriptor();
pb.CS_WARBAND_CHANGELEADER_TARGETID_FIELD = protobuf.FieldDescriptor();
pb.SC_WARBAND_CHANGELEADER = protobuf.Descriptor();
pb.SC_WARBAND_CHANGELEADER_RESULT_FIELD = protobuf.FieldDescriptor();
pb.SC_WARBAND_GETACTVRECORD = protobuf.Descriptor();
pb.SC_WARBAND_GETACTVRECORD_TODAYACTVCOUNT_FIELD = protobuf.FieldDescriptor();
pb.SC_WARBAND_GETACTVRECORD_TODAYLOGINCOUNT_FIELD = protobuf.FieldDescriptor();
pb.SC_WARBAND_GETACTVRECORD_ACTVCOUNTRECORD1_FIELD = protobuf.FieldDescriptor();
pb.SC_WARBAND_GETACTVRECORD_LOGINCOUNTRECORD1_FIELD = protobuf.FieldDescriptor();
pb.SC_WARBAND_GETACTVRECORD_ACTVCOUNTRECORD2_FIELD = protobuf.FieldDescriptor();
pb.SC_WARBAND_GETACTVRECORD_LOGINCOUNTRECORD2_FIELD = protobuf.FieldDescriptor();
pb.CS_WARBAND_GETRECHARGEHISTORY = protobuf.Descriptor();
pb.CS_WARBAND_GETRECHARGEHISTORY_RECHARGETYPE_FIELD = protobuf.FieldDescriptor();
pb.SC_WARBAND_GETRECHARGEHISTORY = protobuf.Descriptor();
pb.SC_WARBAND_GETRECHARGEHISTORY_HISTORY = protobuf.Descriptor();
pb.SC_WARBAND_GETRECHARGEHISTORY_HISTORY_ACTORNAME_FIELD = protobuf.FieldDescriptor();
pb.SC_WARBAND_GETRECHARGEHISTORY_HISTORY_TIME_FIELD = protobuf.FieldDescriptor();
pb.SC_WARBAND_GETRECHARGEHISTORY_HISTORY_TOTALRECHARGECOUNT_FIELD = protobuf.FieldDescriptor();
pb.SC_WARBAND_GETRECHARGEHISTORY_HISTORY_ITEMID_FIELD = protobuf.FieldDescriptor();
pb.SC_WARBAND_GETRECHARGEHISTORY_HISTORY_RECHARGECOUNT_FIELD = protobuf.FieldDescriptor();
pb.SC_WARBAND_GETRECHARGEHISTORY_HISTORYLIST_FIELD = protobuf.FieldDescriptor();
pb.SC_WARBAND_GETRECHARGEMESSAGE = protobuf.Descriptor();
pb.SC_WARBAND_GETRECHARGEMESSAGE_RESULT_FIELD = protobuf.FieldDescriptor();
pb.SC_WARBAND_GETRECHARGEMESSAGE_WARBANDGIFTPRIZECOUNT_FIELD = protobuf.FieldDescriptor();
pb.SC_WARBAND_GETRECHARGEMESSAGE_RECHARGECOUNT_FIELD = protobuf.FieldDescriptor();
pb.SC_WARBAND_GETRECHARGEMESSAGE_TOTALRECHARGECOUNT_FIELD = protobuf.FieldDescriptor();
pb.SC_WARBAND_GETRECHARGEMESSAGE_TOTALRECHARGEPRIZECOUNT_FIELD = protobuf.FieldDescriptor();
pb.SC_WARBAND_GETRECHARGEMESSAGE_TOTALRECHARGEDRAWCOUNT_FIELD = protobuf.FieldDescriptor();
pb.SC_WARBAND_GETRECHARGEMESSAGE_ISFIRSTDAYJOIN_FIELD = protobuf.FieldDescriptor();
pb.SC_WARBAND_GETRECHARGEMESSAGE_TOTALRECHARGEPRIZECOUNTNEXT_FIELD = protobuf.FieldDescriptor();
pb.CS_WARBAND_INVITEJOIN = protobuf.Descriptor();
pb.CS_WARBAND_INVITEJOIN_ACTORID_FIELD = protobuf.FieldDescriptor();
pb.SC_WARBAND_INVITEJOIN = protobuf.Descriptor();
pb.SC_WARBAND_INVITEJOIN_ACTORID_FIELD = protobuf.FieldDescriptor();
pb.SC_WARBAND_INVITEJOIN_ACTORNAME_FIELD = protobuf.FieldDescriptor();
pb.SC_WARBAND_INVITEJOIN_WARBANDID_FIELD = protobuf.FieldDescriptor();
pb.SC_WARBAND_INVITEJOIN_WARBANDNAME_FIELD = protobuf.FieldDescriptor();
pb.SC_WARBAND_PRIZENOTICE = protobuf.Descriptor();
pb.SC_WARBAND_PRIZENOTICE_PRIZETYPE_FIELD = protobuf.FieldDescriptor();
pb.SC_WARBAND_PRIZENOTICE_PARAM1_FIELD = protobuf.FieldDescriptor();
pb.SC_WARBAND_PRIZENOTICE_PARAM2_FIELD = protobuf.FieldDescriptor();
pb.SC_WARBAND_OPENRANDOMECTYPENOTICE = protobuf.Descriptor();
pb.SC_WARBAND_OPENRANDOMECTYPENOTICE_CONFIGID_FIELD = protobuf.FieldDescriptor();
pb.SC_WARBAND_OPENRANDOMECTYPENOTICE_BEGINCANOPENTIME_FIELD = protobuf.FieldDescriptor();
pb.SC_WARBAND_OPENRANDOMECTYPE = protobuf.Descriptor();
pb.SC_WARBAND_OPENRANDOMECTYPE_RESULT_FIELD = protobuf.FieldDescriptor();
pb.SC_WARBAND_ENTERRANDOMECTYPE = protobuf.Descriptor();
pb.SC_WARBAND_ENTERRANDOMECTYPE_RESULT_FIELD = protobuf.FieldDescriptor();
pb.SC_WARBAND_RANDOMECTYPERECORD = protobuf.Descriptor();
pb.SC_WARBAND_RANDOMECTYPERECORD_RECORDINFO = protobuf.Descriptor();
pb.SC_WARBAND_RANDOMECTYPERECORD_RECORDINFO_CONFIGID_FIELD = protobuf.FieldDescriptor();
pb.SC_WARBAND_RANDOMECTYPERECORD_RECORDINFO_STATUS_FIELD = protobuf.FieldDescriptor();
pb.SC_WARBAND_RANDOMECTYPERECORD_RECORDINFO_ACTORID_FIELD = protobuf.FieldDescriptor();
pb.SC_WARBAND_RANDOMECTYPERECORD_RECORDINFO_ACTORNAME_FIELD = protobuf.FieldDescriptor();
pb.SC_WARBAND_RANDOMECTYPERECORD_RECORDINFO_ACTORVOCATION_FIELD = protobuf.FieldDescriptor();
pb.SC_WARBAND_RANDOMECTYPERECORD_RECORDLIST_FIELD = protobuf.FieldDescriptor();
pb.SC_WARBAND_INVITEFOLLOW = protobuf.Descriptor();
pb.SC_WARBAND_INVITEFOLLOW_ACTORID_FIELD = protobuf.FieldDescriptor();
pb.CS_WARBAND_FIND = protobuf.Descriptor();
pb.CS_WARBAND_FIND_WARBANDNAME_FIELD = protobuf.FieldDescriptor();
pb.SC_WARBAND_FIND = protobuf.Descriptor();
pb.SC_WARBAND_FIND_WARBANDID_FIELD = protobuf.FieldDescriptor();
pb.SC_WARBAND_FIND_WARBANDNAME_FIELD = protobuf.FieldDescriptor();
pb.SC_WARBAND_FIND_MEMBERNUM_FIELD = protobuf.FieldDescriptor();
pb.SC_WARBAND_FIND_LEVELLIMIT_FIELD = protobuf.FieldDescriptor();
pb.SC_WARBAND_FIND_JOINLIMIT_FIELD = protobuf.FieldDescriptor();
pb.SC_WARBAND_FIND_RESULT_FIELD = protobuf.FieldDescriptor();
pb.SC_WARBAND_FIND_COUNTRY_FIELD = protobuf.FieldDescriptor();

pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_NONE_ENUM.name = "MSG_SOCIETY_NONE"
pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_NONE_ENUM.index = 0
pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_NONE_ENUM.number = 0
pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_CREATE_ENUM.name = "MSG_SOCIETY_CREATE"
pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_CREATE_ENUM.index = 1
pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_CREATE_ENUM.number = 1
pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_DELETE_ENUM.name = "MSG_SOCIETY_DELETE"
pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_DELETE_ENUM.index = 2
pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_DELETE_ENUM.number = 2
pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_MEMBERLIST_ENUM.name = "MSG_SOCIETY_MEMBERLIST"
pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_MEMBERLIST_ENUM.index = 3
pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_MEMBERLIST_ENUM.number = 3
pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_JOIN_ENUM.name = "MSG_SOCIETY_JOIN"
pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_JOIN_ENUM.index = 4
pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_JOIN_ENUM.number = 4
pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_LEAVE_ENUM.name = "MSG_SOCIETY_LEAVE"
pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_LEAVE_ENUM.index = 5
pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_LEAVE_ENUM.number = 5
pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_KICK_ENUM.name = "MSG_SOCIETY_KICK"
pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_KICK_ENUM.index = 6
pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_KICK_ENUM.number = 6
pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_APPOINT_ENUM.name = "MSG_SOCIETY_APPOINT"
pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_APPOINT_ENUM.index = 7
pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_APPOINT_ENUM.number = 7
pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_SUCCEED_ENUM.name = "MSG_SOCIETY_SUCCEED"
pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_SUCCEED_ENUM.index = 8
pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_SUCCEED_ENUM.number = 8
pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_GETLIST_ENUM.name = "MSG_SOCIETY_GETLIST"
pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_GETLIST_ENUM.index = 9
pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_GETLIST_ENUM.number = 9
pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_FIND_ENUM.name = "MSG_SOCIETY_FIND"
pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_FIND_ENUM.index = 10
pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_FIND_ENUM.number = 10
pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_INFO_ENUM.name = "MSG_SOCIETY_INFO"
pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_INFO_ENUM.index = 11
pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_INFO_ENUM.number = 11
pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_APPLYLIST_ENUM.name = "MSG_SOCIETY_APPLYLIST"
pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_APPLYLIST_ENUM.index = 12
pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_APPLYLIST_ENUM.number = 12
pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_JOINLIST_ENUM.name = "MSG_SOCIETY_JOINLIST"
pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_JOINLIST_ENUM.index = 13
pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_JOINLIST_ENUM.number = 13
pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_MSGCACHE_ENUM.name = "MSG_SOCIETY_MSGCACHE"
pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_MSGCACHE_ENUM.index = 14
pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_MSGCACHE_ENUM.number = 14
pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_TASKLIST_ENUM.name = "MSG_SOCIETY_TASKLIST"
pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_TASKLIST_ENUM.index = 15
pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_TASKLIST_ENUM.number = 15
pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_DISTRIBUTEHISTORY_ENUM.name = "MSG_SOCIETY_DISTRIBUTEHISTORY"
pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_DISTRIBUTEHISTORY_ENUM.index = 16
pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_DISTRIBUTEHISTORY_ENUM.number = 16
pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_GETEVENTINFOLIST_ENUM.name = "MSG_SOCIETY_GETEVENTINFOLIST"
pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_GETEVENTINFOLIST_ENUM.index = 17
pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_GETEVENTINFOLIST_ENUM.number = 17
pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_CONTRIBUTIONHISTORY_ENUM.name = "MSG_SOCIETY_CONTRIBUTIONHISTORY"
pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_CONTRIBUTIONHISTORY_ENUM.index = 18
pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_CONTRIBUTIONHISTORY_ENUM.number = 18
pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_GETNAME_ENUM.name = "MSG_SOCIETY_GETNAME"
pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_GETNAME_ENUM.index = 19
pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_GETNAME_ENUM.number = 19
pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_ACTIVITY_MSG_ENUM.name = "MSG_SOCIETY_ACTIVITY_MSG"
pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_ACTIVITY_MSG_ENUM.index = 20
pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_ACTIVITY_MSG_ENUM.number = 20
pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_TOWER_CLEARANCE_NUMBER_ENUM.name = "MSG_SOCIETY_TOWER_CLEARANCE_NUMBER"
pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_TOWER_CLEARANCE_NUMBER_ENUM.index = 21
pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_TOWER_CLEARANCE_NUMBER_ENUM.number = 21
pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_TOWER_CLEARANCE_TOPLEVEL_ENUM.name = "MSG_SOCIETY_TOWER_CLEARANCE_TOPLEVEL"
pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_TOWER_CLEARANCE_TOPLEVEL_ENUM.index = 22
pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_TOWER_CLEARANCE_TOPLEVEL_ENUM.number = 22
pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_ADDCONTRIBUTIONHISTORY_ENUM.name = "MSG_SOCIETY_ADDCONTRIBUTIONHISTORY"
pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_ADDCONTRIBUTIONHISTORY_ENUM.index = 23
pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_ADDCONTRIBUTIONHISTORY_ENUM.number = 23
pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_ALCHEMY_SPEEDUP_HELPLIST_ENUM.name = "MSG_SOCIETY_ALCHEMY_SPEEDUP_HELPLIST"
pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_ALCHEMY_SPEEDUP_HELPLIST_ENUM.index = 24
pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_ALCHEMY_SPEEDUP_HELPLIST_ENUM.number = 24
pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_ALCHEMY_SPEEDUP_HELPEDLIST_ENUM.name = "MSG_SOCIETY_ALCHEMY_SPEEDUP_HELPEDLIST"
pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_ALCHEMY_SPEEDUP_HELPEDLIST_ENUM.index = 25
pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_ALCHEMY_SPEEDUP_HELPEDLIST_ENUM.number = 25
pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_INVITEJOIN_ENUM.name = "MSG_SOCIETY_INVITEJOIN"
pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_INVITEJOIN_ENUM.index = 26
pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_INVITEJOIN_ENUM.number = 26
pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_UPDATE_RED_PACKET_ENUM.name = "MSG_SOCIETY_UPDATE_RED_PACKET"
pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_UPDATE_RED_PACKET_ENUM.index = 27
pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_UPDATE_RED_PACKET_ENUM.number = 27
pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_RED_PACKET_RANK_ENUM.name = "MSG_SOCIETY_RED_PACKET_RANK"
pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_RED_PACKET_RANK_ENUM.index = 28
pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_RED_PACKET_RANK_ENUM.number = 28
pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_RED_PACKET_PASSWORD_ENUM.name = "MSG_SOCIETY_RED_PACKET_PASSWORD"
pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_RED_PACKET_PASSWORD_ENUM.index = 29
pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_RED_PACKET_PASSWORD_ENUM.number = 29
pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_RED_PACKET_LIMIT_ENUM.name = "MSG_SOCIETY_RED_PACKET_LIMIT"
pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_RED_PACKET_LIMIT_ENUM.index = 30
pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_RED_PACKET_LIMIT_ENUM.number = 30
pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_RED_PACKET_SELF_RANK_ENUM.name = "MSG_SOCIETY_RED_PACKET_SELF_RANK"
pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_RED_PACKET_SELF_RANK_ENUM.index = 31
pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_RED_PACKET_SELF_RANK_ENUM.number = 31
pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_RED_PACKET_OPEN_HISTORY_ENUM.name = "MSG_SOCIETY_RED_PACKET_OPEN_HISTORY"
pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_RED_PACKET_OPEN_HISTORY_ENUM.index = 32
pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_RED_PACKET_OPEN_HISTORY_ENUM.number = 32
pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_RED_PACKET_SUPPER_ENUM.name = "MSG_SOCIETY_RED_PACKET_SUPPER"
pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_RED_PACKET_SUPPER_ENUM.index = 33
pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_RED_PACKET_SUPPER_ENUM.number = 33
pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_RED_PACKET_PRIZE_INFO_ENUM.name = "MSG_SOCIETY_RED_PACKET_PRIZE_INFO"
pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_RED_PACKET_PRIZE_INFO_ENUM.index = 34
pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_RED_PACKET_PRIZE_INFO_ENUM.number = 34
pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_RED_PACKET_TRANSFER_PASSWORD_ENUM.name = "MSG_SOCIETY_RED_PACKET_TRANSFER_PASSWORD"
pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_RED_PACKET_TRANSFER_PASSWORD_ENUM.index = 35
pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_RED_PACKET_TRANSFER_PASSWORD_ENUM.number = 35
pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_RED_PACKET_OPENTOTAL_PROCESS_ENUM.name = "MSG_SOCIETY_RED_PACKET_OPENTOTAL_PROCESS"
pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_RED_PACKET_OPENTOTAL_PROCESS_ENUM.index = 36
pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_RED_PACKET_OPENTOTAL_PROCESS_ENUM.number = 36
pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_RED_PACKET_OTHER_PRIZE_ENUM.name = "MSG_SOCIETY_RED_PACKET_OTHER_PRIZE"
pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_RED_PACKET_OTHER_PRIZE_ENUM.index = 37
pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_RED_PACKET_OTHER_PRIZE_ENUM.number = 37
pb.MSG_SOCIETY_ACTIONID_MSG_WARBAND_BEGIN_ENUM.name = "MSG_WARBAND_BEGIN"
pb.MSG_SOCIETY_ACTIONID_MSG_WARBAND_BEGIN_ENUM.index = 38
pb.MSG_SOCIETY_ACTIONID_MSG_WARBAND_BEGIN_ENUM.number = 500
pb.MSG_SOCIETY_ACTIONID_MSG_WARBAND_CREATE_ENUM.name = "MSG_WARBAND_CREATE"
pb.MSG_SOCIETY_ACTIONID_MSG_WARBAND_CREATE_ENUM.index = 39
pb.MSG_SOCIETY_ACTIONID_MSG_WARBAND_CREATE_ENUM.number = 501
pb.MSG_SOCIETY_ACTIONID_MSG_WARBAND_DELETE_ENUM.name = "MSG_WARBAND_DELETE"
pb.MSG_SOCIETY_ACTIONID_MSG_WARBAND_DELETE_ENUM.index = 40
pb.MSG_SOCIETY_ACTIONID_MSG_WARBAND_DELETE_ENUM.number = 502
pb.MSG_SOCIETY_ACTIONID_MSG_WARBAND_JOIN_ENUM.name = "MSG_WARBAND_JOIN"
pb.MSG_SOCIETY_ACTIONID_MSG_WARBAND_JOIN_ENUM.index = 41
pb.MSG_SOCIETY_ACTIONID_MSG_WARBAND_JOIN_ENUM.number = 503
pb.MSG_SOCIETY_ACTIONID_MSG_WARBAND_LEAVE_ENUM.name = "MSG_WARBAND_LEAVE"
pb.MSG_SOCIETY_ACTIONID_MSG_WARBAND_LEAVE_ENUM.index = 42
pb.MSG_SOCIETY_ACTIONID_MSG_WARBAND_LEAVE_ENUM.number = 504
pb.MSG_SOCIETY_ACTIONID_MSG_WARBAND_GETLIST_ENUM.name = "MSG_WARBAND_GETLIST"
pb.MSG_SOCIETY_ACTIONID_MSG_WARBAND_GETLIST_ENUM.index = 43
pb.MSG_SOCIETY_ACTIONID_MSG_WARBAND_GETLIST_ENUM.number = 505
pb.MSG_SOCIETY_ACTIONID_MSG_WARBAND_UPDATEDATA_ENUM.name = "MSG_WARBAND_UPDATEDATA"
pb.MSG_SOCIETY_ACTIONID_MSG_WARBAND_UPDATEDATA_ENUM.index = 44
pb.MSG_SOCIETY_ACTIONID_MSG_WARBAND_UPDATEDATA_ENUM.number = 506
pb.MSG_SOCIETY_ACTIONID_MSG_WARBAND_UPDATEMEMBER_ENUM.name = "MSG_WARBAND_UPDATEMEMBER"
pb.MSG_SOCIETY_ACTIONID_MSG_WARBAND_UPDATEMEMBER_ENUM.index = 45
pb.MSG_SOCIETY_ACTIONID_MSG_WARBAND_UPDATEMEMBER_ENUM.number = 507
pb.MSG_SOCIETY_ACTIONID_MSG_WARBAND_SETWARBANDCONFIG_ENUM.name = "MSG_WARBAND_SETWARBANDCONFIG"
pb.MSG_SOCIETY_ACTIONID_MSG_WARBAND_SETWARBANDCONFIG_ENUM.index = 46
pb.MSG_SOCIETY_ACTIONID_MSG_WARBAND_SETWARBANDCONFIG_ENUM.number = 508
pb.MSG_SOCIETY_ACTIONID_MSG_WARBAND_GETAPPLYLIST_ENUM.name = "MSG_WARBAND_GETAPPLYLIST"
pb.MSG_SOCIETY_ACTIONID_MSG_WARBAND_GETAPPLYLIST_ENUM.index = 47
pb.MSG_SOCIETY_ACTIONID_MSG_WARBAND_GETAPPLYLIST_ENUM.number = 509
pb.MSG_SOCIETY_ACTIONID_MSG_WARBAND_DEALAPPLY_ENUM.name = "MSG_WARBAND_DEALAPPLY"
pb.MSG_SOCIETY_ACTIONID_MSG_WARBAND_DEALAPPLY_ENUM.index = 48
pb.MSG_SOCIETY_ACTIONID_MSG_WARBAND_DEALAPPLY_ENUM.number = 510
pb.MSG_SOCIETY_ACTIONID_MSG_WARBAND_KICKMEMBER_ENUM.name = "MSG_WARBAND_KICKMEMBER"
pb.MSG_SOCIETY_ACTIONID_MSG_WARBAND_KICKMEMBER_ENUM.index = 49
pb.MSG_SOCIETY_ACTIONID_MSG_WARBAND_KICKMEMBER_ENUM.number = 511
pb.MSG_SOCIETY_ACTIONID_MSG_WARBAND_CHANGELEADER_ENUM.name = "MSG_WARBAND_CHANGELEADER"
pb.MSG_SOCIETY_ACTIONID_MSG_WARBAND_CHANGELEADER_ENUM.index = 50
pb.MSG_SOCIETY_ACTIONID_MSG_WARBAND_CHANGELEADER_ENUM.number = 512
pb.MSG_SOCIETY_ACTIONID_MSG_WARBAND_GETACTVRECORD_ENUM.name = "MSG_WARBAND_GETACTVRECORD"
pb.MSG_SOCIETY_ACTIONID_MSG_WARBAND_GETACTVRECORD_ENUM.index = 51
pb.MSG_SOCIETY_ACTIONID_MSG_WARBAND_GETACTVRECORD_ENUM.number = 513
pb.MSG_SOCIETY_ACTIONID_MSG_WARBAND_RECHARGEHISTORY_ENUM.name = "MSG_WARBAND_RECHARGEHISTORY"
pb.MSG_SOCIETY_ACTIONID_MSG_WARBAND_RECHARGEHISTORY_ENUM.index = 52
pb.MSG_SOCIETY_ACTIONID_MSG_WARBAND_RECHARGEHISTORY_ENUM.number = 514
pb.MSG_SOCIETY_ACTIONID_MSG_WARBAND_GETRECHARGEMESSAGE_ENUM.name = "MSG_WARBAND_GETRECHARGEMESSAGE"
pb.MSG_SOCIETY_ACTIONID_MSG_WARBAND_GETRECHARGEMESSAGE_ENUM.index = 53
pb.MSG_SOCIETY_ACTIONID_MSG_WARBAND_GETRECHARGEMESSAGE_ENUM.number = 515
pb.MSG_SOCIETY_ACTIONID_MSG_WARBAND_INVITEJOIN_ENUM.name = "MSG_WARBAND_INVITEJOIN"
pb.MSG_SOCIETY_ACTIONID_MSG_WARBAND_INVITEJOIN_ENUM.index = 54
pb.MSG_SOCIETY_ACTIONID_MSG_WARBAND_INVITEJOIN_ENUM.number = 516
pb.MSG_SOCIETY_ACTIONID_MSG_WARBAND_PRIZENOTICE_ENUM.name = "MSG_WARBAND_PRIZENOTICE"
pb.MSG_SOCIETY_ACTIONID_MSG_WARBAND_PRIZENOTICE_ENUM.index = 55
pb.MSG_SOCIETY_ACTIONID_MSG_WARBAND_PRIZENOTICE_ENUM.number = 517
pb.MSG_SOCIETY_ACTIONID_MSG_WARBAND_OPENRANDOMECTYPENOTICE_ENUM.name = "MSG_WARBAND_OPENRANDOMECTYPENOTICE"
pb.MSG_SOCIETY_ACTIONID_MSG_WARBAND_OPENRANDOMECTYPENOTICE_ENUM.index = 56
pb.MSG_SOCIETY_ACTIONID_MSG_WARBAND_OPENRANDOMECTYPENOTICE_ENUM.number = 518
pb.MSG_SOCIETY_ACTIONID_MSG_WARBAND_OPENRANDOMECTYPE_ENUM.name = "MSG_WARBAND_OPENRANDOMECTYPE"
pb.MSG_SOCIETY_ACTIONID_MSG_WARBAND_OPENRANDOMECTYPE_ENUM.index = 57
pb.MSG_SOCIETY_ACTIONID_MSG_WARBAND_OPENRANDOMECTYPE_ENUM.number = 519
pb.MSG_SOCIETY_ACTIONID_MSG_WARBAND_ENTERRANDOMECTYPE_ENUM.name = "MSG_WARBAND_ENTERRANDOMECTYPE"
pb.MSG_SOCIETY_ACTIONID_MSG_WARBAND_ENTERRANDOMECTYPE_ENUM.index = 58
pb.MSG_SOCIETY_ACTIONID_MSG_WARBAND_ENTERRANDOMECTYPE_ENUM.number = 520
pb.MSG_SOCIETY_ACTIONID_MSG_WARBAND_RANDOMECTYPERECORD_ENUM.name = "MSG_WARBAND_RANDOMECTYPERECORD"
pb.MSG_SOCIETY_ACTIONID_MSG_WARBAND_RANDOMECTYPERECORD_ENUM.index = 59
pb.MSG_SOCIETY_ACTIONID_MSG_WARBAND_RANDOMECTYPERECORD_ENUM.number = 521
pb.MSG_SOCIETY_ACTIONID_MSG_WARBAND_INVITEFOLLOW_ENUM.name = "MSG_WARBAND_INVITEFOLLOW"
pb.MSG_SOCIETY_ACTIONID_MSG_WARBAND_INVITEFOLLOW_ENUM.index = 60
pb.MSG_SOCIETY_ACTIONID_MSG_WARBAND_INVITEFOLLOW_ENUM.number = 522
pb.MSG_SOCIETY_ACTIONID_MSG_WARBAND_FIND_ENUM.name = "MSG_WARBAND_FIND"
pb.MSG_SOCIETY_ACTIONID_MSG_WARBAND_FIND_ENUM.index = 61
pb.MSG_SOCIETY_ACTIONID_MSG_WARBAND_FIND_ENUM.number = 523
pb.MSG_SOCIETY_ACTIONID_MSG_WARBAND_END_ENUM.name = "MSG_WARBAND_END"
pb.MSG_SOCIETY_ACTIONID_MSG_WARBAND_END_ENUM.index = 62
pb.MSG_SOCIETY_ACTIONID_MSG_WARBAND_END_ENUM.number = 1000
pb.MSG_SOCIETY_ACTIONID.name = "MSG_SOCIETY_ACTIONID"
pb.MSG_SOCIETY_ACTIONID.full_name = ".MSG_SOCIETY_ACTIONID"
pb.MSG_SOCIETY_ACTIONID.values = {pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_NONE_ENUM,pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_CREATE_ENUM,pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_DELETE_ENUM,pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_MEMBERLIST_ENUM,pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_JOIN_ENUM,pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_LEAVE_ENUM,pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_KICK_ENUM,pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_APPOINT_ENUM,pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_SUCCEED_ENUM,pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_GETLIST_ENUM,pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_FIND_ENUM,pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_INFO_ENUM,pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_APPLYLIST_ENUM,pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_JOINLIST_ENUM,pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_MSGCACHE_ENUM,pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_TASKLIST_ENUM,pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_DISTRIBUTEHISTORY_ENUM,pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_GETEVENTINFOLIST_ENUM,pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_CONTRIBUTIONHISTORY_ENUM,pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_GETNAME_ENUM,pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_ACTIVITY_MSG_ENUM,pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_TOWER_CLEARANCE_NUMBER_ENUM,pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_TOWER_CLEARANCE_TOPLEVEL_ENUM,pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_ADDCONTRIBUTIONHISTORY_ENUM,pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_ALCHEMY_SPEEDUP_HELPLIST_ENUM,pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_ALCHEMY_SPEEDUP_HELPEDLIST_ENUM,pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_INVITEJOIN_ENUM,pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_UPDATE_RED_PACKET_ENUM,pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_RED_PACKET_RANK_ENUM,pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_RED_PACKET_PASSWORD_ENUM,pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_RED_PACKET_LIMIT_ENUM,pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_RED_PACKET_SELF_RANK_ENUM,pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_RED_PACKET_OPEN_HISTORY_ENUM,pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_RED_PACKET_SUPPER_ENUM,pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_RED_PACKET_PRIZE_INFO_ENUM,pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_RED_PACKET_TRANSFER_PASSWORD_ENUM,pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_RED_PACKET_OPENTOTAL_PROCESS_ENUM,pb.MSG_SOCIETY_ACTIONID_MSG_SOCIETY_RED_PACKET_OTHER_PRIZE_ENUM,pb.MSG_SOCIETY_ACTIONID_MSG_WARBAND_BEGIN_ENUM,pb.MSG_SOCIETY_ACTIONID_MSG_WARBAND_CREATE_ENUM,pb.MSG_SOCIETY_ACTIONID_MSG_WARBAND_DELETE_ENUM,pb.MSG_SOCIETY_ACTIONID_MSG_WARBAND_JOIN_ENUM,pb.MSG_SOCIETY_ACTIONID_MSG_WARBAND_LEAVE_ENUM,pb.MSG_SOCIETY_ACTIONID_MSG_WARBAND_GETLIST_ENUM,pb.MSG_SOCIETY_ACTIONID_MSG_WARBAND_UPDATEDATA_ENUM,pb.MSG_SOCIETY_ACTIONID_MSG_WARBAND_UPDATEMEMBER_ENUM,pb.MSG_SOCIETY_ACTIONID_MSG_WARBAND_SETWARBANDCONFIG_ENUM,pb.MSG_SOCIETY_ACTIONID_MSG_WARBAND_GETAPPLYLIST_ENUM,pb.MSG_SOCIETY_ACTIONID_MSG_WARBAND_DEALAPPLY_ENUM,pb.MSG_SOCIETY_ACTIONID_MSG_WARBAND_KICKMEMBER_ENUM,pb.MSG_SOCIETY_ACTIONID_MSG_WARBAND_CHANGELEADER_ENUM,pb.MSG_SOCIETY_ACTIONID_MSG_WARBAND_GETACTVRECORD_ENUM,pb.MSG_SOCIETY_ACTIONID_MSG_WARBAND_RECHARGEHISTORY_ENUM,pb.MSG_SOCIETY_ACTIONID_MSG_WARBAND_GETRECHARGEMESSAGE_ENUM,pb.MSG_SOCIETY_ACTIONID_MSG_WARBAND_INVITEJOIN_ENUM,pb.MSG_SOCIETY_ACTIONID_MSG_WARBAND_PRIZENOTICE_ENUM,pb.MSG_SOCIETY_ACTIONID_MSG_WARBAND_OPENRANDOMECTYPENOTICE_ENUM,pb.MSG_SOCIETY_ACTIONID_MSG_WARBAND_OPENRANDOMECTYPE_ENUM,pb.MSG_SOCIETY_ACTIONID_MSG_WARBAND_ENTERRANDOMECTYPE_ENUM,pb.MSG_SOCIETY_ACTIONID_MSG_WARBAND_RANDOMECTYPERECORD_ENUM,pb.MSG_SOCIETY_ACTIONID_MSG_WARBAND_INVITEFOLLOW_ENUM,pb.MSG_SOCIETY_ACTIONID_MSG_WARBAND_FIND_ENUM,pb.MSG_SOCIETY_ACTIONID_MSG_WARBAND_END_ENUM}
pb.CS_SOCIETY_CREATE_SOCIETYNAME_FIELD.name = "SocietyName"
pb.CS_SOCIETY_CREATE_SOCIETYNAME_FIELD.full_name = ".CS_Society_Create.SocietyName"
pb.CS_SOCIETY_CREATE_SOCIETYNAME_FIELD.number = 1
pb.CS_SOCIETY_CREATE_SOCIETYNAME_FIELD.index = 0
pb.CS_SOCIETY_CREATE_SOCIETYNAME_FIELD.label = 2
pb.CS_SOCIETY_CREATE_SOCIETYNAME_FIELD.has_default_value = false
pb.CS_SOCIETY_CREATE_SOCIETYNAME_FIELD.default_value = ""
pb.CS_SOCIETY_CREATE_SOCIETYNAME_FIELD.type = 9
pb.CS_SOCIETY_CREATE_SOCIETYNAME_FIELD.cpp_type = 9

pb.CS_SOCIETY_CREATE.name = "CS_Society_Create"
pb.CS_SOCIETY_CREATE.full_name = ".CS_Society_Create"
pb.CS_SOCIETY_CREATE.nested_types = {}
pb.CS_SOCIETY_CREATE.enum_types = {}
pb.CS_SOCIETY_CREATE.fields = {pb.CS_SOCIETY_CREATE_SOCIETYNAME_FIELD}
pb.CS_SOCIETY_CREATE.is_extendable = false
pb.CS_SOCIETY_CREATE.extensions = {}
pb.SC_SOCIETY_CREATE_SOCIETYID_FIELD.name = "SocietyID"
pb.SC_SOCIETY_CREATE_SOCIETYID_FIELD.full_name = ".SC_Society_Create.SocietyID"
pb.SC_SOCIETY_CREATE_SOCIETYID_FIELD.number = 1
pb.SC_SOCIETY_CREATE_SOCIETYID_FIELD.index = 0
pb.SC_SOCIETY_CREATE_SOCIETYID_FIELD.label = 1
pb.SC_SOCIETY_CREATE_SOCIETYID_FIELD.has_default_value = false
pb.SC_SOCIETY_CREATE_SOCIETYID_FIELD.default_value = 0
pb.SC_SOCIETY_CREATE_SOCIETYID_FIELD.type = 13
pb.SC_SOCIETY_CREATE_SOCIETYID_FIELD.cpp_type = 3

pb.SC_SOCIETY_CREATE_SOCIETYNAME_FIELD.name = "SocietyName"
pb.SC_SOCIETY_CREATE_SOCIETYNAME_FIELD.full_name = ".SC_Society_Create.SocietyName"
pb.SC_SOCIETY_CREATE_SOCIETYNAME_FIELD.number = 2
pb.SC_SOCIETY_CREATE_SOCIETYNAME_FIELD.index = 1
pb.SC_SOCIETY_CREATE_SOCIETYNAME_FIELD.label = 1
pb.SC_SOCIETY_CREATE_SOCIETYNAME_FIELD.has_default_value = false
pb.SC_SOCIETY_CREATE_SOCIETYNAME_FIELD.default_value = ""
pb.SC_SOCIETY_CREATE_SOCIETYNAME_FIELD.type = 9
pb.SC_SOCIETY_CREATE_SOCIETYNAME_FIELD.cpp_type = 9

pb.SC_SOCIETY_CREATE_CHIEFID_FIELD.name = "ChiefID"
pb.SC_SOCIETY_CREATE_CHIEFID_FIELD.full_name = ".SC_Society_Create.ChiefID"
pb.SC_SOCIETY_CREATE_CHIEFID_FIELD.number = 3
pb.SC_SOCIETY_CREATE_CHIEFID_FIELD.index = 2
pb.SC_SOCIETY_CREATE_CHIEFID_FIELD.label = 1
pb.SC_SOCIETY_CREATE_CHIEFID_FIELD.has_default_value = false
pb.SC_SOCIETY_CREATE_CHIEFID_FIELD.default_value = 0
pb.SC_SOCIETY_CREATE_CHIEFID_FIELD.type = 13
pb.SC_SOCIETY_CREATE_CHIEFID_FIELD.cpp_type = 3

pb.SC_SOCIETY_CREATE_SOCIETYLEVEL_FIELD.name = "SocietyLevel"
pb.SC_SOCIETY_CREATE_SOCIETYLEVEL_FIELD.full_name = ".SC_Society_Create.SocietyLevel"
pb.SC_SOCIETY_CREATE_SOCIETYLEVEL_FIELD.number = 4
pb.SC_SOCIETY_CREATE_SOCIETYLEVEL_FIELD.index = 3
pb.SC_SOCIETY_CREATE_SOCIETYLEVEL_FIELD.label = 1
pb.SC_SOCIETY_CREATE_SOCIETYLEVEL_FIELD.has_default_value = false
pb.SC_SOCIETY_CREATE_SOCIETYLEVEL_FIELD.default_value = 0
pb.SC_SOCIETY_CREATE_SOCIETYLEVEL_FIELD.type = 13
pb.SC_SOCIETY_CREATE_SOCIETYLEVEL_FIELD.cpp_type = 3

pb.SC_SOCIETY_CREATE_SOCIETYEXP_FIELD.name = "SocietyExp"
pb.SC_SOCIETY_CREATE_SOCIETYEXP_FIELD.full_name = ".SC_Society_Create.SocietyExp"
pb.SC_SOCIETY_CREATE_SOCIETYEXP_FIELD.number = 5
pb.SC_SOCIETY_CREATE_SOCIETYEXP_FIELD.index = 4
pb.SC_SOCIETY_CREATE_SOCIETYEXP_FIELD.label = 1
pb.SC_SOCIETY_CREATE_SOCIETYEXP_FIELD.has_default_value = false
pb.SC_SOCIETY_CREATE_SOCIETYEXP_FIELD.default_value = 0
pb.SC_SOCIETY_CREATE_SOCIETYEXP_FIELD.type = 13
pb.SC_SOCIETY_CREATE_SOCIETYEXP_FIELD.cpp_type = 3

pb.SC_SOCIETY_CREATE_VITALITY_FIELD.name = "Vitality"
pb.SC_SOCIETY_CREATE_VITALITY_FIELD.full_name = ".SC_Society_Create.Vitality"
pb.SC_SOCIETY_CREATE_VITALITY_FIELD.number = 6
pb.SC_SOCIETY_CREATE_VITALITY_FIELD.index = 5
pb.SC_SOCIETY_CREATE_VITALITY_FIELD.label = 1
pb.SC_SOCIETY_CREATE_VITALITY_FIELD.has_default_value = false
pb.SC_SOCIETY_CREATE_VITALITY_FIELD.default_value = 0
pb.SC_SOCIETY_CREATE_VITALITY_FIELD.type = 13
pb.SC_SOCIETY_CREATE_VITALITY_FIELD.cpp_type = 3

pb.SC_SOCIETY_CREATE_MEMBERNUM_FIELD.name = "MemberNum"
pb.SC_SOCIETY_CREATE_MEMBERNUM_FIELD.full_name = ".SC_Society_Create.MemberNum"
pb.SC_SOCIETY_CREATE_MEMBERNUM_FIELD.number = 7
pb.SC_SOCIETY_CREATE_MEMBERNUM_FIELD.index = 6
pb.SC_SOCIETY_CREATE_MEMBERNUM_FIELD.label = 1
pb.SC_SOCIETY_CREATE_MEMBERNUM_FIELD.has_default_value = false
pb.SC_SOCIETY_CREATE_MEMBERNUM_FIELD.default_value = 0
pb.SC_SOCIETY_CREATE_MEMBERNUM_FIELD.type = 13
pb.SC_SOCIETY_CREATE_MEMBERNUM_FIELD.cpp_type = 3

pb.SC_SOCIETY_CREATE_FEASTLEAVE_FIELD.name = "FeastLeave"
pb.SC_SOCIETY_CREATE_FEASTLEAVE_FIELD.full_name = ".SC_Society_Create.FeastLeave"
pb.SC_SOCIETY_CREATE_FEASTLEAVE_FIELD.number = 8
pb.SC_SOCIETY_CREATE_FEASTLEAVE_FIELD.index = 7
pb.SC_SOCIETY_CREATE_FEASTLEAVE_FIELD.label = 1
pb.SC_SOCIETY_CREATE_FEASTLEAVE_FIELD.has_default_value = false
pb.SC_SOCIETY_CREATE_FEASTLEAVE_FIELD.default_value = 0
pb.SC_SOCIETY_CREATE_FEASTLEAVE_FIELD.type = 13
pb.SC_SOCIETY_CREATE_FEASTLEAVE_FIELD.cpp_type = 3

pb.SC_SOCIETY_CREATE_FEASTADD_FIELD.name = "FeastAdd"
pb.SC_SOCIETY_CREATE_FEASTADD_FIELD.full_name = ".SC_Society_Create.FeastAdd"
pb.SC_SOCIETY_CREATE_FEASTADD_FIELD.number = 9
pb.SC_SOCIETY_CREATE_FEASTADD_FIELD.index = 8
pb.SC_SOCIETY_CREATE_FEASTADD_FIELD.label = 1
pb.SC_SOCIETY_CREATE_FEASTADD_FIELD.has_default_value = false
pb.SC_SOCIETY_CREATE_FEASTADD_FIELD.default_value = 0
pb.SC_SOCIETY_CREATE_FEASTADD_FIELD.type = 13
pb.SC_SOCIETY_CREATE_FEASTADD_FIELD.cpp_type = 3

pb.SC_SOCIETY_CREATE_LASTFEAST_FIELD.name = "LastFeast"
pb.SC_SOCIETY_CREATE_LASTFEAST_FIELD.full_name = ".SC_Society_Create.LastFeast"
pb.SC_SOCIETY_CREATE_LASTFEAST_FIELD.number = 10
pb.SC_SOCIETY_CREATE_LASTFEAST_FIELD.index = 9
pb.SC_SOCIETY_CREATE_LASTFEAST_FIELD.label = 1
pb.SC_SOCIETY_CREATE_LASTFEAST_FIELD.has_default_value = false
pb.SC_SOCIETY_CREATE_LASTFEAST_FIELD.default_value = 0
pb.SC_SOCIETY_CREATE_LASTFEAST_FIELD.type = 13
pb.SC_SOCIETY_CREATE_LASTFEAST_FIELD.cpp_type = 3

pb.SC_SOCIETY_CREATE_CHIEFLOGOUTTIME_FIELD.name = "ChiefLogoutTime"
pb.SC_SOCIETY_CREATE_CHIEFLOGOUTTIME_FIELD.full_name = ".SC_Society_Create.ChiefLogoutTime"
pb.SC_SOCIETY_CREATE_CHIEFLOGOUTTIME_FIELD.number = 11
pb.SC_SOCIETY_CREATE_CHIEFLOGOUTTIME_FIELD.index = 10
pb.SC_SOCIETY_CREATE_CHIEFLOGOUTTIME_FIELD.label = 1
pb.SC_SOCIETY_CREATE_CHIEFLOGOUTTIME_FIELD.has_default_value = false
pb.SC_SOCIETY_CREATE_CHIEFLOGOUTTIME_FIELD.default_value = 0
pb.SC_SOCIETY_CREATE_CHIEFLOGOUTTIME_FIELD.type = 13
pb.SC_SOCIETY_CREATE_CHIEFLOGOUTTIME_FIELD.cpp_type = 3

pb.SC_SOCIETY_CREATE_SOCIETYINFO_FIELD.name = "SocietyInfo"
pb.SC_SOCIETY_CREATE_SOCIETYINFO_FIELD.full_name = ".SC_Society_Create.SocietyInfo"
pb.SC_SOCIETY_CREATE_SOCIETYINFO_FIELD.number = 12
pb.SC_SOCIETY_CREATE_SOCIETYINFO_FIELD.index = 11
pb.SC_SOCIETY_CREATE_SOCIETYINFO_FIELD.label = 1
pb.SC_SOCIETY_CREATE_SOCIETYINFO_FIELD.has_default_value = false
pb.SC_SOCIETY_CREATE_SOCIETYINFO_FIELD.default_value = ""
pb.SC_SOCIETY_CREATE_SOCIETYINFO_FIELD.type = 9
pb.SC_SOCIETY_CREATE_SOCIETYINFO_FIELD.cpp_type = 9

pb.SC_SOCIETY_CREATE_LEVELLIMIT_FIELD.name = "LevelLimit"
pb.SC_SOCIETY_CREATE_LEVELLIMIT_FIELD.full_name = ".SC_Society_Create.LevelLimit"
pb.SC_SOCIETY_CREATE_LEVELLIMIT_FIELD.number = 13
pb.SC_SOCIETY_CREATE_LEVELLIMIT_FIELD.index = 12
pb.SC_SOCIETY_CREATE_LEVELLIMIT_FIELD.label = 1
pb.SC_SOCIETY_CREATE_LEVELLIMIT_FIELD.has_default_value = false
pb.SC_SOCIETY_CREATE_LEVELLIMIT_FIELD.default_value = 0
pb.SC_SOCIETY_CREATE_LEVELLIMIT_FIELD.type = 13
pb.SC_SOCIETY_CREATE_LEVELLIMIT_FIELD.cpp_type = 3

pb.SC_SOCIETY_CREATE_JOINLIMIT_FIELD.name = "JoinLimit"
pb.SC_SOCIETY_CREATE_JOINLIMIT_FIELD.full_name = ".SC_Society_Create.JoinLimit"
pb.SC_SOCIETY_CREATE_JOINLIMIT_FIELD.number = 14
pb.SC_SOCIETY_CREATE_JOINLIMIT_FIELD.index = 13
pb.SC_SOCIETY_CREATE_JOINLIMIT_FIELD.label = 1
pb.SC_SOCIETY_CREATE_JOINLIMIT_FIELD.has_default_value = false
pb.SC_SOCIETY_CREATE_JOINLIMIT_FIELD.default_value = 0
pb.SC_SOCIETY_CREATE_JOINLIMIT_FIELD.type = 13
pb.SC_SOCIETY_CREATE_JOINLIMIT_FIELD.cpp_type = 3

pb.SC_SOCIETY_CREATE_FEASTACTOR_FIELD.name = "FeastActor"
pb.SC_SOCIETY_CREATE_FEASTACTOR_FIELD.full_name = ".SC_Society_Create.FeastActor"
pb.SC_SOCIETY_CREATE_FEASTACTOR_FIELD.number = 15
pb.SC_SOCIETY_CREATE_FEASTACTOR_FIELD.index = 14
pb.SC_SOCIETY_CREATE_FEASTACTOR_FIELD.label = 1
pb.SC_SOCIETY_CREATE_FEASTACTOR_FIELD.has_default_value = false
pb.SC_SOCIETY_CREATE_FEASTACTOR_FIELD.default_value = 0
pb.SC_SOCIETY_CREATE_FEASTACTOR_FIELD.type = 13
pb.SC_SOCIETY_CREATE_FEASTACTOR_FIELD.cpp_type = 3

pb.SC_SOCIETY_CREATE_FEASTACTORNAME_FIELD.name = "FeastActorName"
pb.SC_SOCIETY_CREATE_FEASTACTORNAME_FIELD.full_name = ".SC_Society_Create.FeastActorName"
pb.SC_SOCIETY_CREATE_FEASTACTORNAME_FIELD.number = 16
pb.SC_SOCIETY_CREATE_FEASTACTORNAME_FIELD.index = 15
pb.SC_SOCIETY_CREATE_FEASTACTORNAME_FIELD.label = 1
pb.SC_SOCIETY_CREATE_FEASTACTORNAME_FIELD.has_default_value = false
pb.SC_SOCIETY_CREATE_FEASTACTORNAME_FIELD.default_value = ""
pb.SC_SOCIETY_CREATE_FEASTACTORNAME_FIELD.type = 9
pb.SC_SOCIETY_CREATE_FEASTACTORNAME_FIELD.cpp_type = 9

pb.SC_SOCIETY_CREATE_RESULT_FIELD.name = "Result"
pb.SC_SOCIETY_CREATE_RESULT_FIELD.full_name = ".SC_Society_Create.Result"
pb.SC_SOCIETY_CREATE_RESULT_FIELD.number = 17
pb.SC_SOCIETY_CREATE_RESULT_FIELD.index = 16
pb.SC_SOCIETY_CREATE_RESULT_FIELD.label = 1
pb.SC_SOCIETY_CREATE_RESULT_FIELD.has_default_value = false
pb.SC_SOCIETY_CREATE_RESULT_FIELD.default_value = 0
pb.SC_SOCIETY_CREATE_RESULT_FIELD.type = 13
pb.SC_SOCIETY_CREATE_RESULT_FIELD.cpp_type = 3

pb.SC_SOCIETY_CREATE_BUYREVIVE_FIELD.name = "BuyRevive"
pb.SC_SOCIETY_CREATE_BUYREVIVE_FIELD.full_name = ".SC_Society_Create.BuyRevive"
pb.SC_SOCIETY_CREATE_BUYREVIVE_FIELD.number = 18
pb.SC_SOCIETY_CREATE_BUYREVIVE_FIELD.index = 17
pb.SC_SOCIETY_CREATE_BUYREVIVE_FIELD.label = 1
pb.SC_SOCIETY_CREATE_BUYREVIVE_FIELD.has_default_value = false
pb.SC_SOCIETY_CREATE_BUYREVIVE_FIELD.default_value = 0
pb.SC_SOCIETY_CREATE_BUYREVIVE_FIELD.type = 13
pb.SC_SOCIETY_CREATE_BUYREVIVE_FIELD.cpp_type = 3

pb.SC_SOCIETY_CREATE_REVIVE_FIELD.name = "Revive"
pb.SC_SOCIETY_CREATE_REVIVE_FIELD.full_name = ".SC_Society_Create.Revive"
pb.SC_SOCIETY_CREATE_REVIVE_FIELD.number = 19
pb.SC_SOCIETY_CREATE_REVIVE_FIELD.index = 18
pb.SC_SOCIETY_CREATE_REVIVE_FIELD.label = 1
pb.SC_SOCIETY_CREATE_REVIVE_FIELD.has_default_value = false
pb.SC_SOCIETY_CREATE_REVIVE_FIELD.default_value = 0
pb.SC_SOCIETY_CREATE_REVIVE_FIELD.type = 13
pb.SC_SOCIETY_CREATE_REVIVE_FIELD.cpp_type = 3

pb.SC_SOCIETY_CREATE_PRACTICECOUNT_FIELD.name = "PracticeCount"
pb.SC_SOCIETY_CREATE_PRACTICECOUNT_FIELD.full_name = ".SC_Society_Create.PracticeCount"
pb.SC_SOCIETY_CREATE_PRACTICECOUNT_FIELD.number = 20
pb.SC_SOCIETY_CREATE_PRACTICECOUNT_FIELD.index = 19
pb.SC_SOCIETY_CREATE_PRACTICECOUNT_FIELD.label = 1
pb.SC_SOCIETY_CREATE_PRACTICECOUNT_FIELD.has_default_value = false
pb.SC_SOCIETY_CREATE_PRACTICECOUNT_FIELD.default_value = 0
pb.SC_SOCIETY_CREATE_PRACTICECOUNT_FIELD.type = 13
pb.SC_SOCIETY_CREATE_PRACTICECOUNT_FIELD.cpp_type = 3

pb.SC_SOCIETY_CREATE_PRACTICETIME_FIELD.name = "PracticeTime"
pb.SC_SOCIETY_CREATE_PRACTICETIME_FIELD.full_name = ".SC_Society_Create.PracticeTime"
pb.SC_SOCIETY_CREATE_PRACTICETIME_FIELD.number = 21
pb.SC_SOCIETY_CREATE_PRACTICETIME_FIELD.index = 20
pb.SC_SOCIETY_CREATE_PRACTICETIME_FIELD.label = 1
pb.SC_SOCIETY_CREATE_PRACTICETIME_FIELD.has_default_value = false
pb.SC_SOCIETY_CREATE_PRACTICETIME_FIELD.default_value = 0
pb.SC_SOCIETY_CREATE_PRACTICETIME_FIELD.type = 13
pb.SC_SOCIETY_CREATE_PRACTICETIME_FIELD.cpp_type = 3

pb.SC_SOCIETY_CREATE_COUNTRY_FIELD.name = "Country"
pb.SC_SOCIETY_CREATE_COUNTRY_FIELD.full_name = ".SC_Society_Create.Country"
pb.SC_SOCIETY_CREATE_COUNTRY_FIELD.number = 22
pb.SC_SOCIETY_CREATE_COUNTRY_FIELD.index = 21
pb.SC_SOCIETY_CREATE_COUNTRY_FIELD.label = 1
pb.SC_SOCIETY_CREATE_COUNTRY_FIELD.has_default_value = false
pb.SC_SOCIETY_CREATE_COUNTRY_FIELD.default_value = 0
pb.SC_SOCIETY_CREATE_COUNTRY_FIELD.type = 13
pb.SC_SOCIETY_CREATE_COUNTRY_FIELD.cpp_type = 3

pb.SC_SOCIETY_CREATE_PRACTICENOTICE_FIELD.name = "PracticeNotice"
pb.SC_SOCIETY_CREATE_PRACTICENOTICE_FIELD.full_name = ".SC_Society_Create.PracticeNotice"
pb.SC_SOCIETY_CREATE_PRACTICENOTICE_FIELD.number = 23
pb.SC_SOCIETY_CREATE_PRACTICENOTICE_FIELD.index = 22
pb.SC_SOCIETY_CREATE_PRACTICENOTICE_FIELD.label = 1
pb.SC_SOCIETY_CREATE_PRACTICENOTICE_FIELD.has_default_value = false
pb.SC_SOCIETY_CREATE_PRACTICENOTICE_FIELD.default_value = 0
pb.SC_SOCIETY_CREATE_PRACTICENOTICE_FIELD.type = 13
pb.SC_SOCIETY_CREATE_PRACTICENOTICE_FIELD.cpp_type = 3

pb.SC_SOCIETY_CREATE_FEASTNOTICE_FIELD.name = "FeastNotice"
pb.SC_SOCIETY_CREATE_FEASTNOTICE_FIELD.full_name = ".SC_Society_Create.FeastNotice"
pb.SC_SOCIETY_CREATE_FEASTNOTICE_FIELD.number = 24
pb.SC_SOCIETY_CREATE_FEASTNOTICE_FIELD.index = 23
pb.SC_SOCIETY_CREATE_FEASTNOTICE_FIELD.label = 1
pb.SC_SOCIETY_CREATE_FEASTNOTICE_FIELD.has_default_value = false
pb.SC_SOCIETY_CREATE_FEASTNOTICE_FIELD.default_value = 0
pb.SC_SOCIETY_CREATE_FEASTNOTICE_FIELD.type = 13
pb.SC_SOCIETY_CREATE_FEASTNOTICE_FIELD.cpp_type = 3

pb.SC_SOCIETY_CREATE_CONTRIBUTIONNOTICE_FIELD.name = "ContributionNotice"
pb.SC_SOCIETY_CREATE_CONTRIBUTIONNOTICE_FIELD.full_name = ".SC_Society_Create.ContributionNotice"
pb.SC_SOCIETY_CREATE_CONTRIBUTIONNOTICE_FIELD.number = 25
pb.SC_SOCIETY_CREATE_CONTRIBUTIONNOTICE_FIELD.index = 24
pb.SC_SOCIETY_CREATE_CONTRIBUTIONNOTICE_FIELD.label = 1
pb.SC_SOCIETY_CREATE_CONTRIBUTIONNOTICE_FIELD.has_default_value = false
pb.SC_SOCIETY_CREATE_CONTRIBUTIONNOTICE_FIELD.default_value = 0
pb.SC_SOCIETY_CREATE_CONTRIBUTIONNOTICE_FIELD.type = 13
pb.SC_SOCIETY_CREATE_CONTRIBUTIONNOTICE_FIELD.cpp_type = 3

pb.SC_SOCIETY_CREATE_PRIZELIMIT_FIELD.name = "PrizeLimit"
pb.SC_SOCIETY_CREATE_PRIZELIMIT_FIELD.full_name = ".SC_Society_Create.PrizeLimit"
pb.SC_SOCIETY_CREATE_PRIZELIMIT_FIELD.number = 26
pb.SC_SOCIETY_CREATE_PRIZELIMIT_FIELD.index = 25
pb.SC_SOCIETY_CREATE_PRIZELIMIT_FIELD.label = 1
pb.SC_SOCIETY_CREATE_PRIZELIMIT_FIELD.has_default_value = false
pb.SC_SOCIETY_CREATE_PRIZELIMIT_FIELD.default_value = 0
pb.SC_SOCIETY_CREATE_PRIZELIMIT_FIELD.type = 13
pb.SC_SOCIETY_CREATE_PRIZELIMIT_FIELD.cpp_type = 3

pb.SC_SOCIETY_CREATE_POWERLIMIT_FIELD.name = "PowerLimit"
pb.SC_SOCIETY_CREATE_POWERLIMIT_FIELD.full_name = ".SC_Society_Create.PowerLimit"
pb.SC_SOCIETY_CREATE_POWERLIMIT_FIELD.number = 27
pb.SC_SOCIETY_CREATE_POWERLIMIT_FIELD.index = 26
pb.SC_SOCIETY_CREATE_POWERLIMIT_FIELD.label = 1
pb.SC_SOCIETY_CREATE_POWERLIMIT_FIELD.has_default_value = false
pb.SC_SOCIETY_CREATE_POWERLIMIT_FIELD.default_value = 0
pb.SC_SOCIETY_CREATE_POWERLIMIT_FIELD.type = 13
pb.SC_SOCIETY_CREATE_POWERLIMIT_FIELD.cpp_type = 3

pb.SC_SOCIETY_CREATE_FOUND_FIELD.name = "Found"
pb.SC_SOCIETY_CREATE_FOUND_FIELD.full_name = ".SC_Society_Create.Found"
pb.SC_SOCIETY_CREATE_FOUND_FIELD.number = 28
pb.SC_SOCIETY_CREATE_FOUND_FIELD.index = 27
pb.SC_SOCIETY_CREATE_FOUND_FIELD.label = 1
pb.SC_SOCIETY_CREATE_FOUND_FIELD.has_default_value = false
pb.SC_SOCIETY_CREATE_FOUND_FIELD.default_value = 0
pb.SC_SOCIETY_CREATE_FOUND_FIELD.type = 13
pb.SC_SOCIETY_CREATE_FOUND_FIELD.cpp_type = 3

pb.SC_SOCIETY_CREATE_CONSTRUCTION_FIELD.name = "Construction"
pb.SC_SOCIETY_CREATE_CONSTRUCTION_FIELD.full_name = ".SC_Society_Create.Construction"
pb.SC_SOCIETY_CREATE_CONSTRUCTION_FIELD.number = 29
pb.SC_SOCIETY_CREATE_CONSTRUCTION_FIELD.index = 28
pb.SC_SOCIETY_CREATE_CONSTRUCTION_FIELD.label = 1
pb.SC_SOCIETY_CREATE_CONSTRUCTION_FIELD.has_default_value = false
pb.SC_SOCIETY_CREATE_CONSTRUCTION_FIELD.default_value = 0
pb.SC_SOCIETY_CREATE_CONSTRUCTION_FIELD.type = 13
pb.SC_SOCIETY_CREATE_CONSTRUCTION_FIELD.cpp_type = 3

pb.SC_SOCIETY_CREATE_BUILDTIMEEND_FIELD.name = "buildTimeEnd"
pb.SC_SOCIETY_CREATE_BUILDTIMEEND_FIELD.full_name = ".SC_Society_Create.buildTimeEnd"
pb.SC_SOCIETY_CREATE_BUILDTIMEEND_FIELD.number = 30
pb.SC_SOCIETY_CREATE_BUILDTIMEEND_FIELD.index = 29
pb.SC_SOCIETY_CREATE_BUILDTIMEEND_FIELD.label = 1
pb.SC_SOCIETY_CREATE_BUILDTIMEEND_FIELD.has_default_value = false
pb.SC_SOCIETY_CREATE_BUILDTIMEEND_FIELD.default_value = 0
pb.SC_SOCIETY_CREATE_BUILDTIMEEND_FIELD.type = 13
pb.SC_SOCIETY_CREATE_BUILDTIMEEND_FIELD.cpp_type = 3

pb.SC_SOCIETY_CREATE_CLEARANCELEVEL5_FIELD.name = "ClearanceLevel5"
pb.SC_SOCIETY_CREATE_CLEARANCELEVEL5_FIELD.full_name = ".SC_Society_Create.ClearanceLevel5"
pb.SC_SOCIETY_CREATE_CLEARANCELEVEL5_FIELD.number = 31
pb.SC_SOCIETY_CREATE_CLEARANCELEVEL5_FIELD.index = 30
pb.SC_SOCIETY_CREATE_CLEARANCELEVEL5_FIELD.label = 1
pb.SC_SOCIETY_CREATE_CLEARANCELEVEL5_FIELD.has_default_value = false
pb.SC_SOCIETY_CREATE_CLEARANCELEVEL5_FIELD.default_value = 0
pb.SC_SOCIETY_CREATE_CLEARANCELEVEL5_FIELD.type = 13
pb.SC_SOCIETY_CREATE_CLEARANCELEVEL5_FIELD.cpp_type = 3

pb.SC_SOCIETY_CREATE_CLEARANCELEVEL10_FIELD.name = "ClearanceLevel10"
pb.SC_SOCIETY_CREATE_CLEARANCELEVEL10_FIELD.full_name = ".SC_Society_Create.ClearanceLevel10"
pb.SC_SOCIETY_CREATE_CLEARANCELEVEL10_FIELD.number = 32
pb.SC_SOCIETY_CREATE_CLEARANCELEVEL10_FIELD.index = 31
pb.SC_SOCIETY_CREATE_CLEARANCELEVEL10_FIELD.label = 1
pb.SC_SOCIETY_CREATE_CLEARANCELEVEL10_FIELD.has_default_value = false
pb.SC_SOCIETY_CREATE_CLEARANCELEVEL10_FIELD.default_value = 0
pb.SC_SOCIETY_CREATE_CLEARANCELEVEL10_FIELD.type = 13
pb.SC_SOCIETY_CREATE_CLEARANCELEVEL10_FIELD.cpp_type = 3

pb.SC_SOCIETY_CREATE_CLEARANCELEVEL15_FIELD.name = "ClearanceLevel15"
pb.SC_SOCIETY_CREATE_CLEARANCELEVEL15_FIELD.full_name = ".SC_Society_Create.ClearanceLevel15"
pb.SC_SOCIETY_CREATE_CLEARANCELEVEL15_FIELD.number = 33
pb.SC_SOCIETY_CREATE_CLEARANCELEVEL15_FIELD.index = 32
pb.SC_SOCIETY_CREATE_CLEARANCELEVEL15_FIELD.label = 1
pb.SC_SOCIETY_CREATE_CLEARANCELEVEL15_FIELD.has_default_value = false
pb.SC_SOCIETY_CREATE_CLEARANCELEVEL15_FIELD.default_value = 0
pb.SC_SOCIETY_CREATE_CLEARANCELEVEL15_FIELD.type = 13
pb.SC_SOCIETY_CREATE_CLEARANCELEVEL15_FIELD.cpp_type = 3

pb.SC_SOCIETY_CREATE_CLEARANCELEVEL20_FIELD.name = "ClearanceLevel20"
pb.SC_SOCIETY_CREATE_CLEARANCELEVEL20_FIELD.full_name = ".SC_Society_Create.ClearanceLevel20"
pb.SC_SOCIETY_CREATE_CLEARANCELEVEL20_FIELD.number = 34
pb.SC_SOCIETY_CREATE_CLEARANCELEVEL20_FIELD.index = 33
pb.SC_SOCIETY_CREATE_CLEARANCELEVEL20_FIELD.label = 1
pb.SC_SOCIETY_CREATE_CLEARANCELEVEL20_FIELD.has_default_value = false
pb.SC_SOCIETY_CREATE_CLEARANCELEVEL20_FIELD.default_value = 0
pb.SC_SOCIETY_CREATE_CLEARANCELEVEL20_FIELD.type = 13
pb.SC_SOCIETY_CREATE_CLEARANCELEVEL20_FIELD.cpp_type = 3

pb.SC_SOCIETY_CREATE_CLEARANCELEVELRANK1ACTORNAME_FIELD.name = "ClearanceLevelRank1ActorName"
pb.SC_SOCIETY_CREATE_CLEARANCELEVELRANK1ACTORNAME_FIELD.full_name = ".SC_Society_Create.ClearanceLevelRank1ActorName"
pb.SC_SOCIETY_CREATE_CLEARANCELEVELRANK1ACTORNAME_FIELD.number = 35
pb.SC_SOCIETY_CREATE_CLEARANCELEVELRANK1ACTORNAME_FIELD.index = 34
pb.SC_SOCIETY_CREATE_CLEARANCELEVELRANK1ACTORNAME_FIELD.label = 1
pb.SC_SOCIETY_CREATE_CLEARANCELEVELRANK1ACTORNAME_FIELD.has_default_value = false
pb.SC_SOCIETY_CREATE_CLEARANCELEVELRANK1ACTORNAME_FIELD.default_value = ""
pb.SC_SOCIETY_CREATE_CLEARANCELEVELRANK1ACTORNAME_FIELD.type = 9
pb.SC_SOCIETY_CREATE_CLEARANCELEVELRANK1ACTORNAME_FIELD.cpp_type = 9

pb.SC_SOCIETY_CREATE_CLEARANCELEVELRANK1VOCATIONID_FIELD.name = "ClearanceLevelRank1VocationID"
pb.SC_SOCIETY_CREATE_CLEARANCELEVELRANK1VOCATIONID_FIELD.full_name = ".SC_Society_Create.ClearanceLevelRank1VocationID"
pb.SC_SOCIETY_CREATE_CLEARANCELEVELRANK1VOCATIONID_FIELD.number = 36
pb.SC_SOCIETY_CREATE_CLEARANCELEVELRANK1VOCATIONID_FIELD.index = 35
pb.SC_SOCIETY_CREATE_CLEARANCELEVELRANK1VOCATIONID_FIELD.label = 1
pb.SC_SOCIETY_CREATE_CLEARANCELEVELRANK1VOCATIONID_FIELD.has_default_value = false
pb.SC_SOCIETY_CREATE_CLEARANCELEVELRANK1VOCATIONID_FIELD.default_value = 0
pb.SC_SOCIETY_CREATE_CLEARANCELEVELRANK1VOCATIONID_FIELD.type = 13
pb.SC_SOCIETY_CREATE_CLEARANCELEVELRANK1VOCATIONID_FIELD.cpp_type = 3

pb.SC_SOCIETY_CREATE_CLEARANCELEVELRANK1ECTYPEID_FIELD.name = "ClearanceLevelRank1EctypeID"
pb.SC_SOCIETY_CREATE_CLEARANCELEVELRANK1ECTYPEID_FIELD.full_name = ".SC_Society_Create.ClearanceLevelRank1EctypeID"
pb.SC_SOCIETY_CREATE_CLEARANCELEVELRANK1ECTYPEID_FIELD.number = 37
pb.SC_SOCIETY_CREATE_CLEARANCELEVELRANK1ECTYPEID_FIELD.index = 36
pb.SC_SOCIETY_CREATE_CLEARANCELEVELRANK1ECTYPEID_FIELD.label = 1
pb.SC_SOCIETY_CREATE_CLEARANCELEVELRANK1ECTYPEID_FIELD.has_default_value = false
pb.SC_SOCIETY_CREATE_CLEARANCELEVELRANK1ECTYPEID_FIELD.default_value = 0
pb.SC_SOCIETY_CREATE_CLEARANCELEVELRANK1ECTYPEID_FIELD.type = 13
pb.SC_SOCIETY_CREATE_CLEARANCELEVELRANK1ECTYPEID_FIELD.cpp_type = 3

pb.SC_SOCIETY_CREATE_CLEARANCEECTYPEID_FIELD.name = "ClearanceEctypeID"
pb.SC_SOCIETY_CREATE_CLEARANCEECTYPEID_FIELD.full_name = ".SC_Society_Create.ClearanceEctypeID"
pb.SC_SOCIETY_CREATE_CLEARANCEECTYPEID_FIELD.number = 38
pb.SC_SOCIETY_CREATE_CLEARANCEECTYPEID_FIELD.index = 37
pb.SC_SOCIETY_CREATE_CLEARANCEECTYPEID_FIELD.label = 1
pb.SC_SOCIETY_CREATE_CLEARANCEECTYPEID_FIELD.has_default_value = false
pb.SC_SOCIETY_CREATE_CLEARANCEECTYPEID_FIELD.default_value = 0
pb.SC_SOCIETY_CREATE_CLEARANCEECTYPEID_FIELD.type = 13
pb.SC_SOCIETY_CREATE_CLEARANCEECTYPEID_FIELD.cpp_type = 3

pb.SC_SOCIETY_CREATE_CLEARANCETIME_FIELD.name = "ClearanceTime"
pb.SC_SOCIETY_CREATE_CLEARANCETIME_FIELD.full_name = ".SC_Society_Create.ClearanceTime"
pb.SC_SOCIETY_CREATE_CLEARANCETIME_FIELD.number = 39
pb.SC_SOCIETY_CREATE_CLEARANCETIME_FIELD.index = 38
pb.SC_SOCIETY_CREATE_CLEARANCETIME_FIELD.label = 1
pb.SC_SOCIETY_CREATE_CLEARANCETIME_FIELD.has_default_value = false
pb.SC_SOCIETY_CREATE_CLEARANCETIME_FIELD.default_value = 0
pb.SC_SOCIETY_CREATE_CLEARANCETIME_FIELD.type = 13
pb.SC_SOCIETY_CREATE_CLEARANCETIME_FIELD.cpp_type = 3

pb.SC_SOCIETY_CREATE_ALCHEMYTYPE_FIELD.name = "AlchemyType"
pb.SC_SOCIETY_CREATE_ALCHEMYTYPE_FIELD.full_name = ".SC_Society_Create.AlchemyType"
pb.SC_SOCIETY_CREATE_ALCHEMYTYPE_FIELD.number = 40
pb.SC_SOCIETY_CREATE_ALCHEMYTYPE_FIELD.index = 39
pb.SC_SOCIETY_CREATE_ALCHEMYTYPE_FIELD.label = 1
pb.SC_SOCIETY_CREATE_ALCHEMYTYPE_FIELD.has_default_value = false
pb.SC_SOCIETY_CREATE_ALCHEMYTYPE_FIELD.default_value = 0
pb.SC_SOCIETY_CREATE_ALCHEMYTYPE_FIELD.type = 13
pb.SC_SOCIETY_CREATE_ALCHEMYTYPE_FIELD.cpp_type = 3

pb.SC_SOCIETY_CREATE_FURNACELEVEL_FIELD.name = "FurnaceLevel"
pb.SC_SOCIETY_CREATE_FURNACELEVEL_FIELD.full_name = ".SC_Society_Create.FurnaceLevel"
pb.SC_SOCIETY_CREATE_FURNACELEVEL_FIELD.number = 41
pb.SC_SOCIETY_CREATE_FURNACELEVEL_FIELD.index = 40
pb.SC_SOCIETY_CREATE_FURNACELEVEL_FIELD.label = 1
pb.SC_SOCIETY_CREATE_FURNACELEVEL_FIELD.has_default_value = false
pb.SC_SOCIETY_CREATE_FURNACELEVEL_FIELD.default_value = 0
pb.SC_SOCIETY_CREATE_FURNACELEVEL_FIELD.type = 13
pb.SC_SOCIETY_CREATE_FURNACELEVEL_FIELD.cpp_type = 3

pb.SC_SOCIETY_CREATE_CHANGEALCHEMYTYPETIMES_FIELD.name = "ChangeAlchemyTypeTimes"
pb.SC_SOCIETY_CREATE_CHANGEALCHEMYTYPETIMES_FIELD.full_name = ".SC_Society_Create.ChangeAlchemyTypeTimes"
pb.SC_SOCIETY_CREATE_CHANGEALCHEMYTYPETIMES_FIELD.number = 42
pb.SC_SOCIETY_CREATE_CHANGEALCHEMYTYPETIMES_FIELD.index = 41
pb.SC_SOCIETY_CREATE_CHANGEALCHEMYTYPETIMES_FIELD.label = 1
pb.SC_SOCIETY_CREATE_CHANGEALCHEMYTYPETIMES_FIELD.has_default_value = false
pb.SC_SOCIETY_CREATE_CHANGEALCHEMYTYPETIMES_FIELD.default_value = 0
pb.SC_SOCIETY_CREATE_CHANGEALCHEMYTYPETIMES_FIELD.type = 13
pb.SC_SOCIETY_CREATE_CHANGEALCHEMYTYPETIMES_FIELD.cpp_type = 3

pb.SC_SOCIETY_CREATE_POWER_FIELD.name = "Power"
pb.SC_SOCIETY_CREATE_POWER_FIELD.full_name = ".SC_Society_Create.Power"
pb.SC_SOCIETY_CREATE_POWER_FIELD.number = 43
pb.SC_SOCIETY_CREATE_POWER_FIELD.index = 42
pb.SC_SOCIETY_CREATE_POWER_FIELD.label = 1
pb.SC_SOCIETY_CREATE_POWER_FIELD.has_default_value = false
pb.SC_SOCIETY_CREATE_POWER_FIELD.default_value = 0
pb.SC_SOCIETY_CREATE_POWER_FIELD.type = 13
pb.SC_SOCIETY_CREATE_POWER_FIELD.cpp_type = 3

pb.SC_SOCIETY_CREATE.name = "SC_Society_Create"
pb.SC_SOCIETY_CREATE.full_name = ".SC_Society_Create"
pb.SC_SOCIETY_CREATE.nested_types = {}
pb.SC_SOCIETY_CREATE.enum_types = {}
pb.SC_SOCIETY_CREATE.fields = {pb.SC_SOCIETY_CREATE_SOCIETYID_FIELD, pb.SC_SOCIETY_CREATE_SOCIETYNAME_FIELD, pb.SC_SOCIETY_CREATE_CHIEFID_FIELD, pb.SC_SOCIETY_CREATE_SOCIETYLEVEL_FIELD, pb.SC_SOCIETY_CREATE_SOCIETYEXP_FIELD, pb.SC_SOCIETY_CREATE_VITALITY_FIELD, pb.SC_SOCIETY_CREATE_MEMBERNUM_FIELD, pb.SC_SOCIETY_CREATE_FEASTLEAVE_FIELD, pb.SC_SOCIETY_CREATE_FEASTADD_FIELD, pb.SC_SOCIETY_CREATE_LASTFEAST_FIELD, pb.SC_SOCIETY_CREATE_CHIEFLOGOUTTIME_FIELD, pb.SC_SOCIETY_CREATE_SOCIETYINFO_FIELD, pb.SC_SOCIETY_CREATE_LEVELLIMIT_FIELD, pb.SC_SOCIETY_CREATE_JOINLIMIT_FIELD, pb.SC_SOCIETY_CREATE_FEASTACTOR_FIELD, pb.SC_SOCIETY_CREATE_FEASTACTORNAME_FIELD, pb.SC_SOCIETY_CREATE_RESULT_FIELD, pb.SC_SOCIETY_CREATE_BUYREVIVE_FIELD, pb.SC_SOCIETY_CREATE_REVIVE_FIELD, pb.SC_SOCIETY_CREATE_PRACTICECOUNT_FIELD, pb.SC_SOCIETY_CREATE_PRACTICETIME_FIELD, pb.SC_SOCIETY_CREATE_COUNTRY_FIELD, pb.SC_SOCIETY_CREATE_PRACTICENOTICE_FIELD, pb.SC_SOCIETY_CREATE_FEASTNOTICE_FIELD, pb.SC_SOCIETY_CREATE_CONTRIBUTIONNOTICE_FIELD, pb.SC_SOCIETY_CREATE_PRIZELIMIT_FIELD, pb.SC_SOCIETY_CREATE_POWERLIMIT_FIELD, pb.SC_SOCIETY_CREATE_FOUND_FIELD, pb.SC_SOCIETY_CREATE_CONSTRUCTION_FIELD, pb.SC_SOCIETY_CREATE_BUILDTIMEEND_FIELD, pb.SC_SOCIETY_CREATE_CLEARANCELEVEL5_FIELD, pb.SC_SOCIETY_CREATE_CLEARANCELEVEL10_FIELD, pb.SC_SOCIETY_CREATE_CLEARANCELEVEL15_FIELD, pb.SC_SOCIETY_CREATE_CLEARANCELEVEL20_FIELD, pb.SC_SOCIETY_CREATE_CLEARANCELEVELRANK1ACTORNAME_FIELD, pb.SC_SOCIETY_CREATE_CLEARANCELEVELRANK1VOCATIONID_FIELD, pb.SC_SOCIETY_CREATE_CLEARANCELEVELRANK1ECTYPEID_FIELD, pb.SC_SOCIETY_CREATE_CLEARANCEECTYPEID_FIELD, pb.SC_SOCIETY_CREATE_CLEARANCETIME_FIELD, pb.SC_SOCIETY_CREATE_ALCHEMYTYPE_FIELD, pb.SC_SOCIETY_CREATE_FURNACELEVEL_FIELD, pb.SC_SOCIETY_CREATE_CHANGEALCHEMYTYPETIMES_FIELD, pb.SC_SOCIETY_CREATE_POWER_FIELD}
pb.SC_SOCIETY_CREATE.is_extendable = false
pb.SC_SOCIETY_CREATE.extensions = {}
pb.CS_SOCIETY_DELETE_SOCIETYID_FIELD.name = "SocietyID"
pb.CS_SOCIETY_DELETE_SOCIETYID_FIELD.full_name = ".CS_Society_Delete.SocietyID"
pb.CS_SOCIETY_DELETE_SOCIETYID_FIELD.number = 1
pb.CS_SOCIETY_DELETE_SOCIETYID_FIELD.index = 0
pb.CS_SOCIETY_DELETE_SOCIETYID_FIELD.label = 2
pb.CS_SOCIETY_DELETE_SOCIETYID_FIELD.has_default_value = false
pb.CS_SOCIETY_DELETE_SOCIETYID_FIELD.default_value = 0
pb.CS_SOCIETY_DELETE_SOCIETYID_FIELD.type = 13
pb.CS_SOCIETY_DELETE_SOCIETYID_FIELD.cpp_type = 3

pb.CS_SOCIETY_DELETE.name = "CS_Society_Delete"
pb.CS_SOCIETY_DELETE.full_name = ".CS_Society_Delete"
pb.CS_SOCIETY_DELETE.nested_types = {}
pb.CS_SOCIETY_DELETE.enum_types = {}
pb.CS_SOCIETY_DELETE.fields = {pb.CS_SOCIETY_DELETE_SOCIETYID_FIELD}
pb.CS_SOCIETY_DELETE.is_extendable = false
pb.CS_SOCIETY_DELETE.extensions = {}
pb.SC_SOCIETY_DELETE_SOCIETYID_FIELD.name = "SocietyID"
pb.SC_SOCIETY_DELETE_SOCIETYID_FIELD.full_name = ".SC_Society_Delete.SocietyID"
pb.SC_SOCIETY_DELETE_SOCIETYID_FIELD.number = 1
pb.SC_SOCIETY_DELETE_SOCIETYID_FIELD.index = 0
pb.SC_SOCIETY_DELETE_SOCIETYID_FIELD.label = 1
pb.SC_SOCIETY_DELETE_SOCIETYID_FIELD.has_default_value = false
pb.SC_SOCIETY_DELETE_SOCIETYID_FIELD.default_value = 0
pb.SC_SOCIETY_DELETE_SOCIETYID_FIELD.type = 13
pb.SC_SOCIETY_DELETE_SOCIETYID_FIELD.cpp_type = 3

pb.SC_SOCIETY_DELETE_RESULT_FIELD.name = "Result"
pb.SC_SOCIETY_DELETE_RESULT_FIELD.full_name = ".SC_Society_Delete.Result"
pb.SC_SOCIETY_DELETE_RESULT_FIELD.number = 2
pb.SC_SOCIETY_DELETE_RESULT_FIELD.index = 1
pb.SC_SOCIETY_DELETE_RESULT_FIELD.label = 1
pb.SC_SOCIETY_DELETE_RESULT_FIELD.has_default_value = false
pb.SC_SOCIETY_DELETE_RESULT_FIELD.default_value = 0
pb.SC_SOCIETY_DELETE_RESULT_FIELD.type = 13
pb.SC_SOCIETY_DELETE_RESULT_FIELD.cpp_type = 3

pb.SC_SOCIETY_DELETE_SOCIETYNAME_FIELD.name = "SocietyName"
pb.SC_SOCIETY_DELETE_SOCIETYNAME_FIELD.full_name = ".SC_Society_Delete.SocietyName"
pb.SC_SOCIETY_DELETE_SOCIETYNAME_FIELD.number = 3
pb.SC_SOCIETY_DELETE_SOCIETYNAME_FIELD.index = 2
pb.SC_SOCIETY_DELETE_SOCIETYNAME_FIELD.label = 1
pb.SC_SOCIETY_DELETE_SOCIETYNAME_FIELD.has_default_value = false
pb.SC_SOCIETY_DELETE_SOCIETYNAME_FIELD.default_value = ""
pb.SC_SOCIETY_DELETE_SOCIETYNAME_FIELD.type = 9
pb.SC_SOCIETY_DELETE_SOCIETYNAME_FIELD.cpp_type = 9

pb.SC_SOCIETY_DELETE_INITIATIVE_FIELD.name = "Initiative"
pb.SC_SOCIETY_DELETE_INITIATIVE_FIELD.full_name = ".SC_Society_Delete.Initiative"
pb.SC_SOCIETY_DELETE_INITIATIVE_FIELD.number = 4
pb.SC_SOCIETY_DELETE_INITIATIVE_FIELD.index = 3
pb.SC_SOCIETY_DELETE_INITIATIVE_FIELD.label = 1
pb.SC_SOCIETY_DELETE_INITIATIVE_FIELD.has_default_value = false
pb.SC_SOCIETY_DELETE_INITIATIVE_FIELD.default_value = 0
pb.SC_SOCIETY_DELETE_INITIATIVE_FIELD.type = 13
pb.SC_SOCIETY_DELETE_INITIATIVE_FIELD.cpp_type = 3

pb.SC_SOCIETY_DELETE_CHIEFID_FIELD.name = "ChiefID"
pb.SC_SOCIETY_DELETE_CHIEFID_FIELD.full_name = ".SC_Society_Delete.ChiefID"
pb.SC_SOCIETY_DELETE_CHIEFID_FIELD.number = 5
pb.SC_SOCIETY_DELETE_CHIEFID_FIELD.index = 4
pb.SC_SOCIETY_DELETE_CHIEFID_FIELD.label = 1
pb.SC_SOCIETY_DELETE_CHIEFID_FIELD.has_default_value = false
pb.SC_SOCIETY_DELETE_CHIEFID_FIELD.default_value = 0
pb.SC_SOCIETY_DELETE_CHIEFID_FIELD.type = 13
pb.SC_SOCIETY_DELETE_CHIEFID_FIELD.cpp_type = 3

pb.SC_SOCIETY_DELETE.name = "SC_Society_Delete"
pb.SC_SOCIETY_DELETE.full_name = ".SC_Society_Delete"
pb.SC_SOCIETY_DELETE.nested_types = {}
pb.SC_SOCIETY_DELETE.enum_types = {}
pb.SC_SOCIETY_DELETE.fields = {pb.SC_SOCIETY_DELETE_SOCIETYID_FIELD, pb.SC_SOCIETY_DELETE_RESULT_FIELD, pb.SC_SOCIETY_DELETE_SOCIETYNAME_FIELD, pb.SC_SOCIETY_DELETE_INITIATIVE_FIELD, pb.SC_SOCIETY_DELETE_CHIEFID_FIELD}
pb.SC_SOCIETY_DELETE.is_extendable = false
pb.SC_SOCIETY_DELETE.extensions = {}
pb.CS_SOCIETY_MEMBERLIST_SOCIETYID_FIELD.name = "SocietyID"
pb.CS_SOCIETY_MEMBERLIST_SOCIETYID_FIELD.full_name = ".CS_Society_MemberList.SocietyID"
pb.CS_SOCIETY_MEMBERLIST_SOCIETYID_FIELD.number = 1
pb.CS_SOCIETY_MEMBERLIST_SOCIETYID_FIELD.index = 0
pb.CS_SOCIETY_MEMBERLIST_SOCIETYID_FIELD.label = 2
pb.CS_SOCIETY_MEMBERLIST_SOCIETYID_FIELD.has_default_value = false
pb.CS_SOCIETY_MEMBERLIST_SOCIETYID_FIELD.default_value = 0
pb.CS_SOCIETY_MEMBERLIST_SOCIETYID_FIELD.type = 13
pb.CS_SOCIETY_MEMBERLIST_SOCIETYID_FIELD.cpp_type = 3

pb.CS_SOCIETY_MEMBERLIST.name = "CS_Society_MemberList"
pb.CS_SOCIETY_MEMBERLIST.full_name = ".CS_Society_MemberList"
pb.CS_SOCIETY_MEMBERLIST.nested_types = {}
pb.CS_SOCIETY_MEMBERLIST.enum_types = {}
pb.CS_SOCIETY_MEMBERLIST.fields = {pb.CS_SOCIETY_MEMBERLIST_SOCIETYID_FIELD}
pb.CS_SOCIETY_MEMBERLIST.is_extendable = false
pb.CS_SOCIETY_MEMBERLIST.extensions = {}
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_ACTORID_FIELD.name = "ActorID"
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_ACTORID_FIELD.full_name = ".SC_Society_MemberList.SocietyMember.ActorID"
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_ACTORID_FIELD.number = 1
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_ACTORID_FIELD.index = 0
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_ACTORID_FIELD.label = 1
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_ACTORID_FIELD.has_default_value = false
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_ACTORID_FIELD.default_value = 0
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_ACTORID_FIELD.type = 13
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_ACTORID_FIELD.cpp_type = 3

pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_ACTORNAME_FIELD.name = "ActorName"
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_ACTORNAME_FIELD.full_name = ".SC_Society_MemberList.SocietyMember.ActorName"
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_ACTORNAME_FIELD.number = 2
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_ACTORNAME_FIELD.index = 1
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_ACTORNAME_FIELD.label = 1
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_ACTORNAME_FIELD.has_default_value = false
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_ACTORNAME_FIELD.default_value = ""
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_ACTORNAME_FIELD.type = 9
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_ACTORNAME_FIELD.cpp_type = 9

pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_LEVEL_FIELD.name = "Level"
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_LEVEL_FIELD.full_name = ".SC_Society_MemberList.SocietyMember.Level"
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_LEVEL_FIELD.number = 3
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_LEVEL_FIELD.index = 2
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_LEVEL_FIELD.label = 1
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_LEVEL_FIELD.has_default_value = false
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_LEVEL_FIELD.default_value = 0
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_LEVEL_FIELD.type = 13
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_LEVEL_FIELD.cpp_type = 3

pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_VOCATION_FIELD.name = "Vocation"
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_VOCATION_FIELD.full_name = ".SC_Society_MemberList.SocietyMember.Vocation"
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_VOCATION_FIELD.number = 4
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_VOCATION_FIELD.index = 3
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_VOCATION_FIELD.label = 1
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_VOCATION_FIELD.has_default_value = false
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_VOCATION_FIELD.default_value = 0
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_VOCATION_FIELD.type = 13
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_VOCATION_FIELD.cpp_type = 3

pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_POWER_FIELD.name = "Power"
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_POWER_FIELD.full_name = ".SC_Society_MemberList.SocietyMember.Power"
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_POWER_FIELD.number = 5
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_POWER_FIELD.index = 4
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_POWER_FIELD.label = 1
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_POWER_FIELD.has_default_value = false
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_POWER_FIELD.default_value = 0
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_POWER_FIELD.type = 13
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_POWER_FIELD.cpp_type = 3

pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_VIPSCORE_FIELD.name = "VipScore"
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_VIPSCORE_FIELD.full_name = ".SC_Society_MemberList.SocietyMember.VipScore"
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_VIPSCORE_FIELD.number = 6
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_VIPSCORE_FIELD.index = 5
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_VIPSCORE_FIELD.label = 1
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_VIPSCORE_FIELD.has_default_value = false
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_VIPSCORE_FIELD.default_value = 0
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_VIPSCORE_FIELD.type = 13
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_VIPSCORE_FIELD.cpp_type = 3

pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_CHARM_FIELD.name = "Charm"
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_CHARM_FIELD.full_name = ".SC_Society_MemberList.SocietyMember.Charm"
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_CHARM_FIELD.number = 7
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_CHARM_FIELD.index = 6
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_CHARM_FIELD.label = 1
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_CHARM_FIELD.has_default_value = false
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_CHARM_FIELD.default_value = 0
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_CHARM_FIELD.type = 13
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_CHARM_FIELD.cpp_type = 3

pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_ISONLINE_FIELD.name = "IsOnline"
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_ISONLINE_FIELD.full_name = ".SC_Society_MemberList.SocietyMember.IsOnline"
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_ISONLINE_FIELD.number = 8
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_ISONLINE_FIELD.index = 7
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_ISONLINE_FIELD.label = 1
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_ISONLINE_FIELD.has_default_value = false
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_ISONLINE_FIELD.default_value = 0
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_ISONLINE_FIELD.type = 13
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_ISONLINE_FIELD.cpp_type = 3

pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_TITLE_FIELD.name = "Title"
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_TITLE_FIELD.full_name = ".SC_Society_MemberList.SocietyMember.Title"
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_TITLE_FIELD.number = 9
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_TITLE_FIELD.index = 8
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_TITLE_FIELD.label = 1
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_TITLE_FIELD.has_default_value = false
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_TITLE_FIELD.default_value = 0
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_TITLE_FIELD.type = 13
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_TITLE_FIELD.cpp_type = 3

pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_CONTRIBUTION_FIELD.name = "Contribution"
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_CONTRIBUTION_FIELD.full_name = ".SC_Society_MemberList.SocietyMember.Contribution"
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_CONTRIBUTION_FIELD.number = 10
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_CONTRIBUTION_FIELD.index = 9
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_CONTRIBUTION_FIELD.label = 1
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_CONTRIBUTION_FIELD.has_default_value = false
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_CONTRIBUTION_FIELD.default_value = 0
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_CONTRIBUTION_FIELD.type = 13
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_CONTRIBUTION_FIELD.cpp_type = 3

pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_LOGOUTTIME_FIELD.name = "LogoutTime"
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_LOGOUTTIME_FIELD.full_name = ".SC_Society_MemberList.SocietyMember.LogoutTime"
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_LOGOUTTIME_FIELD.number = 11
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_LOGOUTTIME_FIELD.index = 10
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_LOGOUTTIME_FIELD.label = 1
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_LOGOUTTIME_FIELD.has_default_value = false
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_LOGOUTTIME_FIELD.default_value = 0
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_LOGOUTTIME_FIELD.type = 13
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_LOGOUTTIME_FIELD.cpp_type = 3

pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_SOCIETYID_FIELD.name = "SocietyID"
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_SOCIETYID_FIELD.full_name = ".SC_Society_MemberList.SocietyMember.SocietyID"
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_SOCIETYID_FIELD.number = 12
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_SOCIETYID_FIELD.index = 11
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_SOCIETYID_FIELD.label = 1
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_SOCIETYID_FIELD.has_default_value = false
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_SOCIETYID_FIELD.default_value = 0
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_SOCIETYID_FIELD.type = 13
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_SOCIETYID_FIELD.cpp_type = 3

pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_ALCHEMYTIME_FIELD.name = "AlchemyTime"
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_ALCHEMYTIME_FIELD.full_name = ".SC_Society_MemberList.SocietyMember.AlchemyTime"
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_ALCHEMYTIME_FIELD.number = 13
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_ALCHEMYTIME_FIELD.index = 12
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_ALCHEMYTIME_FIELD.label = 1
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_ALCHEMYTIME_FIELD.has_default_value = false
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_ALCHEMYTIME_FIELD.default_value = 0
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_ALCHEMYTIME_FIELD.type = 13
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_ALCHEMYTIME_FIELD.cpp_type = 3

pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_ALCHEMYID_FIELD.name = "AlchemyID"
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_ALCHEMYID_FIELD.full_name = ".SC_Society_MemberList.SocietyMember.AlchemyID"
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_ALCHEMYID_FIELD.number = 14
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_ALCHEMYID_FIELD.index = 13
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_ALCHEMYID_FIELD.label = 1
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_ALCHEMYID_FIELD.has_default_value = false
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_ALCHEMYID_FIELD.default_value = 0
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_ALCHEMYID_FIELD.type = 13
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_ALCHEMYID_FIELD.cpp_type = 3

pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_HEPLEDTIMES_FIELD.name = "HepledTimes"
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_HEPLEDTIMES_FIELD.full_name = ".SC_Society_MemberList.SocietyMember.HepledTimes"
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_HEPLEDTIMES_FIELD.number = 15
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_HEPLEDTIMES_FIELD.index = 14
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_HEPLEDTIMES_FIELD.label = 1
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_HEPLEDTIMES_FIELD.has_default_value = false
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_HEPLEDTIMES_FIELD.default_value = 0
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_HEPLEDTIMES_FIELD.type = 13
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_HEPLEDTIMES_FIELD.cpp_type = 3

pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_DHEPLEDTIMES_FIELD.name = "DHepledTimes"
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_DHEPLEDTIMES_FIELD.full_name = ".SC_Society_MemberList.SocietyMember.DHepledTimes"
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_DHEPLEDTIMES_FIELD.number = 16
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_DHEPLEDTIMES_FIELD.index = 15
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_DHEPLEDTIMES_FIELD.label = 1
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_DHEPLEDTIMES_FIELD.has_default_value = false
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_DHEPLEDTIMES_FIELD.default_value = 0
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_DHEPLEDTIMES_FIELD.type = 13
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_DHEPLEDTIMES_FIELD.cpp_type = 3

pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_CURACTVCOUNT_FIELD.name = "CurActvCount"
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_CURACTVCOUNT_FIELD.full_name = ".SC_Society_MemberList.SocietyMember.CurActvCount"
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_CURACTVCOUNT_FIELD.number = 17
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_CURACTVCOUNT_FIELD.index = 16
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_CURACTVCOUNT_FIELD.label = 1
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_CURACTVCOUNT_FIELD.has_default_value = false
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_CURACTVCOUNT_FIELD.default_value = 0
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_CURACTVCOUNT_FIELD.type = 13
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_CURACTVCOUNT_FIELD.cpp_type = 3

pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_TOTALACTVCOUNT_FIELD.name = "TotalActvCount"
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_TOTALACTVCOUNT_FIELD.full_name = ".SC_Society_MemberList.SocietyMember.TotalActvCount"
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_TOTALACTVCOUNT_FIELD.number = 18
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_TOTALACTVCOUNT_FIELD.index = 17
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_TOTALACTVCOUNT_FIELD.label = 1
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_TOTALACTVCOUNT_FIELD.has_default_value = false
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_TOTALACTVCOUNT_FIELD.default_value = 0
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_TOTALACTVCOUNT_FIELD.type = 13
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_TOTALACTVCOUNT_FIELD.cpp_type = 3

pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_BLUEVIPLEVEL_FIELD.name = "BlueVIPLevel"
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_BLUEVIPLEVEL_FIELD.full_name = ".SC_Society_MemberList.SocietyMember.BlueVIPLevel"
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_BLUEVIPLEVEL_FIELD.number = 19
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_BLUEVIPLEVEL_FIELD.index = 18
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_BLUEVIPLEVEL_FIELD.label = 1
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_BLUEVIPLEVEL_FIELD.has_default_value = false
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_BLUEVIPLEVEL_FIELD.default_value = 0
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_BLUEVIPLEVEL_FIELD.type = 13
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_BLUEVIPLEVEL_FIELD.cpp_type = 3

pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_BLUEVIPTYPE_FIELD.name = "BlueVIPType"
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_BLUEVIPTYPE_FIELD.full_name = ".SC_Society_MemberList.SocietyMember.BlueVIPType"
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_BLUEVIPTYPE_FIELD.number = 20
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_BLUEVIPTYPE_FIELD.index = 19
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_BLUEVIPTYPE_FIELD.label = 1
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_BLUEVIPTYPE_FIELD.has_default_value = false
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_BLUEVIPTYPE_FIELD.default_value = 0
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_BLUEVIPTYPE_FIELD.type = 13
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_BLUEVIPTYPE_FIELD.cpp_type = 3

pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_GENRE_FIELD.name = "Genre"
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_GENRE_FIELD.full_name = ".SC_Society_MemberList.SocietyMember.Genre"
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_GENRE_FIELD.number = 21
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_GENRE_FIELD.index = 20
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_GENRE_FIELD.label = 1
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_GENRE_FIELD.has_default_value = false
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_GENRE_FIELD.default_value = 0
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_GENRE_FIELD.type = 13
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_GENRE_FIELD.cpp_type = 3

pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER.name = "SocietyMember"
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER.full_name = ".SC_Society_MemberList.SocietyMember"
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER.nested_types = {}
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER.enum_types = {}
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER.fields = {pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_ACTORID_FIELD, pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_ACTORNAME_FIELD, pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_LEVEL_FIELD, pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_VOCATION_FIELD, pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_POWER_FIELD, pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_VIPSCORE_FIELD, pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_CHARM_FIELD, pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_ISONLINE_FIELD, pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_TITLE_FIELD, pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_CONTRIBUTION_FIELD, pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_LOGOUTTIME_FIELD, pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_SOCIETYID_FIELD, pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_ALCHEMYTIME_FIELD, pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_ALCHEMYID_FIELD, pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_HEPLEDTIMES_FIELD, pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_DHEPLEDTIMES_FIELD, pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_CURACTVCOUNT_FIELD, pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_TOTALACTVCOUNT_FIELD, pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_BLUEVIPLEVEL_FIELD, pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_BLUEVIPTYPE_FIELD, pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER_GENRE_FIELD}
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER.is_extendable = false
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER.extensions = {}
pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER.containing_type = pb.SC_SOCIETY_MEMBERLIST
pb.SC_SOCIETY_MEMBERLIST_SOCIETYID_FIELD.name = "SocietyID"
pb.SC_SOCIETY_MEMBERLIST_SOCIETYID_FIELD.full_name = ".SC_Society_MemberList.SocietyID"
pb.SC_SOCIETY_MEMBERLIST_SOCIETYID_FIELD.number = 1
pb.SC_SOCIETY_MEMBERLIST_SOCIETYID_FIELD.index = 0
pb.SC_SOCIETY_MEMBERLIST_SOCIETYID_FIELD.label = 1
pb.SC_SOCIETY_MEMBERLIST_SOCIETYID_FIELD.has_default_value = false
pb.SC_SOCIETY_MEMBERLIST_SOCIETYID_FIELD.default_value = 0
pb.SC_SOCIETY_MEMBERLIST_SOCIETYID_FIELD.type = 13
pb.SC_SOCIETY_MEMBERLIST_SOCIETYID_FIELD.cpp_type = 3

pb.SC_SOCIETY_MEMBERLIST_MEMBERLIST_FIELD.name = "MemberList"
pb.SC_SOCIETY_MEMBERLIST_MEMBERLIST_FIELD.full_name = ".SC_Society_MemberList.MemberList"
pb.SC_SOCIETY_MEMBERLIST_MEMBERLIST_FIELD.number = 2
pb.SC_SOCIETY_MEMBERLIST_MEMBERLIST_FIELD.index = 1
pb.SC_SOCIETY_MEMBERLIST_MEMBERLIST_FIELD.label = 3
pb.SC_SOCIETY_MEMBERLIST_MEMBERLIST_FIELD.has_default_value = false
pb.SC_SOCIETY_MEMBERLIST_MEMBERLIST_FIELD.default_value = {}
pb.SC_SOCIETY_MEMBERLIST_MEMBERLIST_FIELD.message_type = pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER
pb.SC_SOCIETY_MEMBERLIST_MEMBERLIST_FIELD.type = 11
pb.SC_SOCIETY_MEMBERLIST_MEMBERLIST_FIELD.cpp_type = 10

pb.SC_SOCIETY_MEMBERLIST.name = "SC_Society_MemberList"
pb.SC_SOCIETY_MEMBERLIST.full_name = ".SC_Society_MemberList"
pb.SC_SOCIETY_MEMBERLIST.nested_types = {pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER}
pb.SC_SOCIETY_MEMBERLIST.enum_types = {}
pb.SC_SOCIETY_MEMBERLIST.fields = {pb.SC_SOCIETY_MEMBERLIST_SOCIETYID_FIELD, pb.SC_SOCIETY_MEMBERLIST_MEMBERLIST_FIELD}
pb.SC_SOCIETY_MEMBERLIST.is_extendable = false
pb.SC_SOCIETY_MEMBERLIST.extensions = {}
pb.CS_SOCIETY_JOIN_SOCIETYID_FIELD.name = "SocietyID"
pb.CS_SOCIETY_JOIN_SOCIETYID_FIELD.full_name = ".CS_Society_Join.SocietyID"
pb.CS_SOCIETY_JOIN_SOCIETYID_FIELD.number = 1
pb.CS_SOCIETY_JOIN_SOCIETYID_FIELD.index = 0
pb.CS_SOCIETY_JOIN_SOCIETYID_FIELD.label = 2
pb.CS_SOCIETY_JOIN_SOCIETYID_FIELD.has_default_value = false
pb.CS_SOCIETY_JOIN_SOCIETYID_FIELD.default_value = 0
pb.CS_SOCIETY_JOIN_SOCIETYID_FIELD.type = 13
pb.CS_SOCIETY_JOIN_SOCIETYID_FIELD.cpp_type = 3

pb.CS_SOCIETY_JOIN.name = "CS_Society_Join"
pb.CS_SOCIETY_JOIN.full_name = ".CS_Society_Join"
pb.CS_SOCIETY_JOIN.nested_types = {}
pb.CS_SOCIETY_JOIN.enum_types = {}
pb.CS_SOCIETY_JOIN.fields = {pb.CS_SOCIETY_JOIN_SOCIETYID_FIELD}
pb.CS_SOCIETY_JOIN.is_extendable = false
pb.CS_SOCIETY_JOIN.extensions = {}
pb.SC_SOCIETY_JOIN_SOCIETYID_FIELD.name = "SocietyID"
pb.SC_SOCIETY_JOIN_SOCIETYID_FIELD.full_name = ".SC_Society_Join.SocietyID"
pb.SC_SOCIETY_JOIN_SOCIETYID_FIELD.number = 1
pb.SC_SOCIETY_JOIN_SOCIETYID_FIELD.index = 0
pb.SC_SOCIETY_JOIN_SOCIETYID_FIELD.label = 1
pb.SC_SOCIETY_JOIN_SOCIETYID_FIELD.has_default_value = false
pb.SC_SOCIETY_JOIN_SOCIETYID_FIELD.default_value = 0
pb.SC_SOCIETY_JOIN_SOCIETYID_FIELD.type = 13
pb.SC_SOCIETY_JOIN_SOCIETYID_FIELD.cpp_type = 3

pb.SC_SOCIETY_JOIN_SOCIETYNAME_FIELD.name = "SocietyName"
pb.SC_SOCIETY_JOIN_SOCIETYNAME_FIELD.full_name = ".SC_Society_Join.SocietyName"
pb.SC_SOCIETY_JOIN_SOCIETYNAME_FIELD.number = 2
pb.SC_SOCIETY_JOIN_SOCIETYNAME_FIELD.index = 1
pb.SC_SOCIETY_JOIN_SOCIETYNAME_FIELD.label = 1
pb.SC_SOCIETY_JOIN_SOCIETYNAME_FIELD.has_default_value = false
pb.SC_SOCIETY_JOIN_SOCIETYNAME_FIELD.default_value = ""
pb.SC_SOCIETY_JOIN_SOCIETYNAME_FIELD.type = 9
pb.SC_SOCIETY_JOIN_SOCIETYNAME_FIELD.cpp_type = 9

pb.SC_SOCIETY_JOIN_CHIEFID_FIELD.name = "ChiefID"
pb.SC_SOCIETY_JOIN_CHIEFID_FIELD.full_name = ".SC_Society_Join.ChiefID"
pb.SC_SOCIETY_JOIN_CHIEFID_FIELD.number = 3
pb.SC_SOCIETY_JOIN_CHIEFID_FIELD.index = 2
pb.SC_SOCIETY_JOIN_CHIEFID_FIELD.label = 1
pb.SC_SOCIETY_JOIN_CHIEFID_FIELD.has_default_value = false
pb.SC_SOCIETY_JOIN_CHIEFID_FIELD.default_value = 0
pb.SC_SOCIETY_JOIN_CHIEFID_FIELD.type = 13
pb.SC_SOCIETY_JOIN_CHIEFID_FIELD.cpp_type = 3

pb.SC_SOCIETY_JOIN_SOCIETYLEVEL_FIELD.name = "SocietyLevel"
pb.SC_SOCIETY_JOIN_SOCIETYLEVEL_FIELD.full_name = ".SC_Society_Join.SocietyLevel"
pb.SC_SOCIETY_JOIN_SOCIETYLEVEL_FIELD.number = 4
pb.SC_SOCIETY_JOIN_SOCIETYLEVEL_FIELD.index = 3
pb.SC_SOCIETY_JOIN_SOCIETYLEVEL_FIELD.label = 1
pb.SC_SOCIETY_JOIN_SOCIETYLEVEL_FIELD.has_default_value = false
pb.SC_SOCIETY_JOIN_SOCIETYLEVEL_FIELD.default_value = 0
pb.SC_SOCIETY_JOIN_SOCIETYLEVEL_FIELD.type = 13
pb.SC_SOCIETY_JOIN_SOCIETYLEVEL_FIELD.cpp_type = 3

pb.SC_SOCIETY_JOIN_SOCIETYEXP_FIELD.name = "SocietyExp"
pb.SC_SOCIETY_JOIN_SOCIETYEXP_FIELD.full_name = ".SC_Society_Join.SocietyExp"
pb.SC_SOCIETY_JOIN_SOCIETYEXP_FIELD.number = 5
pb.SC_SOCIETY_JOIN_SOCIETYEXP_FIELD.index = 4
pb.SC_SOCIETY_JOIN_SOCIETYEXP_FIELD.label = 1
pb.SC_SOCIETY_JOIN_SOCIETYEXP_FIELD.has_default_value = false
pb.SC_SOCIETY_JOIN_SOCIETYEXP_FIELD.default_value = 0
pb.SC_SOCIETY_JOIN_SOCIETYEXP_FIELD.type = 13
pb.SC_SOCIETY_JOIN_SOCIETYEXP_FIELD.cpp_type = 3

pb.SC_SOCIETY_JOIN_VITALITY_FIELD.name = "Vitality"
pb.SC_SOCIETY_JOIN_VITALITY_FIELD.full_name = ".SC_Society_Join.Vitality"
pb.SC_SOCIETY_JOIN_VITALITY_FIELD.number = 6
pb.SC_SOCIETY_JOIN_VITALITY_FIELD.index = 5
pb.SC_SOCIETY_JOIN_VITALITY_FIELD.label = 1
pb.SC_SOCIETY_JOIN_VITALITY_FIELD.has_default_value = false
pb.SC_SOCIETY_JOIN_VITALITY_FIELD.default_value = 0
pb.SC_SOCIETY_JOIN_VITALITY_FIELD.type = 13
pb.SC_SOCIETY_JOIN_VITALITY_FIELD.cpp_type = 3

pb.SC_SOCIETY_JOIN_MEMBERNUM_FIELD.name = "MemberNum"
pb.SC_SOCIETY_JOIN_MEMBERNUM_FIELD.full_name = ".SC_Society_Join.MemberNum"
pb.SC_SOCIETY_JOIN_MEMBERNUM_FIELD.number = 7
pb.SC_SOCIETY_JOIN_MEMBERNUM_FIELD.index = 6
pb.SC_SOCIETY_JOIN_MEMBERNUM_FIELD.label = 1
pb.SC_SOCIETY_JOIN_MEMBERNUM_FIELD.has_default_value = false
pb.SC_SOCIETY_JOIN_MEMBERNUM_FIELD.default_value = 0
pb.SC_SOCIETY_JOIN_MEMBERNUM_FIELD.type = 13
pb.SC_SOCIETY_JOIN_MEMBERNUM_FIELD.cpp_type = 3

pb.SC_SOCIETY_JOIN_FEASTLEAVE_FIELD.name = "FeastLeave"
pb.SC_SOCIETY_JOIN_FEASTLEAVE_FIELD.full_name = ".SC_Society_Join.FeastLeave"
pb.SC_SOCIETY_JOIN_FEASTLEAVE_FIELD.number = 8
pb.SC_SOCIETY_JOIN_FEASTLEAVE_FIELD.index = 7
pb.SC_SOCIETY_JOIN_FEASTLEAVE_FIELD.label = 1
pb.SC_SOCIETY_JOIN_FEASTLEAVE_FIELD.has_default_value = false
pb.SC_SOCIETY_JOIN_FEASTLEAVE_FIELD.default_value = 0
pb.SC_SOCIETY_JOIN_FEASTLEAVE_FIELD.type = 13
pb.SC_SOCIETY_JOIN_FEASTLEAVE_FIELD.cpp_type = 3

pb.SC_SOCIETY_JOIN_FEASTADD_FIELD.name = "FeastAdd"
pb.SC_SOCIETY_JOIN_FEASTADD_FIELD.full_name = ".SC_Society_Join.FeastAdd"
pb.SC_SOCIETY_JOIN_FEASTADD_FIELD.number = 9
pb.SC_SOCIETY_JOIN_FEASTADD_FIELD.index = 8
pb.SC_SOCIETY_JOIN_FEASTADD_FIELD.label = 1
pb.SC_SOCIETY_JOIN_FEASTADD_FIELD.has_default_value = false
pb.SC_SOCIETY_JOIN_FEASTADD_FIELD.default_value = 0
pb.SC_SOCIETY_JOIN_FEASTADD_FIELD.type = 13
pb.SC_SOCIETY_JOIN_FEASTADD_FIELD.cpp_type = 3

pb.SC_SOCIETY_JOIN_LASTFEAST_FIELD.name = "LastFeast"
pb.SC_SOCIETY_JOIN_LASTFEAST_FIELD.full_name = ".SC_Society_Join.LastFeast"
pb.SC_SOCIETY_JOIN_LASTFEAST_FIELD.number = 10
pb.SC_SOCIETY_JOIN_LASTFEAST_FIELD.index = 9
pb.SC_SOCIETY_JOIN_LASTFEAST_FIELD.label = 1
pb.SC_SOCIETY_JOIN_LASTFEAST_FIELD.has_default_value = false
pb.SC_SOCIETY_JOIN_LASTFEAST_FIELD.default_value = 0
pb.SC_SOCIETY_JOIN_LASTFEAST_FIELD.type = 13
pb.SC_SOCIETY_JOIN_LASTFEAST_FIELD.cpp_type = 3

pb.SC_SOCIETY_JOIN_CHIEFLOGOUTTIME_FIELD.name = "ChiefLogoutTime"
pb.SC_SOCIETY_JOIN_CHIEFLOGOUTTIME_FIELD.full_name = ".SC_Society_Join.ChiefLogoutTime"
pb.SC_SOCIETY_JOIN_CHIEFLOGOUTTIME_FIELD.number = 11
pb.SC_SOCIETY_JOIN_CHIEFLOGOUTTIME_FIELD.index = 10
pb.SC_SOCIETY_JOIN_CHIEFLOGOUTTIME_FIELD.label = 1
pb.SC_SOCIETY_JOIN_CHIEFLOGOUTTIME_FIELD.has_default_value = false
pb.SC_SOCIETY_JOIN_CHIEFLOGOUTTIME_FIELD.default_value = 0
pb.SC_SOCIETY_JOIN_CHIEFLOGOUTTIME_FIELD.type = 13
pb.SC_SOCIETY_JOIN_CHIEFLOGOUTTIME_FIELD.cpp_type = 3

pb.SC_SOCIETY_JOIN_SOCIETYINFO_FIELD.name = "SocietyInfo"
pb.SC_SOCIETY_JOIN_SOCIETYINFO_FIELD.full_name = ".SC_Society_Join.SocietyInfo"
pb.SC_SOCIETY_JOIN_SOCIETYINFO_FIELD.number = 12
pb.SC_SOCIETY_JOIN_SOCIETYINFO_FIELD.index = 11
pb.SC_SOCIETY_JOIN_SOCIETYINFO_FIELD.label = 1
pb.SC_SOCIETY_JOIN_SOCIETYINFO_FIELD.has_default_value = false
pb.SC_SOCIETY_JOIN_SOCIETYINFO_FIELD.default_value = ""
pb.SC_SOCIETY_JOIN_SOCIETYINFO_FIELD.type = 9
pb.SC_SOCIETY_JOIN_SOCIETYINFO_FIELD.cpp_type = 9

pb.SC_SOCIETY_JOIN_LEVELLIMIT_FIELD.name = "LevelLimit"
pb.SC_SOCIETY_JOIN_LEVELLIMIT_FIELD.full_name = ".SC_Society_Join.LevelLimit"
pb.SC_SOCIETY_JOIN_LEVELLIMIT_FIELD.number = 13
pb.SC_SOCIETY_JOIN_LEVELLIMIT_FIELD.index = 12
pb.SC_SOCIETY_JOIN_LEVELLIMIT_FIELD.label = 1
pb.SC_SOCIETY_JOIN_LEVELLIMIT_FIELD.has_default_value = false
pb.SC_SOCIETY_JOIN_LEVELLIMIT_FIELD.default_value = 0
pb.SC_SOCIETY_JOIN_LEVELLIMIT_FIELD.type = 13
pb.SC_SOCIETY_JOIN_LEVELLIMIT_FIELD.cpp_type = 3

pb.SC_SOCIETY_JOIN_JOINLIMIT_FIELD.name = "JoinLimit"
pb.SC_SOCIETY_JOIN_JOINLIMIT_FIELD.full_name = ".SC_Society_Join.JoinLimit"
pb.SC_SOCIETY_JOIN_JOINLIMIT_FIELD.number = 14
pb.SC_SOCIETY_JOIN_JOINLIMIT_FIELD.index = 13
pb.SC_SOCIETY_JOIN_JOINLIMIT_FIELD.label = 1
pb.SC_SOCIETY_JOIN_JOINLIMIT_FIELD.has_default_value = false
pb.SC_SOCIETY_JOIN_JOINLIMIT_FIELD.default_value = 0
pb.SC_SOCIETY_JOIN_JOINLIMIT_FIELD.type = 13
pb.SC_SOCIETY_JOIN_JOINLIMIT_FIELD.cpp_type = 3

pb.SC_SOCIETY_JOIN_FEASTACTOR_FIELD.name = "FeastActor"
pb.SC_SOCIETY_JOIN_FEASTACTOR_FIELD.full_name = ".SC_Society_Join.FeastActor"
pb.SC_SOCIETY_JOIN_FEASTACTOR_FIELD.number = 15
pb.SC_SOCIETY_JOIN_FEASTACTOR_FIELD.index = 14
pb.SC_SOCIETY_JOIN_FEASTACTOR_FIELD.label = 1
pb.SC_SOCIETY_JOIN_FEASTACTOR_FIELD.has_default_value = false
pb.SC_SOCIETY_JOIN_FEASTACTOR_FIELD.default_value = 0
pb.SC_SOCIETY_JOIN_FEASTACTOR_FIELD.type = 13
pb.SC_SOCIETY_JOIN_FEASTACTOR_FIELD.cpp_type = 3

pb.SC_SOCIETY_JOIN_FEASTACTORNAME_FIELD.name = "FeastActorName"
pb.SC_SOCIETY_JOIN_FEASTACTORNAME_FIELD.full_name = ".SC_Society_Join.FeastActorName"
pb.SC_SOCIETY_JOIN_FEASTACTORNAME_FIELD.number = 16
pb.SC_SOCIETY_JOIN_FEASTACTORNAME_FIELD.index = 15
pb.SC_SOCIETY_JOIN_FEASTACTORNAME_FIELD.label = 1
pb.SC_SOCIETY_JOIN_FEASTACTORNAME_FIELD.has_default_value = false
pb.SC_SOCIETY_JOIN_FEASTACTORNAME_FIELD.default_value = ""
pb.SC_SOCIETY_JOIN_FEASTACTORNAME_FIELD.type = 9
pb.SC_SOCIETY_JOIN_FEASTACTORNAME_FIELD.cpp_type = 9

pb.SC_SOCIETY_JOIN_RESULT_FIELD.name = "Result"
pb.SC_SOCIETY_JOIN_RESULT_FIELD.full_name = ".SC_Society_Join.Result"
pb.SC_SOCIETY_JOIN_RESULT_FIELD.number = 17
pb.SC_SOCIETY_JOIN_RESULT_FIELD.index = 16
pb.SC_SOCIETY_JOIN_RESULT_FIELD.label = 1
pb.SC_SOCIETY_JOIN_RESULT_FIELD.has_default_value = false
pb.SC_SOCIETY_JOIN_RESULT_FIELD.default_value = 0
pb.SC_SOCIETY_JOIN_RESULT_FIELD.type = 13
pb.SC_SOCIETY_JOIN_RESULT_FIELD.cpp_type = 3

pb.SC_SOCIETY_JOIN_BUYREVIVE_FIELD.name = "BuyRevive"
pb.SC_SOCIETY_JOIN_BUYREVIVE_FIELD.full_name = ".SC_Society_Join.BuyRevive"
pb.SC_SOCIETY_JOIN_BUYREVIVE_FIELD.number = 18
pb.SC_SOCIETY_JOIN_BUYREVIVE_FIELD.index = 17
pb.SC_SOCIETY_JOIN_BUYREVIVE_FIELD.label = 1
pb.SC_SOCIETY_JOIN_BUYREVIVE_FIELD.has_default_value = false
pb.SC_SOCIETY_JOIN_BUYREVIVE_FIELD.default_value = 0
pb.SC_SOCIETY_JOIN_BUYREVIVE_FIELD.type = 13
pb.SC_SOCIETY_JOIN_BUYREVIVE_FIELD.cpp_type = 3

pb.SC_SOCIETY_JOIN_REVIVE_FIELD.name = "Revive"
pb.SC_SOCIETY_JOIN_REVIVE_FIELD.full_name = ".SC_Society_Join.Revive"
pb.SC_SOCIETY_JOIN_REVIVE_FIELD.number = 19
pb.SC_SOCIETY_JOIN_REVIVE_FIELD.index = 18
pb.SC_SOCIETY_JOIN_REVIVE_FIELD.label = 1
pb.SC_SOCIETY_JOIN_REVIVE_FIELD.has_default_value = false
pb.SC_SOCIETY_JOIN_REVIVE_FIELD.default_value = 0
pb.SC_SOCIETY_JOIN_REVIVE_FIELD.type = 13
pb.SC_SOCIETY_JOIN_REVIVE_FIELD.cpp_type = 3

pb.SC_SOCIETY_JOIN_PRACTICECOUNT_FIELD.name = "PracticeCount"
pb.SC_SOCIETY_JOIN_PRACTICECOUNT_FIELD.full_name = ".SC_Society_Join.PracticeCount"
pb.SC_SOCIETY_JOIN_PRACTICECOUNT_FIELD.number = 20
pb.SC_SOCIETY_JOIN_PRACTICECOUNT_FIELD.index = 19
pb.SC_SOCIETY_JOIN_PRACTICECOUNT_FIELD.label = 1
pb.SC_SOCIETY_JOIN_PRACTICECOUNT_FIELD.has_default_value = false
pb.SC_SOCIETY_JOIN_PRACTICECOUNT_FIELD.default_value = 0
pb.SC_SOCIETY_JOIN_PRACTICECOUNT_FIELD.type = 13
pb.SC_SOCIETY_JOIN_PRACTICECOUNT_FIELD.cpp_type = 3

pb.SC_SOCIETY_JOIN_PRACTICETIME_FIELD.name = "PracticeTime"
pb.SC_SOCIETY_JOIN_PRACTICETIME_FIELD.full_name = ".SC_Society_Join.PracticeTime"
pb.SC_SOCIETY_JOIN_PRACTICETIME_FIELD.number = 21
pb.SC_SOCIETY_JOIN_PRACTICETIME_FIELD.index = 20
pb.SC_SOCIETY_JOIN_PRACTICETIME_FIELD.label = 1
pb.SC_SOCIETY_JOIN_PRACTICETIME_FIELD.has_default_value = false
pb.SC_SOCIETY_JOIN_PRACTICETIME_FIELD.default_value = 0
pb.SC_SOCIETY_JOIN_PRACTICETIME_FIELD.type = 13
pb.SC_SOCIETY_JOIN_PRACTICETIME_FIELD.cpp_type = 3

pb.SC_SOCIETY_JOIN_COUNTRY_FIELD.name = "Country"
pb.SC_SOCIETY_JOIN_COUNTRY_FIELD.full_name = ".SC_Society_Join.Country"
pb.SC_SOCIETY_JOIN_COUNTRY_FIELD.number = 22
pb.SC_SOCIETY_JOIN_COUNTRY_FIELD.index = 21
pb.SC_SOCIETY_JOIN_COUNTRY_FIELD.label = 1
pb.SC_SOCIETY_JOIN_COUNTRY_FIELD.has_default_value = false
pb.SC_SOCIETY_JOIN_COUNTRY_FIELD.default_value = 0
pb.SC_SOCIETY_JOIN_COUNTRY_FIELD.type = 13
pb.SC_SOCIETY_JOIN_COUNTRY_FIELD.cpp_type = 3

pb.SC_SOCIETY_JOIN_PRACTICENOTICE_FIELD.name = "PracticeNotice"
pb.SC_SOCIETY_JOIN_PRACTICENOTICE_FIELD.full_name = ".SC_Society_Join.PracticeNotice"
pb.SC_SOCIETY_JOIN_PRACTICENOTICE_FIELD.number = 23
pb.SC_SOCIETY_JOIN_PRACTICENOTICE_FIELD.index = 22
pb.SC_SOCIETY_JOIN_PRACTICENOTICE_FIELD.label = 1
pb.SC_SOCIETY_JOIN_PRACTICENOTICE_FIELD.has_default_value = false
pb.SC_SOCIETY_JOIN_PRACTICENOTICE_FIELD.default_value = 0
pb.SC_SOCIETY_JOIN_PRACTICENOTICE_FIELD.type = 13
pb.SC_SOCIETY_JOIN_PRACTICENOTICE_FIELD.cpp_type = 3

pb.SC_SOCIETY_JOIN_FEASTNOTICE_FIELD.name = "FeastNotice"
pb.SC_SOCIETY_JOIN_FEASTNOTICE_FIELD.full_name = ".SC_Society_Join.FeastNotice"
pb.SC_SOCIETY_JOIN_FEASTNOTICE_FIELD.number = 24
pb.SC_SOCIETY_JOIN_FEASTNOTICE_FIELD.index = 23
pb.SC_SOCIETY_JOIN_FEASTNOTICE_FIELD.label = 1
pb.SC_SOCIETY_JOIN_FEASTNOTICE_FIELD.has_default_value = false
pb.SC_SOCIETY_JOIN_FEASTNOTICE_FIELD.default_value = 0
pb.SC_SOCIETY_JOIN_FEASTNOTICE_FIELD.type = 13
pb.SC_SOCIETY_JOIN_FEASTNOTICE_FIELD.cpp_type = 3

pb.SC_SOCIETY_JOIN_CONTRIBUTIONNOTICE_FIELD.name = "ContributionNotice"
pb.SC_SOCIETY_JOIN_CONTRIBUTIONNOTICE_FIELD.full_name = ".SC_Society_Join.ContributionNotice"
pb.SC_SOCIETY_JOIN_CONTRIBUTIONNOTICE_FIELD.number = 25
pb.SC_SOCIETY_JOIN_CONTRIBUTIONNOTICE_FIELD.index = 24
pb.SC_SOCIETY_JOIN_CONTRIBUTIONNOTICE_FIELD.label = 1
pb.SC_SOCIETY_JOIN_CONTRIBUTIONNOTICE_FIELD.has_default_value = false
pb.SC_SOCIETY_JOIN_CONTRIBUTIONNOTICE_FIELD.default_value = 0
pb.SC_SOCIETY_JOIN_CONTRIBUTIONNOTICE_FIELD.type = 13
pb.SC_SOCIETY_JOIN_CONTRIBUTIONNOTICE_FIELD.cpp_type = 3

pb.SC_SOCIETY_JOIN_PRIZELIMIT_FIELD.name = "PrizeLimit"
pb.SC_SOCIETY_JOIN_PRIZELIMIT_FIELD.full_name = ".SC_Society_Join.PrizeLimit"
pb.SC_SOCIETY_JOIN_PRIZELIMIT_FIELD.number = 26
pb.SC_SOCIETY_JOIN_PRIZELIMIT_FIELD.index = 25
pb.SC_SOCIETY_JOIN_PRIZELIMIT_FIELD.label = 1
pb.SC_SOCIETY_JOIN_PRIZELIMIT_FIELD.has_default_value = false
pb.SC_SOCIETY_JOIN_PRIZELIMIT_FIELD.default_value = 0
pb.SC_SOCIETY_JOIN_PRIZELIMIT_FIELD.type = 13
pb.SC_SOCIETY_JOIN_PRIZELIMIT_FIELD.cpp_type = 3

pb.SC_SOCIETY_JOIN_POWERLIMIT_FIELD.name = "PowerLimit"
pb.SC_SOCIETY_JOIN_POWERLIMIT_FIELD.full_name = ".SC_Society_Join.PowerLimit"
pb.SC_SOCIETY_JOIN_POWERLIMIT_FIELD.number = 27
pb.SC_SOCIETY_JOIN_POWERLIMIT_FIELD.index = 26
pb.SC_SOCIETY_JOIN_POWERLIMIT_FIELD.label = 1
pb.SC_SOCIETY_JOIN_POWERLIMIT_FIELD.has_default_value = false
pb.SC_SOCIETY_JOIN_POWERLIMIT_FIELD.default_value = 0
pb.SC_SOCIETY_JOIN_POWERLIMIT_FIELD.type = 13
pb.SC_SOCIETY_JOIN_POWERLIMIT_FIELD.cpp_type = 3

pb.SC_SOCIETY_JOIN_FOUND_FIELD.name = "Found"
pb.SC_SOCIETY_JOIN_FOUND_FIELD.full_name = ".SC_Society_Join.Found"
pb.SC_SOCIETY_JOIN_FOUND_FIELD.number = 28
pb.SC_SOCIETY_JOIN_FOUND_FIELD.index = 27
pb.SC_SOCIETY_JOIN_FOUND_FIELD.label = 1
pb.SC_SOCIETY_JOIN_FOUND_FIELD.has_default_value = false
pb.SC_SOCIETY_JOIN_FOUND_FIELD.default_value = 0
pb.SC_SOCIETY_JOIN_FOUND_FIELD.type = 13
pb.SC_SOCIETY_JOIN_FOUND_FIELD.cpp_type = 3

pb.SC_SOCIETY_JOIN_CONSTRUCTION_FIELD.name = "Construction"
pb.SC_SOCIETY_JOIN_CONSTRUCTION_FIELD.full_name = ".SC_Society_Join.Construction"
pb.SC_SOCIETY_JOIN_CONSTRUCTION_FIELD.number = 29
pb.SC_SOCIETY_JOIN_CONSTRUCTION_FIELD.index = 28
pb.SC_SOCIETY_JOIN_CONSTRUCTION_FIELD.label = 1
pb.SC_SOCIETY_JOIN_CONSTRUCTION_FIELD.has_default_value = false
pb.SC_SOCIETY_JOIN_CONSTRUCTION_FIELD.default_value = 0
pb.SC_SOCIETY_JOIN_CONSTRUCTION_FIELD.type = 13
pb.SC_SOCIETY_JOIN_CONSTRUCTION_FIELD.cpp_type = 3

pb.SC_SOCIETY_JOIN_BUILDTIMEEND_FIELD.name = "buildTimeEnd"
pb.SC_SOCIETY_JOIN_BUILDTIMEEND_FIELD.full_name = ".SC_Society_Join.buildTimeEnd"
pb.SC_SOCIETY_JOIN_BUILDTIMEEND_FIELD.number = 30
pb.SC_SOCIETY_JOIN_BUILDTIMEEND_FIELD.index = 29
pb.SC_SOCIETY_JOIN_BUILDTIMEEND_FIELD.label = 1
pb.SC_SOCIETY_JOIN_BUILDTIMEEND_FIELD.has_default_value = false
pb.SC_SOCIETY_JOIN_BUILDTIMEEND_FIELD.default_value = 0
pb.SC_SOCIETY_JOIN_BUILDTIMEEND_FIELD.type = 13
pb.SC_SOCIETY_JOIN_BUILDTIMEEND_FIELD.cpp_type = 3

pb.SC_SOCIETY_JOIN_CLEARANCELEVEL5_FIELD.name = "ClearanceLevel5"
pb.SC_SOCIETY_JOIN_CLEARANCELEVEL5_FIELD.full_name = ".SC_Society_Join.ClearanceLevel5"
pb.SC_SOCIETY_JOIN_CLEARANCELEVEL5_FIELD.number = 31
pb.SC_SOCIETY_JOIN_CLEARANCELEVEL5_FIELD.index = 30
pb.SC_SOCIETY_JOIN_CLEARANCELEVEL5_FIELD.label = 1
pb.SC_SOCIETY_JOIN_CLEARANCELEVEL5_FIELD.has_default_value = false
pb.SC_SOCIETY_JOIN_CLEARANCELEVEL5_FIELD.default_value = 0
pb.SC_SOCIETY_JOIN_CLEARANCELEVEL5_FIELD.type = 13
pb.SC_SOCIETY_JOIN_CLEARANCELEVEL5_FIELD.cpp_type = 3

pb.SC_SOCIETY_JOIN_CLEARANCELEVEL10_FIELD.name = "ClearanceLevel10"
pb.SC_SOCIETY_JOIN_CLEARANCELEVEL10_FIELD.full_name = ".SC_Society_Join.ClearanceLevel10"
pb.SC_SOCIETY_JOIN_CLEARANCELEVEL10_FIELD.number = 32
pb.SC_SOCIETY_JOIN_CLEARANCELEVEL10_FIELD.index = 31
pb.SC_SOCIETY_JOIN_CLEARANCELEVEL10_FIELD.label = 1
pb.SC_SOCIETY_JOIN_CLEARANCELEVEL10_FIELD.has_default_value = false
pb.SC_SOCIETY_JOIN_CLEARANCELEVEL10_FIELD.default_value = 0
pb.SC_SOCIETY_JOIN_CLEARANCELEVEL10_FIELD.type = 13
pb.SC_SOCIETY_JOIN_CLEARANCELEVEL10_FIELD.cpp_type = 3

pb.SC_SOCIETY_JOIN_CLEARANCELEVEL15_FIELD.name = "ClearanceLevel15"
pb.SC_SOCIETY_JOIN_CLEARANCELEVEL15_FIELD.full_name = ".SC_Society_Join.ClearanceLevel15"
pb.SC_SOCIETY_JOIN_CLEARANCELEVEL15_FIELD.number = 33
pb.SC_SOCIETY_JOIN_CLEARANCELEVEL15_FIELD.index = 32
pb.SC_SOCIETY_JOIN_CLEARANCELEVEL15_FIELD.label = 1
pb.SC_SOCIETY_JOIN_CLEARANCELEVEL15_FIELD.has_default_value = false
pb.SC_SOCIETY_JOIN_CLEARANCELEVEL15_FIELD.default_value = 0
pb.SC_SOCIETY_JOIN_CLEARANCELEVEL15_FIELD.type = 13
pb.SC_SOCIETY_JOIN_CLEARANCELEVEL15_FIELD.cpp_type = 3

pb.SC_SOCIETY_JOIN_CLEARANCELEVEL20_FIELD.name = "ClearanceLevel20"
pb.SC_SOCIETY_JOIN_CLEARANCELEVEL20_FIELD.full_name = ".SC_Society_Join.ClearanceLevel20"
pb.SC_SOCIETY_JOIN_CLEARANCELEVEL20_FIELD.number = 34
pb.SC_SOCIETY_JOIN_CLEARANCELEVEL20_FIELD.index = 33
pb.SC_SOCIETY_JOIN_CLEARANCELEVEL20_FIELD.label = 1
pb.SC_SOCIETY_JOIN_CLEARANCELEVEL20_FIELD.has_default_value = false
pb.SC_SOCIETY_JOIN_CLEARANCELEVEL20_FIELD.default_value = 0
pb.SC_SOCIETY_JOIN_CLEARANCELEVEL20_FIELD.type = 13
pb.SC_SOCIETY_JOIN_CLEARANCELEVEL20_FIELD.cpp_type = 3

pb.SC_SOCIETY_JOIN_CLEARANCELEVELRANK1ACTORNAME_FIELD.name = "ClearanceLevelRank1ActorName"
pb.SC_SOCIETY_JOIN_CLEARANCELEVELRANK1ACTORNAME_FIELD.full_name = ".SC_Society_Join.ClearanceLevelRank1ActorName"
pb.SC_SOCIETY_JOIN_CLEARANCELEVELRANK1ACTORNAME_FIELD.number = 35
pb.SC_SOCIETY_JOIN_CLEARANCELEVELRANK1ACTORNAME_FIELD.index = 34
pb.SC_SOCIETY_JOIN_CLEARANCELEVELRANK1ACTORNAME_FIELD.label = 1
pb.SC_SOCIETY_JOIN_CLEARANCELEVELRANK1ACTORNAME_FIELD.has_default_value = false
pb.SC_SOCIETY_JOIN_CLEARANCELEVELRANK1ACTORNAME_FIELD.default_value = ""
pb.SC_SOCIETY_JOIN_CLEARANCELEVELRANK1ACTORNAME_FIELD.type = 9
pb.SC_SOCIETY_JOIN_CLEARANCELEVELRANK1ACTORNAME_FIELD.cpp_type = 9

pb.SC_SOCIETY_JOIN_CLEARANCELEVELRANK1VOCATIONID_FIELD.name = "ClearanceLevelRank1VocationID"
pb.SC_SOCIETY_JOIN_CLEARANCELEVELRANK1VOCATIONID_FIELD.full_name = ".SC_Society_Join.ClearanceLevelRank1VocationID"
pb.SC_SOCIETY_JOIN_CLEARANCELEVELRANK1VOCATIONID_FIELD.number = 36
pb.SC_SOCIETY_JOIN_CLEARANCELEVELRANK1VOCATIONID_FIELD.index = 35
pb.SC_SOCIETY_JOIN_CLEARANCELEVELRANK1VOCATIONID_FIELD.label = 1
pb.SC_SOCIETY_JOIN_CLEARANCELEVELRANK1VOCATIONID_FIELD.has_default_value = false
pb.SC_SOCIETY_JOIN_CLEARANCELEVELRANK1VOCATIONID_FIELD.default_value = 0
pb.SC_SOCIETY_JOIN_CLEARANCELEVELRANK1VOCATIONID_FIELD.type = 13
pb.SC_SOCIETY_JOIN_CLEARANCELEVELRANK1VOCATIONID_FIELD.cpp_type = 3

pb.SC_SOCIETY_JOIN_CLEARANCELEVELRANK1ECTYPEID_FIELD.name = "ClearanceLevelRank1EctypeID"
pb.SC_SOCIETY_JOIN_CLEARANCELEVELRANK1ECTYPEID_FIELD.full_name = ".SC_Society_Join.ClearanceLevelRank1EctypeID"
pb.SC_SOCIETY_JOIN_CLEARANCELEVELRANK1ECTYPEID_FIELD.number = 37
pb.SC_SOCIETY_JOIN_CLEARANCELEVELRANK1ECTYPEID_FIELD.index = 36
pb.SC_SOCIETY_JOIN_CLEARANCELEVELRANK1ECTYPEID_FIELD.label = 1
pb.SC_SOCIETY_JOIN_CLEARANCELEVELRANK1ECTYPEID_FIELD.has_default_value = false
pb.SC_SOCIETY_JOIN_CLEARANCELEVELRANK1ECTYPEID_FIELD.default_value = 0
pb.SC_SOCIETY_JOIN_CLEARANCELEVELRANK1ECTYPEID_FIELD.type = 13
pb.SC_SOCIETY_JOIN_CLEARANCELEVELRANK1ECTYPEID_FIELD.cpp_type = 3

pb.SC_SOCIETY_JOIN_CLEARANCEECTYPEID_FIELD.name = "ClearanceEctypeID"
pb.SC_SOCIETY_JOIN_CLEARANCEECTYPEID_FIELD.full_name = ".SC_Society_Join.ClearanceEctypeID"
pb.SC_SOCIETY_JOIN_CLEARANCEECTYPEID_FIELD.number = 38
pb.SC_SOCIETY_JOIN_CLEARANCEECTYPEID_FIELD.index = 37
pb.SC_SOCIETY_JOIN_CLEARANCEECTYPEID_FIELD.label = 1
pb.SC_SOCIETY_JOIN_CLEARANCEECTYPEID_FIELD.has_default_value = false
pb.SC_SOCIETY_JOIN_CLEARANCEECTYPEID_FIELD.default_value = 0
pb.SC_SOCIETY_JOIN_CLEARANCEECTYPEID_FIELD.type = 13
pb.SC_SOCIETY_JOIN_CLEARANCEECTYPEID_FIELD.cpp_type = 3

pb.SC_SOCIETY_JOIN_CLEARANCETIME_FIELD.name = "ClearanceTime"
pb.SC_SOCIETY_JOIN_CLEARANCETIME_FIELD.full_name = ".SC_Society_Join.ClearanceTime"
pb.SC_SOCIETY_JOIN_CLEARANCETIME_FIELD.number = 39
pb.SC_SOCIETY_JOIN_CLEARANCETIME_FIELD.index = 38
pb.SC_SOCIETY_JOIN_CLEARANCETIME_FIELD.label = 1
pb.SC_SOCIETY_JOIN_CLEARANCETIME_FIELD.has_default_value = false
pb.SC_SOCIETY_JOIN_CLEARANCETIME_FIELD.default_value = 0
pb.SC_SOCIETY_JOIN_CLEARANCETIME_FIELD.type = 13
pb.SC_SOCIETY_JOIN_CLEARANCETIME_FIELD.cpp_type = 3

pb.SC_SOCIETY_JOIN_ALCHEMYTYPE_FIELD.name = "AlchemyType"
pb.SC_SOCIETY_JOIN_ALCHEMYTYPE_FIELD.full_name = ".SC_Society_Join.AlchemyType"
pb.SC_SOCIETY_JOIN_ALCHEMYTYPE_FIELD.number = 40
pb.SC_SOCIETY_JOIN_ALCHEMYTYPE_FIELD.index = 39
pb.SC_SOCIETY_JOIN_ALCHEMYTYPE_FIELD.label = 1
pb.SC_SOCIETY_JOIN_ALCHEMYTYPE_FIELD.has_default_value = false
pb.SC_SOCIETY_JOIN_ALCHEMYTYPE_FIELD.default_value = 0
pb.SC_SOCIETY_JOIN_ALCHEMYTYPE_FIELD.type = 13
pb.SC_SOCIETY_JOIN_ALCHEMYTYPE_FIELD.cpp_type = 3

pb.SC_SOCIETY_JOIN_FURNACELEVEL_FIELD.name = "FurnaceLevel"
pb.SC_SOCIETY_JOIN_FURNACELEVEL_FIELD.full_name = ".SC_Society_Join.FurnaceLevel"
pb.SC_SOCIETY_JOIN_FURNACELEVEL_FIELD.number = 41
pb.SC_SOCIETY_JOIN_FURNACELEVEL_FIELD.index = 40
pb.SC_SOCIETY_JOIN_FURNACELEVEL_FIELD.label = 1
pb.SC_SOCIETY_JOIN_FURNACELEVEL_FIELD.has_default_value = false
pb.SC_SOCIETY_JOIN_FURNACELEVEL_FIELD.default_value = 0
pb.SC_SOCIETY_JOIN_FURNACELEVEL_FIELD.type = 13
pb.SC_SOCIETY_JOIN_FURNACELEVEL_FIELD.cpp_type = 3

pb.SC_SOCIETY_JOIN_CHANGEALCHEMYTYPETIMES_FIELD.name = "ChangeAlchemyTypeTimes"
pb.SC_SOCIETY_JOIN_CHANGEALCHEMYTYPETIMES_FIELD.full_name = ".SC_Society_Join.ChangeAlchemyTypeTimes"
pb.SC_SOCIETY_JOIN_CHANGEALCHEMYTYPETIMES_FIELD.number = 42
pb.SC_SOCIETY_JOIN_CHANGEALCHEMYTYPETIMES_FIELD.index = 41
pb.SC_SOCIETY_JOIN_CHANGEALCHEMYTYPETIMES_FIELD.label = 1
pb.SC_SOCIETY_JOIN_CHANGEALCHEMYTYPETIMES_FIELD.has_default_value = false
pb.SC_SOCIETY_JOIN_CHANGEALCHEMYTYPETIMES_FIELD.default_value = 0
pb.SC_SOCIETY_JOIN_CHANGEALCHEMYTYPETIMES_FIELD.type = 13
pb.SC_SOCIETY_JOIN_CHANGEALCHEMYTYPETIMES_FIELD.cpp_type = 3

pb.SC_SOCIETY_JOIN_POWER_FIELD.name = "Power"
pb.SC_SOCIETY_JOIN_POWER_FIELD.full_name = ".SC_Society_Join.Power"
pb.SC_SOCIETY_JOIN_POWER_FIELD.number = 43
pb.SC_SOCIETY_JOIN_POWER_FIELD.index = 42
pb.SC_SOCIETY_JOIN_POWER_FIELD.label = 1
pb.SC_SOCIETY_JOIN_POWER_FIELD.has_default_value = false
pb.SC_SOCIETY_JOIN_POWER_FIELD.default_value = 0
pb.SC_SOCIETY_JOIN_POWER_FIELD.type = 13
pb.SC_SOCIETY_JOIN_POWER_FIELD.cpp_type = 3

pb.SC_SOCIETY_JOIN.name = "SC_Society_Join"
pb.SC_SOCIETY_JOIN.full_name = ".SC_Society_Join"
pb.SC_SOCIETY_JOIN.nested_types = {}
pb.SC_SOCIETY_JOIN.enum_types = {}
pb.SC_SOCIETY_JOIN.fields = {pb.SC_SOCIETY_JOIN_SOCIETYID_FIELD, pb.SC_SOCIETY_JOIN_SOCIETYNAME_FIELD, pb.SC_SOCIETY_JOIN_CHIEFID_FIELD, pb.SC_SOCIETY_JOIN_SOCIETYLEVEL_FIELD, pb.SC_SOCIETY_JOIN_SOCIETYEXP_FIELD, pb.SC_SOCIETY_JOIN_VITALITY_FIELD, pb.SC_SOCIETY_JOIN_MEMBERNUM_FIELD, pb.SC_SOCIETY_JOIN_FEASTLEAVE_FIELD, pb.SC_SOCIETY_JOIN_FEASTADD_FIELD, pb.SC_SOCIETY_JOIN_LASTFEAST_FIELD, pb.SC_SOCIETY_JOIN_CHIEFLOGOUTTIME_FIELD, pb.SC_SOCIETY_JOIN_SOCIETYINFO_FIELD, pb.SC_SOCIETY_JOIN_LEVELLIMIT_FIELD, pb.SC_SOCIETY_JOIN_JOINLIMIT_FIELD, pb.SC_SOCIETY_JOIN_FEASTACTOR_FIELD, pb.SC_SOCIETY_JOIN_FEASTACTORNAME_FIELD, pb.SC_SOCIETY_JOIN_RESULT_FIELD, pb.SC_SOCIETY_JOIN_BUYREVIVE_FIELD, pb.SC_SOCIETY_JOIN_REVIVE_FIELD, pb.SC_SOCIETY_JOIN_PRACTICECOUNT_FIELD, pb.SC_SOCIETY_JOIN_PRACTICETIME_FIELD, pb.SC_SOCIETY_JOIN_COUNTRY_FIELD, pb.SC_SOCIETY_JOIN_PRACTICENOTICE_FIELD, pb.SC_SOCIETY_JOIN_FEASTNOTICE_FIELD, pb.SC_SOCIETY_JOIN_CONTRIBUTIONNOTICE_FIELD, pb.SC_SOCIETY_JOIN_PRIZELIMIT_FIELD, pb.SC_SOCIETY_JOIN_POWERLIMIT_FIELD, pb.SC_SOCIETY_JOIN_FOUND_FIELD, pb.SC_SOCIETY_JOIN_CONSTRUCTION_FIELD, pb.SC_SOCIETY_JOIN_BUILDTIMEEND_FIELD, pb.SC_SOCIETY_JOIN_CLEARANCELEVEL5_FIELD, pb.SC_SOCIETY_JOIN_CLEARANCELEVEL10_FIELD, pb.SC_SOCIETY_JOIN_CLEARANCELEVEL15_FIELD, pb.SC_SOCIETY_JOIN_CLEARANCELEVEL20_FIELD, pb.SC_SOCIETY_JOIN_CLEARANCELEVELRANK1ACTORNAME_FIELD, pb.SC_SOCIETY_JOIN_CLEARANCELEVELRANK1VOCATIONID_FIELD, pb.SC_SOCIETY_JOIN_CLEARANCELEVELRANK1ECTYPEID_FIELD, pb.SC_SOCIETY_JOIN_CLEARANCEECTYPEID_FIELD, pb.SC_SOCIETY_JOIN_CLEARANCETIME_FIELD, pb.SC_SOCIETY_JOIN_ALCHEMYTYPE_FIELD, pb.SC_SOCIETY_JOIN_FURNACELEVEL_FIELD, pb.SC_SOCIETY_JOIN_CHANGEALCHEMYTYPETIMES_FIELD, pb.SC_SOCIETY_JOIN_POWER_FIELD}
pb.SC_SOCIETY_JOIN.is_extendable = false
pb.SC_SOCIETY_JOIN.extensions = {}
pb.CS_SOCIETY_LEAVE_SOCIETYID_FIELD.name = "SocietyID"
pb.CS_SOCIETY_LEAVE_SOCIETYID_FIELD.full_name = ".CS_Society_Leave.SocietyID"
pb.CS_SOCIETY_LEAVE_SOCIETYID_FIELD.number = 1
pb.CS_SOCIETY_LEAVE_SOCIETYID_FIELD.index = 0
pb.CS_SOCIETY_LEAVE_SOCIETYID_FIELD.label = 2
pb.CS_SOCIETY_LEAVE_SOCIETYID_FIELD.has_default_value = false
pb.CS_SOCIETY_LEAVE_SOCIETYID_FIELD.default_value = 0
pb.CS_SOCIETY_LEAVE_SOCIETYID_FIELD.type = 13
pb.CS_SOCIETY_LEAVE_SOCIETYID_FIELD.cpp_type = 3

pb.CS_SOCIETY_LEAVE.name = "CS_Society_Leave"
pb.CS_SOCIETY_LEAVE.full_name = ".CS_Society_Leave"
pb.CS_SOCIETY_LEAVE.nested_types = {}
pb.CS_SOCIETY_LEAVE.enum_types = {}
pb.CS_SOCIETY_LEAVE.fields = {pb.CS_SOCIETY_LEAVE_SOCIETYID_FIELD}
pb.CS_SOCIETY_LEAVE.is_extendable = false
pb.CS_SOCIETY_LEAVE.extensions = {}
pb.SC_SOCIETY_LEAVE_SOCIETYID_FIELD.name = "SocietyID"
pb.SC_SOCIETY_LEAVE_SOCIETYID_FIELD.full_name = ".SC_Society_Leave.SocietyID"
pb.SC_SOCIETY_LEAVE_SOCIETYID_FIELD.number = 1
pb.SC_SOCIETY_LEAVE_SOCIETYID_FIELD.index = 0
pb.SC_SOCIETY_LEAVE_SOCIETYID_FIELD.label = 1
pb.SC_SOCIETY_LEAVE_SOCIETYID_FIELD.has_default_value = false
pb.SC_SOCIETY_LEAVE_SOCIETYID_FIELD.default_value = 0
pb.SC_SOCIETY_LEAVE_SOCIETYID_FIELD.type = 13
pb.SC_SOCIETY_LEAVE_SOCIETYID_FIELD.cpp_type = 3

pb.SC_SOCIETY_LEAVE_RESULT_FIELD.name = "Result"
pb.SC_SOCIETY_LEAVE_RESULT_FIELD.full_name = ".SC_Society_Leave.Result"
pb.SC_SOCIETY_LEAVE_RESULT_FIELD.number = 2
pb.SC_SOCIETY_LEAVE_RESULT_FIELD.index = 1
pb.SC_SOCIETY_LEAVE_RESULT_FIELD.label = 1
pb.SC_SOCIETY_LEAVE_RESULT_FIELD.has_default_value = false
pb.SC_SOCIETY_LEAVE_RESULT_FIELD.default_value = 0
pb.SC_SOCIETY_LEAVE_RESULT_FIELD.type = 13
pb.SC_SOCIETY_LEAVE_RESULT_FIELD.cpp_type = 3

pb.SC_SOCIETY_LEAVE_SOCIETYNAME_FIELD.name = "SocietyName"
pb.SC_SOCIETY_LEAVE_SOCIETYNAME_FIELD.full_name = ".SC_Society_Leave.SocietyName"
pb.SC_SOCIETY_LEAVE_SOCIETYNAME_FIELD.number = 3
pb.SC_SOCIETY_LEAVE_SOCIETYNAME_FIELD.index = 2
pb.SC_SOCIETY_LEAVE_SOCIETYNAME_FIELD.label = 1
pb.SC_SOCIETY_LEAVE_SOCIETYNAME_FIELD.has_default_value = false
pb.SC_SOCIETY_LEAVE_SOCIETYNAME_FIELD.default_value = ""
pb.SC_SOCIETY_LEAVE_SOCIETYNAME_FIELD.type = 9
pb.SC_SOCIETY_LEAVE_SOCIETYNAME_FIELD.cpp_type = 9

pb.SC_SOCIETY_LEAVE.name = "SC_Society_Leave"
pb.SC_SOCIETY_LEAVE.full_name = ".SC_Society_Leave"
pb.SC_SOCIETY_LEAVE.nested_types = {}
pb.SC_SOCIETY_LEAVE.enum_types = {}
pb.SC_SOCIETY_LEAVE.fields = {pb.SC_SOCIETY_LEAVE_SOCIETYID_FIELD, pb.SC_SOCIETY_LEAVE_RESULT_FIELD, pb.SC_SOCIETY_LEAVE_SOCIETYNAME_FIELD}
pb.SC_SOCIETY_LEAVE.is_extendable = false
pb.SC_SOCIETY_LEAVE.extensions = {}
pb.CS_SOCIETY_KICK_SOCIETYID_FIELD.name = "SocietyID"
pb.CS_SOCIETY_KICK_SOCIETYID_FIELD.full_name = ".CS_Society_Kick.SocietyID"
pb.CS_SOCIETY_KICK_SOCIETYID_FIELD.number = 1
pb.CS_SOCIETY_KICK_SOCIETYID_FIELD.index = 0
pb.CS_SOCIETY_KICK_SOCIETYID_FIELD.label = 2
pb.CS_SOCIETY_KICK_SOCIETYID_FIELD.has_default_value = false
pb.CS_SOCIETY_KICK_SOCIETYID_FIELD.default_value = 0
pb.CS_SOCIETY_KICK_SOCIETYID_FIELD.type = 13
pb.CS_SOCIETY_KICK_SOCIETYID_FIELD.cpp_type = 3

pb.CS_SOCIETY_KICK_KICKACTORID_FIELD.name = "KickActorID"
pb.CS_SOCIETY_KICK_KICKACTORID_FIELD.full_name = ".CS_Society_Kick.KickActorID"
pb.CS_SOCIETY_KICK_KICKACTORID_FIELD.number = 2
pb.CS_SOCIETY_KICK_KICKACTORID_FIELD.index = 1
pb.CS_SOCIETY_KICK_KICKACTORID_FIELD.label = 2
pb.CS_SOCIETY_KICK_KICKACTORID_FIELD.has_default_value = false
pb.CS_SOCIETY_KICK_KICKACTORID_FIELD.default_value = 0
pb.CS_SOCIETY_KICK_KICKACTORID_FIELD.type = 13
pb.CS_SOCIETY_KICK_KICKACTORID_FIELD.cpp_type = 3

pb.CS_SOCIETY_KICK.name = "CS_Society_Kick"
pb.CS_SOCIETY_KICK.full_name = ".CS_Society_Kick"
pb.CS_SOCIETY_KICK.nested_types = {}
pb.CS_SOCIETY_KICK.enum_types = {}
pb.CS_SOCIETY_KICK.fields = {pb.CS_SOCIETY_KICK_SOCIETYID_FIELD, pb.CS_SOCIETY_KICK_KICKACTORID_FIELD}
pb.CS_SOCIETY_KICK.is_extendable = false
pb.CS_SOCIETY_KICK.extensions = {}
pb.SC_SOCIETY_KICK_ACTORID_FIELD.name = "ActorID"
pb.SC_SOCIETY_KICK_ACTORID_FIELD.full_name = ".SC_Society_Kick.ActorID"
pb.SC_SOCIETY_KICK_ACTORID_FIELD.number = 1
pb.SC_SOCIETY_KICK_ACTORID_FIELD.index = 0
pb.SC_SOCIETY_KICK_ACTORID_FIELD.label = 1
pb.SC_SOCIETY_KICK_ACTORID_FIELD.has_default_value = false
pb.SC_SOCIETY_KICK_ACTORID_FIELD.default_value = 0
pb.SC_SOCIETY_KICK_ACTORID_FIELD.type = 13
pb.SC_SOCIETY_KICK_ACTORID_FIELD.cpp_type = 3

pb.SC_SOCIETY_KICK_SOCIETYID_FIELD.name = "SocietyID"
pb.SC_SOCIETY_KICK_SOCIETYID_FIELD.full_name = ".SC_Society_Kick.SocietyID"
pb.SC_SOCIETY_KICK_SOCIETYID_FIELD.number = 2
pb.SC_SOCIETY_KICK_SOCIETYID_FIELD.index = 1
pb.SC_SOCIETY_KICK_SOCIETYID_FIELD.label = 1
pb.SC_SOCIETY_KICK_SOCIETYID_FIELD.has_default_value = false
pb.SC_SOCIETY_KICK_SOCIETYID_FIELD.default_value = 0
pb.SC_SOCIETY_KICK_SOCIETYID_FIELD.type = 13
pb.SC_SOCIETY_KICK_SOCIETYID_FIELD.cpp_type = 3

pb.SC_SOCIETY_KICK_KICKACTORID_FIELD.name = "KickActorID"
pb.SC_SOCIETY_KICK_KICKACTORID_FIELD.full_name = ".SC_Society_Kick.KickActorID"
pb.SC_SOCIETY_KICK_KICKACTORID_FIELD.number = 3
pb.SC_SOCIETY_KICK_KICKACTORID_FIELD.index = 2
pb.SC_SOCIETY_KICK_KICKACTORID_FIELD.label = 1
pb.SC_SOCIETY_KICK_KICKACTORID_FIELD.has_default_value = false
pb.SC_SOCIETY_KICK_KICKACTORID_FIELD.default_value = 0
pb.SC_SOCIETY_KICK_KICKACTORID_FIELD.type = 13
pb.SC_SOCIETY_KICK_KICKACTORID_FIELD.cpp_type = 3

pb.SC_SOCIETY_KICK_RESULT_FIELD.name = "Result"
pb.SC_SOCIETY_KICK_RESULT_FIELD.full_name = ".SC_Society_Kick.Result"
pb.SC_SOCIETY_KICK_RESULT_FIELD.number = 4
pb.SC_SOCIETY_KICK_RESULT_FIELD.index = 3
pb.SC_SOCIETY_KICK_RESULT_FIELD.label = 1
pb.SC_SOCIETY_KICK_RESULT_FIELD.has_default_value = false
pb.SC_SOCIETY_KICK_RESULT_FIELD.default_value = 0
pb.SC_SOCIETY_KICK_RESULT_FIELD.type = 13
pb.SC_SOCIETY_KICK_RESULT_FIELD.cpp_type = 3

pb.SC_SOCIETY_KICK.name = "SC_Society_Kick"
pb.SC_SOCIETY_KICK.full_name = ".SC_Society_Kick"
pb.SC_SOCIETY_KICK.nested_types = {}
pb.SC_SOCIETY_KICK.enum_types = {}
pb.SC_SOCIETY_KICK.fields = {pb.SC_SOCIETY_KICK_ACTORID_FIELD, pb.SC_SOCIETY_KICK_SOCIETYID_FIELD, pb.SC_SOCIETY_KICK_KICKACTORID_FIELD, pb.SC_SOCIETY_KICK_RESULT_FIELD}
pb.SC_SOCIETY_KICK.is_extendable = false
pb.SC_SOCIETY_KICK.extensions = {}
pb.CS_SOCIETY_APPOINT_SOCIETYID_FIELD.name = "SocietyID"
pb.CS_SOCIETY_APPOINT_SOCIETYID_FIELD.full_name = ".CS_Society_Appoint.SocietyID"
pb.CS_SOCIETY_APPOINT_SOCIETYID_FIELD.number = 1
pb.CS_SOCIETY_APPOINT_SOCIETYID_FIELD.index = 0
pb.CS_SOCIETY_APPOINT_SOCIETYID_FIELD.label = 2
pb.CS_SOCIETY_APPOINT_SOCIETYID_FIELD.has_default_value = false
pb.CS_SOCIETY_APPOINT_SOCIETYID_FIELD.default_value = 0
pb.CS_SOCIETY_APPOINT_SOCIETYID_FIELD.type = 13
pb.CS_SOCIETY_APPOINT_SOCIETYID_FIELD.cpp_type = 3

pb.CS_SOCIETY_APPOINT_APPOINTACTORID_FIELD.name = "AppointActorID"
pb.CS_SOCIETY_APPOINT_APPOINTACTORID_FIELD.full_name = ".CS_Society_Appoint.AppointActorID"
pb.CS_SOCIETY_APPOINT_APPOINTACTORID_FIELD.number = 2
pb.CS_SOCIETY_APPOINT_APPOINTACTORID_FIELD.index = 1
pb.CS_SOCIETY_APPOINT_APPOINTACTORID_FIELD.label = 2
pb.CS_SOCIETY_APPOINT_APPOINTACTORID_FIELD.has_default_value = false
pb.CS_SOCIETY_APPOINT_APPOINTACTORID_FIELD.default_value = 0
pb.CS_SOCIETY_APPOINT_APPOINTACTORID_FIELD.type = 13
pb.CS_SOCIETY_APPOINT_APPOINTACTORID_FIELD.cpp_type = 3

pb.CS_SOCIETY_APPOINT_TITLE_FIELD.name = "Title"
pb.CS_SOCIETY_APPOINT_TITLE_FIELD.full_name = ".CS_Society_Appoint.Title"
pb.CS_SOCIETY_APPOINT_TITLE_FIELD.number = 3
pb.CS_SOCIETY_APPOINT_TITLE_FIELD.index = 2
pb.CS_SOCIETY_APPOINT_TITLE_FIELD.label = 2
pb.CS_SOCIETY_APPOINT_TITLE_FIELD.has_default_value = false
pb.CS_SOCIETY_APPOINT_TITLE_FIELD.default_value = 0
pb.CS_SOCIETY_APPOINT_TITLE_FIELD.type = 13
pb.CS_SOCIETY_APPOINT_TITLE_FIELD.cpp_type = 3

pb.CS_SOCIETY_APPOINT.name = "CS_Society_Appoint"
pb.CS_SOCIETY_APPOINT.full_name = ".CS_Society_Appoint"
pb.CS_SOCIETY_APPOINT.nested_types = {}
pb.CS_SOCIETY_APPOINT.enum_types = {}
pb.CS_SOCIETY_APPOINT.fields = {pb.CS_SOCIETY_APPOINT_SOCIETYID_FIELD, pb.CS_SOCIETY_APPOINT_APPOINTACTORID_FIELD, pb.CS_SOCIETY_APPOINT_TITLE_FIELD}
pb.CS_SOCIETY_APPOINT.is_extendable = false
pb.CS_SOCIETY_APPOINT.extensions = {}
pb.SC_SOCIETY_APPOINT_ACTORID_FIELD.name = "ActorID"
pb.SC_SOCIETY_APPOINT_ACTORID_FIELD.full_name = ".SC_Society_Appoint.ActorID"
pb.SC_SOCIETY_APPOINT_ACTORID_FIELD.number = 1
pb.SC_SOCIETY_APPOINT_ACTORID_FIELD.index = 0
pb.SC_SOCIETY_APPOINT_ACTORID_FIELD.label = 1
pb.SC_SOCIETY_APPOINT_ACTORID_FIELD.has_default_value = false
pb.SC_SOCIETY_APPOINT_ACTORID_FIELD.default_value = 0
pb.SC_SOCIETY_APPOINT_ACTORID_FIELD.type = 13
pb.SC_SOCIETY_APPOINT_ACTORID_FIELD.cpp_type = 3

pb.SC_SOCIETY_APPOINT_SOCIETYID_FIELD.name = "SocietyID"
pb.SC_SOCIETY_APPOINT_SOCIETYID_FIELD.full_name = ".SC_Society_Appoint.SocietyID"
pb.SC_SOCIETY_APPOINT_SOCIETYID_FIELD.number = 2
pb.SC_SOCIETY_APPOINT_SOCIETYID_FIELD.index = 1
pb.SC_SOCIETY_APPOINT_SOCIETYID_FIELD.label = 1
pb.SC_SOCIETY_APPOINT_SOCIETYID_FIELD.has_default_value = false
pb.SC_SOCIETY_APPOINT_SOCIETYID_FIELD.default_value = 0
pb.SC_SOCIETY_APPOINT_SOCIETYID_FIELD.type = 13
pb.SC_SOCIETY_APPOINT_SOCIETYID_FIELD.cpp_type = 3

pb.SC_SOCIETY_APPOINT_APPOINTACTORID_FIELD.name = "AppointActorID"
pb.SC_SOCIETY_APPOINT_APPOINTACTORID_FIELD.full_name = ".SC_Society_Appoint.AppointActorID"
pb.SC_SOCIETY_APPOINT_APPOINTACTORID_FIELD.number = 3
pb.SC_SOCIETY_APPOINT_APPOINTACTORID_FIELD.index = 2
pb.SC_SOCIETY_APPOINT_APPOINTACTORID_FIELD.label = 1
pb.SC_SOCIETY_APPOINT_APPOINTACTORID_FIELD.has_default_value = false
pb.SC_SOCIETY_APPOINT_APPOINTACTORID_FIELD.default_value = 0
pb.SC_SOCIETY_APPOINT_APPOINTACTORID_FIELD.type = 13
pb.SC_SOCIETY_APPOINT_APPOINTACTORID_FIELD.cpp_type = 3

pb.SC_SOCIETY_APPOINT_TITLE_FIELD.name = "Title"
pb.SC_SOCIETY_APPOINT_TITLE_FIELD.full_name = ".SC_Society_Appoint.Title"
pb.SC_SOCIETY_APPOINT_TITLE_FIELD.number = 4
pb.SC_SOCIETY_APPOINT_TITLE_FIELD.index = 3
pb.SC_SOCIETY_APPOINT_TITLE_FIELD.label = 1
pb.SC_SOCIETY_APPOINT_TITLE_FIELD.has_default_value = false
pb.SC_SOCIETY_APPOINT_TITLE_FIELD.default_value = 0
pb.SC_SOCIETY_APPOINT_TITLE_FIELD.type = 13
pb.SC_SOCIETY_APPOINT_TITLE_FIELD.cpp_type = 3

pb.SC_SOCIETY_APPOINT_RESULT_FIELD.name = "Result"
pb.SC_SOCIETY_APPOINT_RESULT_FIELD.full_name = ".SC_Society_Appoint.Result"
pb.SC_SOCIETY_APPOINT_RESULT_FIELD.number = 5
pb.SC_SOCIETY_APPOINT_RESULT_FIELD.index = 4
pb.SC_SOCIETY_APPOINT_RESULT_FIELD.label = 1
pb.SC_SOCIETY_APPOINT_RESULT_FIELD.has_default_value = false
pb.SC_SOCIETY_APPOINT_RESULT_FIELD.default_value = 0
pb.SC_SOCIETY_APPOINT_RESULT_FIELD.type = 13
pb.SC_SOCIETY_APPOINT_RESULT_FIELD.cpp_type = 3

pb.SC_SOCIETY_APPOINT.name = "SC_Society_Appoint"
pb.SC_SOCIETY_APPOINT.full_name = ".SC_Society_Appoint"
pb.SC_SOCIETY_APPOINT.nested_types = {}
pb.SC_SOCIETY_APPOINT.enum_types = {}
pb.SC_SOCIETY_APPOINT.fields = {pb.SC_SOCIETY_APPOINT_ACTORID_FIELD, pb.SC_SOCIETY_APPOINT_SOCIETYID_FIELD, pb.SC_SOCIETY_APPOINT_APPOINTACTORID_FIELD, pb.SC_SOCIETY_APPOINT_TITLE_FIELD, pb.SC_SOCIETY_APPOINT_RESULT_FIELD}
pb.SC_SOCIETY_APPOINT.is_extendable = false
pb.SC_SOCIETY_APPOINT.extensions = {}
pb.CS_SOCIETY_SUCCEED_SOCIETYID_FIELD.name = "SocietyID"
pb.CS_SOCIETY_SUCCEED_SOCIETYID_FIELD.full_name = ".CS_Society_Succeed.SocietyID"
pb.CS_SOCIETY_SUCCEED_SOCIETYID_FIELD.number = 1
pb.CS_SOCIETY_SUCCEED_SOCIETYID_FIELD.index = 0
pb.CS_SOCIETY_SUCCEED_SOCIETYID_FIELD.label = 2
pb.CS_SOCIETY_SUCCEED_SOCIETYID_FIELD.has_default_value = false
pb.CS_SOCIETY_SUCCEED_SOCIETYID_FIELD.default_value = 0
pb.CS_SOCIETY_SUCCEED_SOCIETYID_FIELD.type = 13
pb.CS_SOCIETY_SUCCEED_SOCIETYID_FIELD.cpp_type = 3

pb.CS_SOCIETY_SUCCEED.name = "CS_Society_Succeed"
pb.CS_SOCIETY_SUCCEED.full_name = ".CS_Society_Succeed"
pb.CS_SOCIETY_SUCCEED.nested_types = {}
pb.CS_SOCIETY_SUCCEED.enum_types = {}
pb.CS_SOCIETY_SUCCEED.fields = {pb.CS_SOCIETY_SUCCEED_SOCIETYID_FIELD}
pb.CS_SOCIETY_SUCCEED.is_extendable = false
pb.CS_SOCIETY_SUCCEED.extensions = {}
pb.SC_SOCIETY_SUCCEED_ACTORID_FIELD.name = "ActorID"
pb.SC_SOCIETY_SUCCEED_ACTORID_FIELD.full_name = ".SC_Society_Succeed.ActorID"
pb.SC_SOCIETY_SUCCEED_ACTORID_FIELD.number = 1
pb.SC_SOCIETY_SUCCEED_ACTORID_FIELD.index = 0
pb.SC_SOCIETY_SUCCEED_ACTORID_FIELD.label = 1
pb.SC_SOCIETY_SUCCEED_ACTORID_FIELD.has_default_value = false
pb.SC_SOCIETY_SUCCEED_ACTORID_FIELD.default_value = 0
pb.SC_SOCIETY_SUCCEED_ACTORID_FIELD.type = 13
pb.SC_SOCIETY_SUCCEED_ACTORID_FIELD.cpp_type = 3

pb.SC_SOCIETY_SUCCEED_SOCIETYID_FIELD.name = "SocietyID"
pb.SC_SOCIETY_SUCCEED_SOCIETYID_FIELD.full_name = ".SC_Society_Succeed.SocietyID"
pb.SC_SOCIETY_SUCCEED_SOCIETYID_FIELD.number = 2
pb.SC_SOCIETY_SUCCEED_SOCIETYID_FIELD.index = 1
pb.SC_SOCIETY_SUCCEED_SOCIETYID_FIELD.label = 1
pb.SC_SOCIETY_SUCCEED_SOCIETYID_FIELD.has_default_value = false
pb.SC_SOCIETY_SUCCEED_SOCIETYID_FIELD.default_value = 0
pb.SC_SOCIETY_SUCCEED_SOCIETYID_FIELD.type = 13
pb.SC_SOCIETY_SUCCEED_SOCIETYID_FIELD.cpp_type = 3

pb.SC_SOCIETY_SUCCEED_PRECHIEFID_FIELD.name = "PreChiefID"
pb.SC_SOCIETY_SUCCEED_PRECHIEFID_FIELD.full_name = ".SC_Society_Succeed.PreChiefID"
pb.SC_SOCIETY_SUCCEED_PRECHIEFID_FIELD.number = 3
pb.SC_SOCIETY_SUCCEED_PRECHIEFID_FIELD.index = 2
pb.SC_SOCIETY_SUCCEED_PRECHIEFID_FIELD.label = 1
pb.SC_SOCIETY_SUCCEED_PRECHIEFID_FIELD.has_default_value = false
pb.SC_SOCIETY_SUCCEED_PRECHIEFID_FIELD.default_value = 0
pb.SC_SOCIETY_SUCCEED_PRECHIEFID_FIELD.type = 13
pb.SC_SOCIETY_SUCCEED_PRECHIEFID_FIELD.cpp_type = 3

pb.SC_SOCIETY_SUCCEED_RESULT_FIELD.name = "Result"
pb.SC_SOCIETY_SUCCEED_RESULT_FIELD.full_name = ".SC_Society_Succeed.Result"
pb.SC_SOCIETY_SUCCEED_RESULT_FIELD.number = 4
pb.SC_SOCIETY_SUCCEED_RESULT_FIELD.index = 3
pb.SC_SOCIETY_SUCCEED_RESULT_FIELD.label = 1
pb.SC_SOCIETY_SUCCEED_RESULT_FIELD.has_default_value = false
pb.SC_SOCIETY_SUCCEED_RESULT_FIELD.default_value = 0
pb.SC_SOCIETY_SUCCEED_RESULT_FIELD.type = 13
pb.SC_SOCIETY_SUCCEED_RESULT_FIELD.cpp_type = 3

pb.SC_SOCIETY_SUCCEED.name = "SC_Society_Succeed"
pb.SC_SOCIETY_SUCCEED.full_name = ".SC_Society_Succeed"
pb.SC_SOCIETY_SUCCEED.nested_types = {}
pb.SC_SOCIETY_SUCCEED.enum_types = {}
pb.SC_SOCIETY_SUCCEED.fields = {pb.SC_SOCIETY_SUCCEED_ACTORID_FIELD, pb.SC_SOCIETY_SUCCEED_SOCIETYID_FIELD, pb.SC_SOCIETY_SUCCEED_PRECHIEFID_FIELD, pb.SC_SOCIETY_SUCCEED_RESULT_FIELD}
pb.SC_SOCIETY_SUCCEED.is_extendable = false
pb.SC_SOCIETY_SUCCEED.extensions = {}
pb.CS_SOCIETY_GETLIST_BEGINSOCIETYID_FIELD.name = "BeginSocietyID"
pb.CS_SOCIETY_GETLIST_BEGINSOCIETYID_FIELD.full_name = ".CS_Society_GetList.BeginSocietyID"
pb.CS_SOCIETY_GETLIST_BEGINSOCIETYID_FIELD.number = 1
pb.CS_SOCIETY_GETLIST_BEGINSOCIETYID_FIELD.index = 0
pb.CS_SOCIETY_GETLIST_BEGINSOCIETYID_FIELD.label = 2
pb.CS_SOCIETY_GETLIST_BEGINSOCIETYID_FIELD.has_default_value = false
pb.CS_SOCIETY_GETLIST_BEGINSOCIETYID_FIELD.default_value = 0
pb.CS_SOCIETY_GETLIST_BEGINSOCIETYID_FIELD.type = 13
pb.CS_SOCIETY_GETLIST_BEGINSOCIETYID_FIELD.cpp_type = 3

pb.CS_SOCIETY_GETLIST.name = "CS_Society_GetList"
pb.CS_SOCIETY_GETLIST.full_name = ".CS_Society_GetList"
pb.CS_SOCIETY_GETLIST.nested_types = {}
pb.CS_SOCIETY_GETLIST.enum_types = {}
pb.CS_SOCIETY_GETLIST.fields = {pb.CS_SOCIETY_GETLIST_BEGINSOCIETYID_FIELD}
pb.CS_SOCIETY_GETLIST.is_extendable = false
pb.CS_SOCIETY_GETLIST.extensions = {}
pb.SC_SOCIETY_GETLIST_SOCIETYDATA_SOCIETYID_FIELD.name = "SocietyID"
pb.SC_SOCIETY_GETLIST_SOCIETYDATA_SOCIETYID_FIELD.full_name = ".SC_Society_GetList.SocietyData.SocietyID"
pb.SC_SOCIETY_GETLIST_SOCIETYDATA_SOCIETYID_FIELD.number = 1
pb.SC_SOCIETY_GETLIST_SOCIETYDATA_SOCIETYID_FIELD.index = 0
pb.SC_SOCIETY_GETLIST_SOCIETYDATA_SOCIETYID_FIELD.label = 1
pb.SC_SOCIETY_GETLIST_SOCIETYDATA_SOCIETYID_FIELD.has_default_value = false
pb.SC_SOCIETY_GETLIST_SOCIETYDATA_SOCIETYID_FIELD.default_value = 0
pb.SC_SOCIETY_GETLIST_SOCIETYDATA_SOCIETYID_FIELD.type = 13
pb.SC_SOCIETY_GETLIST_SOCIETYDATA_SOCIETYID_FIELD.cpp_type = 3

pb.SC_SOCIETY_GETLIST_SOCIETYDATA_SOCIETYNAME_FIELD.name = "SocietyName"
pb.SC_SOCIETY_GETLIST_SOCIETYDATA_SOCIETYNAME_FIELD.full_name = ".SC_Society_GetList.SocietyData.SocietyName"
pb.SC_SOCIETY_GETLIST_SOCIETYDATA_SOCIETYNAME_FIELD.number = 2
pb.SC_SOCIETY_GETLIST_SOCIETYDATA_SOCIETYNAME_FIELD.index = 1
pb.SC_SOCIETY_GETLIST_SOCIETYDATA_SOCIETYNAME_FIELD.label = 1
pb.SC_SOCIETY_GETLIST_SOCIETYDATA_SOCIETYNAME_FIELD.has_default_value = false
pb.SC_SOCIETY_GETLIST_SOCIETYDATA_SOCIETYNAME_FIELD.default_value = ""
pb.SC_SOCIETY_GETLIST_SOCIETYDATA_SOCIETYNAME_FIELD.type = 9
pb.SC_SOCIETY_GETLIST_SOCIETYDATA_SOCIETYNAME_FIELD.cpp_type = 9

pb.SC_SOCIETY_GETLIST_SOCIETYDATA_MEMBERNUM_FIELD.name = "MemberNum"
pb.SC_SOCIETY_GETLIST_SOCIETYDATA_MEMBERNUM_FIELD.full_name = ".SC_Society_GetList.SocietyData.MemberNum"
pb.SC_SOCIETY_GETLIST_SOCIETYDATA_MEMBERNUM_FIELD.number = 3
pb.SC_SOCIETY_GETLIST_SOCIETYDATA_MEMBERNUM_FIELD.index = 2
pb.SC_SOCIETY_GETLIST_SOCIETYDATA_MEMBERNUM_FIELD.label = 1
pb.SC_SOCIETY_GETLIST_SOCIETYDATA_MEMBERNUM_FIELD.has_default_value = false
pb.SC_SOCIETY_GETLIST_SOCIETYDATA_MEMBERNUM_FIELD.default_value = 0
pb.SC_SOCIETY_GETLIST_SOCIETYDATA_MEMBERNUM_FIELD.type = 13
pb.SC_SOCIETY_GETLIST_SOCIETYDATA_MEMBERNUM_FIELD.cpp_type = 3

pb.SC_SOCIETY_GETLIST_SOCIETYDATA_SOCIETYINFO_FIELD.name = "SocietyInfo"
pb.SC_SOCIETY_GETLIST_SOCIETYDATA_SOCIETYINFO_FIELD.full_name = ".SC_Society_GetList.SocietyData.SocietyInfo"
pb.SC_SOCIETY_GETLIST_SOCIETYDATA_SOCIETYINFO_FIELD.number = 4
pb.SC_SOCIETY_GETLIST_SOCIETYDATA_SOCIETYINFO_FIELD.index = 3
pb.SC_SOCIETY_GETLIST_SOCIETYDATA_SOCIETYINFO_FIELD.label = 1
pb.SC_SOCIETY_GETLIST_SOCIETYDATA_SOCIETYINFO_FIELD.has_default_value = false
pb.SC_SOCIETY_GETLIST_SOCIETYDATA_SOCIETYINFO_FIELD.default_value = ""
pb.SC_SOCIETY_GETLIST_SOCIETYDATA_SOCIETYINFO_FIELD.type = 9
pb.SC_SOCIETY_GETLIST_SOCIETYDATA_SOCIETYINFO_FIELD.cpp_type = 9

pb.SC_SOCIETY_GETLIST_SOCIETYDATA_LEVELLIMIT_FIELD.name = "LevelLimit"
pb.SC_SOCIETY_GETLIST_SOCIETYDATA_LEVELLIMIT_FIELD.full_name = ".SC_Society_GetList.SocietyData.LevelLimit"
pb.SC_SOCIETY_GETLIST_SOCIETYDATA_LEVELLIMIT_FIELD.number = 5
pb.SC_SOCIETY_GETLIST_SOCIETYDATA_LEVELLIMIT_FIELD.index = 4
pb.SC_SOCIETY_GETLIST_SOCIETYDATA_LEVELLIMIT_FIELD.label = 1
pb.SC_SOCIETY_GETLIST_SOCIETYDATA_LEVELLIMIT_FIELD.has_default_value = false
pb.SC_SOCIETY_GETLIST_SOCIETYDATA_LEVELLIMIT_FIELD.default_value = 0
pb.SC_SOCIETY_GETLIST_SOCIETYDATA_LEVELLIMIT_FIELD.type = 13
pb.SC_SOCIETY_GETLIST_SOCIETYDATA_LEVELLIMIT_FIELD.cpp_type = 3

pb.SC_SOCIETY_GETLIST_SOCIETYDATA_JOINLIMIT_FIELD.name = "JoinLimit"
pb.SC_SOCIETY_GETLIST_SOCIETYDATA_JOINLIMIT_FIELD.full_name = ".SC_Society_GetList.SocietyData.JoinLimit"
pb.SC_SOCIETY_GETLIST_SOCIETYDATA_JOINLIMIT_FIELD.number = 6
pb.SC_SOCIETY_GETLIST_SOCIETYDATA_JOINLIMIT_FIELD.index = 5
pb.SC_SOCIETY_GETLIST_SOCIETYDATA_JOINLIMIT_FIELD.label = 1
pb.SC_SOCIETY_GETLIST_SOCIETYDATA_JOINLIMIT_FIELD.has_default_value = false
pb.SC_SOCIETY_GETLIST_SOCIETYDATA_JOINLIMIT_FIELD.default_value = 0
pb.SC_SOCIETY_GETLIST_SOCIETYDATA_JOINLIMIT_FIELD.type = 13
pb.SC_SOCIETY_GETLIST_SOCIETYDATA_JOINLIMIT_FIELD.cpp_type = 3

pb.SC_SOCIETY_GETLIST_SOCIETYDATA_COUNTRY_FIELD.name = "Country"
pb.SC_SOCIETY_GETLIST_SOCIETYDATA_COUNTRY_FIELD.full_name = ".SC_Society_GetList.SocietyData.Country"
pb.SC_SOCIETY_GETLIST_SOCIETYDATA_COUNTRY_FIELD.number = 7
pb.SC_SOCIETY_GETLIST_SOCIETYDATA_COUNTRY_FIELD.index = 6
pb.SC_SOCIETY_GETLIST_SOCIETYDATA_COUNTRY_FIELD.label = 1
pb.SC_SOCIETY_GETLIST_SOCIETYDATA_COUNTRY_FIELD.has_default_value = false
pb.SC_SOCIETY_GETLIST_SOCIETYDATA_COUNTRY_FIELD.default_value = 0
pb.SC_SOCIETY_GETLIST_SOCIETYDATA_COUNTRY_FIELD.type = 13
pb.SC_SOCIETY_GETLIST_SOCIETYDATA_COUNTRY_FIELD.cpp_type = 3

pb.SC_SOCIETY_GETLIST_SOCIETYDATA_LEVEL_FIELD.name = "Level"
pb.SC_SOCIETY_GETLIST_SOCIETYDATA_LEVEL_FIELD.full_name = ".SC_Society_GetList.SocietyData.Level"
pb.SC_SOCIETY_GETLIST_SOCIETYDATA_LEVEL_FIELD.number = 8
pb.SC_SOCIETY_GETLIST_SOCIETYDATA_LEVEL_FIELD.index = 7
pb.SC_SOCIETY_GETLIST_SOCIETYDATA_LEVEL_FIELD.label = 1
pb.SC_SOCIETY_GETLIST_SOCIETYDATA_LEVEL_FIELD.has_default_value = false
pb.SC_SOCIETY_GETLIST_SOCIETYDATA_LEVEL_FIELD.default_value = 0
pb.SC_SOCIETY_GETLIST_SOCIETYDATA_LEVEL_FIELD.type = 13
pb.SC_SOCIETY_GETLIST_SOCIETYDATA_LEVEL_FIELD.cpp_type = 3

pb.SC_SOCIETY_GETLIST_SOCIETYDATA_POWER_FIELD.name = "Power"
pb.SC_SOCIETY_GETLIST_SOCIETYDATA_POWER_FIELD.full_name = ".SC_Society_GetList.SocietyData.Power"
pb.SC_SOCIETY_GETLIST_SOCIETYDATA_POWER_FIELD.number = 9
pb.SC_SOCIETY_GETLIST_SOCIETYDATA_POWER_FIELD.index = 8
pb.SC_SOCIETY_GETLIST_SOCIETYDATA_POWER_FIELD.label = 1
pb.SC_SOCIETY_GETLIST_SOCIETYDATA_POWER_FIELD.has_default_value = false
pb.SC_SOCIETY_GETLIST_SOCIETYDATA_POWER_FIELD.default_value = 0
pb.SC_SOCIETY_GETLIST_SOCIETYDATA_POWER_FIELD.type = 13
pb.SC_SOCIETY_GETLIST_SOCIETYDATA_POWER_FIELD.cpp_type = 3

pb.SC_SOCIETY_GETLIST_SOCIETYDATA.name = "SocietyData"
pb.SC_SOCIETY_GETLIST_SOCIETYDATA.full_name = ".SC_Society_GetList.SocietyData"
pb.SC_SOCIETY_GETLIST_SOCIETYDATA.nested_types = {}
pb.SC_SOCIETY_GETLIST_SOCIETYDATA.enum_types = {}
pb.SC_SOCIETY_GETLIST_SOCIETYDATA.fields = {pb.SC_SOCIETY_GETLIST_SOCIETYDATA_SOCIETYID_FIELD, pb.SC_SOCIETY_GETLIST_SOCIETYDATA_SOCIETYNAME_FIELD, pb.SC_SOCIETY_GETLIST_SOCIETYDATA_MEMBERNUM_FIELD, pb.SC_SOCIETY_GETLIST_SOCIETYDATA_SOCIETYINFO_FIELD, pb.SC_SOCIETY_GETLIST_SOCIETYDATA_LEVELLIMIT_FIELD, pb.SC_SOCIETY_GETLIST_SOCIETYDATA_JOINLIMIT_FIELD, pb.SC_SOCIETY_GETLIST_SOCIETYDATA_COUNTRY_FIELD, pb.SC_SOCIETY_GETLIST_SOCIETYDATA_LEVEL_FIELD, pb.SC_SOCIETY_GETLIST_SOCIETYDATA_POWER_FIELD}
pb.SC_SOCIETY_GETLIST_SOCIETYDATA.is_extendable = false
pb.SC_SOCIETY_GETLIST_SOCIETYDATA.extensions = {}
pb.SC_SOCIETY_GETLIST_SOCIETYDATA.containing_type = pb.SC_SOCIETY_GETLIST
pb.SC_SOCIETY_GETLIST_SOCIETYLIST_FIELD.name = "SocietyList"
pb.SC_SOCIETY_GETLIST_SOCIETYLIST_FIELD.full_name = ".SC_Society_GetList.SocietyList"
pb.SC_SOCIETY_GETLIST_SOCIETYLIST_FIELD.number = 1
pb.SC_SOCIETY_GETLIST_SOCIETYLIST_FIELD.index = 0
pb.SC_SOCIETY_GETLIST_SOCIETYLIST_FIELD.label = 3
pb.SC_SOCIETY_GETLIST_SOCIETYLIST_FIELD.has_default_value = false
pb.SC_SOCIETY_GETLIST_SOCIETYLIST_FIELD.default_value = {}
pb.SC_SOCIETY_GETLIST_SOCIETYLIST_FIELD.message_type = pb.SC_SOCIETY_GETLIST_SOCIETYDATA
pb.SC_SOCIETY_GETLIST_SOCIETYLIST_FIELD.type = 11
pb.SC_SOCIETY_GETLIST_SOCIETYLIST_FIELD.cpp_type = 10

pb.SC_SOCIETY_GETLIST_BEGINSOCIETYID_FIELD.name = "BeginSocietyID"
pb.SC_SOCIETY_GETLIST_BEGINSOCIETYID_FIELD.full_name = ".SC_Society_GetList.BeginSocietyID"
pb.SC_SOCIETY_GETLIST_BEGINSOCIETYID_FIELD.number = 2
pb.SC_SOCIETY_GETLIST_BEGINSOCIETYID_FIELD.index = 1
pb.SC_SOCIETY_GETLIST_BEGINSOCIETYID_FIELD.label = 1
pb.SC_SOCIETY_GETLIST_BEGINSOCIETYID_FIELD.has_default_value = false
pb.SC_SOCIETY_GETLIST_BEGINSOCIETYID_FIELD.default_value = 0
pb.SC_SOCIETY_GETLIST_BEGINSOCIETYID_FIELD.type = 13
pb.SC_SOCIETY_GETLIST_BEGINSOCIETYID_FIELD.cpp_type = 3

pb.SC_SOCIETY_GETLIST.name = "SC_Society_GetList"
pb.SC_SOCIETY_GETLIST.full_name = ".SC_Society_GetList"
pb.SC_SOCIETY_GETLIST.nested_types = {pb.SC_SOCIETY_GETLIST_SOCIETYDATA}
pb.SC_SOCIETY_GETLIST.enum_types = {}
pb.SC_SOCIETY_GETLIST.fields = {pb.SC_SOCIETY_GETLIST_SOCIETYLIST_FIELD, pb.SC_SOCIETY_GETLIST_BEGINSOCIETYID_FIELD}
pb.SC_SOCIETY_GETLIST.is_extendable = false
pb.SC_SOCIETY_GETLIST.extensions = {}
pb.CS_SOCIETY_FIND_SOCIETYNAME_FIELD.name = "SocietyName"
pb.CS_SOCIETY_FIND_SOCIETYNAME_FIELD.full_name = ".CS_Society_Find.SocietyName"
pb.CS_SOCIETY_FIND_SOCIETYNAME_FIELD.number = 1
pb.CS_SOCIETY_FIND_SOCIETYNAME_FIELD.index = 0
pb.CS_SOCIETY_FIND_SOCIETYNAME_FIELD.label = 2
pb.CS_SOCIETY_FIND_SOCIETYNAME_FIELD.has_default_value = false
pb.CS_SOCIETY_FIND_SOCIETYNAME_FIELD.default_value = ""
pb.CS_SOCIETY_FIND_SOCIETYNAME_FIELD.type = 9
pb.CS_SOCIETY_FIND_SOCIETYNAME_FIELD.cpp_type = 9

pb.CS_SOCIETY_FIND.name = "CS_Society_Find"
pb.CS_SOCIETY_FIND.full_name = ".CS_Society_Find"
pb.CS_SOCIETY_FIND.nested_types = {}
pb.CS_SOCIETY_FIND.enum_types = {}
pb.CS_SOCIETY_FIND.fields = {pb.CS_SOCIETY_FIND_SOCIETYNAME_FIELD}
pb.CS_SOCIETY_FIND.is_extendable = false
pb.CS_SOCIETY_FIND.extensions = {}
pb.SC_SOCIETY_FIND_SOCIETYID_FIELD.name = "SocietyID"
pb.SC_SOCIETY_FIND_SOCIETYID_FIELD.full_name = ".SC_Society_Find.SocietyID"
pb.SC_SOCIETY_FIND_SOCIETYID_FIELD.number = 1
pb.SC_SOCIETY_FIND_SOCIETYID_FIELD.index = 0
pb.SC_SOCIETY_FIND_SOCIETYID_FIELD.label = 1
pb.SC_SOCIETY_FIND_SOCIETYID_FIELD.has_default_value = false
pb.SC_SOCIETY_FIND_SOCIETYID_FIELD.default_value = 0
pb.SC_SOCIETY_FIND_SOCIETYID_FIELD.type = 13
pb.SC_SOCIETY_FIND_SOCIETYID_FIELD.cpp_type = 3

pb.SC_SOCIETY_FIND_SOCIETYNAME_FIELD.name = "SocietyName"
pb.SC_SOCIETY_FIND_SOCIETYNAME_FIELD.full_name = ".SC_Society_Find.SocietyName"
pb.SC_SOCIETY_FIND_SOCIETYNAME_FIELD.number = 2
pb.SC_SOCIETY_FIND_SOCIETYNAME_FIELD.index = 1
pb.SC_SOCIETY_FIND_SOCIETYNAME_FIELD.label = 1
pb.SC_SOCIETY_FIND_SOCIETYNAME_FIELD.has_default_value = false
pb.SC_SOCIETY_FIND_SOCIETYNAME_FIELD.default_value = ""
pb.SC_SOCIETY_FIND_SOCIETYNAME_FIELD.type = 9
pb.SC_SOCIETY_FIND_SOCIETYNAME_FIELD.cpp_type = 9

pb.SC_SOCIETY_FIND_MEMBERNUM_FIELD.name = "MemberNum"
pb.SC_SOCIETY_FIND_MEMBERNUM_FIELD.full_name = ".SC_Society_Find.MemberNum"
pb.SC_SOCIETY_FIND_MEMBERNUM_FIELD.number = 3
pb.SC_SOCIETY_FIND_MEMBERNUM_FIELD.index = 2
pb.SC_SOCIETY_FIND_MEMBERNUM_FIELD.label = 1
pb.SC_SOCIETY_FIND_MEMBERNUM_FIELD.has_default_value = false
pb.SC_SOCIETY_FIND_MEMBERNUM_FIELD.default_value = 0
pb.SC_SOCIETY_FIND_MEMBERNUM_FIELD.type = 13
pb.SC_SOCIETY_FIND_MEMBERNUM_FIELD.cpp_type = 3

pb.SC_SOCIETY_FIND_SOCIETYINFO_FIELD.name = "SocietyInfo"
pb.SC_SOCIETY_FIND_SOCIETYINFO_FIELD.full_name = ".SC_Society_Find.SocietyInfo"
pb.SC_SOCIETY_FIND_SOCIETYINFO_FIELD.number = 4
pb.SC_SOCIETY_FIND_SOCIETYINFO_FIELD.index = 3
pb.SC_SOCIETY_FIND_SOCIETYINFO_FIELD.label = 1
pb.SC_SOCIETY_FIND_SOCIETYINFO_FIELD.has_default_value = false
pb.SC_SOCIETY_FIND_SOCIETYINFO_FIELD.default_value = ""
pb.SC_SOCIETY_FIND_SOCIETYINFO_FIELD.type = 9
pb.SC_SOCIETY_FIND_SOCIETYINFO_FIELD.cpp_type = 9

pb.SC_SOCIETY_FIND_LEVELLIMIT_FIELD.name = "LevelLimit"
pb.SC_SOCIETY_FIND_LEVELLIMIT_FIELD.full_name = ".SC_Society_Find.LevelLimit"
pb.SC_SOCIETY_FIND_LEVELLIMIT_FIELD.number = 5
pb.SC_SOCIETY_FIND_LEVELLIMIT_FIELD.index = 4
pb.SC_SOCIETY_FIND_LEVELLIMIT_FIELD.label = 1
pb.SC_SOCIETY_FIND_LEVELLIMIT_FIELD.has_default_value = false
pb.SC_SOCIETY_FIND_LEVELLIMIT_FIELD.default_value = 0
pb.SC_SOCIETY_FIND_LEVELLIMIT_FIELD.type = 13
pb.SC_SOCIETY_FIND_LEVELLIMIT_FIELD.cpp_type = 3

pb.SC_SOCIETY_FIND_JOINLIMIT_FIELD.name = "JoinLimit"
pb.SC_SOCIETY_FIND_JOINLIMIT_FIELD.full_name = ".SC_Society_Find.JoinLimit"
pb.SC_SOCIETY_FIND_JOINLIMIT_FIELD.number = 6
pb.SC_SOCIETY_FIND_JOINLIMIT_FIELD.index = 5
pb.SC_SOCIETY_FIND_JOINLIMIT_FIELD.label = 1
pb.SC_SOCIETY_FIND_JOINLIMIT_FIELD.has_default_value = false
pb.SC_SOCIETY_FIND_JOINLIMIT_FIELD.default_value = 0
pb.SC_SOCIETY_FIND_JOINLIMIT_FIELD.type = 13
pb.SC_SOCIETY_FIND_JOINLIMIT_FIELD.cpp_type = 3

pb.SC_SOCIETY_FIND_RESULT_FIELD.name = "Result"
pb.SC_SOCIETY_FIND_RESULT_FIELD.full_name = ".SC_Society_Find.Result"
pb.SC_SOCIETY_FIND_RESULT_FIELD.number = 7
pb.SC_SOCIETY_FIND_RESULT_FIELD.index = 6
pb.SC_SOCIETY_FIND_RESULT_FIELD.label = 1
pb.SC_SOCIETY_FIND_RESULT_FIELD.has_default_value = false
pb.SC_SOCIETY_FIND_RESULT_FIELD.default_value = 0
pb.SC_SOCIETY_FIND_RESULT_FIELD.type = 13
pb.SC_SOCIETY_FIND_RESULT_FIELD.cpp_type = 3

pb.SC_SOCIETY_FIND_COUNTRY_FIELD.name = "Country"
pb.SC_SOCIETY_FIND_COUNTRY_FIELD.full_name = ".SC_Society_Find.Country"
pb.SC_SOCIETY_FIND_COUNTRY_FIELD.number = 8
pb.SC_SOCIETY_FIND_COUNTRY_FIELD.index = 7
pb.SC_SOCIETY_FIND_COUNTRY_FIELD.label = 1
pb.SC_SOCIETY_FIND_COUNTRY_FIELD.has_default_value = false
pb.SC_SOCIETY_FIND_COUNTRY_FIELD.default_value = 0
pb.SC_SOCIETY_FIND_COUNTRY_FIELD.type = 13
pb.SC_SOCIETY_FIND_COUNTRY_FIELD.cpp_type = 3

pb.SC_SOCIETY_FIND_LEVEL_FIELD.name = "Level"
pb.SC_SOCIETY_FIND_LEVEL_FIELD.full_name = ".SC_Society_Find.Level"
pb.SC_SOCIETY_FIND_LEVEL_FIELD.number = 9
pb.SC_SOCIETY_FIND_LEVEL_FIELD.index = 8
pb.SC_SOCIETY_FIND_LEVEL_FIELD.label = 1
pb.SC_SOCIETY_FIND_LEVEL_FIELD.has_default_value = false
pb.SC_SOCIETY_FIND_LEVEL_FIELD.default_value = 0
pb.SC_SOCIETY_FIND_LEVEL_FIELD.type = 13
pb.SC_SOCIETY_FIND_LEVEL_FIELD.cpp_type = 3

pb.SC_SOCIETY_FIND.name = "SC_Society_Find"
pb.SC_SOCIETY_FIND.full_name = ".SC_Society_Find"
pb.SC_SOCIETY_FIND.nested_types = {}
pb.SC_SOCIETY_FIND.enum_types = {}
pb.SC_SOCIETY_FIND.fields = {pb.SC_SOCIETY_FIND_SOCIETYID_FIELD, pb.SC_SOCIETY_FIND_SOCIETYNAME_FIELD, pb.SC_SOCIETY_FIND_MEMBERNUM_FIELD, pb.SC_SOCIETY_FIND_SOCIETYINFO_FIELD, pb.SC_SOCIETY_FIND_LEVELLIMIT_FIELD, pb.SC_SOCIETY_FIND_JOINLIMIT_FIELD, pb.SC_SOCIETY_FIND_RESULT_FIELD, pb.SC_SOCIETY_FIND_COUNTRY_FIELD, pb.SC_SOCIETY_FIND_LEVEL_FIELD}
pb.SC_SOCIETY_FIND.is_extendable = false
pb.SC_SOCIETY_FIND.extensions = {}
pb.CS_SOCIETY_INFO_SOCIETYID_FIELD.name = "SocietyID"
pb.CS_SOCIETY_INFO_SOCIETYID_FIELD.full_name = ".CS_Society_Info.SocietyID"
pb.CS_SOCIETY_INFO_SOCIETYID_FIELD.number = 1
pb.CS_SOCIETY_INFO_SOCIETYID_FIELD.index = 0
pb.CS_SOCIETY_INFO_SOCIETYID_FIELD.label = 2
pb.CS_SOCIETY_INFO_SOCIETYID_FIELD.has_default_value = false
pb.CS_SOCIETY_INFO_SOCIETYID_FIELD.default_value = 0
pb.CS_SOCIETY_INFO_SOCIETYID_FIELD.type = 13
pb.CS_SOCIETY_INFO_SOCIETYID_FIELD.cpp_type = 3

pb.CS_SOCIETY_INFO.name = "CS_Society_Info"
pb.CS_SOCIETY_INFO.full_name = ".CS_Society_Info"
pb.CS_SOCIETY_INFO.nested_types = {}
pb.CS_SOCIETY_INFO.enum_types = {}
pb.CS_SOCIETY_INFO.fields = {pb.CS_SOCIETY_INFO_SOCIETYID_FIELD}
pb.CS_SOCIETY_INFO.is_extendable = false
pb.CS_SOCIETY_INFO.extensions = {}
pb.SC_SOCIETY_INFO_SOCIETYID_FIELD.name = "SocietyID"
pb.SC_SOCIETY_INFO_SOCIETYID_FIELD.full_name = ".SC_Society_Info.SocietyID"
pb.SC_SOCIETY_INFO_SOCIETYID_FIELD.number = 1
pb.SC_SOCIETY_INFO_SOCIETYID_FIELD.index = 0
pb.SC_SOCIETY_INFO_SOCIETYID_FIELD.label = 1
pb.SC_SOCIETY_INFO_SOCIETYID_FIELD.has_default_value = false
pb.SC_SOCIETY_INFO_SOCIETYID_FIELD.default_value = 0
pb.SC_SOCIETY_INFO_SOCIETYID_FIELD.type = 13
pb.SC_SOCIETY_INFO_SOCIETYID_FIELD.cpp_type = 3

pb.SC_SOCIETY_INFO_SOCIETYNAME_FIELD.name = "SocietyName"
pb.SC_SOCIETY_INFO_SOCIETYNAME_FIELD.full_name = ".SC_Society_Info.SocietyName"
pb.SC_SOCIETY_INFO_SOCIETYNAME_FIELD.number = 2
pb.SC_SOCIETY_INFO_SOCIETYNAME_FIELD.index = 1
pb.SC_SOCIETY_INFO_SOCIETYNAME_FIELD.label = 1
pb.SC_SOCIETY_INFO_SOCIETYNAME_FIELD.has_default_value = false
pb.SC_SOCIETY_INFO_SOCIETYNAME_FIELD.default_value = ""
pb.SC_SOCIETY_INFO_SOCIETYNAME_FIELD.type = 9
pb.SC_SOCIETY_INFO_SOCIETYNAME_FIELD.cpp_type = 9

pb.SC_SOCIETY_INFO_CHIEFID_FIELD.name = "ChiefID"
pb.SC_SOCIETY_INFO_CHIEFID_FIELD.full_name = ".SC_Society_Info.ChiefID"
pb.SC_SOCIETY_INFO_CHIEFID_FIELD.number = 3
pb.SC_SOCIETY_INFO_CHIEFID_FIELD.index = 2
pb.SC_SOCIETY_INFO_CHIEFID_FIELD.label = 1
pb.SC_SOCIETY_INFO_CHIEFID_FIELD.has_default_value = false
pb.SC_SOCIETY_INFO_CHIEFID_FIELD.default_value = 0
pb.SC_SOCIETY_INFO_CHIEFID_FIELD.type = 13
pb.SC_SOCIETY_INFO_CHIEFID_FIELD.cpp_type = 3

pb.SC_SOCIETY_INFO_SOCIETYLEVEL_FIELD.name = "SocietyLevel"
pb.SC_SOCIETY_INFO_SOCIETYLEVEL_FIELD.full_name = ".SC_Society_Info.SocietyLevel"
pb.SC_SOCIETY_INFO_SOCIETYLEVEL_FIELD.number = 4
pb.SC_SOCIETY_INFO_SOCIETYLEVEL_FIELD.index = 3
pb.SC_SOCIETY_INFO_SOCIETYLEVEL_FIELD.label = 1
pb.SC_SOCIETY_INFO_SOCIETYLEVEL_FIELD.has_default_value = false
pb.SC_SOCIETY_INFO_SOCIETYLEVEL_FIELD.default_value = 0
pb.SC_SOCIETY_INFO_SOCIETYLEVEL_FIELD.type = 13
pb.SC_SOCIETY_INFO_SOCIETYLEVEL_FIELD.cpp_type = 3

pb.SC_SOCIETY_INFO_SOCIETYEXP_FIELD.name = "SocietyExp"
pb.SC_SOCIETY_INFO_SOCIETYEXP_FIELD.full_name = ".SC_Society_Info.SocietyExp"
pb.SC_SOCIETY_INFO_SOCIETYEXP_FIELD.number = 5
pb.SC_SOCIETY_INFO_SOCIETYEXP_FIELD.index = 4
pb.SC_SOCIETY_INFO_SOCIETYEXP_FIELD.label = 1
pb.SC_SOCIETY_INFO_SOCIETYEXP_FIELD.has_default_value = false
pb.SC_SOCIETY_INFO_SOCIETYEXP_FIELD.default_value = 0
pb.SC_SOCIETY_INFO_SOCIETYEXP_FIELD.type = 13
pb.SC_SOCIETY_INFO_SOCIETYEXP_FIELD.cpp_type = 3

pb.SC_SOCIETY_INFO_VITALITY_FIELD.name = "Vitality"
pb.SC_SOCIETY_INFO_VITALITY_FIELD.full_name = ".SC_Society_Info.Vitality"
pb.SC_SOCIETY_INFO_VITALITY_FIELD.number = 6
pb.SC_SOCIETY_INFO_VITALITY_FIELD.index = 5
pb.SC_SOCIETY_INFO_VITALITY_FIELD.label = 1
pb.SC_SOCIETY_INFO_VITALITY_FIELD.has_default_value = false
pb.SC_SOCIETY_INFO_VITALITY_FIELD.default_value = 0
pb.SC_SOCIETY_INFO_VITALITY_FIELD.type = 13
pb.SC_SOCIETY_INFO_VITALITY_FIELD.cpp_type = 3

pb.SC_SOCIETY_INFO_MEMBERNUM_FIELD.name = "MemberNum"
pb.SC_SOCIETY_INFO_MEMBERNUM_FIELD.full_name = ".SC_Society_Info.MemberNum"
pb.SC_SOCIETY_INFO_MEMBERNUM_FIELD.number = 7
pb.SC_SOCIETY_INFO_MEMBERNUM_FIELD.index = 6
pb.SC_SOCIETY_INFO_MEMBERNUM_FIELD.label = 1
pb.SC_SOCIETY_INFO_MEMBERNUM_FIELD.has_default_value = false
pb.SC_SOCIETY_INFO_MEMBERNUM_FIELD.default_value = 0
pb.SC_SOCIETY_INFO_MEMBERNUM_FIELD.type = 13
pb.SC_SOCIETY_INFO_MEMBERNUM_FIELD.cpp_type = 3

pb.SC_SOCIETY_INFO_FEASTLEAVE_FIELD.name = "FeastLeave"
pb.SC_SOCIETY_INFO_FEASTLEAVE_FIELD.full_name = ".SC_Society_Info.FeastLeave"
pb.SC_SOCIETY_INFO_FEASTLEAVE_FIELD.number = 8
pb.SC_SOCIETY_INFO_FEASTLEAVE_FIELD.index = 7
pb.SC_SOCIETY_INFO_FEASTLEAVE_FIELD.label = 1
pb.SC_SOCIETY_INFO_FEASTLEAVE_FIELD.has_default_value = false
pb.SC_SOCIETY_INFO_FEASTLEAVE_FIELD.default_value = 0
pb.SC_SOCIETY_INFO_FEASTLEAVE_FIELD.type = 13
pb.SC_SOCIETY_INFO_FEASTLEAVE_FIELD.cpp_type = 3

pb.SC_SOCIETY_INFO_FEASTADD_FIELD.name = "FeastAdd"
pb.SC_SOCIETY_INFO_FEASTADD_FIELD.full_name = ".SC_Society_Info.FeastAdd"
pb.SC_SOCIETY_INFO_FEASTADD_FIELD.number = 9
pb.SC_SOCIETY_INFO_FEASTADD_FIELD.index = 8
pb.SC_SOCIETY_INFO_FEASTADD_FIELD.label = 1
pb.SC_SOCIETY_INFO_FEASTADD_FIELD.has_default_value = false
pb.SC_SOCIETY_INFO_FEASTADD_FIELD.default_value = 0
pb.SC_SOCIETY_INFO_FEASTADD_FIELD.type = 13
pb.SC_SOCIETY_INFO_FEASTADD_FIELD.cpp_type = 3

pb.SC_SOCIETY_INFO_LASTFEAST_FIELD.name = "LastFeast"
pb.SC_SOCIETY_INFO_LASTFEAST_FIELD.full_name = ".SC_Society_Info.LastFeast"
pb.SC_SOCIETY_INFO_LASTFEAST_FIELD.number = 10
pb.SC_SOCIETY_INFO_LASTFEAST_FIELD.index = 9
pb.SC_SOCIETY_INFO_LASTFEAST_FIELD.label = 1
pb.SC_SOCIETY_INFO_LASTFEAST_FIELD.has_default_value = false
pb.SC_SOCIETY_INFO_LASTFEAST_FIELD.default_value = 0
pb.SC_SOCIETY_INFO_LASTFEAST_FIELD.type = 13
pb.SC_SOCIETY_INFO_LASTFEAST_FIELD.cpp_type = 3

pb.SC_SOCIETY_INFO_CHIEFLOGOUTTIME_FIELD.name = "ChiefLogoutTime"
pb.SC_SOCIETY_INFO_CHIEFLOGOUTTIME_FIELD.full_name = ".SC_Society_Info.ChiefLogoutTime"
pb.SC_SOCIETY_INFO_CHIEFLOGOUTTIME_FIELD.number = 11
pb.SC_SOCIETY_INFO_CHIEFLOGOUTTIME_FIELD.index = 10
pb.SC_SOCIETY_INFO_CHIEFLOGOUTTIME_FIELD.label = 1
pb.SC_SOCIETY_INFO_CHIEFLOGOUTTIME_FIELD.has_default_value = false
pb.SC_SOCIETY_INFO_CHIEFLOGOUTTIME_FIELD.default_value = 0
pb.SC_SOCIETY_INFO_CHIEFLOGOUTTIME_FIELD.type = 13
pb.SC_SOCIETY_INFO_CHIEFLOGOUTTIME_FIELD.cpp_type = 3

pb.SC_SOCIETY_INFO_SOCIETYINFO_FIELD.name = "SocietyInfo"
pb.SC_SOCIETY_INFO_SOCIETYINFO_FIELD.full_name = ".SC_Society_Info.SocietyInfo"
pb.SC_SOCIETY_INFO_SOCIETYINFO_FIELD.number = 12
pb.SC_SOCIETY_INFO_SOCIETYINFO_FIELD.index = 11
pb.SC_SOCIETY_INFO_SOCIETYINFO_FIELD.label = 1
pb.SC_SOCIETY_INFO_SOCIETYINFO_FIELD.has_default_value = false
pb.SC_SOCIETY_INFO_SOCIETYINFO_FIELD.default_value = ""
pb.SC_SOCIETY_INFO_SOCIETYINFO_FIELD.type = 9
pb.SC_SOCIETY_INFO_SOCIETYINFO_FIELD.cpp_type = 9

pb.SC_SOCIETY_INFO_LEVELLIMIT_FIELD.name = "LevelLimit"
pb.SC_SOCIETY_INFO_LEVELLIMIT_FIELD.full_name = ".SC_Society_Info.LevelLimit"
pb.SC_SOCIETY_INFO_LEVELLIMIT_FIELD.number = 13
pb.SC_SOCIETY_INFO_LEVELLIMIT_FIELD.index = 12
pb.SC_SOCIETY_INFO_LEVELLIMIT_FIELD.label = 1
pb.SC_SOCIETY_INFO_LEVELLIMIT_FIELD.has_default_value = false
pb.SC_SOCIETY_INFO_LEVELLIMIT_FIELD.default_value = 0
pb.SC_SOCIETY_INFO_LEVELLIMIT_FIELD.type = 13
pb.SC_SOCIETY_INFO_LEVELLIMIT_FIELD.cpp_type = 3

pb.SC_SOCIETY_INFO_JOINLIMIT_FIELD.name = "JoinLimit"
pb.SC_SOCIETY_INFO_JOINLIMIT_FIELD.full_name = ".SC_Society_Info.JoinLimit"
pb.SC_SOCIETY_INFO_JOINLIMIT_FIELD.number = 14
pb.SC_SOCIETY_INFO_JOINLIMIT_FIELD.index = 13
pb.SC_SOCIETY_INFO_JOINLIMIT_FIELD.label = 1
pb.SC_SOCIETY_INFO_JOINLIMIT_FIELD.has_default_value = false
pb.SC_SOCIETY_INFO_JOINLIMIT_FIELD.default_value = 0
pb.SC_SOCIETY_INFO_JOINLIMIT_FIELD.type = 13
pb.SC_SOCIETY_INFO_JOINLIMIT_FIELD.cpp_type = 3

pb.SC_SOCIETY_INFO_FEASTACTOR_FIELD.name = "FeastActor"
pb.SC_SOCIETY_INFO_FEASTACTOR_FIELD.full_name = ".SC_Society_Info.FeastActor"
pb.SC_SOCIETY_INFO_FEASTACTOR_FIELD.number = 15
pb.SC_SOCIETY_INFO_FEASTACTOR_FIELD.index = 14
pb.SC_SOCIETY_INFO_FEASTACTOR_FIELD.label = 1
pb.SC_SOCIETY_INFO_FEASTACTOR_FIELD.has_default_value = false
pb.SC_SOCIETY_INFO_FEASTACTOR_FIELD.default_value = 0
pb.SC_SOCIETY_INFO_FEASTACTOR_FIELD.type = 13
pb.SC_SOCIETY_INFO_FEASTACTOR_FIELD.cpp_type = 3

pb.SC_SOCIETY_INFO_FEASTACTORNAME_FIELD.name = "FeastActorName"
pb.SC_SOCIETY_INFO_FEASTACTORNAME_FIELD.full_name = ".SC_Society_Info.FeastActorName"
pb.SC_SOCIETY_INFO_FEASTACTORNAME_FIELD.number = 16
pb.SC_SOCIETY_INFO_FEASTACTORNAME_FIELD.index = 15
pb.SC_SOCIETY_INFO_FEASTACTORNAME_FIELD.label = 1
pb.SC_SOCIETY_INFO_FEASTACTORNAME_FIELD.has_default_value = false
pb.SC_SOCIETY_INFO_FEASTACTORNAME_FIELD.default_value = ""
pb.SC_SOCIETY_INFO_FEASTACTORNAME_FIELD.type = 9
pb.SC_SOCIETY_INFO_FEASTACTORNAME_FIELD.cpp_type = 9

pb.SC_SOCIETY_INFO_RESULT_FIELD.name = "Result"
pb.SC_SOCIETY_INFO_RESULT_FIELD.full_name = ".SC_Society_Info.Result"
pb.SC_SOCIETY_INFO_RESULT_FIELD.number = 17
pb.SC_SOCIETY_INFO_RESULT_FIELD.index = 16
pb.SC_SOCIETY_INFO_RESULT_FIELD.label = 1
pb.SC_SOCIETY_INFO_RESULT_FIELD.has_default_value = false
pb.SC_SOCIETY_INFO_RESULT_FIELD.default_value = 0
pb.SC_SOCIETY_INFO_RESULT_FIELD.type = 13
pb.SC_SOCIETY_INFO_RESULT_FIELD.cpp_type = 3

pb.SC_SOCIETY_INFO_BUYREVIVE_FIELD.name = "BuyRevive"
pb.SC_SOCIETY_INFO_BUYREVIVE_FIELD.full_name = ".SC_Society_Info.BuyRevive"
pb.SC_SOCIETY_INFO_BUYREVIVE_FIELD.number = 18
pb.SC_SOCIETY_INFO_BUYREVIVE_FIELD.index = 17
pb.SC_SOCIETY_INFO_BUYREVIVE_FIELD.label = 1
pb.SC_SOCIETY_INFO_BUYREVIVE_FIELD.has_default_value = false
pb.SC_SOCIETY_INFO_BUYREVIVE_FIELD.default_value = 0
pb.SC_SOCIETY_INFO_BUYREVIVE_FIELD.type = 13
pb.SC_SOCIETY_INFO_BUYREVIVE_FIELD.cpp_type = 3

pb.SC_SOCIETY_INFO_REVIVE_FIELD.name = "Revive"
pb.SC_SOCIETY_INFO_REVIVE_FIELD.full_name = ".SC_Society_Info.Revive"
pb.SC_SOCIETY_INFO_REVIVE_FIELD.number = 19
pb.SC_SOCIETY_INFO_REVIVE_FIELD.index = 18
pb.SC_SOCIETY_INFO_REVIVE_FIELD.label = 1
pb.SC_SOCIETY_INFO_REVIVE_FIELD.has_default_value = false
pb.SC_SOCIETY_INFO_REVIVE_FIELD.default_value = 0
pb.SC_SOCIETY_INFO_REVIVE_FIELD.type = 13
pb.SC_SOCIETY_INFO_REVIVE_FIELD.cpp_type = 3

pb.SC_SOCIETY_INFO_PRACTICECOUNT_FIELD.name = "PracticeCount"
pb.SC_SOCIETY_INFO_PRACTICECOUNT_FIELD.full_name = ".SC_Society_Info.PracticeCount"
pb.SC_SOCIETY_INFO_PRACTICECOUNT_FIELD.number = 20
pb.SC_SOCIETY_INFO_PRACTICECOUNT_FIELD.index = 19
pb.SC_SOCIETY_INFO_PRACTICECOUNT_FIELD.label = 1
pb.SC_SOCIETY_INFO_PRACTICECOUNT_FIELD.has_default_value = false
pb.SC_SOCIETY_INFO_PRACTICECOUNT_FIELD.default_value = 0
pb.SC_SOCIETY_INFO_PRACTICECOUNT_FIELD.type = 13
pb.SC_SOCIETY_INFO_PRACTICECOUNT_FIELD.cpp_type = 3

pb.SC_SOCIETY_INFO_PRACTICETIME_FIELD.name = "PracticeTime"
pb.SC_SOCIETY_INFO_PRACTICETIME_FIELD.full_name = ".SC_Society_Info.PracticeTime"
pb.SC_SOCIETY_INFO_PRACTICETIME_FIELD.number = 21
pb.SC_SOCIETY_INFO_PRACTICETIME_FIELD.index = 20
pb.SC_SOCIETY_INFO_PRACTICETIME_FIELD.label = 1
pb.SC_SOCIETY_INFO_PRACTICETIME_FIELD.has_default_value = false
pb.SC_SOCIETY_INFO_PRACTICETIME_FIELD.default_value = 0
pb.SC_SOCIETY_INFO_PRACTICETIME_FIELD.type = 13
pb.SC_SOCIETY_INFO_PRACTICETIME_FIELD.cpp_type = 3

pb.SC_SOCIETY_INFO_COUNTRY_FIELD.name = "Country"
pb.SC_SOCIETY_INFO_COUNTRY_FIELD.full_name = ".SC_Society_Info.Country"
pb.SC_SOCIETY_INFO_COUNTRY_FIELD.number = 22
pb.SC_SOCIETY_INFO_COUNTRY_FIELD.index = 21
pb.SC_SOCIETY_INFO_COUNTRY_FIELD.label = 1
pb.SC_SOCIETY_INFO_COUNTRY_FIELD.has_default_value = false
pb.SC_SOCIETY_INFO_COUNTRY_FIELD.default_value = 0
pb.SC_SOCIETY_INFO_COUNTRY_FIELD.type = 13
pb.SC_SOCIETY_INFO_COUNTRY_FIELD.cpp_type = 3

pb.SC_SOCIETY_INFO_PRACTICENOTICE_FIELD.name = "PracticeNotice"
pb.SC_SOCIETY_INFO_PRACTICENOTICE_FIELD.full_name = ".SC_Society_Info.PracticeNotice"
pb.SC_SOCIETY_INFO_PRACTICENOTICE_FIELD.number = 23
pb.SC_SOCIETY_INFO_PRACTICENOTICE_FIELD.index = 22
pb.SC_SOCIETY_INFO_PRACTICENOTICE_FIELD.label = 1
pb.SC_SOCIETY_INFO_PRACTICENOTICE_FIELD.has_default_value = false
pb.SC_SOCIETY_INFO_PRACTICENOTICE_FIELD.default_value = 0
pb.SC_SOCIETY_INFO_PRACTICENOTICE_FIELD.type = 13
pb.SC_SOCIETY_INFO_PRACTICENOTICE_FIELD.cpp_type = 3

pb.SC_SOCIETY_INFO_FEASTNOTICE_FIELD.name = "FeastNotice"
pb.SC_SOCIETY_INFO_FEASTNOTICE_FIELD.full_name = ".SC_Society_Info.FeastNotice"
pb.SC_SOCIETY_INFO_FEASTNOTICE_FIELD.number = 24
pb.SC_SOCIETY_INFO_FEASTNOTICE_FIELD.index = 23
pb.SC_SOCIETY_INFO_FEASTNOTICE_FIELD.label = 1
pb.SC_SOCIETY_INFO_FEASTNOTICE_FIELD.has_default_value = false
pb.SC_SOCIETY_INFO_FEASTNOTICE_FIELD.default_value = 0
pb.SC_SOCIETY_INFO_FEASTNOTICE_FIELD.type = 13
pb.SC_SOCIETY_INFO_FEASTNOTICE_FIELD.cpp_type = 3

pb.SC_SOCIETY_INFO_CONTRIBUTIONNOTICE_FIELD.name = "ContributionNotice"
pb.SC_SOCIETY_INFO_CONTRIBUTIONNOTICE_FIELD.full_name = ".SC_Society_Info.ContributionNotice"
pb.SC_SOCIETY_INFO_CONTRIBUTIONNOTICE_FIELD.number = 25
pb.SC_SOCIETY_INFO_CONTRIBUTIONNOTICE_FIELD.index = 24
pb.SC_SOCIETY_INFO_CONTRIBUTIONNOTICE_FIELD.label = 1
pb.SC_SOCIETY_INFO_CONTRIBUTIONNOTICE_FIELD.has_default_value = false
pb.SC_SOCIETY_INFO_CONTRIBUTIONNOTICE_FIELD.default_value = 0
pb.SC_SOCIETY_INFO_CONTRIBUTIONNOTICE_FIELD.type = 13
pb.SC_SOCIETY_INFO_CONTRIBUTIONNOTICE_FIELD.cpp_type = 3

pb.SC_SOCIETY_INFO_PRIZELIMIT_FIELD.name = "PrizeLimit"
pb.SC_SOCIETY_INFO_PRIZELIMIT_FIELD.full_name = ".SC_Society_Info.PrizeLimit"
pb.SC_SOCIETY_INFO_PRIZELIMIT_FIELD.number = 26
pb.SC_SOCIETY_INFO_PRIZELIMIT_FIELD.index = 25
pb.SC_SOCIETY_INFO_PRIZELIMIT_FIELD.label = 1
pb.SC_SOCIETY_INFO_PRIZELIMIT_FIELD.has_default_value = false
pb.SC_SOCIETY_INFO_PRIZELIMIT_FIELD.default_value = 0
pb.SC_SOCIETY_INFO_PRIZELIMIT_FIELD.type = 13
pb.SC_SOCIETY_INFO_PRIZELIMIT_FIELD.cpp_type = 3

pb.SC_SOCIETY_INFO_POWERLIMIT_FIELD.name = "PowerLimit"
pb.SC_SOCIETY_INFO_POWERLIMIT_FIELD.full_name = ".SC_Society_Info.PowerLimit"
pb.SC_SOCIETY_INFO_POWERLIMIT_FIELD.number = 27
pb.SC_SOCIETY_INFO_POWERLIMIT_FIELD.index = 26
pb.SC_SOCIETY_INFO_POWERLIMIT_FIELD.label = 1
pb.SC_SOCIETY_INFO_POWERLIMIT_FIELD.has_default_value = false
pb.SC_SOCIETY_INFO_POWERLIMIT_FIELD.default_value = 0
pb.SC_SOCIETY_INFO_POWERLIMIT_FIELD.type = 13
pb.SC_SOCIETY_INFO_POWERLIMIT_FIELD.cpp_type = 3

pb.SC_SOCIETY_INFO_FOUND_FIELD.name = "Found"
pb.SC_SOCIETY_INFO_FOUND_FIELD.full_name = ".SC_Society_Info.Found"
pb.SC_SOCIETY_INFO_FOUND_FIELD.number = 28
pb.SC_SOCIETY_INFO_FOUND_FIELD.index = 27
pb.SC_SOCIETY_INFO_FOUND_FIELD.label = 1
pb.SC_SOCIETY_INFO_FOUND_FIELD.has_default_value = false
pb.SC_SOCIETY_INFO_FOUND_FIELD.default_value = 0
pb.SC_SOCIETY_INFO_FOUND_FIELD.type = 13
pb.SC_SOCIETY_INFO_FOUND_FIELD.cpp_type = 3

pb.SC_SOCIETY_INFO_CONSTRUCTION_FIELD.name = "Construction"
pb.SC_SOCIETY_INFO_CONSTRUCTION_FIELD.full_name = ".SC_Society_Info.Construction"
pb.SC_SOCIETY_INFO_CONSTRUCTION_FIELD.number = 29
pb.SC_SOCIETY_INFO_CONSTRUCTION_FIELD.index = 28
pb.SC_SOCIETY_INFO_CONSTRUCTION_FIELD.label = 1
pb.SC_SOCIETY_INFO_CONSTRUCTION_FIELD.has_default_value = false
pb.SC_SOCIETY_INFO_CONSTRUCTION_FIELD.default_value = 0
pb.SC_SOCIETY_INFO_CONSTRUCTION_FIELD.type = 13
pb.SC_SOCIETY_INFO_CONSTRUCTION_FIELD.cpp_type = 3

pb.SC_SOCIETY_INFO_BUILDTIMEEND_FIELD.name = "buildTimeEnd"
pb.SC_SOCIETY_INFO_BUILDTIMEEND_FIELD.full_name = ".SC_Society_Info.buildTimeEnd"
pb.SC_SOCIETY_INFO_BUILDTIMEEND_FIELD.number = 30
pb.SC_SOCIETY_INFO_BUILDTIMEEND_FIELD.index = 29
pb.SC_SOCIETY_INFO_BUILDTIMEEND_FIELD.label = 1
pb.SC_SOCIETY_INFO_BUILDTIMEEND_FIELD.has_default_value = false
pb.SC_SOCIETY_INFO_BUILDTIMEEND_FIELD.default_value = 0
pb.SC_SOCIETY_INFO_BUILDTIMEEND_FIELD.type = 13
pb.SC_SOCIETY_INFO_BUILDTIMEEND_FIELD.cpp_type = 3

pb.SC_SOCIETY_INFO_ALCHEMYTYPE_FIELD.name = "AlchemyType"
pb.SC_SOCIETY_INFO_ALCHEMYTYPE_FIELD.full_name = ".SC_Society_Info.AlchemyType"
pb.SC_SOCIETY_INFO_ALCHEMYTYPE_FIELD.number = 31
pb.SC_SOCIETY_INFO_ALCHEMYTYPE_FIELD.index = 30
pb.SC_SOCIETY_INFO_ALCHEMYTYPE_FIELD.label = 1
pb.SC_SOCIETY_INFO_ALCHEMYTYPE_FIELD.has_default_value = false
pb.SC_SOCIETY_INFO_ALCHEMYTYPE_FIELD.default_value = 0
pb.SC_SOCIETY_INFO_ALCHEMYTYPE_FIELD.type = 13
pb.SC_SOCIETY_INFO_ALCHEMYTYPE_FIELD.cpp_type = 3

pb.SC_SOCIETY_INFO_FURNACELEVEL_FIELD.name = "FurnaceLevel"
pb.SC_SOCIETY_INFO_FURNACELEVEL_FIELD.full_name = ".SC_Society_Info.FurnaceLevel"
pb.SC_SOCIETY_INFO_FURNACELEVEL_FIELD.number = 32
pb.SC_SOCIETY_INFO_FURNACELEVEL_FIELD.index = 31
pb.SC_SOCIETY_INFO_FURNACELEVEL_FIELD.label = 1
pb.SC_SOCIETY_INFO_FURNACELEVEL_FIELD.has_default_value = false
pb.SC_SOCIETY_INFO_FURNACELEVEL_FIELD.default_value = 0
pb.SC_SOCIETY_INFO_FURNACELEVEL_FIELD.type = 13
pb.SC_SOCIETY_INFO_FURNACELEVEL_FIELD.cpp_type = 3

pb.SC_SOCIETY_INFO_CHANGEALCHEMYTYPETIMES_FIELD.name = "ChangeAlchemyTypeTimes"
pb.SC_SOCIETY_INFO_CHANGEALCHEMYTYPETIMES_FIELD.full_name = ".SC_Society_Info.ChangeAlchemyTypeTimes"
pb.SC_SOCIETY_INFO_CHANGEALCHEMYTYPETIMES_FIELD.number = 33
pb.SC_SOCIETY_INFO_CHANGEALCHEMYTYPETIMES_FIELD.index = 32
pb.SC_SOCIETY_INFO_CHANGEALCHEMYTYPETIMES_FIELD.label = 1
pb.SC_SOCIETY_INFO_CHANGEALCHEMYTYPETIMES_FIELD.has_default_value = false
pb.SC_SOCIETY_INFO_CHANGEALCHEMYTYPETIMES_FIELD.default_value = 0
pb.SC_SOCIETY_INFO_CHANGEALCHEMYTYPETIMES_FIELD.type = 13
pb.SC_SOCIETY_INFO_CHANGEALCHEMYTYPETIMES_FIELD.cpp_type = 3

pb.SC_SOCIETY_INFO_CREATETIME_FIELD.name = "CreateTime"
pb.SC_SOCIETY_INFO_CREATETIME_FIELD.full_name = ".SC_Society_Info.CreateTime"
pb.SC_SOCIETY_INFO_CREATETIME_FIELD.number = 34
pb.SC_SOCIETY_INFO_CREATETIME_FIELD.index = 33
pb.SC_SOCIETY_INFO_CREATETIME_FIELD.label = 1
pb.SC_SOCIETY_INFO_CREATETIME_FIELD.has_default_value = false
pb.SC_SOCIETY_INFO_CREATETIME_FIELD.default_value = 0
pb.SC_SOCIETY_INFO_CREATETIME_FIELD.type = 13
pb.SC_SOCIETY_INFO_CREATETIME_FIELD.cpp_type = 3

pb.SC_SOCIETY_INFO_POWER_FIELD.name = "Power"
pb.SC_SOCIETY_INFO_POWER_FIELD.full_name = ".SC_Society_Info.Power"
pb.SC_SOCIETY_INFO_POWER_FIELD.number = 35
pb.SC_SOCIETY_INFO_POWER_FIELD.index = 34
pb.SC_SOCIETY_INFO_POWER_FIELD.label = 1
pb.SC_SOCIETY_INFO_POWER_FIELD.has_default_value = false
pb.SC_SOCIETY_INFO_POWER_FIELD.default_value = 0
pb.SC_SOCIETY_INFO_POWER_FIELD.type = 13
pb.SC_SOCIETY_INFO_POWER_FIELD.cpp_type = 3

pb.SC_SOCIETY_INFO.name = "SC_Society_Info"
pb.SC_SOCIETY_INFO.full_name = ".SC_Society_Info"
pb.SC_SOCIETY_INFO.nested_types = {}
pb.SC_SOCIETY_INFO.enum_types = {}
pb.SC_SOCIETY_INFO.fields = {pb.SC_SOCIETY_INFO_SOCIETYID_FIELD, pb.SC_SOCIETY_INFO_SOCIETYNAME_FIELD, pb.SC_SOCIETY_INFO_CHIEFID_FIELD, pb.SC_SOCIETY_INFO_SOCIETYLEVEL_FIELD, pb.SC_SOCIETY_INFO_SOCIETYEXP_FIELD, pb.SC_SOCIETY_INFO_VITALITY_FIELD, pb.SC_SOCIETY_INFO_MEMBERNUM_FIELD, pb.SC_SOCIETY_INFO_FEASTLEAVE_FIELD, pb.SC_SOCIETY_INFO_FEASTADD_FIELD, pb.SC_SOCIETY_INFO_LASTFEAST_FIELD, pb.SC_SOCIETY_INFO_CHIEFLOGOUTTIME_FIELD, pb.SC_SOCIETY_INFO_SOCIETYINFO_FIELD, pb.SC_SOCIETY_INFO_LEVELLIMIT_FIELD, pb.SC_SOCIETY_INFO_JOINLIMIT_FIELD, pb.SC_SOCIETY_INFO_FEASTACTOR_FIELD, pb.SC_SOCIETY_INFO_FEASTACTORNAME_FIELD, pb.SC_SOCIETY_INFO_RESULT_FIELD, pb.SC_SOCIETY_INFO_BUYREVIVE_FIELD, pb.SC_SOCIETY_INFO_REVIVE_FIELD, pb.SC_SOCIETY_INFO_PRACTICECOUNT_FIELD, pb.SC_SOCIETY_INFO_PRACTICETIME_FIELD, pb.SC_SOCIETY_INFO_COUNTRY_FIELD, pb.SC_SOCIETY_INFO_PRACTICENOTICE_FIELD, pb.SC_SOCIETY_INFO_FEASTNOTICE_FIELD, pb.SC_SOCIETY_INFO_CONTRIBUTIONNOTICE_FIELD, pb.SC_SOCIETY_INFO_PRIZELIMIT_FIELD, pb.SC_SOCIETY_INFO_POWERLIMIT_FIELD, pb.SC_SOCIETY_INFO_FOUND_FIELD, pb.SC_SOCIETY_INFO_CONSTRUCTION_FIELD, pb.SC_SOCIETY_INFO_BUILDTIMEEND_FIELD, pb.SC_SOCIETY_INFO_ALCHEMYTYPE_FIELD, pb.SC_SOCIETY_INFO_FURNACELEVEL_FIELD, pb.SC_SOCIETY_INFO_CHANGEALCHEMYTYPETIMES_FIELD, pb.SC_SOCIETY_INFO_CREATETIME_FIELD, pb.SC_SOCIETY_INFO_POWER_FIELD}
pb.SC_SOCIETY_INFO.is_extendable = false
pb.SC_SOCIETY_INFO.extensions = {}
pb.CS_SOCIETY_APPLYLIST_SOCIETYID_FIELD.name = "SocietyID"
pb.CS_SOCIETY_APPLYLIST_SOCIETYID_FIELD.full_name = ".CS_Society_ApplyList.SocietyID"
pb.CS_SOCIETY_APPLYLIST_SOCIETYID_FIELD.number = 1
pb.CS_SOCIETY_APPLYLIST_SOCIETYID_FIELD.index = 0
pb.CS_SOCIETY_APPLYLIST_SOCIETYID_FIELD.label = 2
pb.CS_SOCIETY_APPLYLIST_SOCIETYID_FIELD.has_default_value = false
pb.CS_SOCIETY_APPLYLIST_SOCIETYID_FIELD.default_value = 0
pb.CS_SOCIETY_APPLYLIST_SOCIETYID_FIELD.type = 13
pb.CS_SOCIETY_APPLYLIST_SOCIETYID_FIELD.cpp_type = 3

pb.CS_SOCIETY_APPLYLIST.name = "CS_Society_ApplyList"
pb.CS_SOCIETY_APPLYLIST.full_name = ".CS_Society_ApplyList"
pb.CS_SOCIETY_APPLYLIST.nested_types = {}
pb.CS_SOCIETY_APPLYLIST.enum_types = {}
pb.CS_SOCIETY_APPLYLIST.fields = {pb.CS_SOCIETY_APPLYLIST_SOCIETYID_FIELD}
pb.CS_SOCIETY_APPLYLIST.is_extendable = false
pb.CS_SOCIETY_APPLYLIST.extensions = {}
pb.SC_SOCIETY_APPLYLIST_APPLYDATA_APPLICANT_ACTORID_FIELD.name = "ActorID"
pb.SC_SOCIETY_APPLYLIST_APPLYDATA_APPLICANT_ACTORID_FIELD.full_name = ".SC_Society_ApplyList.ApplyData.Applicant.ActorID"
pb.SC_SOCIETY_APPLYLIST_APPLYDATA_APPLICANT_ACTORID_FIELD.number = 1
pb.SC_SOCIETY_APPLYLIST_APPLYDATA_APPLICANT_ACTORID_FIELD.index = 0
pb.SC_SOCIETY_APPLYLIST_APPLYDATA_APPLICANT_ACTORID_FIELD.label = 1
pb.SC_SOCIETY_APPLYLIST_APPLYDATA_APPLICANT_ACTORID_FIELD.has_default_value = false
pb.SC_SOCIETY_APPLYLIST_APPLYDATA_APPLICANT_ACTORID_FIELD.default_value = 0
pb.SC_SOCIETY_APPLYLIST_APPLYDATA_APPLICANT_ACTORID_FIELD.type = 13
pb.SC_SOCIETY_APPLYLIST_APPLYDATA_APPLICANT_ACTORID_FIELD.cpp_type = 3

pb.SC_SOCIETY_APPLYLIST_APPLYDATA_APPLICANT_ACTORNAME_FIELD.name = "ActorName"
pb.SC_SOCIETY_APPLYLIST_APPLYDATA_APPLICANT_ACTORNAME_FIELD.full_name = ".SC_Society_ApplyList.ApplyData.Applicant.ActorName"
pb.SC_SOCIETY_APPLYLIST_APPLYDATA_APPLICANT_ACTORNAME_FIELD.number = 2
pb.SC_SOCIETY_APPLYLIST_APPLYDATA_APPLICANT_ACTORNAME_FIELD.index = 1
pb.SC_SOCIETY_APPLYLIST_APPLYDATA_APPLICANT_ACTORNAME_FIELD.label = 1
pb.SC_SOCIETY_APPLYLIST_APPLYDATA_APPLICANT_ACTORNAME_FIELD.has_default_value = false
pb.SC_SOCIETY_APPLYLIST_APPLYDATA_APPLICANT_ACTORNAME_FIELD.default_value = ""
pb.SC_SOCIETY_APPLYLIST_APPLYDATA_APPLICANT_ACTORNAME_FIELD.type = 9
pb.SC_SOCIETY_APPLYLIST_APPLYDATA_APPLICANT_ACTORNAME_FIELD.cpp_type = 9

pb.SC_SOCIETY_APPLYLIST_APPLYDATA_APPLICANT_POWER_FIELD.name = "Power"
pb.SC_SOCIETY_APPLYLIST_APPLYDATA_APPLICANT_POWER_FIELD.full_name = ".SC_Society_ApplyList.ApplyData.Applicant.Power"
pb.SC_SOCIETY_APPLYLIST_APPLYDATA_APPLICANT_POWER_FIELD.number = 3
pb.SC_SOCIETY_APPLYLIST_APPLYDATA_APPLICANT_POWER_FIELD.index = 2
pb.SC_SOCIETY_APPLYLIST_APPLYDATA_APPLICANT_POWER_FIELD.label = 1
pb.SC_SOCIETY_APPLYLIST_APPLYDATA_APPLICANT_POWER_FIELD.has_default_value = false
pb.SC_SOCIETY_APPLYLIST_APPLYDATA_APPLICANT_POWER_FIELD.default_value = 0
pb.SC_SOCIETY_APPLYLIST_APPLYDATA_APPLICANT_POWER_FIELD.type = 13
pb.SC_SOCIETY_APPLYLIST_APPLYDATA_APPLICANT_POWER_FIELD.cpp_type = 3

pb.SC_SOCIETY_APPLYLIST_APPLYDATA_APPLICANT_LEVEL_FIELD.name = "Level"
pb.SC_SOCIETY_APPLYLIST_APPLYDATA_APPLICANT_LEVEL_FIELD.full_name = ".SC_Society_ApplyList.ApplyData.Applicant.Level"
pb.SC_SOCIETY_APPLYLIST_APPLYDATA_APPLICANT_LEVEL_FIELD.number = 4
pb.SC_SOCIETY_APPLYLIST_APPLYDATA_APPLICANT_LEVEL_FIELD.index = 3
pb.SC_SOCIETY_APPLYLIST_APPLYDATA_APPLICANT_LEVEL_FIELD.label = 1
pb.SC_SOCIETY_APPLYLIST_APPLYDATA_APPLICANT_LEVEL_FIELD.has_default_value = false
pb.SC_SOCIETY_APPLYLIST_APPLYDATA_APPLICANT_LEVEL_FIELD.default_value = 0
pb.SC_SOCIETY_APPLYLIST_APPLYDATA_APPLICANT_LEVEL_FIELD.type = 13
pb.SC_SOCIETY_APPLYLIST_APPLYDATA_APPLICANT_LEVEL_FIELD.cpp_type = 3

pb.SC_SOCIETY_APPLYLIST_APPLYDATA_APPLICANT_CONTRIBUTION_FIELD.name = "Contribution"
pb.SC_SOCIETY_APPLYLIST_APPLYDATA_APPLICANT_CONTRIBUTION_FIELD.full_name = ".SC_Society_ApplyList.ApplyData.Applicant.Contribution"
pb.SC_SOCIETY_APPLYLIST_APPLYDATA_APPLICANT_CONTRIBUTION_FIELD.number = 5
pb.SC_SOCIETY_APPLYLIST_APPLYDATA_APPLICANT_CONTRIBUTION_FIELD.index = 4
pb.SC_SOCIETY_APPLYLIST_APPLYDATA_APPLICANT_CONTRIBUTION_FIELD.label = 1
pb.SC_SOCIETY_APPLYLIST_APPLYDATA_APPLICANT_CONTRIBUTION_FIELD.has_default_value = false
pb.SC_SOCIETY_APPLYLIST_APPLYDATA_APPLICANT_CONTRIBUTION_FIELD.default_value = 0
pb.SC_SOCIETY_APPLYLIST_APPLYDATA_APPLICANT_CONTRIBUTION_FIELD.type = 13
pb.SC_SOCIETY_APPLYLIST_APPLYDATA_APPLICANT_CONTRIBUTION_FIELD.cpp_type = 3

pb.SC_SOCIETY_APPLYLIST_APPLYDATA_APPLICANT.name = "Applicant"
pb.SC_SOCIETY_APPLYLIST_APPLYDATA_APPLICANT.full_name = ".SC_Society_ApplyList.ApplyData.Applicant"
pb.SC_SOCIETY_APPLYLIST_APPLYDATA_APPLICANT.nested_types = {}
pb.SC_SOCIETY_APPLYLIST_APPLYDATA_APPLICANT.enum_types = {}
pb.SC_SOCIETY_APPLYLIST_APPLYDATA_APPLICANT.fields = {pb.SC_SOCIETY_APPLYLIST_APPLYDATA_APPLICANT_ACTORID_FIELD, pb.SC_SOCIETY_APPLYLIST_APPLYDATA_APPLICANT_ACTORNAME_FIELD, pb.SC_SOCIETY_APPLYLIST_APPLYDATA_APPLICANT_POWER_FIELD, pb.SC_SOCIETY_APPLYLIST_APPLYDATA_APPLICANT_LEVEL_FIELD, pb.SC_SOCIETY_APPLYLIST_APPLYDATA_APPLICANT_CONTRIBUTION_FIELD}
pb.SC_SOCIETY_APPLYLIST_APPLYDATA_APPLICANT.is_extendable = false
pb.SC_SOCIETY_APPLYLIST_APPLYDATA_APPLICANT.extensions = {}
pb.SC_SOCIETY_APPLYLIST_APPLYDATA_APPLICANT.containing_type = pb.SC_SOCIETY_APPLYLIST_APPLYDATA
pb.SC_SOCIETY_APPLYLIST_APPLYDATA_PRIZEID_FIELD.name = "PrizeID"
pb.SC_SOCIETY_APPLYLIST_APPLYDATA_PRIZEID_FIELD.full_name = ".SC_Society_ApplyList.ApplyData.PrizeID"
pb.SC_SOCIETY_APPLYLIST_APPLYDATA_PRIZEID_FIELD.number = 1
pb.SC_SOCIETY_APPLYLIST_APPLYDATA_PRIZEID_FIELD.index = 0
pb.SC_SOCIETY_APPLYLIST_APPLYDATA_PRIZEID_FIELD.label = 1
pb.SC_SOCIETY_APPLYLIST_APPLYDATA_PRIZEID_FIELD.has_default_value = false
pb.SC_SOCIETY_APPLYLIST_APPLYDATA_PRIZEID_FIELD.default_value = 0
pb.SC_SOCIETY_APPLYLIST_APPLYDATA_PRIZEID_FIELD.type = 13
pb.SC_SOCIETY_APPLYLIST_APPLYDATA_PRIZEID_FIELD.cpp_type = 3

pb.SC_SOCIETY_APPLYLIST_APPLYDATA_PRIZENUM_FIELD.name = "PrizeNum"
pb.SC_SOCIETY_APPLYLIST_APPLYDATA_PRIZENUM_FIELD.full_name = ".SC_Society_ApplyList.ApplyData.PrizeNum"
pb.SC_SOCIETY_APPLYLIST_APPLYDATA_PRIZENUM_FIELD.number = 2
pb.SC_SOCIETY_APPLYLIST_APPLYDATA_PRIZENUM_FIELD.index = 1
pb.SC_SOCIETY_APPLYLIST_APPLYDATA_PRIZENUM_FIELD.label = 1
pb.SC_SOCIETY_APPLYLIST_APPLYDATA_PRIZENUM_FIELD.has_default_value = false
pb.SC_SOCIETY_APPLYLIST_APPLYDATA_PRIZENUM_FIELD.default_value = 0
pb.SC_SOCIETY_APPLYLIST_APPLYDATA_PRIZENUM_FIELD.type = 13
pb.SC_SOCIETY_APPLYLIST_APPLYDATA_PRIZENUM_FIELD.cpp_type = 3

pb.SC_SOCIETY_APPLYLIST_APPLYDATA_LASTTIME_FIELD.name = "LastTime"
pb.SC_SOCIETY_APPLYLIST_APPLYDATA_LASTTIME_FIELD.full_name = ".SC_Society_ApplyList.ApplyData.LastTime"
pb.SC_SOCIETY_APPLYLIST_APPLYDATA_LASTTIME_FIELD.number = 3
pb.SC_SOCIETY_APPLYLIST_APPLYDATA_LASTTIME_FIELD.index = 2
pb.SC_SOCIETY_APPLYLIST_APPLYDATA_LASTTIME_FIELD.label = 1
pb.SC_SOCIETY_APPLYLIST_APPLYDATA_LASTTIME_FIELD.has_default_value = false
pb.SC_SOCIETY_APPLYLIST_APPLYDATA_LASTTIME_FIELD.default_value = 0
pb.SC_SOCIETY_APPLYLIST_APPLYDATA_LASTTIME_FIELD.type = 13
pb.SC_SOCIETY_APPLYLIST_APPLYDATA_LASTTIME_FIELD.cpp_type = 3

pb.SC_SOCIETY_APPLYLIST_APPLYDATA_APPLICANTLIST_FIELD.name = "ApplicantList"
pb.SC_SOCIETY_APPLYLIST_APPLYDATA_APPLICANTLIST_FIELD.full_name = ".SC_Society_ApplyList.ApplyData.ApplicantList"
pb.SC_SOCIETY_APPLYLIST_APPLYDATA_APPLICANTLIST_FIELD.number = 4
pb.SC_SOCIETY_APPLYLIST_APPLYDATA_APPLICANTLIST_FIELD.index = 3
pb.SC_SOCIETY_APPLYLIST_APPLYDATA_APPLICANTLIST_FIELD.label = 3
pb.SC_SOCIETY_APPLYLIST_APPLYDATA_APPLICANTLIST_FIELD.has_default_value = false
pb.SC_SOCIETY_APPLYLIST_APPLYDATA_APPLICANTLIST_FIELD.default_value = {}
pb.SC_SOCIETY_APPLYLIST_APPLYDATA_APPLICANTLIST_FIELD.message_type = pb.SC_SOCIETY_APPLYLIST_APPLYDATA_APPLICANT
pb.SC_SOCIETY_APPLYLIST_APPLYDATA_APPLICANTLIST_FIELD.type = 11
pb.SC_SOCIETY_APPLYLIST_APPLYDATA_APPLICANTLIST_FIELD.cpp_type = 10

pb.SC_SOCIETY_APPLYLIST_APPLYDATA.name = "ApplyData"
pb.SC_SOCIETY_APPLYLIST_APPLYDATA.full_name = ".SC_Society_ApplyList.ApplyData"
pb.SC_SOCIETY_APPLYLIST_APPLYDATA.nested_types = {pb.SC_SOCIETY_APPLYLIST_APPLYDATA_APPLICANT}
pb.SC_SOCIETY_APPLYLIST_APPLYDATA.enum_types = {}
pb.SC_SOCIETY_APPLYLIST_APPLYDATA.fields = {pb.SC_SOCIETY_APPLYLIST_APPLYDATA_PRIZEID_FIELD, pb.SC_SOCIETY_APPLYLIST_APPLYDATA_PRIZENUM_FIELD, pb.SC_SOCIETY_APPLYLIST_APPLYDATA_LASTTIME_FIELD, pb.SC_SOCIETY_APPLYLIST_APPLYDATA_APPLICANTLIST_FIELD}
pb.SC_SOCIETY_APPLYLIST_APPLYDATA.is_extendable = false
pb.SC_SOCIETY_APPLYLIST_APPLYDATA.extensions = {}
pb.SC_SOCIETY_APPLYLIST_APPLYDATA.containing_type = pb.SC_SOCIETY_APPLYLIST
pb.SC_SOCIETY_APPLYLIST_APPLYLIST_FIELD.name = "ApplyList"
pb.SC_SOCIETY_APPLYLIST_APPLYLIST_FIELD.full_name = ".SC_Society_ApplyList.ApplyList"
pb.SC_SOCIETY_APPLYLIST_APPLYLIST_FIELD.number = 1
pb.SC_SOCIETY_APPLYLIST_APPLYLIST_FIELD.index = 0
pb.SC_SOCIETY_APPLYLIST_APPLYLIST_FIELD.label = 3
pb.SC_SOCIETY_APPLYLIST_APPLYLIST_FIELD.has_default_value = false
pb.SC_SOCIETY_APPLYLIST_APPLYLIST_FIELD.default_value = {}
pb.SC_SOCIETY_APPLYLIST_APPLYLIST_FIELD.message_type = pb.SC_SOCIETY_APPLYLIST_APPLYDATA
pb.SC_SOCIETY_APPLYLIST_APPLYLIST_FIELD.type = 11
pb.SC_SOCIETY_APPLYLIST_APPLYLIST_FIELD.cpp_type = 10

pb.SC_SOCIETY_APPLYLIST.name = "SC_Society_ApplyList"
pb.SC_SOCIETY_APPLYLIST.full_name = ".SC_Society_ApplyList"
pb.SC_SOCIETY_APPLYLIST.nested_types = {pb.SC_SOCIETY_APPLYLIST_APPLYDATA}
pb.SC_SOCIETY_APPLYLIST.enum_types = {}
pb.SC_SOCIETY_APPLYLIST.fields = {pb.SC_SOCIETY_APPLYLIST_APPLYLIST_FIELD}
pb.SC_SOCIETY_APPLYLIST.is_extendable = false
pb.SC_SOCIETY_APPLYLIST.extensions = {}
pb.SC_SOCIETY_JOINLISTBACK_USERINFO_ACTORID_FIELD.name = "ActorID"
pb.SC_SOCIETY_JOINLISTBACK_USERINFO_ACTORID_FIELD.full_name = ".SC_Society_JoinListBack.UserInfo.ActorID"
pb.SC_SOCIETY_JOINLISTBACK_USERINFO_ACTORID_FIELD.number = 1
pb.SC_SOCIETY_JOINLISTBACK_USERINFO_ACTORID_FIELD.index = 0
pb.SC_SOCIETY_JOINLISTBACK_USERINFO_ACTORID_FIELD.label = 1
pb.SC_SOCIETY_JOINLISTBACK_USERINFO_ACTORID_FIELD.has_default_value = false
pb.SC_SOCIETY_JOINLISTBACK_USERINFO_ACTORID_FIELD.default_value = 0
pb.SC_SOCIETY_JOINLISTBACK_USERINFO_ACTORID_FIELD.type = 13
pb.SC_SOCIETY_JOINLISTBACK_USERINFO_ACTORID_FIELD.cpp_type = 3

pb.SC_SOCIETY_JOINLISTBACK_USERINFO_ACTORNAME_FIELD.name = "ActorName"
pb.SC_SOCIETY_JOINLISTBACK_USERINFO_ACTORNAME_FIELD.full_name = ".SC_Society_JoinListBack.UserInfo.ActorName"
pb.SC_SOCIETY_JOINLISTBACK_USERINFO_ACTORNAME_FIELD.number = 2
pb.SC_SOCIETY_JOINLISTBACK_USERINFO_ACTORNAME_FIELD.index = 1
pb.SC_SOCIETY_JOINLISTBACK_USERINFO_ACTORNAME_FIELD.label = 1
pb.SC_SOCIETY_JOINLISTBACK_USERINFO_ACTORNAME_FIELD.has_default_value = false
pb.SC_SOCIETY_JOINLISTBACK_USERINFO_ACTORNAME_FIELD.default_value = ""
pb.SC_SOCIETY_JOINLISTBACK_USERINFO_ACTORNAME_FIELD.type = 9
pb.SC_SOCIETY_JOINLISTBACK_USERINFO_ACTORNAME_FIELD.cpp_type = 9

pb.SC_SOCIETY_JOINLISTBACK_USERINFO_POWER_FIELD.name = "Power"
pb.SC_SOCIETY_JOINLISTBACK_USERINFO_POWER_FIELD.full_name = ".SC_Society_JoinListBack.UserInfo.Power"
pb.SC_SOCIETY_JOINLISTBACK_USERINFO_POWER_FIELD.number = 3
pb.SC_SOCIETY_JOINLISTBACK_USERINFO_POWER_FIELD.index = 2
pb.SC_SOCIETY_JOINLISTBACK_USERINFO_POWER_FIELD.label = 1
pb.SC_SOCIETY_JOINLISTBACK_USERINFO_POWER_FIELD.has_default_value = false
pb.SC_SOCIETY_JOINLISTBACK_USERINFO_POWER_FIELD.default_value = 0
pb.SC_SOCIETY_JOINLISTBACK_USERINFO_POWER_FIELD.type = 13
pb.SC_SOCIETY_JOINLISTBACK_USERINFO_POWER_FIELD.cpp_type = 3

pb.SC_SOCIETY_JOINLISTBACK_USERINFO_LEVEL_FIELD.name = "Level"
pb.SC_SOCIETY_JOINLISTBACK_USERINFO_LEVEL_FIELD.full_name = ".SC_Society_JoinListBack.UserInfo.Level"
pb.SC_SOCIETY_JOINLISTBACK_USERINFO_LEVEL_FIELD.number = 4
pb.SC_SOCIETY_JOINLISTBACK_USERINFO_LEVEL_FIELD.index = 3
pb.SC_SOCIETY_JOINLISTBACK_USERINFO_LEVEL_FIELD.label = 1
pb.SC_SOCIETY_JOINLISTBACK_USERINFO_LEVEL_FIELD.has_default_value = false
pb.SC_SOCIETY_JOINLISTBACK_USERINFO_LEVEL_FIELD.default_value = 0
pb.SC_SOCIETY_JOINLISTBACK_USERINFO_LEVEL_FIELD.type = 13
pb.SC_SOCIETY_JOINLISTBACK_USERINFO_LEVEL_FIELD.cpp_type = 3

pb.SC_SOCIETY_JOINLISTBACK_USERINFO_VOCATION_FIELD.name = "Vocation"
pb.SC_SOCIETY_JOINLISTBACK_USERINFO_VOCATION_FIELD.full_name = ".SC_Society_JoinListBack.UserInfo.Vocation"
pb.SC_SOCIETY_JOINLISTBACK_USERINFO_VOCATION_FIELD.number = 5
pb.SC_SOCIETY_JOINLISTBACK_USERINFO_VOCATION_FIELD.index = 4
pb.SC_SOCIETY_JOINLISTBACK_USERINFO_VOCATION_FIELD.label = 1
pb.SC_SOCIETY_JOINLISTBACK_USERINFO_VOCATION_FIELD.has_default_value = false
pb.SC_SOCIETY_JOINLISTBACK_USERINFO_VOCATION_FIELD.default_value = 0
pb.SC_SOCIETY_JOINLISTBACK_USERINFO_VOCATION_FIELD.type = 13
pb.SC_SOCIETY_JOINLISTBACK_USERINFO_VOCATION_FIELD.cpp_type = 3

pb.SC_SOCIETY_JOINLISTBACK_USERINFO_BLUEVIPLEVEL_FIELD.name = "BlueVIPLevel"
pb.SC_SOCIETY_JOINLISTBACK_USERINFO_BLUEVIPLEVEL_FIELD.full_name = ".SC_Society_JoinListBack.UserInfo.BlueVIPLevel"
pb.SC_SOCIETY_JOINLISTBACK_USERINFO_BLUEVIPLEVEL_FIELD.number = 6
pb.SC_SOCIETY_JOINLISTBACK_USERINFO_BLUEVIPLEVEL_FIELD.index = 5
pb.SC_SOCIETY_JOINLISTBACK_USERINFO_BLUEVIPLEVEL_FIELD.label = 1
pb.SC_SOCIETY_JOINLISTBACK_USERINFO_BLUEVIPLEVEL_FIELD.has_default_value = false
pb.SC_SOCIETY_JOINLISTBACK_USERINFO_BLUEVIPLEVEL_FIELD.default_value = 0
pb.SC_SOCIETY_JOINLISTBACK_USERINFO_BLUEVIPLEVEL_FIELD.type = 13
pb.SC_SOCIETY_JOINLISTBACK_USERINFO_BLUEVIPLEVEL_FIELD.cpp_type = 3

pb.SC_SOCIETY_JOINLISTBACK_USERINFO_BLUEVIPTYPE_FIELD.name = "BlueVIPType"
pb.SC_SOCIETY_JOINLISTBACK_USERINFO_BLUEVIPTYPE_FIELD.full_name = ".SC_Society_JoinListBack.UserInfo.BlueVIPType"
pb.SC_SOCIETY_JOINLISTBACK_USERINFO_BLUEVIPTYPE_FIELD.number = 7
pb.SC_SOCIETY_JOINLISTBACK_USERINFO_BLUEVIPTYPE_FIELD.index = 6
pb.SC_SOCIETY_JOINLISTBACK_USERINFO_BLUEVIPTYPE_FIELD.label = 1
pb.SC_SOCIETY_JOINLISTBACK_USERINFO_BLUEVIPTYPE_FIELD.has_default_value = false
pb.SC_SOCIETY_JOINLISTBACK_USERINFO_BLUEVIPTYPE_FIELD.default_value = 0
pb.SC_SOCIETY_JOINLISTBACK_USERINFO_BLUEVIPTYPE_FIELD.type = 13
pb.SC_SOCIETY_JOINLISTBACK_USERINFO_BLUEVIPTYPE_FIELD.cpp_type = 3

pb.SC_SOCIETY_JOINLISTBACK_USERINFO.name = "UserInfo"
pb.SC_SOCIETY_JOINLISTBACK_USERINFO.full_name = ".SC_Society_JoinListBack.UserInfo"
pb.SC_SOCIETY_JOINLISTBACK_USERINFO.nested_types = {}
pb.SC_SOCIETY_JOINLISTBACK_USERINFO.enum_types = {}
pb.SC_SOCIETY_JOINLISTBACK_USERINFO.fields = {pb.SC_SOCIETY_JOINLISTBACK_USERINFO_ACTORID_FIELD, pb.SC_SOCIETY_JOINLISTBACK_USERINFO_ACTORNAME_FIELD, pb.SC_SOCIETY_JOINLISTBACK_USERINFO_POWER_FIELD, pb.SC_SOCIETY_JOINLISTBACK_USERINFO_LEVEL_FIELD, pb.SC_SOCIETY_JOINLISTBACK_USERINFO_VOCATION_FIELD, pb.SC_SOCIETY_JOINLISTBACK_USERINFO_BLUEVIPLEVEL_FIELD, pb.SC_SOCIETY_JOINLISTBACK_USERINFO_BLUEVIPTYPE_FIELD}
pb.SC_SOCIETY_JOINLISTBACK_USERINFO.is_extendable = false
pb.SC_SOCIETY_JOINLISTBACK_USERINFO.extensions = {}
pb.SC_SOCIETY_JOINLISTBACK_USERINFO.containing_type = pb.SC_SOCIETY_JOINLISTBACK
pb.SC_SOCIETY_JOINLISTBACK_JOINLIST_FIELD.name = "JoinList"
pb.SC_SOCIETY_JOINLISTBACK_JOINLIST_FIELD.full_name = ".SC_Society_JoinListBack.JoinList"
pb.SC_SOCIETY_JOINLISTBACK_JOINLIST_FIELD.number = 1
pb.SC_SOCIETY_JOINLISTBACK_JOINLIST_FIELD.index = 0
pb.SC_SOCIETY_JOINLISTBACK_JOINLIST_FIELD.label = 3
pb.SC_SOCIETY_JOINLISTBACK_JOINLIST_FIELD.has_default_value = false
pb.SC_SOCIETY_JOINLISTBACK_JOINLIST_FIELD.default_value = {}
pb.SC_SOCIETY_JOINLISTBACK_JOINLIST_FIELD.message_type = pb.SC_SOCIETY_JOINLISTBACK_USERINFO
pb.SC_SOCIETY_JOINLISTBACK_JOINLIST_FIELD.type = 11
pb.SC_SOCIETY_JOINLISTBACK_JOINLIST_FIELD.cpp_type = 10

pb.SC_SOCIETY_JOINLISTBACK_COUNTNUM_FIELD.name = "CountNum"
pb.SC_SOCIETY_JOINLISTBACK_COUNTNUM_FIELD.full_name = ".SC_Society_JoinListBack.CountNum"
pb.SC_SOCIETY_JOINLISTBACK_COUNTNUM_FIELD.number = 2
pb.SC_SOCIETY_JOINLISTBACK_COUNTNUM_FIELD.index = 1
pb.SC_SOCIETY_JOINLISTBACK_COUNTNUM_FIELD.label = 1
pb.SC_SOCIETY_JOINLISTBACK_COUNTNUM_FIELD.has_default_value = false
pb.SC_SOCIETY_JOINLISTBACK_COUNTNUM_FIELD.default_value = 0
pb.SC_SOCIETY_JOINLISTBACK_COUNTNUM_FIELD.type = 13
pb.SC_SOCIETY_JOINLISTBACK_COUNTNUM_FIELD.cpp_type = 3

pb.SC_SOCIETY_JOINLISTBACK_ADDFLAG_FIELD.name = "AddFlag"
pb.SC_SOCIETY_JOINLISTBACK_ADDFLAG_FIELD.full_name = ".SC_Society_JoinListBack.AddFlag"
pb.SC_SOCIETY_JOINLISTBACK_ADDFLAG_FIELD.number = 3
pb.SC_SOCIETY_JOINLISTBACK_ADDFLAG_FIELD.index = 2
pb.SC_SOCIETY_JOINLISTBACK_ADDFLAG_FIELD.label = 1
pb.SC_SOCIETY_JOINLISTBACK_ADDFLAG_FIELD.has_default_value = false
pb.SC_SOCIETY_JOINLISTBACK_ADDFLAG_FIELD.default_value = 0
pb.SC_SOCIETY_JOINLISTBACK_ADDFLAG_FIELD.type = 13
pb.SC_SOCIETY_JOINLISTBACK_ADDFLAG_FIELD.cpp_type = 3

pb.SC_SOCIETY_JOINLISTBACK.name = "SC_Society_JoinListBack"
pb.SC_SOCIETY_JOINLISTBACK.full_name = ".SC_Society_JoinListBack"
pb.SC_SOCIETY_JOINLISTBACK.nested_types = {pb.SC_SOCIETY_JOINLISTBACK_USERINFO}
pb.SC_SOCIETY_JOINLISTBACK.enum_types = {}
pb.SC_SOCIETY_JOINLISTBACK.fields = {pb.SC_SOCIETY_JOINLISTBACK_JOINLIST_FIELD, pb.SC_SOCIETY_JOINLISTBACK_COUNTNUM_FIELD, pb.SC_SOCIETY_JOINLISTBACK_ADDFLAG_FIELD}
pb.SC_SOCIETY_JOINLISTBACK.is_extendable = false
pb.SC_SOCIETY_JOINLISTBACK.extensions = {}
pb.SC_SOCIETY_MSGCACHE_MSGINFO_ACTORID_FIELD.name = "ActorID"
pb.SC_SOCIETY_MSGCACHE_MSGINFO_ACTORID_FIELD.full_name = ".SC_Society_MsgCache.MsgInfo.ActorID"
pb.SC_SOCIETY_MSGCACHE_MSGINFO_ACTORID_FIELD.number = 1
pb.SC_SOCIETY_MSGCACHE_MSGINFO_ACTORID_FIELD.index = 0
pb.SC_SOCIETY_MSGCACHE_MSGINFO_ACTORID_FIELD.label = 1
pb.SC_SOCIETY_MSGCACHE_MSGINFO_ACTORID_FIELD.has_default_value = false
pb.SC_SOCIETY_MSGCACHE_MSGINFO_ACTORID_FIELD.default_value = 0
pb.SC_SOCIETY_MSGCACHE_MSGINFO_ACTORID_FIELD.type = 13
pb.SC_SOCIETY_MSGCACHE_MSGINFO_ACTORID_FIELD.cpp_type = 3

pb.SC_SOCIETY_MSGCACHE_MSGINFO_SENDTIME_FIELD.name = "SendTime"
pb.SC_SOCIETY_MSGCACHE_MSGINFO_SENDTIME_FIELD.full_name = ".SC_Society_MsgCache.MsgInfo.SendTime"
pb.SC_SOCIETY_MSGCACHE_MSGINFO_SENDTIME_FIELD.number = 2
pb.SC_SOCIETY_MSGCACHE_MSGINFO_SENDTIME_FIELD.index = 1
pb.SC_SOCIETY_MSGCACHE_MSGINFO_SENDTIME_FIELD.label = 1
pb.SC_SOCIETY_MSGCACHE_MSGINFO_SENDTIME_FIELD.has_default_value = false
pb.SC_SOCIETY_MSGCACHE_MSGINFO_SENDTIME_FIELD.default_value = 0
pb.SC_SOCIETY_MSGCACHE_MSGINFO_SENDTIME_FIELD.type = 13
pb.SC_SOCIETY_MSGCACHE_MSGINFO_SENDTIME_FIELD.cpp_type = 3

pb.SC_SOCIETY_MSGCACHE_MSGINFO_ACTORNAME_FIELD.name = "ActorName"
pb.SC_SOCIETY_MSGCACHE_MSGINFO_ACTORNAME_FIELD.full_name = ".SC_Society_MsgCache.MsgInfo.ActorName"
pb.SC_SOCIETY_MSGCACHE_MSGINFO_ACTORNAME_FIELD.number = 3
pb.SC_SOCIETY_MSGCACHE_MSGINFO_ACTORNAME_FIELD.index = 2
pb.SC_SOCIETY_MSGCACHE_MSGINFO_ACTORNAME_FIELD.label = 1
pb.SC_SOCIETY_MSGCACHE_MSGINFO_ACTORNAME_FIELD.has_default_value = false
pb.SC_SOCIETY_MSGCACHE_MSGINFO_ACTORNAME_FIELD.default_value = ""
pb.SC_SOCIETY_MSGCACHE_MSGINFO_ACTORNAME_FIELD.type = 9
pb.SC_SOCIETY_MSGCACHE_MSGINFO_ACTORNAME_FIELD.cpp_type = 9

pb.SC_SOCIETY_MSGCACHE_MSGINFO_MSG_FIELD.name = "Msg"
pb.SC_SOCIETY_MSGCACHE_MSGINFO_MSG_FIELD.full_name = ".SC_Society_MsgCache.MsgInfo.Msg"
pb.SC_SOCIETY_MSGCACHE_MSGINFO_MSG_FIELD.number = 4
pb.SC_SOCIETY_MSGCACHE_MSGINFO_MSG_FIELD.index = 3
pb.SC_SOCIETY_MSGCACHE_MSGINFO_MSG_FIELD.label = 1
pb.SC_SOCIETY_MSGCACHE_MSGINFO_MSG_FIELD.has_default_value = false
pb.SC_SOCIETY_MSGCACHE_MSGINFO_MSG_FIELD.default_value = ""
pb.SC_SOCIETY_MSGCACHE_MSGINFO_MSG_FIELD.type = 9
pb.SC_SOCIETY_MSGCACHE_MSGINFO_MSG_FIELD.cpp_type = 9

pb.SC_SOCIETY_MSGCACHE_MSGINFO.name = "MsgInfo"
pb.SC_SOCIETY_MSGCACHE_MSGINFO.full_name = ".SC_Society_MsgCache.MsgInfo"
pb.SC_SOCIETY_MSGCACHE_MSGINFO.nested_types = {}
pb.SC_SOCIETY_MSGCACHE_MSGINFO.enum_types = {}
pb.SC_SOCIETY_MSGCACHE_MSGINFO.fields = {pb.SC_SOCIETY_MSGCACHE_MSGINFO_ACTORID_FIELD, pb.SC_SOCIETY_MSGCACHE_MSGINFO_SENDTIME_FIELD, pb.SC_SOCIETY_MSGCACHE_MSGINFO_ACTORNAME_FIELD, pb.SC_SOCIETY_MSGCACHE_MSGINFO_MSG_FIELD}
pb.SC_SOCIETY_MSGCACHE_MSGINFO.is_extendable = false
pb.SC_SOCIETY_MSGCACHE_MSGINFO.extensions = {}
pb.SC_SOCIETY_MSGCACHE_MSGINFO.containing_type = pb.SC_SOCIETY_MSGCACHE
pb.SC_SOCIETY_MSGCACHE_MSGLIST_FIELD.name = "MsgList"
pb.SC_SOCIETY_MSGCACHE_MSGLIST_FIELD.full_name = ".SC_Society_MsgCache.MsgList"
pb.SC_SOCIETY_MSGCACHE_MSGLIST_FIELD.number = 1
pb.SC_SOCIETY_MSGCACHE_MSGLIST_FIELD.index = 0
pb.SC_SOCIETY_MSGCACHE_MSGLIST_FIELD.label = 3
pb.SC_SOCIETY_MSGCACHE_MSGLIST_FIELD.has_default_value = false
pb.SC_SOCIETY_MSGCACHE_MSGLIST_FIELD.default_value = {}
pb.SC_SOCIETY_MSGCACHE_MSGLIST_FIELD.message_type = pb.SC_SOCIETY_MSGCACHE_MSGINFO
pb.SC_SOCIETY_MSGCACHE_MSGLIST_FIELD.type = 11
pb.SC_SOCIETY_MSGCACHE_MSGLIST_FIELD.cpp_type = 10

pb.SC_SOCIETY_MSGCACHE.name = "SC_Society_MsgCache"
pb.SC_SOCIETY_MSGCACHE.full_name = ".SC_Society_MsgCache"
pb.SC_SOCIETY_MSGCACHE.nested_types = {pb.SC_SOCIETY_MSGCACHE_MSGINFO}
pb.SC_SOCIETY_MSGCACHE.enum_types = {}
pb.SC_SOCIETY_MSGCACHE.fields = {pb.SC_SOCIETY_MSGCACHE_MSGLIST_FIELD}
pb.SC_SOCIETY_MSGCACHE.is_extendable = false
pb.SC_SOCIETY_MSGCACHE.extensions = {}
pb.SC_SOCIETY_TASKLIST_TASKID1_FIELD.name = "TaskID1"
pb.SC_SOCIETY_TASKLIST_TASKID1_FIELD.full_name = ".SC_Society_TaskList.TaskID1"
pb.SC_SOCIETY_TASKLIST_TASKID1_FIELD.number = 1
pb.SC_SOCIETY_TASKLIST_TASKID1_FIELD.index = 0
pb.SC_SOCIETY_TASKLIST_TASKID1_FIELD.label = 1
pb.SC_SOCIETY_TASKLIST_TASKID1_FIELD.has_default_value = false
pb.SC_SOCIETY_TASKLIST_TASKID1_FIELD.default_value = 0
pb.SC_SOCIETY_TASKLIST_TASKID1_FIELD.type = 13
pb.SC_SOCIETY_TASKLIST_TASKID1_FIELD.cpp_type = 3

pb.SC_SOCIETY_TASKLIST_TASKID2_FIELD.name = "TaskID2"
pb.SC_SOCIETY_TASKLIST_TASKID2_FIELD.full_name = ".SC_Society_TaskList.TaskID2"
pb.SC_SOCIETY_TASKLIST_TASKID2_FIELD.number = 2
pb.SC_SOCIETY_TASKLIST_TASKID2_FIELD.index = 1
pb.SC_SOCIETY_TASKLIST_TASKID2_FIELD.label = 1
pb.SC_SOCIETY_TASKLIST_TASKID2_FIELD.has_default_value = false
pb.SC_SOCIETY_TASKLIST_TASKID2_FIELD.default_value = 0
pb.SC_SOCIETY_TASKLIST_TASKID2_FIELD.type = 13
pb.SC_SOCIETY_TASKLIST_TASKID2_FIELD.cpp_type = 3

pb.SC_SOCIETY_TASKLIST_TASKID3_FIELD.name = "TaskID3"
pb.SC_SOCIETY_TASKLIST_TASKID3_FIELD.full_name = ".SC_Society_TaskList.TaskID3"
pb.SC_SOCIETY_TASKLIST_TASKID3_FIELD.number = 3
pb.SC_SOCIETY_TASKLIST_TASKID3_FIELD.index = 2
pb.SC_SOCIETY_TASKLIST_TASKID3_FIELD.label = 1
pb.SC_SOCIETY_TASKLIST_TASKID3_FIELD.has_default_value = false
pb.SC_SOCIETY_TASKLIST_TASKID3_FIELD.default_value = 0
pb.SC_SOCIETY_TASKLIST_TASKID3_FIELD.type = 13
pb.SC_SOCIETY_TASKLIST_TASKID3_FIELD.cpp_type = 3

pb.SC_SOCIETY_TASKLIST_TASKID4_FIELD.name = "TaskID4"
pb.SC_SOCIETY_TASKLIST_TASKID4_FIELD.full_name = ".SC_Society_TaskList.TaskID4"
pb.SC_SOCIETY_TASKLIST_TASKID4_FIELD.number = 4
pb.SC_SOCIETY_TASKLIST_TASKID4_FIELD.index = 3
pb.SC_SOCIETY_TASKLIST_TASKID4_FIELD.label = 1
pb.SC_SOCIETY_TASKLIST_TASKID4_FIELD.has_default_value = false
pb.SC_SOCIETY_TASKLIST_TASKID4_FIELD.default_value = 0
pb.SC_SOCIETY_TASKLIST_TASKID4_FIELD.type = 13
pb.SC_SOCIETY_TASKLIST_TASKID4_FIELD.cpp_type = 3

pb.SC_SOCIETY_TASKLIST.name = "SC_Society_TaskList"
pb.SC_SOCIETY_TASKLIST.full_name = ".SC_Society_TaskList"
pb.SC_SOCIETY_TASKLIST.nested_types = {}
pb.SC_SOCIETY_TASKLIST.enum_types = {}
pb.SC_SOCIETY_TASKLIST.fields = {pb.SC_SOCIETY_TASKLIST_TASKID1_FIELD, pb.SC_SOCIETY_TASKLIST_TASKID2_FIELD, pb.SC_SOCIETY_TASKLIST_TASKID3_FIELD, pb.SC_SOCIETY_TASKLIST_TASKID4_FIELD}
pb.SC_SOCIETY_TASKLIST.is_extendable = false
pb.SC_SOCIETY_TASKLIST.extensions = {}
pb.SC_SOCIETY_DISTRIBUTEHISTORY_HISTORYINFO_ACTORID_FIELD.name = "ActorID"
pb.SC_SOCIETY_DISTRIBUTEHISTORY_HISTORYINFO_ACTORID_FIELD.full_name = ".SC_Society_DistributeHistory.HistoryInfo.ActorID"
pb.SC_SOCIETY_DISTRIBUTEHISTORY_HISTORYINFO_ACTORID_FIELD.number = 1
pb.SC_SOCIETY_DISTRIBUTEHISTORY_HISTORYINFO_ACTORID_FIELD.index = 0
pb.SC_SOCIETY_DISTRIBUTEHISTORY_HISTORYINFO_ACTORID_FIELD.label = 1
pb.SC_SOCIETY_DISTRIBUTEHISTORY_HISTORYINFO_ACTORID_FIELD.has_default_value = false
pb.SC_SOCIETY_DISTRIBUTEHISTORY_HISTORYINFO_ACTORID_FIELD.default_value = 0
pb.SC_SOCIETY_DISTRIBUTEHISTORY_HISTORYINFO_ACTORID_FIELD.type = 13
pb.SC_SOCIETY_DISTRIBUTEHISTORY_HISTORYINFO_ACTORID_FIELD.cpp_type = 3

pb.SC_SOCIETY_DISTRIBUTEHISTORY_HISTORYINFO_TIME_FIELD.name = "Time"
pb.SC_SOCIETY_DISTRIBUTEHISTORY_HISTORYINFO_TIME_FIELD.full_name = ".SC_Society_DistributeHistory.HistoryInfo.Time"
pb.SC_SOCIETY_DISTRIBUTEHISTORY_HISTORYINFO_TIME_FIELD.number = 2
pb.SC_SOCIETY_DISTRIBUTEHISTORY_HISTORYINFO_TIME_FIELD.index = 1
pb.SC_SOCIETY_DISTRIBUTEHISTORY_HISTORYINFO_TIME_FIELD.label = 1
pb.SC_SOCIETY_DISTRIBUTEHISTORY_HISTORYINFO_TIME_FIELD.has_default_value = false
pb.SC_SOCIETY_DISTRIBUTEHISTORY_HISTORYINFO_TIME_FIELD.default_value = 0
pb.SC_SOCIETY_DISTRIBUTEHISTORY_HISTORYINFO_TIME_FIELD.type = 13
pb.SC_SOCIETY_DISTRIBUTEHISTORY_HISTORYINFO_TIME_FIELD.cpp_type = 3

pb.SC_SOCIETY_DISTRIBUTEHISTORY_HISTORYINFO_ACTORNAME_FIELD.name = "ActorName"
pb.SC_SOCIETY_DISTRIBUTEHISTORY_HISTORYINFO_ACTORNAME_FIELD.full_name = ".SC_Society_DistributeHistory.HistoryInfo.ActorName"
pb.SC_SOCIETY_DISTRIBUTEHISTORY_HISTORYINFO_ACTORNAME_FIELD.number = 3
pb.SC_SOCIETY_DISTRIBUTEHISTORY_HISTORYINFO_ACTORNAME_FIELD.index = 2
pb.SC_SOCIETY_DISTRIBUTEHISTORY_HISTORYINFO_ACTORNAME_FIELD.label = 1
pb.SC_SOCIETY_DISTRIBUTEHISTORY_HISTORYINFO_ACTORNAME_FIELD.has_default_value = false
pb.SC_SOCIETY_DISTRIBUTEHISTORY_HISTORYINFO_ACTORNAME_FIELD.default_value = ""
pb.SC_SOCIETY_DISTRIBUTEHISTORY_HISTORYINFO_ACTORNAME_FIELD.type = 9
pb.SC_SOCIETY_DISTRIBUTEHISTORY_HISTORYINFO_ACTORNAME_FIELD.cpp_type = 9

pb.SC_SOCIETY_DISTRIBUTEHISTORY_HISTORYINFO_ITEMID_FIELD.name = "ItemID"
pb.SC_SOCIETY_DISTRIBUTEHISTORY_HISTORYINFO_ITEMID_FIELD.full_name = ".SC_Society_DistributeHistory.HistoryInfo.ItemID"
pb.SC_SOCIETY_DISTRIBUTEHISTORY_HISTORYINFO_ITEMID_FIELD.number = 4
pb.SC_SOCIETY_DISTRIBUTEHISTORY_HISTORYINFO_ITEMID_FIELD.index = 3
pb.SC_SOCIETY_DISTRIBUTEHISTORY_HISTORYINFO_ITEMID_FIELD.label = 1
pb.SC_SOCIETY_DISTRIBUTEHISTORY_HISTORYINFO_ITEMID_FIELD.has_default_value = false
pb.SC_SOCIETY_DISTRIBUTEHISTORY_HISTORYINFO_ITEMID_FIELD.default_value = 0
pb.SC_SOCIETY_DISTRIBUTEHISTORY_HISTORYINFO_ITEMID_FIELD.type = 13
pb.SC_SOCIETY_DISTRIBUTEHISTORY_HISTORYINFO_ITEMID_FIELD.cpp_type = 3

pb.SC_SOCIETY_DISTRIBUTEHISTORY_HISTORYINFO_ITEMNUM_FIELD.name = "ItemNum"
pb.SC_SOCIETY_DISTRIBUTEHISTORY_HISTORYINFO_ITEMNUM_FIELD.full_name = ".SC_Society_DistributeHistory.HistoryInfo.ItemNum"
pb.SC_SOCIETY_DISTRIBUTEHISTORY_HISTORYINFO_ITEMNUM_FIELD.number = 5
pb.SC_SOCIETY_DISTRIBUTEHISTORY_HISTORYINFO_ITEMNUM_FIELD.index = 4
pb.SC_SOCIETY_DISTRIBUTEHISTORY_HISTORYINFO_ITEMNUM_FIELD.label = 1
pb.SC_SOCIETY_DISTRIBUTEHISTORY_HISTORYINFO_ITEMNUM_FIELD.has_default_value = false
pb.SC_SOCIETY_DISTRIBUTEHISTORY_HISTORYINFO_ITEMNUM_FIELD.default_value = 0
pb.SC_SOCIETY_DISTRIBUTEHISTORY_HISTORYINFO_ITEMNUM_FIELD.type = 13
pb.SC_SOCIETY_DISTRIBUTEHISTORY_HISTORYINFO_ITEMNUM_FIELD.cpp_type = 3

pb.SC_SOCIETY_DISTRIBUTEHISTORY_HISTORYINFO_ACTORLEVEL_FIELD.name = "ActorLevel"
pb.SC_SOCIETY_DISTRIBUTEHISTORY_HISTORYINFO_ACTORLEVEL_FIELD.full_name = ".SC_Society_DistributeHistory.HistoryInfo.ActorLevel"
pb.SC_SOCIETY_DISTRIBUTEHISTORY_HISTORYINFO_ACTORLEVEL_FIELD.number = 6
pb.SC_SOCIETY_DISTRIBUTEHISTORY_HISTORYINFO_ACTORLEVEL_FIELD.index = 5
pb.SC_SOCIETY_DISTRIBUTEHISTORY_HISTORYINFO_ACTORLEVEL_FIELD.label = 1
pb.SC_SOCIETY_DISTRIBUTEHISTORY_HISTORYINFO_ACTORLEVEL_FIELD.has_default_value = false
pb.SC_SOCIETY_DISTRIBUTEHISTORY_HISTORYINFO_ACTORLEVEL_FIELD.default_value = 0
pb.SC_SOCIETY_DISTRIBUTEHISTORY_HISTORYINFO_ACTORLEVEL_FIELD.type = 13
pb.SC_SOCIETY_DISTRIBUTEHISTORY_HISTORYINFO_ACTORLEVEL_FIELD.cpp_type = 3

pb.SC_SOCIETY_DISTRIBUTEHISTORY_HISTORYINFO_TITLE_FIELD.name = "Title"
pb.SC_SOCIETY_DISTRIBUTEHISTORY_HISTORYINFO_TITLE_FIELD.full_name = ".SC_Society_DistributeHistory.HistoryInfo.Title"
pb.SC_SOCIETY_DISTRIBUTEHISTORY_HISTORYINFO_TITLE_FIELD.number = 7
pb.SC_SOCIETY_DISTRIBUTEHISTORY_HISTORYINFO_TITLE_FIELD.index = 6
pb.SC_SOCIETY_DISTRIBUTEHISTORY_HISTORYINFO_TITLE_FIELD.label = 1
pb.SC_SOCIETY_DISTRIBUTEHISTORY_HISTORYINFO_TITLE_FIELD.has_default_value = false
pb.SC_SOCIETY_DISTRIBUTEHISTORY_HISTORYINFO_TITLE_FIELD.default_value = 0
pb.SC_SOCIETY_DISTRIBUTEHISTORY_HISTORYINFO_TITLE_FIELD.type = 13
pb.SC_SOCIETY_DISTRIBUTEHISTORY_HISTORYINFO_TITLE_FIELD.cpp_type = 3

pb.SC_SOCIETY_DISTRIBUTEHISTORY_HISTORYINFO_GIVEPRIZEACTORID_FIELD.name = "GivePrizeActorID"
pb.SC_SOCIETY_DISTRIBUTEHISTORY_HISTORYINFO_GIVEPRIZEACTORID_FIELD.full_name = ".SC_Society_DistributeHistory.HistoryInfo.GivePrizeActorID"
pb.SC_SOCIETY_DISTRIBUTEHISTORY_HISTORYINFO_GIVEPRIZEACTORID_FIELD.number = 8
pb.SC_SOCIETY_DISTRIBUTEHISTORY_HISTORYINFO_GIVEPRIZEACTORID_FIELD.index = 7
pb.SC_SOCIETY_DISTRIBUTEHISTORY_HISTORYINFO_GIVEPRIZEACTORID_FIELD.label = 1
pb.SC_SOCIETY_DISTRIBUTEHISTORY_HISTORYINFO_GIVEPRIZEACTORID_FIELD.has_default_value = false
pb.SC_SOCIETY_DISTRIBUTEHISTORY_HISTORYINFO_GIVEPRIZEACTORID_FIELD.default_value = 0
pb.SC_SOCIETY_DISTRIBUTEHISTORY_HISTORYINFO_GIVEPRIZEACTORID_FIELD.type = 13
pb.SC_SOCIETY_DISTRIBUTEHISTORY_HISTORYINFO_GIVEPRIZEACTORID_FIELD.cpp_type = 3

pb.SC_SOCIETY_DISTRIBUTEHISTORY_HISTORYINFO_GIVEPRIZEACTORNAME_FIELD.name = "GivePrizeActorName"
pb.SC_SOCIETY_DISTRIBUTEHISTORY_HISTORYINFO_GIVEPRIZEACTORNAME_FIELD.full_name = ".SC_Society_DistributeHistory.HistoryInfo.GivePrizeActorName"
pb.SC_SOCIETY_DISTRIBUTEHISTORY_HISTORYINFO_GIVEPRIZEACTORNAME_FIELD.number = 9
pb.SC_SOCIETY_DISTRIBUTEHISTORY_HISTORYINFO_GIVEPRIZEACTORNAME_FIELD.index = 8
pb.SC_SOCIETY_DISTRIBUTEHISTORY_HISTORYINFO_GIVEPRIZEACTORNAME_FIELD.label = 1
pb.SC_SOCIETY_DISTRIBUTEHISTORY_HISTORYINFO_GIVEPRIZEACTORNAME_FIELD.has_default_value = false
pb.SC_SOCIETY_DISTRIBUTEHISTORY_HISTORYINFO_GIVEPRIZEACTORNAME_FIELD.default_value = ""
pb.SC_SOCIETY_DISTRIBUTEHISTORY_HISTORYINFO_GIVEPRIZEACTORNAME_FIELD.type = 9
pb.SC_SOCIETY_DISTRIBUTEHISTORY_HISTORYINFO_GIVEPRIZEACTORNAME_FIELD.cpp_type = 9

pb.SC_SOCIETY_DISTRIBUTEHISTORY_HISTORYINFO.name = "HistoryInfo"
pb.SC_SOCIETY_DISTRIBUTEHISTORY_HISTORYINFO.full_name = ".SC_Society_DistributeHistory.HistoryInfo"
pb.SC_SOCIETY_DISTRIBUTEHISTORY_HISTORYINFO.nested_types = {}
pb.SC_SOCIETY_DISTRIBUTEHISTORY_HISTORYINFO.enum_types = {}
pb.SC_SOCIETY_DISTRIBUTEHISTORY_HISTORYINFO.fields = {pb.SC_SOCIETY_DISTRIBUTEHISTORY_HISTORYINFO_ACTORID_FIELD, pb.SC_SOCIETY_DISTRIBUTEHISTORY_HISTORYINFO_TIME_FIELD, pb.SC_SOCIETY_DISTRIBUTEHISTORY_HISTORYINFO_ACTORNAME_FIELD, pb.SC_SOCIETY_DISTRIBUTEHISTORY_HISTORYINFO_ITEMID_FIELD, pb.SC_SOCIETY_DISTRIBUTEHISTORY_HISTORYINFO_ITEMNUM_FIELD, pb.SC_SOCIETY_DISTRIBUTEHISTORY_HISTORYINFO_ACTORLEVEL_FIELD, pb.SC_SOCIETY_DISTRIBUTEHISTORY_HISTORYINFO_TITLE_FIELD, pb.SC_SOCIETY_DISTRIBUTEHISTORY_HISTORYINFO_GIVEPRIZEACTORID_FIELD, pb.SC_SOCIETY_DISTRIBUTEHISTORY_HISTORYINFO_GIVEPRIZEACTORNAME_FIELD}
pb.SC_SOCIETY_DISTRIBUTEHISTORY_HISTORYINFO.is_extendable = false
pb.SC_SOCIETY_DISTRIBUTEHISTORY_HISTORYINFO.extensions = {}
pb.SC_SOCIETY_DISTRIBUTEHISTORY_HISTORYINFO.containing_type = pb.SC_SOCIETY_DISTRIBUTEHISTORY
pb.SC_SOCIETY_DISTRIBUTEHISTORY_HISTORYLIST_FIELD.name = "HistoryList"
pb.SC_SOCIETY_DISTRIBUTEHISTORY_HISTORYLIST_FIELD.full_name = ".SC_Society_DistributeHistory.HistoryList"
pb.SC_SOCIETY_DISTRIBUTEHISTORY_HISTORYLIST_FIELD.number = 1
pb.SC_SOCIETY_DISTRIBUTEHISTORY_HISTORYLIST_FIELD.index = 0
pb.SC_SOCIETY_DISTRIBUTEHISTORY_HISTORYLIST_FIELD.label = 3
pb.SC_SOCIETY_DISTRIBUTEHISTORY_HISTORYLIST_FIELD.has_default_value = false
pb.SC_SOCIETY_DISTRIBUTEHISTORY_HISTORYLIST_FIELD.default_value = {}
pb.SC_SOCIETY_DISTRIBUTEHISTORY_HISTORYLIST_FIELD.message_type = pb.SC_SOCIETY_DISTRIBUTEHISTORY_HISTORYINFO
pb.SC_SOCIETY_DISTRIBUTEHISTORY_HISTORYLIST_FIELD.type = 11
pb.SC_SOCIETY_DISTRIBUTEHISTORY_HISTORYLIST_FIELD.cpp_type = 10

pb.SC_SOCIETY_DISTRIBUTEHISTORY_COUNTNUM_FIELD.name = "CountNum"
pb.SC_SOCIETY_DISTRIBUTEHISTORY_COUNTNUM_FIELD.full_name = ".SC_Society_DistributeHistory.CountNum"
pb.SC_SOCIETY_DISTRIBUTEHISTORY_COUNTNUM_FIELD.number = 2
pb.SC_SOCIETY_DISTRIBUTEHISTORY_COUNTNUM_FIELD.index = 1
pb.SC_SOCIETY_DISTRIBUTEHISTORY_COUNTNUM_FIELD.label = 1
pb.SC_SOCIETY_DISTRIBUTEHISTORY_COUNTNUM_FIELD.has_default_value = false
pb.SC_SOCIETY_DISTRIBUTEHISTORY_COUNTNUM_FIELD.default_value = 0
pb.SC_SOCIETY_DISTRIBUTEHISTORY_COUNTNUM_FIELD.type = 13
pb.SC_SOCIETY_DISTRIBUTEHISTORY_COUNTNUM_FIELD.cpp_type = 3

pb.SC_SOCIETY_DISTRIBUTEHISTORY.name = "SC_Society_DistributeHistory"
pb.SC_SOCIETY_DISTRIBUTEHISTORY.full_name = ".SC_Society_DistributeHistory"
pb.SC_SOCIETY_DISTRIBUTEHISTORY.nested_types = {pb.SC_SOCIETY_DISTRIBUTEHISTORY_HISTORYINFO}
pb.SC_SOCIETY_DISTRIBUTEHISTORY.enum_types = {}
pb.SC_SOCIETY_DISTRIBUTEHISTORY.fields = {pb.SC_SOCIETY_DISTRIBUTEHISTORY_HISTORYLIST_FIELD, pb.SC_SOCIETY_DISTRIBUTEHISTORY_COUNTNUM_FIELD}
pb.SC_SOCIETY_DISTRIBUTEHISTORY.is_extendable = false
pb.SC_SOCIETY_DISTRIBUTEHISTORY.extensions = {}
pb.CS_SOCIETY_GETEVENTINFOLIST_SOCIETYID_FIELD.name = "SocietyID"
pb.CS_SOCIETY_GETEVENTINFOLIST_SOCIETYID_FIELD.full_name = ".CS_Society_GETEVENTINFOLIST.SocietyID"
pb.CS_SOCIETY_GETEVENTINFOLIST_SOCIETYID_FIELD.number = 1
pb.CS_SOCIETY_GETEVENTINFOLIST_SOCIETYID_FIELD.index = 0
pb.CS_SOCIETY_GETEVENTINFOLIST_SOCIETYID_FIELD.label = 2
pb.CS_SOCIETY_GETEVENTINFOLIST_SOCIETYID_FIELD.has_default_value = false
pb.CS_SOCIETY_GETEVENTINFOLIST_SOCIETYID_FIELD.default_value = 0
pb.CS_SOCIETY_GETEVENTINFOLIST_SOCIETYID_FIELD.type = 13
pb.CS_SOCIETY_GETEVENTINFOLIST_SOCIETYID_FIELD.cpp_type = 3

pb.CS_SOCIETY_GETEVENTINFOLIST.name = "CS_Society_GETEVENTINFOLIST"
pb.CS_SOCIETY_GETEVENTINFOLIST.full_name = ".CS_Society_GETEVENTINFOLIST"
pb.CS_SOCIETY_GETEVENTINFOLIST.nested_types = {}
pb.CS_SOCIETY_GETEVENTINFOLIST.enum_types = {}
pb.CS_SOCIETY_GETEVENTINFOLIST.fields = {pb.CS_SOCIETY_GETEVENTINFOLIST_SOCIETYID_FIELD}
pb.CS_SOCIETY_GETEVENTINFOLIST.is_extendable = false
pb.CS_SOCIETY_GETEVENTINFOLIST.extensions = {}
pb.SC_SOCIETY_GETEVENTINFOLIST_EVENTINFO_FACTORNAME_FIELD.name = "FActorName"
pb.SC_SOCIETY_GETEVENTINFOLIST_EVENTINFO_FACTORNAME_FIELD.full_name = ".SC_Society_GETEVENTINFOLIST.EventInfo.FActorName"
pb.SC_SOCIETY_GETEVENTINFOLIST_EVENTINFO_FACTORNAME_FIELD.number = 1
pb.SC_SOCIETY_GETEVENTINFOLIST_EVENTINFO_FACTORNAME_FIELD.index = 0
pb.SC_SOCIETY_GETEVENTINFOLIST_EVENTINFO_FACTORNAME_FIELD.label = 1
pb.SC_SOCIETY_GETEVENTINFOLIST_EVENTINFO_FACTORNAME_FIELD.has_default_value = false
pb.SC_SOCIETY_GETEVENTINFOLIST_EVENTINFO_FACTORNAME_FIELD.default_value = ""
pb.SC_SOCIETY_GETEVENTINFOLIST_EVENTINFO_FACTORNAME_FIELD.type = 9
pb.SC_SOCIETY_GETEVENTINFOLIST_EVENTINFO_FACTORNAME_FIELD.cpp_type = 9

pb.SC_SOCIETY_GETEVENTINFOLIST_EVENTINFO_SACTORNAME_FIELD.name = "SActorName"
pb.SC_SOCIETY_GETEVENTINFOLIST_EVENTINFO_SACTORNAME_FIELD.full_name = ".SC_Society_GETEVENTINFOLIST.EventInfo.SActorName"
pb.SC_SOCIETY_GETEVENTINFOLIST_EVENTINFO_SACTORNAME_FIELD.number = 2
pb.SC_SOCIETY_GETEVENTINFOLIST_EVENTINFO_SACTORNAME_FIELD.index = 1
pb.SC_SOCIETY_GETEVENTINFOLIST_EVENTINFO_SACTORNAME_FIELD.label = 1
pb.SC_SOCIETY_GETEVENTINFOLIST_EVENTINFO_SACTORNAME_FIELD.has_default_value = false
pb.SC_SOCIETY_GETEVENTINFOLIST_EVENTINFO_SACTORNAME_FIELD.default_value = ""
pb.SC_SOCIETY_GETEVENTINFOLIST_EVENTINFO_SACTORNAME_FIELD.type = 9
pb.SC_SOCIETY_GETEVENTINFOLIST_EVENTINFO_SACTORNAME_FIELD.cpp_type = 9

pb.SC_SOCIETY_GETEVENTINFOLIST_EVENTINFO_FAPPOINTID_FIELD.name = "FAppointID"
pb.SC_SOCIETY_GETEVENTINFOLIST_EVENTINFO_FAPPOINTID_FIELD.full_name = ".SC_Society_GETEVENTINFOLIST.EventInfo.FAppointID"
pb.SC_SOCIETY_GETEVENTINFOLIST_EVENTINFO_FAPPOINTID_FIELD.number = 3
pb.SC_SOCIETY_GETEVENTINFOLIST_EVENTINFO_FAPPOINTID_FIELD.index = 2
pb.SC_SOCIETY_GETEVENTINFOLIST_EVENTINFO_FAPPOINTID_FIELD.label = 1
pb.SC_SOCIETY_GETEVENTINFOLIST_EVENTINFO_FAPPOINTID_FIELD.has_default_value = false
pb.SC_SOCIETY_GETEVENTINFOLIST_EVENTINFO_FAPPOINTID_FIELD.default_value = 0
pb.SC_SOCIETY_GETEVENTINFOLIST_EVENTINFO_FAPPOINTID_FIELD.type = 13
pb.SC_SOCIETY_GETEVENTINFOLIST_EVENTINFO_FAPPOINTID_FIELD.cpp_type = 3

pb.SC_SOCIETY_GETEVENTINFOLIST_EVENTINFO_SAPPOINTID_FIELD.name = "SAppointID"
pb.SC_SOCIETY_GETEVENTINFOLIST_EVENTINFO_SAPPOINTID_FIELD.full_name = ".SC_Society_GETEVENTINFOLIST.EventInfo.SAppointID"
pb.SC_SOCIETY_GETEVENTINFOLIST_EVENTINFO_SAPPOINTID_FIELD.number = 4
pb.SC_SOCIETY_GETEVENTINFOLIST_EVENTINFO_SAPPOINTID_FIELD.index = 3
pb.SC_SOCIETY_GETEVENTINFOLIST_EVENTINFO_SAPPOINTID_FIELD.label = 1
pb.SC_SOCIETY_GETEVENTINFOLIST_EVENTINFO_SAPPOINTID_FIELD.has_default_value = false
pb.SC_SOCIETY_GETEVENTINFOLIST_EVENTINFO_SAPPOINTID_FIELD.default_value = 0
pb.SC_SOCIETY_GETEVENTINFOLIST_EVENTINFO_SAPPOINTID_FIELD.type = 13
pb.SC_SOCIETY_GETEVENTINFOLIST_EVENTINFO_SAPPOINTID_FIELD.cpp_type = 3

pb.SC_SOCIETY_GETEVENTINFOLIST_EVENTINFO_MONEY_FIELD.name = "Money"
pb.SC_SOCIETY_GETEVENTINFOLIST_EVENTINFO_MONEY_FIELD.full_name = ".SC_Society_GETEVENTINFOLIST.EventInfo.Money"
pb.SC_SOCIETY_GETEVENTINFOLIST_EVENTINFO_MONEY_FIELD.number = 5
pb.SC_SOCIETY_GETEVENTINFOLIST_EVENTINFO_MONEY_FIELD.index = 4
pb.SC_SOCIETY_GETEVENTINFOLIST_EVENTINFO_MONEY_FIELD.label = 1
pb.SC_SOCIETY_GETEVENTINFOLIST_EVENTINFO_MONEY_FIELD.has_default_value = false
pb.SC_SOCIETY_GETEVENTINFOLIST_EVENTINFO_MONEY_FIELD.default_value = 0
pb.SC_SOCIETY_GETEVENTINFOLIST_EVENTINFO_MONEY_FIELD.type = 13
pb.SC_SOCIETY_GETEVENTINFOLIST_EVENTINFO_MONEY_FIELD.cpp_type = 3

pb.SC_SOCIETY_GETEVENTINFOLIST_EVENTINFO_DIAMOND_FIELD.name = "Diamond"
pb.SC_SOCIETY_GETEVENTINFOLIST_EVENTINFO_DIAMOND_FIELD.full_name = ".SC_Society_GETEVENTINFOLIST.EventInfo.Diamond"
pb.SC_SOCIETY_GETEVENTINFOLIST_EVENTINFO_DIAMOND_FIELD.number = 6
pb.SC_SOCIETY_GETEVENTINFOLIST_EVENTINFO_DIAMOND_FIELD.index = 5
pb.SC_SOCIETY_GETEVENTINFOLIST_EVENTINFO_DIAMOND_FIELD.label = 1
pb.SC_SOCIETY_GETEVENTINFOLIST_EVENTINFO_DIAMOND_FIELD.has_default_value = false
pb.SC_SOCIETY_GETEVENTINFOLIST_EVENTINFO_DIAMOND_FIELD.default_value = 0
pb.SC_SOCIETY_GETEVENTINFOLIST_EVENTINFO_DIAMOND_FIELD.type = 13
pb.SC_SOCIETY_GETEVENTINFOLIST_EVENTINFO_DIAMOND_FIELD.cpp_type = 3

pb.SC_SOCIETY_GETEVENTINFOLIST_EVENTINFO_GOODSID_FIELD.name = "GoodsID"
pb.SC_SOCIETY_GETEVENTINFOLIST_EVENTINFO_GOODSID_FIELD.full_name = ".SC_Society_GETEVENTINFOLIST.EventInfo.GoodsID"
pb.SC_SOCIETY_GETEVENTINFOLIST_EVENTINFO_GOODSID_FIELD.number = 7
pb.SC_SOCIETY_GETEVENTINFOLIST_EVENTINFO_GOODSID_FIELD.index = 6
pb.SC_SOCIETY_GETEVENTINFOLIST_EVENTINFO_GOODSID_FIELD.label = 1
pb.SC_SOCIETY_GETEVENTINFOLIST_EVENTINFO_GOODSID_FIELD.has_default_value = false
pb.SC_SOCIETY_GETEVENTINFOLIST_EVENTINFO_GOODSID_FIELD.default_value = 0
pb.SC_SOCIETY_GETEVENTINFOLIST_EVENTINFO_GOODSID_FIELD.type = 13
pb.SC_SOCIETY_GETEVENTINFOLIST_EVENTINFO_GOODSID_FIELD.cpp_type = 3

pb.SC_SOCIETY_GETEVENTINFOLIST_EVENTINFO_TIME_FIELD.name = "Time"
pb.SC_SOCIETY_GETEVENTINFOLIST_EVENTINFO_TIME_FIELD.full_name = ".SC_Society_GETEVENTINFOLIST.EventInfo.Time"
pb.SC_SOCIETY_GETEVENTINFOLIST_EVENTINFO_TIME_FIELD.number = 8
pb.SC_SOCIETY_GETEVENTINFOLIST_EVENTINFO_TIME_FIELD.index = 7
pb.SC_SOCIETY_GETEVENTINFOLIST_EVENTINFO_TIME_FIELD.label = 1
pb.SC_SOCIETY_GETEVENTINFOLIST_EVENTINFO_TIME_FIELD.has_default_value = false
pb.SC_SOCIETY_GETEVENTINFOLIST_EVENTINFO_TIME_FIELD.default_value = 0
pb.SC_SOCIETY_GETEVENTINFOLIST_EVENTINFO_TIME_FIELD.type = 13
pb.SC_SOCIETY_GETEVENTINFOLIST_EVENTINFO_TIME_FIELD.cpp_type = 3

pb.SC_SOCIETY_GETEVENTINFOLIST_EVENTINFO_EVENTTYPE_FIELD.name = "EventType"
pb.SC_SOCIETY_GETEVENTINFOLIST_EVENTINFO_EVENTTYPE_FIELD.full_name = ".SC_Society_GETEVENTINFOLIST.EventInfo.EventType"
pb.SC_SOCIETY_GETEVENTINFOLIST_EVENTINFO_EVENTTYPE_FIELD.number = 9
pb.SC_SOCIETY_GETEVENTINFOLIST_EVENTINFO_EVENTTYPE_FIELD.index = 8
pb.SC_SOCIETY_GETEVENTINFOLIST_EVENTINFO_EVENTTYPE_FIELD.label = 1
pb.SC_SOCIETY_GETEVENTINFOLIST_EVENTINFO_EVENTTYPE_FIELD.has_default_value = false
pb.SC_SOCIETY_GETEVENTINFOLIST_EVENTINFO_EVENTTYPE_FIELD.default_value = 0
pb.SC_SOCIETY_GETEVENTINFOLIST_EVENTINFO_EVENTTYPE_FIELD.type = 13
pb.SC_SOCIETY_GETEVENTINFOLIST_EVENTINFO_EVENTTYPE_FIELD.cpp_type = 3

pb.SC_SOCIETY_GETEVENTINFOLIST_EVENTINFO.name = "EventInfo"
pb.SC_SOCIETY_GETEVENTINFOLIST_EVENTINFO.full_name = ".SC_Society_GETEVENTINFOLIST.EventInfo"
pb.SC_SOCIETY_GETEVENTINFOLIST_EVENTINFO.nested_types = {}
pb.SC_SOCIETY_GETEVENTINFOLIST_EVENTINFO.enum_types = {}
pb.SC_SOCIETY_GETEVENTINFOLIST_EVENTINFO.fields = {pb.SC_SOCIETY_GETEVENTINFOLIST_EVENTINFO_FACTORNAME_FIELD, pb.SC_SOCIETY_GETEVENTINFOLIST_EVENTINFO_SACTORNAME_FIELD, pb.SC_SOCIETY_GETEVENTINFOLIST_EVENTINFO_FAPPOINTID_FIELD, pb.SC_SOCIETY_GETEVENTINFOLIST_EVENTINFO_SAPPOINTID_FIELD, pb.SC_SOCIETY_GETEVENTINFOLIST_EVENTINFO_MONEY_FIELD, pb.SC_SOCIETY_GETEVENTINFOLIST_EVENTINFO_DIAMOND_FIELD, pb.SC_SOCIETY_GETEVENTINFOLIST_EVENTINFO_GOODSID_FIELD, pb.SC_SOCIETY_GETEVENTINFOLIST_EVENTINFO_TIME_FIELD, pb.SC_SOCIETY_GETEVENTINFOLIST_EVENTINFO_EVENTTYPE_FIELD}
pb.SC_SOCIETY_GETEVENTINFOLIST_EVENTINFO.is_extendable = false
pb.SC_SOCIETY_GETEVENTINFOLIST_EVENTINFO.extensions = {}
pb.SC_SOCIETY_GETEVENTINFOLIST_EVENTINFO.containing_type = pb.SC_SOCIETY_GETEVENTINFOLIST
pb.SC_SOCIETY_GETEVENTINFOLIST_EVENTINFOLIST_FIELD.name = "EventInfoList"
pb.SC_SOCIETY_GETEVENTINFOLIST_EVENTINFOLIST_FIELD.full_name = ".SC_Society_GETEVENTINFOLIST.EventInfoList"
pb.SC_SOCIETY_GETEVENTINFOLIST_EVENTINFOLIST_FIELD.number = 1
pb.SC_SOCIETY_GETEVENTINFOLIST_EVENTINFOLIST_FIELD.index = 0
pb.SC_SOCIETY_GETEVENTINFOLIST_EVENTINFOLIST_FIELD.label = 3
pb.SC_SOCIETY_GETEVENTINFOLIST_EVENTINFOLIST_FIELD.has_default_value = false
pb.SC_SOCIETY_GETEVENTINFOLIST_EVENTINFOLIST_FIELD.default_value = {}
pb.SC_SOCIETY_GETEVENTINFOLIST_EVENTINFOLIST_FIELD.message_type = pb.SC_SOCIETY_GETEVENTINFOLIST_EVENTINFO
pb.SC_SOCIETY_GETEVENTINFOLIST_EVENTINFOLIST_FIELD.type = 11
pb.SC_SOCIETY_GETEVENTINFOLIST_EVENTINFOLIST_FIELD.cpp_type = 10

pb.SC_SOCIETY_GETEVENTINFOLIST_COUNTNUM_FIELD.name = "CountNum"
pb.SC_SOCIETY_GETEVENTINFOLIST_COUNTNUM_FIELD.full_name = ".SC_Society_GETEVENTINFOLIST.CountNum"
pb.SC_SOCIETY_GETEVENTINFOLIST_COUNTNUM_FIELD.number = 2
pb.SC_SOCIETY_GETEVENTINFOLIST_COUNTNUM_FIELD.index = 1
pb.SC_SOCIETY_GETEVENTINFOLIST_COUNTNUM_FIELD.label = 1
pb.SC_SOCIETY_GETEVENTINFOLIST_COUNTNUM_FIELD.has_default_value = false
pb.SC_SOCIETY_GETEVENTINFOLIST_COUNTNUM_FIELD.default_value = 0
pb.SC_SOCIETY_GETEVENTINFOLIST_COUNTNUM_FIELD.type = 13
pb.SC_SOCIETY_GETEVENTINFOLIST_COUNTNUM_FIELD.cpp_type = 3

pb.SC_SOCIETY_GETEVENTINFOLIST.name = "SC_Society_GETEVENTINFOLIST"
pb.SC_SOCIETY_GETEVENTINFOLIST.full_name = ".SC_Society_GETEVENTINFOLIST"
pb.SC_SOCIETY_GETEVENTINFOLIST.nested_types = {pb.SC_SOCIETY_GETEVENTINFOLIST_EVENTINFO}
pb.SC_SOCIETY_GETEVENTINFOLIST.enum_types = {}
pb.SC_SOCIETY_GETEVENTINFOLIST.fields = {pb.SC_SOCIETY_GETEVENTINFOLIST_EVENTINFOLIST_FIELD, pb.SC_SOCIETY_GETEVENTINFOLIST_COUNTNUM_FIELD}
pb.SC_SOCIETY_GETEVENTINFOLIST.is_extendable = false
pb.SC_SOCIETY_GETEVENTINFOLIST.extensions = {}
pb.SC_SOCIETY_GETCONTRIBUTIONHISTORY_HISTORY_ACTORID_FIELD.name = "ActorID"
pb.SC_SOCIETY_GETCONTRIBUTIONHISTORY_HISTORY_ACTORID_FIELD.full_name = ".SC_Society_GetContributionHistory.History.ActorID"
pb.SC_SOCIETY_GETCONTRIBUTIONHISTORY_HISTORY_ACTORID_FIELD.number = 1
pb.SC_SOCIETY_GETCONTRIBUTIONHISTORY_HISTORY_ACTORID_FIELD.index = 0
pb.SC_SOCIETY_GETCONTRIBUTIONHISTORY_HISTORY_ACTORID_FIELD.label = 1
pb.SC_SOCIETY_GETCONTRIBUTIONHISTORY_HISTORY_ACTORID_FIELD.has_default_value = false
pb.SC_SOCIETY_GETCONTRIBUTIONHISTORY_HISTORY_ACTORID_FIELD.default_value = 0
pb.SC_SOCIETY_GETCONTRIBUTIONHISTORY_HISTORY_ACTORID_FIELD.type = 13
pb.SC_SOCIETY_GETCONTRIBUTIONHISTORY_HISTORY_ACTORID_FIELD.cpp_type = 3

pb.SC_SOCIETY_GETCONTRIBUTIONHISTORY_HISTORY_TIME_FIELD.name = "Time"
pb.SC_SOCIETY_GETCONTRIBUTIONHISTORY_HISTORY_TIME_FIELD.full_name = ".SC_Society_GetContributionHistory.History.Time"
pb.SC_SOCIETY_GETCONTRIBUTIONHISTORY_HISTORY_TIME_FIELD.number = 2
pb.SC_SOCIETY_GETCONTRIBUTIONHISTORY_HISTORY_TIME_FIELD.index = 1
pb.SC_SOCIETY_GETCONTRIBUTIONHISTORY_HISTORY_TIME_FIELD.label = 1
pb.SC_SOCIETY_GETCONTRIBUTIONHISTORY_HISTORY_TIME_FIELD.has_default_value = false
pb.SC_SOCIETY_GETCONTRIBUTIONHISTORY_HISTORY_TIME_FIELD.default_value = 0
pb.SC_SOCIETY_GETCONTRIBUTIONHISTORY_HISTORY_TIME_FIELD.type = 13
pb.SC_SOCIETY_GETCONTRIBUTIONHISTORY_HISTORY_TIME_FIELD.cpp_type = 3

pb.SC_SOCIETY_GETCONTRIBUTIONHISTORY_HISTORY_ACTORNAME_FIELD.name = "ActorName"
pb.SC_SOCIETY_GETCONTRIBUTIONHISTORY_HISTORY_ACTORNAME_FIELD.full_name = ".SC_Society_GetContributionHistory.History.ActorName"
pb.SC_SOCIETY_GETCONTRIBUTIONHISTORY_HISTORY_ACTORNAME_FIELD.number = 3
pb.SC_SOCIETY_GETCONTRIBUTIONHISTORY_HISTORY_ACTORNAME_FIELD.index = 2
pb.SC_SOCIETY_GETCONTRIBUTIONHISTORY_HISTORY_ACTORNAME_FIELD.label = 1
pb.SC_SOCIETY_GETCONTRIBUTIONHISTORY_HISTORY_ACTORNAME_FIELD.has_default_value = false
pb.SC_SOCIETY_GETCONTRIBUTIONHISTORY_HISTORY_ACTORNAME_FIELD.default_value = ""
pb.SC_SOCIETY_GETCONTRIBUTIONHISTORY_HISTORY_ACTORNAME_FIELD.type = 9
pb.SC_SOCIETY_GETCONTRIBUTIONHISTORY_HISTORY_ACTORNAME_FIELD.cpp_type = 9

pb.SC_SOCIETY_GETCONTRIBUTIONHISTORY_HISTORY_TYPE_FIELD.name = "Type"
pb.SC_SOCIETY_GETCONTRIBUTIONHISTORY_HISTORY_TYPE_FIELD.full_name = ".SC_Society_GetContributionHistory.History.Type"
pb.SC_SOCIETY_GETCONTRIBUTIONHISTORY_HISTORY_TYPE_FIELD.number = 4
pb.SC_SOCIETY_GETCONTRIBUTIONHISTORY_HISTORY_TYPE_FIELD.index = 3
pb.SC_SOCIETY_GETCONTRIBUTIONHISTORY_HISTORY_TYPE_FIELD.label = 1
pb.SC_SOCIETY_GETCONTRIBUTIONHISTORY_HISTORY_TYPE_FIELD.has_default_value = false
pb.SC_SOCIETY_GETCONTRIBUTIONHISTORY_HISTORY_TYPE_FIELD.default_value = 0
pb.SC_SOCIETY_GETCONTRIBUTIONHISTORY_HISTORY_TYPE_FIELD.type = 13
pb.SC_SOCIETY_GETCONTRIBUTIONHISTORY_HISTORY_TYPE_FIELD.cpp_type = 3

pb.SC_SOCIETY_GETCONTRIBUTIONHISTORY_HISTORY_NUM_FIELD.name = "Num"
pb.SC_SOCIETY_GETCONTRIBUTIONHISTORY_HISTORY_NUM_FIELD.full_name = ".SC_Society_GetContributionHistory.History.Num"
pb.SC_SOCIETY_GETCONTRIBUTIONHISTORY_HISTORY_NUM_FIELD.number = 5
pb.SC_SOCIETY_GETCONTRIBUTIONHISTORY_HISTORY_NUM_FIELD.index = 4
pb.SC_SOCIETY_GETCONTRIBUTIONHISTORY_HISTORY_NUM_FIELD.label = 1
pb.SC_SOCIETY_GETCONTRIBUTIONHISTORY_HISTORY_NUM_FIELD.has_default_value = false
pb.SC_SOCIETY_GETCONTRIBUTIONHISTORY_HISTORY_NUM_FIELD.default_value = 0
pb.SC_SOCIETY_GETCONTRIBUTIONHISTORY_HISTORY_NUM_FIELD.type = 13
pb.SC_SOCIETY_GETCONTRIBUTIONHISTORY_HISTORY_NUM_FIELD.cpp_type = 3

pb.SC_SOCIETY_GETCONTRIBUTIONHISTORY_HISTORY.name = "History"
pb.SC_SOCIETY_GETCONTRIBUTIONHISTORY_HISTORY.full_name = ".SC_Society_GetContributionHistory.History"
pb.SC_SOCIETY_GETCONTRIBUTIONHISTORY_HISTORY.nested_types = {}
pb.SC_SOCIETY_GETCONTRIBUTIONHISTORY_HISTORY.enum_types = {}
pb.SC_SOCIETY_GETCONTRIBUTIONHISTORY_HISTORY.fields = {pb.SC_SOCIETY_GETCONTRIBUTIONHISTORY_HISTORY_ACTORID_FIELD, pb.SC_SOCIETY_GETCONTRIBUTIONHISTORY_HISTORY_TIME_FIELD, pb.SC_SOCIETY_GETCONTRIBUTIONHISTORY_HISTORY_ACTORNAME_FIELD, pb.SC_SOCIETY_GETCONTRIBUTIONHISTORY_HISTORY_TYPE_FIELD, pb.SC_SOCIETY_GETCONTRIBUTIONHISTORY_HISTORY_NUM_FIELD}
pb.SC_SOCIETY_GETCONTRIBUTIONHISTORY_HISTORY.is_extendable = false
pb.SC_SOCIETY_GETCONTRIBUTIONHISTORY_HISTORY.extensions = {}
pb.SC_SOCIETY_GETCONTRIBUTIONHISTORY_HISTORY.containing_type = pb.SC_SOCIETY_GETCONTRIBUTIONHISTORY
pb.SC_SOCIETY_GETCONTRIBUTIONHISTORY_HISTORYLIST_FIELD.name = "HistoryList"
pb.SC_SOCIETY_GETCONTRIBUTIONHISTORY_HISTORYLIST_FIELD.full_name = ".SC_Society_GetContributionHistory.HistoryList"
pb.SC_SOCIETY_GETCONTRIBUTIONHISTORY_HISTORYLIST_FIELD.number = 1
pb.SC_SOCIETY_GETCONTRIBUTIONHISTORY_HISTORYLIST_FIELD.index = 0
pb.SC_SOCIETY_GETCONTRIBUTIONHISTORY_HISTORYLIST_FIELD.label = 3
pb.SC_SOCIETY_GETCONTRIBUTIONHISTORY_HISTORYLIST_FIELD.has_default_value = false
pb.SC_SOCIETY_GETCONTRIBUTIONHISTORY_HISTORYLIST_FIELD.default_value = {}
pb.SC_SOCIETY_GETCONTRIBUTIONHISTORY_HISTORYLIST_FIELD.message_type = pb.SC_SOCIETY_GETCONTRIBUTIONHISTORY_HISTORY
pb.SC_SOCIETY_GETCONTRIBUTIONHISTORY_HISTORYLIST_FIELD.type = 11
pb.SC_SOCIETY_GETCONTRIBUTIONHISTORY_HISTORYLIST_FIELD.cpp_type = 10

pb.SC_SOCIETY_GETCONTRIBUTIONHISTORY.name = "SC_Society_GetContributionHistory"
pb.SC_SOCIETY_GETCONTRIBUTIONHISTORY.full_name = ".SC_Society_GetContributionHistory"
pb.SC_SOCIETY_GETCONTRIBUTIONHISTORY.nested_types = {pb.SC_SOCIETY_GETCONTRIBUTIONHISTORY_HISTORY}
pb.SC_SOCIETY_GETCONTRIBUTIONHISTORY.enum_types = {}
pb.SC_SOCIETY_GETCONTRIBUTIONHISTORY.fields = {pb.SC_SOCIETY_GETCONTRIBUTIONHISTORY_HISTORYLIST_FIELD}
pb.SC_SOCIETY_GETCONTRIBUTIONHISTORY.is_extendable = false
pb.SC_SOCIETY_GETCONTRIBUTIONHISTORY.extensions = {}
pb.CS_SOCIETY_GETNAME_SOCIETYID_FIELD.name = "SocietyID"
pb.CS_SOCIETY_GETNAME_SOCIETYID_FIELD.full_name = ".CS_Society_GetName.SocietyID"
pb.CS_SOCIETY_GETNAME_SOCIETYID_FIELD.number = 1
pb.CS_SOCIETY_GETNAME_SOCIETYID_FIELD.index = 0
pb.CS_SOCIETY_GETNAME_SOCIETYID_FIELD.label = 2
pb.CS_SOCIETY_GETNAME_SOCIETYID_FIELD.has_default_value = false
pb.CS_SOCIETY_GETNAME_SOCIETYID_FIELD.default_value = 0
pb.CS_SOCIETY_GETNAME_SOCIETYID_FIELD.type = 13
pb.CS_SOCIETY_GETNAME_SOCIETYID_FIELD.cpp_type = 3

pb.CS_SOCIETY_GETNAME.name = "CS_Society_GetName"
pb.CS_SOCIETY_GETNAME.full_name = ".CS_Society_GetName"
pb.CS_SOCIETY_GETNAME.nested_types = {}
pb.CS_SOCIETY_GETNAME.enum_types = {}
pb.CS_SOCIETY_GETNAME.fields = {pb.CS_SOCIETY_GETNAME_SOCIETYID_FIELD}
pb.CS_SOCIETY_GETNAME.is_extendable = false
pb.CS_SOCIETY_GETNAME.extensions = {}
pb.SC_SOCIETY_GETNAME_SOCIETYID_FIELD.name = "SocietyID"
pb.SC_SOCIETY_GETNAME_SOCIETYID_FIELD.full_name = ".SC_Society_GetName.SocietyID"
pb.SC_SOCIETY_GETNAME_SOCIETYID_FIELD.number = 1
pb.SC_SOCIETY_GETNAME_SOCIETYID_FIELD.index = 0
pb.SC_SOCIETY_GETNAME_SOCIETYID_FIELD.label = 1
pb.SC_SOCIETY_GETNAME_SOCIETYID_FIELD.has_default_value = false
pb.SC_SOCIETY_GETNAME_SOCIETYID_FIELD.default_value = 0
pb.SC_SOCIETY_GETNAME_SOCIETYID_FIELD.type = 13
pb.SC_SOCIETY_GETNAME_SOCIETYID_FIELD.cpp_type = 3

pb.SC_SOCIETY_GETNAME_SOCIETYNAME_FIELD.name = "SocietyName"
pb.SC_SOCIETY_GETNAME_SOCIETYNAME_FIELD.full_name = ".SC_Society_GetName.SocietyName"
pb.SC_SOCIETY_GETNAME_SOCIETYNAME_FIELD.number = 2
pb.SC_SOCIETY_GETNAME_SOCIETYNAME_FIELD.index = 1
pb.SC_SOCIETY_GETNAME_SOCIETYNAME_FIELD.label = 1
pb.SC_SOCIETY_GETNAME_SOCIETYNAME_FIELD.has_default_value = false
pb.SC_SOCIETY_GETNAME_SOCIETYNAME_FIELD.default_value = ""
pb.SC_SOCIETY_GETNAME_SOCIETYNAME_FIELD.type = 9
pb.SC_SOCIETY_GETNAME_SOCIETYNAME_FIELD.cpp_type = 9

pb.SC_SOCIETY_GETNAME.name = "SC_Society_GetName"
pb.SC_SOCIETY_GETNAME.full_name = ".SC_Society_GetName"
pb.SC_SOCIETY_GETNAME.nested_types = {}
pb.SC_SOCIETY_GETNAME.enum_types = {}
pb.SC_SOCIETY_GETNAME.fields = {pb.SC_SOCIETY_GETNAME_SOCIETYID_FIELD, pb.SC_SOCIETY_GETNAME_SOCIETYNAME_FIELD}
pb.SC_SOCIETY_GETNAME.is_extendable = false
pb.SC_SOCIETY_GETNAME.extensions = {}
pb.SC_SOCIETY_ACTIVITYMSG_SOCIETYID_FIELD.name = "SocietyID"
pb.SC_SOCIETY_ACTIVITYMSG_SOCIETYID_FIELD.full_name = ".SC_Society_ActivityMsg.SocietyID"
pb.SC_SOCIETY_ACTIVITYMSG_SOCIETYID_FIELD.number = 1
pb.SC_SOCIETY_ACTIVITYMSG_SOCIETYID_FIELD.index = 0
pb.SC_SOCIETY_ACTIVITYMSG_SOCIETYID_FIELD.label = 1
pb.SC_SOCIETY_ACTIVITYMSG_SOCIETYID_FIELD.has_default_value = false
pb.SC_SOCIETY_ACTIVITYMSG_SOCIETYID_FIELD.default_value = 0
pb.SC_SOCIETY_ACTIVITYMSG_SOCIETYID_FIELD.type = 13
pb.SC_SOCIETY_ACTIVITYMSG_SOCIETYID_FIELD.cpp_type = 3

pb.SC_SOCIETY_ACTIVITYMSG_ACTIVITYTYPE_FIELD.name = "ActivityType"
pb.SC_SOCIETY_ACTIVITYMSG_ACTIVITYTYPE_FIELD.full_name = ".SC_Society_ActivityMsg.ActivityType"
pb.SC_SOCIETY_ACTIVITYMSG_ACTIVITYTYPE_FIELD.number = 2
pb.SC_SOCIETY_ACTIVITYMSG_ACTIVITYTYPE_FIELD.index = 1
pb.SC_SOCIETY_ACTIVITYMSG_ACTIVITYTYPE_FIELD.label = 1
pb.SC_SOCIETY_ACTIVITYMSG_ACTIVITYTYPE_FIELD.has_default_value = false
pb.SC_SOCIETY_ACTIVITYMSG_ACTIVITYTYPE_FIELD.default_value = 0
pb.SC_SOCIETY_ACTIVITYMSG_ACTIVITYTYPE_FIELD.type = 13
pb.SC_SOCIETY_ACTIVITYMSG_ACTIVITYTYPE_FIELD.cpp_type = 3

pb.SC_SOCIETY_ACTIVITYMSG_ENDTIME_FIELD.name = "EndTime"
pb.SC_SOCIETY_ACTIVITYMSG_ENDTIME_FIELD.full_name = ".SC_Society_ActivityMsg.EndTime"
pb.SC_SOCIETY_ACTIVITYMSG_ENDTIME_FIELD.number = 3
pb.SC_SOCIETY_ACTIVITYMSG_ENDTIME_FIELD.index = 2
pb.SC_SOCIETY_ACTIVITYMSG_ENDTIME_FIELD.label = 1
pb.SC_SOCIETY_ACTIVITYMSG_ENDTIME_FIELD.has_default_value = false
pb.SC_SOCIETY_ACTIVITYMSG_ENDTIME_FIELD.default_value = 0
pb.SC_SOCIETY_ACTIVITYMSG_ENDTIME_FIELD.type = 13
pb.SC_SOCIETY_ACTIVITYMSG_ENDTIME_FIELD.cpp_type = 3

pb.SC_SOCIETY_ACTIVITYMSG.name = "SC_Society_ActivityMsg"
pb.SC_SOCIETY_ACTIVITYMSG.full_name = ".SC_Society_ActivityMsg"
pb.SC_SOCIETY_ACTIVITYMSG.nested_types = {}
pb.SC_SOCIETY_ACTIVITYMSG.enum_types = {}
pb.SC_SOCIETY_ACTIVITYMSG.fields = {pb.SC_SOCIETY_ACTIVITYMSG_SOCIETYID_FIELD, pb.SC_SOCIETY_ACTIVITYMSG_ACTIVITYTYPE_FIELD, pb.SC_SOCIETY_ACTIVITYMSG_ENDTIME_FIELD}
pb.SC_SOCIETY_ACTIVITYMSG.is_extendable = false
pb.SC_SOCIETY_ACTIVITYMSG.extensions = {}
pb.SC_SOCIETY_TOWER_CLEARANCE_NUMBER_SOCIETYID_FIELD.name = "SocietyID"
pb.SC_SOCIETY_TOWER_CLEARANCE_NUMBER_SOCIETYID_FIELD.full_name = ".SC_Society_Tower_Clearance_Number.SocietyID"
pb.SC_SOCIETY_TOWER_CLEARANCE_NUMBER_SOCIETYID_FIELD.number = 1
pb.SC_SOCIETY_TOWER_CLEARANCE_NUMBER_SOCIETYID_FIELD.index = 0
pb.SC_SOCIETY_TOWER_CLEARANCE_NUMBER_SOCIETYID_FIELD.label = 1
pb.SC_SOCIETY_TOWER_CLEARANCE_NUMBER_SOCIETYID_FIELD.has_default_value = false
pb.SC_SOCIETY_TOWER_CLEARANCE_NUMBER_SOCIETYID_FIELD.default_value = 0
pb.SC_SOCIETY_TOWER_CLEARANCE_NUMBER_SOCIETYID_FIELD.type = 13
pb.SC_SOCIETY_TOWER_CLEARANCE_NUMBER_SOCIETYID_FIELD.cpp_type = 3

pb.SC_SOCIETY_TOWER_CLEARANCE_NUMBER_LEVEL_FIELD.name = "Level"
pb.SC_SOCIETY_TOWER_CLEARANCE_NUMBER_LEVEL_FIELD.full_name = ".SC_Society_Tower_Clearance_Number.Level"
pb.SC_SOCIETY_TOWER_CLEARANCE_NUMBER_LEVEL_FIELD.number = 2
pb.SC_SOCIETY_TOWER_CLEARANCE_NUMBER_LEVEL_FIELD.index = 1
pb.SC_SOCIETY_TOWER_CLEARANCE_NUMBER_LEVEL_FIELD.label = 1
pb.SC_SOCIETY_TOWER_CLEARANCE_NUMBER_LEVEL_FIELD.has_default_value = false
pb.SC_SOCIETY_TOWER_CLEARANCE_NUMBER_LEVEL_FIELD.default_value = 0
pb.SC_SOCIETY_TOWER_CLEARANCE_NUMBER_LEVEL_FIELD.type = 13
pb.SC_SOCIETY_TOWER_CLEARANCE_NUMBER_LEVEL_FIELD.cpp_type = 3

pb.SC_SOCIETY_TOWER_CLEARANCE_NUMBER_NUMBER_FIELD.name = "Number"
pb.SC_SOCIETY_TOWER_CLEARANCE_NUMBER_NUMBER_FIELD.full_name = ".SC_Society_Tower_Clearance_Number.Number"
pb.SC_SOCIETY_TOWER_CLEARANCE_NUMBER_NUMBER_FIELD.number = 3
pb.SC_SOCIETY_TOWER_CLEARANCE_NUMBER_NUMBER_FIELD.index = 2
pb.SC_SOCIETY_TOWER_CLEARANCE_NUMBER_NUMBER_FIELD.label = 1
pb.SC_SOCIETY_TOWER_CLEARANCE_NUMBER_NUMBER_FIELD.has_default_value = false
pb.SC_SOCIETY_TOWER_CLEARANCE_NUMBER_NUMBER_FIELD.default_value = 0
pb.SC_SOCIETY_TOWER_CLEARANCE_NUMBER_NUMBER_FIELD.type = 13
pb.SC_SOCIETY_TOWER_CLEARANCE_NUMBER_NUMBER_FIELD.cpp_type = 3

pb.SC_SOCIETY_TOWER_CLEARANCE_NUMBER.name = "SC_Society_Tower_Clearance_Number"
pb.SC_SOCIETY_TOWER_CLEARANCE_NUMBER.full_name = ".SC_Society_Tower_Clearance_Number"
pb.SC_SOCIETY_TOWER_CLEARANCE_NUMBER.nested_types = {}
pb.SC_SOCIETY_TOWER_CLEARANCE_NUMBER.enum_types = {}
pb.SC_SOCIETY_TOWER_CLEARANCE_NUMBER.fields = {pb.SC_SOCIETY_TOWER_CLEARANCE_NUMBER_SOCIETYID_FIELD, pb.SC_SOCIETY_TOWER_CLEARANCE_NUMBER_LEVEL_FIELD, pb.SC_SOCIETY_TOWER_CLEARANCE_NUMBER_NUMBER_FIELD}
pb.SC_SOCIETY_TOWER_CLEARANCE_NUMBER.is_extendable = false
pb.SC_SOCIETY_TOWER_CLEARANCE_NUMBER.extensions = {}
pb.SC_SOCIETY_TOWER_CLEARANCE_TOPLEVEL_ACTORNAME_FIELD.name = "ActorName"
pb.SC_SOCIETY_TOWER_CLEARANCE_TOPLEVEL_ACTORNAME_FIELD.full_name = ".SC_Society_Tower_Clearance_Toplevel.ActorName"
pb.SC_SOCIETY_TOWER_CLEARANCE_TOPLEVEL_ACTORNAME_FIELD.number = 1
pb.SC_SOCIETY_TOWER_CLEARANCE_TOPLEVEL_ACTORNAME_FIELD.index = 0
pb.SC_SOCIETY_TOWER_CLEARANCE_TOPLEVEL_ACTORNAME_FIELD.label = 1
pb.SC_SOCIETY_TOWER_CLEARANCE_TOPLEVEL_ACTORNAME_FIELD.has_default_value = false
pb.SC_SOCIETY_TOWER_CLEARANCE_TOPLEVEL_ACTORNAME_FIELD.default_value = ""
pb.SC_SOCIETY_TOWER_CLEARANCE_TOPLEVEL_ACTORNAME_FIELD.type = 9
pb.SC_SOCIETY_TOWER_CLEARANCE_TOPLEVEL_ACTORNAME_FIELD.cpp_type = 9

pb.SC_SOCIETY_TOWER_CLEARANCE_TOPLEVEL_VOCATION_FIELD.name = "Vocation"
pb.SC_SOCIETY_TOWER_CLEARANCE_TOPLEVEL_VOCATION_FIELD.full_name = ".SC_Society_Tower_Clearance_Toplevel.Vocation"
pb.SC_SOCIETY_TOWER_CLEARANCE_TOPLEVEL_VOCATION_FIELD.number = 2
pb.SC_SOCIETY_TOWER_CLEARANCE_TOPLEVEL_VOCATION_FIELD.index = 1
pb.SC_SOCIETY_TOWER_CLEARANCE_TOPLEVEL_VOCATION_FIELD.label = 1
pb.SC_SOCIETY_TOWER_CLEARANCE_TOPLEVEL_VOCATION_FIELD.has_default_value = false
pb.SC_SOCIETY_TOWER_CLEARANCE_TOPLEVEL_VOCATION_FIELD.default_value = 0
pb.SC_SOCIETY_TOWER_CLEARANCE_TOPLEVEL_VOCATION_FIELD.type = 13
pb.SC_SOCIETY_TOWER_CLEARANCE_TOPLEVEL_VOCATION_FIELD.cpp_type = 3

pb.SC_SOCIETY_TOWER_CLEARANCE_TOPLEVEL_ECTYPEID_FIELD.name = "EctypeID"
pb.SC_SOCIETY_TOWER_CLEARANCE_TOPLEVEL_ECTYPEID_FIELD.full_name = ".SC_Society_Tower_Clearance_Toplevel.EctypeID"
pb.SC_SOCIETY_TOWER_CLEARANCE_TOPLEVEL_ECTYPEID_FIELD.number = 3
pb.SC_SOCIETY_TOWER_CLEARANCE_TOPLEVEL_ECTYPEID_FIELD.index = 2
pb.SC_SOCIETY_TOWER_CLEARANCE_TOPLEVEL_ECTYPEID_FIELD.label = 1
pb.SC_SOCIETY_TOWER_CLEARANCE_TOPLEVEL_ECTYPEID_FIELD.has_default_value = false
pb.SC_SOCIETY_TOWER_CLEARANCE_TOPLEVEL_ECTYPEID_FIELD.default_value = 0
pb.SC_SOCIETY_TOWER_CLEARANCE_TOPLEVEL_ECTYPEID_FIELD.type = 13
pb.SC_SOCIETY_TOWER_CLEARANCE_TOPLEVEL_ECTYPEID_FIELD.cpp_type = 3

pb.SC_SOCIETY_TOWER_CLEARANCE_TOPLEVEL.name = "SC_Society_Tower_Clearance_Toplevel"
pb.SC_SOCIETY_TOWER_CLEARANCE_TOPLEVEL.full_name = ".SC_Society_Tower_Clearance_Toplevel"
pb.SC_SOCIETY_TOWER_CLEARANCE_TOPLEVEL.nested_types = {}
pb.SC_SOCIETY_TOWER_CLEARANCE_TOPLEVEL.enum_types = {}
pb.SC_SOCIETY_TOWER_CLEARANCE_TOPLEVEL.fields = {pb.SC_SOCIETY_TOWER_CLEARANCE_TOPLEVEL_ACTORNAME_FIELD, pb.SC_SOCIETY_TOWER_CLEARANCE_TOPLEVEL_VOCATION_FIELD, pb.SC_SOCIETY_TOWER_CLEARANCE_TOPLEVEL_ECTYPEID_FIELD}
pb.SC_SOCIETY_TOWER_CLEARANCE_TOPLEVEL.is_extendable = false
pb.SC_SOCIETY_TOWER_CLEARANCE_TOPLEVEL.extensions = {}
pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPLIST_HELPDATA_ACTORID_FIELD.name = "ActorID"
pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPLIST_HELPDATA_ACTORID_FIELD.full_name = ".SC_Society_Alchemy_SpeedUp_HelpList.HelpData.ActorID"
pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPLIST_HELPDATA_ACTORID_FIELD.number = 1
pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPLIST_HELPDATA_ACTORID_FIELD.index = 0
pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPLIST_HELPDATA_ACTORID_FIELD.label = 1
pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPLIST_HELPDATA_ACTORID_FIELD.has_default_value = false
pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPLIST_HELPDATA_ACTORID_FIELD.default_value = 0
pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPLIST_HELPDATA_ACTORID_FIELD.type = 13
pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPLIST_HELPDATA_ACTORID_FIELD.cpp_type = 3

pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPLIST_HELPDATA_ACTORNAME_FIELD.name = "ActorName"
pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPLIST_HELPDATA_ACTORNAME_FIELD.full_name = ".SC_Society_Alchemy_SpeedUp_HelpList.HelpData.ActorName"
pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPLIST_HELPDATA_ACTORNAME_FIELD.number = 2
pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPLIST_HELPDATA_ACTORNAME_FIELD.index = 1
pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPLIST_HELPDATA_ACTORNAME_FIELD.label = 1
pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPLIST_HELPDATA_ACTORNAME_FIELD.has_default_value = false
pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPLIST_HELPDATA_ACTORNAME_FIELD.default_value = ""
pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPLIST_HELPDATA_ACTORNAME_FIELD.type = 9
pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPLIST_HELPDATA_ACTORNAME_FIELD.cpp_type = 9

pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPLIST_HELPDATA_USEDIAMOND_FIELD.name = "UseDiamond"
pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPLIST_HELPDATA_USEDIAMOND_FIELD.full_name = ".SC_Society_Alchemy_SpeedUp_HelpList.HelpData.UseDiamond"
pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPLIST_HELPDATA_USEDIAMOND_FIELD.number = 3
pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPLIST_HELPDATA_USEDIAMOND_FIELD.index = 2
pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPLIST_HELPDATA_USEDIAMOND_FIELD.label = 1
pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPLIST_HELPDATA_USEDIAMOND_FIELD.has_default_value = false
pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPLIST_HELPDATA_USEDIAMOND_FIELD.default_value = false
pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPLIST_HELPDATA_USEDIAMOND_FIELD.type = 8
pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPLIST_HELPDATA_USEDIAMOND_FIELD.cpp_type = 7

pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPLIST_HELPDATA_REDUCETIME_FIELD.name = "ReduceTime"
pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPLIST_HELPDATA_REDUCETIME_FIELD.full_name = ".SC_Society_Alchemy_SpeedUp_HelpList.HelpData.ReduceTime"
pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPLIST_HELPDATA_REDUCETIME_FIELD.number = 4
pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPLIST_HELPDATA_REDUCETIME_FIELD.index = 3
pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPLIST_HELPDATA_REDUCETIME_FIELD.label = 1
pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPLIST_HELPDATA_REDUCETIME_FIELD.has_default_value = false
pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPLIST_HELPDATA_REDUCETIME_FIELD.default_value = 0
pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPLIST_HELPDATA_REDUCETIME_FIELD.type = 13
pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPLIST_HELPDATA_REDUCETIME_FIELD.cpp_type = 3

pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPLIST_HELPDATA_SPEEDUPTIME_FIELD.name = "SpeedUpTime"
pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPLIST_HELPDATA_SPEEDUPTIME_FIELD.full_name = ".SC_Society_Alchemy_SpeedUp_HelpList.HelpData.SpeedUpTime"
pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPLIST_HELPDATA_SPEEDUPTIME_FIELD.number = 5
pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPLIST_HELPDATA_SPEEDUPTIME_FIELD.index = 4
pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPLIST_HELPDATA_SPEEDUPTIME_FIELD.label = 1
pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPLIST_HELPDATA_SPEEDUPTIME_FIELD.has_default_value = false
pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPLIST_HELPDATA_SPEEDUPTIME_FIELD.default_value = 0
pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPLIST_HELPDATA_SPEEDUPTIME_FIELD.type = 13
pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPLIST_HELPDATA_SPEEDUPTIME_FIELD.cpp_type = 3

pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPLIST_HELPDATA_ISREADED_FIELD.name = "IsReaded"
pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPLIST_HELPDATA_ISREADED_FIELD.full_name = ".SC_Society_Alchemy_SpeedUp_HelpList.HelpData.IsReaded"
pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPLIST_HELPDATA_ISREADED_FIELD.number = 6
pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPLIST_HELPDATA_ISREADED_FIELD.index = 5
pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPLIST_HELPDATA_ISREADED_FIELD.label = 1
pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPLIST_HELPDATA_ISREADED_FIELD.has_default_value = false
pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPLIST_HELPDATA_ISREADED_FIELD.default_value = false
pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPLIST_HELPDATA_ISREADED_FIELD.type = 8
pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPLIST_HELPDATA_ISREADED_FIELD.cpp_type = 7

pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPLIST_HELPDATA.name = "HelpData"
pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPLIST_HELPDATA.full_name = ".SC_Society_Alchemy_SpeedUp_HelpList.HelpData"
pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPLIST_HELPDATA.nested_types = {}
pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPLIST_HELPDATA.enum_types = {}
pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPLIST_HELPDATA.fields = {pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPLIST_HELPDATA_ACTORID_FIELD, pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPLIST_HELPDATA_ACTORNAME_FIELD, pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPLIST_HELPDATA_USEDIAMOND_FIELD, pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPLIST_HELPDATA_REDUCETIME_FIELD, pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPLIST_HELPDATA_SPEEDUPTIME_FIELD, pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPLIST_HELPDATA_ISREADED_FIELD}
pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPLIST_HELPDATA.is_extendable = false
pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPLIST_HELPDATA.extensions = {}
pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPLIST_HELPDATA.containing_type = pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPLIST
pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPLIST_HELPLIST_FIELD.name = "HelpList"
pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPLIST_HELPLIST_FIELD.full_name = ".SC_Society_Alchemy_SpeedUp_HelpList.HelpList"
pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPLIST_HELPLIST_FIELD.number = 1
pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPLIST_HELPLIST_FIELD.index = 0
pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPLIST_HELPLIST_FIELD.label = 3
pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPLIST_HELPLIST_FIELD.has_default_value = false
pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPLIST_HELPLIST_FIELD.default_value = {}
pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPLIST_HELPLIST_FIELD.message_type = pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPLIST_HELPDATA
pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPLIST_HELPLIST_FIELD.type = 11
pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPLIST_HELPLIST_FIELD.cpp_type = 10

pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPLIST.name = "SC_Society_Alchemy_SpeedUp_HelpList"
pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPLIST.full_name = ".SC_Society_Alchemy_SpeedUp_HelpList"
pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPLIST.nested_types = {pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPLIST_HELPDATA}
pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPLIST.enum_types = {}
pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPLIST.fields = {pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPLIST_HELPLIST_FIELD}
pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPLIST.is_extendable = false
pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPLIST.extensions = {}
pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPEDLIST_HELPEDDATA_ACTORID_FIELD.name = "ActorID"
pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPEDLIST_HELPEDDATA_ACTORID_FIELD.full_name = ".SC_Society_Alchemy_SpeedUp_HelpedList.HelpedData.ActorID"
pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPEDLIST_HELPEDDATA_ACTORID_FIELD.number = 1
pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPEDLIST_HELPEDDATA_ACTORID_FIELD.index = 0
pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPEDLIST_HELPEDDATA_ACTORID_FIELD.label = 1
pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPEDLIST_HELPEDDATA_ACTORID_FIELD.has_default_value = false
pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPEDLIST_HELPEDDATA_ACTORID_FIELD.default_value = 0
pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPEDLIST_HELPEDDATA_ACTORID_FIELD.type = 13
pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPEDLIST_HELPEDDATA_ACTORID_FIELD.cpp_type = 3

pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPEDLIST_HELPEDDATA_ACTORNAME_FIELD.name = "ActorName"
pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPEDLIST_HELPEDDATA_ACTORNAME_FIELD.full_name = ".SC_Society_Alchemy_SpeedUp_HelpedList.HelpedData.ActorName"
pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPEDLIST_HELPEDDATA_ACTORNAME_FIELD.number = 2
pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPEDLIST_HELPEDDATA_ACTORNAME_FIELD.index = 1
pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPEDLIST_HELPEDDATA_ACTORNAME_FIELD.label = 1
pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPEDLIST_HELPEDDATA_ACTORNAME_FIELD.has_default_value = false
pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPEDLIST_HELPEDDATA_ACTORNAME_FIELD.default_value = ""
pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPEDLIST_HELPEDDATA_ACTORNAME_FIELD.type = 9
pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPEDLIST_HELPEDDATA_ACTORNAME_FIELD.cpp_type = 9

pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPEDLIST_HELPEDDATA_USEDIAMOND_FIELD.name = "UseDiamond"
pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPEDLIST_HELPEDDATA_USEDIAMOND_FIELD.full_name = ".SC_Society_Alchemy_SpeedUp_HelpedList.HelpedData.UseDiamond"
pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPEDLIST_HELPEDDATA_USEDIAMOND_FIELD.number = 3
pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPEDLIST_HELPEDDATA_USEDIAMOND_FIELD.index = 2
pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPEDLIST_HELPEDDATA_USEDIAMOND_FIELD.label = 1
pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPEDLIST_HELPEDDATA_USEDIAMOND_FIELD.has_default_value = false
pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPEDLIST_HELPEDDATA_USEDIAMOND_FIELD.default_value = false
pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPEDLIST_HELPEDDATA_USEDIAMOND_FIELD.type = 8
pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPEDLIST_HELPEDDATA_USEDIAMOND_FIELD.cpp_type = 7

pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPEDLIST_HELPEDDATA_REDUCETIME_FIELD.name = "ReduceTime"
pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPEDLIST_HELPEDDATA_REDUCETIME_FIELD.full_name = ".SC_Society_Alchemy_SpeedUp_HelpedList.HelpedData.ReduceTime"
pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPEDLIST_HELPEDDATA_REDUCETIME_FIELD.number = 4
pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPEDLIST_HELPEDDATA_REDUCETIME_FIELD.index = 3
pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPEDLIST_HELPEDDATA_REDUCETIME_FIELD.label = 1
pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPEDLIST_HELPEDDATA_REDUCETIME_FIELD.has_default_value = false
pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPEDLIST_HELPEDDATA_REDUCETIME_FIELD.default_value = 0
pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPEDLIST_HELPEDDATA_REDUCETIME_FIELD.type = 13
pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPEDLIST_HELPEDDATA_REDUCETIME_FIELD.cpp_type = 3

pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPEDLIST_HELPEDDATA_SPEEDUPTIME_FIELD.name = "SpeedUpTime"
pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPEDLIST_HELPEDDATA_SPEEDUPTIME_FIELD.full_name = ".SC_Society_Alchemy_SpeedUp_HelpedList.HelpedData.SpeedUpTime"
pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPEDLIST_HELPEDDATA_SPEEDUPTIME_FIELD.number = 5
pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPEDLIST_HELPEDDATA_SPEEDUPTIME_FIELD.index = 4
pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPEDLIST_HELPEDDATA_SPEEDUPTIME_FIELD.label = 1
pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPEDLIST_HELPEDDATA_SPEEDUPTIME_FIELD.has_default_value = false
pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPEDLIST_HELPEDDATA_SPEEDUPTIME_FIELD.default_value = 0
pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPEDLIST_HELPEDDATA_SPEEDUPTIME_FIELD.type = 13
pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPEDLIST_HELPEDDATA_SPEEDUPTIME_FIELD.cpp_type = 3

pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPEDLIST_HELPEDDATA_ISREADED_FIELD.name = "IsReaded"
pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPEDLIST_HELPEDDATA_ISREADED_FIELD.full_name = ".SC_Society_Alchemy_SpeedUp_HelpedList.HelpedData.IsReaded"
pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPEDLIST_HELPEDDATA_ISREADED_FIELD.number = 6
pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPEDLIST_HELPEDDATA_ISREADED_FIELD.index = 5
pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPEDLIST_HELPEDDATA_ISREADED_FIELD.label = 1
pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPEDLIST_HELPEDDATA_ISREADED_FIELD.has_default_value = false
pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPEDLIST_HELPEDDATA_ISREADED_FIELD.default_value = false
pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPEDLIST_HELPEDDATA_ISREADED_FIELD.type = 8
pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPEDLIST_HELPEDDATA_ISREADED_FIELD.cpp_type = 7

pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPEDLIST_HELPEDDATA.name = "HelpedData"
pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPEDLIST_HELPEDDATA.full_name = ".SC_Society_Alchemy_SpeedUp_HelpedList.HelpedData"
pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPEDLIST_HELPEDDATA.nested_types = {}
pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPEDLIST_HELPEDDATA.enum_types = {}
pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPEDLIST_HELPEDDATA.fields = {pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPEDLIST_HELPEDDATA_ACTORID_FIELD, pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPEDLIST_HELPEDDATA_ACTORNAME_FIELD, pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPEDLIST_HELPEDDATA_USEDIAMOND_FIELD, pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPEDLIST_HELPEDDATA_REDUCETIME_FIELD, pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPEDLIST_HELPEDDATA_SPEEDUPTIME_FIELD, pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPEDLIST_HELPEDDATA_ISREADED_FIELD}
pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPEDLIST_HELPEDDATA.is_extendable = false
pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPEDLIST_HELPEDDATA.extensions = {}
pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPEDLIST_HELPEDDATA.containing_type = pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPEDLIST
pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPEDLIST_HELPEDLIST_FIELD.name = "HelpedList"
pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPEDLIST_HELPEDLIST_FIELD.full_name = ".SC_Society_Alchemy_SpeedUp_HelpedList.HelpedList"
pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPEDLIST_HELPEDLIST_FIELD.number = 1
pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPEDLIST_HELPEDLIST_FIELD.index = 0
pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPEDLIST_HELPEDLIST_FIELD.label = 3
pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPEDLIST_HELPEDLIST_FIELD.has_default_value = false
pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPEDLIST_HELPEDLIST_FIELD.default_value = {}
pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPEDLIST_HELPEDLIST_FIELD.message_type = pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPEDLIST_HELPEDDATA
pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPEDLIST_HELPEDLIST_FIELD.type = 11
pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPEDLIST_HELPEDLIST_FIELD.cpp_type = 10

pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPEDLIST.name = "SC_Society_Alchemy_SpeedUp_HelpedList"
pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPEDLIST.full_name = ".SC_Society_Alchemy_SpeedUp_HelpedList"
pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPEDLIST.nested_types = {pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPEDLIST_HELPEDDATA}
pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPEDLIST.enum_types = {}
pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPEDLIST.fields = {pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPEDLIST_HELPEDLIST_FIELD}
pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPEDLIST.is_extendable = false
pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPEDLIST.extensions = {}
pb.CS_SOCIETY_INVITEJOIN_ACTORID_FIELD.name = "ActorID"
pb.CS_SOCIETY_INVITEJOIN_ACTORID_FIELD.full_name = ".CS_Society_InviteJoin.ActorID"
pb.CS_SOCIETY_INVITEJOIN_ACTORID_FIELD.number = 1
pb.CS_SOCIETY_INVITEJOIN_ACTORID_FIELD.index = 0
pb.CS_SOCIETY_INVITEJOIN_ACTORID_FIELD.label = 1
pb.CS_SOCIETY_INVITEJOIN_ACTORID_FIELD.has_default_value = false
pb.CS_SOCIETY_INVITEJOIN_ACTORID_FIELD.default_value = 0
pb.CS_SOCIETY_INVITEJOIN_ACTORID_FIELD.type = 13
pb.CS_SOCIETY_INVITEJOIN_ACTORID_FIELD.cpp_type = 3

pb.CS_SOCIETY_INVITEJOIN.name = "CS_Society_InviteJoin"
pb.CS_SOCIETY_INVITEJOIN.full_name = ".CS_Society_InviteJoin"
pb.CS_SOCIETY_INVITEJOIN.nested_types = {}
pb.CS_SOCIETY_INVITEJOIN.enum_types = {}
pb.CS_SOCIETY_INVITEJOIN.fields = {pb.CS_SOCIETY_INVITEJOIN_ACTORID_FIELD}
pb.CS_SOCIETY_INVITEJOIN.is_extendable = false
pb.CS_SOCIETY_INVITEJOIN.extensions = {}
pb.SC_SOCIETY_INVITEJOIN_ACTORID_FIELD.name = "ActorID"
pb.SC_SOCIETY_INVITEJOIN_ACTORID_FIELD.full_name = ".SC_Society_InviteJoin.ActorID"
pb.SC_SOCIETY_INVITEJOIN_ACTORID_FIELD.number = 1
pb.SC_SOCIETY_INVITEJOIN_ACTORID_FIELD.index = 0
pb.SC_SOCIETY_INVITEJOIN_ACTORID_FIELD.label = 1
pb.SC_SOCIETY_INVITEJOIN_ACTORID_FIELD.has_default_value = false
pb.SC_SOCIETY_INVITEJOIN_ACTORID_FIELD.default_value = 0
pb.SC_SOCIETY_INVITEJOIN_ACTORID_FIELD.type = 13
pb.SC_SOCIETY_INVITEJOIN_ACTORID_FIELD.cpp_type = 3

pb.SC_SOCIETY_INVITEJOIN_ACTORNAME_FIELD.name = "ActorName"
pb.SC_SOCIETY_INVITEJOIN_ACTORNAME_FIELD.full_name = ".SC_Society_InviteJoin.ActorName"
pb.SC_SOCIETY_INVITEJOIN_ACTORNAME_FIELD.number = 2
pb.SC_SOCIETY_INVITEJOIN_ACTORNAME_FIELD.index = 1
pb.SC_SOCIETY_INVITEJOIN_ACTORNAME_FIELD.label = 1
pb.SC_SOCIETY_INVITEJOIN_ACTORNAME_FIELD.has_default_value = false
pb.SC_SOCIETY_INVITEJOIN_ACTORNAME_FIELD.default_value = ""
pb.SC_SOCIETY_INVITEJOIN_ACTORNAME_FIELD.type = 9
pb.SC_SOCIETY_INVITEJOIN_ACTORNAME_FIELD.cpp_type = 9

pb.SC_SOCIETY_INVITEJOIN_SOCIETYID_FIELD.name = "SocietyID"
pb.SC_SOCIETY_INVITEJOIN_SOCIETYID_FIELD.full_name = ".SC_Society_InviteJoin.SocietyID"
pb.SC_SOCIETY_INVITEJOIN_SOCIETYID_FIELD.number = 3
pb.SC_SOCIETY_INVITEJOIN_SOCIETYID_FIELD.index = 2
pb.SC_SOCIETY_INVITEJOIN_SOCIETYID_FIELD.label = 1
pb.SC_SOCIETY_INVITEJOIN_SOCIETYID_FIELD.has_default_value = false
pb.SC_SOCIETY_INVITEJOIN_SOCIETYID_FIELD.default_value = 0
pb.SC_SOCIETY_INVITEJOIN_SOCIETYID_FIELD.type = 13
pb.SC_SOCIETY_INVITEJOIN_SOCIETYID_FIELD.cpp_type = 3

pb.SC_SOCIETY_INVITEJOIN_SOCIETYNAME_FIELD.name = "SocietyName"
pb.SC_SOCIETY_INVITEJOIN_SOCIETYNAME_FIELD.full_name = ".SC_Society_InviteJoin.SocietyName"
pb.SC_SOCIETY_INVITEJOIN_SOCIETYNAME_FIELD.number = 4
pb.SC_SOCIETY_INVITEJOIN_SOCIETYNAME_FIELD.index = 3
pb.SC_SOCIETY_INVITEJOIN_SOCIETYNAME_FIELD.label = 1
pb.SC_SOCIETY_INVITEJOIN_SOCIETYNAME_FIELD.has_default_value = false
pb.SC_SOCIETY_INVITEJOIN_SOCIETYNAME_FIELD.default_value = ""
pb.SC_SOCIETY_INVITEJOIN_SOCIETYNAME_FIELD.type = 9
pb.SC_SOCIETY_INVITEJOIN_SOCIETYNAME_FIELD.cpp_type = 9

pb.SC_SOCIETY_INVITEJOIN.name = "SC_Society_InviteJoin"
pb.SC_SOCIETY_INVITEJOIN.full_name = ".SC_Society_InviteJoin"
pb.SC_SOCIETY_INVITEJOIN.nested_types = {}
pb.SC_SOCIETY_INVITEJOIN.enum_types = {}
pb.SC_SOCIETY_INVITEJOIN.fields = {pb.SC_SOCIETY_INVITEJOIN_ACTORID_FIELD, pb.SC_SOCIETY_INVITEJOIN_ACTORNAME_FIELD, pb.SC_SOCIETY_INVITEJOIN_SOCIETYID_FIELD, pb.SC_SOCIETY_INVITEJOIN_SOCIETYNAME_FIELD}
pb.SC_SOCIETY_INVITEJOIN.is_extendable = false
pb.SC_SOCIETY_INVITEJOIN.extensions = {}
pb.CS_SOCIETY_UPDATEREDPACKET_ACTORID_FIELD.name = "ActorID"
pb.CS_SOCIETY_UPDATEREDPACKET_ACTORID_FIELD.full_name = ".CS_Society_UpdateRedPacket.ActorID"
pb.CS_SOCIETY_UPDATEREDPACKET_ACTORID_FIELD.number = 1
pb.CS_SOCIETY_UPDATEREDPACKET_ACTORID_FIELD.index = 0
pb.CS_SOCIETY_UPDATEREDPACKET_ACTORID_FIELD.label = 1
pb.CS_SOCIETY_UPDATEREDPACKET_ACTORID_FIELD.has_default_value = false
pb.CS_SOCIETY_UPDATEREDPACKET_ACTORID_FIELD.default_value = 0
pb.CS_SOCIETY_UPDATEREDPACKET_ACTORID_FIELD.type = 13
pb.CS_SOCIETY_UPDATEREDPACKET_ACTORID_FIELD.cpp_type = 3

pb.CS_SOCIETY_UPDATEREDPACKET.name = "CS_Society_UpdateRedPacket"
pb.CS_SOCIETY_UPDATEREDPACKET.full_name = ".CS_Society_UpdateRedPacket"
pb.CS_SOCIETY_UPDATEREDPACKET.nested_types = {}
pb.CS_SOCIETY_UPDATEREDPACKET.enum_types = {}
pb.CS_SOCIETY_UPDATEREDPACKET.fields = {pb.CS_SOCIETY_UPDATEREDPACKET_ACTORID_FIELD}
pb.CS_SOCIETY_UPDATEREDPACKET.is_extendable = false
pb.CS_SOCIETY_UPDATEREDPACKET.extensions = {}
pb.SC_SOCIETY_UPDATEREDPACKET_RESULT_FIELD.name = "Result"
pb.SC_SOCIETY_UPDATEREDPACKET_RESULT_FIELD.full_name = ".SC_Society_UpdateRedPacket.Result"
pb.SC_SOCIETY_UPDATEREDPACKET_RESULT_FIELD.number = 1
pb.SC_SOCIETY_UPDATEREDPACKET_RESULT_FIELD.index = 0
pb.SC_SOCIETY_UPDATEREDPACKET_RESULT_FIELD.label = 1
pb.SC_SOCIETY_UPDATEREDPACKET_RESULT_FIELD.has_default_value = false
pb.SC_SOCIETY_UPDATEREDPACKET_RESULT_FIELD.default_value = 0
pb.SC_SOCIETY_UPDATEREDPACKET_RESULT_FIELD.type = 5
pb.SC_SOCIETY_UPDATEREDPACKET_RESULT_FIELD.cpp_type = 1

pb.SC_SOCIETY_UPDATEREDPACKET_TOTALOPENCOUNT_FIELD.name = "TotalOpenCount"
pb.SC_SOCIETY_UPDATEREDPACKET_TOTALOPENCOUNT_FIELD.full_name = ".SC_Society_UpdateRedPacket.TotalOpenCount"
pb.SC_SOCIETY_UPDATEREDPACKET_TOTALOPENCOUNT_FIELD.number = 2
pb.SC_SOCIETY_UPDATEREDPACKET_TOTALOPENCOUNT_FIELD.index = 1
pb.SC_SOCIETY_UPDATEREDPACKET_TOTALOPENCOUNT_FIELD.label = 1
pb.SC_SOCIETY_UPDATEREDPACKET_TOTALOPENCOUNT_FIELD.has_default_value = false
pb.SC_SOCIETY_UPDATEREDPACKET_TOTALOPENCOUNT_FIELD.default_value = 0
pb.SC_SOCIETY_UPDATEREDPACKET_TOTALOPENCOUNT_FIELD.type = 13
pb.SC_SOCIETY_UPDATEREDPACKET_TOTALOPENCOUNT_FIELD.cpp_type = 3

pb.SC_SOCIETY_UPDATEREDPACKET_SOCIETYTOTALOPENCOUNT_FIELD.name = "SocietyTotalOpenCount"
pb.SC_SOCIETY_UPDATEREDPACKET_SOCIETYTOTALOPENCOUNT_FIELD.full_name = ".SC_Society_UpdateRedPacket.SocietyTotalOpenCount"
pb.SC_SOCIETY_UPDATEREDPACKET_SOCIETYTOTALOPENCOUNT_FIELD.number = 3
pb.SC_SOCIETY_UPDATEREDPACKET_SOCIETYTOTALOPENCOUNT_FIELD.index = 2
pb.SC_SOCIETY_UPDATEREDPACKET_SOCIETYTOTALOPENCOUNT_FIELD.label = 1
pb.SC_SOCIETY_UPDATEREDPACKET_SOCIETYTOTALOPENCOUNT_FIELD.has_default_value = false
pb.SC_SOCIETY_UPDATEREDPACKET_SOCIETYTOTALOPENCOUNT_FIELD.default_value = 0
pb.SC_SOCIETY_UPDATEREDPACKET_SOCIETYTOTALOPENCOUNT_FIELD.type = 13
pb.SC_SOCIETY_UPDATEREDPACKET_SOCIETYTOTALOPENCOUNT_FIELD.cpp_type = 3

pb.SC_SOCIETY_UPDATEREDPACKET.name = "SC_Society_UpdateRedPacket"
pb.SC_SOCIETY_UPDATEREDPACKET.full_name = ".SC_Society_UpdateRedPacket"
pb.SC_SOCIETY_UPDATEREDPACKET.nested_types = {}
pb.SC_SOCIETY_UPDATEREDPACKET.enum_types = {}
pb.SC_SOCIETY_UPDATEREDPACKET.fields = {pb.SC_SOCIETY_UPDATEREDPACKET_RESULT_FIELD, pb.SC_SOCIETY_UPDATEREDPACKET_TOTALOPENCOUNT_FIELD, pb.SC_SOCIETY_UPDATEREDPACKET_SOCIETYTOTALOPENCOUNT_FIELD}
pb.SC_SOCIETY_UPDATEREDPACKET.is_extendable = false
pb.SC_SOCIETY_UPDATEREDPACKET.extensions = {}
pb.CS_SOCIETY_RED_PACKET_RANK_ACTORID_FIELD.name = "ActorID"
pb.CS_SOCIETY_RED_PACKET_RANK_ACTORID_FIELD.full_name = ".CS_Society_Red_Packet_Rank.ActorID"
pb.CS_SOCIETY_RED_PACKET_RANK_ACTORID_FIELD.number = 1
pb.CS_SOCIETY_RED_PACKET_RANK_ACTORID_FIELD.index = 0
pb.CS_SOCIETY_RED_PACKET_RANK_ACTORID_FIELD.label = 1
pb.CS_SOCIETY_RED_PACKET_RANK_ACTORID_FIELD.has_default_value = false
pb.CS_SOCIETY_RED_PACKET_RANK_ACTORID_FIELD.default_value = 0
pb.CS_SOCIETY_RED_PACKET_RANK_ACTORID_FIELD.type = 13
pb.CS_SOCIETY_RED_PACKET_RANK_ACTORID_FIELD.cpp_type = 3

pb.CS_SOCIETY_RED_PACKET_RANK.name = "CS_Society_Red_Packet_Rank"
pb.CS_SOCIETY_RED_PACKET_RANK.full_name = ".CS_Society_Red_Packet_Rank"
pb.CS_SOCIETY_RED_PACKET_RANK.nested_types = {}
pb.CS_SOCIETY_RED_PACKET_RANK.enum_types = {}
pb.CS_SOCIETY_RED_PACKET_RANK.fields = {pb.CS_SOCIETY_RED_PACKET_RANK_ACTORID_FIELD}
pb.CS_SOCIETY_RED_PACKET_RANK.is_extendable = false
pb.CS_SOCIETY_RED_PACKET_RANK.extensions = {}
pb.SC_SOCIETY_RED_PACKET_RANK_RANKINFO_SOCIETYID_FIELD.name = "SocietyID"
pb.SC_SOCIETY_RED_PACKET_RANK_RANKINFO_SOCIETYID_FIELD.full_name = ".SC_Society_Red_Packet_Rank.RankInfo.SocietyID"
pb.SC_SOCIETY_RED_PACKET_RANK_RANKINFO_SOCIETYID_FIELD.number = 1
pb.SC_SOCIETY_RED_PACKET_RANK_RANKINFO_SOCIETYID_FIELD.index = 0
pb.SC_SOCIETY_RED_PACKET_RANK_RANKINFO_SOCIETYID_FIELD.label = 1
pb.SC_SOCIETY_RED_PACKET_RANK_RANKINFO_SOCIETYID_FIELD.has_default_value = false
pb.SC_SOCIETY_RED_PACKET_RANK_RANKINFO_SOCIETYID_FIELD.default_value = 0
pb.SC_SOCIETY_RED_PACKET_RANK_RANKINFO_SOCIETYID_FIELD.type = 13
pb.SC_SOCIETY_RED_PACKET_RANK_RANKINFO_SOCIETYID_FIELD.cpp_type = 3

pb.SC_SOCIETY_RED_PACKET_RANK_RANKINFO_COUNTRYID_FIELD.name = "CountryID"
pb.SC_SOCIETY_RED_PACKET_RANK_RANKINFO_COUNTRYID_FIELD.full_name = ".SC_Society_Red_Packet_Rank.RankInfo.CountryID"
pb.SC_SOCIETY_RED_PACKET_RANK_RANKINFO_COUNTRYID_FIELD.number = 2
pb.SC_SOCIETY_RED_PACKET_RANK_RANKINFO_COUNTRYID_FIELD.index = 1
pb.SC_SOCIETY_RED_PACKET_RANK_RANKINFO_COUNTRYID_FIELD.label = 1
pb.SC_SOCIETY_RED_PACKET_RANK_RANKINFO_COUNTRYID_FIELD.has_default_value = false
pb.SC_SOCIETY_RED_PACKET_RANK_RANKINFO_COUNTRYID_FIELD.default_value = 0
pb.SC_SOCIETY_RED_PACKET_RANK_RANKINFO_COUNTRYID_FIELD.type = 13
pb.SC_SOCIETY_RED_PACKET_RANK_RANKINFO_COUNTRYID_FIELD.cpp_type = 3

pb.SC_SOCIETY_RED_PACKET_RANK_RANKINFO_SOCIETYNAME_FIELD.name = "SocietyName"
pb.SC_SOCIETY_RED_PACKET_RANK_RANKINFO_SOCIETYNAME_FIELD.full_name = ".SC_Society_Red_Packet_Rank.RankInfo.SocietyName"
pb.SC_SOCIETY_RED_PACKET_RANK_RANKINFO_SOCIETYNAME_FIELD.number = 3
pb.SC_SOCIETY_RED_PACKET_RANK_RANKINFO_SOCIETYNAME_FIELD.index = 2
pb.SC_SOCIETY_RED_PACKET_RANK_RANKINFO_SOCIETYNAME_FIELD.label = 1
pb.SC_SOCIETY_RED_PACKET_RANK_RANKINFO_SOCIETYNAME_FIELD.has_default_value = false
pb.SC_SOCIETY_RED_PACKET_RANK_RANKINFO_SOCIETYNAME_FIELD.default_value = ""
pb.SC_SOCIETY_RED_PACKET_RANK_RANKINFO_SOCIETYNAME_FIELD.type = 9
pb.SC_SOCIETY_RED_PACKET_RANK_RANKINFO_SOCIETYNAME_FIELD.cpp_type = 9

pb.SC_SOCIETY_RED_PACKET_RANK_RANKINFO_OPENCOUNT_FIELD.name = "OpenCount"
pb.SC_SOCIETY_RED_PACKET_RANK_RANKINFO_OPENCOUNT_FIELD.full_name = ".SC_Society_Red_Packet_Rank.RankInfo.OpenCount"
pb.SC_SOCIETY_RED_PACKET_RANK_RANKINFO_OPENCOUNT_FIELD.number = 4
pb.SC_SOCIETY_RED_PACKET_RANK_RANKINFO_OPENCOUNT_FIELD.index = 3
pb.SC_SOCIETY_RED_PACKET_RANK_RANKINFO_OPENCOUNT_FIELD.label = 1
pb.SC_SOCIETY_RED_PACKET_RANK_RANKINFO_OPENCOUNT_FIELD.has_default_value = false
pb.SC_SOCIETY_RED_PACKET_RANK_RANKINFO_OPENCOUNT_FIELD.default_value = 0
pb.SC_SOCIETY_RED_PACKET_RANK_RANKINFO_OPENCOUNT_FIELD.type = 13
pb.SC_SOCIETY_RED_PACKET_RANK_RANKINFO_OPENCOUNT_FIELD.cpp_type = 3

pb.SC_SOCIETY_RED_PACKET_RANK_RANKINFO.name = "RankInfo"
pb.SC_SOCIETY_RED_PACKET_RANK_RANKINFO.full_name = ".SC_Society_Red_Packet_Rank.RankInfo"
pb.SC_SOCIETY_RED_PACKET_RANK_RANKINFO.nested_types = {}
pb.SC_SOCIETY_RED_PACKET_RANK_RANKINFO.enum_types = {}
pb.SC_SOCIETY_RED_PACKET_RANK_RANKINFO.fields = {pb.SC_SOCIETY_RED_PACKET_RANK_RANKINFO_SOCIETYID_FIELD, pb.SC_SOCIETY_RED_PACKET_RANK_RANKINFO_COUNTRYID_FIELD, pb.SC_SOCIETY_RED_PACKET_RANK_RANKINFO_SOCIETYNAME_FIELD, pb.SC_SOCIETY_RED_PACKET_RANK_RANKINFO_OPENCOUNT_FIELD}
pb.SC_SOCIETY_RED_PACKET_RANK_RANKINFO.is_extendable = false
pb.SC_SOCIETY_RED_PACKET_RANK_RANKINFO.extensions = {}
pb.SC_SOCIETY_RED_PACKET_RANK_RANKINFO.containing_type = pb.SC_SOCIETY_RED_PACKET_RANK
pb.SC_SOCIETY_RED_PACKET_RANK_RESULT_FIELD.name = "Result"
pb.SC_SOCIETY_RED_PACKET_RANK_RESULT_FIELD.full_name = ".SC_Society_Red_Packet_Rank.Result"
pb.SC_SOCIETY_RED_PACKET_RANK_RESULT_FIELD.number = 1
pb.SC_SOCIETY_RED_PACKET_RANK_RESULT_FIELD.index = 0
pb.SC_SOCIETY_RED_PACKET_RANK_RESULT_FIELD.label = 1
pb.SC_SOCIETY_RED_PACKET_RANK_RESULT_FIELD.has_default_value = false
pb.SC_SOCIETY_RED_PACKET_RANK_RESULT_FIELD.default_value = 0
pb.SC_SOCIETY_RED_PACKET_RANK_RESULT_FIELD.type = 5
pb.SC_SOCIETY_RED_PACKET_RANK_RESULT_FIELD.cpp_type = 1

pb.SC_SOCIETY_RED_PACKET_RANK_RANKLIST_FIELD.name = "RankList"
pb.SC_SOCIETY_RED_PACKET_RANK_RANKLIST_FIELD.full_name = ".SC_Society_Red_Packet_Rank.RankList"
pb.SC_SOCIETY_RED_PACKET_RANK_RANKLIST_FIELD.number = 2
pb.SC_SOCIETY_RED_PACKET_RANK_RANKLIST_FIELD.index = 1
pb.SC_SOCIETY_RED_PACKET_RANK_RANKLIST_FIELD.label = 3
pb.SC_SOCIETY_RED_PACKET_RANK_RANKLIST_FIELD.has_default_value = false
pb.SC_SOCIETY_RED_PACKET_RANK_RANKLIST_FIELD.default_value = {}
pb.SC_SOCIETY_RED_PACKET_RANK_RANKLIST_FIELD.message_type = pb.SC_SOCIETY_RED_PACKET_RANK_RANKINFO
pb.SC_SOCIETY_RED_PACKET_RANK_RANKLIST_FIELD.type = 11
pb.SC_SOCIETY_RED_PACKET_RANK_RANKLIST_FIELD.cpp_type = 10

pb.SC_SOCIETY_RED_PACKET_RANK.name = "SC_Society_Red_Packet_Rank"
pb.SC_SOCIETY_RED_PACKET_RANK.full_name = ".SC_Society_Red_Packet_Rank"
pb.SC_SOCIETY_RED_PACKET_RANK.nested_types = {pb.SC_SOCIETY_RED_PACKET_RANK_RANKINFO}
pb.SC_SOCIETY_RED_PACKET_RANK.enum_types = {}
pb.SC_SOCIETY_RED_PACKET_RANK.fields = {pb.SC_SOCIETY_RED_PACKET_RANK_RESULT_FIELD, pb.SC_SOCIETY_RED_PACKET_RANK_RANKLIST_FIELD}
pb.SC_SOCIETY_RED_PACKET_RANK.is_extendable = false
pb.SC_SOCIETY_RED_PACKET_RANK.extensions = {}
pb.CS_SOCIETY_RED_PACKET_PASSWORD_ACTORID_FIELD.name = "ActorID"
pb.CS_SOCIETY_RED_PACKET_PASSWORD_ACTORID_FIELD.full_name = ".CS_Society_Red_Packet_PassWord.ActorID"
pb.CS_SOCIETY_RED_PACKET_PASSWORD_ACTORID_FIELD.number = 1
pb.CS_SOCIETY_RED_PACKET_PASSWORD_ACTORID_FIELD.index = 0
pb.CS_SOCIETY_RED_PACKET_PASSWORD_ACTORID_FIELD.label = 1
pb.CS_SOCIETY_RED_PACKET_PASSWORD_ACTORID_FIELD.has_default_value = false
pb.CS_SOCIETY_RED_PACKET_PASSWORD_ACTORID_FIELD.default_value = 0
pb.CS_SOCIETY_RED_PACKET_PASSWORD_ACTORID_FIELD.type = 13
pb.CS_SOCIETY_RED_PACKET_PASSWORD_ACTORID_FIELD.cpp_type = 3

pb.CS_SOCIETY_RED_PACKET_PASSWORD.name = "CS_Society_Red_Packet_PassWord"
pb.CS_SOCIETY_RED_PACKET_PASSWORD.full_name = ".CS_Society_Red_Packet_PassWord"
pb.CS_SOCIETY_RED_PACKET_PASSWORD.nested_types = {}
pb.CS_SOCIETY_RED_PACKET_PASSWORD.enum_types = {}
pb.CS_SOCIETY_RED_PACKET_PASSWORD.fields = {pb.CS_SOCIETY_RED_PACKET_PASSWORD_ACTORID_FIELD}
pb.CS_SOCIETY_RED_PACKET_PASSWORD.is_extendable = false
pb.CS_SOCIETY_RED_PACKET_PASSWORD.extensions = {}
pb.SC_SOCIETY_RED_PACKET_PASSWORD_RESULT_FIELD.name = "Result"
pb.SC_SOCIETY_RED_PACKET_PASSWORD_RESULT_FIELD.full_name = ".SC_Society_Red_Packet_PassWord.Result"
pb.SC_SOCIETY_RED_PACKET_PASSWORD_RESULT_FIELD.number = 1
pb.SC_SOCIETY_RED_PACKET_PASSWORD_RESULT_FIELD.index = 0
pb.SC_SOCIETY_RED_PACKET_PASSWORD_RESULT_FIELD.label = 1
pb.SC_SOCIETY_RED_PACKET_PASSWORD_RESULT_FIELD.has_default_value = false
pb.SC_SOCIETY_RED_PACKET_PASSWORD_RESULT_FIELD.default_value = 0
pb.SC_SOCIETY_RED_PACKET_PASSWORD_RESULT_FIELD.type = 5
pb.SC_SOCIETY_RED_PACKET_PASSWORD_RESULT_FIELD.cpp_type = 1

pb.SC_SOCIETY_RED_PACKET_PASSWORD_PASSWORD_FIELD.name = "PassWord"
pb.SC_SOCIETY_RED_PACKET_PASSWORD_PASSWORD_FIELD.full_name = ".SC_Society_Red_Packet_PassWord.PassWord"
pb.SC_SOCIETY_RED_PACKET_PASSWORD_PASSWORD_FIELD.number = 2
pb.SC_SOCIETY_RED_PACKET_PASSWORD_PASSWORD_FIELD.index = 1
pb.SC_SOCIETY_RED_PACKET_PASSWORD_PASSWORD_FIELD.label = 1
pb.SC_SOCIETY_RED_PACKET_PASSWORD_PASSWORD_FIELD.has_default_value = false
pb.SC_SOCIETY_RED_PACKET_PASSWORD_PASSWORD_FIELD.default_value = 0
pb.SC_SOCIETY_RED_PACKET_PASSWORD_PASSWORD_FIELD.type = 13
pb.SC_SOCIETY_RED_PACKET_PASSWORD_PASSWORD_FIELD.cpp_type = 3

pb.SC_SOCIETY_RED_PACKET_PASSWORD.name = "SC_Society_Red_Packet_PassWord"
pb.SC_SOCIETY_RED_PACKET_PASSWORD.full_name = ".SC_Society_Red_Packet_PassWord"
pb.SC_SOCIETY_RED_PACKET_PASSWORD.nested_types = {}
pb.SC_SOCIETY_RED_PACKET_PASSWORD.enum_types = {}
pb.SC_SOCIETY_RED_PACKET_PASSWORD.fields = {pb.SC_SOCIETY_RED_PACKET_PASSWORD_RESULT_FIELD, pb.SC_SOCIETY_RED_PACKET_PASSWORD_PASSWORD_FIELD}
pb.SC_SOCIETY_RED_PACKET_PASSWORD.is_extendable = false
pb.SC_SOCIETY_RED_PACKET_PASSWORD.extensions = {}
pb.CS_SOCIETY_RED_PACKET_LIMIT_ACTORID_FIELD.name = "ActorID"
pb.CS_SOCIETY_RED_PACKET_LIMIT_ACTORID_FIELD.full_name = ".CS_Society_Red_Packet_Limit.ActorID"
pb.CS_SOCIETY_RED_PACKET_LIMIT_ACTORID_FIELD.number = 1
pb.CS_SOCIETY_RED_PACKET_LIMIT_ACTORID_FIELD.index = 0
pb.CS_SOCIETY_RED_PACKET_LIMIT_ACTORID_FIELD.label = 1
pb.CS_SOCIETY_RED_PACKET_LIMIT_ACTORID_FIELD.has_default_value = false
pb.CS_SOCIETY_RED_PACKET_LIMIT_ACTORID_FIELD.default_value = 0
pb.CS_SOCIETY_RED_PACKET_LIMIT_ACTORID_FIELD.type = 13
pb.CS_SOCIETY_RED_PACKET_LIMIT_ACTORID_FIELD.cpp_type = 3

pb.CS_SOCIETY_RED_PACKET_LIMIT.name = "CS_Society_Red_Packet_Limit"
pb.CS_SOCIETY_RED_PACKET_LIMIT.full_name = ".CS_Society_Red_Packet_Limit"
pb.CS_SOCIETY_RED_PACKET_LIMIT.nested_types = {}
pb.CS_SOCIETY_RED_PACKET_LIMIT.enum_types = {}
pb.CS_SOCIETY_RED_PACKET_LIMIT.fields = {pb.CS_SOCIETY_RED_PACKET_LIMIT_ACTORID_FIELD}
pb.CS_SOCIETY_RED_PACKET_LIMIT.is_extendable = false
pb.CS_SOCIETY_RED_PACKET_LIMIT.extensions = {}
pb.SC_SOCIETY_RED_PACKET_LIMIT_HISTROYINFO_PASSWORD_FIELD.name = "PassWord"
pb.SC_SOCIETY_RED_PACKET_LIMIT_HISTROYINFO_PASSWORD_FIELD.full_name = ".SC_Society_Red_Packet_Limit.HistroyInfo.PassWord"
pb.SC_SOCIETY_RED_PACKET_LIMIT_HISTROYINFO_PASSWORD_FIELD.number = 1
pb.SC_SOCIETY_RED_PACKET_LIMIT_HISTROYINFO_PASSWORD_FIELD.index = 0
pb.SC_SOCIETY_RED_PACKET_LIMIT_HISTROYINFO_PASSWORD_FIELD.label = 1
pb.SC_SOCIETY_RED_PACKET_LIMIT_HISTROYINFO_PASSWORD_FIELD.has_default_value = false
pb.SC_SOCIETY_RED_PACKET_LIMIT_HISTROYINFO_PASSWORD_FIELD.default_value = 0
pb.SC_SOCIETY_RED_PACKET_LIMIT_HISTROYINFO_PASSWORD_FIELD.type = 13
pb.SC_SOCIETY_RED_PACKET_LIMIT_HISTROYINFO_PASSWORD_FIELD.cpp_type = 3

pb.SC_SOCIETY_RED_PACKET_LIMIT_HISTROYINFO.name = "HistroyInfo"
pb.SC_SOCIETY_RED_PACKET_LIMIT_HISTROYINFO.full_name = ".SC_Society_Red_Packet_Limit.HistroyInfo"
pb.SC_SOCIETY_RED_PACKET_LIMIT_HISTROYINFO.nested_types = {}
pb.SC_SOCIETY_RED_PACKET_LIMIT_HISTROYINFO.enum_types = {}
pb.SC_SOCIETY_RED_PACKET_LIMIT_HISTROYINFO.fields = {pb.SC_SOCIETY_RED_PACKET_LIMIT_HISTROYINFO_PASSWORD_FIELD}
pb.SC_SOCIETY_RED_PACKET_LIMIT_HISTROYINFO.is_extendable = false
pb.SC_SOCIETY_RED_PACKET_LIMIT_HISTROYINFO.extensions = {}
pb.SC_SOCIETY_RED_PACKET_LIMIT_HISTROYINFO.containing_type = pb.SC_SOCIETY_RED_PACKET_LIMIT
pb.SC_SOCIETY_RED_PACKET_LIMIT_RESULT_FIELD.name = "Result"
pb.SC_SOCIETY_RED_PACKET_LIMIT_RESULT_FIELD.full_name = ".SC_Society_Red_Packet_Limit.Result"
pb.SC_SOCIETY_RED_PACKET_LIMIT_RESULT_FIELD.number = 1
pb.SC_SOCIETY_RED_PACKET_LIMIT_RESULT_FIELD.index = 0
pb.SC_SOCIETY_RED_PACKET_LIMIT_RESULT_FIELD.label = 1
pb.SC_SOCIETY_RED_PACKET_LIMIT_RESULT_FIELD.has_default_value = false
pb.SC_SOCIETY_RED_PACKET_LIMIT_RESULT_FIELD.default_value = 0
pb.SC_SOCIETY_RED_PACKET_LIMIT_RESULT_FIELD.type = 5
pb.SC_SOCIETY_RED_PACKET_LIMIT_RESULT_FIELD.cpp_type = 1

pb.SC_SOCIETY_RED_PACKET_LIMIT_HISTORY_FIELD.name = "history"
pb.SC_SOCIETY_RED_PACKET_LIMIT_HISTORY_FIELD.full_name = ".SC_Society_Red_Packet_Limit.history"
pb.SC_SOCIETY_RED_PACKET_LIMIT_HISTORY_FIELD.number = 2
pb.SC_SOCIETY_RED_PACKET_LIMIT_HISTORY_FIELD.index = 1
pb.SC_SOCIETY_RED_PACKET_LIMIT_HISTORY_FIELD.label = 3
pb.SC_SOCIETY_RED_PACKET_LIMIT_HISTORY_FIELD.has_default_value = false
pb.SC_SOCIETY_RED_PACKET_LIMIT_HISTORY_FIELD.default_value = {}
pb.SC_SOCIETY_RED_PACKET_LIMIT_HISTORY_FIELD.message_type = pb.SC_SOCIETY_RED_PACKET_LIMIT_HISTROYINFO
pb.SC_SOCIETY_RED_PACKET_LIMIT_HISTORY_FIELD.type = 11
pb.SC_SOCIETY_RED_PACKET_LIMIT_HISTORY_FIELD.cpp_type = 10

pb.SC_SOCIETY_RED_PACKET_LIMIT.name = "SC_Society_Red_Packet_Limit"
pb.SC_SOCIETY_RED_PACKET_LIMIT.full_name = ".SC_Society_Red_Packet_Limit"
pb.SC_SOCIETY_RED_PACKET_LIMIT.nested_types = {pb.SC_SOCIETY_RED_PACKET_LIMIT_HISTROYINFO}
pb.SC_SOCIETY_RED_PACKET_LIMIT.enum_types = {}
pb.SC_SOCIETY_RED_PACKET_LIMIT.fields = {pb.SC_SOCIETY_RED_PACKET_LIMIT_RESULT_FIELD, pb.SC_SOCIETY_RED_PACKET_LIMIT_HISTORY_FIELD}
pb.SC_SOCIETY_RED_PACKET_LIMIT.is_extendable = false
pb.SC_SOCIETY_RED_PACKET_LIMIT.extensions = {}
pb.CS_SOCIETY_RED_PACKET_SELF_RANK_ACTORID_FIELD.name = "ActorID"
pb.CS_SOCIETY_RED_PACKET_SELF_RANK_ACTORID_FIELD.full_name = ".CS_Society_Red_Packet_Self_Rank.ActorID"
pb.CS_SOCIETY_RED_PACKET_SELF_RANK_ACTORID_FIELD.number = 1
pb.CS_SOCIETY_RED_PACKET_SELF_RANK_ACTORID_FIELD.index = 0
pb.CS_SOCIETY_RED_PACKET_SELF_RANK_ACTORID_FIELD.label = 1
pb.CS_SOCIETY_RED_PACKET_SELF_RANK_ACTORID_FIELD.has_default_value = false
pb.CS_SOCIETY_RED_PACKET_SELF_RANK_ACTORID_FIELD.default_value = 0
pb.CS_SOCIETY_RED_PACKET_SELF_RANK_ACTORID_FIELD.type = 13
pb.CS_SOCIETY_RED_PACKET_SELF_RANK_ACTORID_FIELD.cpp_type = 3

pb.CS_SOCIETY_RED_PACKET_SELF_RANK.name = "CS_Society_Red_Packet_Self_Rank"
pb.CS_SOCIETY_RED_PACKET_SELF_RANK.full_name = ".CS_Society_Red_Packet_Self_Rank"
pb.CS_SOCIETY_RED_PACKET_SELF_RANK.nested_types = {}
pb.CS_SOCIETY_RED_PACKET_SELF_RANK.enum_types = {}
pb.CS_SOCIETY_RED_PACKET_SELF_RANK.fields = {pb.CS_SOCIETY_RED_PACKET_SELF_RANK_ACTORID_FIELD}
pb.CS_SOCIETY_RED_PACKET_SELF_RANK.is_extendable = false
pb.CS_SOCIETY_RED_PACKET_SELF_RANK.extensions = {}
pb.SC_SOCIETY_RED_PACKET_SELF_RANK_RANKINFO_ACTORID_FIELD.name = "ActorID"
pb.SC_SOCIETY_RED_PACKET_SELF_RANK_RANKINFO_ACTORID_FIELD.full_name = ".SC_Society_Red_Packet_Self_Rank.RankInfo.ActorID"
pb.SC_SOCIETY_RED_PACKET_SELF_RANK_RANKINFO_ACTORID_FIELD.number = 1
pb.SC_SOCIETY_RED_PACKET_SELF_RANK_RANKINFO_ACTORID_FIELD.index = 0
pb.SC_SOCIETY_RED_PACKET_SELF_RANK_RANKINFO_ACTORID_FIELD.label = 1
pb.SC_SOCIETY_RED_PACKET_SELF_RANK_RANKINFO_ACTORID_FIELD.has_default_value = false
pb.SC_SOCIETY_RED_PACKET_SELF_RANK_RANKINFO_ACTORID_FIELD.default_value = 0
pb.SC_SOCIETY_RED_PACKET_SELF_RANK_RANKINFO_ACTORID_FIELD.type = 13
pb.SC_SOCIETY_RED_PACKET_SELF_RANK_RANKINFO_ACTORID_FIELD.cpp_type = 3

pb.SC_SOCIETY_RED_PACKET_SELF_RANK_RANKINFO_ACTORNAME_FIELD.name = "ActorName"
pb.SC_SOCIETY_RED_PACKET_SELF_RANK_RANKINFO_ACTORNAME_FIELD.full_name = ".SC_Society_Red_Packet_Self_Rank.RankInfo.ActorName"
pb.SC_SOCIETY_RED_PACKET_SELF_RANK_RANKINFO_ACTORNAME_FIELD.number = 2
pb.SC_SOCIETY_RED_PACKET_SELF_RANK_RANKINFO_ACTORNAME_FIELD.index = 1
pb.SC_SOCIETY_RED_PACKET_SELF_RANK_RANKINFO_ACTORNAME_FIELD.label = 1
pb.SC_SOCIETY_RED_PACKET_SELF_RANK_RANKINFO_ACTORNAME_FIELD.has_default_value = false
pb.SC_SOCIETY_RED_PACKET_SELF_RANK_RANKINFO_ACTORNAME_FIELD.default_value = ""
pb.SC_SOCIETY_RED_PACKET_SELF_RANK_RANKINFO_ACTORNAME_FIELD.type = 9
pb.SC_SOCIETY_RED_PACKET_SELF_RANK_RANKINFO_ACTORNAME_FIELD.cpp_type = 9

pb.SC_SOCIETY_RED_PACKET_SELF_RANK_RANKINFO_OPENCOUNT_FIELD.name = "OpenCount"
pb.SC_SOCIETY_RED_PACKET_SELF_RANK_RANKINFO_OPENCOUNT_FIELD.full_name = ".SC_Society_Red_Packet_Self_Rank.RankInfo.OpenCount"
pb.SC_SOCIETY_RED_PACKET_SELF_RANK_RANKINFO_OPENCOUNT_FIELD.number = 3
pb.SC_SOCIETY_RED_PACKET_SELF_RANK_RANKINFO_OPENCOUNT_FIELD.index = 2
pb.SC_SOCIETY_RED_PACKET_SELF_RANK_RANKINFO_OPENCOUNT_FIELD.label = 1
pb.SC_SOCIETY_RED_PACKET_SELF_RANK_RANKINFO_OPENCOUNT_FIELD.has_default_value = false
pb.SC_SOCIETY_RED_PACKET_SELF_RANK_RANKINFO_OPENCOUNT_FIELD.default_value = 0
pb.SC_SOCIETY_RED_PACKET_SELF_RANK_RANKINFO_OPENCOUNT_FIELD.type = 13
pb.SC_SOCIETY_RED_PACKET_SELF_RANK_RANKINFO_OPENCOUNT_FIELD.cpp_type = 3

pb.SC_SOCIETY_RED_PACKET_SELF_RANK_RANKINFO_SUPPEROPENCOUNT_FIELD.name = "SupperOpenCount"
pb.SC_SOCIETY_RED_PACKET_SELF_RANK_RANKINFO_SUPPEROPENCOUNT_FIELD.full_name = ".SC_Society_Red_Packet_Self_Rank.RankInfo.SupperOpenCount"
pb.SC_SOCIETY_RED_PACKET_SELF_RANK_RANKINFO_SUPPEROPENCOUNT_FIELD.number = 4
pb.SC_SOCIETY_RED_PACKET_SELF_RANK_RANKINFO_SUPPEROPENCOUNT_FIELD.index = 3
pb.SC_SOCIETY_RED_PACKET_SELF_RANK_RANKINFO_SUPPEROPENCOUNT_FIELD.label = 1
pb.SC_SOCIETY_RED_PACKET_SELF_RANK_RANKINFO_SUPPEROPENCOUNT_FIELD.has_default_value = false
pb.SC_SOCIETY_RED_PACKET_SELF_RANK_RANKINFO_SUPPEROPENCOUNT_FIELD.default_value = 0
pb.SC_SOCIETY_RED_PACKET_SELF_RANK_RANKINFO_SUPPEROPENCOUNT_FIELD.type = 13
pb.SC_SOCIETY_RED_PACKET_SELF_RANK_RANKINFO_SUPPEROPENCOUNT_FIELD.cpp_type = 3

pb.SC_SOCIETY_RED_PACKET_SELF_RANK_RANKINFO_COUNTRYID_FIELD.name = "CountryID"
pb.SC_SOCIETY_RED_PACKET_SELF_RANK_RANKINFO_COUNTRYID_FIELD.full_name = ".SC_Society_Red_Packet_Self_Rank.RankInfo.CountryID"
pb.SC_SOCIETY_RED_PACKET_SELF_RANK_RANKINFO_COUNTRYID_FIELD.number = 5
pb.SC_SOCIETY_RED_PACKET_SELF_RANK_RANKINFO_COUNTRYID_FIELD.index = 4
pb.SC_SOCIETY_RED_PACKET_SELF_RANK_RANKINFO_COUNTRYID_FIELD.label = 1
pb.SC_SOCIETY_RED_PACKET_SELF_RANK_RANKINFO_COUNTRYID_FIELD.has_default_value = false
pb.SC_SOCIETY_RED_PACKET_SELF_RANK_RANKINFO_COUNTRYID_FIELD.default_value = 0
pb.SC_SOCIETY_RED_PACKET_SELF_RANK_RANKINFO_COUNTRYID_FIELD.type = 13
pb.SC_SOCIETY_RED_PACKET_SELF_RANK_RANKINFO_COUNTRYID_FIELD.cpp_type = 3

pb.SC_SOCIETY_RED_PACKET_SELF_RANK_RANKINFO.name = "RankInfo"
pb.SC_SOCIETY_RED_PACKET_SELF_RANK_RANKINFO.full_name = ".SC_Society_Red_Packet_Self_Rank.RankInfo"
pb.SC_SOCIETY_RED_PACKET_SELF_RANK_RANKINFO.nested_types = {}
pb.SC_SOCIETY_RED_PACKET_SELF_RANK_RANKINFO.enum_types = {}
pb.SC_SOCIETY_RED_PACKET_SELF_RANK_RANKINFO.fields = {pb.SC_SOCIETY_RED_PACKET_SELF_RANK_RANKINFO_ACTORID_FIELD, pb.SC_SOCIETY_RED_PACKET_SELF_RANK_RANKINFO_ACTORNAME_FIELD, pb.SC_SOCIETY_RED_PACKET_SELF_RANK_RANKINFO_OPENCOUNT_FIELD, pb.SC_SOCIETY_RED_PACKET_SELF_RANK_RANKINFO_SUPPEROPENCOUNT_FIELD, pb.SC_SOCIETY_RED_PACKET_SELF_RANK_RANKINFO_COUNTRYID_FIELD}
pb.SC_SOCIETY_RED_PACKET_SELF_RANK_RANKINFO.is_extendable = false
pb.SC_SOCIETY_RED_PACKET_SELF_RANK_RANKINFO.extensions = {}
pb.SC_SOCIETY_RED_PACKET_SELF_RANK_RANKINFO.containing_type = pb.SC_SOCIETY_RED_PACKET_SELF_RANK
pb.SC_SOCIETY_RED_PACKET_SELF_RANK_RESULT_FIELD.name = "Result"
pb.SC_SOCIETY_RED_PACKET_SELF_RANK_RESULT_FIELD.full_name = ".SC_Society_Red_Packet_Self_Rank.Result"
pb.SC_SOCIETY_RED_PACKET_SELF_RANK_RESULT_FIELD.number = 1
pb.SC_SOCIETY_RED_PACKET_SELF_RANK_RESULT_FIELD.index = 0
pb.SC_SOCIETY_RED_PACKET_SELF_RANK_RESULT_FIELD.label = 1
pb.SC_SOCIETY_RED_PACKET_SELF_RANK_RESULT_FIELD.has_default_value = false
pb.SC_SOCIETY_RED_PACKET_SELF_RANK_RESULT_FIELD.default_value = 0
pb.SC_SOCIETY_RED_PACKET_SELF_RANK_RESULT_FIELD.type = 5
pb.SC_SOCIETY_RED_PACKET_SELF_RANK_RESULT_FIELD.cpp_type = 1

pb.SC_SOCIETY_RED_PACKET_SELF_RANK_RANKLIST_FIELD.name = "RankList"
pb.SC_SOCIETY_RED_PACKET_SELF_RANK_RANKLIST_FIELD.full_name = ".SC_Society_Red_Packet_Self_Rank.RankList"
pb.SC_SOCIETY_RED_PACKET_SELF_RANK_RANKLIST_FIELD.number = 2
pb.SC_SOCIETY_RED_PACKET_SELF_RANK_RANKLIST_FIELD.index = 1
pb.SC_SOCIETY_RED_PACKET_SELF_RANK_RANKLIST_FIELD.label = 3
pb.SC_SOCIETY_RED_PACKET_SELF_RANK_RANKLIST_FIELD.has_default_value = false
pb.SC_SOCIETY_RED_PACKET_SELF_RANK_RANKLIST_FIELD.default_value = {}
pb.SC_SOCIETY_RED_PACKET_SELF_RANK_RANKLIST_FIELD.message_type = pb.SC_SOCIETY_RED_PACKET_SELF_RANK_RANKINFO
pb.SC_SOCIETY_RED_PACKET_SELF_RANK_RANKLIST_FIELD.type = 11
pb.SC_SOCIETY_RED_PACKET_SELF_RANK_RANKLIST_FIELD.cpp_type = 10

pb.SC_SOCIETY_RED_PACKET_SELF_RANK.name = "SC_Society_Red_Packet_Self_Rank"
pb.SC_SOCIETY_RED_PACKET_SELF_RANK.full_name = ".SC_Society_Red_Packet_Self_Rank"
pb.SC_SOCIETY_RED_PACKET_SELF_RANK.nested_types = {pb.SC_SOCIETY_RED_PACKET_SELF_RANK_RANKINFO}
pb.SC_SOCIETY_RED_PACKET_SELF_RANK.enum_types = {}
pb.SC_SOCIETY_RED_PACKET_SELF_RANK.fields = {pb.SC_SOCIETY_RED_PACKET_SELF_RANK_RESULT_FIELD, pb.SC_SOCIETY_RED_PACKET_SELF_RANK_RANKLIST_FIELD}
pb.SC_SOCIETY_RED_PACKET_SELF_RANK.is_extendable = false
pb.SC_SOCIETY_RED_PACKET_SELF_RANK.extensions = {}
pb.CS_SOCIETY_RED_PACKET_OPEN_HISTORY_ACTORID_FIELD.name = "ActorID"
pb.CS_SOCIETY_RED_PACKET_OPEN_HISTORY_ACTORID_FIELD.full_name = ".CS_Society_Red_Packet_Open_History.ActorID"
pb.CS_SOCIETY_RED_PACKET_OPEN_HISTORY_ACTORID_FIELD.number = 1
pb.CS_SOCIETY_RED_PACKET_OPEN_HISTORY_ACTORID_FIELD.index = 0
pb.CS_SOCIETY_RED_PACKET_OPEN_HISTORY_ACTORID_FIELD.label = 1
pb.CS_SOCIETY_RED_PACKET_OPEN_HISTORY_ACTORID_FIELD.has_default_value = false
pb.CS_SOCIETY_RED_PACKET_OPEN_HISTORY_ACTORID_FIELD.default_value = 0
pb.CS_SOCIETY_RED_PACKET_OPEN_HISTORY_ACTORID_FIELD.type = 13
pb.CS_SOCIETY_RED_PACKET_OPEN_HISTORY_ACTORID_FIELD.cpp_type = 3

pb.CS_SOCIETY_RED_PACKET_OPEN_HISTORY.name = "CS_Society_Red_Packet_Open_History"
pb.CS_SOCIETY_RED_PACKET_OPEN_HISTORY.full_name = ".CS_Society_Red_Packet_Open_History"
pb.CS_SOCIETY_RED_PACKET_OPEN_HISTORY.nested_types = {}
pb.CS_SOCIETY_RED_PACKET_OPEN_HISTORY.enum_types = {}
pb.CS_SOCIETY_RED_PACKET_OPEN_HISTORY.fields = {pb.CS_SOCIETY_RED_PACKET_OPEN_HISTORY_ACTORID_FIELD}
pb.CS_SOCIETY_RED_PACKET_OPEN_HISTORY.is_extendable = false
pb.CS_SOCIETY_RED_PACKET_OPEN_HISTORY.extensions = {}
pb.SC_SOCIETY_RED_PACKET_OPEN_HISTORY_HISTROYINFO_PRIZES_FIELD.name = "Prizes"
pb.SC_SOCIETY_RED_PACKET_OPEN_HISTORY_HISTROYINFO_PRIZES_FIELD.full_name = ".SC_Society_Red_Packet_Open_History.HistroyInfo.Prizes"
pb.SC_SOCIETY_RED_PACKET_OPEN_HISTORY_HISTROYINFO_PRIZES_FIELD.number = 1
pb.SC_SOCIETY_RED_PACKET_OPEN_HISTORY_HISTROYINFO_PRIZES_FIELD.index = 0
pb.SC_SOCIETY_RED_PACKET_OPEN_HISTORY_HISTROYINFO_PRIZES_FIELD.label = 1
pb.SC_SOCIETY_RED_PACKET_OPEN_HISTORY_HISTROYINFO_PRIZES_FIELD.has_default_value = false
pb.SC_SOCIETY_RED_PACKET_OPEN_HISTORY_HISTROYINFO_PRIZES_FIELD.default_value = ""
pb.SC_SOCIETY_RED_PACKET_OPEN_HISTORY_HISTROYINFO_PRIZES_FIELD.type = 9
pb.SC_SOCIETY_RED_PACKET_OPEN_HISTORY_HISTROYINFO_PRIZES_FIELD.cpp_type = 9

pb.SC_SOCIETY_RED_PACKET_OPEN_HISTORY_HISTROYINFO_REDTYPE_FIELD.name = "RedType"
pb.SC_SOCIETY_RED_PACKET_OPEN_HISTORY_HISTROYINFO_REDTYPE_FIELD.full_name = ".SC_Society_Red_Packet_Open_History.HistroyInfo.RedType"
pb.SC_SOCIETY_RED_PACKET_OPEN_HISTORY_HISTROYINFO_REDTYPE_FIELD.number = 2
pb.SC_SOCIETY_RED_PACKET_OPEN_HISTORY_HISTROYINFO_REDTYPE_FIELD.index = 1
pb.SC_SOCIETY_RED_PACKET_OPEN_HISTORY_HISTROYINFO_REDTYPE_FIELD.label = 1
pb.SC_SOCIETY_RED_PACKET_OPEN_HISTORY_HISTROYINFO_REDTYPE_FIELD.has_default_value = false
pb.SC_SOCIETY_RED_PACKET_OPEN_HISTORY_HISTROYINFO_REDTYPE_FIELD.default_value = 0
pb.SC_SOCIETY_RED_PACKET_OPEN_HISTORY_HISTROYINFO_REDTYPE_FIELD.type = 13
pb.SC_SOCIETY_RED_PACKET_OPEN_HISTORY_HISTROYINFO_REDTYPE_FIELD.cpp_type = 3

pb.SC_SOCIETY_RED_PACKET_OPEN_HISTORY_HISTROYINFO.name = "HistroyInfo"
pb.SC_SOCIETY_RED_PACKET_OPEN_HISTORY_HISTROYINFO.full_name = ".SC_Society_Red_Packet_Open_History.HistroyInfo"
pb.SC_SOCIETY_RED_PACKET_OPEN_HISTORY_HISTROYINFO.nested_types = {}
pb.SC_SOCIETY_RED_PACKET_OPEN_HISTORY_HISTROYINFO.enum_types = {}
pb.SC_SOCIETY_RED_PACKET_OPEN_HISTORY_HISTROYINFO.fields = {pb.SC_SOCIETY_RED_PACKET_OPEN_HISTORY_HISTROYINFO_PRIZES_FIELD, pb.SC_SOCIETY_RED_PACKET_OPEN_HISTORY_HISTROYINFO_REDTYPE_FIELD}
pb.SC_SOCIETY_RED_PACKET_OPEN_HISTORY_HISTROYINFO.is_extendable = false
pb.SC_SOCIETY_RED_PACKET_OPEN_HISTORY_HISTROYINFO.extensions = {}
pb.SC_SOCIETY_RED_PACKET_OPEN_HISTORY_HISTROYINFO.containing_type = pb.SC_SOCIETY_RED_PACKET_OPEN_HISTORY
pb.SC_SOCIETY_RED_PACKET_OPEN_HISTORY_RESULT_FIELD.name = "Result"
pb.SC_SOCIETY_RED_PACKET_OPEN_HISTORY_RESULT_FIELD.full_name = ".SC_Society_Red_Packet_Open_History.Result"
pb.SC_SOCIETY_RED_PACKET_OPEN_HISTORY_RESULT_FIELD.number = 1
pb.SC_SOCIETY_RED_PACKET_OPEN_HISTORY_RESULT_FIELD.index = 0
pb.SC_SOCIETY_RED_PACKET_OPEN_HISTORY_RESULT_FIELD.label = 1
pb.SC_SOCIETY_RED_PACKET_OPEN_HISTORY_RESULT_FIELD.has_default_value = false
pb.SC_SOCIETY_RED_PACKET_OPEN_HISTORY_RESULT_FIELD.default_value = 0
pb.SC_SOCIETY_RED_PACKET_OPEN_HISTORY_RESULT_FIELD.type = 5
pb.SC_SOCIETY_RED_PACKET_OPEN_HISTORY_RESULT_FIELD.cpp_type = 1

pb.SC_SOCIETY_RED_PACKET_OPEN_HISTORY_HISTORYLIST_FIELD.name = "HistoryList"
pb.SC_SOCIETY_RED_PACKET_OPEN_HISTORY_HISTORYLIST_FIELD.full_name = ".SC_Society_Red_Packet_Open_History.HistoryList"
pb.SC_SOCIETY_RED_PACKET_OPEN_HISTORY_HISTORYLIST_FIELD.number = 2
pb.SC_SOCIETY_RED_PACKET_OPEN_HISTORY_HISTORYLIST_FIELD.index = 1
pb.SC_SOCIETY_RED_PACKET_OPEN_HISTORY_HISTORYLIST_FIELD.label = 3
pb.SC_SOCIETY_RED_PACKET_OPEN_HISTORY_HISTORYLIST_FIELD.has_default_value = false
pb.SC_SOCIETY_RED_PACKET_OPEN_HISTORY_HISTORYLIST_FIELD.default_value = {}
pb.SC_SOCIETY_RED_PACKET_OPEN_HISTORY_HISTORYLIST_FIELD.message_type = pb.SC_SOCIETY_RED_PACKET_OPEN_HISTORY_HISTROYINFO
pb.SC_SOCIETY_RED_PACKET_OPEN_HISTORY_HISTORYLIST_FIELD.type = 11
pb.SC_SOCIETY_RED_PACKET_OPEN_HISTORY_HISTORYLIST_FIELD.cpp_type = 10

pb.SC_SOCIETY_RED_PACKET_OPEN_HISTORY.name = "SC_Society_Red_Packet_Open_History"
pb.SC_SOCIETY_RED_PACKET_OPEN_HISTORY.full_name = ".SC_Society_Red_Packet_Open_History"
pb.SC_SOCIETY_RED_PACKET_OPEN_HISTORY.nested_types = {pb.SC_SOCIETY_RED_PACKET_OPEN_HISTORY_HISTROYINFO}
pb.SC_SOCIETY_RED_PACKET_OPEN_HISTORY.enum_types = {}
pb.SC_SOCIETY_RED_PACKET_OPEN_HISTORY.fields = {pb.SC_SOCIETY_RED_PACKET_OPEN_HISTORY_RESULT_FIELD, pb.SC_SOCIETY_RED_PACKET_OPEN_HISTORY_HISTORYLIST_FIELD}
pb.SC_SOCIETY_RED_PACKET_OPEN_HISTORY.is_extendable = false
pb.SC_SOCIETY_RED_PACKET_OPEN_HISTORY.extensions = {}
pb.CS_SOCIETY_RED_PACKET_SUPPER_ACTORID_FIELD.name = "ActorID"
pb.CS_SOCIETY_RED_PACKET_SUPPER_ACTORID_FIELD.full_name = ".CS_Society_Red_Packet_Supper.ActorID"
pb.CS_SOCIETY_RED_PACKET_SUPPER_ACTORID_FIELD.number = 1
pb.CS_SOCIETY_RED_PACKET_SUPPER_ACTORID_FIELD.index = 0
pb.CS_SOCIETY_RED_PACKET_SUPPER_ACTORID_FIELD.label = 1
pb.CS_SOCIETY_RED_PACKET_SUPPER_ACTORID_FIELD.has_default_value = false
pb.CS_SOCIETY_RED_PACKET_SUPPER_ACTORID_FIELD.default_value = 0
pb.CS_SOCIETY_RED_PACKET_SUPPER_ACTORID_FIELD.type = 13
pb.CS_SOCIETY_RED_PACKET_SUPPER_ACTORID_FIELD.cpp_type = 3

pb.CS_SOCIETY_RED_PACKET_SUPPER.name = "CS_Society_Red_Packet_Supper"
pb.CS_SOCIETY_RED_PACKET_SUPPER.full_name = ".CS_Society_Red_Packet_Supper"
pb.CS_SOCIETY_RED_PACKET_SUPPER.nested_types = {}
pb.CS_SOCIETY_RED_PACKET_SUPPER.enum_types = {}
pb.CS_SOCIETY_RED_PACKET_SUPPER.fields = {pb.CS_SOCIETY_RED_PACKET_SUPPER_ACTORID_FIELD}
pb.CS_SOCIETY_RED_PACKET_SUPPER.is_extendable = false
pb.CS_SOCIETY_RED_PACKET_SUPPER.extensions = {}
pb.SC_SOCIETY_RED_PACKET_SUPPER_RESULT_FIELD.name = "Result"
pb.SC_SOCIETY_RED_PACKET_SUPPER_RESULT_FIELD.full_name = ".SC_Society_Red_Packet_Supper.Result"
pb.SC_SOCIETY_RED_PACKET_SUPPER_RESULT_FIELD.number = 1
pb.SC_SOCIETY_RED_PACKET_SUPPER_RESULT_FIELD.index = 0
pb.SC_SOCIETY_RED_PACKET_SUPPER_RESULT_FIELD.label = 1
pb.SC_SOCIETY_RED_PACKET_SUPPER_RESULT_FIELD.has_default_value = false
pb.SC_SOCIETY_RED_PACKET_SUPPER_RESULT_FIELD.default_value = 0
pb.SC_SOCIETY_RED_PACKET_SUPPER_RESULT_FIELD.type = 5
pb.SC_SOCIETY_RED_PACKET_SUPPER_RESULT_FIELD.cpp_type = 1

pb.SC_SOCIETY_RED_PACKET_SUPPER_COUNTRYID_FIELD.name = "CountryID"
pb.SC_SOCIETY_RED_PACKET_SUPPER_COUNTRYID_FIELD.full_name = ".SC_Society_Red_Packet_Supper.CountryID"
pb.SC_SOCIETY_RED_PACKET_SUPPER_COUNTRYID_FIELD.number = 2
pb.SC_SOCIETY_RED_PACKET_SUPPER_COUNTRYID_FIELD.index = 1
pb.SC_SOCIETY_RED_PACKET_SUPPER_COUNTRYID_FIELD.label = 1
pb.SC_SOCIETY_RED_PACKET_SUPPER_COUNTRYID_FIELD.has_default_value = false
pb.SC_SOCIETY_RED_PACKET_SUPPER_COUNTRYID_FIELD.default_value = 0
pb.SC_SOCIETY_RED_PACKET_SUPPER_COUNTRYID_FIELD.type = 13
pb.SC_SOCIETY_RED_PACKET_SUPPER_COUNTRYID_FIELD.cpp_type = 3

pb.SC_SOCIETY_RED_PACKET_SUPPER_SOCIETYNAME_FIELD.name = "SocietyName"
pb.SC_SOCIETY_RED_PACKET_SUPPER_SOCIETYNAME_FIELD.full_name = ".SC_Society_Red_Packet_Supper.SocietyName"
pb.SC_SOCIETY_RED_PACKET_SUPPER_SOCIETYNAME_FIELD.number = 3
pb.SC_SOCIETY_RED_PACKET_SUPPER_SOCIETYNAME_FIELD.index = 2
pb.SC_SOCIETY_RED_PACKET_SUPPER_SOCIETYNAME_FIELD.label = 1
pb.SC_SOCIETY_RED_PACKET_SUPPER_SOCIETYNAME_FIELD.has_default_value = false
pb.SC_SOCIETY_RED_PACKET_SUPPER_SOCIETYNAME_FIELD.default_value = ""
pb.SC_SOCIETY_RED_PACKET_SUPPER_SOCIETYNAME_FIELD.type = 9
pb.SC_SOCIETY_RED_PACKET_SUPPER_SOCIETYNAME_FIELD.cpp_type = 9

pb.SC_SOCIETY_RED_PACKET_SUPPER_ACTORNAME_FIELD.name = "ActorName"
pb.SC_SOCIETY_RED_PACKET_SUPPER_ACTORNAME_FIELD.full_name = ".SC_Society_Red_Packet_Supper.ActorName"
pb.SC_SOCIETY_RED_PACKET_SUPPER_ACTORNAME_FIELD.number = 4
pb.SC_SOCIETY_RED_PACKET_SUPPER_ACTORNAME_FIELD.index = 3
pb.SC_SOCIETY_RED_PACKET_SUPPER_ACTORNAME_FIELD.label = 1
pb.SC_SOCIETY_RED_PACKET_SUPPER_ACTORNAME_FIELD.has_default_value = false
pb.SC_SOCIETY_RED_PACKET_SUPPER_ACTORNAME_FIELD.default_value = ""
pb.SC_SOCIETY_RED_PACKET_SUPPER_ACTORNAME_FIELD.type = 9
pb.SC_SOCIETY_RED_PACKET_SUPPER_ACTORNAME_FIELD.cpp_type = 9

pb.SC_SOCIETY_RED_PACKET_SUPPER_PRIZES_FIELD.name = "Prizes"
pb.SC_SOCIETY_RED_PACKET_SUPPER_PRIZES_FIELD.full_name = ".SC_Society_Red_Packet_Supper.Prizes"
pb.SC_SOCIETY_RED_PACKET_SUPPER_PRIZES_FIELD.number = 5
pb.SC_SOCIETY_RED_PACKET_SUPPER_PRIZES_FIELD.index = 4
pb.SC_SOCIETY_RED_PACKET_SUPPER_PRIZES_FIELD.label = 1
pb.SC_SOCIETY_RED_PACKET_SUPPER_PRIZES_FIELD.has_default_value = false
pb.SC_SOCIETY_RED_PACKET_SUPPER_PRIZES_FIELD.default_value = ""
pb.SC_SOCIETY_RED_PACKET_SUPPER_PRIZES_FIELD.type = 9
pb.SC_SOCIETY_RED_PACKET_SUPPER_PRIZES_FIELD.cpp_type = 9

pb.SC_SOCIETY_RED_PACKET_SUPPER.name = "SC_Society_Red_Packet_Supper"
pb.SC_SOCIETY_RED_PACKET_SUPPER.full_name = ".SC_Society_Red_Packet_Supper"
pb.SC_SOCIETY_RED_PACKET_SUPPER.nested_types = {}
pb.SC_SOCIETY_RED_PACKET_SUPPER.enum_types = {}
pb.SC_SOCIETY_RED_PACKET_SUPPER.fields = {pb.SC_SOCIETY_RED_PACKET_SUPPER_RESULT_FIELD, pb.SC_SOCIETY_RED_PACKET_SUPPER_COUNTRYID_FIELD, pb.SC_SOCIETY_RED_PACKET_SUPPER_SOCIETYNAME_FIELD, pb.SC_SOCIETY_RED_PACKET_SUPPER_ACTORNAME_FIELD, pb.SC_SOCIETY_RED_PACKET_SUPPER_PRIZES_FIELD}
pb.SC_SOCIETY_RED_PACKET_SUPPER.is_extendable = false
pb.SC_SOCIETY_RED_PACKET_SUPPER.extensions = {}
pb.SC_SOCIETY_RED_PACKET_PRIZE_PRIZEINFO_GOODSID_FIELD.name = "GoodsID"
pb.SC_SOCIETY_RED_PACKET_PRIZE_PRIZEINFO_GOODSID_FIELD.full_name = ".SC_Society_Red_Packet_Prize.PrizeInfo.GoodsID"
pb.SC_SOCIETY_RED_PACKET_PRIZE_PRIZEINFO_GOODSID_FIELD.number = 1
pb.SC_SOCIETY_RED_PACKET_PRIZE_PRIZEINFO_GOODSID_FIELD.index = 0
pb.SC_SOCIETY_RED_PACKET_PRIZE_PRIZEINFO_GOODSID_FIELD.label = 1
pb.SC_SOCIETY_RED_PACKET_PRIZE_PRIZEINFO_GOODSID_FIELD.has_default_value = false
pb.SC_SOCIETY_RED_PACKET_PRIZE_PRIZEINFO_GOODSID_FIELD.default_value = 0
pb.SC_SOCIETY_RED_PACKET_PRIZE_PRIZEINFO_GOODSID_FIELD.type = 13
pb.SC_SOCIETY_RED_PACKET_PRIZE_PRIZEINFO_GOODSID_FIELD.cpp_type = 3

pb.SC_SOCIETY_RED_PACKET_PRIZE_PRIZEINFO_COUNT_FIELD.name = "Count"
pb.SC_SOCIETY_RED_PACKET_PRIZE_PRIZEINFO_COUNT_FIELD.full_name = ".SC_Society_Red_Packet_Prize.PrizeInfo.Count"
pb.SC_SOCIETY_RED_PACKET_PRIZE_PRIZEINFO_COUNT_FIELD.number = 2
pb.SC_SOCIETY_RED_PACKET_PRIZE_PRIZEINFO_COUNT_FIELD.index = 1
pb.SC_SOCIETY_RED_PACKET_PRIZE_PRIZEINFO_COUNT_FIELD.label = 1
pb.SC_SOCIETY_RED_PACKET_PRIZE_PRIZEINFO_COUNT_FIELD.has_default_value = false
pb.SC_SOCIETY_RED_PACKET_PRIZE_PRIZEINFO_COUNT_FIELD.default_value = 0
pb.SC_SOCIETY_RED_PACKET_PRIZE_PRIZEINFO_COUNT_FIELD.type = 13
pb.SC_SOCIETY_RED_PACKET_PRIZE_PRIZEINFO_COUNT_FIELD.cpp_type = 3

pb.SC_SOCIETY_RED_PACKET_PRIZE_PRIZEINFO.name = "PrizeInfo"
pb.SC_SOCIETY_RED_PACKET_PRIZE_PRIZEINFO.full_name = ".SC_Society_Red_Packet_Prize.PrizeInfo"
pb.SC_SOCIETY_RED_PACKET_PRIZE_PRIZEINFO.nested_types = {}
pb.SC_SOCIETY_RED_PACKET_PRIZE_PRIZEINFO.enum_types = {}
pb.SC_SOCIETY_RED_PACKET_PRIZE_PRIZEINFO.fields = {pb.SC_SOCIETY_RED_PACKET_PRIZE_PRIZEINFO_GOODSID_FIELD, pb.SC_SOCIETY_RED_PACKET_PRIZE_PRIZEINFO_COUNT_FIELD}
pb.SC_SOCIETY_RED_PACKET_PRIZE_PRIZEINFO.is_extendable = false
pb.SC_SOCIETY_RED_PACKET_PRIZE_PRIZEINFO.extensions = {}
pb.SC_SOCIETY_RED_PACKET_PRIZE_PRIZEINFO.containing_type = pb.SC_SOCIETY_RED_PACKET_PRIZE
pb.SC_SOCIETY_RED_PACKET_PRIZE_TOTALOPENCOUNT_FIELD.name = "TotalOpenCount"
pb.SC_SOCIETY_RED_PACKET_PRIZE_TOTALOPENCOUNT_FIELD.full_name = ".SC_Society_Red_Packet_Prize.TotalOpenCount"
pb.SC_SOCIETY_RED_PACKET_PRIZE_TOTALOPENCOUNT_FIELD.number = 1
pb.SC_SOCIETY_RED_PACKET_PRIZE_TOTALOPENCOUNT_FIELD.index = 0
pb.SC_SOCIETY_RED_PACKET_PRIZE_TOTALOPENCOUNT_FIELD.label = 1
pb.SC_SOCIETY_RED_PACKET_PRIZE_TOTALOPENCOUNT_FIELD.has_default_value = false
pb.SC_SOCIETY_RED_PACKET_PRIZE_TOTALOPENCOUNT_FIELD.default_value = 0
pb.SC_SOCIETY_RED_PACKET_PRIZE_TOTALOPENCOUNT_FIELD.type = 13
pb.SC_SOCIETY_RED_PACKET_PRIZE_TOTALOPENCOUNT_FIELD.cpp_type = 3

pb.SC_SOCIETY_RED_PACKET_PRIZE_TOTALSUPPEROPENCOUNT_FIELD.name = "TotalSupperOpenCount"
pb.SC_SOCIETY_RED_PACKET_PRIZE_TOTALSUPPEROPENCOUNT_FIELD.full_name = ".SC_Society_Red_Packet_Prize.TotalSupperOpenCount"
pb.SC_SOCIETY_RED_PACKET_PRIZE_TOTALSUPPEROPENCOUNT_FIELD.number = 2
pb.SC_SOCIETY_RED_PACKET_PRIZE_TOTALSUPPEROPENCOUNT_FIELD.index = 1
pb.SC_SOCIETY_RED_PACKET_PRIZE_TOTALSUPPEROPENCOUNT_FIELD.label = 1
pb.SC_SOCIETY_RED_PACKET_PRIZE_TOTALSUPPEROPENCOUNT_FIELD.has_default_value = false
pb.SC_SOCIETY_RED_PACKET_PRIZE_TOTALSUPPEROPENCOUNT_FIELD.default_value = 0
pb.SC_SOCIETY_RED_PACKET_PRIZE_TOTALSUPPEROPENCOUNT_FIELD.type = 13
pb.SC_SOCIETY_RED_PACKET_PRIZE_TOTALSUPPEROPENCOUNT_FIELD.cpp_type = 3

pb.SC_SOCIETY_RED_PACKET_PRIZE_INFO_FIELD.name = "info"
pb.SC_SOCIETY_RED_PACKET_PRIZE_INFO_FIELD.full_name = ".SC_Society_Red_Packet_Prize.info"
pb.SC_SOCIETY_RED_PACKET_PRIZE_INFO_FIELD.number = 3
pb.SC_SOCIETY_RED_PACKET_PRIZE_INFO_FIELD.index = 2
pb.SC_SOCIETY_RED_PACKET_PRIZE_INFO_FIELD.label = 3
pb.SC_SOCIETY_RED_PACKET_PRIZE_INFO_FIELD.has_default_value = false
pb.SC_SOCIETY_RED_PACKET_PRIZE_INFO_FIELD.default_value = {}
pb.SC_SOCIETY_RED_PACKET_PRIZE_INFO_FIELD.message_type = pb.SC_SOCIETY_RED_PACKET_PRIZE_PRIZEINFO
pb.SC_SOCIETY_RED_PACKET_PRIZE_INFO_FIELD.type = 11
pb.SC_SOCIETY_RED_PACKET_PRIZE_INFO_FIELD.cpp_type = 10

pb.SC_SOCIETY_RED_PACKET_PRIZE.name = "SC_Society_Red_Packet_Prize"
pb.SC_SOCIETY_RED_PACKET_PRIZE.full_name = ".SC_Society_Red_Packet_Prize"
pb.SC_SOCIETY_RED_PACKET_PRIZE.nested_types = {pb.SC_SOCIETY_RED_PACKET_PRIZE_PRIZEINFO}
pb.SC_SOCIETY_RED_PACKET_PRIZE.enum_types = {}
pb.SC_SOCIETY_RED_PACKET_PRIZE.fields = {pb.SC_SOCIETY_RED_PACKET_PRIZE_TOTALOPENCOUNT_FIELD, pb.SC_SOCIETY_RED_PACKET_PRIZE_TOTALSUPPEROPENCOUNT_FIELD, pb.SC_SOCIETY_RED_PACKET_PRIZE_INFO_FIELD}
pb.SC_SOCIETY_RED_PACKET_PRIZE.is_extendable = false
pb.SC_SOCIETY_RED_PACKET_PRIZE.extensions = {}
pb.CS_SOCIETY_RED_PACKET_TRANSFER_ACTORID_FIELD.name = "ActorID"
pb.CS_SOCIETY_RED_PACKET_TRANSFER_ACTORID_FIELD.full_name = ".CS_Society_Red_Packet_Transfer.ActorID"
pb.CS_SOCIETY_RED_PACKET_TRANSFER_ACTORID_FIELD.number = 1
pb.CS_SOCIETY_RED_PACKET_TRANSFER_ACTORID_FIELD.index = 0
pb.CS_SOCIETY_RED_PACKET_TRANSFER_ACTORID_FIELD.label = 1
pb.CS_SOCIETY_RED_PACKET_TRANSFER_ACTORID_FIELD.has_default_value = false
pb.CS_SOCIETY_RED_PACKET_TRANSFER_ACTORID_FIELD.default_value = 0
pb.CS_SOCIETY_RED_PACKET_TRANSFER_ACTORID_FIELD.type = 13
pb.CS_SOCIETY_RED_PACKET_TRANSFER_ACTORID_FIELD.cpp_type = 3

pb.CS_SOCIETY_RED_PACKET_TRANSFER_PASSWORD_FIELD.name = "PassWord"
pb.CS_SOCIETY_RED_PACKET_TRANSFER_PASSWORD_FIELD.full_name = ".CS_Society_Red_Packet_Transfer.PassWord"
pb.CS_SOCIETY_RED_PACKET_TRANSFER_PASSWORD_FIELD.number = 2
pb.CS_SOCIETY_RED_PACKET_TRANSFER_PASSWORD_FIELD.index = 1
pb.CS_SOCIETY_RED_PACKET_TRANSFER_PASSWORD_FIELD.label = 1
pb.CS_SOCIETY_RED_PACKET_TRANSFER_PASSWORD_FIELD.has_default_value = false
pb.CS_SOCIETY_RED_PACKET_TRANSFER_PASSWORD_FIELD.default_value = 0
pb.CS_SOCIETY_RED_PACKET_TRANSFER_PASSWORD_FIELD.type = 13
pb.CS_SOCIETY_RED_PACKET_TRANSFER_PASSWORD_FIELD.cpp_type = 3

pb.CS_SOCIETY_RED_PACKET_TRANSFER_TRANSFERTYPE_FIELD.name = "TransferType"
pb.CS_SOCIETY_RED_PACKET_TRANSFER_TRANSFERTYPE_FIELD.full_name = ".CS_Society_Red_Packet_Transfer.TransferType"
pb.CS_SOCIETY_RED_PACKET_TRANSFER_TRANSFERTYPE_FIELD.number = 3
pb.CS_SOCIETY_RED_PACKET_TRANSFER_TRANSFERTYPE_FIELD.index = 2
pb.CS_SOCIETY_RED_PACKET_TRANSFER_TRANSFERTYPE_FIELD.label = 1
pb.CS_SOCIETY_RED_PACKET_TRANSFER_TRANSFERTYPE_FIELD.has_default_value = false
pb.CS_SOCIETY_RED_PACKET_TRANSFER_TRANSFERTYPE_FIELD.default_value = 0
pb.CS_SOCIETY_RED_PACKET_TRANSFER_TRANSFERTYPE_FIELD.type = 13
pb.CS_SOCIETY_RED_PACKET_TRANSFER_TRANSFERTYPE_FIELD.cpp_type = 3

pb.CS_SOCIETY_RED_PACKET_TRANSFER.name = "CS_Society_Red_Packet_Transfer"
pb.CS_SOCIETY_RED_PACKET_TRANSFER.full_name = ".CS_Society_Red_Packet_Transfer"
pb.CS_SOCIETY_RED_PACKET_TRANSFER.nested_types = {}
pb.CS_SOCIETY_RED_PACKET_TRANSFER.enum_types = {}
pb.CS_SOCIETY_RED_PACKET_TRANSFER.fields = {pb.CS_SOCIETY_RED_PACKET_TRANSFER_ACTORID_FIELD, pb.CS_SOCIETY_RED_PACKET_TRANSFER_PASSWORD_FIELD, pb.CS_SOCIETY_RED_PACKET_TRANSFER_TRANSFERTYPE_FIELD}
pb.CS_SOCIETY_RED_PACKET_TRANSFER.is_extendable = false
pb.CS_SOCIETY_RED_PACKET_TRANSFER.extensions = {}
pb.SC_SOCIETY_RED_PACKET_TRANSFER_HISTROYINFO_ACTORNAME_FIELD.name = "ActorName"
pb.SC_SOCIETY_RED_PACKET_TRANSFER_HISTROYINFO_ACTORNAME_FIELD.full_name = ".SC_Society_Red_Packet_Transfer.HistroyInfo.ActorName"
pb.SC_SOCIETY_RED_PACKET_TRANSFER_HISTROYINFO_ACTORNAME_FIELD.number = 1
pb.SC_SOCIETY_RED_PACKET_TRANSFER_HISTROYINFO_ACTORNAME_FIELD.index = 0
pb.SC_SOCIETY_RED_PACKET_TRANSFER_HISTROYINFO_ACTORNAME_FIELD.label = 1
pb.SC_SOCIETY_RED_PACKET_TRANSFER_HISTROYINFO_ACTORNAME_FIELD.has_default_value = false
pb.SC_SOCIETY_RED_PACKET_TRANSFER_HISTROYINFO_ACTORNAME_FIELD.default_value = ""
pb.SC_SOCIETY_RED_PACKET_TRANSFER_HISTROYINFO_ACTORNAME_FIELD.type = 9
pb.SC_SOCIETY_RED_PACKET_TRANSFER_HISTROYINFO_ACTORNAME_FIELD.cpp_type = 9

pb.SC_SOCIETY_RED_PACKET_TRANSFER_HISTROYINFO_PASSWORD_FIELD.name = "PassWord"
pb.SC_SOCIETY_RED_PACKET_TRANSFER_HISTROYINFO_PASSWORD_FIELD.full_name = ".SC_Society_Red_Packet_Transfer.HistroyInfo.PassWord"
pb.SC_SOCIETY_RED_PACKET_TRANSFER_HISTROYINFO_PASSWORD_FIELD.number = 2
pb.SC_SOCIETY_RED_PACKET_TRANSFER_HISTROYINFO_PASSWORD_FIELD.index = 1
pb.SC_SOCIETY_RED_PACKET_TRANSFER_HISTROYINFO_PASSWORD_FIELD.label = 1
pb.SC_SOCIETY_RED_PACKET_TRANSFER_HISTROYINFO_PASSWORD_FIELD.has_default_value = false
pb.SC_SOCIETY_RED_PACKET_TRANSFER_HISTROYINFO_PASSWORD_FIELD.default_value = 0
pb.SC_SOCIETY_RED_PACKET_TRANSFER_HISTROYINFO_PASSWORD_FIELD.type = 13
pb.SC_SOCIETY_RED_PACKET_TRANSFER_HISTROYINFO_PASSWORD_FIELD.cpp_type = 3

pb.SC_SOCIETY_RED_PACKET_TRANSFER_HISTROYINFO.name = "HistroyInfo"
pb.SC_SOCIETY_RED_PACKET_TRANSFER_HISTROYINFO.full_name = ".SC_Society_Red_Packet_Transfer.HistroyInfo"
pb.SC_SOCIETY_RED_PACKET_TRANSFER_HISTROYINFO.nested_types = {}
pb.SC_SOCIETY_RED_PACKET_TRANSFER_HISTROYINFO.enum_types = {}
pb.SC_SOCIETY_RED_PACKET_TRANSFER_HISTROYINFO.fields = {pb.SC_SOCIETY_RED_PACKET_TRANSFER_HISTROYINFO_ACTORNAME_FIELD, pb.SC_SOCIETY_RED_PACKET_TRANSFER_HISTROYINFO_PASSWORD_FIELD}
pb.SC_SOCIETY_RED_PACKET_TRANSFER_HISTROYINFO.is_extendable = false
pb.SC_SOCIETY_RED_PACKET_TRANSFER_HISTROYINFO.extensions = {}
pb.SC_SOCIETY_RED_PACKET_TRANSFER_HISTROYINFO.containing_type = pb.SC_SOCIETY_RED_PACKET_TRANSFER
pb.SC_SOCIETY_RED_PACKET_TRANSFER_RESULT_FIELD.name = "Result"
pb.SC_SOCIETY_RED_PACKET_TRANSFER_RESULT_FIELD.full_name = ".SC_Society_Red_Packet_Transfer.Result"
pb.SC_SOCIETY_RED_PACKET_TRANSFER_RESULT_FIELD.number = 1
pb.SC_SOCIETY_RED_PACKET_TRANSFER_RESULT_FIELD.index = 0
pb.SC_SOCIETY_RED_PACKET_TRANSFER_RESULT_FIELD.label = 1
pb.SC_SOCIETY_RED_PACKET_TRANSFER_RESULT_FIELD.has_default_value = false
pb.SC_SOCIETY_RED_PACKET_TRANSFER_RESULT_FIELD.default_value = 0
pb.SC_SOCIETY_RED_PACKET_TRANSFER_RESULT_FIELD.type = 5
pb.SC_SOCIETY_RED_PACKET_TRANSFER_RESULT_FIELD.cpp_type = 1

pb.SC_SOCIETY_RED_PACKET_TRANSFER_HISTORY_FIELD.name = "history"
pb.SC_SOCIETY_RED_PACKET_TRANSFER_HISTORY_FIELD.full_name = ".SC_Society_Red_Packet_Transfer.history"
pb.SC_SOCIETY_RED_PACKET_TRANSFER_HISTORY_FIELD.number = 2
pb.SC_SOCIETY_RED_PACKET_TRANSFER_HISTORY_FIELD.index = 1
pb.SC_SOCIETY_RED_PACKET_TRANSFER_HISTORY_FIELD.label = 3
pb.SC_SOCIETY_RED_PACKET_TRANSFER_HISTORY_FIELD.has_default_value = false
pb.SC_SOCIETY_RED_PACKET_TRANSFER_HISTORY_FIELD.default_value = {}
pb.SC_SOCIETY_RED_PACKET_TRANSFER_HISTORY_FIELD.message_type = pb.SC_SOCIETY_RED_PACKET_TRANSFER_HISTROYINFO
pb.SC_SOCIETY_RED_PACKET_TRANSFER_HISTORY_FIELD.type = 11
pb.SC_SOCIETY_RED_PACKET_TRANSFER_HISTORY_FIELD.cpp_type = 10

pb.SC_SOCIETY_RED_PACKET_TRANSFER.name = "SC_Society_Red_Packet_Transfer"
pb.SC_SOCIETY_RED_PACKET_TRANSFER.full_name = ".SC_Society_Red_Packet_Transfer"
pb.SC_SOCIETY_RED_PACKET_TRANSFER.nested_types = {pb.SC_SOCIETY_RED_PACKET_TRANSFER_HISTROYINFO}
pb.SC_SOCIETY_RED_PACKET_TRANSFER.enum_types = {}
pb.SC_SOCIETY_RED_PACKET_TRANSFER.fields = {pb.SC_SOCIETY_RED_PACKET_TRANSFER_RESULT_FIELD, pb.SC_SOCIETY_RED_PACKET_TRANSFER_HISTORY_FIELD}
pb.SC_SOCIETY_RED_PACKET_TRANSFER.is_extendable = false
pb.SC_SOCIETY_RED_PACKET_TRANSFER.extensions = {}
pb.SC_SOCIETY_RED_PACKET_OPENTOTAL_PROCESS_OPENCOUNT_FIELD.name = "OpenCount"
pb.SC_SOCIETY_RED_PACKET_OPENTOTAL_PROCESS_OPENCOUNT_FIELD.full_name = ".SC_Society_Red_Packet_OpenTotal_Process.OpenCount"
pb.SC_SOCIETY_RED_PACKET_OPENTOTAL_PROCESS_OPENCOUNT_FIELD.number = 1
pb.SC_SOCIETY_RED_PACKET_OPENTOTAL_PROCESS_OPENCOUNT_FIELD.index = 0
pb.SC_SOCIETY_RED_PACKET_OPENTOTAL_PROCESS_OPENCOUNT_FIELD.label = 1
pb.SC_SOCIETY_RED_PACKET_OPENTOTAL_PROCESS_OPENCOUNT_FIELD.has_default_value = false
pb.SC_SOCIETY_RED_PACKET_OPENTOTAL_PROCESS_OPENCOUNT_FIELD.default_value = 0
pb.SC_SOCIETY_RED_PACKET_OPENTOTAL_PROCESS_OPENCOUNT_FIELD.type = 13
pb.SC_SOCIETY_RED_PACKET_OPENTOTAL_PROCESS_OPENCOUNT_FIELD.cpp_type = 3

pb.SC_SOCIETY_RED_PACKET_OPENTOTAL_PROCESS_NEEDCOUNT_FIELD.name = "NeedCount"
pb.SC_SOCIETY_RED_PACKET_OPENTOTAL_PROCESS_NEEDCOUNT_FIELD.full_name = ".SC_Society_Red_Packet_OpenTotal_Process.NeedCount"
pb.SC_SOCIETY_RED_PACKET_OPENTOTAL_PROCESS_NEEDCOUNT_FIELD.number = 2
pb.SC_SOCIETY_RED_PACKET_OPENTOTAL_PROCESS_NEEDCOUNT_FIELD.index = 1
pb.SC_SOCIETY_RED_PACKET_OPENTOTAL_PROCESS_NEEDCOUNT_FIELD.label = 1
pb.SC_SOCIETY_RED_PACKET_OPENTOTAL_PROCESS_NEEDCOUNT_FIELD.has_default_value = false
pb.SC_SOCIETY_RED_PACKET_OPENTOTAL_PROCESS_NEEDCOUNT_FIELD.default_value = 0
pb.SC_SOCIETY_RED_PACKET_OPENTOTAL_PROCESS_NEEDCOUNT_FIELD.type = 13
pb.SC_SOCIETY_RED_PACKET_OPENTOTAL_PROCESS_NEEDCOUNT_FIELD.cpp_type = 3

pb.SC_SOCIETY_RED_PACKET_OPENTOTAL_PROCESS_LIMITCOUNT_FIELD.name = "LimitCount"
pb.SC_SOCIETY_RED_PACKET_OPENTOTAL_PROCESS_LIMITCOUNT_FIELD.full_name = ".SC_Society_Red_Packet_OpenTotal_Process.LimitCount"
pb.SC_SOCIETY_RED_PACKET_OPENTOTAL_PROCESS_LIMITCOUNT_FIELD.number = 3
pb.SC_SOCIETY_RED_PACKET_OPENTOTAL_PROCESS_LIMITCOUNT_FIELD.index = 2
pb.SC_SOCIETY_RED_PACKET_OPENTOTAL_PROCESS_LIMITCOUNT_FIELD.label = 1
pb.SC_SOCIETY_RED_PACKET_OPENTOTAL_PROCESS_LIMITCOUNT_FIELD.has_default_value = false
pb.SC_SOCIETY_RED_PACKET_OPENTOTAL_PROCESS_LIMITCOUNT_FIELD.default_value = 0
pb.SC_SOCIETY_RED_PACKET_OPENTOTAL_PROCESS_LIMITCOUNT_FIELD.type = 13
pb.SC_SOCIETY_RED_PACKET_OPENTOTAL_PROCESS_LIMITCOUNT_FIELD.cpp_type = 3

pb.SC_SOCIETY_RED_PACKET_OPENTOTAL_PROCESS_PRIZESTRING_FIELD.name = "PrizeString"
pb.SC_SOCIETY_RED_PACKET_OPENTOTAL_PROCESS_PRIZESTRING_FIELD.full_name = ".SC_Society_Red_Packet_OpenTotal_Process.PrizeString"
pb.SC_SOCIETY_RED_PACKET_OPENTOTAL_PROCESS_PRIZESTRING_FIELD.number = 4
pb.SC_SOCIETY_RED_PACKET_OPENTOTAL_PROCESS_PRIZESTRING_FIELD.index = 3
pb.SC_SOCIETY_RED_PACKET_OPENTOTAL_PROCESS_PRIZESTRING_FIELD.label = 1
pb.SC_SOCIETY_RED_PACKET_OPENTOTAL_PROCESS_PRIZESTRING_FIELD.has_default_value = false
pb.SC_SOCIETY_RED_PACKET_OPENTOTAL_PROCESS_PRIZESTRING_FIELD.default_value = ""
pb.SC_SOCIETY_RED_PACKET_OPENTOTAL_PROCESS_PRIZESTRING_FIELD.type = 9
pb.SC_SOCIETY_RED_PACKET_OPENTOTAL_PROCESS_PRIZESTRING_FIELD.cpp_type = 9

pb.SC_SOCIETY_RED_PACKET_OPENTOTAL_PROCESS.name = "SC_Society_Red_Packet_OpenTotal_Process"
pb.SC_SOCIETY_RED_PACKET_OPENTOTAL_PROCESS.full_name = ".SC_Society_Red_Packet_OpenTotal_Process"
pb.SC_SOCIETY_RED_PACKET_OPENTOTAL_PROCESS.nested_types = {}
pb.SC_SOCIETY_RED_PACKET_OPENTOTAL_PROCESS.enum_types = {}
pb.SC_SOCIETY_RED_PACKET_OPENTOTAL_PROCESS.fields = {pb.SC_SOCIETY_RED_PACKET_OPENTOTAL_PROCESS_OPENCOUNT_FIELD, pb.SC_SOCIETY_RED_PACKET_OPENTOTAL_PROCESS_NEEDCOUNT_FIELD, pb.SC_SOCIETY_RED_PACKET_OPENTOTAL_PROCESS_LIMITCOUNT_FIELD, pb.SC_SOCIETY_RED_PACKET_OPENTOTAL_PROCESS_PRIZESTRING_FIELD}
pb.SC_SOCIETY_RED_PACKET_OPENTOTAL_PROCESS.is_extendable = false
pb.SC_SOCIETY_RED_PACKET_OPENTOTAL_PROCESS.extensions = {}
pb.SC_SOCIETY_RED_PACKET_OTHER_PRIZE_PRIZESTRING_FIELD.name = "PrizeString"
pb.SC_SOCIETY_RED_PACKET_OTHER_PRIZE_PRIZESTRING_FIELD.full_name = ".SC_Society_Red_Packet_Other_Prize.PrizeString"
pb.SC_SOCIETY_RED_PACKET_OTHER_PRIZE_PRIZESTRING_FIELD.number = 1
pb.SC_SOCIETY_RED_PACKET_OTHER_PRIZE_PRIZESTRING_FIELD.index = 0
pb.SC_SOCIETY_RED_PACKET_OTHER_PRIZE_PRIZESTRING_FIELD.label = 1
pb.SC_SOCIETY_RED_PACKET_OTHER_PRIZE_PRIZESTRING_FIELD.has_default_value = false
pb.SC_SOCIETY_RED_PACKET_OTHER_PRIZE_PRIZESTRING_FIELD.default_value = ""
pb.SC_SOCIETY_RED_PACKET_OTHER_PRIZE_PRIZESTRING_FIELD.type = 9
pb.SC_SOCIETY_RED_PACKET_OTHER_PRIZE_PRIZESTRING_FIELD.cpp_type = 9

pb.SC_SOCIETY_RED_PACKET_OTHER_PRIZE.name = "SC_Society_Red_Packet_Other_Prize"
pb.SC_SOCIETY_RED_PACKET_OTHER_PRIZE.full_name = ".SC_Society_Red_Packet_Other_Prize"
pb.SC_SOCIETY_RED_PACKET_OTHER_PRIZE.nested_types = {}
pb.SC_SOCIETY_RED_PACKET_OTHER_PRIZE.enum_types = {}
pb.SC_SOCIETY_RED_PACKET_OTHER_PRIZE.fields = {pb.SC_SOCIETY_RED_PACKET_OTHER_PRIZE_PRIZESTRING_FIELD}
pb.SC_SOCIETY_RED_PACKET_OTHER_PRIZE.is_extendable = false
pb.SC_SOCIETY_RED_PACKET_OTHER_PRIZE.extensions = {}
pb.CS_WARBAND_CREATE_WARBANDNAME_FIELD.name = "WarbandName"
pb.CS_WARBAND_CREATE_WARBANDNAME_FIELD.full_name = ".CS_Warband_Create.WarbandName"
pb.CS_WARBAND_CREATE_WARBANDNAME_FIELD.number = 1
pb.CS_WARBAND_CREATE_WARBANDNAME_FIELD.index = 0
pb.CS_WARBAND_CREATE_WARBANDNAME_FIELD.label = 1
pb.CS_WARBAND_CREATE_WARBANDNAME_FIELD.has_default_value = false
pb.CS_WARBAND_CREATE_WARBANDNAME_FIELD.default_value = ""
pb.CS_WARBAND_CREATE_WARBANDNAME_FIELD.type = 9
pb.CS_WARBAND_CREATE_WARBANDNAME_FIELD.cpp_type = 9

pb.CS_WARBAND_CREATE.name = "CS_Warband_Create"
pb.CS_WARBAND_CREATE.full_name = ".CS_Warband_Create"
pb.CS_WARBAND_CREATE.nested_types = {}
pb.CS_WARBAND_CREATE.enum_types = {}
pb.CS_WARBAND_CREATE.fields = {pb.CS_WARBAND_CREATE_WARBANDNAME_FIELD}
pb.CS_WARBAND_CREATE.is_extendable = false
pb.CS_WARBAND_CREATE.extensions = {}
pb.SC_WARBAND_CREATE_RESULT_FIELD.name = "Result"
pb.SC_WARBAND_CREATE_RESULT_FIELD.full_name = ".SC_Warband_Create.Result"
pb.SC_WARBAND_CREATE_RESULT_FIELD.number = 1
pb.SC_WARBAND_CREATE_RESULT_FIELD.index = 0
pb.SC_WARBAND_CREATE_RESULT_FIELD.label = 1
pb.SC_WARBAND_CREATE_RESULT_FIELD.has_default_value = false
pb.SC_WARBAND_CREATE_RESULT_FIELD.default_value = 0
pb.SC_WARBAND_CREATE_RESULT_FIELD.type = 5
pb.SC_WARBAND_CREATE_RESULT_FIELD.cpp_type = 1

pb.SC_WARBAND_CREATE_PARAM_FIELD.name = "Param"
pb.SC_WARBAND_CREATE_PARAM_FIELD.full_name = ".SC_Warband_Create.Param"
pb.SC_WARBAND_CREATE_PARAM_FIELD.number = 2
pb.SC_WARBAND_CREATE_PARAM_FIELD.index = 1
pb.SC_WARBAND_CREATE_PARAM_FIELD.label = 1
pb.SC_WARBAND_CREATE_PARAM_FIELD.has_default_value = false
pb.SC_WARBAND_CREATE_PARAM_FIELD.default_value = 0
pb.SC_WARBAND_CREATE_PARAM_FIELD.type = 5
pb.SC_WARBAND_CREATE_PARAM_FIELD.cpp_type = 1

pb.SC_WARBAND_CREATE.name = "SC_Warband_Create"
pb.SC_WARBAND_CREATE.full_name = ".SC_Warband_Create"
pb.SC_WARBAND_CREATE.nested_types = {}
pb.SC_WARBAND_CREATE.enum_types = {}
pb.SC_WARBAND_CREATE.fields = {pb.SC_WARBAND_CREATE_RESULT_FIELD, pb.SC_WARBAND_CREATE_PARAM_FIELD}
pb.SC_WARBAND_CREATE.is_extendable = false
pb.SC_WARBAND_CREATE.extensions = {}
pb.SC_WARBAND_DELETE_RESULT_FIELD.name = "Result"
pb.SC_WARBAND_DELETE_RESULT_FIELD.full_name = ".SC_Warband_Delete.Result"
pb.SC_WARBAND_DELETE_RESULT_FIELD.number = 1
pb.SC_WARBAND_DELETE_RESULT_FIELD.index = 0
pb.SC_WARBAND_DELETE_RESULT_FIELD.label = 1
pb.SC_WARBAND_DELETE_RESULT_FIELD.has_default_value = false
pb.SC_WARBAND_DELETE_RESULT_FIELD.default_value = 0
pb.SC_WARBAND_DELETE_RESULT_FIELD.type = 5
pb.SC_WARBAND_DELETE_RESULT_FIELD.cpp_type = 1

pb.SC_WARBAND_DELETE.name = "SC_Warband_Delete"
pb.SC_WARBAND_DELETE.full_name = ".SC_Warband_Delete"
pb.SC_WARBAND_DELETE.nested_types = {}
pb.SC_WARBAND_DELETE.enum_types = {}
pb.SC_WARBAND_DELETE.fields = {pb.SC_WARBAND_DELETE_RESULT_FIELD}
pb.SC_WARBAND_DELETE.is_extendable = false
pb.SC_WARBAND_DELETE.extensions = {}
pb.CS_WARBAND_JOIN_WARBANDID_FIELD.name = "WarbandID"
pb.CS_WARBAND_JOIN_WARBANDID_FIELD.full_name = ".CS_Warband_Join.WarbandID"
pb.CS_WARBAND_JOIN_WARBANDID_FIELD.number = 1
pb.CS_WARBAND_JOIN_WARBANDID_FIELD.index = 0
pb.CS_WARBAND_JOIN_WARBANDID_FIELD.label = 1
pb.CS_WARBAND_JOIN_WARBANDID_FIELD.has_default_value = false
pb.CS_WARBAND_JOIN_WARBANDID_FIELD.default_value = 0
pb.CS_WARBAND_JOIN_WARBANDID_FIELD.type = 5
pb.CS_WARBAND_JOIN_WARBANDID_FIELD.cpp_type = 1

pb.CS_WARBAND_JOIN.name = "CS_Warband_Join"
pb.CS_WARBAND_JOIN.full_name = ".CS_Warband_Join"
pb.CS_WARBAND_JOIN.nested_types = {}
pb.CS_WARBAND_JOIN.enum_types = {}
pb.CS_WARBAND_JOIN.fields = {pb.CS_WARBAND_JOIN_WARBANDID_FIELD}
pb.CS_WARBAND_JOIN.is_extendable = false
pb.CS_WARBAND_JOIN.extensions = {}
pb.SC_WARBAND_JOIN_RESULT_FIELD.name = "Result"
pb.SC_WARBAND_JOIN_RESULT_FIELD.full_name = ".SC_Warband_Join.Result"
pb.SC_WARBAND_JOIN_RESULT_FIELD.number = 1
pb.SC_WARBAND_JOIN_RESULT_FIELD.index = 0
pb.SC_WARBAND_JOIN_RESULT_FIELD.label = 1
pb.SC_WARBAND_JOIN_RESULT_FIELD.has_default_value = false
pb.SC_WARBAND_JOIN_RESULT_FIELD.default_value = 0
pb.SC_WARBAND_JOIN_RESULT_FIELD.type = 5
pb.SC_WARBAND_JOIN_RESULT_FIELD.cpp_type = 1

pb.SC_WARBAND_JOIN.name = "SC_Warband_Join"
pb.SC_WARBAND_JOIN.full_name = ".SC_Warband_Join"
pb.SC_WARBAND_JOIN.nested_types = {}
pb.SC_WARBAND_JOIN.enum_types = {}
pb.SC_WARBAND_JOIN.fields = {pb.SC_WARBAND_JOIN_RESULT_FIELD}
pb.SC_WARBAND_JOIN.is_extendable = false
pb.SC_WARBAND_JOIN.extensions = {}
pb.SC_WARBAND_LEAVE_RESULT_FIELD.name = "Result"
pb.SC_WARBAND_LEAVE_RESULT_FIELD.full_name = ".SC_Warband_Leave.Result"
pb.SC_WARBAND_LEAVE_RESULT_FIELD.number = 1
pb.SC_WARBAND_LEAVE_RESULT_FIELD.index = 0
pb.SC_WARBAND_LEAVE_RESULT_FIELD.label = 1
pb.SC_WARBAND_LEAVE_RESULT_FIELD.has_default_value = false
pb.SC_WARBAND_LEAVE_RESULT_FIELD.default_value = 0
pb.SC_WARBAND_LEAVE_RESULT_FIELD.type = 5
pb.SC_WARBAND_LEAVE_RESULT_FIELD.cpp_type = 1

pb.SC_WARBAND_LEAVE.name = "SC_Warband_Leave"
pb.SC_WARBAND_LEAVE.full_name = ".SC_Warband_Leave"
pb.SC_WARBAND_LEAVE.nested_types = {}
pb.SC_WARBAND_LEAVE.enum_types = {}
pb.SC_WARBAND_LEAVE.fields = {pb.SC_WARBAND_LEAVE_RESULT_FIELD}
pb.SC_WARBAND_LEAVE.is_extendable = false
pb.SC_WARBAND_LEAVE.extensions = {}
pb.CS_WARBAND_GETLIST_COUNTRY_FIELD.name = "Country"
pb.CS_WARBAND_GETLIST_COUNTRY_FIELD.full_name = ".CS_Warband_GetList.Country"
pb.CS_WARBAND_GETLIST_COUNTRY_FIELD.number = 1
pb.CS_WARBAND_GETLIST_COUNTRY_FIELD.index = 0
pb.CS_WARBAND_GETLIST_COUNTRY_FIELD.label = 1
pb.CS_WARBAND_GETLIST_COUNTRY_FIELD.has_default_value = false
pb.CS_WARBAND_GETLIST_COUNTRY_FIELD.default_value = 0
pb.CS_WARBAND_GETLIST_COUNTRY_FIELD.type = 5
pb.CS_WARBAND_GETLIST_COUNTRY_FIELD.cpp_type = 1

pb.CS_WARBAND_GETLIST_PAGEINDEX_FIELD.name = "PageIndex"
pb.CS_WARBAND_GETLIST_PAGEINDEX_FIELD.full_name = ".CS_Warband_GetList.PageIndex"
pb.CS_WARBAND_GETLIST_PAGEINDEX_FIELD.number = 2
pb.CS_WARBAND_GETLIST_PAGEINDEX_FIELD.index = 1
pb.CS_WARBAND_GETLIST_PAGEINDEX_FIELD.label = 1
pb.CS_WARBAND_GETLIST_PAGEINDEX_FIELD.has_default_value = false
pb.CS_WARBAND_GETLIST_PAGEINDEX_FIELD.default_value = 0
pb.CS_WARBAND_GETLIST_PAGEINDEX_FIELD.type = 5
pb.CS_WARBAND_GETLIST_PAGEINDEX_FIELD.cpp_type = 1

pb.CS_WARBAND_GETLIST_PAGESIZE_FIELD.name = "PageSize"
pb.CS_WARBAND_GETLIST_PAGESIZE_FIELD.full_name = ".CS_Warband_GetList.PageSize"
pb.CS_WARBAND_GETLIST_PAGESIZE_FIELD.number = 3
pb.CS_WARBAND_GETLIST_PAGESIZE_FIELD.index = 2
pb.CS_WARBAND_GETLIST_PAGESIZE_FIELD.label = 1
pb.CS_WARBAND_GETLIST_PAGESIZE_FIELD.has_default_value = false
pb.CS_WARBAND_GETLIST_PAGESIZE_FIELD.default_value = 0
pb.CS_WARBAND_GETLIST_PAGESIZE_FIELD.type = 5
pb.CS_WARBAND_GETLIST_PAGESIZE_FIELD.cpp_type = 1

pb.CS_WARBAND_GETLIST_SEARCHNAME_FIELD.name = "SearchName"
pb.CS_WARBAND_GETLIST_SEARCHNAME_FIELD.full_name = ".CS_Warband_GetList.SearchName"
pb.CS_WARBAND_GETLIST_SEARCHNAME_FIELD.number = 4
pb.CS_WARBAND_GETLIST_SEARCHNAME_FIELD.index = 3
pb.CS_WARBAND_GETLIST_SEARCHNAME_FIELD.label = 1
pb.CS_WARBAND_GETLIST_SEARCHNAME_FIELD.has_default_value = false
pb.CS_WARBAND_GETLIST_SEARCHNAME_FIELD.default_value = ""
pb.CS_WARBAND_GETLIST_SEARCHNAME_FIELD.type = 9
pb.CS_WARBAND_GETLIST_SEARCHNAME_FIELD.cpp_type = 9

pb.CS_WARBAND_GETLIST.name = "CS_Warband_GetList"
pb.CS_WARBAND_GETLIST.full_name = ".CS_Warband_GetList"
pb.CS_WARBAND_GETLIST.nested_types = {}
pb.CS_WARBAND_GETLIST.enum_types = {}
pb.CS_WARBAND_GETLIST.fields = {pb.CS_WARBAND_GETLIST_COUNTRY_FIELD, pb.CS_WARBAND_GETLIST_PAGEINDEX_FIELD, pb.CS_WARBAND_GETLIST_PAGESIZE_FIELD, pb.CS_WARBAND_GETLIST_SEARCHNAME_FIELD}
pb.CS_WARBAND_GETLIST.is_extendable = false
pb.CS_WARBAND_GETLIST.extensions = {}
pb.SC_WARBAND_GETLIST_WARBANDDATA_WARBANDID_FIELD.name = "WarbandID"
pb.SC_WARBAND_GETLIST_WARBANDDATA_WARBANDID_FIELD.full_name = ".SC_Warband_GetList.WarbandData.WarbandID"
pb.SC_WARBAND_GETLIST_WARBANDDATA_WARBANDID_FIELD.number = 1
pb.SC_WARBAND_GETLIST_WARBANDDATA_WARBANDID_FIELD.index = 0
pb.SC_WARBAND_GETLIST_WARBANDDATA_WARBANDID_FIELD.label = 1
pb.SC_WARBAND_GETLIST_WARBANDDATA_WARBANDID_FIELD.has_default_value = false
pb.SC_WARBAND_GETLIST_WARBANDDATA_WARBANDID_FIELD.default_value = 0
pb.SC_WARBAND_GETLIST_WARBANDDATA_WARBANDID_FIELD.type = 5
pb.SC_WARBAND_GETLIST_WARBANDDATA_WARBANDID_FIELD.cpp_type = 1

pb.SC_WARBAND_GETLIST_WARBANDDATA_WARBANDNAME_FIELD.name = "WarbandName"
pb.SC_WARBAND_GETLIST_WARBANDDATA_WARBANDNAME_FIELD.full_name = ".SC_Warband_GetList.WarbandData.WarbandName"
pb.SC_WARBAND_GETLIST_WARBANDDATA_WARBANDNAME_FIELD.number = 2
pb.SC_WARBAND_GETLIST_WARBANDDATA_WARBANDNAME_FIELD.index = 1
pb.SC_WARBAND_GETLIST_WARBANDDATA_WARBANDNAME_FIELD.label = 1
pb.SC_WARBAND_GETLIST_WARBANDDATA_WARBANDNAME_FIELD.has_default_value = false
pb.SC_WARBAND_GETLIST_WARBANDDATA_WARBANDNAME_FIELD.default_value = ""
pb.SC_WARBAND_GETLIST_WARBANDDATA_WARBANDNAME_FIELD.type = 9
pb.SC_WARBAND_GETLIST_WARBANDDATA_WARBANDNAME_FIELD.cpp_type = 9

pb.SC_WARBAND_GETLIST_WARBANDDATA_MEMBERNUM_FIELD.name = "MemberNum"
pb.SC_WARBAND_GETLIST_WARBANDDATA_MEMBERNUM_FIELD.full_name = ".SC_Warband_GetList.WarbandData.MemberNum"
pb.SC_WARBAND_GETLIST_WARBANDDATA_MEMBERNUM_FIELD.number = 3
pb.SC_WARBAND_GETLIST_WARBANDDATA_MEMBERNUM_FIELD.index = 2
pb.SC_WARBAND_GETLIST_WARBANDDATA_MEMBERNUM_FIELD.label = 1
pb.SC_WARBAND_GETLIST_WARBANDDATA_MEMBERNUM_FIELD.has_default_value = false
pb.SC_WARBAND_GETLIST_WARBANDDATA_MEMBERNUM_FIELD.default_value = 0
pb.SC_WARBAND_GETLIST_WARBANDDATA_MEMBERNUM_FIELD.type = 5
pb.SC_WARBAND_GETLIST_WARBANDDATA_MEMBERNUM_FIELD.cpp_type = 1

pb.SC_WARBAND_GETLIST_WARBANDDATA_LEVELLIMIT_FIELD.name = "LevelLimit"
pb.SC_WARBAND_GETLIST_WARBANDDATA_LEVELLIMIT_FIELD.full_name = ".SC_Warband_GetList.WarbandData.LevelLimit"
pb.SC_WARBAND_GETLIST_WARBANDDATA_LEVELLIMIT_FIELD.number = 4
pb.SC_WARBAND_GETLIST_WARBANDDATA_LEVELLIMIT_FIELD.index = 3
pb.SC_WARBAND_GETLIST_WARBANDDATA_LEVELLIMIT_FIELD.label = 1
pb.SC_WARBAND_GETLIST_WARBANDDATA_LEVELLIMIT_FIELD.has_default_value = false
pb.SC_WARBAND_GETLIST_WARBANDDATA_LEVELLIMIT_FIELD.default_value = 0
pb.SC_WARBAND_GETLIST_WARBANDDATA_LEVELLIMIT_FIELD.type = 5
pb.SC_WARBAND_GETLIST_WARBANDDATA_LEVELLIMIT_FIELD.cpp_type = 1

pb.SC_WARBAND_GETLIST_WARBANDDATA_JOINLIMIT_FIELD.name = "JoinLimit"
pb.SC_WARBAND_GETLIST_WARBANDDATA_JOINLIMIT_FIELD.full_name = ".SC_Warband_GetList.WarbandData.JoinLimit"
pb.SC_WARBAND_GETLIST_WARBANDDATA_JOINLIMIT_FIELD.number = 5
pb.SC_WARBAND_GETLIST_WARBANDDATA_JOINLIMIT_FIELD.index = 4
pb.SC_WARBAND_GETLIST_WARBANDDATA_JOINLIMIT_FIELD.label = 1
pb.SC_WARBAND_GETLIST_WARBANDDATA_JOINLIMIT_FIELD.has_default_value = false
pb.SC_WARBAND_GETLIST_WARBANDDATA_JOINLIMIT_FIELD.default_value = 0
pb.SC_WARBAND_GETLIST_WARBANDDATA_JOINLIMIT_FIELD.type = 5
pb.SC_WARBAND_GETLIST_WARBANDDATA_JOINLIMIT_FIELD.cpp_type = 1

pb.SC_WARBAND_GETLIST_WARBANDDATA_TOTALPOWER_FIELD.name = "TotalPower"
pb.SC_WARBAND_GETLIST_WARBANDDATA_TOTALPOWER_FIELD.full_name = ".SC_Warband_GetList.WarbandData.TotalPower"
pb.SC_WARBAND_GETLIST_WARBANDDATA_TOTALPOWER_FIELD.number = 7
pb.SC_WARBAND_GETLIST_WARBANDDATA_TOTALPOWER_FIELD.index = 5
pb.SC_WARBAND_GETLIST_WARBANDDATA_TOTALPOWER_FIELD.label = 1
pb.SC_WARBAND_GETLIST_WARBANDDATA_TOTALPOWER_FIELD.has_default_value = false
pb.SC_WARBAND_GETLIST_WARBANDDATA_TOTALPOWER_FIELD.default_value = 0
pb.SC_WARBAND_GETLIST_WARBANDDATA_TOTALPOWER_FIELD.type = 5
pb.SC_WARBAND_GETLIST_WARBANDDATA_TOTALPOWER_FIELD.cpp_type = 1

pb.SC_WARBAND_GETLIST_WARBANDDATA.name = "WarbandData"
pb.SC_WARBAND_GETLIST_WARBANDDATA.full_name = ".SC_Warband_GetList.WarbandData"
pb.SC_WARBAND_GETLIST_WARBANDDATA.nested_types = {}
pb.SC_WARBAND_GETLIST_WARBANDDATA.enum_types = {}
pb.SC_WARBAND_GETLIST_WARBANDDATA.fields = {pb.SC_WARBAND_GETLIST_WARBANDDATA_WARBANDID_FIELD, pb.SC_WARBAND_GETLIST_WARBANDDATA_WARBANDNAME_FIELD, pb.SC_WARBAND_GETLIST_WARBANDDATA_MEMBERNUM_FIELD, pb.SC_WARBAND_GETLIST_WARBANDDATA_LEVELLIMIT_FIELD, pb.SC_WARBAND_GETLIST_WARBANDDATA_JOINLIMIT_FIELD, pb.SC_WARBAND_GETLIST_WARBANDDATA_TOTALPOWER_FIELD}
pb.SC_WARBAND_GETLIST_WARBANDDATA.is_extendable = false
pb.SC_WARBAND_GETLIST_WARBANDDATA.extensions = {}
pb.SC_WARBAND_GETLIST_WARBANDDATA.containing_type = pb.SC_WARBAND_GETLIST
pb.SC_WARBAND_GETLIST_WARBANDLIST_FIELD.name = "WarbandList"
pb.SC_WARBAND_GETLIST_WARBANDLIST_FIELD.full_name = ".SC_Warband_GetList.WarbandList"
pb.SC_WARBAND_GETLIST_WARBANDLIST_FIELD.number = 1
pb.SC_WARBAND_GETLIST_WARBANDLIST_FIELD.index = 0
pb.SC_WARBAND_GETLIST_WARBANDLIST_FIELD.label = 3
pb.SC_WARBAND_GETLIST_WARBANDLIST_FIELD.has_default_value = false
pb.SC_WARBAND_GETLIST_WARBANDLIST_FIELD.default_value = {}
pb.SC_WARBAND_GETLIST_WARBANDLIST_FIELD.message_type = pb.SC_WARBAND_GETLIST_WARBANDDATA
pb.SC_WARBAND_GETLIST_WARBANDLIST_FIELD.type = 11
pb.SC_WARBAND_GETLIST_WARBANDLIST_FIELD.cpp_type = 10

pb.SC_WARBAND_GETLIST_COUNTRY_FIELD.name = "Country"
pb.SC_WARBAND_GETLIST_COUNTRY_FIELD.full_name = ".SC_Warband_GetList.Country"
pb.SC_WARBAND_GETLIST_COUNTRY_FIELD.number = 2
pb.SC_WARBAND_GETLIST_COUNTRY_FIELD.index = 1
pb.SC_WARBAND_GETLIST_COUNTRY_FIELD.label = 1
pb.SC_WARBAND_GETLIST_COUNTRY_FIELD.has_default_value = false
pb.SC_WARBAND_GETLIST_COUNTRY_FIELD.default_value = 0
pb.SC_WARBAND_GETLIST_COUNTRY_FIELD.type = 5
pb.SC_WARBAND_GETLIST_COUNTRY_FIELD.cpp_type = 1

pb.SC_WARBAND_GETLIST_PAGEINDEX_FIELD.name = "PageIndex"
pb.SC_WARBAND_GETLIST_PAGEINDEX_FIELD.full_name = ".SC_Warband_GetList.PageIndex"
pb.SC_WARBAND_GETLIST_PAGEINDEX_FIELD.number = 3
pb.SC_WARBAND_GETLIST_PAGEINDEX_FIELD.index = 2
pb.SC_WARBAND_GETLIST_PAGEINDEX_FIELD.label = 1
pb.SC_WARBAND_GETLIST_PAGEINDEX_FIELD.has_default_value = false
pb.SC_WARBAND_GETLIST_PAGEINDEX_FIELD.default_value = 0
pb.SC_WARBAND_GETLIST_PAGEINDEX_FIELD.type = 5
pb.SC_WARBAND_GETLIST_PAGEINDEX_FIELD.cpp_type = 1

pb.SC_WARBAND_GETLIST_PAGESIZE_FIELD.name = "PageSize"
pb.SC_WARBAND_GETLIST_PAGESIZE_FIELD.full_name = ".SC_Warband_GetList.PageSize"
pb.SC_WARBAND_GETLIST_PAGESIZE_FIELD.number = 4
pb.SC_WARBAND_GETLIST_PAGESIZE_FIELD.index = 3
pb.SC_WARBAND_GETLIST_PAGESIZE_FIELD.label = 1
pb.SC_WARBAND_GETLIST_PAGESIZE_FIELD.has_default_value = false
pb.SC_WARBAND_GETLIST_PAGESIZE_FIELD.default_value = 0
pb.SC_WARBAND_GETLIST_PAGESIZE_FIELD.type = 5
pb.SC_WARBAND_GETLIST_PAGESIZE_FIELD.cpp_type = 1

pb.SC_WARBAND_GETLIST_WARBANDCOUNT_FIELD.name = "WarbandCount"
pb.SC_WARBAND_GETLIST_WARBANDCOUNT_FIELD.full_name = ".SC_Warband_GetList.WarbandCount"
pb.SC_WARBAND_GETLIST_WARBANDCOUNT_FIELD.number = 5
pb.SC_WARBAND_GETLIST_WARBANDCOUNT_FIELD.index = 4
pb.SC_WARBAND_GETLIST_WARBANDCOUNT_FIELD.label = 1
pb.SC_WARBAND_GETLIST_WARBANDCOUNT_FIELD.has_default_value = false
pb.SC_WARBAND_GETLIST_WARBANDCOUNT_FIELD.default_value = 0
pb.SC_WARBAND_GETLIST_WARBANDCOUNT_FIELD.type = 5
pb.SC_WARBAND_GETLIST_WARBANDCOUNT_FIELD.cpp_type = 1

pb.SC_WARBAND_GETLIST.name = "SC_Warband_GetList"
pb.SC_WARBAND_GETLIST.full_name = ".SC_Warband_GetList"
pb.SC_WARBAND_GETLIST.nested_types = {pb.SC_WARBAND_GETLIST_WARBANDDATA}
pb.SC_WARBAND_GETLIST.enum_types = {}
pb.SC_WARBAND_GETLIST.fields = {pb.SC_WARBAND_GETLIST_WARBANDLIST_FIELD, pb.SC_WARBAND_GETLIST_COUNTRY_FIELD, pb.SC_WARBAND_GETLIST_PAGEINDEX_FIELD, pb.SC_WARBAND_GETLIST_PAGESIZE_FIELD, pb.SC_WARBAND_GETLIST_WARBANDCOUNT_FIELD}
pb.SC_WARBAND_GETLIST.is_extendable = false
pb.SC_WARBAND_GETLIST.extensions = {}
pb.SC_WARBAND_UPDATEDATA_WARBANDID_FIELD.name = "WarbandID"
pb.SC_WARBAND_UPDATEDATA_WARBANDID_FIELD.full_name = ".SC_Warband_UpdateData.WarbandID"
pb.SC_WARBAND_UPDATEDATA_WARBANDID_FIELD.number = 1
pb.SC_WARBAND_UPDATEDATA_WARBANDID_FIELD.index = 0
pb.SC_WARBAND_UPDATEDATA_WARBANDID_FIELD.label = 1
pb.SC_WARBAND_UPDATEDATA_WARBANDID_FIELD.has_default_value = false
pb.SC_WARBAND_UPDATEDATA_WARBANDID_FIELD.default_value = 0
pb.SC_WARBAND_UPDATEDATA_WARBANDID_FIELD.type = 5
pb.SC_WARBAND_UPDATEDATA_WARBANDID_FIELD.cpp_type = 1

pb.SC_WARBAND_UPDATEDATA_WARBANDNAME_FIELD.name = "WarbandName"
pb.SC_WARBAND_UPDATEDATA_WARBANDNAME_FIELD.full_name = ".SC_Warband_UpdateData.WarbandName"
pb.SC_WARBAND_UPDATEDATA_WARBANDNAME_FIELD.number = 2
pb.SC_WARBAND_UPDATEDATA_WARBANDNAME_FIELD.index = 1
pb.SC_WARBAND_UPDATEDATA_WARBANDNAME_FIELD.label = 1
pb.SC_WARBAND_UPDATEDATA_WARBANDNAME_FIELD.has_default_value = false
pb.SC_WARBAND_UPDATEDATA_WARBANDNAME_FIELD.default_value = ""
pb.SC_WARBAND_UPDATEDATA_WARBANDNAME_FIELD.type = 9
pb.SC_WARBAND_UPDATEDATA_WARBANDNAME_FIELD.cpp_type = 9

pb.SC_WARBAND_UPDATEDATA_COUNTRY_FIELD.name = "Country"
pb.SC_WARBAND_UPDATEDATA_COUNTRY_FIELD.full_name = ".SC_Warband_UpdateData.Country"
pb.SC_WARBAND_UPDATEDATA_COUNTRY_FIELD.number = 3
pb.SC_WARBAND_UPDATEDATA_COUNTRY_FIELD.index = 2
pb.SC_WARBAND_UPDATEDATA_COUNTRY_FIELD.label = 1
pb.SC_WARBAND_UPDATEDATA_COUNTRY_FIELD.has_default_value = false
pb.SC_WARBAND_UPDATEDATA_COUNTRY_FIELD.default_value = 0
pb.SC_WARBAND_UPDATEDATA_COUNTRY_FIELD.type = 5
pb.SC_WARBAND_UPDATEDATA_COUNTRY_FIELD.cpp_type = 1

pb.SC_WARBAND_UPDATEDATA_LEADERID_FIELD.name = "LeaderID"
pb.SC_WARBAND_UPDATEDATA_LEADERID_FIELD.full_name = ".SC_Warband_UpdateData.LeaderID"
pb.SC_WARBAND_UPDATEDATA_LEADERID_FIELD.number = 4
pb.SC_WARBAND_UPDATEDATA_LEADERID_FIELD.index = 3
pb.SC_WARBAND_UPDATEDATA_LEADERID_FIELD.label = 1
pb.SC_WARBAND_UPDATEDATA_LEADERID_FIELD.has_default_value = false
pb.SC_WARBAND_UPDATEDATA_LEADERID_FIELD.default_value = 0
pb.SC_WARBAND_UPDATEDATA_LEADERID_FIELD.type = 5
pb.SC_WARBAND_UPDATEDATA_LEADERID_FIELD.cpp_type = 1

pb.SC_WARBAND_UPDATEDATA_LEVELLIMIT_FIELD.name = "LevelLimit"
pb.SC_WARBAND_UPDATEDATA_LEVELLIMIT_FIELD.full_name = ".SC_Warband_UpdateData.LevelLimit"
pb.SC_WARBAND_UPDATEDATA_LEVELLIMIT_FIELD.number = 5
pb.SC_WARBAND_UPDATEDATA_LEVELLIMIT_FIELD.index = 4
pb.SC_WARBAND_UPDATEDATA_LEVELLIMIT_FIELD.label = 1
pb.SC_WARBAND_UPDATEDATA_LEVELLIMIT_FIELD.has_default_value = false
pb.SC_WARBAND_UPDATEDATA_LEVELLIMIT_FIELD.default_value = 0
pb.SC_WARBAND_UPDATEDATA_LEVELLIMIT_FIELD.type = 5
pb.SC_WARBAND_UPDATEDATA_LEVELLIMIT_FIELD.cpp_type = 1

pb.SC_WARBAND_UPDATEDATA_JOINLIMIT_FIELD.name = "JoinLimit"
pb.SC_WARBAND_UPDATEDATA_JOINLIMIT_FIELD.full_name = ".SC_Warband_UpdateData.JoinLimit"
pb.SC_WARBAND_UPDATEDATA_JOINLIMIT_FIELD.number = 6
pb.SC_WARBAND_UPDATEDATA_JOINLIMIT_FIELD.index = 5
pb.SC_WARBAND_UPDATEDATA_JOINLIMIT_FIELD.label = 1
pb.SC_WARBAND_UPDATEDATA_JOINLIMIT_FIELD.has_default_value = false
pb.SC_WARBAND_UPDATEDATA_JOINLIMIT_FIELD.default_value = 0
pb.SC_WARBAND_UPDATEDATA_JOINLIMIT_FIELD.type = 5
pb.SC_WARBAND_UPDATEDATA_JOINLIMIT_FIELD.cpp_type = 1

pb.SC_WARBAND_UPDATEDATA_POWERLIMIT_FIELD.name = "PowerLimit"
pb.SC_WARBAND_UPDATEDATA_POWERLIMIT_FIELD.full_name = ".SC_Warband_UpdateData.PowerLimit"
pb.SC_WARBAND_UPDATEDATA_POWERLIMIT_FIELD.number = 7
pb.SC_WARBAND_UPDATEDATA_POWERLIMIT_FIELD.index = 6
pb.SC_WARBAND_UPDATEDATA_POWERLIMIT_FIELD.label = 1
pb.SC_WARBAND_UPDATEDATA_POWERLIMIT_FIELD.has_default_value = false
pb.SC_WARBAND_UPDATEDATA_POWERLIMIT_FIELD.default_value = 0
pb.SC_WARBAND_UPDATEDATA_POWERLIMIT_FIELD.type = 5
pb.SC_WARBAND_UPDATEDATA_POWERLIMIT_FIELD.cpp_type = 1

pb.SC_WARBAND_UPDATEDATA_RANDOMECTYPECOUNT_FIELD.name = "RandomEctypeCount"
pb.SC_WARBAND_UPDATEDATA_RANDOMECTYPECOUNT_FIELD.full_name = ".SC_Warband_UpdateData.RandomEctypeCount"
pb.SC_WARBAND_UPDATEDATA_RANDOMECTYPECOUNT_FIELD.number = 8
pb.SC_WARBAND_UPDATEDATA_RANDOMECTYPECOUNT_FIELD.index = 7
pb.SC_WARBAND_UPDATEDATA_RANDOMECTYPECOUNT_FIELD.label = 1
pb.SC_WARBAND_UPDATEDATA_RANDOMECTYPECOUNT_FIELD.has_default_value = false
pb.SC_WARBAND_UPDATEDATA_RANDOMECTYPECOUNT_FIELD.default_value = 0
pb.SC_WARBAND_UPDATEDATA_RANDOMECTYPECOUNT_FIELD.type = 5
pb.SC_WARBAND_UPDATEDATA_RANDOMECTYPECOUNT_FIELD.cpp_type = 1

pb.SC_WARBAND_UPDATEDATA_RANDOMECTYPECONFIGID_FIELD.name = "RandomEctypeConfigID"
pb.SC_WARBAND_UPDATEDATA_RANDOMECTYPECONFIGID_FIELD.full_name = ".SC_Warband_UpdateData.RandomEctypeConfigID"
pb.SC_WARBAND_UPDATEDATA_RANDOMECTYPECONFIGID_FIELD.number = 9
pb.SC_WARBAND_UPDATEDATA_RANDOMECTYPECONFIGID_FIELD.index = 8
pb.SC_WARBAND_UPDATEDATA_RANDOMECTYPECONFIGID_FIELD.label = 1
pb.SC_WARBAND_UPDATEDATA_RANDOMECTYPECONFIGID_FIELD.has_default_value = false
pb.SC_WARBAND_UPDATEDATA_RANDOMECTYPECONFIGID_FIELD.default_value = 0
pb.SC_WARBAND_UPDATEDATA_RANDOMECTYPECONFIGID_FIELD.type = 5
pb.SC_WARBAND_UPDATEDATA_RANDOMECTYPECONFIGID_FIELD.cpp_type = 1

pb.SC_WARBAND_UPDATEDATA_RANDOMECTYPEOPENTIME_FIELD.name = "RandomEctypeOpenTime"
pb.SC_WARBAND_UPDATEDATA_RANDOMECTYPEOPENTIME_FIELD.full_name = ".SC_Warband_UpdateData.RandomEctypeOpenTime"
pb.SC_WARBAND_UPDATEDATA_RANDOMECTYPEOPENTIME_FIELD.number = 10
pb.SC_WARBAND_UPDATEDATA_RANDOMECTYPEOPENTIME_FIELD.index = 9
pb.SC_WARBAND_UPDATEDATA_RANDOMECTYPEOPENTIME_FIELD.label = 1
pb.SC_WARBAND_UPDATEDATA_RANDOMECTYPEOPENTIME_FIELD.has_default_value = false
pb.SC_WARBAND_UPDATEDATA_RANDOMECTYPEOPENTIME_FIELD.default_value = 0
pb.SC_WARBAND_UPDATEDATA_RANDOMECTYPEOPENTIME_FIELD.type = 5
pb.SC_WARBAND_UPDATEDATA_RANDOMECTYPEOPENTIME_FIELD.cpp_type = 1

pb.SC_WARBAND_UPDATEDATA_RANDOMECTYPECOOLDOWNTIME_FIELD.name = "RandomEctypeCoolDownTime"
pb.SC_WARBAND_UPDATEDATA_RANDOMECTYPECOOLDOWNTIME_FIELD.full_name = ".SC_Warband_UpdateData.RandomEctypeCoolDownTime"
pb.SC_WARBAND_UPDATEDATA_RANDOMECTYPECOOLDOWNTIME_FIELD.number = 11
pb.SC_WARBAND_UPDATEDATA_RANDOMECTYPECOOLDOWNTIME_FIELD.index = 10
pb.SC_WARBAND_UPDATEDATA_RANDOMECTYPECOOLDOWNTIME_FIELD.label = 1
pb.SC_WARBAND_UPDATEDATA_RANDOMECTYPECOOLDOWNTIME_FIELD.has_default_value = false
pb.SC_WARBAND_UPDATEDATA_RANDOMECTYPECOOLDOWNTIME_FIELD.default_value = 0
pb.SC_WARBAND_UPDATEDATA_RANDOMECTYPECOOLDOWNTIME_FIELD.type = 5
pb.SC_WARBAND_UPDATEDATA_RANDOMECTYPECOOLDOWNTIME_FIELD.cpp_type = 1

pb.SC_WARBAND_UPDATEDATA.name = "SC_Warband_UpdateData"
pb.SC_WARBAND_UPDATEDATA.full_name = ".SC_Warband_UpdateData"
pb.SC_WARBAND_UPDATEDATA.nested_types = {}
pb.SC_WARBAND_UPDATEDATA.enum_types = {}
pb.SC_WARBAND_UPDATEDATA.fields = {pb.SC_WARBAND_UPDATEDATA_WARBANDID_FIELD, pb.SC_WARBAND_UPDATEDATA_WARBANDNAME_FIELD, pb.SC_WARBAND_UPDATEDATA_COUNTRY_FIELD, pb.SC_WARBAND_UPDATEDATA_LEADERID_FIELD, pb.SC_WARBAND_UPDATEDATA_LEVELLIMIT_FIELD, pb.SC_WARBAND_UPDATEDATA_JOINLIMIT_FIELD, pb.SC_WARBAND_UPDATEDATA_POWERLIMIT_FIELD, pb.SC_WARBAND_UPDATEDATA_RANDOMECTYPECOUNT_FIELD, pb.SC_WARBAND_UPDATEDATA_RANDOMECTYPECONFIGID_FIELD, pb.SC_WARBAND_UPDATEDATA_RANDOMECTYPEOPENTIME_FIELD, pb.SC_WARBAND_UPDATEDATA_RANDOMECTYPECOOLDOWNTIME_FIELD}
pb.SC_WARBAND_UPDATEDATA.is_extendable = false
pb.SC_WARBAND_UPDATEDATA.extensions = {}
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_ACTORID_FIELD.name = "ActorID"
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_ACTORID_FIELD.full_name = ".SC_Warband_MemberList.MemberData.ActorID"
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_ACTORID_FIELD.number = 1
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_ACTORID_FIELD.index = 0
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_ACTORID_FIELD.label = 1
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_ACTORID_FIELD.has_default_value = false
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_ACTORID_FIELD.default_value = 0
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_ACTORID_FIELD.type = 5
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_ACTORID_FIELD.cpp_type = 1

pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_ACTORNAME_FIELD.name = "ActorName"
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_ACTORNAME_FIELD.full_name = ".SC_Warband_MemberList.MemberData.ActorName"
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_ACTORNAME_FIELD.number = 2
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_ACTORNAME_FIELD.index = 1
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_ACTORNAME_FIELD.label = 1
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_ACTORNAME_FIELD.has_default_value = false
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_ACTORNAME_FIELD.default_value = ""
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_ACTORNAME_FIELD.type = 9
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_ACTORNAME_FIELD.cpp_type = 9

pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_LEVEL_FIELD.name = "Level"
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_LEVEL_FIELD.full_name = ".SC_Warband_MemberList.MemberData.Level"
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_LEVEL_FIELD.number = 3
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_LEVEL_FIELD.index = 2
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_LEVEL_FIELD.label = 1
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_LEVEL_FIELD.has_default_value = false
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_LEVEL_FIELD.default_value = 0
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_LEVEL_FIELD.type = 5
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_LEVEL_FIELD.cpp_type = 1

pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_VOCATION_FIELD.name = "Vocation"
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_VOCATION_FIELD.full_name = ".SC_Warband_MemberList.MemberData.Vocation"
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_VOCATION_FIELD.number = 4
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_VOCATION_FIELD.index = 3
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_VOCATION_FIELD.label = 1
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_VOCATION_FIELD.has_default_value = false
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_VOCATION_FIELD.default_value = 0
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_VOCATION_FIELD.type = 5
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_VOCATION_FIELD.cpp_type = 1

pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_POWER_FIELD.name = "Power"
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_POWER_FIELD.full_name = ".SC_Warband_MemberList.MemberData.Power"
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_POWER_FIELD.number = 5
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_POWER_FIELD.index = 4
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_POWER_FIELD.label = 1
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_POWER_FIELD.has_default_value = false
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_POWER_FIELD.default_value = 0
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_POWER_FIELD.type = 5
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_POWER_FIELD.cpp_type = 1

pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_VIPLEVEL_FIELD.name = "VipLevel"
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_VIPLEVEL_FIELD.full_name = ".SC_Warband_MemberList.MemberData.VipLevel"
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_VIPLEVEL_FIELD.number = 6
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_VIPLEVEL_FIELD.index = 5
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_VIPLEVEL_FIELD.label = 1
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_VIPLEVEL_FIELD.has_default_value = false
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_VIPLEVEL_FIELD.default_value = 0
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_VIPLEVEL_FIELD.type = 5
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_VIPLEVEL_FIELD.cpp_type = 1

pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_ISONLINE_FIELD.name = "IsOnline"
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_ISONLINE_FIELD.full_name = ".SC_Warband_MemberList.MemberData.IsOnline"
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_ISONLINE_FIELD.number = 7
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_ISONLINE_FIELD.index = 6
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_ISONLINE_FIELD.label = 1
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_ISONLINE_FIELD.has_default_value = false
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_ISONLINE_FIELD.default_value = 0
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_ISONLINE_FIELD.type = 5
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_ISONLINE_FIELD.cpp_type = 1

pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_CURACTVCOUNT_FIELD.name = "CurActvCount"
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_CURACTVCOUNT_FIELD.full_name = ".SC_Warband_MemberList.MemberData.CurActvCount"
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_CURACTVCOUNT_FIELD.number = 8
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_CURACTVCOUNT_FIELD.index = 7
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_CURACTVCOUNT_FIELD.label = 1
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_CURACTVCOUNT_FIELD.has_default_value = false
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_CURACTVCOUNT_FIELD.default_value = 0
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_CURACTVCOUNT_FIELD.type = 5
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_CURACTVCOUNT_FIELD.cpp_type = 1

pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_TOTALACTVCOUNT_FIELD.name = "TotalActvCount"
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_TOTALACTVCOUNT_FIELD.full_name = ".SC_Warband_MemberList.MemberData.TotalActvCount"
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_TOTALACTVCOUNT_FIELD.number = 9
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_TOTALACTVCOUNT_FIELD.index = 8
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_TOTALACTVCOUNT_FIELD.label = 1
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_TOTALACTVCOUNT_FIELD.has_default_value = false
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_TOTALACTVCOUNT_FIELD.default_value = 0
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_TOTALACTVCOUNT_FIELD.type = 5
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_TOTALACTVCOUNT_FIELD.cpp_type = 1

pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_LOGOUTTIME_FIELD.name = "LogoutTime"
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_LOGOUTTIME_FIELD.full_name = ".SC_Warband_MemberList.MemberData.LogoutTime"
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_LOGOUTTIME_FIELD.number = 10
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_LOGOUTTIME_FIELD.index = 9
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_LOGOUTTIME_FIELD.label = 1
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_LOGOUTTIME_FIELD.has_default_value = false
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_LOGOUTTIME_FIELD.default_value = 0
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_LOGOUTTIME_FIELD.type = 5
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_LOGOUTTIME_FIELD.cpp_type = 1

pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_EQUIPMODEID_FIELD.name = "EquipModeID"
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_EQUIPMODEID_FIELD.full_name = ".SC_Warband_MemberList.MemberData.EquipModeID"
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_EQUIPMODEID_FIELD.number = 11
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_EQUIPMODEID_FIELD.index = 10
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_EQUIPMODEID_FIELD.label = 1
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_EQUIPMODEID_FIELD.has_default_value = false
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_EQUIPMODEID_FIELD.default_value = 0
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_EQUIPMODEID_FIELD.type = 5
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_EQUIPMODEID_FIELD.cpp_type = 1

pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_WEAPONCREATUREID_FIELD.name = "WeaponCreatureID"
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_WEAPONCREATUREID_FIELD.full_name = ".SC_Warband_MemberList.MemberData.WeaponCreatureID"
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_WEAPONCREATUREID_FIELD.number = 12
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_WEAPONCREATUREID_FIELD.index = 11
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_WEAPONCREATUREID_FIELD.label = 1
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_WEAPONCREATUREID_FIELD.has_default_value = false
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_WEAPONCREATUREID_FIELD.default_value = 0
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_WEAPONCREATUREID_FIELD.type = 5
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_WEAPONCREATUREID_FIELD.cpp_type = 1

pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_WINGCREATUREID_FIELD.name = "WingCreatureID"
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_WINGCREATUREID_FIELD.full_name = ".SC_Warband_MemberList.MemberData.WingCreatureID"
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_WINGCREATUREID_FIELD.number = 13
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_WINGCREATUREID_FIELD.index = 12
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_WINGCREATUREID_FIELD.label = 1
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_WINGCREATUREID_FIELD.has_default_value = false
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_WINGCREATUREID_FIELD.default_value = 0
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_WINGCREATUREID_FIELD.type = 5
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_WINGCREATUREID_FIELD.cpp_type = 1

pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_NEWWINGCREATUREID_FIELD.name = "NewWingCreatureID"
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_NEWWINGCREATUREID_FIELD.full_name = ".SC_Warband_MemberList.MemberData.NewWingCreatureID"
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_NEWWINGCREATUREID_FIELD.number = 14
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_NEWWINGCREATUREID_FIELD.index = 13
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_NEWWINGCREATUREID_FIELD.label = 1
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_NEWWINGCREATUREID_FIELD.has_default_value = false
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_NEWWINGCREATUREID_FIELD.default_value = 0
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_NEWWINGCREATUREID_FIELD.type = 5
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_NEWWINGCREATUREID_FIELD.cpp_type = 1

pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_OPENECTYPECOUNT_FIELD.name = "OpenEctypeCount"
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_OPENECTYPECOUNT_FIELD.full_name = ".SC_Warband_MemberList.MemberData.OpenEctypeCount"
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_OPENECTYPECOUNT_FIELD.number = 15
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_OPENECTYPECOUNT_FIELD.index = 14
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_OPENECTYPECOUNT_FIELD.label = 1
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_OPENECTYPECOUNT_FIELD.has_default_value = false
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_OPENECTYPECOUNT_FIELD.default_value = 0
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_OPENECTYPECOUNT_FIELD.type = 5
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_OPENECTYPECOUNT_FIELD.cpp_type = 1

pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_JOINTIME_FIELD.name = "JoinTime"
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_JOINTIME_FIELD.full_name = ".SC_Warband_MemberList.MemberData.JoinTime"
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_JOINTIME_FIELD.number = 16
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_JOINTIME_FIELD.index = 15
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_JOINTIME_FIELD.label = 1
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_JOINTIME_FIELD.has_default_value = false
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_JOINTIME_FIELD.default_value = 0
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_JOINTIME_FIELD.type = 5
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_JOINTIME_FIELD.cpp_type = 1

pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_BLUEVIPLEVEL_FIELD.name = "BlueVIPLevel"
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_BLUEVIPLEVEL_FIELD.full_name = ".SC_Warband_MemberList.MemberData.BlueVIPLevel"
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_BLUEVIPLEVEL_FIELD.number = 17
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_BLUEVIPLEVEL_FIELD.index = 16
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_BLUEVIPLEVEL_FIELD.label = 1
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_BLUEVIPLEVEL_FIELD.has_default_value = false
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_BLUEVIPLEVEL_FIELD.default_value = 0
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_BLUEVIPLEVEL_FIELD.type = 5
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_BLUEVIPLEVEL_FIELD.cpp_type = 1

pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_BLUEVIPTYPE_FIELD.name = "BlueVIPType"
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_BLUEVIPTYPE_FIELD.full_name = ".SC_Warband_MemberList.MemberData.BlueVIPType"
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_BLUEVIPTYPE_FIELD.number = 18
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_BLUEVIPTYPE_FIELD.index = 17
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_BLUEVIPTYPE_FIELD.label = 1
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_BLUEVIPTYPE_FIELD.has_default_value = false
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_BLUEVIPTYPE_FIELD.default_value = 0
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_BLUEVIPTYPE_FIELD.type = 5
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_BLUEVIPTYPE_FIELD.cpp_type = 1

pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_GENRE_FIELD.name = "Genre"
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_GENRE_FIELD.full_name = ".SC_Warband_MemberList.MemberData.Genre"
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_GENRE_FIELD.number = 19
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_GENRE_FIELD.index = 18
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_GENRE_FIELD.label = 1
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_GENRE_FIELD.has_default_value = false
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_GENRE_FIELD.default_value = 0
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_GENRE_FIELD.type = 5
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_GENRE_FIELD.cpp_type = 1

pb.SC_WARBAND_MEMBERLIST_MEMBERDATA.name = "MemberData"
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA.full_name = ".SC_Warband_MemberList.MemberData"
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA.nested_types = {}
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA.enum_types = {}
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA.fields = {pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_ACTORID_FIELD, pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_ACTORNAME_FIELD, pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_LEVEL_FIELD, pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_VOCATION_FIELD, pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_POWER_FIELD, pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_VIPLEVEL_FIELD, pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_ISONLINE_FIELD, pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_CURACTVCOUNT_FIELD, pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_TOTALACTVCOUNT_FIELD, pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_LOGOUTTIME_FIELD, pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_EQUIPMODEID_FIELD, pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_WEAPONCREATUREID_FIELD, pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_WINGCREATUREID_FIELD, pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_NEWWINGCREATUREID_FIELD, pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_OPENECTYPECOUNT_FIELD, pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_JOINTIME_FIELD, pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_BLUEVIPLEVEL_FIELD, pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_BLUEVIPTYPE_FIELD, pb.SC_WARBAND_MEMBERLIST_MEMBERDATA_GENRE_FIELD}
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA.is_extendable = false
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA.extensions = {}
pb.SC_WARBAND_MEMBERLIST_MEMBERDATA.containing_type = pb.SC_WARBAND_MEMBERLIST
pb.SC_WARBAND_MEMBERLIST_WARBANDID_FIELD.name = "WarbandID"
pb.SC_WARBAND_MEMBERLIST_WARBANDID_FIELD.full_name = ".SC_Warband_MemberList.WarbandID"
pb.SC_WARBAND_MEMBERLIST_WARBANDID_FIELD.number = 1
pb.SC_WARBAND_MEMBERLIST_WARBANDID_FIELD.index = 0
pb.SC_WARBAND_MEMBERLIST_WARBANDID_FIELD.label = 1
pb.SC_WARBAND_MEMBERLIST_WARBANDID_FIELD.has_default_value = false
pb.SC_WARBAND_MEMBERLIST_WARBANDID_FIELD.default_value = 0
pb.SC_WARBAND_MEMBERLIST_WARBANDID_FIELD.type = 5
pb.SC_WARBAND_MEMBERLIST_WARBANDID_FIELD.cpp_type = 1

pb.SC_WARBAND_MEMBERLIST_ISINIT_FIELD.name = "IsInit"
pb.SC_WARBAND_MEMBERLIST_ISINIT_FIELD.full_name = ".SC_Warband_MemberList.IsInit"
pb.SC_WARBAND_MEMBERLIST_ISINIT_FIELD.number = 2
pb.SC_WARBAND_MEMBERLIST_ISINIT_FIELD.index = 1
pb.SC_WARBAND_MEMBERLIST_ISINIT_FIELD.label = 1
pb.SC_WARBAND_MEMBERLIST_ISINIT_FIELD.has_default_value = false
pb.SC_WARBAND_MEMBERLIST_ISINIT_FIELD.default_value = 0
pb.SC_WARBAND_MEMBERLIST_ISINIT_FIELD.type = 5
pb.SC_WARBAND_MEMBERLIST_ISINIT_FIELD.cpp_type = 1

pb.SC_WARBAND_MEMBERLIST_MEMBERLIST_FIELD.name = "MemberList"
pb.SC_WARBAND_MEMBERLIST_MEMBERLIST_FIELD.full_name = ".SC_Warband_MemberList.MemberList"
pb.SC_WARBAND_MEMBERLIST_MEMBERLIST_FIELD.number = 3
pb.SC_WARBAND_MEMBERLIST_MEMBERLIST_FIELD.index = 2
pb.SC_WARBAND_MEMBERLIST_MEMBERLIST_FIELD.label = 3
pb.SC_WARBAND_MEMBERLIST_MEMBERLIST_FIELD.has_default_value = false
pb.SC_WARBAND_MEMBERLIST_MEMBERLIST_FIELD.default_value = {}
pb.SC_WARBAND_MEMBERLIST_MEMBERLIST_FIELD.message_type = pb.SC_WARBAND_MEMBERLIST_MEMBERDATA
pb.SC_WARBAND_MEMBERLIST_MEMBERLIST_FIELD.type = 11
pb.SC_WARBAND_MEMBERLIST_MEMBERLIST_FIELD.cpp_type = 10

pb.SC_WARBAND_MEMBERLIST.name = "SC_Warband_MemberList"
pb.SC_WARBAND_MEMBERLIST.full_name = ".SC_Warband_MemberList"
pb.SC_WARBAND_MEMBERLIST.nested_types = {pb.SC_WARBAND_MEMBERLIST_MEMBERDATA}
pb.SC_WARBAND_MEMBERLIST.enum_types = {}
pb.SC_WARBAND_MEMBERLIST.fields = {pb.SC_WARBAND_MEMBERLIST_WARBANDID_FIELD, pb.SC_WARBAND_MEMBERLIST_ISINIT_FIELD, pb.SC_WARBAND_MEMBERLIST_MEMBERLIST_FIELD}
pb.SC_WARBAND_MEMBERLIST.is_extendable = false
pb.SC_WARBAND_MEMBERLIST.extensions = {}
pb.CS_WARBAND_SETWARBANDCONFIG_JOINLIMIT_FIELD.name = "JoinLimit"
pb.CS_WARBAND_SETWARBANDCONFIG_JOINLIMIT_FIELD.full_name = ".CS_Warband_SetWarbandConfig.JoinLimit"
pb.CS_WARBAND_SETWARBANDCONFIG_JOINLIMIT_FIELD.number = 1
pb.CS_WARBAND_SETWARBANDCONFIG_JOINLIMIT_FIELD.index = 0
pb.CS_WARBAND_SETWARBANDCONFIG_JOINLIMIT_FIELD.label = 1
pb.CS_WARBAND_SETWARBANDCONFIG_JOINLIMIT_FIELD.has_default_value = false
pb.CS_WARBAND_SETWARBANDCONFIG_JOINLIMIT_FIELD.default_value = 0
pb.CS_WARBAND_SETWARBANDCONFIG_JOINLIMIT_FIELD.type = 5
pb.CS_WARBAND_SETWARBANDCONFIG_JOINLIMIT_FIELD.cpp_type = 1

pb.CS_WARBAND_SETWARBANDCONFIG_LEVELLIMIT_FIELD.name = "LevelLimit"
pb.CS_WARBAND_SETWARBANDCONFIG_LEVELLIMIT_FIELD.full_name = ".CS_Warband_SetWarbandConfig.LevelLimit"
pb.CS_WARBAND_SETWARBANDCONFIG_LEVELLIMIT_FIELD.number = 2
pb.CS_WARBAND_SETWARBANDCONFIG_LEVELLIMIT_FIELD.index = 1
pb.CS_WARBAND_SETWARBANDCONFIG_LEVELLIMIT_FIELD.label = 1
pb.CS_WARBAND_SETWARBANDCONFIG_LEVELLIMIT_FIELD.has_default_value = false
pb.CS_WARBAND_SETWARBANDCONFIG_LEVELLIMIT_FIELD.default_value = 0
pb.CS_WARBAND_SETWARBANDCONFIG_LEVELLIMIT_FIELD.type = 5
pb.CS_WARBAND_SETWARBANDCONFIG_LEVELLIMIT_FIELD.cpp_type = 1

pb.CS_WARBAND_SETWARBANDCONFIG_POWERLIMIT_FIELD.name = "PowerLimit"
pb.CS_WARBAND_SETWARBANDCONFIG_POWERLIMIT_FIELD.full_name = ".CS_Warband_SetWarbandConfig.PowerLimit"
pb.CS_WARBAND_SETWARBANDCONFIG_POWERLIMIT_FIELD.number = 3
pb.CS_WARBAND_SETWARBANDCONFIG_POWERLIMIT_FIELD.index = 2
pb.CS_WARBAND_SETWARBANDCONFIG_POWERLIMIT_FIELD.label = 1
pb.CS_WARBAND_SETWARBANDCONFIG_POWERLIMIT_FIELD.has_default_value = false
pb.CS_WARBAND_SETWARBANDCONFIG_POWERLIMIT_FIELD.default_value = 0
pb.CS_WARBAND_SETWARBANDCONFIG_POWERLIMIT_FIELD.type = 5
pb.CS_WARBAND_SETWARBANDCONFIG_POWERLIMIT_FIELD.cpp_type = 1

pb.CS_WARBAND_SETWARBANDCONFIG.name = "CS_Warband_SetWarbandConfig"
pb.CS_WARBAND_SETWARBANDCONFIG.full_name = ".CS_Warband_SetWarbandConfig"
pb.CS_WARBAND_SETWARBANDCONFIG.nested_types = {}
pb.CS_WARBAND_SETWARBANDCONFIG.enum_types = {}
pb.CS_WARBAND_SETWARBANDCONFIG.fields = {pb.CS_WARBAND_SETWARBANDCONFIG_JOINLIMIT_FIELD, pb.CS_WARBAND_SETWARBANDCONFIG_LEVELLIMIT_FIELD, pb.CS_WARBAND_SETWARBANDCONFIG_POWERLIMIT_FIELD}
pb.CS_WARBAND_SETWARBANDCONFIG.is_extendable = false
pb.CS_WARBAND_SETWARBANDCONFIG.extensions = {}
pb.SC_WARBAND_SETWARBANDCONFIG_RESULT_FIELD.name = "Result"
pb.SC_WARBAND_SETWARBANDCONFIG_RESULT_FIELD.full_name = ".SC_Warband_SetWarbandConfig.Result"
pb.SC_WARBAND_SETWARBANDCONFIG_RESULT_FIELD.number = 1
pb.SC_WARBAND_SETWARBANDCONFIG_RESULT_FIELD.index = 0
pb.SC_WARBAND_SETWARBANDCONFIG_RESULT_FIELD.label = 1
pb.SC_WARBAND_SETWARBANDCONFIG_RESULT_FIELD.has_default_value = false
pb.SC_WARBAND_SETWARBANDCONFIG_RESULT_FIELD.default_value = 0
pb.SC_WARBAND_SETWARBANDCONFIG_RESULT_FIELD.type = 5
pb.SC_WARBAND_SETWARBANDCONFIG_RESULT_FIELD.cpp_type = 1

pb.SC_WARBAND_SETWARBANDCONFIG.name = "SC_Warband_SetWarbandConfig"
pb.SC_WARBAND_SETWARBANDCONFIG.full_name = ".SC_Warband_SetWarbandConfig"
pb.SC_WARBAND_SETWARBANDCONFIG.nested_types = {}
pb.SC_WARBAND_SETWARBANDCONFIG.enum_types = {}
pb.SC_WARBAND_SETWARBANDCONFIG.fields = {pb.SC_WARBAND_SETWARBANDCONFIG_RESULT_FIELD}
pb.SC_WARBAND_SETWARBANDCONFIG.is_extendable = false
pb.SC_WARBAND_SETWARBANDCONFIG.extensions = {}
pb.SC_WARBAND_GETAPPLYLIST_APPLYACTOR_ACTORID_FIELD.name = "ActorID"
pb.SC_WARBAND_GETAPPLYLIST_APPLYACTOR_ACTORID_FIELD.full_name = ".SC_Warband_GetApplyList.ApplyActor.ActorID"
pb.SC_WARBAND_GETAPPLYLIST_APPLYACTOR_ACTORID_FIELD.number = 1
pb.SC_WARBAND_GETAPPLYLIST_APPLYACTOR_ACTORID_FIELD.index = 0
pb.SC_WARBAND_GETAPPLYLIST_APPLYACTOR_ACTORID_FIELD.label = 1
pb.SC_WARBAND_GETAPPLYLIST_APPLYACTOR_ACTORID_FIELD.has_default_value = false
pb.SC_WARBAND_GETAPPLYLIST_APPLYACTOR_ACTORID_FIELD.default_value = 0
pb.SC_WARBAND_GETAPPLYLIST_APPLYACTOR_ACTORID_FIELD.type = 5
pb.SC_WARBAND_GETAPPLYLIST_APPLYACTOR_ACTORID_FIELD.cpp_type = 1

pb.SC_WARBAND_GETAPPLYLIST_APPLYACTOR_ACTORNAME_FIELD.name = "ActorName"
pb.SC_WARBAND_GETAPPLYLIST_APPLYACTOR_ACTORNAME_FIELD.full_name = ".SC_Warband_GetApplyList.ApplyActor.ActorName"
pb.SC_WARBAND_GETAPPLYLIST_APPLYACTOR_ACTORNAME_FIELD.number = 2
pb.SC_WARBAND_GETAPPLYLIST_APPLYACTOR_ACTORNAME_FIELD.index = 1
pb.SC_WARBAND_GETAPPLYLIST_APPLYACTOR_ACTORNAME_FIELD.label = 1
pb.SC_WARBAND_GETAPPLYLIST_APPLYACTOR_ACTORNAME_FIELD.has_default_value = false
pb.SC_WARBAND_GETAPPLYLIST_APPLYACTOR_ACTORNAME_FIELD.default_value = ""
pb.SC_WARBAND_GETAPPLYLIST_APPLYACTOR_ACTORNAME_FIELD.type = 9
pb.SC_WARBAND_GETAPPLYLIST_APPLYACTOR_ACTORNAME_FIELD.cpp_type = 9

pb.SC_WARBAND_GETAPPLYLIST_APPLYACTOR_LEVEL_FIELD.name = "Level"
pb.SC_WARBAND_GETAPPLYLIST_APPLYACTOR_LEVEL_FIELD.full_name = ".SC_Warband_GetApplyList.ApplyActor.Level"
pb.SC_WARBAND_GETAPPLYLIST_APPLYACTOR_LEVEL_FIELD.number = 3
pb.SC_WARBAND_GETAPPLYLIST_APPLYACTOR_LEVEL_FIELD.index = 2
pb.SC_WARBAND_GETAPPLYLIST_APPLYACTOR_LEVEL_FIELD.label = 1
pb.SC_WARBAND_GETAPPLYLIST_APPLYACTOR_LEVEL_FIELD.has_default_value = false
pb.SC_WARBAND_GETAPPLYLIST_APPLYACTOR_LEVEL_FIELD.default_value = 0
pb.SC_WARBAND_GETAPPLYLIST_APPLYACTOR_LEVEL_FIELD.type = 5
pb.SC_WARBAND_GETAPPLYLIST_APPLYACTOR_LEVEL_FIELD.cpp_type = 1

pb.SC_WARBAND_GETAPPLYLIST_APPLYACTOR_VOCATION_FIELD.name = "Vocation"
pb.SC_WARBAND_GETAPPLYLIST_APPLYACTOR_VOCATION_FIELD.full_name = ".SC_Warband_GetApplyList.ApplyActor.Vocation"
pb.SC_WARBAND_GETAPPLYLIST_APPLYACTOR_VOCATION_FIELD.number = 4
pb.SC_WARBAND_GETAPPLYLIST_APPLYACTOR_VOCATION_FIELD.index = 3
pb.SC_WARBAND_GETAPPLYLIST_APPLYACTOR_VOCATION_FIELD.label = 1
pb.SC_WARBAND_GETAPPLYLIST_APPLYACTOR_VOCATION_FIELD.has_default_value = false
pb.SC_WARBAND_GETAPPLYLIST_APPLYACTOR_VOCATION_FIELD.default_value = 0
pb.SC_WARBAND_GETAPPLYLIST_APPLYACTOR_VOCATION_FIELD.type = 5
pb.SC_WARBAND_GETAPPLYLIST_APPLYACTOR_VOCATION_FIELD.cpp_type = 1

pb.SC_WARBAND_GETAPPLYLIST_APPLYACTOR_POWER_FIELD.name = "Power"
pb.SC_WARBAND_GETAPPLYLIST_APPLYACTOR_POWER_FIELD.full_name = ".SC_Warband_GetApplyList.ApplyActor.Power"
pb.SC_WARBAND_GETAPPLYLIST_APPLYACTOR_POWER_FIELD.number = 5
pb.SC_WARBAND_GETAPPLYLIST_APPLYACTOR_POWER_FIELD.index = 4
pb.SC_WARBAND_GETAPPLYLIST_APPLYACTOR_POWER_FIELD.label = 1
pb.SC_WARBAND_GETAPPLYLIST_APPLYACTOR_POWER_FIELD.has_default_value = false
pb.SC_WARBAND_GETAPPLYLIST_APPLYACTOR_POWER_FIELD.default_value = 0
pb.SC_WARBAND_GETAPPLYLIST_APPLYACTOR_POWER_FIELD.type = 5
pb.SC_WARBAND_GETAPPLYLIST_APPLYACTOR_POWER_FIELD.cpp_type = 1

pb.SC_WARBAND_GETAPPLYLIST_APPLYACTOR_VIPLEVEL_FIELD.name = "VIPLevel"
pb.SC_WARBAND_GETAPPLYLIST_APPLYACTOR_VIPLEVEL_FIELD.full_name = ".SC_Warband_GetApplyList.ApplyActor.VIPLevel"
pb.SC_WARBAND_GETAPPLYLIST_APPLYACTOR_VIPLEVEL_FIELD.number = 6
pb.SC_WARBAND_GETAPPLYLIST_APPLYACTOR_VIPLEVEL_FIELD.index = 5
pb.SC_WARBAND_GETAPPLYLIST_APPLYACTOR_VIPLEVEL_FIELD.label = 1
pb.SC_WARBAND_GETAPPLYLIST_APPLYACTOR_VIPLEVEL_FIELD.has_default_value = false
pb.SC_WARBAND_GETAPPLYLIST_APPLYACTOR_VIPLEVEL_FIELD.default_value = 0
pb.SC_WARBAND_GETAPPLYLIST_APPLYACTOR_VIPLEVEL_FIELD.type = 5
pb.SC_WARBAND_GETAPPLYLIST_APPLYACTOR_VIPLEVEL_FIELD.cpp_type = 1

pb.SC_WARBAND_GETAPPLYLIST_APPLYACTOR_BLUEVIPLEVEL_FIELD.name = "BlueVIPLevel"
pb.SC_WARBAND_GETAPPLYLIST_APPLYACTOR_BLUEVIPLEVEL_FIELD.full_name = ".SC_Warband_GetApplyList.ApplyActor.BlueVIPLevel"
pb.SC_WARBAND_GETAPPLYLIST_APPLYACTOR_BLUEVIPLEVEL_FIELD.number = 7
pb.SC_WARBAND_GETAPPLYLIST_APPLYACTOR_BLUEVIPLEVEL_FIELD.index = 6
pb.SC_WARBAND_GETAPPLYLIST_APPLYACTOR_BLUEVIPLEVEL_FIELD.label = 1
pb.SC_WARBAND_GETAPPLYLIST_APPLYACTOR_BLUEVIPLEVEL_FIELD.has_default_value = false
pb.SC_WARBAND_GETAPPLYLIST_APPLYACTOR_BLUEVIPLEVEL_FIELD.default_value = 0
pb.SC_WARBAND_GETAPPLYLIST_APPLYACTOR_BLUEVIPLEVEL_FIELD.type = 5
pb.SC_WARBAND_GETAPPLYLIST_APPLYACTOR_BLUEVIPLEVEL_FIELD.cpp_type = 1

pb.SC_WARBAND_GETAPPLYLIST_APPLYACTOR_BLUEVIPTYPE_FIELD.name = "BlueVIPType"
pb.SC_WARBAND_GETAPPLYLIST_APPLYACTOR_BLUEVIPTYPE_FIELD.full_name = ".SC_Warband_GetApplyList.ApplyActor.BlueVIPType"
pb.SC_WARBAND_GETAPPLYLIST_APPLYACTOR_BLUEVIPTYPE_FIELD.number = 8
pb.SC_WARBAND_GETAPPLYLIST_APPLYACTOR_BLUEVIPTYPE_FIELD.index = 7
pb.SC_WARBAND_GETAPPLYLIST_APPLYACTOR_BLUEVIPTYPE_FIELD.label = 1
pb.SC_WARBAND_GETAPPLYLIST_APPLYACTOR_BLUEVIPTYPE_FIELD.has_default_value = false
pb.SC_WARBAND_GETAPPLYLIST_APPLYACTOR_BLUEVIPTYPE_FIELD.default_value = 0
pb.SC_WARBAND_GETAPPLYLIST_APPLYACTOR_BLUEVIPTYPE_FIELD.type = 5
pb.SC_WARBAND_GETAPPLYLIST_APPLYACTOR_BLUEVIPTYPE_FIELD.cpp_type = 1

pb.SC_WARBAND_GETAPPLYLIST_APPLYACTOR.name = "ApplyActor"
pb.SC_WARBAND_GETAPPLYLIST_APPLYACTOR.full_name = ".SC_Warband_GetApplyList.ApplyActor"
pb.SC_WARBAND_GETAPPLYLIST_APPLYACTOR.nested_types = {}
pb.SC_WARBAND_GETAPPLYLIST_APPLYACTOR.enum_types = {}
pb.SC_WARBAND_GETAPPLYLIST_APPLYACTOR.fields = {pb.SC_WARBAND_GETAPPLYLIST_APPLYACTOR_ACTORID_FIELD, pb.SC_WARBAND_GETAPPLYLIST_APPLYACTOR_ACTORNAME_FIELD, pb.SC_WARBAND_GETAPPLYLIST_APPLYACTOR_LEVEL_FIELD, pb.SC_WARBAND_GETAPPLYLIST_APPLYACTOR_VOCATION_FIELD, pb.SC_WARBAND_GETAPPLYLIST_APPLYACTOR_POWER_FIELD, pb.SC_WARBAND_GETAPPLYLIST_APPLYACTOR_VIPLEVEL_FIELD, pb.SC_WARBAND_GETAPPLYLIST_APPLYACTOR_BLUEVIPLEVEL_FIELD, pb.SC_WARBAND_GETAPPLYLIST_APPLYACTOR_BLUEVIPTYPE_FIELD}
pb.SC_WARBAND_GETAPPLYLIST_APPLYACTOR.is_extendable = false
pb.SC_WARBAND_GETAPPLYLIST_APPLYACTOR.extensions = {}
pb.SC_WARBAND_GETAPPLYLIST_APPLYACTOR.containing_type = pb.SC_WARBAND_GETAPPLYLIST
pb.SC_WARBAND_GETAPPLYLIST_APPLYLIST_FIELD.name = "ApplyList"
pb.SC_WARBAND_GETAPPLYLIST_APPLYLIST_FIELD.full_name = ".SC_Warband_GetApplyList.ApplyList"
pb.SC_WARBAND_GETAPPLYLIST_APPLYLIST_FIELD.number = 1
pb.SC_WARBAND_GETAPPLYLIST_APPLYLIST_FIELD.index = 0
pb.SC_WARBAND_GETAPPLYLIST_APPLYLIST_FIELD.label = 3
pb.SC_WARBAND_GETAPPLYLIST_APPLYLIST_FIELD.has_default_value = false
pb.SC_WARBAND_GETAPPLYLIST_APPLYLIST_FIELD.default_value = {}
pb.SC_WARBAND_GETAPPLYLIST_APPLYLIST_FIELD.message_type = pb.SC_WARBAND_GETAPPLYLIST_APPLYACTOR
pb.SC_WARBAND_GETAPPLYLIST_APPLYLIST_FIELD.type = 11
pb.SC_WARBAND_GETAPPLYLIST_APPLYLIST_FIELD.cpp_type = 10

pb.SC_WARBAND_GETAPPLYLIST.name = "SC_Warband_GetApplyList"
pb.SC_WARBAND_GETAPPLYLIST.full_name = ".SC_Warband_GetApplyList"
pb.SC_WARBAND_GETAPPLYLIST.nested_types = {pb.SC_WARBAND_GETAPPLYLIST_APPLYACTOR}
pb.SC_WARBAND_GETAPPLYLIST.enum_types = {}
pb.SC_WARBAND_GETAPPLYLIST.fields = {pb.SC_WARBAND_GETAPPLYLIST_APPLYLIST_FIELD}
pb.SC_WARBAND_GETAPPLYLIST.is_extendable = false
pb.SC_WARBAND_GETAPPLYLIST.extensions = {}
pb.CS_WARBAND_DEALAPPLY_TARGETID_FIELD.name = "TargetID"
pb.CS_WARBAND_DEALAPPLY_TARGETID_FIELD.full_name = ".CS_Warband_DealApply.TargetID"
pb.CS_WARBAND_DEALAPPLY_TARGETID_FIELD.number = 1
pb.CS_WARBAND_DEALAPPLY_TARGETID_FIELD.index = 0
pb.CS_WARBAND_DEALAPPLY_TARGETID_FIELD.label = 1
pb.CS_WARBAND_DEALAPPLY_TARGETID_FIELD.has_default_value = false
pb.CS_WARBAND_DEALAPPLY_TARGETID_FIELD.default_value = 0
pb.CS_WARBAND_DEALAPPLY_TARGETID_FIELD.type = 5
pb.CS_WARBAND_DEALAPPLY_TARGETID_FIELD.cpp_type = 1

pb.CS_WARBAND_DEALAPPLY_REPLY_FIELD.name = "Reply"
pb.CS_WARBAND_DEALAPPLY_REPLY_FIELD.full_name = ".CS_Warband_DealApply.Reply"
pb.CS_WARBAND_DEALAPPLY_REPLY_FIELD.number = 2
pb.CS_WARBAND_DEALAPPLY_REPLY_FIELD.index = 1
pb.CS_WARBAND_DEALAPPLY_REPLY_FIELD.label = 1
pb.CS_WARBAND_DEALAPPLY_REPLY_FIELD.has_default_value = false
pb.CS_WARBAND_DEALAPPLY_REPLY_FIELD.default_value = 0
pb.CS_WARBAND_DEALAPPLY_REPLY_FIELD.type = 5
pb.CS_WARBAND_DEALAPPLY_REPLY_FIELD.cpp_type = 1

pb.CS_WARBAND_DEALAPPLY.name = "CS_Warband_DealApply"
pb.CS_WARBAND_DEALAPPLY.full_name = ".CS_Warband_DealApply"
pb.CS_WARBAND_DEALAPPLY.nested_types = {}
pb.CS_WARBAND_DEALAPPLY.enum_types = {}
pb.CS_WARBAND_DEALAPPLY.fields = {pb.CS_WARBAND_DEALAPPLY_TARGETID_FIELD, pb.CS_WARBAND_DEALAPPLY_REPLY_FIELD}
pb.CS_WARBAND_DEALAPPLY.is_extendable = false
pb.CS_WARBAND_DEALAPPLY.extensions = {}
pb.SC_WARBAND_DEALAPPLY_RESULT_FIELD.name = "Result"
pb.SC_WARBAND_DEALAPPLY_RESULT_FIELD.full_name = ".SC_Warband_DealApply.Result"
pb.SC_WARBAND_DEALAPPLY_RESULT_FIELD.number = 1
pb.SC_WARBAND_DEALAPPLY_RESULT_FIELD.index = 0
pb.SC_WARBAND_DEALAPPLY_RESULT_FIELD.label = 1
pb.SC_WARBAND_DEALAPPLY_RESULT_FIELD.has_default_value = false
pb.SC_WARBAND_DEALAPPLY_RESULT_FIELD.default_value = 0
pb.SC_WARBAND_DEALAPPLY_RESULT_FIELD.type = 5
pb.SC_WARBAND_DEALAPPLY_RESULT_FIELD.cpp_type = 1

pb.SC_WARBAND_DEALAPPLY_WARBANDNAME_FIELD.name = "WarbandName"
pb.SC_WARBAND_DEALAPPLY_WARBANDNAME_FIELD.full_name = ".SC_Warband_DealApply.WarbandName"
pb.SC_WARBAND_DEALAPPLY_WARBANDNAME_FIELD.number = 2
pb.SC_WARBAND_DEALAPPLY_WARBANDNAME_FIELD.index = 1
pb.SC_WARBAND_DEALAPPLY_WARBANDNAME_FIELD.label = 1
pb.SC_WARBAND_DEALAPPLY_WARBANDNAME_FIELD.has_default_value = false
pb.SC_WARBAND_DEALAPPLY_WARBANDNAME_FIELD.default_value = ""
pb.SC_WARBAND_DEALAPPLY_WARBANDNAME_FIELD.type = 9
pb.SC_WARBAND_DEALAPPLY_WARBANDNAME_FIELD.cpp_type = 9

pb.SC_WARBAND_DEALAPPLY.name = "SC_Warband_DealApply"
pb.SC_WARBAND_DEALAPPLY.full_name = ".SC_Warband_DealApply"
pb.SC_WARBAND_DEALAPPLY.nested_types = {}
pb.SC_WARBAND_DEALAPPLY.enum_types = {}
pb.SC_WARBAND_DEALAPPLY.fields = {pb.SC_WARBAND_DEALAPPLY_RESULT_FIELD, pb.SC_WARBAND_DEALAPPLY_WARBANDNAME_FIELD}
pb.SC_WARBAND_DEALAPPLY.is_extendable = false
pb.SC_WARBAND_DEALAPPLY.extensions = {}
pb.CS_WARBAND_KICKMEMBER_TARGETID_FIELD.name = "TargetID"
pb.CS_WARBAND_KICKMEMBER_TARGETID_FIELD.full_name = ".CS_Warband_KickMember.TargetID"
pb.CS_WARBAND_KICKMEMBER_TARGETID_FIELD.number = 1
pb.CS_WARBAND_KICKMEMBER_TARGETID_FIELD.index = 0
pb.CS_WARBAND_KICKMEMBER_TARGETID_FIELD.label = 1
pb.CS_WARBAND_KICKMEMBER_TARGETID_FIELD.has_default_value = false
pb.CS_WARBAND_KICKMEMBER_TARGETID_FIELD.default_value = 0
pb.CS_WARBAND_KICKMEMBER_TARGETID_FIELD.type = 5
pb.CS_WARBAND_KICKMEMBER_TARGETID_FIELD.cpp_type = 1

pb.CS_WARBAND_KICKMEMBER.name = "CS_Warband_KickMember"
pb.CS_WARBAND_KICKMEMBER.full_name = ".CS_Warband_KickMember"
pb.CS_WARBAND_KICKMEMBER.nested_types = {}
pb.CS_WARBAND_KICKMEMBER.enum_types = {}
pb.CS_WARBAND_KICKMEMBER.fields = {pb.CS_WARBAND_KICKMEMBER_TARGETID_FIELD}
pb.CS_WARBAND_KICKMEMBER.is_extendable = false
pb.CS_WARBAND_KICKMEMBER.extensions = {}
pb.SC_WARBAND_KICKMEMBER_RESULT_FIELD.name = "Result"
pb.SC_WARBAND_KICKMEMBER_RESULT_FIELD.full_name = ".SC_Warband_KickMember.Result"
pb.SC_WARBAND_KICKMEMBER_RESULT_FIELD.number = 1
pb.SC_WARBAND_KICKMEMBER_RESULT_FIELD.index = 0
pb.SC_WARBAND_KICKMEMBER_RESULT_FIELD.label = 1
pb.SC_WARBAND_KICKMEMBER_RESULT_FIELD.has_default_value = false
pb.SC_WARBAND_KICKMEMBER_RESULT_FIELD.default_value = 0
pb.SC_WARBAND_KICKMEMBER_RESULT_FIELD.type = 5
pb.SC_WARBAND_KICKMEMBER_RESULT_FIELD.cpp_type = 1

pb.SC_WARBAND_KICKMEMBER.name = "SC_Warband_KickMember"
pb.SC_WARBAND_KICKMEMBER.full_name = ".SC_Warband_KickMember"
pb.SC_WARBAND_KICKMEMBER.nested_types = {}
pb.SC_WARBAND_KICKMEMBER.enum_types = {}
pb.SC_WARBAND_KICKMEMBER.fields = {pb.SC_WARBAND_KICKMEMBER_RESULT_FIELD}
pb.SC_WARBAND_KICKMEMBER.is_extendable = false
pb.SC_WARBAND_KICKMEMBER.extensions = {}
pb.CS_WARBAND_CHANGELEADER_TARGETID_FIELD.name = "TargetID"
pb.CS_WARBAND_CHANGELEADER_TARGETID_FIELD.full_name = ".CS_Warband_ChangeLeader.TargetID"
pb.CS_WARBAND_CHANGELEADER_TARGETID_FIELD.number = 1
pb.CS_WARBAND_CHANGELEADER_TARGETID_FIELD.index = 0
pb.CS_WARBAND_CHANGELEADER_TARGETID_FIELD.label = 1
pb.CS_WARBAND_CHANGELEADER_TARGETID_FIELD.has_default_value = false
pb.CS_WARBAND_CHANGELEADER_TARGETID_FIELD.default_value = 0
pb.CS_WARBAND_CHANGELEADER_TARGETID_FIELD.type = 5
pb.CS_WARBAND_CHANGELEADER_TARGETID_FIELD.cpp_type = 1

pb.CS_WARBAND_CHANGELEADER.name = "CS_Warband_ChangeLeader"
pb.CS_WARBAND_CHANGELEADER.full_name = ".CS_Warband_ChangeLeader"
pb.CS_WARBAND_CHANGELEADER.nested_types = {}
pb.CS_WARBAND_CHANGELEADER.enum_types = {}
pb.CS_WARBAND_CHANGELEADER.fields = {pb.CS_WARBAND_CHANGELEADER_TARGETID_FIELD}
pb.CS_WARBAND_CHANGELEADER.is_extendable = false
pb.CS_WARBAND_CHANGELEADER.extensions = {}
pb.SC_WARBAND_CHANGELEADER_RESULT_FIELD.name = "Result"
pb.SC_WARBAND_CHANGELEADER_RESULT_FIELD.full_name = ".SC_Warband_ChangeLeader.Result"
pb.SC_WARBAND_CHANGELEADER_RESULT_FIELD.number = 1
pb.SC_WARBAND_CHANGELEADER_RESULT_FIELD.index = 0
pb.SC_WARBAND_CHANGELEADER_RESULT_FIELD.label = 1
pb.SC_WARBAND_CHANGELEADER_RESULT_FIELD.has_default_value = false
pb.SC_WARBAND_CHANGELEADER_RESULT_FIELD.default_value = 0
pb.SC_WARBAND_CHANGELEADER_RESULT_FIELD.type = 5
pb.SC_WARBAND_CHANGELEADER_RESULT_FIELD.cpp_type = 1

pb.SC_WARBAND_CHANGELEADER.name = "SC_Warband_ChangeLeader"
pb.SC_WARBAND_CHANGELEADER.full_name = ".SC_Warband_ChangeLeader"
pb.SC_WARBAND_CHANGELEADER.nested_types = {}
pb.SC_WARBAND_CHANGELEADER.enum_types = {}
pb.SC_WARBAND_CHANGELEADER.fields = {pb.SC_WARBAND_CHANGELEADER_RESULT_FIELD}
pb.SC_WARBAND_CHANGELEADER.is_extendable = false
pb.SC_WARBAND_CHANGELEADER.extensions = {}
pb.SC_WARBAND_GETACTVRECORD_TODAYACTVCOUNT_FIELD.name = "TodayActvCount"
pb.SC_WARBAND_GETACTVRECORD_TODAYACTVCOUNT_FIELD.full_name = ".SC_Warband_GetActvRecord.TodayActvCount"
pb.SC_WARBAND_GETACTVRECORD_TODAYACTVCOUNT_FIELD.number = 1
pb.SC_WARBAND_GETACTVRECORD_TODAYACTVCOUNT_FIELD.index = 0
pb.SC_WARBAND_GETACTVRECORD_TODAYACTVCOUNT_FIELD.label = 3
pb.SC_WARBAND_GETACTVRECORD_TODAYACTVCOUNT_FIELD.has_default_value = false
pb.SC_WARBAND_GETACTVRECORD_TODAYACTVCOUNT_FIELD.default_value = {}
pb.SC_WARBAND_GETACTVRECORD_TODAYACTVCOUNT_FIELD.type = 5
pb.SC_WARBAND_GETACTVRECORD_TODAYACTVCOUNT_FIELD.cpp_type = 1

pb.SC_WARBAND_GETACTVRECORD_TODAYLOGINCOUNT_FIELD.name = "TodayLoginCount"
pb.SC_WARBAND_GETACTVRECORD_TODAYLOGINCOUNT_FIELD.full_name = ".SC_Warband_GetActvRecord.TodayLoginCount"
pb.SC_WARBAND_GETACTVRECORD_TODAYLOGINCOUNT_FIELD.number = 2
pb.SC_WARBAND_GETACTVRECORD_TODAYLOGINCOUNT_FIELD.index = 1
pb.SC_WARBAND_GETACTVRECORD_TODAYLOGINCOUNT_FIELD.label = 1
pb.SC_WARBAND_GETACTVRECORD_TODAYLOGINCOUNT_FIELD.has_default_value = false
pb.SC_WARBAND_GETACTVRECORD_TODAYLOGINCOUNT_FIELD.default_value = 0
pb.SC_WARBAND_GETACTVRECORD_TODAYLOGINCOUNT_FIELD.type = 5
pb.SC_WARBAND_GETACTVRECORD_TODAYLOGINCOUNT_FIELD.cpp_type = 1

pb.SC_WARBAND_GETACTVRECORD_ACTVCOUNTRECORD1_FIELD.name = "ActvCountRecord1"
pb.SC_WARBAND_GETACTVRECORD_ACTVCOUNTRECORD1_FIELD.full_name = ".SC_Warband_GetActvRecord.ActvCountRecord1"
pb.SC_WARBAND_GETACTVRECORD_ACTVCOUNTRECORD1_FIELD.number = 3
pb.SC_WARBAND_GETACTVRECORD_ACTVCOUNTRECORD1_FIELD.index = 2
pb.SC_WARBAND_GETACTVRECORD_ACTVCOUNTRECORD1_FIELD.label = 3
pb.SC_WARBAND_GETACTVRECORD_ACTVCOUNTRECORD1_FIELD.has_default_value = false
pb.SC_WARBAND_GETACTVRECORD_ACTVCOUNTRECORD1_FIELD.default_value = {}
pb.SC_WARBAND_GETACTVRECORD_ACTVCOUNTRECORD1_FIELD.type = 5
pb.SC_WARBAND_GETACTVRECORD_ACTVCOUNTRECORD1_FIELD.cpp_type = 1

pb.SC_WARBAND_GETACTVRECORD_LOGINCOUNTRECORD1_FIELD.name = "LoginCountRecord1"
pb.SC_WARBAND_GETACTVRECORD_LOGINCOUNTRECORD1_FIELD.full_name = ".SC_Warband_GetActvRecord.LoginCountRecord1"
pb.SC_WARBAND_GETACTVRECORD_LOGINCOUNTRECORD1_FIELD.number = 4
pb.SC_WARBAND_GETACTVRECORD_LOGINCOUNTRECORD1_FIELD.index = 3
pb.SC_WARBAND_GETACTVRECORD_LOGINCOUNTRECORD1_FIELD.label = 1
pb.SC_WARBAND_GETACTVRECORD_LOGINCOUNTRECORD1_FIELD.has_default_value = false
pb.SC_WARBAND_GETACTVRECORD_LOGINCOUNTRECORD1_FIELD.default_value = 0
pb.SC_WARBAND_GETACTVRECORD_LOGINCOUNTRECORD1_FIELD.type = 5
pb.SC_WARBAND_GETACTVRECORD_LOGINCOUNTRECORD1_FIELD.cpp_type = 1

pb.SC_WARBAND_GETACTVRECORD_ACTVCOUNTRECORD2_FIELD.name = "ActvCountRecord2"
pb.SC_WARBAND_GETACTVRECORD_ACTVCOUNTRECORD2_FIELD.full_name = ".SC_Warband_GetActvRecord.ActvCountRecord2"
pb.SC_WARBAND_GETACTVRECORD_ACTVCOUNTRECORD2_FIELD.number = 5
pb.SC_WARBAND_GETACTVRECORD_ACTVCOUNTRECORD2_FIELD.index = 4
pb.SC_WARBAND_GETACTVRECORD_ACTVCOUNTRECORD2_FIELD.label = 3
pb.SC_WARBAND_GETACTVRECORD_ACTVCOUNTRECORD2_FIELD.has_default_value = false
pb.SC_WARBAND_GETACTVRECORD_ACTVCOUNTRECORD2_FIELD.default_value = {}
pb.SC_WARBAND_GETACTVRECORD_ACTVCOUNTRECORD2_FIELD.type = 5
pb.SC_WARBAND_GETACTVRECORD_ACTVCOUNTRECORD2_FIELD.cpp_type = 1

pb.SC_WARBAND_GETACTVRECORD_LOGINCOUNTRECORD2_FIELD.name = "LoginCountRecord2"
pb.SC_WARBAND_GETACTVRECORD_LOGINCOUNTRECORD2_FIELD.full_name = ".SC_Warband_GetActvRecord.LoginCountRecord2"
pb.SC_WARBAND_GETACTVRECORD_LOGINCOUNTRECORD2_FIELD.number = 6
pb.SC_WARBAND_GETACTVRECORD_LOGINCOUNTRECORD2_FIELD.index = 5
pb.SC_WARBAND_GETACTVRECORD_LOGINCOUNTRECORD2_FIELD.label = 1
pb.SC_WARBAND_GETACTVRECORD_LOGINCOUNTRECORD2_FIELD.has_default_value = false
pb.SC_WARBAND_GETACTVRECORD_LOGINCOUNTRECORD2_FIELD.default_value = 0
pb.SC_WARBAND_GETACTVRECORD_LOGINCOUNTRECORD2_FIELD.type = 5
pb.SC_WARBAND_GETACTVRECORD_LOGINCOUNTRECORD2_FIELD.cpp_type = 1

pb.SC_WARBAND_GETACTVRECORD.name = "SC_Warband_GetActvRecord"
pb.SC_WARBAND_GETACTVRECORD.full_name = ".SC_Warband_GetActvRecord"
pb.SC_WARBAND_GETACTVRECORD.nested_types = {}
pb.SC_WARBAND_GETACTVRECORD.enum_types = {}
pb.SC_WARBAND_GETACTVRECORD.fields = {pb.SC_WARBAND_GETACTVRECORD_TODAYACTVCOUNT_FIELD, pb.SC_WARBAND_GETACTVRECORD_TODAYLOGINCOUNT_FIELD, pb.SC_WARBAND_GETACTVRECORD_ACTVCOUNTRECORD1_FIELD, pb.SC_WARBAND_GETACTVRECORD_LOGINCOUNTRECORD1_FIELD, pb.SC_WARBAND_GETACTVRECORD_ACTVCOUNTRECORD2_FIELD, pb.SC_WARBAND_GETACTVRECORD_LOGINCOUNTRECORD2_FIELD}
pb.SC_WARBAND_GETACTVRECORD.is_extendable = false
pb.SC_WARBAND_GETACTVRECORD.extensions = {}
pb.CS_WARBAND_GETRECHARGEHISTORY_RECHARGETYPE_FIELD.name = "RechargeType"
pb.CS_WARBAND_GETRECHARGEHISTORY_RECHARGETYPE_FIELD.full_name = ".CS_Warband_GetRechargeHistory.RechargeType"
pb.CS_WARBAND_GETRECHARGEHISTORY_RECHARGETYPE_FIELD.number = 1
pb.CS_WARBAND_GETRECHARGEHISTORY_RECHARGETYPE_FIELD.index = 0
pb.CS_WARBAND_GETRECHARGEHISTORY_RECHARGETYPE_FIELD.label = 2
pb.CS_WARBAND_GETRECHARGEHISTORY_RECHARGETYPE_FIELD.has_default_value = false
pb.CS_WARBAND_GETRECHARGEHISTORY_RECHARGETYPE_FIELD.default_value = 0
pb.CS_WARBAND_GETRECHARGEHISTORY_RECHARGETYPE_FIELD.type = 13
pb.CS_WARBAND_GETRECHARGEHISTORY_RECHARGETYPE_FIELD.cpp_type = 3

pb.CS_WARBAND_GETRECHARGEHISTORY.name = "CS_Warband_GetRechargeHistory"
pb.CS_WARBAND_GETRECHARGEHISTORY.full_name = ".CS_Warband_GetRechargeHistory"
pb.CS_WARBAND_GETRECHARGEHISTORY.nested_types = {}
pb.CS_WARBAND_GETRECHARGEHISTORY.enum_types = {}
pb.CS_WARBAND_GETRECHARGEHISTORY.fields = {pb.CS_WARBAND_GETRECHARGEHISTORY_RECHARGETYPE_FIELD}
pb.CS_WARBAND_GETRECHARGEHISTORY.is_extendable = false
pb.CS_WARBAND_GETRECHARGEHISTORY.extensions = {}
pb.SC_WARBAND_GETRECHARGEHISTORY_HISTORY_ACTORNAME_FIELD.name = "ActorName"
pb.SC_WARBAND_GETRECHARGEHISTORY_HISTORY_ACTORNAME_FIELD.full_name = ".SC_Warband_GetRechargeHistory.History.ActorName"
pb.SC_WARBAND_GETRECHARGEHISTORY_HISTORY_ACTORNAME_FIELD.number = 1
pb.SC_WARBAND_GETRECHARGEHISTORY_HISTORY_ACTORNAME_FIELD.index = 0
pb.SC_WARBAND_GETRECHARGEHISTORY_HISTORY_ACTORNAME_FIELD.label = 1
pb.SC_WARBAND_GETRECHARGEHISTORY_HISTORY_ACTORNAME_FIELD.has_default_value = false
pb.SC_WARBAND_GETRECHARGEHISTORY_HISTORY_ACTORNAME_FIELD.default_value = ""
pb.SC_WARBAND_GETRECHARGEHISTORY_HISTORY_ACTORNAME_FIELD.type = 9
pb.SC_WARBAND_GETRECHARGEHISTORY_HISTORY_ACTORNAME_FIELD.cpp_type = 9

pb.SC_WARBAND_GETRECHARGEHISTORY_HISTORY_TIME_FIELD.name = "Time"
pb.SC_WARBAND_GETRECHARGEHISTORY_HISTORY_TIME_FIELD.full_name = ".SC_Warband_GetRechargeHistory.History.Time"
pb.SC_WARBAND_GETRECHARGEHISTORY_HISTORY_TIME_FIELD.number = 2
pb.SC_WARBAND_GETRECHARGEHISTORY_HISTORY_TIME_FIELD.index = 1
pb.SC_WARBAND_GETRECHARGEHISTORY_HISTORY_TIME_FIELD.label = 1
pb.SC_WARBAND_GETRECHARGEHISTORY_HISTORY_TIME_FIELD.has_default_value = false
pb.SC_WARBAND_GETRECHARGEHISTORY_HISTORY_TIME_FIELD.default_value = 0
pb.SC_WARBAND_GETRECHARGEHISTORY_HISTORY_TIME_FIELD.type = 13
pb.SC_WARBAND_GETRECHARGEHISTORY_HISTORY_TIME_FIELD.cpp_type = 3

pb.SC_WARBAND_GETRECHARGEHISTORY_HISTORY_TOTALRECHARGECOUNT_FIELD.name = "TotalRechargeCount"
pb.SC_WARBAND_GETRECHARGEHISTORY_HISTORY_TOTALRECHARGECOUNT_FIELD.full_name = ".SC_Warband_GetRechargeHistory.History.TotalRechargeCount"
pb.SC_WARBAND_GETRECHARGEHISTORY_HISTORY_TOTALRECHARGECOUNT_FIELD.number = 3
pb.SC_WARBAND_GETRECHARGEHISTORY_HISTORY_TOTALRECHARGECOUNT_FIELD.index = 2
pb.SC_WARBAND_GETRECHARGEHISTORY_HISTORY_TOTALRECHARGECOUNT_FIELD.label = 1
pb.SC_WARBAND_GETRECHARGEHISTORY_HISTORY_TOTALRECHARGECOUNT_FIELD.has_default_value = false
pb.SC_WARBAND_GETRECHARGEHISTORY_HISTORY_TOTALRECHARGECOUNT_FIELD.default_value = 0
pb.SC_WARBAND_GETRECHARGEHISTORY_HISTORY_TOTALRECHARGECOUNT_FIELD.type = 13
pb.SC_WARBAND_GETRECHARGEHISTORY_HISTORY_TOTALRECHARGECOUNT_FIELD.cpp_type = 3

pb.SC_WARBAND_GETRECHARGEHISTORY_HISTORY_ITEMID_FIELD.name = "ItemID"
pb.SC_WARBAND_GETRECHARGEHISTORY_HISTORY_ITEMID_FIELD.full_name = ".SC_Warband_GetRechargeHistory.History.ItemID"
pb.SC_WARBAND_GETRECHARGEHISTORY_HISTORY_ITEMID_FIELD.number = 4
pb.SC_WARBAND_GETRECHARGEHISTORY_HISTORY_ITEMID_FIELD.index = 3
pb.SC_WARBAND_GETRECHARGEHISTORY_HISTORY_ITEMID_FIELD.label = 1
pb.SC_WARBAND_GETRECHARGEHISTORY_HISTORY_ITEMID_FIELD.has_default_value = false
pb.SC_WARBAND_GETRECHARGEHISTORY_HISTORY_ITEMID_FIELD.default_value = ""
pb.SC_WARBAND_GETRECHARGEHISTORY_HISTORY_ITEMID_FIELD.type = 9
pb.SC_WARBAND_GETRECHARGEHISTORY_HISTORY_ITEMID_FIELD.cpp_type = 9

pb.SC_WARBAND_GETRECHARGEHISTORY_HISTORY_RECHARGECOUNT_FIELD.name = "RechargeCount"
pb.SC_WARBAND_GETRECHARGEHISTORY_HISTORY_RECHARGECOUNT_FIELD.full_name = ".SC_Warband_GetRechargeHistory.History.RechargeCount"
pb.SC_WARBAND_GETRECHARGEHISTORY_HISTORY_RECHARGECOUNT_FIELD.number = 5
pb.SC_WARBAND_GETRECHARGEHISTORY_HISTORY_RECHARGECOUNT_FIELD.index = 4
pb.SC_WARBAND_GETRECHARGEHISTORY_HISTORY_RECHARGECOUNT_FIELD.label = 1
pb.SC_WARBAND_GETRECHARGEHISTORY_HISTORY_RECHARGECOUNT_FIELD.has_default_value = false
pb.SC_WARBAND_GETRECHARGEHISTORY_HISTORY_RECHARGECOUNT_FIELD.default_value = 0
pb.SC_WARBAND_GETRECHARGEHISTORY_HISTORY_RECHARGECOUNT_FIELD.type = 13
pb.SC_WARBAND_GETRECHARGEHISTORY_HISTORY_RECHARGECOUNT_FIELD.cpp_type = 3

pb.SC_WARBAND_GETRECHARGEHISTORY_HISTORY.name = "History"
pb.SC_WARBAND_GETRECHARGEHISTORY_HISTORY.full_name = ".SC_Warband_GetRechargeHistory.History"
pb.SC_WARBAND_GETRECHARGEHISTORY_HISTORY.nested_types = {}
pb.SC_WARBAND_GETRECHARGEHISTORY_HISTORY.enum_types = {}
pb.SC_WARBAND_GETRECHARGEHISTORY_HISTORY.fields = {pb.SC_WARBAND_GETRECHARGEHISTORY_HISTORY_ACTORNAME_FIELD, pb.SC_WARBAND_GETRECHARGEHISTORY_HISTORY_TIME_FIELD, pb.SC_WARBAND_GETRECHARGEHISTORY_HISTORY_TOTALRECHARGECOUNT_FIELD, pb.SC_WARBAND_GETRECHARGEHISTORY_HISTORY_ITEMID_FIELD, pb.SC_WARBAND_GETRECHARGEHISTORY_HISTORY_RECHARGECOUNT_FIELD}
pb.SC_WARBAND_GETRECHARGEHISTORY_HISTORY.is_extendable = false
pb.SC_WARBAND_GETRECHARGEHISTORY_HISTORY.extensions = {}
pb.SC_WARBAND_GETRECHARGEHISTORY_HISTORY.containing_type = pb.SC_WARBAND_GETRECHARGEHISTORY
pb.SC_WARBAND_GETRECHARGEHISTORY_HISTORYLIST_FIELD.name = "HistoryList"
pb.SC_WARBAND_GETRECHARGEHISTORY_HISTORYLIST_FIELD.full_name = ".SC_Warband_GetRechargeHistory.HistoryList"
pb.SC_WARBAND_GETRECHARGEHISTORY_HISTORYLIST_FIELD.number = 1
pb.SC_WARBAND_GETRECHARGEHISTORY_HISTORYLIST_FIELD.index = 0
pb.SC_WARBAND_GETRECHARGEHISTORY_HISTORYLIST_FIELD.label = 3
pb.SC_WARBAND_GETRECHARGEHISTORY_HISTORYLIST_FIELD.has_default_value = false
pb.SC_WARBAND_GETRECHARGEHISTORY_HISTORYLIST_FIELD.default_value = {}
pb.SC_WARBAND_GETRECHARGEHISTORY_HISTORYLIST_FIELD.message_type = pb.SC_WARBAND_GETRECHARGEHISTORY_HISTORY
pb.SC_WARBAND_GETRECHARGEHISTORY_HISTORYLIST_FIELD.type = 11
pb.SC_WARBAND_GETRECHARGEHISTORY_HISTORYLIST_FIELD.cpp_type = 10

pb.SC_WARBAND_GETRECHARGEHISTORY.name = "SC_Warband_GetRechargeHistory"
pb.SC_WARBAND_GETRECHARGEHISTORY.full_name = ".SC_Warband_GetRechargeHistory"
pb.SC_WARBAND_GETRECHARGEHISTORY.nested_types = {pb.SC_WARBAND_GETRECHARGEHISTORY_HISTORY}
pb.SC_WARBAND_GETRECHARGEHISTORY.enum_types = {}
pb.SC_WARBAND_GETRECHARGEHISTORY.fields = {pb.SC_WARBAND_GETRECHARGEHISTORY_HISTORYLIST_FIELD}
pb.SC_WARBAND_GETRECHARGEHISTORY.is_extendable = false
pb.SC_WARBAND_GETRECHARGEHISTORY.extensions = {}
pb.SC_WARBAND_GETRECHARGEMESSAGE_RESULT_FIELD.name = "Result"
pb.SC_WARBAND_GETRECHARGEMESSAGE_RESULT_FIELD.full_name = ".SC_Warband_GetRechargeMessage.Result"
pb.SC_WARBAND_GETRECHARGEMESSAGE_RESULT_FIELD.number = 1
pb.SC_WARBAND_GETRECHARGEMESSAGE_RESULT_FIELD.index = 0
pb.SC_WARBAND_GETRECHARGEMESSAGE_RESULT_FIELD.label = 2
pb.SC_WARBAND_GETRECHARGEMESSAGE_RESULT_FIELD.has_default_value = false
pb.SC_WARBAND_GETRECHARGEMESSAGE_RESULT_FIELD.default_value = 0
pb.SC_WARBAND_GETRECHARGEMESSAGE_RESULT_FIELD.type = 5
pb.SC_WARBAND_GETRECHARGEMESSAGE_RESULT_FIELD.cpp_type = 1

pb.SC_WARBAND_GETRECHARGEMESSAGE_WARBANDGIFTPRIZECOUNT_FIELD.name = "WarbandGiftPrizeCount"
pb.SC_WARBAND_GETRECHARGEMESSAGE_WARBANDGIFTPRIZECOUNT_FIELD.full_name = ".SC_Warband_GetRechargeMessage.WarbandGiftPrizeCount"
pb.SC_WARBAND_GETRECHARGEMESSAGE_WARBANDGIFTPRIZECOUNT_FIELD.number = 2
pb.SC_WARBAND_GETRECHARGEMESSAGE_WARBANDGIFTPRIZECOUNT_FIELD.index = 1
pb.SC_WARBAND_GETRECHARGEMESSAGE_WARBANDGIFTPRIZECOUNT_FIELD.label = 1
pb.SC_WARBAND_GETRECHARGEMESSAGE_WARBANDGIFTPRIZECOUNT_FIELD.has_default_value = false
pb.SC_WARBAND_GETRECHARGEMESSAGE_WARBANDGIFTPRIZECOUNT_FIELD.default_value = 0
pb.SC_WARBAND_GETRECHARGEMESSAGE_WARBANDGIFTPRIZECOUNT_FIELD.type = 5
pb.SC_WARBAND_GETRECHARGEMESSAGE_WARBANDGIFTPRIZECOUNT_FIELD.cpp_type = 1

pb.SC_WARBAND_GETRECHARGEMESSAGE_RECHARGECOUNT_FIELD.name = "RechargeCount"
pb.SC_WARBAND_GETRECHARGEMESSAGE_RECHARGECOUNT_FIELD.full_name = ".SC_Warband_GetRechargeMessage.RechargeCount"
pb.SC_WARBAND_GETRECHARGEMESSAGE_RECHARGECOUNT_FIELD.number = 3
pb.SC_WARBAND_GETRECHARGEMESSAGE_RECHARGECOUNT_FIELD.index = 2
pb.SC_WARBAND_GETRECHARGEMESSAGE_RECHARGECOUNT_FIELD.label = 1
pb.SC_WARBAND_GETRECHARGEMESSAGE_RECHARGECOUNT_FIELD.has_default_value = false
pb.SC_WARBAND_GETRECHARGEMESSAGE_RECHARGECOUNT_FIELD.default_value = 0
pb.SC_WARBAND_GETRECHARGEMESSAGE_RECHARGECOUNT_FIELD.type = 5
pb.SC_WARBAND_GETRECHARGEMESSAGE_RECHARGECOUNT_FIELD.cpp_type = 1

pb.SC_WARBAND_GETRECHARGEMESSAGE_TOTALRECHARGECOUNT_FIELD.name = "TotalRechargeCount"
pb.SC_WARBAND_GETRECHARGEMESSAGE_TOTALRECHARGECOUNT_FIELD.full_name = ".SC_Warband_GetRechargeMessage.TotalRechargeCount"
pb.SC_WARBAND_GETRECHARGEMESSAGE_TOTALRECHARGECOUNT_FIELD.number = 4
pb.SC_WARBAND_GETRECHARGEMESSAGE_TOTALRECHARGECOUNT_FIELD.index = 3
pb.SC_WARBAND_GETRECHARGEMESSAGE_TOTALRECHARGECOUNT_FIELD.label = 1
pb.SC_WARBAND_GETRECHARGEMESSAGE_TOTALRECHARGECOUNT_FIELD.has_default_value = false
pb.SC_WARBAND_GETRECHARGEMESSAGE_TOTALRECHARGECOUNT_FIELD.default_value = 0
pb.SC_WARBAND_GETRECHARGEMESSAGE_TOTALRECHARGECOUNT_FIELD.type = 5
pb.SC_WARBAND_GETRECHARGEMESSAGE_TOTALRECHARGECOUNT_FIELD.cpp_type = 1

pb.SC_WARBAND_GETRECHARGEMESSAGE_TOTALRECHARGEPRIZECOUNT_FIELD.name = "TotalRechargePrizeCount"
pb.SC_WARBAND_GETRECHARGEMESSAGE_TOTALRECHARGEPRIZECOUNT_FIELD.full_name = ".SC_Warband_GetRechargeMessage.TotalRechargePrizeCount"
pb.SC_WARBAND_GETRECHARGEMESSAGE_TOTALRECHARGEPRIZECOUNT_FIELD.number = 5
pb.SC_WARBAND_GETRECHARGEMESSAGE_TOTALRECHARGEPRIZECOUNT_FIELD.index = 4
pb.SC_WARBAND_GETRECHARGEMESSAGE_TOTALRECHARGEPRIZECOUNT_FIELD.label = 1
pb.SC_WARBAND_GETRECHARGEMESSAGE_TOTALRECHARGEPRIZECOUNT_FIELD.has_default_value = false
pb.SC_WARBAND_GETRECHARGEMESSAGE_TOTALRECHARGEPRIZECOUNT_FIELD.default_value = 0
pb.SC_WARBAND_GETRECHARGEMESSAGE_TOTALRECHARGEPRIZECOUNT_FIELD.type = 5
pb.SC_WARBAND_GETRECHARGEMESSAGE_TOTALRECHARGEPRIZECOUNT_FIELD.cpp_type = 1

pb.SC_WARBAND_GETRECHARGEMESSAGE_TOTALRECHARGEDRAWCOUNT_FIELD.name = "TotalRechargeDrawCount"
pb.SC_WARBAND_GETRECHARGEMESSAGE_TOTALRECHARGEDRAWCOUNT_FIELD.full_name = ".SC_Warband_GetRechargeMessage.TotalRechargeDrawCount"
pb.SC_WARBAND_GETRECHARGEMESSAGE_TOTALRECHARGEDRAWCOUNT_FIELD.number = 6
pb.SC_WARBAND_GETRECHARGEMESSAGE_TOTALRECHARGEDRAWCOUNT_FIELD.index = 5
pb.SC_WARBAND_GETRECHARGEMESSAGE_TOTALRECHARGEDRAWCOUNT_FIELD.label = 1
pb.SC_WARBAND_GETRECHARGEMESSAGE_TOTALRECHARGEDRAWCOUNT_FIELD.has_default_value = false
pb.SC_WARBAND_GETRECHARGEMESSAGE_TOTALRECHARGEDRAWCOUNT_FIELD.default_value = 0
pb.SC_WARBAND_GETRECHARGEMESSAGE_TOTALRECHARGEDRAWCOUNT_FIELD.type = 5
pb.SC_WARBAND_GETRECHARGEMESSAGE_TOTALRECHARGEDRAWCOUNT_FIELD.cpp_type = 1

pb.SC_WARBAND_GETRECHARGEMESSAGE_ISFIRSTDAYJOIN_FIELD.name = "IsFirstDayJoin"
pb.SC_WARBAND_GETRECHARGEMESSAGE_ISFIRSTDAYJOIN_FIELD.full_name = ".SC_Warband_GetRechargeMessage.IsFirstDayJoin"
pb.SC_WARBAND_GETRECHARGEMESSAGE_ISFIRSTDAYJOIN_FIELD.number = 7
pb.SC_WARBAND_GETRECHARGEMESSAGE_ISFIRSTDAYJOIN_FIELD.index = 6
pb.SC_WARBAND_GETRECHARGEMESSAGE_ISFIRSTDAYJOIN_FIELD.label = 1
pb.SC_WARBAND_GETRECHARGEMESSAGE_ISFIRSTDAYJOIN_FIELD.has_default_value = false
pb.SC_WARBAND_GETRECHARGEMESSAGE_ISFIRSTDAYJOIN_FIELD.default_value = 0
pb.SC_WARBAND_GETRECHARGEMESSAGE_ISFIRSTDAYJOIN_FIELD.type = 5
pb.SC_WARBAND_GETRECHARGEMESSAGE_ISFIRSTDAYJOIN_FIELD.cpp_type = 1

pb.SC_WARBAND_GETRECHARGEMESSAGE_TOTALRECHARGEPRIZECOUNTNEXT_FIELD.name = "TotalRechargePrizeCountNext"
pb.SC_WARBAND_GETRECHARGEMESSAGE_TOTALRECHARGEPRIZECOUNTNEXT_FIELD.full_name = ".SC_Warband_GetRechargeMessage.TotalRechargePrizeCountNext"
pb.SC_WARBAND_GETRECHARGEMESSAGE_TOTALRECHARGEPRIZECOUNTNEXT_FIELD.number = 8
pb.SC_WARBAND_GETRECHARGEMESSAGE_TOTALRECHARGEPRIZECOUNTNEXT_FIELD.index = 7
pb.SC_WARBAND_GETRECHARGEMESSAGE_TOTALRECHARGEPRIZECOUNTNEXT_FIELD.label = 1
pb.SC_WARBAND_GETRECHARGEMESSAGE_TOTALRECHARGEPRIZECOUNTNEXT_FIELD.has_default_value = false
pb.SC_WARBAND_GETRECHARGEMESSAGE_TOTALRECHARGEPRIZECOUNTNEXT_FIELD.default_value = 0
pb.SC_WARBAND_GETRECHARGEMESSAGE_TOTALRECHARGEPRIZECOUNTNEXT_FIELD.type = 5
pb.SC_WARBAND_GETRECHARGEMESSAGE_TOTALRECHARGEPRIZECOUNTNEXT_FIELD.cpp_type = 1

pb.SC_WARBAND_GETRECHARGEMESSAGE.name = "SC_Warband_GetRechargeMessage"
pb.SC_WARBAND_GETRECHARGEMESSAGE.full_name = ".SC_Warband_GetRechargeMessage"
pb.SC_WARBAND_GETRECHARGEMESSAGE.nested_types = {}
pb.SC_WARBAND_GETRECHARGEMESSAGE.enum_types = {}
pb.SC_WARBAND_GETRECHARGEMESSAGE.fields = {pb.SC_WARBAND_GETRECHARGEMESSAGE_RESULT_FIELD, pb.SC_WARBAND_GETRECHARGEMESSAGE_WARBANDGIFTPRIZECOUNT_FIELD, pb.SC_WARBAND_GETRECHARGEMESSAGE_RECHARGECOUNT_FIELD, pb.SC_WARBAND_GETRECHARGEMESSAGE_TOTALRECHARGECOUNT_FIELD, pb.SC_WARBAND_GETRECHARGEMESSAGE_TOTALRECHARGEPRIZECOUNT_FIELD, pb.SC_WARBAND_GETRECHARGEMESSAGE_TOTALRECHARGEDRAWCOUNT_FIELD, pb.SC_WARBAND_GETRECHARGEMESSAGE_ISFIRSTDAYJOIN_FIELD, pb.SC_WARBAND_GETRECHARGEMESSAGE_TOTALRECHARGEPRIZECOUNTNEXT_FIELD}
pb.SC_WARBAND_GETRECHARGEMESSAGE.is_extendable = false
pb.SC_WARBAND_GETRECHARGEMESSAGE.extensions = {}
pb.CS_WARBAND_INVITEJOIN_ACTORID_FIELD.name = "ActorID"
pb.CS_WARBAND_INVITEJOIN_ACTORID_FIELD.full_name = ".CS_Warband_InviteJoin.ActorID"
pb.CS_WARBAND_INVITEJOIN_ACTORID_FIELD.number = 1
pb.CS_WARBAND_INVITEJOIN_ACTORID_FIELD.index = 0
pb.CS_WARBAND_INVITEJOIN_ACTORID_FIELD.label = 1
pb.CS_WARBAND_INVITEJOIN_ACTORID_FIELD.has_default_value = false
pb.CS_WARBAND_INVITEJOIN_ACTORID_FIELD.default_value = 0
pb.CS_WARBAND_INVITEJOIN_ACTORID_FIELD.type = 13
pb.CS_WARBAND_INVITEJOIN_ACTORID_FIELD.cpp_type = 3

pb.CS_WARBAND_INVITEJOIN.name = "CS_Warband_InviteJoin"
pb.CS_WARBAND_INVITEJOIN.full_name = ".CS_Warband_InviteJoin"
pb.CS_WARBAND_INVITEJOIN.nested_types = {}
pb.CS_WARBAND_INVITEJOIN.enum_types = {}
pb.CS_WARBAND_INVITEJOIN.fields = {pb.CS_WARBAND_INVITEJOIN_ACTORID_FIELD}
pb.CS_WARBAND_INVITEJOIN.is_extendable = false
pb.CS_WARBAND_INVITEJOIN.extensions = {}
pb.SC_WARBAND_INVITEJOIN_ACTORID_FIELD.name = "ActorID"
pb.SC_WARBAND_INVITEJOIN_ACTORID_FIELD.full_name = ".SC_Warband_InviteJoin.ActorID"
pb.SC_WARBAND_INVITEJOIN_ACTORID_FIELD.number = 1
pb.SC_WARBAND_INVITEJOIN_ACTORID_FIELD.index = 0
pb.SC_WARBAND_INVITEJOIN_ACTORID_FIELD.label = 1
pb.SC_WARBAND_INVITEJOIN_ACTORID_FIELD.has_default_value = false
pb.SC_WARBAND_INVITEJOIN_ACTORID_FIELD.default_value = 0
pb.SC_WARBAND_INVITEJOIN_ACTORID_FIELD.type = 13
pb.SC_WARBAND_INVITEJOIN_ACTORID_FIELD.cpp_type = 3

pb.SC_WARBAND_INVITEJOIN_ACTORNAME_FIELD.name = "ActorName"
pb.SC_WARBAND_INVITEJOIN_ACTORNAME_FIELD.full_name = ".SC_Warband_InviteJoin.ActorName"
pb.SC_WARBAND_INVITEJOIN_ACTORNAME_FIELD.number = 2
pb.SC_WARBAND_INVITEJOIN_ACTORNAME_FIELD.index = 1
pb.SC_WARBAND_INVITEJOIN_ACTORNAME_FIELD.label = 1
pb.SC_WARBAND_INVITEJOIN_ACTORNAME_FIELD.has_default_value = false
pb.SC_WARBAND_INVITEJOIN_ACTORNAME_FIELD.default_value = ""
pb.SC_WARBAND_INVITEJOIN_ACTORNAME_FIELD.type = 9
pb.SC_WARBAND_INVITEJOIN_ACTORNAME_FIELD.cpp_type = 9

pb.SC_WARBAND_INVITEJOIN_WARBANDID_FIELD.name = "WarbandID"
pb.SC_WARBAND_INVITEJOIN_WARBANDID_FIELD.full_name = ".SC_Warband_InviteJoin.WarbandID"
pb.SC_WARBAND_INVITEJOIN_WARBANDID_FIELD.number = 3
pb.SC_WARBAND_INVITEJOIN_WARBANDID_FIELD.index = 2
pb.SC_WARBAND_INVITEJOIN_WARBANDID_FIELD.label = 1
pb.SC_WARBAND_INVITEJOIN_WARBANDID_FIELD.has_default_value = false
pb.SC_WARBAND_INVITEJOIN_WARBANDID_FIELD.default_value = 0
pb.SC_WARBAND_INVITEJOIN_WARBANDID_FIELD.type = 13
pb.SC_WARBAND_INVITEJOIN_WARBANDID_FIELD.cpp_type = 3

pb.SC_WARBAND_INVITEJOIN_WARBANDNAME_FIELD.name = "WarbandName"
pb.SC_WARBAND_INVITEJOIN_WARBANDNAME_FIELD.full_name = ".SC_Warband_InviteJoin.WarbandName"
pb.SC_WARBAND_INVITEJOIN_WARBANDNAME_FIELD.number = 4
pb.SC_WARBAND_INVITEJOIN_WARBANDNAME_FIELD.index = 3
pb.SC_WARBAND_INVITEJOIN_WARBANDNAME_FIELD.label = 1
pb.SC_WARBAND_INVITEJOIN_WARBANDNAME_FIELD.has_default_value = false
pb.SC_WARBAND_INVITEJOIN_WARBANDNAME_FIELD.default_value = ""
pb.SC_WARBAND_INVITEJOIN_WARBANDNAME_FIELD.type = 9
pb.SC_WARBAND_INVITEJOIN_WARBANDNAME_FIELD.cpp_type = 9

pb.SC_WARBAND_INVITEJOIN.name = "SC_Warband_InviteJoin"
pb.SC_WARBAND_INVITEJOIN.full_name = ".SC_Warband_InviteJoin"
pb.SC_WARBAND_INVITEJOIN.nested_types = {}
pb.SC_WARBAND_INVITEJOIN.enum_types = {}
pb.SC_WARBAND_INVITEJOIN.fields = {pb.SC_WARBAND_INVITEJOIN_ACTORID_FIELD, pb.SC_WARBAND_INVITEJOIN_ACTORNAME_FIELD, pb.SC_WARBAND_INVITEJOIN_WARBANDID_FIELD, pb.SC_WARBAND_INVITEJOIN_WARBANDNAME_FIELD}
pb.SC_WARBAND_INVITEJOIN.is_extendable = false
pb.SC_WARBAND_INVITEJOIN.extensions = {}
pb.SC_WARBAND_PRIZENOTICE_PRIZETYPE_FIELD.name = "PrizeType"
pb.SC_WARBAND_PRIZENOTICE_PRIZETYPE_FIELD.full_name = ".SC_Warband_PrizeNotice.PrizeType"
pb.SC_WARBAND_PRIZENOTICE_PRIZETYPE_FIELD.number = 1
pb.SC_WARBAND_PRIZENOTICE_PRIZETYPE_FIELD.index = 0
pb.SC_WARBAND_PRIZENOTICE_PRIZETYPE_FIELD.label = 1
pb.SC_WARBAND_PRIZENOTICE_PRIZETYPE_FIELD.has_default_value = false
pb.SC_WARBAND_PRIZENOTICE_PRIZETYPE_FIELD.default_value = 0
pb.SC_WARBAND_PRIZENOTICE_PRIZETYPE_FIELD.type = 5
pb.SC_WARBAND_PRIZENOTICE_PRIZETYPE_FIELD.cpp_type = 1

pb.SC_WARBAND_PRIZENOTICE_PARAM1_FIELD.name = "Param1"
pb.SC_WARBAND_PRIZENOTICE_PARAM1_FIELD.full_name = ".SC_Warband_PrizeNotice.Param1"
pb.SC_WARBAND_PRIZENOTICE_PARAM1_FIELD.number = 2
pb.SC_WARBAND_PRIZENOTICE_PARAM1_FIELD.index = 1
pb.SC_WARBAND_PRIZENOTICE_PARAM1_FIELD.label = 1
pb.SC_WARBAND_PRIZENOTICE_PARAM1_FIELD.has_default_value = false
pb.SC_WARBAND_PRIZENOTICE_PARAM1_FIELD.default_value = 0
pb.SC_WARBAND_PRIZENOTICE_PARAM1_FIELD.type = 5
pb.SC_WARBAND_PRIZENOTICE_PARAM1_FIELD.cpp_type = 1

pb.SC_WARBAND_PRIZENOTICE_PARAM2_FIELD.name = "Param2"
pb.SC_WARBAND_PRIZENOTICE_PARAM2_FIELD.full_name = ".SC_Warband_PrizeNotice.Param2"
pb.SC_WARBAND_PRIZENOTICE_PARAM2_FIELD.number = 3
pb.SC_WARBAND_PRIZENOTICE_PARAM2_FIELD.index = 2
pb.SC_WARBAND_PRIZENOTICE_PARAM2_FIELD.label = 1
pb.SC_WARBAND_PRIZENOTICE_PARAM2_FIELD.has_default_value = false
pb.SC_WARBAND_PRIZENOTICE_PARAM2_FIELD.default_value = 0
pb.SC_WARBAND_PRIZENOTICE_PARAM2_FIELD.type = 5
pb.SC_WARBAND_PRIZENOTICE_PARAM2_FIELD.cpp_type = 1

pb.SC_WARBAND_PRIZENOTICE.name = "SC_Warband_PrizeNotice"
pb.SC_WARBAND_PRIZENOTICE.full_name = ".SC_Warband_PrizeNotice"
pb.SC_WARBAND_PRIZENOTICE.nested_types = {}
pb.SC_WARBAND_PRIZENOTICE.enum_types = {}
pb.SC_WARBAND_PRIZENOTICE.fields = {pb.SC_WARBAND_PRIZENOTICE_PRIZETYPE_FIELD, pb.SC_WARBAND_PRIZENOTICE_PARAM1_FIELD, pb.SC_WARBAND_PRIZENOTICE_PARAM2_FIELD}
pb.SC_WARBAND_PRIZENOTICE.is_extendable = false
pb.SC_WARBAND_PRIZENOTICE.extensions = {}
pb.SC_WARBAND_OPENRANDOMECTYPENOTICE_CONFIGID_FIELD.name = "ConfigID"
pb.SC_WARBAND_OPENRANDOMECTYPENOTICE_CONFIGID_FIELD.full_name = ".SC_Warband_OpenRandomEctypeNotice.ConfigID"
pb.SC_WARBAND_OPENRANDOMECTYPENOTICE_CONFIGID_FIELD.number = 1
pb.SC_WARBAND_OPENRANDOMECTYPENOTICE_CONFIGID_FIELD.index = 0
pb.SC_WARBAND_OPENRANDOMECTYPENOTICE_CONFIGID_FIELD.label = 1
pb.SC_WARBAND_OPENRANDOMECTYPENOTICE_CONFIGID_FIELD.has_default_value = false
pb.SC_WARBAND_OPENRANDOMECTYPENOTICE_CONFIGID_FIELD.default_value = 0
pb.SC_WARBAND_OPENRANDOMECTYPENOTICE_CONFIGID_FIELD.type = 5
pb.SC_WARBAND_OPENRANDOMECTYPENOTICE_CONFIGID_FIELD.cpp_type = 1

pb.SC_WARBAND_OPENRANDOMECTYPENOTICE_BEGINCANOPENTIME_FIELD.name = "BeginCanOpenTime"
pb.SC_WARBAND_OPENRANDOMECTYPENOTICE_BEGINCANOPENTIME_FIELD.full_name = ".SC_Warband_OpenRandomEctypeNotice.BeginCanOpenTime"
pb.SC_WARBAND_OPENRANDOMECTYPENOTICE_BEGINCANOPENTIME_FIELD.number = 2
pb.SC_WARBAND_OPENRANDOMECTYPENOTICE_BEGINCANOPENTIME_FIELD.index = 1
pb.SC_WARBAND_OPENRANDOMECTYPENOTICE_BEGINCANOPENTIME_FIELD.label = 1
pb.SC_WARBAND_OPENRANDOMECTYPENOTICE_BEGINCANOPENTIME_FIELD.has_default_value = false
pb.SC_WARBAND_OPENRANDOMECTYPENOTICE_BEGINCANOPENTIME_FIELD.default_value = 0
pb.SC_WARBAND_OPENRANDOMECTYPENOTICE_BEGINCANOPENTIME_FIELD.type = 5
pb.SC_WARBAND_OPENRANDOMECTYPENOTICE_BEGINCANOPENTIME_FIELD.cpp_type = 1

pb.SC_WARBAND_OPENRANDOMECTYPENOTICE.name = "SC_Warband_OpenRandomEctypeNotice"
pb.SC_WARBAND_OPENRANDOMECTYPENOTICE.full_name = ".SC_Warband_OpenRandomEctypeNotice"
pb.SC_WARBAND_OPENRANDOMECTYPENOTICE.nested_types = {}
pb.SC_WARBAND_OPENRANDOMECTYPENOTICE.enum_types = {}
pb.SC_WARBAND_OPENRANDOMECTYPENOTICE.fields = {pb.SC_WARBAND_OPENRANDOMECTYPENOTICE_CONFIGID_FIELD, pb.SC_WARBAND_OPENRANDOMECTYPENOTICE_BEGINCANOPENTIME_FIELD}
pb.SC_WARBAND_OPENRANDOMECTYPENOTICE.is_extendable = false
pb.SC_WARBAND_OPENRANDOMECTYPENOTICE.extensions = {}
pb.SC_WARBAND_OPENRANDOMECTYPE_RESULT_FIELD.name = "Result"
pb.SC_WARBAND_OPENRANDOMECTYPE_RESULT_FIELD.full_name = ".SC_Warband_OpenRandomEctype.Result"
pb.SC_WARBAND_OPENRANDOMECTYPE_RESULT_FIELD.number = 1
pb.SC_WARBAND_OPENRANDOMECTYPE_RESULT_FIELD.index = 0
pb.SC_WARBAND_OPENRANDOMECTYPE_RESULT_FIELD.label = 1
pb.SC_WARBAND_OPENRANDOMECTYPE_RESULT_FIELD.has_default_value = false
pb.SC_WARBAND_OPENRANDOMECTYPE_RESULT_FIELD.default_value = 0
pb.SC_WARBAND_OPENRANDOMECTYPE_RESULT_FIELD.type = 5
pb.SC_WARBAND_OPENRANDOMECTYPE_RESULT_FIELD.cpp_type = 1

pb.SC_WARBAND_OPENRANDOMECTYPE.name = "SC_Warband_OpenRandomEctype"
pb.SC_WARBAND_OPENRANDOMECTYPE.full_name = ".SC_Warband_OpenRandomEctype"
pb.SC_WARBAND_OPENRANDOMECTYPE.nested_types = {}
pb.SC_WARBAND_OPENRANDOMECTYPE.enum_types = {}
pb.SC_WARBAND_OPENRANDOMECTYPE.fields = {pb.SC_WARBAND_OPENRANDOMECTYPE_RESULT_FIELD}
pb.SC_WARBAND_OPENRANDOMECTYPE.is_extendable = false
pb.SC_WARBAND_OPENRANDOMECTYPE.extensions = {}
pb.SC_WARBAND_ENTERRANDOMECTYPE_RESULT_FIELD.name = "Result"
pb.SC_WARBAND_ENTERRANDOMECTYPE_RESULT_FIELD.full_name = ".SC_Warband_EnterRandomEctype.Result"
pb.SC_WARBAND_ENTERRANDOMECTYPE_RESULT_FIELD.number = 1
pb.SC_WARBAND_ENTERRANDOMECTYPE_RESULT_FIELD.index = 0
pb.SC_WARBAND_ENTERRANDOMECTYPE_RESULT_FIELD.label = 1
pb.SC_WARBAND_ENTERRANDOMECTYPE_RESULT_FIELD.has_default_value = false
pb.SC_WARBAND_ENTERRANDOMECTYPE_RESULT_FIELD.default_value = 0
pb.SC_WARBAND_ENTERRANDOMECTYPE_RESULT_FIELD.type = 5
pb.SC_WARBAND_ENTERRANDOMECTYPE_RESULT_FIELD.cpp_type = 1

pb.SC_WARBAND_ENTERRANDOMECTYPE.name = "SC_Warband_EnterRandomEctype"
pb.SC_WARBAND_ENTERRANDOMECTYPE.full_name = ".SC_Warband_EnterRandomEctype"
pb.SC_WARBAND_ENTERRANDOMECTYPE.nested_types = {}
pb.SC_WARBAND_ENTERRANDOMECTYPE.enum_types = {}
pb.SC_WARBAND_ENTERRANDOMECTYPE.fields = {pb.SC_WARBAND_ENTERRANDOMECTYPE_RESULT_FIELD}
pb.SC_WARBAND_ENTERRANDOMECTYPE.is_extendable = false
pb.SC_WARBAND_ENTERRANDOMECTYPE.extensions = {}
pb.SC_WARBAND_RANDOMECTYPERECORD_RECORDINFO_CONFIGID_FIELD.name = "ConfigID"
pb.SC_WARBAND_RANDOMECTYPERECORD_RECORDINFO_CONFIGID_FIELD.full_name = ".SC_Warband_RandomEctypeRecord.RecordInfo.ConfigID"
pb.SC_WARBAND_RANDOMECTYPERECORD_RECORDINFO_CONFIGID_FIELD.number = 1
pb.SC_WARBAND_RANDOMECTYPERECORD_RECORDINFO_CONFIGID_FIELD.index = 0
pb.SC_WARBAND_RANDOMECTYPERECORD_RECORDINFO_CONFIGID_FIELD.label = 1
pb.SC_WARBAND_RANDOMECTYPERECORD_RECORDINFO_CONFIGID_FIELD.has_default_value = false
pb.SC_WARBAND_RANDOMECTYPERECORD_RECORDINFO_CONFIGID_FIELD.default_value = 0
pb.SC_WARBAND_RANDOMECTYPERECORD_RECORDINFO_CONFIGID_FIELD.type = 5
pb.SC_WARBAND_RANDOMECTYPERECORD_RECORDINFO_CONFIGID_FIELD.cpp_type = 1

pb.SC_WARBAND_RANDOMECTYPERECORD_RECORDINFO_STATUS_FIELD.name = "Status"
pb.SC_WARBAND_RANDOMECTYPERECORD_RECORDINFO_STATUS_FIELD.full_name = ".SC_Warband_RandomEctypeRecord.RecordInfo.Status"
pb.SC_WARBAND_RANDOMECTYPERECORD_RECORDINFO_STATUS_FIELD.number = 2
pb.SC_WARBAND_RANDOMECTYPERECORD_RECORDINFO_STATUS_FIELD.index = 1
pb.SC_WARBAND_RANDOMECTYPERECORD_RECORDINFO_STATUS_FIELD.label = 1
pb.SC_WARBAND_RANDOMECTYPERECORD_RECORDINFO_STATUS_FIELD.has_default_value = false
pb.SC_WARBAND_RANDOMECTYPERECORD_RECORDINFO_STATUS_FIELD.default_value = 0
pb.SC_WARBAND_RANDOMECTYPERECORD_RECORDINFO_STATUS_FIELD.type = 5
pb.SC_WARBAND_RANDOMECTYPERECORD_RECORDINFO_STATUS_FIELD.cpp_type = 1

pb.SC_WARBAND_RANDOMECTYPERECORD_RECORDINFO_ACTORID_FIELD.name = "ActorID"
pb.SC_WARBAND_RANDOMECTYPERECORD_RECORDINFO_ACTORID_FIELD.full_name = ".SC_Warband_RandomEctypeRecord.RecordInfo.ActorID"
pb.SC_WARBAND_RANDOMECTYPERECORD_RECORDINFO_ACTORID_FIELD.number = 3
pb.SC_WARBAND_RANDOMECTYPERECORD_RECORDINFO_ACTORID_FIELD.index = 2
pb.SC_WARBAND_RANDOMECTYPERECORD_RECORDINFO_ACTORID_FIELD.label = 1
pb.SC_WARBAND_RANDOMECTYPERECORD_RECORDINFO_ACTORID_FIELD.has_default_value = false
pb.SC_WARBAND_RANDOMECTYPERECORD_RECORDINFO_ACTORID_FIELD.default_value = 0
pb.SC_WARBAND_RANDOMECTYPERECORD_RECORDINFO_ACTORID_FIELD.type = 5
pb.SC_WARBAND_RANDOMECTYPERECORD_RECORDINFO_ACTORID_FIELD.cpp_type = 1

pb.SC_WARBAND_RANDOMECTYPERECORD_RECORDINFO_ACTORNAME_FIELD.name = "ActorName"
pb.SC_WARBAND_RANDOMECTYPERECORD_RECORDINFO_ACTORNAME_FIELD.full_name = ".SC_Warband_RandomEctypeRecord.RecordInfo.ActorName"
pb.SC_WARBAND_RANDOMECTYPERECORD_RECORDINFO_ACTORNAME_FIELD.number = 4
pb.SC_WARBAND_RANDOMECTYPERECORD_RECORDINFO_ACTORNAME_FIELD.index = 3
pb.SC_WARBAND_RANDOMECTYPERECORD_RECORDINFO_ACTORNAME_FIELD.label = 1
pb.SC_WARBAND_RANDOMECTYPERECORD_RECORDINFO_ACTORNAME_FIELD.has_default_value = false
pb.SC_WARBAND_RANDOMECTYPERECORD_RECORDINFO_ACTORNAME_FIELD.default_value = ""
pb.SC_WARBAND_RANDOMECTYPERECORD_RECORDINFO_ACTORNAME_FIELD.type = 9
pb.SC_WARBAND_RANDOMECTYPERECORD_RECORDINFO_ACTORNAME_FIELD.cpp_type = 9

pb.SC_WARBAND_RANDOMECTYPERECORD_RECORDINFO_ACTORVOCATION_FIELD.name = "ActorVocation"
pb.SC_WARBAND_RANDOMECTYPERECORD_RECORDINFO_ACTORVOCATION_FIELD.full_name = ".SC_Warband_RandomEctypeRecord.RecordInfo.ActorVocation"
pb.SC_WARBAND_RANDOMECTYPERECORD_RECORDINFO_ACTORVOCATION_FIELD.number = 5
pb.SC_WARBAND_RANDOMECTYPERECORD_RECORDINFO_ACTORVOCATION_FIELD.index = 4
pb.SC_WARBAND_RANDOMECTYPERECORD_RECORDINFO_ACTORVOCATION_FIELD.label = 1
pb.SC_WARBAND_RANDOMECTYPERECORD_RECORDINFO_ACTORVOCATION_FIELD.has_default_value = false
pb.SC_WARBAND_RANDOMECTYPERECORD_RECORDINFO_ACTORVOCATION_FIELD.default_value = 0
pb.SC_WARBAND_RANDOMECTYPERECORD_RECORDINFO_ACTORVOCATION_FIELD.type = 5
pb.SC_WARBAND_RANDOMECTYPERECORD_RECORDINFO_ACTORVOCATION_FIELD.cpp_type = 1

pb.SC_WARBAND_RANDOMECTYPERECORD_RECORDINFO.name = "RecordInfo"
pb.SC_WARBAND_RANDOMECTYPERECORD_RECORDINFO.full_name = ".SC_Warband_RandomEctypeRecord.RecordInfo"
pb.SC_WARBAND_RANDOMECTYPERECORD_RECORDINFO.nested_types = {}
pb.SC_WARBAND_RANDOMECTYPERECORD_RECORDINFO.enum_types = {}
pb.SC_WARBAND_RANDOMECTYPERECORD_RECORDINFO.fields = {pb.SC_WARBAND_RANDOMECTYPERECORD_RECORDINFO_CONFIGID_FIELD, pb.SC_WARBAND_RANDOMECTYPERECORD_RECORDINFO_STATUS_FIELD, pb.SC_WARBAND_RANDOMECTYPERECORD_RECORDINFO_ACTORID_FIELD, pb.SC_WARBAND_RANDOMECTYPERECORD_RECORDINFO_ACTORNAME_FIELD, pb.SC_WARBAND_RANDOMECTYPERECORD_RECORDINFO_ACTORVOCATION_FIELD}
pb.SC_WARBAND_RANDOMECTYPERECORD_RECORDINFO.is_extendable = false
pb.SC_WARBAND_RANDOMECTYPERECORD_RECORDINFO.extensions = {}
pb.SC_WARBAND_RANDOMECTYPERECORD_RECORDINFO.containing_type = pb.SC_WARBAND_RANDOMECTYPERECORD
pb.SC_WARBAND_RANDOMECTYPERECORD_RECORDLIST_FIELD.name = "RecordList"
pb.SC_WARBAND_RANDOMECTYPERECORD_RECORDLIST_FIELD.full_name = ".SC_Warband_RandomEctypeRecord.RecordList"
pb.SC_WARBAND_RANDOMECTYPERECORD_RECORDLIST_FIELD.number = 1
pb.SC_WARBAND_RANDOMECTYPERECORD_RECORDLIST_FIELD.index = 0
pb.SC_WARBAND_RANDOMECTYPERECORD_RECORDLIST_FIELD.label = 3
pb.SC_WARBAND_RANDOMECTYPERECORD_RECORDLIST_FIELD.has_default_value = false
pb.SC_WARBAND_RANDOMECTYPERECORD_RECORDLIST_FIELD.default_value = {}
pb.SC_WARBAND_RANDOMECTYPERECORD_RECORDLIST_FIELD.message_type = pb.SC_WARBAND_RANDOMECTYPERECORD_RECORDINFO
pb.SC_WARBAND_RANDOMECTYPERECORD_RECORDLIST_FIELD.type = 11
pb.SC_WARBAND_RANDOMECTYPERECORD_RECORDLIST_FIELD.cpp_type = 10

pb.SC_WARBAND_RANDOMECTYPERECORD.name = "SC_Warband_RandomEctypeRecord"
pb.SC_WARBAND_RANDOMECTYPERECORD.full_name = ".SC_Warband_RandomEctypeRecord"
pb.SC_WARBAND_RANDOMECTYPERECORD.nested_types = {pb.SC_WARBAND_RANDOMECTYPERECORD_RECORDINFO}
pb.SC_WARBAND_RANDOMECTYPERECORD.enum_types = {}
pb.SC_WARBAND_RANDOMECTYPERECORD.fields = {pb.SC_WARBAND_RANDOMECTYPERECORD_RECORDLIST_FIELD}
pb.SC_WARBAND_RANDOMECTYPERECORD.is_extendable = false
pb.SC_WARBAND_RANDOMECTYPERECORD.extensions = {}
pb.SC_WARBAND_INVITEFOLLOW_ACTORID_FIELD.name = "ActorID"
pb.SC_WARBAND_INVITEFOLLOW_ACTORID_FIELD.full_name = ".SC_Warband_InviteFollow.ActorID"
pb.SC_WARBAND_INVITEFOLLOW_ACTORID_FIELD.number = 1
pb.SC_WARBAND_INVITEFOLLOW_ACTORID_FIELD.index = 0
pb.SC_WARBAND_INVITEFOLLOW_ACTORID_FIELD.label = 1
pb.SC_WARBAND_INVITEFOLLOW_ACTORID_FIELD.has_default_value = false
pb.SC_WARBAND_INVITEFOLLOW_ACTORID_FIELD.default_value = 0
pb.SC_WARBAND_INVITEFOLLOW_ACTORID_FIELD.type = 5
pb.SC_WARBAND_INVITEFOLLOW_ACTORID_FIELD.cpp_type = 1

pb.SC_WARBAND_INVITEFOLLOW.name = "SC_Warband_InviteFollow"
pb.SC_WARBAND_INVITEFOLLOW.full_name = ".SC_Warband_InviteFollow"
pb.SC_WARBAND_INVITEFOLLOW.nested_types = {}
pb.SC_WARBAND_INVITEFOLLOW.enum_types = {}
pb.SC_WARBAND_INVITEFOLLOW.fields = {pb.SC_WARBAND_INVITEFOLLOW_ACTORID_FIELD}
pb.SC_WARBAND_INVITEFOLLOW.is_extendable = false
pb.SC_WARBAND_INVITEFOLLOW.extensions = {}
pb.CS_WARBAND_FIND_WARBANDNAME_FIELD.name = "WarbandName"
pb.CS_WARBAND_FIND_WARBANDNAME_FIELD.full_name = ".CS_Warband_Find.WarbandName"
pb.CS_WARBAND_FIND_WARBANDNAME_FIELD.number = 1
pb.CS_WARBAND_FIND_WARBANDNAME_FIELD.index = 0
pb.CS_WARBAND_FIND_WARBANDNAME_FIELD.label = 2
pb.CS_WARBAND_FIND_WARBANDNAME_FIELD.has_default_value = false
pb.CS_WARBAND_FIND_WARBANDNAME_FIELD.default_value = ""
pb.CS_WARBAND_FIND_WARBANDNAME_FIELD.type = 9
pb.CS_WARBAND_FIND_WARBANDNAME_FIELD.cpp_type = 9

pb.CS_WARBAND_FIND.name = "CS_Warband_Find"
pb.CS_WARBAND_FIND.full_name = ".CS_Warband_Find"
pb.CS_WARBAND_FIND.nested_types = {}
pb.CS_WARBAND_FIND.enum_types = {}
pb.CS_WARBAND_FIND.fields = {pb.CS_WARBAND_FIND_WARBANDNAME_FIELD}
pb.CS_WARBAND_FIND.is_extendable = false
pb.CS_WARBAND_FIND.extensions = {}
pb.SC_WARBAND_FIND_WARBANDID_FIELD.name = "WarbandID"
pb.SC_WARBAND_FIND_WARBANDID_FIELD.full_name = ".SC_Warband_Find.WarbandID"
pb.SC_WARBAND_FIND_WARBANDID_FIELD.number = 1
pb.SC_WARBAND_FIND_WARBANDID_FIELD.index = 0
pb.SC_WARBAND_FIND_WARBANDID_FIELD.label = 1
pb.SC_WARBAND_FIND_WARBANDID_FIELD.has_default_value = false
pb.SC_WARBAND_FIND_WARBANDID_FIELD.default_value = 0
pb.SC_WARBAND_FIND_WARBANDID_FIELD.type = 13
pb.SC_WARBAND_FIND_WARBANDID_FIELD.cpp_type = 3

pb.SC_WARBAND_FIND_WARBANDNAME_FIELD.name = "WarbandName"
pb.SC_WARBAND_FIND_WARBANDNAME_FIELD.full_name = ".SC_Warband_Find.WarbandName"
pb.SC_WARBAND_FIND_WARBANDNAME_FIELD.number = 2
pb.SC_WARBAND_FIND_WARBANDNAME_FIELD.index = 1
pb.SC_WARBAND_FIND_WARBANDNAME_FIELD.label = 1
pb.SC_WARBAND_FIND_WARBANDNAME_FIELD.has_default_value = false
pb.SC_WARBAND_FIND_WARBANDNAME_FIELD.default_value = ""
pb.SC_WARBAND_FIND_WARBANDNAME_FIELD.type = 9
pb.SC_WARBAND_FIND_WARBANDNAME_FIELD.cpp_type = 9

pb.SC_WARBAND_FIND_MEMBERNUM_FIELD.name = "MemberNum"
pb.SC_WARBAND_FIND_MEMBERNUM_FIELD.full_name = ".SC_Warband_Find.MemberNum"
pb.SC_WARBAND_FIND_MEMBERNUM_FIELD.number = 3
pb.SC_WARBAND_FIND_MEMBERNUM_FIELD.index = 2
pb.SC_WARBAND_FIND_MEMBERNUM_FIELD.label = 1
pb.SC_WARBAND_FIND_MEMBERNUM_FIELD.has_default_value = false
pb.SC_WARBAND_FIND_MEMBERNUM_FIELD.default_value = 0
pb.SC_WARBAND_FIND_MEMBERNUM_FIELD.type = 13
pb.SC_WARBAND_FIND_MEMBERNUM_FIELD.cpp_type = 3

pb.SC_WARBAND_FIND_LEVELLIMIT_FIELD.name = "LevelLimit"
pb.SC_WARBAND_FIND_LEVELLIMIT_FIELD.full_name = ".SC_Warband_Find.LevelLimit"
pb.SC_WARBAND_FIND_LEVELLIMIT_FIELD.number = 4
pb.SC_WARBAND_FIND_LEVELLIMIT_FIELD.index = 3
pb.SC_WARBAND_FIND_LEVELLIMIT_FIELD.label = 1
pb.SC_WARBAND_FIND_LEVELLIMIT_FIELD.has_default_value = false
pb.SC_WARBAND_FIND_LEVELLIMIT_FIELD.default_value = 0
pb.SC_WARBAND_FIND_LEVELLIMIT_FIELD.type = 13
pb.SC_WARBAND_FIND_LEVELLIMIT_FIELD.cpp_type = 3

pb.SC_WARBAND_FIND_JOINLIMIT_FIELD.name = "JoinLimit"
pb.SC_WARBAND_FIND_JOINLIMIT_FIELD.full_name = ".SC_Warband_Find.JoinLimit"
pb.SC_WARBAND_FIND_JOINLIMIT_FIELD.number = 5
pb.SC_WARBAND_FIND_JOINLIMIT_FIELD.index = 4
pb.SC_WARBAND_FIND_JOINLIMIT_FIELD.label = 1
pb.SC_WARBAND_FIND_JOINLIMIT_FIELD.has_default_value = false
pb.SC_WARBAND_FIND_JOINLIMIT_FIELD.default_value = 0
pb.SC_WARBAND_FIND_JOINLIMIT_FIELD.type = 13
pb.SC_WARBAND_FIND_JOINLIMIT_FIELD.cpp_type = 3

pb.SC_WARBAND_FIND_RESULT_FIELD.name = "Result"
pb.SC_WARBAND_FIND_RESULT_FIELD.full_name = ".SC_Warband_Find.Result"
pb.SC_WARBAND_FIND_RESULT_FIELD.number = 6
pb.SC_WARBAND_FIND_RESULT_FIELD.index = 5
pb.SC_WARBAND_FIND_RESULT_FIELD.label = 1
pb.SC_WARBAND_FIND_RESULT_FIELD.has_default_value = false
pb.SC_WARBAND_FIND_RESULT_FIELD.default_value = 0
pb.SC_WARBAND_FIND_RESULT_FIELD.type = 13
pb.SC_WARBAND_FIND_RESULT_FIELD.cpp_type = 3

pb.SC_WARBAND_FIND_COUNTRY_FIELD.name = "Country"
pb.SC_WARBAND_FIND_COUNTRY_FIELD.full_name = ".SC_Warband_Find.Country"
pb.SC_WARBAND_FIND_COUNTRY_FIELD.number = 7
pb.SC_WARBAND_FIND_COUNTRY_FIELD.index = 6
pb.SC_WARBAND_FIND_COUNTRY_FIELD.label = 1
pb.SC_WARBAND_FIND_COUNTRY_FIELD.has_default_value = false
pb.SC_WARBAND_FIND_COUNTRY_FIELD.default_value = 0
pb.SC_WARBAND_FIND_COUNTRY_FIELD.type = 13
pb.SC_WARBAND_FIND_COUNTRY_FIELD.cpp_type = 3

pb.SC_WARBAND_FIND.name = "SC_Warband_Find"
pb.SC_WARBAND_FIND.full_name = ".SC_Warband_Find"
pb.SC_WARBAND_FIND.nested_types = {}
pb.SC_WARBAND_FIND.enum_types = {}
pb.SC_WARBAND_FIND.fields = {pb.SC_WARBAND_FIND_WARBANDID_FIELD, pb.SC_WARBAND_FIND_WARBANDNAME_FIELD, pb.SC_WARBAND_FIND_MEMBERNUM_FIELD, pb.SC_WARBAND_FIND_LEVELLIMIT_FIELD, pb.SC_WARBAND_FIND_JOINLIMIT_FIELD, pb.SC_WARBAND_FIND_RESULT_FIELD, pb.SC_WARBAND_FIND_COUNTRY_FIELD}
pb.SC_WARBAND_FIND.is_extendable = false
pb.SC_WARBAND_FIND.extensions = {}

CS_Society_ApplyList = protobuf.Message(pb.CS_SOCIETY_APPLYLIST)
CS_Society_Appoint = protobuf.Message(pb.CS_SOCIETY_APPOINT)
CS_Society_Create = protobuf.Message(pb.CS_SOCIETY_CREATE)
CS_Society_Delete = protobuf.Message(pb.CS_SOCIETY_DELETE)
CS_Society_Find = protobuf.Message(pb.CS_SOCIETY_FIND)
CS_Society_GETEVENTINFOLIST = protobuf.Message(pb.CS_SOCIETY_GETEVENTINFOLIST)
CS_Society_GetList = protobuf.Message(pb.CS_SOCIETY_GETLIST)
CS_Society_GetName = protobuf.Message(pb.CS_SOCIETY_GETNAME)
CS_Society_Info = protobuf.Message(pb.CS_SOCIETY_INFO)
CS_Society_InviteJoin = protobuf.Message(pb.CS_SOCIETY_INVITEJOIN)
CS_Society_Join = protobuf.Message(pb.CS_SOCIETY_JOIN)
CS_Society_Kick = protobuf.Message(pb.CS_SOCIETY_KICK)
CS_Society_Leave = protobuf.Message(pb.CS_SOCIETY_LEAVE)
CS_Society_MemberList = protobuf.Message(pb.CS_SOCIETY_MEMBERLIST)
CS_Society_Red_Packet_Limit = protobuf.Message(pb.CS_SOCIETY_RED_PACKET_LIMIT)
CS_Society_Red_Packet_Open_History = protobuf.Message(pb.CS_SOCIETY_RED_PACKET_OPEN_HISTORY)
CS_Society_Red_Packet_PassWord = protobuf.Message(pb.CS_SOCIETY_RED_PACKET_PASSWORD)
CS_Society_Red_Packet_Rank = protobuf.Message(pb.CS_SOCIETY_RED_PACKET_RANK)
CS_Society_Red_Packet_Self_Rank = protobuf.Message(pb.CS_SOCIETY_RED_PACKET_SELF_RANK)
CS_Society_Red_Packet_Supper = protobuf.Message(pb.CS_SOCIETY_RED_PACKET_SUPPER)
CS_Society_Red_Packet_Transfer = protobuf.Message(pb.CS_SOCIETY_RED_PACKET_TRANSFER)
CS_Society_Succeed = protobuf.Message(pb.CS_SOCIETY_SUCCEED)
CS_Society_UpdateRedPacket = protobuf.Message(pb.CS_SOCIETY_UPDATEREDPACKET)
CS_Warband_ChangeLeader = protobuf.Message(pb.CS_WARBAND_CHANGELEADER)
CS_Warband_Create = protobuf.Message(pb.CS_WARBAND_CREATE)
CS_Warband_DealApply = protobuf.Message(pb.CS_WARBAND_DEALAPPLY)
CS_Warband_Find = protobuf.Message(pb.CS_WARBAND_FIND)
CS_Warband_GetList = protobuf.Message(pb.CS_WARBAND_GETLIST)
CS_Warband_GetRechargeHistory = protobuf.Message(pb.CS_WARBAND_GETRECHARGEHISTORY)
CS_Warband_InviteJoin = protobuf.Message(pb.CS_WARBAND_INVITEJOIN)
CS_Warband_Join = protobuf.Message(pb.CS_WARBAND_JOIN)
CS_Warband_KickMember = protobuf.Message(pb.CS_WARBAND_KICKMEMBER)
CS_Warband_SetWarbandConfig = protobuf.Message(pb.CS_WARBAND_SETWARBANDCONFIG)
MSG_SOCIETY_ACTIVITY_MSG = 20
MSG_SOCIETY_ADDCONTRIBUTIONHISTORY = 23
MSG_SOCIETY_ALCHEMY_SPEEDUP_HELPEDLIST = 25
MSG_SOCIETY_ALCHEMY_SPEEDUP_HELPLIST = 24
MSG_SOCIETY_APPLYLIST = 12
MSG_SOCIETY_APPOINT = 7
MSG_SOCIETY_CONTRIBUTIONHISTORY = 18
MSG_SOCIETY_CREATE = 1
MSG_SOCIETY_DELETE = 2
MSG_SOCIETY_DISTRIBUTEHISTORY = 16
MSG_SOCIETY_FIND = 10
MSG_SOCIETY_GETEVENTINFOLIST = 17
MSG_SOCIETY_GETLIST = 9
MSG_SOCIETY_GETNAME = 19
MSG_SOCIETY_INFO = 11
MSG_SOCIETY_INVITEJOIN = 26
MSG_SOCIETY_JOIN = 4
MSG_SOCIETY_JOINLIST = 13
MSG_SOCIETY_KICK = 6
MSG_SOCIETY_LEAVE = 5
MSG_SOCIETY_MEMBERLIST = 3
MSG_SOCIETY_MSGCACHE = 14
MSG_SOCIETY_NONE = 0
MSG_SOCIETY_RED_PACKET_LIMIT = 30
MSG_SOCIETY_RED_PACKET_OPENTOTAL_PROCESS = 36
MSG_SOCIETY_RED_PACKET_OPEN_HISTORY = 32
MSG_SOCIETY_RED_PACKET_OTHER_PRIZE = 37
MSG_SOCIETY_RED_PACKET_PASSWORD = 29
MSG_SOCIETY_RED_PACKET_PRIZE_INFO = 34
MSG_SOCIETY_RED_PACKET_RANK = 28
MSG_SOCIETY_RED_PACKET_SELF_RANK = 31
MSG_SOCIETY_RED_PACKET_SUPPER = 33
MSG_SOCIETY_RED_PACKET_TRANSFER_PASSWORD = 35
MSG_SOCIETY_SUCCEED = 8
MSG_SOCIETY_TASKLIST = 15
MSG_SOCIETY_TOWER_CLEARANCE_NUMBER = 21
MSG_SOCIETY_TOWER_CLEARANCE_TOPLEVEL = 22
MSG_SOCIETY_UPDATE_RED_PACKET = 27
MSG_WARBAND_BEGIN = 500
MSG_WARBAND_CHANGELEADER = 512
MSG_WARBAND_CREATE = 501
MSG_WARBAND_DEALAPPLY = 510
MSG_WARBAND_DELETE = 502
MSG_WARBAND_END = 1000
MSG_WARBAND_ENTERRANDOMECTYPE = 520
MSG_WARBAND_FIND = 523
MSG_WARBAND_GETACTVRECORD = 513
MSG_WARBAND_GETAPPLYLIST = 509
MSG_WARBAND_GETLIST = 505
MSG_WARBAND_GETRECHARGEMESSAGE = 515
MSG_WARBAND_INVITEFOLLOW = 522
MSG_WARBAND_INVITEJOIN = 516
MSG_WARBAND_JOIN = 503
MSG_WARBAND_KICKMEMBER = 511
MSG_WARBAND_LEAVE = 504
MSG_WARBAND_OPENRANDOMECTYPE = 519
MSG_WARBAND_OPENRANDOMECTYPENOTICE = 518
MSG_WARBAND_PRIZENOTICE = 517
MSG_WARBAND_RANDOMECTYPERECORD = 521
MSG_WARBAND_RECHARGEHISTORY = 514
MSG_WARBAND_SETWARBANDCONFIG = 508
MSG_WARBAND_UPDATEDATA = 506
MSG_WARBAND_UPDATEMEMBER = 507
SC_Society_ActivityMsg = protobuf.Message(pb.SC_SOCIETY_ACTIVITYMSG)
SC_Society_Alchemy_SpeedUp_HelpList = protobuf.Message(pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPLIST)
SC_Society_Alchemy_SpeedUp_HelpList.HelpData = protobuf.Message(pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPLIST_HELPDATA)
SC_Society_Alchemy_SpeedUp_HelpedList = protobuf.Message(pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPEDLIST)
SC_Society_Alchemy_SpeedUp_HelpedList.HelpedData = protobuf.Message(pb.SC_SOCIETY_ALCHEMY_SPEEDUP_HELPEDLIST_HELPEDDATA)
SC_Society_ApplyList = protobuf.Message(pb.SC_SOCIETY_APPLYLIST)
SC_Society_ApplyList.ApplyData = protobuf.Message(pb.SC_SOCIETY_APPLYLIST_APPLYDATA)
SC_Society_ApplyList.ApplyData.Applicant = protobuf.Message(pb.SC_SOCIETY_APPLYLIST_APPLYDATA_APPLICANT)
SC_Society_Appoint = protobuf.Message(pb.SC_SOCIETY_APPOINT)
SC_Society_Create = protobuf.Message(pb.SC_SOCIETY_CREATE)
SC_Society_Delete = protobuf.Message(pb.SC_SOCIETY_DELETE)
SC_Society_DistributeHistory = protobuf.Message(pb.SC_SOCIETY_DISTRIBUTEHISTORY)
SC_Society_DistributeHistory.HistoryInfo = protobuf.Message(pb.SC_SOCIETY_DISTRIBUTEHISTORY_HISTORYINFO)
SC_Society_Find = protobuf.Message(pb.SC_SOCIETY_FIND)
SC_Society_GETEVENTINFOLIST = protobuf.Message(pb.SC_SOCIETY_GETEVENTINFOLIST)
SC_Society_GETEVENTINFOLIST.EventInfo = protobuf.Message(pb.SC_SOCIETY_GETEVENTINFOLIST_EVENTINFO)
SC_Society_GetContributionHistory = protobuf.Message(pb.SC_SOCIETY_GETCONTRIBUTIONHISTORY)
SC_Society_GetContributionHistory.History = protobuf.Message(pb.SC_SOCIETY_GETCONTRIBUTIONHISTORY_HISTORY)
SC_Society_GetList = protobuf.Message(pb.SC_SOCIETY_GETLIST)
SC_Society_GetList.SocietyData = protobuf.Message(pb.SC_SOCIETY_GETLIST_SOCIETYDATA)
SC_Society_GetName = protobuf.Message(pb.SC_SOCIETY_GETNAME)
SC_Society_Info = protobuf.Message(pb.SC_SOCIETY_INFO)
SC_Society_InviteJoin = protobuf.Message(pb.SC_SOCIETY_INVITEJOIN)
SC_Society_Join = protobuf.Message(pb.SC_SOCIETY_JOIN)
SC_Society_JoinListBack = protobuf.Message(pb.SC_SOCIETY_JOINLISTBACK)
SC_Society_JoinListBack.UserInfo = protobuf.Message(pb.SC_SOCIETY_JOINLISTBACK_USERINFO)
SC_Society_Kick = protobuf.Message(pb.SC_SOCIETY_KICK)
SC_Society_Leave = protobuf.Message(pb.SC_SOCIETY_LEAVE)
SC_Society_MemberList = protobuf.Message(pb.SC_SOCIETY_MEMBERLIST)
SC_Society_MemberList.SocietyMember = protobuf.Message(pb.SC_SOCIETY_MEMBERLIST_SOCIETYMEMBER)
SC_Society_MsgCache = protobuf.Message(pb.SC_SOCIETY_MSGCACHE)
SC_Society_MsgCache.MsgInfo = protobuf.Message(pb.SC_SOCIETY_MSGCACHE_MSGINFO)
SC_Society_Red_Packet_Limit = protobuf.Message(pb.SC_SOCIETY_RED_PACKET_LIMIT)
SC_Society_Red_Packet_Limit.HistroyInfo = protobuf.Message(pb.SC_SOCIETY_RED_PACKET_LIMIT_HISTROYINFO)
SC_Society_Red_Packet_OpenTotal_Process = protobuf.Message(pb.SC_SOCIETY_RED_PACKET_OPENTOTAL_PROCESS)
SC_Society_Red_Packet_Open_History = protobuf.Message(pb.SC_SOCIETY_RED_PACKET_OPEN_HISTORY)
SC_Society_Red_Packet_Open_History.HistroyInfo = protobuf.Message(pb.SC_SOCIETY_RED_PACKET_OPEN_HISTORY_HISTROYINFO)
SC_Society_Red_Packet_Other_Prize = protobuf.Message(pb.SC_SOCIETY_RED_PACKET_OTHER_PRIZE)
SC_Society_Red_Packet_PassWord = protobuf.Message(pb.SC_SOCIETY_RED_PACKET_PASSWORD)
SC_Society_Red_Packet_Prize = protobuf.Message(pb.SC_SOCIETY_RED_PACKET_PRIZE)
SC_Society_Red_Packet_Prize.PrizeInfo = protobuf.Message(pb.SC_SOCIETY_RED_PACKET_PRIZE_PRIZEINFO)
SC_Society_Red_Packet_Rank = protobuf.Message(pb.SC_SOCIETY_RED_PACKET_RANK)
SC_Society_Red_Packet_Rank.RankInfo = protobuf.Message(pb.SC_SOCIETY_RED_PACKET_RANK_RANKINFO)
SC_Society_Red_Packet_Self_Rank = protobuf.Message(pb.SC_SOCIETY_RED_PACKET_SELF_RANK)
SC_Society_Red_Packet_Self_Rank.RankInfo = protobuf.Message(pb.SC_SOCIETY_RED_PACKET_SELF_RANK_RANKINFO)
SC_Society_Red_Packet_Supper = protobuf.Message(pb.SC_SOCIETY_RED_PACKET_SUPPER)
SC_Society_Red_Packet_Transfer = protobuf.Message(pb.SC_SOCIETY_RED_PACKET_TRANSFER)
SC_Society_Red_Packet_Transfer.HistroyInfo = protobuf.Message(pb.SC_SOCIETY_RED_PACKET_TRANSFER_HISTROYINFO)
SC_Society_Succeed = protobuf.Message(pb.SC_SOCIETY_SUCCEED)
SC_Society_TaskList = protobuf.Message(pb.SC_SOCIETY_TASKLIST)
SC_Society_Tower_Clearance_Number = protobuf.Message(pb.SC_SOCIETY_TOWER_CLEARANCE_NUMBER)
SC_Society_Tower_Clearance_Toplevel = protobuf.Message(pb.SC_SOCIETY_TOWER_CLEARANCE_TOPLEVEL)
SC_Society_UpdateRedPacket = protobuf.Message(pb.SC_SOCIETY_UPDATEREDPACKET)
SC_Warband_ChangeLeader = protobuf.Message(pb.SC_WARBAND_CHANGELEADER)
SC_Warband_Create = protobuf.Message(pb.SC_WARBAND_CREATE)
SC_Warband_DealApply = protobuf.Message(pb.SC_WARBAND_DEALAPPLY)
SC_Warband_Delete = protobuf.Message(pb.SC_WARBAND_DELETE)
SC_Warband_EnterRandomEctype = protobuf.Message(pb.SC_WARBAND_ENTERRANDOMECTYPE)
SC_Warband_Find = protobuf.Message(pb.SC_WARBAND_FIND)
SC_Warband_GetActvRecord = protobuf.Message(pb.SC_WARBAND_GETACTVRECORD)
SC_Warband_GetApplyList = protobuf.Message(pb.SC_WARBAND_GETAPPLYLIST)
SC_Warband_GetApplyList.ApplyActor = protobuf.Message(pb.SC_WARBAND_GETAPPLYLIST_APPLYACTOR)
SC_Warband_GetList = protobuf.Message(pb.SC_WARBAND_GETLIST)
SC_Warband_GetList.WarbandData = protobuf.Message(pb.SC_WARBAND_GETLIST_WARBANDDATA)
SC_Warband_GetRechargeHistory = protobuf.Message(pb.SC_WARBAND_GETRECHARGEHISTORY)
SC_Warband_GetRechargeHistory.History = protobuf.Message(pb.SC_WARBAND_GETRECHARGEHISTORY_HISTORY)
SC_Warband_GetRechargeMessage = protobuf.Message(pb.SC_WARBAND_GETRECHARGEMESSAGE)
SC_Warband_InviteFollow = protobuf.Message(pb.SC_WARBAND_INVITEFOLLOW)
SC_Warband_InviteJoin = protobuf.Message(pb.SC_WARBAND_INVITEJOIN)
SC_Warband_Join = protobuf.Message(pb.SC_WARBAND_JOIN)
SC_Warband_KickMember = protobuf.Message(pb.SC_WARBAND_KICKMEMBER)
SC_Warband_Leave = protobuf.Message(pb.SC_WARBAND_LEAVE)
SC_Warband_MemberList = protobuf.Message(pb.SC_WARBAND_MEMBERLIST)
SC_Warband_MemberList.MemberData = protobuf.Message(pb.SC_WARBAND_MEMBERLIST_MEMBERDATA)
SC_Warband_OpenRandomEctype = protobuf.Message(pb.SC_WARBAND_OPENRANDOMECTYPE)
SC_Warband_OpenRandomEctypeNotice = protobuf.Message(pb.SC_WARBAND_OPENRANDOMECTYPENOTICE)
SC_Warband_PrizeNotice = protobuf.Message(pb.SC_WARBAND_PRIZENOTICE)
SC_Warband_RandomEctypeRecord = protobuf.Message(pb.SC_WARBAND_RANDOMECTYPERECORD)
SC_Warband_RandomEctypeRecord.RecordInfo = protobuf.Message(pb.SC_WARBAND_RANDOMECTYPERECORD_RECORDINFO)
SC_Warband_SetWarbandConfig = protobuf.Message(pb.SC_WARBAND_SETWARBANDCONFIG)
SC_Warband_UpdateData = protobuf.Message(pb.SC_WARBAND_UPDATEDATA)

