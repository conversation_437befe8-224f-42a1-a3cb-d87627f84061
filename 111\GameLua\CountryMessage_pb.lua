-- Generated By protoc-gen-lua Do not Edit
local protobuf = require "protobuf/protobuf"
module('CountryMessage_pb')
local pb = {}


pb.MSG_COUNTRY_ACTIONID = protobuf.EnumDescriptor();
pb.MSG_COUNTRY_ACTIONID_MSG_COUNTRY_NONE_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_COUNTRY_ACTIONID_MSG_COUNTRY_GETCOUNTRYINFO_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_COUNTRY_ACTIONID_MSG_COUNTRY_SETNOTICE_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_COUNTRY_ACTIONID_MSG_COUNTRY_ADDOFFICER_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_COUNTRY_ACTIONID_MSG_COUNTRY_REMOVEOFFICER_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_COUNTRY_ACTIONID_MSG_COUNTRY_UPDATEOFFICERJOBINFO_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_COUNTRY_ACTIONID_MSG_COUNTRY_SETCOUNTRYWAR_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_COUNTRY_ACTIONID_MSG_COUNTRY_WAR_SYNCPLAYERSCORE_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_COUNTRY_ACTIONID_MSG_COUNTRY_WAR_OPERATECOUNTDOWN_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_COUNTRY_ACTIONID_MSG_COUNTRY_WAR_SYNCFIELDINFO_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_COUNTRY_ACTIONID_MSG_COUNTRY_WAR_RESULTINFO_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_COUNTRY_ACTIONID_MSG_COUNTRY_SIGNCOUNTRYKINGWAR_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_COUNTRY_ACTIONID_MSG_COUNTRY_GETCOUNTRYKINGWARINFO_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_COUNTRY_ACTIONID_MSG_COUNTRY_KINGWAR_SYNCFIELDINFO_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_COUNTRY_ACTIONID_MSG_COUNTRY_KINGWAR_SYNCSCOREINFO_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_COUNTRY_ACTIONID_MSG_COUNTRY_KINGWAR_RESULTINFO_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_COUNTRY_ACTIONID_MSG_COUNTRY_BATTLEFIELD_MAININFO_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_COUNTRY_ACTIONID_MSG_COUNTRY_BATTLEFIELD_MAPINFO_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_COUNTRY_ACTIONID_MSG_COUNTRY_GETPKRECORDINFO_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_COUNTRY_ACTIONID_MSG_COUNTRY_GETCOUNTRYPRINCESSINFO_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_COUNTRY_ACTIONID_MSG_COUNTRY_COUNTRYPRINCESSRESULT_ENUM = protobuf.EnumValueDescriptor();
pb.CS_COUNTRY_GETCOUNTRYINFO = protobuf.Descriptor();
pb.CS_COUNTRY_GETCOUNTRYINFO_COUNTRYID_FIELD = protobuf.FieldDescriptor();
pb.SC_COUNTRY_GETCOUNTRYINFO = protobuf.Descriptor();
pb.SC_COUNTRY_GETCOUNTRYINFO_OFFICEDATA = protobuf.Descriptor();
pb.SC_COUNTRY_GETCOUNTRYINFO_OFFICEDATA_ACTORID_FIELD = protobuf.FieldDescriptor();
pb.SC_COUNTRY_GETCOUNTRYINFO_OFFICEDATA_ACTORNAME_FIELD = protobuf.FieldDescriptor();
pb.SC_COUNTRY_GETCOUNTRYINFO_OFFICEDATA_SOCIETYNAME_FIELD = protobuf.FieldDescriptor();
pb.SC_COUNTRY_GETCOUNTRYINFO_OFFICEDATA_LEVEL_FIELD = protobuf.FieldDescriptor();
pb.SC_COUNTRY_GETCOUNTRYINFO_OFFICEDATA_VOCATION_FIELD = protobuf.FieldDescriptor();
pb.SC_COUNTRY_GETCOUNTRYINFO_OFFICEDATA_POWER_FIELD = protobuf.FieldDescriptor();
pb.SC_COUNTRY_GETCOUNTRYINFO_OFFICEDATA_OFFICETYPE_FIELD = protobuf.FieldDescriptor();
pb.SC_COUNTRY_GETCOUNTRYINFO_OFFICEDATA_EQUIPMODELID_FIELD = protobuf.FieldDescriptor();
pb.SC_COUNTRY_GETCOUNTRYINFO_OFFICEDATA_EQUIPWEAPONID_FIELD = protobuf.FieldDescriptor();
pb.SC_COUNTRY_GETCOUNTRYINFO_OFFICEDATA_EQUIPWINGID_FIELD = protobuf.FieldDescriptor();
pb.SC_COUNTRY_GETCOUNTRYINFO_OFFICEDATA_GENRE_FIELD = protobuf.FieldDescriptor();
pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYWARDATA = protobuf.Descriptor();
pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYWARDATA_SRCCOUNTRYID_FIELD = protobuf.FieldDescriptor();
pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYWARDATA_TARCOUNTRYID_FIELD = protobuf.FieldDescriptor();
pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYWARDATA_WARTIME_FIELD = protobuf.FieldDescriptor();
pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYWARHISTORYDATA = protobuf.Descriptor();
pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYWARHISTORYDATA_SRCCOUNTRYID_FIELD = protobuf.FieldDescriptor();
pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYWARHISTORYDATA_TARCOUNTRYID_FIELD = protobuf.FieldDescriptor();
pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYWARHISTORYDATA_WARTIME_FIELD = protobuf.FieldDescriptor();
pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYWARHISTORYDATA_WINCOUNTRYID_FIELD = protobuf.FieldDescriptor();
pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYID_FIELD = protobuf.FieldDescriptor();
pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYPOWER_FIELD = protobuf.FieldDescriptor();
pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYPOWERRANK_FIELD = protobuf.FieldDescriptor();
pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYMONEY_FIELD = protobuf.FieldDescriptor();
pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYRESINFO_FIELD = protobuf.FieldDescriptor();
pb.SC_COUNTRY_GETCOUNTRYINFO_NOTICE_FIELD = protobuf.FieldDescriptor();
pb.SC_COUNTRY_GETCOUNTRYINFO_NEXTCOUNTRYWARSRC_FIELD = protobuf.FieldDescriptor();
pb.SC_COUNTRY_GETCOUNTRYINFO_NEXTCOUNTRYWARTAR_FIELD = protobuf.FieldDescriptor();
pb.SC_COUNTRY_GETCOUNTRYINFO_OFFICERLIST_FIELD = protobuf.FieldDescriptor();
pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYWARLIST_FIELD = protobuf.FieldDescriptor();
pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYWARHISTORYLIST_FIELD = protobuf.FieldDescriptor();
pb.CS_COUNTRY_SETNOTICE = protobuf.Descriptor();
pb.CS_COUNTRY_SETNOTICE_NOTICE_FIELD = protobuf.FieldDescriptor();
pb.CS_COUNTRY_ADDOFFICER = protobuf.Descriptor();
pb.CS_COUNTRY_ADDOFFICER_ACTORID_FIELD = protobuf.FieldDescriptor();
pb.CS_COUNTRY_ADDOFFICER_OFFICETYPE_FIELD = protobuf.FieldDescriptor();
pb.CS_COUNTRY_REMOVEOFFICER = protobuf.Descriptor();
pb.CS_COUNTRY_REMOVEOFFICER_ACTORID_FIELD = protobuf.FieldDescriptor();
pb.SC_COUNTRY_UPDATEOFFICERJOBINFO = protobuf.Descriptor();
pb.SC_COUNTRY_UPDATEOFFICERJOBINFO_OFFICETYPE_FIELD = protobuf.FieldDescriptor();
pb.SC_COUNTRY_UPDATEOFFICERJOBINFO_BANTALKCOUNT_FIELD = protobuf.FieldDescriptor();
pb.SC_COUNTRY_UPDATEOFFICERJOBINFO_CALLUPCOUNT_FIELD = protobuf.FieldDescriptor();
pb.SC_COUNTRY_UPDATEOFFICERJOBINFO_GETWELFARECOUNT_FIELD = protobuf.FieldDescriptor();
pb.SC_COUNTRY_UPDATEOFFICERJOBINFO_TELEPORTCOUNT_FIELD = protobuf.FieldDescriptor();
pb.CS_COUNTRY_SETCOUNTRYWAR = protobuf.Descriptor();
pb.CS_COUNTRY_SETCOUNTRYWAR_COUNTRYID_FIELD = protobuf.FieldDescriptor();
pb.CS_COUNTRY_SETCOUNTRYWAR_WARDATETYPE_FIELD = protobuf.FieldDescriptor();
pb.SC_COUNTRY_WARSYNCPLAYERSCORE = protobuf.Descriptor();
pb.SC_COUNTRY_WARSYNCPLAYERSCORE_ACTORID_FIELD = protobuf.FieldDescriptor();
pb.SC_COUNTRY_WARSYNCPLAYERSCORE_SCORE_FIELD = protobuf.FieldDescriptor();
pb.SC_COUNTRY_WARSYNCPLAYERSCORE_KILLNUM_FIELD = protobuf.FieldDescriptor();
pb.SC_COUNTRY_WAROPERATECOUNTDOWN = protobuf.Descriptor();
pb.SC_COUNTRY_WAROPERATECOUNTDOWN_ACTORID_FIELD = protobuf.FieldDescriptor();
pb.SC_COUNTRY_WAROPERATECOUNTDOWN_ACTORNAME_FIELD = protobuf.FieldDescriptor();
pb.SC_COUNTRY_WAROPERATECOUNTDOWN_COUNTRYID_FIELD = protobuf.FieldDescriptor();
pb.SC_COUNTRY_WAROPERATECOUNTDOWN_OBJTYPE_FIELD = protobuf.FieldDescriptor();
pb.SC_COUNTRY_WAROPERATECOUNTDOWN_COUNTDOWNTIME_FIELD = protobuf.FieldDescriptor();
pb.SC_COUNTRY_WARSYNCFIELDINFO = protobuf.Descriptor();
pb.SC_COUNTRY_WARSYNCFIELDINFO_BATTLEPHASE_FIELD = protobuf.FieldDescriptor();
pb.SC_COUNTRY_WARSYNCFIELDINFO_DEFENCECOUNTRY_FIELD = protobuf.FieldDescriptor();
pb.SC_COUNTRY_WARSYNCFIELDINFO_DBUFFGEARLV_FIELD = protobuf.FieldDescriptor();
pb.SC_COUNTRY_WARSYNCFIELDINFO_DRES1NUM_FIELD = protobuf.FieldDescriptor();
pb.SC_COUNTRY_WARSYNCFIELDINFO_DRES2NUM_FIELD = protobuf.FieldDescriptor();
pb.SC_COUNTRY_WARSYNCFIELDINFO_DLEFTTURRETHP_FIELD = protobuf.FieldDescriptor();
pb.SC_COUNTRY_WARSYNCFIELDINFO_DRIGHTTURRETHP_FIELD = protobuf.FieldDescriptor();
pb.SC_COUNTRY_WARSYNCFIELDINFO_DFRONTLEFTFLAGHP_FIELD = protobuf.FieldDescriptor();
pb.SC_COUNTRY_WARSYNCFIELDINFO_DFRONTRIGHTFLAGHP_FIELD = protobuf.FieldDescriptor();
pb.SC_COUNTRY_WARSYNCFIELDINFO_DLEFTFLAGHP_FIELD = protobuf.FieldDescriptor();
pb.SC_COUNTRY_WARSYNCFIELDINFO_DRIGHTFLAGHP_FIELD = protobuf.FieldDescriptor();
pb.SC_COUNTRY_WARSYNCFIELDINFO_DCENTERFLAGHP_FIELD = protobuf.FieldDescriptor();
pb.SC_COUNTRY_WARSYNCFIELDINFO_DSPGEARUSE_FIELD = protobuf.FieldDescriptor();
pb.SC_COUNTRY_WARSYNCFIELDINFO_ATTACKCOUNTRY_FIELD = protobuf.FieldDescriptor();
pb.SC_COUNTRY_WARSYNCFIELDINFO_AATTACKGEARLV_FIELD = protobuf.FieldDescriptor();
pb.SC_COUNTRY_WARSYNCFIELDINFO_ABUFFGEARLV_FIELD = protobuf.FieldDescriptor();
pb.SC_COUNTRY_WARSYNCFIELDINFO_ARES1NUM_FIELD = protobuf.FieldDescriptor();
pb.SC_COUNTRY_WARSYNCFIELDINFO_ARES2NUM_FIELD = protobuf.FieldDescriptor();
pb.SC_COUNTRY_WARSYNCFIELDINFO_APATHENDTIME_FIELD = protobuf.FieldDescriptor();
pb.SC_COUNTRY_WARSYNCFIELDINFO_DPASTWINCOUNT_FIELD = protobuf.FieldDescriptor();
pb.SC_COUNTRY_WARSYNCFIELDINFO_APASTWINCOUNT_FIELD = protobuf.FieldDescriptor();
pb.SC_COUNTRY_WARRESULTINFO = protobuf.Descriptor();
pb.SC_COUNTRY_WARRESULTINFO_ACTORSCOREDATA = protobuf.Descriptor();
pb.SC_COUNTRY_WARRESULTINFO_ACTORSCOREDATA_RANK_FIELD = protobuf.FieldDescriptor();
pb.SC_COUNTRY_WARRESULTINFO_ACTORSCOREDATA_ACTORID_FIELD = protobuf.FieldDescriptor();
pb.SC_COUNTRY_WARRESULTINFO_ACTORSCOREDATA_ACTORNAME_FIELD = protobuf.FieldDescriptor();
pb.SC_COUNTRY_WARRESULTINFO_ACTORSCOREDATA_COUNTRY_FIELD = protobuf.FieldDescriptor();
pb.SC_COUNTRY_WARRESULTINFO_ACTORSCOREDATA_LEVEL_FIELD = protobuf.FieldDescriptor();
pb.SC_COUNTRY_WARRESULTINFO_ACTORSCOREDATA_SCORE_FIELD = protobuf.FieldDescriptor();
pb.SC_COUNTRY_WARRESULTINFO_ACTORSCOREDATA_KILLNUM_FIELD = protobuf.FieldDescriptor();
pb.SC_COUNTRY_WARRESULTINFO_ACTORSCOREDATA_PRIZERES1_FIELD = protobuf.FieldDescriptor();
pb.SC_COUNTRY_WARRESULTINFO_ACTORSCOREDATA_PRIZERES2_FIELD = protobuf.FieldDescriptor();
pb.SC_COUNTRY_WARRESULTINFO_SOCIETYSCOREDATA = protobuf.Descriptor();
pb.SC_COUNTRY_WARRESULTINFO_SOCIETYSCOREDATA_RANK_FIELD = protobuf.FieldDescriptor();
pb.SC_COUNTRY_WARRESULTINFO_SOCIETYSCOREDATA_SOCIETYID_FIELD = protobuf.FieldDescriptor();
pb.SC_COUNTRY_WARRESULTINFO_SOCIETYSCOREDATA_SOCIETYNAME_FIELD = protobuf.FieldDescriptor();
pb.SC_COUNTRY_WARRESULTINFO_SOCIETYSCOREDATA_ACTORID_FIELD = protobuf.FieldDescriptor();
pb.SC_COUNTRY_WARRESULTINFO_SOCIETYSCOREDATA_ACTORNAME_FIELD = protobuf.FieldDescriptor();
pb.SC_COUNTRY_WARRESULTINFO_SOCIETYSCOREDATA_COUNTRY_FIELD = protobuf.FieldDescriptor();
pb.SC_COUNTRY_WARRESULTINFO_SOCIETYSCOREDATA_LEVEL_FIELD = protobuf.FieldDescriptor();
pb.SC_COUNTRY_WARRESULTINFO_SOCIETYSCOREDATA_SCORE_FIELD = protobuf.FieldDescriptor();
pb.SC_COUNTRY_WARRESULTINFO_SOCIETYSCOREDATA_PRIZEID_FIELD = protobuf.FieldDescriptor();
pb.SC_COUNTRY_WARRESULTINFO_SOCIETYSCOREDATA_PRIZENUM_FIELD = protobuf.FieldDescriptor();
pb.SC_COUNTRY_WARRESULTINFO_SRCCOUNTRYID_FIELD = protobuf.FieldDescriptor();
pb.SC_COUNTRY_WARRESULTINFO_TARCOUNTRYID_FIELD = protobuf.FieldDescriptor();
pb.SC_COUNTRY_WARRESULTINFO_WINCOUNTRYID_FIELD = protobuf.FieldDescriptor();
pb.SC_COUNTRY_WARRESULTINFO_ACTORSCORELIST_FIELD = protobuf.FieldDescriptor();
pb.SC_COUNTRY_WARRESULTINFO_SELFACTORSCOREINFO_FIELD = protobuf.FieldDescriptor();
pb.SC_COUNTRY_WARRESULTINFO_SOCIETYSCORELIST_FIELD = protobuf.FieldDescriptor();
pb.SC_COUNTRY_WARRESULTINFO_SELFSOCIETYSCOREINFO_FIELD = protobuf.FieldDescriptor();
pb.CS_COUNTRY_GETCOUNTRYKINGWARINFO = protobuf.Descriptor();
pb.CS_COUNTRY_GETCOUNTRYKINGWARINFO_COUNTRYID_FIELD = protobuf.FieldDescriptor();
pb.SC_COUNTRY_GETCOUNTRYKINGWARINFO = protobuf.Descriptor();
pb.SC_COUNTRY_GETCOUNTRYKINGWARINFO_KINGWARSIGNDATA = protobuf.Descriptor();
pb.SC_COUNTRY_GETCOUNTRYKINGWARINFO_KINGWARSIGNDATA_SOCIETYID_FIELD = protobuf.FieldDescriptor();
pb.SC_COUNTRY_GETCOUNTRYKINGWARINFO_KINGWARSIGNDATA_SOCIETYNAME_FIELD = protobuf.FieldDescriptor();
pb.SC_COUNTRY_GETCOUNTRYKINGWARINFO_KINGWARSIGNDATA_ACTORID_FIELD = protobuf.FieldDescriptor();
pb.SC_COUNTRY_GETCOUNTRYKINGWARINFO_KINGWARSIGNDATA_ACTORNAME_FIELD = protobuf.FieldDescriptor();
pb.SC_COUNTRY_GETCOUNTRYKINGWARINFO_KINGWARSIGNDATA_LEVEL_FIELD = protobuf.FieldDescriptor();
pb.SC_COUNTRY_GETCOUNTRYKINGWARINFO_COUNTRYID_FIELD = protobuf.FieldDescriptor();
pb.SC_COUNTRY_GETCOUNTRYKINGWARINFO_SIGNLIST_FIELD = protobuf.FieldDescriptor();
pb.SC_COUNTRY_KINGWARSYNCFIELDINFO = protobuf.Descriptor();
pb.SC_COUNTRY_KINGWARSYNCFIELDINFO_MONSTERDATA = protobuf.Descriptor();
pb.SC_COUNTRY_KINGWARSYNCFIELDINFO_MONSTERDATA_MONSTERTYPE_FIELD = protobuf.FieldDescriptor();
pb.SC_COUNTRY_KINGWARSYNCFIELDINFO_MONSTERDATA_SOCIETYID_FIELD = protobuf.FieldDescriptor();
pb.SC_COUNTRY_KINGWARSYNCFIELDINFO_MONSTERDATA_SOCIETYNAME_FIELD = protobuf.FieldDescriptor();
pb.SC_COUNTRY_KINGWARSYNCFIELDINFO_MONSTERLIST_FIELD = protobuf.FieldDescriptor();
pb.SC_COUNTRY_KINGWARSYNCSCOREINFO = protobuf.Descriptor();
pb.SC_COUNTRY_KINGWARSYNCSCOREINFO_SCOREDATA = protobuf.Descriptor();
pb.SC_COUNTRY_KINGWARSYNCSCOREINFO_SCOREDATA_RANK_FIELD = protobuf.FieldDescriptor();
pb.SC_COUNTRY_KINGWARSYNCSCOREINFO_SCOREDATA_SOCIETYID_FIELD = protobuf.FieldDescriptor();
pb.SC_COUNTRY_KINGWARSYNCSCOREINFO_SCOREDATA_SOCIETYNAME_FIELD = protobuf.FieldDescriptor();
pb.SC_COUNTRY_KINGWARSYNCSCOREINFO_SCOREDATA_SCORE_FIELD = protobuf.FieldDescriptor();
pb.SC_COUNTRY_KINGWARSYNCSCOREINFO_MSGTYPE_FIELD = protobuf.FieldDescriptor();
pb.SC_COUNTRY_KINGWARSYNCSCOREINFO_RANKLIST_FIELD = protobuf.FieldDescriptor();
pb.SC_COUNTRY_KINGWARRESULTINFO = protobuf.Descriptor();
pb.SC_COUNTRY_KINGWARRESULTINFO_SOCIETYSCOREDATA = protobuf.Descriptor();
pb.SC_COUNTRY_KINGWARRESULTINFO_SOCIETYSCOREDATA_RANK_FIELD = protobuf.FieldDescriptor();
pb.SC_COUNTRY_KINGWARRESULTINFO_SOCIETYSCOREDATA_SOCIETYID_FIELD = protobuf.FieldDescriptor();
pb.SC_COUNTRY_KINGWARRESULTINFO_SOCIETYSCOREDATA_SOCIETYNAME_FIELD = protobuf.FieldDescriptor();
pb.SC_COUNTRY_KINGWARRESULTINFO_SOCIETYSCOREDATA_ACTORID_FIELD = protobuf.FieldDescriptor();
pb.SC_COUNTRY_KINGWARRESULTINFO_SOCIETYSCOREDATA_ACTORNAME_FIELD = protobuf.FieldDescriptor();
pb.SC_COUNTRY_KINGWARRESULTINFO_SOCIETYSCOREDATA_LEVEL_FIELD = protobuf.FieldDescriptor();
pb.SC_COUNTRY_KINGWARRESULTINFO_SOCIETYSCOREDATA_SCORE_FIELD = protobuf.FieldDescriptor();
pb.SC_COUNTRY_KINGWARRESULTINFO_SOCIETYSCORELIST_FIELD = protobuf.FieldDescriptor();
pb.SC_COUNTRY_KINGWARRESULTINFO_SELFSOCIETYSCOREINFO_FIELD = protobuf.FieldDescriptor();
pb.SC_COUNTRY_KINGWARRESULTINFO_PRIZE1NUM_FIELD = protobuf.FieldDescriptor();
pb.SC_COUNTRY_KINGWARRESULTINFO_PRIZE2NUM_FIELD = protobuf.FieldDescriptor();
pb.SC_COUNTRY_BATTLEFIELD_MAININFO = protobuf.Descriptor();
pb.SC_COUNTRY_BATTLEFIELD_MAININFO_OWNCOUNTRY_FIELD = protobuf.FieldDescriptor();
pb.SC_COUNTRY_BATTLEFIELD_MAPINFO = protobuf.Descriptor();
pb.SC_COUNTRY_BATTLEFIELD_MAPINFO_COUNTRY1RES_FIELD = protobuf.FieldDescriptor();
pb.SC_COUNTRY_BATTLEFIELD_MAPINFO_COUNTRY2RES_FIELD = protobuf.FieldDescriptor();
pb.SC_COUNTRY_BATTLEFIELD_MAPINFO_COUNTRY1BOSSTIME_FIELD = protobuf.FieldDescriptor();
pb.SC_COUNTRY_BATTLEFIELD_MAPINFO_COUNTRY2BOSSTIME_FIELD = protobuf.FieldDescriptor();
pb.SC_COUNTRY_BATTLEFIELD_MAPINFO_COUNTRYBOSSOWNER_FIELD = protobuf.FieldDescriptor();
pb.SC_COUNTRY_GETPKRECORDINFO = protobuf.Descriptor();
pb.SC_COUNTRY_GETPKRECORDINFO_PKRECORDDATA = protobuf.Descriptor();
pb.SC_COUNTRY_GETPKRECORDINFO_PKRECORDDATA_RECORDTYPE_FIELD = protobuf.FieldDescriptor();
pb.SC_COUNTRY_GETPKRECORDINFO_PKRECORDDATA_ACTORID_FIELD = protobuf.FieldDescriptor();
pb.SC_COUNTRY_GETPKRECORDINFO_PKRECORDDATA_ACTORNAME_FIELD = protobuf.FieldDescriptor();
pb.SC_COUNTRY_GETPKRECORDINFO_PKRECORDDATA_LEVEL_FIELD = protobuf.FieldDescriptor();
pb.SC_COUNTRY_GETPKRECORDINFO_PKRECORDDATA_COUNTRY_FIELD = protobuf.FieldDescriptor();
pb.SC_COUNTRY_GETPKRECORDINFO_PKRECORDDATA_VOCATION_FIELD = protobuf.FieldDescriptor();
pb.SC_COUNTRY_GETPKRECORDINFO_PKRECORDDATA_MAPID_FIELD = protobuf.FieldDescriptor();
pb.SC_COUNTRY_GETPKRECORDINFO_PKRECORDDATA_RECORDTIME_FIELD = protobuf.FieldDescriptor();
pb.SC_COUNTRY_GETPKRECORDINFO_RECORDLIST_FIELD = protobuf.FieldDescriptor();
pb.SC_COUNTRY_GETPRINCESSINFO = protobuf.Descriptor();
pb.SC_COUNTRY_GETPRINCESSINFO_CURHP_FIELD = protobuf.FieldDescriptor();
pb.SC_COUNTRY_GETPRINCESSINFO_MAXHP_FIELD = protobuf.FieldDescriptor();
pb.SC_COUNTRY_GETPRINCESSINFO_OWNCOUNTRY_FIELD = protobuf.FieldDescriptor();
pb.SC_COUNTRY_GETPRINCESSINFO_EXISTENCETIME_FIELD = protobuf.FieldDescriptor();
pb.SC_COUNTRY_GETPRINCESSINFO_PRIZERATE_FIELD = protobuf.FieldDescriptor();
pb.SC_COUNTRY_GETPRINCESSINFO_COUNTRY_FIELD = protobuf.FieldDescriptor();
pb.SC_COUNTRY_GETPRINCESSINFO_KILLERNAME_FIELD = protobuf.FieldDescriptor();
pb.SC_COUNTRY_GETPRINCESSINFO_INVINCIBLESTATUS_FIELD = protobuf.FieldDescriptor();
pb.SC_COUNTRY_GETPRINCESSINFO_INVINCIBLESTATUSLEFTTIME_FIELD = protobuf.FieldDescriptor();
pb.SC_COUNTRY_PRINCESSINFO_RESULT = protobuf.Descriptor();
pb.SC_COUNTRY_PRINCESSINFO_RESULT_PRINCESSRECORDDATA = protobuf.Descriptor();
pb.SC_COUNTRY_PRINCESSINFO_RESULT_PRINCESSRECORDDATA_COUNTRYID_FIELD = protobuf.FieldDescriptor();
pb.SC_COUNTRY_PRINCESSINFO_RESULT_PRINCESSRECORDDATA_ROBENEMYTIMES_FIELD = protobuf.FieldDescriptor();
pb.SC_COUNTRY_PRINCESSINFO_RESULT_PRINCESSRECORDDATA_SELFROBBEDTIMES_FIELD = protobuf.FieldDescriptor();
pb.SC_COUNTRY_PRINCESSINFO_RESULT_PRINCESSRECORDDATA_ENEMYROBBEDTIMES_FIELD = protobuf.FieldDescriptor();
pb.SC_COUNTRY_PRINCESSINFO_RESULT_PRINCESSRECORDDATA_ROBSELFTIMES_FIELD = protobuf.FieldDescriptor();
pb.SC_COUNTRY_PRINCESSINFO_RESULT_PRINCESSRECORDDATA_OWNCOUNTRY_FIELD = protobuf.FieldDescriptor();
pb.SC_COUNTRY_PRINCESSINFO_RESULT_RECORDLIST_FIELD = protobuf.FieldDescriptor();

pb.MSG_COUNTRY_ACTIONID_MSG_COUNTRY_NONE_ENUM.name = "MSG_COUNTRY_NONE"
pb.MSG_COUNTRY_ACTIONID_MSG_COUNTRY_NONE_ENUM.index = 0
pb.MSG_COUNTRY_ACTIONID_MSG_COUNTRY_NONE_ENUM.number = 0
pb.MSG_COUNTRY_ACTIONID_MSG_COUNTRY_GETCOUNTRYINFO_ENUM.name = "MSG_COUNTRY_GETCOUNTRYINFO"
pb.MSG_COUNTRY_ACTIONID_MSG_COUNTRY_GETCOUNTRYINFO_ENUM.index = 1
pb.MSG_COUNTRY_ACTIONID_MSG_COUNTRY_GETCOUNTRYINFO_ENUM.number = 1
pb.MSG_COUNTRY_ACTIONID_MSG_COUNTRY_SETNOTICE_ENUM.name = "MSG_COUNTRY_SETNOTICE"
pb.MSG_COUNTRY_ACTIONID_MSG_COUNTRY_SETNOTICE_ENUM.index = 2
pb.MSG_COUNTRY_ACTIONID_MSG_COUNTRY_SETNOTICE_ENUM.number = 2
pb.MSG_COUNTRY_ACTIONID_MSG_COUNTRY_ADDOFFICER_ENUM.name = "MSG_COUNTRY_ADDOFFICER"
pb.MSG_COUNTRY_ACTIONID_MSG_COUNTRY_ADDOFFICER_ENUM.index = 3
pb.MSG_COUNTRY_ACTIONID_MSG_COUNTRY_ADDOFFICER_ENUM.number = 3
pb.MSG_COUNTRY_ACTIONID_MSG_COUNTRY_REMOVEOFFICER_ENUM.name = "MSG_COUNTRY_REMOVEOFFICER"
pb.MSG_COUNTRY_ACTIONID_MSG_COUNTRY_REMOVEOFFICER_ENUM.index = 4
pb.MSG_COUNTRY_ACTIONID_MSG_COUNTRY_REMOVEOFFICER_ENUM.number = 4
pb.MSG_COUNTRY_ACTIONID_MSG_COUNTRY_UPDATEOFFICERJOBINFO_ENUM.name = "MSG_COUNTRY_UPDATEOFFICERJOBINFO"
pb.MSG_COUNTRY_ACTIONID_MSG_COUNTRY_UPDATEOFFICERJOBINFO_ENUM.index = 5
pb.MSG_COUNTRY_ACTIONID_MSG_COUNTRY_UPDATEOFFICERJOBINFO_ENUM.number = 5
pb.MSG_COUNTRY_ACTIONID_MSG_COUNTRY_SETCOUNTRYWAR_ENUM.name = "MSG_COUNTRY_SETCOUNTRYWAR"
pb.MSG_COUNTRY_ACTIONID_MSG_COUNTRY_SETCOUNTRYWAR_ENUM.index = 6
pb.MSG_COUNTRY_ACTIONID_MSG_COUNTRY_SETCOUNTRYWAR_ENUM.number = 6
pb.MSG_COUNTRY_ACTIONID_MSG_COUNTRY_WAR_SYNCPLAYERSCORE_ENUM.name = "MSG_COUNTRY_WAR_SYNCPLAYERSCORE"
pb.MSG_COUNTRY_ACTIONID_MSG_COUNTRY_WAR_SYNCPLAYERSCORE_ENUM.index = 7
pb.MSG_COUNTRY_ACTIONID_MSG_COUNTRY_WAR_SYNCPLAYERSCORE_ENUM.number = 7
pb.MSG_COUNTRY_ACTIONID_MSG_COUNTRY_WAR_OPERATECOUNTDOWN_ENUM.name = "MSG_COUNTRY_WAR_OPERATECOUNTDOWN"
pb.MSG_COUNTRY_ACTIONID_MSG_COUNTRY_WAR_OPERATECOUNTDOWN_ENUM.index = 8
pb.MSG_COUNTRY_ACTIONID_MSG_COUNTRY_WAR_OPERATECOUNTDOWN_ENUM.number = 8
pb.MSG_COUNTRY_ACTIONID_MSG_COUNTRY_WAR_SYNCFIELDINFO_ENUM.name = "MSG_COUNTRY_WAR_SYNCFIELDINFO"
pb.MSG_COUNTRY_ACTIONID_MSG_COUNTRY_WAR_SYNCFIELDINFO_ENUM.index = 9
pb.MSG_COUNTRY_ACTIONID_MSG_COUNTRY_WAR_SYNCFIELDINFO_ENUM.number = 9
pb.MSG_COUNTRY_ACTIONID_MSG_COUNTRY_WAR_RESULTINFO_ENUM.name = "MSG_COUNTRY_WAR_RESULTINFO"
pb.MSG_COUNTRY_ACTIONID_MSG_COUNTRY_WAR_RESULTINFO_ENUM.index = 10
pb.MSG_COUNTRY_ACTIONID_MSG_COUNTRY_WAR_RESULTINFO_ENUM.number = 10
pb.MSG_COUNTRY_ACTIONID_MSG_COUNTRY_SIGNCOUNTRYKINGWAR_ENUM.name = "MSG_COUNTRY_SIGNCOUNTRYKINGWAR"
pb.MSG_COUNTRY_ACTIONID_MSG_COUNTRY_SIGNCOUNTRYKINGWAR_ENUM.index = 11
pb.MSG_COUNTRY_ACTIONID_MSG_COUNTRY_SIGNCOUNTRYKINGWAR_ENUM.number = 13
pb.MSG_COUNTRY_ACTIONID_MSG_COUNTRY_GETCOUNTRYKINGWARINFO_ENUM.name = "MSG_COUNTRY_GETCOUNTRYKINGWARINFO"
pb.MSG_COUNTRY_ACTIONID_MSG_COUNTRY_GETCOUNTRYKINGWARINFO_ENUM.index = 12
pb.MSG_COUNTRY_ACTIONID_MSG_COUNTRY_GETCOUNTRYKINGWARINFO_ENUM.number = 14
pb.MSG_COUNTRY_ACTIONID_MSG_COUNTRY_KINGWAR_SYNCFIELDINFO_ENUM.name = "MSG_COUNTRY_KINGWAR_SYNCFIELDINFO"
pb.MSG_COUNTRY_ACTIONID_MSG_COUNTRY_KINGWAR_SYNCFIELDINFO_ENUM.index = 13
pb.MSG_COUNTRY_ACTIONID_MSG_COUNTRY_KINGWAR_SYNCFIELDINFO_ENUM.number = 15
pb.MSG_COUNTRY_ACTIONID_MSG_COUNTRY_KINGWAR_SYNCSCOREINFO_ENUM.name = "MSG_COUNTRY_KINGWAR_SYNCSCOREINFO"
pb.MSG_COUNTRY_ACTIONID_MSG_COUNTRY_KINGWAR_SYNCSCOREINFO_ENUM.index = 14
pb.MSG_COUNTRY_ACTIONID_MSG_COUNTRY_KINGWAR_SYNCSCOREINFO_ENUM.number = 16
pb.MSG_COUNTRY_ACTIONID_MSG_COUNTRY_KINGWAR_RESULTINFO_ENUM.name = "MSG_COUNTRY_KINGWAR_RESULTINFO"
pb.MSG_COUNTRY_ACTIONID_MSG_COUNTRY_KINGWAR_RESULTINFO_ENUM.index = 15
pb.MSG_COUNTRY_ACTIONID_MSG_COUNTRY_KINGWAR_RESULTINFO_ENUM.number = 17
pb.MSG_COUNTRY_ACTIONID_MSG_COUNTRY_BATTLEFIELD_MAININFO_ENUM.name = "MSG_COUNTRY_BATTLEFIELD_MAININFO"
pb.MSG_COUNTRY_ACTIONID_MSG_COUNTRY_BATTLEFIELD_MAININFO_ENUM.index = 16
pb.MSG_COUNTRY_ACTIONID_MSG_COUNTRY_BATTLEFIELD_MAININFO_ENUM.number = 18
pb.MSG_COUNTRY_ACTIONID_MSG_COUNTRY_BATTLEFIELD_MAPINFO_ENUM.name = "MSG_COUNTRY_BATTLEFIELD_MAPINFO"
pb.MSG_COUNTRY_ACTIONID_MSG_COUNTRY_BATTLEFIELD_MAPINFO_ENUM.index = 17
pb.MSG_COUNTRY_ACTIONID_MSG_COUNTRY_BATTLEFIELD_MAPINFO_ENUM.number = 19
pb.MSG_COUNTRY_ACTIONID_MSG_COUNTRY_GETPKRECORDINFO_ENUM.name = "MSG_COUNTRY_GETPKRECORDINFO"
pb.MSG_COUNTRY_ACTIONID_MSG_COUNTRY_GETPKRECORDINFO_ENUM.index = 18
pb.MSG_COUNTRY_ACTIONID_MSG_COUNTRY_GETPKRECORDINFO_ENUM.number = 20
pb.MSG_COUNTRY_ACTIONID_MSG_COUNTRY_GETCOUNTRYPRINCESSINFO_ENUM.name = "MSG_COUNTRY_GETCOUNTRYPRINCESSINFO"
pb.MSG_COUNTRY_ACTIONID_MSG_COUNTRY_GETCOUNTRYPRINCESSINFO_ENUM.index = 19
pb.MSG_COUNTRY_ACTIONID_MSG_COUNTRY_GETCOUNTRYPRINCESSINFO_ENUM.number = 21
pb.MSG_COUNTRY_ACTIONID_MSG_COUNTRY_COUNTRYPRINCESSRESULT_ENUM.name = "MSG_COUNTRY_COUNTRYPRINCESSRESULT"
pb.MSG_COUNTRY_ACTIONID_MSG_COUNTRY_COUNTRYPRINCESSRESULT_ENUM.index = 20
pb.MSG_COUNTRY_ACTIONID_MSG_COUNTRY_COUNTRYPRINCESSRESULT_ENUM.number = 22
pb.MSG_COUNTRY_ACTIONID.name = "MSG_COUNTRY_ACTIONID"
pb.MSG_COUNTRY_ACTIONID.full_name = ".MSG_COUNTRY_ACTIONID"
pb.MSG_COUNTRY_ACTIONID.values = {pb.MSG_COUNTRY_ACTIONID_MSG_COUNTRY_NONE_ENUM,pb.MSG_COUNTRY_ACTIONID_MSG_COUNTRY_GETCOUNTRYINFO_ENUM,pb.MSG_COUNTRY_ACTIONID_MSG_COUNTRY_SETNOTICE_ENUM,pb.MSG_COUNTRY_ACTIONID_MSG_COUNTRY_ADDOFFICER_ENUM,pb.MSG_COUNTRY_ACTIONID_MSG_COUNTRY_REMOVEOFFICER_ENUM,pb.MSG_COUNTRY_ACTIONID_MSG_COUNTRY_UPDATEOFFICERJOBINFO_ENUM,pb.MSG_COUNTRY_ACTIONID_MSG_COUNTRY_SETCOUNTRYWAR_ENUM,pb.MSG_COUNTRY_ACTIONID_MSG_COUNTRY_WAR_SYNCPLAYERSCORE_ENUM,pb.MSG_COUNTRY_ACTIONID_MSG_COUNTRY_WAR_OPERATECOUNTDOWN_ENUM,pb.MSG_COUNTRY_ACTIONID_MSG_COUNTRY_WAR_SYNCFIELDINFO_ENUM,pb.MSG_COUNTRY_ACTIONID_MSG_COUNTRY_WAR_RESULTINFO_ENUM,pb.MSG_COUNTRY_ACTIONID_MSG_COUNTRY_SIGNCOUNTRYKINGWAR_ENUM,pb.MSG_COUNTRY_ACTIONID_MSG_COUNTRY_GETCOUNTRYKINGWARINFO_ENUM,pb.MSG_COUNTRY_ACTIONID_MSG_COUNTRY_KINGWAR_SYNCFIELDINFO_ENUM,pb.MSG_COUNTRY_ACTIONID_MSG_COUNTRY_KINGWAR_SYNCSCOREINFO_ENUM,pb.MSG_COUNTRY_ACTIONID_MSG_COUNTRY_KINGWAR_RESULTINFO_ENUM,pb.MSG_COUNTRY_ACTIONID_MSG_COUNTRY_BATTLEFIELD_MAININFO_ENUM,pb.MSG_COUNTRY_ACTIONID_MSG_COUNTRY_BATTLEFIELD_MAPINFO_ENUM,pb.MSG_COUNTRY_ACTIONID_MSG_COUNTRY_GETPKRECORDINFO_ENUM,pb.MSG_COUNTRY_ACTIONID_MSG_COUNTRY_GETCOUNTRYPRINCESSINFO_ENUM,pb.MSG_COUNTRY_ACTIONID_MSG_COUNTRY_COUNTRYPRINCESSRESULT_ENUM}
pb.CS_COUNTRY_GETCOUNTRYINFO_COUNTRYID_FIELD.name = "CountryID"
pb.CS_COUNTRY_GETCOUNTRYINFO_COUNTRYID_FIELD.full_name = ".CS_Country_GetCountryInfo.CountryID"
pb.CS_COUNTRY_GETCOUNTRYINFO_COUNTRYID_FIELD.number = 1
pb.CS_COUNTRY_GETCOUNTRYINFO_COUNTRYID_FIELD.index = 0
pb.CS_COUNTRY_GETCOUNTRYINFO_COUNTRYID_FIELD.label = 2
pb.CS_COUNTRY_GETCOUNTRYINFO_COUNTRYID_FIELD.has_default_value = false
pb.CS_COUNTRY_GETCOUNTRYINFO_COUNTRYID_FIELD.default_value = 0
pb.CS_COUNTRY_GETCOUNTRYINFO_COUNTRYID_FIELD.type = 13
pb.CS_COUNTRY_GETCOUNTRYINFO_COUNTRYID_FIELD.cpp_type = 3

pb.CS_COUNTRY_GETCOUNTRYINFO.name = "CS_Country_GetCountryInfo"
pb.CS_COUNTRY_GETCOUNTRYINFO.full_name = ".CS_Country_GetCountryInfo"
pb.CS_COUNTRY_GETCOUNTRYINFO.nested_types = {}
pb.CS_COUNTRY_GETCOUNTRYINFO.enum_types = {}
pb.CS_COUNTRY_GETCOUNTRYINFO.fields = {pb.CS_COUNTRY_GETCOUNTRYINFO_COUNTRYID_FIELD}
pb.CS_COUNTRY_GETCOUNTRYINFO.is_extendable = false
pb.CS_COUNTRY_GETCOUNTRYINFO.extensions = {}
pb.SC_COUNTRY_GETCOUNTRYINFO_OFFICEDATA_ACTORID_FIELD.name = "ActorID"
pb.SC_COUNTRY_GETCOUNTRYINFO_OFFICEDATA_ACTORID_FIELD.full_name = ".SC_Country_GetCountryInfo.OfficeData.ActorID"
pb.SC_COUNTRY_GETCOUNTRYINFO_OFFICEDATA_ACTORID_FIELD.number = 1
pb.SC_COUNTRY_GETCOUNTRYINFO_OFFICEDATA_ACTORID_FIELD.index = 0
pb.SC_COUNTRY_GETCOUNTRYINFO_OFFICEDATA_ACTORID_FIELD.label = 1
pb.SC_COUNTRY_GETCOUNTRYINFO_OFFICEDATA_ACTORID_FIELD.has_default_value = false
pb.SC_COUNTRY_GETCOUNTRYINFO_OFFICEDATA_ACTORID_FIELD.default_value = 0
pb.SC_COUNTRY_GETCOUNTRYINFO_OFFICEDATA_ACTORID_FIELD.type = 13
pb.SC_COUNTRY_GETCOUNTRYINFO_OFFICEDATA_ACTORID_FIELD.cpp_type = 3

pb.SC_COUNTRY_GETCOUNTRYINFO_OFFICEDATA_ACTORNAME_FIELD.name = "ActorName"
pb.SC_COUNTRY_GETCOUNTRYINFO_OFFICEDATA_ACTORNAME_FIELD.full_name = ".SC_Country_GetCountryInfo.OfficeData.ActorName"
pb.SC_COUNTRY_GETCOUNTRYINFO_OFFICEDATA_ACTORNAME_FIELD.number = 2
pb.SC_COUNTRY_GETCOUNTRYINFO_OFFICEDATA_ACTORNAME_FIELD.index = 1
pb.SC_COUNTRY_GETCOUNTRYINFO_OFFICEDATA_ACTORNAME_FIELD.label = 1
pb.SC_COUNTRY_GETCOUNTRYINFO_OFFICEDATA_ACTORNAME_FIELD.has_default_value = false
pb.SC_COUNTRY_GETCOUNTRYINFO_OFFICEDATA_ACTORNAME_FIELD.default_value = ""
pb.SC_COUNTRY_GETCOUNTRYINFO_OFFICEDATA_ACTORNAME_FIELD.type = 9
pb.SC_COUNTRY_GETCOUNTRYINFO_OFFICEDATA_ACTORNAME_FIELD.cpp_type = 9

pb.SC_COUNTRY_GETCOUNTRYINFO_OFFICEDATA_SOCIETYNAME_FIELD.name = "SocietyName"
pb.SC_COUNTRY_GETCOUNTRYINFO_OFFICEDATA_SOCIETYNAME_FIELD.full_name = ".SC_Country_GetCountryInfo.OfficeData.SocietyName"
pb.SC_COUNTRY_GETCOUNTRYINFO_OFFICEDATA_SOCIETYNAME_FIELD.number = 3
pb.SC_COUNTRY_GETCOUNTRYINFO_OFFICEDATA_SOCIETYNAME_FIELD.index = 2
pb.SC_COUNTRY_GETCOUNTRYINFO_OFFICEDATA_SOCIETYNAME_FIELD.label = 1
pb.SC_COUNTRY_GETCOUNTRYINFO_OFFICEDATA_SOCIETYNAME_FIELD.has_default_value = false
pb.SC_COUNTRY_GETCOUNTRYINFO_OFFICEDATA_SOCIETYNAME_FIELD.default_value = ""
pb.SC_COUNTRY_GETCOUNTRYINFO_OFFICEDATA_SOCIETYNAME_FIELD.type = 9
pb.SC_COUNTRY_GETCOUNTRYINFO_OFFICEDATA_SOCIETYNAME_FIELD.cpp_type = 9

pb.SC_COUNTRY_GETCOUNTRYINFO_OFFICEDATA_LEVEL_FIELD.name = "Level"
pb.SC_COUNTRY_GETCOUNTRYINFO_OFFICEDATA_LEVEL_FIELD.full_name = ".SC_Country_GetCountryInfo.OfficeData.Level"
pb.SC_COUNTRY_GETCOUNTRYINFO_OFFICEDATA_LEVEL_FIELD.number = 4
pb.SC_COUNTRY_GETCOUNTRYINFO_OFFICEDATA_LEVEL_FIELD.index = 3
pb.SC_COUNTRY_GETCOUNTRYINFO_OFFICEDATA_LEVEL_FIELD.label = 1
pb.SC_COUNTRY_GETCOUNTRYINFO_OFFICEDATA_LEVEL_FIELD.has_default_value = false
pb.SC_COUNTRY_GETCOUNTRYINFO_OFFICEDATA_LEVEL_FIELD.default_value = 0
pb.SC_COUNTRY_GETCOUNTRYINFO_OFFICEDATA_LEVEL_FIELD.type = 13
pb.SC_COUNTRY_GETCOUNTRYINFO_OFFICEDATA_LEVEL_FIELD.cpp_type = 3

pb.SC_COUNTRY_GETCOUNTRYINFO_OFFICEDATA_VOCATION_FIELD.name = "Vocation"
pb.SC_COUNTRY_GETCOUNTRYINFO_OFFICEDATA_VOCATION_FIELD.full_name = ".SC_Country_GetCountryInfo.OfficeData.Vocation"
pb.SC_COUNTRY_GETCOUNTRYINFO_OFFICEDATA_VOCATION_FIELD.number = 5
pb.SC_COUNTRY_GETCOUNTRYINFO_OFFICEDATA_VOCATION_FIELD.index = 4
pb.SC_COUNTRY_GETCOUNTRYINFO_OFFICEDATA_VOCATION_FIELD.label = 1
pb.SC_COUNTRY_GETCOUNTRYINFO_OFFICEDATA_VOCATION_FIELD.has_default_value = false
pb.SC_COUNTRY_GETCOUNTRYINFO_OFFICEDATA_VOCATION_FIELD.default_value = 0
pb.SC_COUNTRY_GETCOUNTRYINFO_OFFICEDATA_VOCATION_FIELD.type = 13
pb.SC_COUNTRY_GETCOUNTRYINFO_OFFICEDATA_VOCATION_FIELD.cpp_type = 3

pb.SC_COUNTRY_GETCOUNTRYINFO_OFFICEDATA_POWER_FIELD.name = "Power"
pb.SC_COUNTRY_GETCOUNTRYINFO_OFFICEDATA_POWER_FIELD.full_name = ".SC_Country_GetCountryInfo.OfficeData.Power"
pb.SC_COUNTRY_GETCOUNTRYINFO_OFFICEDATA_POWER_FIELD.number = 6
pb.SC_COUNTRY_GETCOUNTRYINFO_OFFICEDATA_POWER_FIELD.index = 5
pb.SC_COUNTRY_GETCOUNTRYINFO_OFFICEDATA_POWER_FIELD.label = 1
pb.SC_COUNTRY_GETCOUNTRYINFO_OFFICEDATA_POWER_FIELD.has_default_value = false
pb.SC_COUNTRY_GETCOUNTRYINFO_OFFICEDATA_POWER_FIELD.default_value = 0
pb.SC_COUNTRY_GETCOUNTRYINFO_OFFICEDATA_POWER_FIELD.type = 13
pb.SC_COUNTRY_GETCOUNTRYINFO_OFFICEDATA_POWER_FIELD.cpp_type = 3

pb.SC_COUNTRY_GETCOUNTRYINFO_OFFICEDATA_OFFICETYPE_FIELD.name = "OfficeType"
pb.SC_COUNTRY_GETCOUNTRYINFO_OFFICEDATA_OFFICETYPE_FIELD.full_name = ".SC_Country_GetCountryInfo.OfficeData.OfficeType"
pb.SC_COUNTRY_GETCOUNTRYINFO_OFFICEDATA_OFFICETYPE_FIELD.number = 7
pb.SC_COUNTRY_GETCOUNTRYINFO_OFFICEDATA_OFFICETYPE_FIELD.index = 6
pb.SC_COUNTRY_GETCOUNTRYINFO_OFFICEDATA_OFFICETYPE_FIELD.label = 1
pb.SC_COUNTRY_GETCOUNTRYINFO_OFFICEDATA_OFFICETYPE_FIELD.has_default_value = false
pb.SC_COUNTRY_GETCOUNTRYINFO_OFFICEDATA_OFFICETYPE_FIELD.default_value = 0
pb.SC_COUNTRY_GETCOUNTRYINFO_OFFICEDATA_OFFICETYPE_FIELD.type = 13
pb.SC_COUNTRY_GETCOUNTRYINFO_OFFICEDATA_OFFICETYPE_FIELD.cpp_type = 3

pb.SC_COUNTRY_GETCOUNTRYINFO_OFFICEDATA_EQUIPMODELID_FIELD.name = "EquipModelID"
pb.SC_COUNTRY_GETCOUNTRYINFO_OFFICEDATA_EQUIPMODELID_FIELD.full_name = ".SC_Country_GetCountryInfo.OfficeData.EquipModelID"
pb.SC_COUNTRY_GETCOUNTRYINFO_OFFICEDATA_EQUIPMODELID_FIELD.number = 8
pb.SC_COUNTRY_GETCOUNTRYINFO_OFFICEDATA_EQUIPMODELID_FIELD.index = 7
pb.SC_COUNTRY_GETCOUNTRYINFO_OFFICEDATA_EQUIPMODELID_FIELD.label = 1
pb.SC_COUNTRY_GETCOUNTRYINFO_OFFICEDATA_EQUIPMODELID_FIELD.has_default_value = false
pb.SC_COUNTRY_GETCOUNTRYINFO_OFFICEDATA_EQUIPMODELID_FIELD.default_value = 0
pb.SC_COUNTRY_GETCOUNTRYINFO_OFFICEDATA_EQUIPMODELID_FIELD.type = 13
pb.SC_COUNTRY_GETCOUNTRYINFO_OFFICEDATA_EQUIPMODELID_FIELD.cpp_type = 3

pb.SC_COUNTRY_GETCOUNTRYINFO_OFFICEDATA_EQUIPWEAPONID_FIELD.name = "EquipWeaponID"
pb.SC_COUNTRY_GETCOUNTRYINFO_OFFICEDATA_EQUIPWEAPONID_FIELD.full_name = ".SC_Country_GetCountryInfo.OfficeData.EquipWeaponID"
pb.SC_COUNTRY_GETCOUNTRYINFO_OFFICEDATA_EQUIPWEAPONID_FIELD.number = 9
pb.SC_COUNTRY_GETCOUNTRYINFO_OFFICEDATA_EQUIPWEAPONID_FIELD.index = 8
pb.SC_COUNTRY_GETCOUNTRYINFO_OFFICEDATA_EQUIPWEAPONID_FIELD.label = 1
pb.SC_COUNTRY_GETCOUNTRYINFO_OFFICEDATA_EQUIPWEAPONID_FIELD.has_default_value = false
pb.SC_COUNTRY_GETCOUNTRYINFO_OFFICEDATA_EQUIPWEAPONID_FIELD.default_value = 0
pb.SC_COUNTRY_GETCOUNTRYINFO_OFFICEDATA_EQUIPWEAPONID_FIELD.type = 13
pb.SC_COUNTRY_GETCOUNTRYINFO_OFFICEDATA_EQUIPWEAPONID_FIELD.cpp_type = 3

pb.SC_COUNTRY_GETCOUNTRYINFO_OFFICEDATA_EQUIPWINGID_FIELD.name = "EquipWingID"
pb.SC_COUNTRY_GETCOUNTRYINFO_OFFICEDATA_EQUIPWINGID_FIELD.full_name = ".SC_Country_GetCountryInfo.OfficeData.EquipWingID"
pb.SC_COUNTRY_GETCOUNTRYINFO_OFFICEDATA_EQUIPWINGID_FIELD.number = 10
pb.SC_COUNTRY_GETCOUNTRYINFO_OFFICEDATA_EQUIPWINGID_FIELD.index = 9
pb.SC_COUNTRY_GETCOUNTRYINFO_OFFICEDATA_EQUIPWINGID_FIELD.label = 1
pb.SC_COUNTRY_GETCOUNTRYINFO_OFFICEDATA_EQUIPWINGID_FIELD.has_default_value = false
pb.SC_COUNTRY_GETCOUNTRYINFO_OFFICEDATA_EQUIPWINGID_FIELD.default_value = 0
pb.SC_COUNTRY_GETCOUNTRYINFO_OFFICEDATA_EQUIPWINGID_FIELD.type = 13
pb.SC_COUNTRY_GETCOUNTRYINFO_OFFICEDATA_EQUIPWINGID_FIELD.cpp_type = 3

pb.SC_COUNTRY_GETCOUNTRYINFO_OFFICEDATA_GENRE_FIELD.name = "Genre"
pb.SC_COUNTRY_GETCOUNTRYINFO_OFFICEDATA_GENRE_FIELD.full_name = ".SC_Country_GetCountryInfo.OfficeData.Genre"
pb.SC_COUNTRY_GETCOUNTRYINFO_OFFICEDATA_GENRE_FIELD.number = 11
pb.SC_COUNTRY_GETCOUNTRYINFO_OFFICEDATA_GENRE_FIELD.index = 10
pb.SC_COUNTRY_GETCOUNTRYINFO_OFFICEDATA_GENRE_FIELD.label = 1
pb.SC_COUNTRY_GETCOUNTRYINFO_OFFICEDATA_GENRE_FIELD.has_default_value = false
pb.SC_COUNTRY_GETCOUNTRYINFO_OFFICEDATA_GENRE_FIELD.default_value = 0
pb.SC_COUNTRY_GETCOUNTRYINFO_OFFICEDATA_GENRE_FIELD.type = 13
pb.SC_COUNTRY_GETCOUNTRYINFO_OFFICEDATA_GENRE_FIELD.cpp_type = 3

pb.SC_COUNTRY_GETCOUNTRYINFO_OFFICEDATA.name = "OfficeData"
pb.SC_COUNTRY_GETCOUNTRYINFO_OFFICEDATA.full_name = ".SC_Country_GetCountryInfo.OfficeData"
pb.SC_COUNTRY_GETCOUNTRYINFO_OFFICEDATA.nested_types = {}
pb.SC_COUNTRY_GETCOUNTRYINFO_OFFICEDATA.enum_types = {}
pb.SC_COUNTRY_GETCOUNTRYINFO_OFFICEDATA.fields = {pb.SC_COUNTRY_GETCOUNTRYINFO_OFFICEDATA_ACTORID_FIELD, pb.SC_COUNTRY_GETCOUNTRYINFO_OFFICEDATA_ACTORNAME_FIELD, pb.SC_COUNTRY_GETCOUNTRYINFO_OFFICEDATA_SOCIETYNAME_FIELD, pb.SC_COUNTRY_GETCOUNTRYINFO_OFFICEDATA_LEVEL_FIELD, pb.SC_COUNTRY_GETCOUNTRYINFO_OFFICEDATA_VOCATION_FIELD, pb.SC_COUNTRY_GETCOUNTRYINFO_OFFICEDATA_POWER_FIELD, pb.SC_COUNTRY_GETCOUNTRYINFO_OFFICEDATA_OFFICETYPE_FIELD, pb.SC_COUNTRY_GETCOUNTRYINFO_OFFICEDATA_EQUIPMODELID_FIELD, pb.SC_COUNTRY_GETCOUNTRYINFO_OFFICEDATA_EQUIPWEAPONID_FIELD, pb.SC_COUNTRY_GETCOUNTRYINFO_OFFICEDATA_EQUIPWINGID_FIELD, pb.SC_COUNTRY_GETCOUNTRYINFO_OFFICEDATA_GENRE_FIELD}
pb.SC_COUNTRY_GETCOUNTRYINFO_OFFICEDATA.is_extendable = false
pb.SC_COUNTRY_GETCOUNTRYINFO_OFFICEDATA.extensions = {}
pb.SC_COUNTRY_GETCOUNTRYINFO_OFFICEDATA.containing_type = pb.SC_COUNTRY_GETCOUNTRYINFO
pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYWARDATA_SRCCOUNTRYID_FIELD.name = "SrcCountryID"
pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYWARDATA_SRCCOUNTRYID_FIELD.full_name = ".SC_Country_GetCountryInfo.CountryWarData.SrcCountryID"
pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYWARDATA_SRCCOUNTRYID_FIELD.number = 1
pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYWARDATA_SRCCOUNTRYID_FIELD.index = 0
pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYWARDATA_SRCCOUNTRYID_FIELD.label = 1
pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYWARDATA_SRCCOUNTRYID_FIELD.has_default_value = false
pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYWARDATA_SRCCOUNTRYID_FIELD.default_value = 0
pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYWARDATA_SRCCOUNTRYID_FIELD.type = 13
pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYWARDATA_SRCCOUNTRYID_FIELD.cpp_type = 3

pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYWARDATA_TARCOUNTRYID_FIELD.name = "TarCountryID"
pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYWARDATA_TARCOUNTRYID_FIELD.full_name = ".SC_Country_GetCountryInfo.CountryWarData.TarCountryID"
pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYWARDATA_TARCOUNTRYID_FIELD.number = 2
pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYWARDATA_TARCOUNTRYID_FIELD.index = 1
pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYWARDATA_TARCOUNTRYID_FIELD.label = 1
pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYWARDATA_TARCOUNTRYID_FIELD.has_default_value = false
pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYWARDATA_TARCOUNTRYID_FIELD.default_value = 0
pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYWARDATA_TARCOUNTRYID_FIELD.type = 13
pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYWARDATA_TARCOUNTRYID_FIELD.cpp_type = 3

pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYWARDATA_WARTIME_FIELD.name = "WarTime"
pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYWARDATA_WARTIME_FIELD.full_name = ".SC_Country_GetCountryInfo.CountryWarData.WarTime"
pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYWARDATA_WARTIME_FIELD.number = 3
pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYWARDATA_WARTIME_FIELD.index = 2
pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYWARDATA_WARTIME_FIELD.label = 1
pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYWARDATA_WARTIME_FIELD.has_default_value = false
pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYWARDATA_WARTIME_FIELD.default_value = 0
pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYWARDATA_WARTIME_FIELD.type = 13
pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYWARDATA_WARTIME_FIELD.cpp_type = 3

pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYWARDATA.name = "CountryWarData"
pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYWARDATA.full_name = ".SC_Country_GetCountryInfo.CountryWarData"
pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYWARDATA.nested_types = {}
pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYWARDATA.enum_types = {}
pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYWARDATA.fields = {pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYWARDATA_SRCCOUNTRYID_FIELD, pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYWARDATA_TARCOUNTRYID_FIELD, pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYWARDATA_WARTIME_FIELD}
pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYWARDATA.is_extendable = false
pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYWARDATA.extensions = {}
pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYWARDATA.containing_type = pb.SC_COUNTRY_GETCOUNTRYINFO
pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYWARHISTORYDATA_SRCCOUNTRYID_FIELD.name = "SrcCountryID"
pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYWARHISTORYDATA_SRCCOUNTRYID_FIELD.full_name = ".SC_Country_GetCountryInfo.CountryWarHistoryData.SrcCountryID"
pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYWARHISTORYDATA_SRCCOUNTRYID_FIELD.number = 1
pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYWARHISTORYDATA_SRCCOUNTRYID_FIELD.index = 0
pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYWARHISTORYDATA_SRCCOUNTRYID_FIELD.label = 1
pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYWARHISTORYDATA_SRCCOUNTRYID_FIELD.has_default_value = false
pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYWARHISTORYDATA_SRCCOUNTRYID_FIELD.default_value = 0
pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYWARHISTORYDATA_SRCCOUNTRYID_FIELD.type = 13
pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYWARHISTORYDATA_SRCCOUNTRYID_FIELD.cpp_type = 3

pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYWARHISTORYDATA_TARCOUNTRYID_FIELD.name = "TarCountryID"
pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYWARHISTORYDATA_TARCOUNTRYID_FIELD.full_name = ".SC_Country_GetCountryInfo.CountryWarHistoryData.TarCountryID"
pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYWARHISTORYDATA_TARCOUNTRYID_FIELD.number = 2
pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYWARHISTORYDATA_TARCOUNTRYID_FIELD.index = 1
pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYWARHISTORYDATA_TARCOUNTRYID_FIELD.label = 1
pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYWARHISTORYDATA_TARCOUNTRYID_FIELD.has_default_value = false
pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYWARHISTORYDATA_TARCOUNTRYID_FIELD.default_value = 0
pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYWARHISTORYDATA_TARCOUNTRYID_FIELD.type = 13
pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYWARHISTORYDATA_TARCOUNTRYID_FIELD.cpp_type = 3

pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYWARHISTORYDATA_WARTIME_FIELD.name = "WarTime"
pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYWARHISTORYDATA_WARTIME_FIELD.full_name = ".SC_Country_GetCountryInfo.CountryWarHistoryData.WarTime"
pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYWARHISTORYDATA_WARTIME_FIELD.number = 3
pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYWARHISTORYDATA_WARTIME_FIELD.index = 2
pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYWARHISTORYDATA_WARTIME_FIELD.label = 1
pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYWARHISTORYDATA_WARTIME_FIELD.has_default_value = false
pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYWARHISTORYDATA_WARTIME_FIELD.default_value = 0
pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYWARHISTORYDATA_WARTIME_FIELD.type = 13
pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYWARHISTORYDATA_WARTIME_FIELD.cpp_type = 3

pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYWARHISTORYDATA_WINCOUNTRYID_FIELD.name = "WinCountryID"
pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYWARHISTORYDATA_WINCOUNTRYID_FIELD.full_name = ".SC_Country_GetCountryInfo.CountryWarHistoryData.WinCountryID"
pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYWARHISTORYDATA_WINCOUNTRYID_FIELD.number = 4
pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYWARHISTORYDATA_WINCOUNTRYID_FIELD.index = 3
pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYWARHISTORYDATA_WINCOUNTRYID_FIELD.label = 1
pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYWARHISTORYDATA_WINCOUNTRYID_FIELD.has_default_value = false
pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYWARHISTORYDATA_WINCOUNTRYID_FIELD.default_value = 0
pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYWARHISTORYDATA_WINCOUNTRYID_FIELD.type = 13
pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYWARHISTORYDATA_WINCOUNTRYID_FIELD.cpp_type = 3

pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYWARHISTORYDATA.name = "CountryWarHistoryData"
pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYWARHISTORYDATA.full_name = ".SC_Country_GetCountryInfo.CountryWarHistoryData"
pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYWARHISTORYDATA.nested_types = {}
pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYWARHISTORYDATA.enum_types = {}
pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYWARHISTORYDATA.fields = {pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYWARHISTORYDATA_SRCCOUNTRYID_FIELD, pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYWARHISTORYDATA_TARCOUNTRYID_FIELD, pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYWARHISTORYDATA_WARTIME_FIELD, pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYWARHISTORYDATA_WINCOUNTRYID_FIELD}
pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYWARHISTORYDATA.is_extendable = false
pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYWARHISTORYDATA.extensions = {}
pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYWARHISTORYDATA.containing_type = pb.SC_COUNTRY_GETCOUNTRYINFO
pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYID_FIELD.name = "CountryID"
pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYID_FIELD.full_name = ".SC_Country_GetCountryInfo.CountryID"
pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYID_FIELD.number = 1
pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYID_FIELD.index = 0
pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYID_FIELD.label = 2
pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYID_FIELD.has_default_value = false
pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYID_FIELD.default_value = 0
pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYID_FIELD.type = 13
pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYID_FIELD.cpp_type = 3

pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYPOWER_FIELD.name = "CountryPower"
pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYPOWER_FIELD.full_name = ".SC_Country_GetCountryInfo.CountryPower"
pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYPOWER_FIELD.number = 2
pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYPOWER_FIELD.index = 1
pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYPOWER_FIELD.label = 1
pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYPOWER_FIELD.has_default_value = false
pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYPOWER_FIELD.default_value = 0
pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYPOWER_FIELD.type = 13
pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYPOWER_FIELD.cpp_type = 3

pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYPOWERRANK_FIELD.name = "CountryPowerRank"
pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYPOWERRANK_FIELD.full_name = ".SC_Country_GetCountryInfo.CountryPowerRank"
pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYPOWERRANK_FIELD.number = 3
pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYPOWERRANK_FIELD.index = 2
pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYPOWERRANK_FIELD.label = 1
pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYPOWERRANK_FIELD.has_default_value = false
pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYPOWERRANK_FIELD.default_value = 0
pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYPOWERRANK_FIELD.type = 13
pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYPOWERRANK_FIELD.cpp_type = 3

pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYMONEY_FIELD.name = "CountryMoney"
pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYMONEY_FIELD.full_name = ".SC_Country_GetCountryInfo.CountryMoney"
pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYMONEY_FIELD.number = 4
pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYMONEY_FIELD.index = 3
pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYMONEY_FIELD.label = 1
pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYMONEY_FIELD.has_default_value = false
pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYMONEY_FIELD.default_value = 0
pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYMONEY_FIELD.type = 13
pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYMONEY_FIELD.cpp_type = 3

pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYRESINFO_FIELD.name = "CountryResInfo"
pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYRESINFO_FIELD.full_name = ".SC_Country_GetCountryInfo.CountryResInfo"
pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYRESINFO_FIELD.number = 5
pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYRESINFO_FIELD.index = 4
pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYRESINFO_FIELD.label = 1
pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYRESINFO_FIELD.has_default_value = false
pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYRESINFO_FIELD.default_value = 0
pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYRESINFO_FIELD.type = 13
pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYRESINFO_FIELD.cpp_type = 3

pb.SC_COUNTRY_GETCOUNTRYINFO_NOTICE_FIELD.name = "Notice"
pb.SC_COUNTRY_GETCOUNTRYINFO_NOTICE_FIELD.full_name = ".SC_Country_GetCountryInfo.Notice"
pb.SC_COUNTRY_GETCOUNTRYINFO_NOTICE_FIELD.number = 6
pb.SC_COUNTRY_GETCOUNTRYINFO_NOTICE_FIELD.index = 5
pb.SC_COUNTRY_GETCOUNTRYINFO_NOTICE_FIELD.label = 1
pb.SC_COUNTRY_GETCOUNTRYINFO_NOTICE_FIELD.has_default_value = false
pb.SC_COUNTRY_GETCOUNTRYINFO_NOTICE_FIELD.default_value = ""
pb.SC_COUNTRY_GETCOUNTRYINFO_NOTICE_FIELD.type = 9
pb.SC_COUNTRY_GETCOUNTRYINFO_NOTICE_FIELD.cpp_type = 9

pb.SC_COUNTRY_GETCOUNTRYINFO_NEXTCOUNTRYWARSRC_FIELD.name = "NextCountryWarSrc"
pb.SC_COUNTRY_GETCOUNTRYINFO_NEXTCOUNTRYWARSRC_FIELD.full_name = ".SC_Country_GetCountryInfo.NextCountryWarSrc"
pb.SC_COUNTRY_GETCOUNTRYINFO_NEXTCOUNTRYWARSRC_FIELD.number = 7
pb.SC_COUNTRY_GETCOUNTRYINFO_NEXTCOUNTRYWARSRC_FIELD.index = 6
pb.SC_COUNTRY_GETCOUNTRYINFO_NEXTCOUNTRYWARSRC_FIELD.label = 1
pb.SC_COUNTRY_GETCOUNTRYINFO_NEXTCOUNTRYWARSRC_FIELD.has_default_value = false
pb.SC_COUNTRY_GETCOUNTRYINFO_NEXTCOUNTRYWARSRC_FIELD.default_value = 0
pb.SC_COUNTRY_GETCOUNTRYINFO_NEXTCOUNTRYWARSRC_FIELD.type = 13
pb.SC_COUNTRY_GETCOUNTRYINFO_NEXTCOUNTRYWARSRC_FIELD.cpp_type = 3

pb.SC_COUNTRY_GETCOUNTRYINFO_NEXTCOUNTRYWARTAR_FIELD.name = "NextCountryWarTar"
pb.SC_COUNTRY_GETCOUNTRYINFO_NEXTCOUNTRYWARTAR_FIELD.full_name = ".SC_Country_GetCountryInfo.NextCountryWarTar"
pb.SC_COUNTRY_GETCOUNTRYINFO_NEXTCOUNTRYWARTAR_FIELD.number = 8
pb.SC_COUNTRY_GETCOUNTRYINFO_NEXTCOUNTRYWARTAR_FIELD.index = 7
pb.SC_COUNTRY_GETCOUNTRYINFO_NEXTCOUNTRYWARTAR_FIELD.label = 1
pb.SC_COUNTRY_GETCOUNTRYINFO_NEXTCOUNTRYWARTAR_FIELD.has_default_value = false
pb.SC_COUNTRY_GETCOUNTRYINFO_NEXTCOUNTRYWARTAR_FIELD.default_value = 0
pb.SC_COUNTRY_GETCOUNTRYINFO_NEXTCOUNTRYWARTAR_FIELD.type = 13
pb.SC_COUNTRY_GETCOUNTRYINFO_NEXTCOUNTRYWARTAR_FIELD.cpp_type = 3

pb.SC_COUNTRY_GETCOUNTRYINFO_OFFICERLIST_FIELD.name = "OfficerList"
pb.SC_COUNTRY_GETCOUNTRYINFO_OFFICERLIST_FIELD.full_name = ".SC_Country_GetCountryInfo.OfficerList"
pb.SC_COUNTRY_GETCOUNTRYINFO_OFFICERLIST_FIELD.number = 9
pb.SC_COUNTRY_GETCOUNTRYINFO_OFFICERLIST_FIELD.index = 8
pb.SC_COUNTRY_GETCOUNTRYINFO_OFFICERLIST_FIELD.label = 3
pb.SC_COUNTRY_GETCOUNTRYINFO_OFFICERLIST_FIELD.has_default_value = false
pb.SC_COUNTRY_GETCOUNTRYINFO_OFFICERLIST_FIELD.default_value = {}
pb.SC_COUNTRY_GETCOUNTRYINFO_OFFICERLIST_FIELD.message_type = pb.SC_COUNTRY_GETCOUNTRYINFO_OFFICEDATA
pb.SC_COUNTRY_GETCOUNTRYINFO_OFFICERLIST_FIELD.type = 11
pb.SC_COUNTRY_GETCOUNTRYINFO_OFFICERLIST_FIELD.cpp_type = 10

pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYWARLIST_FIELD.name = "CountryWarList"
pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYWARLIST_FIELD.full_name = ".SC_Country_GetCountryInfo.CountryWarList"
pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYWARLIST_FIELD.number = 10
pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYWARLIST_FIELD.index = 9
pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYWARLIST_FIELD.label = 3
pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYWARLIST_FIELD.has_default_value = false
pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYWARLIST_FIELD.default_value = {}
pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYWARLIST_FIELD.message_type = pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYWARDATA
pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYWARLIST_FIELD.type = 11
pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYWARLIST_FIELD.cpp_type = 10

pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYWARHISTORYLIST_FIELD.name = "CountryWarHistoryList"
pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYWARHISTORYLIST_FIELD.full_name = ".SC_Country_GetCountryInfo.CountryWarHistoryList"
pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYWARHISTORYLIST_FIELD.number = 11
pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYWARHISTORYLIST_FIELD.index = 10
pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYWARHISTORYLIST_FIELD.label = 3
pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYWARHISTORYLIST_FIELD.has_default_value = false
pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYWARHISTORYLIST_FIELD.default_value = {}
pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYWARHISTORYLIST_FIELD.message_type = pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYWARHISTORYDATA
pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYWARHISTORYLIST_FIELD.type = 11
pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYWARHISTORYLIST_FIELD.cpp_type = 10

pb.SC_COUNTRY_GETCOUNTRYINFO.name = "SC_Country_GetCountryInfo"
pb.SC_COUNTRY_GETCOUNTRYINFO.full_name = ".SC_Country_GetCountryInfo"
pb.SC_COUNTRY_GETCOUNTRYINFO.nested_types = {pb.SC_COUNTRY_GETCOUNTRYINFO_OFFICEDATA, pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYWARDATA, pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYWARHISTORYDATA}
pb.SC_COUNTRY_GETCOUNTRYINFO.enum_types = {}
pb.SC_COUNTRY_GETCOUNTRYINFO.fields = {pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYID_FIELD, pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYPOWER_FIELD, pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYPOWERRANK_FIELD, pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYMONEY_FIELD, pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYRESINFO_FIELD, pb.SC_COUNTRY_GETCOUNTRYINFO_NOTICE_FIELD, pb.SC_COUNTRY_GETCOUNTRYINFO_NEXTCOUNTRYWARSRC_FIELD, pb.SC_COUNTRY_GETCOUNTRYINFO_NEXTCOUNTRYWARTAR_FIELD, pb.SC_COUNTRY_GETCOUNTRYINFO_OFFICERLIST_FIELD, pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYWARLIST_FIELD, pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYWARHISTORYLIST_FIELD}
pb.SC_COUNTRY_GETCOUNTRYINFO.is_extendable = false
pb.SC_COUNTRY_GETCOUNTRYINFO.extensions = {}
pb.CS_COUNTRY_SETNOTICE_NOTICE_FIELD.name = "Notice"
pb.CS_COUNTRY_SETNOTICE_NOTICE_FIELD.full_name = ".CS_Country_SetNotice.Notice"
pb.CS_COUNTRY_SETNOTICE_NOTICE_FIELD.number = 1
pb.CS_COUNTRY_SETNOTICE_NOTICE_FIELD.index = 0
pb.CS_COUNTRY_SETNOTICE_NOTICE_FIELD.label = 2
pb.CS_COUNTRY_SETNOTICE_NOTICE_FIELD.has_default_value = false
pb.CS_COUNTRY_SETNOTICE_NOTICE_FIELD.default_value = ""
pb.CS_COUNTRY_SETNOTICE_NOTICE_FIELD.type = 9
pb.CS_COUNTRY_SETNOTICE_NOTICE_FIELD.cpp_type = 9

pb.CS_COUNTRY_SETNOTICE.name = "CS_Country_SetNotice"
pb.CS_COUNTRY_SETNOTICE.full_name = ".CS_Country_SetNotice"
pb.CS_COUNTRY_SETNOTICE.nested_types = {}
pb.CS_COUNTRY_SETNOTICE.enum_types = {}
pb.CS_COUNTRY_SETNOTICE.fields = {pb.CS_COUNTRY_SETNOTICE_NOTICE_FIELD}
pb.CS_COUNTRY_SETNOTICE.is_extendable = false
pb.CS_COUNTRY_SETNOTICE.extensions = {}
pb.CS_COUNTRY_ADDOFFICER_ACTORID_FIELD.name = "ActorID"
pb.CS_COUNTRY_ADDOFFICER_ACTORID_FIELD.full_name = ".CS_Country_AddOfficer.ActorID"
pb.CS_COUNTRY_ADDOFFICER_ACTORID_FIELD.number = 1
pb.CS_COUNTRY_ADDOFFICER_ACTORID_FIELD.index = 0
pb.CS_COUNTRY_ADDOFFICER_ACTORID_FIELD.label = 2
pb.CS_COUNTRY_ADDOFFICER_ACTORID_FIELD.has_default_value = false
pb.CS_COUNTRY_ADDOFFICER_ACTORID_FIELD.default_value = 0
pb.CS_COUNTRY_ADDOFFICER_ACTORID_FIELD.type = 13
pb.CS_COUNTRY_ADDOFFICER_ACTORID_FIELD.cpp_type = 3

pb.CS_COUNTRY_ADDOFFICER_OFFICETYPE_FIELD.name = "OfficeType"
pb.CS_COUNTRY_ADDOFFICER_OFFICETYPE_FIELD.full_name = ".CS_Country_AddOfficer.OfficeType"
pb.CS_COUNTRY_ADDOFFICER_OFFICETYPE_FIELD.number = 2
pb.CS_COUNTRY_ADDOFFICER_OFFICETYPE_FIELD.index = 1
pb.CS_COUNTRY_ADDOFFICER_OFFICETYPE_FIELD.label = 2
pb.CS_COUNTRY_ADDOFFICER_OFFICETYPE_FIELD.has_default_value = false
pb.CS_COUNTRY_ADDOFFICER_OFFICETYPE_FIELD.default_value = 0
pb.CS_COUNTRY_ADDOFFICER_OFFICETYPE_FIELD.type = 13
pb.CS_COUNTRY_ADDOFFICER_OFFICETYPE_FIELD.cpp_type = 3

pb.CS_COUNTRY_ADDOFFICER.name = "CS_Country_AddOfficer"
pb.CS_COUNTRY_ADDOFFICER.full_name = ".CS_Country_AddOfficer"
pb.CS_COUNTRY_ADDOFFICER.nested_types = {}
pb.CS_COUNTRY_ADDOFFICER.enum_types = {}
pb.CS_COUNTRY_ADDOFFICER.fields = {pb.CS_COUNTRY_ADDOFFICER_ACTORID_FIELD, pb.CS_COUNTRY_ADDOFFICER_OFFICETYPE_FIELD}
pb.CS_COUNTRY_ADDOFFICER.is_extendable = false
pb.CS_COUNTRY_ADDOFFICER.extensions = {}
pb.CS_COUNTRY_REMOVEOFFICER_ACTORID_FIELD.name = "ActorID"
pb.CS_COUNTRY_REMOVEOFFICER_ACTORID_FIELD.full_name = ".CS_Country_RemoveOfficer.ActorID"
pb.CS_COUNTRY_REMOVEOFFICER_ACTORID_FIELD.number = 1
pb.CS_COUNTRY_REMOVEOFFICER_ACTORID_FIELD.index = 0
pb.CS_COUNTRY_REMOVEOFFICER_ACTORID_FIELD.label = 2
pb.CS_COUNTRY_REMOVEOFFICER_ACTORID_FIELD.has_default_value = false
pb.CS_COUNTRY_REMOVEOFFICER_ACTORID_FIELD.default_value = 0
pb.CS_COUNTRY_REMOVEOFFICER_ACTORID_FIELD.type = 13
pb.CS_COUNTRY_REMOVEOFFICER_ACTORID_FIELD.cpp_type = 3

pb.CS_COUNTRY_REMOVEOFFICER.name = "CS_Country_RemoveOfficer"
pb.CS_COUNTRY_REMOVEOFFICER.full_name = ".CS_Country_RemoveOfficer"
pb.CS_COUNTRY_REMOVEOFFICER.nested_types = {}
pb.CS_COUNTRY_REMOVEOFFICER.enum_types = {}
pb.CS_COUNTRY_REMOVEOFFICER.fields = {pb.CS_COUNTRY_REMOVEOFFICER_ACTORID_FIELD}
pb.CS_COUNTRY_REMOVEOFFICER.is_extendable = false
pb.CS_COUNTRY_REMOVEOFFICER.extensions = {}
pb.SC_COUNTRY_UPDATEOFFICERJOBINFO_OFFICETYPE_FIELD.name = "OfficeType"
pb.SC_COUNTRY_UPDATEOFFICERJOBINFO_OFFICETYPE_FIELD.full_name = ".SC_Country_UpdateOfficerJobInfo.OfficeType"
pb.SC_COUNTRY_UPDATEOFFICERJOBINFO_OFFICETYPE_FIELD.number = 1
pb.SC_COUNTRY_UPDATEOFFICERJOBINFO_OFFICETYPE_FIELD.index = 0
pb.SC_COUNTRY_UPDATEOFFICERJOBINFO_OFFICETYPE_FIELD.label = 2
pb.SC_COUNTRY_UPDATEOFFICERJOBINFO_OFFICETYPE_FIELD.has_default_value = false
pb.SC_COUNTRY_UPDATEOFFICERJOBINFO_OFFICETYPE_FIELD.default_value = 0
pb.SC_COUNTRY_UPDATEOFFICERJOBINFO_OFFICETYPE_FIELD.type = 13
pb.SC_COUNTRY_UPDATEOFFICERJOBINFO_OFFICETYPE_FIELD.cpp_type = 3

pb.SC_COUNTRY_UPDATEOFFICERJOBINFO_BANTALKCOUNT_FIELD.name = "BanTalkCount"
pb.SC_COUNTRY_UPDATEOFFICERJOBINFO_BANTALKCOUNT_FIELD.full_name = ".SC_Country_UpdateOfficerJobInfo.BanTalkCount"
pb.SC_COUNTRY_UPDATEOFFICERJOBINFO_BANTALKCOUNT_FIELD.number = 2
pb.SC_COUNTRY_UPDATEOFFICERJOBINFO_BANTALKCOUNT_FIELD.index = 1
pb.SC_COUNTRY_UPDATEOFFICERJOBINFO_BANTALKCOUNT_FIELD.label = 2
pb.SC_COUNTRY_UPDATEOFFICERJOBINFO_BANTALKCOUNT_FIELD.has_default_value = false
pb.SC_COUNTRY_UPDATEOFFICERJOBINFO_BANTALKCOUNT_FIELD.default_value = 0
pb.SC_COUNTRY_UPDATEOFFICERJOBINFO_BANTALKCOUNT_FIELD.type = 5
pb.SC_COUNTRY_UPDATEOFFICERJOBINFO_BANTALKCOUNT_FIELD.cpp_type = 1

pb.SC_COUNTRY_UPDATEOFFICERJOBINFO_CALLUPCOUNT_FIELD.name = "CallUpCount"
pb.SC_COUNTRY_UPDATEOFFICERJOBINFO_CALLUPCOUNT_FIELD.full_name = ".SC_Country_UpdateOfficerJobInfo.CallUpCount"
pb.SC_COUNTRY_UPDATEOFFICERJOBINFO_CALLUPCOUNT_FIELD.number = 3
pb.SC_COUNTRY_UPDATEOFFICERJOBINFO_CALLUPCOUNT_FIELD.index = 2
pb.SC_COUNTRY_UPDATEOFFICERJOBINFO_CALLUPCOUNT_FIELD.label = 2
pb.SC_COUNTRY_UPDATEOFFICERJOBINFO_CALLUPCOUNT_FIELD.has_default_value = false
pb.SC_COUNTRY_UPDATEOFFICERJOBINFO_CALLUPCOUNT_FIELD.default_value = 0
pb.SC_COUNTRY_UPDATEOFFICERJOBINFO_CALLUPCOUNT_FIELD.type = 5
pb.SC_COUNTRY_UPDATEOFFICERJOBINFO_CALLUPCOUNT_FIELD.cpp_type = 1

pb.SC_COUNTRY_UPDATEOFFICERJOBINFO_GETWELFARECOUNT_FIELD.name = "GetWelfareCount"
pb.SC_COUNTRY_UPDATEOFFICERJOBINFO_GETWELFARECOUNT_FIELD.full_name = ".SC_Country_UpdateOfficerJobInfo.GetWelfareCount"
pb.SC_COUNTRY_UPDATEOFFICERJOBINFO_GETWELFARECOUNT_FIELD.number = 4
pb.SC_COUNTRY_UPDATEOFFICERJOBINFO_GETWELFARECOUNT_FIELD.index = 3
pb.SC_COUNTRY_UPDATEOFFICERJOBINFO_GETWELFARECOUNT_FIELD.label = 2
pb.SC_COUNTRY_UPDATEOFFICERJOBINFO_GETWELFARECOUNT_FIELD.has_default_value = false
pb.SC_COUNTRY_UPDATEOFFICERJOBINFO_GETWELFARECOUNT_FIELD.default_value = 0
pb.SC_COUNTRY_UPDATEOFFICERJOBINFO_GETWELFARECOUNT_FIELD.type = 5
pb.SC_COUNTRY_UPDATEOFFICERJOBINFO_GETWELFARECOUNT_FIELD.cpp_type = 1

pb.SC_COUNTRY_UPDATEOFFICERJOBINFO_TELEPORTCOUNT_FIELD.name = "TeleportCount"
pb.SC_COUNTRY_UPDATEOFFICERJOBINFO_TELEPORTCOUNT_FIELD.full_name = ".SC_Country_UpdateOfficerJobInfo.TeleportCount"
pb.SC_COUNTRY_UPDATEOFFICERJOBINFO_TELEPORTCOUNT_FIELD.number = 5
pb.SC_COUNTRY_UPDATEOFFICERJOBINFO_TELEPORTCOUNT_FIELD.index = 4
pb.SC_COUNTRY_UPDATEOFFICERJOBINFO_TELEPORTCOUNT_FIELD.label = 2
pb.SC_COUNTRY_UPDATEOFFICERJOBINFO_TELEPORTCOUNT_FIELD.has_default_value = false
pb.SC_COUNTRY_UPDATEOFFICERJOBINFO_TELEPORTCOUNT_FIELD.default_value = 0
pb.SC_COUNTRY_UPDATEOFFICERJOBINFO_TELEPORTCOUNT_FIELD.type = 5
pb.SC_COUNTRY_UPDATEOFFICERJOBINFO_TELEPORTCOUNT_FIELD.cpp_type = 1

pb.SC_COUNTRY_UPDATEOFFICERJOBINFO.name = "SC_Country_UpdateOfficerJobInfo"
pb.SC_COUNTRY_UPDATEOFFICERJOBINFO.full_name = ".SC_Country_UpdateOfficerJobInfo"
pb.SC_COUNTRY_UPDATEOFFICERJOBINFO.nested_types = {}
pb.SC_COUNTRY_UPDATEOFFICERJOBINFO.enum_types = {}
pb.SC_COUNTRY_UPDATEOFFICERJOBINFO.fields = {pb.SC_COUNTRY_UPDATEOFFICERJOBINFO_OFFICETYPE_FIELD, pb.SC_COUNTRY_UPDATEOFFICERJOBINFO_BANTALKCOUNT_FIELD, pb.SC_COUNTRY_UPDATEOFFICERJOBINFO_CALLUPCOUNT_FIELD, pb.SC_COUNTRY_UPDATEOFFICERJOBINFO_GETWELFARECOUNT_FIELD, pb.SC_COUNTRY_UPDATEOFFICERJOBINFO_TELEPORTCOUNT_FIELD}
pb.SC_COUNTRY_UPDATEOFFICERJOBINFO.is_extendable = false
pb.SC_COUNTRY_UPDATEOFFICERJOBINFO.extensions = {}
pb.CS_COUNTRY_SETCOUNTRYWAR_COUNTRYID_FIELD.name = "CountryID"
pb.CS_COUNTRY_SETCOUNTRYWAR_COUNTRYID_FIELD.full_name = ".CS_Country_SetCountryWar.CountryID"
pb.CS_COUNTRY_SETCOUNTRYWAR_COUNTRYID_FIELD.number = 1
pb.CS_COUNTRY_SETCOUNTRYWAR_COUNTRYID_FIELD.index = 0
pb.CS_COUNTRY_SETCOUNTRYWAR_COUNTRYID_FIELD.label = 2
pb.CS_COUNTRY_SETCOUNTRYWAR_COUNTRYID_FIELD.has_default_value = false
pb.CS_COUNTRY_SETCOUNTRYWAR_COUNTRYID_FIELD.default_value = 0
pb.CS_COUNTRY_SETCOUNTRYWAR_COUNTRYID_FIELD.type = 13
pb.CS_COUNTRY_SETCOUNTRYWAR_COUNTRYID_FIELD.cpp_type = 3

pb.CS_COUNTRY_SETCOUNTRYWAR_WARDATETYPE_FIELD.name = "WarDateType"
pb.CS_COUNTRY_SETCOUNTRYWAR_WARDATETYPE_FIELD.full_name = ".CS_Country_SetCountryWar.WarDateType"
pb.CS_COUNTRY_SETCOUNTRYWAR_WARDATETYPE_FIELD.number = 2
pb.CS_COUNTRY_SETCOUNTRYWAR_WARDATETYPE_FIELD.index = 1
pb.CS_COUNTRY_SETCOUNTRYWAR_WARDATETYPE_FIELD.label = 2
pb.CS_COUNTRY_SETCOUNTRYWAR_WARDATETYPE_FIELD.has_default_value = false
pb.CS_COUNTRY_SETCOUNTRYWAR_WARDATETYPE_FIELD.default_value = 0
pb.CS_COUNTRY_SETCOUNTRYWAR_WARDATETYPE_FIELD.type = 13
pb.CS_COUNTRY_SETCOUNTRYWAR_WARDATETYPE_FIELD.cpp_type = 3

pb.CS_COUNTRY_SETCOUNTRYWAR.name = "CS_Country_SetCountryWar"
pb.CS_COUNTRY_SETCOUNTRYWAR.full_name = ".CS_Country_SetCountryWar"
pb.CS_COUNTRY_SETCOUNTRYWAR.nested_types = {}
pb.CS_COUNTRY_SETCOUNTRYWAR.enum_types = {}
pb.CS_COUNTRY_SETCOUNTRYWAR.fields = {pb.CS_COUNTRY_SETCOUNTRYWAR_COUNTRYID_FIELD, pb.CS_COUNTRY_SETCOUNTRYWAR_WARDATETYPE_FIELD}
pb.CS_COUNTRY_SETCOUNTRYWAR.is_extendable = false
pb.CS_COUNTRY_SETCOUNTRYWAR.extensions = {}
pb.SC_COUNTRY_WARSYNCPLAYERSCORE_ACTORID_FIELD.name = "ActorID"
pb.SC_COUNTRY_WARSYNCPLAYERSCORE_ACTORID_FIELD.full_name = ".SC_Country_WarSyncPlayerScore.ActorID"
pb.SC_COUNTRY_WARSYNCPLAYERSCORE_ACTORID_FIELD.number = 1
pb.SC_COUNTRY_WARSYNCPLAYERSCORE_ACTORID_FIELD.index = 0
pb.SC_COUNTRY_WARSYNCPLAYERSCORE_ACTORID_FIELD.label = 1
pb.SC_COUNTRY_WARSYNCPLAYERSCORE_ACTORID_FIELD.has_default_value = false
pb.SC_COUNTRY_WARSYNCPLAYERSCORE_ACTORID_FIELD.default_value = 0
pb.SC_COUNTRY_WARSYNCPLAYERSCORE_ACTORID_FIELD.type = 13
pb.SC_COUNTRY_WARSYNCPLAYERSCORE_ACTORID_FIELD.cpp_type = 3

pb.SC_COUNTRY_WARSYNCPLAYERSCORE_SCORE_FIELD.name = "Score"
pb.SC_COUNTRY_WARSYNCPLAYERSCORE_SCORE_FIELD.full_name = ".SC_Country_WarSyncPlayerScore.Score"
pb.SC_COUNTRY_WARSYNCPLAYERSCORE_SCORE_FIELD.number = 2
pb.SC_COUNTRY_WARSYNCPLAYERSCORE_SCORE_FIELD.index = 1
pb.SC_COUNTRY_WARSYNCPLAYERSCORE_SCORE_FIELD.label = 1
pb.SC_COUNTRY_WARSYNCPLAYERSCORE_SCORE_FIELD.has_default_value = false
pb.SC_COUNTRY_WARSYNCPLAYERSCORE_SCORE_FIELD.default_value = 0
pb.SC_COUNTRY_WARSYNCPLAYERSCORE_SCORE_FIELD.type = 13
pb.SC_COUNTRY_WARSYNCPLAYERSCORE_SCORE_FIELD.cpp_type = 3

pb.SC_COUNTRY_WARSYNCPLAYERSCORE_KILLNUM_FIELD.name = "KillNum"
pb.SC_COUNTRY_WARSYNCPLAYERSCORE_KILLNUM_FIELD.full_name = ".SC_Country_WarSyncPlayerScore.KillNum"
pb.SC_COUNTRY_WARSYNCPLAYERSCORE_KILLNUM_FIELD.number = 3
pb.SC_COUNTRY_WARSYNCPLAYERSCORE_KILLNUM_FIELD.index = 2
pb.SC_COUNTRY_WARSYNCPLAYERSCORE_KILLNUM_FIELD.label = 1
pb.SC_COUNTRY_WARSYNCPLAYERSCORE_KILLNUM_FIELD.has_default_value = false
pb.SC_COUNTRY_WARSYNCPLAYERSCORE_KILLNUM_FIELD.default_value = 0
pb.SC_COUNTRY_WARSYNCPLAYERSCORE_KILLNUM_FIELD.type = 13
pb.SC_COUNTRY_WARSYNCPLAYERSCORE_KILLNUM_FIELD.cpp_type = 3

pb.SC_COUNTRY_WARSYNCPLAYERSCORE.name = "SC_Country_WarSyncPlayerScore"
pb.SC_COUNTRY_WARSYNCPLAYERSCORE.full_name = ".SC_Country_WarSyncPlayerScore"
pb.SC_COUNTRY_WARSYNCPLAYERSCORE.nested_types = {}
pb.SC_COUNTRY_WARSYNCPLAYERSCORE.enum_types = {}
pb.SC_COUNTRY_WARSYNCPLAYERSCORE.fields = {pb.SC_COUNTRY_WARSYNCPLAYERSCORE_ACTORID_FIELD, pb.SC_COUNTRY_WARSYNCPLAYERSCORE_SCORE_FIELD, pb.SC_COUNTRY_WARSYNCPLAYERSCORE_KILLNUM_FIELD}
pb.SC_COUNTRY_WARSYNCPLAYERSCORE.is_extendable = false
pb.SC_COUNTRY_WARSYNCPLAYERSCORE.extensions = {}
pb.SC_COUNTRY_WAROPERATECOUNTDOWN_ACTORID_FIELD.name = "ActorID"
pb.SC_COUNTRY_WAROPERATECOUNTDOWN_ACTORID_FIELD.full_name = ".SC_Country_WarOperateCountdown.ActorID"
pb.SC_COUNTRY_WAROPERATECOUNTDOWN_ACTORID_FIELD.number = 1
pb.SC_COUNTRY_WAROPERATECOUNTDOWN_ACTORID_FIELD.index = 0
pb.SC_COUNTRY_WAROPERATECOUNTDOWN_ACTORID_FIELD.label = 1
pb.SC_COUNTRY_WAROPERATECOUNTDOWN_ACTORID_FIELD.has_default_value = false
pb.SC_COUNTRY_WAROPERATECOUNTDOWN_ACTORID_FIELD.default_value = 0
pb.SC_COUNTRY_WAROPERATECOUNTDOWN_ACTORID_FIELD.type = 13
pb.SC_COUNTRY_WAROPERATECOUNTDOWN_ACTORID_FIELD.cpp_type = 3

pb.SC_COUNTRY_WAROPERATECOUNTDOWN_ACTORNAME_FIELD.name = "ActorName"
pb.SC_COUNTRY_WAROPERATECOUNTDOWN_ACTORNAME_FIELD.full_name = ".SC_Country_WarOperateCountdown.ActorName"
pb.SC_COUNTRY_WAROPERATECOUNTDOWN_ACTORNAME_FIELD.number = 2
pb.SC_COUNTRY_WAROPERATECOUNTDOWN_ACTORNAME_FIELD.index = 1
pb.SC_COUNTRY_WAROPERATECOUNTDOWN_ACTORNAME_FIELD.label = 1
pb.SC_COUNTRY_WAROPERATECOUNTDOWN_ACTORNAME_FIELD.has_default_value = false
pb.SC_COUNTRY_WAROPERATECOUNTDOWN_ACTORNAME_FIELD.default_value = ""
pb.SC_COUNTRY_WAROPERATECOUNTDOWN_ACTORNAME_FIELD.type = 9
pb.SC_COUNTRY_WAROPERATECOUNTDOWN_ACTORNAME_FIELD.cpp_type = 9

pb.SC_COUNTRY_WAROPERATECOUNTDOWN_COUNTRYID_FIELD.name = "CountryID"
pb.SC_COUNTRY_WAROPERATECOUNTDOWN_COUNTRYID_FIELD.full_name = ".SC_Country_WarOperateCountdown.CountryID"
pb.SC_COUNTRY_WAROPERATECOUNTDOWN_COUNTRYID_FIELD.number = 3
pb.SC_COUNTRY_WAROPERATECOUNTDOWN_COUNTRYID_FIELD.index = 2
pb.SC_COUNTRY_WAROPERATECOUNTDOWN_COUNTRYID_FIELD.label = 1
pb.SC_COUNTRY_WAROPERATECOUNTDOWN_COUNTRYID_FIELD.has_default_value = false
pb.SC_COUNTRY_WAROPERATECOUNTDOWN_COUNTRYID_FIELD.default_value = 0
pb.SC_COUNTRY_WAROPERATECOUNTDOWN_COUNTRYID_FIELD.type = 13
pb.SC_COUNTRY_WAROPERATECOUNTDOWN_COUNTRYID_FIELD.cpp_type = 3

pb.SC_COUNTRY_WAROPERATECOUNTDOWN_OBJTYPE_FIELD.name = "ObjType"
pb.SC_COUNTRY_WAROPERATECOUNTDOWN_OBJTYPE_FIELD.full_name = ".SC_Country_WarOperateCountdown.ObjType"
pb.SC_COUNTRY_WAROPERATECOUNTDOWN_OBJTYPE_FIELD.number = 4
pb.SC_COUNTRY_WAROPERATECOUNTDOWN_OBJTYPE_FIELD.index = 3
pb.SC_COUNTRY_WAROPERATECOUNTDOWN_OBJTYPE_FIELD.label = 1
pb.SC_COUNTRY_WAROPERATECOUNTDOWN_OBJTYPE_FIELD.has_default_value = false
pb.SC_COUNTRY_WAROPERATECOUNTDOWN_OBJTYPE_FIELD.default_value = 0
pb.SC_COUNTRY_WAROPERATECOUNTDOWN_OBJTYPE_FIELD.type = 13
pb.SC_COUNTRY_WAROPERATECOUNTDOWN_OBJTYPE_FIELD.cpp_type = 3

pb.SC_COUNTRY_WAROPERATECOUNTDOWN_COUNTDOWNTIME_FIELD.name = "CountdownTime"
pb.SC_COUNTRY_WAROPERATECOUNTDOWN_COUNTDOWNTIME_FIELD.full_name = ".SC_Country_WarOperateCountdown.CountdownTime"
pb.SC_COUNTRY_WAROPERATECOUNTDOWN_COUNTDOWNTIME_FIELD.number = 5
pb.SC_COUNTRY_WAROPERATECOUNTDOWN_COUNTDOWNTIME_FIELD.index = 4
pb.SC_COUNTRY_WAROPERATECOUNTDOWN_COUNTDOWNTIME_FIELD.label = 1
pb.SC_COUNTRY_WAROPERATECOUNTDOWN_COUNTDOWNTIME_FIELD.has_default_value = false
pb.SC_COUNTRY_WAROPERATECOUNTDOWN_COUNTDOWNTIME_FIELD.default_value = 0
pb.SC_COUNTRY_WAROPERATECOUNTDOWN_COUNTDOWNTIME_FIELD.type = 13
pb.SC_COUNTRY_WAROPERATECOUNTDOWN_COUNTDOWNTIME_FIELD.cpp_type = 3

pb.SC_COUNTRY_WAROPERATECOUNTDOWN.name = "SC_Country_WarOperateCountdown"
pb.SC_COUNTRY_WAROPERATECOUNTDOWN.full_name = ".SC_Country_WarOperateCountdown"
pb.SC_COUNTRY_WAROPERATECOUNTDOWN.nested_types = {}
pb.SC_COUNTRY_WAROPERATECOUNTDOWN.enum_types = {}
pb.SC_COUNTRY_WAROPERATECOUNTDOWN.fields = {pb.SC_COUNTRY_WAROPERATECOUNTDOWN_ACTORID_FIELD, pb.SC_COUNTRY_WAROPERATECOUNTDOWN_ACTORNAME_FIELD, pb.SC_COUNTRY_WAROPERATECOUNTDOWN_COUNTRYID_FIELD, pb.SC_COUNTRY_WAROPERATECOUNTDOWN_OBJTYPE_FIELD, pb.SC_COUNTRY_WAROPERATECOUNTDOWN_COUNTDOWNTIME_FIELD}
pb.SC_COUNTRY_WAROPERATECOUNTDOWN.is_extendable = false
pb.SC_COUNTRY_WAROPERATECOUNTDOWN.extensions = {}
pb.SC_COUNTRY_WARSYNCFIELDINFO_BATTLEPHASE_FIELD.name = "BattlePhase"
pb.SC_COUNTRY_WARSYNCFIELDINFO_BATTLEPHASE_FIELD.full_name = ".SC_Country_WarSyncFieldInfo.BattlePhase"
pb.SC_COUNTRY_WARSYNCFIELDINFO_BATTLEPHASE_FIELD.number = 1
pb.SC_COUNTRY_WARSYNCFIELDINFO_BATTLEPHASE_FIELD.index = 0
pb.SC_COUNTRY_WARSYNCFIELDINFO_BATTLEPHASE_FIELD.label = 1
pb.SC_COUNTRY_WARSYNCFIELDINFO_BATTLEPHASE_FIELD.has_default_value = false
pb.SC_COUNTRY_WARSYNCFIELDINFO_BATTLEPHASE_FIELD.default_value = 0
pb.SC_COUNTRY_WARSYNCFIELDINFO_BATTLEPHASE_FIELD.type = 13
pb.SC_COUNTRY_WARSYNCFIELDINFO_BATTLEPHASE_FIELD.cpp_type = 3

pb.SC_COUNTRY_WARSYNCFIELDINFO_DEFENCECOUNTRY_FIELD.name = "DefenceCountry"
pb.SC_COUNTRY_WARSYNCFIELDINFO_DEFENCECOUNTRY_FIELD.full_name = ".SC_Country_WarSyncFieldInfo.DefenceCountry"
pb.SC_COUNTRY_WARSYNCFIELDINFO_DEFENCECOUNTRY_FIELD.number = 2
pb.SC_COUNTRY_WARSYNCFIELDINFO_DEFENCECOUNTRY_FIELD.index = 1
pb.SC_COUNTRY_WARSYNCFIELDINFO_DEFENCECOUNTRY_FIELD.label = 1
pb.SC_COUNTRY_WARSYNCFIELDINFO_DEFENCECOUNTRY_FIELD.has_default_value = false
pb.SC_COUNTRY_WARSYNCFIELDINFO_DEFENCECOUNTRY_FIELD.default_value = 0
pb.SC_COUNTRY_WARSYNCFIELDINFO_DEFENCECOUNTRY_FIELD.type = 13
pb.SC_COUNTRY_WARSYNCFIELDINFO_DEFENCECOUNTRY_FIELD.cpp_type = 3

pb.SC_COUNTRY_WARSYNCFIELDINFO_DBUFFGEARLV_FIELD.name = "DBuffGearLv"
pb.SC_COUNTRY_WARSYNCFIELDINFO_DBUFFGEARLV_FIELD.full_name = ".SC_Country_WarSyncFieldInfo.DBuffGearLv"
pb.SC_COUNTRY_WARSYNCFIELDINFO_DBUFFGEARLV_FIELD.number = 3
pb.SC_COUNTRY_WARSYNCFIELDINFO_DBUFFGEARLV_FIELD.index = 2
pb.SC_COUNTRY_WARSYNCFIELDINFO_DBUFFGEARLV_FIELD.label = 1
pb.SC_COUNTRY_WARSYNCFIELDINFO_DBUFFGEARLV_FIELD.has_default_value = false
pb.SC_COUNTRY_WARSYNCFIELDINFO_DBUFFGEARLV_FIELD.default_value = 0
pb.SC_COUNTRY_WARSYNCFIELDINFO_DBUFFGEARLV_FIELD.type = 13
pb.SC_COUNTRY_WARSYNCFIELDINFO_DBUFFGEARLV_FIELD.cpp_type = 3

pb.SC_COUNTRY_WARSYNCFIELDINFO_DRES1NUM_FIELD.name = "DRes1Num"
pb.SC_COUNTRY_WARSYNCFIELDINFO_DRES1NUM_FIELD.full_name = ".SC_Country_WarSyncFieldInfo.DRes1Num"
pb.SC_COUNTRY_WARSYNCFIELDINFO_DRES1NUM_FIELD.number = 4
pb.SC_COUNTRY_WARSYNCFIELDINFO_DRES1NUM_FIELD.index = 3
pb.SC_COUNTRY_WARSYNCFIELDINFO_DRES1NUM_FIELD.label = 1
pb.SC_COUNTRY_WARSYNCFIELDINFO_DRES1NUM_FIELD.has_default_value = false
pb.SC_COUNTRY_WARSYNCFIELDINFO_DRES1NUM_FIELD.default_value = 0
pb.SC_COUNTRY_WARSYNCFIELDINFO_DRES1NUM_FIELD.type = 13
pb.SC_COUNTRY_WARSYNCFIELDINFO_DRES1NUM_FIELD.cpp_type = 3

pb.SC_COUNTRY_WARSYNCFIELDINFO_DRES2NUM_FIELD.name = "DRes2Num"
pb.SC_COUNTRY_WARSYNCFIELDINFO_DRES2NUM_FIELD.full_name = ".SC_Country_WarSyncFieldInfo.DRes2Num"
pb.SC_COUNTRY_WARSYNCFIELDINFO_DRES2NUM_FIELD.number = 5
pb.SC_COUNTRY_WARSYNCFIELDINFO_DRES2NUM_FIELD.index = 4
pb.SC_COUNTRY_WARSYNCFIELDINFO_DRES2NUM_FIELD.label = 1
pb.SC_COUNTRY_WARSYNCFIELDINFO_DRES2NUM_FIELD.has_default_value = false
pb.SC_COUNTRY_WARSYNCFIELDINFO_DRES2NUM_FIELD.default_value = 0
pb.SC_COUNTRY_WARSYNCFIELDINFO_DRES2NUM_FIELD.type = 13
pb.SC_COUNTRY_WARSYNCFIELDINFO_DRES2NUM_FIELD.cpp_type = 3

pb.SC_COUNTRY_WARSYNCFIELDINFO_DLEFTTURRETHP_FIELD.name = "DLeftTurretHP"
pb.SC_COUNTRY_WARSYNCFIELDINFO_DLEFTTURRETHP_FIELD.full_name = ".SC_Country_WarSyncFieldInfo.DLeftTurretHP"
pb.SC_COUNTRY_WARSYNCFIELDINFO_DLEFTTURRETHP_FIELD.number = 6
pb.SC_COUNTRY_WARSYNCFIELDINFO_DLEFTTURRETHP_FIELD.index = 5
pb.SC_COUNTRY_WARSYNCFIELDINFO_DLEFTTURRETHP_FIELD.label = 1
pb.SC_COUNTRY_WARSYNCFIELDINFO_DLEFTTURRETHP_FIELD.has_default_value = false
pb.SC_COUNTRY_WARSYNCFIELDINFO_DLEFTTURRETHP_FIELD.default_value = 0
pb.SC_COUNTRY_WARSYNCFIELDINFO_DLEFTTURRETHP_FIELD.type = 13
pb.SC_COUNTRY_WARSYNCFIELDINFO_DLEFTTURRETHP_FIELD.cpp_type = 3

pb.SC_COUNTRY_WARSYNCFIELDINFO_DRIGHTTURRETHP_FIELD.name = "DRightTurretHP"
pb.SC_COUNTRY_WARSYNCFIELDINFO_DRIGHTTURRETHP_FIELD.full_name = ".SC_Country_WarSyncFieldInfo.DRightTurretHP"
pb.SC_COUNTRY_WARSYNCFIELDINFO_DRIGHTTURRETHP_FIELD.number = 7
pb.SC_COUNTRY_WARSYNCFIELDINFO_DRIGHTTURRETHP_FIELD.index = 6
pb.SC_COUNTRY_WARSYNCFIELDINFO_DRIGHTTURRETHP_FIELD.label = 1
pb.SC_COUNTRY_WARSYNCFIELDINFO_DRIGHTTURRETHP_FIELD.has_default_value = false
pb.SC_COUNTRY_WARSYNCFIELDINFO_DRIGHTTURRETHP_FIELD.default_value = 0
pb.SC_COUNTRY_WARSYNCFIELDINFO_DRIGHTTURRETHP_FIELD.type = 13
pb.SC_COUNTRY_WARSYNCFIELDINFO_DRIGHTTURRETHP_FIELD.cpp_type = 3

pb.SC_COUNTRY_WARSYNCFIELDINFO_DFRONTLEFTFLAGHP_FIELD.name = "DFrontLeftFlagHP"
pb.SC_COUNTRY_WARSYNCFIELDINFO_DFRONTLEFTFLAGHP_FIELD.full_name = ".SC_Country_WarSyncFieldInfo.DFrontLeftFlagHP"
pb.SC_COUNTRY_WARSYNCFIELDINFO_DFRONTLEFTFLAGHP_FIELD.number = 8
pb.SC_COUNTRY_WARSYNCFIELDINFO_DFRONTLEFTFLAGHP_FIELD.index = 7
pb.SC_COUNTRY_WARSYNCFIELDINFO_DFRONTLEFTFLAGHP_FIELD.label = 1
pb.SC_COUNTRY_WARSYNCFIELDINFO_DFRONTLEFTFLAGHP_FIELD.has_default_value = false
pb.SC_COUNTRY_WARSYNCFIELDINFO_DFRONTLEFTFLAGHP_FIELD.default_value = 0
pb.SC_COUNTRY_WARSYNCFIELDINFO_DFRONTLEFTFLAGHP_FIELD.type = 13
pb.SC_COUNTRY_WARSYNCFIELDINFO_DFRONTLEFTFLAGHP_FIELD.cpp_type = 3

pb.SC_COUNTRY_WARSYNCFIELDINFO_DFRONTRIGHTFLAGHP_FIELD.name = "DFrontRightFlagHP"
pb.SC_COUNTRY_WARSYNCFIELDINFO_DFRONTRIGHTFLAGHP_FIELD.full_name = ".SC_Country_WarSyncFieldInfo.DFrontRightFlagHP"
pb.SC_COUNTRY_WARSYNCFIELDINFO_DFRONTRIGHTFLAGHP_FIELD.number = 9
pb.SC_COUNTRY_WARSYNCFIELDINFO_DFRONTRIGHTFLAGHP_FIELD.index = 8
pb.SC_COUNTRY_WARSYNCFIELDINFO_DFRONTRIGHTFLAGHP_FIELD.label = 1
pb.SC_COUNTRY_WARSYNCFIELDINFO_DFRONTRIGHTFLAGHP_FIELD.has_default_value = false
pb.SC_COUNTRY_WARSYNCFIELDINFO_DFRONTRIGHTFLAGHP_FIELD.default_value = 0
pb.SC_COUNTRY_WARSYNCFIELDINFO_DFRONTRIGHTFLAGHP_FIELD.type = 13
pb.SC_COUNTRY_WARSYNCFIELDINFO_DFRONTRIGHTFLAGHP_FIELD.cpp_type = 3

pb.SC_COUNTRY_WARSYNCFIELDINFO_DLEFTFLAGHP_FIELD.name = "DLeftFlagHP"
pb.SC_COUNTRY_WARSYNCFIELDINFO_DLEFTFLAGHP_FIELD.full_name = ".SC_Country_WarSyncFieldInfo.DLeftFlagHP"
pb.SC_COUNTRY_WARSYNCFIELDINFO_DLEFTFLAGHP_FIELD.number = 10
pb.SC_COUNTRY_WARSYNCFIELDINFO_DLEFTFLAGHP_FIELD.index = 9
pb.SC_COUNTRY_WARSYNCFIELDINFO_DLEFTFLAGHP_FIELD.label = 1
pb.SC_COUNTRY_WARSYNCFIELDINFO_DLEFTFLAGHP_FIELD.has_default_value = false
pb.SC_COUNTRY_WARSYNCFIELDINFO_DLEFTFLAGHP_FIELD.default_value = 0
pb.SC_COUNTRY_WARSYNCFIELDINFO_DLEFTFLAGHP_FIELD.type = 13
pb.SC_COUNTRY_WARSYNCFIELDINFO_DLEFTFLAGHP_FIELD.cpp_type = 3

pb.SC_COUNTRY_WARSYNCFIELDINFO_DRIGHTFLAGHP_FIELD.name = "DRightFlagHP"
pb.SC_COUNTRY_WARSYNCFIELDINFO_DRIGHTFLAGHP_FIELD.full_name = ".SC_Country_WarSyncFieldInfo.DRightFlagHP"
pb.SC_COUNTRY_WARSYNCFIELDINFO_DRIGHTFLAGHP_FIELD.number = 11
pb.SC_COUNTRY_WARSYNCFIELDINFO_DRIGHTFLAGHP_FIELD.index = 10
pb.SC_COUNTRY_WARSYNCFIELDINFO_DRIGHTFLAGHP_FIELD.label = 1
pb.SC_COUNTRY_WARSYNCFIELDINFO_DRIGHTFLAGHP_FIELD.has_default_value = false
pb.SC_COUNTRY_WARSYNCFIELDINFO_DRIGHTFLAGHP_FIELD.default_value = 0
pb.SC_COUNTRY_WARSYNCFIELDINFO_DRIGHTFLAGHP_FIELD.type = 13
pb.SC_COUNTRY_WARSYNCFIELDINFO_DRIGHTFLAGHP_FIELD.cpp_type = 3

pb.SC_COUNTRY_WARSYNCFIELDINFO_DCENTERFLAGHP_FIELD.name = "DCenterFlagHP"
pb.SC_COUNTRY_WARSYNCFIELDINFO_DCENTERFLAGHP_FIELD.full_name = ".SC_Country_WarSyncFieldInfo.DCenterFlagHP"
pb.SC_COUNTRY_WARSYNCFIELDINFO_DCENTERFLAGHP_FIELD.number = 12
pb.SC_COUNTRY_WARSYNCFIELDINFO_DCENTERFLAGHP_FIELD.index = 11
pb.SC_COUNTRY_WARSYNCFIELDINFO_DCENTERFLAGHP_FIELD.label = 1
pb.SC_COUNTRY_WARSYNCFIELDINFO_DCENTERFLAGHP_FIELD.has_default_value = false
pb.SC_COUNTRY_WARSYNCFIELDINFO_DCENTERFLAGHP_FIELD.default_value = 0
pb.SC_COUNTRY_WARSYNCFIELDINFO_DCENTERFLAGHP_FIELD.type = 13
pb.SC_COUNTRY_WARSYNCFIELDINFO_DCENTERFLAGHP_FIELD.cpp_type = 3

pb.SC_COUNTRY_WARSYNCFIELDINFO_DSPGEARUSE_FIELD.name = "DSpGearUse"
pb.SC_COUNTRY_WARSYNCFIELDINFO_DSPGEARUSE_FIELD.full_name = ".SC_Country_WarSyncFieldInfo.DSpGearUse"
pb.SC_COUNTRY_WARSYNCFIELDINFO_DSPGEARUSE_FIELD.number = 13
pb.SC_COUNTRY_WARSYNCFIELDINFO_DSPGEARUSE_FIELD.index = 12
pb.SC_COUNTRY_WARSYNCFIELDINFO_DSPGEARUSE_FIELD.label = 1
pb.SC_COUNTRY_WARSYNCFIELDINFO_DSPGEARUSE_FIELD.has_default_value = false
pb.SC_COUNTRY_WARSYNCFIELDINFO_DSPGEARUSE_FIELD.default_value = 0
pb.SC_COUNTRY_WARSYNCFIELDINFO_DSPGEARUSE_FIELD.type = 13
pb.SC_COUNTRY_WARSYNCFIELDINFO_DSPGEARUSE_FIELD.cpp_type = 3

pb.SC_COUNTRY_WARSYNCFIELDINFO_ATTACKCOUNTRY_FIELD.name = "AttackCountry"
pb.SC_COUNTRY_WARSYNCFIELDINFO_ATTACKCOUNTRY_FIELD.full_name = ".SC_Country_WarSyncFieldInfo.AttackCountry"
pb.SC_COUNTRY_WARSYNCFIELDINFO_ATTACKCOUNTRY_FIELD.number = 14
pb.SC_COUNTRY_WARSYNCFIELDINFO_ATTACKCOUNTRY_FIELD.index = 13
pb.SC_COUNTRY_WARSYNCFIELDINFO_ATTACKCOUNTRY_FIELD.label = 1
pb.SC_COUNTRY_WARSYNCFIELDINFO_ATTACKCOUNTRY_FIELD.has_default_value = false
pb.SC_COUNTRY_WARSYNCFIELDINFO_ATTACKCOUNTRY_FIELD.default_value = 0
pb.SC_COUNTRY_WARSYNCFIELDINFO_ATTACKCOUNTRY_FIELD.type = 13
pb.SC_COUNTRY_WARSYNCFIELDINFO_ATTACKCOUNTRY_FIELD.cpp_type = 3

pb.SC_COUNTRY_WARSYNCFIELDINFO_AATTACKGEARLV_FIELD.name = "AAttackGearLv"
pb.SC_COUNTRY_WARSYNCFIELDINFO_AATTACKGEARLV_FIELD.full_name = ".SC_Country_WarSyncFieldInfo.AAttackGearLv"
pb.SC_COUNTRY_WARSYNCFIELDINFO_AATTACKGEARLV_FIELD.number = 15
pb.SC_COUNTRY_WARSYNCFIELDINFO_AATTACKGEARLV_FIELD.index = 14
pb.SC_COUNTRY_WARSYNCFIELDINFO_AATTACKGEARLV_FIELD.label = 1
pb.SC_COUNTRY_WARSYNCFIELDINFO_AATTACKGEARLV_FIELD.has_default_value = false
pb.SC_COUNTRY_WARSYNCFIELDINFO_AATTACKGEARLV_FIELD.default_value = 0
pb.SC_COUNTRY_WARSYNCFIELDINFO_AATTACKGEARLV_FIELD.type = 13
pb.SC_COUNTRY_WARSYNCFIELDINFO_AATTACKGEARLV_FIELD.cpp_type = 3

pb.SC_COUNTRY_WARSYNCFIELDINFO_ABUFFGEARLV_FIELD.name = "ABuffGearLv"
pb.SC_COUNTRY_WARSYNCFIELDINFO_ABUFFGEARLV_FIELD.full_name = ".SC_Country_WarSyncFieldInfo.ABuffGearLv"
pb.SC_COUNTRY_WARSYNCFIELDINFO_ABUFFGEARLV_FIELD.number = 16
pb.SC_COUNTRY_WARSYNCFIELDINFO_ABUFFGEARLV_FIELD.index = 15
pb.SC_COUNTRY_WARSYNCFIELDINFO_ABUFFGEARLV_FIELD.label = 1
pb.SC_COUNTRY_WARSYNCFIELDINFO_ABUFFGEARLV_FIELD.has_default_value = false
pb.SC_COUNTRY_WARSYNCFIELDINFO_ABUFFGEARLV_FIELD.default_value = 0
pb.SC_COUNTRY_WARSYNCFIELDINFO_ABUFFGEARLV_FIELD.type = 13
pb.SC_COUNTRY_WARSYNCFIELDINFO_ABUFFGEARLV_FIELD.cpp_type = 3

pb.SC_COUNTRY_WARSYNCFIELDINFO_ARES1NUM_FIELD.name = "ARes1Num"
pb.SC_COUNTRY_WARSYNCFIELDINFO_ARES1NUM_FIELD.full_name = ".SC_Country_WarSyncFieldInfo.ARes1Num"
pb.SC_COUNTRY_WARSYNCFIELDINFO_ARES1NUM_FIELD.number = 17
pb.SC_COUNTRY_WARSYNCFIELDINFO_ARES1NUM_FIELD.index = 16
pb.SC_COUNTRY_WARSYNCFIELDINFO_ARES1NUM_FIELD.label = 1
pb.SC_COUNTRY_WARSYNCFIELDINFO_ARES1NUM_FIELD.has_default_value = false
pb.SC_COUNTRY_WARSYNCFIELDINFO_ARES1NUM_FIELD.default_value = 0
pb.SC_COUNTRY_WARSYNCFIELDINFO_ARES1NUM_FIELD.type = 13
pb.SC_COUNTRY_WARSYNCFIELDINFO_ARES1NUM_FIELD.cpp_type = 3

pb.SC_COUNTRY_WARSYNCFIELDINFO_ARES2NUM_FIELD.name = "ARes2Num"
pb.SC_COUNTRY_WARSYNCFIELDINFO_ARES2NUM_FIELD.full_name = ".SC_Country_WarSyncFieldInfo.ARes2Num"
pb.SC_COUNTRY_WARSYNCFIELDINFO_ARES2NUM_FIELD.number = 18
pb.SC_COUNTRY_WARSYNCFIELDINFO_ARES2NUM_FIELD.index = 17
pb.SC_COUNTRY_WARSYNCFIELDINFO_ARES2NUM_FIELD.label = 1
pb.SC_COUNTRY_WARSYNCFIELDINFO_ARES2NUM_FIELD.has_default_value = false
pb.SC_COUNTRY_WARSYNCFIELDINFO_ARES2NUM_FIELD.default_value = 0
pb.SC_COUNTRY_WARSYNCFIELDINFO_ARES2NUM_FIELD.type = 13
pb.SC_COUNTRY_WARSYNCFIELDINFO_ARES2NUM_FIELD.cpp_type = 3

pb.SC_COUNTRY_WARSYNCFIELDINFO_APATHENDTIME_FIELD.name = "APathEndTime"
pb.SC_COUNTRY_WARSYNCFIELDINFO_APATHENDTIME_FIELD.full_name = ".SC_Country_WarSyncFieldInfo.APathEndTime"
pb.SC_COUNTRY_WARSYNCFIELDINFO_APATHENDTIME_FIELD.number = 19
pb.SC_COUNTRY_WARSYNCFIELDINFO_APATHENDTIME_FIELD.index = 18
pb.SC_COUNTRY_WARSYNCFIELDINFO_APATHENDTIME_FIELD.label = 1
pb.SC_COUNTRY_WARSYNCFIELDINFO_APATHENDTIME_FIELD.has_default_value = false
pb.SC_COUNTRY_WARSYNCFIELDINFO_APATHENDTIME_FIELD.default_value = 0
pb.SC_COUNTRY_WARSYNCFIELDINFO_APATHENDTIME_FIELD.type = 13
pb.SC_COUNTRY_WARSYNCFIELDINFO_APATHENDTIME_FIELD.cpp_type = 3

pb.SC_COUNTRY_WARSYNCFIELDINFO_DPASTWINCOUNT_FIELD.name = "DPastWinCount"
pb.SC_COUNTRY_WARSYNCFIELDINFO_DPASTWINCOUNT_FIELD.full_name = ".SC_Country_WarSyncFieldInfo.DPastWinCount"
pb.SC_COUNTRY_WARSYNCFIELDINFO_DPASTWINCOUNT_FIELD.number = 20
pb.SC_COUNTRY_WARSYNCFIELDINFO_DPASTWINCOUNT_FIELD.index = 19
pb.SC_COUNTRY_WARSYNCFIELDINFO_DPASTWINCOUNT_FIELD.label = 1
pb.SC_COUNTRY_WARSYNCFIELDINFO_DPASTWINCOUNT_FIELD.has_default_value = false
pb.SC_COUNTRY_WARSYNCFIELDINFO_DPASTWINCOUNT_FIELD.default_value = 0
pb.SC_COUNTRY_WARSYNCFIELDINFO_DPASTWINCOUNT_FIELD.type = 13
pb.SC_COUNTRY_WARSYNCFIELDINFO_DPASTWINCOUNT_FIELD.cpp_type = 3

pb.SC_COUNTRY_WARSYNCFIELDINFO_APASTWINCOUNT_FIELD.name = "APastWinCount"
pb.SC_COUNTRY_WARSYNCFIELDINFO_APASTWINCOUNT_FIELD.full_name = ".SC_Country_WarSyncFieldInfo.APastWinCount"
pb.SC_COUNTRY_WARSYNCFIELDINFO_APASTWINCOUNT_FIELD.number = 21
pb.SC_COUNTRY_WARSYNCFIELDINFO_APASTWINCOUNT_FIELD.index = 20
pb.SC_COUNTRY_WARSYNCFIELDINFO_APASTWINCOUNT_FIELD.label = 1
pb.SC_COUNTRY_WARSYNCFIELDINFO_APASTWINCOUNT_FIELD.has_default_value = false
pb.SC_COUNTRY_WARSYNCFIELDINFO_APASTWINCOUNT_FIELD.default_value = 0
pb.SC_COUNTRY_WARSYNCFIELDINFO_APASTWINCOUNT_FIELD.type = 13
pb.SC_COUNTRY_WARSYNCFIELDINFO_APASTWINCOUNT_FIELD.cpp_type = 3

pb.SC_COUNTRY_WARSYNCFIELDINFO.name = "SC_Country_WarSyncFieldInfo"
pb.SC_COUNTRY_WARSYNCFIELDINFO.full_name = ".SC_Country_WarSyncFieldInfo"
pb.SC_COUNTRY_WARSYNCFIELDINFO.nested_types = {}
pb.SC_COUNTRY_WARSYNCFIELDINFO.enum_types = {}
pb.SC_COUNTRY_WARSYNCFIELDINFO.fields = {pb.SC_COUNTRY_WARSYNCFIELDINFO_BATTLEPHASE_FIELD, pb.SC_COUNTRY_WARSYNCFIELDINFO_DEFENCECOUNTRY_FIELD, pb.SC_COUNTRY_WARSYNCFIELDINFO_DBUFFGEARLV_FIELD, pb.SC_COUNTRY_WARSYNCFIELDINFO_DRES1NUM_FIELD, pb.SC_COUNTRY_WARSYNCFIELDINFO_DRES2NUM_FIELD, pb.SC_COUNTRY_WARSYNCFIELDINFO_DLEFTTURRETHP_FIELD, pb.SC_COUNTRY_WARSYNCFIELDINFO_DRIGHTTURRETHP_FIELD, pb.SC_COUNTRY_WARSYNCFIELDINFO_DFRONTLEFTFLAGHP_FIELD, pb.SC_COUNTRY_WARSYNCFIELDINFO_DFRONTRIGHTFLAGHP_FIELD, pb.SC_COUNTRY_WARSYNCFIELDINFO_DLEFTFLAGHP_FIELD, pb.SC_COUNTRY_WARSYNCFIELDINFO_DRIGHTFLAGHP_FIELD, pb.SC_COUNTRY_WARSYNCFIELDINFO_DCENTERFLAGHP_FIELD, pb.SC_COUNTRY_WARSYNCFIELDINFO_DSPGEARUSE_FIELD, pb.SC_COUNTRY_WARSYNCFIELDINFO_ATTACKCOUNTRY_FIELD, pb.SC_COUNTRY_WARSYNCFIELDINFO_AATTACKGEARLV_FIELD, pb.SC_COUNTRY_WARSYNCFIELDINFO_ABUFFGEARLV_FIELD, pb.SC_COUNTRY_WARSYNCFIELDINFO_ARES1NUM_FIELD, pb.SC_COUNTRY_WARSYNCFIELDINFO_ARES2NUM_FIELD, pb.SC_COUNTRY_WARSYNCFIELDINFO_APATHENDTIME_FIELD, pb.SC_COUNTRY_WARSYNCFIELDINFO_DPASTWINCOUNT_FIELD, pb.SC_COUNTRY_WARSYNCFIELDINFO_APASTWINCOUNT_FIELD}
pb.SC_COUNTRY_WARSYNCFIELDINFO.is_extendable = false
pb.SC_COUNTRY_WARSYNCFIELDINFO.extensions = {}
pb.SC_COUNTRY_WARRESULTINFO_ACTORSCOREDATA_RANK_FIELD.name = "Rank"
pb.SC_COUNTRY_WARRESULTINFO_ACTORSCOREDATA_RANK_FIELD.full_name = ".SC_Country_WarResultInfo.ActorScoreData.Rank"
pb.SC_COUNTRY_WARRESULTINFO_ACTORSCOREDATA_RANK_FIELD.number = 1
pb.SC_COUNTRY_WARRESULTINFO_ACTORSCOREDATA_RANK_FIELD.index = 0
pb.SC_COUNTRY_WARRESULTINFO_ACTORSCOREDATA_RANK_FIELD.label = 1
pb.SC_COUNTRY_WARRESULTINFO_ACTORSCOREDATA_RANK_FIELD.has_default_value = false
pb.SC_COUNTRY_WARRESULTINFO_ACTORSCOREDATA_RANK_FIELD.default_value = 0
pb.SC_COUNTRY_WARRESULTINFO_ACTORSCOREDATA_RANK_FIELD.type = 13
pb.SC_COUNTRY_WARRESULTINFO_ACTORSCOREDATA_RANK_FIELD.cpp_type = 3

pb.SC_COUNTRY_WARRESULTINFO_ACTORSCOREDATA_ACTORID_FIELD.name = "ActorID"
pb.SC_COUNTRY_WARRESULTINFO_ACTORSCOREDATA_ACTORID_FIELD.full_name = ".SC_Country_WarResultInfo.ActorScoreData.ActorID"
pb.SC_COUNTRY_WARRESULTINFO_ACTORSCOREDATA_ACTORID_FIELD.number = 2
pb.SC_COUNTRY_WARRESULTINFO_ACTORSCOREDATA_ACTORID_FIELD.index = 1
pb.SC_COUNTRY_WARRESULTINFO_ACTORSCOREDATA_ACTORID_FIELD.label = 1
pb.SC_COUNTRY_WARRESULTINFO_ACTORSCOREDATA_ACTORID_FIELD.has_default_value = false
pb.SC_COUNTRY_WARRESULTINFO_ACTORSCOREDATA_ACTORID_FIELD.default_value = 0
pb.SC_COUNTRY_WARRESULTINFO_ACTORSCOREDATA_ACTORID_FIELD.type = 13
pb.SC_COUNTRY_WARRESULTINFO_ACTORSCOREDATA_ACTORID_FIELD.cpp_type = 3

pb.SC_COUNTRY_WARRESULTINFO_ACTORSCOREDATA_ACTORNAME_FIELD.name = "ActorName"
pb.SC_COUNTRY_WARRESULTINFO_ACTORSCOREDATA_ACTORNAME_FIELD.full_name = ".SC_Country_WarResultInfo.ActorScoreData.ActorName"
pb.SC_COUNTRY_WARRESULTINFO_ACTORSCOREDATA_ACTORNAME_FIELD.number = 3
pb.SC_COUNTRY_WARRESULTINFO_ACTORSCOREDATA_ACTORNAME_FIELD.index = 2
pb.SC_COUNTRY_WARRESULTINFO_ACTORSCOREDATA_ACTORNAME_FIELD.label = 1
pb.SC_COUNTRY_WARRESULTINFO_ACTORSCOREDATA_ACTORNAME_FIELD.has_default_value = false
pb.SC_COUNTRY_WARRESULTINFO_ACTORSCOREDATA_ACTORNAME_FIELD.default_value = ""
pb.SC_COUNTRY_WARRESULTINFO_ACTORSCOREDATA_ACTORNAME_FIELD.type = 9
pb.SC_COUNTRY_WARRESULTINFO_ACTORSCOREDATA_ACTORNAME_FIELD.cpp_type = 9

pb.SC_COUNTRY_WARRESULTINFO_ACTORSCOREDATA_COUNTRY_FIELD.name = "Country"
pb.SC_COUNTRY_WARRESULTINFO_ACTORSCOREDATA_COUNTRY_FIELD.full_name = ".SC_Country_WarResultInfo.ActorScoreData.Country"
pb.SC_COUNTRY_WARRESULTINFO_ACTORSCOREDATA_COUNTRY_FIELD.number = 4
pb.SC_COUNTRY_WARRESULTINFO_ACTORSCOREDATA_COUNTRY_FIELD.index = 3
pb.SC_COUNTRY_WARRESULTINFO_ACTORSCOREDATA_COUNTRY_FIELD.label = 1
pb.SC_COUNTRY_WARRESULTINFO_ACTORSCOREDATA_COUNTRY_FIELD.has_default_value = false
pb.SC_COUNTRY_WARRESULTINFO_ACTORSCOREDATA_COUNTRY_FIELD.default_value = 0
pb.SC_COUNTRY_WARRESULTINFO_ACTORSCOREDATA_COUNTRY_FIELD.type = 13
pb.SC_COUNTRY_WARRESULTINFO_ACTORSCOREDATA_COUNTRY_FIELD.cpp_type = 3

pb.SC_COUNTRY_WARRESULTINFO_ACTORSCOREDATA_LEVEL_FIELD.name = "Level"
pb.SC_COUNTRY_WARRESULTINFO_ACTORSCOREDATA_LEVEL_FIELD.full_name = ".SC_Country_WarResultInfo.ActorScoreData.Level"
pb.SC_COUNTRY_WARRESULTINFO_ACTORSCOREDATA_LEVEL_FIELD.number = 5
pb.SC_COUNTRY_WARRESULTINFO_ACTORSCOREDATA_LEVEL_FIELD.index = 4
pb.SC_COUNTRY_WARRESULTINFO_ACTORSCOREDATA_LEVEL_FIELD.label = 1
pb.SC_COUNTRY_WARRESULTINFO_ACTORSCOREDATA_LEVEL_FIELD.has_default_value = false
pb.SC_COUNTRY_WARRESULTINFO_ACTORSCOREDATA_LEVEL_FIELD.default_value = 0
pb.SC_COUNTRY_WARRESULTINFO_ACTORSCOREDATA_LEVEL_FIELD.type = 13
pb.SC_COUNTRY_WARRESULTINFO_ACTORSCOREDATA_LEVEL_FIELD.cpp_type = 3

pb.SC_COUNTRY_WARRESULTINFO_ACTORSCOREDATA_SCORE_FIELD.name = "Score"
pb.SC_COUNTRY_WARRESULTINFO_ACTORSCOREDATA_SCORE_FIELD.full_name = ".SC_Country_WarResultInfo.ActorScoreData.Score"
pb.SC_COUNTRY_WARRESULTINFO_ACTORSCOREDATA_SCORE_FIELD.number = 6
pb.SC_COUNTRY_WARRESULTINFO_ACTORSCOREDATA_SCORE_FIELD.index = 5
pb.SC_COUNTRY_WARRESULTINFO_ACTORSCOREDATA_SCORE_FIELD.label = 1
pb.SC_COUNTRY_WARRESULTINFO_ACTORSCOREDATA_SCORE_FIELD.has_default_value = false
pb.SC_COUNTRY_WARRESULTINFO_ACTORSCOREDATA_SCORE_FIELD.default_value = 0
pb.SC_COUNTRY_WARRESULTINFO_ACTORSCOREDATA_SCORE_FIELD.type = 13
pb.SC_COUNTRY_WARRESULTINFO_ACTORSCOREDATA_SCORE_FIELD.cpp_type = 3

pb.SC_COUNTRY_WARRESULTINFO_ACTORSCOREDATA_KILLNUM_FIELD.name = "KillNum"
pb.SC_COUNTRY_WARRESULTINFO_ACTORSCOREDATA_KILLNUM_FIELD.full_name = ".SC_Country_WarResultInfo.ActorScoreData.KillNum"
pb.SC_COUNTRY_WARRESULTINFO_ACTORSCOREDATA_KILLNUM_FIELD.number = 7
pb.SC_COUNTRY_WARRESULTINFO_ACTORSCOREDATA_KILLNUM_FIELD.index = 6
pb.SC_COUNTRY_WARRESULTINFO_ACTORSCOREDATA_KILLNUM_FIELD.label = 1
pb.SC_COUNTRY_WARRESULTINFO_ACTORSCOREDATA_KILLNUM_FIELD.has_default_value = false
pb.SC_COUNTRY_WARRESULTINFO_ACTORSCOREDATA_KILLNUM_FIELD.default_value = 0
pb.SC_COUNTRY_WARRESULTINFO_ACTORSCOREDATA_KILLNUM_FIELD.type = 13
pb.SC_COUNTRY_WARRESULTINFO_ACTORSCOREDATA_KILLNUM_FIELD.cpp_type = 3

pb.SC_COUNTRY_WARRESULTINFO_ACTORSCOREDATA_PRIZERES1_FIELD.name = "PrizeRes1"
pb.SC_COUNTRY_WARRESULTINFO_ACTORSCOREDATA_PRIZERES1_FIELD.full_name = ".SC_Country_WarResultInfo.ActorScoreData.PrizeRes1"
pb.SC_COUNTRY_WARRESULTINFO_ACTORSCOREDATA_PRIZERES1_FIELD.number = 8
pb.SC_COUNTRY_WARRESULTINFO_ACTORSCOREDATA_PRIZERES1_FIELD.index = 7
pb.SC_COUNTRY_WARRESULTINFO_ACTORSCOREDATA_PRIZERES1_FIELD.label = 1
pb.SC_COUNTRY_WARRESULTINFO_ACTORSCOREDATA_PRIZERES1_FIELD.has_default_value = false
pb.SC_COUNTRY_WARRESULTINFO_ACTORSCOREDATA_PRIZERES1_FIELD.default_value = 0
pb.SC_COUNTRY_WARRESULTINFO_ACTORSCOREDATA_PRIZERES1_FIELD.type = 13
pb.SC_COUNTRY_WARRESULTINFO_ACTORSCOREDATA_PRIZERES1_FIELD.cpp_type = 3

pb.SC_COUNTRY_WARRESULTINFO_ACTORSCOREDATA_PRIZERES2_FIELD.name = "PrizeRes2"
pb.SC_COUNTRY_WARRESULTINFO_ACTORSCOREDATA_PRIZERES2_FIELD.full_name = ".SC_Country_WarResultInfo.ActorScoreData.PrizeRes2"
pb.SC_COUNTRY_WARRESULTINFO_ACTORSCOREDATA_PRIZERES2_FIELD.number = 9
pb.SC_COUNTRY_WARRESULTINFO_ACTORSCOREDATA_PRIZERES2_FIELD.index = 8
pb.SC_COUNTRY_WARRESULTINFO_ACTORSCOREDATA_PRIZERES2_FIELD.label = 1
pb.SC_COUNTRY_WARRESULTINFO_ACTORSCOREDATA_PRIZERES2_FIELD.has_default_value = false
pb.SC_COUNTRY_WARRESULTINFO_ACTORSCOREDATA_PRIZERES2_FIELD.default_value = 0
pb.SC_COUNTRY_WARRESULTINFO_ACTORSCOREDATA_PRIZERES2_FIELD.type = 13
pb.SC_COUNTRY_WARRESULTINFO_ACTORSCOREDATA_PRIZERES2_FIELD.cpp_type = 3

pb.SC_COUNTRY_WARRESULTINFO_ACTORSCOREDATA.name = "ActorScoreData"
pb.SC_COUNTRY_WARRESULTINFO_ACTORSCOREDATA.full_name = ".SC_Country_WarResultInfo.ActorScoreData"
pb.SC_COUNTRY_WARRESULTINFO_ACTORSCOREDATA.nested_types = {}
pb.SC_COUNTRY_WARRESULTINFO_ACTORSCOREDATA.enum_types = {}
pb.SC_COUNTRY_WARRESULTINFO_ACTORSCOREDATA.fields = {pb.SC_COUNTRY_WARRESULTINFO_ACTORSCOREDATA_RANK_FIELD, pb.SC_COUNTRY_WARRESULTINFO_ACTORSCOREDATA_ACTORID_FIELD, pb.SC_COUNTRY_WARRESULTINFO_ACTORSCOREDATA_ACTORNAME_FIELD, pb.SC_COUNTRY_WARRESULTINFO_ACTORSCOREDATA_COUNTRY_FIELD, pb.SC_COUNTRY_WARRESULTINFO_ACTORSCOREDATA_LEVEL_FIELD, pb.SC_COUNTRY_WARRESULTINFO_ACTORSCOREDATA_SCORE_FIELD, pb.SC_COUNTRY_WARRESULTINFO_ACTORSCOREDATA_KILLNUM_FIELD, pb.SC_COUNTRY_WARRESULTINFO_ACTORSCOREDATA_PRIZERES1_FIELD, pb.SC_COUNTRY_WARRESULTINFO_ACTORSCOREDATA_PRIZERES2_FIELD}
pb.SC_COUNTRY_WARRESULTINFO_ACTORSCOREDATA.is_extendable = false
pb.SC_COUNTRY_WARRESULTINFO_ACTORSCOREDATA.extensions = {}
pb.SC_COUNTRY_WARRESULTINFO_ACTORSCOREDATA.containing_type = pb.SC_COUNTRY_WARRESULTINFO
pb.SC_COUNTRY_WARRESULTINFO_SOCIETYSCOREDATA_RANK_FIELD.name = "Rank"
pb.SC_COUNTRY_WARRESULTINFO_SOCIETYSCOREDATA_RANK_FIELD.full_name = ".SC_Country_WarResultInfo.SocietyScoreData.Rank"
pb.SC_COUNTRY_WARRESULTINFO_SOCIETYSCOREDATA_RANK_FIELD.number = 1
pb.SC_COUNTRY_WARRESULTINFO_SOCIETYSCOREDATA_RANK_FIELD.index = 0
pb.SC_COUNTRY_WARRESULTINFO_SOCIETYSCOREDATA_RANK_FIELD.label = 1
pb.SC_COUNTRY_WARRESULTINFO_SOCIETYSCOREDATA_RANK_FIELD.has_default_value = false
pb.SC_COUNTRY_WARRESULTINFO_SOCIETYSCOREDATA_RANK_FIELD.default_value = 0
pb.SC_COUNTRY_WARRESULTINFO_SOCIETYSCOREDATA_RANK_FIELD.type = 13
pb.SC_COUNTRY_WARRESULTINFO_SOCIETYSCOREDATA_RANK_FIELD.cpp_type = 3

pb.SC_COUNTRY_WARRESULTINFO_SOCIETYSCOREDATA_SOCIETYID_FIELD.name = "SocietyID"
pb.SC_COUNTRY_WARRESULTINFO_SOCIETYSCOREDATA_SOCIETYID_FIELD.full_name = ".SC_Country_WarResultInfo.SocietyScoreData.SocietyID"
pb.SC_COUNTRY_WARRESULTINFO_SOCIETYSCOREDATA_SOCIETYID_FIELD.number = 2
pb.SC_COUNTRY_WARRESULTINFO_SOCIETYSCOREDATA_SOCIETYID_FIELD.index = 1
pb.SC_COUNTRY_WARRESULTINFO_SOCIETYSCOREDATA_SOCIETYID_FIELD.label = 1
pb.SC_COUNTRY_WARRESULTINFO_SOCIETYSCOREDATA_SOCIETYID_FIELD.has_default_value = false
pb.SC_COUNTRY_WARRESULTINFO_SOCIETYSCOREDATA_SOCIETYID_FIELD.default_value = 0
pb.SC_COUNTRY_WARRESULTINFO_SOCIETYSCOREDATA_SOCIETYID_FIELD.type = 13
pb.SC_COUNTRY_WARRESULTINFO_SOCIETYSCOREDATA_SOCIETYID_FIELD.cpp_type = 3

pb.SC_COUNTRY_WARRESULTINFO_SOCIETYSCOREDATA_SOCIETYNAME_FIELD.name = "SocietyName"
pb.SC_COUNTRY_WARRESULTINFO_SOCIETYSCOREDATA_SOCIETYNAME_FIELD.full_name = ".SC_Country_WarResultInfo.SocietyScoreData.SocietyName"
pb.SC_COUNTRY_WARRESULTINFO_SOCIETYSCOREDATA_SOCIETYNAME_FIELD.number = 3
pb.SC_COUNTRY_WARRESULTINFO_SOCIETYSCOREDATA_SOCIETYNAME_FIELD.index = 2
pb.SC_COUNTRY_WARRESULTINFO_SOCIETYSCOREDATA_SOCIETYNAME_FIELD.label = 1
pb.SC_COUNTRY_WARRESULTINFO_SOCIETYSCOREDATA_SOCIETYNAME_FIELD.has_default_value = false
pb.SC_COUNTRY_WARRESULTINFO_SOCIETYSCOREDATA_SOCIETYNAME_FIELD.default_value = ""
pb.SC_COUNTRY_WARRESULTINFO_SOCIETYSCOREDATA_SOCIETYNAME_FIELD.type = 9
pb.SC_COUNTRY_WARRESULTINFO_SOCIETYSCOREDATA_SOCIETYNAME_FIELD.cpp_type = 9

pb.SC_COUNTRY_WARRESULTINFO_SOCIETYSCOREDATA_ACTORID_FIELD.name = "ActorID"
pb.SC_COUNTRY_WARRESULTINFO_SOCIETYSCOREDATA_ACTORID_FIELD.full_name = ".SC_Country_WarResultInfo.SocietyScoreData.ActorID"
pb.SC_COUNTRY_WARRESULTINFO_SOCIETYSCOREDATA_ACTORID_FIELD.number = 4
pb.SC_COUNTRY_WARRESULTINFO_SOCIETYSCOREDATA_ACTORID_FIELD.index = 3
pb.SC_COUNTRY_WARRESULTINFO_SOCIETYSCOREDATA_ACTORID_FIELD.label = 1
pb.SC_COUNTRY_WARRESULTINFO_SOCIETYSCOREDATA_ACTORID_FIELD.has_default_value = false
pb.SC_COUNTRY_WARRESULTINFO_SOCIETYSCOREDATA_ACTORID_FIELD.default_value = 0
pb.SC_COUNTRY_WARRESULTINFO_SOCIETYSCOREDATA_ACTORID_FIELD.type = 13
pb.SC_COUNTRY_WARRESULTINFO_SOCIETYSCOREDATA_ACTORID_FIELD.cpp_type = 3

pb.SC_COUNTRY_WARRESULTINFO_SOCIETYSCOREDATA_ACTORNAME_FIELD.name = "ActorName"
pb.SC_COUNTRY_WARRESULTINFO_SOCIETYSCOREDATA_ACTORNAME_FIELD.full_name = ".SC_Country_WarResultInfo.SocietyScoreData.ActorName"
pb.SC_COUNTRY_WARRESULTINFO_SOCIETYSCOREDATA_ACTORNAME_FIELD.number = 5
pb.SC_COUNTRY_WARRESULTINFO_SOCIETYSCOREDATA_ACTORNAME_FIELD.index = 4
pb.SC_COUNTRY_WARRESULTINFO_SOCIETYSCOREDATA_ACTORNAME_FIELD.label = 1
pb.SC_COUNTRY_WARRESULTINFO_SOCIETYSCOREDATA_ACTORNAME_FIELD.has_default_value = false
pb.SC_COUNTRY_WARRESULTINFO_SOCIETYSCOREDATA_ACTORNAME_FIELD.default_value = ""
pb.SC_COUNTRY_WARRESULTINFO_SOCIETYSCOREDATA_ACTORNAME_FIELD.type = 9
pb.SC_COUNTRY_WARRESULTINFO_SOCIETYSCOREDATA_ACTORNAME_FIELD.cpp_type = 9

pb.SC_COUNTRY_WARRESULTINFO_SOCIETYSCOREDATA_COUNTRY_FIELD.name = "Country"
pb.SC_COUNTRY_WARRESULTINFO_SOCIETYSCOREDATA_COUNTRY_FIELD.full_name = ".SC_Country_WarResultInfo.SocietyScoreData.Country"
pb.SC_COUNTRY_WARRESULTINFO_SOCIETYSCOREDATA_COUNTRY_FIELD.number = 6
pb.SC_COUNTRY_WARRESULTINFO_SOCIETYSCOREDATA_COUNTRY_FIELD.index = 5
pb.SC_COUNTRY_WARRESULTINFO_SOCIETYSCOREDATA_COUNTRY_FIELD.label = 1
pb.SC_COUNTRY_WARRESULTINFO_SOCIETYSCOREDATA_COUNTRY_FIELD.has_default_value = false
pb.SC_COUNTRY_WARRESULTINFO_SOCIETYSCOREDATA_COUNTRY_FIELD.default_value = 0
pb.SC_COUNTRY_WARRESULTINFO_SOCIETYSCOREDATA_COUNTRY_FIELD.type = 13
pb.SC_COUNTRY_WARRESULTINFO_SOCIETYSCOREDATA_COUNTRY_FIELD.cpp_type = 3

pb.SC_COUNTRY_WARRESULTINFO_SOCIETYSCOREDATA_LEVEL_FIELD.name = "Level"
pb.SC_COUNTRY_WARRESULTINFO_SOCIETYSCOREDATA_LEVEL_FIELD.full_name = ".SC_Country_WarResultInfo.SocietyScoreData.Level"
pb.SC_COUNTRY_WARRESULTINFO_SOCIETYSCOREDATA_LEVEL_FIELD.number = 7
pb.SC_COUNTRY_WARRESULTINFO_SOCIETYSCOREDATA_LEVEL_FIELD.index = 6
pb.SC_COUNTRY_WARRESULTINFO_SOCIETYSCOREDATA_LEVEL_FIELD.label = 1
pb.SC_COUNTRY_WARRESULTINFO_SOCIETYSCOREDATA_LEVEL_FIELD.has_default_value = false
pb.SC_COUNTRY_WARRESULTINFO_SOCIETYSCOREDATA_LEVEL_FIELD.default_value = 0
pb.SC_COUNTRY_WARRESULTINFO_SOCIETYSCOREDATA_LEVEL_FIELD.type = 13
pb.SC_COUNTRY_WARRESULTINFO_SOCIETYSCOREDATA_LEVEL_FIELD.cpp_type = 3

pb.SC_COUNTRY_WARRESULTINFO_SOCIETYSCOREDATA_SCORE_FIELD.name = "Score"
pb.SC_COUNTRY_WARRESULTINFO_SOCIETYSCOREDATA_SCORE_FIELD.full_name = ".SC_Country_WarResultInfo.SocietyScoreData.Score"
pb.SC_COUNTRY_WARRESULTINFO_SOCIETYSCOREDATA_SCORE_FIELD.number = 8
pb.SC_COUNTRY_WARRESULTINFO_SOCIETYSCOREDATA_SCORE_FIELD.index = 7
pb.SC_COUNTRY_WARRESULTINFO_SOCIETYSCOREDATA_SCORE_FIELD.label = 1
pb.SC_COUNTRY_WARRESULTINFO_SOCIETYSCOREDATA_SCORE_FIELD.has_default_value = false
pb.SC_COUNTRY_WARRESULTINFO_SOCIETYSCOREDATA_SCORE_FIELD.default_value = 0
pb.SC_COUNTRY_WARRESULTINFO_SOCIETYSCOREDATA_SCORE_FIELD.type = 13
pb.SC_COUNTRY_WARRESULTINFO_SOCIETYSCOREDATA_SCORE_FIELD.cpp_type = 3

pb.SC_COUNTRY_WARRESULTINFO_SOCIETYSCOREDATA_PRIZEID_FIELD.name = "PrizeID"
pb.SC_COUNTRY_WARRESULTINFO_SOCIETYSCOREDATA_PRIZEID_FIELD.full_name = ".SC_Country_WarResultInfo.SocietyScoreData.PrizeID"
pb.SC_COUNTRY_WARRESULTINFO_SOCIETYSCOREDATA_PRIZEID_FIELD.number = 9
pb.SC_COUNTRY_WARRESULTINFO_SOCIETYSCOREDATA_PRIZEID_FIELD.index = 8
pb.SC_COUNTRY_WARRESULTINFO_SOCIETYSCOREDATA_PRIZEID_FIELD.label = 1
pb.SC_COUNTRY_WARRESULTINFO_SOCIETYSCOREDATA_PRIZEID_FIELD.has_default_value = false
pb.SC_COUNTRY_WARRESULTINFO_SOCIETYSCOREDATA_PRIZEID_FIELD.default_value = 0
pb.SC_COUNTRY_WARRESULTINFO_SOCIETYSCOREDATA_PRIZEID_FIELD.type = 13
pb.SC_COUNTRY_WARRESULTINFO_SOCIETYSCOREDATA_PRIZEID_FIELD.cpp_type = 3

pb.SC_COUNTRY_WARRESULTINFO_SOCIETYSCOREDATA_PRIZENUM_FIELD.name = "PrizeNum"
pb.SC_COUNTRY_WARRESULTINFO_SOCIETYSCOREDATA_PRIZENUM_FIELD.full_name = ".SC_Country_WarResultInfo.SocietyScoreData.PrizeNum"
pb.SC_COUNTRY_WARRESULTINFO_SOCIETYSCOREDATA_PRIZENUM_FIELD.number = 10
pb.SC_COUNTRY_WARRESULTINFO_SOCIETYSCOREDATA_PRIZENUM_FIELD.index = 9
pb.SC_COUNTRY_WARRESULTINFO_SOCIETYSCOREDATA_PRIZENUM_FIELD.label = 1
pb.SC_COUNTRY_WARRESULTINFO_SOCIETYSCOREDATA_PRIZENUM_FIELD.has_default_value = false
pb.SC_COUNTRY_WARRESULTINFO_SOCIETYSCOREDATA_PRIZENUM_FIELD.default_value = 0
pb.SC_COUNTRY_WARRESULTINFO_SOCIETYSCOREDATA_PRIZENUM_FIELD.type = 13
pb.SC_COUNTRY_WARRESULTINFO_SOCIETYSCOREDATA_PRIZENUM_FIELD.cpp_type = 3

pb.SC_COUNTRY_WARRESULTINFO_SOCIETYSCOREDATA.name = "SocietyScoreData"
pb.SC_COUNTRY_WARRESULTINFO_SOCIETYSCOREDATA.full_name = ".SC_Country_WarResultInfo.SocietyScoreData"
pb.SC_COUNTRY_WARRESULTINFO_SOCIETYSCOREDATA.nested_types = {}
pb.SC_COUNTRY_WARRESULTINFO_SOCIETYSCOREDATA.enum_types = {}
pb.SC_COUNTRY_WARRESULTINFO_SOCIETYSCOREDATA.fields = {pb.SC_COUNTRY_WARRESULTINFO_SOCIETYSCOREDATA_RANK_FIELD, pb.SC_COUNTRY_WARRESULTINFO_SOCIETYSCOREDATA_SOCIETYID_FIELD, pb.SC_COUNTRY_WARRESULTINFO_SOCIETYSCOREDATA_SOCIETYNAME_FIELD, pb.SC_COUNTRY_WARRESULTINFO_SOCIETYSCOREDATA_ACTORID_FIELD, pb.SC_COUNTRY_WARRESULTINFO_SOCIETYSCOREDATA_ACTORNAME_FIELD, pb.SC_COUNTRY_WARRESULTINFO_SOCIETYSCOREDATA_COUNTRY_FIELD, pb.SC_COUNTRY_WARRESULTINFO_SOCIETYSCOREDATA_LEVEL_FIELD, pb.SC_COUNTRY_WARRESULTINFO_SOCIETYSCOREDATA_SCORE_FIELD, pb.SC_COUNTRY_WARRESULTINFO_SOCIETYSCOREDATA_PRIZEID_FIELD, pb.SC_COUNTRY_WARRESULTINFO_SOCIETYSCOREDATA_PRIZENUM_FIELD}
pb.SC_COUNTRY_WARRESULTINFO_SOCIETYSCOREDATA.is_extendable = false
pb.SC_COUNTRY_WARRESULTINFO_SOCIETYSCOREDATA.extensions = {}
pb.SC_COUNTRY_WARRESULTINFO_SOCIETYSCOREDATA.containing_type = pb.SC_COUNTRY_WARRESULTINFO
pb.SC_COUNTRY_WARRESULTINFO_SRCCOUNTRYID_FIELD.name = "SrcCountryID"
pb.SC_COUNTRY_WARRESULTINFO_SRCCOUNTRYID_FIELD.full_name = ".SC_Country_WarResultInfo.SrcCountryID"
pb.SC_COUNTRY_WARRESULTINFO_SRCCOUNTRYID_FIELD.number = 1
pb.SC_COUNTRY_WARRESULTINFO_SRCCOUNTRYID_FIELD.index = 0
pb.SC_COUNTRY_WARRESULTINFO_SRCCOUNTRYID_FIELD.label = 2
pb.SC_COUNTRY_WARRESULTINFO_SRCCOUNTRYID_FIELD.has_default_value = false
pb.SC_COUNTRY_WARRESULTINFO_SRCCOUNTRYID_FIELD.default_value = 0
pb.SC_COUNTRY_WARRESULTINFO_SRCCOUNTRYID_FIELD.type = 13
pb.SC_COUNTRY_WARRESULTINFO_SRCCOUNTRYID_FIELD.cpp_type = 3

pb.SC_COUNTRY_WARRESULTINFO_TARCOUNTRYID_FIELD.name = "TarCountryID"
pb.SC_COUNTRY_WARRESULTINFO_TARCOUNTRYID_FIELD.full_name = ".SC_Country_WarResultInfo.TarCountryID"
pb.SC_COUNTRY_WARRESULTINFO_TARCOUNTRYID_FIELD.number = 2
pb.SC_COUNTRY_WARRESULTINFO_TARCOUNTRYID_FIELD.index = 1
pb.SC_COUNTRY_WARRESULTINFO_TARCOUNTRYID_FIELD.label = 2
pb.SC_COUNTRY_WARRESULTINFO_TARCOUNTRYID_FIELD.has_default_value = false
pb.SC_COUNTRY_WARRESULTINFO_TARCOUNTRYID_FIELD.default_value = 0
pb.SC_COUNTRY_WARRESULTINFO_TARCOUNTRYID_FIELD.type = 13
pb.SC_COUNTRY_WARRESULTINFO_TARCOUNTRYID_FIELD.cpp_type = 3

pb.SC_COUNTRY_WARRESULTINFO_WINCOUNTRYID_FIELD.name = "WinCountryID"
pb.SC_COUNTRY_WARRESULTINFO_WINCOUNTRYID_FIELD.full_name = ".SC_Country_WarResultInfo.WinCountryID"
pb.SC_COUNTRY_WARRESULTINFO_WINCOUNTRYID_FIELD.number = 3
pb.SC_COUNTRY_WARRESULTINFO_WINCOUNTRYID_FIELD.index = 2
pb.SC_COUNTRY_WARRESULTINFO_WINCOUNTRYID_FIELD.label = 2
pb.SC_COUNTRY_WARRESULTINFO_WINCOUNTRYID_FIELD.has_default_value = false
pb.SC_COUNTRY_WARRESULTINFO_WINCOUNTRYID_FIELD.default_value = 0
pb.SC_COUNTRY_WARRESULTINFO_WINCOUNTRYID_FIELD.type = 13
pb.SC_COUNTRY_WARRESULTINFO_WINCOUNTRYID_FIELD.cpp_type = 3

pb.SC_COUNTRY_WARRESULTINFO_ACTORSCORELIST_FIELD.name = "ActorScoreList"
pb.SC_COUNTRY_WARRESULTINFO_ACTORSCORELIST_FIELD.full_name = ".SC_Country_WarResultInfo.ActorScoreList"
pb.SC_COUNTRY_WARRESULTINFO_ACTORSCORELIST_FIELD.number = 4
pb.SC_COUNTRY_WARRESULTINFO_ACTORSCORELIST_FIELD.index = 3
pb.SC_COUNTRY_WARRESULTINFO_ACTORSCORELIST_FIELD.label = 3
pb.SC_COUNTRY_WARRESULTINFO_ACTORSCORELIST_FIELD.has_default_value = false
pb.SC_COUNTRY_WARRESULTINFO_ACTORSCORELIST_FIELD.default_value = {}
pb.SC_COUNTRY_WARRESULTINFO_ACTORSCORELIST_FIELD.message_type = pb.SC_COUNTRY_WARRESULTINFO_ACTORSCOREDATA
pb.SC_COUNTRY_WARRESULTINFO_ACTORSCORELIST_FIELD.type = 11
pb.SC_COUNTRY_WARRESULTINFO_ACTORSCORELIST_FIELD.cpp_type = 10

pb.SC_COUNTRY_WARRESULTINFO_SELFACTORSCOREINFO_FIELD.name = "SelfActorScoreInfo"
pb.SC_COUNTRY_WARRESULTINFO_SELFACTORSCOREINFO_FIELD.full_name = ".SC_Country_WarResultInfo.SelfActorScoreInfo"
pb.SC_COUNTRY_WARRESULTINFO_SELFACTORSCOREINFO_FIELD.number = 5
pb.SC_COUNTRY_WARRESULTINFO_SELFACTORSCOREINFO_FIELD.index = 4
pb.SC_COUNTRY_WARRESULTINFO_SELFACTORSCOREINFO_FIELD.label = 1
pb.SC_COUNTRY_WARRESULTINFO_SELFACTORSCOREINFO_FIELD.has_default_value = false
pb.SC_COUNTRY_WARRESULTINFO_SELFACTORSCOREINFO_FIELD.default_value = nil
pb.SC_COUNTRY_WARRESULTINFO_SELFACTORSCOREINFO_FIELD.message_type = pb.SC_COUNTRY_WARRESULTINFO_ACTORSCOREDATA
pb.SC_COUNTRY_WARRESULTINFO_SELFACTORSCOREINFO_FIELD.type = 11
pb.SC_COUNTRY_WARRESULTINFO_SELFACTORSCOREINFO_FIELD.cpp_type = 10

pb.SC_COUNTRY_WARRESULTINFO_SOCIETYSCORELIST_FIELD.name = "SocietyScoreList"
pb.SC_COUNTRY_WARRESULTINFO_SOCIETYSCORELIST_FIELD.full_name = ".SC_Country_WarResultInfo.SocietyScoreList"
pb.SC_COUNTRY_WARRESULTINFO_SOCIETYSCORELIST_FIELD.number = 6
pb.SC_COUNTRY_WARRESULTINFO_SOCIETYSCORELIST_FIELD.index = 5
pb.SC_COUNTRY_WARRESULTINFO_SOCIETYSCORELIST_FIELD.label = 3
pb.SC_COUNTRY_WARRESULTINFO_SOCIETYSCORELIST_FIELD.has_default_value = false
pb.SC_COUNTRY_WARRESULTINFO_SOCIETYSCORELIST_FIELD.default_value = {}
pb.SC_COUNTRY_WARRESULTINFO_SOCIETYSCORELIST_FIELD.message_type = pb.SC_COUNTRY_WARRESULTINFO_SOCIETYSCOREDATA
pb.SC_COUNTRY_WARRESULTINFO_SOCIETYSCORELIST_FIELD.type = 11
pb.SC_COUNTRY_WARRESULTINFO_SOCIETYSCORELIST_FIELD.cpp_type = 10

pb.SC_COUNTRY_WARRESULTINFO_SELFSOCIETYSCOREINFO_FIELD.name = "SelfSocietyScoreInfo"
pb.SC_COUNTRY_WARRESULTINFO_SELFSOCIETYSCOREINFO_FIELD.full_name = ".SC_Country_WarResultInfo.SelfSocietyScoreInfo"
pb.SC_COUNTRY_WARRESULTINFO_SELFSOCIETYSCOREINFO_FIELD.number = 7
pb.SC_COUNTRY_WARRESULTINFO_SELFSOCIETYSCOREINFO_FIELD.index = 6
pb.SC_COUNTRY_WARRESULTINFO_SELFSOCIETYSCOREINFO_FIELD.label = 1
pb.SC_COUNTRY_WARRESULTINFO_SELFSOCIETYSCOREINFO_FIELD.has_default_value = false
pb.SC_COUNTRY_WARRESULTINFO_SELFSOCIETYSCOREINFO_FIELD.default_value = nil
pb.SC_COUNTRY_WARRESULTINFO_SELFSOCIETYSCOREINFO_FIELD.message_type = pb.SC_COUNTRY_WARRESULTINFO_SOCIETYSCOREDATA
pb.SC_COUNTRY_WARRESULTINFO_SELFSOCIETYSCOREINFO_FIELD.type = 11
pb.SC_COUNTRY_WARRESULTINFO_SELFSOCIETYSCOREINFO_FIELD.cpp_type = 10

pb.SC_COUNTRY_WARRESULTINFO.name = "SC_Country_WarResultInfo"
pb.SC_COUNTRY_WARRESULTINFO.full_name = ".SC_Country_WarResultInfo"
pb.SC_COUNTRY_WARRESULTINFO.nested_types = {pb.SC_COUNTRY_WARRESULTINFO_ACTORSCOREDATA, pb.SC_COUNTRY_WARRESULTINFO_SOCIETYSCOREDATA}
pb.SC_COUNTRY_WARRESULTINFO.enum_types = {}
pb.SC_COUNTRY_WARRESULTINFO.fields = {pb.SC_COUNTRY_WARRESULTINFO_SRCCOUNTRYID_FIELD, pb.SC_COUNTRY_WARRESULTINFO_TARCOUNTRYID_FIELD, pb.SC_COUNTRY_WARRESULTINFO_WINCOUNTRYID_FIELD, pb.SC_COUNTRY_WARRESULTINFO_ACTORSCORELIST_FIELD, pb.SC_COUNTRY_WARRESULTINFO_SELFACTORSCOREINFO_FIELD, pb.SC_COUNTRY_WARRESULTINFO_SOCIETYSCORELIST_FIELD, pb.SC_COUNTRY_WARRESULTINFO_SELFSOCIETYSCOREINFO_FIELD}
pb.SC_COUNTRY_WARRESULTINFO.is_extendable = false
pb.SC_COUNTRY_WARRESULTINFO.extensions = {}
pb.CS_COUNTRY_GETCOUNTRYKINGWARINFO_COUNTRYID_FIELD.name = "CountryID"
pb.CS_COUNTRY_GETCOUNTRYKINGWARINFO_COUNTRYID_FIELD.full_name = ".CS_Country_GetCountryKingWarInfo.CountryID"
pb.CS_COUNTRY_GETCOUNTRYKINGWARINFO_COUNTRYID_FIELD.number = 1
pb.CS_COUNTRY_GETCOUNTRYKINGWARINFO_COUNTRYID_FIELD.index = 0
pb.CS_COUNTRY_GETCOUNTRYKINGWARINFO_COUNTRYID_FIELD.label = 2
pb.CS_COUNTRY_GETCOUNTRYKINGWARINFO_COUNTRYID_FIELD.has_default_value = false
pb.CS_COUNTRY_GETCOUNTRYKINGWARINFO_COUNTRYID_FIELD.default_value = 0
pb.CS_COUNTRY_GETCOUNTRYKINGWARINFO_COUNTRYID_FIELD.type = 13
pb.CS_COUNTRY_GETCOUNTRYKINGWARINFO_COUNTRYID_FIELD.cpp_type = 3

pb.CS_COUNTRY_GETCOUNTRYKINGWARINFO.name = "CS_Country_GetCountryKingWarInfo"
pb.CS_COUNTRY_GETCOUNTRYKINGWARINFO.full_name = ".CS_Country_GetCountryKingWarInfo"
pb.CS_COUNTRY_GETCOUNTRYKINGWARINFO.nested_types = {}
pb.CS_COUNTRY_GETCOUNTRYKINGWARINFO.enum_types = {}
pb.CS_COUNTRY_GETCOUNTRYKINGWARINFO.fields = {pb.CS_COUNTRY_GETCOUNTRYKINGWARINFO_COUNTRYID_FIELD}
pb.CS_COUNTRY_GETCOUNTRYKINGWARINFO.is_extendable = false
pb.CS_COUNTRY_GETCOUNTRYKINGWARINFO.extensions = {}
pb.SC_COUNTRY_GETCOUNTRYKINGWARINFO_KINGWARSIGNDATA_SOCIETYID_FIELD.name = "SocietyID"
pb.SC_COUNTRY_GETCOUNTRYKINGWARINFO_KINGWARSIGNDATA_SOCIETYID_FIELD.full_name = ".SC_Country_GetCountryKingWarInfo.KingWarSignData.SocietyID"
pb.SC_COUNTRY_GETCOUNTRYKINGWARINFO_KINGWARSIGNDATA_SOCIETYID_FIELD.number = 1
pb.SC_COUNTRY_GETCOUNTRYKINGWARINFO_KINGWARSIGNDATA_SOCIETYID_FIELD.index = 0
pb.SC_COUNTRY_GETCOUNTRYKINGWARINFO_KINGWARSIGNDATA_SOCIETYID_FIELD.label = 1
pb.SC_COUNTRY_GETCOUNTRYKINGWARINFO_KINGWARSIGNDATA_SOCIETYID_FIELD.has_default_value = false
pb.SC_COUNTRY_GETCOUNTRYKINGWARINFO_KINGWARSIGNDATA_SOCIETYID_FIELD.default_value = 0
pb.SC_COUNTRY_GETCOUNTRYKINGWARINFO_KINGWARSIGNDATA_SOCIETYID_FIELD.type = 13
pb.SC_COUNTRY_GETCOUNTRYKINGWARINFO_KINGWARSIGNDATA_SOCIETYID_FIELD.cpp_type = 3

pb.SC_COUNTRY_GETCOUNTRYKINGWARINFO_KINGWARSIGNDATA_SOCIETYNAME_FIELD.name = "SocietyName"
pb.SC_COUNTRY_GETCOUNTRYKINGWARINFO_KINGWARSIGNDATA_SOCIETYNAME_FIELD.full_name = ".SC_Country_GetCountryKingWarInfo.KingWarSignData.SocietyName"
pb.SC_COUNTRY_GETCOUNTRYKINGWARINFO_KINGWARSIGNDATA_SOCIETYNAME_FIELD.number = 2
pb.SC_COUNTRY_GETCOUNTRYKINGWARINFO_KINGWARSIGNDATA_SOCIETYNAME_FIELD.index = 1
pb.SC_COUNTRY_GETCOUNTRYKINGWARINFO_KINGWARSIGNDATA_SOCIETYNAME_FIELD.label = 1
pb.SC_COUNTRY_GETCOUNTRYKINGWARINFO_KINGWARSIGNDATA_SOCIETYNAME_FIELD.has_default_value = false
pb.SC_COUNTRY_GETCOUNTRYKINGWARINFO_KINGWARSIGNDATA_SOCIETYNAME_FIELD.default_value = ""
pb.SC_COUNTRY_GETCOUNTRYKINGWARINFO_KINGWARSIGNDATA_SOCIETYNAME_FIELD.type = 9
pb.SC_COUNTRY_GETCOUNTRYKINGWARINFO_KINGWARSIGNDATA_SOCIETYNAME_FIELD.cpp_type = 9

pb.SC_COUNTRY_GETCOUNTRYKINGWARINFO_KINGWARSIGNDATA_ACTORID_FIELD.name = "ActorID"
pb.SC_COUNTRY_GETCOUNTRYKINGWARINFO_KINGWARSIGNDATA_ACTORID_FIELD.full_name = ".SC_Country_GetCountryKingWarInfo.KingWarSignData.ActorID"
pb.SC_COUNTRY_GETCOUNTRYKINGWARINFO_KINGWARSIGNDATA_ACTORID_FIELD.number = 3
pb.SC_COUNTRY_GETCOUNTRYKINGWARINFO_KINGWARSIGNDATA_ACTORID_FIELD.index = 2
pb.SC_COUNTRY_GETCOUNTRYKINGWARINFO_KINGWARSIGNDATA_ACTORID_FIELD.label = 1
pb.SC_COUNTRY_GETCOUNTRYKINGWARINFO_KINGWARSIGNDATA_ACTORID_FIELD.has_default_value = false
pb.SC_COUNTRY_GETCOUNTRYKINGWARINFO_KINGWARSIGNDATA_ACTORID_FIELD.default_value = 0
pb.SC_COUNTRY_GETCOUNTRYKINGWARINFO_KINGWARSIGNDATA_ACTORID_FIELD.type = 13
pb.SC_COUNTRY_GETCOUNTRYKINGWARINFO_KINGWARSIGNDATA_ACTORID_FIELD.cpp_type = 3

pb.SC_COUNTRY_GETCOUNTRYKINGWARINFO_KINGWARSIGNDATA_ACTORNAME_FIELD.name = "ActorName"
pb.SC_COUNTRY_GETCOUNTRYKINGWARINFO_KINGWARSIGNDATA_ACTORNAME_FIELD.full_name = ".SC_Country_GetCountryKingWarInfo.KingWarSignData.ActorName"
pb.SC_COUNTRY_GETCOUNTRYKINGWARINFO_KINGWARSIGNDATA_ACTORNAME_FIELD.number = 4
pb.SC_COUNTRY_GETCOUNTRYKINGWARINFO_KINGWARSIGNDATA_ACTORNAME_FIELD.index = 3
pb.SC_COUNTRY_GETCOUNTRYKINGWARINFO_KINGWARSIGNDATA_ACTORNAME_FIELD.label = 1
pb.SC_COUNTRY_GETCOUNTRYKINGWARINFO_KINGWARSIGNDATA_ACTORNAME_FIELD.has_default_value = false
pb.SC_COUNTRY_GETCOUNTRYKINGWARINFO_KINGWARSIGNDATA_ACTORNAME_FIELD.default_value = ""
pb.SC_COUNTRY_GETCOUNTRYKINGWARINFO_KINGWARSIGNDATA_ACTORNAME_FIELD.type = 9
pb.SC_COUNTRY_GETCOUNTRYKINGWARINFO_KINGWARSIGNDATA_ACTORNAME_FIELD.cpp_type = 9

pb.SC_COUNTRY_GETCOUNTRYKINGWARINFO_KINGWARSIGNDATA_LEVEL_FIELD.name = "Level"
pb.SC_COUNTRY_GETCOUNTRYKINGWARINFO_KINGWARSIGNDATA_LEVEL_FIELD.full_name = ".SC_Country_GetCountryKingWarInfo.KingWarSignData.Level"
pb.SC_COUNTRY_GETCOUNTRYKINGWARINFO_KINGWARSIGNDATA_LEVEL_FIELD.number = 5
pb.SC_COUNTRY_GETCOUNTRYKINGWARINFO_KINGWARSIGNDATA_LEVEL_FIELD.index = 4
pb.SC_COUNTRY_GETCOUNTRYKINGWARINFO_KINGWARSIGNDATA_LEVEL_FIELD.label = 1
pb.SC_COUNTRY_GETCOUNTRYKINGWARINFO_KINGWARSIGNDATA_LEVEL_FIELD.has_default_value = false
pb.SC_COUNTRY_GETCOUNTRYKINGWARINFO_KINGWARSIGNDATA_LEVEL_FIELD.default_value = 0
pb.SC_COUNTRY_GETCOUNTRYKINGWARINFO_KINGWARSIGNDATA_LEVEL_FIELD.type = 13
pb.SC_COUNTRY_GETCOUNTRYKINGWARINFO_KINGWARSIGNDATA_LEVEL_FIELD.cpp_type = 3

pb.SC_COUNTRY_GETCOUNTRYKINGWARINFO_KINGWARSIGNDATA.name = "KingWarSignData"
pb.SC_COUNTRY_GETCOUNTRYKINGWARINFO_KINGWARSIGNDATA.full_name = ".SC_Country_GetCountryKingWarInfo.KingWarSignData"
pb.SC_COUNTRY_GETCOUNTRYKINGWARINFO_KINGWARSIGNDATA.nested_types = {}
pb.SC_COUNTRY_GETCOUNTRYKINGWARINFO_KINGWARSIGNDATA.enum_types = {}
pb.SC_COUNTRY_GETCOUNTRYKINGWARINFO_KINGWARSIGNDATA.fields = {pb.SC_COUNTRY_GETCOUNTRYKINGWARINFO_KINGWARSIGNDATA_SOCIETYID_FIELD, pb.SC_COUNTRY_GETCOUNTRYKINGWARINFO_KINGWARSIGNDATA_SOCIETYNAME_FIELD, pb.SC_COUNTRY_GETCOUNTRYKINGWARINFO_KINGWARSIGNDATA_ACTORID_FIELD, pb.SC_COUNTRY_GETCOUNTRYKINGWARINFO_KINGWARSIGNDATA_ACTORNAME_FIELD, pb.SC_COUNTRY_GETCOUNTRYKINGWARINFO_KINGWARSIGNDATA_LEVEL_FIELD}
pb.SC_COUNTRY_GETCOUNTRYKINGWARINFO_KINGWARSIGNDATA.is_extendable = false
pb.SC_COUNTRY_GETCOUNTRYKINGWARINFO_KINGWARSIGNDATA.extensions = {}
pb.SC_COUNTRY_GETCOUNTRYKINGWARINFO_KINGWARSIGNDATA.containing_type = pb.SC_COUNTRY_GETCOUNTRYKINGWARINFO
pb.SC_COUNTRY_GETCOUNTRYKINGWARINFO_COUNTRYID_FIELD.name = "CountryID"
pb.SC_COUNTRY_GETCOUNTRYKINGWARINFO_COUNTRYID_FIELD.full_name = ".SC_Country_GetCountryKingWarInfo.CountryID"
pb.SC_COUNTRY_GETCOUNTRYKINGWARINFO_COUNTRYID_FIELD.number = 1
pb.SC_COUNTRY_GETCOUNTRYKINGWARINFO_COUNTRYID_FIELD.index = 0
pb.SC_COUNTRY_GETCOUNTRYKINGWARINFO_COUNTRYID_FIELD.label = 2
pb.SC_COUNTRY_GETCOUNTRYKINGWARINFO_COUNTRYID_FIELD.has_default_value = false
pb.SC_COUNTRY_GETCOUNTRYKINGWARINFO_COUNTRYID_FIELD.default_value = 0
pb.SC_COUNTRY_GETCOUNTRYKINGWARINFO_COUNTRYID_FIELD.type = 13
pb.SC_COUNTRY_GETCOUNTRYKINGWARINFO_COUNTRYID_FIELD.cpp_type = 3

pb.SC_COUNTRY_GETCOUNTRYKINGWARINFO_SIGNLIST_FIELD.name = "SignList"
pb.SC_COUNTRY_GETCOUNTRYKINGWARINFO_SIGNLIST_FIELD.full_name = ".SC_Country_GetCountryKingWarInfo.SignList"
pb.SC_COUNTRY_GETCOUNTRYKINGWARINFO_SIGNLIST_FIELD.number = 2
pb.SC_COUNTRY_GETCOUNTRYKINGWARINFO_SIGNLIST_FIELD.index = 1
pb.SC_COUNTRY_GETCOUNTRYKINGWARINFO_SIGNLIST_FIELD.label = 3
pb.SC_COUNTRY_GETCOUNTRYKINGWARINFO_SIGNLIST_FIELD.has_default_value = false
pb.SC_COUNTRY_GETCOUNTRYKINGWARINFO_SIGNLIST_FIELD.default_value = {}
pb.SC_COUNTRY_GETCOUNTRYKINGWARINFO_SIGNLIST_FIELD.message_type = pb.SC_COUNTRY_GETCOUNTRYKINGWARINFO_KINGWARSIGNDATA
pb.SC_COUNTRY_GETCOUNTRYKINGWARINFO_SIGNLIST_FIELD.type = 11
pb.SC_COUNTRY_GETCOUNTRYKINGWARINFO_SIGNLIST_FIELD.cpp_type = 10

pb.SC_COUNTRY_GETCOUNTRYKINGWARINFO.name = "SC_Country_GetCountryKingWarInfo"
pb.SC_COUNTRY_GETCOUNTRYKINGWARINFO.full_name = ".SC_Country_GetCountryKingWarInfo"
pb.SC_COUNTRY_GETCOUNTRYKINGWARINFO.nested_types = {pb.SC_COUNTRY_GETCOUNTRYKINGWARINFO_KINGWARSIGNDATA}
pb.SC_COUNTRY_GETCOUNTRYKINGWARINFO.enum_types = {}
pb.SC_COUNTRY_GETCOUNTRYKINGWARINFO.fields = {pb.SC_COUNTRY_GETCOUNTRYKINGWARINFO_COUNTRYID_FIELD, pb.SC_COUNTRY_GETCOUNTRYKINGWARINFO_SIGNLIST_FIELD}
pb.SC_COUNTRY_GETCOUNTRYKINGWARINFO.is_extendable = false
pb.SC_COUNTRY_GETCOUNTRYKINGWARINFO.extensions = {}
pb.SC_COUNTRY_KINGWARSYNCFIELDINFO_MONSTERDATA_MONSTERTYPE_FIELD.name = "MonsterType"
pb.SC_COUNTRY_KINGWARSYNCFIELDINFO_MONSTERDATA_MONSTERTYPE_FIELD.full_name = ".SC_Country_KingWarSyncFieldInfo.MonsterData.MonsterType"
pb.SC_COUNTRY_KINGWARSYNCFIELDINFO_MONSTERDATA_MONSTERTYPE_FIELD.number = 1
pb.SC_COUNTRY_KINGWARSYNCFIELDINFO_MONSTERDATA_MONSTERTYPE_FIELD.index = 0
pb.SC_COUNTRY_KINGWARSYNCFIELDINFO_MONSTERDATA_MONSTERTYPE_FIELD.label = 1
pb.SC_COUNTRY_KINGWARSYNCFIELDINFO_MONSTERDATA_MONSTERTYPE_FIELD.has_default_value = false
pb.SC_COUNTRY_KINGWARSYNCFIELDINFO_MONSTERDATA_MONSTERTYPE_FIELD.default_value = 0
pb.SC_COUNTRY_KINGWARSYNCFIELDINFO_MONSTERDATA_MONSTERTYPE_FIELD.type = 13
pb.SC_COUNTRY_KINGWARSYNCFIELDINFO_MONSTERDATA_MONSTERTYPE_FIELD.cpp_type = 3

pb.SC_COUNTRY_KINGWARSYNCFIELDINFO_MONSTERDATA_SOCIETYID_FIELD.name = "SocietyID"
pb.SC_COUNTRY_KINGWARSYNCFIELDINFO_MONSTERDATA_SOCIETYID_FIELD.full_name = ".SC_Country_KingWarSyncFieldInfo.MonsterData.SocietyID"
pb.SC_COUNTRY_KINGWARSYNCFIELDINFO_MONSTERDATA_SOCIETYID_FIELD.number = 2
pb.SC_COUNTRY_KINGWARSYNCFIELDINFO_MONSTERDATA_SOCIETYID_FIELD.index = 1
pb.SC_COUNTRY_KINGWARSYNCFIELDINFO_MONSTERDATA_SOCIETYID_FIELD.label = 1
pb.SC_COUNTRY_KINGWARSYNCFIELDINFO_MONSTERDATA_SOCIETYID_FIELD.has_default_value = false
pb.SC_COUNTRY_KINGWARSYNCFIELDINFO_MONSTERDATA_SOCIETYID_FIELD.default_value = 0
pb.SC_COUNTRY_KINGWARSYNCFIELDINFO_MONSTERDATA_SOCIETYID_FIELD.type = 13
pb.SC_COUNTRY_KINGWARSYNCFIELDINFO_MONSTERDATA_SOCIETYID_FIELD.cpp_type = 3

pb.SC_COUNTRY_KINGWARSYNCFIELDINFO_MONSTERDATA_SOCIETYNAME_FIELD.name = "SocietyName"
pb.SC_COUNTRY_KINGWARSYNCFIELDINFO_MONSTERDATA_SOCIETYNAME_FIELD.full_name = ".SC_Country_KingWarSyncFieldInfo.MonsterData.SocietyName"
pb.SC_COUNTRY_KINGWARSYNCFIELDINFO_MONSTERDATA_SOCIETYNAME_FIELD.number = 3
pb.SC_COUNTRY_KINGWARSYNCFIELDINFO_MONSTERDATA_SOCIETYNAME_FIELD.index = 2
pb.SC_COUNTRY_KINGWARSYNCFIELDINFO_MONSTERDATA_SOCIETYNAME_FIELD.label = 1
pb.SC_COUNTRY_KINGWARSYNCFIELDINFO_MONSTERDATA_SOCIETYNAME_FIELD.has_default_value = false
pb.SC_COUNTRY_KINGWARSYNCFIELDINFO_MONSTERDATA_SOCIETYNAME_FIELD.default_value = ""
pb.SC_COUNTRY_KINGWARSYNCFIELDINFO_MONSTERDATA_SOCIETYNAME_FIELD.type = 9
pb.SC_COUNTRY_KINGWARSYNCFIELDINFO_MONSTERDATA_SOCIETYNAME_FIELD.cpp_type = 9

pb.SC_COUNTRY_KINGWARSYNCFIELDINFO_MONSTERDATA.name = "MonsterData"
pb.SC_COUNTRY_KINGWARSYNCFIELDINFO_MONSTERDATA.full_name = ".SC_Country_KingWarSyncFieldInfo.MonsterData"
pb.SC_COUNTRY_KINGWARSYNCFIELDINFO_MONSTERDATA.nested_types = {}
pb.SC_COUNTRY_KINGWARSYNCFIELDINFO_MONSTERDATA.enum_types = {}
pb.SC_COUNTRY_KINGWARSYNCFIELDINFO_MONSTERDATA.fields = {pb.SC_COUNTRY_KINGWARSYNCFIELDINFO_MONSTERDATA_MONSTERTYPE_FIELD, pb.SC_COUNTRY_KINGWARSYNCFIELDINFO_MONSTERDATA_SOCIETYID_FIELD, pb.SC_COUNTRY_KINGWARSYNCFIELDINFO_MONSTERDATA_SOCIETYNAME_FIELD}
pb.SC_COUNTRY_KINGWARSYNCFIELDINFO_MONSTERDATA.is_extendable = false
pb.SC_COUNTRY_KINGWARSYNCFIELDINFO_MONSTERDATA.extensions = {}
pb.SC_COUNTRY_KINGWARSYNCFIELDINFO_MONSTERDATA.containing_type = pb.SC_COUNTRY_KINGWARSYNCFIELDINFO
pb.SC_COUNTRY_KINGWARSYNCFIELDINFO_MONSTERLIST_FIELD.name = "MonsterList"
pb.SC_COUNTRY_KINGWARSYNCFIELDINFO_MONSTERLIST_FIELD.full_name = ".SC_Country_KingWarSyncFieldInfo.MonsterList"
pb.SC_COUNTRY_KINGWARSYNCFIELDINFO_MONSTERLIST_FIELD.number = 1
pb.SC_COUNTRY_KINGWARSYNCFIELDINFO_MONSTERLIST_FIELD.index = 0
pb.SC_COUNTRY_KINGWARSYNCFIELDINFO_MONSTERLIST_FIELD.label = 3
pb.SC_COUNTRY_KINGWARSYNCFIELDINFO_MONSTERLIST_FIELD.has_default_value = false
pb.SC_COUNTRY_KINGWARSYNCFIELDINFO_MONSTERLIST_FIELD.default_value = {}
pb.SC_COUNTRY_KINGWARSYNCFIELDINFO_MONSTERLIST_FIELD.message_type = pb.SC_COUNTRY_KINGWARSYNCFIELDINFO_MONSTERDATA
pb.SC_COUNTRY_KINGWARSYNCFIELDINFO_MONSTERLIST_FIELD.type = 11
pb.SC_COUNTRY_KINGWARSYNCFIELDINFO_MONSTERLIST_FIELD.cpp_type = 10

pb.SC_COUNTRY_KINGWARSYNCFIELDINFO.name = "SC_Country_KingWarSyncFieldInfo"
pb.SC_COUNTRY_KINGWARSYNCFIELDINFO.full_name = ".SC_Country_KingWarSyncFieldInfo"
pb.SC_COUNTRY_KINGWARSYNCFIELDINFO.nested_types = {pb.SC_COUNTRY_KINGWARSYNCFIELDINFO_MONSTERDATA}
pb.SC_COUNTRY_KINGWARSYNCFIELDINFO.enum_types = {}
pb.SC_COUNTRY_KINGWARSYNCFIELDINFO.fields = {pb.SC_COUNTRY_KINGWARSYNCFIELDINFO_MONSTERLIST_FIELD}
pb.SC_COUNTRY_KINGWARSYNCFIELDINFO.is_extendable = false
pb.SC_COUNTRY_KINGWARSYNCFIELDINFO.extensions = {}
pb.SC_COUNTRY_KINGWARSYNCSCOREINFO_SCOREDATA_RANK_FIELD.name = "Rank"
pb.SC_COUNTRY_KINGWARSYNCSCOREINFO_SCOREDATA_RANK_FIELD.full_name = ".SC_Country_KingWarSyncScoreInfo.ScoreData.Rank"
pb.SC_COUNTRY_KINGWARSYNCSCOREINFO_SCOREDATA_RANK_FIELD.number = 1
pb.SC_COUNTRY_KINGWARSYNCSCOREINFO_SCOREDATA_RANK_FIELD.index = 0
pb.SC_COUNTRY_KINGWARSYNCSCOREINFO_SCOREDATA_RANK_FIELD.label = 1
pb.SC_COUNTRY_KINGWARSYNCSCOREINFO_SCOREDATA_RANK_FIELD.has_default_value = false
pb.SC_COUNTRY_KINGWARSYNCSCOREINFO_SCOREDATA_RANK_FIELD.default_value = 0
pb.SC_COUNTRY_KINGWARSYNCSCOREINFO_SCOREDATA_RANK_FIELD.type = 13
pb.SC_COUNTRY_KINGWARSYNCSCOREINFO_SCOREDATA_RANK_FIELD.cpp_type = 3

pb.SC_COUNTRY_KINGWARSYNCSCOREINFO_SCOREDATA_SOCIETYID_FIELD.name = "SocietyID"
pb.SC_COUNTRY_KINGWARSYNCSCOREINFO_SCOREDATA_SOCIETYID_FIELD.full_name = ".SC_Country_KingWarSyncScoreInfo.ScoreData.SocietyID"
pb.SC_COUNTRY_KINGWARSYNCSCOREINFO_SCOREDATA_SOCIETYID_FIELD.number = 2
pb.SC_COUNTRY_KINGWARSYNCSCOREINFO_SCOREDATA_SOCIETYID_FIELD.index = 1
pb.SC_COUNTRY_KINGWARSYNCSCOREINFO_SCOREDATA_SOCIETYID_FIELD.label = 1
pb.SC_COUNTRY_KINGWARSYNCSCOREINFO_SCOREDATA_SOCIETYID_FIELD.has_default_value = false
pb.SC_COUNTRY_KINGWARSYNCSCOREINFO_SCOREDATA_SOCIETYID_FIELD.default_value = 0
pb.SC_COUNTRY_KINGWARSYNCSCOREINFO_SCOREDATA_SOCIETYID_FIELD.type = 13
pb.SC_COUNTRY_KINGWARSYNCSCOREINFO_SCOREDATA_SOCIETYID_FIELD.cpp_type = 3

pb.SC_COUNTRY_KINGWARSYNCSCOREINFO_SCOREDATA_SOCIETYNAME_FIELD.name = "SocietyName"
pb.SC_COUNTRY_KINGWARSYNCSCOREINFO_SCOREDATA_SOCIETYNAME_FIELD.full_name = ".SC_Country_KingWarSyncScoreInfo.ScoreData.SocietyName"
pb.SC_COUNTRY_KINGWARSYNCSCOREINFO_SCOREDATA_SOCIETYNAME_FIELD.number = 3
pb.SC_COUNTRY_KINGWARSYNCSCOREINFO_SCOREDATA_SOCIETYNAME_FIELD.index = 2
pb.SC_COUNTRY_KINGWARSYNCSCOREINFO_SCOREDATA_SOCIETYNAME_FIELD.label = 1
pb.SC_COUNTRY_KINGWARSYNCSCOREINFO_SCOREDATA_SOCIETYNAME_FIELD.has_default_value = false
pb.SC_COUNTRY_KINGWARSYNCSCOREINFO_SCOREDATA_SOCIETYNAME_FIELD.default_value = ""
pb.SC_COUNTRY_KINGWARSYNCSCOREINFO_SCOREDATA_SOCIETYNAME_FIELD.type = 9
pb.SC_COUNTRY_KINGWARSYNCSCOREINFO_SCOREDATA_SOCIETYNAME_FIELD.cpp_type = 9

pb.SC_COUNTRY_KINGWARSYNCSCOREINFO_SCOREDATA_SCORE_FIELD.name = "Score"
pb.SC_COUNTRY_KINGWARSYNCSCOREINFO_SCOREDATA_SCORE_FIELD.full_name = ".SC_Country_KingWarSyncScoreInfo.ScoreData.Score"
pb.SC_COUNTRY_KINGWARSYNCSCOREINFO_SCOREDATA_SCORE_FIELD.number = 4
pb.SC_COUNTRY_KINGWARSYNCSCOREINFO_SCOREDATA_SCORE_FIELD.index = 3
pb.SC_COUNTRY_KINGWARSYNCSCOREINFO_SCOREDATA_SCORE_FIELD.label = 1
pb.SC_COUNTRY_KINGWARSYNCSCOREINFO_SCOREDATA_SCORE_FIELD.has_default_value = false
pb.SC_COUNTRY_KINGWARSYNCSCOREINFO_SCOREDATA_SCORE_FIELD.default_value = 0
pb.SC_COUNTRY_KINGWARSYNCSCOREINFO_SCOREDATA_SCORE_FIELD.type = 13
pb.SC_COUNTRY_KINGWARSYNCSCOREINFO_SCOREDATA_SCORE_FIELD.cpp_type = 3

pb.SC_COUNTRY_KINGWARSYNCSCOREINFO_SCOREDATA.name = "ScoreData"
pb.SC_COUNTRY_KINGWARSYNCSCOREINFO_SCOREDATA.full_name = ".SC_Country_KingWarSyncScoreInfo.ScoreData"
pb.SC_COUNTRY_KINGWARSYNCSCOREINFO_SCOREDATA.nested_types = {}
pb.SC_COUNTRY_KINGWARSYNCSCOREINFO_SCOREDATA.enum_types = {}
pb.SC_COUNTRY_KINGWARSYNCSCOREINFO_SCOREDATA.fields = {pb.SC_COUNTRY_KINGWARSYNCSCOREINFO_SCOREDATA_RANK_FIELD, pb.SC_COUNTRY_KINGWARSYNCSCOREINFO_SCOREDATA_SOCIETYID_FIELD, pb.SC_COUNTRY_KINGWARSYNCSCOREINFO_SCOREDATA_SOCIETYNAME_FIELD, pb.SC_COUNTRY_KINGWARSYNCSCOREINFO_SCOREDATA_SCORE_FIELD}
pb.SC_COUNTRY_KINGWARSYNCSCOREINFO_SCOREDATA.is_extendable = false
pb.SC_COUNTRY_KINGWARSYNCSCOREINFO_SCOREDATA.extensions = {}
pb.SC_COUNTRY_KINGWARSYNCSCOREINFO_SCOREDATA.containing_type = pb.SC_COUNTRY_KINGWARSYNCSCOREINFO
pb.SC_COUNTRY_KINGWARSYNCSCOREINFO_MSGTYPE_FIELD.name = "MsgType"
pb.SC_COUNTRY_KINGWARSYNCSCOREINFO_MSGTYPE_FIELD.full_name = ".SC_Country_KingWarSyncScoreInfo.MsgType"
pb.SC_COUNTRY_KINGWARSYNCSCOREINFO_MSGTYPE_FIELD.number = 1
pb.SC_COUNTRY_KINGWARSYNCSCOREINFO_MSGTYPE_FIELD.index = 0
pb.SC_COUNTRY_KINGWARSYNCSCOREINFO_MSGTYPE_FIELD.label = 2
pb.SC_COUNTRY_KINGWARSYNCSCOREINFO_MSGTYPE_FIELD.has_default_value = false
pb.SC_COUNTRY_KINGWARSYNCSCOREINFO_MSGTYPE_FIELD.default_value = 0
pb.SC_COUNTRY_KINGWARSYNCSCOREINFO_MSGTYPE_FIELD.type = 13
pb.SC_COUNTRY_KINGWARSYNCSCOREINFO_MSGTYPE_FIELD.cpp_type = 3

pb.SC_COUNTRY_KINGWARSYNCSCOREINFO_RANKLIST_FIELD.name = "RankList"
pb.SC_COUNTRY_KINGWARSYNCSCOREINFO_RANKLIST_FIELD.full_name = ".SC_Country_KingWarSyncScoreInfo.RankList"
pb.SC_COUNTRY_KINGWARSYNCSCOREINFO_RANKLIST_FIELD.number = 2
pb.SC_COUNTRY_KINGWARSYNCSCOREINFO_RANKLIST_FIELD.index = 1
pb.SC_COUNTRY_KINGWARSYNCSCOREINFO_RANKLIST_FIELD.label = 3
pb.SC_COUNTRY_KINGWARSYNCSCOREINFO_RANKLIST_FIELD.has_default_value = false
pb.SC_COUNTRY_KINGWARSYNCSCOREINFO_RANKLIST_FIELD.default_value = {}
pb.SC_COUNTRY_KINGWARSYNCSCOREINFO_RANKLIST_FIELD.message_type = pb.SC_COUNTRY_KINGWARSYNCSCOREINFO_SCOREDATA
pb.SC_COUNTRY_KINGWARSYNCSCOREINFO_RANKLIST_FIELD.type = 11
pb.SC_COUNTRY_KINGWARSYNCSCOREINFO_RANKLIST_FIELD.cpp_type = 10

pb.SC_COUNTRY_KINGWARSYNCSCOREINFO.name = "SC_Country_KingWarSyncScoreInfo"
pb.SC_COUNTRY_KINGWARSYNCSCOREINFO.full_name = ".SC_Country_KingWarSyncScoreInfo"
pb.SC_COUNTRY_KINGWARSYNCSCOREINFO.nested_types = {pb.SC_COUNTRY_KINGWARSYNCSCOREINFO_SCOREDATA}
pb.SC_COUNTRY_KINGWARSYNCSCOREINFO.enum_types = {}
pb.SC_COUNTRY_KINGWARSYNCSCOREINFO.fields = {pb.SC_COUNTRY_KINGWARSYNCSCOREINFO_MSGTYPE_FIELD, pb.SC_COUNTRY_KINGWARSYNCSCOREINFO_RANKLIST_FIELD}
pb.SC_COUNTRY_KINGWARSYNCSCOREINFO.is_extendable = false
pb.SC_COUNTRY_KINGWARSYNCSCOREINFO.extensions = {}
pb.SC_COUNTRY_KINGWARRESULTINFO_SOCIETYSCOREDATA_RANK_FIELD.name = "Rank"
pb.SC_COUNTRY_KINGWARRESULTINFO_SOCIETYSCOREDATA_RANK_FIELD.full_name = ".SC_Country_KingWarResultInfo.SocietyScoreData.Rank"
pb.SC_COUNTRY_KINGWARRESULTINFO_SOCIETYSCOREDATA_RANK_FIELD.number = 1
pb.SC_COUNTRY_KINGWARRESULTINFO_SOCIETYSCOREDATA_RANK_FIELD.index = 0
pb.SC_COUNTRY_KINGWARRESULTINFO_SOCIETYSCOREDATA_RANK_FIELD.label = 1
pb.SC_COUNTRY_KINGWARRESULTINFO_SOCIETYSCOREDATA_RANK_FIELD.has_default_value = false
pb.SC_COUNTRY_KINGWARRESULTINFO_SOCIETYSCOREDATA_RANK_FIELD.default_value = 0
pb.SC_COUNTRY_KINGWARRESULTINFO_SOCIETYSCOREDATA_RANK_FIELD.type = 13
pb.SC_COUNTRY_KINGWARRESULTINFO_SOCIETYSCOREDATA_RANK_FIELD.cpp_type = 3

pb.SC_COUNTRY_KINGWARRESULTINFO_SOCIETYSCOREDATA_SOCIETYID_FIELD.name = "SocietyID"
pb.SC_COUNTRY_KINGWARRESULTINFO_SOCIETYSCOREDATA_SOCIETYID_FIELD.full_name = ".SC_Country_KingWarResultInfo.SocietyScoreData.SocietyID"
pb.SC_COUNTRY_KINGWARRESULTINFO_SOCIETYSCOREDATA_SOCIETYID_FIELD.number = 2
pb.SC_COUNTRY_KINGWARRESULTINFO_SOCIETYSCOREDATA_SOCIETYID_FIELD.index = 1
pb.SC_COUNTRY_KINGWARRESULTINFO_SOCIETYSCOREDATA_SOCIETYID_FIELD.label = 1
pb.SC_COUNTRY_KINGWARRESULTINFO_SOCIETYSCOREDATA_SOCIETYID_FIELD.has_default_value = false
pb.SC_COUNTRY_KINGWARRESULTINFO_SOCIETYSCOREDATA_SOCIETYID_FIELD.default_value = 0
pb.SC_COUNTRY_KINGWARRESULTINFO_SOCIETYSCOREDATA_SOCIETYID_FIELD.type = 13
pb.SC_COUNTRY_KINGWARRESULTINFO_SOCIETYSCOREDATA_SOCIETYID_FIELD.cpp_type = 3

pb.SC_COUNTRY_KINGWARRESULTINFO_SOCIETYSCOREDATA_SOCIETYNAME_FIELD.name = "SocietyName"
pb.SC_COUNTRY_KINGWARRESULTINFO_SOCIETYSCOREDATA_SOCIETYNAME_FIELD.full_name = ".SC_Country_KingWarResultInfo.SocietyScoreData.SocietyName"
pb.SC_COUNTRY_KINGWARRESULTINFO_SOCIETYSCOREDATA_SOCIETYNAME_FIELD.number = 3
pb.SC_COUNTRY_KINGWARRESULTINFO_SOCIETYSCOREDATA_SOCIETYNAME_FIELD.index = 2
pb.SC_COUNTRY_KINGWARRESULTINFO_SOCIETYSCOREDATA_SOCIETYNAME_FIELD.label = 1
pb.SC_COUNTRY_KINGWARRESULTINFO_SOCIETYSCOREDATA_SOCIETYNAME_FIELD.has_default_value = false
pb.SC_COUNTRY_KINGWARRESULTINFO_SOCIETYSCOREDATA_SOCIETYNAME_FIELD.default_value = ""
pb.SC_COUNTRY_KINGWARRESULTINFO_SOCIETYSCOREDATA_SOCIETYNAME_FIELD.type = 9
pb.SC_COUNTRY_KINGWARRESULTINFO_SOCIETYSCOREDATA_SOCIETYNAME_FIELD.cpp_type = 9

pb.SC_COUNTRY_KINGWARRESULTINFO_SOCIETYSCOREDATA_ACTORID_FIELD.name = "ActorID"
pb.SC_COUNTRY_KINGWARRESULTINFO_SOCIETYSCOREDATA_ACTORID_FIELD.full_name = ".SC_Country_KingWarResultInfo.SocietyScoreData.ActorID"
pb.SC_COUNTRY_KINGWARRESULTINFO_SOCIETYSCOREDATA_ACTORID_FIELD.number = 4
pb.SC_COUNTRY_KINGWARRESULTINFO_SOCIETYSCOREDATA_ACTORID_FIELD.index = 3
pb.SC_COUNTRY_KINGWARRESULTINFO_SOCIETYSCOREDATA_ACTORID_FIELD.label = 1
pb.SC_COUNTRY_KINGWARRESULTINFO_SOCIETYSCOREDATA_ACTORID_FIELD.has_default_value = false
pb.SC_COUNTRY_KINGWARRESULTINFO_SOCIETYSCOREDATA_ACTORID_FIELD.default_value = 0
pb.SC_COUNTRY_KINGWARRESULTINFO_SOCIETYSCOREDATA_ACTORID_FIELD.type = 13
pb.SC_COUNTRY_KINGWARRESULTINFO_SOCIETYSCOREDATA_ACTORID_FIELD.cpp_type = 3

pb.SC_COUNTRY_KINGWARRESULTINFO_SOCIETYSCOREDATA_ACTORNAME_FIELD.name = "ActorName"
pb.SC_COUNTRY_KINGWARRESULTINFO_SOCIETYSCOREDATA_ACTORNAME_FIELD.full_name = ".SC_Country_KingWarResultInfo.SocietyScoreData.ActorName"
pb.SC_COUNTRY_KINGWARRESULTINFO_SOCIETYSCOREDATA_ACTORNAME_FIELD.number = 5
pb.SC_COUNTRY_KINGWARRESULTINFO_SOCIETYSCOREDATA_ACTORNAME_FIELD.index = 4
pb.SC_COUNTRY_KINGWARRESULTINFO_SOCIETYSCOREDATA_ACTORNAME_FIELD.label = 1
pb.SC_COUNTRY_KINGWARRESULTINFO_SOCIETYSCOREDATA_ACTORNAME_FIELD.has_default_value = false
pb.SC_COUNTRY_KINGWARRESULTINFO_SOCIETYSCOREDATA_ACTORNAME_FIELD.default_value = ""
pb.SC_COUNTRY_KINGWARRESULTINFO_SOCIETYSCOREDATA_ACTORNAME_FIELD.type = 9
pb.SC_COUNTRY_KINGWARRESULTINFO_SOCIETYSCOREDATA_ACTORNAME_FIELD.cpp_type = 9

pb.SC_COUNTRY_KINGWARRESULTINFO_SOCIETYSCOREDATA_LEVEL_FIELD.name = "Level"
pb.SC_COUNTRY_KINGWARRESULTINFO_SOCIETYSCOREDATA_LEVEL_FIELD.full_name = ".SC_Country_KingWarResultInfo.SocietyScoreData.Level"
pb.SC_COUNTRY_KINGWARRESULTINFO_SOCIETYSCOREDATA_LEVEL_FIELD.number = 6
pb.SC_COUNTRY_KINGWARRESULTINFO_SOCIETYSCOREDATA_LEVEL_FIELD.index = 5
pb.SC_COUNTRY_KINGWARRESULTINFO_SOCIETYSCOREDATA_LEVEL_FIELD.label = 1
pb.SC_COUNTRY_KINGWARRESULTINFO_SOCIETYSCOREDATA_LEVEL_FIELD.has_default_value = false
pb.SC_COUNTRY_KINGWARRESULTINFO_SOCIETYSCOREDATA_LEVEL_FIELD.default_value = 0
pb.SC_COUNTRY_KINGWARRESULTINFO_SOCIETYSCOREDATA_LEVEL_FIELD.type = 13
pb.SC_COUNTRY_KINGWARRESULTINFO_SOCIETYSCOREDATA_LEVEL_FIELD.cpp_type = 3

pb.SC_COUNTRY_KINGWARRESULTINFO_SOCIETYSCOREDATA_SCORE_FIELD.name = "Score"
pb.SC_COUNTRY_KINGWARRESULTINFO_SOCIETYSCOREDATA_SCORE_FIELD.full_name = ".SC_Country_KingWarResultInfo.SocietyScoreData.Score"
pb.SC_COUNTRY_KINGWARRESULTINFO_SOCIETYSCOREDATA_SCORE_FIELD.number = 7
pb.SC_COUNTRY_KINGWARRESULTINFO_SOCIETYSCOREDATA_SCORE_FIELD.index = 6
pb.SC_COUNTRY_KINGWARRESULTINFO_SOCIETYSCOREDATA_SCORE_FIELD.label = 1
pb.SC_COUNTRY_KINGWARRESULTINFO_SOCIETYSCOREDATA_SCORE_FIELD.has_default_value = false
pb.SC_COUNTRY_KINGWARRESULTINFO_SOCIETYSCOREDATA_SCORE_FIELD.default_value = 0
pb.SC_COUNTRY_KINGWARRESULTINFO_SOCIETYSCOREDATA_SCORE_FIELD.type = 13
pb.SC_COUNTRY_KINGWARRESULTINFO_SOCIETYSCOREDATA_SCORE_FIELD.cpp_type = 3

pb.SC_COUNTRY_KINGWARRESULTINFO_SOCIETYSCOREDATA.name = "SocietyScoreData"
pb.SC_COUNTRY_KINGWARRESULTINFO_SOCIETYSCOREDATA.full_name = ".SC_Country_KingWarResultInfo.SocietyScoreData"
pb.SC_COUNTRY_KINGWARRESULTINFO_SOCIETYSCOREDATA.nested_types = {}
pb.SC_COUNTRY_KINGWARRESULTINFO_SOCIETYSCOREDATA.enum_types = {}
pb.SC_COUNTRY_KINGWARRESULTINFO_SOCIETYSCOREDATA.fields = {pb.SC_COUNTRY_KINGWARRESULTINFO_SOCIETYSCOREDATA_RANK_FIELD, pb.SC_COUNTRY_KINGWARRESULTINFO_SOCIETYSCOREDATA_SOCIETYID_FIELD, pb.SC_COUNTRY_KINGWARRESULTINFO_SOCIETYSCOREDATA_SOCIETYNAME_FIELD, pb.SC_COUNTRY_KINGWARRESULTINFO_SOCIETYSCOREDATA_ACTORID_FIELD, pb.SC_COUNTRY_KINGWARRESULTINFO_SOCIETYSCOREDATA_ACTORNAME_FIELD, pb.SC_COUNTRY_KINGWARRESULTINFO_SOCIETYSCOREDATA_LEVEL_FIELD, pb.SC_COUNTRY_KINGWARRESULTINFO_SOCIETYSCOREDATA_SCORE_FIELD}
pb.SC_COUNTRY_KINGWARRESULTINFO_SOCIETYSCOREDATA.is_extendable = false
pb.SC_COUNTRY_KINGWARRESULTINFO_SOCIETYSCOREDATA.extensions = {}
pb.SC_COUNTRY_KINGWARRESULTINFO_SOCIETYSCOREDATA.containing_type = pb.SC_COUNTRY_KINGWARRESULTINFO
pb.SC_COUNTRY_KINGWARRESULTINFO_SOCIETYSCORELIST_FIELD.name = "SocietyScoreList"
pb.SC_COUNTRY_KINGWARRESULTINFO_SOCIETYSCORELIST_FIELD.full_name = ".SC_Country_KingWarResultInfo.SocietyScoreList"
pb.SC_COUNTRY_KINGWARRESULTINFO_SOCIETYSCORELIST_FIELD.number = 1
pb.SC_COUNTRY_KINGWARRESULTINFO_SOCIETYSCORELIST_FIELD.index = 0
pb.SC_COUNTRY_KINGWARRESULTINFO_SOCIETYSCORELIST_FIELD.label = 3
pb.SC_COUNTRY_KINGWARRESULTINFO_SOCIETYSCORELIST_FIELD.has_default_value = false
pb.SC_COUNTRY_KINGWARRESULTINFO_SOCIETYSCORELIST_FIELD.default_value = {}
pb.SC_COUNTRY_KINGWARRESULTINFO_SOCIETYSCORELIST_FIELD.message_type = pb.SC_COUNTRY_KINGWARRESULTINFO_SOCIETYSCOREDATA
pb.SC_COUNTRY_KINGWARRESULTINFO_SOCIETYSCORELIST_FIELD.type = 11
pb.SC_COUNTRY_KINGWARRESULTINFO_SOCIETYSCORELIST_FIELD.cpp_type = 10

pb.SC_COUNTRY_KINGWARRESULTINFO_SELFSOCIETYSCOREINFO_FIELD.name = "SelfSocietyScoreInfo"
pb.SC_COUNTRY_KINGWARRESULTINFO_SELFSOCIETYSCOREINFO_FIELD.full_name = ".SC_Country_KingWarResultInfo.SelfSocietyScoreInfo"
pb.SC_COUNTRY_KINGWARRESULTINFO_SELFSOCIETYSCOREINFO_FIELD.number = 2
pb.SC_COUNTRY_KINGWARRESULTINFO_SELFSOCIETYSCOREINFO_FIELD.index = 1
pb.SC_COUNTRY_KINGWARRESULTINFO_SELFSOCIETYSCOREINFO_FIELD.label = 1
pb.SC_COUNTRY_KINGWARRESULTINFO_SELFSOCIETYSCOREINFO_FIELD.has_default_value = false
pb.SC_COUNTRY_KINGWARRESULTINFO_SELFSOCIETYSCOREINFO_FIELD.default_value = nil
pb.SC_COUNTRY_KINGWARRESULTINFO_SELFSOCIETYSCOREINFO_FIELD.message_type = pb.SC_COUNTRY_KINGWARRESULTINFO_SOCIETYSCOREDATA
pb.SC_COUNTRY_KINGWARRESULTINFO_SELFSOCIETYSCOREINFO_FIELD.type = 11
pb.SC_COUNTRY_KINGWARRESULTINFO_SELFSOCIETYSCOREINFO_FIELD.cpp_type = 10

pb.SC_COUNTRY_KINGWARRESULTINFO_PRIZE1NUM_FIELD.name = "Prize1Num"
pb.SC_COUNTRY_KINGWARRESULTINFO_PRIZE1NUM_FIELD.full_name = ".SC_Country_KingWarResultInfo.Prize1Num"
pb.SC_COUNTRY_KINGWARRESULTINFO_PRIZE1NUM_FIELD.number = 3
pb.SC_COUNTRY_KINGWARRESULTINFO_PRIZE1NUM_FIELD.index = 2
pb.SC_COUNTRY_KINGWARRESULTINFO_PRIZE1NUM_FIELD.label = 1
pb.SC_COUNTRY_KINGWARRESULTINFO_PRIZE1NUM_FIELD.has_default_value = false
pb.SC_COUNTRY_KINGWARRESULTINFO_PRIZE1NUM_FIELD.default_value = 0
pb.SC_COUNTRY_KINGWARRESULTINFO_PRIZE1NUM_FIELD.type = 13
pb.SC_COUNTRY_KINGWARRESULTINFO_PRIZE1NUM_FIELD.cpp_type = 3

pb.SC_COUNTRY_KINGWARRESULTINFO_PRIZE2NUM_FIELD.name = "Prize2Num"
pb.SC_COUNTRY_KINGWARRESULTINFO_PRIZE2NUM_FIELD.full_name = ".SC_Country_KingWarResultInfo.Prize2Num"
pb.SC_COUNTRY_KINGWARRESULTINFO_PRIZE2NUM_FIELD.number = 4
pb.SC_COUNTRY_KINGWARRESULTINFO_PRIZE2NUM_FIELD.index = 3
pb.SC_COUNTRY_KINGWARRESULTINFO_PRIZE2NUM_FIELD.label = 1
pb.SC_COUNTRY_KINGWARRESULTINFO_PRIZE2NUM_FIELD.has_default_value = false
pb.SC_COUNTRY_KINGWARRESULTINFO_PRIZE2NUM_FIELD.default_value = 0
pb.SC_COUNTRY_KINGWARRESULTINFO_PRIZE2NUM_FIELD.type = 13
pb.SC_COUNTRY_KINGWARRESULTINFO_PRIZE2NUM_FIELD.cpp_type = 3

pb.SC_COUNTRY_KINGWARRESULTINFO.name = "SC_Country_KingWarResultInfo"
pb.SC_COUNTRY_KINGWARRESULTINFO.full_name = ".SC_Country_KingWarResultInfo"
pb.SC_COUNTRY_KINGWARRESULTINFO.nested_types = {pb.SC_COUNTRY_KINGWARRESULTINFO_SOCIETYSCOREDATA}
pb.SC_COUNTRY_KINGWARRESULTINFO.enum_types = {}
pb.SC_COUNTRY_KINGWARRESULTINFO.fields = {pb.SC_COUNTRY_KINGWARRESULTINFO_SOCIETYSCORELIST_FIELD, pb.SC_COUNTRY_KINGWARRESULTINFO_SELFSOCIETYSCOREINFO_FIELD, pb.SC_COUNTRY_KINGWARRESULTINFO_PRIZE1NUM_FIELD, pb.SC_COUNTRY_KINGWARRESULTINFO_PRIZE2NUM_FIELD}
pb.SC_COUNTRY_KINGWARRESULTINFO.is_extendable = false
pb.SC_COUNTRY_KINGWARRESULTINFO.extensions = {}
pb.SC_COUNTRY_BATTLEFIELD_MAININFO_OWNCOUNTRY_FIELD.name = "OwnCountry"
pb.SC_COUNTRY_BATTLEFIELD_MAININFO_OWNCOUNTRY_FIELD.full_name = ".SC_Country_BattleField_MainInfo.OwnCountry"
pb.SC_COUNTRY_BATTLEFIELD_MAININFO_OWNCOUNTRY_FIELD.number = 1
pb.SC_COUNTRY_BATTLEFIELD_MAININFO_OWNCOUNTRY_FIELD.index = 0
pb.SC_COUNTRY_BATTLEFIELD_MAININFO_OWNCOUNTRY_FIELD.label = 1
pb.SC_COUNTRY_BATTLEFIELD_MAININFO_OWNCOUNTRY_FIELD.has_default_value = false
pb.SC_COUNTRY_BATTLEFIELD_MAININFO_OWNCOUNTRY_FIELD.default_value = 0
pb.SC_COUNTRY_BATTLEFIELD_MAININFO_OWNCOUNTRY_FIELD.type = 5
pb.SC_COUNTRY_BATTLEFIELD_MAININFO_OWNCOUNTRY_FIELD.cpp_type = 1

pb.SC_COUNTRY_BATTLEFIELD_MAININFO.name = "SC_Country_BattleField_MainInfo"
pb.SC_COUNTRY_BATTLEFIELD_MAININFO.full_name = ".SC_Country_BattleField_MainInfo"
pb.SC_COUNTRY_BATTLEFIELD_MAININFO.nested_types = {}
pb.SC_COUNTRY_BATTLEFIELD_MAININFO.enum_types = {}
pb.SC_COUNTRY_BATTLEFIELD_MAININFO.fields = {pb.SC_COUNTRY_BATTLEFIELD_MAININFO_OWNCOUNTRY_FIELD}
pb.SC_COUNTRY_BATTLEFIELD_MAININFO.is_extendable = false
pb.SC_COUNTRY_BATTLEFIELD_MAININFO.extensions = {}
pb.SC_COUNTRY_BATTLEFIELD_MAPINFO_COUNTRY1RES_FIELD.name = "Country1Res"
pb.SC_COUNTRY_BATTLEFIELD_MAPINFO_COUNTRY1RES_FIELD.full_name = ".SC_Country_BattleField_MapInfo.Country1Res"
pb.SC_COUNTRY_BATTLEFIELD_MAPINFO_COUNTRY1RES_FIELD.number = 1
pb.SC_COUNTRY_BATTLEFIELD_MAPINFO_COUNTRY1RES_FIELD.index = 0
pb.SC_COUNTRY_BATTLEFIELD_MAPINFO_COUNTRY1RES_FIELD.label = 1
pb.SC_COUNTRY_BATTLEFIELD_MAPINFO_COUNTRY1RES_FIELD.has_default_value = false
pb.SC_COUNTRY_BATTLEFIELD_MAPINFO_COUNTRY1RES_FIELD.default_value = 0
pb.SC_COUNTRY_BATTLEFIELD_MAPINFO_COUNTRY1RES_FIELD.type = 13
pb.SC_COUNTRY_BATTLEFIELD_MAPINFO_COUNTRY1RES_FIELD.cpp_type = 3

pb.SC_COUNTRY_BATTLEFIELD_MAPINFO_COUNTRY2RES_FIELD.name = "Country2Res"
pb.SC_COUNTRY_BATTLEFIELD_MAPINFO_COUNTRY2RES_FIELD.full_name = ".SC_Country_BattleField_MapInfo.Country2Res"
pb.SC_COUNTRY_BATTLEFIELD_MAPINFO_COUNTRY2RES_FIELD.number = 2
pb.SC_COUNTRY_BATTLEFIELD_MAPINFO_COUNTRY2RES_FIELD.index = 1
pb.SC_COUNTRY_BATTLEFIELD_MAPINFO_COUNTRY2RES_FIELD.label = 1
pb.SC_COUNTRY_BATTLEFIELD_MAPINFO_COUNTRY2RES_FIELD.has_default_value = false
pb.SC_COUNTRY_BATTLEFIELD_MAPINFO_COUNTRY2RES_FIELD.default_value = 0
pb.SC_COUNTRY_BATTLEFIELD_MAPINFO_COUNTRY2RES_FIELD.type = 13
pb.SC_COUNTRY_BATTLEFIELD_MAPINFO_COUNTRY2RES_FIELD.cpp_type = 3

pb.SC_COUNTRY_BATTLEFIELD_MAPINFO_COUNTRY1BOSSTIME_FIELD.name = "Country1BossTime"
pb.SC_COUNTRY_BATTLEFIELD_MAPINFO_COUNTRY1BOSSTIME_FIELD.full_name = ".SC_Country_BattleField_MapInfo.Country1BossTime"
pb.SC_COUNTRY_BATTLEFIELD_MAPINFO_COUNTRY1BOSSTIME_FIELD.number = 3
pb.SC_COUNTRY_BATTLEFIELD_MAPINFO_COUNTRY1BOSSTIME_FIELD.index = 2
pb.SC_COUNTRY_BATTLEFIELD_MAPINFO_COUNTRY1BOSSTIME_FIELD.label = 1
pb.SC_COUNTRY_BATTLEFIELD_MAPINFO_COUNTRY1BOSSTIME_FIELD.has_default_value = false
pb.SC_COUNTRY_BATTLEFIELD_MAPINFO_COUNTRY1BOSSTIME_FIELD.default_value = 0
pb.SC_COUNTRY_BATTLEFIELD_MAPINFO_COUNTRY1BOSSTIME_FIELD.type = 13
pb.SC_COUNTRY_BATTLEFIELD_MAPINFO_COUNTRY1BOSSTIME_FIELD.cpp_type = 3

pb.SC_COUNTRY_BATTLEFIELD_MAPINFO_COUNTRY2BOSSTIME_FIELD.name = "Country2BossTime"
pb.SC_COUNTRY_BATTLEFIELD_MAPINFO_COUNTRY2BOSSTIME_FIELD.full_name = ".SC_Country_BattleField_MapInfo.Country2BossTime"
pb.SC_COUNTRY_BATTLEFIELD_MAPINFO_COUNTRY2BOSSTIME_FIELD.number = 4
pb.SC_COUNTRY_BATTLEFIELD_MAPINFO_COUNTRY2BOSSTIME_FIELD.index = 3
pb.SC_COUNTRY_BATTLEFIELD_MAPINFO_COUNTRY2BOSSTIME_FIELD.label = 1
pb.SC_COUNTRY_BATTLEFIELD_MAPINFO_COUNTRY2BOSSTIME_FIELD.has_default_value = false
pb.SC_COUNTRY_BATTLEFIELD_MAPINFO_COUNTRY2BOSSTIME_FIELD.default_value = 0
pb.SC_COUNTRY_BATTLEFIELD_MAPINFO_COUNTRY2BOSSTIME_FIELD.type = 13
pb.SC_COUNTRY_BATTLEFIELD_MAPINFO_COUNTRY2BOSSTIME_FIELD.cpp_type = 3

pb.SC_COUNTRY_BATTLEFIELD_MAPINFO_COUNTRYBOSSOWNER_FIELD.name = "CountryBossOwner"
pb.SC_COUNTRY_BATTLEFIELD_MAPINFO_COUNTRYBOSSOWNER_FIELD.full_name = ".SC_Country_BattleField_MapInfo.CountryBossOwner"
pb.SC_COUNTRY_BATTLEFIELD_MAPINFO_COUNTRYBOSSOWNER_FIELD.number = 5
pb.SC_COUNTRY_BATTLEFIELD_MAPINFO_COUNTRYBOSSOWNER_FIELD.index = 4
pb.SC_COUNTRY_BATTLEFIELD_MAPINFO_COUNTRYBOSSOWNER_FIELD.label = 1
pb.SC_COUNTRY_BATTLEFIELD_MAPINFO_COUNTRYBOSSOWNER_FIELD.has_default_value = false
pb.SC_COUNTRY_BATTLEFIELD_MAPINFO_COUNTRYBOSSOWNER_FIELD.default_value = 0
pb.SC_COUNTRY_BATTLEFIELD_MAPINFO_COUNTRYBOSSOWNER_FIELD.type = 13
pb.SC_COUNTRY_BATTLEFIELD_MAPINFO_COUNTRYBOSSOWNER_FIELD.cpp_type = 3

pb.SC_COUNTRY_BATTLEFIELD_MAPINFO.name = "SC_Country_BattleField_MapInfo"
pb.SC_COUNTRY_BATTLEFIELD_MAPINFO.full_name = ".SC_Country_BattleField_MapInfo"
pb.SC_COUNTRY_BATTLEFIELD_MAPINFO.nested_types = {}
pb.SC_COUNTRY_BATTLEFIELD_MAPINFO.enum_types = {}
pb.SC_COUNTRY_BATTLEFIELD_MAPINFO.fields = {pb.SC_COUNTRY_BATTLEFIELD_MAPINFO_COUNTRY1RES_FIELD, pb.SC_COUNTRY_BATTLEFIELD_MAPINFO_COUNTRY2RES_FIELD, pb.SC_COUNTRY_BATTLEFIELD_MAPINFO_COUNTRY1BOSSTIME_FIELD, pb.SC_COUNTRY_BATTLEFIELD_MAPINFO_COUNTRY2BOSSTIME_FIELD, pb.SC_COUNTRY_BATTLEFIELD_MAPINFO_COUNTRYBOSSOWNER_FIELD}
pb.SC_COUNTRY_BATTLEFIELD_MAPINFO.is_extendable = false
pb.SC_COUNTRY_BATTLEFIELD_MAPINFO.extensions = {}
pb.SC_COUNTRY_GETPKRECORDINFO_PKRECORDDATA_RECORDTYPE_FIELD.name = "RecordType"
pb.SC_COUNTRY_GETPKRECORDINFO_PKRECORDDATA_RECORDTYPE_FIELD.full_name = ".SC_Country_GetPKRecordInfo.PKRecordData.RecordType"
pb.SC_COUNTRY_GETPKRECORDINFO_PKRECORDDATA_RECORDTYPE_FIELD.number = 1
pb.SC_COUNTRY_GETPKRECORDINFO_PKRECORDDATA_RECORDTYPE_FIELD.index = 0
pb.SC_COUNTRY_GETPKRECORDINFO_PKRECORDDATA_RECORDTYPE_FIELD.label = 1
pb.SC_COUNTRY_GETPKRECORDINFO_PKRECORDDATA_RECORDTYPE_FIELD.has_default_value = false
pb.SC_COUNTRY_GETPKRECORDINFO_PKRECORDDATA_RECORDTYPE_FIELD.default_value = 0
pb.SC_COUNTRY_GETPKRECORDINFO_PKRECORDDATA_RECORDTYPE_FIELD.type = 5
pb.SC_COUNTRY_GETPKRECORDINFO_PKRECORDDATA_RECORDTYPE_FIELD.cpp_type = 1

pb.SC_COUNTRY_GETPKRECORDINFO_PKRECORDDATA_ACTORID_FIELD.name = "ActorID"
pb.SC_COUNTRY_GETPKRECORDINFO_PKRECORDDATA_ACTORID_FIELD.full_name = ".SC_Country_GetPKRecordInfo.PKRecordData.ActorID"
pb.SC_COUNTRY_GETPKRECORDINFO_PKRECORDDATA_ACTORID_FIELD.number = 2
pb.SC_COUNTRY_GETPKRECORDINFO_PKRECORDDATA_ACTORID_FIELD.index = 1
pb.SC_COUNTRY_GETPKRECORDINFO_PKRECORDDATA_ACTORID_FIELD.label = 1
pb.SC_COUNTRY_GETPKRECORDINFO_PKRECORDDATA_ACTORID_FIELD.has_default_value = false
pb.SC_COUNTRY_GETPKRECORDINFO_PKRECORDDATA_ACTORID_FIELD.default_value = 0
pb.SC_COUNTRY_GETPKRECORDINFO_PKRECORDDATA_ACTORID_FIELD.type = 5
pb.SC_COUNTRY_GETPKRECORDINFO_PKRECORDDATA_ACTORID_FIELD.cpp_type = 1

pb.SC_COUNTRY_GETPKRECORDINFO_PKRECORDDATA_ACTORNAME_FIELD.name = "ActorName"
pb.SC_COUNTRY_GETPKRECORDINFO_PKRECORDDATA_ACTORNAME_FIELD.full_name = ".SC_Country_GetPKRecordInfo.PKRecordData.ActorName"
pb.SC_COUNTRY_GETPKRECORDINFO_PKRECORDDATA_ACTORNAME_FIELD.number = 3
pb.SC_COUNTRY_GETPKRECORDINFO_PKRECORDDATA_ACTORNAME_FIELD.index = 2
pb.SC_COUNTRY_GETPKRECORDINFO_PKRECORDDATA_ACTORNAME_FIELD.label = 1
pb.SC_COUNTRY_GETPKRECORDINFO_PKRECORDDATA_ACTORNAME_FIELD.has_default_value = false
pb.SC_COUNTRY_GETPKRECORDINFO_PKRECORDDATA_ACTORNAME_FIELD.default_value = ""
pb.SC_COUNTRY_GETPKRECORDINFO_PKRECORDDATA_ACTORNAME_FIELD.type = 9
pb.SC_COUNTRY_GETPKRECORDINFO_PKRECORDDATA_ACTORNAME_FIELD.cpp_type = 9

pb.SC_COUNTRY_GETPKRECORDINFO_PKRECORDDATA_LEVEL_FIELD.name = "Level"
pb.SC_COUNTRY_GETPKRECORDINFO_PKRECORDDATA_LEVEL_FIELD.full_name = ".SC_Country_GetPKRecordInfo.PKRecordData.Level"
pb.SC_COUNTRY_GETPKRECORDINFO_PKRECORDDATA_LEVEL_FIELD.number = 4
pb.SC_COUNTRY_GETPKRECORDINFO_PKRECORDDATA_LEVEL_FIELD.index = 3
pb.SC_COUNTRY_GETPKRECORDINFO_PKRECORDDATA_LEVEL_FIELD.label = 1
pb.SC_COUNTRY_GETPKRECORDINFO_PKRECORDDATA_LEVEL_FIELD.has_default_value = false
pb.SC_COUNTRY_GETPKRECORDINFO_PKRECORDDATA_LEVEL_FIELD.default_value = 0
pb.SC_COUNTRY_GETPKRECORDINFO_PKRECORDDATA_LEVEL_FIELD.type = 5
pb.SC_COUNTRY_GETPKRECORDINFO_PKRECORDDATA_LEVEL_FIELD.cpp_type = 1

pb.SC_COUNTRY_GETPKRECORDINFO_PKRECORDDATA_COUNTRY_FIELD.name = "Country"
pb.SC_COUNTRY_GETPKRECORDINFO_PKRECORDDATA_COUNTRY_FIELD.full_name = ".SC_Country_GetPKRecordInfo.PKRecordData.Country"
pb.SC_COUNTRY_GETPKRECORDINFO_PKRECORDDATA_COUNTRY_FIELD.number = 5
pb.SC_COUNTRY_GETPKRECORDINFO_PKRECORDDATA_COUNTRY_FIELD.index = 4
pb.SC_COUNTRY_GETPKRECORDINFO_PKRECORDDATA_COUNTRY_FIELD.label = 1
pb.SC_COUNTRY_GETPKRECORDINFO_PKRECORDDATA_COUNTRY_FIELD.has_default_value = false
pb.SC_COUNTRY_GETPKRECORDINFO_PKRECORDDATA_COUNTRY_FIELD.default_value = 0
pb.SC_COUNTRY_GETPKRECORDINFO_PKRECORDDATA_COUNTRY_FIELD.type = 5
pb.SC_COUNTRY_GETPKRECORDINFO_PKRECORDDATA_COUNTRY_FIELD.cpp_type = 1

pb.SC_COUNTRY_GETPKRECORDINFO_PKRECORDDATA_VOCATION_FIELD.name = "Vocation"
pb.SC_COUNTRY_GETPKRECORDINFO_PKRECORDDATA_VOCATION_FIELD.full_name = ".SC_Country_GetPKRecordInfo.PKRecordData.Vocation"
pb.SC_COUNTRY_GETPKRECORDINFO_PKRECORDDATA_VOCATION_FIELD.number = 6
pb.SC_COUNTRY_GETPKRECORDINFO_PKRECORDDATA_VOCATION_FIELD.index = 5
pb.SC_COUNTRY_GETPKRECORDINFO_PKRECORDDATA_VOCATION_FIELD.label = 1
pb.SC_COUNTRY_GETPKRECORDINFO_PKRECORDDATA_VOCATION_FIELD.has_default_value = false
pb.SC_COUNTRY_GETPKRECORDINFO_PKRECORDDATA_VOCATION_FIELD.default_value = 0
pb.SC_COUNTRY_GETPKRECORDINFO_PKRECORDDATA_VOCATION_FIELD.type = 5
pb.SC_COUNTRY_GETPKRECORDINFO_PKRECORDDATA_VOCATION_FIELD.cpp_type = 1

pb.SC_COUNTRY_GETPKRECORDINFO_PKRECORDDATA_MAPID_FIELD.name = "MapID"
pb.SC_COUNTRY_GETPKRECORDINFO_PKRECORDDATA_MAPID_FIELD.full_name = ".SC_Country_GetPKRecordInfo.PKRecordData.MapID"
pb.SC_COUNTRY_GETPKRECORDINFO_PKRECORDDATA_MAPID_FIELD.number = 7
pb.SC_COUNTRY_GETPKRECORDINFO_PKRECORDDATA_MAPID_FIELD.index = 6
pb.SC_COUNTRY_GETPKRECORDINFO_PKRECORDDATA_MAPID_FIELD.label = 1
pb.SC_COUNTRY_GETPKRECORDINFO_PKRECORDDATA_MAPID_FIELD.has_default_value = false
pb.SC_COUNTRY_GETPKRECORDINFO_PKRECORDDATA_MAPID_FIELD.default_value = 0
pb.SC_COUNTRY_GETPKRECORDINFO_PKRECORDDATA_MAPID_FIELD.type = 5
pb.SC_COUNTRY_GETPKRECORDINFO_PKRECORDDATA_MAPID_FIELD.cpp_type = 1

pb.SC_COUNTRY_GETPKRECORDINFO_PKRECORDDATA_RECORDTIME_FIELD.name = "RecordTime"
pb.SC_COUNTRY_GETPKRECORDINFO_PKRECORDDATA_RECORDTIME_FIELD.full_name = ".SC_Country_GetPKRecordInfo.PKRecordData.RecordTime"
pb.SC_COUNTRY_GETPKRECORDINFO_PKRECORDDATA_RECORDTIME_FIELD.number = 8
pb.SC_COUNTRY_GETPKRECORDINFO_PKRECORDDATA_RECORDTIME_FIELD.index = 7
pb.SC_COUNTRY_GETPKRECORDINFO_PKRECORDDATA_RECORDTIME_FIELD.label = 1
pb.SC_COUNTRY_GETPKRECORDINFO_PKRECORDDATA_RECORDTIME_FIELD.has_default_value = false
pb.SC_COUNTRY_GETPKRECORDINFO_PKRECORDDATA_RECORDTIME_FIELD.default_value = 0
pb.SC_COUNTRY_GETPKRECORDINFO_PKRECORDDATA_RECORDTIME_FIELD.type = 5
pb.SC_COUNTRY_GETPKRECORDINFO_PKRECORDDATA_RECORDTIME_FIELD.cpp_type = 1

pb.SC_COUNTRY_GETPKRECORDINFO_PKRECORDDATA.name = "PKRecordData"
pb.SC_COUNTRY_GETPKRECORDINFO_PKRECORDDATA.full_name = ".SC_Country_GetPKRecordInfo.PKRecordData"
pb.SC_COUNTRY_GETPKRECORDINFO_PKRECORDDATA.nested_types = {}
pb.SC_COUNTRY_GETPKRECORDINFO_PKRECORDDATA.enum_types = {}
pb.SC_COUNTRY_GETPKRECORDINFO_PKRECORDDATA.fields = {pb.SC_COUNTRY_GETPKRECORDINFO_PKRECORDDATA_RECORDTYPE_FIELD, pb.SC_COUNTRY_GETPKRECORDINFO_PKRECORDDATA_ACTORID_FIELD, pb.SC_COUNTRY_GETPKRECORDINFO_PKRECORDDATA_ACTORNAME_FIELD, pb.SC_COUNTRY_GETPKRECORDINFO_PKRECORDDATA_LEVEL_FIELD, pb.SC_COUNTRY_GETPKRECORDINFO_PKRECORDDATA_COUNTRY_FIELD, pb.SC_COUNTRY_GETPKRECORDINFO_PKRECORDDATA_VOCATION_FIELD, pb.SC_COUNTRY_GETPKRECORDINFO_PKRECORDDATA_MAPID_FIELD, pb.SC_COUNTRY_GETPKRECORDINFO_PKRECORDDATA_RECORDTIME_FIELD}
pb.SC_COUNTRY_GETPKRECORDINFO_PKRECORDDATA.is_extendable = false
pb.SC_COUNTRY_GETPKRECORDINFO_PKRECORDDATA.extensions = {}
pb.SC_COUNTRY_GETPKRECORDINFO_PKRECORDDATA.containing_type = pb.SC_COUNTRY_GETPKRECORDINFO
pb.SC_COUNTRY_GETPKRECORDINFO_RECORDLIST_FIELD.name = "RecordList"
pb.SC_COUNTRY_GETPKRECORDINFO_RECORDLIST_FIELD.full_name = ".SC_Country_GetPKRecordInfo.RecordList"
pb.SC_COUNTRY_GETPKRECORDINFO_RECORDLIST_FIELD.number = 1
pb.SC_COUNTRY_GETPKRECORDINFO_RECORDLIST_FIELD.index = 0
pb.SC_COUNTRY_GETPKRECORDINFO_RECORDLIST_FIELD.label = 3
pb.SC_COUNTRY_GETPKRECORDINFO_RECORDLIST_FIELD.has_default_value = false
pb.SC_COUNTRY_GETPKRECORDINFO_RECORDLIST_FIELD.default_value = {}
pb.SC_COUNTRY_GETPKRECORDINFO_RECORDLIST_FIELD.message_type = pb.SC_COUNTRY_GETPKRECORDINFO_PKRECORDDATA
pb.SC_COUNTRY_GETPKRECORDINFO_RECORDLIST_FIELD.type = 11
pb.SC_COUNTRY_GETPKRECORDINFO_RECORDLIST_FIELD.cpp_type = 10

pb.SC_COUNTRY_GETPKRECORDINFO.name = "SC_Country_GetPKRecordInfo"
pb.SC_COUNTRY_GETPKRECORDINFO.full_name = ".SC_Country_GetPKRecordInfo"
pb.SC_COUNTRY_GETPKRECORDINFO.nested_types = {pb.SC_COUNTRY_GETPKRECORDINFO_PKRECORDDATA}
pb.SC_COUNTRY_GETPKRECORDINFO.enum_types = {}
pb.SC_COUNTRY_GETPKRECORDINFO.fields = {pb.SC_COUNTRY_GETPKRECORDINFO_RECORDLIST_FIELD}
pb.SC_COUNTRY_GETPKRECORDINFO.is_extendable = false
pb.SC_COUNTRY_GETPKRECORDINFO.extensions = {}
pb.SC_COUNTRY_GETPRINCESSINFO_CURHP_FIELD.name = "CurHP"
pb.SC_COUNTRY_GETPRINCESSINFO_CURHP_FIELD.full_name = ".SC_Country_GetPrincessInfo.CurHP"
pb.SC_COUNTRY_GETPRINCESSINFO_CURHP_FIELD.number = 1
pb.SC_COUNTRY_GETPRINCESSINFO_CURHP_FIELD.index = 0
pb.SC_COUNTRY_GETPRINCESSINFO_CURHP_FIELD.label = 1
pb.SC_COUNTRY_GETPRINCESSINFO_CURHP_FIELD.has_default_value = false
pb.SC_COUNTRY_GETPRINCESSINFO_CURHP_FIELD.default_value = 0
pb.SC_COUNTRY_GETPRINCESSINFO_CURHP_FIELD.type = 5
pb.SC_COUNTRY_GETPRINCESSINFO_CURHP_FIELD.cpp_type = 1

pb.SC_COUNTRY_GETPRINCESSINFO_MAXHP_FIELD.name = "MaxHP"
pb.SC_COUNTRY_GETPRINCESSINFO_MAXHP_FIELD.full_name = ".SC_Country_GetPrincessInfo.MaxHP"
pb.SC_COUNTRY_GETPRINCESSINFO_MAXHP_FIELD.number = 2
pb.SC_COUNTRY_GETPRINCESSINFO_MAXHP_FIELD.index = 1
pb.SC_COUNTRY_GETPRINCESSINFO_MAXHP_FIELD.label = 1
pb.SC_COUNTRY_GETPRINCESSINFO_MAXHP_FIELD.has_default_value = false
pb.SC_COUNTRY_GETPRINCESSINFO_MAXHP_FIELD.default_value = 0
pb.SC_COUNTRY_GETPRINCESSINFO_MAXHP_FIELD.type = 5
pb.SC_COUNTRY_GETPRINCESSINFO_MAXHP_FIELD.cpp_type = 1

pb.SC_COUNTRY_GETPRINCESSINFO_OWNCOUNTRY_FIELD.name = "OwnCountry"
pb.SC_COUNTRY_GETPRINCESSINFO_OWNCOUNTRY_FIELD.full_name = ".SC_Country_GetPrincessInfo.OwnCountry"
pb.SC_COUNTRY_GETPRINCESSINFO_OWNCOUNTRY_FIELD.number = 3
pb.SC_COUNTRY_GETPRINCESSINFO_OWNCOUNTRY_FIELD.index = 2
pb.SC_COUNTRY_GETPRINCESSINFO_OWNCOUNTRY_FIELD.label = 1
pb.SC_COUNTRY_GETPRINCESSINFO_OWNCOUNTRY_FIELD.has_default_value = false
pb.SC_COUNTRY_GETPRINCESSINFO_OWNCOUNTRY_FIELD.default_value = 0
pb.SC_COUNTRY_GETPRINCESSINFO_OWNCOUNTRY_FIELD.type = 5
pb.SC_COUNTRY_GETPRINCESSINFO_OWNCOUNTRY_FIELD.cpp_type = 1

pb.SC_COUNTRY_GETPRINCESSINFO_EXISTENCETIME_FIELD.name = "ExistenceTime"
pb.SC_COUNTRY_GETPRINCESSINFO_EXISTENCETIME_FIELD.full_name = ".SC_Country_GetPrincessInfo.ExistenceTime"
pb.SC_COUNTRY_GETPRINCESSINFO_EXISTENCETIME_FIELD.number = 4
pb.SC_COUNTRY_GETPRINCESSINFO_EXISTENCETIME_FIELD.index = 3
pb.SC_COUNTRY_GETPRINCESSINFO_EXISTENCETIME_FIELD.label = 1
pb.SC_COUNTRY_GETPRINCESSINFO_EXISTENCETIME_FIELD.has_default_value = false
pb.SC_COUNTRY_GETPRINCESSINFO_EXISTENCETIME_FIELD.default_value = 0
pb.SC_COUNTRY_GETPRINCESSINFO_EXISTENCETIME_FIELD.type = 5
pb.SC_COUNTRY_GETPRINCESSINFO_EXISTENCETIME_FIELD.cpp_type = 1

pb.SC_COUNTRY_GETPRINCESSINFO_PRIZERATE_FIELD.name = "PrizeRate"
pb.SC_COUNTRY_GETPRINCESSINFO_PRIZERATE_FIELD.full_name = ".SC_Country_GetPrincessInfo.PrizeRate"
pb.SC_COUNTRY_GETPRINCESSINFO_PRIZERATE_FIELD.number = 5
pb.SC_COUNTRY_GETPRINCESSINFO_PRIZERATE_FIELD.index = 4
pb.SC_COUNTRY_GETPRINCESSINFO_PRIZERATE_FIELD.label = 1
pb.SC_COUNTRY_GETPRINCESSINFO_PRIZERATE_FIELD.has_default_value = false
pb.SC_COUNTRY_GETPRINCESSINFO_PRIZERATE_FIELD.default_value = 0
pb.SC_COUNTRY_GETPRINCESSINFO_PRIZERATE_FIELD.type = 5
pb.SC_COUNTRY_GETPRINCESSINFO_PRIZERATE_FIELD.cpp_type = 1

pb.SC_COUNTRY_GETPRINCESSINFO_COUNTRY_FIELD.name = "Country"
pb.SC_COUNTRY_GETPRINCESSINFO_COUNTRY_FIELD.full_name = ".SC_Country_GetPrincessInfo.Country"
pb.SC_COUNTRY_GETPRINCESSINFO_COUNTRY_FIELD.number = 6
pb.SC_COUNTRY_GETPRINCESSINFO_COUNTRY_FIELD.index = 5
pb.SC_COUNTRY_GETPRINCESSINFO_COUNTRY_FIELD.label = 1
pb.SC_COUNTRY_GETPRINCESSINFO_COUNTRY_FIELD.has_default_value = false
pb.SC_COUNTRY_GETPRINCESSINFO_COUNTRY_FIELD.default_value = 0
pb.SC_COUNTRY_GETPRINCESSINFO_COUNTRY_FIELD.type = 5
pb.SC_COUNTRY_GETPRINCESSINFO_COUNTRY_FIELD.cpp_type = 1

pb.SC_COUNTRY_GETPRINCESSINFO_KILLERNAME_FIELD.name = "KillerName"
pb.SC_COUNTRY_GETPRINCESSINFO_KILLERNAME_FIELD.full_name = ".SC_Country_GetPrincessInfo.KillerName"
pb.SC_COUNTRY_GETPRINCESSINFO_KILLERNAME_FIELD.number = 7
pb.SC_COUNTRY_GETPRINCESSINFO_KILLERNAME_FIELD.index = 6
pb.SC_COUNTRY_GETPRINCESSINFO_KILLERNAME_FIELD.label = 1
pb.SC_COUNTRY_GETPRINCESSINFO_KILLERNAME_FIELD.has_default_value = false
pb.SC_COUNTRY_GETPRINCESSINFO_KILLERNAME_FIELD.default_value = ""
pb.SC_COUNTRY_GETPRINCESSINFO_KILLERNAME_FIELD.type = 9
pb.SC_COUNTRY_GETPRINCESSINFO_KILLERNAME_FIELD.cpp_type = 9

pb.SC_COUNTRY_GETPRINCESSINFO_INVINCIBLESTATUS_FIELD.name = "InvincibleStatus"
pb.SC_COUNTRY_GETPRINCESSINFO_INVINCIBLESTATUS_FIELD.full_name = ".SC_Country_GetPrincessInfo.InvincibleStatus"
pb.SC_COUNTRY_GETPRINCESSINFO_INVINCIBLESTATUS_FIELD.number = 8
pb.SC_COUNTRY_GETPRINCESSINFO_INVINCIBLESTATUS_FIELD.index = 7
pb.SC_COUNTRY_GETPRINCESSINFO_INVINCIBLESTATUS_FIELD.label = 1
pb.SC_COUNTRY_GETPRINCESSINFO_INVINCIBLESTATUS_FIELD.has_default_value = false
pb.SC_COUNTRY_GETPRINCESSINFO_INVINCIBLESTATUS_FIELD.default_value = 0
pb.SC_COUNTRY_GETPRINCESSINFO_INVINCIBLESTATUS_FIELD.type = 5
pb.SC_COUNTRY_GETPRINCESSINFO_INVINCIBLESTATUS_FIELD.cpp_type = 1

pb.SC_COUNTRY_GETPRINCESSINFO_INVINCIBLESTATUSLEFTTIME_FIELD.name = "InvincibleStatusLeftTime"
pb.SC_COUNTRY_GETPRINCESSINFO_INVINCIBLESTATUSLEFTTIME_FIELD.full_name = ".SC_Country_GetPrincessInfo.InvincibleStatusLeftTime"
pb.SC_COUNTRY_GETPRINCESSINFO_INVINCIBLESTATUSLEFTTIME_FIELD.number = 9
pb.SC_COUNTRY_GETPRINCESSINFO_INVINCIBLESTATUSLEFTTIME_FIELD.index = 8
pb.SC_COUNTRY_GETPRINCESSINFO_INVINCIBLESTATUSLEFTTIME_FIELD.label = 1
pb.SC_COUNTRY_GETPRINCESSINFO_INVINCIBLESTATUSLEFTTIME_FIELD.has_default_value = false
pb.SC_COUNTRY_GETPRINCESSINFO_INVINCIBLESTATUSLEFTTIME_FIELD.default_value = 0
pb.SC_COUNTRY_GETPRINCESSINFO_INVINCIBLESTATUSLEFTTIME_FIELD.type = 5
pb.SC_COUNTRY_GETPRINCESSINFO_INVINCIBLESTATUSLEFTTIME_FIELD.cpp_type = 1

pb.SC_COUNTRY_GETPRINCESSINFO.name = "SC_Country_GetPrincessInfo"
pb.SC_COUNTRY_GETPRINCESSINFO.full_name = ".SC_Country_GetPrincessInfo"
pb.SC_COUNTRY_GETPRINCESSINFO.nested_types = {}
pb.SC_COUNTRY_GETPRINCESSINFO.enum_types = {}
pb.SC_COUNTRY_GETPRINCESSINFO.fields = {pb.SC_COUNTRY_GETPRINCESSINFO_CURHP_FIELD, pb.SC_COUNTRY_GETPRINCESSINFO_MAXHP_FIELD, pb.SC_COUNTRY_GETPRINCESSINFO_OWNCOUNTRY_FIELD, pb.SC_COUNTRY_GETPRINCESSINFO_EXISTENCETIME_FIELD, pb.SC_COUNTRY_GETPRINCESSINFO_PRIZERATE_FIELD, pb.SC_COUNTRY_GETPRINCESSINFO_COUNTRY_FIELD, pb.SC_COUNTRY_GETPRINCESSINFO_KILLERNAME_FIELD, pb.SC_COUNTRY_GETPRINCESSINFO_INVINCIBLESTATUS_FIELD, pb.SC_COUNTRY_GETPRINCESSINFO_INVINCIBLESTATUSLEFTTIME_FIELD}
pb.SC_COUNTRY_GETPRINCESSINFO.is_extendable = false
pb.SC_COUNTRY_GETPRINCESSINFO.extensions = {}
pb.SC_COUNTRY_PRINCESSINFO_RESULT_PRINCESSRECORDDATA_COUNTRYID_FIELD.name = "CountryID"
pb.SC_COUNTRY_PRINCESSINFO_RESULT_PRINCESSRECORDDATA_COUNTRYID_FIELD.full_name = ".SC_Country_PrincessInfo_Result.PrincessRecordData.CountryID"
pb.SC_COUNTRY_PRINCESSINFO_RESULT_PRINCESSRECORDDATA_COUNTRYID_FIELD.number = 1
pb.SC_COUNTRY_PRINCESSINFO_RESULT_PRINCESSRECORDDATA_COUNTRYID_FIELD.index = 0
pb.SC_COUNTRY_PRINCESSINFO_RESULT_PRINCESSRECORDDATA_COUNTRYID_FIELD.label = 1
pb.SC_COUNTRY_PRINCESSINFO_RESULT_PRINCESSRECORDDATA_COUNTRYID_FIELD.has_default_value = false
pb.SC_COUNTRY_PRINCESSINFO_RESULT_PRINCESSRECORDDATA_COUNTRYID_FIELD.default_value = 0
pb.SC_COUNTRY_PRINCESSINFO_RESULT_PRINCESSRECORDDATA_COUNTRYID_FIELD.type = 5
pb.SC_COUNTRY_PRINCESSINFO_RESULT_PRINCESSRECORDDATA_COUNTRYID_FIELD.cpp_type = 1

pb.SC_COUNTRY_PRINCESSINFO_RESULT_PRINCESSRECORDDATA_ROBENEMYTIMES_FIELD.name = "RobEnemyTimes"
pb.SC_COUNTRY_PRINCESSINFO_RESULT_PRINCESSRECORDDATA_ROBENEMYTIMES_FIELD.full_name = ".SC_Country_PrincessInfo_Result.PrincessRecordData.RobEnemyTimes"
pb.SC_COUNTRY_PRINCESSINFO_RESULT_PRINCESSRECORDDATA_ROBENEMYTIMES_FIELD.number = 2
pb.SC_COUNTRY_PRINCESSINFO_RESULT_PRINCESSRECORDDATA_ROBENEMYTIMES_FIELD.index = 1
pb.SC_COUNTRY_PRINCESSINFO_RESULT_PRINCESSRECORDDATA_ROBENEMYTIMES_FIELD.label = 1
pb.SC_COUNTRY_PRINCESSINFO_RESULT_PRINCESSRECORDDATA_ROBENEMYTIMES_FIELD.has_default_value = false
pb.SC_COUNTRY_PRINCESSINFO_RESULT_PRINCESSRECORDDATA_ROBENEMYTIMES_FIELD.default_value = 0
pb.SC_COUNTRY_PRINCESSINFO_RESULT_PRINCESSRECORDDATA_ROBENEMYTIMES_FIELD.type = 5
pb.SC_COUNTRY_PRINCESSINFO_RESULT_PRINCESSRECORDDATA_ROBENEMYTIMES_FIELD.cpp_type = 1

pb.SC_COUNTRY_PRINCESSINFO_RESULT_PRINCESSRECORDDATA_SELFROBBEDTIMES_FIELD.name = "SelfRobbedTimes"
pb.SC_COUNTRY_PRINCESSINFO_RESULT_PRINCESSRECORDDATA_SELFROBBEDTIMES_FIELD.full_name = ".SC_Country_PrincessInfo_Result.PrincessRecordData.SelfRobbedTimes"
pb.SC_COUNTRY_PRINCESSINFO_RESULT_PRINCESSRECORDDATA_SELFROBBEDTIMES_FIELD.number = 3
pb.SC_COUNTRY_PRINCESSINFO_RESULT_PRINCESSRECORDDATA_SELFROBBEDTIMES_FIELD.index = 2
pb.SC_COUNTRY_PRINCESSINFO_RESULT_PRINCESSRECORDDATA_SELFROBBEDTIMES_FIELD.label = 1
pb.SC_COUNTRY_PRINCESSINFO_RESULT_PRINCESSRECORDDATA_SELFROBBEDTIMES_FIELD.has_default_value = false
pb.SC_COUNTRY_PRINCESSINFO_RESULT_PRINCESSRECORDDATA_SELFROBBEDTIMES_FIELD.default_value = 0
pb.SC_COUNTRY_PRINCESSINFO_RESULT_PRINCESSRECORDDATA_SELFROBBEDTIMES_FIELD.type = 5
pb.SC_COUNTRY_PRINCESSINFO_RESULT_PRINCESSRECORDDATA_SELFROBBEDTIMES_FIELD.cpp_type = 1

pb.SC_COUNTRY_PRINCESSINFO_RESULT_PRINCESSRECORDDATA_ENEMYROBBEDTIMES_FIELD.name = "EnemyRobbedTimes"
pb.SC_COUNTRY_PRINCESSINFO_RESULT_PRINCESSRECORDDATA_ENEMYROBBEDTIMES_FIELD.full_name = ".SC_Country_PrincessInfo_Result.PrincessRecordData.EnemyRobbedTimes"
pb.SC_COUNTRY_PRINCESSINFO_RESULT_PRINCESSRECORDDATA_ENEMYROBBEDTIMES_FIELD.number = 4
pb.SC_COUNTRY_PRINCESSINFO_RESULT_PRINCESSRECORDDATA_ENEMYROBBEDTIMES_FIELD.index = 3
pb.SC_COUNTRY_PRINCESSINFO_RESULT_PRINCESSRECORDDATA_ENEMYROBBEDTIMES_FIELD.label = 1
pb.SC_COUNTRY_PRINCESSINFO_RESULT_PRINCESSRECORDDATA_ENEMYROBBEDTIMES_FIELD.has_default_value = false
pb.SC_COUNTRY_PRINCESSINFO_RESULT_PRINCESSRECORDDATA_ENEMYROBBEDTIMES_FIELD.default_value = 0
pb.SC_COUNTRY_PRINCESSINFO_RESULT_PRINCESSRECORDDATA_ENEMYROBBEDTIMES_FIELD.type = 5
pb.SC_COUNTRY_PRINCESSINFO_RESULT_PRINCESSRECORDDATA_ENEMYROBBEDTIMES_FIELD.cpp_type = 1

pb.SC_COUNTRY_PRINCESSINFO_RESULT_PRINCESSRECORDDATA_ROBSELFTIMES_FIELD.name = "RobSelfTimes"
pb.SC_COUNTRY_PRINCESSINFO_RESULT_PRINCESSRECORDDATA_ROBSELFTIMES_FIELD.full_name = ".SC_Country_PrincessInfo_Result.PrincessRecordData.RobSelfTimes"
pb.SC_COUNTRY_PRINCESSINFO_RESULT_PRINCESSRECORDDATA_ROBSELFTIMES_FIELD.number = 5
pb.SC_COUNTRY_PRINCESSINFO_RESULT_PRINCESSRECORDDATA_ROBSELFTIMES_FIELD.index = 4
pb.SC_COUNTRY_PRINCESSINFO_RESULT_PRINCESSRECORDDATA_ROBSELFTIMES_FIELD.label = 1
pb.SC_COUNTRY_PRINCESSINFO_RESULT_PRINCESSRECORDDATA_ROBSELFTIMES_FIELD.has_default_value = false
pb.SC_COUNTRY_PRINCESSINFO_RESULT_PRINCESSRECORDDATA_ROBSELFTIMES_FIELD.default_value = 0
pb.SC_COUNTRY_PRINCESSINFO_RESULT_PRINCESSRECORDDATA_ROBSELFTIMES_FIELD.type = 5
pb.SC_COUNTRY_PRINCESSINFO_RESULT_PRINCESSRECORDDATA_ROBSELFTIMES_FIELD.cpp_type = 1

pb.SC_COUNTRY_PRINCESSINFO_RESULT_PRINCESSRECORDDATA_OWNCOUNTRY_FIELD.name = "OwnCountry"
pb.SC_COUNTRY_PRINCESSINFO_RESULT_PRINCESSRECORDDATA_OWNCOUNTRY_FIELD.full_name = ".SC_Country_PrincessInfo_Result.PrincessRecordData.OwnCountry"
pb.SC_COUNTRY_PRINCESSINFO_RESULT_PRINCESSRECORDDATA_OWNCOUNTRY_FIELD.number = 6
pb.SC_COUNTRY_PRINCESSINFO_RESULT_PRINCESSRECORDDATA_OWNCOUNTRY_FIELD.index = 5
pb.SC_COUNTRY_PRINCESSINFO_RESULT_PRINCESSRECORDDATA_OWNCOUNTRY_FIELD.label = 1
pb.SC_COUNTRY_PRINCESSINFO_RESULT_PRINCESSRECORDDATA_OWNCOUNTRY_FIELD.has_default_value = false
pb.SC_COUNTRY_PRINCESSINFO_RESULT_PRINCESSRECORDDATA_OWNCOUNTRY_FIELD.default_value = 0
pb.SC_COUNTRY_PRINCESSINFO_RESULT_PRINCESSRECORDDATA_OWNCOUNTRY_FIELD.type = 5
pb.SC_COUNTRY_PRINCESSINFO_RESULT_PRINCESSRECORDDATA_OWNCOUNTRY_FIELD.cpp_type = 1

pb.SC_COUNTRY_PRINCESSINFO_RESULT_PRINCESSRECORDDATA.name = "PrincessRecordData"
pb.SC_COUNTRY_PRINCESSINFO_RESULT_PRINCESSRECORDDATA.full_name = ".SC_Country_PrincessInfo_Result.PrincessRecordData"
pb.SC_COUNTRY_PRINCESSINFO_RESULT_PRINCESSRECORDDATA.nested_types = {}
pb.SC_COUNTRY_PRINCESSINFO_RESULT_PRINCESSRECORDDATA.enum_types = {}
pb.SC_COUNTRY_PRINCESSINFO_RESULT_PRINCESSRECORDDATA.fields = {pb.SC_COUNTRY_PRINCESSINFO_RESULT_PRINCESSRECORDDATA_COUNTRYID_FIELD, pb.SC_COUNTRY_PRINCESSINFO_RESULT_PRINCESSRECORDDATA_ROBENEMYTIMES_FIELD, pb.SC_COUNTRY_PRINCESSINFO_RESULT_PRINCESSRECORDDATA_SELFROBBEDTIMES_FIELD, pb.SC_COUNTRY_PRINCESSINFO_RESULT_PRINCESSRECORDDATA_ENEMYROBBEDTIMES_FIELD, pb.SC_COUNTRY_PRINCESSINFO_RESULT_PRINCESSRECORDDATA_ROBSELFTIMES_FIELD, pb.SC_COUNTRY_PRINCESSINFO_RESULT_PRINCESSRECORDDATA_OWNCOUNTRY_FIELD}
pb.SC_COUNTRY_PRINCESSINFO_RESULT_PRINCESSRECORDDATA.is_extendable = false
pb.SC_COUNTRY_PRINCESSINFO_RESULT_PRINCESSRECORDDATA.extensions = {}
pb.SC_COUNTRY_PRINCESSINFO_RESULT_PRINCESSRECORDDATA.containing_type = pb.SC_COUNTRY_PRINCESSINFO_RESULT
pb.SC_COUNTRY_PRINCESSINFO_RESULT_RECORDLIST_FIELD.name = "RecordList"
pb.SC_COUNTRY_PRINCESSINFO_RESULT_RECORDLIST_FIELD.full_name = ".SC_Country_PrincessInfo_Result.RecordList"
pb.SC_COUNTRY_PRINCESSINFO_RESULT_RECORDLIST_FIELD.number = 1
pb.SC_COUNTRY_PRINCESSINFO_RESULT_RECORDLIST_FIELD.index = 0
pb.SC_COUNTRY_PRINCESSINFO_RESULT_RECORDLIST_FIELD.label = 3
pb.SC_COUNTRY_PRINCESSINFO_RESULT_RECORDLIST_FIELD.has_default_value = false
pb.SC_COUNTRY_PRINCESSINFO_RESULT_RECORDLIST_FIELD.default_value = {}
pb.SC_COUNTRY_PRINCESSINFO_RESULT_RECORDLIST_FIELD.message_type = pb.SC_COUNTRY_PRINCESSINFO_RESULT_PRINCESSRECORDDATA
pb.SC_COUNTRY_PRINCESSINFO_RESULT_RECORDLIST_FIELD.type = 11
pb.SC_COUNTRY_PRINCESSINFO_RESULT_RECORDLIST_FIELD.cpp_type = 10

pb.SC_COUNTRY_PRINCESSINFO_RESULT.name = "SC_Country_PrincessInfo_Result"
pb.SC_COUNTRY_PRINCESSINFO_RESULT.full_name = ".SC_Country_PrincessInfo_Result"
pb.SC_COUNTRY_PRINCESSINFO_RESULT.nested_types = {pb.SC_COUNTRY_PRINCESSINFO_RESULT_PRINCESSRECORDDATA}
pb.SC_COUNTRY_PRINCESSINFO_RESULT.enum_types = {}
pb.SC_COUNTRY_PRINCESSINFO_RESULT.fields = {pb.SC_COUNTRY_PRINCESSINFO_RESULT_RECORDLIST_FIELD}
pb.SC_COUNTRY_PRINCESSINFO_RESULT.is_extendable = false
pb.SC_COUNTRY_PRINCESSINFO_RESULT.extensions = {}

CS_Country_AddOfficer = protobuf.Message(pb.CS_COUNTRY_ADDOFFICER)
CS_Country_GetCountryInfo = protobuf.Message(pb.CS_COUNTRY_GETCOUNTRYINFO)
CS_Country_GetCountryKingWarInfo = protobuf.Message(pb.CS_COUNTRY_GETCOUNTRYKINGWARINFO)
CS_Country_RemoveOfficer = protobuf.Message(pb.CS_COUNTRY_REMOVEOFFICER)
CS_Country_SetCountryWar = protobuf.Message(pb.CS_COUNTRY_SETCOUNTRYWAR)
CS_Country_SetNotice = protobuf.Message(pb.CS_COUNTRY_SETNOTICE)
MSG_COUNTRY_ADDOFFICER = 3
MSG_COUNTRY_BATTLEFIELD_MAININFO = 18
MSG_COUNTRY_BATTLEFIELD_MAPINFO = 19
MSG_COUNTRY_COUNTRYPRINCESSRESULT = 22
MSG_COUNTRY_GETCOUNTRYINFO = 1
MSG_COUNTRY_GETCOUNTRYKINGWARINFO = 14
MSG_COUNTRY_GETCOUNTRYPRINCESSINFO = 21
MSG_COUNTRY_GETPKRECORDINFO = 20
MSG_COUNTRY_KINGWAR_RESULTINFO = 17
MSG_COUNTRY_KINGWAR_SYNCFIELDINFO = 15
MSG_COUNTRY_KINGWAR_SYNCSCOREINFO = 16
MSG_COUNTRY_NONE = 0
MSG_COUNTRY_REMOVEOFFICER = 4
MSG_COUNTRY_SETCOUNTRYWAR = 6
MSG_COUNTRY_SETNOTICE = 2
MSG_COUNTRY_SIGNCOUNTRYKINGWAR = 13
MSG_COUNTRY_UPDATEOFFICERJOBINFO = 5
MSG_COUNTRY_WAR_OPERATECOUNTDOWN = 8
MSG_COUNTRY_WAR_RESULTINFO = 10
MSG_COUNTRY_WAR_SYNCFIELDINFO = 9
MSG_COUNTRY_WAR_SYNCPLAYERSCORE = 7
SC_Country_BattleField_MainInfo = protobuf.Message(pb.SC_COUNTRY_BATTLEFIELD_MAININFO)
SC_Country_BattleField_MapInfo = protobuf.Message(pb.SC_COUNTRY_BATTLEFIELD_MAPINFO)
SC_Country_GetCountryInfo = protobuf.Message(pb.SC_COUNTRY_GETCOUNTRYINFO)
SC_Country_GetCountryInfo.CountryWarData = protobuf.Message(pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYWARDATA)
SC_Country_GetCountryInfo.CountryWarHistoryData = protobuf.Message(pb.SC_COUNTRY_GETCOUNTRYINFO_COUNTRYWARHISTORYDATA)
SC_Country_GetCountryInfo.OfficeData = protobuf.Message(pb.SC_COUNTRY_GETCOUNTRYINFO_OFFICEDATA)
SC_Country_GetCountryKingWarInfo = protobuf.Message(pb.SC_COUNTRY_GETCOUNTRYKINGWARINFO)
SC_Country_GetCountryKingWarInfo.KingWarSignData = protobuf.Message(pb.SC_COUNTRY_GETCOUNTRYKINGWARINFO_KINGWARSIGNDATA)
SC_Country_GetPKRecordInfo = protobuf.Message(pb.SC_COUNTRY_GETPKRECORDINFO)
SC_Country_GetPKRecordInfo.PKRecordData = protobuf.Message(pb.SC_COUNTRY_GETPKRECORDINFO_PKRECORDDATA)
SC_Country_GetPrincessInfo = protobuf.Message(pb.SC_COUNTRY_GETPRINCESSINFO)
SC_Country_KingWarResultInfo = protobuf.Message(pb.SC_COUNTRY_KINGWARRESULTINFO)
SC_Country_KingWarResultInfo.SocietyScoreData = protobuf.Message(pb.SC_COUNTRY_KINGWARRESULTINFO_SOCIETYSCOREDATA)
SC_Country_KingWarSyncFieldInfo = protobuf.Message(pb.SC_COUNTRY_KINGWARSYNCFIELDINFO)
SC_Country_KingWarSyncFieldInfo.MonsterData = protobuf.Message(pb.SC_COUNTRY_KINGWARSYNCFIELDINFO_MONSTERDATA)
SC_Country_KingWarSyncScoreInfo = protobuf.Message(pb.SC_COUNTRY_KINGWARSYNCSCOREINFO)
SC_Country_KingWarSyncScoreInfo.ScoreData = protobuf.Message(pb.SC_COUNTRY_KINGWARSYNCSCOREINFO_SCOREDATA)
SC_Country_PrincessInfo_Result = protobuf.Message(pb.SC_COUNTRY_PRINCESSINFO_RESULT)
SC_Country_PrincessInfo_Result.PrincessRecordData = protobuf.Message(pb.SC_COUNTRY_PRINCESSINFO_RESULT_PRINCESSRECORDDATA)
SC_Country_UpdateOfficerJobInfo = protobuf.Message(pb.SC_COUNTRY_UPDATEOFFICERJOBINFO)
SC_Country_WarOperateCountdown = protobuf.Message(pb.SC_COUNTRY_WAROPERATECOUNTDOWN)
SC_Country_WarResultInfo = protobuf.Message(pb.SC_COUNTRY_WARRESULTINFO)
SC_Country_WarResultInfo.ActorScoreData = protobuf.Message(pb.SC_COUNTRY_WARRESULTINFO_ACTORSCOREDATA)
SC_Country_WarResultInfo.SocietyScoreData = protobuf.Message(pb.SC_COUNTRY_WARRESULTINFO_SOCIETYSCOREDATA)
SC_Country_WarSyncFieldInfo = protobuf.Message(pb.SC_COUNTRY_WARSYNCFIELDINFO)
SC_Country_WarSyncPlayerScore = protobuf.Message(pb.SC_COUNTRY_WARSYNCPLAYERSCORE)

