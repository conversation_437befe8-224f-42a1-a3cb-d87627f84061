﻿using System;
using System.Collections.Generic;
using System.Linq;

using Apq.ChangeBubbling;

using View;

using ViewModel;

namespace Thing
{
    public partial class ThingBase
    {
        /// <summary>
        /// 枪械列表
        /// </summary>
        public BubblingList<GunThing> Guns { get; }

        /// <summary>
        /// 背包可见枪械列表
        /// </summary>
        public List<GunThing> GunsInBag => Guns.Where(x => !x.IsHidden).ToList();

        /// <summary>
        /// 重新创建枪及其固有属性、战场属性
        /// </summary>
        /// <param name="gunsInBag"></param>
        /// <param name="apply">加载附着的属性后是否应用</param>
        public virtual void ReCreateGuns(List<GunItem> gunsInBag, bool apply = false)
        {
            Guns.Where(p => p.IsHidden).ToList().ForEach(g => g.StopCdExecutor());
            Guns.Clear();
            
            gunsInBag.Where(g => g.GunId > 0).ToList().ForEach(g =>
            {
                var (gun, _) = AddGun(g, apply);
                gun.Camp = Camp;
            });
        }

        /// <summary>
        /// 添加枪械及其固有属性、战场属性
        /// </summary>
        /// <param name="gunItem"></param>
        /// <param name="apply">加载附着的属性后是否应用</param>
        /// <returns>添加的枪,需要重新计算总属性的物件</returns>
        public virtual (GunThing gun, List<ThingBase> lst) AddGun(GunItem gunItem, bool apply = false)
        {
            var lst = new List<ThingBase>();
            var gun = new GunThing(null, Guns)
            {
                Owner = this,
            }.InitFromCsv(gunItem.GunId, gunItem.GunLvl, gunItem.StarCount);
            gun.Guid = gunItem.GunGuid;

            if (gunItem.PosInBag?.Count > 0)
            {
                gun.PosInBag.Clear();
                gun.PosInBag.AddRange(gunItem.PosInBag);
                // gun.Position = gun.RectArea2D.Center;
            }

            Guns.Add(gun);
            gun.ReloadAttachedProps();

            if (apply)
            {
            	// 物件应用来自这只枪的属性
            	if (AddProp(gun))
            	{
            		lst.Add(this);
            	}

            	// 所有枪应用来自这只枪的属性
            	Guns.ToList().ForEach(g =>
            	{
            		if (g.AddProp(gun))
            		{
            			if (!lst.Contains(g))
            			{
            				lst.Add(g);
            			}
            		}
            	});

            	// 怪物应用来自这只枪的属性
            	SingletonMgr.Instance.BattleMgr.Monsters.ToList().ForEach(m =>
            	{
            		if (m.AddProp(gun))
            		{
            			lst.Add(m);
            		}
            	});
            }

            return (gun, lst);
        }

        /// <summary>
        /// 移除枪械
        /// </summary>
        /// <returns>需要重新计算提升的物件</returns>
        public virtual List<ThingBase> RemoveGun(Guid guid)
        {
            var rtn = new List<ThingBase>();
            var gun = Guns.FirstOrDefault(g => g.Guid == guid);
            if (gun != null)
            {
                gun.StopCdExecutor();

                rtn = RemovePropFromThing(guid);

                Guns.Remove(gun);
            }
            return rtn;
        }

        /// <summary>
        /// 怪物、物件(及其枪) 移除 来自指定物件 的属性
        /// </summary>
        /// <returns>需要重新计算提升的物件</returns>
        public virtual List<ThingBase> RemovePropFromThing(Guid guid)
        {
            var rtn = new List<ThingBase>();

            // 怪物移除来自指定物件的属性
            SingletonMgr.Instance.BattleMgr.Monsters.ToList().ForEach(m =>
            {
                // 怪物的枪移除来自指定物件的属性
                m.Guns.ToList().ForEach(g =>
                {
                    if (g.RemoveProp(guid))
                    {
                        rtn.Add(g);
                    }
                });

                if (m.RemoveProp(guid))
                {
                    rtn.Add(m);
                }
            });
            
            // 物件的枪移除来自指定物件的属性
            Guns//.Where(g => g.Guid != gun.Guid)
                .ToList().ForEach(g =>
            {
                if (g.RemoveProp(guid))
                {
                    rtn.Add(g);
                }
            });

            // 物件移除来自指定物件的属性
            if (RemoveProp(guid) || (this is GunThing gun && gun.FindTotalPropsForCreature().Count > 0))
            {
                rtn.Add(this);
            }
            
            return rtn;
        }
    }
}