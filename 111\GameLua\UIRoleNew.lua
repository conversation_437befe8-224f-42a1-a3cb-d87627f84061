--------------------------------------------------------------------
-- 文件名:	.\GameLua\UIRole.lua
-- 版  权:	(C) DDF Studio
-- 创建人:	吴奇达
-- 日  期:	2022/12/07 10:36
-- 描  述:	细胞界面
--------------------------------------------------------------------

local luaID = ('UIRoleNew')

---@class UIRoleNew:UIWndBase
local m = {
    IconBgList = { "10003", "10005", "10001", "10006", "10002" },
    ShuXingIds = {
        PLAYER_FIELD.PLAYER_FIELD_HP,
        PLAYER_FIELD.PLAYER_FIELD_PHYSICS_ATTACK,
        PLAYER_FIELD.PLAYER_FIELD_CRITICAL_STRIKE,
        PLAYER_FIELD.PLAYER_FIELD_ANTI_PARRY,
        PLAYER_FIELD.PLAYER_FIELD_ANTI_ARMOR
    },
    EquipCollectItemList = {},
    -- 当前升级的实体索引
    curEntityIndex = 0,
    -- 升级需要消耗金币
    CostMoneyList = {},
}

local SET = {
    BagSlotMax = 20,  -- 背包单页槽最大值
    SlotWidth = 130,  -- 装备物品槽宽
    SlotHeight = 130, -- 装备物品槽高
    AllFilter = 0,    -- 全部物品按钮
    EuipFilter = 1,   -- 装备帅选按钮
    GoodFilter = 2,   -- 材料帅选按钮
    BoxFilter = 3,    -- 宝箱帅选按钮
    SmeltSort = 1,    -- 强化等级排序
    QuaSort = 2,      -- 品质颜色排序
    TypeSort = 3      -- 部位ID排序
}

local BatchType = {
    compound = 1,
    resolve = 2
}

--------------------------------------------------------------------
-- 佩戴，卸下装备物品处理
--------------------------------------------------------------------
function m:GetOpenEventList()
    return {
        -- [EventID.OnGoodsCreate] = self.OnHeroPropChange,
        -- [EventID.OnGoodsPropChange] = self.OnHeroPropChange,
        -- [EventID.OnSkepGoodsChange] = self.OnHeroPropChange,
        [EventID.LogicDataChange] = self.OnHeroPropChange,
        [EventID.OnHeroPropChange] = self.OnHeroPropChange,
    }
end

--------------------------------------------------------------------
--预制体自适应
--------------------------------------------------------------------
function m:AdaptScale()
    HelperL.AdaptScale(self.objList.Img_bg1, 6)
end

--------------------------------------------------------------------
-- 初始化
--------------------------------------------------------------------
function m:OnCreate()
    m.IsPropShow = false
    self.objList.Btn_Close.onClick:AddListenerEx(self.OnClickClose)
    self.objList.Btn_Mask.onClick:AddListenerEx(self.OnClickMask)
    self.objList.Btn_Attribute.onClick:AddListenerEx(self.OnClickShowShuXing)
    m.objList.Btn_Explain.onClick:AddListenerEx(function()
        HelperL.OpenTipsWindow(7)
    end)
    -- self.objList.Txt_Title.text = GetGameText(luaID, 1)
    -- self.objList.Txt_LeftName.text = GetGameText(luaID, 2)
    self.rootModel = self.objList.Img_Model.gameObject -- 3D模型挂接控件
    self:CreateSlots()                                 -- 创建槽
    m:CreateQiangHua()

    m.Item_Fight = FightAttribute.New()
    m.Item_Fight.Init(m.objList.Item_Fight)
    return true
end

-- 主角属性变化通知
function m.OnHeroPropChange()
    m:RefreshEquipCollectItem()
end

--- 超级月卡(等)增加的体力上限
function m:MaxTiLi()
    local coefficient = 0
    if HelperL.IsBuyChaoJiYueKa() then
        -- 之前写死,这里也写死
        coefficient = coefficient + 20
    end
    return coefficient
end

--------------------------------------------------------------------
-- 窗口开启
--------------------------------------------------------------------
function m:OnOpen(filterType)
    UIManager:SubscribeFrameUpdate(self:GetWndID(), true)
    self:RefreshEquipCollectItem()
    if not filterType then
        filterType = SET.EuipFilter
    end
    self:UpdateViewL() -- 左侧面板
    self:FilterGoods() -- 帅选背包
    self.curFilterType = filterType
    self:OnClickSort() -- 默认排序
    local hero = EntityModule.hero
    if not hero then
        return
    end
    local actorID = hero:GetProperty(ENTITY_FIELD.ENTITY_FIELD_ID)
    local level = hero:GetProperty(CREATURE_FIELD.CREATURE_FIELD_LEVEL)
    self.objList.Txt_HeroPropertyValue.text = string.format(GetGameText(luaID, 19), hero.name, actorID, level)
    -- self.objList.Tog_ShuXing.isOn = true

    -- local healthLevel = HelperL.GetActorPropLevel(ActorPropLevelType.Health)
    -- if level == 2 and healthLevel <= 7 and not GuideManager.isRunning then
    --     GuideManager.isRunning = true
    --     local delayGuide = Timer.New(function()
    --         GuideManager.StartGuide(GuideManager.EventType.PropUpgradeGuide, 2)
    --     end, 0.5, 1)
    --     delayGuide:Start()
    -- end
end

--------------------------------------------------------------------
-- 佩戴，卸下装备物品处理
--------------------------------------------------------------------
function m.OnSkepGoodsChange(skeyID, m)
    local self = m
    if self.gameObject.activeSelf then
        self:FilterGoods()
        if skeyID == SKEPID.SKEPID_EQUIP then
            -- print("更新装备槽")
            for _, v in ipairs(m.PlaceList) do
                self:UpdateEuipSlot(v.Place)
            end

            local delayTime = TimerEx.New(function()
                self:UpdateViewL() -- 左侧面板
            end, 0.2, 1)           -- 延迟更新，属性才正确
            delayTime:Start()
        elseif skeyID == SKEPID.SKEPID_PACKET then
            if self.beginCompounding then
                return
            end
            print("更新背包")

            self:OnClickSort()
            self:UpdateBag()
        end
    end
end

--------------------------------------------------------------------
-- 刷新单个装备槽显示
--------------------------------------------------------------------
function m:UpdateEuipSlot(place)
    local euipType = place
    local slotItem = self.equipSlots[euipType]
    -- print('place='..place)
    if slotItem then
        local eUID = SkepModule.GetEquipSkep()[euipType]
        local entity = EntityModule:GetEntity(eUID)
        if entity then
            slotItem:SetEntity(entity)
            slotItem:SetDes(nil)
            local itemID = entity:GetProperty(ENTITY_FIELD.ENTITY_FIELD_ID)
            local schemeItem = Schemes:GetGoodsConfig(itemID)
            local itemQuality = entity:GetProperty(EQUIPMENT_FIELD.EQUIPMENT_FIELD_QUALITY)
            local itemStarNum = entity:GetProperty(EQUIPMENT_FIELD.EQUIPMENT_FIELD_STARNUM)
            local itemExp = entity:GetProperty(EQUIPMENT_FIELD.EQUIPMENT_FIELD_SMELTEXP)
            -- print('itemID='..itemID)
            if schemeItem then
                slotItem.TxtEquipName.text = schemeItem.GoodsName
                slotItem.TxtEquipLevel.text = schemeItem.UseLevel .. CommonTextID.LEVEL
                slotItem.Txt_StarLevel.text = "+" .. (itemQuality * 1000 + itemStarNum * 100 + itemExp)
                -- slotItem:SetName(schemeItem.GoodsName)
            end
            -- slotItem.Img_vfx.gameObject:SetActive(true)
        else
            -- print('SetEntity is nil')
            slotItem:SetEntity(nil)
            local equipPlaceName = self.equipSlotsDes[euipType]
            slotItem:SetDes(equipPlaceName)
            slotItem.TxtEquipName.text = ''
            slotItem.TxtEquipLevel.text = ''
            slotItem.Txt_StarLevel.text = ''
            -- slotItem.Img_vfx.gameObject:SetActive(false)
        end

        self:SetEnableUpgradeOrBreake(slotItem)
    end
end

--------------------------------------------------------------------
-- 单个装备槽是否满足升级或进阶
--------------------------------------------------------------------
function m:SetEnableUpgradeOrBreake(slotItem)
    local hero = EntityModule.hero
    if not hero then
        return
    end
    local schemeItem = Schemes:GetGoodsConfig(slotItem.itemID)
    if schemeItem then
        local starNum = 0
        local costMoney = 0
        local needGoodsID = 0
        local needGoodsNum = 0
        if slotItem.entity then
            starNum = slotItem.entity:GetProperty(EQUIPMENT_FIELD.EQUIPMENT_FIELD_STARNUM)
        end
        local smeltItem = Schemes.EquipSmelt:Get(schemeItem.SmeltID, 0, starNum)
        local isMaxLevel = false
        if smeltItem then
            if smeltItem.NextQuality == 0 and smeltItem.NextStarNum == 0 then
                -- 已满级
                isMaxLevel = true
            else
                costMoney = smeltItem.CostMoney
                needGoodsID = smeltItem.GoodsID
                needGoodsNum = smeltItem.GoodsNum
            end
        end

        -- 最大等级，显示进阶
        if isMaxLevel then
            if schemeItem.Exchangeid > 0 then
                local exchangeItem = Schemes.EquipExchange:Get(schemeItem.Exchangeid)
                if exchangeItem then
                    costMoney = exchangeItem.CostMoney
                    needGoodsID = exchangeItem.NeedGoodsID1
                    needGoodsNum = exchangeItem.NeedGoodsNum + exchangeItem.NeedGoodsNum1 + exchangeItem.NeedGoodsNum2 +
                        exchangeItem.NeedGoodsNum3
                end
            end
        end

        local moneyEnable = false
        -- 消耗金钱
        local moneyAll = SkepModule:GetGoodsCount(4)
        if moneyAll >= costMoney then
            moneyEnable = true
        end

        local goodsEnable = false
        -- 消耗材料
        local skepBag = SkepModule:GetSkepByID(SKEPID.SKEPID_PACKET)
        if skepBag then
            local goodsCount = skepBag:FastCount(needGoodsID)
            if goodsCount >= needGoodsNum then
                goodsEnable = true
            end
        end

        if moneyEnable and goodsEnable then
            slotItem.upPointItem.gameObject:SetActive(true)
            return
        end
    end
    slotItem.upPointItem.gameObject:SetActive(false)
end

--------------------------------------------------------------------
-- 获取装备槽显示
--------------------------------------------------------------------
function m:GetEuipSlotItem(place)
    if not self.equipSlots then
        return nil
    end
    local euipType = place
    local slotItem = self.equipSlots[euipType]
    return slotItem
end

function m:GetEuipSlotID(place)
    local euipType = place
    local eUID = SkepModule.GetEquipSkep()[euipType]
    local entity = EntityModule:GetEntity(eUID)
    local itemID
    if entity then
        itemID = entity:GetProperty(ENTITY_FIELD.ENTITY_FIELD_ID)
    end
    return itemID
end

--------------------------------------------------------------------
-- 帅选预存储背包物品数据
--------------------------------------------------------------------
function m:FilterGoods()
    self.dataGoods = {
        -- [SET.AllFilter] = {}, -- 全部
        [SET.EuipFilter] = {}, -- 装备
        [SET.GoodFilter] = {}, -- 材料
        [SET.BoxFilter] = {}   -- 材料
    }
    -- local allList = self.dataGoods[SET.AllFilter]
    local euipList = self.dataGoods[SET.EuipFilter]
    local goodList = self.dataGoods[SET.GoodFilter]
    local boxList = self.dataGoods[SET.BoxFilter]
    if self.skepBag then
        -- 每次打开背包重新刷新物品数据
        for bagIndex = 0, self.skepBag.indexMaxsize do
            local eUID = self.skepBag[bagIndex]
            if eUID then
                local entity = EntityModule:GetEntity(eUID)
                if entity then
                    local goodsID = entity:GetProperty(ENTITY_FIELD.ENTITY_FIELD_ID)
                    if not HelperL.IsPet(goodsID) then
                        if HelperL.IsEuipType(goodsID) then
                            -- 装备类物品
                            table.insert(euipList, bagIndex)
                        else
                            -- 材料类物品
                            if HelperL.IsBox(goodsID) then
                                table.insert(boxList, bagIndex)
                            else
                                table.insert(goodList, bagIndex)
                            end
                        end
                        -- 通吃
                        -- table.insert(allList, bagIndex)
                    end
                end
            end
        end
    end
end

--------------------------------------------------------------------
-- 创建装备槽和背包槽、翻页按钮
--------------------------------------------------------------------
function m:CreateSlots()
    -- 装备槽
    local skepEuip = SkepModule.GetEquipSkep()
    if not skepEuip then
        warn('UUIRole:CreateSlots，装备篮子都找不到')
    end
    self.skepBag = SkepModule:GetSkepByID(SKEPID.SKEPID_PACKET)
    if not self.skepBag then
        warn('UIRole:CreateSlots，背包篮子都找不到')
    end
    self.equipSlots = {}
    self.equipSlotsDes = {}
    local LSlotRoot = self.objList.LSlots:GetRectTransform()
    local equipSlotsItem = self.objList.EquipSlotsItem.gameObject
    for e = 1, 4 do
        -- 左边4个
        local lineItem = self:CreateSubItem(LSlotRoot, equipSlotsItem)
        local euipType = EquipOrder[e]
        local item = _GAddSlotItem(lineItem.Img_itembg.transform, nil, nil, SET.SlotWidth, SET.SlotHeight)
        item.transform.localPosition = Vector3.zero
        item:SetJustShow(false)
        item:SetIconSize(SET.SlotWidth - 20, SET.SlotHeight - 20)
        local equipPlaceName = GetEquipTypeDescribe(euipType)
        item:SetDes(equipPlaceName)
        item.upPointItem = lineItem.Img_Up
        item.upPointItem.transform:SetAsLastSibling()
        item.TxtEquipLevel = lineItem.Txt_EquipLevel
        item.Txt_StarLevel = lineItem.Txt_StarLevel
        item.TxtEquipLevel.transform:SetAsLastSibling()
        item.TxtEquipName = lineItem.Txt_EquipName
        self.equipSlots[euipType] = item
        self.equipSlotsDes[euipType] = equipPlaceName
    end
    local RSlotRoot = self.objList.RSlots:GetRectTransform()
    for e = 5, #EquipOrder do
        -- 右边4个
        local lineItem = self:CreateSubItem(RSlotRoot, equipSlotsItem)
        local euipType = EquipOrder[e]
        local item = _GAddSlotItem(lineItem.Img_itembg.transform, nil, nil, SET.SlotWidth, SET.SlotHeight)
        item.transform.localPosition = Vector3.zero
        item:SetJustShow(false)
        item:SetIconSize(SET.SlotWidth - 20, SET.SlotHeight - 20)
        local equipPlaceName = GetEquipTypeDescribe(euipType)
        item:SetDes(equipPlaceName)
        item.upPointItem = lineItem.Img_Up
        item.upPointItem.transform:SetAsLastSibling()
        item.TxtEquipLevel = lineItem.Txt_EquipLevel
        item.Txt_StarLevel = lineItem.Txt_StarLevel
        item.TxtEquipLevel.transform:SetAsLastSibling()
        item.TxtEquipName = lineItem.Txt_EquipName
        self.equipSlots[euipType] = item
        self.equipSlotsDes[euipType] = equipPlaceName
    end
end

--------------------------------------------------------------------
-- 刷新左侧面板信息
--------------------------------------------------------------------
function m:UpdateViewL()
    self:CreateAirMode() -- 出战细胞

    if SkepModule.GetEquipSkep() then
        -- 实时获取玩家装备信息
        for euipType, slotItem in pairs(self.equipSlots) do
            self:UpdateEuipSlot(euipType)
        end
    end
end

--------------------------------------------------------------------
-- 刷新背包面板
--------------------------------------------------------------------
function m:UpdateBag()
    local hero = EntityModule.hero
    if not hero then
        return
    end
    if self.skepBag and self.curFilterType then
        -- local curGoodList = self.dataGoods[self.curFilterType]
        -- 根据页数计算物品列表起始索引
        -- local startBag = (self.curPageIndex - 1) * SET.BagSlotMax
        -- 根据过滤按钮显示对应物品信息
        if self.bagSlots then
            for index, slotItem in ipairs(self.bagSlots) do
                slotItem:SetRedDot(false)
                slotItem:SetEntity(nil)
                -- slotItem.gameObject:SetActive(false)
                slotItem.transform.parent.gameObject:SetActive(false)
                slotItem.TextName.text = ''
                slotItem.TextDes.text = ''
            end
        end

        if self.boxExchangeBagSlots then
            for index, slotItem in ipairs(self.boxExchangeBagSlots) do
                slotItem:SetRedDot(false)
                slotItem:SetEntity(nil)
                slotItem.transform.parent.gameObject:SetActive(false)
                -- slotItem.TextNum.text = ''
                -- slotItem.TextDes.text = ''
            end
        end

        local curGoodList = self.dataGoods[self.curFilterType]

        if self.curFilterType == SET.BoxFilter then
            -- self.objList.ScrollViewBox.gameObject:SetActive(true)
            self.objList.Img_LeftBg.gameObject:SetActive(false)
            self.objList.BoxSpine.gameObject:SetActive(true)
            -- self.objList.Btn_FeiJiCangKu.gameObject:SetActive(false)
            -- self.objList.Btn_Sort.gameObject:SetActive(false)
            -- self.objList.Btn_HeCheng.gameObject:SetActive(false)
            self.objList.Btn_FenJie.gameObject:SetActive(false)
            self.objList.BagSelectView.gameObject:SetActive(false)
            self.IsBoxBagFilter = true

            local killNum = hero:GetProperty(PLAYER_FIELD.PLAYER_FIELD_MORAL)
            local skepBoxBag = SkepModule:GetSkepByID(SKEPID.SKEPID_PACKET)
            local boxBagSlotsLen = #self.boxExchangeBagSlots
            for i = 1, boxBagSlotsLen do
                local exchangeBoxItem = self.boxExchangeBagSlots[i]
                exchangeBoxItem.transform.parent.gameObject:SetActive(true)
                local goodsID = exchangeBoxItem.config.MedicamID
                local goodsCount = skepBoxBag:FastCount(goodsID)
                if not goodsCount then
                    goodsCount = 0
                end
                local costNum = exchangeBoxItem.config.exchange
                if costNum <= 0 then
                    exchangeBoxItem.BtnExchange.gameObject:SetActive(false)
                    exchangeBoxItem.slider.gameObject:SetActive(false)
                    -- exchangeBoxItem.TxtItemNum.gameObject:SetActive(true)
                    exchangeBoxItem.TextDes.gameObject:SetActive(false)
                else
                    if killNum <= costNum then
                        exchangeBoxItem.ShadowExchange:GetComponent("Image").color = HelperL.color_gray
                        exchangeBoxItem.BaseExchange:GetComponent("Image").color = HelperL.color_gray
                        exchangeBoxItem.MainExchange:GetComponent("Image").color = HelperL.color_gray
                    else
                        exchangeBoxItem.ShadowExchange:GetComponent("Image").color = HelperL.btn_color
                        exchangeBoxItem.BaseExchange:GetComponent("Image").color = HelperL.btn_base_color
                        exchangeBoxItem.MainExchange:GetComponent("Image").color = HelperL.btn_color
                    end
                end

                exchangeBoxItem.slider.value = killNum / costNum
                exchangeBoxItem.TextExchangeNum.text = string.format('%d/%d', killNum, costNum)
                exchangeBoxItem.TextDes.text = '有效杀怪数量：'
                -- exchangeBoxItem.TxtItemNum.text = '拥有数量：'..goodsCount
                exchangeBoxItem.TextName.text = exchangeBoxItem.config.Tips .. ':' .. goodsCount

                -- print(goodsID..'   '..goodsCount)
                if goodsCount > 0 then
                    for index = 1, #curGoodList do
                        local bagIndex = curGoodList[index]
                        if bagIndex then
                            local eUID = self.skepBag[bagIndex]
                            if eUID then
                                local entity = EntityModule:GetEntity(eUID)
                                if entity then
                                    local ownGoodsID = entity:GetProperty(ENTITY_FIELD.ENTITY_FIELD_ID)
                                    if goodsID == ownGoodsID then
                                        -- print(i..'   '..goodsID..'   '..ownGoodsID)
                                        exchangeBoxItem:SetEntity(entity)
                                    end
                                end
                            end
                        end
                    end
                else
                    exchangeBoxItem:SetItemID(goodsID)
                end
            end
            self.OnClickSelect(self.curSelectBoxIndex)
        else
            self.objList.Img_LeftBg.gameObject:SetActive(true)
            -- self.objList.ScrollViewBox.gameObject:SetActive(false)
            self.objList.BoxSpine.gameObject:SetActive(false)
            -- self.objList.Btn_FeiJiCangKu.gameObject:SetActive(true)
            -- self.objList.Btn_Sort.gameObject:SetActive(true)
            -- self.objList.Btn_HeCheng.gameObject:SetActive(true)
            self.objList.Btn_FenJie.gameObject:SetActive(true)
            self.objList.BagSelectView.gameObject:SetActive(true)
            self.IsBoxBagFilter = false
            for index = 1, #curGoodList do
                local bagIndex = curGoodList[index]
                if bagIndex then
                    local eUID = self.skepBag[bagIndex]
                    if eUID then
                        local entity = EntityModule:GetEntity(eUID)
                        if entity then
                            local goodsID = entity:GetProperty(ENTITY_FIELD.ENTITY_FIELD_ID)
                            local schemeItem = Schemes:GetGoodsConfig(goodsID)
                            if schemeItem then
                                local quality = schemeItem.Quality > 0 and schemeItem.Quality or schemeItem.QualityLevel
                                if not quality then
                                    quality = 0
                                end
                                local selectIndex = quality + 1
                                if selectIndex > 7 then
                                    selectIndex = 7
                                end
                                local selectSlotItem = self.selectEquipBagSlotItemList[selectIndex]
                                local select_ison = selectSlotItem.isOn
                                if select_ison then
                                    local slotItem = self.bagSlots[index]
                                    local goodsNum = entity:GetProperty(GOODS_FIELD.GOODS_FIELD_QTY)
                                    slotItem.transform.parent.gameObject:SetActive(true)
                                    slotItem:SetCount(goodsNum)

                                    slotItem:SetEntity(entity)
                                    slotItem.TextName.text = schemeItem.GoodsName
                                    slotItem.BtnLineBg.onClick:RemoveAllListeners()
                                    slotItem.BtnLineBg.onClick:AddListenerEx(function()
                                        self.OnClickLineItem(slotItem)
                                    end)
                                    self:RedDotLogic(slotItem, goodsID)
                                    if HelperL.IsEuipType(goodsID) then
                                        -- 基础属性
                                        -- slotItem.TextName.color = HelperL.GetQualityColorRGBA(schemeItem.QualityLevel)
                                        local randomIdList = HelperL.Split(schemeItem.RandomID, ';')
                                        for i = 1, #randomIdList do
                                            local randomId = tonumber(randomIdList[i])
                                            if randomId > 1 then
                                                local equipEffectItem = Schemes.EquipEffect:Get(randomId)
                                                if equipEffectItem then
                                                    slotItem.TextDes.text = equipEffectItem.Desc ..
                                                        equipEffectItem.EffectParam
                                                end
                                            end
                                        end
                                    else
                                        -- slotItem.TextName.color = HelperL.GetQualityColorRGBA(schemeItem.Quality)
                                        slotItem.TextDes.text = HelperL.GetContenBRText(schemeItem.TipsDes)
                                    end
                                end
                            end
                        end
                    end
                end
            end
        end
    end
end

function m:UpdataBoxBag()
    local hero = EntityModule.hero
    if not hero then
        return
    end
    self.openBoxDelayTimeUpdata = nil
    if self.curFilterType == SET.BoxFilter then
        for index, slotItem in ipairs(self.boxExchangeBagSlots) do
            slotItem:SetRedDot(false)
            slotItem:SetEntity(nil)
            slotItem.transform.parent.gameObject:SetActive(false)
        end
        local killNum = hero:GetProperty(PLAYER_FIELD.PLAYER_FIELD_MORAL)
        local skepBoxBag = SkepModule:GetSkepByID(SKEPID.SKEPID_PACKET)
        local boxBagSlotsLen = #self.boxExchangeBagSlots
        local curGoodList = self.dataGoods[self.curFilterType]
        for i = 1, boxBagSlotsLen do
            local exchangeBoxItem = self.boxExchangeBagSlots[i]
            exchangeBoxItem.transform.parent.gameObject:SetActive(true)
            local goodsID = exchangeBoxItem.config.MedicamID
            local goodsCount = skepBoxBag:FastCount(goodsID)
            if not goodsCount then
                goodsCount = 0
            end
            local costNum = exchangeBoxItem.config.exchange
            if costNum <= 0 then
                exchangeBoxItem.BtnExchange.gameObject:SetActive(false)
                exchangeBoxItem.slider.gameObject:SetActive(false)
            end
            exchangeBoxItem.slider.value = killNum / costNum
            exchangeBoxItem.TextExchangeNum.text = string.format('%d/%d', killNum, costNum)
            exchangeBoxItem.TextDes.text = '击杀怪物数量：'
            -- exchangeBoxItem.TxtItemNum.text = '拥有数量：'..goodsCount
            -- exchangeBoxItem.TextName.text = exchangeBoxItem.config.Tips
            exchangeBoxItem.TextName.text = exchangeBoxItem.config.Tips .. ':' .. goodsCount
            exchangeBoxItem:SetItemID(goodsID)

            for index = 1, #curGoodList do
                local bagIndex = curGoodList[index]
                if bagIndex then
                    local eUID = self.skepBag[bagIndex]
                    if eUID then
                        local entity = EntityModule:GetEntity(eUID)
                        if entity then
                            local ownGoodsID = entity:GetProperty(ENTITY_FIELD.ENTITY_FIELD_ID)
                            if goodsID == ownGoodsID then
                                exchangeBoxItem:SetEntity(entity)
                            end
                        end
                    end
                end
            end
        end
    end
end

function m.OnClickLineItem(slotItem)
    HelperL.OnShowTips(slotItem)
end

--------------------------------------------------------------------
-- 创建细胞模型
--------------------------------------------------------------------
function m:CreateAirMode()
    local equipID = HelperL.GetWeaponEquipID()
    if not m.ui2DModel then
        m.ui2DModel = HelperL.CreateRenderTextureAndModel(m.objList.Img_Model)
    end
    m.ui2DModel:CreateUIModel3(equipID)
end

--------------------------------------------------------------------
-- 强化等级排序
--------------------------------------------------------------------
local SmeltSortFunc = function(bagIndexA, bagIndexB)
    local self = m
    local entityA = EntityModule:GetEntity(self.skepBag[bagIndexA])
    local entityB = EntityModule:GetEntity(self.skepBag[bagIndexB])
    if not entityA or not entityB then
        return false
    end
    local goodIDA = entityA:GetProperty(ENTITY_FIELD.ENTITY_FIELD_ID)
    local goodIDB = entityB:GetProperty(ENTITY_FIELD.ENTITY_FIELD_ID)
    -- if not HelperL.IsEuipType(goodIDA) or not HelperL.IsEuipType(goodIDB) then
    ---- 非装备类物品不做比较
    -- return false
    -- end
    local typeA = 0
    local typeB = 0
    local schemeItemA = Schemes:GetGoodsConfig(goodIDA)
    if schemeItemA then
        typeA = schemeItemA.SubType
    end
    local schemeItemB = Schemes:GetGoodsConfig(goodIDB)
    if schemeItemB then
        typeB = schemeItemB.SubType
    end
    local smeltLvA = entityA:GetProperty(EQUIPMENT_FIELD.EQUIPMENT_FIELD_STARNUM)
    local smeltLvB = entityB:GetProperty(EQUIPMENT_FIELD.EQUIPMENT_FIELD_STARNUM)
    local qualityA = schemeItemA.QualityLevel
    local qualityB = schemeItemB.QualityLevel
    if smeltLvA ~= smeltLvB then
        -- 强化等级高的在前
        return smeltLvA > smeltLvB
    end
    if qualityA ~= qualityB then
        -- 品质颜色高的在前
        if qualityA == nil then
            return false
        end
        if qualityB == nil then
            return true
        end
        return qualityA > qualityB
    end
    if typeA ~= typeB then
        -- 部位ID小的在前
        return typeA < typeB
    end
    if goodIDA ~= goodIDB then
        -- 物品ID排序，ID大的在前
        return goodIDA > goodIDB
    end

    return false
end

--------------------------------------------------------------------
-- 品质颜色排序
--------------------------------------------------------------------
local QuaSortFunc = function(bagIndexA, bagIndexB)
    local self = m
    local entityA = EntityModule:GetEntity(self.skepBag[bagIndexA])
    local entityB = EntityModule:GetEntity(self.skepBag[bagIndexB])
    if not entityA or not entityB then
        return false
    end
    local goodIDA = entityA:GetProperty(ENTITY_FIELD.ENTITY_FIELD_ID)
    local goodIDB = entityB:GetProperty(ENTITY_FIELD.ENTITY_FIELD_ID)
    -- if not HelperL.IsEuipType(goodIDA) or not HelperL.IsEuipType(goodIDB) then
    ---- 非装备类物品不做比较
    -- return false
    -- end

    local schemeItemA = Schemes:GetGoodsConfig(goodIDA)
    local schemeItemB = Schemes:GetGoodsConfig(goodIDB)

    local qualityA = schemeItemA.Quality > 0 and schemeItemA.Quality or schemeItemA.QualityLevel
    local qualityB = schemeItemB.Quality > 0 and schemeItemB.Quality or schemeItemB.QualityLevel
    -- print(qualityA.."   "..qualityB)
    if not qualityA or not qualityB then
        return false
    end
    if qualityA ~= qualityB then
        -- 品质颜色高的在前
        -- if qualityA == nil then return false end
        -- if qualityB == nil then return true end
        return qualityA > qualityB
    end
    -- if smeltLvA ~= smeltLvB then
    ---- 强化等级高的在前
    -- return smeltLvA > smeltLvB
    -- end
    -- if typeA ~= typeB then
    ---- 部位ID小的在前
    -- return typeA < typeB
    -- end
    if goodIDA ~= goodIDB then
        -- 物品ID排序，ID大的在前
        return goodIDA > goodIDB
    end

    local smeltLvA = entityA:GetProperty(EQUIPMENT_FIELD.EQUIPMENT_FIELD_STARNUM)
    local smeltLvB = entityB:GetProperty(EQUIPMENT_FIELD.EQUIPMENT_FIELD_STARNUM)
    if smeltLvA ~= smeltLvB then
        -- 强化等级高的在前
        return smeltLvA > smeltLvB
    end

    local typeA = 0
    local typeB = 0
    if schemeItemA then
        typeA = schemeItemA.SubType
    end
    if schemeItemB then
        typeB = schemeItemB.SubType
    end
    if typeA ~= typeB then
        -- 部位ID小的在前
        return typeA < typeB
    end
    return false
end

--------------------------------------------------------------------
-- 部位ID排序
--------------------------------------------------------------------
local TypeSortFunc = function(bagIndexA, bagIndexB)
    local self = m
    local entityA = EntityModule:GetEntity(self.skepBag[bagIndexA])
    local entityB = EntityModule:GetEntity(self.skepBag[bagIndexB])
    if not entityA or not entityB then
        return false
    end
    local goodIDA = entityA:GetProperty(ENTITY_FIELD.ENTITY_FIELD_ID)
    local goodIDB = entityB:GetProperty(ENTITY_FIELD.ENTITY_FIELD_ID)
    if not HelperL.IsEuipType(goodIDA) or not HelperL.IsEuipType(goodIDB) then
        -- 非装备类物品不做比较
        return false
    end
    local typeA = 0
    local typeB = 0
    local schemeItemA = Schemes:GetGoodsConfig(goodIDA)
    if schemeItemA then
        typeA = schemeItemA.SubType
    end
    local schemeItemB = Schemes:GetGoodsConfig(goodIDB)
    if schemeItemB then
        typeB = schemeItemB.SubType
    end
    local smeltLvA = entityA:GetProperty(EQUIPMENT_FIELD.EQUIPMENT_FIELD_STARNUM)
    local smeltLvB = entityB:GetProperty(EQUIPMENT_FIELD.EQUIPMENT_FIELD_STARNUM)
    local qualityA = schemeItemA.QualityLevel
    local qualityB = schemeItemB.QualityLevel
    if typeA ~= typeB then
        -- 部位ID小的在前
        return typeA < typeB
    end
    if qualityA ~= qualityB then
        -- 品质颜色高的在前
        return qualityA > qualityB
    end
    if smeltLvA ~= smeltLvB then
        -- 强化等级高的在前
        return smeltLvA > smeltLvB
    end
    if goodIDA ~= goodIDB then
        -- 物品ID排序，ID大的在前
        return goodIDA > goodIDB
    end

    return false
end

--------------------------------------------------------------------
-- 点击排序按钮
--------------------------------------------------------------------
function m:OnClickSort()
    if self.curFilterType then
        local sortFunc = {
            [SET.SmeltSort] = SmeltSortFunc,
            [SET.QuaSort] = QuaSortFunc,
            [SET.TypeSort] = TypeSortFunc
        }
        -- 三种排序规则轮流使用
        if not self.sortOrder then
            self.sortOrder = SET.QuaSort
        else
            -- if self.sortOrder == SET.QuaSort then
            -- self.sortOrder = SET.SmeltSort
            -- self.objList.Txt_Sort.text = GetGameText(luaID, 17)
            -- else
            -- self.sortOrder = SET.QuaSort
            -- self.objList.Txt_Sort.text = GetGameText(luaID, 18)
            -- end
        end

        -- if not self.sortOrder then
        -- self.sortOrder = SET.SmeltSort - 1
        -- end
        -- if self.sortOrder >= SET.TypeSort then
        -- self.sortOrder = SET.SmeltSort
        -- else
        -- self.sortOrder = self.sortOrder + 1
        -- end
        local curGoodList = self.dataGoods[self.curFilterType]
        table.sort(curGoodList, sortFunc[self.sortOrder])
    end
end

function m.OnBagToggleValueChange(isOn, curIndex)
    local self = m
    self:UpdateBag()
end

--------------------------------------------------------------------
-- 细胞仓库
--------------------------------------------------------------------
function m:OnClickFeiJiCangKu()
    local self = m
    self:CloseSelf()
    if UIManager:IsWndOpen(WndID.Airplane) then
        return
    end
    UIManager:OpenWnd(WndID.Airplane)
end

--------------------------------------------------------------------
-- 红点逻辑
--------------------------------------------------------------------
function m:RedDotLogic(slotItem, goodsID)
    if HelperL.IsEuipType(goodsID) then
        local typeA = 0
        local schemeItem = Schemes:GetGoodsConfig(goodsID)
        if schemeItem then
            typeA = schemeItem.SubType
            local qualityA = schemeItem.Quality -- entity:GetProperty(EQUIPMENT_FIELD.EQUIPMENT_FIELD_QUALITY)
            -- 如果背包品质为0 也不做对比
            if qualityA > 0 then
                -- 实时获取玩家装备信息
                for euipType, slotItemEquip in pairs(self.equipSlots) do
                    if slotItemEquip then
                        local eUIDEquip = SkepModule.GetEquipSkep()[euipType]
                        local entityEquip = EntityModule:GetEntity(eUIDEquip)
                        if entityEquip then
                            local itemID = entityEquip:GetProperty(ENTITY_FIELD.ENTITY_FIELD_ID)
                            local schemeItemEquip = Schemes:GetGoodsConfig(itemID)
                            local quality = entityEquip:GetProperty(EQUIPMENT_FIELD.EQUIPMENT_FIELD_QUALITY)
                            if schemeItemEquip then
                                if schemeItemEquip.SubType == typeA then
                                    if qualityA > quality then
                                        slotItem:SetRedDot(true)
                                    else
                                        slotItem:SetRedDot(false)
                                    end
                                end
                            else
                                slotItem:SetRedDot(false)
                            end
                            -- else
                            -- 	slotItem:SetRedDot(false)
                        end
                    else
                        slotItem:SetRedDot(false)
                    end
                end
            else
                slotItem:SetRedDot(false)
            end
        else
            slotItem:SetRedDot(false)
        end
    else
        slotItem:SetRedDot(false)
    end
end

function m:OpenBatchCompoundUI(batchType)
    self.batchCompoundUI = self.objList.Dir_BatchCompound
    self.batchCompoundObjs = self.objList.BatchCompound
    self.batchCompoundUI.gameObject:SetActive(true)
    self.curBatchType = batchType

    if self.curBatchType == BatchType.compound then
        self.batchCompoundObjs.Txt_BatchCompoundTitle.text = GetGameText(luaID, 23)
        self.batchCompoundObjs.Txt_BeginCompound.text = GetGameText(luaID, 25)
        self.batchCompoundObjs.Txt_BatchCompoundDes.text = GetGameText(luaID, 28)
    else
        self.batchCompoundObjs.Txt_BatchCompoundTitle.text = GetGameText(luaID, 24)
        self.batchCompoundObjs.Txt_BeginCompound.text = GetGameText(luaID, 26)
        self.batchCompoundObjs.Txt_BatchCompoundDes.text = GetGameText(luaID, 29)
    end

    if self.selectSlotItemList then
        for index = 1, #self.selectSlotItemList do
            local item_toggle = self.selectSlotItemList[index]
            item_toggle.isOn = false
        end
    end
    if not self.selectSlotItemList or #self.selectSlotItemList <= 0 then
        self.selectSlotItemList = {}
        for index = 1, 7 do
            local item = self:CreateSubItem(self.batchCompoundObjs.BatchSelectContent.transform,
                self.batchCompoundObjs.SelectSlotItem.gameObject)
            local item_toggle = item.transform:GetComponent('Toggle')
            item_toggle.isOn = false
            -- item_toggle.onValueChanged = self.OnToggleValueChange
            item_toggle.onValueChanged:AddListener(function(isOn)
                self.OnToggleValueChange(isOn, index)
            end)
            local color = HelperL.GetQualityColorRGBA(index - 1)
            item.Img_Bg.color = color
            table.insert(self.selectSlotItemList, item_toggle)
        end
    end
    if not self.enableBatchCompoundItemList then -- 可以批量合成
        self.enableBatchCompoundItemList = {}
    end
    if not self.hadBatchCompoundItemList then -- 已经批量合成
        self.hadBatchCompoundItemList = {}
    else
        for i, v in ipairs(self.hadBatchCompoundItemList) do
            GameObject.Destroy(v.gameObject)
        end
        self.hadBatchCompoundItemList = {}
    end

    self:UpdataBatchCompound()
    self.batchCompoundObjs.Btn_BatchCompoundMask.onClick:RemoveAllListeners()
    self.batchCompoundObjs.Btn_BatchCompoundMask.onClick:AddListenerEx(self.OnClickCloseBatchCompoundUI)
    self.batchCompoundObjs.Btn_BeginCompound.onClick:RemoveAllListeners()
    self.batchCompoundObjs.Btn_BeginCompound.onClick:AddListenerEx(self.OnClickBatchCompound)

    self.beginCompounding = false
    self:SetBatchCompoundBtnEnable(not self.beginCompounding)
end

function m:SetBatchCompoundBtnEnable(enable)
    if enable then
        self.batchCompoundObjs.Shadow_BeginCompound:GetComponent("Image").color = HelperL.btn_color
        self.batchCompoundObjs.Base_BeginCompound:GetComponent("Image").color = HelperL.btn_base_color
        self.batchCompoundObjs.Main_BeginCompound:GetComponent("Image").color = HelperL.btn_color
    else
        self.batchCompoundObjs.Shadow_BeginCompound:GetComponent("Image").color = HelperL.color_gray
        self.batchCompoundObjs.Base_BeginCompound:GetComponent("Image").color = HelperL.color_gray
        self.batchCompoundObjs.Main_BeginCompound:GetComponent("Image").color = HelperL.color_gray
    end
end

--------------------------------------------------------------------
-- 排序
--------------------------------------------------------------------
local BatchCompoundSortFunc = function(bagIndexA, bagIndexB)
    local self = m
    -- print('bagIndexA:'..bagIndexA..'   bagIndexB:'..bagIndexB)
    local entityA = EntityModule:GetEntity(self.skepBag[bagIndexA])
    local entityB = EntityModule:GetEntity(self.skepBag[bagIndexB])
    if not entityA or not entityB then
        return false
    end
    local goodIDA = entityA:GetProperty(ENTITY_FIELD.ENTITY_FIELD_ID)
    local goodIDB = entityB:GetProperty(ENTITY_FIELD.ENTITY_FIELD_ID)

    local schemeItemA = Schemes:GetGoodsConfig(goodIDA)
    local schemeItemB = Schemes:GetGoodsConfig(goodIDB)

    local qualityA = schemeItemA.Quality > 0 and schemeItemA.Quality or schemeItemA.QualityLevel
    local qualityB = schemeItemB.Quality > 0 and schemeItemB.Quality or schemeItemB.QualityLevel
    -- print('qualityA:'..qualityA..'   qualityB:'..qualityB)
    if not qualityA or not qualityB then
        return false
    end
    if qualityA ~= qualityB then
        -- 品质颜色高的在前
        return qualityA > qualityB
    end

    return false
end

function m:UpdataBatchCompound()
    for i, v in ipairs(self.enableBatchCompoundItemList) do
        GameObject.Destroy(v.gameObject)
    end
    self.enableBatchCompoundItemList = {}
    self.enableBatchCount = 0
    local curGoodList = self.dataGoods[SET.AllFilter]
    self.batchResolveReward = {}
    local moneyTip = ''
    if self.curBatchType == BatchType.compound then
        curGoodList = self.dataGoods[SET.GoodFilter]
        moneyTip = GetGameText(luaID, 10)
    end
    if self.curBatchType == BatchType.resolve then
        curGoodList = self.dataGoods[SET.EuipFilter]
        moneyTip = GetGameText(luaID, 27)
    end
    table.sort(curGoodList, BatchCompoundSortFunc)
    local totalCostMoney = 0
    self.isEnoughMoney = false
    for index = #curGoodList, 1, -1 do
        local bagIndex = curGoodList[index]
        if bagIndex then
            local eUID = self.skepBag[bagIndex]
            if eUID then
                local entity = EntityModule:GetEntity(eUID)
                if entity then
                    local goodsID = entity:GetProperty(ENTITY_FIELD.ENTITY_FIELD_ID)
                    local goodsNum = entity:GetProperty(GOODS_FIELD.GOODS_FIELD_QTY)
                    local goodsConfig = Schemes:GetGoodsConfig(goodsID)
                    totalCostMoney = totalCostMoney + self:UpdateBatchItem(entity, goodsConfig, goodsID, goodsNum)
                end
            end
        end
    end
    print('totalCostMoney=' .. totalCostMoney)
    -- 消耗总金币量
    local curMoney = SkepModule:GetGoodsCount(4)
    if totalCostMoney < curMoney then
        self.isEnoughMoney = true
    end

    local money_str = string.format('<color=yellow>%d/%d</color>', totalCostMoney, curMoney)
    if not self.isEnoughMoney then
        money_str = string.format('<color=yellow>%d/%d</color>', totalCostMoney, curMoney)
    end

    if self.curBatchType == BatchType.resolve then
        money_str = totalCostMoney
        self.isEnoughMoney = true
    end

    self.batchCompoundObjs.Txt_BatchTotalMoney.text = string.format(moneyTip, money_str)
end

function m:UpdateBatchItem(entity, goodsConfig, goodsID, goodsNum)
    local totalCostMoney = 0
    if not goodsConfig then
        return totalCostMoney
    end
    if self.curBatchType == BatchType.compound then -- 批量合成
        if goodsConfig.Exchangeid > 0 then
            local goodsItem = self:CreateSubItem(self.batchCompoundObjs.EnableBatchCompoundContent.transform,
                self.batchCompoundObjs.EnableCompoundItem.gameObject)
            table.insert(self.enableBatchCompoundItemList, goodsItem)
            goodsItem.entity = entity
            goodsItem.goodsConfig = goodsConfig
            goodsItem.Txt_Item_Name.text = goodsConfig.GoodsName
            AtlasManager:AsyncGetSprite(goodsConfig.IconID, goodsItem.Img_Item_Icon)
            goodsItem.Sld_Proccese.value = 0
            local exchangeItem = Schemes.EquipExchange:Get(goodsConfig.Exchangeid)
            local compoundCount = 0
            if exchangeItem then
                compoundCount = math.floor(goodsNum / exchangeItem.NeedGoodsNum)
                local costGoods_str = ''
                if compoundCount > 0 then
                    costGoods_str = string.format('<color=yellow>%d/%d</color>', goodsNum, exchangeItem.NeedGoodsNum)
                else
                    costGoods_str = string.format('<color=yellow>%d/%d</color>', goodsNum, exchangeItem.NeedGoodsNum)
                end
                goodsItem.Txt_Item_Num.text = string.format(GetGameText(luaID, 9), costGoods_str)
                local quality = goodsConfig.Quality
                local color = HelperL.GetQualityColorRGBA(quality)
                goodsItem.Img_Item_Bg.color = color
                goodsItem.Txt_Item_Name.color = color
                HelperL.SetImageMaterial(goodsItem.Img_vfx, quality)
                local index = quality + 1
                if index > 7 then
                    index = 7
                end
                local selectSlotItem = self.selectSlotItemList[index]
                local select_ison = compoundCount > 0 and selectSlotItem.isOn
                goodsItem.Img_Enable.gameObject:SetActive(select_ison)
                goodsItem.IsSelecting = select_ison
                if select_ison then
                    self.enableBatchCount = self.enableBatchCount + 1
                end
                if select_ison then
                    totalCostMoney = totalCostMoney + exchangeItem.CostMoney * compoundCount
                end
            end
        end
    else -- 批量分解
        if goodsConfig.UpgradeExp > 0 then
            local goodsItem = self:CreateSubItem(self.batchCompoundObjs.EnableBatchCompoundContent.transform,
                self.batchCompoundObjs.EnableCompoundItem.gameObject)
            table.insert(self.enableBatchCompoundItemList, goodsItem)
            goodsItem.entity = entity
            goodsItem.goodsConfig = goodsConfig
            goodsItem.Txt_Item_Name.text = goodsConfig.GoodsName
            AtlasManager:AsyncGetSprite(goodsConfig.IconID, goodsItem.Img_Item_Icon)
            goodsItem.Sld_Proccese.value = 0
            goodsItem.Txt_Item_Num.text = string.format(GetGameText(luaID, 21), goodsNum)
            local quality = goodsConfig.QualityLevel
            local color = HelperL.GetQualityColorRGBA(quality)
            goodsItem.Img_Item_Bg.color = color
            goodsItem.Txt_Item_Name.color = color
            HelperL.SetImageMaterial(goodsItem.Img_vfx, quality)

            local index = quality + 1
            if index > 7 then
                index = 7
            end
            local selectSlotItem = self.selectSlotItemList[index]
            local select_ison = selectSlotItem.isOn
            goodsItem.Img_Enable.gameObject:SetActive(select_ison)
            if select_ison then
                self.enableBatchCount = self.enableBatchCount + 1
            end
            goodsItem.IsSelecting = select_ison

            if select_ison then
                local starNum = entity:GetProperty(EQUIPMENT_FIELD.EQUIPMENT_FIELD_STARNUM)
                local decompositionItem = Schemes.EquipDecomposition:Get(goodsConfig.UpgradeExp, starNum)
                if decompositionItem then
                    local exchangeItem = Schemes.EquipExchange:Get(decompositionItem.ExchangeID)
                    local compoundCount = 0
                    if exchangeItem then
                        local needPrizeType = exchangeItem.PrizeType
                        for i = 1, 11 do
                            local bindingPrizeStr = string.format("BindingPrize%d", i - 1)
                            local goodsID = exchangeItem[bindingPrizeStr]
                            if goodsID > 0 then
                                local prizeStr = string.format("Prize%d", i - 1)
                                local getGoodsNum = exchangeItem[prizeStr]
                                if goodsID == 4 then
                                    totalCostMoney = totalCostMoney + getGoodsNum
                                end
                                if self.batchResolveReward[goodsID] then
                                    self.batchResolveReward[goodsID] = self.batchResolveReward[goodsID] + getGoodsNum
                                else
                                    self.batchResolveReward[goodsID] = getGoodsNum
                                end
                            end
                        end
                    end
                end
            end
        end
    end
    return totalCostMoney
end

function m.OnToggleValueChange(isOn, curIndex)
    local self = m
    self:UpdataBatchCompound()
end

function m.OnClickBatchCompound()
    local self = m
    if not self.isEnoughMoney then
        HelperL.ShowMessage(TipType.FlowText, GetGameText(luaID, 11))
        return
    end
    if self.beginCompounding then
        return
    end
    self.beginCompounding = true
    if not self.hadBatchCompoundItemList then -- 已经批量操作
        self.hadBatchCompoundItemList = {}
    else
        for i, v in ipairs(self.hadBatchCompoundItemList) do
            GameObject.Destroy(v.gameObject)
        end
        self.hadBatchCompoundItemList = {}
    end

    if self.curBatchType == BatchType.compound then
        local selectCount = 0
        for i, v in ipairs(self.selectSlotItemList) do
            if v.isOn then
                selectCount = selectCount + 1
            end
        end
        if selectCount > 0 then
            self.LoopRequestExchange(1)
        else
            self.beginCompounding = false
            HelperL.ShowMessage(TipType.FlowText, '请选择需合成的品质')
        end
    end
    if self.curBatchType == BatchType.resolve then
        self.BatchResolve()
    end
    self:SetBatchCompoundBtnEnable(not self.beginCompounding)
end

function m.LoopRequestExchange(index)
    local self = m
    if index < 1 or index > #self.enableBatchCompoundItemList then
        self.beginCompounding = false
        self:SetBatchCompoundBtnEnable(not self.beginCompounding)
        -- self:UpdataBatchCompound()--延迟更新
        local delayUpdataBatch = TimerEx.New(function()
            self:UpdataBatchCompound() -- 延迟更新
            delayUpdataBatch = nil
        end, 0.5, 1)
        delayUpdataBatch:Start()
        return
    end
    local goodsID = 0
    local compoundCount = 0
    -- print('len ='..#self.enableBatchCompoundItemList)
    local goodsItem = self.enableBatchCompoundItemList[index]
    if goodsItem.entity then
        goodsID = goodsItem.entity:GetProperty(ENTITY_FIELD.ENTITY_FIELD_ID)
        -- print('goodsID='..goodsID)
        local goodsNum = goodsItem.entity:GetProperty(GOODS_FIELD.GOODS_FIELD_QTY)
        if goodsItem.IsSelecting then
            local exchangeItem = Schemes.EquipExchange:Get(goodsItem.goodsConfig.Exchangeid)
            if exchangeItem then
                -- print('goodsNum='..goodsNum..'   exchangeItem.NeedGoodsNum='..exchangeItem.NeedGoodsNum)
                compoundCount = math.floor(goodsNum / exchangeItem.NeedGoodsNum)
            end
        end
    end
    print('compoundCount:' .. compoundCount)
    if compoundCount == 0 then
        self.LoopRequestExchange(index + 1)
        return
    end
    SoundManager:PlaySound(1011)
    local newGoodsNum = compoundCount
    self.delayTime = TimerEx.New(function()
        compoundCount = compoundCount - 1
        if compoundCount < 0 then
            print('next')
            SoundManager:PlaySound(1012)
            self.delayTime:Stop()
            self.LoopRequestExchange(index + 1)
            self.CompoundComplete(goodsID, newGoodsNum)
            return
        end
        print('require:' .. compoundCount)
        goodsItem.Sld_Proccese.value = 1 - compoundCount / newGoodsNum
        local str_req = string.format("LuaRequestEquipExchange?equip=%s", goodsItem.entity.uid)
        LuaModule.RunLuaRequest(str_req, self.OnCompoundBack)
    end, 0.01, -1)
    self.delayTime:Start()
end

function m.BatchResolve()
    local self = m
    self.enableBatchResolveLen = 0
    local haveSelecting = false
    for id, item in ipairs(self.enableBatchCompoundItemList) do
        if item.IsSelecting then
            haveSelecting = true
            local str_req = string.format("LuaRequestGemExchange?equip=%s", item.entity.uid)
            LuaModule.RunLuaRequest(str_req, self.OnResolveBack)
        end
    end
    if not haveSelecting then
        self.beginCompounding = false
        self:SetBatchCompoundBtnEnable(not self.beginCompounding)
        HelperL.ShowMessage(TipType.FlowText, '请勾选需要分解的品质')
    end
end

--------------------------------------------------------------------
-- 分解返回
--------------------------------------------------------------------
function m.OnResolveBack(resultCode, content)
    local self = m
    self.enableBatchResolveLen = self.enableBatchResolveLen + 1
    if resultCode == RESULT_CODE.RESULT_COMMON_SUCCEED then

    else
        self.beginCompounding = false
        HelperL.ShowMessage(TipType.FlowText, ResultCode:GetResultCodeStr(resultCode))
    end
    if self.enableBatchResolveLen >= self.enableBatchCount then
        for id, num in pairs(self.batchResolveReward) do
            print('id=' .. id .. '   num=' .. num)
            local item_slot =
                _GAddSlotItem(self.batchCompoundObjs.HadBatchCompoundContent.transform, nil, nil, 140, 140)
            item_slot:SetItemID(id)
            item_slot:SetCount(num)
            table.insert(self.hadBatchCompoundItemList, item_slot)
        end
        self.beginCompounding = false
        self:UpdataBatchCompound()
    end
    self:SetBatchCompoundBtnEnable(not self.beginCompounding)
end

function m.CompoundComplete(compoundgoodsID, newGoodsNum)
    local self = m

    local newGoodsID
    local goodsConfig = Schemes:GetGoodsConfig(compoundgoodsID)
    if goodsConfig.Exchangeid > 0 then
        local exchangeItem = Schemes.EquipExchange:Get(goodsConfig.Exchangeid)
        if exchangeItem then
            newGoodsID = exchangeItem.GoodsID
        end
    end
    if not newGoodsID then
        return
    end
    local item_slot = _GAddSlotItem(self.batchCompoundObjs.HadBatchCompoundContent.transform, nil, nil, 140, 140)
    item_slot:SetItemID(newGoodsID)
    item_slot:SetCount(newGoodsNum)
    table.insert(self.hadBatchCompoundItemList, item_slot)
    -- local newGoodsConfig = Schemes:GetGoodsConfig(newGoodsID)
    -- if newGoodsConfig then item_slot:SetName(newGoodsConfig.GoodsName) end

    -- local curGoodList = self.dataGoods[SET.AllFilter]
    -- for index = 1, #curGoodList do
    -- local bagIndex = curGoodList[index]
    -- if bagIndex then
    -- local eUID = self.skepBag[bagIndex]
    -- if eUID then
    -- local entity = EntityModule:GetEntity(eUID)
    -- if entity then
    -- local goodsID = entity:GetProperty(ENTITY_FIELD.ENTITY_FIELD_ID)

    -- if goodsID == newGoodsID then
    -- local newGoodsNum = entity:GetProperty(GOODS_FIELD.GOODS_FIELD_QTY)
    ----local newGoodsConfig = Schemes:GetGoodsConfig(newGoodsID)
    -- print('newGoodsNum:'..newGoodsNum)
    -- local item_slot = _GAddSlotItem(self.batchCompoundObjs.HadBatchCompoundContent.transform, nil, nil, 140, 140)
    -- item_slot:SetEntity(entity)
    -- item_slot:SetCount(newGoodsNum)
    -- end

    -- end
    -- end
    -- end
    -- end
end

function m.OnCompoundBack()

end

function m.OnClickCloseBatchCompoundUI()
    local self = m
    self.batchCompoundUI.gameObject:SetActive(false)
    self:OnClickSort()
end

--------------------------------------------------------------------
-- 窗口关闭
--------------------------------------------------------------------
function m.OnClickClose()
    m:PlayBtnSound()
    UIManager:CloseWndByID(45)
    UIManager:CloseWndByID(WndID.AirPlaneNew)
    m:CloseSelf()
end

--------------------------------------------------------------------
-- 兑换
--------------------------------------------------------------------
function m.OnClickExchange(index)
    local self = m
    if self.IsBoxOpening then -- 正在开箱子
        HelperL.ShowMessage(TipType.FlowText, '正在开启箱子中...')
        return
    end
    local boxItem = self.boxExchangeBagSlots[index]
    local costNum = boxItem.config.exchange
    local killNum = EntityModule.hero:GetProperty(PLAYER_FIELD.PLAYER_FIELD_MORAL)
    if killNum >= costNum then
        local str_req = string.format("LuaRequestExchangeBox?exchangeID=%s", boxItem.config.ID)
        LuaModule.RunLuaRequest(str_req, self.ExchangeCallback)
    else
        HelperL.ShowMessage(TipType.FlowText, '击杀怪物数量不足')
    end
end

function m.ExchangeCallback(resultCode, content)
    local self = m
    if resultCode == RESULT_CODE.RESULT_COMMON_SUCCEED then
        self:UpdataBoxBag()
    else
        HelperL.ShowMessage(TipType.FlowText, ResultCode:GetResultCodeStr(resultCode))
    end
end

local BoxSkinName = { 'defaulta', 'defaultc', 'defaultf', 'defaultb', 'defaultd', 'defaulth' }
--------------------------------------------------------------------
-- 选择宝箱
--------------------------------------------------------------------
function m.OnClickSelect(index)
    local self = m
    if self.IsBoxOpening then -- 正在开箱子
        HelperL.ShowMessage(TipType.FlowText, '正在开启箱子中...')
        return
    end
    if self.curSelectBoxIndex then -- 恢复正常icon
        local preBoxItem = self.boxExchangeBagSlots[self.curSelectBoxIndex]
        local preEntity = preBoxItem.entity
        if preEntity then
            preBoxItem:SetEntity(preEntity)
        else
            preBoxItem:SetItemID(preBoxItem.config.MedicamID)
        end

        preBoxItem.ImgSelecting.gameObject:SetActive(false)
    end

    self.curSelectBoxIndex = index
    if not self.curSelectBoxIndex then
        self.curSelectBoxIndex = 1
    end
    local boxItem = self.boxExchangeBagSlots[self.curSelectBoxIndex]
    local curBoxIconName = boxItem.Img_Icon.sprite.name
    curBoxIconName = string.gsub(curBoxIconName, '(Clone)', '')
    local len = string.len(curBoxIconName)
    local curIndex = string.sub(curBoxIconName, len - 2, len - 2)
    local boxLightIconName = 'BX-L0' .. curIndex
    boxItem:SetIcon(boxLightIconName)
    boxItem.ImgSelecting.gameObject:SetActive(true)

    local goodsID = boxItem.config.MedicamID
    local goodsConfig = Schemes:GetGoodsConfig(goodsID)
    if goodsConfig then
        local skepBoxBag = SkepModule:GetSkepByID(SKEPID.SKEPID_PACKET)
        local goodsCount = skepBoxBag:FastCount(goodsID)
        if not goodsCount then
            goodsCount = 0
        end

        if goodsCount <= 0 then
            self.objList.Shadow_Open:GetComponent("Image").color = HelperL.color_gray
            self.objList.Base_Open:GetComponent("Image").color = HelperL.color_gray
            self.objList.Main_Open:GetComponent("Image").color = HelperL.color_gray
        else
            self.objList.Shadow_Open:GetComponent("Image").color = HelperL.btn_color
            self.objList.Base_Open:GetComponent("Image").color = HelperL.btn_base_color
            self.objList.Main_Open:GetComponent("Image").color = HelperL.btn_color
        end
        self.objList.Txt_BoxOpenNum.text = string.format(GetGameText(luaID, 21), goodsCount)
        local tips = goodsConfig.TipsDes
        local tipsList = HelperL.Split(tips, ';')
        local tipLen = #tipsList
        if tipLen > 1 then
            if self.boxOpenTipList then
                for i, v in ipairs(self.boxOpenTipList) do
                    GameObject.Destroy(v.gameObject)
                end
            end

            local boxOpenRotaContenter = self.objList.BoxOpenRotaContenter.transform
            local boxOpenNumItem = self.objList.Txt_BoxOpenRota.gameObject
            self.boxOpenTipList = {}
            for i = 1, tipLen do
                local tip = tipsList[i]
                local tipItem = self:CreateSubItem(boxOpenRotaContenter, boxOpenNumItem)
                tipItem.transform:GetComponent('Text').text = tip
                table.insert(self.boxOpenTipList, tipItem)
            end
        end
    end

    if self.curSelectBoxIndex == 6 then
        self.objList.Skl_Box1.gameObject:SetActive(false)
        self.objList.Skl_Box2.gameObject:SetActive(true)
        self.curSpineBox = self.spineXianzi02
    else
        self.objList.Skl_Box1.gameObject:SetActive(true)
        self.objList.Skl_Box2.gameObject:SetActive(false)
        self.curSpineBox = self.spineXianzi01
    end
    self.curSpineBox.initialSkinName = BoxSkinName[self.curSelectBoxIndex]
    self.curSpineBox:Initialize(true)
    self.curSpineBox.AnimationState:SetAnimation(0, "idle2", true)
end

function m.OnClickOpenBox()
    local self = m
    if self.IsBoxOpening then -- 正在开箱子
        HelperL.ShowMessage(TipType.FlowText, '正在开启箱子中...')
        return
    end
    if not self.curSelectBoxIndex then
        self.curSelectBoxIndex = 1
    end
    local boxItem = self.boxExchangeBagSlots[self.curSelectBoxIndex]
    local entity = boxItem.entity
    if not entity then
        HelperL.ShowMessage(TipType.FlowText, string.format('%s数量为0', boxItem.config.Tips))
        return
    end
    local goodsNum = entity:GetProperty(GOODS_FIELD.GOODS_FIELD_QTY)
    if goodsNum <= 0 then
        HelperL.ShowMessage(TipType.FlowText, string.format('%s数量为0', boxItem.config.Tips))
        return
    end
    self.IsBoxOpening = true
    self.curSpineBox.AnimationState.Complete = self.curSpineBox.AnimationState.Complete + self.SpineOpenCallback
    self.curSpineBox.AnimationState:SetAnimation(0, "fire1", false)
end

function m.SpineOpenCallback(tracky)
    local self = m
    self.IsBoxOpening = false
    self.curSpineBox.AnimationState.Complete = self.curSpineBox.AnimationState.Complete - self.SpineOpenCallback
    local boxItem = self.boxExchangeBagSlots[self.curSelectBoxIndex]
    local entity = boxItem.entity
    if not entity then
        HelperL.ShowMessage(TipType.FlowText, string.format('%s数量为0', boxItem.config.Tips))
        return
    end
    local goodsNum = entity:GetProperty(GOODS_FIELD.GOODS_FIELD_QTY)
    local skepID = entity:GetProperty(GOODS_FIELD.GOODS_FIELD_SKEPID)
    local goodSkep = SkepModule:GetSkepByID(skepID)
    if not goodSkep then
        print('not goodSkep  skepID= ' .. skepID)
        return
    end
    local m = SkepMessage_pb.CS_Skep_UseGoods()
    local skepUID = goodSkep.skepUID
    m.SkepUID = skepUID
    m.GoodsUID = entity.uid
    m.Target = 0
    -- m.UseNum = self.curSchemItem.UseMenu == 1 and 1 or goodsNum
    m.UseNum = goodsNum
    print('goodSkep.skepUID=' .. goodSkep.skepUID .. '   goodsNum:' .. goodsNum)
    Premier.Instance:GetNetwork():SendFromLua(ENDPOINT.ENDPOINT_GAMECLIENT, ENDPOINT.ENDPOINT_GAMESERVER,
        MSG_MODULEID.MSG_MODULEID_SKEP, SkepMessage_pb.MSG_SKEP_USEGOODS, m:SerializeToString())
end

--------------------------------------------------------------------
-- 每秒更新
--------------------------------------------------------------------
function m:OnSecondUpdate()
    -- UIRole:RefreshEquipCollectItem()
end

--------------------------------------------------------------------
-- 窗口销毁
--------------------------------------------------------------------
function m:OnDestroy()

end

function m:PlayBtnSound()
    SoundManager:PlaySound(1005)
end

function m:GetAllGoodsList()
    return self.dataGoods[SET.EuipFilter]
end

function m:UpdataPackge()
    -- self:FilterGoods()
    self:OnClickSort()
    self:UpdateViewL()
    self:UpdateBag()
end

-- 打开物品的弹窗
function m.OnGetItem(uid)
    local self = m
    if LoginModule.isReconnecting then
        return
    end
    if not self.IsBoxBagFilter then -- 非宝箱界面
        return
    end

    if not self.openBoxDelayTimeUpdata then
        self.openBoxDelayTimeUpdata = TimerEx.New(function()
            self:UpdataBoxBag()
        end, 0.5, 1) -- 延迟更新
        self.openBoxDelayTimeUpdata:Start()
    end
end

-- 物品属性改变
function m.OnGoodsPropChange(uid, propID, oldValue, newValue)
    local self = m
    -- 非宝箱界面
    if not self.IsBoxBagFilter then
        return
    end
    if uid and propID and oldValue and newValue then
        if oldValue == 0 then
            return
        end
        local value = newValue - oldValue
        if value > 0 then
            if not self.openBoxDelayTimeUpdata then
                self.openBoxDelayTimeUpdata = TimerEx.New(function()
                    self:UpdataBoxBag()
                end, 0.5, 1) -- 延迟更新
                self.openBoxDelayTimeUpdata:Start()
            end
        end
    end
end

--------------------------------------------------------------------
-- 创建强化item
--------------------------------------------------------------------
function m:CreateQiangHua()
    self.EquipCollectItemList = {}
    local cardSkep = SkepModule:GetSkepByID(SKEPID.SKEPID_MONSTERCARD)
    local cfg
    for i, v in ipairs(EquipConfigIdList) do
        local item = self:CreateSubItem(self.objList.Content, self.objList.QiangHuaItem)
        item.data = v
        cfg = Schemes.Equipment:Get(v.equipID)
        item.transform.localPosition = Vector3.zero
        item.Txt_Desc.text = cfg.GoodsName
        item.Txt_QiangHua.text = GetGameText(luaID, 30)
        local entity = cardSkep:GetEntityByGoodsID(v.equipID)
        AtlasManager:AsyncGetSprite(self.IconBgList[i], item.Img_IconBg)
        item.Btn_QiangHua.onClick:AddListenerEx(function()
            m:PlayBtnSound()
            self:QiangHuaClick(i, entity)
        end)
        item.Btn_QiangHua.onLongPress:AddListenerEx(function()
            m:PlayBtnSound()
            self:QiangHuaClick(i, entity)
        end)
        item.Btn_QiangHua2.onClick:AddListenerEx(function()
            SoundManager:PlaySound(1001)
            self:QiangHuaClick(i, entity)
        end)
        item.Btn_QiangHua2.onLongPress:AddListenerEx(function()
            SoundManager:PlaySound(1001)
            self:QiangHuaClick(i, entity)
        end)
        table.insert(self.EquipCollectItemList, item)
    end
    self:RefreshEquipCollectItem()
end

--特殊解锁条件
function m.UnlockConditions(index)
    --解锁条件对象uid
    local uidList = {
        [1] = 2,
        [2] = 1,
    }
    local index2 = uidList[index]
    if not index2 then
        return true, ''
    end
    
    local uid1 = EquipConfigIdList[index].equipID
    local uid2 = EquipConfigIdList[index2].equipID

    local cardSkep = SkepModule:GetSkepByID(SKEPID.SKEPID_MONSTERCARD)
    local entity1 = cardSkep:GetEntityByGoodsID(uid1)
    local entity2 = cardSkep:GetEntityByGoodsID(uid2)
    if not entity1 or not entity2 then
        return true, ''
    end

    local lvA = math.floor(entity1:GetProperty(EQUIPMENT_FIELD.EQUIPMENT_FIELD_SMELTEXP) / 100)
    local lvB = math.floor(entity2:GetProperty(EQUIPMENT_FIELD.EQUIPMENT_FIELD_SMELTEXP) / 100)
    --K结算公式
    local get_K = function(A, B)
        local MIN = math.min
        local INT = math.floor
        local K = MIN(20 * INT(MIN(A, B) / 20 + 1), 20) + MIN(20 * INT(MIN(A, B) / 5), 100) + MIN(30 * INT(MIN(A, B) / 30), 400) + MIN(100 * INT(MIN(A, B) / 200), 2000)
        return K
    end
    local K = get_K(lvA, lvB)

    if lvA > (K + lvB - 1) then
        return false, string.format(GetGameText(luaID, 53), GetGameText(luaID, 53 + index2))
    end
    return true, ''
end

-- 更新数据
function m:RefreshEquipCollectItem()
    local cardSkep = SkepModule:GetSkepByID(SKEPID.SKEPID_MONSTERCARD)
    for i, item in ipairs(self.EquipCollectItemList) do
        local entity = cardSkep:GetEntityByGoodsID(item.data.equipID)
        local equipProp = ActorProp.ReadEquipProp(entity)
        local text = 0
        local value
        for _, p in ipairs(ActorProp.EffectType_Prop) do
            value = equipProp.StarProp[p.ActorProp] + equipProp.StarExpProp[p.ActorProp]
            if value > 0 then
                if p.PctEnd then
                    text = HelperL.Round(value / 100, 2, false) .. '%'
                else
                    text = math.floor(value)
                end
                item.Txt_Desc.text = p.Text_cn
            end
        end
        item.Txt_Value.text = text


        item.Btn_QiangHua.gameObject:SetActive(true)
        item.Btn_QiangHua2.gameObject:SetActive(true)
        item.Img_Ievel.gameObject:SetActive(false)

        local special, str = true, ''
        --原有解锁条件
        if i > 1 then
            local entity2 = cardSkep:GetEntityByGoodsID(EquipConfigIdList[i - 1].equipID)
            local level = math.floor(entity2:GetProperty(EQUIPMENT_FIELD.EQUIPMENT_FIELD_SMELTEXP) / 100)
            if level < item.data.unlockConditions then
                special = false
                str = string.format(GetGameText(luaID, 47 + i), item.data.unlockConditions)
            end
        end
        --特殊解锁条件
        if special then
            special, str = m.UnlockConditions(i)
        end
        if not special then
            item.Btn_QiangHua.gameObject:SetActive(false)
            item.Btn_QiangHua2.gameObject:SetActive(false)
            item.Txt_LevelLimit.text = str
            item.Img_Ievel.gameObject:SetActive(true)
        end

        local equipScheme = Schemes.Equipment:Get(item.data.equipID)
        local quality = entity:GetProperty(EQUIPMENT_FIELD.EQUIPMENT_FIELD_QUALITY)
        local starNum = entity:GetProperty(EQUIPMENT_FIELD.EQUIPMENT_FIELD_STARNUM)
        if starNum > 10 then
            quality = 1
            starNum = starNum - 10
        end
        item.Txt_Level.text = math.floor(entity:GetProperty(EQUIPMENT_FIELD.EQUIPMENT_FIELD_SMELTEXP) /
            100)
        local smeltScheme = Schemes.EquipSmelt:Get(equipScheme.SmeltID, quality, starNum)
        if not smeltScheme then return end
        item.Txt_FullLevel.gameObject:SetActive(false)
        if smeltScheme.LevelMaxExp == -1 then --满级
            item.Btn_QiangHua.gameObject:SetActive(false)
            item.Btn_QiangHua2.gameObject:SetActive(false)
            item.Txt_FullLevel.gameObject:SetActive(true)
        else
            item.RedDot:SetActive(RedDotCheckFunc:CheckRoleShuXingQiangHua(smeltScheme.CostMoney))
            local hasMoney = SkepModule:GetGoodsCount(4)
            self.CostMoneyList[i] = smeltScheme.CostMoney
            local showcoin = HelperL.GetChangeNum(smeltScheme.CostMoney)
            if hasMoney >= smeltScheme.CostMoney then
                AtlasManager:AsyncGetGoodsSprite(4, item.Img_Coin)
                item.Img_QiangHua.gameObject:SetActive(false)
                item.Btn_QiangHua.gameObject:GetComponent('Button').enabled = true
                item.Txt_NeedCoin.text = string.format(GetGameText(luaID, 31), showcoin)
            else
                AtlasManager:AsyncGetGoodsSprite(4, item.Img_Coin)
                item.Img_QiangHua.gameObject:SetActive(true)
                item.Btn_QiangHua.gameObject:GetComponent('Button').enabled = false
                item.Txt_NeedCoin.text = string.format(GetGameText(luaID, 32), showcoin)
            end
        end
    end
    m:UpdateViewL()
end

function m:QiangHuaClick(index, entity)
    --判断游戏币
    if HelperL.IsLackGoods(4, self.CostMoneyList[index]) then
        return
    end

    SoundManager:PlaySound(SoundID.ButtonSound)
    self.curEntityIndex = index
    local str_req = string.format("LuaRequestNewCardAddExp?card=%s&LoopTimes=%s", entity.uid, 0)
    LuaModule.RunLuaRequest(str_req, m.LevelCallback)
end

-- 进阶消息服务器响应回调函数
function m.LevelCallback(result, content)
    local self = m
    if result == RESULT_CODE.RESULT_COMMON_SUCCEED then
        m:RefreshEquipCollectItem()
    else
        HelperL.ShowMessage(TipType.FlowText, ResultCode:GetResultCodeStr(result, content))
    end
end

function m.OnClickShowShuXing()
    m:PlayBtnSound()
    local self = m
    self.IsPropShow = not self.IsPropShow
    self.objList.Img_Model.gameObject:SetActive(not self.IsPropShow)
end

function m.OnClickMask()
    local self = m
    if self.IsPropShow then
        self.IsPropShow = false
    end
end

return m
