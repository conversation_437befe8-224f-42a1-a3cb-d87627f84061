--[[
********************************************************************
    created:	2024/05/22
    author :	李锦剑
    purpose:    逻辑值封装，方便使用
    note   :    提示：只在 GamePlayerData.lua 和 LuaFunctionalModule.lua 中使用，其它地方不要直接使用！！！
*********************************************************************
--]]

-- 32位逻辑值
---@alias Bit
---| 0
---| 1

local ActorDataMgr = require 'ActorDataMgr'

local m = {}
LogicValue = m

--------------------------------------------------------------------
-- 按位获取逻辑值 32位
---@param value integer 逻辑值
---@param index integer 索引(索引从1开始)
---@return Bit
--------------------------------------------------------------------
function m.GetBit(value, index)
    index = tonumber(index) or -1
    if index < 1 or index > 32 then
        return 0
    end
    return Helper.GetBit(value, index - 1)
end

--------------------------------------------------------------------
-- 按位设置逻辑值 32位
---@param value integer 逻辑值
---@param index integer 索引(索引从1开始)
---@return integer
--------------------------------------------------------------------
function m.SetBit(value, index)
    index = tonumber(index) or -1
    if index < 1 or index > 32 then
        return 0
    end
    return Helper.SetBit(value, index - 1)
end

--------------------------------------------------------------------
---获取逻辑值
---@param dataCatalog integer 逻辑值ID
---@param index integer 索引(索引从1开始)
---@param onlyValid ?boolean 是否仅取效数据，默认：true
---@return integer
--------------------------------------------------------------------
function m.GetIntByIndex(dataCatalog, index, onlyValid)
    return ActorDataMgr.GetIntByIndex(dataCatalog, index, onlyValid)
end

--------------------------------------------------------------------
---保存逻辑值
---@param dataCatalog integer 逻辑值ID
---@param index integer 索引(索引从1开始)
---@param value integer 值
---@return boolean
--------------------------------------------------------------------
function m.SetIntByIndex(dataCatalog, index, value)
    return ActorDataMgr.SetIntByIndex(dataCatalog, index, value)
end

--------------------------------------------------------------------
---获取--副本管卡逻辑值
---@param stageType EctypeType_LV 副本类型
---@param index EctypeStage_Index 索引
--------------------------------------------------------------------
function m.GetEctypeStageLV(stageType, index)
    if stageType > EctypeType_LV.NONE and stageType < EctypeType_LV.MAX then
        if index > EctypeStage_Index.NONE and index < EctypeStage_Index.MAX then
            return ActorDataMgr.GetIntByIndex(stageType, index)
        end
    end
    return 0
end

--------------------------------------------------------------------
---设置--副本管卡逻辑值
---@param stageType EctypeType_LV 副本类型
---@param index EctypeStage_Index 索引
---@param value integer 逻辑值
---@return boolean
--------------------------------------------------------------------
function m.SetEctypeStageLV(stageType, index, value)
    if stageType > EctypeType_LV.NONE and stageType < EctypeType_LV.MAX then
        if index > EctypeStage_Index.NONE and index < EctypeStage_Index.MAX then
            return ActorDataMgr.SetIntByIndex(stageType, index, value)
        end
    end
    return false
end

--------------------------------------------------------------------
---获取--副本宝箱逻辑值
---@param stageID integer 副本ID
---@param index EctypeBox_Index 索引
---@return integer
--------------------------------------------------------------------
function m.GetEctypeBoxLV(stageID, index)
    local catMainStage = Schemes.CatMainStage:Get(stageID)
    if catMainStage then
        if index > EctypeBox_Index.NONE and index < EctypeBox_Index.MAX then
            local lv = ActorDataMgr.GetIntByIndex(catMainStage.EndCondition, index)
            return lv
        end
    end
    return 0
end

--------------------------------------------------------------------
---获取--副本宝箱逻辑值
---@param stageID integer 副本ID
---@param index EctypeBox_Index 索引
---@param value integer 逻辑值
---@return boolean
--------------------------------------------------------------------
function m.SetEctypeBoxLV(stageID, index, value)
    local catMainStage = Schemes.CatMainStage:Get(stageID)
    if catMainStage then
        if index > EctypeBox_Index.NONE and index < EctypeBox_Index.MAX then
            return ActorDataMgr.SetIntByIndex(catMainStage.EndCondition, index, value)
        end
    end
    return false
end

--------------------------------------------------------------------
---获取--装备数据逻辑值
---@param equipID integer 装备ID
---@param index EquipData_Index 索引
---@return integer
--------------------------------------------------------------------
function m.GetEquipDataLV(equipID, index)
    --if index > EquipData_Index.NONE and index < EquipData_Index.MAX then
        local equipment = Schemes.Equipment:Get(equipID)
        if equipment then
            local equipSmelt = Schemes.EquipSmelt:Get(equipment.SmeltID, equipment.Quality, equipment.StarNum)
            if equipSmelt then
                return HeroDataManager:GetLogicWord(SmeltIDSaveLogic[equipment.SmeltID],index)--ActorDataMgr.GetIntByIndex(equipSmelt.FrontStar, index)
            end
        end
    --end
    return 0
end

--------------------------------------------------------------------
---获取--装备数据逻辑值
---@param equipID integer 装备ID
---@param index EctypeBox_Index 索引
---@param value integer 逻辑值
---@return boolean
--------------------------------------------------------------------
function m.SetEquipDataLV(equipID, index, value)
    --if index > EquipData_Index.NONE and index < EquipData_Index.MAX then
        local equipment = Schemes.Equipment:Get(equipID)
        if equipment then
            local equipSmelt = Schemes.EquipSmelt:Get(equipment.SmeltID, equipment.Quality, equipment.StarNum)
            if equipSmelt then
                print("equipment.SmeltID = ", equipment.SmeltID)
                print("SmeltIDSaveLogic[equipment.SmeltID] = ", SmeltIDSaveLogic[equipment.SmeltID])
                local str_req2 = string.format('LuaRequestSetLogicWord?logicID=%d&logicIndex=%d&logicValue=%d', SmeltIDSaveLogic[equipment.SmeltID],index,value)
                LuaModule.RunLuaRequest(str_req2, function(resultCode2, content2)
                    print("resultCode2 = ", resultCode2)
                    if resultCode2 == RESULT_CODE_BASE.RESULT_COMMON_SUCCEED[1] then
                    end
                end)
                ActorDataMgr.SetIntByIndex(equipSmelt.FrontStar, index, value)
                return true
            end
        end
    --end
    return false
end

--------------------------------------------------------------------
---获取--战斗进度
---@param index BattleProgress_Index 索引
---@return integer
--------------------------------------------------------------------
function m.GetBattleProgress(index)
    if index > BattleProgress_Index.NONE and index < BattleProgress_Index.MAX then
        return ActorDataMgr.GetIntByIndex(ActorDataCatalog_Const.BattleProgress, index)
    end
    return 0
end

--------------------------------------------------------------------
---设置--战斗进度
---@param index BattleProgress_Index 索引
---@param value integer 逻辑值
---@return boolean
--------------------------------------------------------------------
function m.SetBattleProgress(index, value)
    if index > BattleProgress_Index.NONE and index < BattleProgress_Index.MAX then
        return ActorDataMgr.SetIntByIndex(ActorDataCatalog_Const.BattleProgress, index, value)
    end
    return false
end

--------------------------------------------------------------------
---获取--副本每日buffs
---@param stageID integer 副本ID
---@return integer[]|nil
--------------------------------------------------------------------
function m.GetEctypeBuffs(stageID)
    local catMainStage = Schemes.CatMainStage:Get(stageID)
    if not catMainStage then
        return nil
    end
    local dataCatalog = tonumber(catMainStage.ChapMonster) or 0
    if dataCatalog == 0 then
        return nil
    end
    return ActorDataMgr.GetIntArray(dataCatalog)
end

--------------------------------------------------------------------
---获取--体魄背包数据
---@return integer[]|nil
--------------------------------------------------------------------
function m.GetWeaponsKnapsack()
    return ActorDataMgr.GetIntArray(ActorDataCatalog_Const.WeaponsInStage)
end

--------------------------------------------------------------------
---设置--体魄背包数据
---@param value integer[] 新体魄背包数据
---@return boolean
--------------------------------------------------------------------
function m.SetWeaponsKnapsack(value)
    return ActorDataMgr.SetIntArray(ActorDataCatalog_Const.WeaponsInStage, value)
end

--------------------------------------------------------------------
---获取--装备数据逻辑值2
---@param smeltID integer 升星Id
---@param index EquipData_Index 索引
---@return integer
--------------------------------------------------------------------
function m.GetEquipDataLV2(smeltID, index)
    --if index > EquipData_Index.NONE and index < EquipData_Index.MAX then
        local equipSmeltStar = Schemes.EquipSmeltStar:GetEquipSmeltStarCfg(smeltID, 0)
        if equipSmeltStar then            
            return HeroDataManager:GetLogicWord(SmeltIDSaveLogic[smeltID],index)--ActorDataMgr.GetIntByIndex(equipSmeltStar.ActorDataCatalog, index)
        end
    --end
    return 0
end

--------------------------------------------------------------------
---设置--装备数据逻辑值2
---@param smeltID integer 升星Id
---@param index EctypeBox_Index 索引
---@param value integer 逻辑值
---@return boolean
--------------------------------------------------------------------
function m.SetEquipDataLV2(smeltID, index, value)
    --if index > EquipData_Index.NONE and index < EquipData_Index.MAX then
        local equipSmeltStar = Schemes.EquipSmeltStar:GetEquipSmeltStarCfg(smeltID, 0)
        if equipSmeltStar then
            local str_req2 = string.format('LuaRequestSetLogicWord?logicID=%d&logicIndex=%d&logicValue=%d', SmeltIDSaveLogic[smeltID],index,value)
            LuaModule.RunLuaRequest(str_req2, function(resultCode2, content2)
                print("resultCode2 = ", resultCode2)
                if resultCode2 == RESULT_CODE_BASE.RESULT_COMMON_SUCCEED[1] then
                end
            end)
            ActorDataMgr.SetIntByIndex(equipSmeltStar.ActorDataCatalog, index, value)
            return true
        end
    --end
    return false
end

--------------------------------------------------------------------
---获取--签到数据
---@param index SignInData_Index 索引
---@return integer
--------------------------------------------------------------------
function m.GetSignInData(index)
    if index > SignInData_Index.NONE and index < SignInData_Index.MAX then
        return ActorDataMgr.GetIntByIndex(ActorDataCatalog_Const.SignInData, index)
    end
    return 0
end

--------------------------------------------------------------------
---获取--装备数据逻辑值2
---@param index SignInData_Index 索引
---@param value integer 逻辑值
---@return boolean
--------------------------------------------------------------------
function m.SetSigninData(index, value)
    if index > SignInData_Index.NONE and index < SignInData_Index.MAX then
        return ActorDataMgr.SetIntByIndex(ActorDataCatalog_Const.SignInData, index, value)
    end
    return false
end

--------------------------------------------------------------------
---获取--任务逻辑值
---@param taskID integer 任务ID
---@param index TaskData_Index 索引
---@return integer
--------------------------------------------------------------------
function m.GetTaskDataLV(taskID, index)
    local task = Schemes.Task:Get(taskID)
    if task then
        if index > TaskData_Index.NONE and index < TaskData_Index.MAX then           
            --return ActorDataMgr.GetIntByIndex(task.Parameter7, index)
            return HeroDataManager:GetLogicData(LOGIC_DATA.DATA_TRUNK_TASK_CUR_SERIAL)
        end
    end
    return 0
end

--------------------------------------------------------------------
--设置--任务逻辑值
---@param taskID integer 任务ID
---@param index TaskData_Index 索引
---@param value integer 逻辑值
---@return boolean
--------------------------------------------------------------------
function m.SetTaskDataLV(taskID, index, value)
    local task = Schemes.Task:Get(taskID)
    if task then
        if index > TaskData_Index.NONE and index < TaskData_Index.MAX then
            print("SetTaskDataLV task.Parameter7 = ", task.Parameter7, " index = ", index, " setvalue = ",value)
            --return ActorDataMgr.SetIntByIndex(task.Parameter7, index, value)
            local str_req2 = string.format('LuaRequestSetLogicData?logicID=%d&logicValue=%d', LOGIC_DATA.DATA_TRUNK_TASK_CUR_SERIAL,value)
            LuaModule.RunLuaRequest(str_req2, function(resultCode2, content2)
                print("resultCode2 = ", resultCode2)
                if resultCode2 == RESULT_CODE_BASE.RESULT_COMMON_SUCCEED[1] then
                end
            end)
            return true
        end
    end
    return false
end
