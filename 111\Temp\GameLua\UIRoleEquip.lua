--[[
********************************************************************
    created:	2024/05/17
    author :	李锦剑
    purpose:    界面
*********************************************************************
--]]

local luaID = 'UIRoleEquip'

---@class UIRoleEquip:UIWndBase
local m = {}

--角色框
---@type Item_Role[]
m.Item_Role_List = {}
--------------------------------------------------------------------
-- 订阅事件列表
--------------------------------------------------------------------
function m.GetOpenEventList()
    return {
        [EventID.StoreList] = m.UpdateView,
        [EventID.StoreBuyItem] = m.UpdateView,
        [EventID.LogicDataChange] = m.UpdateView,
        [EventID.OnGoodsPropChange] = m.UpdateView,
        [EventID.OnHeroPropChange] = m.UpdateView,
        [EventID.ActorDataChanged] = m.UpdateView,
    }
end

--------------------------------------------------------------------
-- 初始化
--------------------------------------------------------------------
function m.OnCreate()
    m.lastTime = 0
    m.selectBulletLevel = 1
    ---属性框
    ---@type Item_Attribute[]
    m.Item_Attribute_List = {}
    ---属性框
    ---@type Item_Attribute4[]
    m.Item_Attribute_List2 = {}

    ---按钮
    ---@type Item_Tab[]
    m.Item_Tab_List = {}

    m.RegisterClickEvent()

    --体魄ID
    ---@type integer[]
    m.EquipWeaponIDList = {}
    for i, v in ipairs(Schemes.EquipWeapon.items) do
        if v.GroupID < 100 then
            table.insert(m.EquipWeaponIDList, v.ID)
        end
    end

    m.equipID = m.EquipWeaponIDList[1]

    return true
end

--------------------------------------------------------------------
-- 窗口开启
--------------------------------------------------------------------
function m:OnOpen()
    m.UpdateView()
    m.objList.Img_bjtb_02.gameObject:SetActive(true)
    m.objList.Btn_Role.gameObject:SetActive(true)
    m.objList.Btn_Role1.gameObject:SetActive(false)
    m.objList.Txt_Title.text = "伙伴"
    m.objList.Btn_BaoShi.gameObject:SetActive(false)
    m.objList.Btn_BaoShi1.gameObject:SetActive(true)
    --m.UpdateRole()
    --m.UpdateUIModel()
end

--------------------------------------------------------------------
--更新角色模型
--------------------------------------------------------------------
function m.UpdateUIModel()
    local weaponsKnapsack = GamePlayerData.ActorEquip:GetWeaponsKnapsack(1) or {}
    local equipID = weaponsKnapsack[1] or 0
    local cfg = Schemes.Equipment:Get(equipID)
    local level = GamePlayerData.ActorEquipNew:GetEquipUpgradeLevel(cfg.SmeltID)
    --m.objList.Txt_Level.text = GetGameText(luaID, 6) .. level .. CommonTextID.LEVEL
    --m.objList.Txt_EquipName.text = cfg.GoodsName
    --AtlasManager:AsyncGetSprite(HelperL.GetGunIcon(equipID), m.objList.Img_EquipIcon, true)

    --暂时屏蔽

    local gun = Schemes.Gun:Get(cfg.ConsignmentStyle)
    local bullet_List = Schemes.Bullet:GetByBulletId(gun.BulletId)
    local trans = m.objList.Obj_Model.transform
    for c = trans.childCount - 1, 0, -1 do
        if trans:GetChild(c) then
            GameObject.Destroy(trans:GetChild(c).gameObject)
        end
    end
    local parameter
    ResMgr.LoadGameObjectAsync("model/uiSpine/" .. bullet_List[1].Model, function(obj, parameter)
        local model = GameObject.Instantiate(obj, m.objList.Obj_Model.transform)
    end, parameter)
end

--------------------------------------------------------------------
--更新角色模型
--------------------------------------------------------------------
function m.ChangeUIModel(equipID, modeltransform)
    local cfg = Schemes.Equipment:Get(equipID)
    if not cfg then
        --print("11111 ChangeUIModel-装备配置不存在:", equipID)
        return
    end
    
    -- 根据EquipWeapon表的GroupID判断是否需要加载模型
    local equipWeaponConfig = Schemes.EquipWeapon:Get(equipID)
    if not equipWeaponConfig then
        --print("11111 ChangeUIModel-EquipWeapon配置不存在:", equipID)
        return
    end
    
    -- GroupID < 100的是主装备，需要加载模型；其他跳过
    if equipWeaponConfig.GroupID >= 100 then
        --print("11111 ChangeUIModel-装备", equipID, "GroupID=", equipWeaponConfig.GroupID, ">=100，跳过模型加载")
        return
    end
    
    --print("11111 ChangeUIModel-装备", equipID, "GroupID=", equipWeaponConfig.GroupID, "<100，准备加载模型")
    
    local level = GamePlayerData.ActorEquipNew:GetEquipUpgradeLevel(cfg.SmeltID)
    
    local gun = Schemes.Gun:Get(cfg.ConsignmentStyle)
    if not gun then
        --print("11111 ChangeUIModel-Gun配置不存在:", cfg.ConsignmentStyle)
        return
    end
    
    local bullet_List = Schemes.Bullet:GetByBulletId(gun.BulletId)
    if not bullet_List or #bullet_List == 0 then
        --print("11111 ChangeUIModel-Bullet配置不存在:", gun.BulletId)
        return
    end
    
    -- 检查modeltransform是否有效
    if not modeltransform then
        --print("11111 ChangeUIModel-modeltransform为空，跳过模型加载")
        return
    end
    
    local trans = modeltransform
    for c = trans.childCount - 1, 0, -1 do
        if trans:GetChild(c) then
            GameObject.Destroy(trans:GetChild(c).gameObject)
        end
    end
    
    local modelPath = "model/uiSpine/" .. bullet_List[1].Model
    
    local parameter
    ResMgr.LoadGameObjectAsync(modelPath, function(obj, parameter)
        if obj then
            local model = GameObject.Instantiate(obj, modeltransform)
            --print("11111 ChangeUIModel-主装备模型加载成功:", modelPath, "装备ID:", equipID, "GroupID:", equipWeaponConfig.GroupID)
        else
            -- 资源不存在时静默跳过，不报错
            --print("11111 ChangeUIModel-跳过不存在的主装备模型资源:", modelPath, "装备ID:", equipID, "GroupID:", equipWeaponConfig.GroupID)
        end
    end, parameter)
end

--------------------------------------------------------------------
--更新角色
--------------------------------------------------------------------
function m.UpdateRole()
    -- if not m.objList.Obj_Role.gameObject.activeSelf then
    --     return
    --
    if m.ItemRole ~= nil then
        m.ItemRole.Img_Select.gameObject:SetActive(false)
    end
    local dataList = m.EquipWeaponIDList
    for i, v in ipairs(dataList) do
        if not m.Item_Role_List[i] then
            m.Item_Role_List[i] = m.Creation_Item_Role(i)
        end
        m.Item_Role_List[i].UpdateData(v)
    end
end

function m.UpdateRoleInfo()
    if m.ItemRole ~= nil then
        m.ItemRole.Img_Select.gameObject:SetActive(false)
    end
    local dataList = m.EquipWeaponIDList
    for i, v in ipairs(dataList) do
        if not m.Item_Role_List[i] then
            m.Item_Role_List[i] = m.Creation_Item_Role(i)
        end
        m.Item_Role_List[i].UpdateDataInfo(v)
        m.Item_Role_List[i].UpdateBtns()
    end
end

function m.ShowBattleInfo(index)
    local item
    if m.selectIndex then
        item = m.Item_Role_List[m.selectIndex]
        item.com.Img_Bg.gameObject:SetActive(true)
        item.com.Img_Select.gameObject:SetActive(false)
    end
    item = m.Item_Role_List[index]
    item.com.Img_Bg.gameObject:SetActive(false)
    --item.com.Img_Select.gameObject:SetActive(true)

    m.selectIndex = index
end

m.ItemRole = nil
m.curEquipID = nil
--------------------------------------------------------------------
--创建属性框
--------------------------------------------------------------------
function m.Creation_Item_Role(index)
    ---@class Item_Role
    local item = {}
    item.index = index
    item.com = m:CreateSubItem(m.objList.Grid_Tab, m.objList.Item_Tab)
    m:AddClick(item.com.Btn_Click, function()
        --m.ShowBattleInfo(item.index)
        m.selectIndex = item.index
        if m.ItemRole == item.com then return end
        if m.ItemRole ~= nil then
            m.ItemRole.Img_Select.gameObject:SetActive(false)
        end
        m.ItemRole = item.com
        -- m.ItemRole.Img_Select.gameObject:SetActive(true)
        --m.ChangeUIModel(item.equipID)
        m.curEquipID = item.equipID
        m.equipID = item.equipID

        local weaponsKnapsack = GamePlayerData.ActorEquip:GetWeaponsKnapsack(1) or {}
        local curEquipID = weaponsKnapsack[1] or 0
        if curEquipID == item.equipID then
            m.objList.Txt_Battle.text = "已出战"
            m.objList.Txt_Battle1.text = "已出战"
            m.objList.Img_Battle.gameObject:SetActive(true)
        else
            local cfg = Schemes.Equipment:Get(item.equipID)
            local level = GamePlayerData.ActorEquipNew:GetEquipUpgradeLevel(cfg.SmeltID)
            m.objList.Txt_Battle.text = "出战"
            m.objList.Txt_Battle1.text = "出战"
            if level > 0 then
                m.objList.Img_Battle.gameObject:SetActive(false)
            else
                m.objList.Img_Battle.gameObject:SetActive(true)
            end
        end
        m.UpdateView()
    end)
    -- m:AddClick(item.com.Btn_Equip, function()
    --     if not item.equipID then return end
    --     -- if GamePlayerData.ActorEquip:IsWear(item.equipID, 1) then
    --     --     return
    --     -- end
    --     GamePlayerData.ActorEquip:ReplaceEquipIndex(item.equipID, 1, 1)
    -- end)
    m:AddClick(item.com.Btn_Active, function()
        if not item.equipID then return end
        m.Upgrade(item.equipID)
    end)
    -- m:AddClick(item.com.Btn_Select, function()
    --     if m.ItemRole == item.com then return end
    --     if m.ItemRole ~= nil then
    --         m.ItemRole.Img_Select.gameObject:SetActive(false)
    --     end
    --     m.ItemRole = item.com
    --     m.ItemRole.Img_Select.gameObject:SetActive(true)
    --     m.ChangeUIModel(item.equipID)
    -- end)
    ---更新数据
    ---@param equipID integer
    item.UpdateData = function(equipID)
        item.equipID = equipID
        if equipID then
            local cfg = Schemes.Equipment:Get(equipID)
            local level = GamePlayerData.ActorEquipNew:GetEquipUpgradeLevel(cfg.SmeltID)
            -- local equipSmeltStar = Schemes.EquipSmeltStar:GetEquipSmeltStarCfg(cfg.SmeltID, level)
            -- local attack = 0
            -- local hp = 0
            -- if equipSmeltStar then
            -- if cfg.QualityLevel < equipSmeltStar.AttackList.Length then
            --     attack = attack + equipSmeltStar.AttackList[cfg.QualityLevel]
            -- end
            -- if cfg.QualityLevel < equipSmeltStar.MaxHpList.Length then
            --     hp = hp + equipSmeltStar.MaxHpList[cfg.QualityLevel]
            -- end

            -- local num1 = 0
            -- if equipSmeltStar.CostGoodsID1 > 0 then
            --     num1 = SkepModule:GetGoodsCount(equipSmeltStar.CostGoodsID1)
            --     local color1 = num1 < equipSmeltStar.CostGoodsID1Num and UI_COLOR.Red or UI_COLOR.White
            --     item.com.Txt_Expend1.text = string.format("<color=%s>%s/%s</color>", color1,
            --         HelperL.GetChangeNum(num1),
            --         equipSmeltStar.CostGoodsID1Num)
            --     AtlasManager:AsyncGetGoodsSprite(equipSmeltStar.CostGoodsID1, item.com.Img_Expend1)
            --     item.com.Txt_Expend1.gameObject:SetActive(true)
            -- else
            --     item.com.Txt_Expend1.gameObject:SetActive(false)
            -- end

            --local num2 = 0
            --if equipSmeltStar.CostGoodsID2 > 0 then
            --num2 = SkepModule:GetGoodsCount(equipSmeltStar.CostGoodsID2)
            --local color2 = num2 < equipSmeltStar.CostGoodsID2Num and UI_COLOR.Red or UI_COLOR.White
            --item.com.Txt_Expend2.text = string.format("<color=%s>%s/%s</color>", color2,
            --    HelperL.GetChangeNum(num2),
            --    equipSmeltStar.CostGoodsID2Num)
            --AtlasManager:AsyncGetGoodsSprite(equipSmeltStar.CostGoodsID2, item.com.Img_Expend2)
            --item.com.Txt_Expend2.gameObject:SetActive(true)
            --else
            --item.com.Txt_Expend2.gameObject:SetActive(false)
            --end

            -- if num1 >= equipSmeltStar.CostGoodsID1Num and num2 >= equipSmeltStar.CostGoodsID2Num then
            --     HelperL.SetImageGray(item.com.Btn_Upgrade, false)
            -- else
            --     HelperL.SetImageGray(item.com.Btn_Upgrade, true)
            -- end

            --item.com.Btn_Upgrade.gameObject:SetActive(true)
            -- else
            --item.com.Btn_Upgrade.gameObject:SetActive(false)
            -- end

            AtlasManager:AsyncGetSprite(HelperL.GetGunIcon(equipID), item.com.Img_icon1, true)
            AtlasManager:AsyncGetSprite(HelperL.GetGunIcon(equipID), item.com.Img_icon2, true)
            item.com.Txt_Name1.text = cfg.GoodsName
            item.com.Txt_Name2.text = cfg.GoodsName
            item.com.Txt_Lv1.text = level .. "级"
            -- item.com.Txt_Level.text = GetGameText(luaID, 6) .. level .. CommonTextID.LEVEL
            -- item.com.Txt_Attack.text = GetGameText(luaID, 4) .. HelperL.GetChangeNum(attack)
            -- item.com.Txt_HP.text = GetGameText(luaID, 5) .. HelperL.GetChangeNum(hp)

            --获取最大等级
            local maxLevel = PropertyCompute.GetEquipSmeltStarMaxLevel(cfg.SmeltID)
            local weaponcfg = Schemes.EquipWeapon:Get(cfg.ID)
            local levelText = EntityModule.hero:GetProperty(CREATURE_FIELD.CREATURE_FIELD_LEVEL)
            local cureffectID5 = weaponcfg.EffectID5
            --print("levelText ==== "..levelText)
            --print("cureffectID5 ==== "..cureffectID5)
            if levelText >= cureffectID5 then
                item.com.Btn_Battle.gameObject:SetActive(true)
                item.com.Btn_Up.gameObject:SetActive(true)
                item.com.Btn_Active.gameObject:SetActive(false)
                item.com.Txt_Full.gameObject:SetActive(false)
                --是否是满级
                if level >= maxLevel then
                    item.com.Btn_Up.gameObject:SetActive(false)
                    item.com.Txt_Full.gameObject:SetActive(true)
                else
                    if level == 0 then
                        item.com.Btn_Active.gameObject:SetActive(true)
                        item.com.Btn_Up.gameObject:SetActive(false)
                        item.com.Btn_Battle.gameObject:SetActive(false)
                    else
                        item.com.Btn_Up.gameObject:SetActive(true)
                    end
                    
                end
                item.com.Txt_Lock.gameObject:SetActive(false)
            else
                item.com.Btn_Battle.gameObject:SetActive(false)
                item.com.Btn_Up.gameObject:SetActive(false)
                item.com.Btn_Active.gameObject:SetActive(false)
                item.com.Txt_Full.gameObject:SetActive(false)
                item.com.Txt_Lock.gameObject:SetActive(true)
                
            end

            item.com.Img_UpRedDot.gameObject:SetActive(false)
            local maxLevel = PropertyCompute.GetEquipSmeltStarMaxLevel(cfg.SmeltID)
            if level < maxLevel then
                if cfg then
                    local starLvl = GamePlayerData.ActorEquipNew:GetEquipUpgradeLevel(cfg.SmeltID)
                    local equipSmeltStar = Schemes.EquipSmeltStar:GetEquipSmeltStarCfg(cfg.SmeltID, starLvl)

                     local bool1 = not HelperL.IsLackGoods(equipSmeltStar.CostGoodsID1, equipSmeltStar.CostGoodsID1Num, false,false)
                     local bool2 = not HelperL.IsLackGoods(equipSmeltStar.CostGoodsID2, equipSmeltStar.CostGoodsID1Num, false,false)
                    if bool1 and bool2 then
                        item.com.Img_UpRedDot.gameObject:SetActive(true)
                    end
                end
            end
            
            item.com.gameObject:SetActive(true)
            local weaponsKnapsack = GamePlayerData.ActorEquip:GetWeaponsKnapsack(1) or {}
            local curEquipID = weaponsKnapsack[1] or 0
            m.ChangeUIModel(equipID, item.com.Obj_Model.transform)

            m:AddClick(item.com.Btn_Battle, function()
                if m.ItemRole == nil then
                    return
                end
                local cfg = Schemes.Equipment:Get(equipID)
                local level = GamePlayerData.ActorEquipNew:GetEquipUpgradeLevel(cfg.SmeltID)
                if level > 0 then
                    m:Wear(equipID)  -- 调用完整的Wear方法
                    item.com.Txt_Battle.text = "已出战"
                    item.com.Txt_Battle1.text = "已出战"
                    item.com.Img_Battle.gameObject:SetActive(true)
                end
            end)

            m:AddClick(item.com.Btn_Up, function()
                m.equipID = equipID
                m.objList.Btn_UpBox.gameObject:SetActive(true)
                m.ShowBulletInfo(equipID)
            end)

            -- if curEquipID == equipID then
            m.ItemRole = item.com
            m.curEquipID = equipID
            --  m.ItemRole.Img_Select.gameObject:SetActive(true)
            if GamePlayerData.ActorEquip:IsWear(equipID, 1) then
                item.com.Txt_Battle.text = "已出战"
                item.com.Txt_Battle1.text = "已出战"
                item.com.Img_Battle.gameObject:SetActive(true)
            else
                local level = GamePlayerData.ActorEquipNew:GetEquipUpgradeLevel(cfg.SmeltID)
                item.com.Txt_Battle.text = "出战"
                item.com.Txt_Battle1.text = "出战"
                if level > 0 then
                    item.com.Img_Battle.gameObject:SetActive(false)
                else
                    item.com.Img_Battle.gameObject:SetActive(true)
                end
            end
            --end
        else
            item.com.gameObject:SetActive(false)
        end
    end

    item.UpdateBtns = function()
        local cfg = Schemes.Equipment:Get(item.equipID)
        local level = GamePlayerData.ActorEquipNew:GetEquipUpgradeLevel(cfg.SmeltID)
        local maxLevel = PropertyCompute.GetEquipSmeltStarMaxLevel(cfg.SmeltID)
        local weaponcfg = Schemes.EquipWeapon:Get(cfg.ID)
        local levelText = EntityModule.hero:GetProperty(CREATURE_FIELD.CREATURE_FIELD_LEVEL)
        local cureffectID5 = weaponcfg.EffectID5

        item.com.Img_ActiveRedDot.gameObject:SetActive(false)

        if levelText >= cureffectID5 then
            item.com.Btn_Battle.gameObject:SetActive(true)
            item.com.Btn_Up.gameObject:SetActive(true)
            item.com.Btn_Active.gameObject:SetActive(false)
            --是否是满级
            if level >= maxLevel then
                item.com.Btn_Up.gameObject:SetActive(false)
            else
                if level == 0 then
                    -- V36.0 增加材料判断：只有材料足够时才显示激活按钮和红点
                    local equipSmeltStar = Schemes.EquipSmeltStar:GetEquipSmeltStarCfg(cfg.SmeltID, level)
                    if equipSmeltStar then
                        local bool1 = not HelperL.IsLackGoods(equipSmeltStar.CostGoodsID1, equipSmeltStar.CostGoodsID1Num, false, false)
                        local bool2 = not HelperL.IsLackGoods(equipSmeltStar.CostGoodsID2, equipSmeltStar.CostGoodsID2Num, false, false)
                        
                        if bool1 and bool2 then
                            -- 材料足够：显示激活按钮和红点
                            item.com.Btn_Active.gameObject:SetActive(true)
                            item.com.Img_ActiveRedDot.gameObject:SetActive(true)
                        else
                            -- 材料不足：显示激活按钮但不显示红点
                            item.com.Btn_Active.gameObject:SetActive(true)
                            item.com.Img_ActiveRedDot.gameObject:SetActive(false)
                        end
                    else
                        -- 找不到升级配置：显示激活按钮但不显示红点
                        item.com.Btn_Active.gameObject:SetActive(true)
                        item.com.Img_ActiveRedDot.gameObject:SetActive(false)
                    end
                    item.com.Btn_Up.gameObject:SetActive(false)
                    item.com.Btn_Battle.gameObject:SetActive(false)
                else
                    item.com.Btn_Up.gameObject:SetActive(true)
                end
                
            end
            item.com.Txt_Lock.gameObject:SetActive(false)
        else
            item.com.Btn_Battle.gameObject:SetActive(false)
            item.com.Btn_Up.gameObject:SetActive(false)
            item.com.Btn_Active.gameObject:SetActive(false)
            item.com.Txt_Lock.gameObject:SetActive(true)
        end
    end

    item.UpdateDataInfo = function(equipID)
        item.equipID = equipID
        if equipID then
            local cfg = Schemes.Equipment:Get(equipID)
            local level = GamePlayerData.ActorEquipNew:GetEquipUpgradeLevel(cfg.SmeltID)
            item.com.Txt_Lv1.text = level .. "级"
            if GamePlayerData.ActorEquip:IsWear(equipID, 1) then
                item.com.Txt_Battle.text = "已出战"
                item.com.Txt_Battle1.text = "已出战"
                item.com.Img_Battle.gameObject:SetActive(true)
            else
                local level = GamePlayerData.ActorEquipNew:GetEquipUpgradeLevel(cfg.SmeltID)
                item.com.Txt_Battle.text = "出战"
                item.com.Txt_Battle1.text = "出战"
                if level > 0 then
                    item.com.Img_Battle.gameObject:SetActive(false)
                else
                    item.com.Img_Battle.gameObject:SetActive(true)
                end
            end
        end
    end
    return item
end

function m.UpdateBattle()
    local weaponsKnapsack = GamePlayerData.ActorEquip:GetWeaponsKnapsack(1) or {}
    local curEquipID = weaponsKnapsack[1] or 0
    if curEquipID == m.equipID then
        -- if GamePlayerData.ActorEquip:IsWear(m.equipID, 1) then
        --     m.objList.Txt_Battle.text = "已出战"
        --     m.objList.Txt_Battle1.text = "已出战"
        --     m.objList.Img_Battle.gameObject:SetActive(true)
        -- else
        --     local cfg = Schemes.Equipment:Get(m.equipID)
        --     local level = GamePlayerData.ActorEquipNew:GetEquipUpgradeLevel(cfg.SmeltID)
        --     m.objList.Txt_Battle.text = "出战"
        --     m.objList.Txt_Battle1.text = "出战"
        --     if level > 0 then
        --         m.objList.Img_Battle.gameObject:SetActive(false)
        --     else
        --         m.objList.Img_Battle.gameObject:SetActive(true)
        --     end
        -- end
    end
end

--------------------------------------------------------------------
-- 注册点击事件
--------------------------------------------------------------------
function m.RegisterClickEvent()
    m:AddClick(m.objList.Btn_Close, function()
        m:CloseSelf()
        UIManager:CloseWndByID(WndID.EquipKnapsack)
    end)
    m:AddClick(m.objList.Btn_Upgrade1, function()
        m.Upgrade()
    end)
    m:AddClick(m.objList.Btn_UpBox, function()
        m.objList.Btn_UpBox.gameObject:SetActive(false)
    end)

    --角色 宝石
    m:AddClick(m.objList.Btn_Role1, function()
        m.objList.Btn_Role.gameObject:SetActive(true)
        m.objList.Btn_Role1.gameObject:SetActive(false)
        m.objList.Btn_BaoShi.gameObject:SetActive(false)
        m.objList.Btn_BaoShi1.gameObject:SetActive(true)
        m.objList.Img_bjtb_02.gameObject:SetActive(true)
        m.objList.Txt_Title.text = "伙伴"
        UIManager:CloseWndByID(WndID.EquipKnapsack)
    end)
    m:AddClick(m.objList.Btn_BaoShi1, function()
        m.objList.Btn_Role.gameObject:SetActive(false)
        m.objList.Btn_Role1.gameObject:SetActive(true)
        m.objList.Btn_BaoShi.gameObject:SetActive(true)
        m.objList.Btn_BaoShi1.gameObject:SetActive(false)
        m.objList.Img_bjtb_02.gameObject:SetActive(false)
        m.objList.Txt_Title.text = "镶嵌"
        UIManager:OpenWnd(WndID.EquipKnapsack)
    end)
end

--------------------------------------------------------------------
-- 更新界面
--------------------------------------------------------------------
function m.UpdateView()
    local equipment = Schemes.Equipment:Get(m.equipID)
    local cfg = Schemes.Equipment:Get(m.equipID)
    local level = GamePlayerData.ActorEquipNew:GetEquipUpgradeLevel(cfg.SmeltID)
    local equipSme = Schemes.EquipSmeltStar:GetEquipSmeltStarCfg(cfg.SmeltID, level)
    if not equipSme then
        error('EquipSmeltStar is nil, SmeltID=' .. SmeltID .. ' level=' .. level)
        return
    end

    --AtlasManager:AsyncGetSprite(HelperL.GetGunIcon(m.equipID), m.objList.Img_Icon, true)

    -------------------------基础属性-----------------------------------
    local attributeList = {}
    local attack = 0
    local hp = 0
    if equipment.QualityLevel < equipSme.AttackList.Length then
        attack = attack + equipSme.AttackList[equipment.QualityLevel]
    end
    if equipment.QualityLevel < equipSme.MaxHpList.Length then
        hp = hp + equipSme.MaxHpList[equipment.QualityLevel]
    end
    --攻击
    table.insert(attributeList,
        { icon = 'AttributeIcon03', name = GetGameText(luaID, 8), describe = attack })
    --生命
    table.insert(attributeList,
        { icon = 'AttributeIcon3', name = GetGameText(luaID, 7), describe = hp })

    local num = math.max(#m.Item_Attribute_List, #attributeList)
    for i = 1, num, 1 do
        if not m.Item_Attribute_List[i] then
            m.Item_Attribute_List[i] = m.Creation_Item_Attribute(i)
        end
        m.Item_Attribute_List[i].UpdateData(attributeList[i])
    end
    m.ShowBulletInfo(m.equipID)
    m.UpdateRoleInfo()
    m.UpdateRole()
end

--------------------------------------------------------------------
---创建属性框
---@param index integer
---@return Item_Attribute
--------------------------------------------------------------------
function m.Creation_Item_Attribute(index)
    ---@class Item_Attribute
    local item = {}
    item.index = index
    item.com = m:CreateSubItem(m.objList.Grid_Attribute, m.objList.Item_Attribute)
    ---属性数据--图片、名字、描述
    ---@param data {icon:string, name:string, describe:string}
    item.UpdateData = function(data)
        item.data = data
        if data then
            item.com.Txt_Value.text = data.name .. data.describe
            AtlasManager:AsyncGetSprite(data.icon, item.com.Img_Icon, true)
            item.com.gameObject:SetActive(true)
        else
            item.com.gameObject:SetActive(false)
        end
    end
    return item
end

--------------------------------------------------------------------
---创建属性框
---@param index integer
---@return Item_Attribute4
--------------------------------------------------------------------
function m.Creation_Item_Attribute2(index)
    ---@class Item_Attribute4
    local item = {}
    item.index = index
    item.com = m:CreateSubItem(m.objList.Grid_Attribute2, m.objList.Item_Attribute2)
    ---属性数据--图片、名字、描述
    ---@param equipSme EquipSmeltStarCfg
    item.UpdateData = function(equipSme)
        item.equipSme = equipSme
        if equipSme then
            item.com.Txt_Level.text = string.format(GetGameText(luaID, 19), equipSme.StarLvl)
            item.com.Txt_Value.text = equipSme.Remark
            local level = GamePlayerData.ActorEquipNew:GetEquipUpgradeLevel(equipSme.SmeltID)
            item.com.Img_Lock.gameObject:SetActive(level <= equipSme.StarLvl)
            item.com.gameObject:SetActive(true)
        else
            item.com.gameObject:SetActive(false)
        end
    end
    return item
end

--------------------------------------------------------------------
---创建按钮
---@param index integer
---@return Item_Tab
--------------------------------------------------------------------
function m.Creation_Item_Tab(index)
    ---@class Item_Tab
    local item = {}
    item.index = index
    item.com = m:CreateSubItem(m.objList.Grid_Tab, m.objList.Item_Tab)
    m:AddClick(item.com.Btn_Click, function()
        --m.ShowBulletInfo(item.index)
    end)

    item.com.Img_Bg.gameObject:SetActive(true)
    item.com.Img_Select.gameObject:SetActive(false)
    --- 更新数据
    ---@param SmeltID integer 升星ID
    item.UpdateData = function(SmeltID)
        item.SmeltID = SmeltID
        --print("SmeltID ===== ", SmeltID)
        local maxLevel = PropertyCompute.GetEquipSmeltStarMaxLevel(SmeltID)
        local level = GamePlayerData.ActorEquipNew:GetEquipUpgradeLevel(SmeltID)
        -- local exp = GamePlayerData.ActorEquipNew:GetEquipUpgradeEXP(SmeltID)
        local equipSme = Schemes.EquipSmeltStar:GetEquipSmeltStarCfg(SmeltID, level)
        if not equipSme then
            error('EquipSmeltStar is nil, SmeltID=' .. SmeltID .. ' level=' .. level)
            return
        end
        item.com.Txt_Name1.text = equipSme.Name
        item.com.Txt_Name2.text = equipSme.Name

        item.com.Img_RedDot.gameObject:SetActive(false)
        if level < maxLevel then
            local bool1 = not HelperL.IsLackGoods(equipSme.CostGoodsID1, equipSme.CostGoodsID1Num, false, false)
            local bool2 = not HelperL.IsLackGoods(equipSme.CostGoodsID2, equipSme.CostGoodsID1Num, false, false)
            if bool1 and bool2 then
                item.com.Img_RedDot.gameObject:SetActive(true)
            end
        end
    end
    return item
end

function m.ShowAttribute()

end

--------------------------------------------------------------------
--显示子弹信息
---@param index integer
--------------------------------------------------------------------
function m.ShowBulletInfo(equipID)
    -- local item
    -- if m.selectIndex then
    --     item = m.Item_Tab_List[m.selectIndex]
    --     item.com.Img_Bg.gameObject:SetActive(true)
    --     item.com.Img_Select.gameObject:SetActive(false)
    -- end
    -- item = m.Item_Tab_List[index]
    -- item.com.Img_Bg.gameObject:SetActive(false)
    -- item.com.Img_Select.gameObject:SetActive(true)

    -- m.selectIndex = index

    local equipment = Schemes.Equipment:Get(equipID)
    local cfg = Schemes.Equipment:Get(equipID)
    local level = GamePlayerData.ActorEquipNew:GetEquipUpgradeLevel(cfg.SmeltID)
    local maxLevel = PropertyCompute.GetEquipSmeltStarMaxLevel(cfg.SmeltID)
    local equipSme = Schemes.EquipSmeltStar:GetEquipSmeltStarCfg(cfg.SmeltID, level)
    if not equipSme then
        error('EquipSmeltStar is nil, SmeltID=' .. SmeltID .. ' level=' .. level)
        return
    end
    local attack = 0
    local hp = 0
    if cfg.QualityLevel < equipSme.AttackList.Length then
        attack = attack + equipSme.AttackList[cfg.QualityLevel]
    end
    if cfg.QualityLevel < equipSme.MaxHpList.Length then
        hp = hp + equipSme.MaxHpList[cfg.QualityLevel]
    end
    --设置默认状态
    m.objList.Txt_Hint.gameObject:SetActive(false)
    m.objList.Btn_Upgrade1.gameObject:SetActive(false)
    m.objList.Img_Gray.gameObject:SetActive(false)
    m.objList.Img_UpgradeRedDot.gameObject:SetActive(false)
    m.objList.Txt_Lv.text = level .. "级"
    if level < maxLevel then
        m.objList.Btn_Upgrade1.gameObject:SetActive(true)
        AtlasManager:AsyncGetGoodsSprite(equipSme.CostGoodsID1, m.objList.Img_UpgradeExpend)
        local bool1 = not HelperL.IsLackGoods(equipSme.CostGoodsID1, equipSme.CostGoodsID1Num, false, false)
        m.objList.Txt_UpgradeExpend.text = string.format("<color=%s>%s</color>/%s",
            bool1 and UI_COLOR.Red or UI_COLOR.Red,
            HelperL.GetChangeNum(SkepModule:GetGoodsCount(equipSme.CostGoodsID1)),
            HelperL.GetChangeNum(equipSme.CostGoodsID1Num))

        AtlasManager:AsyncGetGoodsSprite(equipSme.CostGoodsID2, m.objList.Img_UpgradeExpend2)
        local bool2 = not HelperL.IsLackGoods(equipSme.CostGoodsID2, equipSme.CostGoodsID1Num, false, false)
        m.objList.Txt_UpgradeExpend2.text = string.format("%s <color=%s>%s</color>/%s",
            HelperL.GetGoodsName(equipSme.CostGoodsID2),
            bool2 and UI_COLOR.Red or UI_COLOR.Red,
            HelperL.GetChangeNum(SkepModule:GetGoodsCount(equipSme.CostGoodsID2)),
            HelperL.GetChangeNum(equipSme.CostGoodsID2Num))

        if bool1 and bool2 then
            m.objList.Img_UpgradeRedDot.gameObject:SetActive(true)
        else
            m.objList.Img_Gray.gameObject:SetActive(true)
        end
        m.objList.Img_11.gameObject:SetActive(true)
        --m.objList.Txt_Lv.text = string.format(GetGameText(luaID, 19), equipSme.StarLvl)
        --m.objList.Txt_Fill.text = level .. "/" .. maxLevel
        --m.objList.Img_Fill.fillAmount = level / maxLevel
    else
        m.objList.Txt_Hint.text = CommonTextID.IS_FULL_LEVEL
        m.objList.Txt_Hint.gameObject:SetActive(true)
        m.objList.Img_11.gameObject:SetActive(false)
    end

    --获取最大等级
    -- if level == 0 then
    --     m.objList.Txt_Upgrade.text = GetGameText(luaID, 20)
    --     m.objList.Img_Battle.gameObject:SetActive(true)
    -- else
    --     --是否是满级
    --     if level >= maxLevel then
    --         m.objList.Txt_Upgrade.text = GetGameText(luaID, 22)
    --     else
    --         m.objList.Txt_Upgrade.text = GetGameText(luaID, 21)
    --     end
    --     m.objList.Img_Battle.gameObject:SetActive(false)
    -- end
    if level < maxLevel then
        local nextattack = 0
        local nexthp = 0
        local nextequipSme = Schemes.EquipSmeltStar:GetEquipSmeltStarCfg(cfg.SmeltID, level + 1)
        if cfg.QualityLevel + 1 < nextequipSme.AttackList.Length then
            nextattack = nextattack + nextequipSme.AttackList[cfg.QualityLevel]
        end
        if cfg.QualityLevel + 1 < nextequipSme.MaxHpList.Length then
            nexthp = nexthp + nextequipSme.MaxHpList[cfg.QualityLevel]
        end
        m.objList.Txt_Desc.text = GetGameText(luaID, 8) .. "         " ..
            HelperL.GetChangeNum(attack) ..
            "                 " ..
            string.format(GetGameText(luaID, 23), HelperL.GetChangeNum(nextattack)) .. "\n\n\n" ..
            GetGameText(luaID, 7) .. "         " .. HelperL.GetChangeNum(hp) .. "                 " ..
            string.format(GetGameText(luaID, 23), HelperL.GetChangeNum(nexthp))
    else
        m.objList.Txt_Desc.text = GetGameText(luaID, 8) .. "         " ..
            HelperL.GetChangeNum(attack) .. "\n\n\n" .. GetGameText(luaID, 7) .. "         " .. HelperL.GetChangeNum(hp)
        m.objList.Img_1.gameObject:SetActive(false)
        m.objList.Img_2.gameObject:SetActive(false)
    end


    m.UpdateBattle()

    local equipSmeltList = Schemes.EquipSmeltStar:GetBySmeltID(cfg.SmeltID) or {}
    for i = 2, #equipSmeltList, 1 do
        if not m.Item_Attribute_List2[i] then
            m.Item_Attribute_List2[i] = m.Creation_Item_Attribute2(i)
        end
        m.Item_Attribute_List2[i].UpdateData(equipSmeltList[i])
    end
end

--------------------------------------------------------------------
--请求回调
--------------------------------------------------------------------
function m.RequestCallback(resultCode, content)
    if resultCode == RESULT_CODE_BASE.RESULT_COMMON_SUCCEED[1] then
        m.UpdateView()
        --播放升级特效
        HelperL.PlayVFX()
        SoundManager:PlaySound(SoundID.Upgrade)
    else
        ResultCode:DefaultShowResultCode(resultCode, content)
    end
end

--------------------------------------------------------------------
--升级
---@param equipID integer 装备ID
--------------------------------------------------------------------
function m.Upgrade(equipID)
    local curTime = os.clock()
    if curTime - m.lastTime < 0.3 then
        --print('天赋升级点太快了1秒后尝试！')
        return
    end
    m.lastTime = curTime
    local equipID1 = equipID
    if equipID1 == nil then
        equipID1 = m.equipID
    end
    local cfg = Schemes.Equipment:Get(equipID1)
    local smeltID = cfg.SmeltID

    local starLvl = GamePlayerData.ActorEquipNew:GetEquipUpgradeLevel(smeltID)
    local equipSmeltStar = Schemes.EquipSmeltStar:GetEquipSmeltStarCfg(smeltID, starLvl)
    if equipSmeltStar == nil then
        warn("天赋升级:未找到升星配置 smeltID=", smeltID, starLvl)
        return
    end

    if HelperL.IsLackGoods(equipSmeltStar.CostGoodsID1, equipSmeltStar.CostGoodsID1Num, false) then return end
    if HelperL.IsLackGoods(equipSmeltStar.CostGoodsID2, equipSmeltStar.CostGoodsID2Num, false) then return end

    local form = {}
    form["smeltID"] = smeltID
    form["loopTimes"] = 1
    LuaModuleNew.SendRequest(LuaRequestID.NewCardAddExp2, form, m.RequestCallback)
end

--------------------------------------------------------------------
--获取多层关联装备ID列表
---@param equipId integer 主装备ID
---@return table 关联装备ID列表
--------------------------------------------------------------------
function m.GetAllAssociatedEquipIDs(equipId)
    local associatedEquipIDs = {}
    local currentEquipId = equipId
    local visitedEquipIDs = {} -- 防止循环引用
    
    while true do
        -- 防止循环引用
        if visitedEquipIDs[currentEquipId] then
          --  --print("UIRoleEquip.GetAllAssociatedEquipIDs: 检测到循环引用，停止查找关联装备，当前ID=" .. currentEquipId)
            break
        end
        visitedEquipIDs[currentEquipId] = true
        
        local equipWeaponConfig = Schemes.EquipWeapon:Get(currentEquipId)
        if not equipWeaponConfig then
          --  --print("UIRoleEquip.GetAllAssociatedEquipIDs: 未找到EquipWeapon配置，装备ID=" .. currentEquipId)
            break
        end
        
        if equipWeaponConfig.EffectID3 and equipWeaponConfig.EffectID3 ~= 0 then
            local associatedId = equipWeaponConfig.EffectID3
            table.insert(associatedEquipIDs, associatedId)
          --  --print("UIRoleEquip.GetAllAssociatedEquipIDs: 找到关联装备 " .. associatedId .. " (关联于装备 " .. currentEquipId .. ")")
            currentEquipId = associatedId -- 继续查找下一层关联
        else
         --   --print("UIRoleEquip.GetAllAssociatedEquipIDs: 装备 " .. currentEquipId .. " 无关联装备或EffectID3为0，停止查找")
            break
        end
    end
    
    return associatedEquipIDs
end

--------------------------------------------------------------------
--装备出战 - 完整的关联装备逻辑
---@param equipId integer 装备ID
--------------------------------------------------------------------
function m:Wear(equipId)
    local newEquipConfig = Schemes.Equipment:Get(equipId)
    if not newEquipConfig then
        --print("UIRoleEquip.Wear: Invalid equipId " .. tostring(equipId))
        return
    end
    
    -- 获取装备的EquipWeapon配置
    local newEquipWeaponConfig = Schemes.EquipWeapon:Get(equipId) 
    if not newEquipWeaponConfig then
        --print("UIRoleEquip.Wear: EquipWeapon config not found for Equipment.ID " .. tostring(equipId) .. ". Also, ConsignmentStyle (" .. tostring(newEquipConfig.ConsignmentStyle) .. ") lookup also failed previously.")
        return
    end

    local newGroupID = newEquipWeaponConfig.GroupID
    local itemsToUnload = {}
    local itemsToWear = {equipId} -- 要装备的主装备
    
    -- 检查是否有关联装备需要一起出战 - 使用多层关联获取
    local associatedEquipIDs = m.GetAllAssociatedEquipIDs(equipId)
    for _, associatedId in ipairs(associatedEquipIDs) do
        table.insert(itemsToWear, associatedId)
      --  --print("UIRoleEquip.Wear: 将装备 " .. equipId .. " 的关联装备 " .. associatedId .. " 加入出战列表")
    end
    
    -- 获取当前已出战装备列表并打印
    local currentEquipIDs_InKnapsack = GamePlayerData.ActorEquip:GetWeaponsKnapsack(1) or {}
    local currentEquipStr = "当前出战装备: "
    for _, id in ipairs(currentEquipIDs_InKnapsack) do
        if id ~= 0 then
            currentEquipStr = currentEquipStr .. id .. ", "
        end
    end
    --print(currentEquipStr)
    
    -- 检查当前装备是否已出战
    local isAlreadyWorn = false
    for _, currentEquipId in ipairs(currentEquipIDs_InKnapsack) do
        if currentEquipId == equipId then
            isAlreadyWorn = true
            break
        end
    end
    
    if isAlreadyWorn then
        --print("UIRoleEquip.Wear: 装备 " .. equipId .. " 已经出战，无需再次出战")
        return -- 如果已出战，直接返回，避免重复操作
    end
    
    -- 收集需要卸下的装备
    for _, currentEquipId in ipairs(currentEquipIDs_InKnapsack) do
        if currentEquipId ~= 0 then
            local currentEquipWeaponConfig = Schemes.EquipWeapon:Get(currentEquipId)
            if currentEquipWeaponConfig then
                local currentGroupID = currentEquipWeaponConfig.GroupID
                
                -- 核心逻辑：根据GroupID分组处理
                -- 同组互斥：如果是同一组装备，需要卸下
                if (newGroupID < 100 and currentGroupID < 100) or 
                   (newGroupID >= 100 and newGroupID < 1000 and currentGroupID >= 100 and currentGroupID < 1000) then
                    table.insert(itemsToUnload, currentEquipId)
                   -- --print("UIRoleEquip.Wear: 将卸下同组装备 " .. currentEquipId .. " (GroupID: " .. currentGroupID .. ")")
                    
                    -- 检查该装备是否有关联装备也需要卸下 - 使用多层关联获取
                    local currentAssociatedEquipIDs = m.GetAllAssociatedEquipIDs(currentEquipId)
                    for _, associatedId in ipairs(currentAssociatedEquipIDs) do
                        -- 检查关联装备是否在已出战列表中
                        for _, wornId in ipairs(currentEquipIDs_InKnapsack) do
                            if wornId == associatedId then
                                table.insert(itemsToUnload, associatedId)
                                --print("UIRoleEquip.Wear: 将卸下关联装备 " .. associatedId .. " (关联于装备 " .. currentEquipId .. ")")
                                break
                            end
                        end
                    end
                end
                
                -- 反向检查：如果这个装备是其他装备的关联装备且其主装备要被卸下，则这个装备也要卸下
                for _, unloadId in ipairs(itemsToUnload) do
                    local unloadAssociatedEquipIDs = m.GetAllAssociatedEquipIDs(unloadId)
                    for _, associatedId in ipairs(unloadAssociatedEquipIDs) do
                        if associatedId == currentEquipId then
                            table.insert(itemsToUnload, currentEquipId)
                           -- --print("UIRoleEquip.Wear: 将卸下关联装备 " .. currentEquipId .. " (是将被卸下的装备 " .. unloadId .. " 的关联装备)")
                            break
                        end
                    end
                end
            end
        end
    end
    
    -- 去重卸下列表
    local unloadSet = {}
    local uniqueUnloadList = {}
    for _, id in ipairs(itemsToUnload) do
        if not unloadSet[id] then
            unloadSet[id] = true
            table.insert(uniqueUnloadList, id)
        end
    end
    
    -- 打印卸下装备列表
    local unloadStr = "将卸下装备: "
    for _, id in ipairs(uniqueUnloadList) do
        unloadStr = unloadStr .. id .. ", "
    end
    --print(unloadStr)
    
    -- 装备新装备及其关联装备
    -- 修改：不再分别调用WearEquip，而是统一收集所有装备然后一次性保存
    local currentWearList = GamePlayerData.ActorEquip:GetWeaponsKnapsack(1) or {}
    local finalWearList = {}
    
    -- 先保留未被卸下的装备
    for _, currentEquipId in ipairs(currentWearList) do
        local shouldKeep = true
        for _, unloadId in ipairs(uniqueUnloadList) do
            if currentEquipId == unloadId then
                shouldKeep = false
                break
            end
        end
        if shouldKeep then
            table.insert(finalWearList, currentEquipId)
        end
    end
    
    -- 添加新装备
    for _, wearId in ipairs(itemsToWear) do
        table.insert(finalWearList, wearId)
        --print("UIRoleEquip.Wear: 已装备 " .. wearId)
    end
    
    -- 一次性保存所有装备
    LogicValue.SetWeaponsKnapsack(finalWearList)
    
    -- 如果是主武器(GroupID < 100)，更新HelperL.clientTempAirplaneUID
    if newGroupID < 100 then
        if m.updateClientTempUIDTimer then
            m.updateClientTempUIDTimer:Stop()
            m.updateClientTempUIDTimer = nil
        end
        -- 使用定时器尝试获取更新后的SkepModule数据
        m.updateClientTempUIDTimer = Timer.New(function()
            local mainWeaponSkepType = (EQUIP_TYPE and EQUIP_TYPE.EQUIP_TYPE_NORMALWEAPON) or 1
            
            if SkepModule and SkepModule.GetEquipSkep and EntityModule and EntityModule.GetEntity then
                local skepData = SkepModule.GetEquipSkep()
                if skepData and skepData[mainWeaponSkepType] then
                    local mainWeaponEntityUID = skepData[mainWeaponSkepType]
                    if mainWeaponEntityUID and mainWeaponEntityUID ~= 0 then
                        -- 验证此entityUID是否对应我们刚刚装备的equipId
                        local entity = EntityModule:GetEntity(mainWeaponEntityUID)
                        if entity and entity:GetProperty(ENTITY_FIELD.ENTITY_FIELD_ID) == equipId then
                            if HelperL.clientTempAirplaneUID ~= mainWeaponEntityUID then
                                --print("UIRoleEquip.Wear: Updating HelperL.clientTempAirplaneUID from " .. HelperL.clientTempAirplaneUID .. " to: " .. mainWeaponEntityUID .. " for equipped item " .. equipId)
                                HelperL.clientTempAirplaneUID = mainWeaponEntityUID
                            else
                                --print("UIRoleEquip.Wear: HelperL.clientTempAirplaneUID (" .. HelperL.clientTempAirplaneUID .. ") already matches mainWeaponEntityUID for item " .. equipId)
                            end
                        else
                            --print("UIRoleEquip.Wear: HelperL.clientTempAirplaneUID not updated. Entity in slot " .. mainWeaponSkepType .. " (UID: " .. mainWeaponEntityUID .. ", ItemID: " .. (entity and entity:GetProperty(ENTITY_FIELD.ENTITY_FIELD_ID) or "N/A") .. ") does not match equipped item " .. equipId)
                        end
                    else
                        --print("UIRoleEquip.Wear: HelperL.clientTempAirplaneUID not updated. No valid entity UID found in SkepModule for type " .. mainWeaponSkepType)
                    end
                else
                    --print("UIRoleEquip.Wear: SkepModule.GetEquipSkep() returned nil or type " .. mainWeaponSkepType .. " not found in skepData.")
                end
            else
                --print("UIRoleEquip.Wear: SkepModule, SkepModule.GetEquipSkep, EntityModule or EntityModule.GetEntity not available for updating clientTempAirplaneUID.")
            end
            if m then m.updateClientTempUIDTimer = nil end -- 定时器触发一次后清理自身
        end, 0.2, 1) 
        m.updateClientTempUIDTimer:Start()
    end
    
    -- 清理旧定时器
    if m.smallTimer then
        m.smallTimer:Stop()
        m.smallTimer = nil
    end

    -- 打印操作后出战装备列表
    local finalEquipIDs = GamePlayerData.ActorEquip:GetWeaponsKnapsack(1) or {}
    local finalEquipStr = "操作后出战装备: "
    for _, id in ipairs(finalEquipIDs) do
        if id ~= 0 then
            finalEquipStr = finalEquipStr .. id .. ", "
        end
    end
    --print(finalEquipStr)

    -- 执行【#逻辑值 200 X】，其中X值是本次操作出战的装备ID
    local chatContent = "#逻辑值 200 " .. equipId
    local msg = ChatMessage_pb.CS_Char_SendChat()
    msg.Channel = 12
    msg.Content = chatContent
    msg.ChatType = 0
    Premier.Instance:GetNetwork():SendFromLua(
        ENDPOINT.ENDPOINT_GAMECLIENT,
        ENDPOINT.ENDPOINT_GAMESERVER,
        MSG_MODULEID.MSG_MODULEID_CHAT,
        ChatMessage_pb.MSG_CHAT_SENDCHAT,
        msg:SerializeToString()
    )
    --print("11111 装备出战-设置逻辑值200:", equipId, "命令:", chatContent)

    -- 刷新界面
    if m.UpdateView then
        m.UpdateView()
    else
        --print("UIRoleEquip.Wear: m.UpdateView function not found.")
    end
end

return m
