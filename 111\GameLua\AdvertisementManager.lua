--[[
********************************************************************
    created:	
    author :	
    purpose:    广告管理
*********************************************************************
--]]

local luaID = ('AdvertisementManager')

---广告管理
---@class AdvertisementManager
AdvertisementManager = {}

---获取广告奖励(不看广告获取奖励)
---@param _adverID integer|string 广告ID
---@param callback? fun(bool:boolean, adID:integer|string, code:integer) 回调
---@param isAdCfg? boolean 奖励是否走广告表
function AdvertisementManager.GetAdAward(_adverID, callback, isAdCfg)
	--不走广告表
	if isAdCfg == false then
		if callback then callback(true, _adverID, 0) end
	else
		LuaModule.RunLuaRequest(string.format('LuaRequestSeeAdvertiseUseBuff?AdverID=%s', _adverID),
			function(code, content)
				local fun = callback
				if fun then fun(code == RESULT_CODE_BASE.RESULT_COMMON_SUCCEED[1], content, code) end
				--ResultCode.ShowResultCodeCallback(code, content)
			end)
	end
end

--上一次看广告时间
AdvertisementManager.LastTime = 0
--看广告间隔时间
AdvertisementManager.AD_CD = 9
---观看广告
---@param adverID integer|string 广告ID
---@param fun? fun(bool:boolean, adID:integer, code:integer) 看广告回调函数，回传参数boolean看广告是否成功
---@param openClose? boolean 开启关闭按钮(PC看广告界面)，默认：true
function AdvertisementManager.ShowRewardAd(adverID, fun, openClose)
	local actorID = LoginModule:GetSelectActorData().ActorID
	LuaModule.RunLuaRequest(string.format('LuaRequestStartSeeAdvertise?AdverID=%s', actorID), nil)
	local time = HelperL.GetServerTime()
	local interval = time - AdvertisementManager.LastTime
	if interval < AdvertisementManager.AD_CD then
		HelperL.ShowMessage(TipType.FlowText, string.format(GetGameText(luaID, 8), AdvertisementManager.AD_CD - interval))
		return
	end
	adverID = tonumber(adverID)
	if not adverID then
		warn('无广告配置  CommonText  adverID=', adverID)
		return
	end
	local isAdCfg = adverID > 0
	--走广告表
	if isAdCfg then
		local commonText = Schemes.CommonText:Get(adverID)
		if not commonText then
			warn('无广告配置  CommonText  adverID=', adverID)
			if fun then fun(false, adverID, 1) end
			return
		end

		--获取广告状态
		local state = HelperL.GetAdverState(adverID)
		local str
		if state == 4 then
			str = GetGameText(luaID, 6)
		elseif state == 3 then
			str = GetGameText(luaID, 7)
		elseif state == 2 then
			local _time = EntityModule.hero.buffLC:GetBuffLeftTime(commonText.Param1)
			str = string.format(GetGameText(luaID, 8), math.floor(_time))
		end
		--观看失败提示
		if state ~= 1 then
			if fun then fun(false, adverID, 1) end
			if str then HelperL.ShowMessage(TipType.FlowText, str) end
			return
		end
	end

	--判断是否可以免广告
	local cfg = Schemes.CommonText:Get(173)
	local isNoAD2 = HeroDataManager:GetLogicByte(cfg.Param4, cfg.Param5) >= cfg.DayTime

	--判断是否购买免广告
	local isNoAD = HelperL.IsNoAD()
	if isNoAD or isNoAD2 then
		AdvertisementManager.GetAdAward(adverID, fun, isAdCfg)
		return
	end

	--判断是PC渠道
	local isPC = GameLuaAPI.Channel == GameLuaAPI.eChannel.eChannel_None
	if isPC then
		UIManager:OpenWnd(WndID.ADWindow, adverID, fun, isAdCfg, openClose)
		return
	end

	-- --判断是PC渠道
	-- local isPC = GameLuaAPI.GetPlatform() == 'unityEditor'
	-- if isPC then
	-- 	UIManager:OpenWnd(WndID.ADWindow, adverID, fun, isAdCfg, openClose)
	-- 	return
	-- end

	--获取区域数据
	local zoneID = LoginModule:GetZoneID()
	local zoneData = nil
	for i, v in pairs(Zones) do
		if v.id == zoneID then
			zoneData = v
			break
		end
	end
	if not zoneData then
		HelperL.ShowMessage(TipType.FlowText, GetGameText(luaID, 9))
		if fun then fun(false, adverID, 1) end
		return
	end

	HelperL.ShowMessage(TipType.FlowText, GetGameText(luaID, 10))

	--能看广告在注册事件
	AdvertisementManager.callback = fun
	--调用看广告sdk
	GameLuaAPI.ShowRewardAd(
		adverID,
		tostring(adverID),
		tostring('广告:' .. adverID),
		tostring(EntityModule.hero:GetProperty(ENTITY_FIELD.ENTITY_FIELD_ID)),
		tostring(EntityModule.hero.name),
		tostring(zoneData.id),
		tostring(zoneData.name),
		''
	)
end

---@type fun(bool:boolean, adID:integer, code:integer) 看广告回调
AdvertisementManager.callback = nil
---观看广告回调(给C#调用)
---@param jsonstr string json字符串数据
function AdvertisementManager.AdCallbackFunction(jsonstr)
	print('-------观看广告回调---------', jsonstr)
	AdvertisementManager.LastTime = HelperL.GetServerTime()

	--解析json数据
	local data = dkjsonHelper.decode(jsonstr or '{"code": 1, "msg": "看广告失败", "adverID": -1}')
		or { code = 1, msg = "看广告失败", adverID = -1 }
	if data.code ~= 0 and data.msg ~= "" then
		HelperL.ShowMessage(TipType.FlowText, data.msg)
	end

	local fun = AdvertisementManager.callback
	--清除回调函数
	AdvertisementManager.callback = nil

	local adverID = data.adverID or -1
	local commonText = Schemes.CommonText:Get(adverID)
	--走广告表
	if commonText and data.code == 0 then
		LuaModule.RunLuaRequest(string.format('LuaRequestSeeAdvertiseUseBuff?AdverID=%s', adverID),
			function(resultCode, content)
				ResultCode:DefaultShowResultCode(resultCode, content)
				if fun then
					fun(resultCode == RESULT_CODE_BASE.RESULT_COMMON_SUCCEED[1], adverID, resultCode)
				end
			end
		)
	else --不走广告表
		if fun then
			fun(data.code == 0, adverID, data.code)
		end
	end
end

---通关--充值卡ID--观看广告
---@param cardID integer 充值卡ID
---@param fun? fun(bool:boolean) 看广告回调函数，回传参数boolean看广告是否成功
---@param openClose? boolean 开启关闭按钮，默认：false
function AdvertisementManager.ShowRewardAdByCardID(cardID, fun, openClose)
	local scheme = Schemes.RechargeCard:Get(cardID)
	if not scheme or scheme.Description == '0' then return end
	ResMgr.LoadGameObjectAsync("Assets/Temp/ui/Main/ADWindow.prefab", function()
		AdvertisementManager.ShowRewardAd(scheme.Description, fun, openClose)
	end)
end

---观看广告后直接发物品
---@param adverID integer|string 广告ID
---@param adType integer 广告类型，1：看广告获取奖励，2：不看广告获取奖励
function AdvertisementManager.ShowRewardAdAndRequestDirectGiveGoods(adverID, adType)
	--直接向服务器请求发放物品和扣除消耗物品
	local RequestDirectGiveGoodsy = function(isSucceed, adID)
		if isSucceed then
			local commonText = Schemes.CommonText:Get(adID)
			if commonText then
				HelperL.RequestDirectGiveGoodsy(commonText.AddGoods, commonText.DeductGoods)
			end
		end
	end
	if adType == 1 then
		AdvertisementManager.ShowRewardAd(adverID, RequestDirectGiveGoodsy)
	else
		AdvertisementManager.GetAdAward(adverID, RequestDirectGiveGoodsy)
	end
end

---C#调用看广告接口
---@param adverId integer 广告id
---@param csharpEventType integer C#事件类型
function AdvertisementManager.CsharpShowRewardAd(adverId, csharpEventType)
	local callback = function(succ)
		LuaEventToCsharp.Instance:Notify(csharpEventType, { succ })
	end
	AdvertisementManager.ShowRewardAd(adverId, callback);
end
