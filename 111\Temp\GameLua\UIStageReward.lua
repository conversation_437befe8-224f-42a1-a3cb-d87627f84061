﻿--[[
********************************************************************
    created:    2023/08/29
    author :    李锦剑
    purpose:    章节礼包界面
*********************************************************************
--]]
local luaID = ('UIStageReward')

local m = {}
local Recharge_ID = 79
-- 是否广告过的标识初始位置 从332 开始
local adMarkBegin = 331
-- local adPrizeBegin = 5300
local MaxMainStage = 40
--------------------------------------------------------------------
-- 订阅事件列表
--------------------------------------------------------------------
function m.GetOpenEventList()
    return {
        -- [EventID.EntityModuleDataUpdate] = m.UpdateView,
        [EventID.LogicDataChange] = m.UpdateView,
        [EventID.EntitySyncBuff] = m.UpdateView,
        [EventID.OnHeroPropChange] = m.UpdateView
    }
end

--------------------------------------------------------------------
-- 预制体自适应
--------------------------------------------------------------------
function m.AdaptScale()
    HelperL.AdaptScale(m.objList.Img_Bg2, 6)
    HelperL.AdaptScale_Width(m.objList.Item_DayLogin)
end

--------------------------------------------------------------------
-- 初始化
--------------------------------------------------------------------
function m.OnCreate()
    m.dayLoginItemList = {}
    m.objList.Btn_Close.onClick:AddListenerEx(m.OnClickClose)
    return true
end

--------------------------------------------------------------------
-- 每秒更新
--------------------------------------------------------------------
function m.OnSecondUpdate()
    if m.dayLoginItem then
        m.dayLoginItem.SecondUpdate()
    end
end

--------------------------------------------------------------------
-- 窗口页面关闭
--------------------------------------------------------------------
function m.OnClickClose()
    EventManager:Fire(EventID.OnCloseStageWin)
    m:CloseSelf()
end

--------------------------------------------------------------------
-- 窗口开启
--------------------------------------------------------------------
function m.OnOpen()
    m.UpdateView()
    local hero = EntityModule.hero
    if not hero then
        return
    end
    -- local heroLevel = hero:GetProperty(CREATURE_FIELD.CREATURE_FIELD_LEVEL)
    -- if heroLevel == MainButtonOpenLevel[5] then
    --     local curStep = GuideManager:GetStep(GuideManager.EventType.GiftBoxGuide)
    --     if not GuideManager.isRunning and curStep == 1 then
    --         local delayGuide = Timer.New(function()
    --             GuideManager.StartGuide(GuideManager.EventType.GiftBoxGuide, 2)
    --         end, 0.5, 1)
    --         delayGuide:Start()
    --     end
    -- end
end

--------------------------------------------------------------------
-- 更新界面
--------------------------------------------------------------------
function m.UpdateView()
    local curMainStage = HeroDataManager:GetLogicData(LOGIC_DATA.DATA_SOCIETY_ECTYPETOWER_CURSTAGE)
    local index
    for i = 1, MaxMainStage do
        -- if i <= curMainStage then
        -- 	if not m.dayLoginItemList[i] then
        -- 		m.dayLoginItemList[i] = m.CreationDayLogin(i)
        -- 	end
        -- 	m.dayLoginItemList[i].UpdateView(Schemes.RechargeCard:Get(Recharge_ID + i))
        -- else
        -- 	break
        -- end
        --	local bitIndex = 331 + BitId
        if i <= curMainStage and (not HelperL.HadBoughtCardID(Recharge_ID + i)) then
            index = i
            break
        end
    end
    local cfg
    if index then
        cfg = Schemes.RechargeCard:Get(Recharge_ID + index)
    end
    if not m.dayLoginItem then
        m.dayLoginItem = m.CreationDayLogin(index)
    end
    m.dayLoginItem.UpdateView(cfg, index)
    m.objList.Txt_Hint2.gameObject:SetActive(cfg == nil)
end

--------------------------------------------------------------------
-- 创建章节
--------------------------------------------------------------------
function m.CreationDayLogin(index)
    local item = {}
    item.index = index
    item.goodsItemList = {}
    item.com = m:CreateSubItem(m.objList.Grid_DayLogin, m.objList.Item_DayLogin)
    item.com.Btn_Buy.onClick:AddListenerEx(function()
        -- m.OnClickGiftBuy(item.data)
        m.OnClickGiftAD(item.data, item.index)
    end)
    item.com.Btn_Grey.onClick:AddListenerEx(function()
        m.OnClickGiftBuy(item.data)
    end)
    item.com.Btn_AD.onClick:AddListenerEx(function()
        m.OnClickGiftAD(item.data, item.index)
    end)
    -- 每秒更新
    item.SecondUpdate = function()
        -- item.com.Btn_AD.gameObject:SetActive(false)
        -- item.com.Btn_Ad2.gameObject:SetActive(false)
        -- if item.data and item.data.Description ~= '0' then
        -- 	local commonText = Schemes.CommonText:Get(tonumber(item.data.Description))
        -- 	local state = HelperL.GetAdverState(item.data.Description)
        -- 	if state ~= 4 then
        -- 		if state == 2 then
        -- 			local time = EntityModule.hero.buffLC:GetBuffLeftTime(commonText.Param1)
        -- 			item.com.Txt_Ad2.text = HelperL.GetTimeString(TimeStringType.FullAuto2, time)
        -- 			item.com.Btn_Ad2.gameObject:SetActive(true)
        -- 		else
        -- 			item.com.Btn_AD.gameObject:SetActive(true)
        -- 		end
        -- 	end
        -- end
    end
    item.UpdateView = function(data, _index)
        item.data = data
        item.index = _index or item.index
        if data then
            item.com.Txt_Hint.text = string.format(GetGameText(luaID, 1), ToChineseNumbers(item.index))
            if data.Description ~= '0' then
                local commonText = Schemes.CommonText:Get(data.Description)
                local num = HeroDataManager:GetLogicByte(commonText.Param2, commonText.Param3)
                local colot = UI_COLOR.Red
                if num >= commonText.ToTime then
                    colot = UI_COLOR.White
                end
                item.com.Txt_AD.text = string.format(GetGameText(luaID, 3), colot, commonText.ToTime - num)
            end
            local price = string.format(GetGameText(luaID, 2), math.floor(data.FirstRMB / 100))
            local isBuyCard = HelperL.HadBoughtCardID(data.ID)
            -- if isBuyCard then
            -- 	item.com.Txt_Buy.text = CommonTextID.IS_GET
            -- else
            -- 	item.com.Txt_Buy.text = price
            -- end
            item.com.Txt_Buy.text = CommonTextID.GET
            item.com.Txt_Grey.text = price
            local goodsList = Schemes.PrizeTable:GetGoodsList(data.PrizeID)
            if goodsList then
                local num = math.max(#item.goodsItemList, #goodsList)
                local goods
                for i = 1, num, 1 do
                    if not item.goodsItemList[i] then
                        item.goodsItemList[i] = CreateSingleGoods(item.com.Grid_Goods)
                    end
                    goods = item.goodsItemList[i]
                    if goodsList[i] then
                        goods:SetItemData(goodsList[i].id, goodsList[i].num)
                        -- goods:SetSize(90, 90)
                        goods:SetShowName(false)
                        goods:SetShowNum(true)
                        goods:SetVisible(true)
                    else
                        goods:SetVisible(false)
                    end
                end
            end
            -- item.com.Btn_Buy.gameObject:SetActive(false)
            -- item.com.Btn_Grey.gameObject:SetActive(false)
            -- if SWITCH.RECHARGE then
            -- 	item.com.Btn_Buy.gameObject:SetActive(true)
            -- else
            -- 	-- item.com.Btn_Grey.gameObject:SetActive(true)
            -- end
        end
        item.com.gameObject:SetActive(data ~= nil)
    end
    return item
end

--------------------------------------------------------------------
-- 充值
--------------------------------------------------------------------
function m.OnClickGiftBuy(data)
    if not data then
        return
    end
    local isBuyCard = HelperL.HadBoughtCardID(data.ID)
    if isBuyCard then
        HelperL.ShowMessage(TipType.FlowText, CommonTextID.IS_PURCHASE)
        return
    end
    HelperL.Recharge(data.ID)
end

--------------------------------------------------------------------
-- 广告
--------------------------------------------------------------------
function m.OnClickGiftAD(data, bitId)
    -- AdvertisementManager.ShowRewardAd(data.Description, function(bool)
    -- 	if bool then
    -- 		LuaModule.RunLuaRequest(string.format('LuaRequestADStageReward?BitId=%d', bitId),
    -- 			function(resultCode, content)
    -- 				if resultCode ~= RESULT_CODE.RESULT_COMMON_SUCCEED then
    -- 					HelperL.ShowMessage(TipType.FlowText, ResultCode:GetResultCodeStr(resultCode, content))
    -- 				end
    -- 			end
    -- 		)
    -- 	end
    -- end)
    LuaModule.RunLuaRequest(string.format('LuaRequestSeeAdvertiseUseBuff?AdverID=%s', data.Description),
        function(resultCode, content)
            if resultCode ~= RESULT_CODE.RESULT_COMMON_SUCCEED then
                HelperL.ShowMessage(TipType.FlowText, ResultCode:GetResultCodeStr(resultCode, content))
            end
        end)
end

return m
