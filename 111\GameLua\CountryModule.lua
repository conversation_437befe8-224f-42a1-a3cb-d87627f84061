require "CountryMessage_pb"

local luaID = ('CountryModule')

CountryModule = {}

function CountryModule.Handle(action, data)
	--if action == CountryMessage_pb.MSG_COUNTRY_GETCOUNTRYINFO then
	--	local m = CountryMessage_pb.SC_Country_GetCountryInfo()
	--	m:ParseFromString(data)
	--	CountryDataCenter.OnRecvGetCountryInfo(m)
	--end
end

Premier.Instance:RegisterLuaModule(MSG_MODULEID.MSG_MODULEID_COUNTRY, 'CountryModule.Handle')
