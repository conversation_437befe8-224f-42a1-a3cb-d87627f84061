local luaID = ('UIBroatCast')


local UIBroatCast = {}

-- 订阅事件列表
function UIBroatCast:GetOpenEventList()
	return
	{
		[EventID.ChatModule_AddChat] = self.ChatModuleAddChat,
	}
end

function UIBroatCast.ChatModuleAddChat(message)
	local self = UIBroatCast
	self:Updata()
end

-- 初始化
function UIBroatCast:OnCreate()
	self.txtNotic = self.objList.Txt_notic
	self.noticObj = self.objList.notic.gameObject
	self.SetIsPlayBroatCast(false)
	return true
end 

-- 窗口开启
function UIBroatCast:OnOpen()

    
end

function UIBroatCast:Updata()
	print(#ChatModule.horn_list_)
	if #ChatModule.horn_list_ > 0 then
		if not self.GetIsPlayBroatCast() then
			self.PlayBroadCast(ChatModule.horn_list_[1])
			return
		end
	end
end

function UIBroatCast.PlayBroadCast(broadCastText)
	local self = UIBroatCast
	self.SetIsPlayBroatCast(true)
	self.txtNotic.text = broadCastText
	Helper.RebuilRT(self.txtNotic.transform)
	local rectTrans = self.txtNotic:GetRectTransform()
	local size = rectTrans.sizeDelta
	local pos = self.txtNotic.transform.localPosition
	local right = HelperL.GetNoticRightPositionX()
	pos.x = right
	self.txtNotic.transform.localPosition = pos
	local left = -(right + size.x)
	local seq = DOTween.Sequence()
	seq:Append(self.txtNotic.transform:DOLocalMove(Vector3.New(left, -30, 0), 10))
	seq:AppendCallback(function()
			self.SetIsPlayBroatCast(false)
			table.remove(ChatModule.horn_list_,1)
			self:Updata()
		end)
	seq:SetUpdate(true)
end

function UIBroatCast.SetIsPlayBroatCast(value)
	local self = UIBroatCast
	self.isPlayBroatCast = value
	self.noticObj:SetActive(value)
	if not value then
		self.txtNotic.text = ""
	end
end
function UIBroatCast.GetIsPlayBroatCast()
	local self = UIBroatCast
	return self.isPlayBroatCast
end

-- 窗口关闭
function UIBroatCast:OnClose()
	
end

-- 窗口销毁
function UIBroatCast:OnDestroy()
	-- 释放内存

end

-- 点击返回按钮
function UIBroatCast.OnClickClose()
	local self = UIBroatCast
	self:CloseSelf()
end



return UIBroatCast