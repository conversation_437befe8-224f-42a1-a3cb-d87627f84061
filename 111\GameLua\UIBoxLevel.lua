--[[
********************************************************************
    created:	2024/05/25
    author :	李锦剑
    purpose:    宝箱购买界面
*********************************************************************
--]]

local luaID = 'UIBoxLevel'

--宝箱最大等级
-- local maxBoxLevel = 10

local boxIconList = {
    [BOX_EQUIP_ID[1]] = {
        bg = 'xsd_bxbjt_1',
        bg2 = 'xsd_bxbjk_1',
        icon = 'xsd_bxtb_1',
        icon2 = 'xsd_bxtb_3',
    },
    [BOX_EQUIP_ID[2]] = {
        bg = 'xsd_bxbjt_2',
        bg2 = 'xsd_bxbjk_2',
        icon = 'xsd_bxtb_2',
        icon2 = 'xsd_bxtb_4',
    },
}

---宝箱购买界面
---@class UIBoxLevel:UIWndBase
local m = {}

--------------------------------------------------------------------
-- 初始化
--------------------------------------------------------------------
function m.OnCreate()
    m.selectLevel = 1
    ---@type Item_Box2[]
    m.Item_Box_List = {}
    local i = 0
    for k, v in pairs(BOX_EQUIP_ID) do
        i = i + 1
        if not m.Item_Box_List[i] then
            m.Item_Box_List[i] = m.Creation_Item_Box(m.objList.Grid_Box, i)
        end
        m.Item_Box_List[i].SetData(v)
    end

    m.RegisterClickEvent()
    return true
end

--------------------------------------------------------------------
-- 窗口开启
--------------------------------------------------------------------
function m:OnOpen(level)
    m.selectLevel = level or 1
    m.UpdateView()
end

--------------------------------------------------------------------
-- 注册点击事件
--------------------------------------------------------------------
function m.RegisterClickEvent()
    m:AddClick(m.objList.Btn_Close, function()
        m:CloseSelf()
    end)
end

--------------------------------------------------------------------
--更新界面
--------------------------------------------------------------------
function m.UpdateView()
    m.objList.Txt_Level.text = "LV." .. m.selectLevel
    for i, v in ipairs(m.Item_Box_List) do
        v.UpdateView(m.selectLevel)
    end
end

--------------------------------------------------------------------
-- 创建宝箱栏
---@param parent integer
---@param index integer
---@return Item_Box2
--------------------------------------------------------------------
function m.Creation_Item_Box(parent, index)
    local item = {}
    item.index = index
    ---@type Item_Goods2[]
    item.Item_Goods_Liat = {}
    item.com = m:CreateSubItem(parent, m.objList.Item_Box)
    ---设置数据
    ---@param equipID integer
    item.SetData = function(equipID)
        item.equipID = equipID
        if item.equipID then
            item.treasureList = {}
            local boxEquipData = PropertyCompute.GetBoxEquipData(equipID)
            if boxEquipData then
                item.treasureList = boxEquipData.treasureList
                local bgData = boxIconList[equipID]
                AtlasManager:AsyncGetSprite(bgData.icon2, item.com.Img_Icon)
                AtlasManager:AsyncGetSprite(bgData.bg2, item.com.Img_Bg)
            end
            item.com.gameObject:SetActive(true)
        else
            item.com.gameObject:SetActive(false)
        end
    end

    ---更新界面
    ---@param level integer 宝箱等级
    item.UpdateView = function(level)
        if level < 1 then
            level = 1
        end
        if level > #item.treasureList then
            level = #item.treasureList
        end
        local treasure = item.treasureList[level]
        for i, v in ipairs(treasure) do
            if not item.Item_Goods_Liat[i] then
                item.Item_Goods_Liat[i] = m.Creation_Item_Goods(item.com.Grid_Goods, m.objList.Item_Goods)
            end
            item.Item_Goods_Liat[i].UpdateData(v)
        end
    end
    return item
end

--------------------------------------------------------------------
--- 创建宝箱栏
---@param parent integer
---@param index integer
---@return Item_Goods2
--------------------------------------------------------------------
function m.Creation_Item_Goods(parent, index)
    local item = {}
    item.index = index
    item.com = m:CreateSubItem(parent, m.objList.Item_Goods)
    ---更新数据
    ---@param data {id:integer, num:integer}
    item.UpdateData = function(data)
        if data then
            AtlasManager:AsyncGetGoodsSprite(data.id, item.com.Img_Icon)
            item.com.Txt_Amount.text = "+" .. data.num
            item.com.gameObject:SetActive(true)
        else
            item.com.gameObject:SetActive(false)
        end
    end
    return item
end

return m
