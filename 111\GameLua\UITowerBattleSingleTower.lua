-- 塔防单个塔物件
local luaID = ('UITowerBattleSingleTower')

-- 初始化物件
local itemPrefab = nil
local function InitSingleTowerItem(self, parent)
	if not itemPrefab then
		itemPrefab = HotResManager.ReadUI('ui/Main/TowerBattleSingleTower')
		if not itemPrefab then
			warn('InitSingleTowerItem 找不到塔图标预制体')
			return false
		end
	end
	
	if not parent then
		warn('InitSingleTowerItem 未提供父节点')
		return false
	end
	
	local itemObj = GameObject.Instantiate(itemPrefab, parent)
	local itemTrans = itemObj.transform
	local objList = {}
	Helper.FillLuaComps(itemTrans, objList)
	self.gameObject = itemObj
	self.transform = itemTrans
	self.objList = objList
	
	self.iconObj = self.objList.Img_Icon.gameObject
	self.mask = objList.Img_Mask
	self.frameAniObj = objList.Img_Effect.gameObject
	self.frameAniComp = self.frameAniObj:GetComponent('SpriteAnimationComp')
	self.rectTrans = itemObj:GetRectTransform()
	self.transContent = objList.Rct_Content
	self.rightTopObjTran = objList.RightTopObj:GetRectTransform()
	self.levelObjTran = objList.LevelObj:GetRectTransform()
	self.starContentTran = objList.StarContent:GetRectTransform()
	self.starContentObj = objList.StarContent.gameObject
	self.ImgBGKObj = objList.Img_BGK.gameObject
	
	self.isValid = true
	self.isDark = false
	self.showCost = true
	self.showLevel = true
	self.curLevel = -1
	self.showTip = false
	self.curStrengthLevel = 0
	
	return true
end

-- 设置物件数据
local function SetItemData(self, itemID)
	if self.config and self.config.ID == itemID then
		-- 不需要重复设置
		return
	end
	local config = Schemes.TowerBattleObject:Get(itemID)
	self.config = config
	if config then
		-- config类型=TowerBattleObject
		local resColor = HelperL.GetTowerResTypeColor(config.CostResType)
		self.objList.Txt_Cost.text = string.format('<color=#%s>%s</color>', resColor, config.CostResValue)
		if config.CostResValue <= 0 then
			self.showCost = false
		end
		if self.objList.CostObj.activeSelf ~= self.showCost then
			self.objList.CostObj:SetActive(self.showCost)
		end
		local showRightTop = false
		if config.ShowTypeRange > 0 then
			AtlasManager:AsyncGetSprite(config.ShowTypeBG, self.objList.Img_RightTopBG)
			if config.ShowTypeBG then
				self.objList.Txt_RightTop.text = config.ShowTypeName[1]
				showRightTop = true
			else
				showRightTop = false
			end
		else
			showRightTop = false
		end
		if self.objList.RightTopObj.activeSelf ~= showRightTop then
			self.objList.RightTopObj:SetActive(showRightTop)
		end
		
		self:UpdateShowLevel()
		AtlasManager:AsyncGetSprite(config.ShowBG, self.objList.Img_BG)
		AtlasManager:AsyncGetSprite(config.ShowBG..'-k', self.objList.Img_BGK)
		AtlasManager:AsyncGetSprite(config.ShowIcon, self.objList.Img_Icon)
		if self.iconObj.activeSelf ~= true then
			self.iconObj:SetActive(true)
		end
	else
		self.objList.Txt_Cost.text = ''
		if self.objList.CostObj.activeSelf ~= false then
			self.objList.CostObj:SetActive(false)
		end
		-- 给个默认底图
		AtlasManager:AsyncGetSprite('ty-tbd1', self.objList.Img_BG)
		if self.iconObj.activeSelf ~= false then
			self.iconObj:SetActive(false)
		end
		
		if self.objList.RightTopObj.activeSelf ~= false then
			self.objList.RightTopObj:SetActive(false)
		end
		
		self:UpdateShowLevel()
	end
	self:UpdateShowStrengthLevel()
end

-- 设置物件是否可用
local function SetItemValid(self, isValid)
	if self.isValid == isValid then
		return
	end
	HelperL.SetImageGray(self.objList.Img_BG, not isValid)
	HelperL.SetImageGray(self.objList.Img_Icon, not isValid)
	HelperL.SetImageGray(self.objList.Img_RightTopBG, not isValid)
	HelperL.SetImageGray(self.objList.Img_CostIcon, not isValid)
	self.isValid = isValid
end

-- 获取物件是否可用
local function IsItemValid(self)
	return self.isValid
end

-- 设置物件是否可用
local function SetItemDark(self, isDark)
	if self.isDark == isDark then
		return
	end
	self.objList.Img_Dark.gameObject:SetActive(isDark)
	self.isDark = isDark
end

-- 获取物件是否可用
local function IsItemDark(self)
	return self.isDark
end

-- 设置是否显示消耗
local function SetShowCost(self, isShow)
	self.showCost = isShow
	if self.objList.CostObj.activeSelf ~= isShow then
		self.objList.CostObj:SetActive(isShow)
	end
end

-- 设置是否显示等级
local function SetShowLevel(self, isShow)
	self.showLevel = isShow
	self:UpdateShowLevel()
end

-- 设置显示未获得提示
local function SetShowTip(self, tipStr)
	self.objList.Txt_Name.text = tipStr
end

-- 设置是否显示等级
local function UpdateShowLevel(self)
	local showLevelObj = false
	if self.showLevel and self.config then
		local curLevel = -1
		local itemID = self.config.ID
		local collectConfig = Schemes.TowerBattleCollect:Get(itemID)
		if collectConfig then
			local cardSkep = SkepModule:GetSkepByID(SKEPID.SKEPID_MONSTERCARD)
			if cardSkep then
				local goodsCount = cardSkep:FastCount(collectConfig.GoodsID)
				if goodsCount > 0 then
					local goodsEntity = cardSkep:GetEntityByGoodsID(collectConfig.GoodsID)
					if goodsEntity then
						curLevel = HelperL.CalcTowerCollectGoodsLevel(goodsEntity)
					end
				end
			end
		end
		self.curLevel = curLevel
		if curLevel > 0 then
			self.objList.Txt_Level.text = tostring(curLevel)
			showLevelObj = true
		else
			self.objList.Txt_Level.text = ''
			showLevelObj = false
		end
	end
	if self.objList.LevelObj.activeSelf ~= showLevelObj then
		self.objList.LevelObj:SetActive(showLevelObj)
	end
end

-- 设置强化等级
local function UpdateShowStrengthLevel(self)
	if self.config then
		local strengthLV = 0
		local goodsEntity = nil
		local itemID = self.config.ID
		local collectConfig = Schemes.TowerBattleCollect:Get(itemID)
		if collectConfig then
			local cardSkep = SkepModule:GetSkepByID(SKEPID.SKEPID_MONSTERCARD)
			if cardSkep then
				local goodsCount = cardSkep:FastCount(collectConfig.GoodsID)
				if goodsCount > 0 then
					goodsEntity = cardSkep:GetEntityByGoodsID(collectConfig.GoodsID)
					if goodsEntity then
						strengthLV = goodsEntity:GetProperty(EQUIPMENT_FIELD.EQUIPMENT_FIELD_SMELTLEVEL)
					end
				end
			end
		end
		self.curStrengthLevel = strengthLV
		for i = 1, 5 do
			if i <= self.curStrengthLevel then
				AtlasManager:AsyncGetSprite('TY-lx', self.objList['Img_Star'..i])
			else
				AtlasManager:AsyncGetSprite('TY-hx', self.objList['Img_Star'..i])
			end
		end
		if goodsEntity then
			self.starContentObj:SetActive(true)
		else
			self.starContentObj:SetActive(false)
		end
	else
		self.starContentObj:SetActive(false)
	end
end

-- 设置帧动画
local function SetFrameAnimation(self, useAni, aniType)
	if not useAni then
		self.frameAniObj:SetActive(false)
	else
		if self.frameAniComp then
			-- 设置prefix
			if aniType == 1 then
				self.frameAniComp.prefixName = 'A01_'
				self.frameAniComp.framePerSecond = 12
			end
		end
		self.frameAniObj:SetActive(true)
	end
end

-- 是否在播放帧动画
local function IsFrameAnimating(self)
	return self.frameAniObj.activeSelf
end

-- 显示高亮图片
local function ShowHighLightEffect(self, isShow)
	if self.objList.Img_Light.gameObject.activeSelf ~= isShow then
		self.objList.Img_Light.gameObject:SetActive(isShow)
	end
end

-- 设置是否显示
local function SetVisible(self, isVisible)
	if self.gameObject.activeSelf ~= isVisible then
		self.gameObject:SetActive(isVisible)
	end
end

-- 设置大小
local function SetSize(self, width, height)
	local layout = self.gameObject:GetComponent('LayoutElement')
	if layout then
		layout.preferredWidth = width
		layout.preferredHeight = height
	end
	self.rectTrans.sizeDelta = Vector2(width, height)
end

-- 设置物件封面图
local function SetCoverSprite(self, spriteName)
	if self.coverSpriteName == spriteName then return end
	self.coverSpriteName = spriteName
	AtlasManager:AsyncGetSprite(spriteName, self.objList.Img_Cover, false, function (sprite, image, param)
		if sprite then
			image.enabled = true
			image.sprite = sprite
			image:SetNativeSize()
		else
			image.enabled = false
		end
	end)
end

-- 获取物件封面图
local function GetCoverSprite(self)
	return self.coverSpriteName
end

-- 获取等级
local function GetCurLevel(self)
	return self.curLevel
end

--设置攻防图标位置和缩放
local function SetAttDefPosAndScale(self, posX, posY, scale)
	self.rightTopObjTran.anchoredPosition = CachedVector3:Set(posX, posY)
	self.rightTopObjTran.localScale = CachedVector3:Set(scale, scale, scale)
end

--设置等级位置
local function SetLevelPos(self, posX, posY)
	self.levelObjTran.anchoredPosition = CachedVector3:Set(posX, posY)
end

--设置强化星级图标位置和缩放
local function SetStarPosAndScale(self, posX, posY, scale)
	self.starContentTran.anchoredPosition = CachedVector3:Set(posX, posY)
	self.starContentTran.localScale = CachedVector3:Set(scale, scale, scale)
end

--设置按钮 raycastTarget
local function SetImgBtnRaycast(self, select) 
	self.objList.Img_Btn.raycastTarget = select
end

function CreateTowerBattleSingleTower(parent)
	local item = {}
	item.Init = InitSingleTowerItem
	item.SetItemData = SetItemData
	item.SetItemValid = SetItemValid
	item.IsItemValid = IsItemValid
	item.SetItemDark = SetItemDark
	item.IsItemDark = IsItemDark
	item.SetShowCost = SetShowCost
	item.SetShowLevel = SetShowLevel
	item.UpdateShowLevel = UpdateShowLevel
	item.UpdateShowStrengthLevel = UpdateShowStrengthLevel
	item.SetFrameAnimation = SetFrameAnimation
	item.IsFrameAnimating = IsFrameAnimating
	item.ShowHighLightEffect = ShowHighLightEffect
	item.SetVisible = SetVisible
	item.SetSize = SetSize
	item.SetCoverSprite = SetCoverSprite
	item.GetCoverSprite = GetCoverSprite
	item.SetShowTip = SetShowTip
	item.GetCurLevel = GetCurLevel
	item.SetAttDefPosAndScale = SetAttDefPosAndScale
	item.SetLevelPos = SetLevelPos
	item.SetStarPosAndScale = SetStarPosAndScale
	item.SetImgBtnRaycast = SetImgBtnRaycast
	if not item:Init(parent) then
		warn('CreateTowerBattleSingleTower 初始化失败')
		return nil
	end
	
	return item
end