--[[
********************************************************************
    created:    2024/12/30
    author :    装备关联系统
    purpose:    装备出战关联系统测试命令
*********************************************************************
--]]

local luaID = 'EquipAssociationTestCommands'

local m = {}

--------------------------------------------------------------------
-- 装备关联系统测试命令
--------------------------------------------------------------------

-- 测试装备关联系统
function m.TestEquipAssociation()
    print("11111[TestCommands] ========== 开始测试装备关联系统 ==========")
    
    -- 获取当前出战装备列表
    local currentEquipIDs = GamePlayerData.ActorEquip:GetWeaponsKnapsack(1) or {}
    print("11111[TestCommands] 当前出战装备列表:", table.concat(currentEquipIDs, ","))
    
    -- 统计实际出战装备数量
    local actualEquipCount = 0
    local mainEquipID = 0
    for i, equipID in ipairs(currentEquipIDs) do
        if equipID and equipID > 0 then
            actualEquipCount = actualEquipCount + 1
            if i == 1 then
                mainEquipID = equipID
            end
        end
    end
    
    print("11111[TestCommands] 实际出战装备数量:", actualEquipCount)
    print("11111[TestCommands] 主装备ID:", mainEquipID)
    
    if mainEquipID > 0 then
        local associatedEquipIDs = m.GetAllAssociatedEquipIDs(mainEquipID)
        print("11111[TestCommands] 主装备", mainEquipID, "的关联装备:", table.concat(associatedEquipIDs, ","))
        
        -- 显示装备配置信息
        local equipConfig = Schemes.EquipWeapon:Get(mainEquipID)
        if equipConfig then
            print("11111[TestCommands] 主装备配置 - GroupID:", equipConfig.GroupID, "EffectID2:", equipConfig.EffectID2 or 0, "EffectID3:", equipConfig.EffectID3 or 0)
        end
    else
        print("11111[TestCommands] 没有主装备")
    end
    
    print("11111[TestCommands] ========== 装备关联系统测试完成 ==========")
end

-- 查看当前出战装备
function m.ViewCurrentEquip()
    print("11111[TestCommands] ========== 当前出战装备信息 ==========")
    
    local currentEquipIDs = GamePlayerData.ActorEquip:GetWeaponsKnapsack(1) or {}
    for i, equipID in ipairs(currentEquipIDs) do
        if equipID and equipID > 0 then
            local equipConfig = Schemes.EquipWeapon:Get(equipID)
            if equipConfig then
                print(string.format("11111[TestCommands] 槽位%d: 装备ID=%d, GroupID=%d, EffectID2=%d, EffectID3=%d", 
                    i, equipID, equipConfig.GroupID, equipConfig.EffectID2 or 0, equipConfig.EffectID3 or 0))
            else
                print(string.format("11111[TestCommands] 槽位%d: 装备ID=%d (配置不存在)", i, equipID))
            end
        else
            print(string.format("11111[TestCommands] 槽位%d: 空", i))
        end
    end
    
    print("11111[TestCommands] ========== 当前出战装备信息结束 ==========")
end

-- 强制执行关联检查
function m.ForceEquipCheck(triggerType)
    triggerType = triggerType or "manual"
    print("11111[TestCommands] ========== 强制执行装备关联检查 ==========")
    print("11111[TestCommands] 触发类型:", triggerType)
    
    if triggerType == "login" then
        -- 调用登录检查
        if UIMainTitle and UIMainTitle.CheckAndEnsureEquipAssociationForLogin then
            UIMainTitle.CheckAndEnsureEquipAssociationForLogin()
        else
            print("11111[TestCommands] UIMainTitle登录检查函数不存在")
        end
    elseif triggerType == "battle" then
        -- 调用战斗检查
        if BattleManager and BattleManager.CheckAndEnsureEquipAssociationForBattle then
            BattleManager.CheckAndEnsureEquipAssociationForBattle()
        else
            print("11111[TestCommands] BattleManager战斗检查函数不存在")
        end
    else
        -- 手动检查，调用所有检查
        print("11111[TestCommands] 执行手动装备关联检查")
        local currentEquipIDs = GamePlayerData.ActorEquip:GetWeaponsKnapsack(1) or {}
        local actualEquipCount = 0
        local mainEquipID = 0
        for i, equipID in ipairs(currentEquipIDs) do
            if equipID and equipID > 0 then
                actualEquipCount = actualEquipCount + 1
                if i == 1 then
                    mainEquipID = equipID
                end
            end
        end
        
        if actualEquipCount == 1 and mainEquipID > 0 then
            local associatedEquipIDs = m.GetAllAssociatedEquipIDs(mainEquipID)
            for _, associatedEquipID in ipairs(associatedEquipIDs) do
                local isAlreadyWorn = false
                for _, wornEquipID in ipairs(currentEquipIDs) do
                    if wornEquipID == associatedEquipID then
                        isAlreadyWorn = true
                        break
                    end
                end
                
                if not isAlreadyWorn then
                    GamePlayerData.ActorEquip:ReplaceEquipIndex(associatedEquipID, 2, 1)
                    print("11111[TestCommands] 已设置关联装备", associatedEquipID, "为出战")
                end
            end
        end
    end
    
    print("11111[TestCommands] ========== 强制装备关联检查完成 ==========")
end

-- 查看指定装备的关联信息
function m.ViewEquipAssociation(equipID)
    equipID = tonumber(equipID)
    if not equipID or equipID <= 0 then
        print("11111[TestCommands] 无效的装备ID:", equipID)
        return
    end
    
    print("11111[TestCommands] ========== 装备关联信息 ==========")
    print("11111[TestCommands] 查询装备ID:", equipID)
    
    local equipConfig = Schemes.EquipWeapon:Get(equipID)
    if not equipConfig then
        print("11111[TestCommands] 装备配置不存在")
        return
    end
    
    print("11111[TestCommands] 装备GroupID:", equipConfig.GroupID)
    print("11111[TestCommands] EffectID2:", equipConfig.EffectID2 or 0)
    print("11111[TestCommands] EffectID3:", equipConfig.EffectID3 or 0)
    
    local associatedEquipIDs = m.GetAllAssociatedEquipIDs(equipID)
    print("11111[TestCommands] 所有关联装备:", table.concat(associatedEquipIDs, ","))
    
    print("11111[TestCommands] ========== 装备关联信息结束 ==========")
end

-- 测试循环关联检测
function m.TestCircularAssociation(equipID1, equipID2)
    equipID1 = tonumber(equipID1)
    equipID2 = tonumber(equipID2)
    
    if not equipID1 or not equipID2 then
        print("11111[TestCommands] 需要提供两个装备ID进行循环关联测试")
        return
    end
    
    print("11111[TestCommands] ========== 循环关联检测测试 ==========")
    print("11111[TestCommands] 测试装备ID1:", equipID1)
    print("11111[TestCommands] 测试装备ID2:", equipID2)
    
    local visitedEquipIDs = {}
    local result1 = m.GetAllAssociatedEquipIDs(equipID1, visitedEquipIDs, 1)
    print("11111[TestCommands] 装备", equipID1, "的关联装备:", table.concat(result1, ","))
    
    visitedEquipIDs = {}
    local result2 = m.GetAllAssociatedEquipIDs(equipID2, visitedEquipIDs, 1)
    print("11111[TestCommands] 装备", equipID2, "的关联装备:", table.concat(result2, ","))
    
    print("11111[TestCommands] ========== 循环关联检测测试完成 ==========")
end

-- 清理装备关联缓存（如果有的话）
function m.ClearEquipCache()
    print("11111[TestCommands] ========== 清理装备关联缓存 ==========")
    -- 这里可以添加缓存清理逻辑
    print("11111[TestCommands] 装备关联缓存已清理")
    print("11111[TestCommands] ========== 缓存清理完成 ==========")
end

--------------------------------------------------------------------
-- 递归获取装备的所有关联装备ID（支持EffectID2和EffectID3）
--------------------------------------------------------------------
function m.GetAllAssociatedEquipIDs(equipID, visitedEquipIDs, depth)
    visitedEquipIDs = visitedEquipIDs or {}
    depth = depth or 1
    
    -- 防止无限递归，最大深度10层
    if depth > 10 then
        print("11111[TestCommands] 达到最大关联深度10层，停止递归")
        return {}
    end
    
    -- 防止循环关联
    if visitedEquipIDs[equipID] then
        print("11111[TestCommands] 检测到循环关联，装备ID:", equipID)
        return {}
    end
    
    visitedEquipIDs[equipID] = true
    
    -- 获取装备配置
    local equipConfig = Schemes.EquipWeapon:Get(equipID)
    if not equipConfig then
        print("11111[TestCommands] 装备配置不存在，装备ID:", equipID)
        return {}
    end
    
    local associatedEquipIDs = {}
    
    -- 检查EffectID2字段
    if equipConfig.EffectID2 and equipConfig.EffectID2 > 0 then
        local effectID2Config = Schemes.EquipWeapon:Get(equipConfig.EffectID2)
        if effectID2Config and effectID2Config.GroupID >= 100 then
            table.insert(associatedEquipIDs, equipConfig.EffectID2)
            print("11111[TestCommands] 找到EffectID2关联装备:", equipConfig.EffectID2)
            
            -- 递归获取关联装备的关联装备
            local subAssociatedIDs = m.GetAllAssociatedEquipIDs(equipConfig.EffectID2, visitedEquipIDs, depth + 1)
            for _, subID in ipairs(subAssociatedIDs) do
                table.insert(associatedEquipIDs, subID)
            end
        end
    end
    
    -- 检查EffectID3字段
    if equipConfig.EffectID3 and equipConfig.EffectID3 > 0 then
        local effectID3Config = Schemes.EquipWeapon:Get(equipConfig.EffectID3)
        if effectID3Config and effectID3Config.GroupID >= 100 then
            table.insert(associatedEquipIDs, equipConfig.EffectID3)
            print("11111[TestCommands] 找到EffectID3关联装备:", equipConfig.EffectID3)
            
            -- 递归获取关联装备的关联装备
            local subAssociatedIDs = m.GetAllAssociatedEquipIDs(equipConfig.EffectID3, visitedEquipIDs, depth + 1)
            for _, subID in ipairs(subAssociatedIDs) do
                table.insert(associatedEquipIDs, subID)
            end
        end
    end
    
    return associatedEquipIDs
end

-- 导出测试命令到全局
EquipAssociationTestCommands = m

return m
