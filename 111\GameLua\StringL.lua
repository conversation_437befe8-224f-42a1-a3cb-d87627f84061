local stringx = require 'pl_stringx'

---@class StringL
local this = {}

---判断字符串是否为空
---@param s string
---@return boolean
function this.IsEmpty( s )
    return s == nil or type(s) ~= "string" or s == ''
end

---判断是否为空白字符
function this.IsEmptyOrWhiteSpace(s)
    return this.IsEmpty(s) or string.find(s,"^%s*$") ~= nil
end

---判断字符串是否不为空
---@param s string
---@return boolean
function this.IsNotEmpty(s)
    return not this.IsEmpty(s)
end

---获取子串的下标
---@param haystack string 源字符串
---@param needle string 要搜索的字符串
---@return string 搜索到的下标, 如果搜索不到, 返回-1
function this.LastIndexOf(haystack, needle)
    local i=haystack:match(".*"..needle.."()")
    if i==nil then return nil else return i-1 end
end



local  string_gsub = string.gsub
local function lua_string_split(s, p)
    local rt = {}
    string_gsub(s, '[^'..p..']+', function(w) rt[#rt+1]=(w) end )
    return rt
end

---分割字符串
---@param str string 被分割的字符串
---@param sep string 分割符
---@return string[] 分割后的字符数组
function this.Split(str, sep)
    --local sep, fields = sep or ":", {}
    --local pattern = string.format("([^%s]+)", sep)
    --str:gsub(pattern, function(c) fields[#fields+1] = c end)
    --return fields
    return lua_string_split(str, sep)
end

---替换字符中的{0}，{1}
---@param content string 替换的目标
function this.ReplaceContent(content,...)
    if content == nil then
        return ""
    end
    for i = 1, select("#",...) do
        local nextValue = select(i, ...) or ""
        content = string.gsub(content, "{" .. (i - 1) .. "}", nextValue);
    end
    return content;
end

--- 解析奖励类型字符串
---@param rewardStr string 奖励字符串形式
---@return table[] {{type, id, num}, {type, id, num}}
function this.ParseRewardStr(rewardStr)
    local rewards = {}
    local rewardArray = this.Split(rewardStr, ";")
    for i, itemInfo in ipairs(rewardArray) do
        local itemInfoArray = this.Split(itemInfo, ",")
        rewards[#rewards + 1] = itemInfoArray
    end
    return rewards
end
--- 解析奖励类型字符串
---@param rewardStr string 奖励字符串形式
---@return table[] {{type, id, num}, {type, id, num}}
function this.ParseRewardDataStr(rewardStr)
    local rewards = {}
    local rewardArray = this.Split(rewardStr, ";")
    local index = 0
    for i, itemInfo in ipairs(rewardArray) do
        index = index + 1
        local itemInfoArray = this.Split(itemInfo, ",")
        rewards[index] = {
            type = tonumber(itemInfoArray[1]),
            id = tonumber(itemInfoArray[2]),
            num = tonumber(itemInfoArray[3]),
        }
    end
    return rewards
end

---@param textStr string @文本
---@param colorText string @颜色（十六进制文本格式，eg："FF0000"）
---@return string @文本（eg：<color=FF0000>文本</color>）
function this.SetColoredText(textStr, colorText)
    local format = "<color=#{0}>{1}</color>"
    return StringL.ReplaceContent(format, colorText, textStr)
end

--- 将协议中的返回值转换为键值对
---
---	输入格式："key1=value1&key2=value2"
---
---	返回格式：{key1 = value1, key2 = value2}
function this.StringToParam(content)
    local result = {}
    if this.IsNotEmpty(content) then
        local paramArray = HelperL.Split(content, '&')
        for _, paramStr in ipairs(paramArray) do
            local param = HelperL.Split(paramStr, '=')
            if #param == 2 then
                result[param[1]] = param[2]
            end
        end
    end
    return result
end

--- 将协议中的【通用奖励字符串】转换为【奖励表】，例子如下：
---
--- 输入格式："1x100+2x100"
---
---	返回格式：{[1] = {id = ?, num = ?}, [2] = {id = ?, num = ?}}
---@return CommonPrize[]
function this.StringToPrizes(content)
    local result = {}
    if this.IsNotEmpty(content) then
        local prizeStrArray = HelperL.Split(content, '+')	-- {"1x100", "2x100"}
        for _, prizeStr in ipairs(prizeStrArray) do
            local param = HelperL.Split(prizeStr, 'x')
            local id = tonumber(param[1])
            local num = tonumber(param[2])
            if #param == 2 and id and num then
                local item = {id = id, num = num}	-- {id = 1, num = 100}
                table.insert(result, item)
            end
        end
    end
    return result
end

return this


