using UnityEngine;

namespace View
{
    /// <summary>
    /// 强制控制渲染排序层级组件
    /// </summary>
    public class SortingOrderController : MonoBehaviour
    {
        /// <summary>
        /// 控制的渲染器
        /// </summary>
        public MeshRenderer TargetRenderer { get; private set; }
        
        /// <summary>
        /// 正确的排序层级值
        /// </summary>
        public int CorrectSortingOrder { get; set; } = -1000;
        
        /// <summary>
        /// 上次检查时间
        /// </summary>
        private float _lastCheckTime;
        
        /// <summary>
        /// 是否已初始化
        /// </summary>
        private bool _initialized = false;
        
        /// <summary>
        /// Unity允许的排序层级最小值(-32768)
        /// </summary>
        private const int MIN_SORTING_VALUE = -32768;
        
        /// <summary>
        /// Unity允许的排序层级最大值(32767)
        /// </summary>
        private const int MAX_SORTING_VALUE = 32767;

        /// <summary>
        /// 初始化控制器
        /// </summary>
        public void Initialize(MeshRenderer renderer, int correctSortingOrder)
        {
            TargetRenderer = renderer;
            
            // 确保层级值在Unity的合法范围内
            if (correctSortingOrder < MIN_SORTING_VALUE)
                correctSortingOrder = MIN_SORTING_VALUE;
            if (correctSortingOrder > MAX_SORTING_VALUE)
                correctSortingOrder = MAX_SORTING_VALUE;
                
            CorrectSortingOrder = correctSortingOrder;
            _initialized = true;
            
            // 立即应用正确的层级值
            ApplyCorrectSortingOrder();
        }
        
        private void Awake()
        {
            _lastCheckTime = Time.time;
        }
        
        private void OnEnable()
        {
            // 确保在启用时应用正确的层级
            if (_initialized)
            {
                ApplyCorrectSortingOrder();
            }
        }
        
        private void LateUpdate()
        {
            // 每帧末尾检查并确保正确的层级值
            if (_initialized && TargetRenderer != null)
            {
                ApplyCorrectSortingOrder();
            }
        }
        
        /// <summary>
        /// 应用正确的排序层级
        /// </summary>
        public void ApplyCorrectSortingOrder()
        {
            if (TargetRenderer == null) return;
            
            // 确保值在合法范围内
            if (CorrectSortingOrder < MIN_SORTING_VALUE)
                CorrectSortingOrder = MIN_SORTING_VALUE;
            if (CorrectSortingOrder > MAX_SORTING_VALUE)
                CorrectSortingOrder = MAX_SORTING_VALUE;
            
            if (TargetRenderer.sortingOrder != CorrectSortingOrder)
            {
                // 尝试设置层级值前记录原值
                int oldValue = TargetRenderer.sortingOrder;
                
                // 应用正确值
                TargetRenderer.sortingOrder = CorrectSortingOrder;
                _lastCheckTime = Time.time;
                
                // 防御性检查: 如果设置后值仍然不正确，记录错误
                if (TargetRenderer.sortingOrder != CorrectSortingOrder)
                {
                    //Debug.LogError($"严重错误: 无法设置渲染层级! 对象={gameObject.name}, 目标={CorrectSortingOrder}, 实际={TargetRenderer.sortingOrder}, 原值={oldValue}");
                    
                    // 尝试使用备用方法设置
                    try
                    {
                        // 立即再次尝试
                        TargetRenderer.sortingOrder = CorrectSortingOrder;
                        
                        // 最后尝试使用反射方式设置
                        var sortingOrderField = typeof(Renderer).GetProperty("sortingOrder");
                        if (sortingOrderField != null)
                        {
                            sortingOrderField.SetValue(TargetRenderer, CorrectSortingOrder, null);
                        }
                    }
                    catch (System.Exception ex)
                    {
                     //   Debug.LogError($"严重错误: 备用方法设置层级失败! {ex.Message}");
                    }
                }
            }
        }
    }
}