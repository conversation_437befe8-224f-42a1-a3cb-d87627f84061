-- Generated By protoc-gen-lua Do not Edit
local protobuf = require "protobuf/protobuf"
module('RankMessage_pb')
local pb = {}


pb.MSG_RANK_ACTIONID = protobuf.EnumDescriptor();
pb.MSG_RANK_ACTIONID_MSG_RANK_NONE_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_RANK_ACTIONID_MSG_RANK_GETLIST_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_RANK_ACTIONID_MSG_RANK_CHANGE_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_RANK_ACTIONID_MSG_RANK_GETMATCHRECORDDATA_ENUM = protobuf.EnumValueDescriptor();
pb.CS_RANK_GETRANKLIST = protobuf.Descriptor();
pb.CS_RANK_GETRANKLIST_RANKTYPE_FIELD = protobuf.FieldDescriptor();
pb.SC_RANK_GETRANKLIST = protobuf.Descriptor();
pb.SC_RANK_GETRANKLIST_RANKDATA = protobuf.Descriptor();
pb.SC_RANK_GETRANKLIST_RANKDATA_ACTORID_FIELD = protobuf.FieldDescriptor();
pb.SC_RANK_GETRANKLIST_RANKDATA_ACTORNAME_FIELD = protobuf.FieldDescriptor();
pb.SC_RANK_GETRANKLIST_RANKDATA_LEVEL_FIELD = protobuf.FieldDescriptor();
pb.SC_RANK_GETRANKLIST_RANKDATA_VOCATION_FIELD = protobuf.FieldDescriptor();
pb.SC_RANK_GETRANKLIST_RANKDATA_VALUE1_FIELD = protobuf.FieldDescriptor();
pb.SC_RANK_GETRANKLIST_RANKDATA_VALUE2_FIELD = protobuf.FieldDescriptor();
pb.SC_RANK_GETRANKLIST_RANKDATA_VIPLEVEL_FIELD = protobuf.FieldDescriptor();
pb.SC_RANK_GETRANKLIST_RANKDATA_BLUEVIPLEVEL_FIELD = protobuf.FieldDescriptor();
pb.SC_RANK_GETRANKLIST_RANKDATA_BLUEVIPTYPE_FIELD = protobuf.FieldDescriptor();
pb.SC_RANK_GETRANKLIST_RANKDATA_GENRE_FIELD = protobuf.FieldDescriptor();
pb.SC_RANK_GETRANKLIST_RANKTYPE_FIELD = protobuf.FieldDescriptor();
pb.SC_RANK_GETRANKLIST_SELFRANK_FIELD = protobuf.FieldDescriptor();
pb.SC_RANK_GETRANKLIST_RANKLIST_FIELD = protobuf.FieldDescriptor();
pb.SC_RANK_GETRANKLIST_SELFRANKVALUE_FIELD = protobuf.FieldDescriptor();
pb.SC_RANK_CHANGE = protobuf.Descriptor();
pb.SC_RANK_CHANGE_RANKTYPE_FIELD = protobuf.FieldDescriptor();
pb.SC_RANK_CHANGE_OLDRANK_FIELD = protobuf.FieldDescriptor();
pb.SC_RANK_CHANGE_NEWRANK_FIELD = protobuf.FieldDescriptor();
pb.SC_RANK_GETMATCHRECORDDATA = protobuf.Descriptor();
pb.SC_RANK_GETMATCHRECORDDATA_STAGECHALLENGERECORDPARAM1_FIELD = protobuf.FieldDescriptor();
pb.SC_RANK_GETMATCHRECORDDATA_STAGECHALLENGERECORDPARAM2_FIELD = protobuf.FieldDescriptor();
pb.SC_RANK_GETMATCHRECORDDATA_MATCHRANKRECORDPARAM1_FIELD = protobuf.FieldDescriptor();
pb.SC_RANK_GETMATCHRECORDDATA_MATCHRANKRECORDPARAM2_FIELD = protobuf.FieldDescriptor();
pb.SC_RANK_GETMATCHRECORDDATA_MATCHSCORERANKRECORDPARAM1_FIELD = protobuf.FieldDescriptor();
pb.SC_RANK_GETMATCHRECORDDATA_MATCHSCORERANKRECORDPARAM2_FIELD = protobuf.FieldDescriptor();

pb.MSG_RANK_ACTIONID_MSG_RANK_NONE_ENUM.name = "MSG_RANK_NONE"
pb.MSG_RANK_ACTIONID_MSG_RANK_NONE_ENUM.index = 0
pb.MSG_RANK_ACTIONID_MSG_RANK_NONE_ENUM.number = 0
pb.MSG_RANK_ACTIONID_MSG_RANK_GETLIST_ENUM.name = "MSG_RANK_GETLIST"
pb.MSG_RANK_ACTIONID_MSG_RANK_GETLIST_ENUM.index = 1
pb.MSG_RANK_ACTIONID_MSG_RANK_GETLIST_ENUM.number = 1
pb.MSG_RANK_ACTIONID_MSG_RANK_CHANGE_ENUM.name = "MSG_RANK_CHANGE"
pb.MSG_RANK_ACTIONID_MSG_RANK_CHANGE_ENUM.index = 2
pb.MSG_RANK_ACTIONID_MSG_RANK_CHANGE_ENUM.number = 2
pb.MSG_RANK_ACTIONID_MSG_RANK_GETMATCHRECORDDATA_ENUM.name = "MSG_RANK_GETMATCHRECORDDATA"
pb.MSG_RANK_ACTIONID_MSG_RANK_GETMATCHRECORDDATA_ENUM.index = 3
pb.MSG_RANK_ACTIONID_MSG_RANK_GETMATCHRECORDDATA_ENUM.number = 3
pb.MSG_RANK_ACTIONID.name = "MSG_RANK_ACTIONID"
pb.MSG_RANK_ACTIONID.full_name = ".MSG_RANK_ACTIONID"
pb.MSG_RANK_ACTIONID.values = {pb.MSG_RANK_ACTIONID_MSG_RANK_NONE_ENUM,pb.MSG_RANK_ACTIONID_MSG_RANK_GETLIST_ENUM,pb.MSG_RANK_ACTIONID_MSG_RANK_CHANGE_ENUM,pb.MSG_RANK_ACTIONID_MSG_RANK_GETMATCHRECORDDATA_ENUM}
pb.CS_RANK_GETRANKLIST_RANKTYPE_FIELD.name = "RankType"
pb.CS_RANK_GETRANKLIST_RANKTYPE_FIELD.full_name = ".CS_Rank_GetRankList.RankType"
pb.CS_RANK_GETRANKLIST_RANKTYPE_FIELD.number = 1
pb.CS_RANK_GETRANKLIST_RANKTYPE_FIELD.index = 0
pb.CS_RANK_GETRANKLIST_RANKTYPE_FIELD.label = 2
pb.CS_RANK_GETRANKLIST_RANKTYPE_FIELD.has_default_value = false
pb.CS_RANK_GETRANKLIST_RANKTYPE_FIELD.default_value = 0
pb.CS_RANK_GETRANKLIST_RANKTYPE_FIELD.type = 13
pb.CS_RANK_GETRANKLIST_RANKTYPE_FIELD.cpp_type = 3

pb.CS_RANK_GETRANKLIST.name = "CS_Rank_GetRankList"
pb.CS_RANK_GETRANKLIST.full_name = ".CS_Rank_GetRankList"
pb.CS_RANK_GETRANKLIST.nested_types = {}
pb.CS_RANK_GETRANKLIST.enum_types = {}
pb.CS_RANK_GETRANKLIST.fields = {pb.CS_RANK_GETRANKLIST_RANKTYPE_FIELD}
pb.CS_RANK_GETRANKLIST.is_extendable = false
pb.CS_RANK_GETRANKLIST.extensions = {}
pb.SC_RANK_GETRANKLIST_RANKDATA_ACTORID_FIELD.name = "ActorID"
pb.SC_RANK_GETRANKLIST_RANKDATA_ACTORID_FIELD.full_name = ".SC_Rank_GetRankList.RankData.ActorID"
pb.SC_RANK_GETRANKLIST_RANKDATA_ACTORID_FIELD.number = 1
pb.SC_RANK_GETRANKLIST_RANKDATA_ACTORID_FIELD.index = 0
pb.SC_RANK_GETRANKLIST_RANKDATA_ACTORID_FIELD.label = 2
pb.SC_RANK_GETRANKLIST_RANKDATA_ACTORID_FIELD.has_default_value = false
pb.SC_RANK_GETRANKLIST_RANKDATA_ACTORID_FIELD.default_value = 0
pb.SC_RANK_GETRANKLIST_RANKDATA_ACTORID_FIELD.type = 13
pb.SC_RANK_GETRANKLIST_RANKDATA_ACTORID_FIELD.cpp_type = 3

pb.SC_RANK_GETRANKLIST_RANKDATA_ACTORNAME_FIELD.name = "ActorName"
pb.SC_RANK_GETRANKLIST_RANKDATA_ACTORNAME_FIELD.full_name = ".SC_Rank_GetRankList.RankData.ActorName"
pb.SC_RANK_GETRANKLIST_RANKDATA_ACTORNAME_FIELD.number = 2
pb.SC_RANK_GETRANKLIST_RANKDATA_ACTORNAME_FIELD.index = 1
pb.SC_RANK_GETRANKLIST_RANKDATA_ACTORNAME_FIELD.label = 2
pb.SC_RANK_GETRANKLIST_RANKDATA_ACTORNAME_FIELD.has_default_value = false
pb.SC_RANK_GETRANKLIST_RANKDATA_ACTORNAME_FIELD.default_value = ""
pb.SC_RANK_GETRANKLIST_RANKDATA_ACTORNAME_FIELD.type = 9
pb.SC_RANK_GETRANKLIST_RANKDATA_ACTORNAME_FIELD.cpp_type = 9

pb.SC_RANK_GETRANKLIST_RANKDATA_LEVEL_FIELD.name = "Level"
pb.SC_RANK_GETRANKLIST_RANKDATA_LEVEL_FIELD.full_name = ".SC_Rank_GetRankList.RankData.Level"
pb.SC_RANK_GETRANKLIST_RANKDATA_LEVEL_FIELD.number = 3
pb.SC_RANK_GETRANKLIST_RANKDATA_LEVEL_FIELD.index = 2
pb.SC_RANK_GETRANKLIST_RANKDATA_LEVEL_FIELD.label = 2
pb.SC_RANK_GETRANKLIST_RANKDATA_LEVEL_FIELD.has_default_value = false
pb.SC_RANK_GETRANKLIST_RANKDATA_LEVEL_FIELD.default_value = 0
pb.SC_RANK_GETRANKLIST_RANKDATA_LEVEL_FIELD.type = 13
pb.SC_RANK_GETRANKLIST_RANKDATA_LEVEL_FIELD.cpp_type = 3

pb.SC_RANK_GETRANKLIST_RANKDATA_VOCATION_FIELD.name = "Vocation"
pb.SC_RANK_GETRANKLIST_RANKDATA_VOCATION_FIELD.full_name = ".SC_Rank_GetRankList.RankData.Vocation"
pb.SC_RANK_GETRANKLIST_RANKDATA_VOCATION_FIELD.number = 4
pb.SC_RANK_GETRANKLIST_RANKDATA_VOCATION_FIELD.index = 3
pb.SC_RANK_GETRANKLIST_RANKDATA_VOCATION_FIELD.label = 2
pb.SC_RANK_GETRANKLIST_RANKDATA_VOCATION_FIELD.has_default_value = false
pb.SC_RANK_GETRANKLIST_RANKDATA_VOCATION_FIELD.default_value = 0
pb.SC_RANK_GETRANKLIST_RANKDATA_VOCATION_FIELD.type = 13
pb.SC_RANK_GETRANKLIST_RANKDATA_VOCATION_FIELD.cpp_type = 3

pb.SC_RANK_GETRANKLIST_RANKDATA_VALUE1_FIELD.name = "Value1"
pb.SC_RANK_GETRANKLIST_RANKDATA_VALUE1_FIELD.full_name = ".SC_Rank_GetRankList.RankData.Value1"
pb.SC_RANK_GETRANKLIST_RANKDATA_VALUE1_FIELD.number = 5
pb.SC_RANK_GETRANKLIST_RANKDATA_VALUE1_FIELD.index = 4
pb.SC_RANK_GETRANKLIST_RANKDATA_VALUE1_FIELD.label = 2
pb.SC_RANK_GETRANKLIST_RANKDATA_VALUE1_FIELD.has_default_value = false
pb.SC_RANK_GETRANKLIST_RANKDATA_VALUE1_FIELD.default_value = 0
pb.SC_RANK_GETRANKLIST_RANKDATA_VALUE1_FIELD.type = 5
pb.SC_RANK_GETRANKLIST_RANKDATA_VALUE1_FIELD.cpp_type = 1

pb.SC_RANK_GETRANKLIST_RANKDATA_VALUE2_FIELD.name = "Value2"
pb.SC_RANK_GETRANKLIST_RANKDATA_VALUE2_FIELD.full_name = ".SC_Rank_GetRankList.RankData.Value2"
pb.SC_RANK_GETRANKLIST_RANKDATA_VALUE2_FIELD.number = 6
pb.SC_RANK_GETRANKLIST_RANKDATA_VALUE2_FIELD.index = 5
pb.SC_RANK_GETRANKLIST_RANKDATA_VALUE2_FIELD.label = 2
pb.SC_RANK_GETRANKLIST_RANKDATA_VALUE2_FIELD.has_default_value = false
pb.SC_RANK_GETRANKLIST_RANKDATA_VALUE2_FIELD.default_value = 0
pb.SC_RANK_GETRANKLIST_RANKDATA_VALUE2_FIELD.type = 5
pb.SC_RANK_GETRANKLIST_RANKDATA_VALUE2_FIELD.cpp_type = 1

pb.SC_RANK_GETRANKLIST_RANKDATA_VIPLEVEL_FIELD.name = "VipLevel"
pb.SC_RANK_GETRANKLIST_RANKDATA_VIPLEVEL_FIELD.full_name = ".SC_Rank_GetRankList.RankData.VipLevel"
pb.SC_RANK_GETRANKLIST_RANKDATA_VIPLEVEL_FIELD.number = 7
pb.SC_RANK_GETRANKLIST_RANKDATA_VIPLEVEL_FIELD.index = 6
pb.SC_RANK_GETRANKLIST_RANKDATA_VIPLEVEL_FIELD.label = 2
pb.SC_RANK_GETRANKLIST_RANKDATA_VIPLEVEL_FIELD.has_default_value = false
pb.SC_RANK_GETRANKLIST_RANKDATA_VIPLEVEL_FIELD.default_value = 0
pb.SC_RANK_GETRANKLIST_RANKDATA_VIPLEVEL_FIELD.type = 13
pb.SC_RANK_GETRANKLIST_RANKDATA_VIPLEVEL_FIELD.cpp_type = 3

pb.SC_RANK_GETRANKLIST_RANKDATA_BLUEVIPLEVEL_FIELD.name = "BlueVIPLevel"
pb.SC_RANK_GETRANKLIST_RANKDATA_BLUEVIPLEVEL_FIELD.full_name = ".SC_Rank_GetRankList.RankData.BlueVIPLevel"
pb.SC_RANK_GETRANKLIST_RANKDATA_BLUEVIPLEVEL_FIELD.number = 8
pb.SC_RANK_GETRANKLIST_RANKDATA_BLUEVIPLEVEL_FIELD.index = 7
pb.SC_RANK_GETRANKLIST_RANKDATA_BLUEVIPLEVEL_FIELD.label = 2
pb.SC_RANK_GETRANKLIST_RANKDATA_BLUEVIPLEVEL_FIELD.has_default_value = false
pb.SC_RANK_GETRANKLIST_RANKDATA_BLUEVIPLEVEL_FIELD.default_value = 0
pb.SC_RANK_GETRANKLIST_RANKDATA_BLUEVIPLEVEL_FIELD.type = 13
pb.SC_RANK_GETRANKLIST_RANKDATA_BLUEVIPLEVEL_FIELD.cpp_type = 3

pb.SC_RANK_GETRANKLIST_RANKDATA_BLUEVIPTYPE_FIELD.name = "BlueVIPType"
pb.SC_RANK_GETRANKLIST_RANKDATA_BLUEVIPTYPE_FIELD.full_name = ".SC_Rank_GetRankList.RankData.BlueVIPType"
pb.SC_RANK_GETRANKLIST_RANKDATA_BLUEVIPTYPE_FIELD.number = 9
pb.SC_RANK_GETRANKLIST_RANKDATA_BLUEVIPTYPE_FIELD.index = 8
pb.SC_RANK_GETRANKLIST_RANKDATA_BLUEVIPTYPE_FIELD.label = 2
pb.SC_RANK_GETRANKLIST_RANKDATA_BLUEVIPTYPE_FIELD.has_default_value = false
pb.SC_RANK_GETRANKLIST_RANKDATA_BLUEVIPTYPE_FIELD.default_value = 0
pb.SC_RANK_GETRANKLIST_RANKDATA_BLUEVIPTYPE_FIELD.type = 13
pb.SC_RANK_GETRANKLIST_RANKDATA_BLUEVIPTYPE_FIELD.cpp_type = 3

pb.SC_RANK_GETRANKLIST_RANKDATA_GENRE_FIELD.name = "Genre"
pb.SC_RANK_GETRANKLIST_RANKDATA_GENRE_FIELD.full_name = ".SC_Rank_GetRankList.RankData.Genre"
pb.SC_RANK_GETRANKLIST_RANKDATA_GENRE_FIELD.number = 10
pb.SC_RANK_GETRANKLIST_RANKDATA_GENRE_FIELD.index = 9
pb.SC_RANK_GETRANKLIST_RANKDATA_GENRE_FIELD.label = 2
pb.SC_RANK_GETRANKLIST_RANKDATA_GENRE_FIELD.has_default_value = false
pb.SC_RANK_GETRANKLIST_RANKDATA_GENRE_FIELD.default_value = 0
pb.SC_RANK_GETRANKLIST_RANKDATA_GENRE_FIELD.type = 13
pb.SC_RANK_GETRANKLIST_RANKDATA_GENRE_FIELD.cpp_type = 3

pb.SC_RANK_GETRANKLIST_RANKDATA.name = "RankData"
pb.SC_RANK_GETRANKLIST_RANKDATA.full_name = ".SC_Rank_GetRankList.RankData"
pb.SC_RANK_GETRANKLIST_RANKDATA.nested_types = {}
pb.SC_RANK_GETRANKLIST_RANKDATA.enum_types = {}
pb.SC_RANK_GETRANKLIST_RANKDATA.fields = {pb.SC_RANK_GETRANKLIST_RANKDATA_ACTORID_FIELD, pb.SC_RANK_GETRANKLIST_RANKDATA_ACTORNAME_FIELD, pb.SC_RANK_GETRANKLIST_RANKDATA_LEVEL_FIELD, pb.SC_RANK_GETRANKLIST_RANKDATA_VOCATION_FIELD, pb.SC_RANK_GETRANKLIST_RANKDATA_VALUE1_FIELD, pb.SC_RANK_GETRANKLIST_RANKDATA_VALUE2_FIELD, pb.SC_RANK_GETRANKLIST_RANKDATA_VIPLEVEL_FIELD, pb.SC_RANK_GETRANKLIST_RANKDATA_BLUEVIPLEVEL_FIELD, pb.SC_RANK_GETRANKLIST_RANKDATA_BLUEVIPTYPE_FIELD, pb.SC_RANK_GETRANKLIST_RANKDATA_GENRE_FIELD}
pb.SC_RANK_GETRANKLIST_RANKDATA.is_extendable = false
pb.SC_RANK_GETRANKLIST_RANKDATA.extensions = {}
pb.SC_RANK_GETRANKLIST_RANKDATA.containing_type = pb.SC_RANK_GETRANKLIST
pb.SC_RANK_GETRANKLIST_RANKTYPE_FIELD.name = "RankType"
pb.SC_RANK_GETRANKLIST_RANKTYPE_FIELD.full_name = ".SC_Rank_GetRankList.RankType"
pb.SC_RANK_GETRANKLIST_RANKTYPE_FIELD.number = 1
pb.SC_RANK_GETRANKLIST_RANKTYPE_FIELD.index = 0
pb.SC_RANK_GETRANKLIST_RANKTYPE_FIELD.label = 2
pb.SC_RANK_GETRANKLIST_RANKTYPE_FIELD.has_default_value = false
pb.SC_RANK_GETRANKLIST_RANKTYPE_FIELD.default_value = 0
pb.SC_RANK_GETRANKLIST_RANKTYPE_FIELD.type = 13
pb.SC_RANK_GETRANKLIST_RANKTYPE_FIELD.cpp_type = 3

pb.SC_RANK_GETRANKLIST_SELFRANK_FIELD.name = "SelfRank"
pb.SC_RANK_GETRANKLIST_SELFRANK_FIELD.full_name = ".SC_Rank_GetRankList.SelfRank"
pb.SC_RANK_GETRANKLIST_SELFRANK_FIELD.number = 2
pb.SC_RANK_GETRANKLIST_SELFRANK_FIELD.index = 1
pb.SC_RANK_GETRANKLIST_SELFRANK_FIELD.label = 2
pb.SC_RANK_GETRANKLIST_SELFRANK_FIELD.has_default_value = false
pb.SC_RANK_GETRANKLIST_SELFRANK_FIELD.default_value = 0
pb.SC_RANK_GETRANKLIST_SELFRANK_FIELD.type = 13
pb.SC_RANK_GETRANKLIST_SELFRANK_FIELD.cpp_type = 3

pb.SC_RANK_GETRANKLIST_RANKLIST_FIELD.name = "RankList"
pb.SC_RANK_GETRANKLIST_RANKLIST_FIELD.full_name = ".SC_Rank_GetRankList.RankList"
pb.SC_RANK_GETRANKLIST_RANKLIST_FIELD.number = 3
pb.SC_RANK_GETRANKLIST_RANKLIST_FIELD.index = 2
pb.SC_RANK_GETRANKLIST_RANKLIST_FIELD.label = 3
pb.SC_RANK_GETRANKLIST_RANKLIST_FIELD.has_default_value = false
pb.SC_RANK_GETRANKLIST_RANKLIST_FIELD.default_value = {}
pb.SC_RANK_GETRANKLIST_RANKLIST_FIELD.message_type = pb.SC_RANK_GETRANKLIST_RANKDATA
pb.SC_RANK_GETRANKLIST_RANKLIST_FIELD.type = 11
pb.SC_RANK_GETRANKLIST_RANKLIST_FIELD.cpp_type = 10

pb.SC_RANK_GETRANKLIST_SELFRANKVALUE_FIELD.name = "SelfRankValue"
pb.SC_RANK_GETRANKLIST_SELFRANKVALUE_FIELD.full_name = ".SC_Rank_GetRankList.SelfRankValue"
pb.SC_RANK_GETRANKLIST_SELFRANKVALUE_FIELD.number = 4
pb.SC_RANK_GETRANKLIST_SELFRANKVALUE_FIELD.index = 3
pb.SC_RANK_GETRANKLIST_SELFRANKVALUE_FIELD.label = 2
pb.SC_RANK_GETRANKLIST_SELFRANKVALUE_FIELD.has_default_value = false
pb.SC_RANK_GETRANKLIST_SELFRANKVALUE_FIELD.default_value = 0
pb.SC_RANK_GETRANKLIST_SELFRANKVALUE_FIELD.type = 13
pb.SC_RANK_GETRANKLIST_SELFRANKVALUE_FIELD.cpp_type = 3

pb.SC_RANK_GETRANKLIST.name = "SC_Rank_GetRankList"
pb.SC_RANK_GETRANKLIST.full_name = ".SC_Rank_GetRankList"
pb.SC_RANK_GETRANKLIST.nested_types = {pb.SC_RANK_GETRANKLIST_RANKDATA}
pb.SC_RANK_GETRANKLIST.enum_types = {}
pb.SC_RANK_GETRANKLIST.fields = {pb.SC_RANK_GETRANKLIST_RANKTYPE_FIELD, pb.SC_RANK_GETRANKLIST_SELFRANK_FIELD, pb.SC_RANK_GETRANKLIST_RANKLIST_FIELD, pb.SC_RANK_GETRANKLIST_SELFRANKVALUE_FIELD}
pb.SC_RANK_GETRANKLIST.is_extendable = false
pb.SC_RANK_GETRANKLIST.extensions = {}
pb.SC_RANK_CHANGE_RANKTYPE_FIELD.name = "RankType"
pb.SC_RANK_CHANGE_RANKTYPE_FIELD.full_name = ".SC_Rank_Change.RankType"
pb.SC_RANK_CHANGE_RANKTYPE_FIELD.number = 1
pb.SC_RANK_CHANGE_RANKTYPE_FIELD.index = 0
pb.SC_RANK_CHANGE_RANKTYPE_FIELD.label = 1
pb.SC_RANK_CHANGE_RANKTYPE_FIELD.has_default_value = false
pb.SC_RANK_CHANGE_RANKTYPE_FIELD.default_value = 0
pb.SC_RANK_CHANGE_RANKTYPE_FIELD.type = 5
pb.SC_RANK_CHANGE_RANKTYPE_FIELD.cpp_type = 1

pb.SC_RANK_CHANGE_OLDRANK_FIELD.name = "OldRank"
pb.SC_RANK_CHANGE_OLDRANK_FIELD.full_name = ".SC_Rank_Change.OldRank"
pb.SC_RANK_CHANGE_OLDRANK_FIELD.number = 2
pb.SC_RANK_CHANGE_OLDRANK_FIELD.index = 1
pb.SC_RANK_CHANGE_OLDRANK_FIELD.label = 1
pb.SC_RANK_CHANGE_OLDRANK_FIELD.has_default_value = false
pb.SC_RANK_CHANGE_OLDRANK_FIELD.default_value = 0
pb.SC_RANK_CHANGE_OLDRANK_FIELD.type = 5
pb.SC_RANK_CHANGE_OLDRANK_FIELD.cpp_type = 1

pb.SC_RANK_CHANGE_NEWRANK_FIELD.name = "NewRank"
pb.SC_RANK_CHANGE_NEWRANK_FIELD.full_name = ".SC_Rank_Change.NewRank"
pb.SC_RANK_CHANGE_NEWRANK_FIELD.number = 3
pb.SC_RANK_CHANGE_NEWRANK_FIELD.index = 2
pb.SC_RANK_CHANGE_NEWRANK_FIELD.label = 1
pb.SC_RANK_CHANGE_NEWRANK_FIELD.has_default_value = false
pb.SC_RANK_CHANGE_NEWRANK_FIELD.default_value = 0
pb.SC_RANK_CHANGE_NEWRANK_FIELD.type = 5
pb.SC_RANK_CHANGE_NEWRANK_FIELD.cpp_type = 1

pb.SC_RANK_CHANGE.name = "SC_Rank_Change"
pb.SC_RANK_CHANGE.full_name = ".SC_Rank_Change"
pb.SC_RANK_CHANGE.nested_types = {}
pb.SC_RANK_CHANGE.enum_types = {}
pb.SC_RANK_CHANGE.fields = {pb.SC_RANK_CHANGE_RANKTYPE_FIELD, pb.SC_RANK_CHANGE_OLDRANK_FIELD, pb.SC_RANK_CHANGE_NEWRANK_FIELD}
pb.SC_RANK_CHANGE.is_extendable = false
pb.SC_RANK_CHANGE.extensions = {}
pb.SC_RANK_GETMATCHRECORDDATA_STAGECHALLENGERECORDPARAM1_FIELD.name = "StageChallengeRecordParam1"
pb.SC_RANK_GETMATCHRECORDDATA_STAGECHALLENGERECORDPARAM1_FIELD.full_name = ".SC_Rank_GetMatchRecordData.StageChallengeRecordParam1"
pb.SC_RANK_GETMATCHRECORDDATA_STAGECHALLENGERECORDPARAM1_FIELD.number = 1
pb.SC_RANK_GETMATCHRECORDDATA_STAGECHALLENGERECORDPARAM1_FIELD.index = 0
pb.SC_RANK_GETMATCHRECORDDATA_STAGECHALLENGERECORDPARAM1_FIELD.label = 1
pb.SC_RANK_GETMATCHRECORDDATA_STAGECHALLENGERECORDPARAM1_FIELD.has_default_value = false
pb.SC_RANK_GETMATCHRECORDDATA_STAGECHALLENGERECORDPARAM1_FIELD.default_value = 0
pb.SC_RANK_GETMATCHRECORDDATA_STAGECHALLENGERECORDPARAM1_FIELD.type = 5
pb.SC_RANK_GETMATCHRECORDDATA_STAGECHALLENGERECORDPARAM1_FIELD.cpp_type = 1

pb.SC_RANK_GETMATCHRECORDDATA_STAGECHALLENGERECORDPARAM2_FIELD.name = "StageChallengeRecordParam2"
pb.SC_RANK_GETMATCHRECORDDATA_STAGECHALLENGERECORDPARAM2_FIELD.full_name = ".SC_Rank_GetMatchRecordData.StageChallengeRecordParam2"
pb.SC_RANK_GETMATCHRECORDDATA_STAGECHALLENGERECORDPARAM2_FIELD.number = 2
pb.SC_RANK_GETMATCHRECORDDATA_STAGECHALLENGERECORDPARAM2_FIELD.index = 1
pb.SC_RANK_GETMATCHRECORDDATA_STAGECHALLENGERECORDPARAM2_FIELD.label = 1
pb.SC_RANK_GETMATCHRECORDDATA_STAGECHALLENGERECORDPARAM2_FIELD.has_default_value = false
pb.SC_RANK_GETMATCHRECORDDATA_STAGECHALLENGERECORDPARAM2_FIELD.default_value = 0
pb.SC_RANK_GETMATCHRECORDDATA_STAGECHALLENGERECORDPARAM2_FIELD.type = 5
pb.SC_RANK_GETMATCHRECORDDATA_STAGECHALLENGERECORDPARAM2_FIELD.cpp_type = 1

pb.SC_RANK_GETMATCHRECORDDATA_MATCHRANKRECORDPARAM1_FIELD.name = "MatchRankRecordParam1"
pb.SC_RANK_GETMATCHRECORDDATA_MATCHRANKRECORDPARAM1_FIELD.full_name = ".SC_Rank_GetMatchRecordData.MatchRankRecordParam1"
pb.SC_RANK_GETMATCHRECORDDATA_MATCHRANKRECORDPARAM1_FIELD.number = 3
pb.SC_RANK_GETMATCHRECORDDATA_MATCHRANKRECORDPARAM1_FIELD.index = 2
pb.SC_RANK_GETMATCHRECORDDATA_MATCHRANKRECORDPARAM1_FIELD.label = 1
pb.SC_RANK_GETMATCHRECORDDATA_MATCHRANKRECORDPARAM1_FIELD.has_default_value = false
pb.SC_RANK_GETMATCHRECORDDATA_MATCHRANKRECORDPARAM1_FIELD.default_value = 0
pb.SC_RANK_GETMATCHRECORDDATA_MATCHRANKRECORDPARAM1_FIELD.type = 5
pb.SC_RANK_GETMATCHRECORDDATA_MATCHRANKRECORDPARAM1_FIELD.cpp_type = 1

pb.SC_RANK_GETMATCHRECORDDATA_MATCHRANKRECORDPARAM2_FIELD.name = "MatchRankRecordParam2"
pb.SC_RANK_GETMATCHRECORDDATA_MATCHRANKRECORDPARAM2_FIELD.full_name = ".SC_Rank_GetMatchRecordData.MatchRankRecordParam2"
pb.SC_RANK_GETMATCHRECORDDATA_MATCHRANKRECORDPARAM2_FIELD.number = 4
pb.SC_RANK_GETMATCHRECORDDATA_MATCHRANKRECORDPARAM2_FIELD.index = 3
pb.SC_RANK_GETMATCHRECORDDATA_MATCHRANKRECORDPARAM2_FIELD.label = 1
pb.SC_RANK_GETMATCHRECORDDATA_MATCHRANKRECORDPARAM2_FIELD.has_default_value = false
pb.SC_RANK_GETMATCHRECORDDATA_MATCHRANKRECORDPARAM2_FIELD.default_value = 0
pb.SC_RANK_GETMATCHRECORDDATA_MATCHRANKRECORDPARAM2_FIELD.type = 5
pb.SC_RANK_GETMATCHRECORDDATA_MATCHRANKRECORDPARAM2_FIELD.cpp_type = 1

pb.SC_RANK_GETMATCHRECORDDATA_MATCHSCORERANKRECORDPARAM1_FIELD.name = "MatchScoreRankRecordParam1"
pb.SC_RANK_GETMATCHRECORDDATA_MATCHSCORERANKRECORDPARAM1_FIELD.full_name = ".SC_Rank_GetMatchRecordData.MatchScoreRankRecordParam1"
pb.SC_RANK_GETMATCHRECORDDATA_MATCHSCORERANKRECORDPARAM1_FIELD.number = 5
pb.SC_RANK_GETMATCHRECORDDATA_MATCHSCORERANKRECORDPARAM1_FIELD.index = 4
pb.SC_RANK_GETMATCHRECORDDATA_MATCHSCORERANKRECORDPARAM1_FIELD.label = 1
pb.SC_RANK_GETMATCHRECORDDATA_MATCHSCORERANKRECORDPARAM1_FIELD.has_default_value = false
pb.SC_RANK_GETMATCHRECORDDATA_MATCHSCORERANKRECORDPARAM1_FIELD.default_value = 0
pb.SC_RANK_GETMATCHRECORDDATA_MATCHSCORERANKRECORDPARAM1_FIELD.type = 5
pb.SC_RANK_GETMATCHRECORDDATA_MATCHSCORERANKRECORDPARAM1_FIELD.cpp_type = 1

pb.SC_RANK_GETMATCHRECORDDATA_MATCHSCORERANKRECORDPARAM2_FIELD.name = "MatchScoreRankRecordParam2"
pb.SC_RANK_GETMATCHRECORDDATA_MATCHSCORERANKRECORDPARAM2_FIELD.full_name = ".SC_Rank_GetMatchRecordData.MatchScoreRankRecordParam2"
pb.SC_RANK_GETMATCHRECORDDATA_MATCHSCORERANKRECORDPARAM2_FIELD.number = 6
pb.SC_RANK_GETMATCHRECORDDATA_MATCHSCORERANKRECORDPARAM2_FIELD.index = 5
pb.SC_RANK_GETMATCHRECORDDATA_MATCHSCORERANKRECORDPARAM2_FIELD.label = 1
pb.SC_RANK_GETMATCHRECORDDATA_MATCHSCORERANKRECORDPARAM2_FIELD.has_default_value = false
pb.SC_RANK_GETMATCHRECORDDATA_MATCHSCORERANKRECORDPARAM2_FIELD.default_value = 0
pb.SC_RANK_GETMATCHRECORDDATA_MATCHSCORERANKRECORDPARAM2_FIELD.type = 5
pb.SC_RANK_GETMATCHRECORDDATA_MATCHSCORERANKRECORDPARAM2_FIELD.cpp_type = 1

pb.SC_RANK_GETMATCHRECORDDATA.name = "SC_Rank_GetMatchRecordData"
pb.SC_RANK_GETMATCHRECORDDATA.full_name = ".SC_Rank_GetMatchRecordData"
pb.SC_RANK_GETMATCHRECORDDATA.nested_types = {}
pb.SC_RANK_GETMATCHRECORDDATA.enum_types = {}
pb.SC_RANK_GETMATCHRECORDDATA.fields = {pb.SC_RANK_GETMATCHRECORDDATA_STAGECHALLENGERECORDPARAM1_FIELD, pb.SC_RANK_GETMATCHRECORDDATA_STAGECHALLENGERECORDPARAM2_FIELD, pb.SC_RANK_GETMATCHRECORDDATA_MATCHRANKRECORDPARAM1_FIELD, pb.SC_RANK_GETMATCHRECORDDATA_MATCHRANKRECORDPARAM2_FIELD, pb.SC_RANK_GETMATCHRECORDDATA_MATCHSCORERANKRECORDPARAM1_FIELD, pb.SC_RANK_GETMATCHRECORDDATA_MATCHSCORERANKRECORDPARAM2_FIELD}
pb.SC_RANK_GETMATCHRECORDDATA.is_extendable = false
pb.SC_RANK_GETMATCHRECORDDATA.extensions = {}

CS_Rank_GetRankList = protobuf.Message(pb.CS_RANK_GETRANKLIST)
MSG_RANK_CHANGE = 2
MSG_RANK_GETLIST = 1
MSG_RANK_GETMATCHRECORDDATA = 3
MSG_RANK_NONE = 0
SC_Rank_Change = protobuf.Message(pb.SC_RANK_CHANGE)
SC_Rank_GetMatchRecordData = protobuf.Message(pb.SC_RANK_GETMATCHRECORDDATA)
SC_Rank_GetRankList = protobuf.Message(pb.SC_RANK_GETRANKLIST)
SC_Rank_GetRankList.RankData = protobuf.Message(pb.SC_RANK_GETRANKLIST_RANKDATA)

