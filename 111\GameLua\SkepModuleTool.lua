---
--- Generated by <PERSON><PERSON><PERSON>(https://github.com/EmmyLua)
--- Created by Administrator.
--- DateTime: 2023/8/4 3:17
--- 篮子相关的工具（提供接口，核心是SkepModule）
---

local EGenre = require("Enum_Genre")

---@class SkepModuleTool
local this = {}

--- 获取菌落装备篮子
function this.GetFateEquipmentSkep()
    local genre = EntityModule.hero:GetProperty(PLAYER_FIELD.PLAYER_FIELD_GENRE)
    ---@type SkepBase[]
    local skeps = SkepModule.skeps
    if genre == EGenre.GENRE_SWORD then
        return skeps[SKEPID.SKEPID_WINGEQUIP]
    elseif genre == EGenre.GENRE_PEN then
        return skeps[SKEPID.SKEPID_WINGEQUIP2]
    elseif genre == EGenre.GENRE_BOW then
        return skeps[SKEPID.SKEPID_WINGEQUIP3]
    elseif genre == EGenre.GENRE_FAN then
        return skeps[SKEPID.SKEPID_WINGEQUIP4]
    end
    return nil
end

return this