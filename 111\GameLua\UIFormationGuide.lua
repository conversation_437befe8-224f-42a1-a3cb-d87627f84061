-- 塔防阵型指引UI
local luaID = ('UIFormationGuide')

local UIFormationGuide = {}

-- 初始化
function UIFormationGuide:OnCreate()
	self.objList.Btn_Close.onClick:AddListenerEx(self.OnClickClose)
	self.objList.Txt_Close.text = GetGameText(luaID, 1)
	self.objList.Txt_EnemySide.text = GetGameText(luaID, 2)
	self.objList.Txt_SelfSide.text = GetGameText(luaID, 3)

	self:InitTowerItem()

	return true
end

-- 窗口是否可开启
function UIFormationGuide:CanOpen(itemID)
	local config = Schemes.TowerBattleFormationGuide:Get(itemID)
	if not config then
		return false
	end
	
	return true
end

-- 窗口开启
function UIFormationGuide:OnOpen(itemID)
	local config = Schemes.TowerBattleFormationGuide:Get(itemID)
	if not config then
		return
	end

	self.objList.Txt_Title.text = config.Title
	self.objList.Txt_Desc.text = string.gsub(config.InfoText,'\\n','\n')

	for i, v in ipairs(self.towerItemList) do
		v.TowerItemContent:SetActive(false)
	end

	for i, v in ipairs(config.GridIndex) do
		local item = self.towerItemList[v]
		if item then
			local bgSprite = config.TowerBG[i]
			local text = config.TowerText[i]
			if bgSprite and text then
				AtlasManager:AsyncGetSprite(bgSprite, item.Img_TowerBG)
				item.Txt_TowerDesc.text = text
				item.TowerItemContent:SetActive(true)
			end
		end
	end
end

-- 初始化塔物件
function UIFormationGuide:InitTowerItem()
	self.towerItemList = {}
	local prefab = self.objList.SingleTowerItem
	prefab:SetActive(true)
	for i = 1, 18 do
		local container = nil
		if i <= 9 then
			container = self.objList.TowerContainer1.transform
		else
			container = self.objList.TowerContainer2.transform
		end

		local newItem = self:CreateSubItem(container, prefab)
		newItem.index = i
		newItem.gameObject.name = 'Item'..i
		table.insert(self.towerItemList, newItem)
	end
	prefab:SetActive(false)
end

-- 点击关闭按钮
function UIFormationGuide.OnClickClose()
	local self = UIFormationGuide
	self:CloseSelf()
end

return UIFormationGuide