local luaID = ('UIADFree')
local m = {}

--------------------------------------------------------------------
-- 订阅事件列表
--------------------------------------------------------------------
function m:GetOpenEventList()
	return {
		[EventID.OnHeroPropChange] = m.UpdateView,
	}
end

--------------------------------------------------------------------
-- 初始化
--------------------------------------------------------------------
function m.OnCreate()
	m.config = Schemes.PrivilegeCard:Get(3)
	local rechargeConfig = Schemes.RechargeCard:Get(m.config.Renew)
	m.objList.Txt_Buy.text = '￥' .. rechargeConfig.FirstRMB / 100
	m.objList.Txt_BuyGrey.text = '￥' .. rechargeConfig.FirstRMB / 100
	local desList = HelperL.Split(m.config.Desc, ";")
	if rechargeConfig.Diamond > 0 then
		table.insert(desList, string.format(GetGameText(luaID, 1), rechargeConfig.Diamond))
	end

	if m.config.PerDayAward ~= 0 then
		local str = ''
		for i, v in ipairs(m.GetSprite2(m.config.PerDayAward)) do
			if v.num > 0 then
				str = str .. v.num .. v.name .. '  '
			end
		end
		table.insert(desList, str)
	end
	local num = m.objList.Obj_List.transform.childCount
	for i = 1, num do
		if desList[i] then
			m.objList["Txt_D" .. i].text = string.format('<color=#49536C>%s</color>', desList[i])
		else
			m.objList["Txt_D" .. i].text = ''
		end
	end

	m.RegisterClickEvent()
	return true
end

--------------------------------------------------------------------
-- 窗口开启
--------------------------------------------------------------------
function m.OnOpen()
	m.AddWndRedDots()
	m.UpdateView()
end

function m.UpdateView()
	-- 暂时屏蔽
	-- local hadBuy = HelperL.HadBoughtCardID(m.config.Renew)
	-- if SWITCH.RECHARGE then
	-- 	m.objList.Btn_Buy.gameObject:SetActive(not hadBuy)
	-- 	-- m.objList.Btn_BuyGrey.gameObject:SetActive(false)
	-- else
	-- 	m.objList.Btn_Buy.gameObject:SetActive(false)
	-- 	m.objList.Btn_BuyGrey.gameObject:SetActive(true)
	-- end
	m.objList.Btn_Buy2.gameObject:SetActive(UIManager:JudgeOpenLevel(WndID.Invitation, false))
	m.objList.Btn_Buy.gameObject:SetActive(false)
	m.objList.Btn_Get.gameObject:SetActive(false)
	m.objList.Btn_BuyGrey.gameObject:SetActive(false)

	--m.objList.Btn_Get.gameObject:SetActive(hadBuy)
	local hadGetReward = HeroDataManager:GetLogicBit(LOGIC_DATA.DATA_VIP_GET_PERDAY_GIFT, 19)
	m.objList.Txt_Get.text = hadGetReward == 1 and "已领取" or "领取"
	HelperL.SetImageGray(m.objList.Btn_Get:GetComponent('Image'), hadGetReward == 1)

	local commonText = Schemes.CommonText:Get(173)
	--判断今日次数
	local num = HeroDataManager:GetLogicByte(commonText.Param4, commonText.Param5)
	local color = UI_COLOR.Red
	if num >= commonText.DayTime then
		color = "#00FF00"
	end
	m.objList.Txt_AdTime.text = string.format(GetGameText(luaID, 3), color, num, commonText.DayTime)
end

--------------------------------------------------------------------
--注册点击事件
--------------------------------------------------------------------
function m.RegisterClickEvent()
	m.objList.Btn_mask.onClick:AddListenerEx(function()
		m:CloseSelf()
	end)
	m.objList.Btn_Buy.onClick:AddListenerEx(function()
		HelperL.RechargeAndJudge(m.config.Renew)
	end)
	m.objList.Btn_BuyGrey.onClick:AddListenerEx(function()
		HelperL.RechargeAndJudge(m.config.Renew)
	end)
	m.objList.Btn_Get.onClick:AddListenerEx(m.OnClickGet)
	m.objList.Btn_Buy2.onClick:AddListenerEx(function()
		UIManager:OpenWnd(WndID.Invitation)
	end)
end

--------------------------------------------------------------------
-- 添加界面红点
--------------------------------------------------------------------
function m.AddWndRedDots()
	local redDot = m:SetWndRedDot(m.objList.Btn_Get.transform)
	if redDot then
		redDot:AddCheckParam(RedDotCheckType.ADFree)
	end
end

--------------------------------------------------------------------
-- 点击领取奖励按钮
--------------------------------------------------------------------
function m.OnClickGet()
	local getDailyGift = HeroDataManager:GetLogicBit(LOGIC_DATA.DATA_VIP_GET_PERDAY_GIFT, 19)
	if getDailyGift == 1 then return end
	LuaModule.RunLuaRequest(
		string.format('LuaRequestGetPrivilegeCardPrize?cardID=%d', m.config.ID),
		m.OnClickBuyBack
	)
end

--------------------------------------------------------------------
-- 请求领取奖励返回
--------------------------------------------------------------------
function m.OnClickBuyBack(resultCode, content)
	if resultCode == RESULT_CODE.RESULT_COMMON_SUCCEED then
		m.UpdateView()
	else
		HelperL.ShowMessage(TipType.FlowText, ResultCode:GetResultCodeStr(resultCode))
	end
end

--------------------------------------------------------------------
-- 获取第一个奖励图片和数量
--------------------------------------------------------------------
function m.GetSprite2(ID)
	local GoodList = {}
	local prizeGoods = Schemes.PrizeTable:GetGoodInfos(ID)
	for i = 1, #prizeGoods do
		local goods = prizeGoods[i]
		local item
		if HelperL.IsEuipType(goods.ID) then
			item = Schemes.Equipment:Get(goods.ID)
		else
			item = Schemes.Medicament:Get(goods.ID)
		end
		local goodShuXing = {}
		if item then
			goodShuXing.name = item.GoodsName
			goodShuXing.num = goods.Num
			goodShuXing.sprite = item.IconID
			goodShuXing.id = goods.ID
			local quaLevel = item.QualityLevel or item.Quality
			local quaImg = HelperL.GetImageByQuality(quaLevel)
			goodShuXing.quaLevel = quaLevel
			goodShuXing.quaSprite = quaImg
		end
		table.insert(GoodList, goodShuXing)
	end
	return GoodList
end

return m
