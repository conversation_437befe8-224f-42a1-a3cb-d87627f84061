---@class ItemData 商品数据
---@field ItemID integer 商品ID
---@field GoodsID integer 物品ID
---@field GoodsNum integer
---@field CostType integer
---@field CostNum integer
---@field SoldOut boolean 售罄
---@field Discount integer 折扣
---@field BindFlag integer
---@field VIP integer
---@field PlayerLevel integer
---@field SocietyLevel integer
---@field NeedRecharge integer
---@field LimitActorDay integer
---@field NumsLimitType integer
---@field NumsLimitDay integer
---@field NumsIsBuy integer
---@field NumsGroupIsBuy integer

---商品列表
---@class SC_Store_GetItemList 
---@field StoreID integer 商店ID
---@field ItemList ItemData[] 物品数据
---@field RefreshID integer 刷新ID
---@field RefreshTime integer 自动刷新时间
---@field RefreshNum integer 刷新次数
---@field RefreshFreeCount integer 免费刷新次数

---@class SC_Store_BuyItem 商品购买回调
---@field Result integer
---@field StoreID integer 商店ID
---@field ItemID integer 商品ID
---@field SoldOut boolean 售罄
---@field Param integer
---@field NumsIsBuy integer
---@field NumsGroupIsBuy integer

-- 商店模块
require "StoreMessage_pb"

-- local luaID = ('StoreModule')

StoreModule = {}
---@type SC_Store_GetItemList[]
StoreModule.store = {}
StoreModule.storeRefreshList = {}
StoreModule.timeCounter = 0

function StoreModule.Handle(action, data)
    local m
    if action == StoreMessage_pb.MSG_STORE_GETLIST then --获取商品列表
        m = StoreMessage_pb.SC_Store_GetItemList()
        m:ParseFromString(data)
        StoreModule:SC_Store_GetItemList(m)
    elseif action == StoreMessage_pb.MSG_STORE_BUYITEM then --购买商品
        m = StoreMessage_pb.SC_Store_BuyItem()
        m:ParseFromString(data)
        StoreModule:SC_Store_BuyItem(m)
    elseif action == StoreMessage_pb.MSG_STORE_REFRESH then --刷新商店
        m = StoreMessage_pb.SC_Store_Refresh()
        m:ParseFromString(data)
        StoreModule:ShowResultTips(m.Result, '')
    end
end

function StoreModule:SC_Store_BuyItem(m)
    local store = self.store[m.StoreID]
    if store then
        for k, v in ipairs(store.ItemList) do
            if v.ItemID == m.ItemID then
                v.SoldOut = m.SoldOut
                v.NumsIsBuy = m.NumsIsBuy
                break
            end
        end
    end

    EventManager:Fire(EventID.StoreBuyItem, m)
end

function StoreModule:GetStoreItemList(storeID)
    if not storeID then
        return nil
    end
    return self.store[storeID]
end

function StoreModule:SC_Store_GetItemList(m)
    local serverTime = HelperL.GetServerTime()
    self.store[m.StoreID] = m
    if m.RefreshTime >= serverTime then
        self.storeRefreshList[m.StoreID] = m
    end
    EventManager:Fire(EventID.StoreList, m.StoreID)
end

function StoreModule:RequestGetStoreList(storeID)
    local m = StoreMessage_pb.CS_Store_GetItemList()
    m.StoreID = storeID
    Premier.Instance:GetNetwork():SendFromLua(
        ENDPOINT.ENDPOINT_GAMECLIENT,
        ENDPOINT.ENDPOINT_GAMESERVER,
        MSG_MODULEID.MSG_MODULEID_STORE,
        StoreMessage_pb.MSG_STORE_GETLIST,
        m:SerializeToString()
    )
end

function StoreModule:RequestRefreshStore(storeID, advertiseRefresh)
    local m = StoreMessage_pb.CS_Store_Refresh()
    m.StoreID = storeID
    m.AdvertiseRefresh = advertiseRefresh
    Premier.Instance:GetNetwork():SendFromLua(
        ENDPOINT.ENDPOINT_GAMECLIENT,
        ENDPOINT.ENDPOINT_GAMESERVER,
        MSG_MODULEID.MSG_MODULEID_STORE,
        StoreMessage_pb.MSG_STORE_REFRESH,
        m:SerializeToString()
    )
end

---购买商品
---@param storeID integer 商店ID
---@param itemID integer 商品ID
---@param itemNum integer 购买数量
function StoreModule:StoreBuyItem(storeID, itemID, itemNum)
    local m = StoreMessage_pb.CS_Store_BuyItem()
    m.StoreID = storeID
    m.ItemID = itemID
    m.ItemNum = itemNum or 1
    Premier.Instance:GetNetwork():SendFromLua(
        ENDPOINT.ENDPOINT_GAMECLIENT,
        ENDPOINT.ENDPOINT_GAMESERVER,
        MSG_MODULEID.MSG_MODULEID_STORE,
        StoreMessage_pb.MSG_STORE_BUYITEM,
        m:SerializeToString()
    )
end

-- 每帧调用
function StoreModule:OnUpdate()
    if self.timeCounter < 50 then
        self.timeCounter = self.timeCounter + 1
        return
    end
    self.timeCounter = 0
    local serverTime = HelperL.GetServerTime()
    for k, v in ipairs(self.storeRefreshList) do
        if v then
            local refreshTime = v.RefreshTime
            if serverTime >= refreshTime then
                self.storeRefreshList[k] = nil
                EventManager:Fire(EventID.StoreUpdateFree)
            end
        end
    end
end

function StoreModule:ShowResultTips(resultCode, content)
    ResultCode.ShowResultCodeCallback(resultCode, content)
    EventManager:Fire(EventID.StoreUpdateFree)
end

Premier.Instance:RegisterLuaModule(MSG_MODULEID.MSG_MODULEID_STORE, 'StoreModule.Handle')
