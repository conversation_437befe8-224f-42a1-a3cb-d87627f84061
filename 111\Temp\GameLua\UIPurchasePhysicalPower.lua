--[[
********************************************************************
    created:    2024/05/07
    author :    李锦剑
    purpose:    购买体力
*********************************************************************
--]]

local luaID = ('UIPurchasePhysicalPower')
local physicalPowerCfg = {
    { adID = 121, bg = 'tl_bjt_1', icon = 'tl_tb_1', name = "免费领取" },
    { adID = 118, bg = 'tl_bjt_2', icon = 'tl_tb_1', name = "购买"},
}

---@class UIPurchasePhysicalPower:UIWndBase
local m = {}

-- 添加冷却时间管理
local cooldownTimers = {}

--------------------------------------------------------------------
-- 订阅事件列表
--------------------------------------------------------------------
function m.GetOpenEventList()
    return {
        [EventID.OnHeroPropChange] = m.UpdateView,
        [EventID.LogicDataChange] = m.UpdateView,
    }
end

--------------------------------------------------------------------
-- 初始化
--------------------------------------------------------------------
function m.OnCreate()
    m.goodsItemList = {}
    m.RegisterClickEvent()
    return true
end

--------------------------------------------------------------------
-- 窗口开启
--------------------------------------------------------------------
function m.OnOpen()
    m.UpdateView()
    -- 启动倒计时更新
    m.StartCooldownUpdate()
end

--------------------------------------------------------------------
--注册点击事件
--------------------------------------------------------------------
function m.RegisterClickEvent()
    m.objList.Btn_Cance.onClick:AddListenerEx(m.OnClickClose)
    m.objList.Btn_Close.onClick:AddListenerEx(m.OnClickClose)
end

--------------------------------------------------------------------
-- 启动倒计时更新
--------------------------------------------------------------------
function m.StartCooldownUpdate()
    -- 清除之前的定时器
    if m.cooldownTimer then
        m.cooldownTimer:Stop()
    end
    
    -- 创建新的定时器，每秒更新一次
    m.cooldownTimer = Timer.New(function()
        m.UpdateAllCooldowns()
    end, 1, -1)
    m.cooldownTimer:Start()
end

--------------------------------------------------------------------
-- 更新所有冷却时间
--------------------------------------------------------------------
function m.UpdateAllCooldowns()
    for i = 1, #m.goodsItemList do
        local item = m.goodsItemList[i]
        if item and item.cfg then
            local commonText = Schemes.CommonText:Get(item.cfg.adID)
            if commonText then
                local time = EntityModule.hero.buffLC:GetBuffLeftTime(commonText.Param1)
                if time > 0 then
                    item.com.Txt_Time.text = string.format("冷却中 %s。", HelperL.GetTimeString(TimeStringType.FullAuto1, time))
                    item.com.Btn_Click.interactable = false
                else
                    item.com.Txt_Time.text = ""
                    item.com.Btn_Click.interactable = true
                end
            end
        end
    end
end

--------------------------------------------------------------------
--发放奖励
--------------------------------------------------------------------
function m.OnPurchasePhysicalPower(adID)
    local commonText = Schemes.CommonText:Get(adID)
    if not commonText then return end
    
    -- 检查冷却时间
    local time = EntityModule.hero.buffLC:GetBuffLeftTime(commonText.Param1)
    if time > 0 then
        HelperL.ShowMessage(TipType.FlowText, "冷却中，请稍后再购买。")
        return
    end
    
    local num = commonText.DayTime - HeroDataManager:GetLogicByte(commonText.Param4, commonText.Param5)
    if num == 0 then
        ResultCode.ShowResultCodeCallback(RESULT_CODE_BASE.RESULT_COMMON_LACK_DAYTIMES[1])
        return
    end

    if commonText.DeductGoods == "0" then
        AdvertisementManager.GetAdAward(adID, function(bool)
            if bool then
                if tonumber(adID) == 121 then
                    HelperL.RequestDirectGiveGoodsy(m.goodInfo, commonText.DeductGoods)
                else                    
                    HelperL.RequestDirectGiveGoodsy(commonText.AddGoods, commonText.DeductGoods)
                end
                -- 更新冷却时间显示
                m.UpdateAllCooldowns()
            end
        end, false)
    else
        local goodsList = m.GetGoodsList(commonText.DeductGoods)
        if HelperL.IsLackGoods(goodsList[1].id, goodsList[1].num, false) then
            return
        end
        
        local data = {
            type = NotarizeWindowsType.Windows1,
            titleContent = '购买提示',
            content = string.format(GetGameText('UIShopNew', 19), goodsList[1].num),
            okCallback = function()
                AdvertisementManager.GetAdAward(adID, function(bool)
                    if bool then
                        if tonumber(adID) == 121 then 
                            HelperL.RequestDirectGiveGoodsy(m.goodInfo, commonText.DeductGoods)
                        else               
                            HelperL.RequestDirectGiveGoodsy(commonText.AddGoods, commonText.DeductGoods)
                        end
                        -- 更新冷却时间显示
                        m.UpdateAllCooldowns()
                    end
                end)
            end,
        }
        HelperL.NotarizeUI(data)
    end
end

--------------------------------------------------------------------
-- 创建物品
--------------------------------------------------------------------
function m.CreateItem_Goods(parent, index)
    local item = {}
    item.index = index
    ---@type SlotItem[]
    item.goodsItemList = {}
    item.com = m:CreateSubItem(parent, m.objList.Item_Goods)
    item.com.Btn_Click.onClick:AddListenerEx(function()
        local commonText = Schemes.CommonText:Get(item.cfg.adID)
        local time = EntityModule.hero.buffLC:GetBuffLeftTime(commonText.Param1)
        if time > 0 then
            HelperL.ShowMessage(TipType.FlowText, "冷却中，请稍后再够买。")
            return
        end
        m.OnPurchasePhysicalPower(item.cfg.adID)
    end)
    item.com.Img_Icon.gameObject:SetActive(false)
    item.com.Img_Bg1.gameObject:SetActive(index == 1)
    item.com.Img_Bg2.gameObject:SetActive(index == 2)
    ---更新数据
    ---@param cfg {adID:integer, bg:string, icon:string}
    item.UpdateData = function(cfg)
        item.cfg = cfg
        if cfg then
            local commonText = Schemes.CommonText:Get(cfg.adID)
            
            --AtlasManager:AsyncGetSprite(cfg.bg, item.com.Img_Bg)
            
            local num = commonText.DayTime - HeroDataManager:GetLogicByte(commonText.Param4, commonText.Param5)
            local color = num > 0 and UI_COLOR.Red or UI_COLOR.Red
            item.com.Txt_Hint.text = string.format(GetGameText(luaID, 1), num, commonText.DayTime)
            item.com.Txt_Title1.text = cfg.name
            if commonText.DeductGoods ~= "0" then
                local goodsList2 = m.GetGoodsList(commonText.DeductGoods)
                local bool = HelperL.IsLackGoods(goodsList2[1].id, goodsList2[1].num, false, false)
                local color2 = not bool and UI_COLOR.Red or UI_COLOR.Red
                item.com.Txt_Expend.text = goodsList2[1].num
                AtlasManager:AsyncGetGoodsSprite(goodsList2[1].id, item.com.Img_Expend)
                item.com.Txt_Expend.gameObject:SetActive(true)
                item.com.Txt_AD.gameObject:SetActive(false)
                item.com.Txt_Title1.text = "购买"
            else
                item.com.Txt_Expend.gameObject:SetActive(false)
                item.com.Txt_AD.gameObject:SetActive(true)
                item.com.Txt_Title1.text = "免费领取"
            end
            
            if cfg.adID == 121 then
                local goodsList = Schemes.PrizeTable:GetGoodsList(commonText.AddGoods)
                m.goodInfo = ""
                if goodsList then
                    for i = 1, #goodsList, 1 do
                        if i == #goodsList then
                            m.goodInfo = m.goodInfo .. goodsList[i].id..";"..goodsList[i].num
                        else
                            m.goodInfo = m.goodInfo .. goodsList[i].id..";"..goodsList[i].num.."|"
                        end
                        if not item.goodsItemList[i] then
                            item.goodsItemList[i] = _GAddSlotItem(item.com.Content)
                        end
                        if goodsList[i] then
                            item.goodsItemList[i]:SetItemID(goodsList[i].id)
                            item.goodsItemList[i]:SetCount(goodsList[i].num)
                            item.goodsItemList[i]:SetSize(80, 80)
                            item.goodsItemList[i].gameObject:SetActive(true)
                        else
                            item.goodsItemList[i].gameObject:SetActive(false)
                        end
                    end
                end
            else
                local goodsList = m.GetGoodsList(commonText.AddGoods)
                AtlasManager:AsyncGetGoodsSprite(goodsList[1].id, item.com.Img_Icon, true)
                item.com.Txt_Num.text = "X" .. goodsList[1].num
                item.com.Img_Icon.gameObject:SetActive(true)
                item.com.Txt_Num.gameObject:SetActive(true)
            end
            

            --item.com.Img_Icon.gameObject:SetActive(true)
        else
            --item.com.Img_Icon.gameObject:SetActive(false)
        end
    end

    item.UpdateTimer = function()
        local commonText = Schemes.CommonText:Get(item.cfg.adID)
        local time = EntityModule.hero.buffLC:GetBuffLeftTime(commonText.Param1)
        if time <= 0 then
            item.com.Txt_Time.text = ""
            return
        end
        item.com.Txt_Time.text = "正在冷却中"..HelperL.GetTimeString(TimeStringType.FullAuto1, time).."。"
    end

    return item
end


--------------------------------------------------------------------
-- 每秒更新
--------------------------------------------------------------------
function m.OnSecondUpdate()
    -- local adID = 119
    -- local commonText = Schemes.CommonText:Get(adID)
	-- local time = EntityModule.hero.buffLC:GetBuffLeftTime(commonText.Param1)
    -- if time <= 0 then
    --     return
    -- end
    
    -- m.objList.Txt_Time.text = "正在冷却中"..HelperL.GetTimeString(TimeStringType.FullAuto1, time)
    -- for i = 1, 2, 1 do        
    --     m.goodsItemList[i].UpdateTimer()
    -- end
end


--------------------------------------------------------------------
-- 获取物品列表
--------------------------------------------------------------------
function m.GetGoodsList(content)
    ---@type {id:integer, num:integer}[]
    local goodsList = {}
    local strList = HelperL.Split(content, "|")
    local tempList
    for i, v in ipairs(strList) do
        tempList = HelperL.Split(v, ";")
        table.insert(goodsList, {
            id = tonumber(tempList[1]),
            num = tonumber(tempList[2]),
        })
    end
    return goodsList
end

--------------------------------------------------------------------
--更新界面
--------------------------------------------------------------------
function m.UpdateView()
    local num = math.max(#m.goodsItemList, #physicalPowerCfg)
    for i = 1, num, 1 do
        if not m.goodsItemList[i] then
            m.goodsItemList[i] = m.CreateItem_Goods(m.objList.Grid_Goods, i)
        end
        m.goodsItemList[i].UpdateData(physicalPowerCfg[i])
    end
    -- if #physicalPowerCfg > 0 then
    --     local cfg = physicalPowerCfg[1]
    --     if cfg then
    --         m.objList.Btn_Click.onClick:AddListenerEx(function()
    --             m.OnPurchasePhysicalPower(cfg.adID)
    --         end)
    --         local commonText = Schemes.CommonText:Get(cfg.adID)
    --         local goodsList = m.GetGoodsList(commonText.AddGoods)
    --         m.objList.Txt_Num.text = goodsList[1].num
    --         local num = HeroDataManager:GetLogicByte(commonText.Param4, commonText.Param5)
    --         local color = num > 0 and UI_COLOR.Red or UI_COLOR.Red
    --         m.objList.Txt_Hint.text = string.format(GetGameText(luaID, 1), num, commonText.DayTime)
    --         --GetGameText(luaID, 2)

    --         if commonText.DeductGoods ~= "0" then
    --             local goodsList2 = m.GetGoodsList(commonText.DeductGoods)
    --             local bool = HelperL.IsLackGoods(goodsList2[1].id, goodsList2[1].num, false, false)
    --             local color2 = not bool and UI_COLOR.Red or UI_COLOR.Red
    --             m.objList.Txt_Expend.text = goodsList2[1].num
    --             --AtlasManager:AsyncGetGoodsSprite(goodsList2[1].id, m.objList.Img_Expend)
    --             m.objList.Txt_des.text = string.format("花费%s钻石，购买%s体力，\n是否确定？",goodsList2[1].num,goodsList[1].num)
    --             m.objList.Txt_Expend.gameObject:SetActive(false)
    --             m.objList.Txt_AD.gameObject:SetActive(false)
    --         else
    --             m.objList.Txt_Expend.gameObject:SetActive(false)
    --             m.objList.Txt_AD.gameObject:SetActive(true)
    --         end
    --     end
    -- end
end

--------------------------------------------------------------------
--关闭界面
--------------------------------------------------------------------
function m.OnClickClose()
    -- 停止倒计时更新
    if m.cooldownTimer then
        m.cooldownTimer:Stop()
        m.cooldownTimer = nil
    end
    m:CloseSelf()
end

return m
