--[[
********************************************************************
created:    2016/11/5
author :    唐旋
purpose:    装备宝石数据
*********************************************************************
--]]


EquipGemPartDataLC = {}

function EquipGemPartDataLC.New()
	---@class EquipGemPartDataLC
	local o = { equipGemPartData = {}, gemData = 0 }
	-- 镶嵌宝石---
	function o:Set(k, v)
		self.isDirty = true
		o.equipGemPartData[k] = v
	end

	-- 镶嵌宝石---
	function o:Get(k)
		return o.equipGemPartData[k]
	end

	-- 猎魂---
	function o:SetFlag(v)
		o.gemData = v
		EventManager:Fire(EventID.GemPartChange)
	end

	-- 猎魂---
	function o:GetFlag()
		return o.gemData
	end

	-- 镶嵌宝物战力 ---
	function o:GetSelfPower()
		self.power = self.power or 0
		if not self.isDirty then
			return self.power
		end
		self.power = 0
		for k, v in pairs(self.equipGemPartData) do
			for i, v1 in ipairs(v.GemID) do
				if v1 ~= 0 then
					local gemScheme = Schemes.Medicament.Get(v1)
					if gemScheme then
						local smeltID = gemScheme.SmeltID
						if smeltID ~= 0 then
							local smeltScheme = Schemes.EquipSmelt.Get(smeltID, gemScheme.Quality, gemScheme.StarNum)
							if not smeltScheme then
								error(' 宝石找不到强化配置 ' ..
								smeltID .. ' ' .. gemScheme.Quality .. ' ' .. gemScheme.StarNum)
							else
								local pow = Schemes.EquipSmelt.GetSchemePower(smeltScheme, nil)
								self.power = self.power + pow
							end
						end
					else
						error(' 宝石找不到配置 ' .. v1)
					end
				end
			end
		end
		self.isDirty = false
		return self.power
	end

	return o
end
