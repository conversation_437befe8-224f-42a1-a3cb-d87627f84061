--[[
********************************************************************
    created:	2024/05/22
    author :	李锦剑
    purpose:    逻辑值枚举
*********************************************************************
--]]

--逻辑值常量(对应ActorDataCatalog.csv表)
ActorDataCatalog_Const = {
    -- 战斗进度-关卡、回合数、金币、供选经验
    BattleProgress = ActorDataCatalog.BattleProgress,
    -- 战斗进度-战场属性
    BattlePropIds = ActorDataCatalog.BattlePropIds,
    -- 战斗进度-Hp&MaxHp
    BattleHp = ActorDataCatalog.BattleHp,
    -- 背包状态-刷枪次数、钱袋是否已发放金币
    BattleBagStatus = ActorDataCatalog.BattleBagStatus,
    -- 背包状态-商店已刷出的枪
    BattleBagStatus_GunsInStore = ActorDataCatalog.BattleBagStatus_GunsInStore,
    -- 背包状态-已激活的格子、枪
    BattleBagGuns = ActorDataCatalog.BattleBagGuns,
    -- 体魄/火之装备出战
    WeaponsInStage = ActorDataCatalog.WeaponsInStage,
    -- 签到数据，1七日签到次数，2累计签到次数，3七日签到时间
    SignInData = 9102,
}



--#region 副本
--------------------------------------------------------------

--副本类型
---@enum EctypeType_LV
--副本类型
EctypeType_LV = {
    NONE = 0,            --无
    FRONT_TYPEH_1 = 1,   --副本类型1
    FRONT_TYPEH_2 = 2,   --副本类型2
    FRONT_TYPEH_3 = 3,   --副本类型3
    FRONT_TYPEH_4 = 4,   --副本类型4
    FRONT_TYPEH_5 = 5,   --副本类型5
    FRONT_TYPEH_6 = 6,   --副本类型6
    FRONT_TYPEH_7 = 7,   --副本类型7
    FRONT_TYPEH_8 = 8,   --副本类型8
    FRONT_TYPEH_9 = 9,   --副本类型9
    FRONT_TYPEH_10 = 10, --副本类型10
    FRONT_TYPEH_11 = 11, --副本类型11
    FRONT_TYPEH_12 = 12, --副本类型12
    FRONT_TYPEH_13 = 13, --副本类型13
    FRONT_TYPEH_14 = 14, --副本类型14
    MAX = 15,            --最大值
}

--副本关卡逻辑值--索引类型
---@enum EctypeStage_Index
--副本关卡逻辑值--索引
EctypeStage_Index = {
    NONE = 0,         --无
    MAX_STAGE_ID = 1, --已通关最大关卡ID
    MAX = 2,          --最大值
}

--副本宝箱逻辑值--索引类型
---@enum EctypeBox_Index
--副本宝箱逻辑值--索引
EctypeBox_Index = {
    NONE = 0,                      --无
    TREASURE_LOGICAL_VALUE = 1,    --关卡宝箱领取逻辑值
    MAX_RANK_NO = 2,               --最大波数
    EVERYDAY_ACCUMULATE_SCORE = 3, --每日累计积分
    MAX = 4,                       --最大值
}

--------------------------------------------------------------
--#endregion



--#region 装备
--------------------------------------------------------------

--装备逻辑值--索引类型
---@enum EquipData_Index
--装备逻辑值--索引
EquipData_Index = {
    NONE = 0,          --无
    UPGRADE_EXP = 1,   --升级经验(升星/培养)
    UPGRADE_LEVEL = 2, --升级经验2
    MAX = 3,           --最大值
}

--------------------------------------------------------------
--#endregion



--#region 战斗进度
--------------------------------------------------------------

--战斗进度逻辑值--索引类型
---@enum BattleProgress_Index
--战斗进度逻辑值--索引
BattleProgress_Index = {
    NONE = 0,      --无
    StageType = 1, --关卡类型
    StageLvl = 2,  --关卡等级
    RoundNo = 3,   --回合数
    Coins = 4,     --金币数
    OfferExp = 5,  --供选经验
    OfferLvl = 6,  --供选等级
    MAX = 7,       --最大值
}

--------------------------------------------------------------
--#endregion


--#region 活动
--------------------------------------------------------------

--签到数据--索引类型
---@enum SignInData_Index
--签到数据--索引
SignInData_Index = {
    NONE = 0,                    --无
    SignInNumber = 1,            --签到次数(7日)
    SignInCumulativeNumber = 2,  --累计次数(总数)
    SignInTime = 3,              --签到时间
    SignInCumulativeMaxSort = 4, --累计签到奖励，y已领取最大序号
    MAX = 5,                     --最大值
}

--任务数据--索引类型
---@enum TaskData_Index
--任务数据--索引
TaskData_Index = {
    NONE = 0,     --无
    FinishID = 1, --已完成任务ID
    State = 2,    --当前任务状态，0为未完成，1为已完成
    MAX = 3,      --最大值
}

--------------------------------------------------------------
--#endregion
