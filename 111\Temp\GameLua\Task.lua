local setmetatable = setmetatable




local function CommonCanAcceptTask(task, checkDistance)
	local self = task
	if self.status == TASK_STATUS.TASK_STATUS_COMPLETED then
		return false, RESULT_CODE.RESULT_TASK_COMPLETED
	end
	if self.status == TASK_STATUS.TASK_STATUS_ACCEPTED then
		return false, RESULT_CODE.RESULT_TASK_ACCEPTED
	end
	local heroLevel = EntityModule.hero.propertyLC:GetProperty(CREATURE_FIELD.CREATURE_FIELD_LEVEL)
	if heroLevel < self.taskScheme.MinLevel then
		return false, RESULT_CODE.RESULT_COMMON_LACK_LEVEL
	end
	if heroLevel > self.taskScheme.MaxLevel then
		return false, RESULT_CODE.RESULT_TASK_OVER_LEVEL
	end
	local preTaskID = self.taskScheme.PreTask
	if preTaskID ~= 0 then
		local preTaskLogicKey = HeroDataManager:GetLogicData(LOGIC_DATA.DATA_TRUNK_TASK_CUR_SERIAL)
		if preTaskID ~= preTaskLogicKey then
			return false, RESULT_CODE.RESULT_TASK_PRETASK
		end
	end
	local needCountry = self.taskScheme.NeedCountry
	local country     = EntityModule.hero.propertyLC:GetProperty(PLAYER_FIELD.PLAYER_FIELD_COUNTRY)
	if needCountry ~= 0 and country ~= needCountry then
		return false, RESULT_CODE.RESULT_TASK_NEED_COUNTRY
	end
	local needOpenDay = self.taskScheme.OpenDayLimit
	if needOpenDay > 0 then
		local serverOpenDays = HelperL.GetServerOpenDays()
		if serverOpenDays < needOpenDay then
			return false, RESULT_CODE.RESULT_TASK_OPENDAY_LIMIT, needOpenDay - serverOpenDays
		end
	end
	local tCount = self.taskScheme.Count
	if self.branchID ~= 0 and self.round >= tCount then
		return false, RESULT_CODE.RESULT_TASK_OVER_COUNT
	end
	local mapID = EntityModule.hero.heroTaskLC.GetMapID(self.taskScheme.AcceptMapID)
	local tagMapID = EntityModule.hero.heroTaskLC.CalcMapID(0, mapID)
	if checkDistance and tagMapID ~= 0 then
		local nowMapID = WorldModule.mapId
		local nowPoint = self.master.modelTrans.position
		local tagPoint = HelperL.PBString2Vector3(boxData.AcceptPoint)
		local diffVec3 = tagPoint - nowPoint
		diffVec3.y = 0
		if nowMapID ~= tagMapID or diffVec3:SqrMagnitude() >= 20 then
			return false,RESULT_CODE.RESULT_TASK_ACCEPT_NPC
		end
	end
	return true, RESULT_CODE.RESULT_COMMON_SUCCEED
end
local function CommonCanCompleteTask(task, checkDistance)
	local self = task
	if self.status ~= TASK_STATUS.TASK_STATUS_ACCEPTED then return false end
	if self:GetTaskGoal() <= self:GetTaskScore() then
		if not checkDistance then
			return true
		else
			local nowMapID = WorldModule.mapId
			local nowPoint = self.master.position
			local tagPoint = HelperL.PBString2Vector3(taskScheme.GivePoint)
			local diffVec3 = tagPoint - nowPoint
			if nowMapID ~= tagMapID or diffVec3:SqrMagnitude() >= 9 then
				return false
			end
		end
	end
	return false
end
local function CommonGetClientStatus( task, ignoreBranchType)
	local self = task
	if self.status == TASK_STATUS.TASK_STATUS_COMPLETED then 
		return ECTaskStatus.Completed
	end
	if self.branchID == 1 then
		local branchScheme = GetThisTaskBranchScheme(task)
		if not branchScheme or self.round == branchScheme.GroupCount then
			return ECTaskStatus.Completed 
		end
	end
	if self.status == TASK_STATUS.TASK_STATUS_ACCEPTED and self:CanComplete() then
		return ECTaskStatus.CanComplete 
	end
	if self.status == TASK_STATUS.TASK_STATUS_ACCEPTED then
		return ECTaskStatus.Doing 
	end
	if self.status == TASK_STATUS.TASK_STATUS_NULL or
		self.status == TASK_STATUS.TASK_STATUS_BRANCH or
		self.status == TASK_STATUS.TASK_STATUS_ROUND then
		local canAccept, resultCode = self:CanAccept()
		if canAccept then
			return ECTaskStatus.CanAccept
		end
	end
	return ECTaskStatus.Null
end
function IsTaskNavigationNeedAutoFight(taskScheme)
	if taskScheme.Type == TaskType.TaskType_KillTargetMonster then
		return true
	end
	if taskScheme.Type == TaskType.TaskType_KillLevelMonster then
		return true
	end
	if taskScheme.Type == TaskType.TaskType_CollectGood and taskScheme.Parameter3 > 0 then
		return true
	end
	
	return false
end
--- 接受任务后自动跑去做任务的地点
local INVALID_POSITION = '0;0;0'
local function CommonAfterAccept( task )
	--MiscDataCenter.StopRunToFollowNpc()
	EntityModule.hero.heroTaskLC.SetFocusTask(task)
	local wayNPC = EntityModule.hero.heroTaskLC.GetDoNPC(task.taskScheme)
	if wayNPC and wayNPC ~= 0 then
		EntityModule.hero.navigationLG:NavigateToNPC(wayNPC, nil, nil, nil, task.taskID * 256 + task.branchID)
	elseif task.taskScheme.WayCoordinate ~= INVALID_POSITION then
		local autoFight = IsTaskNavigationNeedAutoFight(task.taskScheme)
		local mapID = EntityModule.hero.heroTaskLC.GetMapID(task.taskScheme.WayMapID)
		local wayMap = EntityModule.hero.heroTaskLC.CalcMapID(0, mapID)
		EntityModule.hero.navigationLG:SetWorldDestination(wayMap, HelperL.PBString2Vector3(task.taskScheme.WayCoordinate), autoFight,_,_,task.taskID * 256 + task.branchID )
	end
	if task.taskScheme.GroupID == 16 or task.taskScheme.GroupID == 18 then	-- 帮会跑环，建设
		-- if UIManager.curWindowType and UIManager.curWindowType ~= 'MainLandUI' then 
		-- 	EntityModule.hero.navigationLG.Pause()
		-- end
	end
	if task.taskScheme.Style == 1 then		
		--HelperL.SetNavigatePauseFlag(true)
	end
end
local function CommonAfterFinishing(task )
	if  EntityModule.hero.heroTaskLC.autoDoLoopTask
	and (EntityModule.hero.heroTaskLC.autoDoDailyTask ~= 0 and task.taskScheme.GroupID == EntityModule.hero.heroTaskLC.autoDoDailyTask) then

		EntityModule.hero.heroTaskLC.SendLuaTurnInTask(task.branchID,
											task.taskID,
											task.step)
		return
	end
	EntityModule.hero.heroTaskLC.SetFocusTask(task)
	if task.taskScheme.Pathfinding == 1 then return end
	local turnInNPC = EntityModule.hero.heroTaskLC.GetTurnInNPC(task.taskScheme)
	if turnInNPC and turnInNPC ~= 0 then
		if WorldModule.map.MapType == 0 then
			HelperL.SetAutoFight(false)
		end
		if task.taskID == 76 then	--跳舞福利特殊处理
			if EntityModule.hero then
				if EntityModule.hero.buffLC:HasBuff(1101) or EntityModule.hero.buffLC:HasBuff(1102) then
					return
				end	
			end
		end
		EntityModule.hero.navigationLG:NavigateToNPC(turnInNPC, nil, nil, nil, task.taskID * 256 + task.branchID)
		-- if task.taskScheme.Type == TaskType.TaskType_UpGrade and (UIManager.curWindowType and UIManager.curWindowType ~= 'MainLandUI') then
		-- 	EntityModule.hero.navigationLG.Pause()
		-- end
	elseif task.taskScheme.GivePoint ~= INVALID_POSITION then
		local mapID = EntityModule.hero.heroTaskLC.GetMapID(task.taskScheme.GiveMapID)
		local turnInMap = EntityModule.hero.heroTaskLC.CalcMapID(0, mapID)
		EntityModule.hero.navigationLG:SetWorldDestination(turnInMap, HelperL.PBString2Vector3(task.taskScheme.GivePoint))
	end
end
function CommonGetTaskNPC (task)
	local turnInNPC = EntityModule.hero.heroTaskLC.GetTurnInNPC(task.taskScheme)
	local doNPC     = EntityModule.hero.heroTaskLC.GetDoNPC(task.taskScheme)
	local acceptNPC = EntityModule.hero.heroTaskLC.GetAcceptNPC(task.taskScheme)
	return acceptNPC, doNPC, turnInNPC
end
local function CommonWantToAccept( task )
	EntityModule.hero.heroTaskLC.SetFocusTask(task)
	local acceptNPC,_,_ = CommonGetTaskNPC(task)
	if acceptNPC ~= 0 then
		EntityModule.hero.navigationLG:NavigateToNPC(acceptNPC, nil, nil, nil, task.taskID * 256 + task.branchID)
		if task.taskScheme.Style == 1 then
			--HelperL.SetNavigatePauseFlag(true)
		end
	else
		EntityModule.hero.heroTaskLC.SendLuaAcceptTask(task.branchID,task.taskID, task.step)
	end
	if task.taskScheme.Type == 9 and task.taskScheme.ClickPointTo > 0 then
		EventManager:Fire(EventID.AutoSeekMainTask,2)
	end
end
local function CommonWantToTurnIn( task )
	EntityModule.hero.heroTaskLC.SetFocusTask(task)
	local mapID = EntityModule.hero.heroTaskLC.GetMapID(task.taskScheme.GiveMapID)
	local tagMapID = EntityModule.hero.heroTaskLC.CalcMapID(0, mapID)
	if tagMapID ~= 0 then
		local _,_,giveNPC  = CommonGetTaskNPC(task)
		if giveNPC ~= 0 then
			EntityModule.hero.navigationLG:NavigateToNPC(giveNPC, nil, nil, nil, task.taskID * 256 + task.branchID)
		else
			EntityModule.hero:SetWorldDestination(
					tagMapID,
					HelperL.PBString2Vector3(task.GivePoint),
					false)
		end
		if task.taskScheme.Style == 1 then
			--HelperL.SetNavigatePauseFlag(true)
		end
	else
		if task.taskScheme.Style == ECTaskStyle.Society then
			EntityModule.hero.heroTaskLC.SendLuaTurnInTask(task.branchID,task.taskID,task.step)
		else
			local index = 1
			--local winName = 'UITask'
			local style = task.taskScheme.Style
			local groupID = task.taskScheme.GroupID
			if style == ECTaskStyle.Branch then 
				
			elseif style == ECTaskStyle.Daily then
				if PlayerPrefs.GetInt('isCompleteFormClick', 0) == groupID then
					groupID = groupID + 100000
				end
				PlayerPrefs.SetInt('isCompleteFormClick', 0)
			elseif style == ECTaskStyle.Loop then
				UIManager.LoadUI('UILoopTask')
				return
			elseif style == ECTaskStyle.Trunk then
				--UIManager:OpenWnd(WndID.MainTask)
				return
			end
			if task.taskScheme.GroupID ~= 100 then				--潜入敌营不弹每日必做
				UIManager.LoadUI('UIDailyPlay')
				UIManager.SendWndMsg('UIDailyPlay', UIWndMsg.UIDailyPlay.jumpAppointTaskComplete, groupID)
			end
		end
	end
end
local TalkTask = {
	GetClientStatus = function (self, ignoreBranchType)
		return CommonGetClientStatus(self, ignoreBranchType)
	end,
	CanComplete = function (self, checkDistance)
		return CommonCanCompleteTask(self, checkDistance)
	end,
	CanAccept = function (self, checkDistance)
		return CommonCanAcceptTask(self, checkDistance)
	end,
	WantToAccept = function ( self )
		CommonWantToAccept(self)
	end,
	GetTaskGoal = function(self)
		return 2
	end,
	GetTaskScore = function(self)
		if self.taskScheme.WayNPC == '0' then
			self.score = self:GetTaskGoal()
		end
		return self.score
	end,
	UpdateTaskScore = function(self, newScore)
		self.score = newScore
		if EntityModule.hero and EntityModule.hero.heroTaskLC then
			EntityModule.hero.heroTaskLC.UpdateTask(self)
		end
	end,
	AfterAccept = function(self)
		CommonAfterAccept(self)
		if self.taskScheme.WayNPC == '0' then
			self.score = self:GetTaskGoal()
			EntityModule.hero.heroTaskLC.UpdateTask(self)
		end
	end,
	WantToTurnInTask = function ( self )
		CommonWantToTurnIn(self)
	end,
	AfterFinishTask = function(self)

		CommonAfterFinishing(self)

	end,
	GetNPCStatus = function (self, npcID)
		local clientStatus = self:GetClientStatus()
		local acceptNPC, doNPC, turnInNPC = CommonGetTaskNPC(self)
		if turnInNPC == npcID and clientStatus == ECTaskStatus.CanComplete then
			return NPC_STATUS.CAN_COMPLETE
		elseif doNPC == npcID and clientStatus >= ECTaskStatus.Accepted then
			return NPC_STATUS.DOING
		elseif acceptNPC == npcID and clientStatus == ECTaskStatus.CanAccept then
			return NPC_STATUS.CAN_ACCEPT
		end
		return NPC_STATUS.DEFAULT
	end
}
local talkTaskMT =  { __index = TalkTask}
function CreateTalkTask (
		branchID,
		taskID,
		step,
		acceptTime,
		status,
		round,
		score,
		taskScheme)
	return setmetatable({
		branchID   = branchID,
		taskID     = taskID,
		step       = step,
		acceptTime = acceptTime,
		status     = status,
		round      = round,
		score      = score,
		taskScheme = taskScheme,
		haveUpdatedCanCompleteStatus = false}, talkTaskMT)
end

local TimePassTask =
{
	GetClientStatus = function(self, ignoreBranchType)
		local clientStatus = CommonGetClientStatus(self, ignoreBranchType)
		return clientStatus
	end,
	CanComplete = function(self, checkDistance)
		return CommonCanCompleteTask(self, checkDistance)
	end,
	CanAccept = function(self, checkDistance)
		return CommonCanAcceptTask(self, checkDistance)
	end,
	WantToAccept = function ( self )
		CommonWantToAccept(self)
	end,
	GetTaskGoal = function(self)
		return self.taskScheme.Parameter1
	end,
	GetTaskScore = function(self)
		if self.acceptTime ~= 0 then
			return HelperL.GetServerTime() - self.acceptTime
		else
			return 0
		end
	end,
	AfterAccept = function(self)
		if not self.timer then
			self.timer = Timer.New(HelperL.bind(self, self.CountDown), 1, -1 )
			self.timer:Start()
		else
			if not self.timer.running then
				self.timer:Start()
			end
		end
	end,
	WantToTurnInTask = function ( self )
		CommonWantToTurnIn(self)
	end,
	AfterFinishTask = function(self)
		CommonAfterFinishing(self)
	end,
	GetNPCStatus = function(self, npcID)
		local acceptNPC, doNPC, turnInNPC = CommonGetTaskNPC(self)
		local clientStatus = self:GetClientStatus()

		if turnInNPC == npcID and clientStatus == ECTaskStatus.CanComplete then
			return NPC_STATUS.CAN_COMPLETE
		end
		if acceptNPC == npcID and clientStatus == ECTaskStatus.CanAccept then
			return NPC_STATUS.CAN_ACCEPT
		end
		return NPC_STATUS.DEFAULT
	end,
	CountDown = function ( self )
		local curScore = self:GetTaskScore()
		local diff = self:GetTaskGoal() - curScore
		if diff <= 0 then
			self.timer:Stop()
			EntityModule.hero.heroTaskLC.UpdateTask(self)
		end
	end
}
local timePassTaskMT = { __index = TimePassTask}
function CreateTimePassTask (
		branchID,
		taskID,
		step,
		acceptTime,
		status,
		round,
		score,
		taskScheme)
	local o = setmetatable({
		branchID   = branchID,
		taskID     = taskID,
		step       = step,
		acceptTime = acceptTime,
		status     = status,
		round      = round,
		score      = score,
		taskScheme = taskScheme,
		haveUpdatedCanCompleteStatus = false	}, timePassTaskMT)

	if o.status == TASK_STATUS.TASK_STATUS_ACCEPTED and o:GetClientStatus() ~= ECTaskStatus.CanComplete then
		o:AfterAccept()
	end
	return o
end
local TimeTalkTask =  --- 对话并读条
{
	GetClientStatus = function(self, ignoreBranchType)
		local clientStatus = CommonGetClientStatus(self, ignoreBranchType)
		return clientStatus
	end,
	CanComplete = function(self, checkDistance)
		return CommonCanCompleteTask(self, checkDistance)
	end,
	CanAccept  = function(...)
		return CommonCanAcceptTask(...)
	end,
	WantToAccept = function ( self )
		CommonWantToAccept(self)
	end,
	GetTaskGoal = function(self)
		return 1
	end,
	GetTaskScore = function(self)
		return self.score
	end,
	AfterAccept = function ( self )
		print('时间任务接受后')
		CommonAfterAccept(self)
	end,
	AfterFinishTask = function(self)
		print('时间任务完成', self.taskScheme.Pathfinding)
		CommonAfterFinishing(self)
	end,
	WantToTurnInTask = function ( self )
		CommonWantToTurnIn(self)
	end,
	GetNPCStatus = function(self, npcID)
		local acceptNPC, doNPC, turnInNPC = CommonGetTaskNPC(self)
		local clientStatus = self:GetClientStatus()
		if npcID == turnInNPC and clientStatus == ECTaskStatus.CanComplete then
			return NPC_STATUS.CAN_COMPLETE
		end
		if npcID == doNPC and clientStatus == ECTaskStatus.CanComplete then
			return NPC_STATUS.CAN_REFRESH
		end
		if npcID == doNPC and clientStatus == ECTaskStatus.Doing then
			return NPC_STATUS.DOING
		end
		if npcID == acceptNPC and clientStatus < ECTaskStatus.Accepted then
			return NPC_STATUS.CAN_ACCEPT
		end
	end
}
local timeTalkTaskMT = { __index = TimeTalkTask}
function CreateTimeTalkTask (
		branchID,
		taskID,
		step,
		acceptTime,
		status,
		round,
		score,
		taskScheme)
	return setmetatable({
		branchID   = branchID,
		taskID     = taskID,
		step       = step,
		acceptTime = acceptTime,
		status     = status,
		round      = round,
		score      = score,
		taskScheme = taskScheme,
		haveUpdatedCanCompleteStatus = false	}, timeTalkTaskMT)
end
local KillTargetMonsterTask = --- 杀指定怪任务
{
	GetClientStatus = function(self, ignoreBranchType)
		local clientStatus = CommonGetClientStatus(self, ignoreBranchType)
		return clientStatus
	end,
	CanComplete = function(self, checkDistance)
		return CommonCanCompleteTask(self, checkDistance)
	end,
	CanAccept = function(...)
		return CommonCanAcceptTask(...)
	end,
	WantToAccept = function ( self )
		CommonWantToAccept(self)
	end,
	GetTaskGoal = function(self)
		return self.taskScheme.Parameter2
	end,
	GetTaskScore = function(self)
		return self.score
	end,
	AfterAccept = function ( self )
		CommonAfterAccept(self)
	end,
	AfterFinishTask = function(self)
		CommonAfterFinishing(self)
	end,
	WantToTurnInTask = function ( self )
		CommonWantToTurnIn(self)
	end,
	GetNPCStatus = function(self, npcID)
		local acceptNPC, doNPC, turnInNPC = CommonGetTaskNPC(self)
		local clientStatus = self:GetClientStatus()

		if npcID == turnInNPC and clientStatus == ECTaskStatus.CanComplete then
			return NPC_STATUS.CAN_COMPLETE
		end
		if npcID == acceptNPC and clientStatus < ECTaskStatus.Accepted then
			return NPC_STATUS.CAN_ACCEPT
		end
	end
}
local killTargetMonsterTaskMT = { __index = KillTargetMonsterTask}
function CreateKillTargetMonsterTask (
		branchID,
		taskID,
		step,
		acceptTime,
		status,
		round,
		score,
		taskScheme)
	return setmetatable({
		branchID   = branchID,
		taskID     = taskID,
		step       = step,
		acceptTime = acceptTime,
		status     = status,
		round      = round,
		score      = score,
		taskScheme = taskScheme,
		haveUpdatedCanCompleteStatus = false}, killTargetMonsterTaskMT)


end
local DramaEctypeTask =
{
	GetClientStatus = function(...)
		local clientStatus = CommonGetClientStatus(...)
		return clientStatus
	end,
	CanComplete = function(...)
		return CommonCanCompleteTask(...)
	end,
	CanAccept = function(...)
		return CommonCanAcceptTask(...)
	end,
	WantToAccept = function ( self )
		CommonWantToAccept(self)
	end,
	GetTaskGoal = function(self)
		return 1
	end,
	GetTaskScore = function(self)
		return self.score
	end,
	AfterAccept = function ( self )
		CommonAfterAccept(self)
	end,
	WantToTurnInTask = function ( self )
		CommonWantToTurnIn(self)
	end,
	AfterFinishTask = function(self)
		CommonAfterFinishing(self)
	end,
	GetNPCStatus = function(self, npcID)
		local acceptNPC, doNPC, turnInNPC = CommonGetTaskNPC(self)
		local clientStatus = self:GetClientStatus()
		if turnInNPC == npcID and clientStatus == ECTaskStatus.CanComplete then
			return NPC_STATUS.CAN_COMPLETE
		end
		if doNPC == npcID and clientStatus == ECTaskStatus.Doing then
			return NPC_STATUS.DOING
		end
		if acceptNPC == npcID and clientStatus == ECTaskStatus.CanAccept then
			return NPC_STATUS.CAN_ACCEPT
		end
	end
}
local dramaEctypeTaskMT = { __index = DramaEctypeTask}
function CreateDramaEctypeTask (
		branchID,
		taskID,
		step,
		acceptTime,
		status,
		round,
		score,
		taskScheme)
	return setmetatable({
		branchID   = branchID,
		taskID     = taskID,
		step       = step,
		acceptTime = acceptTime,
		status     = status,
		round      = round,
		score      = score,
		taskScheme = taskScheme,
	haveUpdatedCanCompleteStatus = false}, dramaEctypeTaskMT)
end
local DailyEctypeTask =
{
	GetClientStatus = function(...)
		local clientStatus = CommonGetClientStatus(...)
		return clientStatus
	end,
	CanComplete = function(...)
		return CommonCanCompleteTask(...)
	end,
	CanAccept = function(...)
		return CommonCanAcceptTask(...)
	end,
	WantToAccept = function ( self )
		CommonWantToAccept(self)
	end,
	GetTaskGoal = function(self)
		return 1
	end,
	GetTaskScore = function(self)
		return self.score
	end,
	AfterAccept = function ( self )
		CommonAfterAccept(self)
	end,
	WantToTurnInTask = function ( self )
		CommonWantToTurnIn(self)
	end,
	AfterFinishTask = function(self)
		CommonAfterFinishing(self)
	end,
	GetNPCStatus = function(self, npcID)
		local acceptNPC, doNPC, turnInNPC = CommonGetTaskNPC(self)
		local clientStatus = self:GetClientStatus()
		if turnInNPC == npcID and clientStatus == ECTaskStatus.CanComplete then
			return NPC_STATUS.CAN_COMPLETE
		end
		if doNPC == npcID and clientStatus == ECTaskStatus.Doing then
			return NPC_STATUS.DOING
		end
		if acceptNPC == npcID and clientStatus == ECTaskStatus.CanAccept then
			return NPC_STATUS.CAN_ACCEPT
		end
	end
}
local dailyEctypeTaskMT = { __index = DailyEctypeTask}
function CreateDailyEctypeTask(
		branchID,
		taskID,
		step,
		acceptTime,
		status,
		round,
		score,
		taskScheme)
	return setmetatable({
		branchID   = branchID,
		taskID     = taskID,
		step       = step,
		acceptTime = acceptTime,
		status     = status,
		round      = round,
		score      = score,
		taskScheme = taskScheme,
	haveUpdatedCanCompleteStatus = false}, dailyEctypeTaskMT)
end
local JoinActivityTask =
{
	GetClientStatus = function(...)
		local clientStatus = CommonGetClientStatus(...)
		return clientStatus
	end,
	CanComplete = function(...)
		return CommonCanCompleteTask(...)
	end,
	CanAccept = function(...)
		return CommonCanAcceptTask(...)
	end,
	WantToAccept = function ( self )
		CommonWantToAccept(self)
	end,
	GetTaskGoal = function(self)
		return self.taskScheme.Parameter2
	end,
	GetTaskScore = function(self)
		return self.score
	end,
	AfterAccept = function ( self )
		if self.taskScheme.Parameter1 == 1 then
			-- 跳舞挂机
			EntityModule.hero.heroTaskLC.SetFocusTask(self)
			--MiscDataCenter.BeginRunToFollowNpc()
		else
			CommonAfterAccept(self)
		end
	end,
	AfterFinishTask = function(self)
		CommonAfterFinishing(self)
	end,
	WantToTurnInTask = function ( self )
		CommonWantToTurnIn(self)
	end,
	GetNPCStatus = function(self, npcID)
		local acceptNPC, doNPC, turnInNPC = CommonGetTaskNPC(self)
		local clientStatus = self:GetClientStatus()
		if turnInNPC == npcID and clientStatus == ECTaskStatus.CanComplete then
			return NPC_STATUS.CAN_COMPLETE
		end
		if doNPC == npcID and clientStatus == ECTaskStatus.Doing then
			return NPC_STATUS.DOING
		end
		if acceptNPC == npcID and clientStatus == ECTaskStatus.CanAccept then
			return NPC_STATUS.CAN_ACCEPT
		end
	end
}
local joinActivityTaskMT  = { __index = JoinActivityTask}
function CreateJoinActivityTask(
		branchID,
		taskID,
		step,
		acceptTime,
		status,
		round,
		score,
		taskScheme)
	return setmetatable({
		branchID   = branchID,
		taskID     = taskID,
		step       = step,
		acceptTime = acceptTime,
		status     = status,
		round      = round,
		score      = score,
		taskScheme = taskScheme,
	haveUpdatedCanCompleteStatus = false}, joinActivityTaskMT)
end
local KillLevelMonsterTask =
{
	GetClientStatus = function(...)
		local clientStatus = CommonGetClientStatus(...)
		return clientStatus
	end,
	CanComplete = function(...)
		return CommonCanCompleteTask(...)
	end,
	CanAccept = function(...)
		return CommonCanAcceptTask(...)
	end,
	WantToAccept = function ( self )
		CommonWantToAccept(self)
	end,
	GetTaskGoal = function(self)
		return self.taskScheme.Parameter2
	end,
	GetTaskScore = function(self)
		return self.score
	end,
	AfterAccept = function ( self )
		CommonAfterAccept(self)
	end,
	AfterFinishTask = function(self)
		CommonAfterFinishing(self)
	end,
	WantToTurnInTask = function ( self )
		CommonWantToTurnIn(self)
	end,
	GetNPCStatus = function(self, npcID)
		local acceptNPC, doNPC, turnInNPC = CommonGetTaskNPC(self)
		local clientStatus = self:GetClientStatus()
		if npcID == turnInNPC and clientStatus == ECTaskStatus.CanComplete then
			return NPC_STATUS.CAN_COMPLETE
		end
		if npcID == acceptNPC and clientStatus == ECTaskStatus.CanAccept then
			return NPC_STATUS.CAN_ACCEPT
		end
	end
}
local killLevelMonsterMT = { __index = KillLevelMonsterTask}
function CreateKillLevelMonsterTask(
		branchID,
		taskID,
		step,
		acceptTime,
		status,
		round,
		score,
		taskScheme)
	return setmetatable({
		branchID   = branchID,
		taskID     = taskID,
		step       = step,
		acceptTime = acceptTime,
		status     = status,
		round      = round,
		score      = score,
		taskScheme = taskScheme,
	haveUpdatedCanCompleteStatus = false}, killLevelMonsterMT)
end
local ActorLevelTask =
{
	GetClientStatus = function(...)
		local clientStatus = CommonGetClientStatus(...)
		return clientStatus
	end,
	CanComplete = function(...)
		return CommonCanCompleteTask(...)
	end,
	CanAccept = function(...)
		return CommonCanAcceptTask(...)
	end,
	WantToAccept = function ( self )
		CommonWantToAccept(self)
	end,
	GetTaskGoal = function(self)
		return self.taskScheme.Parameter1
	end,
	GetTaskScore = function(self)
		if self.score < self.taskScheme.Parameter1 then
			local dayPass = false
			if self.taskScheme.Parameter2 > 0 then
				local openDay = HelperL.GetServerOpenDays()
				if openDay >= self.taskScheme.Parameter2 then
					dayPass = true
					if openDay == self.taskScheme.Parameter2 and self.taskScheme.Parameter3 > 0 then
						local curDate = os.date('*t', HelperL.GetServerTime())
						local curHour = curDate.hour
						if curHour < self.taskScheme.Parameter3 then
							dayPass = false
						end
					end
				end
			end
			if dayPass then
				self.score = self.taskScheme.Parameter1
			end
		end
		return self.score
	end,
	AfterAccept = function ( self )
		CommonAfterAccept(self)
	end,
	AfterFinishTask = function(self)
		CommonAfterFinishing(self)
	end,
	WantToTurnInTask = function ( self )
		CommonWantToTurnIn(self)
	end,
	GetNPCStatus = function(self, npcID)
		local acceptNPC, doNPC, turnInNPC  = CommonGetTaskNPC(self)
		local clientStatus = self:GetClientStatus()
		if npcID == turnInNPC and clientStatus == ECTaskStatus.CanComplete then
			return NPC_STATUS.CAN_COMPLETE
		end
		if npcID == acceptNPC and clientStatus == ECTaskStatus.CanAccept then
			return NPC_STATUS.CAN_ACCEPT
		end
	end
}
local actorLevelTaskMT = { __index = ActorLevelTask}
function CreateActorLevelTask(
		branchID,
		taskID,
		step,
		acceptTime,
		status,
		round,
		score,
		taskScheme)
	return setmetatable({
		branchID   = branchID,
		taskID     = taskID,
		step       = step,
		acceptTime = acceptTime,
		status     = status,
		round      = round,
		score      = score,
		taskScheme = taskScheme,
	haveUpdatedCanCompleteStatus = false}, actorLevelTaskMT)
end
local FriendShipTask =
{
	GetClientStatus = function(...)
		local clientStatus = CommonGetClientStatus(...)
		return clientStatus
	end,
	CanComplete = function(...)
		return CommonCanCompleteTask(...)
	end,
	CanAccept = function(...)
		return CommonCanAcceptTask(...)
	end,
	WantToAccept = function ( self )
		CommonWantToAccept(self)
	end,
	GetTaskGoal = function(self)
		return self.taskScheme.Parameter1
	end,
	GetTaskScore = function(self)
		return self.score
	end,
	AfterAccept = function ( self )
		CommonAfterAccept(self)
	end,
	AfterFinishTask = function(self)
		CommonAfterFinishing(self)
	end,
	WantToTurnInTask = function ( self )
		CommonWantToTurnIn(self)
	end,
	GetNPCStatus = function(self, npcID)
		local acceptNPC, doNPC, turnInNPC = CommonGetTaskNPC(self)
		local clientStatus = self:GetClientStatus()
		if turnInNPC == npcID and clientStatus == ECTaskStatus.CanComplete then
			return NPC_STATUS.CAN_COMPLETE
		end
		if doNPC == npcID and clientStatus == ECTaskStatus.Doing then
			return NPC_STATUS.DOING
		end
		if acceptNPC == npcID and clientStatus == ECTaskStatus.CanAccept then
			return NPC_STATUS.CAN_ACCEPT
		end
	end


}
local friendShipTaskMT  = { __index = FriendShipTask}
function CreateFriendShipTask(
		branchID,
		taskID,
		step,
		acceptTime,
		status,
		round,
		score,
		taskScheme)
	return setmetatable({
		branchID   = branchID,
		taskID     = taskID,
		step       = step,
		acceptTime = acceptTime,
		status     = status,
		round      = round,
		score      = score,
		taskScheme = taskScheme,
	haveUpdatedCanCompleteStatus = false}, friendShipTaskMT)
end
local KillPlayerTask =
{
	GetClientStatus = function(...)
		local clientStatus = CommonGetClientStatus(...)
		return clientStatus
	end,
	CanComplete = function(...)
		return CommonCanCompleteTask(...)
	end,
	CanAccept = function(...)
		return CommonCanAcceptTask(...)
	end,
	WantToAccept = function ( self )
		CommonWantToAccept(self)
	end,
	GetTaskGoal = function(self)
		return self.taskScheme.Parameter1
	end,
	GetTaskScore = function(self)
		return self.score
	end,
	AfterAccept = function ( self )
		CommonAfterAccept(self)
	end,
	AfterFinishTask = function(self)
		CommonAfterFinishing(self)
	end,
	WantToTurnInTask = function ( self )
		CommonWantToTurnIn(self)
	end,
	GetNPCStatus = function(self, npcID)
		local acceptNPC, doNPC, turnInNPC = CommonGetTaskNPC(self)
		local clientStatus = self:GetClientStatus()
		if turnInNPC == npcID and clientStatus == ECTaskStatus.CanComplete then
			return NPC_STATUS.CAN_COMPLETE
		end
		if doNPC == npcID and clientStatus == ECTaskStatus.Doing then
			return NPC_STATUS.DOING
		end
		if acceptNPC == npcID and clientStatus == ECTaskStatus.CanAccept then
			return NPC_STATUS.CAN_ACCEPT
		end
	end
}
local killPlayerTaskMT = { __index = KillPlayerTask}
function CreateKillPlayerTask(
		branchID,
		taskID,
		step,
		acceptTime,
		status,
		round,
		score,
		taskScheme)
	return setmetatable({
		branchID   = branchID,
		taskID     = taskID,
		step       = step,
		acceptTime = acceptTime,
		status     = status,
		round      = round,
		score      = score,
		taskScheme = taskScheme,
	haveUpdatedCanCompleteStatus = false}, killPlayerTaskMT)
end
local EscortTask =
{
	GetClientStatus = function(...)
		local clientStatus = CommonGetClientStatus(...)
		return clientStatus
	end,
	CanComplete = function(...)
		return CommonCanCompleteTask(...)
	end,
	CanAccept = function(...)
		return CommonCanAcceptTask(...)
	end,
	WantToAccept = function ( self )
		CommonWantToAccept(self)
	end,
	GetTaskGoal = function(self)
		return 2
	end,
	GetTaskScore = function(self)
		return self.score
	end,
	WantToTurnInTask = function ( self )
		CommonWantToTurnIn(self)
	end,
	UpdateTaskScore = function(self, score)
		self.score = score
		if EntityModule.hero then
			local m = EntityMessage_pb.SC_Entity_SyncTaskItem()
			local taskItem = m.ItemList:add()
			taskItem.BranchID = self.branchID
			taskItem.TaskID = self.taskID
			taskItem.Step = self.step
			taskItem.AcceptTime = self.acceptTime
			taskItem.Status = self.status
			taskItem.Round = self.round
			taskItem.Score = self.score
			taskItem.Change = 1
			EntityModule.hero.heroTaskLC.Update(m)
		end
	end,
	AfterAccept = function ( self )
		EntityModule.hero.heroTaskLC.SetCurFocusTask(self)
		if self:GetClientStatus() ~= ECTaskStatus.CanComplete then
			if EntityModule.hero.heroTaskLC.onEscort == 1 then
				local taskPart = EntityModule.hero.heroTaskLC
				EntityModule.hero.escortLG.GoToDestination()
				local mapID = taskPart.GetMapID(self.taskScheme.WayMapID)
				local tagMapID = taskPart.CalcMapID(0, mapID)
				if tagMapID ~= 0 then
					local doNPC = taskPart.GetDoNPC(self.taskScheme)
					doNPC = tonumber(doNPC)
					if doNPC ~= 0 then
						if EntityModule.hero.heroTaskLC.curFocusTask ~= self then return end
						local wayNpcID = EntityModule.hero.heroTaskLC.GetNpcID(self.taskScheme.WayNPC)
						EntityModule.hero.navigationLG:NavigateToNPC(wayNpcID)
					else
						local doMapPoint = HelperL.PBString2Vector3(self.taskScheme.WayCoordinate)
						if EntityModule.hero.heroTaskLC.curFocusTask ~= self then return end
						local mapID = EntityModule.hero.heroTaskLC.GetMapID(self.taskScheme.WayMapID)
						EntityModule.hero.navigationLG:SetWorldDestination(mapID, doMapPoint, false)
					end
				end
			else
				if self.monsterMapID and self.pos then
					EntityModule.hero:SetWorldDestination( self.monsterMapID, self.pos, false,true)
				end
			end
		end
	end,
	AfterFinishTask = function(self)
		--CommonAfterFinishing(self)
		local turnInNPC = EntityModule.hero.heroTaskLC.GetTurnInNPC(self.taskScheme)
		if turnInNPC and turnInNPC ~= 0 then
			if NPCManager.npcs[turnInNPC] then
				NPCManager.npcs[turnInNPC].npcComp:TriggerEvent()
			end
		end
	end,
	GetNPCStatus = function(self, npcID)
		assert(npcID,'参数npcid')
		local acceptNPC, doNPC, turnInNPC = CommonGetTaskNPC(self)
		local clientStatus = self:GetClientStatus()
		if turnInNPC == npcID and clientStatus == ECTaskStatus.CanComplete then
			return NPC_STATUS.CAN_COMPLETE
		end
		if doNPC == npcID and clientStatus == ECTaskStatus.Doing then
			return NPC_STATUS.DOING
		end
		if acceptNPC == npcID and clientStatus == ECTaskStatus.CanAccept then
			return NPC_STATUS.CAN_ACCEPT
		end
		return NPC_STATUS.DEFAULT
	end
}
local escortTaskMT = { __index = EscortTask}
function CreateEscortTask (
		branchID,
		taskID,
		step,
		acceptTime,
		status,
		round,
		score,
		taskScheme)
	return setmetatable({
		branchID   = branchID,
		taskID     = taskID,
		step       = step,
		acceptTime = acceptTime,
		status     = status,
		round      = round,
		score      = score,
		taskScheme = taskScheme,
	haveUpdatedCanCompleteStatus = false}, escortTaskMT)
end
local TalkEctypeTask =
{
	GetClientStatus = function(...)
		local clientStatus = CommonGetClientStatus(...)
		return clientStatus
	end,
	CanComplete = function(...)
		return CommonCanCompleteTask(...)
	end,
	CanAccept = function(...)
		return CommonCanAcceptTask(...)
	end,
	WantToAccept = function ( self )
		CommonWantToAccept(self)
	end,
	GetTaskGoal = function(self)
		return 1
	end,
	GetTaskScore = function(self)
		return self.score
	end,
	AfterAccept = function ( self )
		CommonAfterAccept(self)
	end,
	AfterFinishTask = function(self)
		CommonAfterFinishing(self)
	end,
	WantToTurnInTask = function ( self )
		CommonWantToTurnIn(self)
	end,
	GetNPCStatus = function(self, npcID)
		local acceptNPC, doNPC, turnInNPC = CommonGetTaskNPC(self)
		local clientStatus = self:GetClientStatus()

		if turnInNPC == npcID and clientStatus == ECTaskStatus.CanComplete then
			return NPC_STATUS.CAN_COMPLETE
		end
		if doNPC == npcID and clientStatus == ECTaskStatus.Doing then
			return NPC_STATUS.DOING
		end
		if acceptNPC == npcID and clientStatus == ECTaskStatus.CanAccept then
			return NPC_STATUS.CAN_ACCEPT
		end
	end
}
local talkEctypeMT = { __index = TalkEctypeTask}
function CreateTalkEctypeTask(
		branchID,
		taskID,
		step,
		acceptTime,
		status,
		round,
		score,
		taskScheme)
	return setmetatable({
		branchID   = branchID,
		taskID     = taskID,
		step       = step,
		acceptTime = acceptTime,
		status     = status,
		round      = round,
		score      = score,
		taskScheme = taskScheme,
	haveUpdatedCanCompleteStatus = false}, talkEctypeMT)
end
local CollectGoodTask =
{
	GetClientStatus = function(...)
		local clientStatus = CommonGetClientStatus(...)
		return clientStatus
	end,
	CanComplete = function(...)
		return CommonCanCompleteTask(...)
	end,
	CanAccept = function(...)
		return CommonCanAcceptTask(...)
	end,
	WantToAccept = function ( self )
		CommonWantToAccept(self)
	end,
	GetTaskGoal = function(self)
		return self.taskScheme.Parameter2
	end,
	GetTaskScore = function(self)
		local pack = SkepModule:GetSkepByID(SKEPID.SKEPID_TASKPACKET)
		local goodsID = self.taskScheme.Parameter1
		if goodsID > DEFINE.MAX_MEDICAMENT_ID then
			local equipSchemeItem = Schemes.Equipment.Get(goodsID)
			if equipSchemeItem and equipSchemeItem.PacketID == SKEPID.SKEPID_TASKPACKET then
				pack = SkepModule:GetSkepByID(SKEPID.SKEPID_TASKPACKET)
			end
		else
			local medicamentSchemeItem = Schemes.Medicament.Get(goodsID)
			if medicamentSchemeItem and medicamentSchemeItem.PacketID == SKEPID.SKEPID_TASKPACKET then
				pack = SkepModule:GetSkepByID(SKEPID.SKEPID_TASKPACKET)
			end
		end
		local count = pack:Count(self.taskScheme.Parameter1)
		self.score = count
		return count
	end,
	WantToTurnInTask = function ( self )
		CommonWantToTurnIn(self)
	end,
	UpdateData = function(self )
		if EntityModule.hero then
			local m = EntityMessage_pb.SC_Entity_SyncTaskItem()
			local taskItem = m.ItemList:add()
			taskItem.BranchID = self.branchID
			taskItem.TaskID = self.taskID
			taskItem.Step = self.step
			taskItem.AcceptTime = self.acceptTime
			taskItem.Status = self.status
			taskItem.Round = self.round
			taskItem.Score = self.score
			taskItem.Change = 1
			EntityModule.hero.heroTaskLC.Update(m)
			--EventManager:Fire(EventID.TaskPartUpdate)
		end
	end,
	AfterAccept = function ( self )
		CommonAfterAccept(self)

		local status = self:GetClientStatus()
		if status == ECTaskStatus.CanComplete then
			self:WantToTurnInTask()
		end
	end,
	AfterFinishTask = function(self)
		CommonAfterFinishing(self)
	end,
	GetNPCStatus = function(self, npcID)
		local acceptNPC, doNPC, turnInNPC = CommonGetTaskNPC(self)
		local clientStatus = self:GetClientStatus()
		if turnInNPC == npcID and clientStatus == ECTaskStatus.CanComplete then
			return NPC_STATUS.CAN_COMPLETE
		end
		if doNPC == npcID and clientStatus == ECTaskStatus.Doing then
			return NPC_STATUS.DOING
		end
		if acceptNPC == npcID and clientStatus == ECTaskStatus.CanAccept then
			return NPC_STATUS.CAN_ACCEPT
		end
	end
}
local collectGoodTaskMT = {__index = CollectGoodTask }
function CreateCollectGoodTask(
		branchID,
		taskID,
		step,
		acceptTime,
		status,
		round,
		score,
		taskScheme)
	return setmetatable({
		branchID   = branchID,
		taskID     = taskID,
		step       = step,
		acceptTime = acceptTime,
		status     = status,
		round      = round,
		score      = score,
		taskScheme = taskScheme,
	haveUpdatedCanCompleteStatus = false}, collectGoodTaskMT)
end
local TalkThenTransmitTask =
{
	GetClientStatus = function(...)
		local clientStatus = CommonGetClientStatus(...)
		return clientStatus
	end,
	CanComplete = function(...)
		return CommonCanCompleteTask(...)
	end,
	CanAccept = function(...)
		return CommonCanAcceptTask(...)
	end,
	WantToAccept = function ( self )
		CommonWantToAccept(self)
	end,
	GetTaskGoal = function(self)
		return 1
	end,
	GetTaskScore = function(self)
		return self.score
	end,
	WantToTurnInTask = function ( self )
		CommonWantToTurnIn(self)
	end,
	AfterAccept = function ( self )
		--CommonAfterAccept(self)
	end,
	AfterFinishTask = function(self)
		CommonAfterFinishing(self)
	end,
	GetNPCStatus = function(self, npcID)
		local acceptNPC, doNPC, turnInNPC = CommonGetTaskNPC(self)
		local clientStatus = self:GetClientStatus()
		if turnInNPC == npcID and clientStatus == ECTaskStatus.CanComplete then
			return NPC_STATUS.CAN_COMPLETE
		end
		if acceptNPC == npcID and clientStatus == ECTaskStatus.CanAccept then
			return NPC_STATUS.CAN_ACCEPT
		end
	end
}
local talkThenTransmitTaskMT =
{
	__index = TalkThenTransmitTask
}
function CreateTalkThenTransmitTask(
		branchID,
		taskID,
		step,
		acceptTime,
		status,
		round,
		score,
		taskScheme)
	return setmetatable({
		branchID   = branchID,
		taskID     = taskID,
		step       = step,
		acceptTime = acceptTime,
		status     = status,
		round      = round,
		score      = score,
		taskScheme = taskScheme},talkThenTransmitTaskMT)
end
local UpgradeTask =
{
	GetClientStatus = function(...)
		local clientStatus = CommonGetClientStatus(...)
		return clientStatus
	end,
	CanComplete = function(...)
		return CommonCanCompleteTask(...)
	end,
	CanAccept = function(...)
		return CommonCanAcceptTask(...)
	end,
	WantToAccept = function ( self )
		CommonWantToAccept(self)
	end,
	GetTaskGoal = function(self)
		return self.taskScheme.Parameter2
	end,
	GetTaskScore = function(self)
		return self.score
	end,
	WantToTurnInTask = function ( self )
		CommonWantToTurnIn(self)
	end,
	AfterAccept = function ( self )
		--CommonAfterAccept(self)
	end,
	AfterFinishTask = function(self)
		CommonAfterFinishing(self)
	end,
	GetNPCStatus = function(self, npcID)
		local acceptNPC, doNPC, turnInNPC = CommonGetTaskNPC(self)
		local clientStatus = self:GetClientStatus()
		if turnInNPC == npcID and clientStatus == ECTaskStatus.CanComplete then
			return NPC_STATUS.CAN_COMPLETE
		end
		if acceptNPC == npcID and clientStatus == ECTaskStatus.CanAccept then
			return NPC_STATUS.CAN_ACCEPT
		end
	end
}
local UpgradeTaskMT =
{
	__index = UpgradeTask
}
function CreateTalkUpgrade(
		branchID,
		taskID,
		step,
		acceptTime,
		status,
		round,
		score,
		taskScheme)
	return setmetatable({
		branchID   = branchID,
		taskID     = taskID,
		step       = step,
		acceptTime = acceptTime,
		status     = status,
		round      = round,
		score      = score,
		taskScheme = taskScheme},UpgradeTaskMT)
end

local DoDailyTask =
{
	GetClientStatus = function(...)
		local clientStatus = CommonGetClientStatus(...)
		return clientStatus
	end,
	CanComplete = function(...)
		return CommonCanCompleteTask(...)
	end,
	CanAccept = function(...)
		return CommonCanAcceptTask(...)
	end,
	WantToAccept = function ( self )
		CommonWantToAccept(self)
	end,
	GetTaskGoal = function(self)
		return self.taskScheme.Parameter2
	end,
	GetTaskScore = function(self)
		return self.score
	end,
	AfterAccept = function ( self )
		CommonAfterAccept(self)
	end,
	AfterFinishTask = function(self)
		CommonAfterFinishing(self)
	end,
	WantToTurnInTask = function ( self )
		CommonWantToTurnIn(self)
	end,
	GetNPCStatus = function(self, npcID)
		local acceptNPC, doNPC, turnInNPC = CommonGetTaskNPC(self)
		local clientStatus = self:GetClientStatus()

		if turnInNPC == npcID and clientStatus == ECTaskStatus.CanComplete then
			return NPC_STATUS.CAN_COMPLETE
		end
		if doNPC == npcID and clientStatus == ECTaskStatus.Doing then
			return NPC_STATUS.DOING
		end
		if acceptNPC == npcID and clientStatus == ECTaskStatus.CanAccept then
			return NPC_STATUS.CAN_ACCEPT
		end
	end
}
local doDailyTaskMT = { __index = DoDailyTask}
function CreateTaskDoDailyTask(
		branchID,
		taskID,
		step,
		acceptTime,
		status,
		round,
		score,
		taskScheme)
	return setmetatable({
		branchID   = branchID,
		taskID     = taskID,
		step       = step,
		acceptTime = acceptTime,
		status     = status,
		round      = round,
		score      = score,
		taskScheme = taskScheme},doDailyTaskMT)
end

local OperateCollectGoodTask =
{
	GetClientStatus = function(...)
		local clientStatus = CommonGetClientStatus(...)
		return clientStatus
	end,
	CanComplete = function(...)
		return CommonCanCompleteTask(...)
	end,
	CanAccept = function(...)
		return CommonCanAcceptTask(...)
	end,
	WantToAccept = function ( self )
		CommonWantToAccept(self)
	end,
	GetTaskGoal = function(self)
		return self.taskScheme.Parameter2
	end,
	GetTaskScore = function(self)
		local pack = SkepModule:GetSkepByID(SKEPID.SKEPID_TASKPACKET)
		local goodsID = self.taskScheme.Parameter1
		if goodsID > DEFINE.MAX_MEDICAMENT_ID then
			local equipSchemeItem = Schemes.Equipment.Get(goodsID)
			if equipSchemeItem and equipSchemeItem.PacketID == SKEPID.SKEPID_TASKPACKET then
				pack = SkepModule:GetSkepByID(SKEPID.SKEPID_TASKPACKET)
			end
		else
			local medicamentSchemeItem = Schemes.Medicament.Get(goodsID)
			if medicamentSchemeItem and medicamentSchemeItem.PacketID == SKEPID.SKEPID_TASKPACKET then
				pack = SkepModule:GetSkepByID(SKEPID.SKEPID_TASKPACKET)
			end
		end
		local count = pack:Count(self.taskScheme.Parameter1)
		self.score = count
		return count
	end,
	WantToTurnInTask = function ( self )
		CommonWantToTurnIn(self)
	end,
	UpdateData = function(self )
		if EntityModule.hero then
			local m = EntityMessage_pb.SC_Entity_SyncTaskItem()
			local taskItem = m.ItemList:add()
			taskItem.BranchID = self.branchID
			taskItem.TaskID = self.taskID
			taskItem.Step = self.step
			taskItem.AcceptTime = self.acceptTime
			taskItem.Status = self.status
			taskItem.Round = self.round
			taskItem.Score = self.score
			taskItem.Change = 1
			EntityModule.hero.heroTaskLC.Update(m)
			--EventManager:Fire(EventID.TaskPartUpdate)
		end
	end,
	AfterAccept = function ( self )
		CommonAfterAccept(self)

		local status = self:GetClientStatus()
		if status == ECTaskStatus.CanComplete then
			self:WantToTurnInTask()
		end
	end,
	AfterFinishTask = function(self)
		CommonAfterFinishing(self)
	end,
	GetNPCStatus = function(self, npcID)
		local acceptNPC, doNPC, turnInNPC = CommonGetTaskNPC(self)
		local clientStatus = self:GetClientStatus()
		if turnInNPC == npcID and clientStatus == ECTaskStatus.CanComplete then
			return NPC_STATUS.CAN_COMPLETE
		end
		if doNPC == npcID and clientStatus == ECTaskStatus.Doing then
			return NPC_STATUS.DOING
		end
		if acceptNPC == npcID and clientStatus == ECTaskStatus.CanAccept then
			return NPC_STATUS.CAN_ACCEPT
		end
	end
}
local operateCollectGoodTaskMT = {__index = OperateCollectGoodTask }
function CreateOperateCollectGoodTask(
		branchID,
		taskID,
		step,
		acceptTime,
		status,
		round,
		score,
		taskScheme)
	return setmetatable({
		branchID   = branchID,
		taskID     = taskID,
		step       = step,
		acceptTime = acceptTime,
		status     = status,
		round      = round,
		score      = score,
		taskScheme = taskScheme,
	haveUpdatedCanCompleteStatus = false}, operateCollectGoodTaskMT)
end

local EveryDayUpgradeTask =
{
	GetClientStatus = function(...)
		local clientStatus = CommonGetClientStatus(...)
		return clientStatus
	end,
	CanComplete = function(...)
		return CommonCanCompleteTask(...)
	end,
	CanAccept = function(...)
		return CommonCanAcceptTask(...)
	end,
	WantToAccept = function ( self )
		CommonWantToAccept(self)
	end,
	GetTaskGoal = function(self)
		return self.taskScheme.Parameter2
	end,
	GetTaskScore = function(self)
		return self.score 
	end,
	WantToTurnInTask = function ( self )
		CommonWantToTurnIn(self)
	end,
	UpdateData = function(self )

	end,
	AfterAccept = function ( self )
		CommonAfterAccept(self)
	end,
	AfterFinishTask = function(self)
		CommonAfterFinishing(self)
	end,
	GetNPCStatus = function(self, npcID)
		local acceptNPC, doNPC, turnInNPC = CommonGetTaskNPC(self)
		local clientStatus = self:GetClientStatus()
		if turnInNPC == npcID and clientStatus == ECTaskStatus.CanComplete then
			return NPC_STATUS.CAN_COMPLETE
		end
		if doNPC == npcID and clientStatus == ECTaskStatus.Doing then
			return NPC_STATUS.DOING
		end
		if acceptNPC == npcID and clientStatus == ECTaskStatus.CanAccept then
			return NPC_STATUS.CAN_ACCEPT
		end
	end
}
local EveryDayUpgradeTaskMT = {__index = EveryDayUpgradeTask }
function CreateTaskEveryDayUpgrade(
		branchID,
		taskID,
		step,
		acceptTime,
		status,
		round,
		score,
		taskScheme)
	return setmetatable({
		branchID   = branchID,
		taskID     = taskID,
		step       = step,
		acceptTime = acceptTime,
		status     = status,
		round      = round,
		score      = score,
		taskScheme = taskScheme,
	haveUpdatedCanCompleteStatus = false}, EveryDayUpgradeTaskMT)
end

local EnterSceneTask =
{
	GetClientStatus = function(...)
		local clientStatus = CommonGetClientStatus(...)
		return clientStatus
	end,
	CanComplete = function(...)
		return CommonCanCompleteTask(...)
	end,
	CanAccept = function(...)
		return CommonCanAcceptTask(...)
	end,
	WantToAccept = function ( self )
		CommonWantToAccept(self)
	end,
	GetTaskGoal = function(self)
		return 1
	end,
	GetTaskScore = function(self)
		return self.score
	end,
	AfterAccept = function ( self )
		CommonAfterAccept(self)
	end,
	AfterFinishTask = function(self)
		CommonAfterFinishing(self)
	end,
	WantToTurnInTask = function ( self )
		CommonWantToTurnIn(self)
	end,
	GetNPCStatus = function(self, npcID)
		local acceptNPC, doNPC, turnInNPC = CommonGetTaskNPC(self)
		local clientStatus = self:GetClientStatus()

		if turnInNPC == npcID and clientStatus == ECTaskStatus.CanComplete then
			return NPC_STATUS.CAN_COMPLETE
		end
		if doNPC == npcID and clientStatus == ECTaskStatus.Doing then
			return NPC_STATUS.DOING
		end
		if acceptNPC == npcID and clientStatus == ECTaskStatus.CanAccept then
			return NPC_STATUS.CAN_ACCEPT
		end
	end
}
local enterSceneMT = { __index = EnterSceneTask}
function CreateEnterSceneTask(
		branchID,
		taskID,
		step,
		acceptTime,
		status,
		round,
		score,
		taskScheme)
	return setmetatable({
		branchID   = branchID,
		taskID     = taskID,
		step       = step,
		acceptTime = acceptTime,
		status     = status,
		round      = round,
		score      = score,
		taskScheme = taskScheme,
	haveUpdatedCanCompleteStatus = false}, enterSceneMT)
end

local NewbieTargetTask =
{
	GetClientStatus = function(...)
		local clientStatus = CommonGetClientStatus(...)
		return clientStatus
	end,
	CanComplete = function(...)
		return CommonCanCompleteTask(...)
	end,
	CanAccept = function(...)
		return CommonCanAcceptTask(...)
	end,
	WantToAccept = function ( self )
		CommonWantToAccept(self)
	end,
	GetTaskGoal = function(self)
		local result, num, maxNum = RedSpotManager.CheckSevenDayTargetType(self.taskScheme.Parameter1, self.taskScheme.Parameter2, self.taskScheme.Parameter3)
		maxNum = maxNum or 0
		return maxNum
	end,
	GetTaskScore = function(self)
		local result, num, maxNum = RedSpotManager.CheckSevenDayTargetType(self.taskScheme.Parameter1, self.taskScheme.Parameter2, self.taskScheme.Parameter3)
		num = num or 0
		self.score = num
		return num
	end,
	WantToTurnInTask = function ( self )
		CommonWantToTurnIn(self)
	end,
	UpdateData = function(self )
		if EntityModule.hero then
			local m = EntityMessage_pb.SC_Entity_SyncTaskItem()
			local taskItem = m.ItemList:add()
			taskItem.BranchID = self.branchID
			taskItem.TaskID = self.taskID
			taskItem.Step = self.step
			taskItem.AcceptTime = self.acceptTime
			taskItem.Status = self.status
			taskItem.Round = self.round
			taskItem.Score = self.score
			taskItem.Change = 1
			EntityModule.hero.heroTaskLC.Update(m)
		end
	end,
	AfterAccept = function ( self )
		CommonAfterAccept(self)

		local status = self:GetClientStatus()
		if status == ECTaskStatus.CanComplete then
			self:WantToTurnInTask()
		end
	end,
	AfterFinishTask = function(self)
		CommonAfterFinishing(self)
	end,
	GetNPCStatus = function(self, npcID)
		local acceptNPC, doNPC, turnInNPC = CommonGetTaskNPC(self)
		local clientStatus = self:GetClientStatus()
		if turnInNPC == npcID and clientStatus == ECTaskStatus.CanComplete then
			return NPC_STATUS.CAN_COMPLETE
		end
		if doNPC == npcID and clientStatus == ECTaskStatus.Doing then
			return NPC_STATUS.DOING
		end
		if acceptNPC == npcID and clientStatus == ECTaskStatus.CanAccept then
			return NPC_STATUS.CAN_ACCEPT
		end
	end
}
local newbieTargetTaskMT = {__index = NewbieTargetTask }
function CreateNewbieTargetTask(
		branchID,
		taskID,
		step,
		acceptTime,
		status,
		round,
		score,
		taskScheme)
	return setmetatable({
		branchID   = branchID,
		taskID     = taskID,
		step       = step,
		acceptTime = acceptTime,
		status     = status,
		round      = round,
		score      = score,
		taskScheme = taskScheme,
	haveUpdatedCanCompleteStatus = false}, newbieTargetTaskMT)
end

local LoginDayTask =
{
	GetClientStatus = function(...)
		local clientStatus = CommonGetClientStatus(...)
		return clientStatus
	end,
	CanComplete = function(...)
		return CommonCanCompleteTask(...)
	end,
	CanAccept = function(...)
		return CommonCanAcceptTask(...)
	end,
	WantToAccept = function ( self )
		CommonWantToAccept(self)
	end,
	GetTaskGoal = function(self)
		return self.taskScheme.Parameter2
	end,
	GetTaskScore = function(self)
		local score = 0
		if self.taskScheme.Parameter1 == 1 then
			score = HelperL.GetServerOpenDays()
			if score == self.taskScheme.Parameter2 and self.taskScheme.Parameter3 > 0 then
				local curDate = os.date('*t', HelperL.GetServerTime())
				local curHour = curDate.hour
				if curHour < self.taskScheme.Parameter3 then
					score = score - 1
				end
			end
		end
		self.score = score
		return score
	end,
	WantToTurnInTask = function ( self )
		CommonWantToTurnIn(self)
	end,
	UpdateData = function(self )
		if EntityModule.hero then
			local m = EntityMessage_pb.SC_Entity_SyncTaskItem()
			local taskItem = m.ItemList:add()
			taskItem.BranchID = self.branchID
			taskItem.TaskID = self.taskID
			taskItem.Step = self.step
			taskItem.AcceptTime = self.acceptTime
			taskItem.Status = self.status
			taskItem.Round = self.round
			taskItem.Score = self.score
			taskItem.Change = 1
			EntityModule.hero.heroTaskLC.Update(m)
		end
	end,
	AfterAccept = function ( self )
		CommonAfterAccept(self)

		local status = self:GetClientStatus()
		if status == ECTaskStatus.CanComplete then
			self:WantToTurnInTask()
		end
	end,
	AfterFinishTask = function(self)
		CommonAfterFinishing(self)
	end,
	GetNPCStatus = function(self, npcID)
		local acceptNPC, doNPC, turnInNPC = CommonGetTaskNPC(self)
		local clientStatus = self:GetClientStatus()
		if turnInNPC == npcID and clientStatus == ECTaskStatus.CanComplete then
			return NPC_STATUS.CAN_COMPLETE
		end
		if doNPC == npcID and clientStatus == ECTaskStatus.Doing then
			return NPC_STATUS.DOING
		end
		if acceptNPC == npcID and clientStatus == ECTaskStatus.CanAccept then
			return NPC_STATUS.CAN_ACCEPT
		end
	end
}
local loginDayTaskMT = {__index = LoginDayTask }
function CreateLoginDayTask(
		branchID,
		taskID,
		step,
		acceptTime,
		status,
		round,
		score,
		taskScheme)
	return setmetatable({
		branchID   = branchID,
		taskID     = taskID,
		step       = step,
		acceptTime = acceptTime,
		status     = status,
		round      = round,
		score      = score,
		taskScheme = taskScheme,
	haveUpdatedCanCompleteStatus = false}, loginDayTaskMT)
end

local ActorPowerTask =
{
	GetClientStatus = function(...)
		local clientStatus = CommonGetClientStatus(...)
		return clientStatus
	end,
	CanComplete = function(...)
		return CommonCanCompleteTask(...)
	end,
	CanAccept = function(...)
		return CommonCanAcceptTask(...)
	end,
	WantToAccept = function ( self )
		CommonWantToAccept(self)
	end,
	GetTaskGoal = function(self)
		return self.taskScheme.Parameter1
	end,
	GetTaskScore = function(self)
		local score = EntityModule.hero.propertyLC:GetProperty(PLAYER_FIELD.PLAYER_FIELD_POWER)
		if score < self.taskScheme.Parameter1 then
			local dayPass = false
			if self.taskScheme.Parameter2 > 0 then
				local openDay = HelperL.GetServerOpenDays()
				if openDay >= self.taskScheme.Parameter2 then
					dayPass = true
					if openDay == self.taskScheme.Parameter2 and self.taskScheme.Parameter3 > 0 then
						local curDate = os.date('*t', HelperL.GetServerTime())
						local curHour = curDate.hour
						if curHour < self.taskScheme.Parameter3 then
							dayPass = false
						end
					end
				end
			end
			if dayPass then
				score = self.taskScheme.Parameter1
			end
		end
		self.score = score
		return score
	end,
	WantToTurnInTask = function ( self )
		CommonWantToTurnIn(self)
	end,
	UpdateData = function(self )
		if EntityModule.hero then
			local m = EntityMessage_pb.SC_Entity_SyncTaskItem()
			local taskItem = m.ItemList:add()
			taskItem.BranchID = self.branchID
			taskItem.TaskID = self.taskID
			taskItem.Step = self.step
			taskItem.AcceptTime = self.acceptTime
			taskItem.Status = self.status
			taskItem.Round = self.round
			taskItem.Score = self.score
			taskItem.Change = 1
			EntityModule.hero.heroTaskLC.Update(m)
		end
	end,
	AfterAccept = function ( self )
		CommonAfterAccept(self)

		local status = self:GetClientStatus()
		if status == ECTaskStatus.CanComplete then
			self:WantToTurnInTask()
		end
	end,
	AfterFinishTask = function(self)
		CommonAfterFinishing(self)
	end,
	GetNPCStatus = function(self, npcID)
		local acceptNPC, doNPC, turnInNPC = CommonGetTaskNPC(self)
		local clientStatus = self:GetClientStatus()
		if turnInNPC == npcID and clientStatus == ECTaskStatus.CanComplete then
			return NPC_STATUS.CAN_COMPLETE
		end
		if doNPC == npcID and clientStatus == ECTaskStatus.Doing then
			return NPC_STATUS.DOING
		end
		if acceptNPC == npcID and clientStatus == ECTaskStatus.CanAccept then
			return NPC_STATUS.CAN_ACCEPT
		end
	end
}
local actorPowerTaskMT = {__index = ActorPowerTask }
function CreateActorPowerTask(
		branchID,
		taskID,
		step,
		acceptTime,
		status,
		round,
		score,
		taskScheme)
	return setmetatable({
		branchID   = branchID,
		taskID     = taskID,
		step       = step,
		acceptTime = acceptTime,
		status     = status,
		round      = round,
		score      = score,
		taskScheme = taskScheme,
	haveUpdatedCanCompleteStatus = false}, actorPowerTaskMT)
end

local BuyRechargeCardTask =
{
	GetClientStatus = CommonGetClientStatus,
	CanComplete = CommonCanCompleteTask,
	CanAccept = CommonCanAcceptTask,
	WantToAccept = CommonWantToAccept,
	GetTaskGoal = function(self)
		return 1
	end,
	GetTaskScore = function(self)
		local score = 0
		if EntityModule.hero.logicLC:HadBoughtCardID(self.taskScheme.Parameter1) then
			score = 1
		end
		self.score = score
		return score
	end,
	WantToTurnInTask = CommonWantToTurnIn,
	UpdateData = function(self )
		if EntityModule.hero then
			local m = EntityMessage_pb.SC_Entity_SyncTaskItem()
			local taskItem = m.ItemList:add()
			taskItem.BranchID = self.branchID
			taskItem.TaskID = self.taskID
			taskItem.Step = self.step
			taskItem.AcceptTime = self.acceptTime
			taskItem.Status = self.status
			taskItem.Round = self.round
			taskItem.Score = self.score
			taskItem.Change = 1
			EntityModule.hero.heroTaskLC.Update(m)
		end
	end,
	AfterAccept = function ( self )
		CommonAfterAccept(self)

		local status = self:GetClientStatus()
		if status == ECTaskStatus.CanComplete then
			self:WantToTurnInTask()
		end
	end,
	AfterFinishTask = CommonAfterFinishing,
	GetNPCStatus = function(self, npcID)
		local acceptNPC, doNPC, turnInNPC = CommonGetTaskNPC(self)
		local clientStatus = self:GetClientStatus()
		if turnInNPC == npcID and clientStatus == ECTaskStatus.CanComplete then
			return NPC_STATUS.CAN_COMPLETE
		end
		if doNPC == npcID and clientStatus == ECTaskStatus.Doing then
			return NPC_STATUS.DOING
		end
		if acceptNPC == npcID and clientStatus == ECTaskStatus.CanAccept then
			return NPC_STATUS.CAN_ACCEPT
		end
	end
}
local buyRechargeCardTaskMT = {__index = BuyRechargeCardTask }
function CreateBuyRechargeCardTask(
		branchID,
		taskID,
		step,
		acceptTime,
		status,
		round,
		score,
		taskScheme)
	return setmetatable({
		branchID   = branchID,
		taskID     = taskID,
		step       = step,
		acceptTime = acceptTime,
		status     = status,
		round      = round,
		score      = score,
		taskScheme = taskScheme,
	haveUpdatedCanCompleteStatus = false}, buyRechargeCardTaskMT)
end

local LieMingAndQiFuCountTask =
{
	GetClientStatus = CommonGetClientStatus,
	CanComplete = CommonCanCompleteTask,
	CanAccept = CommonCanAcceptTask,
	WantToAccept = CommonWantToAccept,
	GetTaskGoal = function(self)
		return self.taskScheme.Parameter2
	end,
	GetTaskScore = function(self)
		-- local score = 0
		-- local maxStage = HeroDataManager:GetLogicData(LOGIC_DATA.DATA_SOCIETY_ECTYPETOWER_CURSTAGE)
		-- if maxStage>=self.taskScheme.Parameter1 then
		-- 	score = 1
		-- end
		-- self.score = score
		return self.score
	end,
	WantToTurnInTask = CommonWantToTurnIn,
	UpdateData = function(self )
		if EntityModule.hero then
			local m = EntityMessage_pb.SC_Entity_SyncTaskItem()
			local taskItem = m.ItemList:add()
			taskItem.BranchID = self.branchID
			taskItem.TaskID = self.taskID
			taskItem.Step = self.step
			taskItem.AcceptTime = self.acceptTime
			taskItem.Status = self.status
			taskItem.Round = self.round
			taskItem.Score = self.score
			taskItem.Change = 1
			EntityModule.hero.heroTaskLC.Update(m)
		end
	end,
	AfterAccept = function ( self )
		CommonAfterAccept(self)

		local status = self:GetClientStatus()
		if status == ECTaskStatus.CanComplete then
			self:WantToTurnInTask()
		end
	end,
	AfterFinishTask = CommonAfterFinishing,
	GetNPCStatus = function(self, npcID)
		local acceptNPC, doNPC, turnInNPC = CommonGetTaskNPC(self)
		local clientStatus = self:GetClientStatus()
		if turnInNPC == npcID and clientStatus == ECTaskStatus.CanComplete then
			return NPC_STATUS.CAN_COMPLETE
		end
		if doNPC == npcID and clientStatus == ECTaskStatus.Doing then
			return NPC_STATUS.DOING
		end
		if acceptNPC == npcID and clientStatus == ECTaskStatus.CanAccept then
			return NPC_STATUS.CAN_ACCEPT
		end
	end
}
local LieMingAndQiFuCountTaskMT = {__index = LieMingAndQiFuCountTask }
function CreateLieMingAndQiFuCountTask(
		branchID,
		taskID,
		step,
		acceptTime,
		status,
		round,
		score,
		taskScheme)
	return setmetatable({
		branchID   = branchID,
		taskID     = taskID,
		step       = step,
		acceptTime = acceptTime,
		status     = status,
		round      = round,
		score      = score,
		taskScheme = taskScheme,
	haveUpdatedCanCompleteStatus = false}, LieMingAndQiFuCountTaskMT)
end

local TEctypeToLogicValue =
{
	[1] = 25,
    [2] = 406,
    [3] = 505,
    [5] = 495,
    [6] = 496,
    [7] = 497,
    [8] = 498,
    [9] = 499,
    [10] = 500,
    [11] = 501,
    [12] = 502,
    [13] = 503,
    [14] = 504,
}

local FuBenIdTask =
{
	GetClientStatus = CommonGetClientStatus,
	CanComplete = CommonCanCompleteTask,
	CanAccept = CommonCanAcceptTask,
	WantToAccept = CommonWantToAccept,
	GetTaskGoal = function(self)
		return 1
	end,
	GetTaskScore = function(self)
		local score = 0
		local fubenType = self.taskScheme.Parameter1
		print("fubenType:"..fubenType.."logic:"..TEctypeToLogicValue[fubenType])
		local maxStage = HeroDataManager:GetLogicData(TEctypeToLogicValue[fubenType])
		if maxStage == 0 or self.taskScheme.Parameter2 > maxStage then
		else
			score = 1
		end
		--self.score = score
		return score
	end,
	WantToTurnInTask = CommonWantToTurnIn,
	UpdateData = function(self )
		if EntityModule.hero then
			local m = EntityMessage_pb.SC_Entity_SyncTaskItem()
			local taskItem = m.ItemList:add()
			taskItem.BranchID = self.branchID
			taskItem.TaskID = self.taskID
			taskItem.Step = self.step
			taskItem.AcceptTime = self.acceptTime
			taskItem.Status = self.status
			taskItem.Round = self.round
			taskItem.Score = self.score
			taskItem.Change = 1
			EntityModule.hero.heroTaskLC.Update(m)
		end
	end,
	AfterAccept = function ( self )
		CommonAfterAccept(self)

		local status = self:GetClientStatus()
		if status == ECTaskStatus.CanComplete then
			self:WantToTurnInTask()
		end
	end,
	AfterFinishTask = CommonAfterFinishing,
	GetNPCStatus = function(self, npcID)
		local acceptNPC, doNPC, turnInNPC = CommonGetTaskNPC(self)
		local clientStatus = self:GetClientStatus()
		if turnInNPC == npcID and clientStatus == ECTaskStatus.CanComplete then
			return NPC_STATUS.CAN_COMPLETE
		end
		if doNPC == npcID and clientStatus == ECTaskStatus.Doing then
			return NPC_STATUS.DOING
		end
		if acceptNPC == npcID and clientStatus == ECTaskStatus.CanAccept then
			return NPC_STATUS.CAN_ACCEPT
		end
	end
}
local FuBenIdTaskMT = {__index = FuBenIdTask }
function CreateFuBenIdTask(
		branchID,
		taskID,
		step,
		acceptTime,
		status,
		round,
		score,
		taskScheme)
	return setmetatable({
		branchID   = branchID,
		taskID     = taskID,
		step       = step,
		acceptTime = acceptTime,
		status     = status,
		round      = round,
		score      = score,
		taskScheme = taskScheme,
	haveUpdatedCanCompleteStatus = false}, FuBenIdTaskMT)
end

function TaskFactory (_type, ...)
	if _type == TaskType.TaskType_KillTargetMonster then
		return CreateKillTargetMonsterTask(...)
	elseif _type == TaskType.TaskType_DramaEctype then
		return CreateDramaEctypeTask(...)
	elseif _type == TaskType.TaskType_DailyEctype then
		return CreateDailyEctypeTask(...)
	elseif _type == TaskType.TaskType_JoinActivity then
		return CreateJoinActivityTask(...)
	elseif _type == TaskType.TaskType_KillLevelMonster then
		return CreateKillLevelMonsterTask(...)
	elseif _type == TaskType.TaskType_Talk then
		return CreateTalkTask(...)
	elseif _type == TaskType.TaskType_TimeTalk then
		return CreateTimeTalkTask(...)
	elseif _type == TaskType.TaskType_TimePass then
		return CreateTimePassTask(...)
	elseif _type == TaskType.TaskType_ActorLevel then
		return CreateActorLevelTask(...)
	elseif _type == TaskType.TaskType_FriendShip then
		return CreateFriendShipTask(...)
	elseif _type == TaskType.TaskType_KillPlayer then
		return CreateKillPlayerTask(...)
	elseif _type == TaskType.TaskType_Escort then
		return CreateEscortTask(...)
	elseif _type == TaskType.TaskType_TalkEctype then
		return CreateTalkEctypeTask(...)
	elseif _type == TaskType.TaskType_CollectGood then
		return CreateCollectGoodTask(...)
	elseif _type == TaskType.TaskType_TalkThenTransmit then
		return CreateTalkThenTransmitTask(...)
	elseif _type == TaskType.TaskType_UpGrade then
		return CreateTalkUpgrade(...)
	elseif _type == TaskType.TaskType_DoDailyTask then
		return CreateTaskDoDailyTask(...)
	elseif _type == TaskType.TaskType_OperateCollectGood then
		return CreateOperateCollectGoodTask(...)
	elseif _type == TaskType.TaskType_EveryDayUpgrade then
		return CreateTaskEveryDayUpgrade(...)
	elseif _type == TaskType.TaskType_EnterScene then
		return CreateEnterSceneTask(...)
	elseif _type == TaskType.TaskType_NewbieTarget then
		return CreateNewbieTargetTask(...)
	elseif _type == TaskType.TaskType_LoginDay then
		return CreateLoginDayTask(...)
	elseif _type == TaskType.TaskType_BuyRechargeCard then
		return CreateBuyRechargeCardTask(...)
	elseif _type == TaskType.TaskType_ActorPower then
		return CreateActorPowerTask(...)
	elseif _type == TaskType.TaskType_LieMingAndQiFuCount then
		return CreateLieMingAndQiFuCountTask(...)
	elseif _type == TaskType.TaskType_FuBenId then
		return CreateFuBenIdTask(...)
	end
	-- error('未知任务类型 '..tostring(_type))
	warn('未知任务类型 '..tostring(_type))
end

return TaskFactory


