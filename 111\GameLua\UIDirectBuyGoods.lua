-- 直购物品UI
local luaID = ('UIDirectBuyGoods')

local UIDirectBuyGoods = {}

-- 初始化
function UIDirectBuyGoods:OnCreate()
	self.objList.Txt_Title.text = GetGameText(luaID, 1)
	self.objList.Txt_Gift1Title.text = GetGameText(luaID, 2)
	self.objList.Txt_Gift1Desc2.text = GetGameText(luaID, 3)
	self.objList.Txt_Sign2.text = GetGameText(luaID, 6)
	self.objList.Txt_Close.text = GetGameText(luaID, 8)
	self.objList.Btn_Close.onClick:AddListenerEx(self.OnClickClose)
	self.objList.Btn_GiftItem1Bg.onClick:AddListenerEx(self.OnClickGift1)
	self.objList.Btn_GiftItem2Bg.onClick:AddListenerEx(self.OnClickGift2)
	return true
end

-- 搬运来的
function UIDirectBuyGoods:GetGiftConfigByTypeParam(typeParam)
	local list = Schemes.PrizeTime:GetTypeParamListData(typeParam)
	if list and #list > 0 then
		local maxIndex = -1	
		local tarV = nil	
		
		for k, v in ipairs(list) do
			local value = HeroDataManager:GetLogicByte(v.SaveID1, v.SaveIndex)
			if v.GetIndex > maxIndex then
				maxIndex = v.GetIndex
				tarV = v
			end
			if value == v.GetIndex then
				return v
			end
		end
		return tarV
	end
	return nil
end

-- 窗口开启
function UIDirectBuyGoods:OnOpen(cardID)
	self.curShowCardID = cardID

	local typeParamValue = 2
	local typeParam = Schemes.PrizeTime:GetTypeParamList()
	local giftConfig = self:GetGiftConfigByTypeParam(typeParamValue)
	if giftConfig and typeParam[typeParamValue] then
		local value = HeroDataManager:GetLogicByte(giftConfig.SaveID1, giftConfig.SaveIndex)
		self.objList.Txt_Gift1Desc1.text = string.format(GetGameText(luaID, 4), typeParam[typeParamValue] - value, typeParam[typeParamValue])
	end

	local config = nil
	if cardID then
		config = Schemes.RechargeCard:Get(cardID)
	end
	if config and not HeroDataManager:HaveBuyRechardCardRecord(cardID) then
		local goodsID = nil
		local goodsNum = nil
		local prizeConfig = Schemes.PrizeTable:GetPrize(config.PrizeID)
		if prizeConfig then
			local prizeList = Schemes.PrizeTable:GetPrizeGoods(prizeConfig)
			if prizeList and prizeList[1] then
				goodsID = prizeList[1].ID
				goodsNum = prizeList[1].Num
			end
		end

		local goodsConfig = nil
		if goodsID then
			goodsConfig = Schemes:GetGoodsConfig(goodsID)
		end

		if goodsConfig then
			AtlasManager:AsyncGetSprite(goodsConfig.IconID, self.objList.Img_GoodsIcon2)
			local colorStr = HelperL.GetQualityColor(goodsConfig.Quality)
			self.objList.Txt_GiftTitle2.text = string.format(GetGameText(luaID, 5), config.Pic1, colorStr, goodsConfig.GoodsName, goodsNum)
			self.objList.Txt_GiftDesc2A.text = string.format(GetGameText(luaID, 7), 'FF9937', math.floor(config.FirstRMB / 100))
			self.objList.Txt_GiftDesc2B.text = string.format(GetGameText(luaID, 7), 'B8B8B8', config.ShowHighPrice)
			self.objList.GiftItem2:SetActive(true)
		else
			self.objList.GiftItem2:SetActive(false)
		end
	else
		-- 屏蔽gift2
		self.objList.GiftItem2:SetActive(false)
	end
	HelperL.AddFloatTween(self.objList.Img_Gift1Icon.gameObject.transform, 1, 5, 0.5)
	HelperL.AddFloatTween(self.objList.Img_GoodsIcon2.gameObject.transform, 1, 5, 0.5)	
end

-- 点击关闭按钮
function UIDirectBuyGoods.OnClickClose()
	local self = UIDirectBuyGoods
	HelperL.RemoveFloatTween(self.objList.Img_Gift1Icon.gameObject.transform)
	HelperL.RemoveFloatTween(self.objList.Img_GoodsIcon2.gameObject.transform)	
	self:CloseSelf()
end

-- 点击礼包1按钮
function UIDirectBuyGoods.OnClickGift1()
	local self = UIDirectBuyGoods
	self:CloseSelf()
	local wndMsg = WndMsg.Welfare_FreeGift
	HelperL.OnGotoWelfare(wndMsg)
end

-- 点击礼包2按钮
function UIDirectBuyGoods.OnClickGift2()
	local self = UIDirectBuyGoods
	if self.curShowCardID then
		HelperL.Recharge(self.curShowCardID)
	end
end

return UIDirectBuyGoods