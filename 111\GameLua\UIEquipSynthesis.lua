--[[
********************************************************************
    created:    2024/02/08
    author :    李锦剑
    purpose:    装备合成
*********************************************************************
--]]

local luaID = ('UIEquipSynthesis')

---@class UIEquipSynthesis:UIWndBase 装备宝箱界面
local m = {}

--------------------------------------------------------------------
-- 订阅事件列表
--------------------------------------------------------------------
function m.GetOpenEventList()
    return {
        [EventID.OnSkepGoodsChange] = m.UpdateView,
        [EventID.StoreBuyItem] = m.UpdateView,
        [EventID.LogicDataChange] = m.UpdateView,
        [EventID.OnHeroPropChange] = m.UpdateView,
        [EventID.EquipmentReplace] = m.UpdateView,
    }
end

--------------------------------------------------------------------
--预制体自适应
--------------------------------------------------------------------
function m.AdaptScale()
    HelperL.AdaptScale_Width(m.objList.Item_AttributeBox)
    HelperL.AdaptScale(m.objList.Img_Bg1, 6)
    HelperL.AdaptScale(m.objList.Img_Bg2, 6)
end

--------------------------------------------------------------------
--创建时事件
--------------------------------------------------------------------
function m.OnCreate()
    --已穿戴装备框
    ---@type SlotItem[]
    m.equipItem = {}
    --背包装备框
    ---@type SlotItem[]
    m.knapsackEquipItem = {}
    ---@type SlotItem[]
    m.goodItemList = {}

    ---@type Item_Attribute3[]
    m.Item_Attribute_List = {}

    m.selectEquipNum = 0
    ---@type {uid:integer, itemID:integer}[]
    m.selectIndexList = {}
    m.selectIndexTable = {}

    m.SynthesisEquip_Item = _GAddSlotItem(m.objList.Obj_Equip)
    ---@type SlotItem[]
    m.SynthesisExpend_Item_List = {}
    local list = { m.objList.Content1, m.objList.Content2, m.objList.Content3, m.objList.Content4 }
    for i, parent in ipairs(list) do
        m.SynthesisExpend_Item_List[i] = _GAddSlotItem(parent)
    end

    ---@type Item_Quality[]
    m.Item_Quality_List = {}
    for i = QUALITY.QUALITY_WHITE, QUALITY.QUALITY_ORANGE, 1 do
        m.Item_Quality_List[i] = m.Creation_Item_Quality(i)
    end

    m.RegisterClickEvent()
    return true
end

--------------------------------------------------------------------
--打开时
--------------------------------------------------------------------
function m.OnOpen()
    m.objList.Obj_SynthesisHint.gameObject:SetActive(false)
    m.objList.Obj_OneKeySynthesisHint.gameObject:SetActive(false)

    m.ColseOneKeySynthesis()
    m.ClearSelection()
    m.UpdateView()
end

--------------------------------------------------------------------
--注册点击事件
--------------------------------------------------------------------
function m.RegisterClickEvent()
    m:AddClick(m.objList.Btn_Close, function()
        m:CloseSelf()
    end)
    m:AddClick(m.objList.Btn_Synthesis, function()
        m.SynthesisEquip()
    end)
    m:AddClick(m.objList.Btn_SynthesisAll, function()
        m.objList.Obj_OneKeySynthesis.gameObject:SetActive(true)
    end)
    m:AddClick(m.objList.Btn_SynthesisAll2, function()
        m.OneKeySynthesisEquip()
        m.ColseOneKeySynthesis()
    end)
    m:AddClick(m.objList.Btn_OneKeySynthesisClose, function()
        m.ColseOneKeySynthesis()
    end)
    m:AddClick(m.objList.Btn_SynthesisHint, function()
        m.objList.Obj_SynthesisHint.gameObject:SetActive(false)
    end)
    m:AddClick(m.objList.Btn_OneKeySynthesisHint, function()
        m.objList.Obj_OneKeySynthesisHint.gameObject:SetActive(false)
    end)
end

--------------------------------------------------------------------
---装备排序
--------------------------------------------------------------------
function m.EquipSort(a, b)
    local goodsID_A = a:GetProperty(ENTITY_FIELD.ENTITY_FIELD_ID)
    local goodsID_B = b:GetProperty(ENTITY_FIELD.ENTITY_FIELD_ID)
    --区分装备和物品
    local isEquip_A = goodsID_A > DEFINE.MAX_MEDICAMENT_ID and 1 or 0
    local isEquip_B = goodsID_B > DEFINE.MAX_MEDICAMENT_ID and 1 or 0
    if isEquip_A ~= isEquip_B then
        return isEquip_A > isEquip_B
    end

    --是合成所需材料--排序
    local isExpend_A = m.IsSynthesisExpend(goodsID_A) and 1 or 0
    local isExpend_B = m.IsSynthesisExpend(goodsID_B) and 1 or 0
    if isExpend_A ~= isExpend_B then
        return isExpend_A > isExpend_B
    end

    --装备推荐--排序
    local int_A = HelperL.IsRecommendedEquipment(a) and 1 or 0
    local int_B = HelperL.IsRecommendedEquipment(b) and 1 or 0
    if int_A ~= int_B then
        return int_A > int_B
    end

    local cfg_A = Schemes:GetGoodsConfig(goodsID_A)
    local cfg_B = Schemes:GetGoodsConfig(goodsID_B)
    local quality_A = isEquip_A and cfg_A.QualityLevel or cfg_A.Quality
    local quality_B = isEquip_B and cfg_B.QualityLevel or cfg_B.Quality
    --装备品质--排序
    if quality_A ~= quality_B then
        return quality_A > quality_B
    end

    --装备部位--排序
    if cfg_A.SubType ~= cfg_B.SubType then
        return cfg_A.SubType > cfg_B.SubType
    end

    --装备等级--排序
    if cfg_A.UseLevel ~= cfg_B.UseLevel then
        return cfg_A.UseLevel > cfg_B.UseLevel
    end

    --装备ID--排序
    return goodsID_A > goodsID_B
end

--------------------------------------------------------------------
--更新背包
--------------------------------------------------------------------
function m.UpdateKnapsack()
    local skepBag = SkepModule:GetSkepByID(SKEPID.SKEPID_PACKET)
    local equips = {}
    local goodsID, entity, cfg
    -- 背包里的数据
    for i = 0, skepBag.indexMaxsize do
        entity = EntityModule:GetEntity(skepBag[i])
        if entity then
            goodsID = entity:GetProperty(ENTITY_FIELD.ENTITY_FIELD_ID)
            cfg = Schemes.Equipment:Get(goodsID)
            if cfg then
                if cfg.MeltingGoods > 0 and cfg.QualityLevel <= QUALITY.QUALITY_ORANGE then
                    table.insert(equips, entity)
                end
            end
        end
    end

    table.sort(equips, m.EquipSort)
    m.entityList = equips

    local item, stateIcon
    --最少要显示100个格子
    local num = math.max(#m.knapsackEquipItem, #equips)
    for i = 1, num, 1 do
        if not m.knapsackEquipItem[i] then
            m.knapsackEquipItem[i] = _GAddSlotItem(m.objList.Grid_Knapsack)
            m:CollectChild(m.objList.Grid_Knapsack, m.knapsackEquipItem[i])
        end
        item = m.knapsackEquipItem[i]
        if equips[i] then
            item:SetEntity(equips[i])
            item:SetClick(m.OnClickEquip, equips[i].uid)
            item:ShowLevel(true)

            --默认没有状态
            stateIcon = nil
            if m.GetSelectFirstEquip() then
                --选中--合成所需材料
                if m.selectIndexTable[equips[i].uid] then
                    stateIcon = "com_gxtb"
                else
                    --未选中--不是合成所需材料，都锁住
                    if not m.IsSynthesisExpend(item.itemID) then
                        stateIcon = "suo_wks"
                    end
                end
            end
            --显示锁住状态--设置无法点击
            item:EnableClick(stateIcon ~= "suo_wks")
            item:SetStateIcon(stateIcon, true)
            item:SetRedDot(RedDotCheckFunc:Check_UIEquipSynthesis(item.itemID))
            item:SetActive(true)
        else
            item:SetActive(false)
        end
    end
end

--------------------------------------------------------------------
--装备点击事件
--------------------------------------------------------------------
function m.OnClickEquip(itemID, uid)
    if m.selectIndexTable[uid] == true then
        for i, v in ipairs(m.selectIndexList) do
            if v.uid == uid then
                table.remove(m.selectIndexList, i)
                break
            end
        end
        m.selectIndexTable[uid] = nil
    else
        --材料已足够，不能在选中
        if m.CanSynthesis() then
            return
        end
        table.insert(m.selectIndexList, {
            uid = uid,
            itemID = itemID
        })
        m.selectIndexTable[uid] = true
    end

    m.UpdateView()
end

--------------------------------------------------------------------
---获取第一个选中的装备
--------------------------------------------------------------------
function m.GetSelectFirstEquip()
    if #m.selectIndexList > 0 then
        return m.selectIndexList[1]
    end
    return nil
end

--------------------------------------------------------------------
--更新装备框
--------------------------------------------------------------------
function m.UpdateSlotItem()
    ----------------合成装备----------------
    local firstEquip = m.GetSelectFirstEquip()
    if firstEquip then
        local cfg = Schemes.Equipment:Get(firstEquip.itemID)
        m.SynthesisEquip_Item:SetItemID(cfg.MeltingGoods)

        m.SynthesisEquip_Item:SetActive(true)
    else
        m.SynthesisEquip_Item:SetActive(false)
    end


    ----------------合成消耗----------------
    local data
    for i, v in ipairs(m.SynthesisExpend_Item_List) do
        data = m.selectIndexList[i]
        if data then
            v:SetItemID(data.itemID)
            v:SetClick(m.OnClickEquip, data.uid)
            v:SetActive(true)
        else
            v:SetActive(false)
        end
    end
end

--------------------------------------------------------------------
--更新界面
--------------------------------------------------------------------
function m.UpdateView()
    m.UpdateSlotItem()
    m.UpdateKnapsack()
    m.objList.Btn_Synthesis.gameObject:SetActive(m.CanSynthesis())
    m.objList.Btn_SynthesisAll.gameObject:SetActive(false)
end

--------------------------------------------------------------------
---判断是否可以合成(合成材料已足够)
--------------------------------------------------------------------
function m.CanSynthesis()
    local firstEquip = m.GetSelectFirstEquip()
    if firstEquip then
        local cfg = Schemes.Equipment:Get(firstEquip.itemID)
        local strList = HelperL.Split(cfg.ModelID, "|")
        return #strList == #m.selectIndexList
    end
    return false
end

--------------------------------------------------------------------
--判断是所需材料
---@param goodsID integer 物品ID
---@return boolean
--------------------------------------------------------------------
function m.IsSynthesisExpend(goodsID)
    local firstEquip = m.GetSelectFirstEquip()
    if firstEquip then
        --已第一个选中为主，判断所需材料
        local cfg = Schemes.Equipment:Get(firstEquip.itemID)
        if string.find(cfg.ModelID, goodsID .. ";") then
            return true
        end
    end

    return false
end

--------------------------------------------------------------------
--清除选择
--------------------------------------------------------------------
function m.ClearSelection()
    m.selectIndexList = {}
    m.selectIndexTable = {}
end

--------------------------------------------------------------------
--分解请求回调
--------------------------------------------------------------------
function m.RequestCallback(result, content)
    if result == RESULT_CODE_BASE.RESULT_COMMON_SUCCEED[1] then
        m.ClearSelection()     
        HelperL.PlayVFX()
        if m.Request == 1 then
            m.SynthesisCallback(content)
        elseif m.Request == 2 then
            m.OneKeySynthesisCallback(content)
        end
    else
        ResultCode.ShowResultCodeCallback(result, content)
    end
end

--------------------------------------------------------------------
---合成装备
--------------------------------------------------------------------
function m.SynthesisEquip()
    local firstEquip = m.GetSelectFirstEquip()
    if not firstEquip then
        error('请选择要合成的装备！')
        return
    end

    local goodInfo = ""
    local costInfo = ""

    --合成装备
    local cfg = Schemes.Equipment:Get(firstEquip.itemID)
    if cfg and cfg.MeltingGoods > 0 then
        goodInfo = string.format("%s;%s", cfg.MeltingGoods, 1)
    end

    --合成消耗
    for _, v in ipairs(m.selectIndexList) do
        if costInfo == "" then
            costInfo = string.format("%s;%s", v.uid, 1)
        else
            costInfo = string.format("%s|%s;%s", costInfo, v.uid, 1)
        end
    end

    if goodInfo == "" or costInfo == "" then
        error('合成信息错误！！！')
        return
    end
    m.Request = 1
    HelperL.RequestDirectGiveGoodsy(goodInfo, costInfo, m.RequestCallback, false)
end

--------------------------------------------------------------------
---一键合成装备
--------------------------------------------------------------------
function m.OneKeySynthesisEquip()
    local goodInfoAll = ''
    local costInfoAll = ''
    local goodInfo, costInfo, cfg, item
    for i, entity in ipairs(m.entityList) do
        cfg = Schemes.Equipment:Get(entity:GetProperty(ENTITY_FIELD.ENTITY_FIELD_ID))
        item = m.Item_Quality_List[cfg.QualityLevel]
        if item and item.isSelect == true then
            goodInfo, costInfo = m.GetSynthesisExpend(cfg, costInfoAll)
            if goodInfo ~= "" and costInfo ~= "" then
                --合成装备
                if goodInfoAll == '' then
                    goodInfoAll = goodInfo
                else
                    goodInfoAll = string.format("%s|%s", goodInfoAll, goodInfo)
                end

                --合成消耗
                if costInfoAll == '' then
                    costInfoAll = costInfo
                else
                    costInfoAll = string.format("%s|%s", costInfoAll, costInfo)
                end
            end
        end
    end

    if goodInfoAll == "" or costInfoAll == "" then
        warn('没有可合成的装备！')
        HelperL.ShowMessage(TipType.FlowText, '没有可合成的装备！')
        return
    end
    m.Request = 2
    HelperL.RequestDirectGiveGoodsy(goodInfoAll, costInfoAll, m.RequestCallback, false)
end

--------------------------------------------------------------------
---获取合成消耗
---@param cfg any 装备ID
---@param elseExpend ?string 其他合成消耗(用于判断uid是否重复使用)
---@return string,string
--------------------------------------------------------------------
function m.GetSynthesisExpend(cfg, elseExpend)
    elseExpend = elseExpend or ""
    local strList = HelperL.Split(cfg.ModelID, "|")
    local uidList = {}
    local uidTable = {}
    local temp, id, num, equipID2, item
    for i, v in ipairs(strList) do
        temp = HelperL.Split(v, ";")
        id = tonumber(temp[1]) or 0
        num = tonumber(temp[2]) or 0
        if id > 0 and num > 0 then
            for _, entity in ipairs(m.entityList) do
                if not string.find(elseExpend, entity.uid .. ";") then
                    if not uidTable[entity.uid] then
                        equipID2 = entity:GetProperty(ENTITY_FIELD.ENTITY_FIELD_ID)
                        item = m.Item_Quality_List[Schemes.Equipment:Get(equipID2).QualityLevel]
                        if item and item.isSelect == true then
                            if id == equipID2 then
                                uidTable[entity.uid] = entity.uid
                                table.insert(uidList, entity.uid)
                                break
                            end
                        end
                    end
                end
            end
        end
    end

    local goodInfo = ''
    local costInfo = ''
    if #strList == #uidList then
        --合成装备
        goodInfo = string.format("%s;%s", cfg.MeltingGoods, 1)
        --合成消耗
        for i, uid in ipairs(uidList) do
            if costInfo == '' then
                costInfo = string.format("%s;%s", uid, 1)
            else
                costInfo = string.format("%s|%s;%s", costInfo, uid, 1)
            end
        end
    end
    return goodInfo, costInfo
end

--------------------------------------------------------------------
---选择品质
--------------------------------------------------------------------
function m.SelectQuality(index)
    local item = m.Item_Quality_List[index]
    item.isSelect = not item.isSelect
    item.com.Img_Select.gameObject:SetActive(item.isSelect)
end

--------------------------------------------------------------------
---创建品质框
--------------------------------------------------------------------
function m.Creation_Item_Quality(index)
    ---@class Item_Quality
    local item = {}
    item.index = index
    item.isSelect = false
    item.com = m:CreateSubItem(m.objList.Grid_Quality, m.objList.Item_Quality)
    m:AddClick(item.com.Btn_Select, function()
        m.SelectQuality(item.index)
    end)

    item.com.Txt_Quality.text = HelperL.GetNameByQuality(index)
    item.com.Txt_Quality.color = HelperL.GetQualityColorRGBA(index)
    item.com.Img_Select.gameObject:SetActive(item.isSelect)
    return item
end

--------------------------------------------------------------------
---关闭一键合成
--------------------------------------------------------------------
function m.ColseOneKeySynthesis()
    m.objList.Obj_OneKeySynthesis.gameObject:SetActive(false)
    for i, item in ipairs(m.Item_Quality_List) do
        item.isSelect = false
        item.com.Img_Select.gameObject:SetActive(item.isSelect)
    end
end

--------------------------------------------------------------------
---合成回调
---@param content string 合成装备内容
--------------------------------------------------------------------
function m.SynthesisCallback(content)
    local str = HelperL.Split(content, "|")[1]
    if str then
        local id = tonumber(HelperL.Split(str, ";")[1]) or 0
        if id > 0 then
            local cfg = Schemes.Equipment:Get(id)
            if cfg then
                if cfg.QualityLevel == 0 then
                    return
                end


                if not m.SynthesisHintEquip_Item then
                    m.SynthesisHintEquip_Item = _GAddSlotItem(m.objList.Obj_SynthesisHintEquip)
                end
                m.SynthesisHintEquip_Item:SetItemID(id)

                m.objList.Txt_EquipName.text = cfg.GoodsName
                m.objList.Txt_EquipName.color = HelperL.GetQualityColorRGBA(cfg.QualityLevel)
                m.objList.Txt_EquipQuality.text = HelperL.GetNameByQuality(cfg.QualityLevel)
                m.objList.Txt_EquipQuality.color = HelperL.GetQualityColorRGBA(cfg.QualityLevel)

                local equipSmeltStar = Schemes.EquipSmeltStar:GetEquipSmeltStarCfg(cfg.SmeltID, 0)
                if equipSmeltStar then
                    local attack1 = 0
                    local attack2 = 0
                    if cfg.QualityLevel < equipSmeltStar.AttackList.Length then
                        attack1 = equipSmeltStar.AttackList[cfg.QualityLevel - 1]
                        attack2 = equipSmeltStar.AttackList[cfg.QualityLevel]
                    end
                    local hp1 = 0
                    local hp2 = 0
                    if cfg.QualityLevel < equipSmeltStar.MaxHpList.Length then
                        hp1 = equipSmeltStar.MaxHpList[cfg.QualityLevel - 1]
                        hp2 = equipSmeltStar.MaxHpList[cfg.QualityLevel]
                    end

                    ---@type { Name: string, Value1: string, Value2: string }[]
                    local attributeDataList = {}
                    local data
                    if attack1 > 0 or attack2 > 0 then
                        data = { Name = GetAttributeTypeDesc(3), Value1 = attack1, Value2 = nil }
                        if attack2 > attack1 then
                            data.Value2 = string.format("<color=#1ACE34>%s</color>", attack2)
                        end
                        table.insert(attributeDataList, data)
                    end
                    if hp1 > 0 or hp2 > 0 then
                        data = { Name = GetAttributeTypeDesc(0), Value1 = hp1, Value2 = nil }
                        if hp2 > hp1 then
                            data.Value2 = string.format("<color=#1ACE34>%s</color>", hp2)
                        end
                        table.insert(attributeDataList, data)
                    end

                    local num = math.max(#attributeDataList, #m.Item_Attribute_List)
                    for i = 1, num, 1 do
                        if not m.Item_Attribute_List[i] then
                            m.Item_Attribute_List[i] = m.Create_Item_Attribute(i)
                        end
                        m.Item_Attribute_List[i].UpdateData(attributeDataList[i])
                    end
                end
            end
        end
    end

    m.objList.Obj_SynthesisHint.gameObject:SetActive(true)
end

--------------------------------------------------------------------
---一键合成回调
---@param content string 合成装备内容
--------------------------------------------------------------------
function m.OneKeySynthesisCallback(content)
    local prizeInfo = HelperL.Split(content, '|')
    local tempPrizes = {}
    local goodsInfo, id, goodsNum, cfg
    for i, v in ipairs(prizeInfo) do
        goodsInfo = HelperL.Split(v, ';')
        if goodsInfo[1] and goodsInfo[2] then
            id = tonumber(goodsInfo[1]) or 0
            goodsNum = (tonumber(goodsInfo[2]) or 0)
            cfg = Schemes:GetGoodsConfig(id)
            if cfg and goodsNum > 0 then
                table.insert(tempPrizes, { ID = id, Num = goodsNum })
            end
        end
    end
    local num = math.max(#m.goodItemList, #tempPrizes)
    for i = 1, num, 1 do
        if not m.goodItemList[i] then
            m.goodItemList[i] = _GAddSlotItem(m.objList.Grid_OneKeySynthesisHint)
        end
        local item = m.goodItemList[i]
        if tempPrizes[i] then
            item:SetItemID(tempPrizes[i].ID)
            item:SetCount(tempPrizes[i].Num)
            item:SetActive(true)
        else
            item:SetActive(false)
        end
    end

    m.objList.Obj_OneKeySynthesisHint.gameObject:SetActive(true)
end

--------------------------------------------------------------------
---创建属性框
--------------------------------------------------------------------
function m.Create_Item_Attribute(index)
    ---@class Item_Attribute3
    local item = {}
    item.index = index
    item.com = m:CreateSubItem(m.objList.Grid_Attribute, m.objList.Item_Attribute)

    --- 更新数据
    ---@param data {Name:string, Value1:string, Value2:string}
    item.UpdateData = function(data)
        if data then
            item.com.Txt_Name.text = data.Name
            item.com.Txt_Value1.text = data.Value1
            print('-------data.Value2-------', data.Value1, data.Value2)
            if data.Value2 and data.Value2 ~= "" then
                item.com.Txt_Value2.text = data.Value2
                item.com.Txt_Value2.gameObject:SetActive(true)
            else
                item.com.Txt_Value2.gameObject:SetActive(false)
            end
            item.com.gameObject:SetActive(true)
        else
            item.com.gameObject:SetActive(false)
        end
    end

    return item
end

return m
