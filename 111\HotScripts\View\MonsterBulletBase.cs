﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;

using Apq;
using Apq.Unity3D.Extension;
using Apq.Unity3D.UnityHelpers;

using Cysharp.Threading.Tasks;

using HotScripts;

using RxEventsM2V;

using Thing;

using UnityEngine;

using X.PB;

namespace View
{
    /// <summary>
    ///     怪物阵营的子弹
    /// </summary>
    public class MonsterBulletBase : BulletBase
    {
        /// <summary>
        ///     哪个怪物发出的子弹
        /// </summary>
        public MonsterThing MonsterThing => BulletThing?.CdExecutor?.Thing.Owner as MonsterThing;

        /// <inheritdoc />
        public override IList<HitThingCells> DoCollideEnemies(LineSegment line)
        {
            List<HitThingCells> rtn = new();

            // 子弹可攻击到的胶囊区域的 中线 线段 (起止点为两头圆的边缘)
            Vector3 pEnd = line.PosEnd + (line.DirNormal * BulletThing.TotalProp_Radius);
            LineSegment lineSegment = new LineSegment
            {
                PosStart = line.PosStart - (line.DirNormal * BulletThing.TotalProp_Radius)
            };
            lineSegment.Dir = pEnd - lineSegment.PosStart;

            // 玩家所在的点与线段lineSegment的距离
            float distanceP1P2 = Actor.Position.CalcDistance_PointToLineSegment(lineSegment);
            if (distanceP1P2 > Actor.GetTotalDouble(PropType.Radius)
                    .FirstOrDefault())
            {
                return rtn;
            }

            // 击中玩家了
            //Debug.Log("怪物的子弹击中玩家了!");
            rtn.Add(new HitThingCells { Thing = Actor });

            return rtn;
        }

        /// <inheritdoc />
        protected override void OnHitEnemy(LineSegment line, ThingBase thing, IList<HitThingCells> enemies)
        {
            enemies.ToList().ForEach(enemy =>
            {
                EffectMgr.Instance.ShowEffect(EffectPath.behit_NvFuZhu01,
                    enemy.Thing.Position, 2).Forget();
            });

            // 伤害
            //var maxRadius = DamageEnemy(gun, enemies);
            DamageEnemy(thing, enemies);
            //Debug.Log($"击中的敌人 最大半径:{maxRadius}");
        }

        /// <inheritdoc />
        public override float DamageEnemy(ThingBase thing, IList<HitThingCells> enemies,
            Action<MonsterThing> onDied = null)
        {
            // 受击生物的最大半径
            float maxRadius = 0;
            // 计算伤害量
            foreach (HitThingCells player in enemies)
            {
                maxRadius = (float)player.Thing.GetTotalDouble(PropType.Radius).FirstOrDefault();

                // 受击方先接受攻击方携带的Buff
                player.Thing.ReceiveBuffByBulletHit(thing, BuffRecvType.Hit);

                // 计算伤害量
                (double damage, bool isCriticalHit) = Helper.CalcDamage(BulletThing, 0, 0, player.Thing);

                // 接受伤害
                player.Thing.TakeHit(thing, damage, isCriticalHit);
            }

            return maxRadius;
        }

        /// <inheritdoc />
        protected override async UniTaskVoid DoExplose(CancellationToken token, ThingBase thing,
            Vector3 pos, byte exploseType = 1, int exploseNo = 0)
        {
            try
            {
                // 延时后爆炸:秒-爆炸延时
                float delay = 0f;
                if (exploseType == 2)
                {
                    // 连爆次数用完就不爆炸了
                    if (exploseNo > thing.GetTotalLong(PropType.KilledExploseTimes).FirstOrDefault())
                    {
                        return;
                    }

                    delay = 0.3f;
                    await UniTask.Delay(TimeSpan.FromSeconds(delay), cancellationToken: token);
                    if (token.IsCancellationRequested)
                    {
                        return;
                    }
                }

                // 爆炸概率
                double explosePriority = thing.GetTotalDouble(PropType.ExplosePriority).FirstOrDefault();

                //Debug.Log((exploseType == 1 ? "击中" : "击杀") + $"爆炸概率:{explosePriority}");

                // 按概率爆炸
                if (exploseNo > 0 // 总是连爆
                    // 首爆按概率
                    || RandomNum.RandomDouble(0, 1) < explosePriority)
                {
                    // 爆炸声音
                    string exploseSound = thing.GetTotalString(PropType.ExploseSound).FirstOrDefault();
                    AudioPlayer.Instance.PlaySound(exploseSound).Forget();
                    // 爆炸特效
                    string exploseEffect = thing.GetTotalString(PropType.ExploseEffect).FirstOrDefault();
                    // 爆炸半径
                    float exploseRadius = (float)thing.GetTotalDouble(PropType.ExploseRadius).FirstOrDefault();

                    Debug.Log("怪物的子弹爆炸" + (delay > 0 ? $"(已延时{delay}秒)" : string.Empty)
                                        + $",位置:{pos} 半径:{exploseRadius}");

                    if (!string.IsNullOrWhiteSpace(exploseEffect))
                    {
                        EffectMgr.Instance.ShowEffect(EffectMgr.Instance.GetEffectPath(exploseEffect), pos,
                            exploseRadius).Forget();
                    }

                    // 找出被炸到的敌人
                    if (Actor.CircularArea2D.IsIntersect(new CircularArea2D
                        {
                            Center = thing.Position, Radius = exploseRadius
                        }))
                    {
                        (double damage, bool isCritical) = Helper.CalcDamage(BulletThing, 0, 1, Actor);

                        // 受击方先接受枪携带的Buff
                        Actor.ReceiveBuffByBulletHit(BulletThing.CdExecutor.Thing,
                            BuffRecvType.Explose);

                        // 敌人接受伤害
                        Actor.TakeHit(BulletThing.CdExecutor.Thing, damage, isCritical);
                    }
                }
            }
            catch (OperationCanceledException)
            {
                throw;
            }
            catch (Exception ex)
            {
                Debug.LogException(ex);
            }
        }
    }
}