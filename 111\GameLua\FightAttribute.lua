--[[
********************************************************************
    created:    2024/04/08
    author :    李锦剑
    purpose:    战力界面
*********************************************************************
--]]

--战力界面
FightAttribute = {}

--创建战力界面
function FightAttribute.New()
    ---@class Fight_Item
    local m = {}
    --已注册事件
    m.isSubscribe = false
    m.gameObject = nil
    m.transform = nil
    m.isInit = false

    ---初始化--对象
    ---@param obj integer 对象
    m.Init = function(obj)
        m.gameObject = obj
        m.transform = obj.transform
        m.objList = {}
        Helper.FillLuaComps(m.transform, m.objList)
        m.objList.Btn_Attribute.onClick:RemoveAllListeners()
        m.objList.Btn_Attribute.onClick:AddListenerEx(function()
            local bool = not m.objList.Img_Prop.gameObject.activeSelf
            m.objList.Img_Prop.gameObject:SetActive(bool)
            if bool then
                m.UpdateFightAndAttribute(true)
            end
        end)
        m.isInit = true


        --注册事件
        m.Subscribe()
        --更新战力和属性
        m.UpdateFightAndAttribute()
    end

    ---初始化
    ---@param parent integer 父节点
    m.Init2 = function(parent)
        if tolua.isnull(parent) then
            warn('父节点为空 parent==nil')
            return
        end

        m.parent = parent
        ResMgr.LoadGameObjectAsync('ui/Common/Item_Fight', function(obj, m2)
            local obj2 = GameObject.Instantiate(obj, m2.parent.transform)
            m2.Init(obj2)
        end, m)
    end

    --更新战力和属性
    m.UpdateFightAndAttribute = function(isEffect)
        if not m.isInit then return end
        -- 客户端自己算属性
        local pow = ActorProp.GetPlayerFieldsByClient()
        if isEffect == true then
            local effect, effectValue
            for i, v in ipairs(ActorProp.RoleAttributes) do
                effect = ActorProp.PropNames[v]
                if effect.PctEnd then
                    effectValue = HelperL.Round(pow.Result[v] / 100, 2) .. '%'
                else
                    effectValue = HelperL.GetChangeNum(pow.Result[v])
                end
                m.objList['Txt_Desc' .. i].text = string.format("%s：<color=#00FF00>%s</color>", effect.Text_cn,
                    effectValue)
            end
        end

        m.objList.Txt_Fight1.text = HelperL.GetChangeNum(pow.FirePower)
        m.objList.Txt_Fight2.text = HelperL.GetChangeNum(pow.DefensePower)
    end

    --隐藏属性界面
    m.HideAttribute = function()
        m.objList.Img_Prop.gameObject:SetActive(false)
    end

    --注册事件
    m.Subscribe = function()
        if not m.isSubscribe then
            m.isSubscribe = true
            EventManager:Subscribe(EventID.LogicDataChange, m.UpdateFightAndAttribute)
            EventManager:Subscribe(EventID.OnHeroPropChange, m.UpdateFightAndAttribute)
            EventManager:Subscribe(EventID.OnSkepGoodsChange, m.UpdateFightAndAttribute)
            EventManager:Subscribe(EventID.WindowOpen, m.HideAttribute)
        end
    end

    --取消注册事件
    m.UnSubscribe = function()
        if m.isSubscribe then
            m.isSubscribe = false
            EventManager:UnSubscribe(EventID.LogicDataChange, m.UpdateFightAndAttribute)
            EventManager:UnSubscribe(EventID.OnHeroPropChange, m.UpdateFightAndAttribute)
            EventManager:UnSubscribe(EventID.OnSkepGoodsChange, m.UpdateFightAndAttribute)
            EventManager:UnSubscribe(EventID.WindowOpen, m.HideAttribute)
        end
    end

    return m
end
