-- Generated By protoc-gen-lua Do not Edit
local protobuf = require "protobuf/protobuf"
module('CenterMessage_pb')
local pb = {}


pb.MSG_CENTER_ACTIONID = protobuf.EnumDescriptor();
pb.MSG_CENTER_ACTIONID_MSG_CENTER_NONE_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_CENTER_ACTIONID_MSG_CENTER_REGISTER_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_CENTER_ACTIONID_MSG_CENTER_HEART_BEAT_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_CENTER_ACTIONID_MSG_CENTER_SCENE_DB_PAIR_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_CENTER_ACTIONID_MSG_CENTER_STOP_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_CENTER_ACTIONID_MSG_CENTER_NOTIFY_ZONESTATUS_ENUM = protobuf.EnumValueDescriptor();
pb.MSG_CENTER_BROADCAST = protobuf.Descriptor();
pb.MSG_CENTER_BROADCAST_MESSAGE_FIELD = protobuf.FieldDescriptor();
pb.MSG_CENTER_BROADCAST_ACTION_FIELD = protobuf.FieldDescriptor();
pb.MSG_CENTER_REGISTER = protobuf.Descriptor();
pb.MSG_CENTER_REGISTER_SERVERID_FIELD = protobuf.FieldDescriptor();
pb.MSG_CENTER_REGISTER_REPLY = protobuf.Descriptor();
pb.MSG_CENTER_REGISTER_REPLY_SERVERID_FIELD = protobuf.FieldDescriptor();
pb.MSG_CENTER_REGISTER_REPLY_RESULT_FIELD = protobuf.FieldDescriptor();
pb.MSG_CENTER_HEARTBEAT = protobuf.Descriptor();
pb.MSG_CENTER_HEARTBEAT_SERVERID_FIELD = protobuf.FieldDescriptor();
pb.MSG_CENTER_SCENEDBPAIR = protobuf.Descriptor();
pb.MSG_CENTER_SCENEDBPAIR_ZONEID_FIELD = protobuf.FieldDescriptor();
pb.MSG_CENTER_SCENEDBPAIR_DBINDEX_FIELD = protobuf.FieldDescriptor();
pb.MSG_CENTER_SCENEDBPAIR_REPLY = protobuf.Descriptor();
pb.MSG_CENTER_SCENEDBPAIR_REPLY_RESULT_FIELD = protobuf.FieldDescriptor();
pb.MSG_CENTER_NOTIFY_ZONESTATUS = protobuf.Descriptor();
pb.MSG_CENTER_NOTIFY_ZONESTATUS_ZONEID_FIELD = protobuf.FieldDescriptor();
pb.MSG_CENTER_NOTIFY_ZONESTATUS_STATUS_FIELD = protobuf.FieldDescriptor();

pb.MSG_CENTER_ACTIONID_MSG_CENTER_NONE_ENUM.name = "MSG_CENTER_NONE"
pb.MSG_CENTER_ACTIONID_MSG_CENTER_NONE_ENUM.index = 0
pb.MSG_CENTER_ACTIONID_MSG_CENTER_NONE_ENUM.number = 0
pb.MSG_CENTER_ACTIONID_MSG_CENTER_REGISTER_ENUM.name = "MSG_CENTER_REGISTER"
pb.MSG_CENTER_ACTIONID_MSG_CENTER_REGISTER_ENUM.index = 1
pb.MSG_CENTER_ACTIONID_MSG_CENTER_REGISTER_ENUM.number = 2
pb.MSG_CENTER_ACTIONID_MSG_CENTER_HEART_BEAT_ENUM.name = "MSG_CENTER_HEART_BEAT"
pb.MSG_CENTER_ACTIONID_MSG_CENTER_HEART_BEAT_ENUM.index = 2
pb.MSG_CENTER_ACTIONID_MSG_CENTER_HEART_BEAT_ENUM.number = 3
pb.MSG_CENTER_ACTIONID_MSG_CENTER_SCENE_DB_PAIR_ENUM.name = "MSG_CENTER_SCENE_DB_PAIR"
pb.MSG_CENTER_ACTIONID_MSG_CENTER_SCENE_DB_PAIR_ENUM.index = 3
pb.MSG_CENTER_ACTIONID_MSG_CENTER_SCENE_DB_PAIR_ENUM.number = 4
pb.MSG_CENTER_ACTIONID_MSG_CENTER_STOP_ENUM.name = "MSG_CENTER_STOP"
pb.MSG_CENTER_ACTIONID_MSG_CENTER_STOP_ENUM.index = 4
pb.MSG_CENTER_ACTIONID_MSG_CENTER_STOP_ENUM.number = 5
pb.MSG_CENTER_ACTIONID_MSG_CENTER_NOTIFY_ZONESTATUS_ENUM.name = "MSG_CENTER_NOTIFY_ZONESTATUS"
pb.MSG_CENTER_ACTIONID_MSG_CENTER_NOTIFY_ZONESTATUS_ENUM.index = 5
pb.MSG_CENTER_ACTIONID_MSG_CENTER_NOTIFY_ZONESTATUS_ENUM.number = 6
pb.MSG_CENTER_ACTIONID.name = "MSG_CENTER_ACTIONID"
pb.MSG_CENTER_ACTIONID.full_name = ".MSG_CENTER_ACTIONID"
pb.MSG_CENTER_ACTIONID.values = {pb.MSG_CENTER_ACTIONID_MSG_CENTER_NONE_ENUM,pb.MSG_CENTER_ACTIONID_MSG_CENTER_REGISTER_ENUM,pb.MSG_CENTER_ACTIONID_MSG_CENTER_HEART_BEAT_ENUM,pb.MSG_CENTER_ACTIONID_MSG_CENTER_SCENE_DB_PAIR_ENUM,pb.MSG_CENTER_ACTIONID_MSG_CENTER_STOP_ENUM,pb.MSG_CENTER_ACTIONID_MSG_CENTER_NOTIFY_ZONESTATUS_ENUM}
pb.MSG_CENTER_BROADCAST_MESSAGE_FIELD.name = "Message"
pb.MSG_CENTER_BROADCAST_MESSAGE_FIELD.full_name = ".MSG_CENTER_Broadcast.Message"
pb.MSG_CENTER_BROADCAST_MESSAGE_FIELD.number = 1
pb.MSG_CENTER_BROADCAST_MESSAGE_FIELD.index = 0
pb.MSG_CENTER_BROADCAST_MESSAGE_FIELD.label = 1
pb.MSG_CENTER_BROADCAST_MESSAGE_FIELD.has_default_value = false
pb.MSG_CENTER_BROADCAST_MESSAGE_FIELD.default_value = ""
pb.MSG_CENTER_BROADCAST_MESSAGE_FIELD.type = 12
pb.MSG_CENTER_BROADCAST_MESSAGE_FIELD.cpp_type = 9

pb.MSG_CENTER_BROADCAST_ACTION_FIELD.name = "Action"
pb.MSG_CENTER_BROADCAST_ACTION_FIELD.full_name = ".MSG_CENTER_Broadcast.Action"
pb.MSG_CENTER_BROADCAST_ACTION_FIELD.number = 2
pb.MSG_CENTER_BROADCAST_ACTION_FIELD.index = 1
pb.MSG_CENTER_BROADCAST_ACTION_FIELD.label = 1
pb.MSG_CENTER_BROADCAST_ACTION_FIELD.has_default_value = false
pb.MSG_CENTER_BROADCAST_ACTION_FIELD.default_value = 0
pb.MSG_CENTER_BROADCAST_ACTION_FIELD.type = 13
pb.MSG_CENTER_BROADCAST_ACTION_FIELD.cpp_type = 3

pb.MSG_CENTER_BROADCAST.name = "MSG_CENTER_Broadcast"
pb.MSG_CENTER_BROADCAST.full_name = ".MSG_CENTER_Broadcast"
pb.MSG_CENTER_BROADCAST.nested_types = {}
pb.MSG_CENTER_BROADCAST.enum_types = {}
pb.MSG_CENTER_BROADCAST.fields = {pb.MSG_CENTER_BROADCAST_MESSAGE_FIELD, pb.MSG_CENTER_BROADCAST_ACTION_FIELD}
pb.MSG_CENTER_BROADCAST.is_extendable = false
pb.MSG_CENTER_BROADCAST.extensions = {}
pb.MSG_CENTER_REGISTER_SERVERID_FIELD.name = "ServerID"
pb.MSG_CENTER_REGISTER_SERVERID_FIELD.full_name = ".MSG_CENTER_Register.ServerID"
pb.MSG_CENTER_REGISTER_SERVERID_FIELD.number = 1
pb.MSG_CENTER_REGISTER_SERVERID_FIELD.index = 0
pb.MSG_CENTER_REGISTER_SERVERID_FIELD.label = 2
pb.MSG_CENTER_REGISTER_SERVERID_FIELD.has_default_value = false
pb.MSG_CENTER_REGISTER_SERVERID_FIELD.default_value = 0
pb.MSG_CENTER_REGISTER_SERVERID_FIELD.type = 13
pb.MSG_CENTER_REGISTER_SERVERID_FIELD.cpp_type = 3

pb.MSG_CENTER_REGISTER.name = "MSG_CENTER_Register"
pb.MSG_CENTER_REGISTER.full_name = ".MSG_CENTER_Register"
pb.MSG_CENTER_REGISTER.nested_types = {}
pb.MSG_CENTER_REGISTER.enum_types = {}
pb.MSG_CENTER_REGISTER.fields = {pb.MSG_CENTER_REGISTER_SERVERID_FIELD}
pb.MSG_CENTER_REGISTER.is_extendable = false
pb.MSG_CENTER_REGISTER.extensions = {}
pb.MSG_CENTER_REGISTER_REPLY_SERVERID_FIELD.name = "ServerID"
pb.MSG_CENTER_REGISTER_REPLY_SERVERID_FIELD.full_name = ".MSG_CENTER_Register_Reply.ServerID"
pb.MSG_CENTER_REGISTER_REPLY_SERVERID_FIELD.number = 1
pb.MSG_CENTER_REGISTER_REPLY_SERVERID_FIELD.index = 0
pb.MSG_CENTER_REGISTER_REPLY_SERVERID_FIELD.label = 2
pb.MSG_CENTER_REGISTER_REPLY_SERVERID_FIELD.has_default_value = false
pb.MSG_CENTER_REGISTER_REPLY_SERVERID_FIELD.default_value = 0
pb.MSG_CENTER_REGISTER_REPLY_SERVERID_FIELD.type = 13
pb.MSG_CENTER_REGISTER_REPLY_SERVERID_FIELD.cpp_type = 3

pb.MSG_CENTER_REGISTER_REPLY_RESULT_FIELD.name = "Result"
pb.MSG_CENTER_REGISTER_REPLY_RESULT_FIELD.full_name = ".MSG_CENTER_Register_Reply.Result"
pb.MSG_CENTER_REGISTER_REPLY_RESULT_FIELD.number = 2
pb.MSG_CENTER_REGISTER_REPLY_RESULT_FIELD.index = 1
pb.MSG_CENTER_REGISTER_REPLY_RESULT_FIELD.label = 1
pb.MSG_CENTER_REGISTER_REPLY_RESULT_FIELD.has_default_value = false
pb.MSG_CENTER_REGISTER_REPLY_RESULT_FIELD.default_value = 0
pb.MSG_CENTER_REGISTER_REPLY_RESULT_FIELD.type = 5
pb.MSG_CENTER_REGISTER_REPLY_RESULT_FIELD.cpp_type = 1

pb.MSG_CENTER_REGISTER_REPLY.name = "MSG_CENTER_Register_Reply"
pb.MSG_CENTER_REGISTER_REPLY.full_name = ".MSG_CENTER_Register_Reply"
pb.MSG_CENTER_REGISTER_REPLY.nested_types = {}
pb.MSG_CENTER_REGISTER_REPLY.enum_types = {}
pb.MSG_CENTER_REGISTER_REPLY.fields = {pb.MSG_CENTER_REGISTER_REPLY_SERVERID_FIELD, pb.MSG_CENTER_REGISTER_REPLY_RESULT_FIELD}
pb.MSG_CENTER_REGISTER_REPLY.is_extendable = false
pb.MSG_CENTER_REGISTER_REPLY.extensions = {}
pb.MSG_CENTER_HEARTBEAT_SERVERID_FIELD.name = "ServerID"
pb.MSG_CENTER_HEARTBEAT_SERVERID_FIELD.full_name = ".MSG_CENTER_HeartBeat.ServerID"
pb.MSG_CENTER_HEARTBEAT_SERVERID_FIELD.number = 1
pb.MSG_CENTER_HEARTBEAT_SERVERID_FIELD.index = 0
pb.MSG_CENTER_HEARTBEAT_SERVERID_FIELD.label = 2
pb.MSG_CENTER_HEARTBEAT_SERVERID_FIELD.has_default_value = false
pb.MSG_CENTER_HEARTBEAT_SERVERID_FIELD.default_value = 0
pb.MSG_CENTER_HEARTBEAT_SERVERID_FIELD.type = 13
pb.MSG_CENTER_HEARTBEAT_SERVERID_FIELD.cpp_type = 3

pb.MSG_CENTER_HEARTBEAT.name = "MSG_CENTER_HeartBeat"
pb.MSG_CENTER_HEARTBEAT.full_name = ".MSG_CENTER_HeartBeat"
pb.MSG_CENTER_HEARTBEAT.nested_types = {}
pb.MSG_CENTER_HEARTBEAT.enum_types = {}
pb.MSG_CENTER_HEARTBEAT.fields = {pb.MSG_CENTER_HEARTBEAT_SERVERID_FIELD}
pb.MSG_CENTER_HEARTBEAT.is_extendable = false
pb.MSG_CENTER_HEARTBEAT.extensions = {}
pb.MSG_CENTER_SCENEDBPAIR_ZONEID_FIELD.name = "ZoneID"
pb.MSG_CENTER_SCENEDBPAIR_ZONEID_FIELD.full_name = ".MSG_CENTER_SceneDBPair.ZoneID"
pb.MSG_CENTER_SCENEDBPAIR_ZONEID_FIELD.number = 1
pb.MSG_CENTER_SCENEDBPAIR_ZONEID_FIELD.index = 0
pb.MSG_CENTER_SCENEDBPAIR_ZONEID_FIELD.label = 2
pb.MSG_CENTER_SCENEDBPAIR_ZONEID_FIELD.has_default_value = false
pb.MSG_CENTER_SCENEDBPAIR_ZONEID_FIELD.default_value = 0
pb.MSG_CENTER_SCENEDBPAIR_ZONEID_FIELD.type = 13
pb.MSG_CENTER_SCENEDBPAIR_ZONEID_FIELD.cpp_type = 3

pb.MSG_CENTER_SCENEDBPAIR_DBINDEX_FIELD.name = "DBIndex"
pb.MSG_CENTER_SCENEDBPAIR_DBINDEX_FIELD.full_name = ".MSG_CENTER_SceneDBPair.DBIndex"
pb.MSG_CENTER_SCENEDBPAIR_DBINDEX_FIELD.number = 2
pb.MSG_CENTER_SCENEDBPAIR_DBINDEX_FIELD.index = 1
pb.MSG_CENTER_SCENEDBPAIR_DBINDEX_FIELD.label = 2
pb.MSG_CENTER_SCENEDBPAIR_DBINDEX_FIELD.has_default_value = false
pb.MSG_CENTER_SCENEDBPAIR_DBINDEX_FIELD.default_value = 0
pb.MSG_CENTER_SCENEDBPAIR_DBINDEX_FIELD.type = 13
pb.MSG_CENTER_SCENEDBPAIR_DBINDEX_FIELD.cpp_type = 3

pb.MSG_CENTER_SCENEDBPAIR.name = "MSG_CENTER_SceneDBPair"
pb.MSG_CENTER_SCENEDBPAIR.full_name = ".MSG_CENTER_SceneDBPair"
pb.MSG_CENTER_SCENEDBPAIR.nested_types = {}
pb.MSG_CENTER_SCENEDBPAIR.enum_types = {}
pb.MSG_CENTER_SCENEDBPAIR.fields = {pb.MSG_CENTER_SCENEDBPAIR_ZONEID_FIELD, pb.MSG_CENTER_SCENEDBPAIR_DBINDEX_FIELD}
pb.MSG_CENTER_SCENEDBPAIR.is_extendable = false
pb.MSG_CENTER_SCENEDBPAIR.extensions = {}
pb.MSG_CENTER_SCENEDBPAIR_REPLY_RESULT_FIELD.name = "Result"
pb.MSG_CENTER_SCENEDBPAIR_REPLY_RESULT_FIELD.full_name = ".MSG_CENTER_SceneDBPair_Reply.Result"
pb.MSG_CENTER_SCENEDBPAIR_REPLY_RESULT_FIELD.number = 1
pb.MSG_CENTER_SCENEDBPAIR_REPLY_RESULT_FIELD.index = 0
pb.MSG_CENTER_SCENEDBPAIR_REPLY_RESULT_FIELD.label = 2
pb.MSG_CENTER_SCENEDBPAIR_REPLY_RESULT_FIELD.has_default_value = false
pb.MSG_CENTER_SCENEDBPAIR_REPLY_RESULT_FIELD.default_value = 0
pb.MSG_CENTER_SCENEDBPAIR_REPLY_RESULT_FIELD.type = 13
pb.MSG_CENTER_SCENEDBPAIR_REPLY_RESULT_FIELD.cpp_type = 3

pb.MSG_CENTER_SCENEDBPAIR_REPLY.name = "MSG_CENTER_SceneDBPair_Reply"
pb.MSG_CENTER_SCENEDBPAIR_REPLY.full_name = ".MSG_CENTER_SceneDBPair_Reply"
pb.MSG_CENTER_SCENEDBPAIR_REPLY.nested_types = {}
pb.MSG_CENTER_SCENEDBPAIR_REPLY.enum_types = {}
pb.MSG_CENTER_SCENEDBPAIR_REPLY.fields = {pb.MSG_CENTER_SCENEDBPAIR_REPLY_RESULT_FIELD}
pb.MSG_CENTER_SCENEDBPAIR_REPLY.is_extendable = false
pb.MSG_CENTER_SCENEDBPAIR_REPLY.extensions = {}
pb.MSG_CENTER_NOTIFY_ZONESTATUS_ZONEID_FIELD.name = "ZoneID"
pb.MSG_CENTER_NOTIFY_ZONESTATUS_ZONEID_FIELD.full_name = ".MSG_CENTER_Notify_ZoneStatus.ZoneID"
pb.MSG_CENTER_NOTIFY_ZONESTATUS_ZONEID_FIELD.number = 1
pb.MSG_CENTER_NOTIFY_ZONESTATUS_ZONEID_FIELD.index = 0
pb.MSG_CENTER_NOTIFY_ZONESTATUS_ZONEID_FIELD.label = 2
pb.MSG_CENTER_NOTIFY_ZONESTATUS_ZONEID_FIELD.has_default_value = false
pb.MSG_CENTER_NOTIFY_ZONESTATUS_ZONEID_FIELD.default_value = 0
pb.MSG_CENTER_NOTIFY_ZONESTATUS_ZONEID_FIELD.type = 13
pb.MSG_CENTER_NOTIFY_ZONESTATUS_ZONEID_FIELD.cpp_type = 3

pb.MSG_CENTER_NOTIFY_ZONESTATUS_STATUS_FIELD.name = "Status"
pb.MSG_CENTER_NOTIFY_ZONESTATUS_STATUS_FIELD.full_name = ".MSG_CENTER_Notify_ZoneStatus.Status"
pb.MSG_CENTER_NOTIFY_ZONESTATUS_STATUS_FIELD.number = 2
pb.MSG_CENTER_NOTIFY_ZONESTATUS_STATUS_FIELD.index = 1
pb.MSG_CENTER_NOTIFY_ZONESTATUS_STATUS_FIELD.label = 2
pb.MSG_CENTER_NOTIFY_ZONESTATUS_STATUS_FIELD.has_default_value = false
pb.MSG_CENTER_NOTIFY_ZONESTATUS_STATUS_FIELD.default_value = 0
pb.MSG_CENTER_NOTIFY_ZONESTATUS_STATUS_FIELD.type = 13
pb.MSG_CENTER_NOTIFY_ZONESTATUS_STATUS_FIELD.cpp_type = 3

pb.MSG_CENTER_NOTIFY_ZONESTATUS.name = "MSG_CENTER_Notify_ZoneStatus"
pb.MSG_CENTER_NOTIFY_ZONESTATUS.full_name = ".MSG_CENTER_Notify_ZoneStatus"
pb.MSG_CENTER_NOTIFY_ZONESTATUS.nested_types = {}
pb.MSG_CENTER_NOTIFY_ZONESTATUS.enum_types = {}
pb.MSG_CENTER_NOTIFY_ZONESTATUS.fields = {pb.MSG_CENTER_NOTIFY_ZONESTATUS_ZONEID_FIELD, pb.MSG_CENTER_NOTIFY_ZONESTATUS_STATUS_FIELD}
pb.MSG_CENTER_NOTIFY_ZONESTATUS.is_extendable = false
pb.MSG_CENTER_NOTIFY_ZONESTATUS.extensions = {}

MSG_CENTER_Broadcast = protobuf.Message(pb.MSG_CENTER_BROADCAST)
MSG_CENTER_HEART_BEAT = 3
MSG_CENTER_HeartBeat = protobuf.Message(pb.MSG_CENTER_HEARTBEAT)
MSG_CENTER_NONE = 0
MSG_CENTER_NOTIFY_ZONESTATUS = 6
MSG_CENTER_Notify_ZoneStatus = protobuf.Message(pb.MSG_CENTER_NOTIFY_ZONESTATUS)
MSG_CENTER_REGISTER = 2
MSG_CENTER_Register = protobuf.Message(pb.MSG_CENTER_REGISTER)
MSG_CENTER_Register_Reply = protobuf.Message(pb.MSG_CENTER_REGISTER_REPLY)
MSG_CENTER_SCENE_DB_PAIR = 4
MSG_CENTER_STOP = 5
MSG_CENTER_SceneDBPair = protobuf.Message(pb.MSG_CENTER_SCENEDBPAIR)
MSG_CENTER_SceneDBPair_Reply = protobuf.Message(pb.MSG_CENTER_SCENEDBPAIR_REPLY)

