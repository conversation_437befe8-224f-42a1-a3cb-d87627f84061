---@class NotchPhoneModel
local m = {}

--手机型号
local PhoneModel = {
    ["小米"] = {
        "小米8",
        "小米8 SE",
        "小米8 青春版",
        "小米9",
        "小米9 SE",
        "小米CIVI",
        "小米CIVI 1S",
        "小米MIX 3",
        "小米MIX 2S",
        "小米6X",
        "小米Note 3",
        "小米CC9",
        "小米CC9 Pro",
        "小米8探索版",
        "小米10",
        "小米10 Pro",
        "小米10 Lite",
    },
    --小米
    ["Redmi"] = {
        "Redmi Note 7 Pro",
        "Redmi Note 8",
        "Redmi Note 8 Pro",
        "Redmi Note 9",
        "Redmi Note 9 Pro",
        "Redmi Note 10",
        "Redmi Note 10 Pro",
        "Redmi Note 11 Pro",
        "Redmi K20",
        "Redmi K20 Pro",
        "Redmi K30",
        "Redmi K30i",
        "Redmi K30 Pro",
        "Redmi K30S至尊纪念版",
        "Redmi K40",
        "Redmi K40 Pro",
        "Redmi K40游戏增强版",
    },
    ["华为"] = {
        "华为P20",
        "华为P20 Pro",
        "华为Mate 20",
        "华为Mate 20 Pro",
        "华为Mate 20 X",
        "华为Mate 20 RS",
        "华为nova 3",
        "华为nova 3i",
        "华为P30",
        "华为P30 Pro",
        "华为nova 4",
        "华为nova 5",
        "华为nova 5 Pro",
        "华为nova 5i",
        "华为Y9s",
        "华为麦芒8",
        "华为畅享10 Plus",
        "华为nova 6",
        "华为nova 6 5G",
        "华为Mate 30",
        "华为Mate 30 Pro",
        "华为Mate 30 RS",
        "华为P40",
        "华为P40 Pro",
        "华为P40 Pro+",
        "华为nova 7",
        "华为nova 7 Pro",
        "华为nova 7 SE",
        "华为Mate 40",
        "华为Mate 40 Pro",
    },
    ["vivo"] = {
        "vivo X21",
        "vivo X21i",
        "vivo X23",
        "vivo X27",
        "vivo Nex",
        "vivo Nex S",
        "vivo Nex A",
        "vivo Y91",
        "vivo Y93",
        "vivo Z3",
        "vivo X21s",
        "vivo X23幻彩版",
        "vivo X25",
        "vivo X27 Pro",
        "vivo X30",
        "vivo X30 Pro",
        "vivo S1",
        "vivo S1 Pro",
        "vivo S5",
        "vivo S6",
        "vivo S7",
        "vivo S9",
        "vivo Y93s",
        "vivo Y95",
        "vivo Y97",
        "vivo Z1",
        "vivo Z1i",
        "vivo Z3i",
        "vivo Z5",
        "vivo Z5x",
        "vivo Z5i",
        "vivo Y3s",
        "vivo Y5s",
        "vivo Y7s",
        "vivo U1",
        "vivo U3"
    },
    --苹果
    ["iPhone"] = {
        "iPhone SE (2nd generation)",
        "iPhone X",
        "iPhone XS",
        "iPhone XS Max",
        "iPhone XR",
        "iPhone 11",
        "iPhone 11 Pro",
        "iPhone 11 Pro Max",
        "iPhone 12",
        "iPhone 12 Pro",
        "iPhone 12 Pro Max",
        "iPhone X",
        "iPhone 12 mini",
        "iPhone 13",
        "iPhone 13 Pro",
        "iPhone 13 Pro Max",
        "iPhone 13 mini",
        "iPhone SE (3rd generation)",
        "iPhone 14",
        "iPhone 14 Pro",
        "iPhone 14 Pro Max",
        "iPhone 14 Plus",
    },
    ["OPPO"] = {
        "OPPO A7",
        "OPPO R17",
        "OPPO Find X",
        "OPPO Reno 2",
        "OPPO Reno 2 Z",
        "OPPO A5",
        "OPPO R17 Pro",
        "OPPO R15",
        "OPPO R15 Dream Mirror Edition",
        "OPPO K1",
        "OPPO A7x",
        "OPPO A9",
        "OPPO A9 2020",
        "OPPO A9x",
        "OPPO A9 2020 Pro",
        "OPPO F9",
        "OPPO F9 Pro",
        "OPPO F11",
        "OPPO F11 Pro",
        "OPPO Reno",
        "OPPO Reno Z",
        "OPPO Reno 10x Zoom",
        "OPPO Reno2",
        "OPPO Reno2 Z",
        "OPPO Reno3",
        "OPPO Reno3 Pro",
        "OPPO Reno3 Z",
        "OPPO A5s",
        "OPPO A5 2020",
        "OPPO A8",
        "OPPO A12",
        "OPPO A15",
        "OPPO K3",
        "OPPO K5",
        "OPPO K7",
        "OPPO A91",
        "OPPO A92",
        "OPPO A92s",
        "OPPO A93",
        "OPPO A93s",
        "OPPO A94",
        "OPPO A95",
        "OPPO Reno4",
        "OPPO Reno4 Pro",
        "OPPO Reno4 SE",
        "OPPO Reno5",
        "OPPO Reno5 Pro",
        "OPPO Reno5 Pro+",
        "OPPO Reno5 Z",
        "OPPO Reno6",
        "OPPO Reno6 Pro",
        "OPPO Reno6 Pro+",
        "OPPO Reno6 Z",
    },
    ["魅族"] = {
        "魅族16",
        "魅族15",
        "魅族Zero",
        "魅族16 X",
        "魅族16th",
        "魅族16th Plus",
        "魅族Note 9",
        "魅族X8",
        "魅族17",
        "魅族17 Pro",
        "魅族18",
        "魅族18 Pro",
        "魅族20",
        "魅族20 Pro",
        "魅族16s",
        "魅族16s Pro",
        "魅族18s",
        "魅族18s Pro",
        "魅族20s",
        "魅族20s Pro",
        "魅族Note 10",
        "魅族Note 11",
        "魅族21",
        "魅族21 Pro",
        "魅族21 Ultra",
    },
    --三星
    ["Samsung"] = {
        "Samsung Galaxy A8",
        "Samsung Galaxy A8+",
        "Samsung Galaxy Note9",
        "Samsung Galaxy Note10",
        "Samsung Galaxy Note10+",
        "Samsung Galaxy S9",
        "Samsung Galaxy S9+",
        "Samsung Galaxy Note 8",
        "Samsung Galaxy A8 (2018)",
        "Samsung Galaxy A8+ (2018)",
        "Samsung Galaxy J8",
        "Samsung Galaxy S10",
        "Samsung Galaxy S10+",
        "Samsung Galaxy S10e",
        "Samsung Galaxy Note 9",
        "Samsung Galaxy A9 (2018)",
        "Samsung Galaxy A9 Star",
        "Samsung Galaxy A9 Pro (2018)",
        "Samsung Galaxy A7 (2018)",
        "Samsung Galaxy J7 Pro",
        "Samsung Galaxy J7 Max",
        "Samsung Galaxy A6 (2018)",
        "Samsung Galaxy A8 (2019)",
        "Samsung Galaxy A8+ (2019)",
        "Samsung Galaxy A70",
        "Samsung Galaxy M30",
        "Samsung Galaxy M20",
        "Samsung Galaxy Note 10",
        "Samsung Galaxy Note 10+",
        "Samsung Galaxy S20",
        "Samsung Galaxy S20+",
        "Samsung Galaxy S20 Ultra",
        "Samsung Galaxy Note 20",
        "Samsung Galaxy Note 20 Ultra",
        "Samsung Galaxy A51",
        "Samsung Galaxy A71",
        "Samsung Galaxy E4",
        "Samsung Galaxy E4 Plus",
        "Samsung Galaxy F41",
        "Samsung Galaxy F62",
        "Samsung Galaxy M51",
        "Samsung Galaxy A21",
        "Samsung Galaxy A31",
        "Samsung Galaxy A41",
        "Samsung Galaxy A52",
        "Samsung Galaxy A52 5G",
        "Samsung Galaxy A72",
    },
    --一加
    ["OnePlus"] = {
        "OnePlus 6",
        "OnePlus 6T",
        "OnePlus 7",
        "OnePlus 7 Pro",
        "OnePlus 7T",
        "OnePlus 7T Pro",
        "OnePlus 8",
        "OnePlus 8 Pro",
        "OnePlus 9",
        "OnePlus 9 Pro",
    },
    --努比亚
    ["nubia"] = {
        "nubia Z11 Max",
        "nubia Z17",
        "nubia Z17 Mini",
        "nubia Z17S",
        "nubia Z18",
        "nubia X",
        "nubia Red Magic 3",
        "nubia Red Magic 3S",
        "nubia Red Magic 5G",
        "nubia Play",
        "nubia Alpha",
        "nubia Red Magic 5",
        "nubia Red Magic 5S",
        "nubia Red Magic 6",
        "nubia Red Magic 6 Pro",
        "nubia Red Magic 6R",
        "nubia Red Magic 7",
        "nubia Red Magic 7 Pro",
        "nubia Red Magic 7S",
        "nubia Red Magic 7S Pro",
        "nubia Z30",
        "nubia Z30 Pro",
        "nubia Z40",
        "nubia Z40 Pro",
        "nubia Z40 Pro 5G",
        "nubia 5G",
        "nubia 5S",
        "nubia 6",
        "nubia 6Z",
        "nubia 7",
        "nubia 7 Mini",
    }
}

--获取手机型号
function m.GetPhoneDeviceModel()
    return GameLuaAPI.GetDeviceModel()
end

--获取手机品牌
function m.GetPhoneBrand()
    local deviceModel = m.GetPhoneDeviceModel()
    for brand, modelsList in pairs(PhoneModel) do
        if string.find(deviceModel, brand) ~= nil then
            return brand
        end
    end
    return ""
end

--是否是刘海屏
function m.IsNotchDetection()
    --手机型号
    local deviceModel = m.GetPhoneDeviceModel()
    --手机品牌
    local brand = m.GetPhoneBrand()
    --获取对应品牌刘海屏机型列表
    local modelsList = PhoneModel[brand]
    if modelsList then
        for i, v in ipairs(modelsList) do
            if v == deviceModel then
                return true
            end
        end
    end
    if brand ~= '华为' then
        return (UnityEngine.Screen.height / UnityEngine.Screen.width) > 2
    end
    return false
end

return m
