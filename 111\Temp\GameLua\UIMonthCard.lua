--[[
********************************************************************
    created:    2023/08/26
    author :    李锦剑
    purpose:    月卡界面
*********************************************************************
--]]

local luaID = ('UIMonthCard')
---@class UIMonthCard:UIWndBase
local m = {}

--------------------------------------------------------------------
-- 订阅事件列表
--------------------------------------------------------------------
function m.GetO<PERSON>List()
	return {
		[EventID.OnHeroPropChange] = m.UpdateView,
		[EventID.LogicDataChange] = m.UpdateView,
	}
end

--------------------------------------------------------------------
-- 初始化
--------------------------------------------------------------------
function m.OnCreate()
	m.PrivilegeItemList = {}
	local item1 = m.CreatePrivilege(1,m.objList.ItemPrivilege1)
	local item2 = m.CreatePrivilege(2,m.objList.ItemPrivilege2)
	m.PrivilegeItemList[1] = item1
	m.PrivilegeItemList[2] = item2
	item1.UpdateData(Schemes.PrivilegeCard:Get(1))
	item2.UpdateData(Schemes.PrivilegeCard:Get(2))
	m.objList.Btn_Close.onClick:AddListenerEx(function()
		m:CloseSelf()
	end)
	
    m.comResInfo = ComResInfo.New()
    m.comResInfo.BindView(m.objList.Com_Res)
	return true
end

--------------------------------------------------------------------
-- 开启
--------------------------------------------------------------------
function m.OnOpen()
	m.UpdateView()
	-- for k, v in pairs(m.PrivilegeItemList) do
	-- 	v.AddRedDot()
	-- end
end

--------------------------------------------------------------------
-- 创建月卡item
--------------------------------------------------------------------
function m.CreatePrivilege(index,view)
	local item = {}
	item.index = index
	item.descList = {}
	item.com = {}
	Helper.FillLuaComps(view.transform, item.com)
	item.com.Btn_Buy.onClick:AddListenerEx(function()
		m.OnClickBuy(item.cfg)
	end)
	item.com.Btn_Grey.onClick:AddListenerEx(function()
		m.OnClickBuy(item.cfg)
	end)

	--更新月卡属性描述
	item.UpdateDesc = function(cfg)
		local desc = HelperL.Split(cfg.Desc, ";")
		local num = math.max(#desc, #item.descList)
		for ii = 1, num, 1 do
			if not item.descList[ii] then
				item.descList[ii] = m:CreateSubItem(item.com.Grid_Desc, m.objList.Item_Desc)
			end
			if desc[ii] then
				item.descList[ii].Txt_Desc.text = desc[ii]
				item.descList[ii].gameObject:SetActive(true)
			else
				item.descList[ii].gameObject:SetActive(false)
			end
		end
	end

	--更新奖励
	item.UpdatePrize = function(cfg)
		local rechargeCard = Schemes.RechargeCard:Get(cfg.Renew)
		--创建购买奖励
		local goodList = Schemes.PrizeTable:GetGoodsList(rechargeCard.PrizeID)
		if not item.goodsItem1 then
			item.goodsItem1 = _GAddSlotItem(item.com.Goods1)
		end
		if goodList and #goodList > 0 then
			item.goodsItem1:SetItemID(goodList[1].id)
			item.goodsItem1:SetCount(goodList[1].num)
			item.goodsItem1:SetActive(true)
		else
			item.goodsItem1:SetActive(false)
		end
		--创建每日奖励
		goodList = Schemes.PrizeTable:GetGoodsList(cfg.PerDayAward)
		if not item.goodsItem2 then
			item.goodsItem2 = _GAddSlotItem(item.com.Goods2)
		end
		if goodList and #goodList > 0 then
			item.goodsItem2:SetItemID(goodList[1].id)
			item.goodsItem2:SetCount(goodList[1].num)
			item.goodsItem2:SetActive(true)
		else
			item.goodsItem2:SetActive(false)
		end
	end

	--添加红点
	item.AddRedDot = function()
		m:SetWndRedDot(item.com.Btn_Buy):AddCheckParam(WndID.MonthCard, 2, 2)
	end

	--更新数据
	item.UpdateData = function(cfg)
		item.cfg = cfg
		if cfg then
			item.UpdateDesc(cfg)
			item.UpdatePrize(cfg)
			item.com.Txt_Title.text = cfg.Name
			--AtlasManager:AsyncGetSprite('yk_bg' .. cfg.ID, item.com.Img_Bg)
			AtlasManager:AsyncGetSprite('yk_' .. (3 + cfg.ID), item.com.Img_type)

			local rechargeCard = Schemes.RechargeCard:Get(cfg.Renew)
			local isBuyCard = HelperL.IsBuyPrivilegeCard(cfg.ID)
			print('-------isBuyCard----------',cfg.ID, isBuyCard)
			item.rechargeCard = rechargeCard
			item.com.Btn_Buy.gameObject:SetActive(false)
			item.com.Btn_Grey.gameObject:SetActive(false)
			item.com.RedDot.gameObject:SetActive(false)
			if isBuyCard then
				local lv = HeroDataManager:GetLogicBit(LOGIC_DATA.DATA_VIP_GET_PERDAY_GIFT, 16 + cfg.ID)
				if lv == 1 then
					item.com.Btn_Grey.gameObject:SetActive(true)
					item.com.Txt_Grey.text = CommonTextID.IS_GET
				else
					item.com.Txt_Buy.text = CommonTextID.GET
					item.com.Btn_Buy.gameObject:SetActive(true)
					item.com.RedDot.gameObject:SetActive(true)
				end
			else
				item.com.Btn_Buy.gameObject:SetActive(true)
				item.com.Txt_Buy.text = string.format(GetGameText(luaID, 1), rechargeCard.FirstRMB / 100)
			end
			-- item.com.Img_Activate.gameObject:SetActive(not isBuyCard)
		end
		--item.com.gameObject:SetActive(cfg ~= nil)
	end
	return item
end

--------------------------------------------------------------------
-- 刷新界面
--------------------------------------------------------------------
function m.UpdateView()
	for k, v in pairs(m.PrivilegeItemList) do
		v.UpdateData(v.cfg)
	end
end

--------------------------------------------------------------------
-- 点击月卡购买按钮
--------------------------------------------------------------------
function m.OnClickBuy(cfg)
	if HelperL.IsBuyPrivilegeCard(cfg.ID) then
		m.OnClickGet(cfg)
	else
		HelperL.Recharge(cfg.Renew)
	end
end

--------------------------------------------------------------------
-- 点击月卡领取奖励按钮
--------------------------------------------------------------------
function m.OnClickGet(cfg)
	local index = 16 + cfg.ID
	local getDailyGift = HeroDataManager:GetLogicBit(LOGIC_DATA.DATA_VIP_GET_PERDAY_GIFT, index)
	if getDailyGift == 1 then
		HelperL.ShowMessage(TipType.FlowText, RESULT_CODE_BASE.RESULT_RECHARGECARD_HAVEGOT[2])
		return
	end
	LuaModule.RunLuaRequest(
		string.format('LuaRequestGetPrivilegeCardPrize?cardID=%d', cfg.ID),
		m.OnClickBuyBack
	)
	if cfg.ID == 1 then
		LogicValue.SetIntByIndex(9103,0,100)
	else
		LogicValue.SetIntByIndex(9104,0,100)
	end
end

--------------------------------------------------------------------
-- 请求领取月卡奖励返回
--------------------------------------------------------------------
function m.OnClickBuyBack(resultCode, content)
	if resultCode == RESULT_CODE_BASE.RESULT_COMMON_SUCCEED[0] then
		m.UpdateView()
	else
		HelperL.ShowMessage(TipType.FlowText, ResultCode:GetResultCodeStr(resultCode))
	end
end

return m
