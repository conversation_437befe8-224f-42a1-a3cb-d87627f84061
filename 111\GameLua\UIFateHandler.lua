---
--- Generated by <PERSON><PERSON><PERSON>(https://github.com/<PERSON><PERSON>ua)
--- Created by Administrator.
--- DateTime: 2023/7/25 20:08
--- 菌落界面的操作处理（广播监听、点击事件等）

local UIHandlerBase = require("Base_UI_Handler")
local Event = require("UIFateEnumDefine").Event
local ECacheField = require("UIFateEnumDefine").CacheField
local ECostType = require("UIFateEnumDefine").CostType
local EStatus = require("UIFateEnumDefine").Status
local TimerCache = require("TimerCache")
local Config = require("UIFateConfig")

---@class UIFateHandler:UIHandlerBase
---@field private _base UIHandlerBase
---@field private view UIFate
local this = class(UIHandlerBase)

function this:_init(view, cache)
    self:Init(view, cache)
end

---@private
---@param view UIFate
---@param cache UIFateCache
function this:Init(view, cache)
    self:baseInit(view)
    self.cache = cache
    local timer = self:GetTimerCache():GetOneTimer()
end

---@private
function this:GetTimerCache()
    if self.timerCache == nil then
        ---@see TimerCache
        self.timerCache = TimerCache()
    end

    return self.timerCache
end

function this:AddListener()
    --local temp = function(a, b) self:Hunt(a, b) end
    --self:Subscribe(Event.UI_FATE_HUNT, temp)
    self:Subscribe(EventID.OnSkepGoodsChange, function(skepId, m)
        if skepId == SKEPID.SKEPID_GEMPACK then
            self:OnFateTempStorageChanged()
        elseif skepId == SKEPID.SKEPID_WINGEQUIP or skepId == SKEPID.SKEPID_WINGEQUIP2 or
            skepId == SKEPID.SKEPID_WINGEQUIP3 or skepId == SKEPID.SKEPID_WINGEQUIP4 then
            self:OnFateEquipChanged()
        end
    end)
    self:Subscribe(EventID.MAIN_TITLE_HIDE_SUB_VIEW, function(winId)
        if winId == WndID.Fate then
            self:OnDisable()
        end
    end)
end

--- 菌落装备更改时
---@private
function this:OnFateEquipChanged()
    if self:CheckViewExist() then
        self.view:OnFateEquipChanged()
    end
end

--- 菌落临时仓库更改时
---@private
function this:OnFateTempStorageChanged()
    if self:CheckViewExist() then
        self.view:OnFateTempStorageChanged()
    end
end

--- 检查是否可以狩猎
---@param huntingId number @狩猎对象Id
---@return boolean @是否可狩猎
function this:CheckCanHunt(huntingId, costType)
    if true then
        return true
    end
    local hero = EntityModule.hero
    if hero == nil then return false end
    -- 根据类型，区分判断条件、不足提示内容、拥有量、消耗量等
    --local own = 0                                             -- 拥有
    local cost = 0                                            -- 消耗
    --local msg_Insufficient = ""
    local huntingTargetCfg = Schemes.GemCreate:Get(huntingId) -- 狩猎对象的配置信息
    if costType == ECostType.PRESTIGE then                    -- 声望
        -- 如果有剩余的免费次数，直接跳出
        local freeHuntUsed = EntityModule.hero.logicLC:GetGemDrawUseTimes()
        local freeHuntTotal = EntityModule.hero.logicLC:GetGemDrawFreeTimes()
        if freeHuntUsed < freeHuntTotal then
            return true
        end
        --msg_Insufficient = self.view:GetGameText(5) -- 声望不足，请获取声望
        cost = huntingTargetCfg.CostSilver
        --own = hero:GetProperty(PLAYER_FIELD.PLAYER_FIELD_PRESTIGE)
    end
    -- 开服七日挑战活动
    local deadline_FirstSevenDay = HelperL.GetSevenTargetTime(7) -- 开服七日的最终期限
    local curDay_FirstSevenDay = 7 - math.floor(deadline_FirstSevenDay / (60 * 60 * 24))
    if curDay_FirstSevenDay == 6 then                            -- 开服的第六天，消耗减半
        cost = cost * 0.5
    end

    -- -- 资产不足
    -- if own < cost then
    --     HelperL.ShowMessage(TipType.FlowText, msg_Insufficient)
    --     --UI.AddMessageWnd(msg_Insufficient)
    --     return false
    -- end

    --判断声望
    if HelperL.IsLackGoods(7, cost) then
        return false
    end

    return true
end

--- 自动狩猎
---@private
function this:AutoHunt()
    -- 设置【自动狩猎中】
    self.cache:SetField(ECacheField.IS_AUTO_HUNTING, true)
    if self.AutoHuntTimer then
        self.AutoHuntTimer:Stop()
        self.AutoHuntTimer = nil
    end
    if not self.AutoHuntTimer then
        --自动狩猎定时器
        self.AutoHuntTimer = Timer.New(function()
            local targetHuntingId = self:SearchSuitableHuntingId()
            local costType = self.cache:GetField(ECacheField.CUR_COST_TYPE)
            self:Hunt(targetHuntingId, costType)
        end, 0.5, -1)
        self.AutoHuntTimer:Start()
    end
end

-- 取消【自动狩猎中】
function this:CantNotHuntCallback()
    if self.AutoHuntTimer then
        self.AutoHuntTimer:Stop()
        self.AutoHuntTimer = nil
    end
    if self.cache then
        self.cache:SetField(ECacheField.IS_AUTO_HUNTING, false)
    end
    -- 界面刷新
    if self:CheckViewExist() then
        self.view:HuntFailCallback()
    end
end

--- 探测（单次）逻辑
---@param huntingId number @狩猎对象Id
---@param costType number @消耗类型
function this:Hunt(huntingId, costType)
    local isAuto = self.cache:GetField(ECacheField.IS_AUTO_HUNTING)
    local autoNum = HeroDataManager:GetLogicData(LOGIC_DATA.DATA_AUTO_GEMCOUNT)
    -- 不能狩猎了
    if not self:CheckCanHunt(huntingId, costType) or (isAuto and autoNum <= 0) then
        self:CantNotHuntCallback()
        return
    end
    -- 菌落背包满了
    local skep_Fate = SkepModule:GetSkepByID(SKEPID.SKEPID_GEMPACK)
    if skep_Fate:EmptyCount() <= 0 then
        if isAuto then
            -- 自动领取
            ---@type Fate_QuickCollect_ExtraData
            local extraData = {}
            extraData.isAuto = true
            extraData.extraCallback = function()
                -- 再次狩猎
                local curHuntingId = self.cache:GetField(ECacheField.CUR_HUNTING_ID)
                local curCostType = self.cache:GetField(ECacheField.CUR_COST_TYPE)
                self:Hunt(curHuntingId, curCostType)
            end
            self:QuickCollect(extraData)
        else
            self:CantNotHuntCallback()
            HelperL.ShowMessage(TipType.FlowText, self.view:GetGameText(8))
            return
        end
    end
    -- 发送请求
    local request = string.format('LuaRequestGemDrawOne?createID=%s&costType=%s&auto=%s',
        huntingId, costType, isAuto and 1 or 0
    )
    LuaModule.RunLuaRequest(request, function(result, content)
        self:HuntCallback(result, content)
    end)
end

--- 探测回调
---@param resultCode number @对应RESULT_CODE中的id
---@param content string @返回值
function this:HuntCallback(resultCode, content)
    -- 狩猎失败
    if resultCode ~= RESULT_CODE_BASE.RESULT_COMMON_SUCCEED[1] then
        HelperL.ShowMessage(TipType.FlowText, ResultCode:GetResultCodeStr(resultCode))
        if self.cache:GetField(ECacheField.IS_AUTO_HUNTING) then
            self:CantNotHuntCallback()
        end
        return
    end
    -- 狩猎成功
    self:HuntSuccess(resultCode, content)
end

---@type Timer
local autoHuntTimer
--- 狩猎成功
---@private
---@param resultCode number @对应RESULT_CODE中的id
---@param content string @返回值
function this:HuntSuccess(resultCode, content)
    ---@type Fate_HuntCallback_Proto
    local p = StringL.StringToParam(content)
    local successTip = ""               --
    local goodsArr = StringL.StringToPrizes(p.prize)
    for _, goods in ipairs(goodsArr) do -- 遍历获得物
        -- 获得的数量有误
        local goodsCfg = Schemes:GetGoodsConfig(goods.id)
        assert(goodsCfg ~= nil, StringL.ReplaceContent("goodsId=={0}的配置缺失", goods.id))
        assert(goods.num > 0, "狩猎时获得的菌落数异常")
        -- 有新的目标
        local newHuntingId = tonumber(p.createID)
        if newHuntingId ~= nil then
            print('---------newHuntingId',newHuntingId)
            self.cache:SetField(ECacheField.CUR_HUNTING_ID, newHuntingId)
            local huntingTargetCfg = Schemes.GemCreate:Get(newHuntingId)
            -- '获得{0}并遇到{1}'
            successTip = StringL.ReplaceContent(self.view:GetGameText(6),
                StringL.SetColoredText(goodsCfg.GoodsName, HelperL.GetColorByQuality(goodsCfg.QualityLevel)),
                StringL.SetColoredText(huntingTargetCfg.Desc, "FFFF00"))
        else
            -- 没遇到就从头开始
            self.cache:SetField(ECacheField.CUR_HUNTING_ID, 1)
            -- 档次满足
            if goodsCfg.Grade >= 0 then
                -- '获得{0}'
                successTip = StringL.ReplaceContent(self.view:GetGameText(7),
                    StringL.SetColoredText(goodsCfg.GoodsName, HelperL.GetColorByQuality(goodsCfg.QualityLevel)))
            end
        end
        -- 若有提示
        if StringL.IsEmptyOrWhiteSpace(successTip) == false then
            self.view:ShowGetTip(successTip)
        end
    end
    -- 界面刷新
    if self:CheckViewExist() then
        self.view:HuntSuccessCallback()
    end
end

--- 一键拾取
---@param extraData Fate_QuickCollect_ExtraData @额外数据
function this:QuickCollect(extraData)
    extraData = extraData or {}
    local isAuto = extraData.isAuto or false
    -- 自动狩猎情况下，不允许手动拾取
    if isAuto == false and self.cache:GetField(ECacheField.IS_AUTO_HUNTING) then
        return
    end
    local hero = EntityModule.hero
    -- 菌落临时篮子（狩猎到且未拾取的、临时的）
    local skep = SkepModule:GetSkepByID(SKEPID.SKEPID_GEMPACK)
    local entityList = {}
    for i = 0, skep.indexMaxsize do
        local entityId = skep[i] -- 菌落临时篮子中，对应实力的Id
        local entity = EntityModule:GetEntity(entityId)
        table.insert(entityList, entity)
    end
    -- 准备请求所需参数
    local isFirst = true
    local entityFormat = "{0}x{1}x{2}"
    local getGoods = ""
    for i = 1, #entityList do
        local entity = entityList[i]
        local entityUID = entity.uid
        local goodsId = entity:GetProperty(ENTITY_FIELD.ENTITY_FIELD_ID)
        local goodsNum = entity:GetProperty(GOODS_FIELD.GOODS_FIELD_QTY)
        local goodsStr = StringL.ReplaceContent(entityFormat, entityUID, goodsId, goodsNum)
        if isFirst then
            getGoods = goodsStr
            isFirst = false
        else
            getGoods = getGoods .. "+" .. goodsStr
        end
    end
    local genre = hero:GetProperty(PLAYER_FIELD.PLAYER_FIELD_GENRE)
    -- 发送请求
    local request = StringL.ReplaceContent("LuaRequestGemGet?getGoods={0}&genre={1}", getGoods, genre)
    LuaModule.RunLuaRequest(request, function(result, content)
        self:QuickCollectCallback(result, content, extraData)
    end)
end

---@param extraData Fate_QuickCollect_ExtraData
function this:QuickCollectCallback(result, content, extraData)
    extraData = extraData or {}
    local extraCallback = extraData.extraCallback
    -- 界面存在，响应界面回调
    if self:CheckViewExist() == true then
        self.view:QuickCollectCallback()
    end
    -- 如果有额外回调
    if extraCallback ~= nil then
        extraCallback()
    end
end

--- 检查界面是否存在
---@private
function this:CheckViewExist()
    if self.view == nil or IsNullGo(self.view.gameObject) then
        return false
    end
    return true
end

--- 寻找最适合的狩猎ID
function this:SearchSuitableHuntingId()
    local tarHuntingId = 1
    local hero = EntityModule.hero
    -- 当前金额
    local money = 0
    local curCostType = self.cache:GetField(ECacheField.CUR_COST_TYPE)
    -- 狩猎对象
    local curHuntingId = self.cache:GetField(ECacheField.CUR_HUNTING_ID)
    local cfg = Schemes.GemCreate:Get(curHuntingId)
    local cost = 0
    if curCostType == ECostType.PRESTIGE then
        money = hero:GetProperty(PLAYER_FIELD.PLAYER_FIELD_PRESTIGE)
        cost = cfg.CostSilver
    end
    -- 如果金额足够，返回该huntingId
    if money >= cost then
        tarHuntingId = curHuntingId
    end
    return tarHuntingId
end

--- 观看广告（获取免费狩猎次数）
function this:WatchAds()

end

--- 购买（自动狩猎）免广告
function this:PurchaseNoAds()

end

--- 购买无限制自动狩猎
function this:PurchaseUnlimitedAutoHunt()

end

--- 隐藏
function this:OnDisable()
    this:CantNotHuntCallback()
    if self.timerCache ~= nil then
        self.timerCache:Clear()
    end
end

--- 销毁
function this:OnDestroy()
    self:baseDestroy()
end

return this

---@class Fate_HuntCallback_Proto
---@field createID number
---@field prize CommonPrize

---@class Fate_QuickCollect_ExtraData
---@field extraCallback fun():void @额外添加的回调
---@field isAuto boolean @是否为自动拾取
