-- 测试UI
local luaID = ('UITestMode')

local UITestMode = {}

-- 初始化
function UITestMode:OnCreate()
	-- 此窗口不对外，所以文本不放进GameText了，以免无谓地增加翻译人员工作量
	self.objList.Txt_Confirm.text = '确认'
	self.objList.Btn_Confirm.onClick:AddListenerEx(self.OnClickConfirm)
	self.objList.Txt_Close.text = '关闭'
	self.objList.Btn_Close.onClick:AddListenerEx(self.OnClickClose)
	
	self.objList.Txt_CenterTitle.text = '快捷按钮'
	local itemContainer = self.objList.CenterContainer.transform
	local itemPrefab = self.objList.SingleBtn
	itemPrefab:SetActive(true)
	local itemNameList = { '给奖励', '加物品', '模拟充值', '初级测试好', '高级测试号', '中级测试好', '删细胞', '加邮件', '主线和挑战全通', '获得传奇装备', "直接给奖励",
		"引导测试" }
	local itemContentList = { '给奖励', '加物品', '模拟充值', '初级测试好', '高级测试号', '中级测试好', 'sjs', '发奖励邮件' }
	for i, v in ipairs(itemNameList) do
		local newItem = self:CreateSubItem(itemContainer, itemPrefab)
		newItem.Txt_Item.text = v
		if i == 4 then
			newItem.Btn_Item.onClick:AddListenerEx(function()
				self:SendBasicRequests()
			end)
		elseif i == 6 then
			newItem.Btn_Item.onClick:AddListenerEx(function()
				self:SendIntermediateRequests()
			end)
		elseif i == 5 then
			newItem.Btn_Item.onClick:AddListenerEx(function()
				self:SendMultipleRequests()
			end)
		elseif i == 9 then
			newItem.Btn_Item.onClick:AddListenerEx(function() self:OnClickAllUnlock() end)
		elseif i == 10 then
			newItem.Btn_Item.onClick:AddListenerEx(function() self:OnClickAllEquipment() end)
		elseif i == 11 then
			newItem.Btn_Item.onClick:AddListenerEx(function() HelperL.GetDirectGoods({ { ID = 4, Num = 1 } }) end)
		elseif i == 12 then
			newItem.Btn_Item.onClick:AddListenerEx(function() self:OnClickGuide() end)
		else
			local content = itemContentList[i]
			newItem.Btn_Item.onClick:AddListenerEx(function() self:OnClickShortcutBtn(content) end)
		end
	end
	itemPrefab:SetActive(false)

	return true
end

-- 点击确认按钮
function UITestMode.OnClickConfirm()
	local self = UITestMode
	local txt = self.objList.Inp_Msg.text
	if string.len(txt) <= 1 then
		HelperL.ShowMessage(TipType.FlowText, '内容不能为空')
		return
	end

	self.objList.Inp_Msg.text = ''
	local subStr = string.sub(txt, 1, 4)
	if subStr == '#lua' then
		local param = string.sub(txt, 6)
		local func = loadstring(param)
		func()
		return
	elseif subStr == '#log' then
		Helper.OpenTestLog()
		print('开启日志成功')
		return
	elseif subStr == '#sjs' then
		LoginModule:DeleteActor()
		return
	elseif subStr == '#jgq' then
		local param = string.sub(txt, 6)
		local stage = tonumber(param)
		UIManager:OpenWnd(WndID.TowerBattleChooseDeck, 1, stage)
		return
	end

	local strList = HelperL.Split(txt, ' ')
	if strList[1] == "#引导测试" then
		local guideType = tonumber(strList[2]) or 0
		GuideManager:OnGuideStart(guideType)
		UITestMode.OnClickClose()
		return
	end

	local msg = ChatMessage_pb.CS_Char_SendChat()
	msg.Channel = 12
	msg.Content = txt
	msg.ChatType = 0
	Premier.Instance:GetNetwork():SendFromLua(ENDPOINT.ENDPOINT_GAMECLIENT,
		ENDPOINT.ENDPOINT_GAMESERVER,
		MSG_MODULEID.MSG_MODULEID_CHAT,
		ChatMessage_pb.MSG_CHAT_SENDCHAT,
		msg:SerializeToString())
	
	HelperL.ShowMessage(TipType.FlowText, '发送完毕')
end

-- 点击快捷按钮
function UITestMode:OnClickShortcutBtn(txt)
	self.objList.Inp_Msg.text = '#' .. txt .. ' '
end

function UITestMode:OnClickGuide()
	self.objList.Inp_Msg.text = '#引导测试 '
end

-- 主线和挑战全通
function UITestMode:OnClickAllUnlock()
	LuaModule.RunLuaRequest("LuaRequestUnLockALLstage",function(resultCode, content)
		if resultCode ~= RESULT_CODE.RESULT_COMMON_SUCCEED then
			self.goToNextMissionStr = ResultCode:GetResultCodeStr(resultCode, content)
			HelperL.ShowMessage(TipType.FlowText, ResultCode:GetResultCodeStr(resultCode, content))
		else
			HelperL.ShowMessage(TipType.FlowText, "解锁成功，重启游戏")					
		end
	end)
end

-- 获得装备
function UITestMode:OnClickAllEquipment()
	-- LuaModule.RunLuaRequest("LuaRequestGetAllEquipment",function(resultCode, content)
	-- 	if resultCode ~= RESULT_CODE.RESULT_COMMON_SUCCEED then
	-- 		self.goToNextMissionStr = ResultCode:GetResultCodeStr(resultCode, content)
	-- 		HelperL.ShowMessage(TipType.FlowText, ResultCode:GetResultCodeStr(resultCode, content))
	-- 	else
	-- 		HelperL.ShowMessage(TipType.FlowText, "获得成功")					
	-- 	end
	-- end)
	local index = math.random(1, #Schemes.Equipment.items)
	local text = '#加物品 ' .. Schemes.Equipment.items[index].ID .. ' 1'
	local msg = ChatMessage_pb.CS_Char_SendChat()
	msg.Channel = 12
	msg.Content = text
	msg.ChatType = 0
	Premier.Instance:GetNetwork():SendFromLua(
		ENDPOINT.ENDPOINT_GAMECLIENT,
		ENDPOINT.ENDPOINT_GAMESERVER,
		MSG_MODULEID.MSG_MODULEID_CHAT,
		ChatMessage_pb.MSG_CHAT_SENDCHAT,
		msg:SerializeToString()
	)
end

-- 点击关闭按钮
function UITestMode.OnClickClose()
	local self = UITestMode
	self:CloseSelf()
end

function UITestMode:SendMultipleRequests()
    local requests = {
        "#设等级 63",
		"#给奖励 50003",
        "#逻辑值 5 352",
        "#逻辑值 25 352",--	[1] = 25,--LOGIC_DATA.DATA_NEWBIE_TARGETPRIZE_HAVEGET4,
        "#逻辑值 496 110115",--	[6] = 496,--LOGIC_DATA.DATA_FIRSTCHARGE_DATA,
        "#逻辑值 499 150115",--	[9] = 499,--LOGIC_DATA.DATA_RECHARGECARD_GIFTGOT,
        "#逻辑值 497 120115",--	[7] = 497,--LOGIC_DATA.DATA_RECHARGECARD_VALIDTIME,
        "#逻辑值 498 130115",--	[8] = 498,--LOGIC_DATA.DATA_RECHARGECARD_CARDID,
        "#逻辑值 501 140115",
    }

--副本存储通关逻辑值--这个有效

--	[5] = 495,--LOGIC_DATA.DATA_FIRSTCHARGE_TIME,




--	[10] = 500,--LOGIC_DATA.DATA_RECHARGECARD_LIMITTIME,
--	[11] = 501,--LOGIC_DATA.DATA_NEWBIE_TARGETPRIZE_HAVEGET5,
--	[12] = 502,--LOGIC_DATA.DATA_RECHARGECARD_LIMITTIME,
--	[13] = 503,--LOGIC_DATA.DATA_NEWBIE_TARGETPRIZE_HAVEGET5,
--	[14] = 504,--LOGIC_DATA.DATA_NEWBIE_TARGETPRIZE_HAVEGET5,
                     ----逻辑值还有502到505能用

    for _, request in ipairs(requests) do
        local msg = ChatMessage_pb.CS_Char_SendChat()
        msg.Channel = 12
        msg.Content = request
        msg.ChatType = 0
        Premier.Instance:GetNetwork():SendFromLua(
            ENDPOINT.ENDPOINT_GAMECLIENT,
            ENDPOINT.ENDPOINT_GAMESERVER,
            MSG_MODULEID.MSG_MODULEID_CHAT,
            ChatMessage_pb.MSG_CHAT_SENDCHAT,
            msg:SerializeToString()
        )
    end
end

function UITestMode:SendIntermediateRequests()
    local requests = {
        "#设等级 37",
		"#给奖励 50002",
        "#逻辑值 5 48",
        "#逻辑值 25 48",
        "#逻辑值 496 110015",--	[6] = 496,--LOGIC_DATA.DATA_FIRSTCHARGE_DATA,
        "#逻辑值 499 150015",--	[9] = 499,--LOGIC_DATA.DATA_RECHARGECARD_GIFTGOT,
        "#逻辑值 497 120015",--	[7] = 497,--LOGIC_DATA.DATA_RECHARGECARD_VALIDTIME,
        "#逻辑值 498 130015",--	[8] = 498,--LOGIC_DATA.DATA_RECHARGECARD_CARDID,
        "#逻辑值 501 140015",
    }

--副本存储通关逻辑值--这个有效
--	[1] = 25,--LOGIC_DATA.DATA_NEWBIE_TARGETPRIZE_HAVEGET4,
--	[5] = 495,--LOGIC_DATA.DATA_FIRSTCHARGE_TIME,
--	[6] = 496,--LOGIC_DATA.DATA_FIRSTCHARGE_DATA,
--	[7] = 497,--LOGIC_DATA.DATA_RECHARGECARD_VALIDTIME,
--	[8] = 498,--LOGIC_DATA.DATA_RECHARGECARD_CARDID,
--	[9] = 499,--LOGIC_DATA.DATA_RECHARGECARD_GIFTGOT,
--	[10] = 500,--LOGIC_DATA.DATA_RECHARGECARD_LIMITTIME,
--	[11] = 501,--LOGIC_DATA.DATA_NEWBIE_TARGETPRIZE_HAVEGET5,
--	[12] = 502,--LOGIC_DATA.DATA_RECHARGECARD_LIMITTIME,
--	[13] = 503,--LOGIC_DATA.DATA_NEWBIE_TARGETPRIZE_HAVEGET5,
--	[14] = 504,--LOGIC_DATA.DATA_NEWBIE_TARGETPRIZE_HAVEGET5,
                     ----逻辑值还有502到505能用
    for _, request in ipairs(requests) do
        local msg = ChatMessage_pb.CS_Char_SendChat()
        msg.Channel = 12
        msg.Content = request
        msg.ChatType = 0
        Premier.Instance:GetNetwork():SendFromLua(
            ENDPOINT.ENDPOINT_GAMECLIENT,
            ENDPOINT.ENDPOINT_GAMESERVER,
            MSG_MODULEID.MSG_MODULEID_CHAT,
            ChatMessage_pb.MSG_CHAT_SENDCHAT,
            msg:SerializeToString()
        )
    end
end

function UITestMode:SendBasicRequests()
    local requests = {
        "#给奖励 50001",
        "#逻辑值 5 5",
        "#逻辑值 25 5",
        "#设等级 6"
    }
    for _, request in ipairs(requests) do
        local msg = ChatMessage_pb.CS_Char_SendChat()
        msg.Channel = 12
        msg.Content = request
        msg.ChatType = 0
        Premier.Instance:GetNetwork():SendFromLua(
            ENDPOINT.ENDPOINT_GAMECLIENT,
            ENDPOINT.ENDPOINT_GAMESERVER,
            MSG_MODULEID.MSG_MODULEID_CHAT,
            ChatMessage_pb.MSG_CHAT_SENDCHAT,
            msg:SerializeToString()
        )
    end
end

return UITestMode