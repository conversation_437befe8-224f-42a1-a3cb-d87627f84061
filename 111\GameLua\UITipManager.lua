-- UI管理中心
local luaID = ('UITipManager')

local UITipManager = { }

-- 初始化
function UITipManager:OnCreate()
	self.objList.Dir_MsgBoxType1.gameObject:SetActive(false)
	self.objList.Dir_MsgBoxType2.gameObject:SetActive(false)
	self.objList.Dir_MsgBoxType3.gameObject:SetActive(false)
	self.objList.Dir_MsgBoxType4.gameObject:SetActive(false)
	local tipData = {}
	
	local flowTextData = {}
	flowTextData.obj = self.objList.Dir_FlowText
	flowTextData.context = self.objList.FlowText.Txt_FlowContent
	flowTextData.txtobj = flowTextData.context.gameObject
	flowTextData.txtobj:SetActive(true)
	flowTextData.bgobj = self.objList.FlowText.Img_Bg.gameObject
	flowTextData.canvasGroup = flowTextData.obj:GetComponent('CanvasGroup')
	tipData[TipType.FlowText] = flowTextData
	
	local moveTextData = {}
	moveTextData.obj = self.objList.Dir_MoveText
	moveTextData.itemPrefab = self.objList.MoveText.MoveTextSingleItem
	moveTextData.itemContainer = self.objList.MoveText.MoveTextContainer.transform
	moveTextData.itemList = {}
	tipData[TipType.MoveText] = moveTextData
	
	local msgBoxType1Data = {}
	msgBoxType1Data.obj = self.objList.Dir_MsgBoxType1
	local msgBoxType1Dir = self.objList.MsgBoxType1
	msgBoxType1Data.descText = msgBoxType1Dir.Txt_Desc
	msgBoxType1Data.btnList = {}
	table.insert(msgBoxType1Data.btnList, {msgBoxType1Dir.Btn_Btn1.gameObject, msgBoxType1Dir.Txt_Btn1, msgBoxType1Dir.Btn_Btn1, msgBoxType1Dir.Btn_Btn1:GetComponent('Image')})
	table.insert(msgBoxType1Data.btnList, {msgBoxType1Dir.Btn_Btn2.gameObject, msgBoxType1Dir.Txt_Btn2, msgBoxType1Dir.Btn_Btn2, msgBoxType1Dir.Btn_Btn2:GetComponent('Image')})
	table.insert(msgBoxType1Data.btnList, {msgBoxType1Dir.Btn_Btn3.gameObject, msgBoxType1Dir.Txt_Btn3, msgBoxType1Dir.Btn_Btn3, msgBoxType1Dir.Btn_Btn3:GetComponent('Image')})
	--msgBoxType1Dir.Btn_Mask.onClick:AddListenerEx(self.OnClickMsgBoxType1Mask)
	msgBoxType1Dir.Btn_Btn1.onClick:AddListenerEx(function () self:OnClickMsgBoxType1Btn(1) end)
	msgBoxType1Dir.Btn_Btn2.onClick:AddListenerEx(function () self:OnClickMsgBoxType1Btn(2) end)
	msgBoxType1Dir.Btn_Btn3.onClick:AddListenerEx(function () self:OnClickMsgBoxType1Btn(3) end)
	tipData[TipType.MsgBoxType1] = msgBoxType1Data
	
	local msgBoxType2Data = {}
	msgBoxType2Data.obj = self.objList.Dir_MsgBoxType2
	local msgBoxType2Dir = self.objList.MsgBoxType2
	msgBoxType2Data.resIcon = msgBoxType2Dir.Img_ResIcon
	msgBoxType2Data.resValue = msgBoxType2Dir.Txt_ResValue
	msgBoxType2Data.descText = msgBoxType2Dir.Txt_Desc
	msgBoxType2Data.Btn_Close = msgBoxType2Dir.Btn_Close
	msgBoxType2Data.btnList = {}
	table.insert(msgBoxType2Data.btnList, {msgBoxType2Dir.Btn_Btn1.gameObject, msgBoxType2Dir.Txt_Btn1, msgBoxType2Dir.Btn_Btn1, msgBoxType2Dir.Btn_Btn1:GetComponent('Image')})
	table.insert(msgBoxType2Data.btnList, {msgBoxType2Dir.Btn_Btn2.gameObject, msgBoxType2Dir.Txt_Btn2, msgBoxType2Dir.Btn_Btn2, msgBoxType2Dir.Btn_Btn2:GetComponent('Image')})
	table.insert(msgBoxType2Data.btnList, {msgBoxType2Dir.Btn_Btn3.gameObject, msgBoxType2Dir.Txt_Btn3, msgBoxType2Dir.Btn_Btn3, msgBoxType2Dir.Btn_Btn3:GetComponent('Image'), msgBoxType2Dir.Txt_Value, msgBoxType2Dir.Img_Icon})
	table.insert(msgBoxType2Data.btnList, {msgBoxType2Dir.Btn_Btn4.gameObject, msgBoxType2Dir.Txt_Btn4, msgBoxType2Dir.Btn_Btn4, msgBoxType2Dir.Btn_Btn4:GetComponent('Image'), msgBoxType2Dir.Txt_Free})
	msgBoxType2Dir.Btn_Mask.onClick:AddListenerEx(self.OnClickMsgBoxType2Mask)
	msgBoxType2Dir.Btn_Close.onClick:AddListenerEx(self.OnClickMsgBoxType2Mask)
	msgBoxType2Dir.Btn_Btn1.onClick:AddListenerEx(function () self:OnClickMsgBoxType2Btn(1) end)
	msgBoxType2Dir.Btn_Btn2.onClick:AddListenerEx(function () self:OnClickMsgBoxType2Btn(2) end)
	msgBoxType2Dir.Btn_Btn3.onClick:AddListenerEx(function () self:OnClickMsgBoxType2Btn(3) end)
	msgBoxType2Dir.Btn_Btn4.onClick:AddListenerEx(function () self:OnClickMsgBoxType2Btn(4) end)
	tipData[TipType.MsgBoxType2] = msgBoxType2Data
	
	local msgBoxType3Data = {}
	msgBoxType3Data.obj = self.objList.Dir_MsgBoxType3
	local msgBoxType3Dir = self.objList.MsgBoxType3
	msgBoxType3Data.descText = msgBoxType3Dir.Txt_Desc
	msgBoxType3Data.inputComp = msgBoxType3Dir.Inp_Content
	msgBoxType3Data.btnList = {}
	table.insert(msgBoxType3Data.btnList, {msgBoxType3Dir.Btn_Btn1.gameObject, msgBoxType3Dir.Txt_Btn1, msgBoxType3Dir.Btn_Btn1, msgBoxType3Dir.Btn_Btn1:GetComponent('Image')})
	table.insert(msgBoxType3Data.btnList, {msgBoxType3Dir.Btn_Btn2.gameObject, msgBoxType3Dir.Txt_Btn2, msgBoxType3Dir.Btn_Btn2, msgBoxType3Dir.Btn_Btn2:GetComponent('Image')})
	table.insert(msgBoxType3Data.btnList, {msgBoxType3Dir.Btn_Btn3.gameObject, msgBoxType3Dir.Txt_Btn3, msgBoxType3Dir.Btn_Btn3, msgBoxType3Dir.Btn_Btn3:GetComponent('Image')})
	msgBoxType3Data.btnList[-1] = { msgBoxType3Dir.Btn_InpBtn.gameObject, msgBoxType3Dir.Txt_InpBtn, msgBoxType3Dir.Btn_InpBtn, msgBoxType3Dir.Btn_InpBtn:GetComponent('Image') }
	msgBoxType3Dir.Btn_Btn1.onClick:AddListenerEx(function () self:OnClickMsgBoxType3Btn(1) end)
	msgBoxType3Dir.Btn_Btn2.onClick:AddListenerEx(function () self:OnClickMsgBoxType3Btn(2) end)
	msgBoxType3Dir.Btn_Btn3.onClick:AddListenerEx(function () self:OnClickMsgBoxType3Btn(3) end)
	msgBoxType3Dir.Btn_InpBtn.onClick:AddListenerEx(function () self:OnClickMsgBoxType3Btn(-1) end)
	tipData[TipType.MsgBoxType3] = msgBoxType3Data
	
	local msgBoxType4Data = {}
	msgBoxType4Data.obj = self.objList.Dir_MsgBoxType4
	local msgBoxType4Dir = self.objList.MsgBoxType4
	msgBoxType4Data.titleText = msgBoxType4Dir.Txt_Title
	msgBoxType4Data.titleShadowText = msgBoxType4Dir.Txt_Title_Shadow
	msgBoxType4Data.desc1Text = msgBoxType4Dir.Txt_Desc1
	msgBoxType4Data.left1Text = msgBoxType4Dir.Txt_Left1
	msgBoxType4Data.desc2Text = msgBoxType4Dir.Txt_Desc2
	msgBoxType4Data.left2Text = msgBoxType4Dir.Txt_Left2
	msgBoxType4Data.Txt_Diamond_Num = msgBoxType4Dir.Txt_Diamond_Num
	msgBoxType4Data.btnList = {}
	table.insert(msgBoxType4Data.btnList, {msgBoxType4Dir.Btn_Btn1.gameObject, msgBoxType4Dir.Txt_Btn1, msgBoxType4Dir.Btn_Btn1, msgBoxType4Dir.Btn_Btn1:GetComponent('Image')})
	table.insert(msgBoxType4Data.btnList, {msgBoxType4Dir.Btn_Btn2.gameObject, msgBoxType4Dir.Txt_Btn2, msgBoxType4Dir.Btn_Btn2, msgBoxType4Dir.Btn_Btn2:GetComponent('Image')})
	table.insert(msgBoxType4Data.btnList, {msgBoxType4Dir.Btn_Close.gameObject, msgBoxType4Dir.Txt_Close, msgBoxType4Dir.Btn_Close, msgBoxType4Dir.Btn_Close:GetComponent('Image')})
	msgBoxType4Dir.Btn_Btn1.onClick:AddListenerEx(function () self:OnClickMsgBoxType4Btn(1) end)
	msgBoxType4Dir.Btn_Btn2.onClick:AddListenerEx(function () self:OnClickMsgBoxType4Btn(2) end)
	msgBoxType4Dir.Btn_Close.onClick:AddListenerEx(function () self:OnClickMsgBoxType4Btn(3) end)
	tipData[TipType.MsgBoxType4] = msgBoxType4Data

	local msgBoxType5Data = {}
	msgBoxType5Data.obj = self.objList.Dir_MsgBoxType5
	local msgBoxType5Dir = self.objList.MsgBoxType5
	msgBoxType5Data.descText = msgBoxType5Dir.Txt_Desc
	msgBoxType5Data.btnList = {}
	table.insert(msgBoxType5Data.btnList, {msgBoxType5Dir.Btn_Btn1.gameObject, msgBoxType5Dir.Btn_Btn1})
	msgBoxType5Dir.Btn_Btn1.onClick:AddListenerEx(function () 
		local data = self.tipData[TipType.MsgBoxType5]
		data.obj:SetActive(false) 
	end)
	tipData[TipType.MsgBoxType5] = msgBoxType5Data
	
	self.tipData = tipData
	
	return true
end

-- 收到窗口消息
function UITipManager:OnWndMsg(msg, param1)	
	if msg == WndMsg.TipManager_CloseTip then
		if param1 == TipType.MsgBoxType1 then
			self:CloseMsgBoxType1()
		elseif param1 == TipType.MsgBoxType2 then
			self:CloseMsgBoxType2()
		elseif param1 == TipType.MsgBoxType3 then
			self:CloseMsgBoxType3()
		elseif param1 == TipType.MsgBoxType4 then
			self:CloseMsgBoxType4()
		end
	end
end

-- 窗口开启
function UITipManager:OnOpen(tipType, content, param1, param2, param3, param4, param5, param6, param7)
	if tipType == TipType.FlowText then
		-- 飘字 - V13.0版本震撼动画升级
		local data = self.tipData[tipType]
		
		-- V13.0: 设置随机偏移位置
		local randomOffset = Vector3(math.random(-100, 100), math.random(10, 50), 0)
		if param1 then 
			data.obj.transform.localPosition = param1 + randomOffset
		else
			data.obj.transform.localPosition = randomOffset
		end
		
		data.context.text = content
		data.bgobj:SetActive(true)
		
		-- V13.0: 震撼缩放效果
		local rectTransform = data.obj:GetComponent('RectTransform')
		rectTransform.localScale = Vector3(0.5, 0.5, 1)  -- 从0.5倍开始
		
		-- RichText的代码颜色不支持直接调属性，所以这里直接Fade整个UI
		if self.FlowTextSeq ~= nil then
			self.FlowTextSeq:Kill()
			self.FlowTextSeq = nil
		end
		
		data.canvasGroup.alpha = 1
		
		-- V13.0: 震撼动画序列
		self.FlowTextSeq = DOTween.Sequence()
		
		-- 震撼缩放动画
		self.FlowTextSeq:Append(rectTransform:DOScale(Vector3(1.5, 1.5, 1), 0.15))  -- 快速放大到1.5倍
		self.FlowTextSeq:Append(rectTransform:DOScale(Vector3(1, 1, 1), 0.15))      -- 缓回1倍
		
		-- 螺旋上升动画
		local startPos = data.obj.transform.localPosition
		local spiralRadius = 40
		local spiralHeight = 60
		local spiralDuration = 1.2
		
		local spiralTween = data.obj.transform:DOLocalMove(
			Vector3(startPos.x, startPos.y + spiralHeight, startPos.z), 
			spiralDuration
		):SetEase(DG.Tweening.Ease.OutQuart)
		
		-- 添加螺旋效果
		spiralTween:OnUpdate(function()
			local progress = spiralTween.ElapsedPercentage()
			local angle = progress * 720  -- 两圈螺旋
			local currentRadius = spiralRadius * (1 - progress * 0.5)  -- 半径递减
			local offsetX = math.cos(math.rad(angle)) * currentRadius
			local baseY = startPos.y + spiralHeight * progress
			data.obj.transform.localPosition = Vector3(startPos.x + offsetX, baseY, startPos.z)
		end)
		
		self.FlowTextSeq:Insert(0.3, spiralTween)  -- 缩放完成后开始螺旋
		
		-- 颤抖效果（前0.5秒）
		local shakeSequence = DOTween.Sequence()
		for i = 1, 8 do
			local shakeOffset = Vector3(math.random(-3, 3), math.random(-3, 3), 0)
			shakeSequence:Append(data.obj.transform:DOPunchPosition(shakeOffset, 0.06, 1, 0))
		end
		self.FlowTextSeq:Insert(0.3, shakeSequence)
		
		-- 透明度渐变
		self.FlowTextSeq:Insert(1.0, data.canvasGroup:DOFade(0, 0.5))
		
		self.FlowTextSeq:AppendCallback(UITipManager.OnFlowTextEnd)
		self.FlowTextSeq:SetUpdate(true)
	elseif tipType == TipType.MoveText then
		-- 飘字2(移动文字)
		local data = self.tipData[tipType]
		
		local posType = param1
		local targetPos = param2
		local direction = param3
		local style = param4 or 1
		local iconSprite = param5
		local moveTime = param6 or 1
		local stayTime = param7 or 0
		
		local item = nil
		for i, v in ipairs(data.itemList) do
			if not v.isActive then
				item = v
				break
			end
		end
		if not item then
			item = {}
			item.uiObj = GameObject.Instantiate(data.itemPrefab, data.itemContainer)
			item.uiTrans = item.uiObj:GetRectTransform()
			item.objList = {}
			Helper.FillLuaComps(item.uiTrans, item.objList)
			item.bg = item.objList.Img_Bg
			item.bgTrans = item.objList.Img_Bg:GetRectTransform()
			item.icon = item.objList.Img_Icon
			item.iconTrans = item.objList.Img_Icon:GetRectTransform()
			item.text = item.objList.Txt_MoveTextContent
			item.textOutline = item.text:GetComponent('Outline')
			-- 立即刷新字体以便计算
			item.text:GetComponent('TextAssist'):Reflush()
			item.txtTrans = item.objList.Txt_MoveTextContent:GetRectTransform()
			item.onAniEndFunc = function()
				item.uiObj:SetActive(false)
				item.isActive = false
			end
			table.insert(data.itemList, item)
		end
		item.isActive = true
		item.text.text = content
		local iconFirst = true
		local contentType = 0
		if style == 1 then
			-- 整行文字
			iconFirst = false
			contentType = 1
			
			item.bg.gameObject:SetActive(false)
			item.textOutline.enabled = true
		elseif style == 2 then
			-- 己方底图
			iconFirst = false
			contentType = 2
			
			item.bg.gameObject:SetActive(true)
			AtlasManager:AsyncGetSprite('ZD-33', item.bg)
			item.bgTrans.localEulerAngles = CachedVector3:Set(0, 0, 0)
			item.bgTrans.anchoredPosition = CachedVector2:Set(-64, 22)
			item.textOutline.enabled = false
		elseif style == 3 then
			-- 敌方底图
			iconFirst = true
			contentType = 3
			
			item.bg.gameObject:SetActive(true)
			AtlasManager:AsyncGetSprite('ZD-34', item.bg)
			item.bgTrans.localEulerAngles = CachedVector3:Set(0, 180, 0)
			item.bgTrans.anchoredPosition = CachedVector2:Set(76, 17)
			item.textOutline.enabled = false
		elseif style == 4 then
			iconFirst = false
			contentType = 4
			
			item.bg.gameObject:SetActive(true)
			item.textOutline.enabled = true
			--item.bg:SetNativeSize()
		end
		
		local anchorVec = nil
		if contentType == 1 or contentType == 4 then
			-- 文字居中
			anchorVec = CachedVector2:Set(0.5, 0.5)
			item.text.alignment = UnityEngine.TextAnchor.MiddleCenter
		elseif contentType == 2 then
			-- 内容从左排
			anchorVec = CachedVector2:Set(0, 0.5)
			item.text.alignment = UnityEngine.TextAnchor.MiddleLeft
		else
			-- 内容从右排
			anchorVec = CachedVector2:Set(1, 0.5)
			item.text.alignment = UnityEngine.TextAnchor.MiddleRight
		end
		item.txtTrans.anchorMin = anchorVec
		item.txtTrans.anchorMax = anchorVec
		item.txtTrans.pivot = anchorVec
		item.iconTrans.anchorMin = anchorVec
		item.iconTrans.anchorMax = anchorVec
		item.iconTrans.pivot = anchorVec
		item.uiObj:SetActive(true)
		
		local hasIcon = false
		if iconSprite and string.len(iconSprite) > 1 then
			AtlasManager:AsyncGetSprite(iconSprite, item.icon)
			item.icon.gameObject:SetActive(true)
			hasIcon = true
		else
			item.icon.gameObject:SetActive(false)
		end
		local curOffset = 0
		if iconFirst then
			-- 图标先出
			if hasIcon then
				item.iconTrans.anchoredPosition = CachedVector2:Set(0, 0)
				curOffset = item.icon.preferredWidth
			end
		else
			-- 文字先出
			item.txtTrans.anchoredPosition = CachedVector2:Set(0, 0)
			curOffset = item.text.preferredWidth
		end
		
		if curOffset > 0 then
			curOffset = curOffset + 5
		end
		
		if contentType == 3 then
			curOffset = -curOffset
		end
		
		if iconFirst then
			item.txtTrans.anchoredPosition = CachedVector2:Set(curOffset, 0)
		else
			item.iconTrans.anchoredPosition = CachedVector2:Set(curOffset, 0)
		end
		
		if posType == PosType.WorldPos then
			-- 先转成屏幕坐标
			local screenPos = RectTransformUtility.WorldToScreenPoint(SceneManager.mainCamera, targetPos)
			local result, localPoint = RectTransformUtility.ScreenPointToLocalPointInRectangle(data.itemContainer, screenPos, UIManager:GetUICamera(), nil)
			if result then
				targetPos = localPoint
			end
		elseif posType == PosType.ScreenPos then
			local result, localPoint = RectTransformUtility.ScreenPointToLocalPointInRectangle(data.itemContainer, screenPos, UIManager:GetUICamera(), nil)
			if result then
				targetPos = localPoint
			end
		elseif posType == PosType.UIPos then
			-- 不用变
		elseif posType == PosType.UIWorldPos then
			local screenPos = RectTransformUtility.WorldToScreenPoint(UIManager:GetUICamera(), targetPos)
			local result, localPoint = RectTransformUtility.ScreenPointToLocalPointInRectangle(data.itemContainer, screenPos, UIManager:GetUICamera(), nil)
			if result then
				targetPos = localPoint
			end
		end
		
		local rectTrans = item.uiTrans
		if item.sequence then
			item.sequence:Kill()
			item.sequence = nil
		end
		local tween = nil
		if direction < 1000 then
			-- 飘出
			rectTrans.anchoredPosition = targetPos
			local toPos = nil
			if direction == TipDirection.ToRight then
				toPos = CachedVector2:Set(targetPos.x + 20, targetPos.y + 50)
			elseif direction == TipDirection.ToLeft then
				toPos = CachedVector2:Set(targetPos.x - 20, targetPos.y + 50)
			elseif direction == TipDirection.ToUp then
				toPos = CachedVector2:Set(targetPos.x, targetPos.y + 50)
			elseif direction == TipDirection.ToDown then
				toPos = CachedVector2:Set(targetPos.x, targetPos.y - 50)
			end
			if toPos then
				tween = rectTrans:DOAnchorPos(toPos, moveTime)
			end
		else
			-- 飘入
			if direction == TipDirection.FromRight then
				rectTrans.anchoredPosition = CachedVector2:Set(UIManager:GetScreenRect().width * 0.5 + 100, targetPos.y)
			elseif direction == TipDirection.FromLeft then
				rectTrans.anchoredPosition = CachedVector2:Set(-UIManager:GetScreenRect().width * 0.5 - 100, targetPos.y)
			elseif direction == TipDirection.FromUp then
				rectTrans.anchoredPosition = CachedVector2:Set(targetPos.x, UIManager:GetScreenRect().height * 0.5 + 100)
			elseif direction == TipDirection.FromDown then
				rectTrans.anchoredPosition = CachedVector2:Set(targetPos.x, -UIManager:GetScreenRect().height * 0.5 - 100)
			end
			tween = rectTrans:DOAnchorPos(targetPos, moveTime):SetEase(TweeningEase.OutBack, 1.5)
		end
		if tween then
			-- V13.0: 增强MoveText动画效果
			local seq = DOTween.Sequence()
			
			-- 震撼缩放效果
			rectTrans.localScale = Vector3(0.3, 0.3, 1)  -- 从0.3倍开始，比FlowText更震撼
			seq:Append(rectTrans:DOScale(Vector3(1.8, 1.8, 1), 0.12))  -- 快速放大到1.8倍
			seq:Append(rectTrans:DOScale(Vector3(1, 1, 1), 0.12))      -- 缓回1倍
			
			-- 主要移动动画
			seq:Append(tween)
			
			-- 颤抖效果（在移动过程中）
			local shakeSequence = DOTween.Sequence()
			for i = 1, 6 do
				local shakeOffset = Vector3(math.random(-2, 2), math.random(-2, 2), 0)
				shakeSequence:Append(rectTrans:DOPunchPosition(shakeOffset, 0.08, 1, 0))
			end
			seq:Insert(0.24, shakeSequence)  -- 缩放完成后开始颤抖
			
			if stayTime > 0 then
				local add_stay_time =0
				for i,v in ipairs(data.itemList) do
					if v.isActive then
						add_stay_time=add_stay_time+0.2
					end
				end
				--print(stayTime)
				
				seq:AppendInterval(stayTime)
				seq:SetDelay(add_stay_time)
			end
			seq:SetUpdate(true)
			seq:OnComplete(item.onAniEndFunc)
			item.sequence = seq
			item.sequence:PlayForward()
		end
	elseif tipType == TipType.MsgBoxType1 then
		-- 提示框类型1
		local data = self.tipData[tipType]
		
		local desc = content
		data.descText.text = desc
		data.btnList[1].data = param1
		data.btnList[2].data = param2
		data.btnList[3].data = param3
		
		data.obj:SetActive(true)
		for i = 1, 3 do
			if data.btnList[i].data then
				local param = data.btnList[i].data
				data.btnList[i][1]:SetActive(true)
				if param.highlight then 
					data.btnList[i][2].text = param.text
				else
					data.btnList[i][2].text = param.text
				end
			else
				data.btnList[i][1]:SetActive(false)
			end
		end
		if param4 then
			if param4 == 7 then
				self.uiCanvas.gameObject:SetActive(true)
				self.uiCanvas.overrideSorting = true
				self.uiCanvas.sortingLayerName = "UI"
				self.uiCanvas.sortingOrder = param4
			end
		end
	elseif tipType == TipType.MsgBoxType2 then
		-- 提示框类型2
		local data = self.tipData[tipType]
		
		local desc = content
		data.descText.text = desc
		data.descText.gameObject.transform.localPosition = CachedVector3:Set(0, -22, 0)
		if param1 then
			data.resValue:GetComponent('TextAssist'):Reflush()
			data.resValue.text = param1
			data.resValue.gameObject:SetActive(true)
		end
		if param2 then
			data.resIcon.gameObject:SetActive(true)
			AtlasManager:AsyncGetSprite(param2, data.resIcon)
			data.resIcon:GetRectTransform().anchoredPosition = CachedVector2:Set(data.resValue.preferredWidth/2+10, 45)
		end 
		
		data.btnList[1].data = param3
		data.btnList[2].data = param4
		data.btnList[3].data = param5
		data.btnList[4].data = param6		
			
		data.obj:SetActive(true)
		data.Btn_Close.gameObject:SetActive(param7==true and true or false)
		for i = 1, 4 do
			if data.btnList[i].data then
				local param = data.btnList[i].data
				data.btnList[i][1]:SetActive(true)
				if not param.playAdvertise then
					if param.highlight then 
						AtlasManager:AsyncGetSprite('kuang_lbsd_02', data.btnList[i][4])
						data.btnList[i][2].text = string.format('<color=#df4104>%s</color>',param.text)
					else
						AtlasManager:AsyncGetSprite('TY-anl', data.btnList[i][4])
						data.btnList[i][2].text = string.format('<color=#0e6ec6>%s</color>',param.text)
					end
				else
					data.btnList[i][2].text = string.format('<color=#df4104>%s</color>',param.text)
				end

				if data.btnList[i][5] and data.btnList[i][6] then
					if param1 and param2 then
						data.btnList[i][5].gameObject:SetActive(true)
						data.btnList[i][6].gameObject:SetActive(true)
						data.resValue.gameObject:SetActive(false)
						data.resIcon.gameObject:SetActive(false)
						data.btnList[i][5].text = param1
						AtlasManager:AsyncGetSprite(param2, data.btnList[i][6])
						data.descText.gameObject.transform.localPosition = CachedVector3:Set(0, 15, 0)
					else
						data.btnList[i][5].gameObject:SetActive(false)
						data.btnList[i][6].gameObject:SetActive(false)
					end
				end

				if param.Desc and data.btnList[i][5] then
					data.btnList[i][5].text = param.Desc
				end
			else
				data.btnList[i][1]:SetActive(false)
			end
		end
	elseif tipType == TipType.MsgBoxType3 then
		-- 提示框类型3
		local data = self.tipData[tipType]
		
		local desc = content
		data.descText.text = desc

		local charLimit = param1
		if not charLimit then
			charLimit = 0
		end
		data.inputComp.characterLimit = charLimit

		data.btnList[-1].data = param2
		if param2 then
			data.btnList[-1][1]:SetActive(true)
			data.btnList[-1][2].text = param2.text
			AtlasManager:AsyncGetSprite(param2.image, data.btnList[-1][4])
			data.btnList[-1][4]:SetNativeSize()
		else
			data.btnList[-1][1]:SetActive(true)
		end

		data.btnList[1].data = param3
		data.btnList[2].data = param4
		data.btnList[3].data = param5
		
		data.obj:SetActive(true)
		for i = 1, 3 do
			if data.btnList[i].data then
				local param = data.btnList[i].data
				data.btnList[i][1]:SetActive(true)
				if param.highlight then 
					AtlasManager:AsyncGetSprite('kuang_lbsd_02', data.btnList[i][4])
					data.btnList[i][2].text = string.format('<color=white>%s</color>',param.text)
				else
					AtlasManager:AsyncGetSprite('TY-anl', data.btnList[i][4])
					data.btnList[i][2].text = string.format('<color=white>%s</color>',param.text)
				end
			else
				data.btnList[i][1]:SetActive(false)
			end
		end
	elseif tipType == TipType.MsgBoxType4 then
		-- 提示框类型4
		local data = self.tipData[tipType]

		local desc = content
		data.titleText.text = desc
		data.titleShadowText.text = desc

		data.btnList[1].data = param1
		data.btnList[2].data = param2
		data.btnList[3].data = param3
		data.desc1Text.text = param4[1]
		data.left1Text.text = param4[2]
		data.desc2Text.text = param4[3]
		data.left2Text.text = param4[4]
		data.Txt_Diamond_Num.text = param4[5]


		data.obj:SetActive(true)
		for i = 1, 2 do
			if data.btnList[i].data then
				local param = data.btnList[i].data
				data.btnList[i][1]:SetActive(true)
				if param.highlight then
					AtlasManager:AsyncGetSprite('kuang_lbsd_02', data.btnList[i][4])
					data.btnList[i][2].text = string.format('<color=white>%s</color>',param.text)
				else
					AtlasManager:AsyncGetSprite('TY-anl', data.btnList[i][4])
					data.btnList[i][2].text = string.format('<color=white>%s</color>',param.text)
				end
			else
				data.btnList[i][1]:SetActive(false)
			end
		end
	elseif tipType == TipType.MsgBoxType5 then
		-- 说明框类型
		local data = self.tipData[tipType]
		
		local desc = HelperL.GetContenBRText(content)
		data.descText.text = desc
		data.btnList[1].data = param1
		self.uiCanvas.overrideSorting = true
		self.uiCanvas.sortingOrder = 1
		data.obj:SetActive(true)
	end
end

-- 飘字结束通知
function UITipManager.OnFlowTextEnd()
	local self = UITipManager
	local data = self.tipData[TipType.FlowText]
	if not data then
		return
	end
	data.context.text = ''
	data.bgobj:SetActive(false)
end

-- 提示框类型1按钮点击
function UITipManager:OnClickMsgBoxType1Btn(index)
	self:CloseMsgBoxType1()
	
	local data = self.tipData[TipType.MsgBoxType1]
	local btnItem = data.btnList[index]
	if not btnItem then
		return
	end
	
	local btnData = btnItem.data
	if not btnData then
		return
	end
	
	if btnData.callback then
		btnData.callback()
	end
	self.uiCanvas.overrideSorting = false
end

-- 关闭提示框类型1
function UITipManager:CloseMsgBoxType1()
	local data = self.tipData[TipType.MsgBoxType1]
	data.obj:SetActive(false)
	self.uiCanvas.overrideSorting = false
end

-- 提示框类型1遮罩点击
function UITipManager.OnClickMsgBoxType1Mask()
	local self = UITipManager
	self:CloseMsgBoxType1()
	self.uiCanvas.overrideSorting = false
end

-- 提示框类型2按钮点击
function UITipManager:OnClickMsgBoxType2Btn(index)
	self:CloseMsgBoxType2()
	
	local data = self.tipData[TipType.MsgBoxType2]
	local btnItem = data.btnList[index]
	if not btnItem then
		return
	end
	
	local btnData = btnItem.data
	if not btnData then
		return
	end
	
	if btnData.callback then
		btnData.callback(btnData.paramArgs)
	end
end

-- 提示框类型2遮罩点击
function UITipManager.OnClickMsgBoxType2Mask()
	local self = UITipManager
	self:CloseMsgBoxType2()
end

-- 关闭提示框类型2
function UITipManager:CloseMsgBoxType2()
	local data = self.tipData[TipType.MsgBoxType2]
	data.obj:SetActive(false)
end

-- 提示框类型3按钮点击
function UITipManager:OnClickMsgBoxType3Btn(index)
	local data = self.tipData[TipType.MsgBoxType3]
	local btnItem = data.btnList[index]
	if not btnItem then
		self:CloseMsgBoxType3()
		return
	end
	
	local btnData = btnItem.data
	if not btnData then
		self:CloseMsgBoxType3()
		return
	end
	
	local close = true
	if btnData.callback then
		close = btnData.callback(data.inputComp)
	end

	if close then
		self:CloseMsgBoxType3()
	end
end

-- 关闭提示框类型1
function UITipManager:CloseMsgBoxType3()
	local data = self.tipData[TipType.MsgBoxType3]
	data.obj:SetActive(false)
end

-- 提示框类型4按钮点击
function UITipManager:OnClickMsgBoxType4Btn(index)
	self:CloseMsgBoxType4()

	local data = self.tipData[TipType.MsgBoxType4]
	local btnItem = data.btnList[index]
	if not btnItem then
		print("btn item is nil")
		return
	end

	local btnData = btnItem.data
	if not btnData then
		print("btnData is nil")
		return
	end
	if btnData.callback then
		btnData.callback()
	end
end

-- 关闭提示框类型4
function UITipManager:CloseMsgBoxType4()
	local data = self.tipData[TipType.MsgBoxType4]
	data.obj:SetActive(false)
end

return UITipManager