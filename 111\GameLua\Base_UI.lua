---
--- Generated by <PERSON><PERSON><PERSON>(https://github.com/EmmyLua)
--- Created by Admin.
--- DateTime: 2023/7/21 12:08
--- UI相关
---

local ClassBase = require("Base_Class")

---@class UIBase:ClassBase
---@field _base ClassBase
local this = class(ClassBase)

---@param go GameObject
function this:baseInit(go)
    self.gameObject = go
    self.objList = {}
    Helper.FillLuaComps(go.transform, self.objList)
    self:AddListener()
end

--- 设置LuaFileName
function this:SetLuaFileName(luaFileId)
    self.luaFileName = fileName
    self.luaFileId = (fileName)
end

--- 获得文本
---@param subId number @文本组中的subId（对应luaFileId的文本组）
function this:GetGameText(subId)
    if self.luaFileId == nil or type(self.luaFileId) ~= "number" then
        error("参数luaFileId为空，或者有误")
    end
    GetGameText(self.luaFileId, subId)
end

--- 添加监听
function this:AddListener()

end

--- 移除监听（若有需要，则调用）
function this:RemoveListener()

end

--- 销毁
function this:OnDestroy()
    self:RemoveListener()
end

--- 添加点击事件
---@param btn Button
function this:AddClickEvent(btn, callback)
    if IsNullGo(btn) then
        return
    end
    UI.AddBtnClickListener(btn, callback)
end

return this