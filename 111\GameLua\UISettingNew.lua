--[[
********************************************************************
    created:	2024/05/16
    author :	李锦剑
    purpose:    设置新
*********************************************************************
--]]

local luaID = 'UISettingNew'
---@class UISettingNew:UIWndBase
local m = {}
local switchDataList = {
    --音乐
    { Name = 'Music_Volume',       Text = GetGameText(luaID, 1), Icon = 'sz_tb_01' },
    --音效
    { Name = 'SoundEffect_Volume', Text = GetGameText(luaID, 2), Icon = 'sz_tb_02' },
}
--------------------------------------------------------------------
-- 初始化
--------------------------------------------------------------------
function m.OnCreate()
    ---@type Item_Switch[]
    m.Item_Switch_List = {}

    ---@type {Name:string, Text:string, Icon:string}[]
    

    for i, v in ipairs(switchDataList) do
        if not m.Item_Switch_List[i] then
            m.Item_Switch_List[i] = m.Creation_Item_Switch(m.objList.Grid_Switch, i)
        end
        m.Item_Switch_List[i].UpdateData(v)
    end

    m.RegisterClickEvent()
    return true
end

--------------------------------------------------------------------
-- 窗口开启
--------------------------------------------------------------------
function m:OnOpen()

end

--------------------------------------------------------------------
-- 注册点击事件
--------------------------------------------------------------------
function m.RegisterClickEvent()
    m:AddClick(m.objList.Btn_Close, function()
        m:CloseSelf()
    end)
    local Music_Volume = PlayerPrefs.GetFloat("Music_Volume", 1.0)
    if Music_Volume == 0 then
        --m.objList.Btn_onYinYue.gameObject:SetActive(true)
        --m.objList.Btn_offYinYue.gameObject:SetActive(false)
    else
        --m.objList.Btn_onYinYue.gameObject:SetActive(false)
        --m.objList.Btn_offYinYue.gameObject:SetActive(true)
    end
    m:AddClick(m.objList.Btn_onZhenDong, function()
        m:Btn_onZhenDongClick()
    end)
    m:AddClick(m.objList.Btn_offZhenDong, function()
        m:Btn_offZhenDongClick()
    end)
    m:AddClick(m.objList.Btn_onYinYue, function()
        m:Btn_onYinYueClick()
    end)
    m:AddClick(m.objList.Btn_offYinYue, function()
        m:Btn_offYinYueClick()
    end)
    m:AddClick(m.objList.Btn_Exit, function()
        --Premier.Instance:ExitGameConfirm()
        GameLuaAPI.QuitGame()
    end)
end

function m.Btn_onZhenDongClick()
    m.objList.Btn_onZhenDong.gameObject:SetActive(false)
    m.objList.Btn_offZhenDong.gameObject:SetActive(true)
end

function m.Btn_offZhenDongClick()
    m.objList.Btn_onZhenDong.gameObject:SetActive(true)
    m.objList.Btn_offZhenDong.gameObject:SetActive(false)
end

function m.Btn_onYinYueClick()
    m.objList.Btn_onYinYue.gameObject:SetActive(false)
    m.objList.Btn_offYinYue.gameObject:SetActive(true)
    SoundManager:SetVolume_Music(0)
end

function m.Btn_offYinYueClick()
    m.objList.Btn_onYinYue.gameObject:SetActive(true)
    m.objList.Btn_offYinYue.gameObject:SetActive(false)
    SoundManager:SetVolume_Music(1)
end

--------------------------------------------------------------------
---创建设置框
---@param parent integer
---@param index integer
---@return Item_Switch
--------------------------------------------------------------------
function m.Creation_Item_Switch(parent, index)
    ---@class Item_Switch
    local item = {}
    item.index = index
    item.com = m:CreateSubItem(parent, m.objList.Item_Switch)
    -- item.com.Btn_Set.onClick:AddListener(function()
    --     if PlayerPrefs.GetFloat(switchDataList[item.index].Name, 1.0) == 1.0 then
    --         m.SettingSwitch(0, switchDataList[item.index].Name)
    --     else
    --         m.SettingSwitch(1.0, switchDataList[item.index].Name)
    --     end
    --     if PlayerPrefs.GetFloat(switchDataList[item.index].Name, 1.0) == 1.0 then
    --         item.com.Img_Set.gameObject.transform.localPosition = Vector3.New(-30,0,0)
    --     else
    --         item.com.Img_Set.gameObject.transform.localPosition = Vector3.New(30,0,0)
    --     end
    -- end)
    item.com.Sld_Slider.onValueChanged:AddListener(function(value)
        m.SettingSwitch(value, switchDataList[item.index].Name)
    end)
    ---更新商品数据
    ---@param data {Name:string, Text:string, Icon:string}
    item.UpdateData = function(data)
        item.data = data
        if data then
            --item.com.Sld_Set.value = PlayerPrefs.GetFloat(data.Name, 1.0)
            -- if PlayerPrefs.GetFloat(data.Name, 1.0) == 1.0 then
            --     item.com.Img_Set.gameObject.transform.localPosition = Vector3.New(-30,0,0)
            -- else
            --     item.com.Img_Set.gameObject.transform.localPosition = Vector3.New(30,0,0)
            -- end
            item.com.Sld_Slider.value = PlayerPrefs.GetFloat(data.Name, 1.0)
            item.com.Txt_Switch.text = data.Text
            --AtlasManager:AsyncGetSprite(data.Icon, item.com.Img_Icon)
            item.com.gameObject:SetActive(true)
        else
            item.com.gameObject:SetActive(false)
        end
    end
    return item
end

--------------------------------------------------------------------
---设置开关
---@param value number
---@param name string
--------------------------------------------------------------------
function m.SettingSwitch(value, name)
    if name == 'Music_Volume' then
        SoundManager:SetVolume_Music(value)
    elseif name == 'SoundEffect_Volume' then
        SoundManager:SetVolume_SoundEffect(value)
    end
end

return m
