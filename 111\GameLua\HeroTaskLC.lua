--[[
   created:	2016/10/08
   author :	吴德燊
   purpose:	英雄任务部件
--]]

local luaID = ('HeroTaskLC')

local TaskFactory =require "Task"
HeroTaskLC = {}
ECTaskStatus =
{
	Null = 0,
	CanAccept = 1,
	Accepted = 2,
	Doing = 3,
	UnComplete = 4,
	CanComplete = 5,
	Completed = 6,
	CanRefresh = 7,
	Max = 8,
}

ECTaskStyle =
{
	Null = 0,
	Trunk = 1,
	Branch = 2,
	Daily = 3,
	Country = 4,
	Help = 5,
	Society = 6,
	Escort = 7,
	Loop = 8,
	Max = 9,
}

ECTaskStyleName = {}
ECTaskStyleName[0] = '[FFFF00]'
ECTaskStyleName[1] = GetGameText(luaID, 1)
ECTaskStyleName[2] = GetGameText(luaID, 2)
ECTaskStyleName[3] = GetGameText(luaID, 3)
ECTaskStyleName[4] = GetGameText(luaID, 4)
ECTaskStyleName[5] = GetGameText(luaID, 5)
ECTaskStyleName[6] = GetGameText(luaID, 6)
ECTaskStyleName[7] = GetGameText(luaID, 3)
ECTaskStyleName[8] = GetGameText(luaID, 7)

local function CheckValueInTable(list, value)
	for i, v in ipairs(list) do
		if v == value then
			return true
		end
	end
	return false
end

local format = string.format
local o = {}
local GetType = System.Type.GetType
function HeroTaskLC.Clean()
	o.idToItem = {}
	o.taskStatusList = {}
	o.branchShowTask = {}
	o.timerTask = {}
	for i = TASK_STATUS.TASK_STATUS_NULL,TASK_STATUS.TASK_STATUS_MAXID -1 do
		o.taskStatusList[i] = o.taskStatusList[i] or {}
	end
end
function HeroTaskLC.New( master )
	o.master = master
	o.idToItem = o.idToItem or {}
	--o.isAddClientTask = false
	o.escortUID = 0
	o.onEscort = 0
	o.autoDoDailyTask = -1
	o.autoDoLoopTask = false
	o.curFocusTask = nil   --- 当前正在做这个任务
	function o:Build( taskData )
		if not o.master then
			return
		end
		o.escortUID = taskData.EscortUID
		o.onEscort = taskData.OnEscort
		o.taskStatusList = o.taskStatusList or {}
		o.branchShowTask = o.branchShowTask or {}
		o.timerTask = o.timerTask or {}
		for i = TASK_STATUS.TASK_STATUS_NULL,TASK_STATUS.TASK_STATUS_MAXID -1 do
			o.taskStatusList[i] = o.taskStatusList[i] or {}
		end
		--o.AddClientTask()
	end
	
	function o:SetEscortUID( uid )
		self.escortUID = uid
		if tonumber(self.onEscort) == 1 and uid == '0' then
			--EntityModule.hero.escortLC.GetOff()
			self:SetOnEscort(0)
		end
		EntityModule.hero.escortLC.CheckEscortExist()
		if not uid or uid == '0' then
			--UIManager.mainLandUI.UpdateHeroStatusText(EventID.EscortHelp, false)
		end
	end
	
	function o:SetOnEscort( onEscort )
		if self.onEscort == 1 and onEscort == 0 then
			EntityModule.hero.escortLC.GetOff()
		end
		self.onEscort = onEscort
		if onEscort > 0 or o.master.isAttaching then
			o.master.headNameComp:Disable()
		else
			o.master.headNameComp:Enable()
		end
		local result, dataItem = o.GetEscortTask()
		if result and tonumber(self.escortUID) ~= 0 and onEscort == 1 then
			if EntityModule.hero.isRiding then
				MountLC.OffMount()
			end
			if EntityModule.hero.heroTaskLC.curFocusTask == dataItem then
				dataItem:AfterAccept()
			end
		end
	end
	
	function o.GetTaskGroupCount(groupID)
		for k,v in pairs(o.idToItem) do
			if v.branchID == 1 then
				local branchScheme = GetThisTaskBranchScheme(v)
				if branchScheme then
					if groupID == branchScheme.GroupID  then
						return v.round, branchScheme.GroupCount
					end
				end
			end
		end
		return 0, 0
	end
	
	--[[@param 			taskItem.branchID
						taskItem.taskID
						taskItem.step
						taskItem.acceptTime
						taskItem.status
						taskItem.round
						taskItem.score
						taskItem.change
						taskItem.taskScheme
		]]--
	function o.UpdateTask( taskItem )
		local key  = taskItem.taskID * 256 + taskItem.branchID

		local oldScore = 0
		local oldStatus = -1
		if o.idToItem[key] then
			local oldData = o.idToItem[key]
			oldScore = oldData.score
			oldStatus = oldData:GetClientStatus()
			local statusList = o.taskStatusList[oldData.status]
			if statusList then
				local cnt = 1
				for _,v in pairs(statusList) do
					if v == key then
						table.remove(statusList,cnt)
						break
					end
					cnt =  cnt + 1
				end
			end
		end
		local statusList2 = o.taskStatusList[taskItem.status]
		table.insert(statusList2, key)
		if o.idToItem[key] then
			if o.idToItem[key].acceptTime == 0 then
				o.idToItem[key] = TaskFactory(taskItem.taskScheme.Type, taskItem.branchID, taskItem.taskID, taskItem.step, taskItem.acceptTime, taskItem.status, taskItem.round, taskItem.score, taskItem.taskScheme)
			else
				local newTask = TaskFactory(taskItem.taskScheme.Type,
					taskItem.branchID,
					taskItem.taskID,
					taskItem.step,
					taskItem.acceptTime,
					taskItem.status,
					taskItem.round,
					taskItem.score,
					taskItem.taskScheme)
				o.idToItem[key] = newTask
				--local level = EntityModule.GetPlayerNumProp(CREATURE_FIELD.CREATURE_FIELD_LEVEL)
				if newTask and newTask.status == TASK_STATUS.TASK_STATUS_ACCEPTED
					and newTask:GetClientStatus() == ECTaskStatus.CanComplete and newTask.taskScheme.AutoNextStep == 1 then
					newTask:AfterFinishTask()
				end
				if newTask and newTask.status == TASK_STATUS.TASK_STATUS_COMPLETED
					and newTask:GetClientStatus() == ECTaskStatus.Completed then
					EventManager:Fire(EventID.CheckGuideEvent, 4)
				end
				if newTask.status == TASK_STATUS.TASK_STATUS_ACCEPTED and newTask:GetClientStatus() ~= ECTaskStatus.CanComplete then
					if newTask.taskScheme.Type == TaskType.TaskType_Talk or
						newTask.taskScheme.Type == TaskType.TaskType_Escort
						and oldScore and oldScore > 0 then
						newTask.score = oldScore
					end
				end
			end
		else
			o.idToItem[key] = TaskFactory(taskItem.taskScheme.Type,
				taskItem.branchID,
				taskItem.taskID,
				taskItem.step,
				taskItem.acceptTime,
				taskItem.status,
				taskItem.round,
				taskItem.score,
				taskItem.taskScheme)
		end
		
		local item = o.idToItem[key]
		if item == nil then return end
		if (o.autoDoLoopTask and o.curFocusTask and item.taskScheme.GroupID == o.curFocusTask.taskScheme.GroupID)
			or (o.autoDoDailyTask ~= -1 and o.curFocusTask and item.taskScheme.GroupID == o.autoDoDailyTask and o.curFocusTask.taskScheme.GroupID == o.autoDoDailyTask) then
			if item.score ~= 0 and item.score ~= item.taskScheme.Parameter2 then return end
			local status = item:GetClientStatus()
			if status == ECTaskStatus.CanAccept then
				o.SendLuaAcceptTask(taskItem.branchID,
					taskItem.taskID,
					taskItem.step)
			elseif status == ECTaskStatus.Doing then
				item:AfterAccept()
			elseif status == ECTaskStatus.Complete then
				EventManager:Fire(EventID.CloseAutoLoopTask)
			end
		end
		
		if o.curFocusTask and o.curFocusTask.taskID == taskItem.taskID then
			o.SetFocusTask(item)
		end
		
		local noticeType = item.taskScheme.AcceptNotice
		if noticeType and #noticeType > 0 and noticeType[1] > 0 then
			local newStatus = item:GetClientStatus()
			if oldStatus ~= newStatus then
				local hasArrow = false
				if newStatus == ECTaskStatus.CanAccept then
					if CheckValueInTable(noticeType, 1) then
						hasArrow = true
					end
				elseif newStatus == ECTaskStatus.Accepted or newStatus == ECTaskStatus.Doing then
					if CheckValueInTable(noticeType, 2) then
						hasArrow = true
					end
				elseif newStatus == ECTaskStatus.CanComplete then
					if CheckValueInTable(noticeType, 3) then
						hasArrow = true
					end
				end
				if hasArrow then
					EventManager:Fire(EventID.TaskAcceptNotice, item, true)
				else
					EventManager:Fire(EventID.TaskAcceptNotice, item, false)
				end
			end
		end
		local wayNpcID = o.GetNpcID(item.taskScheme.WayNPC)
		if item.taskScheme.GroupID == 2 and item:GetClientStatus() == ECTaskStatus.CanComplete
			and NPCManager.lastTrigNpcID == wayNpcID then
			EntityModule.hero.navigationLG.Pause()
			if item.taskScheme.Quality ~= 5 then 
				-- 刺探任务特殊处理，自动刷到最高品质
				EntityModule.hero.navigationLG:NavigateToNPC(wayNpcID)
			elseif item.taskScheme.Quality == 5 then
				local taskItem = o.GetItem(taskItem.branchID,taskItem.taskID)
				taskItem:WantToTurnInTask()
			end
		end
	end
	
	-- 获取做任务对应细胞国家的NPC
	function o.GetNpcID( NPCStr )
		local npcList = HelperL.Split(NPCStr, ';')
		if not npcList then
			return 0
		end
		local npcID = npcList[1]
		if #npcList > 1 then
			local countryID = EntityModule.hero.propertyLC:GetProperty(PLAYER_FIELD.PLAYER_FIELD_COUNTRY)
			npcID = npcList[countryID]
		end
		if not npcID then
			npcID = 0
		end
		local npcID = tonumber(npcID)
		if not npcID then
			return 0
		end
		return npcID
	end

	-- 获取做任务对应细胞国家的地图ID
	function o.GetMapID( MapIDStr )
		local mapIDList = HelperL.Split(MapIDStr, ';')
		if not mapIDList then
			return 0
		end
		local mapID = mapIDList[1]
		if #mapIDList > 1 then
			local countryID = EntityModule.hero.propertyLC:GetProperty(PLAYER_FIELD.PLAYER_FIELD_COUNTRY)
			mapID = mapIDList[countryID]
		end
		if not mapID then
			mapID = 0
		end
		local mapID = tonumber(mapID)
		if not mapID then
			return 0
		end
		return mapID
	end

	-- 删除任务
	function o.RemoveTask( branchID, taskID )
		local key = taskID * 256 + branchID
		local oldData = o.idToItem[key]
		if oldData then
			local statusList = o.taskStatusList[oldData.status]
			local cnt = 1
			for _,v in pairs(statusList) do
				if v == key then table.remove(statusList,cnt) break end
				cnt = cnt + 1
			end
			o.idToItem[key] = nil
		end
		EventManager:Fire(EventID.TaskItemRemove, branchID, taskID)
	end
	
	local messageLength = function ( t )
		local cnt = 0
		for _, v in ipairs(t) do
			cnt = cnt + 1
		end
		return cnt
	end
	
	local function EventFire( )
		EventManager:Fire(EventID.DailyPlayTaskRefresh)
		EventManager:Fire(EventID.TaskPartUpdate)
		EventManager:Fire(EventID.SetSocietyCJPanel)
		EventManager:Fire(EventID.SetSocietyPHPanel)
		EventManager:Fire(EventID.SetSocietyJSPanel)
	end
	
	--- 同步任务数据
	--@param SC_Entity_SyncTaskItem
	function o.Update( message )
		local mLen = messageLength(message.ItemList)

		for _,v in ipairs(message.ItemList) do
			o.UpdateData(v)
		end

		local taskSerial = HeroDataManager:GetLogicData(LOGIC_DATA.DATA_TRUNK_TASK_CUR_SERIAL)
		if taskSerial == nil then error('找不到主线任务序号 id = nil') return end
		--print('Update：'..taskSerial)
		local nextTaskID = 0
		if taskSerial ~= 0 then
			local nextTaskScheme = Schemes.Task:Get(taskSerial, 1)
			if nextTaskScheme then
				nextTaskID = nextTaskScheme.FollowTask
			else
				nextTaskID = 0
			end
		else
			nextTaskID = 1
		end
		--print('nextTaskID',nextTaskID)
		if not nextTaskID then return end
		if not o.idToItem[ (nextTaskID) * 256 ] then
			local taskScheme = Schemes.Task:Get(nextTaskID, 1)
			if taskScheme then
			if taskScheme.MinLevel > EntityModule.hero.propertyLC:GetProperty(CREATURE_FIELD.CREATURE_FIELD_LEVEL) then
				EventFire()
				return
			end
				o.UpdateData({BranchID = 0, TaskID = nextTaskID, Step = 1,AcceptTime = 0,Status = TASK_STATUS.TASK_STATUS_NULL,
					Round = 0,Score = 0,Change = 1})
			end
		end
		--- 数据更新完再更新UI
		EventFire()
	end
	
	function o.OnTaskSerialUpdate(  )
		local heroLv = EntityModule.hero.propertyLC:GetProperty(CREATURE_FIELD.CREATURE_FIELD_LEVEL)
		--if heroLv > HangUp.autoTaskLevel then return end
		local taskSerial = HeroDataManager:GetLogicData(LOGIC_DATA.DATA_TRUNK_TASK_CUR_SERIAL)
		local curTask = Schemes.Task:Get(taskSerial, 1)
		if not curTask then return end
		local nextTaskID = curTask.FollowTask
		--print('nextTaskID',nextTaskID)
		if not nextTaskID then return end
		if not o.idToItem[ (nextTaskID ) * 256 ] then
			local taskScheme = Schemes.Task:Get(nextTaskID, 1)

			if taskScheme then
				if taskScheme.MinLevel > heroLv then
					return
				end
				o.UpdateData({BranchID = 0, TaskID = nextTaskID, Step = 1,AcceptTime = 0,Status = TASK_STATUS.TASK_STATUS_NULL,
					Round = 0,Score = 0,Change = 1})
			end
		end
		EventManager:Fire(EventID.TaskPartUpdate)
	end
	
	local function NewTaskItem( o )
		local o =
		{
			branchID = 0,
			taskID = 0,
			step = 0,
			acceptTime = 0,
			status = 0,
			round = 0,
			score = 0,
			change = 0,
		}
		return o
	end

	--@param SC_Entity_SyncTaskItem.TaskItem
	function o.UpdateData(value  )
		-- body
		local data = NewTaskItem()
		data.branchID = value.BranchID
		data.taskID = value.TaskID
		data.step = value.Step or 1
		data.acceptTime = value.AcceptTime or 0
		data.status = value.Status or TASK_STATUS.TASK_STATUS_NULL
		data.round = value.Round or 0
		data.score = value.Score or 0
		data.change = value.Change or 1

		--- change < 0 删除任务
		if data.change < 0 then
			o.RemoveTask(value.BranchID, value.TaskID)
		else
			local taskScheme = Schemes.Task:Get(data.taskID, data.step)
			if not taskScheme then return end
			data.taskScheme = taskScheme
			o.UpdateTask(data)
		end
	end

	--@param branchID
	--[[@param 			taskItem.branchID
						taskItem.taskID
						taskItem.step
						taskItem.acceptTime
						taskItem.status
						taskItem.round
						taskItem.score
						taskItem.change
		]]--
	function o.OnCompleteTask( branchID, taskScheme )
		--[[if not taskScheme then print('找不到任务ID为'..taskItem.taskID..'的配置') return end
		local nextTaskID = taskScheme.FollowTask
		local nextScheme = Schemes.Task:Get(nextTaskID, 1)
		if not nextScheme then print('找不到任务ID为'..nextTaskID..'的配置') return end
		local nextData = {}
		nextData.branchID = branchID
		nextData.taskID = nextScheme.ID
		nextData.step = nextScheme.Step
		nextData.acceptTime = 0
		nextData.status = TASK_STATUS.TASK_STATUS_NULL
		nextData.round = 0
		nextData.score = 0
		nextData.change = 1
		nextData.taskScheme = nextScheme
		o.UpdateTask(nextData, true)]]
	end

	function o.GetItem( branchID, taskID )
		local key = taskID * 256 + branchID
		return o.idToItem[key] or nil
	end

	--@param checkDistance 是否要做距离判断
	function o.CanCompleteTask( branchID, taskScheme, checkDistance )
		local tID = taskScheme.ID
		local taskItem = o.GetItem(branchID, tID)
		if not taskItem then  return  false end
		if  taskItem.status == TASK_STATUS.TASK_STATUS_ACCEPTED then
			-- do nothing
		else
			return false
		end

		local goal = taskItem:GetTaskGoal()
		local score = taskItem:GetTaskScore()
		if score < goal then return false end
		local mapID = o.GetMapID(taskScheme.GiveMapID)
		local tagMapID = o.CalcMapID(0, mapID)
		if checkDistance ~= nil and checkDistance == true and tagMapID ~= 0 then
			local nowMapID = WorldModule.mapId
			local nowPoint = o.master.position
			local tagPoint = HelperL.PBString2Vector3(taskScheme.GivePoint)
			local diffVec3 = tagPoint - nowPoint
			if nowMapID ~= tagMapID or diffVec3:SqrMagnitude() >= 9 then
				return false
			end
		end
		return true
	end

	--- 地图判断
	function o.CalcMapID( countryID, mapID )
		-- if o.master == nil then return 0 end
		-- local nowMapID = WorldModule.mapId
		-- local mapScheme = Schemes.GameScene:Get(nowMapID)
		-- if mapScheme == nil then return 0 end
		-- local nowCountryID = mapScheme.Country
		-- local actorCountryID = o.master.propertyLC:GetProperty(PLAYER_FIELD.PLAYER_FIELD_COUNTRY)
		-- if countryID == 0 then
		-- 	mapID = nowCountryID * 1000 + mapID % 1000
		-- elseif countryID == 7 then
		-- 	mapID = actorCountryID * 1000 + mapID % 1000;
		-- elseif countryID == 8 then
		-- 	if nowCountryID == actorCountryID then
		-- 		if nowCountryID < 3 then
		-- 			mapID = 3000 + mapID % 1000
		-- 		else
		-- 			mapID = 1000 + mapID % 1000
		-- 		end
		-- 	else
		-- 		mapID = nowCountryID * 1000 + mapID % 1000
		-- 	end
		-- elseif countryID == 9 then
		-- 	mapID = 0
		-- end
		return mapID
	end
	
	--- 发送任务刷新请求
	function o.SendLuaRefreshTask( branchID, taskID, step, branchScheme, costDiamond)
		assert(branchScheme, "需传入日常任务配置")
		local str = string.format("LuaRequestRefreshTask?branchID=%d&taskID=%d&step=%d", branchID, taskID, step)
		LuaModule.RunLuaRequest(str, o.UpdateRefreshTask)
	end

	--- 解析任务刷新请求返回的指令
	--@param content "branchID=%d+&taskID=%d+&step=%d+"
	function o.UpdateRefreshTask( resultCode, content )
		if resultCode ~= RESULT_CODE.RESULT_COMMON_SUCCEED then
			if resultCode == RESULT_CODE.RESULT_COMMON_LACK_DIAMOND or resultCode == RESULT_CODE.RESULT_COMMON_LACK_BINDDIAMOND then
				HelperL.CheckPlayerDiamond()
            else
            	HelperL.ShowMessage(TipType.FlowText, ResultCode:GetResultCodeStr(resultCode))
			end
			return
		end
		local iter = string.gmatch(content, '(%d+)')
		local p1 = iter()
		local p2 = iter()
		local p3 = iter()
		EventManager:Fire(EventID.TaskPageUpdate, tonumber(p1),tonumber(p2))
		--EventManager:Fire(EventID.RefreshTaskCallBack, tonumber(p1), tonumber(p2), tonumber(p3) )
	end

	function o.SendLuaBestTask(branchID, taskID, step, branchScheme)
		local cost = branchScheme.Diamond
		if HelperL.CheckPlayerDiamond(cost, 3) then
			local str = string.format('LuaRequestBestTask?branchID=%d&taskID=%d&step=%d',branchID, taskID, step)
			LuaModule.RunLuaRequest(str, o.UpdateBestTask )
		end
	end
	
	function o.UpdateBestTask( resultCode, content )
		if resultCode ~= RESULT_CODE.RESULT_COMMON_SUCCEED then
			HelperL.ShowMessage(TipType.FlowText, ResultCode:GetResultCodeStr(resultCode))
			return
		end
		HelperL.ShowMessage(TipType.FlowText, GetGameText(luaID, 8))
		local iter = string.gmatch(content, '(%d+)')
		local p1 = iter()
		local p2 = iter()
		local p3 = iter()
		EventManager:Fire(EventID.TaskPageUpdate, tonumber(p1),tonumber(p2))
		--EventManager:Fire(EventID.RefreshTaskCallBack, tonumber(p1), tonumber(p2), tonumber(p3) )
	end

	--发送接受任务请求
	function o.SendLuaAcceptTask( branchID, taskID, step, customCallBack)
		local taskScheme = Schemes.Task:Get(taskID, step)
		--local canAccept, result= o.CanAcceptTask(branchID, taskScheme ,_ ,true )
		-- HelperL.AddAMessageTip(HelperL.GetResultCodeStr(result))
		--if canAccept == false then HelperL.AddAMessageTip(HelperL.GetResultCodeStr(result)) return end
		local str = string.format('LuaRequestAcceptTask?branchID=%d&taskID=%d&step=%d',branchID, taskID, step)
		if customCallBack then
			LuaModule.RunLuaRequest(str,function (...)
				customCallBack(...)
				o.UpdateAcceptTask(...)
			end)
		else
			LuaModule.RunLuaRequest(str,o.UpdateAcceptTask)
		end
	end

	function o.SendLuaTurnInMultiPrizeTask(  branchID, taskID, step, customCallBack )
		local taskScheme = Schemes.Task:Get(taskID, step)
		if not taskScheme then return end
		local result = o.CanCompleteTask(branchID, taskScheme)
		if result then
			local str = format('LuaRequestTurnInTaskBranchMultiPrize?branchID=%d&taskID=%d&step=%d', branchID, taskID, step)
			if customCallBack then
				LuaModule.RunLuaRequest(str, function (...)
				customCallBack(...)
				o.AfterTurnInTask(...)
			end)
			else
				LuaModule.RunLuaRequest(str, o.AfterTurnInTask)
			end
		else
			HelperL.ShowMessage(TipType.FlowText, GetGameText(luaID,9))
		end
	end

	function o.UpdateAcceptTask( resultCode, content )
		if resultCode ~= RESULT_CODE.RESULT_COMMON_SUCCEED then
			HelperL.ShowMessage(TipType.FlowText, ResultCode:GetResultCodeStr(resultCode, content))
			return
		end
		local iter = string.gmatch(content, '(%d+)')
		local branchID = iter()
		local taskID = iter()
		local step = iter()

		-- print('返回')
		local task = o.GetItem(tonumber(branchID), tonumber(taskID))
		task:AfterAccept()
		if task.taskScheme.AcceptDramaID ~= 0 then
			GuideManager:TaskCameraShow(task.taskScheme.AcceptDramaID)
		end
		--HelperL.FlySprite('rw_00_5')
		EventManager:Fire(EventID.CheckGuideEvent,3,tonumber(branchID), tonumber(taskID), tonumber(step))
		EventManager:Fire(EventID.TaskPartUpdate, true)
		--EventManager:Fire(EventID.RefreshTaskCallBack, tonumber(branchID), tonumber(taskID), tonumber(step) )
		--UIManager.AcceptTask(tonumber(taskID), tonumber(step))
	end
	
	--- 发送立即完成任务请求
	function o.SendLuaFinishTaskRightNow( branchID, taskID, step )
		local taskItem = o.GetItem(branchID, taskID)
		local status = taskItem:GetClientStatus()
		if status == ECTaskStatus.CanAccept or
			status == ECTaskStatus.Accepted then
				HelperL.ShowMessage(TipType.FlowText, GetGameText(luaID, 10))
			return
		end
		local str = string.format('LuaRequestFinishTaskRightNow?branchID=%d&taskID=%d&step=%d',branchID, taskID, step)
		LuaModule.RunLuaRequest(str,o.UpdateFinshRNTask)
	end
	
	function o.UpdateFinshRNTask( resultCode, content )
		if resultCode ~= RESULT_CODE.RESULT_COMMON_SUCCEED then
			HelperL.ShowMessage(TipType.FlowText, ResultCode:GetResultCodeStr(resultCode)..content)
			return
		end
		local iter = string.gmatch(content, '(%d+)')
		local branchID = iter()
		local taskID = iter()
		local step = iter()
		EventManager:Fire(EventID.TaskPageUpdate,branchID,taskID)
		--EventManager:Fire(EventID.CheckGuideEvent)
	end

	function o.GetTurnInNPC( taskScheme )
		-- local turnInCountryID = o.CalcCountryID(taskScheme.GiveCountry)
		-- if turnInCountryID > eCountry.eCountry_Null and turnInCountryID < eCountry.eCountry_Max then
		-- 	local turnInNPC = tonumber(HelperL.Split(taskScheme.GiveNPC, ';')[turnInCountryID])
		-- 	if not turnInNPC then
		-- 		error('计算turnInNPC失败,任务ID'..taskScheme.ID)
		-- 	end
		-- 	return turnInNPC
		-- else
		-- 	return tonumber(taskScheme.GiveNPC)
		-- end
		local turnInNPC = o.GetNpcID(taskScheme.GiveNPC)
		return turnInNPC
	end

	function o.GetDoNPC( taskScheme )
		-- local doCountryID = o.CalcCountryID(taskScheme.WayCountry)
		-- if doCountryID > eCountry.eCountry_Null and doCountryID < eCountry.eCountry_Max then
		-- 	local doNPC = tonumber(HelperL.Split(taskScheme.WayNPC,';')[doCountryID])
		-- 	if not doNPC then
		-- 		error('计算waynpc失败,任务ID'..taskScheme.ID)
		-- 	end
		-- 	return doNPC
		-- else
		-- 	return tonumber(taskScheme.WayNPC)
		-- end
		local doNPCID = o.GetNpcID(taskScheme.WayNPC)
		return doNPCID
	end
	
	function o.GetAcceptNPC( taskScheme )
		-- local acceptCountryID = o.CalcCountryID (taskScheme.JudgeCountry)
		-- if acceptCountryID > eCountry.eCountry_Null and acceptCountryID < eCountry.eCountry_Max then
		-- 	local acceptNPC = tonumber(HelperL.Split(taskScheme.AcceptNPC,';')[acceptCountryID])
		-- 	assert(acceptNPC, '计算acceptNPC失败,任务ID：'..taskScheme.ID)
		-- 	return acceptNPC
		-- else
		-- 	return tonumber(taskScheme.AcceptNPC)
		-- end
		local acceptNPC = o.GetNpcID(taskScheme.AcceptNPC)
		return acceptNPC
	end
	
	--- 返回一个或多个npc处理的任务
	function o:GetNPCTaskStatus( npcID )
		if not npcID then return false end
		local npTskList = HelperL.NewQueue():New()
		for k,v in pairs(o.idToItem) do
			if v.status ~= TASK_STATUS.TASK_STATUS_COMPLETED then
				local npcTaskStatus = v:GetNPCStatus(npcID)
				if npcTaskStatus and npcTaskStatus ~= NPC_STATUS.DEFAULT then
					npTskList:pushLast(v)
				end
			end
		end
		if npTskList:Count() == 0 then
			return false
		else
			return true, npTskList
		end
	end --- npc task status function end

	--- 阵营判断
	function o.CalcCountryID( judge )
		assert(judge, "阵营判断为空")
		local mapID = WorldModule.mapId
		local mapScheme = Schemes.GameScene:Get(mapID)
		local actorCountryID = o.master.propertyLC:GetProperty(PLAYER_FIELD.PLAYER_FIELD_COUNTRY)
		local nowCountryID = mapScheme.Country
		if judge == 0 then return nowCountryID end

		if judge == 7 then return actorCountryID end
		if judge == 8 then
			if nowCountryID == actorCountryID then
				if nowCountryID < eCountry.eCountry_Max - 1 then
					return eCountry.eCountry_Max - 1
				else
					return 1
				end
			else
				return nowCountryID
			end
		end
		return judge
	end

	--玩家请求完成任务
	function o.SendLuaTurnInTask( branchID, taskID, step, callback )
		local taskScheme = Schemes.Task:Get(taskID, step)
		if not taskScheme then return end
		local result = o.CanCompleteTask(branchID, taskScheme)
		if result then
			local str = format('LuaRequestTurnInTask?branchID=%d&taskID=%d&step=%d', branchID, taskID, step)
			if callback then
				LuaModule.RunLuaRequest(str, function (...)
				callback(...)
				o.AfterTurnInTask(...)
			end)
			else
				LuaModule.RunLuaRequest(str, o.AfterTurnInTask)
			end
		else
			HelperL.ShowMessage(TipType.FlowText, GetGameText(luaID, 9))
		end
	end
	
	function o.AfterTurnInTask (resultCode, content)
		if resultCode ~= RESULT_CODE.RESULT_COMMON_SUCCEED then
				HelperL.ShowMessage(TipType.FlowText, ResultCode:GetResultCodeStr(resultCode))
			return
		end
		local iter = string.gmatch(content, '(%d+)')
		local branchID = tonumber(iter())
		local taskID = tonumber(iter())
		local step = tonumber(iter())
		local taskScheme = Schemes.Task:Get(taskID, step)
		if not taskScheme then return end
		if taskScheme.AccomplishDramaID ~= 0 then
			GuideManager:TaskCameraShow( taskScheme.AccomplishDramaID)
		end
		-- 还有任务可接则自动接受
		-- local npcID = NPCManager.lastTrigNpcID
		-- local result, tskList = o:GetNPCTaskStatus(npcID)
		-- if result then
		-- 	local nextTask = tskList:iter()

		-- 	local taskData = nextTask()
		-- 	if not taskData then return end
		-- 	local theFirstCanAccept = nil
		-- 	while taskData do
		-- 		local dataItem = o.GetItem(taskData.branchID, taskData.taskID)
		-- 		local status = dataItem:GetNPCStatus(npcID)
		-- 		if status == NPC_STATUS.CAN_ACCEPT then
		-- 			local taskScheme = Schemes.Task:Get(taskData.taskID, taskData.step)
		-- 			if taskScheme.GroupID == 100 then return end
		-- 			theFirstCanAccept = theFirstCanAccept or taskData
		-- 			if taskData.taskID == taskScheme.FollowTask then
		-- 				o.SetFocusTask(taskData)
		-- 				EntityModule.hero.navigationLG:NavigateToNPC(npcID)
		-- 				return
		-- 			end
		-- 		end
		-- 		taskData = nextTask()
		-- 	end
		-- 	if theFirstCanAccept then
		-- 		o.SetFocusTask(theFirstCanAccept)
		-- 		EntityModule.hero.navigationLG:NavigateToNPC(npcID)
		-- 	end
		-- end
	end
	
	--- 押镖任务
	function o.GetEscortTask(  )
		local acceptedTask = o.taskStatusList[TASK_STATUS.TASK_STATUS_ACCEPTED]
		for _,v in pairs(acceptedTask) do
			if v then
				local key = v
				local bID = key % 256
				local tID = math.floor(key / 256)
				local dataItem = o.GetItem(bID, tID)
				local taskScheme = dataItem.taskScheme
				if taskScheme.Type == TaskType.TaskType_Escort then
					return true,dataItem
				end
			end
		end
		return false
	end

	--- 时间自动完成任务类型
	function o.SendLuaTaskTimeTalk( branchID, taskID, step , customCallBack)
		local str = format('LuaRequestTaskTimeTalk?branchID=%d&taskID=%d&step=%d',branchID, taskID, step)
		LuaModule.RunLuaRequest(str, customCallBack)
	end

	function o.TimeTaskCallBack( resultCode, content )
		if resultCode ~= RESULT_CODE.RESULT_COMMON_SUCCEED then
				HelperL.ShowMessage(TipType.FlowText, ResultCode:GetResultCodeStr(resultCode))
			return
		end
		local iter = string.gmatch(content, '(%d+)')
		local branchID = tonumber(iter())
		local taskID = tonumber(iter())
		local step = tonumber(iter())
		local taskScheme = Schemes.Task:Get(taskID, step)
		if taskScheme.Type ~= TaskType.TaskType_TimePass and taskScheme.Type ~= TaskType.TaskType_TimeTalk then
			error(string.format('时间任务%d配置出错%d',taskID,taskScheme.Type))
		end
	end
	
	function o.UpdateCollectGoodTask()
		local acceptedTask = o.taskStatusList[TASK_STATUS.TASK_STATUS_ACCEPTED]
		for _,v in pairs(acceptedTask) do
			if v then
				local isEnough = false
				local key = v
				local bID = key % 256
				local tID = math.floor(key / 256)
				local dataItem = o.GetItem(bID, tID)
				if dataItem.taskScheme.Type == TaskType.TaskType_CollectGood or dataItem.taskScheme.Type == TaskType.TaskType_OperateCollectGood then
					dataItem:UpdateData()
				end
			end
		end
	end
	
	function o.UpdateEquipSmeltTask()
		local acceptedTask = o.taskStatusList[TASK_STATUS.TASK_STATUS_ACCEPTED]
		for _,v in pairs(acceptedTask) do
			if v then
				local key = v
				local bID = key % 256
				local tID = math.floor(key / 256)
				local dataItem = o.GetItem(bID, tID)
				if dataItem.taskScheme.Type == TaskType.TaskType_NewbieTarget or dataItem.taskScheme.Type == TaskType.TaskType_ActorPower then
					dataItem:UpdateData()
				end
			end
		end
	end
	
	function o.OnFlyTaskRequest(  )
		local curTask = EntityModule.hero.heroTaskLC.curFocusTask
		if not curTask then return end
		local status = curTask:GetClientStatus()
		local targetSceneID = 0
		local calcMap = o.CalcMapID
		local toPos = Vector3(0,0,0)
		local taskStatus = 0
		if status == ECTaskStatus.CanAccept then
			local mapID = o.GetMapID(curTask.taskScheme.AcceptMapID)
			targetSceneID = calcMap(0, mapID)
			toPos = HelperL.PBString2Vector3(curTask.taskScheme.AcceptPoint)
			taskStatus = 1
		elseif status == ECTaskStatus.Doing then
			if curTask.monsterMapID and curTask.pos then
				targetSceneID = curTask.monsterMapID
				toPos = curTask.pos
			else
				targetSceneID = calcMap(0, o.GetMapID(curTask.taskScheme.WayMapID))
				toPos = HelperL.PBString2Vector3(curTask.taskScheme.WayCoordinate)
			end
			taskStatus = 2
		elseif status == ECTaskStatus.CanComplete then
			local mapID = EntityModule.hero.heroTaskLC.GetMapID(curTask.taskScheme.GiveMapID)
			targetSceneID = calcMap(0, mapID)
			toPos = HelperL.PBString2Vector3(curTask.taskScheme.GivePoint)
			taskStatus = 3
		else
			return
		end

		HelperL.MapFlyAnimation(targetSceneID, toPos, nil, curTask.taskScheme.ID, taskStatus)
	end
	
	function o.SendFlyTaskRequest( toPos, mapID, taskID, taskStatus, callback )
		local curTask = o.curFocusTask
		local gameSheme = Schemes.GameScene:Get(mapID)
		if not gameSheme or gameSheme.MapType ~= 0 then
			return
		end
		taskID = taskID or 0
		taskStatus = taskStatus or 0
		EntityModule.ForceSyn()
		local reqStr = string.format('LuaRequestFlyToTaskPoint?x=%.2f&y=%.2f&z=%.2f&mapId=%d&nowMapID=%d&taskID=%d&taskSt=%d', toPos.x, toPos.y, toPos.z, mapID, WorldModule.mapId, taskID, taskStatus)
		LuaModule.RunLuaRequest(reqStr, function ( resultCode, content )
			if resultCode ~= RESULT_CODE.RESULT_COMMON_SUCCEED then
				EventManager:Fire(EventID.FlyMap)
			end
			if callback then
				callback(resultCode == RESULT_CODE.RESULT_COMMON_SUCCEED)
			end
		end)
	end
	function o.SendFlyAcitivityRequest( activityID )
		if not activityID or activityID == 0 then
			return
		end
		EntityModule.ForceSyn()
		LuaModule.RunLuaRequest(string.format('LuaRequestFlyActivityPoint?activityID=%d', activityID), 		function ( resultCode, content  )
			-- if resultCode ~= RESULT_CODE.RESULT_COMMON_SUCCEED then
				EventManager:Fire(EventID.FlyMap)
			-- end
		end)
	end
	function o.SetAutoTask( taskItem )
		o.autoDoLoopTask = true
		o.autoDoDailyTask = taskItem.taskScheme.GroupID
		o.SetFocusTask( taskItem)
	end
	
	function o.SetFocusTask( task )
		o.curFocusTask = task
		-- if task == nil then
		-- 	if UIManager.mainLandUI.UIInfiltrateEnemyAutoTask then
		-- 		o.autoDoDailyTask = 100
		-- 	else
		-- 		o.autoDoLoopTask = false
		-- 		o.autoDoDailyTask = -1
		-- 	end
		-- 	UIManager.mainLandUI.InfiltrateEnemyAutoAcceptTaskFunc = nil
		-- 	return
		-- end
		-- if task.taskScheme.GroupID == 16 or task.taskScheme.GroupID == 6 or task.taskScheme.GroupID == 4 or task.taskScheme.GroupID == 18 then
		-- else
		-- 	if UIManager.mainLandUI.UIInfiltrateEnemyAutoTask then
		-- 		o.autoDoDailyTask = 100
		-- 	else
		-- 		o.autoDoLoopTask = false
		-- 		o.autoDoDailyTask = -1
		-- 	end
		-- 	if task.taskScheme.GroupID ~= 100 then
		-- 		UIManager.mainLandUI.InfiltrateEnemyAutoAcceptTaskFunc = nil
		-- 	end
		-- end
	end
	
	function o.OnHeroFlyToDestination( scale, _afterFlyFunc )
		EventManager:UnSubscribe(EventID.FlyMap, HelperL.MapFlyAnimationChangeMapHandle)
		local naviComp = EntityModule.hero.navComp
		local tweenScale = naviComp.nav_mesh_agent_.gameObject:GetComponent('TweenScale')
		if not tweenScale then
			tweenScale = naviComp.nav_mesh_agent_.gameObject:AddComponent(GetType('TweenScale, Assembly-CSharp-firstpass'))
		end
		tweenScale:SetStartToCurrentValue()
		tweenScale.to = Vector3(1 * scale, 1 * scale, 1 * scale)
		tweenScale.duration = HelperL.HeroScaleDuration
		tweenScale.enabled = true
		tweenScale:ResetToBeginning()
		tweenScale:SetOnFinished( EventDelegate.Callback(function (  )
			if WorldModule.ectypeInfo.EctypeID == 0 then
				HelperL.LoadEctypeEffect( naviComp.nav_mesh_agent_.gameObject.transform )
			end
			if _afterFlyFunc then
				_afterFlyFunc()
			end
		end))
	end

	function o.GetCollectGoodTaskGoodID()
		local acceptedTask = o.taskStatusList[TASK_STATUS.TASK_STATUS_ACCEPTED]
		for _,v in pairs(acceptedTask) do
			if v then
				local isEnough = false
				local key = v
				local bID = key % 256
				local tID = math.floor(key / 256)
				local dataItem = o.GetItem(bID, tID)
				if dataItem ~= nil and dataItem.taskScheme.Type == TaskType.TaskType_CollectGood 
				and dataItem:GetClientStatus() ~= ECTaskStatus.CanComplete then
					if o.curFocusTask and o.curFocusTask.taskScheme.GroupID == dataItem.taskScheme.GroupID then
						return dataItem.taskScheme.Parameter1
					end
				end
			end
		end
		return 0
	end
	
	function o.SetCurFocusTask(task)
		o.curFocusTask = task
		EventManager:Fire(EventID.TaskPartUpdate)
	end
	
	function o.GetCurrentMainTaskID()
		for _,v in pairs(o.idToItem) do
			if v.taskScheme.Style == 1 then
				return v.taskScheme.ID
			end
		end
	end

	function o.GetCurrentMainMainTask()
		for _,v in pairs(o.idToItem) do
			if v.taskScheme.Style == 1 then
				return v
			end
		end
	end

	--EventManager:Subscribe(EventID.GoodChange, o.UpdateCollectGoodTask)
	--EventManager:Subscribe(EventID.AddPowerEffectEnd, o.UpdateEquipSmeltTask)

	return o
end

