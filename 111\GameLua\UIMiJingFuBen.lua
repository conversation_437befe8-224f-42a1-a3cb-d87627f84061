--[[
********************************************************************
    created:	2024/09/13
    author :	高
    purpose:    活动副本
*********************************************************************
--]]


local luaID = 'UIMiJingFuBen'

---秘境副本(暗月岛)
---@class UIMiJingFuBen:UIWndBase
local m = {}

local ectypeType = MiJingFuBen_EctypeType
local maxStage
--------------------------------------------------------------------
-- 订阅事件列表
--------------------------------------------------------------------
function m.GetOpenEventList()
    return {
        [EventID.LogicDataChange] = m.UpdateView,
        [EventID.EntitySyncBuff] = m.UpdateView,
        [EventID.OnGoodsPropChange] = m.UpdateView,
        [EventID.OnHeroPropChange] = m.UpdateView,
        [EventID.ActorDataChanged] = m.UpdateView,
    }
end

--------------------------------------------------------------------
-- 初始化
--------------------------------------------------------------------
function m.OnCreate()
    m.StageList = Schemes.CatMainStage:GetByFrontType(ectypeType)

    m.Item_Ectype_List = {}
    for i, v in ipairs(m.StageList) do
        m.Item_Ectype_List[i] = m.Creation_Item_Ectype(m.objList.Grid_Content, i, v)
        --m.Item_Ectype_List[i].UpdateView(v)
    end

    -- m.comResInfo = ComResInfo.New()
    -- m.comResInfo.BindView(m.objList.Img_Res)

    m.slotItemList = {}
    m.RegisterClickEvent()
    return true
end

--------------------------------------------------------------------
-- 创建副本栏
--------------------------------------------------------------------
function m.Creation_Item_Ectype(parent, index, cfg)
    ---@class Item_Ectype2
    local item = {}
    item.index = index
    item.cfg = cfg
    item.slotItemList = {}
    item.com = m:CreateSubItem(parent, m.objList.Item_Ectype)
    item.com.Btn_Challenge.onClick:AddListenerEx(function()
        m.ChuZheng()
    end)    
    AtlasManager:AsyncGetSprite(cfg.Icon, item.com.Img_Bg1)
    AtlasManager:AsyncGetSprite(cfg.Icon, item.com.Img_Bg2)
    ---更新界面
    ---@param cfg CatMainStageCfg
    item.UpdateView = function()
        item.com.Txt_Title1.text = item.cfg.Name
        item.com.Txt_Title2.text = item.cfg.Name
        item.com.Txt_Desc.text = item.cfg.Desc
        item.com.Img_Lock.gameObject:SetActive(false)
        if item.cfg.ID > maxStage + 1 then
            item.com.Img_Lock.gameObject:SetActive(true)
            item.com.Img_Bg1.gameObject:SetActive(true)
            item.com.Img_Bg2.gameObject:SetActive(false)
            item.com.Btn_Challenge.gameObject:SetActive(false)
        else
            if maxStage + 1 == item.cfg.ID then
                item.com.Img_Lock.gameObject:SetActive(false)
                item.com.Img_Bg1.gameObject:SetActive(false)
                item.com.Img_Bg2.gameObject:SetActive(true)
                item.com.Btn_Challenge.gameObject:SetActive(true)
            else
                item.com.Img_Lock.gameObject:SetActive(false)
                item.com.Img_Bg1.gameObject:SetActive(false)
                item.com.Img_Bg2.gameObject:SetActive(true)
                item.com.Btn_Challenge.gameObject:SetActive(false)
            end
        end

        local expendList = HelperL.Split(item.cfg.Need, ';')
        --消耗物品ID
        local expID = tonumber(expendList[1]) or 0
        --消耗物品数量
        local expNum = tonumber(expendList[2]) or 0
        --消耗物品ID2
        local expID2 = tonumber(expendList[4]) or 0
        --消耗物品数量2
        local expNum2 = tonumber(expendList[5]) or 0

        local count1 = SkepModule:GetGoodsCount(expID) or 0
        local count2 = SkepModule:GetGoodsCount(expID2) or 0
        if expID > 0 then
            local count1 = SkepModule:GetGoodsCount(expID)
            item.com.Img_ChallengeGray.gameObject:SetActive(count1 < expNum)
            item.com.Txt_Num.text = string.format(GetGameText(luaID, 4), count1)
            item.com.Txt_Num.gameObject:SetActive(true)
        else
            item.com.Txt_Num.gameObject:SetActive(false)
        end

        if expID2 > 0 then
            local count2 = SkepModule:GetGoodsCount(expID2)
            local color2 = count2 < expNum2 and "#FFA500" or "#ffffff"
            item.com.Txt_Expend.text = string.format("<color=%s>%s/%s</color>", color2, count2, expNum2)
            AtlasManager:AsyncGetGoodsSprite(expID2, item.com.Img_Expend)
            item.com.Txt_Expend.gameObject:SetActive(true)
        else
            item.com.Txt_Expend.gameObject:SetActive(false)
        end

        local prizeGoods = Schemes.CatMainStage:GetSucceedRewards(item.cfg.ID)
        local num = math.max(#prizeGoods, #item.slotItemList)
        for i = 1, num, 1 do
            if not item.slotItemList[i] then
                item.slotItemList[i] = _GAddSlotItem(item.com.Grid_Goods)
            end
            if prizeGoods[i] then
                item.slotItemList[i]:SetItemID(prizeGoods[i].ID)
                item.slotItemList[i]:SetCount(prizeGoods[i].Num)
                item.slotItemList[i]:SetActive(true)
            else
                item.slotItemList[i]:SetActive(false)
            end
        end

        item.com.gameObject:SetActive(true)
    end

    return item
end

--------------------------------------------------------------------
-- 窗口开启
--------------------------------------------------------------------
function m:OnOpen()    
    maxStage = GamePlayerData.GameEctype:GetProgress(ectypeType)
    if maxStage == 0 then
        maxStage = 150000
    end

    for i, v in ipairs(m.Item_Ectype_List) do
        v.UpdateView()
    end
end

--------------------------------------------------------------------
-- 注册点击事件
--------------------------------------------------------------------
function m.RegisterClickEvent()
    m:AddClick(m.objList.Btn_Close, function()
        m:CloseSelf()
    end)
end


--------------------------------------------------------------------
--更新界面
--------------------------------------------------------------------
function m.RefreshEctypeView(config)
    m.objList.Txt_Headline.text = config.Desc
    local expendList = HelperL.Split(config.Need, ';')
    --消耗物品ID
    local expID = tonumber(expendList[1]) or 0
    --消耗物品数量
    local expNum = tonumber(expendList[2]) or 0
    --广告物品ID
    -- local adID = tonumber(expendList[3]) or 0
    --消耗物品ID2
    local expID2 = tonumber(expendList[4]) or 0
    --消耗物品数量2
    local expNum2 = tonumber(expendList[5]) or 0

    local count1 = SkepModule:GetGoodsCount(expID) or 0
    local count2 = SkepModule:GetGoodsCount(expID2) or 0
    if expID > 0 then
        local count1 = SkepModule:GetGoodsCount(expID)
        m.objList.Img_ChallengeGray.gameObject:SetActive(count1 < expNum)
        m.objList.Txt_Num.text = string.format(GetGameText(luaID, 4), count1)
        m.objList.Txt_Num.gameObject:SetActive(true)
    else
        m.objList.Txt_Num.gameObject:SetActive(false)
    end
    m.objList.Txt_Content.text = config.Desc2

    if expID2 > 0 then
        local count2 = SkepModule:GetGoodsCount(expID2)
        local color2 = count2 < expNum2 and "#FFA500" or "#ffffff"
        m.objList.Txt_Expend.text = string.format("<color=%s>%s/%s</color>", color2, count2, expNum2)
        AtlasManager:AsyncGetGoodsSprite(expID2, m.objList.Img_Expend)
        m.objList.Txt_Expend.gameObject:SetActive(true)
    else
        m.objList.Txt_Expend.gameObject:SetActive(false)
    end

    local prizeGoods = Schemes.CatMainStage:GetSucceedRewards(config.ID)
    local num = math.max(#prizeGoods, #m.slotItemList)
    for i = 1, num, 1 do
        if not m.slotItemList[i] then
            m.slotItemList[i] = _GAddSlotItem(m.objList.Grid_Goods)
        end
        if prizeGoods[i] then
            m.slotItemList[i]:SetItemID(prizeGoods[i].ID)
            m.slotItemList[i]:SetCount(prizeGoods[i].Num)
            m.slotItemList[i]:SetActive(true)
        else
            m.slotItemList[i]:SetActive(false)
        end
    end
end

--------------------------------------------------------------------
-- 出征
--------------------------------------------------------------------
function m.ChuZheng()
    local stageID = GamePlayerData.GameEctype:GetChallengeableID(ectypeType)
    local cfg = Schemes.CatMainStage:Get(stageID)
    if not cfg then
        error('读取 CatMainStage 表失败 stageID=' .. stageID)
        return
    end

    local expendList = HelperL.Split(cfg.Need, ';')
    --消耗物品ID
    local expID = tonumber(expendList[1]) or 0
    --消耗物品数量
    local expNum = tonumber(expendList[2]) or 0
    --广告物品ID
    -- local adID = tonumber(expendList[3])
    --消耗物品ID2
    local expID2 = tonumber(expendList[4]) or 0
    --消耗物品数量2
    local expNum2 = tonumber(expendList[5]) or 0

    --判断挑战次数
    if HelperL.IsLackGoods(expID, expNum, false, false) then
        HelperL.ShowMessage(TipType.FlowText, GetGameText(luaID, 2))
        return
    end
    --消耗判断
    if HelperL.IsLackGoods(expID2, expNum2, false) then
        return
    end

    m:CloseSelf()
    -- -- 进入战斗
    BattleManager:EnterBattle(cfg.ID)
    --:DestroyWndByID(WndID.MiJingFuBen)
end

return m
