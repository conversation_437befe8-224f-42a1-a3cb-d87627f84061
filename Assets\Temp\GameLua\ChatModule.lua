--[[
********************************************************************
created:	2016/09/10
author :	张呈鹏
purpose:	聊天模块
*********************************************************************
--]]
require "ChatMessage_pb"
require "HelperL"
local luaID = ('ChatModule')

ChatModule = {}
ChatModule.currentChatPlayerInfo={}
ChatModule.worldMsg={}
ChatModule.currentChannel = nil
ChatModule.uiMniChatNameList={}
ChatModule.updateMsg=true
ChatModule.dragMsgCache={}
ChatModule.channelMsgLastTime={}

ChatModule.chatCache = {}
local chatCache = ChatModule.chatCache
local queue = HelperL.NewQueue()
ChatModule.ChatToPackage = false
local messageQueue = queue:New()
function ChatModule.Handle(action, data)
	--
	if action == ChatMessage_pb.MSG_CHAT_SENDCHAT then
		local m = ChatMessage_pb.SC_Char_SendChat()
		m:ParseFromString(data)
		--- 英雄没创建时，收到聊天消息，延迟处理
		if EntityModule.hero then
			ChatModule.SC_Char_SendChat(m)
		else
			messageQueue:pushLast(m)
		end
	end
end
function ChatModule.SC_Char_SendChat(m)
	ChatModule.currentChatPlayerInfo=m
	local serverCh = m.Channel
	if m.ChatType == CHAT_TYPE.RED_BAG then
		-- 初始化帮会红包
		local heroSocietyID = EntityModule.hero.societyLC.SocietyData.SocietyID
		if not heroSocietyID then return end
		LuaModule.RunLuaRequest(string.format('LuaRequestSendRedBagList?type=%d&param=%d', 2, heroSocietyID),function ()
				local flag = RedSpotManager.CheckSocietyRedBag()
				if flag then
					EventManager:Fire(EventID.NewRedBag)
				end
			end)
	end
	if serverCh < CHAT_CHANNEL.CHAT_CHANNEL_MAXID then
		--显示在屏幕中心的广播信息去除特殊字符显示
		local function EraseSpecialStr(str)
			local index, _ = string.find(str, '#!', 1)
			if index then
				str = string.sub(str, 1, index-1)
			end
			return str
		end
		local oldCotent = m.Content
		m.Content = EraseSpecialStr(m.Content)
		
		--print('m.Content = '..m.Content)
		ChatModule.BroadCastHandle(m.Channel ,m)
		local chatMsg = {}
		chatMsg.channel = serverCh
		chatMsg.clientChannel = serverCh
		chatMsg.receiveTime = HelperL.GetServerTime()
		chatMsg.timeLabelActive = false
		chatMsg.senderID = m.SenderID
		chatMsg.senderName = m.SenderName
		chatMsg.content = oldCotent
		chatMsg.vocation = m.SenderVocation
		chatMsg.country = m.SenderCountry

		-- 装备关联系统测试命令处理
		ChatModule.HandleEquipAssociationCommands(oldCotent)
		chatMsg.broadCastPos = m.BroadCastPos
		chatMsg.title = ChatModule.GetSocietyTitleByID(m.Title)
		chatMsg.uiminiContent=ChatModule.GetUIMiniChatNameColor( m.Channel ).. m.SenderName..':[-]'..m.Content
		chatMsg.senderNameFormat=ChatModule.GetUIMiniChatNameColor( m.Channel ).. m.SenderName..':[-]'
		chatMsg.answerFlag = m.AnswerFlag
		chatMsg.chatType=m.ChatType
		--if serverCh == CHAT_CHANNEL.CHAT_CHANNEL_WORLD then
		chatMsg.vip = m.VIP
		chatMsg.level = m.Level
		--end
		local contentInfo = HelperL.Split(chatMsg.content, "|")
		if chatMsg.chatType == CHAT_TYPE.VOICE then
			if HelperL.voiceInited then
				chatMsg.isNewMsg=true
				chatMsg.voiceID=contentInfo[1]
				chatMsg.voiceTime=math.floor(contentInfo[2])
				if contentInfo[3]=='' or contentInfo[3]==nil or Helper.GetStringLength(contentInfo[3]) > 30 then
					chatMsg.voiceContent=GetGameText(luaID, 12)
				else
					chatMsg.voiceContent=contentInfo[3]
				end
				chatMsg.content=GetGameText(luaID, 13)
			else
				chatMsg.chatType = 0
				chatMsg.content=GetGameText(luaID, 14)
			end
		end
		if ChatModule.channelMsgLastTime[serverCh] then
			local interval = chatMsg.receiveTime-ChatModule.channelMsgLastTime[serverCh]
			if interval>=120 then
				chatMsg.timeLabelActive = true
			end
		end
		ChatModule.channelMsgLastTime[serverCh]=chatMsg.receiveTime
		ChatModule.AddChatToCache(serverCh, chatMsg)
	end
end
function ChatModule.AddChatToCache(channel, msg)
	-- old_print("测试广播")
	--把阵营、组队、帮会频道消息放到世界频道队列里
	--[[if channel~=CHAT_CHANNEL.CHAT_CHANNEL_WORLD and channel ~= CHAT_CHANNEL.CHAT_CHANNEL_SYSTEM and channel~=CHAT_CHANNEL.CHAT_CHANNEL_COUNTRY then
	if channel == CHAT_CHANNEL.CHAT_CHANNEL_BROADCAST then
	--if msg.broadCastPos~=2 then--广播字段BroadcastPosition不是2的放到世界频道
	--chatCache[CHAT_CHANNEL.CHAT_CHANNEL_WORLD]=chatCache[CHAT_CHANNEL.CHAT_CHANNEL_WORLD] or queue:New()
	--chatCache[CHAT_CHANNEL.CHAT_CHANNEL_WORLD]:pushLast(msg)
	--if chatCache[CHAT_CHANNEL.CHAT_CHANNEL_WORLD]:Count() > 30 then
	--chatCache[CHAT_CHANNEL.CHAT_CHANNEL_WORLD]:popFirst()
	--end
	--end
	else
	chatCache[CHAT_CHANNEL.CHAT_CHANNEL_WORLD]=chatCache[CHAT_CHANNEL.CHAT_CHANNEL_WORLD] or queue:New()
	chatCache[CHAT_CHANNEL.CHAT_CHANNEL_WORLD]:pushLast(msg)
	if chatCache[CHAT_CHANNEL.CHAT_CHANNEL_WORLD]:Count() > 30 then
	chatCache[CHAT_CHANNEL.CHAT_CHANNEL_WORLD]:popFirst()
	end
	end
	end--]]
	if channel == CHAT_CHANNEL.CHAT_CHANNEL_GLOBALTRUMP then
		--local days = HelperL.GetServerOpenDays()
		--if days < 5 then
		--return
		--end
	end
	if channel == CHAT_CHANNEL.CHAT_CHANNEL_BROADCAST then
		--广播信息转化成系统信息显示
		channel = CHAT_CHANNEL.CHAT_CHANNEL_SYSTEM
		msg.channel= CHAT_CHANNEL.CHAT_CHANNEL_SYSTEM
	end
	chatCache[CHAT_CHANNEL.CHAT_CHANNEL_ALL]=chatCache[CHAT_CHANNEL.CHAT_CHANNEL_ALL] or queue:New()
	if channel ~= CHAT_CHANNEL.CHAT_CHANNEL_ALL and channel ~= CHAT_CHANNEL.CHAT_CHANNEL_SYSTEM then
		chatCache[CHAT_CHANNEL.CHAT_CHANNEL_ALL]:pushLast(msg)--把不是全部频道和系统频道的信息放到全部频道队列里
	end
	if chatCache[CHAT_CHANNEL.CHAT_CHANNEL_ALL]:Count() > 30 then
		chatCache[CHAT_CHANNEL.CHAT_CHANNEL_ALL]:popFirst()
	end

	if not ChatModule.updateMsg then
		ChatModule.dragMsgCache[ChatModule.currentChannel] = ChatModule.dragMsgCache[ChatModule.currentChannel] or queue:New()
		if ChatModule.currentChannel==CHAT_CHANNEL.CHAT_CHANNEL_WORLD then
			ChatModule.dragMsgCache[CHAT_CHANNEL.CHAT_CHANNEL_WORLD]:pushLast(msg)
		else
			if ChatModule.currentChannel==channel then
				ChatModule.dragMsgCache[ChatModule.currentChannel]:pushLast(msg)
			end
		end
		if msg.channel~=CHAT_CHANNEL.CHAT_CHANNEL_SYSTEM then
			ChatModule.dragMsgCache[CHAT_CHANNEL.CHAT_CHANNEL_ALL]:pushLast(msg)
		end
		--local newLockMsgNum = ChatModule.dragMsgCache[ChatModule.currentChannel]:Count()
		--if ChatModule.currentChannel==CHAT_CHANNEL.CHAT_CHANNEL_ALL and msg.channel~=CHAT_CHANNEL.CHAT_CHANNEL_SYSTEM then
			--newLockMsgNum = ChatModule.dragMsgCache[CHAT_CHANNEL.CHAT_CHANNEL_ALL]:Count()
			--EventManager:Fire(EventID.ReflashNewLockMsg, newLockMsgNum)
		--elseif msg.channel==ChatModule.currentChannel then
			--EventManager:Fire(EventID.ReflashNewLockMsg, newLockMsgNum)
		--end
		--print('新信息',ChatModule.dragMsgCache[ChatModule.currentChannel]:Count())
	end

	chatCache[channel] = chatCache[channel] or queue:New()
	chatCache[channel]:pushLast(msg)
	if chatCache[channel]:Count() > 30 then
		chatCache[channel]:popFirst()
	end
	if ChatModule.updateMsg then
		EventManager:Fire(EventID.ChatModule_AddChat, msg)
	end
	--EventManager:Fire(EventID.ChatModuleMiniAddChat, msg)

	--if channel == CHAT_CHANNEL.CHAT_CHANNEL_SOCIETY or channel == CHAT_CHANNEL.CHAT_CHANNEL_TEAM
		--or channel == CHAT_CHANNEL.CHAT_CHANNEL_SCENE or channel == CHAT_CHANNEL.CHAT_CHANNEL_WARBAND then
		--EventManager:Fire(EventID.Chatbubble, msg)
	--end
end

--广播
ChatModule.ChannelStr = { GetGameText(luaID, 1), GetGameText(luaID, 2), GetGameText(luaID, 3), GetGameText(luaID, 4), GetGameText(luaID, 5) }
ChatModule.horn_list_ = {}
ChatModule.cast_list_ = {}
ChatModule.hornMsgList= {}
ChatModule.horn_pos_ = Vector3.zero
function ChatModule.BroadCastHandle(channel ,message)
	local hero = EntityModule.hero
	if not hero then return end
	--print('channel = '..channel)
	--HelperL.DumpNew('ChatModule',message)
	--if HelperL.GameID==9 then return end--韩服屏蔽广播
	--local heroLV = hero:GetProperty(CREATURE_FIELD.CREATURE_FIELD_LEVEL)
	--if heroLV<5 then return end--玩家5级以下不播广播
	
	if channel == CHAT_CHANNEL.CHAT_CHANNEL_BROADCAST or channel == CHAT_CHANNEL.CHAT_CHANNEL_TRUMPET then
		if message.BroadCastPos < 2 then
			local chatContent = message.SenderName .. ":"  .. message.Content
			--local index = 1
			--while true do
				--local c = string.sub(chatContent,index,index)
				--local byte = string.byte(c)
				--local char = nil
				--if byte then
					--if byte <= 192 then
						--char = string.sub(chatContent,index,index)
						--if char == "#" then
							--local symbol = string.sub(chatContent,index,index+1)
							-------------------------物品信息解析-----------------------
							--if symbol == "#:" then
								--local xIndex = string.find(chatContent,"x",index)
								--if xIndex > 0 then
									--local equipSchemeItem = nil
									--local color = nil
									--local goodsID = tonumber(string.sub(chatContent,index+2,index+7))
									--if goodsID > DEFINE.MAX_MEDICAMENT_ID then
										--equipSchemeItem = Schemes.Equipment.Get(goodsID) --GoodsName
										--color = HelperL.GetColorValueByQuality(equipSchemeItem.QualityLevel)
									--else
										--equipSchemeItem = Schemes.Medicament.Get(goodsID)
										--color = HelperL.GetColorValueByQuality(equipSchemeItem.Quality)
										--if equipSchemeItem.GoodsName == GetGameText(0, 1) or equipSchemeItem.GoodsName == GetGameText(0, 2) or equipSchemeItem.GoodsName == GetGameText(0, 3) then
											--color='[ffff00]'
										--end
									--end
									--local needRep = string.sub(chatContent,index,xIndex-1)
									----local repCont = color .. " [u]" .. equipSchemeItem.GoodsName .. "[/u] [-]"
									--local repCont = color .. equipSchemeItem.GoodsName .. "[-]"
									--chatContent = string.gsub(chatContent, needRep, repCont)
									--index = string.find(chatContent,"x",index)
								--end
							--end
						--end
					--end
					--index = index + 1
				--else
					--break
				--end
			--end
			table.insert(ChatModule.horn_list_, chatContent)
			table.insert(ChatModule.hornMsgList, message)
		elseif message.BroadCastPos == 2 then
			--local content = string.gsub(message.Content, "#.+", "")
			--table.insert(ChatModule.cast_list_, content)
			--EventManager:Fire(EventID.BroadCastNew)
		end
	end
end

function ChatModule.CastServerChannel( serverCh )
	if serverCh == CHAT_CHANNEL.CHAT_CHANNEL_BROADCAST then
		return ECChatChannel.ChatChannel_System
	elseif serverCh == CHAT_CHANNEL.CHAT_CHANNEL_SYSTEM then
		return ECChatChannel.ChatChannel_System
	elseif serverCh == CHAT_CHANNEL.CHAT_CHANNEL_TRUMPET then
		return ECChatChannel.ChatChannel_Horn
	elseif serverCh == CHAT_CHANNEL.CHAT_CHANNEL_WORLD then
		return ECChatChannel.ChatChannel_All
	elseif serverCh == CHAT_CHANNEL.CHAT_CHANNEL_SOCIETY then
		return ECChatChannel.ChatChannel_Society
	elseif serverCh == CHAT_CHANNEL.CHAT_CHANNEL_COUNTRY then
		return ECChatChannel.ChatChannel_Country
	end
end

-- 帮会职位名
ChatModule.societyTitleName =
{
	GetGameText(luaID, 8),--帮众
	GetGameText(luaID, 11),--长老
	GetGameText(luaID, 10),--副帮主
	GetGameText(luaID, 9),--帮主
	GetGameText(luaID, 8),--护法
	GetGameText(luaID, 8),--精英
}
function ChatModule.GetSocietyTitleByID( titleID )
	if titleID then
		if titleID==1 or titleID==2 or titleID==3 or titleID==4 then
			return ChatModule.societyTitleName[titleID]
		end
	end
	return nil
end
function ChatModule.GetUIMiniChatNameColor( channelType )
	local nameColor = ''
	if channelType==CHAT_CHANNEL.CHAT_CHANNEL_BROADCAST then --系统广播
		--channelIcon='ZJMliaotian02_6'
	elseif channelType==CHAT_CHANNEL.CHAT_CHANNEL_SYSTEM then --系统频道
		nameColor='[e71f19]'
	elseif channelType==CHAT_CHANNEL.CHAT_CHANNEL_TRUMPET then --小喇叭
		--channelIcon='ZJMliaotian02_6'
	elseif channelType==CHAT_CHANNEL.CHAT_CHANNEL_WORLD then --世界频道
		nameColor='[ff7b00]'
	elseif channelType==CHAT_CHANNEL.CHAT_CHANNEL_SOCIETY then --公会频道
		nameColor='[00ff00]'
	elseif channelType==CHAT_CHANNEL.CHAT_CHANNEL_COUNTRY then --国家频道
		nameColor='[ff00ff]'
	elseif channelType==CHAT_CHANNEL.CHAT_CHANNEL_TEAM then --队伍
		nameColor='[00ffff]'
	elseif channelType==CHAT_CHANNEL.CHAT_CHANNEL_WARBAND then --战队
		nameColor='[e71f19]'
	elseif channelType==CHAT_CHANNEL.CHAT_CHANNEL_MAXID then --其他
	end
	return nameColor
end

function ChatModule.GetUIMiniChatNameColorByChannel( channelType )
	local nameColor = Color(1,1,1,1)
	if channelType==CHAT_CHANNEL.CHAT_CHANNEL_BROADCAST then --系统广播
		--channelIcon='ZJMliaotian02_6'
	elseif channelType==CHAT_CHANNEL.CHAT_CHANNEL_SYSTEM then --系统频道
		nameColor=Color(231/255,31/255,25/255,1)
	elseif channelType==CHAT_CHANNEL.CHAT_CHANNEL_TRUMPET then --小喇叭
		--channelIcon='ZJMliaotian02_6'
	elseif channelType==CHAT_CHANNEL.CHAT_CHANNEL_WORLD then --世界频道
		nameColor=Color(255/255,123/255,0,1)
	elseif channelType==CHAT_CHANNEL.CHAT_CHANNEL_SOCIETY then --公会频道
		nameColor=Color(0,1,0,1)
	elseif channelType==CHAT_CHANNEL.CHAT_CHANNEL_COUNTRY then --国家频道
		nameColor=Color(1,0,1,1)
	elseif channelType==CHAT_CHANNEL.CHAT_CHANNEL_TEAM then --队伍
		nameColor=Color(0,1,1,1)
	elseif channelType==CHAT_CHANNEL.CHAT_CHANNEL_MAXID then --其他
	end
	return nameColor
end

function ChatModule.SendMsgLevelLimilt(  )
	local level = EntityModule.hero.propertyLC:GetProperty(CREATURE_FIELD.CREATURE_FIELD_LEVEL)
	local vip = EntityModule.hero.propertyLC:GetProperty(PLAYER_FIELD.PLAYER_FIELD_VIPLEVEL)
	local needLevel = Schemes.ConstValue.Get(CONST_VALUE.CONST_WORLD_CHAT_LEVEL)
	local needVip = Schemes.ConstValue.Get(CONST_VALUE.CONST_CHAT_VIPLEVEL)
	if not needLevel or not needVip then return true end
	if level>=needLevel.Value or vip>=needVip.Value then
		return true
	else
		local _,_,str = HelperL.CalculatePlayerLoopNum(level)
		return false,str
	end
end

function ChatModule.PlayerCanChat(channel)--聊天讲话限制条件
	if channel == CHAT_CHANNEL.CHAT_CHANNEL_WORLD then
		local limitSend = ChatModule.SendMsgLevelLimilt(  )
		if not limitSend then
			return false
		end
	elseif channel == CHAT_CHANNEL.CHAT_CHANNEL_SOCIETY then
		local society = EntityModule.hero.propertyLC:GetProperty(PLAYER_FIELD.PLAYER_FIELD_SOCIETY
		)
		if society == 0 then
			return false
		end
	elseif channel == CHAT_CHANNEL.CHAT_CHANNEL_TEAM then
		local teamID = EntityModule.GetTeamID(EntityModule.hero.uid)
		if teamID == 0 then
			return false
		end
	elseif channel == CHAT_CHANNEL.CHAT_CHANNEL_COUNTRY then
		local countryID = EntityModule.hero.propertyLC:GetProperty(PLAYER_FIELD.PLAYER_FIELD_COUNTRY)
		if countryID == 0 then
			return false
		end
		local limitSend = ChatModule.SendMsgLevelLimilt(  )
		if not limitSend then
			return false
		end
	elseif channel == CHAT_CHANNEL.CHAT_CHANNEL_SYSTEM then
		return false
	elseif channel == CHAT_CHANNEL.CHAT_CHANNEL_ROOM then
		local roomInfo = PlayRoomDataCenter.GetRoomInfo()
		if roomInfo.roomID > 0 then
			return true
		end
		return false
	elseif channel == CHAT_CHANNEL.CHAT_CHANNEL_HELP then
		return true
	elseif channel == CHAT_CHANNEL.CHAT_CHANNEL_ALL then
		local limitSend = ChatModule.SendMsgLevelLimilt(  )
		if not limitSend then
			return false
		end
	end
	return true
end

function ChatModule.SendChatMsg(channel, inputStr, chatType)
	if inputStr == nil or inputStr == "" then HelperL.AddAMessageTip(GetGameText(luaID, 6)) return end
	if channel <= CHAT_CHANNEL.CHAT_CHANNEL_NULL or channel >= CHAT_CHANNEL.CHAT_CHANNEL_MAXID then return end
	local msg = ChatMessage_pb.CS_Char_SendChat()
	chatType = chatType or 0
	msg.Channel = channel
	msg.Content = inputStr
	msg.ChatType = chatType
	Premier.Instance:GetNetwork():SendFromLua(ENDPOINT.ENDPOINT_GAMECLIENT,
		ENDPOINT.ENDPOINT_GAMESERVER,
		MSG_MODULEID.MSG_MODULEID_CHAT,
		ChatMessage_pb.MSG_CHAT_SENDCHAT,
		msg:SerializeToString())
end
function ChatModule.LateHandle( )
	local function PopOutMsg(  )
		while true do
			local m = messageQueue:popFirst()
			if not m then break end
			ChatModule.SC_Char_SendChat(m)
		end
	end
	local ChatMsgLateTimer = Timer.New(PopOutMsg, 0.5, 1)
	ChatMsgLateTimer:Start()
	ChatMsgLateTimer=nil
end

function ChatModule.SystemSendSocietyChat( msg)
	local m = {}
	m.Channel = CHAT_CHANNEL.CHAT_CHANNEL_SOCIETY
	m.SenderID = 0
	m.SenderName = GetGameText(luaID, 7)
	m.Content = msg
	m.BroadCastPos = 0
	m.BroadCastSubPos = 0
	m.BroadCastParam = 0
	m.Level = 0
	m.VIP = 0
	m.ChatType = 0
	m.SenderVocation = 0
	ChatModule.SC_Char_SendChat(m)
end
--------------------------------------------------------------------
-- 装备关联系统测试命令处理
--------------------------------------------------------------------
function ChatModule.HandleEquipAssociationCommands(content)
    if not content or type(content) ~= "string" then
        return
    end

    -- 确保测试命令模块已加载
    if not EquipAssociationTestCommands then
        require("EquipAssociationTestCommands")
    end

    -- 处理装备关联测试命令
    if content == "/testequipassoc" then
        EquipAssociationTestCommands.TestEquipAssociation()
    elseif content == "/viewcurequip" then
        EquipAssociationTestCommands.ViewCurrentEquip()
    elseif string.find(content, "^/forceequipcheck") then
        local parts = HelperL.Split(content, " ")
        local triggerType = parts[2] or "manual"
        EquipAssociationTestCommands.ForceEquipCheck(triggerType)
    elseif string.find(content, "^/viewequipassoc") then
        local parts = HelperL.Split(content, " ")
        local equipID = parts[2]
        if equipID then
            EquipAssociationTestCommands.ViewEquipAssociation(equipID)
        else
            print("11111[TestCommands] 用法: /viewequipassoc [装备ID]")
        end
    elseif string.find(content, "^/testcircular") then
        local parts = HelperL.Split(content, " ")
        local equipID1 = parts[2]
        local equipID2 = parts[3]
        if equipID1 and equipID2 then
            EquipAssociationTestCommands.TestCircularAssociation(equipID1, equipID2)
        else
            print("11111[TestCommands] 用法: /testcircular [装备ID1] [装备ID2]")
        end
    elseif content == "/clearequipcache" then
        EquipAssociationTestCommands.ClearEquipCache()
    end
end

--EventManager:Subscribe(EventID.HeroEntered, ChatModule.LateHandle)
Premier.Instance:RegisterLuaModule(MSG_MODULEID.MSG_MODULEID_CHAT, 'ChatModule.Handle')
