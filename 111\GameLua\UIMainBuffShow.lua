-- local luaID = ('UIMainBuffShow')
local UIMainBuffShow = {
    m_ImageList = {},
    EntityList = {},
    m_ImageBGList = {},
    Img_reddot = nil,
}

-- 订阅事件列表
function UIMainBuffShow:GetOpenEventList()
    return {
        [EventID.LogicDataChange] = self.OnLogicDataChange,
        [EventID.UseMainBuff] = self.UseMainBuff
    }
end

function UIMainBuffShow.OnLogicDataChange(k, v)
    local self = UIMainBuffShow
    if k == LOGIC_DATA.DATA_SEVEN_COLOR_EGGS_DRAW_TIMES or k == LOGIC_DATA.DATA_SEVEN_COLOR_EGGS_CLICK_TIME then

    elseif k == LOGIC_DATA.DATA_GUESS_FINGER_COUNT or k == LOGIC_DATA.DATA_NEWBIE_TARGETPRIZE_HAVEGET4 then

    end
end

function UIMainBuffShow.UseMainBuff()
    --UIMainBuffShow:ShowBuff()
end

function UIMainBuffShow:New(luaObj, gameObject)
    if gameObject then
        self.gameObject = gameObject
    end

    local parentTrans = self.gameObject.transform:Find('root')
    self.btn_bg = parentTrans:Find('Btn_bg'):GetComponent('Button')
    self.btn_bg.onClick:RemoveAllListeners()
    self.btn_bg.onClick:AddListenerEx(function()
        UIMainBuffShow.OnClickUI()
    end)
    --self.Img_reddot = self.gameObject.transform:Find('root/Img_reddot').gameObject
    self.m_ImageList = {}
    local EquipmentData = Schemes.Equipment:GetByPacketID()[18]
    local index = 0
    for i, v in pairs(EquipmentData) do
        if v.ID > 600050 and v.ID < 600054 then
            local cardSkep = SkepModule:GetSkepByID(SKEPID.SKEPID_MOUNTEQUIPPACKET)
            if cardSkep then
                local entity = cardSkep:GetEntityByGoodsID(v.ID)
                if entity then
                    table.insert(self.EntityList, entity)
                end
            else
                print("cardSkep is nil")
            end
        end
    end
    table.insert(self.m_ImageList, self.btn_bg.transform:Find('Image').gameObject:GetComponent('Image'))
    table.insert(self.m_ImageList, self.btn_bg.transform:Find('Image1').gameObject:GetComponent('Image'))
    table.insert(self.m_ImageList, self.btn_bg.transform:Find('Image2').gameObject:GetComponent('Image'))
    table.insert(self.m_ImageBGList, self.btn_bg.transform:Find('Image_1').gameObject:GetComponent('Image'))
    table.insert(self.m_ImageBGList, self.btn_bg.transform:Find('Image_2').gameObject:GetComponent('Image'))
    table.insert(self.m_ImageBGList, self.btn_bg.transform:Find('Image_3').gameObject:GetComponent('Image'))
    EventManager:UnSubscribe(EventID.UseMainBuff, UIMainBuffShow.UseMainBuff)
    EventManager:Subscribe(EventID.UseMainBuff, UIMainBuffShow.UseMainBuff)
    local addtime = Timer.New(function()
        UIMainBuffShow:ShowBuff()
    end, 1, -1, true)
    addtime:Start()
    self.RedDot = RedDotManager:SetRedDot(parentTrans:Find('BuffUI'), CachedVector2:Set(0, 0))
    self.RedDot:AddCheckParam(WndID.Buff)
    return true
end

function UIMainBuffShow:ShowBuff()
    if not EntityModule.hero then
        return
    end
    for i = 1, #self.m_ImageList do
        self.m_ImageList[i].color = Color.gray
        local commonText = Schemes.CommonText:Get(99 + i)
        local sprite
        if EntityModule.hero.buffLC:HasBuff(commonText.Param1) and EntityModule.hero.buffLC:GetBuffLeftTime(commonText.Param1) > 0 then
            self.m_ImageList[i].color = Color.white
            sprite = 'kuang_2'
        else
            self.m_ImageList[i].color = Color.gray
            sprite = 'kuang_0'
        end
        AtlasManager:AsyncGetSprite(sprite, self.m_ImageBGList[i])
    end
end

function UIMainBuffShow:CheckRed()
    for i = 1, 3 do
        local commonText = Schemes.CommonText:Get(99 + i)
        if EntityModule.hero.buffLC:HasBuff(commonText.Param1) and EntityModule.hero.buffLC:GetBuffLeftTime(commonText.Param1) > 0 then
            return true
        end
    end
    return false
end

function UIMainBuffShow:OnOpen()

end

function UIMainBuffShow.OnClickUI()
    UIManager:OpenWnd(WndID.Buff)
end

return UIMainBuffShow
