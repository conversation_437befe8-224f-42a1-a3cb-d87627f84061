// ReSharper disable InconsistentNaming
// ReSharper disable SuggestVarOrType_SimpleTypes
// ReSharper disable SuggestVarOrType_Elsewhere
// ReSharper disable SuggestVarOrType_BuiltInTypes
// ReSharper disable ArrangeObjectCreationWhenTypeNotEvident

using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Threading;

using Apq.ChangeBubbling;
using Apq.Unity3D.Extension;
using Apq.Unity3D.UnityHelpers;
using Apq.Web5.JsonReturn;

using CsvTables;

using Cysharp.Threading.Tasks;

using DTO;

using Newtonsoft.Json;

using Props;

using UnityEngine;

using X;
using X.PB;

namespace View
{
    /// <summary>
    /// 全局管理器
    /// </summary>
    [DisallowMultipleComponent]
    public class GlobalMgr : MonoBehaviour
    {
        /// <summary>
        /// 是否使用本地时间
        /// </summary>
        public bool UseLocalTime { get; set; }

        /// <summary>
        /// 服务器时间
        /// </summary>
        public ServerTimeDTO ServerTime { get; set; } = new();
        /// <summary>
        /// 获取到服务器时间时的客户端时间
        /// </summary>
        public DateTime ClientTime { get; set; } = DateTime.Now;

        #region CommonProp配置表

        /// <summary>
        /// 读取CommonProp配置表
        /// </summary>
        public Dictionary<int, CommonProp> CommonPropCfg { get; } = new();

        /// <summary>
        /// 加载CommonProp配置表时每一帧最多加载的行数
        /// </summary>
        public int MaxRowsPerFrame { get; set; } = 100;

        /// <summary>
        /// CommonProp配置表是否已加载完成
        /// </summary>
        public BubblingList<bool> CommonPropCfgLoaded { get; } = new(nameof(CommonPropCfgLoaded));

        /// <summary>
        /// 重新加载CommonProp配置表
        /// </summary>
        /// <returns></returns>
        public async UniTask<Dictionary<int, CommonProp>> ReloadCommonProp()
        {
            CommonPropCfgLoaded.Value = false;
            var csvTable = SingletonMgr.Instance.CsvLoaderMgr.GetOrAddLoader<CommonPropCsv>().Pb.CSVTable;
            CommonPropCfg.Clear();

            for (var i = 0; i < csvTable.Count; i++)
            {
                CommonPropCfg.Add(csvTable[i].Id, new CommonProp().SetRowData(csvTable[i]));

                if ((i + 1) % MaxRowsPerFrame == 0)
                {
                    await UniTask.NextFrame();
                }
            }

            CommonPropCfgLoaded.Value = true;

            return CommonPropCfg;
        }

        /// <summary>
        /// 缓存附着于怪物的通用属性(含枪)
        /// </summary>
        private Dictionary<int, List<CommonProp>> MonsterProps { get; } = new();

        /// <summary>
        /// 获取并缓存附着于怪物的通用属性
        /// </summary>
        /// <param name="csvTable_Monster">确保缓存中有附着于这些怪物的属性</param>
        public Dictionary<int, List<CommonProp>> GetMonsterPropIds(
            params BattleBrushEnemy.Item[] csvTable_Monster)
        {
            foreach (var csvRow_Monster in csvTable_Monster)
            {
                if (!MonsterProps.TryGetValue(csvRow_Monster.Id, out _))
                {
                    var lst = CommonPropCfg.Values.FindPropsCanAttachTo(csvRow_Monster, PropCatalog.Inherent);

                    // 将BattleBrushEnemy的属性 转为通用属性
                    // 移动方法
                    lst.Add(new CommonProp
                    {
                        AttachedType = AttachedType.Monster,
                        AttachTo = new List<int> { csvRow_Monster.Id },
                        PropCatalog = PropCatalog.Inherent,
                        PropType = PropType.MoveMethod,
                        ValueType = CsValueType.Long,
                        ApplyType = ApplyType.Attached,
                    }.SetLong((int)MoveMethod.RigidBodyMove));
                    // 移动方向
                    lst.Add(new CommonProp
                    {
                        AttachedType = AttachedType.Monster,
                        AttachTo = new List<int> { csvRow_Monster.Id },
                        PropCatalog = PropCatalog.Inherent,
                        PropType = PropType.HowMoveDir,
                        ValueType = CsValueType.Long,
                        ApplyType = ApplyType.Attached,
                    }.SetLong((int)DirMethod.Straight));
                    // 攻击
                    lst.Add(new CommonProp
                    {
                        AttachedType = AttachedType.Monster,
                        AttachTo = new List<int> { csvRow_Monster.Id },
                        PropCatalog = PropCatalog.Inherent,
                        PropType = PropType.Attack,
                        ValueType = CsValueType.Double,
                        ApplyType = ApplyType.Attached,
                    }.SetDouble(csvRow_Monster.PhysicsAttack));
                    // 最大Hp
                    lst.Add(new CommonProp
                    {
                        AttachedType = AttachedType.Monster,
                        AttachTo = new List<int> { csvRow_Monster.Id },
                        PropCatalog = PropCatalog.Inherent,
                        PropType = PropType.MaxHp,
                        ValueType = CsValueType.Double,
                        ApplyType = ApplyType.Attached,
                    }.SetDouble(csvRow_Monster.Hp));
                    // 质量
                    lst.Add(new CommonProp
                    {
                        AttachedType = AttachedType.Monster,
                        AttachTo = new List<int> { csvRow_Monster.Id },
                        PropCatalog = PropCatalog.Inherent,
                        PropType = PropType.Mass,
                        ValueType = CsValueType.Double,
                        ApplyType = ApplyType.Attached,
                    }.SetDouble(csvRow_Monster.Mass));
                    // 半径
                    lst.Add(new CommonProp
                    {
                        AttachedType = AttachedType.Monster,
                        AttachTo = new List<int> { csvRow_Monster.Id },
                        PropCatalog = PropCatalog.Inherent,
                        PropType = PropType.Radius,
                        ValueType = CsValueType.Double,
                        ApplyType = ApplyType.Attached,
                    }.SetDouble(csvRow_Monster.Radius));
                    // 移动速度
                    lst.Add(new CommonProp
                    {
                        AttachedType = AttachedType.Monster,
                        AttachTo = new List<int> { csvRow_Monster.Id },
                        PropCatalog = PropCatalog.Inherent,
                        PropType = PropType.Speed,
                        ValueType = CsValueType.Double,
                        ApplyType = ApplyType.Attached,
                    }.SetDouble(Globals.UnityValueTransform(csvRow_Monster.MoveSpeed)));
                    // 护甲
                    lst.Add(new CommonProp
                    {
                        AttachedType = AttachedType.Monster,
                        AttachTo = new List<int> { csvRow_Monster.Id },
                        PropCatalog = PropCatalog.Inherent,
                        PropType = PropType.Armor,
                        ValueType = CsValueType.Double,
                        ApplyType = ApplyType.Attached,
                    }.SetDouble(Globals.UnityValueTransform(csvRow_Monster.Armor)));
                    // 护甲回升
                    lst.Add(new CommonProp
                    {
                        AttachedType = AttachedType.Monster,
                        AttachTo = new List<int> { csvRow_Monster.Id },
                        PropCatalog = PropCatalog.Inherent,
                        PropType = PropType.ArmorRally,
                        ValueType = CsValueType.Double,
                        ApplyType = ApplyType.Attached,
                    }.SetDouble(Globals.UnityValueTransform(csvRow_Monster.ArmorRally)));
                    // 死亡后的重生个数
                    lst.Add(new CommonProp
                    {
                        AttachedType = AttachedType.Monster,
                        AttachTo = new List<int> { csvRow_Monster.Id },
                        PropCatalog = PropCatalog.Inherent,
                        PropType = PropType.MaxRebirthTimes,
                        ValueType = CsValueType.Long,
                        ApplyType = ApplyType.Attached,
                    }.SetLong(2));

                    // var csvRow_Gun = SingletonMgr.Instance.CsvLoaderMgr.GetOrAddLoader<GunCsv>()
                    //     .Dic[csvRow_Monster.SkillID];
                    // // 将GunCfg.csv中的(部分)列 转为通用属性
                    // // ShootMethod
                    // lst.Add(new CommonProp
                    // {
                    //     AttachedType = AttachedType.Monster,
                    //     AttachTo = new List<int> { csvRow_Monster.Id },
                    //     PropCatalog = PropCatalog.Inherent,
                    //     PropType = PropType.ShootMethod,
                    //     ValueType = CsValueType.Long,
                    //     ApplyType = ApplyType.Attached,
                    // }.SetLong((int)csvRow_Gun.ShootMethod));
                    // // BulletId
                    // lst.Add(new CommonProp
                    // {
                    //     AttachedType = AttachedType.Monster,
                    //     AttachTo = new List<int> { csvRow_Monster.Id },
                    //     PropCatalog = PropCatalog.Inherent,
                    //     PropType = PropType.BulletId,
                    //     ValueType = CsValueType.Long,
                    //     ApplyType = ApplyType.Attached,
                    // }.SetLong(csvRow_Gun.BulletId));
                    // // MinCD
                    // lst.Add(new CommonProp
                    // {
                    //     AttachedType = AttachedType.Monster,
                    //     AttachTo = new List<int> { csvRow_Monster.Id },
                    //     PropCatalog = PropCatalog.Inherent,
                    //     PropType = PropType.MinCd,
                    //     ValueType = CsValueType.Double,
                    //     ApplyType = ApplyType.Attached,
                    // }.SetDouble(csvRow_Gun.MinCD));
                    // // CD
                    // lst.Add(new CommonProp
                    // {
                    //     AttachedType = AttachedType.Monster,
                    //     AttachTo = new List<int> { csvRow_Monster.Id },
                    //     PropCatalog = PropCatalog.Inherent,
                    //     PropType = PropType.Cd,
                    //     ValueType = CsValueType.Double,
                    //     ApplyType = ApplyType.Attached,
                    // }.SetDouble(csvRow_Gun.CD));
                    // // 射程
                    // lst.Add(new CommonProp
                    // {
                    //     AttachedType = AttachedType.Monster,
                    //     AttachTo = new List<int> { csvRow_Monster.Id },
                    //     PropCatalog = PropCatalog.Inherent,
                    //     PropType = PropType.GunRange,
                    //     ValueType = CsValueType.Double,
                    //     ApplyType = ApplyType.Attached,
                    // }.SetDouble(csvRow_Gun.GunRange));

                    MonsterProps[csvRow_Monster.Id] = lst;
                }
            }

            return MonsterProps;
        }

        #endregion

        #region 部分常量表数据的缓存

        ///// <summary>
        ///// 怪物与玩家的距离小于等于该值时，怪物向玩家移动
        ///// </summary>
        //public float CONST_Monster_MoveDown_MinDistance { get; set; }

        #endregion

        /// <summary>
        /// 即将开始(或正在战斗中)的关卡配置
        /// </summary>
        public CatMainStage.Item CsvRow_CatMainStage { get; set; }

        #region 动态加载的游戏对象池

        private ObjectTmpPool _objectTmpPoolM;
        /// <summary>
        /// 游戏对象池
        /// </summary>
        public ObjectTmpPool ObjectTmpPool
        {
            get
            {
                if (_objectTmpPoolM) return _objectTmpPoolM;

                var gameObj = new GameObject(nameof(ObjectTmpPool));
                _objectTmpPoolM = gameObj.GetOrAddComponent<ObjectTmpPool>();
                return _objectTmpPoolM;
            }
        }

        #endregion

        public async void Start()
        {
            #region 生成地图格子

            // MapCols = CreateMapCols(-10f, 10f, 5);   ////左侧坐标，右侧坐标，分X列  左右5列=4*5=20 
            // MapRows = CreateMapRows(6.0f, -26f, 8); ////// 上坐标，下坐标，格子行数  上下7行=4*8=32   -----》比背包多一行
            // MapCells = CreateMapCells();

            #endregion

            #region 从常量表读取数据并缓存

            //CONST_Monster_MoveDown_MinDistance = Globals.UnityValueTransform(
            //    SingletonMgr.Instance.CsvLoaderMgr.GetOrAddLoader<ConstValueCsv>().Pb.Items
            //        .FirstOrDefault(x => string.Equals(x.KeyName,
            //            "CONST_Monster_MoveDown_MinDistance",
            //            StringComparison.OrdinalIgnoreCase))?.Value ?? 30000);

            #endregion

            //// 处理事件:战斗开始
            //MessageBroker.Default.Receive<FightStart>().Subscribe(e =>
            //{
            //}).AddTo(this);

            //// 处理事件:战斗结束，卸载战斗场景
            //MessageBroker.Default.Receive<FightEnd>().Subscribe(e =>
            //{

            //}).AddTo(this);

            // 加载CommonProp配置表
            await ReloadCommonProp();
        }

        /// <summary>
        /// 加载服务器时间
        /// </summary>
        public async UniTaskVoid LoadServerTime()
        {
            var url = LuaDataSrvClient.Instance.GetSrvUrlRoot("Charge") + "/Gs/GetServerTime";
            var httpRsp = await UnityHttpHelper.GetResponseString(url, HttpMethod.Get);

            if (httpRsp.Success)
            {
                ClientTime = DateTime.Now;
                var httpRtn = JsonConvert.DeserializeObject<JsonRtn<ServerTimeDTO>>(httpRsp.Rsp);
                ServerTime = httpRtn.Value;
            }
        }

        /// <summary>
        /// 同步服务器时间
        /// </summary>
        /// <param name="interval">间隔时长(秒)</param>
        /// <param name="token"></param>
        public async UniTaskVoid Task_SyncServerTime(int interval, CancellationToken token)
        {
            if (UseLocalTime) return;

            try
            {
                for (; ; await UniTask.NextFrame())
                {
                    LoadServerTime().Forget();
                    await UniTask.Delay(TimeSpan.FromSeconds(interval), cancellationToken: token);
                    if (token.IsCancellationRequested) break;
                }
            }
            catch (OperationCanceledException)
            {
                throw;
            }
            catch (MissingReferenceException)
            {
                // ignore
            }
            catch (Exception ex)
            {
                Debug.LogException(ex);
            }
        }

        /// <summary>
        /// 获取客户端计算出来的服务器当前时间
        /// </summary>
        public DateTime GetServerNow()
        {
            var ts = DateTime.Now - ClientTime;
            var rtn = ServerTime.Now + ts;
            return rtn;
        }

        /// <summary>
        /// 将 一个服务器的UTC时间值 转换为 服务器本地时间值
        /// </summary>
        /// <param name="utcTime"></param>
        /// <returns></returns>
        public DateTime GetServerTimeFromUtc(DateTime utcTime)
        {
            var rtn = utcTime.AddHours(ServerTime.ZoneHours);
            return rtn;
        }

        /// <summary>
        /// 获取关卡的刷怪配置(所有回合)
        /// </summary>
        public Dictionary<int, List<BattleBrushEnemy.Item>> ListBrushMonster_Stage(int StageType, int StageLvl)
        {
            // 关卡配置
            var csvRow_CatMainStage = SingletonMgr.Instance.CsvLoaderMgr.GetOrAddLoader<CatMainStageCsv>().Pb.Items.FirstOrDefault(x =>
                x.FrontType == StageType && x.Id == StageLvl);
            return ListBrushMonster_Stage(csvRow_CatMainStage);
        }

        /// <summary>
        /// 获取关卡的刷怪配置(所有回合)
        /// </summary>
        public Dictionary<int, List<BattleBrushEnemy.Item>> ListBrushMonster_Stage(CatMainStage.Item csvRow_CatMainStage)
        {
            if (csvRow_CatMainStage == null) return new();

            var missionId = int.Parse(csvRow_CatMainStage.Map);
            var csvRow_MissionInfo = SingletonMgr.Instance.CsvLoaderMgr.GetOrAddLoader<MissionInfoCsv>().Dic[missionId];

            return SingletonMgr.Instance.CsvLoaderMgr.GetOrAddLoader<BattleBrushEnemyCsv>().GetByStage(csvRow_MissionInfo);
        }

        #region 地图分割为多列

        // /// <summary>
        // /// 将地图分割为多列
        // /// </summary>
        // public List<MapCol> MapCols { get; set; }
        //
        // /// <summary>
        // /// 一列的宽度
        // /// </summary>
        // public float MapColWidth { get; set; }
        //
        // /// <summary>
        // /// 创建地图的列
        // /// </summary>
        // /// <param name="leftX">左侧x值</param>
        // /// <param name="rightX">右侧x值</param>
        // /// <param name="colCount">列数</param>
        // public List<MapCol> CreateMapCols(float leftX, float rightX, int colCount)
        // {
        //     var rtn = new List<MapCol>();
        //     // > 0
        //     MapColWidth = (rightX - leftX) / colCount;
        //     for (var i = 0; i < colCount; i++)
        //     {
        //         rtn.Add(new(i, leftX + MapColWidth * i, leftX + MapColWidth * (i + 1)));
        //     }
        //     return rtn;
        // }
        //
        // /// <summary>
        // /// 将地图分割为多行
        // /// </summary>
        // public List<MapRow> MapRows { get; set; }
        //
        // /// <summary>
        // /// 一行的高度
        // /// </summary>
        // public float MapRowHeight { get; set; }
        //
        // /// <summary>
        // /// 创建地图的行
        // /// </summary>
        // /// <param name="topY">顶部y值</param>
        // /// <param name="bottomY">底部y值</param>
        // /// <param name="rowCount">行数</param>
        // public List<MapRow> CreateMapRows(float topY, float bottomY, int rowCount)
        // {
        //     var rtn = new List<MapRow>();
        //     // < 0
        //     MapRowHeight = (bottomY - topY) / rowCount;
        //     for (var i = 0; i < rowCount; i++)
        //     {
        //         rtn.Add(new(i, topY + MapRowHeight * (i + 1), topY + MapRowHeight * i));
        //     }
        //     return rtn;
        // }
        //
        // /// <summary>
        // /// 获取或设置地图分割的格子
        // /// </summary>
        // public List<MapCell> MapCells { get; set; }
        //
        // /// <summary>
        // /// 创建地图的格子
        // /// </summary>
        // public List<MapCell> CreateMapCells()
        // {
        //     var rtn = MapRows.SelectMany(r =>
        //         MapCols.Select(c => new MapCell { Row = r, Col = c, })).ToList();
        //     return rtn;
        // }
        //
        // /// <summary>
        // /// 计算x坐标属于哪一列
        // /// </summary>
        // public int GetMapColIndex(float x)
        // {
        //     var col = MapCols.FirstOrDefault(c => c.MinX <= x && x < c.MaxX);
        //     if (col == null) return -1;
        //     return col.ColNo;
        // }
        //
        // /// <summary>
        // /// 计算覆盖一个背包坐标的最小外接矩形区域
        // /// </summary>
        // /// <param name="row">行</param>
        // /// <param name="col">列</param>
        // public RectArea2D CalcBagCellArea2D(MapRow row, MapCol col)
        // {
        //     var rtn = new RectArea2D
        //     {
        //         Center = new((col.MaxX + col.MinX) / 2, (row.MaxY + row.MinY) / 2),
        //         Extents = new((col.MaxX - col.MinX) / 2, (row.MaxY - row.MinY) / 2)
        //     };
        //     return rtn;
        // }
        //
        // /// <summary>
        // /// 计算覆盖一组背包坐标的最小外接矩形区域
        // /// </summary>
        // public RectArea2D CalcBagCellArea2D(params BagPosition[] posInBag)
        // {
        //     // 找出对应的地图格子
        //     var mapCells = SingletonMgr.Instance.GlobalMgr.MapCells.Where(c =>
        //         posInBag.Any(p => c.Col.ColNo == p.BagX && c.Row.RowNo == p.BagY)).ToList();
        //
        //     var minX = mapCells.Min(c => c.Col.MinX);
        //     var maxX = mapCells.Max(c => c.Col.MaxX);
        //     var minY = mapCells.Min(c => c.Row.MinY);
        //     var maxY = mapCells.Max(c => c.Row.MaxY);
        //
        //     var rtn = new RectArea2D
        //     {
        //         Center = new Vector2((maxX + minX) / 2, (maxY + minY) / 2),
        //         Extents = new Vector2((maxX - minX) / 2, (maxY - minY) / 2),
        //     };
        //
        //     return rtn;
        // }

        #endregion

        #region 屏幕边界与战斗区域

        /// <summary>
        /// 获取屏幕显示的矩形区域(世界坐标)
        /// </summary>
        public static RectArea2D GetScreenArea()
        {
            //右上角的世界坐标(视口坐标是用比例表示的，左下角固定为(0,0)，右上角固定为(1,1))
            var maxPos = Camera.main!.ViewportToWorldPoint(new Vector2(1, 1));

            var rtn = new RectArea2D { Center = Camera.main.transform.position, };
            rtn.Extents = new(maxPos.x - rtn.Center.x, maxPos.y - rtn.Center.y);
            
            return rtn;
        }

        /// <summary>
        /// 获取战斗区域的边界 上右下左
        /// </summary>
        public static float[] GetBattleArea()
        {
            var screenArea = GetScreenArea();
            var corners = screenArea.GetCornersAABB();
            corners[2] += 7.5f;// 下边界加上UI的高度
            return corners;
        }

        /// <summary>
        /// 获取战斗区域的边 上右下左
        /// </summary>
        public static LineSegment[] GetBattleEdges()
        {
            var corners = GetBattleArea();

            var lines = new LineSegment[]
            {
                // 上
                new() { PosStart = new(corners[3], corners[0]) },
                // 右
                new() { PosStart = new(corners[1], corners[0]) },
                // 下
                new() { PosStart = new(corners[1], corners[2]) },
                // 左
                new() { PosStart = new(corners[3], corners[2]) },
            };

            lines[0].Dir = lines[1].PosStart - lines[0].PosStart;
            lines[1].Dir = lines[2].PosStart - lines[1].PosStart;
            lines[2].Dir = lines[3].PosStart - lines[2].PosStart;
            lines[3].Dir = lines[0].PosStart - lines[3].PosStart;

            return lines;
        }

        ///// <summary>
        ///// 获取围住战斗区域的四条线段 上右下左
        ///// </summary>
        //public static IList<LineSegment> GetAroundBattleArea()
        //{
        //    List<LineSegment> rtn = new();
        //    // 获取战斗区域的边界
        //    float[] area = GetBattleArea();
        //    rtn.Add(new LineSegment { P1 = new Vector2(area[2], area[0]), P2 = new Vector2(area[3], area[0]) });
        //    rtn.Add(new LineSegment { P1 = new Vector2(area[3], area[0]), P2 = new Vector2(area[3], area[1]) });
        //    rtn.Add(new LineSegment { P1 = new Vector2(area[2], area[1]), P2 = new Vector2(area[3], area[1]) });
        //    rtn.Add(new LineSegment { P1 = new Vector2(area[2], area[0]), P2 = new Vector2(area[2], area[1]) });
        //    return rtn;
        //}

        #endregion
    }
}
