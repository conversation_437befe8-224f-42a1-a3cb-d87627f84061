using System.Linq;

using Apq.Unity3D.Extension;

using Cysharp.Threading.Tasks;

using Thing;

using UnityEngine;

namespace View
{
	/// <summary>
	/// 怪物池
	/// </summary>
	[DisallowMultipleComponent]
	public class MonsterPool : MonoBehaviour
	{
		/// <summary>
		/// 从怪物池中获取一个怪物(或新建)
		/// </summary>
		/// <param name="enemyName">怪物的名称(预制体的名称)</param>
        /// <param name="monsterThing">怪物数据</param>
        /// <remarks>只能获取到没有数据的怪物界面</remarks>
		public async UniTask<MonsterBase> GetOrCreateMonsterFromPool(string enemyName, MonsterThing monsterThing)
		{
			var container = transform.Find(enemyName);
			if (container == null)
			{
				var gameObj = new GameObject(enemyName)
				{
					transform =
					{
						parent = transform,
					}
				};
				container = gameObj.transform;
			}
			// var monsters = container.GetComponentsInChildren(typeof(MonsterBase), true);
			// var monster = monsters.OfType<MonsterBase>().FirstOrDefault(x=>!x.gameObject.activeSelf);
			// if (!monster)
			// {
				var prefab =
					await SingletonMgr.Instance.GlobalMgr.ObjectTmpPool.GetOrCreatePrefabFromPool(
						$"Assets/Temp/model/spine/{enemyName}.prefab");
				var monster = Instantiate(prefab, container).GetOrAddComponent<MonsterBase>();
			// }

            if (monster)
            {
                monster.Thing = monsterThing; //界面挂载数据
                monsterThing.ThingBehaviour = monster; //使数据可以找到界面
            }

			return monster;
		}
	}
}
