﻿namespace ViewModel
{
    /// <summary>
    /// 表示一行的y轴范围
    /// </summary>
    public class MapRow
    {
        /// <summary>
        /// 序号(从0开始)
        /// </summary>
        public int RowNo { get; set; }

        /// <summary>
        /// 最小值(包括)
        /// </summary>
        public float MinY { get; set; }

        /// <summary>
        /// 最大值(不包括)
        /// </summary>
        public float MaxY { get; set; }

        public MapRow(int rowNo = default, float minY = default, float maxY = default)
        {
            RowNo = rowNo;
            MinY = minY;
            MaxY = maxY;
        }
    }
}
