--[[
********************************************************************
    created:    2018/05/18
    author :    顾技成
    purpose:    任务快速升级TIPS
*********************************************************************
--]]

local luaID = ('QuickUpgrade')

function QuickUpgradeChangeType(obj, showType, uitaskShowItem)
    local o = {}
    o.gameObject = obj
    o.uitaskShowItem = uitaskShowItem
    o.trans = o.gameObject.transform
    o.New = function()
        o.grid = o.trans:Find('Bg/ScrollView/Grid').gameObject
        o.closeBtn = o.trans:Find('Bg/Close').gameObject
        o.bg = o.trans:Find('Bg').gameObject
        o.bg.gameObject:GetComponent('Button').onClick:AddListenerEx(o.OnBgClick)
        o.closeBtn.gameObject:GetComponent('Button').onClick:AddListenerEx(o.Close)
        o.itemPrefab = HotResManager.ReadUI("UI/Task/prefab/JumpItem")
        o.showType = showType
       -- o.AddItems()
       -- o.CheckQuickUpgrade()
    end

    function o.OnBgClick()
        o.Close()
        --UIManager.LoadUIWithoutPool('UIBattleGround')
    end

    -- 添加Item列表
    function o.AddItems()
        o.roleLevel = EntityModule.hero.propertyLC:GetProperty(CREATURE_FIELD.CREATURE_FIELD_LEVEL)
        o.societyID = EntityModule.hero.societyLC.SocietyData.SocietyID
        local titleName = nil
        for i, v in ipairs(Schemes.TaskFastUpgrade.items) do
            if o.CherkShow(v) then
                o.AddSingleItem(v)
                if not titleName then
                    titleName = v.TypeName
                end
            end
        end
        if not titleName then
            titleName = GetGameText(luaID, 1)
        end
        HelperL.SetChildLabelText(o.trans, 'Bg/Title', titleName)
        o.grid:GetComponent('UIGrid').repositionNow = true
    end

    function o.AddSingleItem(Schemeitem)
        local item = GameObject.Instantiate(o.itemPrefab, o.grid.transform)
        item.name = 1000 + Schemeitem.SortOrder
        item.gameObject.transform:Find('Label'):GetComponent('Text').text = Schemeitem.Name
        item.gameObject:GetComponent("Button").onClick:AddListenerEx(function()
            o.ClickItemFunc(tonumber(Schemeitem.ClickJump))
        end)
    end
    function o.CheckRedSpot()
        local redspot = false
        for i, v in ipairs(Schemes.TaskFastUpgrade.items) do
            if v.ID ~= 2 and v.ID ~= 3 and v.ID ~= 10 then
                if o.CherkShow(v, true) then
                    redspot = true
                    break
                end
            else
                if o.CherkShow(v, true) then
                    if RedSpotManager.CheckSocietyResidentBoss() or RedSpotManager.CheckUIPagoda() then
                        redspot = true
                        break
                    end
                end
            end
        end
        return redspot
    end
    -- 检测是否加载指定Item
    function o.CherkShow(v, ingoreType)
        local roleLevel = EntityModule.hero.propertyLC:GetProperty(CREATURE_FIELD.CREATURE_FIELD_LEVEL)
        -- if o.roleLevel < v.MinLevel then return false end
        if roleLevel < v.MinLevel then
            return false
        end
        if not ingoreType and o.showType ~= v.Type then
            return false
        end
        local jumpID = tonumber(v.ClickJump)
        if jumpID == 1 then
            local cur, total = o.GetTaskCount(6)
            local timeLift = total - cur
            if timeLift > 0 then
                return true
            else
                return false
            end
        elseif jumpID == 4 then
            -- 帮会采集  
            if societyID == 0 then
                return true
            else
                local nowCount = EntityModule.hero.heroTaskLC.GetTaskGroupCount(5)
                local maxCount = Schemes.TaskBranch.GroupCount(5)
                if maxCount > nowCount then
                    return true
                else
                    return false
                end
            end
        elseif jumpID == 5 then
            -- 经验副本
            local expScheme = Schemes.Chapter.Get(2)
            local count = expScheme.LoopTime - EntityModule.hero.ectypeLC:GetTime(2)
            if count > 0 then
                return true
            else
                return false
            end
        elseif jumpID == 6 then
            -- 粮草护送
            local cur, total = o.GetTaskCount(1)
            local timeLift = total - cur
            if timeLift > 0 then
                return true
            else
                return false
            end
        elseif jumpID == 8 then
            -- 军情刺探
            local cur, total = o.GetTaskCount(2)
            local timeLift = total - cur
            if timeLift > 0 then
                return true
            else
                return false
            end
        elseif jumpID == 9 then
            -- 火烧连营
            local cur, total = o.GetTaskCount(25)
            local timeLift = total - cur
            if timeLift > 0 then
                return true
            else
                return false
            end
        elseif jumpID == 10 then
            -- 竞技场
            local todayTimes = HeroDataManager:GetLogicData(LOGIC_DATA.DATA_ARENAGAME_TODAYTIMES) -- 今天已进入竞技场次数
            local buyTimes = HeroDataManager:GetLogicData(LOGIC_DATA.DATA_ARENAGAME_BUYTIMES) -- 今天已购买的额外次数
            local dayBaseCount = 0 -- 可挑战的基础次数
            local arenaConfig = Schemes.ArenaConfig.Get(1)
            if arenaConfig then
                dayBaseCount = arenaConfig.DayBaseCount
            end
            local remainTimes = (buyTimes + dayBaseCount - todayTimes) < 0 and 0 or
                                    (buyTimes + dayBaseCount - todayTimes) -- 今天剩余的挑战的次数  
            if remainTimes > 0 then
                return true
            else
                return false
            end
        end
        return true
    end
    function o.GetTaskCount(groupID)
        if not groupID then
            return
        end
        local curRound = 0
        local totalRound = 0
        local taskPart = EntityModule.hero.heroTaskLC
        for _, v in pairs(taskPart.idToItem) do
            local taskItem = taskPart.GetItem(v.branchID, v.taskID)
            if taskItem then
                local taskScheme = taskItem.taskScheme
                if taskScheme.GroupID == groupID then
                    local branchScheme = GetThisTaskBranchScheme(taskItem)
                    curRound = taskItem.round
                    totalRound = branchScheme.GroupCount
                    break
                end
            end
        end
        return curRound, totalRound
    end
    -- 设置点击方法
    function o.ClickItemFunc(jumpID)
        if jumpID == 1 then
            -- 每日跑环
            UIManager.LoadUI('UILoopTask')
        elseif jumpID == 2 then
            -- 通天塔
            UIManager.OpenTaskEctype(3)
            UIManager.LoadUI('UIEctype')
        elseif jumpID == 3 then
            -- 帮会副本页面
            if o.societyID == 0 then
                UIManager.LoadUI("UISocietyCommonWin")
                UIManager.SendWndMsg('UISocietyCommonWin', UIWndMsg.UISocietyMainPanel.jumpPagMsg, 'societyList')
            else
                UIManager.LoadUI('UISocietyMainPanel')
                UIManager.SendWndMsg('UISocietyMainPanel', UIWndMsg.UISocietyMainPanel.jumpPagMsg, 5)
            end
        elseif jumpID == 4 then
            -- 帮会采集
            if o.societyID == 0 then
                UIManager.LoadUI("UISocietyCommonWin")
                UIManager.SendWndMsg('UISocietyCommonWin', UIWndMsg.UISocietyMainPanel.jumpPagMsg, 'societyList')
            else
                UIManager.LoadUI('UISocietyToCJ')
            end
        elseif jumpID == 5 then
            -- 经验副本
            EntityModule.hero.ectypeLC.selectedChallengeEctypeID = 2
            UIManager.OpenTaskEctype(2)
        elseif jumpID == 6 then
            -- 粮草护送
            local countryId = EntityModule.hero.propertyLC:GetProperty(PLAYER_FIELD.PLAYER_FIELD_COUNTRY)
            if countryId == eCountry.eCountry_Chu then
                EntityModule.hero.navigationLG:NavigateToNPC(1072)
            elseif countryId == eCountry.eCountry_Han then
                EntityModule.hero.navigationLG:NavigateToNPC(1074)
            end
        elseif jumpID == 7 then
            -- 帮会喝酒
            if o.societyID == 0 then
                UIManager.LoadUI("UISocietyCommonWin")
                UIManager.SendWndMsg('UISocietyCommonWin', UIWndMsg.UISocietyMainPanel.jumpPagMsg, 'societyList')
            else
                UIManager.LoadUI('UISocietyMainPanel')
                UIManager.SendWndMsg('UISocietyMainPanel', UIWndMsg.UISocietyMainPanel.jumpPagMsg, 'alcoholic')
            end
        elseif jumpID == 8 then
            -- 军情刺探
            UIManager.LoadUI('UIDailyPlay')
            UIManager.SendWndMsg('UIDailyPlay', UIWndMsg.UIDailyPlay.jumpAppointTaskAccept, 2)
        elseif jumpID == 9 then
            -- 火烧连营
            UIManager.LoadUI('UIDailyPlay')
            UIManager.SendWndMsg('UIDailyPlay', UIWndMsg.UIDailyPlay.jumpAppointTaskAccept, 25)
        elseif jumpID == 10 then
            -- 竞技场
            UIManager.LoadUI('UIArena')
        elseif jumpID == 11 then
            -- 地宫挂机
            local function FlyFunc()
                local targetSceneID = 0
                local toPos = Vector3(175.22, -0.7385312, 35.67)
                local countryID = EntityModule.hero.propertyLC:GetProperty(PLAYER_FIELD.PLAYER_FIELD_COUNTRY)
                if countryID == 1 then
                    targetSceneID = 120
                else
                    targetSceneID = 125
                end
                local gameSceneSchem = Schemes.GameScene:Get(targetSceneID)
                if gameSceneSchem then
                    if gameSceneSchem.BanFly == 1 then
                        EntityModule.hero:SetWorldDestination(targetSceneID, toPos) -- 寻路过去
                        return
                    end
                end
                if not SkepModule.CanUseFly() then
                    HelperL.ShowMessage(TipType.FlowText, GetGameText(luaID, 6))
                    EntityModule.hero:SetWorldDestination(targetSceneID, toPos) -- 寻路过去
                    return
                    -- local parentObj = UIManager.mainlandUI.gameObject.transform:Find('Original/QuickBuy').gameObject
                    -- HelperL.LoadQuickbuy(parentObj,nil,3000)
                    -- return
                end
                HelperL.MapFlyAnimation(targetSceneID, toPos) -- 传送过去
            end
            local btnInfoList = {}
            table.insert(btnInfoList, {
                name = GetGameText(luaID, 4)
            })
            table.insert(btnInfoList, {
                name = GetGameText(luaID, 3),
                light = true,
                callbackFunc = FlyFunc
            })
            local msgStr = GetGameText(luaID, 2)
            UIManager.AddMessageBOXCommon(msgStr, btnInfoList)
            -- 单人副本
        elseif jumpID == 12 then
            UIManager.OpenTaskEctype(1)
            UIManager.LoadUI('UIEctype')
            -- 群雄逐鹿
        elseif jumpID == 13 then
            UIManager.LoadUI('UICommonDailyActivities')
            local param = {
                Id = 18,
                StageID = 1
            }
            local stageID = ActivityManager.GetStageIDByActvID(param.Id)
            if stageID ~= 0 then
                param.StageID = stageID
            end
            UIManager.SendWndMsg('UICommonDailyActivities', UIWndMsg.UICommonDailyActivities.intiMsg, param)
            -- 专属boss
        elseif jumpID == 14 then
            UIManager.LoadUI('UITask')
            UIManager.SendWndMsg('UITask', UIWndMsg.UITask.jumpPagMsg, {
                tag = 'vipBoss_Tabs'
            })
            -- 谍影重重
        elseif jumpID == 15 then
            UIManager.LoadUI('UICommonDailyActivities')
            local param = {
                Id = 23,
                StageID = 1
            }
            local stageID = ActivityManager.GetStageIDByActvID(param.Id)
            if stageID ~= 0 then
                param.StageID = stageID
            end
            UIManager.SendWndMsg('UICommonDailyActivities', UIWndMsg.UICommonDailyActivities.intiMsg, param)
            -- 夺宝奇兵
        elseif jumpID == 16 then
            UIManager.LoadUI('UITreasureRace')
            -- 大乱斗
        elseif jumpID == 17 then
            UIManager.LoadUI('UICommonDailyActivities')
            local param = {
                Id = 9,
                StageID = 1
            }
            local stageID = ActivityManager.GetStageIDByActvID(param.Id)
            if stageID ~= 0 then
                param.StageID = stageID
            end
            UIManager.SendWndMsg('UICommonDailyActivities', UIWndMsg.UICommonDailyActivities.intiMsg, param)
            -- 山海寻宝
        elseif jumpID == 18 then
            UIManager.LoadUI('UISearchTreasure')
            -- 天降宝箱
        elseif jumpID == 19 then
            UIManager.LoadUI('UICommonDailyActivities')
            local param = {
                Id = 19,
                StageID = 1
            }
            local stageID = ActivityManager.GetStageIDByActvID(param.Id)
            if stageID ~= 0 then
                param.StageID = stageID
            end
            UIManager.SendWndMsg('UICommonDailyActivities', UIWndMsg.UICommonDailyActivities.intiMsg, param)
            -- 欢乐水晶
        elseif jumpID == 20 then
            UIManager.LoadUI('UICommonDailyActivities')
            local param = {
                Id = 24,
                StageID = 1
            }
            local stageID = ActivityManager.GetStageIDByActvID(param.Id)
            if stageID ~= 0 then
                param.StageID = stageID
            end
            UIManager.SendWndMsg('UICommonDailyActivities', UIWndMsg.UICommonDailyActivities.intiMsg, param)
            -- 帮会副本
        elseif jumpID == 21 then
            local societyID = EntityModule.hero.societyLC.SocietyData.SocietyID
            if societyID == 0 then
                UIManager.LoadUI("UISocietyCommonWin")
                UIManager.SendWndMsg('UISocietyCommonWin', UIWndMsg.UISocietyMainPanel.jumpPagMsg, 'societyList')
                HelperL.ShowMessage(TipType.FlowText, GetGameText(luaID, 5))
            else
                UIManager.LoadUI('UISocietyMainPanel')
                UIManager.SendWndMsg('UISocietyMainPanel', UIWndMsg.UISocietyMainPanel.jumpPagMsg, 5)
            end
            -- 剧情副本
        elseif jumpID == 22 then
            UIManager.OpenTaskEctype(5)
            UIManager.LoadUI('UIEctype')
            -- 世界boss
        elseif jumpID == 23 then
            UIManager.LoadUI('UITask')
            UIManager.SendWndMsg('UITask', UIWndMsg.UITask.jumpPagMsg, {
                tag = 'worldBoss_Tabs'
            })
            -- 枭雄boss
        elseif jumpID == 24 then
            UIManager.LoadUI('UITask')
            UIManager.SendWndMsg('UITask', UIWndMsg.UITask.jumpPagMsg, {
                tag = 'underGroundBoss_Tabs'
            })
            -- 楚汉之战
        elseif jumpID == 25 then
            UIManager.LoadUI('UICommonDailyActivities')
            local param = {
                Id = 22,
                StageID = 1
            }
            local stageID = ActivityManager.GetStageIDByActvID(param.Id)
            if stageID ~= 0 then
                param.StageID = stageID
            end
            UIManager.SendWndMsg('UICommonDailyActivities', UIWndMsg.UICommonDailyActivities.intiMsg, param)
            -- 个人boss
        elseif jumpID == 26 then
            UIManager.LoadUI("UITask")
            UIManager.SendWndMsg('UITask', UIWndMsg.UITask.jumpPagMsg, {
                tag = 'myBoss_Tabs'
            })
            -- 黄金大盗
        elseif jumpID == 27 then
            UIManager.LoadUI('UICommonDailyActivities')
            local param = {
                Id = 20,
                StageID = 1
            }
            local stageID = ActivityManager.GetStageIDByActvID(param.Id)
            if stageID ~= 0 then
                param.StageID = stageID
            end
            UIManager.SendWndMsg('UICommonDailyActivities', UIWndMsg.UICommonDailyActivities.intiMsg, param)
            -- 巅峰对决
        elseif jumpID == 28 then
            UIManager.LoadUI('UIThreeVSThreeMainPanel')
            -- 帮会活动界面
        elseif jumpID == 29 then
            local societyID = EntityModule.hero.societyLC.SocietyData.SocietyID
            if societyID == 0 then
                UIManager.LoadUI("UISocietyCommonWin")
                UIManager.SendWndMsg('UISocietyCommonWin', UIWndMsg.UISocietyMainPanel.jumpPagMsg, 'societyList')
                HelperL.ShowMessage(TipType.FlowText, GetGameText(luaID, 5))
            else
                UIManager.LoadUI('UISocietyMainPanel')
                UIManager.SendWndMsg('UISocietyMainPanel', UIWndMsg.UISocietyMainPanel.jumpPagMsg, 3)
            end
            -- 贼寇来袭
        elseif jumpID == 30 then
            UIManager.LoadUI('UICommonDailyActivities')
            local param = {
                Id = 17,
                StageID = 1
            }
            local stageID = ActivityManager.GetStageIDByActvID(param.Id)
            if stageID ~= 0 then
                param.StageID = stageID
            end
            UIManager.SendWndMsg('UICommonDailyActivities', UIWndMsg.UICommonDailyActivities.intiMsg, param)
            -- boss界面
        elseif jumpID == 31 then
            UIManager.LoadUI('UITask')
            -- 国战界面
        elseif jumpID == 32 then
            UIManager.Close()
            UIManager.LoadUI('UICampView')
            UIManager.SendWndMsg('UICampView', UIWndMsg.UICampView.jumpPagMsg, 3)
            -- 王者之战
        elseif jumpID == 33 then
            UIManager.LoadUI('UISoloGame')
        else
            warn(string.format('NO jumpID:%d', jumpID))
        end
        o.Close()
    end
    function o.Close()
        GameObject.Destroy(o.gameObject)
    end
    function o.OnEnable()

    end

    function o.OnDisable()

    end

    -- 检测快速升级红点
    function o.CheckQuickUpgrade()
        local haveExp = false
        local Num = 0
        local heroLevel = EntityModule.hero.propertyLC:GetProperty(CREATURE_FIELD.CREATURE_FIELD_LEVEL)
        local levelScheme = Schemes.PlayerBaseProp.Get(heroLevel)
        local maxLimit = 0
        local getExp = HeroDataManager:GetLogicData(LOGIC_DATA.DATA_EXPWELFARE_GET)
        local noGetExp = HeroDataManager:GetLogicData(LOGIC_DATA.DATA_EXPWELFARE_NOGET)
        if levelScheme ~= nil then
            local serverOpenDays = HelperL.GetServerOpenDays()
            local nIndex = 1
            if levelScheme.ServerOpenDay then
                for i = 1, #levelScheme.ServerOpenDay do
                    if serverOpenDays >= levelScheme.ServerOpenDay[i] then
                        nIndex = i
                    else
                        break
                    end
                end
            end
            maxLimit = math.min((levelScheme.PerDayGiveMaxExp[nIndex] + noGetExp),
                levelScheme.PerDayGiveMaxExp[nIndex] * 5)
            -- 算出进度条
            Num = maxLimit - getExp
            if Num <= 0 then
                haveExp = false
            else
                haveExp = true
            end
        end
        local redspot = o.CheckRedSpot()
        if redspot or haveExp then
            o.uitaskShowItem.redsopt:SetActive(true)
        end
        o.uitaskShowItem.redsopt:SetActive(false)
    end

    o.New()
    return UITipManager

end

