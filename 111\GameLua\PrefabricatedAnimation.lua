--[[
********************************************************************
    created:    2023/09/04
    author :    李锦剑
    purpose:    预制动画
*********************************************************************
--]]
---@class PrefabricatedAnimation 预制动画
local m = {}
-- 随机范围
m.randomValue = 50

--#region 对象池
------------------------------------------

---对象池
local pool = {}
--池子
pool.list = {}
--对象池最大缓存数量
pool.maxPoolAmount = 20
--开启对象池
pool.openPool = true

---添加--对象到--对象池
---@param item integer
function pool:AddItem(item)
    item.gameObject:SetActive(false)
    --启用对象池
    if self.openPool and #self.list < self.maxPoolAmount then
        table.insert(self.list, item)
        return
    end
    --销毁
    GameObject.Destroy(item.gameObject)
    item = nil
end

---获取--对象池--对象
---@return Collect_Item
function pool:GteItem()
    --先从对象池获取
    if #self.list > 0 then
        return table.remove(self.list)
    end
    --创建
    return m.CreationPrefab()
end

------------------------------------------
--#endregion

--创建收集动画对象
function m.CreationPrefab()
    --创建
    if tolua.isnull(m.CoinItem) then
        local item = GameObject.New():AddComponent(typeof(UnityEngine.UI.Image))
        item.gameObject:SetActive(false)
        item.name = 'CoinItem'
        item:GetRectTransform().sizeDelta = Vector2(40, 40)
        item.raycastTarget = false
        --图片预制体用于(克隆)
        m.CoinItem = item
    end

    ---@class Collect_Item
    local item = {}
    --克隆
    item.gameObject = GameObject.Instantiate(m.CoinItem.gameObject, UIManager.uiCanvas.transform)
    item.transform = item.gameObject.transform
    --移动动画完成事件
    item.DOLocalMoveComplete = function()
        if item.callbackFun then item.callbackFun(item.index, item.maxIndex) end
        pool:AddItem(item)
    end
    --缩放动画完成事件
    item.DOScaleComplete = function()
        item.transform:DOLocalMove(item.endPos, 1.2):OnComplete(item.DOLocalMoveComplete)
    end
    --播放动画
    item.PlayAnimation = function(startPos, endPos, spriteName, callbackFun, index, maxIndex)
        --------------外部获取参数--------------
        --索引
        item.index = index
        --最大索引
        item.maxIndex = maxIndex
        --移动动画结束点
        item.endPos = endPos
        --动画结束事件
        item.callbackFun = callbackFun
        ----------------------------------------

        local x = math.random(startPos.x - m.randomValue, startPos.x + m.randomValue)
        local y = math.random(startPos.y - m.randomValue, startPos.y + m.randomValue)
        item.transform.localPosition = Vector3(x, y, 0)
        if not spriteName then
            spriteName = 'coinSprite'
        end
        AtlasManager:AsyncGetSprite(spriteName, item.gameObject)

        item.transform.localScale = Vector3.zero
        item.gameObject:SetActive(true)
        item.transform:DOKill()
        --随机大小缩放
        item.transform:DOScale(Vector3.one * math.random(0.7, 1.5), 0.1)
            :SetAutoKill(true)
            :OnComplete(item.DOScaleComplete)
    end

    return item
end

---收集动画
---@param startNode integer 开始节点
---@param endNode integer 结束节点
---@param spriteName? integer 图片
---@param callbackFun? fun(index:integer, maxIndex:integer) 结束回调
function m.CollectAnimation(startNode, endNode, spriteName, callbackFun)
    local startPos = UIManager.uiCanvas.transform:InverseTransformPoint(startNode.transform.position)
    local endPos = UIManager.uiCanvas.transform:InverseTransformPoint(endNode.transform.position)
    local index = 0
    local maxIndex = math.random(15, pool.maxPoolAmount)
    local TimerUpdate = function()
        index = index + 1
        pool:GteItem().PlayAnimation(startPos, endPos, spriteName, callbackFun, index, maxIndex)
    end

    --启动定时器
    Timer.New(TimerUpdate, 0.0001, maxIndex):Start()
end

---渐变消失动画(控制CanvasGroup组件，alpha属性)
---@param obj integer
---@param duration number 动画持续时间
---@param callbackFun? fun(canvasGroup) 结束回调
---@param rweeningEase? integer
function m.GradualVanishAnimation(obj, duration, callbackFun, rweeningEase)
    if tolua.isnull(obj) then return end
    local canvasGroup = obj.gameObject:GetComponent('CanvasGroup')
    if not canvasGroup then
        canvasGroup = obj.gameObject:AddComponent(typeof(UnityEngine.CanvasGroup))
    end
    canvasGroup.alpha = 1
    --canvasGroup:DOKill()
    -- canvasGroup:DOFade(0, duration or 0.6)
    --     :SetEase(rweeningEase or TweeningEase.InOutCubic)
    --     :OnComplete(function()
    --         if callbackFun then callbackFun(canvasGroup) end
    --     end)

    local sep = DOTween.Sequence()
    sep:AppendInterval(1.4)
    sep:Append(canvasGroup:DOFade(0, duration or 0.6))
    sep:AppendInterval(0.6)
    sep:AppendCallback(function()
        if callbackFun then callbackFun(canvasGroup) end
    end)
    sep:SetUpdate(true)
end

---渐变显示动画(控制CanvasGroup组件，alpha属性)
---@param obj integer
---@param duration number 动画持续时间
---@param callbackFun? fun(canvasGroup) 结束回调
---@param rweeningEase? integer
function m.GradualShowAnimation(obj, duration, callbackFun, rweeningEase)
    if tolua.isnull(obj) then return end
    local canvasGroup = obj.gameObject:GetComponent('CanvasGroup')
    if not canvasGroup then
        canvasGroup = obj.gameObject:AddComponent(typeof(UnityEngine.CanvasGroup))
    end
    canvasGroup.alpha = 0
    obj.gameObject:SetActive(true)
    canvasGroup:DOKill()
    canvasGroup:DOFade(1, duration or 2)
        :SetEase(rweeningEase or TweeningEase.InOutCubic)
        :OnComplete(function()
            if callbackFun then callbackFun(canvasGroup) end
        end)
end

---移动动画
---@param target integer 移动对象
---@param endValue integer 目标点 Vector3
---@param duration number 动画持续时间
---@param callbackFun? fun(transform) 结束回调
---@param isLocal? boolean 是局部移动，默认：true
---@param rweeningEase integer
function m.MoveAnimation(target, endValue, duration, callbackFun, isLocal, rweeningEase)
    if tolua.isnull(target) then return end
    if isLocal ~= false then
        target.transform:DOLocalMove(endValue, duration)
            :SetEase(rweeningEase or TweeningEase.InOutCubic)
            :OnComplete(function()
                if callbackFun then callbackFun(target.transform) end
            end)
    else
        target.transform:DOMove(endValue, duration)
            :SetEase(rweeningEase or TweeningEase.InOutCubic)
            :OnComplete(function()
                if callbackFun then callbackFun(target.transform) end
            end)
    end
end

PrefabricatedAnimation = m
