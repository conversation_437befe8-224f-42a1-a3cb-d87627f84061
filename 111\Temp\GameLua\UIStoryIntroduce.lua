-- 登录UI
local luaID = ('UIStoryIntroduce')

local UIStoryIntroduce = {}

-- 初始化
function UIStoryIntroduce:OnCreate()
	self.objList.Btn_Close.onClick:AddListenerEx(self.OnClickClose)
	self.btnList = { self.objList.Btn_Close}
	return true
end

-- 打字效果相关变量
local typewriterSpeed = 0.02  -- 每个字符的显示间隔时间
local typewriterTimer = nil   -- 打字效果计时器
local fullText = ""          -- 完整文本
local currentIndex = 1       -- 当前显示到的字符索引

-- 获取UTF8字符串长度
local function utf8len(str)
    local len = 0
    local pos = 1
    while pos <= #str do
        local char = string.byte(str, pos)
        if char < 128 then
            pos = pos + 1
        elseif char < 224 then
            pos = pos + 2
        elseif char < 240 then
            pos = pos + 3
        else
            pos = pos + 4
        end
        len = len + 1
    end
    return len
end

-- 获取UTF8字符串的子串
local function utf8sub(str, start, finish)
    local pos = 1
    local count = 0
    local result = ""
    
    while pos <= #str do
        local char = string.byte(str, pos)
        local charLen = 1
        if char < 128 then
            charLen = 1
        elseif char < 224 then
            charLen = 2
        elseif char < 240 then
            charLen = 3
        else
            charLen = 4
        end
        
        count = count + 1
        if count >= start then
            result = result .. string.sub(str, pos, pos + charLen - 1)
            if count >= finish then
                break
            end
        end
        pos = pos + charLen
    end
    return result
end

-- 打字效果函数
function UIStoryIntroduce:TypewriterEffect()
    if currentIndex <= utf8len(fullText) then
        self.objList.Txt_Desc.text = utf8sub(fullText, 1, currentIndex)
        currentIndex = currentIndex + 1
    else
        if typewriterTimer then
            typewriterTimer:Stop()
            typewriterTimer = nil
        end
    end
end

-- 窗口开启
function UIStoryIntroduce:OnOpen()	
	fullText = GetGameText(luaID, 1)
	currentIndex = 1
	self.objList.Txt_Desc.text = ""
	self.objList.Txt_Desc.transform.localPosition = Vector3(0,-561,0)
	self.objList.Txt_Desc.transform:DOKill()
	self.objList.Txt_Desc.transform:DOLocalMoveY(5100, 90)
	
	-- 启动打字效果
	if typewriterTimer then
		typewriterTimer:Stop()
	end
	typewriterTimer = Timer.New(function()
		self:TypewriterEffect()
	end, typewriterSpeed, -1)  -- -1表示无限循环
	typewriterTimer:Start()
	
	-- 设置自动关闭计时器
	self.timer = Timer.New(function()
		self:CloseSelf()
	end, 85, 1)
	self.timer:Start()
end

function UIStoryIntroduce.OnClickClose()
	local self = UIStoryIntroduce
	self:CloseSelf()
end

-- 窗口关闭
function UIStoryIntroduce:OnClose()
	self.objList.Txt_Desc.transform:DOKill()
	if typewriterTimer then
		typewriterTimer:Stop()
		typewriterTimer = nil
	end
	self.timer:Stop()
	self.timer = nil
end

-- 窗口销毁
function UIStoryIntroduce:OnDestroy()
	-- 释放内存
	for i, btn in ipairs(self.btnList) do
		btn.onClick:RemoveAllListeners()
		btn.onClick:Invoke()
	end
end

return UIStoryIntroduce