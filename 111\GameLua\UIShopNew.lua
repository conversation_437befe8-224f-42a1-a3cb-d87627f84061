--[[
********************************************************************
    created:	2024/05/16
    author :	李锦剑
    purpose:    商店新
*********************************************************************
--]] ---商店分页数据
---@class StorePagingData
---@field StoreItemsID integer
---@field Name string
---@field Refresh integer
---@field ActivityID integer
---@field NeedLevel integer
---@field MinStoreRank integer
---@field StoreRankDiscount integer
local luaID = 'UIShopNew'

---@class UIShopNew:UIWndBase
local m = {}

-- 命格商店ID
local FateShop = 2
-- 消耗类型转换物品ID
local CostType = {
    [1] = 3, -- 钻石
    [2] = 4, -- 金币
    [3] = 7, -- 声望
    [4] = 6, -- 帮贡(历练)
    [5] = 9, -- 功勋
    [6] = 10, -- 绑定元宝
    [7] = 11, -- 点券
    [8] = 5, -- 熔炼值
    [9] = 16, -- 夺宝券
    [10] = 18 -- 虚拟金币
}

--------------------------------------------------------------------
-- 订阅事件列表
--------------------------------------------------------------------
function m.GetOpenEventList()
    return {
        [EventID.StoreList] = m.UpdateView,
        [EventID.StoreBuyItem] = m.StoreBuyItem,
        [EventID.StoreUpdateFree] = m.UpdateView,
        [EventID.LogicDataChange] = m.UpdateView,
        [EventID.EntitySyncBuff] = m.UpdateView,
        [EventID.OnGoodsPropChange] = m.UpdateView,
        [EventID.OnHeroPropChange] = m.UpdateView,
        [EventID.ActorDataChanged] = m.UpdateView
    }
end

--------------------------------------------------------------------
-- 初始化
--------------------------------------------------------------------
function m.OnCreate()
    ---@type StorePagingData[]
    m.StorePaging = {}
    m.storeID = ShopNew_ShopID
    local itemID
    local cfg = Schemes.Store:Get(m.storeID)
    for i = 1, 10 do
        itemID = cfg['StoreItemsID' .. i]
        if itemID ~= 0 then
            m.StorePaging[itemID] = {
                StoreItemsID = itemID,
                Name = cfg['Name' .. i],
                Refresh = cfg['Refresh' .. i],
                ActivityID = cfg['ActivityID' .. i],
                NeedLevel = cfg['NeedLevel' .. i],
                MinStoreRank = cfg['MinStoreRank' .. i],
                StoreRankDiscount = cfg['StoreRankDiscount' .. i]
            }
            StoreModule:RequestGetStoreList(itemID)
        end
    end

    ---切换按钮
    ---@type Item_Button[]
    m.Item_Button_List = {}
    local buttonDataList =
        { -- { ID = 1, Name = GetGameText(luaID, 16), StoreID = 2, ShopType = 1, Root = m.objList.Obj_EquipShop,   Parent = m.objList.Grid_EquipShop,   Item = m.objList.Item_Goods, },
        {
            ID = 2,
            Name = GetGameText(luaID, 17),
            StoreID = 9,
            ShopType = 1,
            Root = m.objList.Obj_GoldShop,
            Parent = m.objList.Grid_GoldShop,
            Item = m.objList.Item_Goods
        } -- { ID = 3, Name = GetGameText(luaID, 18), StoreID = 0, ShopType = 2, Root = m.objList.Obj_DiamondShop, Parent = m.objList.Grid_DiamondShop, Item = m.objList.Item_Goods, },
        }
    for i, v in ipairs(buttonDataList) do
        if not m.Item_Button_List[i] then
            m.Item_Button_List[i] = m.Creation_Item_Button(m.objList.Grid_Button, i)
        end
        m.Item_Button_List[i].Init(v)
    end
    m.objList.ScrollView2.gameObject:SetActive(false)
    m.RegisterClickEvent()
    return true
end

--------------------------------------------------------------------
-- 窗口开启
--------------------------------------------------------------------
function m:OnOpen(tab)
    if tab == nil then
        m.SelectButton(m.selectButtonIndex or 1)
    else
        m.SelectButton(tab)
    end
end

--------------------------------------------------------------------
-- 注册点击事件
--------------------------------------------------------------------
function m.RegisterClickEvent()
    m:AddClick(m.objList.Btn_Close, function()
        m:CloseSelf()
    end)
end

--------------------------------------------------------------------
-- 更新界面
--------------------------------------------------------------------
function m.UpdateView()
    for i, v in ipairs(m.Item_Button_List) do
        v.UpdateView()
    end
end

--------------------------------------------------------------------
-- 创建商品栏
---@param parent any
---@param index integer
---@param _item any
---@return Item_Goods
--------------------------------------------------------------------
function m.Creation_Item_Goods(parent, index, _item)
    ---@class Item_Goods
    local item = {}
    item.index = index
    item.com = m:CreateSubItem(parent, _item)
    item.com.Img_RedDot.gameObject:SetActive(false)
	if index == 1 then
		item.com.Img_bglight.gameObject:SetActive(true)
		m.curitem = item
	end
    m:AddClick(item.com.Btn_Buy, function()
        m:OnOpenShop(item.StoreID, item.data.GoodsNum, item.data)
		m.curitem.com.Img_bglight.gameObject:SetActive(false)
		item.com.Img_bglight.gameObject:SetActive(true)
		m.curitem = item
        if not HelperL.IsLackGoods(CostType[item.data.CostType], item.data.CostNum, false) then

        end
    end)
    m:AddClick(item.com.Btn_Ad, function()
        AdvertisementManager.ShowRewardAdAndRequestDirectGiveGoods(item.data.LimitActorDay, 2)
    end)

    ---更新商品数据
    ---@param data ItemData
    ---@param storeID integer 商店ID
    item.UpdateData = function(data, storeID)
        -- 商店ID
        item.StoreID = storeID
        item.data = data
        if not data then
            item.com.gameObject:SetActive(false)
            return
        end
        local cfg = Schemes:GetGoodsConfig(data.GoodsID)
        if not cfg then
            return
        end
        item.com.Txt_Amount.text = 'X ' .. data.GoodsNum

        -- 设置默认状态：售罄
        item.com.Btn_Buy.gameObject:SetActive(false)
        item.com.Btn_Ad.gameObject:SetActive(false)
        item.com.Txt_SoldOut.gameObject:SetActive(false)
        item.com.Img_RedDot.gameObject:SetActive(false)
        item.com.Img_Bg1.gameObject:SetActive(false)
        item.com.Img_Bg2.gameObject:SetActive(false)

        if data.LimitActorDay > 0 then -- 看广告免费
            local cfgAD = Schemes.CommonText:Get(data.LimitActorDay)
            local num = HeroDataManager:GetLogicByte(cfgAD.Param4, cfgAD.Param5)
            -- 需要看广告
            if num == 0 then
                item.com.Txt_AdNum.text = GetGameText(luaID, 1)
                item.com.Img_RedDot.gameObject:SetActive(true)
                item.com.Btn_Ad.gameObject:SetActive(true)
            else
                item.com.Txt_SoldOut.gameObject:SetActive(true)
            end
            item.com.Img_Bg2.gameObject:SetActive(true)
        else -- 消耗购买
            if not data.SoldOut then
                AtlasManager:AsyncGetGoodsSprite(CostType[data.CostType], item.com.Img_CostType)
                item.com.Img_CostType:SetNativeSize()
                local bool = HelperL.IsLackGoods(CostType[item.data.CostType], item.data.CostNum, false, false)
                local color = bool and UI_COLOR.Red or UI_COLOR.White
                item.com.Txt_CostNum.text = string.format("<color=%s>%s</color>", color,
                    HelperL.GetChangeNumWan(data.CostNum))
                item.com.Btn_Buy.gameObject:SetActive(true)
            else
                item.com.Txt_SoldOut.gameObject:SetActive(true)
            end
            item.com.Img_Bg1.gameObject:SetActive(true)
        end

        -- 折扣
        if data.Discount > 0 and data.Discount < 10 and data.LimitActorDay == 0 then
            item.com.Txt_Discount.text = string.format(GetGameText(luaID, 2), data.Discount)
            item.com.Img_Discount.gameObject:SetActive(true)
        else
            item.com.Img_Discount.gameObject:SetActive(false)
        end

        local icon = cfg.IconID
        local goodsName = cfg.GoodsName
        -- 金币改名字及图片
        if data.GoodsID == 4 then
            if data.GoodsNum == 500 then
                goodsName = GetGameText(luaID, 13)
                icon = 'icon_64x64_yb_01'
            elseif data.GoodsNum == 2500 then
                goodsName = GetGameText(luaID, 14)
                icon = 'icon_64x64_yb_02'
            elseif data.GoodsNum == 10000 then
                goodsName = GetGameText(luaID, 15)
                icon = 'icon_64x64_yb_03'
            end
        end
        AtlasManager:AsyncGetSprite(icon, item.com.Img_Goods, true)
        item.com.Txt_Name.text = goodsName

        item.com.gameObject:SetActive(true)
    end
    return item
end

--------------------------------------------------------------------
-- 创建商品栏
---@param parent any
---@param index integer
---@param _item any
---@return Item_Diamond
--------------------------------------------------------------------
function m.Creation_Item_Diamond(parent, index, _item)
    ---@class Item_Diamond
    local item = {}
    item.index = index
    item.com = m:CreateSubItem(parent, _item)
    item.com.Img_RedDot.gameObject:SetActive(false)
    m:AddClick(item.com.Btn_Buy, function()
        HelperL.Recharge(item.cfg.ID)
    end)

    item.com.Btn_Ad.gameObject:SetActive(false)
    item.com.Txt_Amount.gameObject:SetActive(false)
    item.com.Img_CostType.gameObject:SetActive(false)
    item.com.Img_Discount.gameObject:SetActive(false)

    ---更新商品数据
    ---@param cfg RechargeCardCfg
    item.UpdateData = function(cfg)
        item.cfg = cfg
        if cfg then
            AtlasManager:AsyncGetSprite(cfg.FirstPicBg, item.com.Img_Goods, true)
            item.com.Txt_Name.text = cfg.CardName
            item.com.Txt_CostNum.text = "￥" .. math.floor(cfg.FirstRMB / 100)
            item.com.gameObject:SetActive(true)
        else
            item.com.gameObject:SetActive(false)
        end
    end
    return item
end

--------------------------------------------------------------------
---选择按钮
---@param index integer 索引
--------------------------------------------------------------------
function m.SelectButton(index)
    local item
    if m.selectButtonIndex then
        item = m.Item_Button_List[m.selectButtonIndex]
        item.SetState(false)
    end
    m.selectButtonIndex = index
    item = m.Item_Button_List[index]
    item.SetState(true)
    m.UpdateView()
end

--------------------------------------------------------------------
---创建按钮
---@param parent any 父节点
---@param index integer 索引
---@return Item_Button
--------------------------------------------------------------------
function m.Creation_Item_Button(parent, index)
    ---@class Item_Button
    local item = {}
    item.index = index
    ---@type Item_Goods[]
    item.goodsItem_List = {}
    ---@type Item_Diamond[]
    item.goodsItem_List2 = {}
    item.com = m:CreateSubItem(parent, m.objList.Item_Button)
    item.com.Img_Bg1.gameObject:SetActive(true)
    item.com.Img_Bg2.gameObject:SetActive(false)
    item.com.Img_RedDot.gameObject:SetActive(false)
    m:AddClick(item.com.Btn_Click, function()
        m.SelectButton(item.index)
    end)

    -- 设置按钮状态
    item.SetState = function(bool)
        if bool then
            item.com.Img_Bg1.gameObject:SetActive(false)
            item.com.Img_Bg2.gameObject:SetActive(true)
            if item.data then
                item.data.Root.gameObject:SetActive(true)
            end
        else
            item.com.Img_Bg1.gameObject:SetActive(true)
            item.com.Img_Bg2.gameObject:SetActive(false)
            if item.data then
                item.data.Root.gameObject:SetActive(false)
            end
        end
    end

    ---初始化
    ---@param data {ID:integer, Name:string, Parent:any, ShopType:integer, Root:any, StoreID:integer, Item:any}
    item.Init = function(data)
        item.data = data
        if data then
            item.com.Txt_Name1.text = data.Name
            item.com.Txt_Name2.text = data.Name
            item.SetState(false)
            item.com.gameObject:SetActive(true)
        else
            item.com.gameObject:SetActive(false)
        end
    end

    ---更新界面
    item.UpdateView = function()
        if not item.data then
            return
        end
        item.com.Img_RedDot.gameObject:SetActive(false)

        if item.data.ShopType == 1 then
            local itemList = {}
            local storeData = StoreModule:GetStoreItemList(item.data.StoreID)
            if storeData then
                itemList = storeData.ItemList or {}
            else
                StoreModule:RequestGetStoreList(item.data.StoreID)
            end
            local num = math.max(#itemList, #item.goodsItem_List)
            for i = 1, num do
                if not item.goodsItem_List[i] then
                    item.goodsItem_List[i] = m.Creation_Item_Goods(item.data.Parent, i, item.data.Item)
                end
                item.goodsItem_List[i].UpdateData(itemList[i], item.data.StoreID)
                if i == 1 then
                    m:OnOpenShop(item.data.StoreID, item.data.GoodsNum, itemList[i])
                end
            end
            item.com.Img_RedDot.gameObject:SetActive(RedDotCheckFunc:Check_UIShopNew(item.data.StoreID))
        elseif item.data.ShopType == 2 then
            local itemList = Schemes.RechargeCard:GetByCardType(2) or {}
            local num = math.max(#itemList, #item.goodsItem_List)
            for i = 1, num do
                if not item.goodsItem_List2[i] then
                    item.goodsItem_List2[i] = m.Creation_Item_Diamond(item.data.Parent, i, item.data.Item)
                end
                item.goodsItem_List2[i].UpdateData(itemList[i])
            end
        end
    end
    return item
end

--------------------------------------------------------------------
---购买商品回调
---@param data SC_Store_BuyItem
--------------------------------------------------------------------
function m.StoreBuyItem(data)
    if not data then
        return
    end
    local storeData = StoreModule:GetStoreItemList(data.StoreID)
    if not storeData then
        return
    end
    local Item
    for i, v in ipairs(storeData.ItemList) do
        if v.ItemID == data.ItemID then
            Item = v
            break
        end
    end
    if not Item then
        return
    end
    HelperL.DisplayOneReward(Item.GoodsID, Item.GoodsNum)
end

--------------------------------------------------------------------
-- 窗口开启
---@param storeID integer 商店ID
---@param itemNum integer 购买数量
---@param data ItemData 购买物品数据
--------------------------------------------------------------------
function m:OnOpenShop(storeID, itemNum, data)
    m.data = data
    m.itemNum = itemNum
    m.storeID = storeID
    m.costType = CostType[data.CostType]
    m.UpdateViewShop()
    m:RegisterClickEventShop()
end

--------------------------------------------------------------------
-- 注册点击事件
--------------------------------------------------------------------
function m:RegisterClickEventShop()
    m.objList.Btn_OK.onClick:AddListenerEx(function()
        if m.storeID == FateShop then
            local skep = SkepModule:GetSkepByID(SKEPID.SKEPID_GEMPACK)
            if skep:IsFull() then
                HelperL.ShowMessage(TipType.FlowText, GetGameText('UIFate', 8))
                return
            end
        end

        if HelperL.IsLackGoods(m.costType, m.data.CostNum, false, true) then
            return
        end

        local data = {
            type = NotarizeWindowsType.Windows1,
            titleContent = '购买提示',
            content = string.format(GetGameText(luaID, 19), m.data.CostNum),
            okCallback = function()
                if m.storeID == 9 then
                    local goodInfo = m.data.GoodsID .. ';' .. m.data.GoodsNum
                    local costInfo = 3 .. ';' .. m.data.CostNum
                    HelperL.RequestDirectGiveGoodsy(goodInfo, costInfo, m.RequestCallback)
                else
                    StoreModule:StoreBuyItem(m.storeID, m.data.ItemID, m.itemNum)
                end
                HelperL.ShowMessage(TipType.FlowText, '购买成功！')
                m:CloseSelf()
            end,
        }
        HelperL.NotarizeUI(data)
    end)
end

--------------------------------------------------------------------
-- 更新界面
--------------------------------------------------------------------
function m.UpdateViewShop()
    if not m.goodsItem then
        m.goodsItem = _GAddSlotItem(m.objList.Obj_Root)
    end
    m.goodsItem:SetItemID(m.data.GoodsID)
    m.goodsItem:SetCount(m.itemNum)
    if m.data.GoodsID > DEFINE.MAX_MEDICAMENT_ID then
        local cfg = Schemes.Equipment:Get(m.data.GoodsID)
        -- local color = HelperL.GetColorByQuality(cfg.QualityLevel)
        m.objList.Txt_NameShop.text = cfg.GoodsName
        m.objList.Txt_DescShop.text = string.gsub(cfg.TipsDes, '<br>', '\n')
    else
        local cfg = Schemes.Medicament:Get(m.data.GoodsID)
        -- local color = HelperL.GetColorByQuality(cfg.Quality)
        m.objList.Txt_NameShop.text = cfg.GoodsName
        m.objList.Txt_DescShop.text = string.gsub(cfg.TipsDes, '<br>', '\n')
    end
    m.objList.Txt_UnivalenceShop.text = m.data.CostNum
    AtlasManager:AsyncGetGoodsSprite(m.costType, m.objList.Img_PropertyShop)

    local num = SkepModule:GetGoodsCount(m.costType)
    local color = '#ffffff'
    if num < m.data.CostNum then
        color = '#ff000'
    end
    m.objList.Txt_PropertyShop.text = string.format('<color=#%s>%s</color>', color, num)
    AtlasManager:AsyncGetGoodsSprite(m.costType, m.objList.Img_UnivalenceShop)
end

return m
