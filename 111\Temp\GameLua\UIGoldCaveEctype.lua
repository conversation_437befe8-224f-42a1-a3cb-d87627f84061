--[[
********************************************************************
    created:	2024/09/13
    author :	高
    purpose:    活动副本
*********************************************************************
--]]


local luaID = 'UIGoldCaveEctype'

---每日副本(黄金洞窟)
---@class UIGoldCaveEctype:UIWndBase
local m = {}

local ectypeType = GoldCave_EctypeType

--------------------------------------------------------------------
-- 订阅事件列表
--------------------------------------------------------------------
function m.GetOpenEventList()
    return {
        [EventID.LogicDataChange] = m.UpdateView,
        [EventID.EntitySyncBuff] = m.UpdateView,
        [EventID.OnGoodsPropChange] = m.UpdateView,
        [EventID.OnHeroPropChange] = m.UpdateView,
        [EventID.ActorDataChanged] = m.UpdateView,
    }
end

--------------------------------------------------------------------
-- 初始化
--------------------------------------------------------------------
function m.OnCreate()
    m.gameEctypeBox_ItemList = {}
    m.slotItemList = {}
    m.StageList = Schemes.CatMainStage:GetByFrontType(ectypeType)
    m.comResInfo = ComResInfo.New()
    m.comResInfo.BindView(m.objList.Img_Res)
    m.RegisterClickEvent()
    return true
end

--------------------------------------------------------------------
-- 窗口开启
--------------------------------------------------------------------
function m:OnOpen()
    m.UpdateView()
end

--------------------------------------------------------------------
-- 注册点击事件
--------------------------------------------------------------------
function m.RegisterClickEvent()
    m:AddClick(m.objList.Btn_Close, function()
        m:CloseSelf()
    end)

    -- m:AddClick(m.objList.Btn_Start, function()
    --     m.ChuZheng()
    -- end)
end

--------------------------------------------------------------------
--更新界面
--------------------------------------------------------------------
function m.UpdateView()
    -- 当前通过的最大关卡
    m.maxStage = GamePlayerData.GameEctype:GetProgress(ectypeType)
    if m.maxStage ~= 0 then
        m.maxStage = (m.maxStage) % 10000
        if m.maxStage >= #m.StageList then
            m.maxStage = #m.StageList
        end
    else
        m.maxStage = 0
    end
    local pagesize = 10
    local gapNum = math.floor(m.maxStage / pagesize)
    local startPos = gapNum * pagesize + 1
    local endPos = gapNum * pagesize + pagesize

    if endPos > #m.StageList then
        endPos = #m.StageList
        startPos = endPos - pagesize - 1
    end
    for i = 1, 10 do
        if not m.gameEctypeBox_ItemList[i] then
            m.gameEctypeBox_ItemList[i] = m.Creation(startPos + i + 110000 - 1)
        end
        m.gameEctypeBox_ItemList[i].UpdateViewItem(startPos + i + 110000 - 1)
    end
end

function m.Creation(index)
    local item = {}
    m.slotItemList[index] = {}
    item.objList = m:CreateSubItem(m.objList.Grid_, m.objList.Img_Item)
    --更新界面
    item.UpdateViewItem = function(index)
        item.stageId = index
        item.StageCfg = m.StageList[item.stageId - 110000]

        item.objList.Txt_Level.text = string.format("第" .. (item.stageId - 110000) .. "关")
        
        if not item.StageCfg then return end
        item.objList.Txt_Title.text = item.StageCfg.Desc
        item.objList.Txt_Desc2.text = item.StageCfg.Desc2
        local iconID = item.StageCfg.Icon
        if iconID == "" or iconID == "0" then
            iconID = "100001"
        end
        AtlasManager:AsyncGetSprite(iconID, item.objList.Img_Icon)
        local expendList = HelperL.Split(item.StageCfg.Need, ';')
        --消耗物品ID
        local expID = tonumber(expendList[1]) or 0
        --消耗物品数量
        local expNum = tonumber(expendList[2]) or 0
        --广告物品ID
        -- local adID = tonumber(expendList[3]) or 0
        --消耗物品ID2
        local expID2 = tonumber(expendList[4]) or 0
        --消耗物品数量2
        local expNum2 = tonumber(expendList[5]) or 0

        local count1 = SkepModule:GetGoodsCount(expID) or 0
        local count2 = SkepModule:GetGoodsCount(expID2) or 0


        local challengeNum = math.floor(count1 / expNum)
        item.objList.Txt_Num.text = string.format(GetGameText(luaID, 1), challengeNum)
        --挑战次数
        --m.objList.Txt_Num.text = string.format(GetGameText(luaID, 1), count1)
        AtlasManager:AsyncGetGoodsSprite(expID2, item.objList.Img_EX)
        local color = count2 >= expNum2 and UI_COLOR.White or UI_COLOR.Red
        item.objList.Txt_EX.text = string.format("<color=%s>%s/%s</color>", color, count2, expNum2)

        local level1 = EntityModule.hero:GetProperty(CREATURE_FIELD.CREATURE_FIELD_LEVEL)
        local level2 = EctypeUnlockLevel[ectypeType] or 0
        if level1 < level2 then
            item.objList.Txt_Hint.text = string.format(GetGameText(luaID, 2), level2)
            item.objList.Btn_Lock.gameObject:SetActive(true)
            item.objList.Btn_Start.gameObject:SetActive(false)
        else
            item.objList.Btn_Lock.gameObject:SetActive(false)
            item.objList.Btn_Start.gameObject:SetActive(true)
        end
        item.objList.Img_RedDot.gameObject:SetActive(RedDotCheckFunc.Check_UIGoldCaveEctype(ectypeType))

        if item.StageCfg.ID ~= item.lastStageID then
            item.lastStageID = item.StageCfg.ID
            local prizeGoods = Schemes.CatMainStage:GetSucceedRewards(item.StageCfg.ID)

            if m.slotItemList[index] == nil then
                m.slotItemList[index] = {}
                --销毁item.objList.Grid_Goods子物体现有奖励
                for c = item.objList.Grid_Goods.transform.childCount - 1, 0, -1 do
                    if item.objList.Grid_Goods.transform:GetChild(c) then
                        GameObject.Destroy(item.objList.Grid_Goods.transform:GetChild(c).gameObject)
                    end
                end
            end
            local num = math.max(#prizeGoods, #m.slotItemList[index])
            for i = 1, num, 1 do
                if not m.slotItemList[index][i] then
                    m.slotItemList[index][i] = _GAddSlotItem(item.objList.Grid_Goods)
                end
                if prizeGoods[i] then
                    m.slotItemList[index][i]:SetItemID(prizeGoods[i].ID)
                    m.slotItemList[index][i]:SetCount(prizeGoods[i].Num)
                    m.slotItemList[index][i]:SetActive(true)
                else
                    m.slotItemList[index][i]:SetActive(false)
                end
            end
        end
        item.objList.Btn_Start.gameObject:SetActive(false)
        HelperL.SetImageGray(item.objList.Img_Item, false)
        --local mStage = GamePlayerData.GameEctype:GetProgress(FrontType)
        if m.maxStage == 0 then
            if index == 110001 then
                item.objList.Btn_Start.gameObject:SetActive(true)
            else   
                item.objList.Img_Gray.gameObject:SetActive(true)
                item.objList.Txt_tg.gameObject:SetActive(false)
            end
        else
            if index == m.maxStage + 1 + 110000 then
                item.objList.Btn_Start.gameObject:SetActive(true)
                item.objList.Txt_tg.gameObject:SetActive(false)
                item.objList.Img_Gray.gameObject:SetActive(false)
            else
                item.objList.Btn_Start.gameObject:SetActive(false)
                if index < m.maxStage + 1 + 110000 then  
                    item.objList.Txt_tg.gameObject:SetActive(true)
                    item.objList.Img_Gray.gameObject:SetActive(false)
                else
                    item.objList.Img_Gray.gameObject:SetActive(true)
                    item.objList.Txt_tg.gameObject:SetActive(false)
                end
            end
        end
        --清除点击事件
        item.objList.Btn_Start.onClick:RemoveAllListeners()
        item.objList.Btn_Start.onClick:AddListenerEx(function()
            m.ChuZheng(index)
        end)
    end
    return item
end

--------------------------------------------------------------------
-- 出征
--------------------------------------------------------------------
function m.ChuZheng(stageID)
    --local stageID = GamePlayerData.GameEctype:GetChallengeableID(ectypeType)
    local cfg = Schemes.CatMainStage:Get(stageID)
    if not cfg then
        error('读取 CatMainStage 表失败 stageID=' .. stageID)
        return
    end

    local expendList = HelperL.Split(cfg.Need, ';')
    --消耗物品ID
    local expID = tonumber(expendList[1]) or 0
    --消耗物品数量
    local expNum = tonumber(expendList[2]) or 0
    --广告物品ID
    -- local adID = tonumber(expendList[3])
    --消耗物品ID2
    local expID2 = tonumber(expendList[4]) or 0
    --消耗物品数量2
    local expNum2 = tonumber(expendList[5]) or 0

    --判断挑战次数
    if HelperL.IsLackGoods(expID, expNum, false, false) then
        HelperL.ShowMessage(TipType.FlowText, GetGameText(luaID, 7))
        return
    end
    --消耗判断
    if HelperL.IsLackGoods(expID2, expNum2, false) then
        return
    end

    --m:CloseSelf()
    -- -- 进入战斗
    BattleManager:EnterBattle(cfg.ID)
end

return m
