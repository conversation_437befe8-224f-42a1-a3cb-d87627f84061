--[[
********************************************************************
    created:	2024/06/01
    author :	李锦剑
    purpose:    暂停界面
*********************************************************************
--]]

local luaID = ('UIGamePauseView')
local buffIDList = {}
---@class UIGamePauseView:UIWndBase
local m = {}

-- 订阅事件列表
function m:GetOpenEventList()
	return {

	}
end

--------------------------------------------------------------------
-- 订阅事件列表
--------------------------------------------------------------------
function m:OnCreate()
	---@type Item_Buff2[]
	m.Item_Buff_List = {}
	return true
end

--------------------------------------------------------------------
-- 窗口开启
--------------------------------------------------------------------
function m:OnOpen()
	m.UpdateView()
end

--------------------------------------------------------------------
--注册点击事件
--------------------------------------------------------------------
function m.RegisterClickEvent()
	m.objList.Btn_Close.onClick:AddListenerEx(m.ClickUI)
	m.objList.Btn_Quit.onClick:AddListenerEx(m.QuitGame)
	m.objList.Btn_GoOn.onClick:AddListenerEx(m.GoOnGame)
	m.objList.Btn_Music.onClick:AddListenerEx(m.BtnMusicClick)
	m.objList.Btn_Sound.onClick:AddListenerEx(m.BtnSoundClick)
	m.objList.Btn_Setting.onClick:AddListenerEx(function()
		UIManager:OpenWnd(WndID.SettingNew)
	end)
end

function m.BtnMusicClick()
{
	if(PlayerPrefs.GetFloat("Music_Volume", 0) == 0)
	{
		SoundManager:SetVolume_Music(1)
		m.objList.Img_MusicLine.gameObject.SetActive(false);
	}else
	{
		SoundManager:SetVolume_Music(0)
		m.objList.Img_MusicLine.gameObject.SetActive(true);
	}
}

function m.BtnSoundClick()
{
	if(PlayerPrefs.GetFloat("SoundEffect_Volume", 0f) == 0f)
	{
		SoundManager:SetVolume_SoundEffect(1)
		m.objList.Img_SoundLine.gameObject.SetActive(true);
	}else
	{
		SoundManager:SetVolume_SoundEffect(0)
		m.objList.Img_SoundLine.gameObject.SetActive(false);
	}
}

--------------------------------------------------------------------
-- 更新界面
--------------------------------------------------------------------
function m.UpdateView()
	local num = math.max(#buffIDList, #m.Item_Buff_List)
	for i = 1, num, 1 do
		if not m.Item_Buff_List[i] then
			m.Item_Buff_List[i] = m.Creation_Item_Buff(m.objList.Grid_Buff, i)
		end
		m.Item_Buff_List[i].UpdateData(buffIDList[i])
	end

	if(PlayerPrefs.GetFloat("Music_Volume", 0f) == 0f)
	{
		m.objList.Img_MusicLine.gameObject.SetActive(true);
	}else
	{
		m.objList.Img_MusicLine.gameObject.SetActive(false);
	}

	if(PlayerPrefs.GetFloat("SoundEffect_Volume", 0f) == 0f)
	{
		m.objList.Img_SoundLine.gameObject.SetActive(true);
	}else
	{
		m.objList.Img_SoundLine.gameObject.SetActive(false);
	}
end

--------------------------------------------------------------------
-- 创建buff
--------------------------------------------------------------------
function m.Creation_Item_Buff(parent, index)
	---@class Item_Buff2
	local item = {}
	item.index = index
	item.com = m:CreateSubItem(parent, m.objList.Item_Box)
	---更新数据
	---@param buffID integer
	item.UpdateData = function(buffID)
		m.buffID = buffID
		if buffID then
			item.com.gameObject:SetActive(true)
		else
			item.com.gameObject:SetActive(false)
		end
	end
	return item
end

--------------------------------------------------------------------
-- 继续游戏
--------------------------------------------------------------------
function m.GoOnGame()
	m.ClickUI()
end

--------------------------------------------------------------------
-- 退出游戏
--------------------------------------------------------------------
function m.QuitGame()
	---@type NotarizeWindowsDatq
	local data = {
		type = NotarizeWindowsType.Windows5,
		title = GetGameText(luaID, 1),
		content = GetGameText(luaID, 2),
		okCallback = function(okParam)
			LuaToCshapeManager.Instance:FightQuit()
			m.ClickUI()
		end
	}
	HelperL.NotarizeUI(data)
end

--------------------------------------------------------------------
-- 关闭界面
--------------------------------------------------------------------
function m.ClickUI()
	m:CloseSelf()
end

return m
