﻿using System;
using System.Linq;

using Apq.ChangeBubbling;

using Thing;

using UnityEngine;

namespace View
{
	public class PlayerActor : CreatureBase
	{
		/// <summary>
		/// 玩家角色物件(数据)
		/// </summary>
		public ActorThing ActorThing => Thing as ActorThing;
        /// <summary>
        /// 角色移动组件
        /// </summary>
        public PlayerMove PlayerMove { get; set; }

		public override bool IsMoving => PlayerMove != null && PlayerMove.IsMoving;

		public override void Start()
		{
            HpBar.bar.gameObject.SetActive(true);
            ActorThing.Hp.Changed += Hp_Changed;
            ActorThing.Armor.Changed += Armor_Changed;
        }

        public override void DoSyncToThing()
        {
            base.DoSyncToThing();
            SetSpineSortingOrderBySelfPosition();
            HpBar.SetDisplayHealth((float)(ActorThing.Hp.Value / ActorThing.GetTotalDouble(X.PB.PropType.MaxHp).FirstOrDefault()));
            HpBar.SetDisplayArmorBar((float)(ActorThing.Armor.Value / ActorThing.GetTotalDouble(X.PB.PropType.MaxHp).FirstOrDefault()));
        }

        public override void Update()
        {
            base.Update();
            
            // 与怪物完全相同的渲染层级更新逻辑
            SetSpineSortingOrderBySelfPosition();
        }

        private void OnDisable()
        {
            ActorThing.Hp.Changed -= Hp_Changed;
            ActorThing.Armor.Changed -= Armor_Changed;
        }

        private void Armor_Changed(ChangeEventArgs e)
        {
            if (e.NewValue is double newValue)
            {
                HpBar.SetDisplayArmorBar((float)(newValue / ActorThing.GetTotalDouble(X.PB.PropType.MaxHp).FirstOrDefault()));
            }
        }

        private void Hp_Changed(ChangeEventArgs e)
        {
            if (e.NewValue is double newValue && e.OriginalValue is double oldValue)
            {
                HpBar.SetDisplayHealth((float)(newValue / ActorThing.GetTotalDouble(X.PB.PropType.MaxHp).FirstOrDefault()));
                if (newValue > oldValue)
                {
                    //回飘字
                    HudMgr.Instance.SpwanDamageHud(hudComp =>
                    {
                        hudComp.Init(transform.position + Vector3.up * -10);
                        hudComp.SetHpRecoverNumber(Mathf.CeilToInt((float)newValue - (float)oldValue));
                    }).Forget();
                }
            }
        }

        /// <summary>
        /// 获取或设置是否拒绝移动
        /// </summary>
        public bool DenyMove
        {
            get => PlayerMove.DenyMove;
            set => PlayerMove.DenyMove = value;
        }

        //public void PerformDash()
        //{
        //	if (PlayerPrefs.GetInt("DashTutorial2") == 1)
        //	{
        //		SingletonMgr.Instance.BattleMgr.TimeManager.SetTimescale(1.0f);
        //		//TODO Mobile and Console Controls
        //		//InputController.instance.dashButtonGlow.SetActive(false);
        //		//InputController.instance.gameObject.SetActive(false);
        //		PlayerPrefs.SetInt("DashTutorial2", 0);
        //	}
        //}

        /*
        public override void Update()
        {
            base.Update();
            
            // 简化的动画播放逻辑，避免干扰attack01攻击动画的完整播放
            var skeAni = GetComponentInChildren<Spine.Unity.SkeletonAnimation>();
            if (skeAni != null && skeAni.state != null)
            {
                var currentTrack = skeAni.state.GetCurrent(0);
                if (currentTrack != null && currentTrack.Animation != null)
                {
                    string currentAnimName = currentTrack.Animation.Name;
                    
                    // 如果当前是攻击动画，不做任何干扰，让其完整播放
                    if (currentAnimName == "attack01")
                    {
                        return;
                    }
                }
                
                // 非攻击动画时，根据移动状态播放相应动画
                if (PlayerMove != null && PlayerMove.IsMoving)
                {
                    // 如果正在移动且当前不是移动动画，播放移动动画
                    if (currentTrack == null || currentTrack.Animation == null || currentTrack.Animation.Name != "move01")
                    {
                        PlayAnimation("move01", true, false);
                    }
                }
                else
                {
                    // 如果不在移动且当前不是待机动画，播放待机动画
                    if (currentTrack == null || currentTrack.Animation == null || currentTrack.Animation.Name != "idle01")
                    {
                        PlayAnimation("idle01", true, false);
                    }
                }
            }
        }
        */
	}

}
