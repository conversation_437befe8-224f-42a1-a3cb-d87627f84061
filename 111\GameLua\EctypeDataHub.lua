--[[
   ********************************************************************
   created:	2016/10/08
   author :	谢燚文
   purpose:	副本
   *********************************************************************
--]]
local luaID = ('EctypeDataHub')

local EctypeDataHub = {}

EctypeDataHub.curEctype = nil
EctypeDataHub.teamMember_message = nil
EctypeDataHub.ectypeTeamOperate = 0
EctypeDataHub.entryEctypeID = 0
EctypeDataHub.isFirstEntry = true
EctypeDataHub.curChapterDegree = nil
EctypeDataHub.leaveEctypeBySelf = false
EctypeDataHub.ectypeDieTimes = 0
EctypeDataHub.ectypeMonsterHP = nil

--检测是否满足进入副本的条件
function EctypeDataHub.CanEnter()
    local msg = ""
    local hero = EntityModule.hero
    if hero == nil then return RESULT_CODE.RESULT_COMMON_FAILURE end
--    local ectype = hero.ectypeLC.curEctype
--    local ectype = Schemes.ChapEctype.GetItemByEctypeId(WorldModule.ectypeInfo.EctypeID)
    local ectype = EctypeDataHub.curEctype
    if (ectype.PreID > 0 and hero.ectypeLC:GetRating(ectype.PreID)) or
        (ectype.PreID2 > 0 and hero.ectypeLC:GetRating(ectype.PreID2)) then
        msg = GetGameText(luaID, 1)
        return RESULT_CODE.RESULT_COMMON_FAILURE
    end

    local roleLevel = hero.propertyLC:GetProperty(CREATURE_FIELD.CREATURE_FIELD_LEVEL)
    if roleLevel < ectype.NeedLvMin then
        msg  = string.format(GetGameText(luaID, 2), ectype.NeedLvMin)
        return RESULT_CODE.RESULT_COMMON_FAILURE
    end
--    local energy = hero.propertyLC:GetProperty(PLAYER_FIELD.PLAYER_FIELD_CURENERGY)
--    if energy < ectype.NeedEnergy then
--        return RESULT_CODE.RESULT_COMMON_LACK_ENERGY
--    end
    return RESULT_CODE.RESULT_COMMON_SUCCEED , msg
end

--进入副本请求
function EctypeDataHub.Enter()
    EntityModule.ForceSyn( )
    local chapScheme = Schemes.ChapEctype.GetItemByEctypeId(EctypeDataHub.curEctype.EctypeID)
    if not chapScheme then
        error(" chapScheme = nil "..EctypeDataHub.curEctype.EctypeID)
        return
    end
    local req = "LuaRequestEnterEctypeByChapID?chapID=" .. chapScheme.ID
    LuaModule.RunLuaRequest(req , EctypeDataHub.EnterCallback)
end

function EctypeDataHub.EnterCallback(result, content)
    if result ~= RESULT_CODE.RESULT_COMMON_SUCCEED.id then
        HelperL.AddAMessageTip(HelperL.GetResultCodeStr(result))
        return
    end
    EventManager.Fire(EventID.CloseAutoLoopTask)
end

function EctypeDataHub.LeaveEctype()
    local hero = EntityModule.hero
    if hero == nil or hero.model == nil then return end
    if hero.asmComp ~= nil then
        hero.asmComp:GotoState(eAS.eAS_Stand)
    end
    local sceneID = WorldModule.sceneId
    local request = "LuaRequestLeaveEctype?sceneID=" .. sceneID
    LuaModule.RunLuaRequest(request, EctypeDataHub.OnLeaveEctypeCallback)
end

function EctypeDataHub.OnLeaveEctypeCallback(result, content)
    if result ~= RESULT_CODE.RESULT_COMMON_SUCCEED.id then
        HelperL.AddAMessageTip(HelperL.GetResultCodeStr(result))
        return
    end
end

function EctypeDataHub.SplitEctypeResult(result)
    local itemArray = HelperL.Split(result,"&")
    local resultInfo = {}
    for i= 1, #itemArray do
        local items = HelperL.Split(itemArray[i],"=")
        if #items == 2 then
            resultInfo[items[1]] = items[2]
        end
    end
    return resultInfo
end

function EctypeDataHub.LuaRequestRandomEctype()
    if WorldModule.ectypeInfo.EctypeID == 0 then return RESULT_CODE.RESULT_COMMON_FAILURE end
    if EctypeDataHub.ectypeResultInfo.Result == FIGHT_RESULT.FIGHT_RESULT_NULL then return RESULT_CODE.RESULT_COMMON_FAILURE end
    local ectype = Schemes.ChapEctype.GetItemByEctypeId(WorldModule.ectypeInfo.EctypeID)
    if ectype == nil or ectype.EnterChap <= 0 then return RESULT_CODE.RESULT_COMMON_FAILURE end
    local sceneID = WorldModule.sceneId
    local request = "LuaRequestRandomEctype?sceneID=" .. sceneID
    LuaModule.RunLuaRequest(request, EctypeDataHub.OnRandomEctypeCallback)
    return RESULT_CODE.RESULT_COMMON_SUCCEED
end

function EctypeDataHub.OnRandomEctypeCallback(result, content)
    if result ~= RESULT_CODE.RESULT_COMMON_SUCCEED.id then
        HelperL.AddAMessageTip(HelperL.GetResultCodeStr(result))
        return
    end
    local paramInfo = EctypeDataHub.SplitEctypeResult(content)
    local ectypeID = tonumber(paramInfo.ectypeID)
    if ectypeID == nil then return end
    if ectypeID <= 0 then
        if WorldModule.ectypeInfo.EctypeID == 0 then return end
        EctypeDataHub.LeaveEctype()
        return
    end
    EctypeDataHub.curEctype.EctypeID = ectypeID
    local ectype = Schemes.ChapEctype.GetItemByEctypeId(ectypeID)
    if ectype == nil then return end
    local result , msg = EctypeDataHub.CanEnter()
    if result ~= RESULT_CODE.RESULT_COMMON_SUCCEED then
        HelperL.AddAMessageTip(result.desc)
        return
    end
    EctypeDataHub.Enter()
end

EctypeDataHub.ectypeResultInfo = {}
function EctypeDataHub.EctypeResultCallback(message)
    if message.SceneID ~= WorldModule.sceneId then return end
    UnityEngine.Time.timeScale = 0.5
    local time = coroutine.create(function (str)
        coroutine.wait(2)
        UnityEngine.Time.timeScale = 1
        coroutine.wait(2)
        EctypeDataHub.EctypeShowIcon(str)
    end)
    coroutine.resume(time,message)
end

function EctypeDataHub.EctypeShowIcon(message)
    EctypeDataHub.ectypeResultInfo = EctypeDataHub.SplitEctypeResult(message.Parameter)
    EctypeDataHub.ectypeResultInfo.Result = message.Result
    local ectypeID = WorldModule.ectypeInfo.EctypeID
    local ectype = Schemes.ChapEctype.GetItemByEctypeId(ectypeID)
    if ectype == nil then return end
    if message.Result == FIGHT_RESULT.FIGHT_RESULT_WIN and ectype.EnterChap ~= 0 then
        EctypeDataHub.LuaRequestRandomEctype()
    else
        if EctypeDataHub.leaveEctypeBySelf then
            EctypeDataHub.leaveEctypeBySelf = false
        elseif message.Result ~=  FIGHT_RESULT.FIGHT_RESULT_LOST then
            UIManager.LoadUI('UIEctypeResultPanel')
        end
    end
end


return EctypeDataHub
