-- UI管理中心
local luaID = ('UIManager')

---@class UIManager
UIManager = {}

--打开窗口列表(只收集正常窗口ID，弹窗ID想不进行收集，请在 ExcludeWndID 里添加ID进行排除)
---@type integer[]
UIManager.opentWndIDList = {}

-- 初始化
function UIManager:Init()
	local uiRootObj = GameObject.Find('UIRoot')
	if not uiRootObj then
		error('UIManager no UIRoot')
		return false
	end
	UnityEngine.Object.DontDestroyOnLoad(uiRootObj)
	self.uiRootObj = uiRootObj
	local rootTrans = uiRootObj.transform
	self.uiCamera = rootTrans:Find('UICamera'):GetComponent('Camera')
	self.uiEventSystem = rootTrans:Find('EventSystem').gameObject
	self.uiCanvas = rootTrans:Find('Canvas'):GetComponent('Canvas')
	--UI根节点
	self.uiRoot = rootTrans:Find('Canvas/Root_UI')
	local hudRoot = GameObject.New("HudRoot", typeof(UnityEngine.RectTransform))
	hudRoot:GetRectTransform():SetParent(self.uiRoot, false)
	--特效根节点
	self.vfxRoot = rootTrans:Find('Canvas/Root_VFX')
	--UI对象池根节点
	self.uiPool = GameObject.New("Pool", typeof(UnityEngine.RectTransform))
	self.uiPool:GetRectTransform():SetParent(self.uiRoot, false)
	self.uiPool:SetActive(false)
	self.uiContainer = self.uiCanvas:GetRectTransform()
	self.uiScreenRect = self.uiContainer.rect
	self.uiScreenScale = {
		x = self.uiContainer.sizeDelta.x / UnityEngine.Screen.width,
		y = self.uiContainer.sizeDelta.y / UnityEngine.Screen.height
	}

	-------------------适配代码勿动--------------------------
	----屏幕适配有问题，修改'屏幕查看尺寸': ScreenSize.width 和 ScreenSize.height
	
	-- local rect = self.uiRoot:GetRectTransform()
	-- rect.anchorMin = Vector2.one * 0.5
	-- rect.anchorMax = Vector2.one * 0.5
	-- rect.pivot = Vector2.one * 0.5
	-- rect.anchoredPosition = Vector2.zero
	-- rect.sizeDelta = Vector2.New(ScreenSize.width, ScreenSize.height)
	--UI适配 缩放
	--HelperL.AdaptScaleAndSize(self.uiRoot)
	---------------------------------------------------------

	-- 创建排序层级
	local sortOrderSet = {}
	local sortOrderList = {}
	for i, v in ipairs(Schemes.UIConfig.items) do
		if not sortOrderSet[v.SortOrder] then
			sortOrderSet[v.SortOrder] = true
			table.insert(sortOrderList, v.SortOrder)
		end
	end
	table.sort(sortOrderList, function(a, b)
		return a < b
	end)

	self.sortLayerObjList = {}
	for i, v in ipairs(sortOrderList) do
		local layerObj = GameObject.New(tostring(v), typeof(UnityEngine.RectTransform))
		local layerTrans = layerObj:GetRectTransform()
		layerTrans:SetParent(self.uiRoot, false)
		layerTrans.sizeDelta = CachedVector2:Set(0, 0)
		layerTrans.anchorMin = CachedVector2:Set(0, 0)
		layerTrans.anchorMax = CachedVector2:Set(1, 1)
		Helper.ResetTransform(layerTrans)
		layerObj:SetActive(false)
		self.sortLayerObjList[v] = layerTrans
	end

	self.useWndOpenAni = true
	self.useWndCloseAni = true

	---@type UIWndBase[] 存储了Lua类
	self.uiList = {}
	self.uiOpenList = {}
	self.uiActiveList = {}
	self.uiWndFrameUpdateList = {}

	self.slowBeginInterval = 2

	AtlasManager:Init()

	-- 公共每秒更新定时器
	if self.secondUpdateTimer ~= nil then
		self.secondUpdateTimer:Stop()
		self.secondUpdateTimer = nil
	end
	self.secondUpdateTimer = Timer.New(self.OnSecondUpdate, 1, -1)
	self.secondUpdateTimer:Start()

	self.garbageCollectCount = 0
end

-- 获取相机
function UIManager:GetUICamera()
	return self.uiCamera
end

-- 获取事件管理器
function UIManager:GetEventSystem()
	return self.uiEventSystem
end

-- 获取主画布
function UIManager:GetCanvas()
	return self.uiCanvas
end

--- 获取UI主容器
---@param sortLayer ?integer 排序层
---@return unknown
function UIManager:GetUIContainer(sortLayer)
	local result = self.uiRoot
	if sortLayer and self.sortLayerObjList[sortLayer] then
		result = self.sortLayerObjList[sortLayer]
		result.gameObject:SetActive(true)
	end
	return result
end

-- 获取屏幕大小
function UIManager:GetScreenRect()
	return self.uiScreenRect
end

-- 获取屏幕大小
function UIManager:GetScreenScale()
	return self.uiScreenScale
end

-- 获取特殊材质
function UIManager:GetSlotMaterial(matType, image)
	local mat = nil
	if self.materialList == nil then
		-- 特殊材质列表
		local materialList = {}
		local materialPrefab = HotResManager.ReadUI('ui/Common/MaterialManager')
		if materialPrefab then
			local item = {}
			Helper.FillLuaComps(materialPrefab.transform, item)
			if item.Img_Gray then
				materialList.gray = item.Img_Gray.material
			end
			if item.Img_slot_00 then
				materialList.slot_0 = item.Img_slot_00.material
			end
			if item.Img_slot_01 then
				materialList.slot_1 = item.Img_slot_01.material
			end
			if item.Img_slot_02 then
				materialList.slot_2 = item.Img_slot_02.material
			end
			if item.Img_slot_03 then
				materialList.slot_3 = item.Img_slot_03.material
			end
			if item.Img_slot_04 then
				materialList.slot_4 = item.Img_slot_04.material
			end
			if item.Img_slot_05 then
				materialList.slot_5 = item.Img_slot_05.material
			end
			if item.Img_slot_06 then
				materialList.slot_6 = item.Img_slot_06.material
			end
		end
		self.materialList = materialList
	end

	if matType == UIMaterialType.Gray then
		mat = self.materialList.gray
	else
		if matType > 6 then
			matType = 6
		end
		mat = self.materialList['slot_' .. tostring(matType)]
	end

	if image then
		image = image.transform:GetComponent("Image")
		if image then
			image.material = mat
		end
	end
end

-- 获取窗口开启延迟帧数
function UIManager:GetSlowBeginInterval()
	return self.slowBeginInterval
end

-- 这个参数来判断登陆的类型 0 首次登陆 1 重新登陆 2 战斗场景返回的登陆
UIManager.loginType = 0
-- 切换场景处理
function UIManager.OnLevelWasLoaded(sceneName)
	local self = UIManager
	if sceneName == 'GameLogin' then
		if UIManager.loginType == 0 or UIManager.loginType == 1 then
			self:CloseWnd(WndType.Common, true)
			self:CloseWnd(WndType.Popup, true)
			self:CloseWnd(WndType.Main, true)
			self:OpenWnd(WndID.Login)
		else
			HelperL.CheckPopUpAffterBattle()
		end
	end
end

-- 是否能打开指定UI
function UIManager:CanOpenWnd(wndID, showMsg, ...)
	local config = Schemes.UIConfig:Get(wndID)
	if not config then
		warn('UIManager.CanOpenWnd 找不到窗口配置 ', wndID)
		return false
	end

	local wndItem = self.uiList[wndID]
	if not wndItem then
		local newWndItem = require(config.LuaFile)
		if not newWndItem then
			warn('UIManager.CanOpenWnd 载入lua失败 ', wndID, config.LuaFile)
			return false
		end
		setmetatable(newWndItem, UIWndBase)

		if not newWndItem:Create(config) then
			warn('UIManager.CanOpenWnd 创建窗口失败 ', wndID, config.LuaFile)
			return false
		end

		self.uiList[wndID] = newWndItem
		wndItem = newWndItem
	end

	local canOpen, msg = wndItem:CanOpenBase(...)
	if not canOpen and showMsg and msg and msg ~= '' then
		HelperL.ShowMessage(TipType.FlowText, msg)
	end

	return canOpen
end

-- 打开指定UI
function UIManager:OpenWnd(wndID, ...)
	return UIManager:OpenWnd2(wndID, nil, true, ...)
end

--UI创建参数(第一次加载预制体的时候需要)
UIManager.WndCreateParam = {}

-- 打开指定UI2(可以指定父节点)
---@param wndID integer 窗口ID
---@param parent integer 父节点
---@param openCloseBut ?boolean 是否显示关闭按钮
---@param ... unknown 参数
---@return UIWndBase|nil
function UIManager:OpenWnd2(wndID, parent, openCloseBut, ...)
	if UIManager:IsWndLoad(wndID) then
		--覆盖参数，使用最后一次创建参数
		UIManager.WndCreateParam[wndID] = { ... }
		return
	end

	if UIManager:IsWndOpen(wndID) then
		UIManager:SetTopWndID(wndID)
		return self.uiList[wndID]:OnOpenBase(parent, openCloseBut, ...)
	end

	local config = Schemes.UIConfig:Get(wndID)
	if not config then
		warn('UIManager.OpenWnd 找不到窗口配置 ', wndID)
		return
	end

	UIManager:AddWndID(wndID)
	---@type UIWndBase
	local uiLua = self.uiList[wndID]
	if not uiLua then
		self.uiList[wndID] = require(config.LuaFile)
		uiLua = self.uiList[wndID]
		if not uiLua then
			warn('UIManager.OpenWnd 载入lua失败 ', wndID, config.LuaFile)
			return
		end
		setmetatable(uiLua, UIWndBase)
		print(config.LuaFile, "打开UI面板+++", config.Prefab)

		--获取资源清单
		local resourceList = {}
		if type(uiLua.GetResourceList) == 'function' then
			resourceList = uiLua.GetResourceList()
		end
		local path = 'Assets/Temp/' .. config.Prefab .. '.prefab'
		table.insert(resourceList, path)

		uiLua.wndID = wndID
		--加载预制体中
		uiLua.isLoad = true
		--保存创建参数
		UIManager.WndCreateParam[wndID] = { ... }

		--加载回调参数
		local param = {
			lua = uiLua,
			parent = parent,
			openCloseBut = openCloseBut,
		}
		ResMgr.PreLoadResToCache(json.encode(resourceList), function(resDic, _param)
			---@type UIWndBase
			local _lua = _param.lua
			--父节点
			local _parent = _param.parent
			local _openCloseBut = _param.openCloseBut == true

			print('------UI预加载资源回调------')
			local config2 = Schemes.UIConfig:Get(_lua.wndID)
			if not _lua:Create(config2) then
				warn('UIManager.OpenWnd 创建窗口失败 ', _lua.wndID, config2.LuaFile)
				return
			end
			--预制体加载完成
			_lua.isLoad = false
			local arg = UIManager.WndCreateParam[wndID]
			local canOpen, msg = _lua:CanOpenBase(arg[1], arg[2], arg[3], arg[4], arg[5], arg[6], arg[7])
			if not canOpen then
				if _lua.gameObject then
					_lua.gameObject:SetActive(false)
				end
				if msg and msg ~= '' then
					HelperL.ShowMessage(TipType.FlowText, msg)
				end
				return
			end

			if _lua.gameObject then
				-- 统一延迟两帧显示，避免图集来不及载入造成闪白
				if _lua.uiconfig and _lua.uiconfig.MainType ~= WndType.Tip then
					_lua.gameObject:SetActive(false)
				end
				_lua.slowBeginCount = 0
				table.insert(self.uiActiveList, wndID)
			end

			_lua:OnOpenBase(_parent, _openCloseBut, arg[1], arg[2], arg[3], arg[4], arg[5], arg[6], arg[7])
		end, param)
	else
		local canOpen, msg = uiLua:CanOpenBase(...)
		if not canOpen then
			if uiLua.gameObject then
				uiLua.gameObject:SetActive(false)
			end
			if msg and msg ~= '' then
				warn(msg)
			end
			return
		end

		if uiLua.gameObject then
			-- 统一延迟两帧显示，避免图集来不及载入造成闪白
			if uiLua.uiconfig and uiLua.uiconfig.MainType ~= WndType.Tip then
				uiLua.gameObject:SetActive(false)
			end
			uiLua.slowBeginCount = 0
			table.insert(self.uiActiveList, wndID)
		end
		uiLua:OnOpenBase(parent, openCloseBut, ...)
	end
	return uiLua
end

-- 关闭指定UI
function UIManager:CloseWnd(mainType, closeAll)
	if not mainType then
		warn('UIManager:CloseWnd not mainType')
		return
	end

	if not closeAll then
		-- 关闭最近一个UI
		local list = self.uiOpenList[mainType]
		if not list or list[1] == nil then
			-- 没有可关闭目标，无需处理
			return
		end

		local targetID = list[#list]
		self:CloseWndByID(targetID)
	else
		-- 关闭所有可关闭UI
		local list = self.uiOpenList[mainType]
		if not list then
			-- 没有可关闭目标，无需处理
			return
		end

		local listCount = #list
		for i = listCount, 1, -1 do
			local targetID = list[i]
			self:CloseWndByID(targetID)
		end
	end
end

-- 关闭指定UI
function UIManager:CloseWndByID(wndID)
	if not wndID then
		return false
	end
	self:RemoveWndID(wndID)
	-- 关闭指定UI
	local wndItem = self.uiList[wndID]
	if wndItem then
		local canClose, msg = wndItem:CanCloseBase()
		if not canClose then
			if msg and msg ~= '' then
				HelperL.ShowMessage(TipType.FlowText, msg)
			end
			return false
		end
		if wndItem.OnCloseBase then
			wndItem:OnCloseBase()
		end
	end
	return true
end

---关闭所有UI
---@param wndID ?integer 过滤窗口ID
function UIManager:CloseAllUI(wndID)
	for k, v in pairs(self.uiList) do
		if v and v.wndID ~= wndID then
			self:CloseWndByID(v.wndID)
		end
	end
end

-- 销毁指定UI
function UIManager:DestroyWndByID(wndID)
	if not wndID then
		return
	end

	-- 关闭指定UI
	local wndItem = self.uiList[wndID]
	if wndItem then
		if wndItem:OnDestroyBase() then
			self.uiList[wndID] = nil
		end
	end
end

-- 窗口加入开启队列
function UIManager:AddOpenWndList(mainType, wndID)
	local list = self.uiOpenList[mainType]
	if not list then
		list = {}
		self.uiOpenList[mainType] = list
	end

	for i, v in ipairs(list) do
		if v == wndID then
			-- 目前禁止重复加入，真有需要时再看具体需求
			return
		end
	end

	table.insert(list, wndID)
end

-- 获取窗口是否开启中
function UIManager:IsWndOpen(wndID)
	if not wndID then
		return false
	end

	local wndItem = self.uiList[wndID]
	if not wndItem then
		return false
	end

	return wndItem:IsOpen()
end

-- 获取窗口是否是加载中
function UIManager:IsWndLoad(wndID)
	if not wndID then
		return false
	end

	local wndItem = self.uiList[wndID]
	if not wndItem then
		return false
	end

	return wndItem:IsLoad()
end

--获取打开窗口数量
function UIManager:GetOpenWndCount(mainType)
	local list = self.uiOpenList[mainType]
	if not list then
		return 0
	end
	return #list
end

-- 每帧更新
function UIManager:OnUpdate()
	if self.uiActiveList == nil then
		return
	end
	if self.uiActiveList[1] then
		for i = #self.uiActiveList, 1, -1 do
			local wndID = self.uiActiveList[i]
			local wndItem = self.uiList[wndID]
			if wndItem and wndItem:IsOpen() then
				wndItem.slowBeginCount = wndItem.slowBeginCount + 1
				if wndItem.slowBeginCount >= self.slowBeginInterval then
					wndItem.gameObject:SetActive(true)
					table.remove(self.uiActiveList, i)
				end
			else
				table.remove(self.uiActiveList, i)
			end
		end
	end

	if self.uiWndFrameUpdateList[1] then
		for i = #self.uiWndFrameUpdateList, 1, -1 do
			local wndID = self.uiWndFrameUpdateList[i]
			local wndItem = self.uiList[wndID]
			if wndItem and wndItem:IsOpen() and wndItem.OnFrameUpdate then
				wndItem:OnFrameUpdate()
			else
				table.remove(self.uiWndFrameUpdateList, i)
			end
		end
	end
end

function UIManager.OnSecondUpdate()
	local self = UIManager
	for wndType, list in pairs(self.uiOpenList) do
		if wndType ~= WndType.Tip and wndType ~= WndType.Guide then
			for i = #list, 1, -1 do
				local wndID = list[i]
				if wndID then
					local wndItem = self.uiList[wndID]
					if wndItem and wndItem.OnSecondUpdate then
						-- 仍然不能保证当OnSecondUpdate执行后list发生变化时逻辑正常
						-- 所以这里尽量不要加会导致窗口开关的复杂逻辑
						wndItem:OnSecondUpdate()
					end
				end
			end
		end
	end

	self.garbageCollectCount = self.garbageCollectCount + 1
	if self.garbageCollectCount > 30 then
		-- 每30秒清一次gc
		self.garbageCollectCount = 0
		collectgarbage("collect")
	end
end

-- 窗口移出开启队列
function UIManager:RemoveOpenWndList(mainType, wndID)
	local list = self.uiOpenList[mainType]
	if not list then
		return
	end

	for i, v in ipairs(list) do
		if v == wndID then
			table.remove(list, i)
			break
		end
	end
end

-- 订阅窗口每帧更新
function UIManager:SubscribeFrameUpdate(wndID, isSub)
	if not wndID then
		warn('UIManager:SubscribeFrameUpdate not wndID', debug.traceback())
		return
	end

	if isSub then
		for i, v in ipairs(self.uiWndFrameUpdateList) do
			if v == wndID then
				warn('UIManager:SubscribeFrameUpdate 重复订阅', wndID)
				return
			end
		end
		table.insert(self.uiWndFrameUpdateList, wndID)
	else
		for i, v in ipairs(self.uiWndFrameUpdateList) do
			if v == wndID then
				table.remove(self.uiWndFrameUpdateList, i)
				break
			end
		end
	end
end

-- 发送窗口消息
function UIManager:SendWndMsg(wndID, msg, ...)
	if not self.uiList[wndID] then
		-- 窗口未创建
		return
	end

	self.uiList[wndID]:OnWndMsgBase(msg, ...)
end

-- 设置是否启用窗口开启动画
function UIManager:SetUseWndOpenAni(isUse)
	self.useWndOpenAni = isUse
end

-- 获取是否启用窗口开启动画
function UIManager:GetUseWndOpenAni()
	return self.useWndOpenAni
end

-- 设置是否启用窗口关闭动画
function UIManager:SetUseWndCloseAni(isUse)
	self.useWndCloseAni = isUse
end

-- 获取是否启用窗口开启动画
function UIManager:GetUseWndCloseAni()
	return self.useWndCloseAni
end

-- 校正屏幕坐标到UI坐标
function UIManager:AdjustScreenPos(screenPos)
	screenPos.x = screenPos.x * self.uiScreenScale.x
	screenPos.y = screenPos.y * self.uiScreenScale.y
	return screenPos
end

-- 获取窗口开放等级
function UIManager:GetOpenLevel(wndID)
	return WanOpenLevel[wndID] or 0
end

--- 判断窗口开放等级
---@param wndID integer 窗口ID
---@param isHint? boolean 等级不足时提示, 默认：true
---@return boolean
function UIManager:JudgeOpenLevel(wndID, isHint)
	if not EntityModule.hero then return false end
	local level = EntityModule.hero:GetProperty(CREATURE_FIELD.CREATURE_FIELD_LEVEL)
	local openLevel = UIManager:GetOpenLevel(wndID)
	local isOpen = level >= openLevel
	--等级不足时提示
	if isHint ~= false and not isOpen then
		--提示内容：XX级开启
		HelperL.ShowMessage(TipType.FlowText, string.format(GetGameText(luaID, 1), openLevel))
	end
	return isOpen
end

---获取UI界面--Lua类
---@param wndID integer
---@return UIWndBase
function UIManager:GetUIWndBase(wndID)
	return self.uiList[wndID]
end

---获取顶层窗口ID
---@return integer
function UIManager:GetTopWndID()
	return self.opentWndIDList[#self.opentWndIDList]
end

--- 添加窗口ID
---@param wndID integer
function UIManager:AddWndID(wndID)
	if self:IsExcludeWndID(wndID) then return end
	table.insert(self.opentWndIDList, wndID)
end

---移除窗口ID
---@param wndID integer
function UIManager:RemoveWndID(wndID)
	if self:IsExcludeWndID(wndID) then return end
	local index = 0
	for i, v in ipairs(self.opentWndIDList) do
		if v == wndID then
			index = i
		end
	end
	if index > 0 then
		table.remove(self.opentWndIDList, index)
	end
end

---设置顶层窗口ID(ui置顶)
---@param wndID integer
function UIManager:SetTopWndID(wndID)
	if self:IsExcludeWndID(wndID) then return end
	if self:GetTopWndID() == wndID then
		return
	end
	self:RemoveWndID(wndID)
	self:AddWndID(wndID)
end

---自动打开角色资产界面
function UIManager:AutoOpenRoleAssets()
	--获取顶层窗口ID
	local wndID = self:GetTopWndID()
	if self:IsExcludeWndID(wndID) then return end
	--自动打开角色资产界面
	if AutoOpenRoleAssets and AutoOpenRoleAssets[wndID] then
		UIManager:OpenWnd(WndID.RoleAssets)
	else
		UIManager:CloseWndByID(WndID.RoleAssets)
	end
end

---是排除的窗口ID
---@param wndID any
---@return boolean
function UIManager:IsExcludeWndID(wndID)
	if not wndID then return true end
	if ExcludeWndID[wndID] == true then return true end
	return false
end

-- EventManager:Subscribe(EventID.OnSceneLoaded, UIManager.OnLevelWasLoaded)
