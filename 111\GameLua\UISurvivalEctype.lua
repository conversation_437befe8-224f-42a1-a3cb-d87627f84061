--[[
********************************************************************
    created:    2024/03/31
    author :    李锦剑
    purpose:    生存副本
*********************************************************************
--]]

local luaID = ('UISurvivalEctype')

---@class UISurvivalEctype: UIWndBase
local m = {}

--副本类型
m.ectypeType = Survival_EctypeType

--------------------------------------------------------------------
-- 订阅事件列表
--------------------------------------------------------------------
function m.GetOpenEventList()
    return {
        [EventID.OnHeroPropChange] = m.UpdateView,
        [EventID.FreshenWorld] = m.UpdateView,
        [EventID.OnRankingListUpdate] = m.UpdateView,
    }
end

--------------------------------------------------------------------
-- 创建时
--------------------------------------------------------------------
function m.OnCreate()
    m.slotItem_List = {}
    m.RegisterClickEvent()
    return true
end

--------------------------------------------------------------------
--打开时
--------------------------------------------------------------------
function m.OnOpen()
    m.UpdateView()
end

--------------------------------------------------------------------
--注册点击事件
--------------------------------------------------------------------
function m.RegisterClickEvent()
    m:AddClick(m.objList.Btn_Close, function()
        m:CloseSelf()
    end)
    m:AddClick(m.objList.Btn_Challenge, function()
        local stageID = GamePlayerData.GameEctype:GetChallengeableID(m.ectypeType)
        m.ChuZheng(stageID)
    end)
end

--------------------------------------------------------------------
--更新界面
--------------------------------------------------------------------
function m.UpdateView()
    local stageID = GamePlayerData.GameEctype:GetChallengeableID(m.ectypeType)
    local catMainStage = Schemes.CatMainStage:Get(stageID)
    m.objList.Txt_Title1.text = catMainStage.Name

    local expendList = HelperL.Split(catMainStage.Need, ';')
    --消耗物品ID
    m.expID = tonumber(expendList[1]) or 0
    --消耗物品数量
    m.expNum = tonumber(expendList[2]) or 0
    --广告物品ID
    -- local adID = tonumber(expendList[3]) or 0
    --消耗物品ID2
    m.expID2 = tonumber(expendList[4]) or 0
    --消耗物品数量2
    m.expNum2 = tonumber(expendList[5]) or 0

    local count1 = SkepModule:GetGoodsCount(m.expID)
    local count2 = SkepModule:GetGoodsCount(m.expID2)
    m.objList.Img_ChallengeGray.gameObject:SetActive(count1 < m.expNum)
    local color2 = count2 < m.expNum2 and "#FFA500" or "#ffffff"
    m.objList.Txt_Expend.text = string.format("<color=%s>%s/%s</color>", color2, count2, m.expNum2)
    AtlasManager:AsyncGetGoodsSprite(m.expID2, m.objList.Img_Expend)

    m.objList.Txt_Num.text = string.format(GetGameText(luaID, 1), count1)

    if stageID ~= m.lastStageID then
        m.lastStageID = stageID
        local prizeGoods = Schemes.CatMainStage:GetSucceedRewards(stageID)
        local num = math.max(#prizeGoods, #m.slotItem_List)
        for i = 1, num, 1 do
            if not m.slotItem_List[i] then
                m.slotItem_List[i] = _GAddSlotItem(m.objList.Grid_Goods)
            end
            if prizeGoods[i] then
                m.slotItem_List[i]:SetItemID(prizeGoods[i].ID)
                m.slotItem_List[i]:SetCount(prizeGoods[i].Num)
                m.slotItem_List[i]:SetActive(true)
            else
                m.slotItem_List[i]:SetActive(false)
            end
        end
    end
end

--------------------------------------------------------------------
-- 出征
--------------------------------------------------------------------
function m.ChuZheng(stageID)
    local cfg = Schemes.CatMainStage:Get(stageID)
    if not cfg then
        return
    end

    local expendList = HelperL.Split(cfg.Need, ';')
    --消耗物品ID
    local expID = tonumber(expendList[1]) or 0
    --消耗物品数量
    local expNum = tonumber(expendList[2]) or 0
    --广告物品ID
    -- local adID = tonumber(expendList[3])
    --消耗物品ID2
    local expID2 = tonumber(expendList[4]) or 0
    --消耗物品数量2
    local expNum2 = tonumber(expendList[5]) or 0

    --判断挑战次数
    if HelperL.IsLackGoods(expID, expNum, false, false) then
        HelperL.ShowMessage(TipType.FlowText, GetGameText(luaID, 2))
        return
    end
    --消耗判断
    if HelperL.IsLackGoods(expID2, expNum2, false) then
        return
    end

    m:CloseSelf()
    -- -- 进入战斗
    BattleManager:EnterBattle(cfg.ID)
end

return m
