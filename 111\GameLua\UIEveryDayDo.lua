-- 签到
local luaID = ('UIEveryDayDo')

local UIEveryDayDo = {
    ActivityCountitems = {},
    -- 是否是每日任务 true：每日任务 false:每周任务
    isDayTask = nil,
    -- 生成的Toggle物体
    m_ToggleObjs = {},
    -- 生成的物体
    m_CloneObjs = {},
    -- 累计登录多少天
    curSelDay = 0,
    -- 七日登录item
    m_DayLoginItem = {},
    -- 七日目标Btnitem
    m_DayTargetBtnItem = {},
    -- 七日目标item
    m_DayTargetItem = {},
    -- 七日目标config
    m_DayTargetConfig = {},
    -- 七日目标生成的物体
    m_DayTargetItemCloneObjs = {},
    -- 当前开服多少天
    m_curDay = 0,
    -- 成就items
    m_AchieveItems = {},
    -- 成就类型items
    m_AchieveTypeItems = {},
    -- 点击领取 刷新scrollviewItem位置
    itemPos = 0,
    activePos = 0
}

-- 订阅事件列表
function UIEveryDayDo:GetOpenEventList()
    return {
        -- [EventID.AdvertisePlayComplete] = self.OnAdvertisePlayComplete,
    }
end

-- 初始化
function UIEveryDayDo:OnCreate()
    self.objList.Btn_Close.onClick:AddListenerEx(self.OnClickClose)
	self.objList.Btn_Txt_Close.onClick:AddListenerEx(self.OnClickClose)
    return true
end

-- 获取第一个奖励图片和数量
function UIEveryDayDo.GetSprite(ID, i)
    i = (i and i) or 1
    local prize = Schemes.PrizeTable:GetPrize(ID)
    local prizeGoods = Schemes.PrizeTable:GetPrizeGoods(prize)
    if prizeGoods and prizeGoods[i] then
        local goods = prizeGoods[i]
        local item = Schemes.Medicament:Get(goods.ID)
        if item then
            local name = item.GoodsName
            local num = goods.Num
            local sprite = item.IconID
            return name, num, sprite
        end
    end
    return nil, nil, nil
end

-- 数据更新处理
function UIEveryDayDo.OnAdvertisePlayComplete(inspireType, refresh)
    local self = UIEveryDayDo
    if inspireType ~= AdvertisementManager.InspireType.Sign or not refresh then
        return
    end
    self:SetAwardState()
end

-- 窗口开启
function UIEveryDayDo:OnOpen(id)
    for i = 1, #UIEveryDayDo.m_ToggleObjs do
        if UIEveryDayDo.m_ToggleObjs[i].gameObject then
            GameObject.Destroy(UIEveryDayDo.m_ToggleObjs[i].gameObject)
        end
    end
    UIEveryDayDo.m_ToggleObjs = {}
    self = UIEveryDayDo
    local nameListStr = GetGameText(luaID, 7)
    local nameList = HelperL.Split(nameListStr, ";")
    local nameListStr1 = GetGameText(luaID, 9)
    local nameList1 = HelperL.Split(nameListStr1, ";")
    local nameListStr2 = GetGameText(luaID, 1)
    local nameList2 = HelperL.Split(nameListStr2, ";")
    for i = 1, #nameList do
        local parent = self.objList.ToggleParent:GetRectTransform()
        local obj = nil
        obj = self:CreateSubItem(parent, self.objList.Tog_Mode)
        AtlasManager:AsyncGetSprite(nameList[i],  obj.Img_Name)
        AtlasManager:AsyncGetSprite(nameList1[i], obj.Img_Name1)
        obj.Txt_Name.text = nameList2[i]
        if i == id then
            obj.Tog_M.isOn = true
        end
        local toggle = obj.Tog_M.gameObject:GetComponent('Toggle')
        toggle.onValueChanged:AddListener(function(ison)
            self:ToggleCommon(ison, i)
        end)
        table.insert(self.m_ToggleObjs, obj)
        UIEveryDayDo:TagRedDot(i, obj)
    end
    UIEveryDayDo:ToggleCommon(true, id or 1)
end
-- 每日必做类型红点
function UIEveryDayDo:TagRedDot(i, obj)
--	print(i)
    -- 每日任务红点显示
    if i == 1 then

        -- 每周任务红点显示
    elseif i == 2 then

        -- 七天登录红点显示
    elseif i == 3 then
        obj.RedDot.gameObject:SetActive(UIEveryDayDo:DayLoginIsRedDot())
        -- 七天目标红点显示
    elseif i == 4 then
        obj.RedDot.gameObject:SetActive(UIEveryDayDo:SevenDayTargetUpdateUI())
        -- 成就红点显示
    elseif i == 5 then
        obj.RedDot.gameObject:SetActive(UIEveryDayDo:AchieveTagRedDot())
    end
end

-- 点击Toggle公共方法,隐藏其他功能的节点
function UIEveryDayDo:ToggleCommon(ison, id)
    self:Clean()
    for i = 1, #self.m_ToggleObjs do
        if i == id then
            self.m_ToggleObjs[i].Img_Light.gameObject:SetActive(true)
        else
            self.m_ToggleObjs[i].Img_Light.gameObject:SetActive(false)
        end
    end
    if ison then
        local AllNode = self.objList.AllNode:GetRectTransform()
        -- 只有4个子物体 每日任务和每周任务是公用的
        for i = 1, AllNode.transform.childCount do
            if id < 3 then
                AllNode.transform:GetChild(0).gameObject:SetActive(true)
                if i > 1 then
                    AllNode.transform:GetChild(i - 1).gameObject:SetActive(false)
                end
            else
                if i == id - 1 then
                    AllNode.transform:GetChild(i - 1).gameObject:SetActive(true)
                else
                    AllNode.transform:GetChild(i - 1).gameObject:SetActive(false)
                end
            end
        end
        if id == 1 then
            self.isDayTask = true
            self:Task(1)
            self.objList.Txt_title.text = '日常'
        elseif id == 2 then
            self.isDayTask = false
            self:Task(2)
            self.objList.Txt_title.text = '周常'
        elseif id == 3 then
            self:SevenDayLogin()
        elseif id == 4 then
            self:SevenDayTarget()
        elseif id == 5 then
            self:Achieve()
        end
    end
end

-- 清楚所有克隆的物体和集合
function UIEveryDayDo:Clean()
    for i = 1, #UIEveryDayDo.m_CloneObjs do
        if UIEveryDayDo.m_CloneObjs[i].gameObject then
            GameObject.Destroy(UIEveryDayDo.m_CloneObjs[i].gameObject)
        end
    end
    UIEveryDayDo.m_CloneObjs = {}
    UIEveryDayDo.m_DayLoginItem = {}
    UIEveryDayDo.m_DayTargetItem = {}
    UIEveryDayDo.m_DayTargetBtnItem = {}
    UIEveryDayDo.m_DayTargetConfig = {}
    UIEveryDayDo.ActivityCountitems = {}
    UIEveryDayDo.curSelDay = 0
    UIEveryDayDo.m_AchieveItems = {}
    UIEveryDayDo.m_AchieveTypeItems = {}
    UIEveryDayDo.itemPos = 0
    UIEveryDayDo.activePos = 0
end

-- #region 每日任务 每周任务
function UIEveryDayDo:Task(type)
    -- 任务item
    UIEveryDayDo:TaskItem(type)
    -- 活跃宝箱Item
    UIEveryDayDo:HuoYueBaoXiang()
end

function UIEveryDayDo:TaskItem(type)
    -- 每日任务item
    for k, v in ipairs(Schemes.ActivityCount.items) do
        if type == 1 then
            if v.ShowPage == type then
                table.insert(UIEveryDayDo.ActivityCountitems, v)
            end
        end
        if type == 2 then
            if v.ShowPage == type then
                table.insert(UIEveryDayDo.ActivityCountitems, v)
            end
        end
    end
    for i = 1, #UIEveryDayDo.ActivityCountitems do
        local parent = self.objList.ContentDayTask:GetRectTransform()
        local obj = self:CreateSubItem(parent, self.objList.DayTaskItem)
        obj.Txt_Name.text = UIEveryDayDo.ActivityCountitems[i].Name
        -- local name, num, spr = self.GetSprite(UIEveryDayDo.ActivityCountitems[i].ShowPrizeID, 1)
        -- obj.Img_Icon.sprite = spr
        local nowCount = 0 -- EntityModule.hero.HeroTaskLC.GetTaskGroupCount(UIEveryDayDo.ActivityCountitems[i].CountValueParam1)
        local maxCount = Schemes.TaskBranch.GroupCount(UIEveryDayDo.ActivityCountitems[i].CountValueParam1)
        obj.Txt_Process.text = nowCount .. "/" .. maxCount
        obj.Sld_Process.value = nowCount / maxCount
        local btnimage =  obj.Btn_Go:GetComponent('Image')
        if maxCount == 0 then
            obj.Txt_type.text = '前往'
            AtlasManager:AsyncGetSprite('shengjianniu3', btnimage)
            -- obj.Txt_Go.text = GetGameText(luaID, 2)
            obj.Btn_Go.onClick:AddListenerEx(function()
                -- 跳转指定窗口
            end)
        else
            if nowCount == maxCount then
                AtlasManager:AsyncGetSprite('lingqu', obj.Img_type)
                obj.Txt_type.text = '领取'
                AtlasManager:AsyncGetSprite('anniu', btnimage)
                -- obj.Txt_Go.text = GetGameText(luaID, 3)
                obj.Btn_Go.onClick:AddListenerEx(function()
                    -- 领取任务奖励

                end)
            else
                obj.Txt_type.text = '前往'
                AtlasManager:AsyncGetSprite('shengjianniu3', btnimage)
                -- obj.Txt_Go.text = GetGameText(luaID, 2)
                obj.Btn_Go.onClick:AddListenerEx(function()
                    -- 跳转指定窗口
                end)
            end
        end
        obj.Img_type:SetNativeSize()
        table.insert(UIEveryDayDo.m_CloneObjs, obj)
    end
end

function UIEveryDayDo:HuoYueBaoXiang()
    -- 获取每日活跃值
    local curActive
    if self.isDayTask then
        curActive = HeroDataManager:GetLogicData(LOGIC_DATA.DATA_ACTIVITYCOUNT_VALUE)
    else
        curActive = HeroDataManager:GetLogicData(LOGIC_DATA.DATA_ACTIVITYCOUNT_VALUE)
    end
    -- print("获取活跃值:" .. curActive)
    local ActivityCountPrizeitems = {}
    for _, v in ipairs(Schemes.ActivityCountPrize.items) do
        if self.isDayTask then
            if v.ValueType == 1 then
                table.insert(ActivityCountPrizeitems, v)
            end
        else
            if v.ValueType == 2 then
                table.insert(ActivityCountPrizeitems, v)
            end
        end
    end
    for i = 1, #ActivityCountPrizeitems do
        local parent = self.objList.Sld_BaoXiangProcess:GetRectTransform()
        local parentRect = parent.gameObject:GetComponent('RectTransform')
        local processWidth = parentRect.sizeDelta.x
        local obj = self:CreateSubItem(parent, self.objList.BaoXiangItem)
        obj.Txt_Name.text = ActivityCountPrizeitems[i].Desc
        obj.Txt_NeedCount.text = ActivityCountPrizeitems[i].NeedCount
        local objRect = obj.gameObject:GetComponent('RectTransform')
        -- 计算宝箱的位置
        local pos =
            (ActivityCountPrizeitems[i].NeedCount / ActivityCountPrizeitems[#ActivityCountPrizeitems].NeedCount) *
                processWidth
        objRect.anchoredPosition = CachedVector2:Set(pos, 0)
        local data
        if self.isDayTask then
            data = HeroDataManager:GetLogicData(LOGIC_DATA.DATA_ACTIVITYCOUNT_PRIZEGETFLAG)
        else
            data = HeroDataManager:GetLogicData(LOGIC_DATA.DATA_ACTIVITYCOUNT_PRIZEGETFLAG)
        end
        local isReceived = HeroDataManager:GetLogicBit(data, ActivityCountPrizeitems[i].ID)
        -- print("index是否领取:" .. isReceived)
        -- local haloEffect = UIDailyPlay.prizeList[i].transform:Find("HaloEffect(Clone)")
        if curActive >= ActivityCountPrizeitems[i].NeedCount and isReceived == 0 then
            obj.RedPot:SetActive(true)
            -- 添加光效
            -- if not haloEffect then
            --     NGUITools.AddChild(UIDailyPlay.prizeList[i], HotResManager.ReadUI('ui/MainLand/prefab/HaloEffect').gameObject)
            -- else
            --     haloEffect.gameObject:SetActive(true)
            -- end
            obj.Btn_LeiJiItem.onClick:AddListenerEx(function()
                -- print(i .. "领取活跃宝箱")
                -- 领取活跃宝箱
                UIEveryDayDo:OnActivePrizeClick(i)
            end)
        end
        if isReceived == 1 then
            obj.RedPot:SetActive(false)
            -- if haloEffect then
            --     haloEffect.gameObject:SetActive(false)
            -- end
        end
        -- local name,num,spr = self.GetSprite(UIEveryDayDo.ActivityCountitems[i].ShowPrizeID,i)
        -- obj.Img_Icon.sprite = spr
        table.insert(UIEveryDayDo.m_CloneObjs, obj)
    end

    -- 显示活跃进度条
    self.objList.Sld_BaoXiangProcess.value = curActive / 150
end

-- 活跃度奖励领取方法
function UIEveryDayDo:OnActivePrizeClick(index)
    local curActive
    if self.isDayTask then
        curActive = HeroDataManager:GetLogicData(LOGIC_DATA.DATA_ACTIVITYCOUNT_VALUE)
    else
        curActive = HeroDataManager:GetLogicData(LOGIC_DATA.DATA_ACTIVITYCOUNT_VALUE)
    end
    local needActive = nil
    local tableID = nil
    local vipLevel = EntityModule.hero:GetProperty(PLAYER_FIELD.PLAYER_FIELD_VIPLEVEL)
    -- print("vipLevel:" .. vipLevel)
    local item = Schemes.ActivityCountPrize.Get(index)
    if not item then
        warn("未找到ActivityCountPrize中ID：", index)
        return
    end
    local vipPrizeID = item.PrizeID[vipLevel + 1]
    if item.PrizeID[vipLevel + 1] == nil then
        local num = #item.PrizeID
        vipPrizeID = item.PrizeID[num]
    end
    needActive = item.NeedCount
    tableID = item.ID
    if not needActive and not tableID then
        warn('活跃奖励表ID配置错误')
        return
    end
    if curActive >= needActive then
        -- 领取过的奖励按钮置为灰色
        local data
        if self.isDayTask then
            data = HeroDataManager:GetLogicData(LOGIC_DATA.DATA_ACTIVITYCOUNT_PRIZEGETFLAG)
        else
            data = HeroDataManager:GetLogicData(LOGIC_DATA.DATA_ACTIVITYCOUNT_PRIZEGETFLAG)
        end
        local isReceived = HeroDataManager:GetLogicBit(data, index)
        -- print("index是否领取:" .. isReceived)
        if isReceived ~= 0 then
            UIManager.AddPrizeTips(vipPrizeID, false, nil, nil)
        else
            UIManager.AddPrizeTips(vipPrizeID, true, nil, function()
                if curActive >= needActive then
                    local str = 'LuaRequestGetActivityCountPrize?id=%d' .. tableID
                    LuaModule.RunLuaRequest(str, function(resultCode, content)
                        if resultCode == RESULT_CODE.RESULT_COMMON_SUCCEED then
                            HelperL.ShowMessage(TipType.FlowText, GetGameText(luaID, 15))
                        else
                            HelperL.ShowMessage(TipType.FlowText, ResultCode:GetResultCodeStr(resultCode, content))
                        end
                    end)
                else
                    HelperL.AddAMessageTip(GetGameText(luaID, 14))
                end
            end)
        end
    else
        UIManager.AddPrizeTips(vipPrizeID, false, nil, nil)
    end
end

-- #endregion

-- #region 七日登录
function UIEveryDayDo.SevenDayLogin()
    UIEveryDayDo.curSelDay = HeroDataManager:GetLogicData(LOGIC_DATA.DATA_LOGIN_TOTALDAYS)
    if UIEveryDayDo.curSelDay < 1 then
        UIEveryDayDo.curSelDay = 1
    elseif UIEveryDayDo.curSelDay > 7 then
        UIEveryDayDo.curSelDay = 7
    end
    -- print("连续登录" .. UIEveryDayDo.curSelDay .. "天")
    UIEveryDayDo:CloneItem()
end

function UIEveryDayDo:CloneItem()
    -- 七天登录Item
    for i, v in ipairs(Schemes.LoginReward.items) do
        local parent = self.objList.ContentSevenDayLogin:GetRectTransform()
        local obj = self:CreateSubItem(parent, self.objList.DayLoginItem)
        obj.Txt_Name.text = i
        obj.Txt_Go.text = GetGameText(luaID, 3)
        -- 七天登录奖励Item
        local prize = Schemes.PrizeTable:GetPrize(v.PrizeID)
        local prizeGoods = Schemes.PrizeTable:GetPrizeGoods(prize)
        for i = 1, #prizeGoods do
            local prizeparent = obj.Content:GetRectTransform()
            local prizeobj = self:CreateSubItem(prizeparent, self.objList.DayLoginPrizeItem)
            if prizeGoods then
                local goods = prizeGoods[i]
				--if HelperL.IsEuipType(goods.ID) then
					
				--else
					
				--end
                local item = Schemes:GetGoodsConfig(goods.ID)
                if item then
                    local name = item.GoodsName
                    local num = goods.Num
                    AtlasManager:AsyncGetSprite(item.IconID, prizeobj.Img_DayTaskItem)
                    prizeobj.Txt_Name.text = name
					if goods.ID == 4 then num = HelperL.ExChangeNum(num) end
                    prizeobj.Txt_Num.text = num
                end
                prizeobj.Btn_bg.onClick:AddListenerEx(function()
                    local GoodTips = {}
                    GoodTips.itemID = goods.ID
					GoodTips.justShow = true
                    HelperL.OnShowTips(GoodTips)
                end)
            end
            table.insert(UIEveryDayDo.m_CloneObjs, prizeobj)
        end
        table.insert(UIEveryDayDo.m_CloneObjs, obj)
        table.insert(UIEveryDayDo.m_DayLoginItem, obj)
    end
    UIEveryDayDo:UpdateUI(true)
end

-- 是否有东西可领取 红点显示
function UIEveryDayDo:DayLoginIsRedDot()
    local curSelDay = HeroDataManager:GetLogicData(LOGIC_DATA.DATA_LOGIN_TOTALDAYS)
    for i = 1, 7 do
        -- 这天的登录是否领取过 0未领取(现在可以领取)1领取过(不能领取)
        local gotPrizeFlag = HeroDataManager:GetLogicBit(LOGIC_DATA.DATA_LOGINPRIZE_GOTFLAG, i) == 0
        -- 这天的登录是否可领取(可能之前未领取)
        if gotPrizeFlag then
            if curSelDay >= i then
                return true
            end
        end
    end
    return false
end

function UIEveryDayDo:UpdateUI(isaddOnClick)
    for i = 1, #UIEveryDayDo.m_DayLoginItem do
        -- 这天的登录是否领取过 0未领取(现在可以领取)1领取过(不能领取)
        local gotPrizeFlag = HeroDataManager:GetLogicBit(LOGIC_DATA.DATA_LOGINPRIZE_GOTFLAG, i) == 0
        -- print("第" .. i .. "天奖励是否可领取:" .. tostring(gotPrizeFlag))
        -- 这天的登录是否可领取(可能之前未领取)
        local canGet = false
        if gotPrizeFlag then
            if UIEveryDayDo.curSelDay >= i then
                canGet = true
            end
        end
        local btnImage =  UIEveryDayDo.m_DayLoginItem[i].Btn_Go:GetComponent('Image')
        if canGet then
            AtlasManager:AsyncGetSprite('tongyongdikuang1', UIEveryDayDo.m_DayLoginItem[i].Img_light)
            UIEveryDayDo.m_DayLoginItem[i].Txt_type.text = '领取'
            btnImage.enabled = true
            UIEveryDayDo.m_DayLoginItem[i].Btn_Go.gameObject:SetActive(true)
            UIEveryDayDo.m_DayLoginItem[i].RedDot.gameObject:SetActive(true)
            if isaddOnClick then
                UIEveryDayDo.m_DayLoginItem[i].Btn_Go.onClick:AddListenerEx(function()
                    self:GetPrizeClick(i)
                end)
            end
        else
            UIEveryDayDo.m_DayLoginItem[i].RedDot.gameObject:SetActive(false)
            AtlasManager:AsyncGetSprite('dikuang (2)', UIEveryDayDo.m_DayLoginItem[i].Img_light)
            UIEveryDayDo.m_DayLoginItem[i].Txt_type.text = '已领取'
            btnImage.enabled = false
            UIEveryDayDo.m_DayLoginItem[i].Btn_Go.gameObject:SetActive(false)
        end
        if UIEveryDayDo.curSelDay < i then
            UIEveryDayDo.m_DayLoginItem[i].Txt_type.text = '未开始'
            btnImage.enabled = false
            AtlasManager:AsyncGetSprite('tongyongdikuang2', UIEveryDayDo.m_DayLoginItem[i].Img_light)
        end
        UIEveryDayDo.m_DayLoginItem[i].Img_type:SetNativeSize()
    end
    UIEveryDayDo.m_ToggleObjs[3].RedDot.gameObject:SetActive(UIEveryDayDo:DayLoginIsRedDot())
end

-- 领取当天登录奖励
function UIEveryDayDo:GetPrizeClick(id)
    -- 这天的登录是否领取过 0未领取(现在可以领取)1领取过(不能领取)
    local gotPrizeFlag = HeroDataManager:GetLogicBit(LOGIC_DATA.DATA_LOGINPRIZE_GOTFLAG, id) == 1
    if gotPrizeFlag then
        return
    end
    if UIEveryDayDo.curSelDay < id then
        return
    end
    local str = 'LuaRequestGetLoginReward?id=' .. id
    LuaModule.RunLuaRequest(str, function(resultCode, content)
        if resultCode == RESULT_CODE.RESULT_COMMON_SUCCEED then
            UIEveryDayDo:UpdateUI(false)
        else
            HelperL.ShowMessage(TipType.FlowText, ResultCode:GetResultCodeStr(resultCode, content))
        end
    end)
end

-- #endregion

-- #region 七日目标
function UIEveryDayDo:SevenDayTarget()
    local iconNames = GetGameText(luaID, 8)
    local nameList = HelperL.Split(iconNames, ";")
    local remainingAllTime = HelperL.GetSevenTargetTime(7)
    UIEveryDayDo.curDay = 7 - math.floor(remainingAllTime / (60 * 60 * 24))
    local nameListStr = GetGameText(luaID, 10)
    local nameList = HelperL.Split(nameListStr, ";")
    -- 创建7天的按钮
    for i = 1, 7 do
        local parent = self.objList.ContentSevenDayTarget:GetRectTransform()
        local obj = self:CreateSubItem(parent, self.objList.DayTargetBtnItem)
        obj.Txt_Name.text = nameList[i]
        obj.Btn_click.onClick:AddListenerEx(function()
            self:SevenDayTargetItemClick(i)
        end)
        table.insert(UIEveryDayDo.m_DayTargetBtnItem, obj)
        table.insert(UIEveryDayDo.m_CloneObjs, obj)
    end
    for k, v in ipairs(Schemes.ActivityNewbieTargetReward.items) do
        table.insert(UIEveryDayDo.m_DayTargetConfig, v)
    end
    self:SevenDayTargetItemClick(1)
end

function UIEveryDayDo:SevenDayTargetItemClick(day)
    for i = 1, #UIEveryDayDo.m_DayTargetItemCloneObjs do
        if UIEveryDayDo.m_DayTargetItemCloneObjs[i].gameObject then
            GameObject.Destroy(UIEveryDayDo.m_DayTargetItemCloneObjs[i].gameObject)
        end
    end
    for i = 1, #UIEveryDayDo.m_DayTargetBtnItem do
        if i == day then
            if i ~= 7 then
                UIEveryDayDo.m_DayTargetBtnItem[i].Img_light.gameObject:SetActive(true)
            end
            UIEveryDayDo.m_DayTargetBtnItem[i].Img_light1.gameObject:SetActive(true)
        else
            UIEveryDayDo.m_DayTargetBtnItem[i].Img_light.gameObject:SetActive(false)
            UIEveryDayDo.m_DayTargetBtnItem[i].Img_light1.gameObject:SetActive(false)
        end
    end
    UIEveryDayDo.m_DayTargetItemCloneObjs = {}
    for i = 1, #UIEveryDayDo.m_DayTargetConfig do
        local v = UIEveryDayDo.m_DayTargetConfig[i]
        if v.Pagination == day then
            local parent = self.objList.ContentSevenDayTargetItem:GetRectTransform()
            local obj = self:CreateSubItem(parent, self.objList.DayTargetItem)
            obj.Txt_Name.text = v.DayDesc
            obj.Txt_Process.text = "0/0"
            obj.Sld_Process.value = 0
            -- 七天目标奖励Item
            local prize = Schemes.PrizeTable:GetPrize(v.PrizeID)
            local prizeGoods = Schemes.PrizeTable:GetPrizeGoods(prize)
            for i = 1, #prizeGoods do
                local prizeparent = obj.Content:GetRectTransform()
                local prizeobj = self:CreateSubItem(prizeparent, self.objList.DayLoginPrizeItem)
                if prizeGoods then
                    local goods = prizeGoods[i]
                    local item = Schemes.Medicament:Get(goods.ID)
                    if item then
                        local name = item.GoodsName
                        local num = goods.Num
                        AtlasManager:AsyncGetSprite(item.IconID, prizeobj.Img_DayTaskItem)
                        prizeobj.Txt_Name.text = name
                        prizeobj.Txt_Num.text = num
                    end
                    prizeobj.Btn_bg.onClick:AddListenerEx(function()  
                        local GoodTips = {}
                        GoodTips.itemID = goods.ID
						GoodTips.justShow = true
                        HelperL.OnShowTips(GoodTips)
                    end)
                end
            end
            local result = RedDotManager.ReturnSevenDayGetPrizeResult(v.ID, UIEveryDayDo.curDay)
            if result == 0 then
                obj.RedDot.gameObject:SetActive(true)
                obj.Txt_Image.text = '领取'
                UIEveryDayDo.m_DayTargetBtnItem[day].RedDot.gameObject:SetActive(true)
                UIEveryDayDo.m_ToggleObjs[4].RedDot.gameObject:SetActive(true)
                obj.Btn_Go.onClick:AddListenerEx(function()
                    UIEveryDayDo:GetFinalPrize(v.ID, i)
                end)
            elseif result == 1 then
                obj.Txt_Image.text = '已完成'
            else
                obj.Txt_Image.text = '前往'
            end
            obj.Img_Image:SetNativeSize()
            table.insert(UIEveryDayDo.m_DayTargetItemCloneObjs, obj)
        end
    end

end

-- 领取最终奖励
function UIEveryDayDo:GetFinalPrize(finalTargetID, i)
    local str = 'LuaRequestGetNewbieTargetReward?targetID=%d' .. finalTargetID
    LuaModule.RunLuaRequest(str, function(resultCode, content)
        if resultCode == RESULT_CODE.RESULT_COMMON_SUCCEED then
            UIEveryDayDo.m_DayTargetItemCloneObjs[i].Txt_Go.text = GetGameText(luaID, 4)
            UIEveryDayDo.m_DayTargetItemCloneObjs[i].RedDot.gameObject:SetActive(false)
            UIEveryDayDo:SevenDayTargetUpdateUI()
        else
            HelperL.ShowMessage(TipType.FlowText, ResultCode:GetResultCodeStr(resultCode, content))
        end
    end)
end

-- 更新红点
function UIEveryDayDo:SevenDayTargetUpdateUI()
    local isred = RedDotManager:CheckSevenDayUI()
    UIEveryDayDo.m_ToggleObjs[4].RedDot.gameObject:SetActive(isred)
    return isred
end

-- #endregion

-- #region 成就
local prizeAchieveDataCenter = require 'prizeAchieveDataCenter'
function UIEveryDayDo:Achieve()
    local t = prizeAchieveDataCenter.GetPrizeTypeSum()
    for i = 1, #t do
        local v = t[i]
        local parent = self.objList.ContentAchieveType:GetRectTransform()
        local obj = self:CreateSubItem(parent, self.objList.AchieveTypeItem)
        -- local parent = self.objList.ContentAchieve:GetRectTransform()
        -- local obj = self:CreateSubItem(parent, self.objList.AchieveItem)
        -- local name, num, spr = self.GetSprite(v.PrizeID, 1)
        -- obj.Img_Icon.sprite = spr
        obj.Txt_Name.text = v.ConditionDesc
        obj.Btn_Go.onClick:AddListenerEx(function()
            UIEveryDayDo:AchieveItem(i, v)
        end)
        table.insert(UIEveryDayDo.m_AchieveTypeItems, obj)
        table.insert(UIEveryDayDo.m_CloneObjs, obj)
    end
    UIEveryDayDo:AchieveItem(1, t[1])
end
-- type：第几个成就类型
function UIEveryDayDo:AchieveItem(type, typeconfig)
    -- btn更换图片背景
    for i = 1, #UIEveryDayDo.m_AchieveTypeItems do
        if i == type then
            if i ~= 7 then
                UIEveryDayDo.m_AchieveTypeItems[i].Img_light.gameObject:SetActive(true)
            end
            UIEveryDayDo.m_AchieveTypeItems[i].Img_light1.gameObject:SetActive(true)
        else
            UIEveryDayDo.m_AchieveTypeItems[i].Img_light.gameObject:SetActive(false)
            UIEveryDayDo.m_AchieveTypeItems[i].Img_light1.gameObject:SetActive(false)
        end
    end
    -- 销毁生成的m_AchieveItems
    for i = 1, #UIEveryDayDo.m_AchieveItems do
        if UIEveryDayDo.m_AchieveItems[i].gameObject then
            GameObject.Destroy(UIEveryDayDo.m_AchieveItems[i].gameObject)
        end
    end
    UIEveryDayDo.m_AchieveItems = {}
    local t = prizeAchieveDataCenter.GetPrizeTypeList(typeconfig.Condition)
    if t then
        for k, v in pairs(t) do
            local parent = self.objList.ContentAchieve:GetRectTransform()
            local obj = self:CreateSubItem(parent, self.objList.AchieveItem)
            -- local name, num, spr = self.GetSprite(v.PrizeID, 1)
            -- obj.Img_Icon.sprite = spr
            obj.Txt_Process.text = "0/0"
            -- 七天目标奖励Item
            local prize = Schemes.PrizeTable:GetPrize(v.PrizeID)
            local prizeGoods = Schemes.PrizeTable:GetPrizeGoods(prize)
            for i = 1, #prizeGoods do
                local prizeparent = obj.Content:GetRectTransform()
                local prizeobj = self:CreateSubItem(prizeparent, self.objList.DayLoginPrizeItem)
                if prizeGoods then
                    local goods = prizeGoods[i]
                    local item = Schemes.Medicament:Get(goods.ID)
                    if item then
                        local name = item.GoodsName
                        local num = goods.Num
                        AtlasManager:AsyncGetSprite(item.IconID, prizeobj.Img_DayTaskItem)
                        prizeobj.Txt_Name.text = name
                        prizeobj.Txt_Num.text = num
                    end
                    prizeobj.Btn_bg.onClick:AddListenerEx(function()
                        local GoodTips = {}
                        GoodTips.itemID = goods.ID
						GoodTips.justShow = true
                        HelperL.OnShowTips(GoodTips)
                    end)
                end
            end
            UIEveryDayDo:UpdatePrizeItem(v, obj, type)
            table.insert(UIEveryDayDo.m_AchieveItems, obj)
            table.insert(UIEveryDayDo.m_CloneObjs, obj)
        end
    end
end

-- 1已达成未领取 2未达成未领取 3已达成已领取
function UIEveryDayDo:UpdatePrizeItem(schemes, o, type)
    local key = prizeAchieveDataCenter.GetPrizeState(schemes.ID)
    local x, y = prizeAchieveDataCenter.GetPrizeValue(schemes.ID)
    o.Sld_Process.value = x / y
    o.Txt_Process.text = x .. "/" .. y
    local btnimage =  o.Btn_Go:GetComponent('Image')
    if key == 1 then
        AtlasManager:AsyncGetSprite('lingqu',  o.Img_Image)
        o.Txt_Image.text = '领取'
        AtlasManager:AsyncGetSprite('anniu', btnimage)
        o.RedDot.gameObject:SetActive(true)
        o.transform:SetSiblingIndex(UIEveryDayDo.itemPos)
        UIEveryDayDo.itemPos = UIEveryDayDo.itemPos + 1
    elseif key == 2 then
        AtlasManager:AsyncGetSprite('qianwang', o.Img_Image)
        o.Txt_Image.text = '前往'
        AtlasManager:AsyncGetSprite('shengjianniu3', btnimage)
        o.RedDot.gameObject:SetActive(false)
        o.transform:SetSiblingIndex(UIEveryDayDo.itemPos + UIEveryDayDo.activePos)
        UIEveryDayDo.activePos = UIEveryDayDo.activePos + 1
    elseif key == 3 then
        AtlasManager:AsyncGetSprite('yiwancheng', o.Img_Image)
        o.Txt_Image.text = '已完成'
        AtlasManager:AsyncGetSprite('yiwanchengdikuang', btnimage)
        o.RedDot.gameObject:SetActive(false)
        o.transform:SetAsLastSibling()
    end
    o.Img_Image:SetNativeSize()
    o.Btn_Go.onClick:AddListenerEx(function()
        if key == 1 then
            UIEveryDayDo:AchieveGetFinalPrize(schemes, o)
        elseif key == 2 then
            HelperL.ShowMessage(TipType.FlowText, GetGameText(luaID, 6))
        end
    end)
    -- 成就Typebtn红点
    UIEveryDayDo:AchieveTypeRedDot(schemes, type)
    -- 成就标签btn红点
    UIEveryDayDo:AchieveTagRedDot()
end

-- 成就Typebtn红点
-- type：第几个成就类型
function UIEveryDayDo:AchieveTypeRedDot(schemes, type)
    local redDot = RedDotManager:AchieveTypeRedDot(schemes, type)
    if #UIEveryDayDo.m_AchieveTypeItems > 0 then
        UIEveryDayDo.m_AchieveTypeItems[type].RedDot.gameObject:SetActive(redDot)
    end
    return redDot
end

-- 成就标签btn红点
function UIEveryDayDo:AchieveTagRedDot()
    local redDot = RedDotManager:CheckAchieveUI()
    UIEveryDayDo.m_ToggleObjs[5].RedDot.gameObject:SetActive(redDot)
    return redDot
end

-- 领取最终奖励
function UIEveryDayDo:AchieveGetFinalPrize(schemes, o)
    local str = string.format("LuaRequestGetPrizeAchieveGift?giftID=%d", schemes.ID)
    LuaModule.RunLuaRequest(str, function(resultCode, content)
        if resultCode == RESULT_CODE.RESULT_COMMON_SUCCEED then
            UIEveryDayDo:UpdatePrizeItem(schemes, o)
        else
            HelperL.ShowMessage(TipType.FlowText, ResultCode:GetResultCodeStr(resultCode, content))
        end
    end)
end

-- #endregion

-- 窗口页面关闭
function UIEveryDayDo.OnClickClose()
    local self = UIEveryDayDo
    self:CloseSelf()
end

-- 每秒更新
function UIEveryDayDo:OnSecondUpdate()

end

-- 窗口关闭
function UIEveryDayDo:OnClose()
end

-- 窗口销毁
function UIEveryDayDo:OnDestroy()
end

return UIEveryDayDo
