-- 活动玩法模块
require 'GameMessage_pb'
require 'GlobalGameMessage_pb'

local luaID = ('GameModule')

GameModule = {}

function GameModule.Handle(action, data)
	-- 收到每个包都更新心跳时间
	GameModule:OnReceiveHeartBeat()

	if action == GameMessage_pb.MSG_ACTIVITY_SYNCSTATE then
		-- 活动状态下发和更新
		local m = GameMessage_pb.SC_Activity_SyncState()
		m:ParseFromString(data)
		ActivityManager:UpdateStateList(m.StateList)
	elseif action == GameMessage_pb.MSG_GAME_HEARTBEAT then
		GameModule:OnReceiveHeartBeat()
	elseif action == GameMessage_pb.MSG_RESULTCODE_SHOW then
		local m = GameMessage_pb.SC_ResultCode_Show()
		m:ParseFromString(data)
		GameModule:OnRecvResultCodeShow(m)
	elseif action == GameMessage_pb.MSG_TESTFUNC_ACTIVE then
		local m = GameMessage_pb.SC_TestFunc_Active()
		m:ParseFromString(data)
		GameModule:OnRecvTestFuncActive(m)
	elseif action == GameMessage_pb.MSG_GLOBAL_SWITCHZONE then
		local m = GameMessage_pb.SC_Global_SwitchZone()
		m:ParseFromString(data)
		GameModule:OnRecvSwitchZone(m)
	elseif action == GameMessage_pb.MSG_GAME_SERVERDAYRESET then
		GameModule:OnRecvServerDayReset()
	elseif action == GameMessage_pb.MSG_MATCH_BEGINMATCH then
		local m = GameMessage_pb.SC_Match_BeginMatch()
		m:ParseFromString(data)
		MatchDataManager:OnRecvBeginMatchResult(m)
	elseif action == GameMessage_pb.MSG_MATCH_SYNCMATCHSTATUS then
		local m = GameMessage_pb.SC_Match_SyncMatchStatus()
		m:ParseFromString(data)
		MatchDataManager:OnRecvMatchStatus(m)
	elseif action == GameMessage_pb.MSG_MATCH_SYNCMATCHLIST then
		local m = GameMessage_pb.SC_Match_SyncMatchList()
		m:ParseFromString(data)
		MatchDataManager:OnRecvMatchList(m)
	elseif action == GameMessage_pb.MSG_RECHARGE_BUYCARDRECORD then
		local m = GameMessage_pb.SC_Recharge_BuyCardRecord()
		m:ParseFromString(data)
		HeroDataManager:SetBuyRechardCardRecord(m.CardList)
	elseif action == GameMessage_pb.MSG_ASSISTFUNC_PRIZETIP then
		local m = GameMessage_pb.SC_AssistFunc_PrizeTip()
		m:ParseFromString(data)
		-- print('----服务器奖励消息-----',m.TipType, m.PrizeContent, m.TipDesc)
		HelperL.ShowPrizeTipBox(m.TipType, m.PrizeContent, m.TipDesc)
	elseif action == GameMessage_pb.MSG_INVITE_LOGINGETCODE then
		local m = GameMessage_pb.SC_INVITE_LOGINGETCODE()
		m:ParseFromString(data)
		EntityModule.SC_INVITE_LOGINGETCODE(m)
	elseif action == GameMessage_pb.MSG_INVITE_ACCEPTINVITELIST then
		local m = GameMessage_pb.SC_INVITE_GETINVITELIST()
		m:ParseFromString(data)
		EntityModule.SC_INVITE_GETINVITELIST(m)
	end
end

-- 是否处于测试模式
function GameModule:IsTestMode()
	return self.TestModeActive
end

function GameModule.OnHeroCreated()
	-- 开启断线重连
	print("开启断线重连");
	local persistentConnection = Premier.Instance:GetNetwork().PersistentConnection;
	persistentConnection.PersistentClient.ReconnectWhenInterrupted = true;
	-- GameModule.ActorDataMgrInited()
end

--角色数据管理器初始化
function GameModule.ActorDataMgrInited()
	--获取HTTP数据有点忙
	--延迟两秒在进入主界面
	local sequence = DOTween.Sequence()
	sequence:AppendInterval(2)
	sequence:AppendCallback(function()
		-- 关闭载入界面
		SceneManager:CloseLoadingUI()
		if SceneManager.curSceneName ~= 'GameScene' then
			--UIManager:CloseWndByID(WndID.Login)
			EventManager:Fire(EventID.LoginComplete)
		end
		-- --关闭等待界面
		-- HelperL.CloseBlackWaitingUI()
		-- -- 打开主界面
		-- local flag = HeroDataManager:GetLogicBit(LOGIC_DATA.DATA_GUIDE_LOG_IDFLAG1, 0) --新手引导（播放视频）
		-- if flag == 0 then
		-- 	if SceneManager.curSceneName ~= 'GameScene' then
		-- 		UIManager:OpenWnd(WndID.MainTitle)
		-- 	end
		-- else
		-- 	UIManager:OpenWnd(WndID.MainTitle)
		-- end
	end)
end

-- 心跳包
function GameModule:OnReceiveHeartBeat(msg)
	self.lastHeartBeatTime = UnityEngine.Time.unscaledTime
end

-- 消息码
function GameModule:OnRecvResultCodeShow(m)
	if m.ShowType == 0 then
		ResultCode:DefaultShowResultCode(m.Result, m.Content)
	else
		warn('GameModule:OnRecvResultCodeShow 未定义的消息码显示类型', m.ShowType)
	end
end

-- 开启测试模式
function GameModule:OnRecvTestFuncActive(m)
	self.TestModeActive = true
	UIManager:SendWndMsg(WndID.RaceView, WndMsg.RaceView_TestFuncActive)
end

-- 换区
function GameModule:OnRecvSwitchZone(m)
	if HelperL.ZoneID == m.ZoneID then
		print('切换到同一区 ' .. tostring(m.ZoneID))
	else
		print('切换区 ' .. tostring(m.ZoneID))
	end
	LoginModule.SwitchZone(m.ZoneID)
end

-- 服务器每日重置完毕
function GameModule:OnRecvServerDayReset()

end

-- 掉线处理
local hasShowReconnectBox = false
local reconnectCount = 0
function GameModule:OnConnectClose()
	-- 如果是登出操作，过程中不重连
	if SceneManager.islogOut then
		return
	end

	if hasShowReconnectBox then
		print('GameModule:OnConnectClose  反复断开')
		return
	end
	if self.heartBeatTimer ~= nil then
		self.heartBeatTimer:Stop()
		self.heartBeatTimer = nil
	end
	reconnectCount = reconnectCount + 1
	-- 弹出提示框，点击后回到选择细胞界面
	local backToLoginFunc = function()
		reconnectCount = 0
		-- 显示载入中UI
		SceneManager:ShowLoadingUI(false)

		local doFunc = function()
			Premier.Instance:GetNetwork():Close()
			HelperL.ClearGameData()
			SceneManager.isLogin = false
			SceneManager:LoadSceneWithLoading('GameLogin', true, false)
		end
		local timer = Timer.New(doFunc, 0.01, 1)
		timer:Start()
	end
	local callBackFunc = function()
		hasShowReconnectBox = false
		if Premier.Instance:GetNetwork():IsConnected() then
			reconnectCount = 0
			return
		end
		HelperL.ShowBlackWaitingUI(1, nil, nil, GetGameText(luaID, 1))
		-- 重新连接
		Premier.Instance:GetNetwork():Connect()
		local checkFunc = function()
			if Premier.Instance:GetNetwork():IsConnected() then
				if self.reconnectTimer then
					self.reconnectTimer:Stop()
					self.reconnectTimer = nil
				end
				-- 显示载入中UI
				local needHideLoading = (reconnectCount == 1)
				reconnectCount = 0
				if not needHideLoading then
					if SceneManager.curSceneName ~= 'GameScene' then
						SceneManager:ShowLoadingUI(false)
					end
					HelperL.CloseBlackWaitingUI()
				end
				-- 清除现有数据
				-- local tempBak = LoginModule
				HelperL.ClearGameData()
				-- LoginModule = tempBak
				if needHideLoading then
					HelperL.ShowBlackWaitingUI(1, 30.0, backToLoginFunc, GetGameText(luaID, 1))
				end

				LoginModule.isReconnecting = true
				LoginModule.OnConnected()
				GuideManager:ReConnect()
			end
		end
		local lastFunc = function()
			if not Premier.Instance:GetNetwork():IsConnected() then
				self:OnConnectClose()
			end
			HelperL.CloseBlackWaitingUI()
		end
		if self.reconnectTimer == nil then
			self.reconnectTimer = Timer.New(checkFunc, 0.5, 10, nil, nil, lastFunc)
			self.reconnectTimer:Start()
		else
			self.reconnectTimer:Stop()
			self.reconnectTimer:Reset(checkFunc, 0.5, 15, nil, nil, lastFunc)
			self.reconnectTimer:Start()
		end
	end
	if reconnectCount > 10 then
		backToLoginFunc()
		return
	end

	hasShowReconnectBox = true

	-- 延迟一帧调用以求稳定
	if self.delayReconnectTimer == nil then
		self.delayReconnectTimer = Timer.New(callBackFunc, 0.01, 1)
		self.delayReconnectTimer:Start()
	else
		self.delayReconnectTimer:Stop()
		self.delayReconnectTimer:Reset(callBackFunc, 0.01, 1)
		self.delayReconnectTimer:Start()
	end
end

EventManager:Subscribe(EventID.OnHeroCreate, GameModule.OnHeroCreated)
EventManager:Subscribe(EventID.ActorDataMgrInited, GameModule.ActorDataMgrInited)

Premier.Instance:RegisterLuaModule(MSG_MODULEID.MSG_MODULEID_GAME, 'GameModule.Handle')
