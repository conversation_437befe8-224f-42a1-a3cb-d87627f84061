--------------------------------------------------------------------------------
--      Copyright (c) 2015 , 蒙占志(topameng) <EMAIL>
--      All rights reserved.
--      Use, modification and distribution are subject to the "MIT License"
--------------------------------------------------------------------------------

local TimerType = require("TimerType")
local setmetatable = setmetatable
local UpdateBeat = UpdateBeat
local CoUpdateBeat = CoUpdateBeat
local Time = Time

local this = {}

---@class TimerBase
---@field timerType TimerType


---@class Timer:TimerBase
local Timer = {
    timerType =  TimerType.Timer
}
this.Timer = Timer
local mt = { __index = Timer }

--scale false 采用deltaTime计时，true 采用 unscaledDeltaTime计时
---New
---@param func function
---@param duration number
---@param loop number
---@param scale number
---@return Timer
function Timer.New(func, duration, loop, scale, endCallBack)
    scale = scale or false and true
    loop = loop or 1
    return setmetatable({ func = func, duration = duration, time = duration, loop = loop, scale = scale, running = false, endCallBack = endCallBack }, mt)
end

function Timer:Start()
    self.running = true

    if not self.handle then
        self.handle = UpdateBeat:CreateListener(self.Update, self)
    end

    UpdateBeat:AddListener(self.handle)
end

--- @return Timer
function Timer:Reset(func, duration, loop, scale, endCallBack)
    self.duration = duration
    self.loop = loop or 1
    self.scale = scale
    self.func = func or self.func
    self.time = duration
    self.endCallBack = endCallBack
    return self
end

function Timer:Stop()
    self.running = false

    if self.handle then
        UpdateBeat:RemoveListener(self.handle)
    end
end

function Timer:Update()
    if not self.running then
        return
    end
    if self.time == nil then
        return
    end
    local delta = self.scale and Time.deltaTime or Time.unscaledDeltaTime
    self.time = self.time - delta

    if self.time <= 0 then
        --
        if self.loop > 0 then
            self.func()
            self.loop = self.loop - 1
            self.time = self.time + self.duration
        end

        if self.loop == 0 then
            if self.endCallBack then
                self.endCallBack()
            end
            self:Stop()
        elseif self.loop < 0 then
            self.func()
            self.time = self.time + self.duration
        end
    end
end


--给协同使用的帧计数timer
---@class FrameTimer:TimerBase
local FrameTimer = {
    timerType = TimerType.FRAME_TIMER
}
this.FrameTimer = FrameTimer
local mt2 = { __index = FrameTimer }

function FrameTimer.New(func, count, loop)
    count = count or 0
    loop = loop or 0
    local c = Time.frameCount + count
    return setmetatable({ func = func, loop = loop, duration = count, count = c, running = false }, mt2)
end

function FrameTimer:Reset(func, count, loop)
    self.func = func
    self.duration = count
    self.loop = loop
    self.count = Time.frameCount + count
    return self
end

function FrameTimer:Start()
    if not self.handle then
        self.handle = CoUpdateBeat:CreateListener(self.Update, self)
    end

    CoUpdateBeat:AddListener(self.handle)
    self.running = true
end

function FrameTimer:Stop()
    self.running = false

    if self.handle then
        CoUpdateBeat:RemoveListener(self.handle)
    end
end

function FrameTimer:Update()
    if not self.running then
        return
    end

    if Time.frameCount >= self.count then
        self.func()

        if self.loop > 0 then
            self.loop = self.loop - 1
        end

        if self.loop == 0 then
            self:Stop()
        else
            self.count = Time.frameCount + self.duration
        end
    end
end


---@class CoTimer:TimerBase
local CoTimer = {
    timerType = TimerType.CO_TIMER
}
this.CoTimer = CoTimer
local mt3 = { __index = CoTimer }

function CoTimer.New(func, duration, loop)
    loop = loop or 1
    return setmetatable({ duration = duration, loop = loop, func = func, time = duration, running = false }, mt3)
end

function CoTimer:Start()
    if not self.handle then
        self.handle = CoUpdateBeat:CreateListener(self.Update, self)
    end

    self.running = true
    CoUpdateBeat:AddListener(self.handle)
end

function CoTimer:Reset(func, duration, loop)
    self.duration = duration
    self.loop = loop or 1
    self.func = func
    self.time = duration
end

function CoTimer:Stop()
    self.running = false

    if self.handle then
        CoUpdateBeat:RemoveListener(self.handle)
    end
end

function CoTimer:Update()
    if not self.running then
        return
    end

    if self.time <= 0 then
        self.func()

        if self.loop > 0 then
            self.loop = self.loop - 1
            self.time = self.time + self.duration
        end

        if self.loop == 0 then
            self:Stop()
        elseif self.loop < 0 then
            self.time = self.time + self.duration
        end
    end

    self.time = self.time - Time.deltaTime
end


--- @class QueueTimer:TimerBase
local QueueTimer = {
    timerType = TimerType.QueueTimer
}
this.QueueTimer = QueueTimer

function QueueTimer.New(_func, _time_queue, _call_back)
    local self = {}
    self.func = _func
    if _time_queue and #_time_queue > 0 then
        self.time = table.remove(_time_queue, 1)
    end
    self.time_queue = _time_queue
    self.running = false
    self.call_back = _call_back
    return setmetatable(self, { __index = QueueTimer })
end

function QueueTimer:Start()
    if not self.handle then
        self.handle = UpdateBeat:CreateListener(self.Update, self)
    end

    self.running = true
    UpdateBeat:AddListener(self.handle)
end

function QueueTimer:Reset(_func, _time_queue, _call_back)
    self.func = _func
    self.time = table.remove(_time_queue, 1)
    self.time_queue = _time_queue
    self.running = false
    self.call_back = _call_back
    return self
end

function QueueTimer:ReStart(_func, _time_queue, _call_back)
    self.func = _func
    self.time_queue = _time_queue
    self.running = false
    self.call_back = _call_back
    self:Start()
end

function QueueTimer:Stop()
    self.running = false
    if self.handle then
        UpdateBeat:RemoveListener(self.handle)
    end
end

function QueueTimer:Update()
    if not self.running then
        return
    end
    if self.time <= 0 then
        self.func()
        local next_time = table.remove(self.time_queue, 1)
        if next_time then
            self.time = self.time + next_time
        else
            if self.call_back ~= nil then
                self.call_back()
            end
            self:Stop()
        end
    end
    self.time = self.time - Time.deltaTime
end

return this