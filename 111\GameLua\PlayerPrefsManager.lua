-- 全局存储配置管理
PlayerPrefsManager = {}
PlayerPrefsManager.valueCache = {}


-- 获取整型值
function PlayerPrefsManager:GetInt(key)
	--local result = self.valueCache[key]
	--if result then
	--	return result
	--end
	--result = PlayerPrefs.GetInt(key, 0)
	--self.valueCache[key] = result
	--return result
	return PlayerPrefs.GetInt(key, 0)
end

-- 设置整型值
function PlayerPrefsManager:SetInt(key, value)
	--local curValue = self.valueCache[key]
	--if curValue == value then
	--	return
	--end
	PlayerPrefs.SetInt(key, value)
	self.valueCache[key] = value
end

-- 获取字符串值
function PlayerPrefsManager:GetString(key)
	local result = self.valueCache[key]
	if result then
		return result
	end
	result = PlayerPrefs.GetString(key, '')
	self.valueCache[key] = result
	return result
end

-- 设置字符串值
function PlayerPrefsManager:SetString(key, value)
	local curValue = self.valueCache[key]
	if curValue == value then
		return
	end
	PlayerPrefs.SetString(key, value)
	self.valueCache[key] = value
end

-- 删除指定键值
function PlayerPrefsManager:Delete<PERSON>ey(key)
	PlayerPrefs.DeleteKey(key)
	self.valueCache[key] = nil
end

-- 保存数据
function PlayerPrefsManager:Save()
	PlayerPrefs.Save()
end