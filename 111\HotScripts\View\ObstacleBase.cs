﻿// ReSharper disable IdentifierTypo

using Thing;

using UnityEngine;

using X.PB;

namespace View
{
    /// <summary>
    /// 障碍物界面
    /// </summary>
    public abstract class ObstacleBase : ThingBehaviour
    {
        /// <summary>
        /// 障碍物物件(数据)
        /// </summary>
        public ObstacleThing ObstacleThing => Thing as ObstacleThing;

        /// <summary>
        /// 获取或设置障碍物的图片
        /// </summary>
        public SpriteRenderer SpriteRenderer { get; protected set; }

        public override void Awake()
        {
            base.Awake();

            SpriteRenderer = GetComponent<SpriteRenderer>();

            Thing ??= new RectObstacleThing { TerrainType = TerrainType.Wall };
        }
    }
}
