local luaID = ('UIMainBuff')

---@class UIMainBuff: UIWndBase
local m = {}

-- 订阅事件列表
function m.GetOpenEventList()
    return {
        [EventID.LogicDataChange] = m.UpdateInterface,
        [EventID.EntitySyncBuff] = m.UpdateInterface,
    }
end

function m.OnCreate()
    m.objList.Btn_Close.onClick:AddListenerEx(function()
        m:CloseSelf()
    end)
    m.objList.Btn_VipUIClose.onClick:AddListenerEx(function()
        m.objList.Img_VipUI.gameObject:SetActive(false)
    end)
    m.objList.Btn_LiBaoGet.onClick:AddListenerEx(function()
        m.objList.Btn_zdzfClose.gameObject:SetActive(true)
    end)
    m.objList.Btn_zdzfClose.onClick:AddListenerEx(function()
        m.objList.Btn_zdzfClose.gameObject:SetActive(false)
    end)
    m.objList.Btn_vip.onClick:AddListenerEx(m.OnClickVip)

    m.objList.Txt_Title.text = GetGameText(luaID, 1)

    ---@type ZhuFuItem[]
    m.zhuFuItemList = {}
    for i, v in ipairs(UIMainBuffDataList) do
        table.insert(m.zhuFuItemList, m.CreateZhuFuItem(i, v))
    end

    return true
end

function m.OnOpen()
    m.UpdateInterface()
end

--更新界面
function m.UpdateInterface()
    m.VipInfo()
    for i, v in ipairs(m.zhuFuItemList) do
        v.UpdateInfo()
    end
end

---创建buff
---@param index integer
---@param data {equipID:integer, adID:integer}
---@return ZhuFuItem
function m.CreateZhuFuItem(index, data)
    ---@class ZhuFuItem
    local item = {}
    item.index = index
    item.data = data
    item.buffID = Schemes.CommonText:Get(data.adID).Param1
    item.com = m:CreateSubItem(m.objList.PrizeContent, m.objList.ZhuFuItem.transform)
    item.com.Txt_Name.text = GetGameText(luaID, 2 + index)
    item.com.Txt_Name1.text = GetGameText(luaID, 5 + index)
    AtlasManager:AsyncGetSprite('gg_icon_' .. index, item.com.Img_icon)
    item.com.Btn_Get.onClick:AddListenerEx(function()
        AdvertisementManager.ShowRewardAd(item.data.adID, function(bool)
            if bool then
                local str = "LuaRequestSeeAdvertiseTakeMountEquip?EquipID=%s&cardUID=%s"
                local entity = SkepModule:GetSkepByID(SKEPID.SKEPID_MONSTERCARD):GetEntityByGoodsID(600006)
                --看完广告后加装备经验
                LuaModule.RunLuaRequest(string.format(str, entity.uid, 600006))
                entity = SkepModule:GetHorseEntity(item.data.equipID)
                LuaModule.RunLuaRequest(string.format(str, entity.uid, item.data.equipID), m.RunLuaRequestCallback)
                EventManager:Fire(EventID.UseMainBuff)
                m.BuffIsEquip(true, item.data.equipID)
            end
        end)
    end)

    --更新buff信息
    item.UpdateInfo = function()
        local entity, _ = SkepModule:GetHorseEntity(item.data.equipID)
        if entity == nil then
            warn('获取装备实体失败 equipID=', item.data.equipID)
            return
        end
        local quality = entity:GetProperty(EQUIPMENT_FIELD.EQUIPMENT_FIELD_QUALITY)
        local starNum = entity:GetProperty(EQUIPMENT_FIELD.EQUIPMENT_FIELD_STARNUM)
        local exp = entity:GetProperty(EQUIPMENT_FIELD.EQUIPMENT_FIELD_SMELTEXP)
        local cfg = Schemes.Equipment:Get(item.data.equipID)
        local smelt = Schemes.EquipSmelt:Get(cfg.SmeltID, quality, starNum)
        if smelt == nil then
            warn('HDSUI EquipSmelt配置错误 SmeltID = %d', cfg.SmeltID)
            return
        end

        item.com.Txt_bufflevel.text = quality * 10 + starNum

        -- 设置当前属性
        local equipProp = ActorProp.ReadEquipProp(entity)
        item.com.Txt_Dec.text = '0'
        for k, v in pairs(equipProp.StarProp) do
            if v + equipProp.StarExpProp[k] > 0 then
                item.com.Txt_Dec.text = math.floor((v + equipProp.StarExpProp[k]) / 100) .. "%"
            end
        end

        local curExp = math.floor((exp - smelt.SaleMoney) / smelt.LevelExpRate)
        local nextExp = math.floor((smelt.LevelExp - smelt.SaleMoney) / smelt.LevelExpRate)
        if nextExp ~= 0 then
            item.com.Sld_exp.value = curExp / nextExp
            item.com.Txt_sld.text = string.format('%d/%d', curExp, nextExp)
        else
            item.com.Sld_exp.value = 0
            item.com.Txt_sld.text = "0/0"
        end
    end

    --更新buff时间
    item.UpdateTime = function()
        local time = EntityModule.hero.buffLC:GetBuffLeftTime(item.buffID)
        local bool = time > 0
        --更新时间
        if bool then
            item.com.Txt_refresh.text = HelperL.GetTimeString(TimeStringType.FullAuto2, time)
        end

        --优化，buff状态变动在更新按钮颜色
        if item.lastHasBuff == bool then return end
        item.lastHasBuff = bool
        if bool then
            item.com.Btn_Get.gameObject:SetActive(false)
            item.com.Img_refresh.gameObject:SetActive(true)
            item.com.Img_bg.color = Color(9 / 255, 253 / 255, 179 / 255, 1)
            item.com.Img_iconbg.color = Color(16 / 255, 251 / 255, 242 / 255, 1)
            item.com.Txt_Name.color = Color(21 / 255, 253 / 255, 33 / 255, 1)
            item.com.Txt_Name1.color = Color(21 / 255, 253 / 255, 33 / 255, 1)
            item.com.Txt_Dec.color = Color(21 / 255, 253 / 255, 33 / 255, 1)
        else
            item.com.Img_refresh.gameObject:SetActive(false)
            item.com.Btn_Get.gameObject:SetActive(true)
            item.com.Img_bg.color = Color.white
            item.com.Img_iconbg.color = Color.white
            item.com.Txt_Name.color = Color.white
            item.com.Txt_Name1.color = Color.white
            item.com.Txt_Dec.color = Color.white
        end
    end
    return item
end

--每秒更新
function m.OnSecondUpdate()
    for i, v in ipairs(m.zhuFuItemList) do
        v.UpdateTime()
    end
end

--更新VIP属性
function m.VipInfo()
    local cfg = Schemes.Equipment:Get(600006)
    local cardSkep = SkepModule:GetSkepByID(SKEPID.SKEPID_MONSTERCARD)
    if cardSkep then
        local entity = cardSkep:GetEntityByGoodsID(cfg.ID)
        if entity then
            local quality = entity:GetProperty(EQUIPMENT_FIELD.EQUIPMENT_FIELD_QUALITY) -- 品质
            local starNum = entity:GetProperty(EQUIPMENT_FIELD.EQUIPMENT_FIELD_STARNUM) -- 星数
            local smeltCfg = Schemes.EquipSmelt:Get(cfg.SmeltID, quality, starNum)      -- 当前锻造配置
            local str = ''
            local bufflist = ActorProp.GetTransAttributeBySmelt(smeltCfg)
            if bufflist then
                for i, v in ipairs(bufflist) do
                    if str == '' then
                        str = v.attrName .. ":" .. v.attrValue
                    else
                        str = str .. '\n' .. v.attrName .. ":" .. v.attrValue
                    end
                end
            end
            m.objList.Txt_VipDec.text = str
            m.objList.Txt_VipUIDec.text = str
        end
    else
        print("cardSkep is nil")
    end
end

--VIP卡牌
function m.OnClickVip()
    m.objList.Img_VipUI.gameObject:SetActive(true)
    local v = Schemes.Equipment:Get(600006)
    local cardSkep = SkepModule:GetSkepByID(SKEPID.SKEPID_MONSTERCARD)
    if cardSkep then
        local entity = cardSkep:GetEntityByGoodsID(v.ID)
        if entity then
            local itemQuality = entity:GetProperty(EQUIPMENT_FIELD.EQUIPMENT_FIELD_QUALITY)
            local itemStarNum = entity:GetProperty(EQUIPMENT_FIELD.EQUIPMENT_FIELD_STARNUM)
            local itemExp = entity:GetProperty(EQUIPMENT_FIELD.EQUIPMENT_FIELD_SMELTEXP)
            local smelt = Schemes.EquipSmelt:Get(v.SmeltID, itemQuality, itemStarNum)
            if smelt == nil then
                error('HDSUI EquipSmelt配置错误 SmeltID = %d', v.SmeltID)
                return
            end
            local curExp = 0
            local nextExp = 0

            curExp = math.floor((itemExp - smelt.SaleMoney) / smelt.LevelExpRate)
            nextExp = math.floor((smelt.LevelExp - smelt.SaleMoney) / smelt.LevelExpRate)
            if curExp == nil then
                curExp = 0
            end
            if nextExp == nil then
                nextExp = 0
            end
            m.objList.Sld_VipUIexp.value = curExp / nextExp
            m.objList.Txt_VipUIsld.text = string.format('%d/%d', curExp, nextExp)
            m.objList.Txt_VipUITitle.text = v.GoodsName
            m.objList.Txt_VipUILevel.text = itemQuality * 10 + itemStarNum
        end
    else
        print("cardSkep is nil")
    end
end

---佩戴和取下
---@param isEquip boolean 是佩戴
---@param equipID integer 装备ID
function m.BuffIsEquip(isEquip, equipID)
    local entity = nil
    if isEquip then --佩戴
        --获取背包装备，获取不到说明以佩戴
        entity = SkepModule:GetSkepByID(SKEPID.SKEPID_MOUNTEQUIPPACKET):GetEntityByGoodsID(equipID)
    else --取下
        --获取佩戴装备，获取不到说明以取下
        entity = SkepModule:GetSkepByID(SKEPID.SKEPID_MOUNTEQUIP):GetEntityByGoodsID(equipID)
    end
    --装备不存在
    if entity == nil then return end

    local str_req = string.format("LuaRequestTakeMountEquip?equip=%s", entity.uid)
    LuaModule.RunLuaRequest(str_req, m.RunLuaRequestCallback)
end

--请求回调
function m.RunLuaRequestCallback(result, content)
    if result == RESULT_CODE.RESULT_COMMON_SUCCEED then
        m.UpdateInterface()
        -- HelperL.ShowMessage(TipType.FlowText, GetGameText(luaID, 12))
    else
        HelperL.ShowMessage(TipType.FlowText, ResultCode:GetResultCodeStr(result))
    end
end

return m
