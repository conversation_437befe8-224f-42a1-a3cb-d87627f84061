--[[
********************************************************************
    created:    2023/08/30
    author :    李锦剑
    purpose:    限时放送界面
*********************************************************************
--]]

local luaID = ('UITimeLimit')

local m = {}
local cardID = 20

--------------------------------------------------------------------
-- 订阅事件列表
--------------------------------------------------------------------
function m.GetOpenEventList()
    return {
        [EventID.LogicDataChange] = m.UpdateView,
        [EventID.OnHeroPropChange] = m.UpdateView,
        -- [EventID.AdvertisePlayComplete] = m.UpdateView,
        -- [EventID.StoreList] = m.UpdateView,
        -- [EventID.StoreBuyItem] = m.UpdateView,
        -- [EventID.OnRechargeSDKCallback] = m.UpdateView,
    }
end

--------------------------------------------------------------------
-- 初始化
--------------------------------------------------------------------
function m.OnCreate()
    m.goodsItemList = {}

    local rechargeCard = Schemes.RechargeCard:Get(cardID)
    local goodsList = Schemes.PrizeTable:GetGoodsList(rechargeCard.PrizeID)
    if goodsList then
        local num = math.max(#m.goodsItemList, #goodsList)
        local item
        for i = 1, num, 1 do
            item = m.CreateGoods()
            item.UpdateView(goodsList[i])
            m.goodsItemList[i] = item
        end
    end

    m.RegisterClickEvent()
    return true
end

--------------------------------------------------------------------
-- 窗口开启
--------------------------------------------------------------------
function m.OnOpen()
    m.UpdateView()
end

--------------------------------------------------------------------
--注册点击事件
--------------------------------------------------------------------
function m.RegisterClickEvent()
    m.objList.Btn_Close.onClick:AddListenerEx(function()
        m:CloseSelf()
    end)

    m.objList.Btn_Buy.onClick:AddListenerEx(function()
        local isBuyCard = HelperL.HadBoughtCardID(cardID)
        if isBuyCard then
            HelperL.ShowMessage(TipType.FlowText, CommonTextID.IS_PURCHASE)
        else
            HelperL.Recharge(cardID)
        end
    end)
end

--------------------------------------------------------------------
--创建物品框
--------------------------------------------------------------------
function m.CreateGoods()
    local item = {}
    item.com = m:CreateSubItem(m.objList.Grid_Goods, m.objList.Item_Goods)
    item.UpdateView = function(data)
        if data then
            item.com.Txt_Name.text = data.name
            item.com.Txt_Amount.text = data.num
            AtlasManager:AsyncGetSprite(data.iconID, item.com.Img_Icon)
            item.com.gameObject:SetActive(true)
        else
            item.com.gameObject:SetActive(false)
        end
    end
    return item
end

--------------------------------------------------------------------
--更新界面
--------------------------------------------------------------------
function m.UpdateView()
    local rechargeCard = Schemes.RechargeCard:Get(cardID)
    if not rechargeCard then return end
    local isBuyCard = HelperL.HadBoughtCardID(cardID)
    if not isBuyCard then
        m.objList.Txt_Buy.text = '￥' .. math.floor(rechargeCard.FirstRMB / 100)
    else
        m.objList.Txt_Buy.text = CommonTextID.IS_PURCHASE
    end
end

return m
