--[[
********************************************************************
    created:	2024/06/14
    author :	李锦剑
    purpose:    角色法宝
*********************************************************************
--]]

local luaID = "UITreasuredTricks"
--显示天赋数量
local ShowTalentNum = 10
-----法宝其他配置
local EquipCfg = {
    [1] = { icon = "zjm_icon_7", pos = Vector2(0, 200), content = GetGameText(luaID, 2) },
    [2] = { icon = "zjm_icon_6", pos = Vector2(-480, -200), content = GetGameText(luaID, 3) },
    [3] = { icon = "zjm_icon_8", pos = Vector2(480, -200), content = GetGameText(luaID, 4) },
}

---角色法宝
---@class UITreasuredTricks:UIWndBase
local m = {}

-- 订阅事件列表
function m.GetOpenEventList()
    return {
        [EventID.StoreList] = m.UpdateView,
        [EventID.LogicDataChange] = m.UpdateView,
        [EventID.OnGoodsPropChange] = m.UpdateView,
        [EventID.ActorDataChanged] = m.UpdateView,
    }
end

--------------------------------------------------------------------
-- 初始化
--------------------------------------------------------------------
function m.OnCreate()
    m.lastTime = 0
    ---@type Item_Treasured[]
    m.Item_Treasured_List = {}
    ---@type Item_Attribute[]
    m.Item_Attribute_List = {}

    ---@type Item_Obj_Type[]
    m.Item_Obj_Type_List = {}

    local list = Schemes.EquipSmeltStarData:GetByOprateType(TreasuredTricks_OprateType) or {}
    for i, v in ipairs(EquipCfg) do
        m.Item_Treasured_List[i] = m.Create_Item_Treasured(i, list[i])
    end
    m.RegisterClickEvent()
    return true
end

--------------------------------------------------------------------
-- 窗口开启
--------------------------------------------------------------------
function m:OnOpen()
    m.CloseInfo()
    m.UpdateView()
end

--------------------------------------------------------------------
-- 注册点击事件
--------------------------------------------------------------------
function m.RegisterClickEvent()
    m:AddClick(m.objList.Btn_Close, m.CloseUI)
    m:AddClick(m.objList.Btn_Close2, m.CloseInfo)
    m:AddClick(m.objList.Btn_Upgrade, function()
        m.Upgrade(m.SmeltID)
        m.objList.Obj_TalentInof.gameObject:SetActive(false)
    end)
    m:AddClick(m.objList.Btn_TalentInof, function()
        m.objList.Obj_TalentInof.gameObject:SetActive(false)
    end)
end

--------------------------------------------------------------------
-- 更新界面
--------------------------------------------------------------------
function m.UpdateView()
    for i, v in ipairs(m.Item_Treasured_List) do
        v.UpdateView()
    end

    local TotalRemark = ''
    if m.selectIndex then
        local smeltID = m.Item_Treasured_List[m.selectIndex].cfg.EquipSmeltStarId
        local starLvl = GamePlayerData.ActorEquipNew:GetEquipUpgradeLevel(smeltID)
        local equipSmeltStar = Schemes.EquipSmeltStar:GetEquipSmeltStarCfg(smeltID, starLvl)
        if equipSmeltStar then
            --TotalRemark = Schemes.CommonProp:Get(equipSmeltStar.PropId).TotalRemark
        end
        m.objList.Txt_Hint.text = EquipCfg[m.selectIndex].content
    end
   -- m.objList.Txt_Attribute.text = TotalRemark
end

--------------------------------------------------------------------
--选择法宝
--------------------------------------------------------------------
function m.SelectEquip(index)
    local item
    if m.selectIndex then
        item = m.Item_Treasured_List[m.selectIndex]
        item.Obj_Type.gameObject:SetActive(false)
    end
    m.selectIndex = index
    item = m.Item_Treasured_List[index]
    item.Obj_Type.gameObject:SetActive(true)

    local level = EntityModule.hero:GetProperty(CREATURE_FIELD.CREATURE_FIELD_LEVEL)
    if level < item.cfg.ActivePremiss1 then
        print('------level---2----', level , item.cfg.ActivePremiss1)
        HelperL.ShowMessage(TipType.FlowText, string.format(GetGameText(luaID, 5), item.cfg.ActivePremiss1))
        return
    end

    m.objList.Txt_Title2.text = HelperL.Split(item.cfg.Name, ';')[1]

    m.UpdateView()
    m.objList.Obj_Info.gameObject:SetActive(true)
    m.objList.Grid_Treasured.gameObject:SetActive(false)
end

--------------------------------------------------------------------
--创角分页按钮
---@param index integer
---@param cfg EquipSmeltStarDataCfg
---@return Item_Treasured
--------------------------------------------------------------------
function m.Create_Item_Treasured(index, cfg)
    ---@class Item_Treasured
    local item = {}
    item.index = index
    item.cfg = cfg
    item.com = m:CreateSubItem(m.objList.Grid_Treasured, m.objList.Item_Treasured)
    m:AddClick(item.com.Btn_Click, function()
        m.SelectEquip(item.index)
    end)

    item.Obj_Type = m.Get_Obj_Type(index)
    item.Obj_Type.gameObject:SetActive(false)

    ---更新界面
    item.UpdateView = function()
        if item.cfg then
            -- local equipCfg = EquipCfg[item.index]
            -- item.com.transform:GetRectTransform().anchoredPosition = equipCfg.pos
            item.com.Txt_Content.text = item.cfg.Remark
            local strList = HelperL.Split(item.cfg.Name, ';')
            item.com.Txt_Content_1.text = strList[1]
            AtlasManager:AsyncGetSprite(strList[2], item.com.Img_Icon, true)
            item.com.Img_Red.gameObject:SetActive(false)
            item.com.Img_Lock.gameObject:SetActive(false)
            HelperL.SetImageGray(item.com.Img_Icon, false)
            HelperL.SetImageGray(item.com.Img_Bg2, false)
            local level = EntityModule.hero:GetProperty(CREATURE_FIELD.CREATURE_FIELD_LEVEL)
            if level >= item.cfg.ActivePremiss1 then
                item.com.Img_Red.gameObject:SetActive(RedDotCheckFunc:Check_UITreasuredTricks(item.cfg.Id))
            else
                item.com.Txt_Unlock.text = string.format(GetGameText(luaID, 5), item.cfg.ActivePremiss1)
                item.com.Img_Lock.gameObject:SetActive(true)
                HelperL.SetImageGray(item.com.Img_Icon, true)
                HelperL.SetImageGray(item.com.Img_Bg2, true)
            end
            item.Obj_Type.UpdateData(item.cfg.EquipSmeltStarId)
            item.com.gameObject:SetActive(true)
        else
            item.com.gameObject:SetActive(false)
        end
    end
    return item
end

--------------------------------------------------------------------
--请求回调
--------------------------------------------------------------------
function m.RequestCallback(resultCode, content)
    if resultCode == RESULT_CODE_BASE.RESULT_COMMON_SUCCEED[1] then
        -- m.UpdateView()
    else
        ResultCode:DefaultShowResultCode(resultCode, content)
    end
end

--------------------------------------------------------------------
--天赋升级
---@param smeltID integer 升星ID
--------------------------------------------------------------------
function m.Upgrade(smeltID)
    local curTime = os.clock()
    if curTime - m.lastTime < 0.3 then
        print('天赋升级点太快了1秒后尝试！')
        return
    end
    m.lastTime = curTime

    local starLvl = GamePlayerData.ActorEquipNew:GetEquipUpgradeLevel(smeltID)
    local equipSmeltStar = Schemes.EquipSmeltStar:GetEquipSmeltStarCfg(smeltID, starLvl)
    if equipSmeltStar == nil then
        warn("天赋升级:未找到升星配置 smeltID=", smeltID, starLvl)
        return
    end

    if HelperL.IsLackGoods(equipSmeltStar.CostGoodsID1, equipSmeltStar.CostGoodsID1Num, false) then return end
    if HelperL.IsLackGoods(equipSmeltStar.CostGoodsID2, equipSmeltStar.CostGoodsID2Num, false) then return end

    local form = {}
    form["smeltID"] = smeltID
    form["loopTimes"] = 1
    LuaModuleNew.SendRequest(LuaRequestID.NewCardAddExp2, form, m.RequestCallback)
end

--------------------------------------------------------------------
--获取装备图标
--------------------------------------------------------------------
function m.Create_Item_Icon(parent, index, parentIndex)
    ---@class Item_Obj_Icon
    local item = {}
    item.index = index
    item.parentIndex = parentIndex
    item.com = m:CreateSubItem(parent, m.objList.Item_Icon)
    local name = index .. parentIndex
    item.Img_JD = m.objList["Img_JD" .. name]
    item.com.transform.position = item.Img_JD.transform.position
    m:AddClick(item.com.Btn_Click, function()
        m.ShowTalentInof(item)
    end)

    ---更新数据
    ---@param cfg  EquipSmeltStarCfg
    item.UpdateData = function(cfg)
        item.equipSme = cfg
        if cfg then
            AtlasManager:AsyncGetSprite(cfg.Icon, item.com.Img_Icon)

            --设置默认状态
            HelperL.SetImageGray(item.Img_JD, true)
            HelperL.SetImageGray(item.com.Img_Bg, true)
            HelperL.SetImageGray(item.com.Img_Icon, true)
            item.com.Img_RedDot.gameObject:SetActive(false)

            local state = m.GetActiveState(item.equipSme)
            --已激活/可激活
            if state ~= 3 then
                --已激活
                if state == 1 then
                    HelperL.SetImageGray(item.Img_JD, false)
                    HelperL.SetImageGray(item.com.Img_Bg, false)
                    HelperL.SetImageGray(item.com.Img_Icon, false)
                else
                    --可激活
                    local bool1 = HelperL.IsLackGoods(
                        item.equipSme.CostGoodsID1, item.equipSme.CostGoodsID1Num, false, false)
                    local bool2 = HelperL.IsLackGoods(
                        item.equipSme.CostGoodsID2, item.equipSme.CostGoodsID2Num, false, false)
                    item.com.Img_RedDot.gameObject:SetActive(not bool1 and not bool2)
                end
            end

            item.com.gameObject:SetActive(true)
        else
            item.com.gameObject:SetActive(false)
        end
    end

    return item
end

--------------------------------------------------------------------
--获取装备类型图
--------------------------------------------------------------------
function m.Get_Obj_Type(index)
    ---@class Item_Obj_Type
    local item = {}
    item.index = index
    item.gameObject = m.objList["Obj_Type" .. index].gameObject
    item.Grid_Type = m.objList["Grid_Type" .. index]
    ---@type Item_Obj_Icon[]
    item.Obj_Icon_List = {}
    for i = 1, ShowTalentNum, 1 do
        item.Obj_Icon_List[i] = m.Create_Item_Icon(item.Grid_Type, i, index)
    end

    ---更新数据
    ---@param smeltID integer 升星ID
    item.UpdateData = function(smeltID)
        local starLvl = GamePlayerData.ActorEquipNew:GetEquipUpgradeLevel(smeltID)
        local equipSmeltList = Schemes.EquipSmeltStar:GetBySmeltID(smeltID) or {}
        local num = math.floor(starLvl / ShowTalentNum) * ShowTalentNum
        for i = 1, ShowTalentNum, 1 do
            item.Obj_Icon_List[i].UpdateData(equipSmeltList[num + i + 1])
        end
    end
    return item
end

--------------------------------------------------------------------
--获取激活状态，1：已激活，2：可激活，3：条件未达成(先激活上一星)
---@param equipSme EquipSmeltStarCfg
--------------------------------------------------------------------
function m.GetActiveState(equipSme)
    if equipSme then
        -- local maxLevel = PropertyCompute.GetEquipSmeltStarMaxLevel(equipSme.SmeltID)
        local level = GamePlayerData.ActorEquipNew:GetEquipUpgradeLevel(equipSme.SmeltID)
        --已激活
        if equipSme.StarLvl <= level then
            return 1
        end
        --可激活
        if equipSme.StarLvl == (level + 1) then
            return 2
        end
    end
    --条件未达成(先激活上一星)
    return 3
end

--------------------------------------------------------------------
--显示天赋信息
---@param item Item_Obj_Icon
--------------------------------------------------------------------
function m.ShowTalentInof(item)
    m.objList.Btn_Upgrade.gameObject:SetActive(false)
    m.objList.Txt_Unlock.text = ''
    local state = m.GetActiveState(item.equipSme)
    if state == 1 then
        m.objList.Txt_Unlock.text = GetGameText(luaID, 9)
    elseif state == 2 then
        local bool1 = HelperL.IsLackGoods(item.equipSme.CostGoodsID1, item.equipSme.CostGoodsID1Num, false, false)
        -- local bool2 = HelperL.IsLackGoods(item.equipSme.CostGoodsID2, item.equipSme.CostGoodsID2Num, false, false)
        local num = SkepModule:GetGoodsCount(item.equipSme.CostGoodsID1)
        local color = (not bool1) and "#fff300" or UI_COLOR.Red
        local tempSme = Schemes.EquipSmeltStar:GetEquipSmeltStarCfg(item.equipSme.SmeltID, item.equipSme.StarLvl - 1)
        m.objList.Txt_Expend.text = string.format("<color=%s>%s/%s</color>", color,
            HelperL.GetChangeNum(num), tempSme.CostGoodsID1Num)
        AtlasManager:AsyncGetGoodsSprite(item.equipSme.CostGoodsID1, m.objList.Img_Expend)
        m.objList.Btn_Upgrade.gameObject:SetActive(true)

        HelperL.SetImageGray(m.objList.Btn_Upgrade, bool1)
    else
        m.objList.Txt_Unlock.text = GetGameText(luaID, 10)
    end

    m.SmeltID = item.equipSme.SmeltID
    m.objList.Txt_TalentName.text = item.equipSme.Name
    m.objList.Txt_TalentContent.text = Schemes.CommonProp:Get(item.equipSme.PropId).Remark
    m.objList.Obj_TalentInof.gameObject:SetActive(true)
end

--------------------------------------------------------------------
-- 关闭信息界面
--------------------------------------------------------------------
function m.CloseInfo()
    m.objList.Obj_Info.gameObject:SetActive(false)
    m.objList.Grid_Treasured.gameObject:SetActive(true)
    m.objList.Obj_TalentInof.gameObject:SetActive(false)
end

--------------------------------------------------------------------
-- 关闭界面
--------------------------------------------------------------------
function m.CloseUI()
    m:CloseSelf()
end

return m
