--[[
********************************************************************
    created:	2024/05/10
    author :	黄宗银
    purpose:    数据服务(静态方法,C#调用)
*********************************************************************
--]]

---数据服务(静态方法,C#调用)
---@class DataService
local m = {}
DataService = m

--- 获取角色ID
---@return integer 角色Id
function m.GetActorID()
    return EntityModule.hero:GetProperty(ENTITY_FIELD.ENTITY_FIELD_ID)
end

function m.GetHeadSprite()
    local weaponsKnapsack = GamePlayerData.ActorEquip:GetWeaponsKnapsack(1) or {}
    local curEquipID = weaponsKnapsack[1] or 0
    return HelperL.GetGunIcon(curEquipID)
end

function m.GetActorName()
    return EntityModule.hero.name
end

--- 获取角色属性
---@return string json 角色属性
function m.GetPlayerProp()
    return dkjsonHelper.encode(ActorProp.GetPlayerFieldsByClient().Result)
end

--- 读取角色的装备属性
---@param goodsID integer 物品ID
---@return string json 装备属性
function m.ReadEquipProp(goodsID)
    local equip = nil
    local equipment = Schemes.Equipment:Get(goodsID)
    if equipment then
        local packsack = SkepModule:GetSkepByID(equipment.PacketID)
        if packsack then
            equip = packsack:GetEntityByGoodsID(equipment.ID)
        end
    end
    return dkjsonHelper.encode(ActorProp.ReadEquipProp(equip))
end

--- 获取进入的关卡类型
---@return integer 角色Id
function m.GetStageType()
    return BattleManager.mainType
end

--- 获取进入的关卡Id
---@return integer 角色Id
function m.GetStageId()
    return BattleManager.stageId
end

--- 获取出战的宠物
---@return string json 宠物列表
function m.GetPetsOnStage()
    return dkjsonHelper.encode(GamePlayerData.PetsInStage)
end

--- 读取装备的属性配置
---@param goodsID integer 物品ID
---@param starCount integer 星量
---@return string json 装备属性
function m.ReadStarProp(goodsID, starCount)
    return dkjsonHelper.encode(ActorProp.ReadStarProp(ActorProp.GetSmeltID(goodsID), starCount))
end

--- 按物品ID读取角色的火之装备属性
---@param goodsID integer 物品ID
---@param needWear ?boolean 是否佩戴(出战)了才读取,否则总是读取
---@return string json 火之装备属性
function m.GetWeaponProp(goodsID, needWear)
    return dkjsonHelper.encode(ActorProp.GetWeaponProp(goodsID, needWear))
end

--- 获取角色的火之装备列表
---@param onlyWear boolean 仅佩戴(出战)的,否则所有拥有的火之装备
---@return string json 火之装备的装备属性列表
function m.GetWeapons(onlyWear)
    return dkjsonHelper.encode(ActorProp.GetWeapons(onlyWear))
end

--- 获取角色主线进度(通过的最大关卡)
---@return integer 第几关
function m.GetMaxMainStage()
    return HeroDataManager:GetLogicData(LOGIC_DATA.DATA_SOCIETY_ECTYPETOWER_CURSTAGE)
end

--- 获取Web应用根Url
---@param key string SrvUrlRoot的成员名称
---@return string Web应用根Url
function m.GetSrvUrlRoot(key)
    return SrvUrlRoot[key]
end

--- 获取装备升星经验
---@param equipID integer 装备ID
---@return integer
function m.GetEquipStarExp(equipID)
    local equipment = Schemes.Equipment:Get(equipID)
    if equipment then
        local packsack = SkepModule:GetSkepByID(equipment.PacketID)
        if packsack then
            local entity = packsack:GetEntityByGoodsID(equipment.ID)
            if entity then
                return entity:GetProperty(EQUIPMENT_FIELD.EQUIPMENT_FIELD_SMELTEXP) or 0
            end
        end
    end
    return 0
end

--- 打开计算界面
---@param stageId integer 关卡ID
---@param isSuccess boolean 是否成功
---@param battleBrushEnemyID integer 刷怪ID用来取失败奖励
---@param addFactor integer 奖励加成
function m.OpenSettleAccounts(stageId, isSuccess, battleBrushEnemyID, addFactor)
    UIManager:OpenWnd(WndID.SettleAccounts, stageId, isSuccess, battleBrushEnemyID, addFactor)
end

--- 打开复活界面
function m.OpenReviveDialog()
    UIManager:OpenWnd(WndID.ReviveDialog)
end

--- 看广告加速游戏
function m.OpenGameAcceleration()
    --广告ID
    local adID = 100
    local commonText = Schemes.CommonText:Get(adID)
    local lv = HeroDataManager:GetLogicByte(commonText.Param2, commonText.Param3)
    print('-------看广告加速游戏----------lv=', lv, commonText.ToTime)
    --激活无需看广告
    if lv >= commonText.ToTime then
        LuaEventToCsharp.Instance:Notify("看广告加速adID=100", { tostring(true) })
    else
        UIManager:OpenWnd(WndID.GameAcceleration)
    end
end

--- 获取物品数量
--- @param goodsID integer 物品ID
function m.GetGoodsCount(goodsID)
    return SkepModule:GetGoodsCount(goodsID)
end

--- 直接向服务器请求发放物品和扣除消耗物品
---@param goodInfo string 发放物品
---@param costInfo string 扣除消耗物品
---@param fun ?fun(resultCode:integer, content:string) 回调函数
function m.RequestDirectGiveGoodsy(goodInfo, costInfo, fun)
    HelperL.RequestDirectGiveGoodsy(goodInfo, costInfo, fun, false)
end
