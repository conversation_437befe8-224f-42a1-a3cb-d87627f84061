--[[
********************************************************************
    created:    2023/08/08
    author :    李锦剑
    purpose:    每日特惠
*********************************************************************
--]]

local luaID = ('UIDiscountGift')
--每日特惠
---@class UIDiscountGift:UIWndBase
local m = {}

--------------------------------------------------------------------
-- 订阅事件列表
--------------------------------------------------------------------
function m.GetOpenEventList()
	return {
		--[EventID.OnHeroPropChange] = m.UpdateView,
		[EventID.LogicDataChange] = m.UpdateView,
	}
end

--------------------------------------------------------------------
--预制体自适应
--------------------------------------------------------------------
function m.AdaptScale()
	-- HelperL.AdaptScale(m.objList.Img_IconBg, 6)
end

--------------------------------------------------------------------
-- 初始化
--------------------------------------------------------------------
function m.OnCreate()
	m.leftTime = 0
	m.lastClickTime = 0
	m.ToggleItemList = {}
	m.DayGiftItems = {}

	m.RegisterClickEvent()
	m.CreationToggle()
	m.ToggleCommon(m.selectIndex or 1)
	return true
end

--------------------------------------------------------------------
--注册点击事件
--------------------------------------------------------------------
function m.RegisterClickEvent()
	m.objList.Btn_Close.onClick:AddListenerEx(m.OnClickClose)
end

-- 创建Toggle
function m.CreationToggle()
	local dataList = {
		{ actvID = ACTVID.ACTVID_RECHARGEBUY_1, text = GetGameText(luaID, 4), },
		--{ actvID = ACTVID.ACTVID_RECHARGEBUY_2, text = GetGameText(luaID, 5), },
		--{ actvID = ACTVID.ACTVID_RECHARGEBUY_3, text = GetGameText(luaID, 6), },
	}

	local _type
	for i, v in ipairs(dataList) do
		if ActivityManager:IsActivityOpen(v.actvID) then
			local item = m:CreateSubItem(m.objList.Grid_Toggle, m.objList.Item_Toggle)
			item.data = v
			item.index = #m.ToggleItemList + 1
			item.Txt_Name1.text = v.text
			item.Txt_Name2.text = v.text
			item.Img_Bg1.gameObject:SetActive(true)
			item.Img_Bg2.gameObject:SetActive(false)

			if i == 1 then
				_type = 2
			elseif i == #dataList then
				_type = 3
			else
				_type = 1
			end
			AtlasManager:AsyncGetSprite("thlb_anbj" .. _type, item.Img_Bg1)
			AtlasManager:AsyncGetSprite("thlb_an" .. _type, item.Img_Bg2)
			item.gameObject:GetComponent("Button").onClick:AddListenerEx(function()
				m.ToggleCommon(item.index)
			end)
			-- item.redDot = m:SetWndRedDot(item.gameObject.transform, Vector2(-30, -27))
			-- if item.redDot then item.redDot:AddCheckParam(WndID.DiscountGift, v.actvID) end
			table.insert(m.ToggleItemList, item)
		end
	end

	m.objList.Grid_Toggle.gameObject:SetActive(false)
end

-- 点击Toggle公共方法
function m.ToggleCommon(index)
	if m.selectIndex == index then return end
	if m.selectIndex then
		m.ToggleItemList[m.selectIndex].Img_Bg1.gameObject:SetActive(true)
		m.ToggleItemList[m.selectIndex].Img_Bg2.gameObject:SetActive(false)
	end

	m.selectIndex = index
	m.ToggleItemList[index].Img_Bg1.gameObject:SetActive(false)
	m.ToggleItemList[index].Img_Bg2.gameObject:SetActive(true)
	m.UpdateView()
end

--更新界面
function m.UpdateView()
	local actvID = m.ToggleItemList[m.selectIndex].data.actvID
	local dataList = {}
	for k, v in ipairs(Schemes.RechargeCard.items) do
		if v.NeedActvID == actvID then
			table.insert(dataList, v)
		end
	end
	local num = math.max(#m.DayGiftItems, #dataList)
	local item
	for i = 1, num, 1 do
		item = m.DayGiftItems[i]
		if not item then
			item = m.CreationDayGift(i)
			m.DayGiftItems[i] = item
		end
		item.UpdateView(dataList[i])
	end
end

--创建礼包
function m.CreationDayGift(index)
	local item = {}
	item.index = index
	item.objList = m:CreateSubItem(m.objList.Grid_DayGift, m.objList.Item_DayGift)

	item.objList.Btn_Buy.onClick:AddListenerEx(function()
		local commonText = Schemes.CommonText:Get(item.data.VipScore)
		local num = HeroDataManager:GetLogicByte(commonText.Param4, commonText.Param5)
		if num == 0 then
			HelperL.Recharge(item.data.ID)
		end
	end)

	--更新界面
	item.UpdateView = function(data2)
		item.data = data2
		if data2 then
			local commonText = Schemes.CommonText:Get(data2.VipScore)
			local num = HeroDataManager:GetLogicByte(commonText.Param4, commonText.Param5)
			if num == 0 then
				item.objList.Btn_Buy.interactable = true
				item.objList.Txt_Buy.text = '￥' .. data2.FirstRMB / 100
			else
				item.objList.Btn_Buy.interactable = false
				item.objList.Txt_Buy.text = CommonTextID.SOLD_OUT
			end
			item.objList.Txt_Title.text = data2.CardName
			--创建显示商品
			m.ShowGiftThreeItemIcon(item)
			item.objList.gameObject:SetActive(true)
		else
			item.objList.gameObject:SetActive(false)
		end
	end

	return item
end

--打开提示
function m.OnClickInstro(data)
	if not data then return end
	UIManager:OpenWnd(
		WndID.ShowBoxInfo,
		data.ShowGiftID,
		data.PrizeID,
		data.FirstPic1,
		data.CardName,
		CommonTextID.OK,
		nil,
		nil
	)
end

-- 领取奖励
function m.RequestBuyZero(data)
	if data.Description ~= '0' then
		local commonText = Schemes.CommonText:Get(data.Description)
		local num = HeroDataManager:GetLogicByte(commonText.Param2, commonText.Param3)
		if num >= commonText.ToTime then
			local str_req = string.format("LuaRequestRechargeCardByZero?CardID=%s", data.ID)
			LuaModule.RunLuaRequest(str_req, function(resultCode, content)
				if resultCode ~= RESULT_CODE_BASE.RESULT_COMMON_SUCCEED[1] then
					HelperL.ShowMessage(TipType.FlowText, ResultCode:GetResultCodeStr(resultCode, content))
				else
					m.UpdateView()
				end
			end)
		end
	end
end

-- 每秒更新
function m.OnSecondUpdate()
	--活动剩余时间刷新
	local time = ActivityManager:GetActivityEndTime(m.ToggleItemList[m.selectIndex].data.actvID)
		- HelperL.GetServerTime()
	if time <= 0 then time = 0 end
	time = HelperL.GetTimeString(TimeStringType.FullAuto1, time)
	m.objList.Txt_ActivityTime.text = string.format(GetGameText(luaID, 7), time)
end

-- 窗口页面关闭
function m.OnClickClose()
	m:CloseSelf()
end

--品字方式显示3个奖励物品
function m.ShowGiftThreeItemIcon(item)
	local prizeList = Schemes.PrizeTable:GetGoodsList(item.data.PrizeID)
	if not prizeList then
		warn('-----奖励ID为空----prizeId=', item.data.PrizeID)
		return
	end
	if not item.GoodsList then
		---@type SlotItem[]
		item.GoodsList = {}
	end
	local posList = { Vector3.New(165, -20, 0), Vector3.New(365, -20, 0), Vector3.New(565, -20, 0) }
	for i, v in ipairs(prizeList) do
		if not item.GoodsList[i] then
			item.GoodsList[i] = _GAddSlotItem(item.objList.Img_Goods)
		end
		item.GoodsList[i]:SetItemID(prizeList[i].id)
		item.GoodsList[i]:SetCount(prizeList[i].num)
		item.GoodsList[i].transform.localPosition = posList[i]
	end
end

return m
